# CLAUDE_MAP.md - BudApp Architecture Map

> **Comprehensive mapping of all files, components, classes, and architectural patterns in the `lib/` directory**

## Overview

BudApp is a Flutter personal finance application built with **feature-based architecture**, leveraging **Firebase**, **Riverpod**, and **Material 3 design**. The codebase follows strict **Repository pattern** implementation with **clean architecture** principles.

### Architecture Summary
- **Total Files**: 294+ Dart files
- **Architecture**: Feature-based with clean separation of concerns
- **State Management**: Riverpod with AsyncNotifier pattern
- **Data Models**: Freezed immutable models with JSON serialization
- **Backend**: Firebase (Auth, Firestore, Remote Config, App Check)
- **Navigation**: go_router with authentication guards
- **Design**: Material 3 with multi-environment theming

---

## 🏗️ Core Architecture

### Application Entry Point

#### `lib/main.dart`
**Classes & Functions:**
- `main()` - Application entry point with Firebase initialization
- `MyApp extends ConsumerWidget` - Root application widget
  - Methods: `build()` - Handles app initialization and theming

**Key Patterns:**
- Environment-specific Firebase configuration via `EnvironmentConfig`
- Global error handling setup
- Provider scope initialization
- Multi-environment theming system

#### `lib/app.dart` ❌ (File not found - main.dart serves as entry point)

---

## ⚙️ Configuration Layer

### `lib/config/`

#### `environment_config.dart`
**Classes & Functions:**
- `EnvironmentConfig` - Static environment management class
  - Static getters: `flavor`, `environmentName`, `appTitle`, `themeColor`, `firebaseProjectName`
  - Static methods: `initializeAppCheck()`, `get firebaseOptions`, `get environmentSummary`
  - Environment detection: `isDevelopment`, `isStaging`, `isProduction`
  - Firebase App Check configuration with environment-specific providers

**Key Patterns:**
- Multi-environment support (dev/staging/prod)
- Firebase App Check security configuration
- Compile-time environment detection via `--dart-define=FLAVOR`

#### `app_theme.dart`
**Classes & Functions:**
- `AppTheme` - Material 3 theme configuration
  - Static methods: `lightTheme`, `darkTheme`
  - Theme customization based on environment colors

#### `design_tokens.dart`
**Classes & Functions:**
- `DesignTokens` - Material 3 design system constants
- Spacing, colors, typography definitions

#### `form_constants.dart`
**Classes & Functions:**
- `FormConstants` - Global form validation constants
- Field length limits, validation rules

#### `text_overflow_config.dart`
**Classes & Functions:**
- `TextOverflowConfig` - Text handling configuration
- Ellipsis and overflow behavior definitions

---

## 🗄️ Data Layer

### `lib/data/models/`

All models follow **Freezed pattern** with JSON serialization:

#### Core Financial Models

**`account.dart`**
- `enum AccountType` - checking, savings, creditCard, cash, investment, loan
- `enum AccountClassification` - asset, liability
- `Account with _$Account` - Financial account model
  - Properties: id, userId, name, type, classification, balances, metadata
  - Methods: `fromJson()`, `toJson()` (generated)

**`transaction.dart`**
- `enum TransactionType` - income, expense, transfer
- `Transaction with _$Transaction` - Financial transaction model
  - Properties: id, userId, accountId, categoryId, amountCents, type, date
  - Atomic balance updates with transaction integrity

**`budget.dart`**
- `enum BudgetType` - monthly, yearly, custom
- `enum BudgetStatus` - active, paused, completed
- `Budget with _$Budget` - Budget allocation model
  - Properties: id, userId, categoryId, amountCents, period, type

**`category.dart`**
- `enum CategoryType` - income, expense, transfer
- `Category with _$Category` - Hierarchical category model
  - Properties: id, userId, name, type, parentId, iconName, colorHex
  - Supports unlimited nesting levels

**`goal.dart`**
- `enum GoalType` - savings, debt_payoff, emergency_fund
- `Goal with _$Goal` - Savings goal model
  - Properties: id, userId, name, targetAmountCents, currentAmountCents

**`goal_contribution.dart`**
- `GoalContribution with _$GoalContribution` - Goal contribution tracking
  - Properties: id, goalId, userId, amountCents, contributionDate

**`tag.dart`**
- `Tag with _$Tag` - Tagging system model
  - Properties: id, userId, name, colorHex, description
  - Many-to-many relationship with transactions

**`user_profile.dart`**
- `UserProfile with _$UserProfile` - User preferences and settings
  - Properties: id, email, displayName, currencyCode, locale, preferences

#### Supporting Models

**`budget_progress.dart`**
- `BudgetProgress with _$BudgetProgress` - Calculated budget status
  - Properties: budgetId, spentAmountCents, remainingAmountCents, percentage

**`remote_config_data.dart`**
- `RemoteConfigData with _$RemoteConfigData` - Server-side configuration
  - Properties: featureFlags, appVersions, maintenanceMode

**`session_state.dart`**
- `SessionState` - User session management
  - Properties: userId, isAuthenticated, lastActivity

#### Converters

**`lib/data/models/converters/metadata_converter.dart`**
- `MetadataConverter extends JsonConverter` - Safe JSON metadata handling
  - Methods: `fromJson()`, `toJson()` - Prevents serialization issues

### `lib/data/repositories/`

#### Interfaces (`interfaces/`)

**`repositories.dart`** - Barrel export for all repository interfaces

**Core Repository Interfaces:**
- `IUserRepository` - User profile operations
- `IAccountRepository` - Account CRUD with balance management
- `ITransactionRepository` - Transaction processing with atomic updates
- `ICategoryRepository` - Hierarchical category management
- `IBudgetRepository` - Budget allocation and tracking
- `IGoalRepository` - Goal management operations
- `IGoalContributionRepository` - Goal contribution tracking
- `ITagRepository` - Tag management operations
- `IRemoteConfigRepository` - Remote configuration management

**`performance_optimized_repository.dart`**
- `IPerformanceOptimizedRepository` - Performance optimization interface
  - Methods: `clearCache()`, `invalidateCache()`, `getCacheStats()`

#### Implementations (`implementations/`)

**Pattern**: All implementations follow strict Repository pattern with dependency injection

**`account_repository_impl.dart`**
- `AccountRepositoryImpl implements IAccountRepository`
  - Firebase operations: createAccount, updateAccount, deleteAccount
  - Real-time streams: watchUserAccounts, watchAccountForUser
  - Balance calculations: getUserAccountSummary with caching
  - Performance optimization: Intelligent caching, batch operations

**`transaction_repository_impl.dart`**
- `TransactionRepositoryImpl implements ITransactionRepository`
  - Atomic transactions: create/update with balance synchronization
  - Complex queries: by account, category, date range, tags
  - Performance: Paginated queries, efficient filtering

**Similar pattern for all other repository implementations**

---

## 🏢 Services Layer

### `lib/services/`

#### Core Infrastructure Services

**`firestore_service.dart`**
- `FirestoreService` - Firebase Firestore abstraction
  - Methods: `collection()`, `doc()`, `batch()`, `runTransaction()`
  - Connectivity: `testConnectivity()`, `enableNetwork()`, `disableNetwork()`
  - Management: `clearPersistence()`, `waitForPendingWrites()`

**`remote_config_service.dart`**
- `RemoteConfigService` - Server-side configuration management
  - Methods: `initialize()`, `fetchAndActivate()`, `getValue()`
  - Caching: Local cache with background updates
  - Fallbacks: Graceful degradation when service unavailable

**`secure_storage_service.dart`**
- `SecureStorageService` - Encrypted local storage
  - Methods: `store()`, `retrieve()`, `delete()`, `clearAll()`
  - Security: Biometric keys, API tokens, user PINs

**`cache_service.dart`**
- `CacheService` - Multi-level caching system
  - Memory cache + persistent storage
  - Intelligent invalidation strategies

#### Business Services

**`currency_service.dart` & `currency_preferences_service.dart`**
- Global currency formatting and preference management
- Multi-currency support with locale-aware formatting
- Methods: `formatAmount()`, `getUserCurrency()`, `setCurrency()`

**`performance_service.dart`**
- App performance monitoring and metrics
- Methods: `trackAppStartup()`, `startTrace()`, `stopTrace()`
- Firebase Performance integration

**`session_service.dart`**
- User session and authentication state management
- Methods: `initialize()`, `getCurrentSession()`, `invalidateSession()`

#### Security & Error Handling

**`logging_service.dart`**
- Production-safe structured logging with PII protection
- Methods: `info()`, `error()`, `debug()`, `warn()`
- Features: Automatic sanitization of emails, phones, amounts, API keys

**`error_service.dart`**
- Centralized error handling and user-friendly error messaging
- Methods: `showErrorSnackBarWithLogging()`, `logError()`, `setUserContext()`
- Features: Firebase-specific error translation, crash reporting integration

**`global_error_handler.dart`**
- Firebase Crashlytics integration for comprehensive crash reporting
- Methods: `initialize()`, `reportError()`, `setUserIdentifier()`
- Features: Global error handlers, user context tracking, breadcrumb logging

#### Connectivity Services

**`lib/services/implementations/firebase_connectivity_service_impl.dart`**
- `FirebaseConnectivityServiceImpl implements IFirebaseConnectivityService`
- Firebase connection state management

---

## 🎯 Features Layer

### `lib/features/`

#### Authentication (`auth/`)

**Presentation Layer:**
- `auth_wrapper.dart` - Authentication flow wrapper
- `login_screen.dart` - Email/password login interface
- `signup_screen.dart` - User registration interface
- `email_verification_screen.dart` - Email verification flow
- `biometric_gate_screen.dart` - Biometric authentication screen
- `forgot_password_screen.dart` - Password reset interface
- `auth_settings_screen.dart` - Authentication preferences

**Widgets:**
- `auth_button.dart` - Styled authentication buttons
- `auth_form_field.dart` - Custom form fields with validation
- `social_login_button.dart` - Google OAuth button
- `biometric_auth_button.dart` - Biometric authentication trigger
- `auth_error_banner.dart` - User-friendly error display

**Services:**
- `auth_service.dart` - Authentication business logic
  - Methods: `signInWithEmail()`, `signUpWithEmail()`, `signInWithGoogle()`
  - Methods: `signOut()`, `sendEmailVerification()`, `resetPassword()`
- `biometric_service.dart` - Biometric authentication handling
  - Methods: `isSupported()`, `authenticate()`, `enrollBiometric()`
- `auth_error_service.dart` - Firebase Auth error translation

**Providers:**
- `auth_providers.dart` - Authentication state management
  - `authServiceProvider`, `authStateProvider`, `currentUserProvider`
- `biometric_providers.dart` - Biometric state management
  - `biometricServiceProvider`, `biometricGateStateNotifierProvider`

#### Accounts (`accounts/`)

**Presentation Layer:**
- `accounts_list_screen.dart` - Account overview with filtering
- `account_detail_screen.dart` - Individual account details
- `account_create_screen.dart` - New account creation form
- `account_edit_screen.dart` - Account modification interface

**Widgets:**
- `account_card.dart` - Account display card with balance
- `account_type_selector.dart` - Account type picker
- `account_color_selector.dart` - Color selection widget
- `account_icon_selector.dart` - Icon selection widget
- `account_type_filter.dart` - Account filtering controls
- `empty_accounts_state.dart` - Empty state illustration

**Services:**
- `account_validators.dart` - Account data validation
  - Methods: `validateAccountName()`, `validateInitialBalance()`

**Providers:**
- `account_providers.dart` - Account state management
  - `accountListProvider`, `accountProvider`, `activeAccountsProvider`
  - `AccountCreationNotifier`, `AccountUpdateNotifier`
  - Performance-optimized providers for asset/liability accounts

#### Transactions (`transactions/`)

**Presentation Layer:**
- `transactions_list_screen.dart` - Transaction history with filtering
- `transaction_create_screen.dart` - New transaction form
- `transaction_edit_screen.dart` - Transaction modification
- `transaction_filters_screen.dart` - Advanced filtering interface
- `transaction_search_screen.dart` - Transaction search functionality

**Widgets:**
- `transaction_card.dart` - Transaction display component
- `transaction_form.dart` - Unified transaction form
- `amount_input_field.dart` - Currency amount input
- `category_selector.dart` - Category selection widget
- `tag_selector.dart` - Multi-tag selection
- `date_picker_field.dart` - Date selection component

**Services:**
- `transaction_validators.dart` - Transaction validation logic
- `balance_calculation_service.dart` - Account balance updates

**Providers:**
- `transaction_providers.dart` - Transaction state management
- Performance-optimized providers for filtered lists

#### Categories (`categories/`)

**Presentation Layer:**
- `categories_list_screen.dart` - Hierarchical category display
- `category_create_screen.dart` - Category creation (supports subcategories)
- `category_edit_screen.dart` - Category modification

**Widgets:**
- `category_card.dart` - Category display with hierarchy
- `category_tree_view.dart` - Expandable category tree
- `subcategory_list.dart` - Subcategory management

**Services:**
- `category_validators.dart` - Category validation rules

**Providers:**
- `category_providers.dart` - Category state management with hierarchical support

#### Tags (`tags/`)

**Presentation Layer:**
- `tags_list_screen.dart` - Tag management interface
- `tag_create_screen.dart` - New tag creation
- `tag_edit_screen.dart` - Tag modification

**Widgets:**
- `tag_chip.dart` - Tag display component
- `tag_selector.dart` - Multi-select tag picker

**Services:**
- `tag_validators.dart` - Tag validation logic

**Providers:**
- `tag_providers.dart` - Tag state management

#### Budgets (`budgets/`)

**Presentation Layer:**
- `budgets_list_screen.dart` - Budget overview with progress
- `budget_edit_screen.dart` - Budget allocation management

**Widgets:**
- `budget_card.dart` - Budget display with progress bar
- `budget_progress_indicator.dart` - Visual progress representation

**Services:**
- `budget_calculation_service.dart` - Budget vs. actual calculations

**Providers:**
- `budget_providers.dart` - Budget state management with progress tracking

#### Goals (`goals/`)

**Presentation Layer:**
- `goals_list_screen.dart` - Goal overview with progress
- `goal_create_screen.dart` - New goal setup
- `goal_edit_screen.dart` - Goal modification
- `goal_contributions_list_screen.dart` - Contribution history
- `goal_contribution_create_screen.dart` - Add contribution
- `goal_contribution_edit_screen.dart` - Edit contribution

**Widgets:**
- `goal_card.dart` - Goal display with progress
- `goal_progress_chart.dart` - Visual progress tracking
- `contribution_form.dart` - Contribution input form

**Providers:**
- `goal_providers.dart` - Goal and contribution state management

#### Profile (`profile/`)

**Presentation Layer:**
- `profile_screen.dart` - User profile overview
- `unified_profile_management_screen.dart` - Profile editing
- `forgot_password_screen.dart` - Password reset (profile context)

**Widgets:**
- `profile_avatar.dart` - User avatar display
- `profile_settings_tile.dart` - Settings list items

**Services:**
- `profile_validators.dart` - Profile data validation

**Providers:**
- `profile_providers.dart` - Profile state management

#### Settings (`settings/`)

**Presentation Layer:**
- `currency_settings_screen.dart` - Currency preference management

#### Dashboard (`dashboard/`)

**Presentation Layer:**
- `home_screen.dart` - Main dashboard with financial overview

**Providers:**
- `dashboard_providers.dart` - Dashboard data aggregation

#### Common (`common/`)

**Models:**
- Time period management models

**Services:**
- Shared business logic across features

**Widgets:**
- Reusable UI components

**Providers:**
- `time_period_providers.dart` - Shared time period state

---

## 🧭 Navigation & Routing

### `lib/routing/`

#### `app_router.dart`
**Classes & Functions:**
- `AppRoutes` - Static route path constants
  - All application routes defined as constants
- `AuthStateNotifier extends ChangeNotifier` - Reactive authentication routing
  - Methods: `get currentUser`, `get isAuthLoading`, `get biometricGateRequired`
  - Listens to auth and biometric state changes
- `goRouterProvider` - Main router configuration
  - Comprehensive route definitions with authentication guards
  - Nested routes with shell routing for main app navigation
  - Error handling with custom error pages

**Key Features:**
- Authentication-aware routing with automatic redirects
- Biometric gate integration
- Email verification flow handling
- Nested routing with bottom navigation shell
- Error boundary routing

---

## 🎨 UI Components & Widgets

### `lib/widgets/`

#### Navigation (`navigation/`)

**`main_app_shell.dart`**
- `MainAppShell extends ConsumerStatefulWidget` - Main navigation container
  - Methods: `didChangeDependencies()`, `build()`
- `shellRouteBuilder()` - Shell route wrapper function

**`custom_bottom_navigation.dart`**
- Custom bottom navigation implementation with Material 3 design

**Provider Files:**
- `bottom_nav_providers.dart` - Bottom navigation state
- `navigation_providers.dart` - Navigation state management

#### Forms (`forms/`)

**Comprehensive form architecture with:**

**Interfaces (`interfaces/`):**
- `form_config.dart` - Form configuration contracts
- `form_field_config.dart` - Field configuration interfaces
- `form_validation.dart` - Validation rule interfaces

**Implementations (`implementations/`):**
- Concrete form implementations for all major forms

**Factory (`factory/`):**
- `form_factory.dart` - Dynamic form generation

**Selectors (`selectors/`):**
- Specialized selector widgets (currency, date, category, etc.)

**Helpers (`helpers/`):**
- Form utilities and helper functions

**Configs (`configs/`):**
- Pre-configured form setups

#### Common (`common/`)

**Reusable UI components:**
- Error displays
- Loading indicators
- Empty state illustrations
- Data visualization components

---

## 🌐 Providers & State Management

### `lib/providers/`

#### Core Providers (`providers.dart`)
**Key Providers:**
- `firebaseAuthProvider` - Firebase Auth instance
- `firestoreProvider` - Firestore instance
- `googleSignInProvider` - Google Sign-In instance
- `firestoreServiceProvider` - Firestore service with DI
- `authStateProvider` - User authentication stream
- `currentUserProvider` - Synchronous current user
- `initializationProvider` - App initialization flow

#### Specialized Provider Files
- `repository_providers.dart` - All repository instances
- `firebase_providers.dart` - Firebase service providers
- `error_providers.dart` - Error handling providers
- `logging_providers.dart` - Logging service providers
- `ui_providers.dart` - UI state providers

---

## 🔧 Utilities

### `lib/utils/`

**Utility functions and helpers** (files exist but not extensively analyzed in detail)

---

## 🌍 Localization

### `lib/l10n/`

#### Localization Files
- `app_localizations.dart` - Generated localization class
- `app_localizations_en.dart` - English translations

**Features:**
- Flutter's built-in internationalization system
- Currently supports English with extensible architecture

---

## 🔥 Firebase Configuration

### Firebase Options Files
- `firebase_options.dart` - Default Firebase configuration
- `firebase_options_dev.dart` - Development environment config
- `firebase_options_staging.dart` - Staging environment config  
- `firebase_options_prod.dart` - Production environment config

**Multi-Environment Support:**
- Automatic environment detection via `FLAVOR` parameter
- Environment-specific Firebase projects and configurations
- Secure App Check configuration per environment

---

## 🏗️ Architecture Patterns & Best Practices

### Repository Pattern (CRITICAL)
✅ **Enforced throughout entire codebase**
- All data access goes through repository interfaces
- Firebase services never accessed directly from presentation layer
- Dependency injection via Riverpod providers
- Comprehensive mocking support for testing

### State Management Patterns
- **Riverpod with AsyncNotifier**: Complex state management
- **StreamProvider**: Real-time data from Firestore
- **FutureProvider**: One-time async operations
- **Provider**: Dependency injection and computed values

### Data Patterns
- **Freezed Models**: Immutable data structures with JSON serialization
- **Schema Versioning**: All models support migration via `schemaVersioning`
- **Currency as Integers**: All amounts stored as cents to avoid floating-point errors
- **Metadata Fields**: Extensible metadata on all major models

### Error Handling Patterns
- **AsyncValue.guard()**: Consistent error handling in providers
- **Global Error Handler**: Firebase Crashlytics integration
- **User-Friendly Errors**: Firebase error code translation
- **PII Protection**: Automatic sensitive data sanitization

### Performance Patterns
- **Intelligent Caching**: Multi-level caching in repositories
- **Batch Operations**: Atomic Firestore transactions
- **Stream Optimization**: Efficient real-time updates
- **Lazy Loading**: Progressive data loading strategies

### Security Patterns
- **Environment-Specific Configuration**: Dev/staging/prod separation
- **Firebase App Check**: Additional security layer
- **Secure Storage**: Encrypted local storage for sensitive data
- **Input Validation**: Comprehensive validation at service layer

---

## 📊 Code Statistics

- **Total Dart Files**: 294+
- **Generated Files**: ~45 (Freezed, JSON serialization)
- **Feature Modules**: 10 major features
- **Repository Interfaces**: 10+ with implementations
- **Services**: 15+ core and business services
- **Providers**: 50+ Riverpod providers
- **Models**: 15+ core data models with Freezed
- **Test Coverage**: 50.2% (target: 90%)

---

## 🎯 Recent Major Updates

### ✅ Production-Ready Features
- **Multi-Environment Support**: Complete dev/staging/prod configuration
- **Centralized Error Handling**: Firebase Crashlytics integration
- **Performance Monitoring**: Firebase Performance integration
- **Security Enhancements**: App Check, secure storage, PII protection
- **Repository Pattern**: Strict enforcement across all layers

### ✅ Architecture Completion
- **Feature-Based Architecture**: Complete module separation
- **Material 3 Design**: Consistent UI across all screens
- **Real-Time Synchronization**: Firestore streams throughout
- **Offline Support**: Firestore persistence enabled
- **Form Architecture**: Unified, reusable form system

---

## 🧪 Testing Architecture

### Test Organization
- Unit tests for all services and repositories
- Widget tests for UI components
- Integration tests for critical user flows
- Firebase emulator integration for backend testing

### Mocking Patterns
- Repository interfaces enable easy mocking
- Service layer abstraction supports unit testing
- Provider overrides for widget testing

---

## 📈 Performance Considerations

### Optimization Strategies
- **Repository-Level Caching**: Intelligent cache invalidation
- **Stream Optimization**: Efficient Firestore listeners
- **Batch Operations**: Atomic multi-document transactions
- **Performance Monitoring**: Firebase Performance integration
- **Memory Management**: Proper provider disposal and cleanup

### Scalability Features
- **Hierarchical Categories**: Unlimited nesting support
- **Tag System**: Many-to-many relationships
- **Modular Architecture**: Easy feature addition/removal
- **Environment Scaling**: Multi-environment deployment ready

---

*This comprehensive map documents the complete architecture and implementation details of the BudApp Flutter application. The codebase demonstrates advanced Flutter development practices with enterprise-grade architecture patterns, comprehensive error handling, and production-ready security measures.*

---

**Generated via OODA Analysis Framework**  
**Observe → Orient → Decide → Act**  
**Analysis Date**: 2025-08-01  
**Total Files Analyzed**: 294+ Dart files