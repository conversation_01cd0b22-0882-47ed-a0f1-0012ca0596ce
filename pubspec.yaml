name: budapp
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  # All dependencies in alphabetical order
  cloud_firestore: ^5.6.12
  cupertino_icons: ^1.0.8
  firebase_analytics: ^11.6.0
  firebase_app_check: ^0.3.2+10
  firebase_auth: ^5.7.0
  firebase_core: ^3.15.2
  firebase_crashlytics: ^4.3.10
  firebase_performance: ^0.10.1+10
  firebase_remote_config: ^5.5.0
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_riverpod: ^2.6.1
  flutter_secure_storage: ^9.2.2
  freezed_annotation: ^3.1.0
  go_router: ^16.0.0
  google_sign_in: ^7.1.1
  intl: any
  json_annotation: ^4.9.0
  local_auth: ^2.1.6
  logger: ^2.0.2+1
  riverpod_annotation: ^2.6.1
  shared_preferences: ^2.3.3
  sign_in_with_apple: ^7.0.1
  url_launcher: ^6.3.1

dev_dependencies:
  # All dev dependencies in alphabetical order
  build_runner: ^2.5.4
  fake_cloud_firestore: ^3.0.3
  firebase_auth_mocks: ^0.14.2
  flutter_launcher_icons: ^0.14.1
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  flutter_native_splash: ^2.4.1
  flutter_test:
    sdk: flutter
  freezed: ^3.1.0
  json_serializable: ^6.9.5
  mocktail: ^1.0.4
  riverpod_generator: ^2.6.2
  # Enhanced linting package with stricter rules for production-quality code
  very_good_analysis: ^9.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Enable localization generation
  generate: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

  # Set dev as the default flavor so 'flutter run' uses dev environment
  default-flavor: dev

# Internationalization configuration
flutter_gen:
  output: lib/gen/
  line_length: 80

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/icon/budapp_icon.png"
  adaptive_icon_foreground: "assets/icon/budapp_icon.png"
  adaptive_icon_background: "#000000"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/icon/budapp_icon.png"
    background_color: "#01A2A1"
    theme_color: "#01A2A1"
  windows:
    generate: true
    image_path: "assets/icon/budapp_icon.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/icon/budapp_icon.png"

# Flutter Native Splash configuration
flutter_native_splash:
  color: "#01A2A1"
  image: assets/splash/budapp_icon.png
  #branding: assets/icon/budapp_icon_mono.png
  color_dark: "#01A2A1"
  image_dark: assets/splash/budapp_icon.png
  #branding_dark: assets/icon/budapp_icon_mono.png

  android_12:
    image: assets/splash/budapp_icon.png
    icon_background_color: "#01A2A1"
    #branding: assets/icon/budapp_icon_mono.png

  android_gravity: center
  ios_content_mode: center
  web_image_mode: center

  fullscreen: true

  info_plist_files:
    - 'ios/Runner/Info.plist'
