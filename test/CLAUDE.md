# Test Directory - CLAUDE.md

This directory contains comprehensive test suites following Flutter testing best practices.

## Test Coverage Status
- **Current Coverage**: 50.2% (Target: 90%)
- **Test Count**: 1587+ passing tests
- **Quality Status**: "No issues found!" (Flutter analyze)

## Directory Structure

```
test/
├── config/              # Configuration testing
├── data/               # Data layer tests (models, repositories)
├── features/           # Feature-specific tests
├── helpers/            # Test utilities and mocks
├── integration/        # Integration tests
├── providers/          # Provider testing
├── services/           # Service layer tests
├── unit/              # Isolated unit tests
└── widgets/           # Widget testing
```

## Testing Infrastructure

### Core Testing Patterns
- **Mock Factory**: Use `MockDataFactory` for consistent test data
- **Firebase Mocking**: Use `mocktail` for Firebase services  
- **Provider Testing**: `ProviderContainer` with overrides
- **Widget Testing**: `ProviderScope` with mock dependencies

### Key Test Helpers

#### MockDataFactory
- Provides consistent test data across all tests
- Includes realistic data for all models
- Supports relationship constraints (accounts, categories, etc.)

#### Firebase Test Helper
- Mocks Firebase services consistently
- Handles authentication state
- Provides Firestore emulator integration

#### Test Wrapper
- Standardized widget testing setup
- Includes Material app, theme, and providers
- Handles localization and navigation context

## Testing Categories

### Unit Tests (`unit/`)  
- Isolated component testing
- Model serialization/deserialization
- Business logic validation
- No external dependencies

### Integration Tests (`integration/`)
- Multi-component interaction testing
- Repository-service integration
- Authentication flow testing
- Firebase emulator integration

### Widget Tests (`widgets/`)
- UI component testing
- User interaction simulation
- State management verification
- Accessibility testing

### Repository Tests (`data/repositories/`)
- CRUD operation testing
- Error handling verification
- Data integrity validation
- Transaction atomicity testing

## Critical Testing Areas

### High-Priority Files (0% coverage + high line count)
Focus testing efforts on:
1. Complex business logic services
2. Repository implementations
3. Form validation logic
4. State management providers
5. Navigation and routing

### Firebase Testing
- Emulator-based testing for realistic scenarios
- Security rules validation (32 comprehensive tests)
- Referential integrity testing
- Performance testing under load

## Testing Commands

```bash
# Run all tests with coverage
flutter test --coverage

# Run specific test categories
flutter test test/unit/
flutter test test/integration/
flutter test test/widgets/

# Firebase emulator testing
./scripts/start_emulators.sh
./scripts/test_with_emulators.sh

# Coverage analysis
lcov --list coverage/lcov.info | grep filename
```

## Agent Guidelines

### Writing Tests
1. **Follow AAA Pattern**: Arrange, Act, Assert
2. **Use descriptive names**: Test names should explain behavior
3. **Mock external dependencies**: Use mocktail for Firebase services
4. **Test error conditions**: Include negative test cases
5. **Verify interactions**: Check that dependencies are called correctly

### Test Data Management
```dart
// Use MockDataFactory for consistent test data
final testAccount = MockDataFactory.createAccount();
final testTransaction = MockDataFactory.createTransaction(
  accountId: testAccount.id,
);
```

### Widget Testing Pattern
```dart
testWidgets('widget displays data correctly', (tester) async {
  await tester.pumpWidget(
    TestWrapper.create(
      child: MyWidget(),
      overrides: [
        myProviderProvider.overrideWith((ref) => mockProvider),
      ],
    ),
  );
  
  expect(find.text('Expected Text'), findsOneWidget);
});
```

### Repository Testing Pattern
```dart
test('repository creates entity successfully', () async {
  // Arrange
  final mockFirestore = MockFirestoreService();
  final repository = MyRepositoryImpl(mockFirestore);
  final testData = MockDataFactory.createMyEntity();
  
  when(() => mockFirestore.createDocument(any(), any()))
      .thenAnswer((_) async => Right(testData));
  
  // Act
  final result = await repository.create(testData);
  
  // Assert
  expect(result.isRight(), true);
  verify(() => mockFirestore.createDocument(any(), any())).called(1);
});
```

## Firebase Emulator Testing

### Setup
```bash
# Start emulators
./scripts/start_emulators.sh

# Run tests with emulators
./scripts/test_with_emulators.sh
```

### Security Rules Testing
- 32 comprehensive security rule tests
- Tests user data isolation
- Validates CRUD permissions
- Covers edge cases and malicious inputs

## Coverage Improvement Strategy

### Priority Areas
1. **Data Models**: Serialization, validation, edge cases
2. **Repositories**: CRUD operations, error handling
3. **Services**: Business logic, external integrations
4. **Providers**: State management, async operations
5. **Widgets**: User interactions, state updates

### Testing Checklist
- [ ] Unit tests for all public methods
- [ ] Error condition testing
- [ ] Edge case validation
- [ ] Mock external dependencies
- [ ] Integration test coverage
- [ ] Widget interaction testing
- [ ] Performance regression testing

## Recent Testing Improvements

- Firebase emulator integration for realistic testing
- Comprehensive security rules testing suite
- MockDataFactory for consistent test data
- Enhanced error handling test coverage
- Repository pattern testing standardization
- Widget testing with proper provider mocking