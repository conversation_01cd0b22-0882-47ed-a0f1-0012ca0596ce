import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';

/// Unit tests for Firestore data model operations
///
/// These tests verify that:
/// 1. Data can be created and retrieved correctly
/// 2. Documents can be updated and deleted
/// 3. Collections work as expected
/// 4. Basic CRUD operations function properly
///
/// Note: These tests use FakeCloudFirestore to simulate Firestore behavior
/// without requiring Firebase emulator or real Firebase connection.
/// For referential integrity, the application logic should handle validation.
void main() {
  group('Firestore Data Model - CRUD Operations Tests', () {
    late FakeFirebaseFirestore firestore;
    late String userId;

    setUpAll(() async {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() async {
      // Create a fresh fake Firestore instance for each test
      firestore = FakeFirebaseFirestore();
      userId = 'test_user_${DateTime.now().millisecondsSinceEpoch}';

      // Create user profile document
      await firestore.collection('users').doc(userId).set({
        'uid': userId,
        'email': '<EMAIL>',
        'createdAt': FieldValue.serverTimestamp(),
        'lastLoginAt': FieldValue.serverTimestamp(),
        'isEmailVerified': false,
        'preferences': <String, dynamic>{},
        'authProviders': <String>['password'],
      });

      // Create user profile
      await firestore.collection('users').doc(userId).set({
        'uid': userId,
        'email': '<EMAIL>',
        'createdAt': FieldValue.serverTimestamp(),
        'lastLoginAt': FieldValue.serverTimestamp(),
        'isEmailVerified': false,
        'preferences': <String, dynamic>{},
        'authProviders': <String>['password'],
      });
    });

    tearDown(() async {
      // Clean up is automatic with FakeFirebaseFirestore
      // Each test gets a fresh instance
    });

    tearDownAll(() async {
      // No cleanup needed for fake Firestore
    });

    group('Account Referential Integrity', () {
      test('should allow deletion of inactive accounts', () async {
        // Create an inactive account
        final accountId =
            'test_account_${DateTime.now().millisecondsSinceEpoch}';
        await firestore
            .collection('users')
            .doc(userId)
            .collection('accounts')
            .doc(accountId)
            .set({
              'id': accountId,
              'userId': userId,
              'name': 'Test Account',
              'type': 'asset',
              'classification': 'checking',
              'currencyCode': 'USD',
              'initialBalanceCents': 0,
              'isActive': false, // Inactive account
              'isPrimary': false,
              'createdAt': FieldValue.serverTimestamp(),
              'updatedAt': FieldValue.serverTimestamp(),
              'metadata': <String, dynamic>{},
            });

        // Should be able to delete inactive account
        await expectLater(
          firestore
              .collection('users')
              .doc(userId)
              .collection('accounts')
              .doc(accountId)
              .delete(),
          completes,
        );
      });

      test('should allow deletion of active accounts', () async {
        // Create an active account
        final accountId =
            'test_account_${DateTime.now().millisecondsSinceEpoch}';
        await firestore
            .collection('users')
            .doc(userId)
            .collection('accounts')
            .doc(accountId)
            .set({
              'id': accountId,
              'userId': userId,
              'name': 'Test Account',
              'type': 'asset',
              'classification': 'checking',
              'currencyCode': 'USD',
              'initialBalanceCents': 0,
              'isActive': true, // Active account
              'isPrimary': false,
              'createdAt': FieldValue.serverTimestamp(),
              'updatedAt': FieldValue.serverTimestamp(),
              'metadata': <String, dynamic>{},
            });

        // Should be able to delete account (referential integrity handled by app logic)
        await expectLater(
          firestore
              .collection('users')
              .doc(userId)
              .collection('accounts')
              .doc(accountId)
              .delete(),
          completes,
        );
      });
    });

    group('Category Referential Integrity', () {
      test('should allow deletion of inactive categories', () async {
        // Create an inactive category
        final categoryId =
            'test_category_${DateTime.now().millisecondsSinceEpoch}';
        await firestore
            .collection('users')
            .doc(userId)
            .collection('categories')
            .doc(categoryId)
            .set({
              'id': categoryId,
              'userId': userId,
              'name': 'Test Category',
              'type': 'expense',
              'isActive': false, // Inactive category
              'isSystem': false,
              'sortOrder': 1,
              'createdAt': FieldValue.serverTimestamp(),
              'updatedAt': FieldValue.serverTimestamp(),
            });

        // Should be able to delete inactive category
        await expectLater(
          firestore
              .collection('users')
              .doc(userId)
              .collection('categories')
              .doc(categoryId)
              .delete(),
          completes,
        );
      });

      test('should allow deletion of active categories', () async {
        // Create an active category
        final categoryId =
            'test_category_${DateTime.now().millisecondsSinceEpoch}';
        await firestore
            .collection('users')
            .doc(userId)
            .collection('categories')
            .doc(categoryId)
            .set({
              'id': categoryId,
              'userId': userId,
              'name': 'Test Category',
              'type': 'expense',
              'isActive': true, // Active category
              'isSystem': false,
              'sortOrder': 1,
              'createdAt': FieldValue.serverTimestamp(),
              'updatedAt': FieldValue.serverTimestamp(),
            });

        // Should be able to delete category (referential integrity handled by app logic)
        await expectLater(
          firestore
              .collection('users')
              .doc(userId)
              .collection('categories')
              .doc(categoryId)
              .delete(),
          completes,
        );
      });
    });

    group('Tag Referential Integrity', () {
      test('should allow deletion of inactive tags', () async {
        // Create an inactive tag
        final tagId = 'test_tag_${DateTime.now().millisecondsSinceEpoch}';
        await firestore
            .collection('users')
            .doc(userId)
            .collection('tags')
            .doc(tagId)
            .set({
              'id': tagId,
              'userId': userId,
              'name': 'Test Tag',
              'isActive': false, // Inactive tag
              'color': '#FF0000',
              'createdAt': FieldValue.serverTimestamp(),
              'updatedAt': FieldValue.serverTimestamp(),
            });

        // Should be able to delete inactive tag
        await expectLater(
          firestore
              .collection('users')
              .doc(userId)
              .collection('tags')
              .doc(tagId)
              .delete(),
          completes,
        );
      });

      test('should allow deletion of active tags', () async {
        // Create an active tag
        final tagId = 'test_tag_${DateTime.now().millisecondsSinceEpoch}';
        await firestore
            .collection('users')
            .doc(userId)
            .collection('tags')
            .doc(tagId)
            .set({
              'id': tagId,
              'userId': userId,
              'name': 'Test Tag',
              'isActive': true, // Active tag
              'color': '#FF0000',
              'createdAt': FieldValue.serverTimestamp(),
              'updatedAt': FieldValue.serverTimestamp(),
            });

        // Should be able to delete tag (referential integrity handled by app logic)
        await expectLater(
          firestore
              .collection('users')
              .doc(userId)
              .collection('tags')
              .doc(tagId)
              .delete(),
          completes,
        );
      });
    });

    group('Transaction Reference Validation', () {
      test(
        'should allow creating transaction with any account reference',
        () async {
          final transactionId =
              'test_transaction_${DateTime.now().millisecondsSinceEpoch}';

          // Can create transaction with any account reference (validation handled by app logic)
          await expectLater(
            firestore
                .collection('users')
                .doc(userId)
                .collection('transactions')
                .doc(transactionId)
                .set({
                  'id': transactionId,
                  'userId': userId,
                  'type': 'expense',
                  'status': 'completed',
                  'amountCents': 1000,
                  'currencyCode': 'USD',
                  'fromAccountId':
                      'any_account_reference', // App logic validates references
                  'description': 'Test transaction',
                  'transactionDate': FieldValue.serverTimestamp(),
                  'createdAt': FieldValue.serverTimestamp(),
                  'updatedAt': FieldValue.serverTimestamp(),
                  'metadata': <String, dynamic>{},
                  'tags': <String>[],
                }),
            completes,
          );
        },
      );

      test(
        'should allow creating transaction with any category reference',
        () async {
          // First create a valid account
          final accountId =
              'test_account_${DateTime.now().millisecondsSinceEpoch}';
          await firestore
              .collection('users')
              .doc(userId)
              .collection('accounts')
              .doc(accountId)
              .set({
                'id': accountId,
                'userId': userId,
                'name': 'Test Account',
                'type': 'asset',
                'classification': 'checking',
                'currencyCode': 'USD',
                'initialBalanceCents': 0,
                'isActive': true,
                'isPrimary': false,
                'createdAt': FieldValue.serverTimestamp(),
                'updatedAt': FieldValue.serverTimestamp(),
                'metadata': <String, dynamic>{},
              });

          final transactionId =
              'test_transaction_${DateTime.now().millisecondsSinceEpoch}';

          // Can create transaction with any category reference (validation handled by app logic)
          await expectLater(
            firestore
                .collection('users')
                .doc(userId)
                .collection('transactions')
                .doc(transactionId)
                .set({
                  'id': transactionId,
                  'userId': userId,
                  'type': 'expense',
                  'status': 'completed',
                  'amountCents': 1000,
                  'currencyCode': 'USD',
                  'fromAccountId': accountId,
                  'categoryId':
                      'any_category_reference', // App logic validates references
                  'description': 'Test transaction',
                  'transactionDate': FieldValue.serverTimestamp(),
                  'createdAt': FieldValue.serverTimestamp(),
                  'updatedAt': FieldValue.serverTimestamp(),
                  'metadata': <String, dynamic>{},
                  'tags': <String>[],
                }),
            completes,
          );
        },
      );

      test('should allow creating transaction with valid references', () async {
        // Create valid account and category first
        final accountId =
            'test_account_${DateTime.now().millisecondsSinceEpoch}';
        final categoryId =
            'test_category_${DateTime.now().millisecondsSinceEpoch}';

        await firestore
            .collection('users')
            .doc(userId)
            .collection('accounts')
            .doc(accountId)
            .set({
              'id': accountId,
              'userId': userId,
              'name': 'Test Account',
              'type': 'asset',
              'classification': 'checking',
              'currencyCode': 'USD',
              'initialBalanceCents': 0,
              'isActive': true,
              'isPrimary': false,
              'createdAt': FieldValue.serverTimestamp(),
              'updatedAt': FieldValue.serverTimestamp(),
              'metadata': <String, dynamic>{},
            });

        await firestore
            .collection('users')
            .doc(userId)
            .collection('categories')
            .doc(categoryId)
            .set({
              'id': categoryId,
              'userId': userId,
              'name': 'Test Category',
              'type': 'expense',
              'isActive': true,
              'isSystem': false,
              'sortOrder': 1,
              'createdAt': FieldValue.serverTimestamp(),
              'updatedAt': FieldValue.serverTimestamp(),
            });

        final transactionId =
            'test_transaction_${DateTime.now().millisecondsSinceEpoch}';

        // Should be able to create transaction with valid references
        await expectLater(
          firestore
              .collection('users')
              .doc(userId)
              .collection('transactions')
              .doc(transactionId)
              .set({
                'id': transactionId,
                'userId': userId,
                'type': 'expense',
                'status': 'completed',
                'amountCents': 1000,
                'currencyCode': 'USD',
                'fromAccountId': accountId,
                'categoryId': categoryId,
                'description': 'Test transaction',
                'transactionDate': FieldValue.serverTimestamp(),
                'createdAt': FieldValue.serverTimestamp(),
                'updatedAt': FieldValue.serverTimestamp(),
                'metadata': <String, dynamic>{},
                'tags': <String>[],
              }),
          completes,
        );
      });
    });

    group('Budget Reference Validation', () {
      test('should allow creating budget with any category reference', () async {
        final budgetId = 'test_budget_${DateTime.now().millisecondsSinceEpoch}';

        // Can create budget with any category reference (validation handled by app logic)
        await expectLater(
          firestore
              .collection('users')
              .doc(userId)
              .collection('budgets')
              .doc(budgetId)
              .set({
                'id': budgetId,
                'userId': userId,
                'name': 'Test Budget',
                'period': 'monthly',
                'status': 'active',
                'targetAmountCents': 50000,
                'currencyCode': 'USD',
                'categoryId':
                    'any_category_reference', // App logic validates references
                'startDate': FieldValue.serverTimestamp(),
                'endDate': Timestamp.fromDate(
                  DateTime.now().add(const Duration(days: 30)),
                ),
                'isRecurring': false,
                'alertsEnabled': true,
                'alertThresholdPercent': 80,
                'createdAt': FieldValue.serverTimestamp(),
                'updatedAt': FieldValue.serverTimestamp(),
              }),
          completes,
        );
      });

      test(
        'should allow creating budget with valid category reference',
        () async {
          // Create valid category first
          final categoryId =
              'test_category_${DateTime.now().millisecondsSinceEpoch}';
          await firestore
              .collection('users')
              .doc(userId)
              .collection('categories')
              .doc(categoryId)
              .set({
                'id': categoryId,
                'userId': userId,
                'name': 'Test Category',
                'type': 'expense',
                'isActive': true,
                'isSystem': false,
                'sortOrder': 1,
                'createdAt': FieldValue.serverTimestamp(),
                'updatedAt': FieldValue.serverTimestamp(),
              });

          final budgetId =
              'test_budget_${DateTime.now().millisecondsSinceEpoch}';

          // Should be able to create budget with valid category reference
          await expectLater(
            firestore
                .collection('users')
                .doc(userId)
                .collection('budgets')
                .doc(budgetId)
                .set({
                  'id': budgetId,
                  'userId': userId,
                  'name': 'Test Budget',
                  'period': 'monthly',
                  'status': 'active',
                  'targetAmountCents': 50000,
                  'currencyCode': 'USD',
                  'categoryId': categoryId,
                  'startDate': FieldValue.serverTimestamp(),
                  'endDate': Timestamp.fromDate(
                    DateTime.now().add(const Duration(days: 30)),
                  ),
                  'isRecurring': false,
                  'alertsEnabled': true,
                  'alertThresholdPercent': 80,
                  'createdAt': FieldValue.serverTimestamp(),
                  'updatedAt': FieldValue.serverTimestamp(),
                }),
            completes,
          );
        },
      );
    });
  });
}
