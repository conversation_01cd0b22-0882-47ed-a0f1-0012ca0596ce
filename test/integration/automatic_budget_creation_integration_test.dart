import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/models/category.dart';

import 'package:budapp/data/repositories/implementations/account_repository_impl.dart';
import 'package:budapp/data/repositories/implementations/budget_repository_impl.dart';
import 'package:budapp/data/repositories/implementations/category_repository_impl.dart';
import 'package:budapp/data/repositories/implementations/transaction_repository_impl.dart';
import 'package:budapp/features/budgets/services/budget_transaction_service.dart';
import 'package:budapp/services/cache_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:budapp/services/performance_rollout_service.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock Firebase Auth for testing
class MockFirebaseAuth extends Mock implements FirebaseAuth {}

class MockUser extends Mock implements User {}

class MockCacheService extends Mock implements CacheService {}

class MockPerformanceRolloutService extends Mock
    implements PerformanceRolloutService {}

void main() {
  group('Automatic Budget Creation Integration Tests', () {
    late FakeFirebaseFirestore fakeFirestore;
    late FirestoreService firestoreService;
    late TransactionRepositoryImpl transactionRepository;
    late BudgetRepositoryImpl budgetRepository;
    late AccountRepositoryImpl accountRepository;
    late CategoryRepositoryImpl categoryRepository;
    late BudgetTransactionService budgetTransactionService;
    late MockFirebaseAuth mockFirebaseAuth;
    late MockUser mockUser;

    const testUserId = 'test-user-123';
    const testAccountId = 'test-account-123';
    const testCategoryId = 'test-category-123';

    setUp(() async {
      fakeFirestore = FakeFirebaseFirestore();
      firestoreService = FirestoreService(fakeFirestore);
      mockFirebaseAuth = MockFirebaseAuth();
      mockUser = MockUser();

      // Setup mock auth
      when(() => mockUser.uid).thenReturn(testUserId);
      when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);

      budgetRepository = BudgetRepositoryImpl(
        firestoreService: firestoreService,
        firebaseAuth: mockFirebaseAuth,
      );
      budgetTransactionService = BudgetTransactionService(
        budgetRepository,
        firestoreService,
      );
      transactionRepository = TransactionRepositoryImpl(
        firestoreService,
        budgetTransactionService,
      );
      final mockCacheService = MockCacheService();
      final mockPerformanceRolloutService = MockPerformanceRolloutService();
      accountRepository = AccountRepositoryImpl(
        firestoreService,
        mockFirebaseAuth,
        mockCacheService,
        mockPerformanceRolloutService,
      );
      categoryRepository = CategoryRepositoryImpl(
        firestoreService,
        firebaseAuth: mockFirebaseAuth,
      );

      // Create test account
      final testAccount = Account(
        id: testAccountId,
        userId: testUserId,
        name: 'Test Account',
        type: AccountType.checking,
        classification: AccountClassification.asset,

        initialBalanceCents: 100000, // $1000
        currentBalanceCents: 100000,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      await accountRepository.create(testAccount);

      // Create test category
      final testCategory = Category(
        id: testCategoryId,
        userId: testUserId,
        name: 'Test Category',
        type: CategoryType.expense,
        color: '#FF5722',
        icon: 'shopping_cart',
        isActive: true,
        sortOrder: 0,
        schemaVersion: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      await categoryRepository.createCategory(testCategory);
    });

    test(
      'should automatically create budget when transaction is created for new category',
      () async {
        // Verify no budgets exist initially
        final initialBudgets = await budgetRepository.getAllBudgets();
        expect(initialBudgets, isEmpty);

        // Create a transaction (removed unused variable)

        // Create the transaction (this should trigger automatic budget creation)
        await transactionRepository.createExpenseTransaction(
          userId: testUserId,
          fromAccountId: testAccountId,
          amountCents: 2500, // $25.00

          transactionDate: DateTime.now(),
          categoryId: testCategoryId,
          description: 'Test expense for budget creation',
          notes: 'Integration test transaction',
        );

        // Verify budgets were created (category budget + total budget)
        final budgets = await budgetRepository.getAllBudgets();

        // Verify both category and total budgets were automatically created
        expect(budgets, hasLength(2));

        // Find the category budget
        final categoryBudget = budgets.firstWhere(
          (budget) => budget.categoryId == testCategoryId,
        );
        expect(categoryBudget.userId, equals(testUserId));
        expect(categoryBudget.categoryId, equals(testCategoryId));
        expect(categoryBudget.type, equals(BudgetType.expense));

        // Find the total budget
        final totalBudget = budgets.firstWhere(
          (budget) => budget.categoryId == null,
        );
        expect(totalBudget.userId, equals(testUserId));
        expect(totalBudget.categoryId, isNull);
        expect(totalBudget.type, equals(BudgetType.expense));

        // Verify category budget properties
        expect(
          categoryBudget.plannedAmountCents,
          equals(0),
        ); // Default planned amount
        expect(
          categoryBudget.currentAmountCents,
          equals(2500),
        ); // Updated by transaction
        expect(categoryBudget.period, equals(BudgetPeriod.monthly));
        expect(categoryBudget.isActive, isTrue);

        // Verify total budget properties
        expect(
          totalBudget.plannedAmountCents,
          equals(0),
        ); // Default planned amount
        expect(
          totalBudget.currentAmountCents,
          equals(2500),
        ); // Updated by transaction
        expect(totalBudget.period, equals(BudgetPeriod.monthly));
        expect(totalBudget.isActive, isTrue);
      },
    );

    test(
      'should automatically create income budget when income transaction is created',
      () async {
        // Verify no budgets exist initially
        final initialBudgets = await budgetRepository.getAllBudgets();
        expect(initialBudgets, isEmpty);

        // Create the transaction (this should trigger automatic budget creation)
        await transactionRepository.createIncomeTransaction(
          userId: testUserId,
          toAccountId: testAccountId,
          amountCents: 10000, // $100

          transactionDate: DateTime.now(),
          categoryId: testCategoryId,
          description: 'Test income for budget creation',
          notes: 'Integration test income transaction',
        );

        // Verify income budgets were automatically created (category budget + total budget)
        final budgets = await budgetRepository.getAllBudgets();
        expect(budgets, hasLength(2));

        // Find the category budget
        final categoryBudget = budgets.firstWhere(
          (budget) => budget.categoryId == testCategoryId,
        );
        expect(categoryBudget.userId, equals(testUserId));
        expect(categoryBudget.categoryId, equals(testCategoryId));
        expect(categoryBudget.type, equals(BudgetType.income));

        // Find the total budget
        final totalBudget = budgets.firstWhere(
          (budget) => budget.categoryId == null,
        );
        expect(totalBudget.userId, equals(testUserId));
        expect(totalBudget.categoryId, isNull);
        expect(totalBudget.type, equals(BudgetType.income));

        // Verify category budget properties
        expect(
          categoryBudget.plannedAmountCents,
          equals(0),
        ); // Default planned amount
        expect(
          categoryBudget.currentAmountCents,
          equals(10000),
        ); // Updated by transaction
        expect(categoryBudget.period, equals(BudgetPeriod.monthly));
        expect(categoryBudget.isActive, isTrue);

        // Verify total budget properties
        expect(
          totalBudget.plannedAmountCents,
          equals(0),
        ); // Default planned amount
        expect(
          totalBudget.currentAmountCents,
          equals(10000),
        ); // Updated by transaction
        expect(totalBudget.period, equals(BudgetPeriod.monthly));
        expect(totalBudget.isActive, isTrue);
      },
    );

    test(
      'should not create duplicate budgets for same category and period',
      () async {
        // Create first transaction
        await transactionRepository.createExpenseTransaction(
          userId: testUserId,
          fromAccountId: testAccountId,
          amountCents: 3000, // $30

          transactionDate: DateTime.now(),
          categoryId: testCategoryId,
          description: 'First expense',
        );

        // Verify budgets were created (category + total)
        final budgetsAfterFirst = await budgetRepository.getAllBudgets();
        expect(budgetsAfterFirst, hasLength(2));

        final categoryBudgetFirst = budgetsAfterFirst.firstWhere(
          (budget) => budget.categoryId == testCategoryId,
        );
        final totalBudgetFirst = budgetsAfterFirst.firstWhere(
          (budget) => budget.categoryId == null,
        );

        expect(categoryBudgetFirst.currentAmountCents, equals(3000));
        expect(totalBudgetFirst.currentAmountCents, equals(3000));

        // Create second transaction for same category
        await transactionRepository.createExpenseTransaction(
          userId: testUserId,
          fromAccountId: testAccountId,
          amountCents: 2000, // $20

          transactionDate: DateTime.now(),
          categoryId: testCategoryId,
          description: 'Second expense',
        );

        // Verify still only two budgets exist, but amounts are updated
        final budgetsAfterSecond = await budgetRepository.getAllBudgets();
        expect(budgetsAfterSecond, hasLength(2));

        final categoryBudgetSecond = budgetsAfterSecond.firstWhere(
          (budget) => budget.categoryId == testCategoryId,
        );
        final totalBudgetSecond = budgetsAfterSecond.firstWhere(
          (budget) => budget.categoryId == null,
        );

        expect(
          categoryBudgetSecond.currentAmountCents,
          equals(5000),
        ); // $30 + $20
        expect(totalBudgetSecond.currentAmountCents, equals(5000)); // $30 + $20
      },
    );

    test(
      'should verify budget is created in correct Firebase collection structure',
      () async {
        // Create a transaction
        await transactionRepository.createExpenseTransaction(
          userId: testUserId,
          fromAccountId: testAccountId,
          amountCents: 7500, // $75

          transactionDate: DateTime.now(),
          categoryId: testCategoryId,
          description: 'Test expense for Firebase structure',
        );

        // Verify budget exists in correct Firebase path
        final budgetSnapshot = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('budgets')
            .get();

        expect(budgetSnapshot.docs, hasLength(2));

        // Find category budget document
        final categoryBudgetDoc = budgetSnapshot.docs.firstWhere(
          (doc) => doc.data()['categoryId'] == testCategoryId,
        );
        final categoryBudgetData = categoryBudgetDoc.data();
        expect(categoryBudgetData['userId'], equals(testUserId));
        expect(categoryBudgetData['categoryId'], equals(testCategoryId));
        expect(categoryBudgetData['type'], equals('expense'));
        expect(categoryBudgetData['plannedAmountCents'], equals(0));
        expect(categoryBudgetData['currentAmountCents'], equals(7500));
        // Currency code removed - using global currency preference

        // Find total budget document
        final totalBudgetDoc = budgetSnapshot.docs.firstWhere(
          (doc) => doc.data()['categoryId'] == null,
        );
        final totalBudgetData = totalBudgetDoc.data();
        expect(totalBudgetData['userId'], equals(testUserId));
        expect(totalBudgetData['categoryId'], isNull);
        expect(totalBudgetData['type'], equals('expense'));
        expect(totalBudgetData['plannedAmountCents'], equals(0));
        expect(totalBudgetData['currentAmountCents'], equals(7500));
        // Currency code removed - using global currency preference

        // Verify common properties for both budgets
        expect(categoryBudgetData['period'], equals('monthly'));
        expect(categoryBudgetData['isActive'], isTrue);
        expect(totalBudgetData['period'], equals('monthly'));
        expect(totalBudgetData['isActive'], isTrue);
      },
    );
  });
}
