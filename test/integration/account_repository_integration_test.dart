import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/repositories/implementations/account_repository_impl.dart';
import 'package:budapp/services/cache_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:budapp/services/performance_rollout_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../helpers/firebase_test_setup.dart';

// Mock classes for testing
class MockCacheService extends Mock implements CacheService {}

class MockPerformanceRolloutService extends Mock
    implements PerformanceRolloutService {}

void main() {
  group('AccountRepository Firestore Integration', () {
    late FirebaseTestSetup testSetup;
    late FirestoreService firestoreService;
    late AccountRepositoryImpl accountRepository;

    const testUserId = 'test-user-123';
    const testAccountId = 'test-account-123';

    setUp(() async {
      // Create Firebase test setup with authenticated user
      testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
        uid: testUserId,
        email: '<EMAIL>',
      );

      firestoreService = FirestoreService(testSetup.firestore);
      final mockCacheService = MockCacheService();
      final mockPerformanceRolloutService = MockPerformanceRolloutService();
      accountRepository = AccountRepositoryImpl(
        firestoreService,
        testSetup.auth,
        mockCacheService,
        mockPerformanceRolloutService,
      );
    });

    tearDown(() async {
      await testSetup.dispose();
    });

    test('create account stores data in correct Firestore structure', () async {
      // Arrange
      final account = Account(
        id: testAccountId,
        userId: testUserId,
        name: 'Test Checking Account',
        type: AccountType.checking,
        classification: AccountClassification.asset,
        initialBalanceCents: 100000,
        currentBalanceCents: 100000,

        isPrimary: true,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        metadata: {},
      );

      // Act
      final createdId = await accountRepository.create(account);

      // Assert
      expect(createdId, equals(testAccountId));

      // Verify data is stored in correct Firestore path
      final docSnapshot = await testSetup.firestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId)
          .get();

      expect(docSnapshot.exists, isTrue);
      final data = docSnapshot.data()!;
      expect(data['id'], equals(testAccountId));
      expect(data['userId'], equals(testUserId));
      expect(data['name'], equals('Test Checking Account'));
      expect(data['type'], equals('checking'));
      expect(data['classification'], equals('asset'));
    });

    test(
      'getAccountById retrieves account from correct Firestore path',
      () async {
        // Arrange - Create account data directly in Firestore
        final now = DateTime.now();
        final accountData = {
          'id': testAccountId,
          'userId': testUserId,
          'name': 'Test Savings Account',
          'type': 'savings',
          'classification': 'asset',
          'initialBalanceCents': 250000,
          'currencyCode': 'USD',
          'isPrimary': false,
          'isActive': true,
          'createdAt': now.toIso8601String(),
          'updatedAt': now.toIso8601String(),
          'metadata': <String, dynamic>{},
        };

        await testSetup.firestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testAccountId)
            .set(accountData);

        // Act
        final retrievedAccount = await accountRepository.getAccountById(
          testUserId,
          testAccountId,
        );

        // Assert
        expect(retrievedAccount, isNotNull);
        expect(retrievedAccount!.id, equals(testAccountId));
        expect(retrievedAccount.userId, equals(testUserId));
        expect(retrievedAccount.name, equals('Test Savings Account'));
        expect(retrievedAccount.type, equals(AccountType.savings));
        expect(
          retrievedAccount.classification,
          equals(AccountClassification.asset),
        );
        expect(retrievedAccount.initialBalanceCents, equals(250000));
      },
    );

    test('update account modifies data in Firestore', () async {
      // Arrange - Create initial account
      final originalAccount = Account(
        id: testAccountId,
        userId: testUserId,
        name: 'Original Account Name',
        type: AccountType.checking,
        classification: AccountClassification.asset,
        initialBalanceCents: 100000,
        currentBalanceCents: 100000,

        isPrimary: false,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        metadata: {},
      );

      await accountRepository.create(originalAccount);

      // Act - Update the account
      final updatedAccount = originalAccount.copyWith(
        name: 'Updated Account Name',
        isPrimary: true,
        updatedAt: DateTime.now(),
      );

      await accountRepository.update(testAccountId, updatedAccount);

      // Assert - Verify changes in Firestore
      final docSnapshot = await testSetup.firestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId)
          .get();

      expect(docSnapshot.exists, isTrue);
      final data = docSnapshot.data()!;
      expect(data['name'], equals('Updated Account Name'));
      expect(data['isPrimary'], equals(true));
      expect(data['id'], equals(testAccountId)); // Should remain unchanged
    });

    test(
      'accountExists returns correct boolean for existing/non-existing accounts',
      () async {
        // Arrange - Create one account
        final account = Account(
          id: testAccountId,
          userId: testUserId,
          name: 'Test Account',
          type: AccountType.cash,
          classification: AccountClassification.asset,
          initialBalanceCents: 50000,
          currentBalanceCents: 50000,

          isPrimary: false,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          metadata: {},
        );

        await accountRepository.create(account);

        // Act & Assert
        final existsResult = await accountRepository.accountExists(
          testUserId,
          testAccountId,
        );
        expect(existsResult, isTrue);

        final notExistsResult = await accountRepository.accountExists(
          testUserId,
          'non-existent-account',
        );
        expect(notExistsResult, isFalse);
      },
    );

    test('delete account removes data from Firestore', () async {
      // Arrange - Create account
      final account = Account(
        id: testAccountId,
        userId: testUserId,
        name: 'Account to Delete',
        type: AccountType.checking,
        classification: AccountClassification.asset,
        initialBalanceCents: 0,
        currentBalanceCents: 0,

        isPrimary: false,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        metadata: {},
      );

      await accountRepository.create(account);

      // Verify account exists
      expect(
        await accountRepository.accountExists(testUserId, testAccountId),
        isTrue,
      );

      // Act - Delete account
      await accountRepository.deleteAccountForUser(testUserId, testAccountId);

      // Assert - Verify account is deleted
      expect(
        await accountRepository.accountExists(testUserId, testAccountId),
        isFalse,
      );

      final docSnapshot = await testSetup.firestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId)
          .get();

      expect(docSnapshot.exists, isFalse);
    });

    test('getAllAccounts returns user-specific accounts only', () async {
      // Arrange - Create accounts for different users
      final user1Account = Account(
        id: 'user1-account',
        userId: testUserId,
        name: 'User 1 Account',
        type: AccountType.checking,
        classification: AccountClassification.asset,
        initialBalanceCents: 100000,
        currentBalanceCents: 100000,

        isPrimary: true,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        metadata: {},
      );

      final user2Account = Account(
        id: 'user2-account',
        userId: 'different-user-id',
        name: 'User 2 Account',
        type: AccountType.savings,
        classification: AccountClassification.asset,
        initialBalanceCents: 200000,
        currentBalanceCents: 200000,

        isPrimary: false,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        metadata: {},
      );

      await accountRepository.create(user1Account);

      // Create user2's account directly in different user collection
      await testSetup.firestore
          .collection('users')
          .doc('different-user-id')
          .collection('accounts')
          .doc('user2-account')
          .set(user2Account.toJson());

      // Act - Get accounts for user1
      final user1Accounts = await accountRepository.getAccountsByUserId(
        testUserId,
      );

      // Assert - Should only return user1's account
      expect(user1Accounts.length, equals(1));
      expect(user1Accounts.first.id, equals('user1-account'));
      expect(user1Accounts.first.userId, equals(testUserId));
      expect(user1Accounts.first.name, equals('User 1 Account'));
    });

    test(
      'repository handles Firestore subcollection structure correctly',
      () async {
        // This test verifies the correct Firestore path structure: users/{userId}/accounts/{accountId}

        // Arrange
        final account = Account(
          id: 'path-test-account',
          userId: 'path-test-user',
          name: 'Path Test Account',
          type: AccountType.investment,
          classification: AccountClassification.asset,
          initialBalanceCents: 500000,
          currentBalanceCents: 500000,

          isPrimary: false,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          metadata: {'testKey': 'testValue'},
        );

        // Act
        await accountRepository.create(account);

        // Assert - Verify correct Firestore path structure
        final userDoc = await testSetup.firestore
            .collection('users')
            .doc('path-test-user')
            .get();
        expect(
          userDoc.exists,
          isFalse,
        ); // User document itself should not exist

        final accountDoc = await testSetup.firestore
            .collection('users')
            .doc('path-test-user')
            .collection('accounts')
            .doc('path-test-account')
            .get();

        expect(accountDoc.exists, isTrue);
        expect(accountDoc.data()!['userId'], equals('path-test-user'));

        // Verify subcollection is properly isolated
        final accountsCollection = await testSetup.firestore
            .collection('users')
            .doc('path-test-user')
            .collection('accounts')
            .get();

        expect(accountsCollection.docs.length, equals(1));
        expect(accountsCollection.docs.first.id, equals('path-test-account'));
      },
    );
  });
}
