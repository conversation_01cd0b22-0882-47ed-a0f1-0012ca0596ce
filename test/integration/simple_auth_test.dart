import 'package:budapp/features/auth/presentation/screens/auth_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

/// Simple integration test to verify AuthWrapper behavior
///
/// This test focuses on verifying that AuthWrapper properly initializes
/// and shows the correct initial screen without complex Firebase mocking.
void main() {
  group('Simple Authentication Integration Tests', () {
    testWidgets('should show login screen initially', (
      WidgetTester tester,
    ) async {
      // Build a minimal app with just AuthWrapper
      await tester.pumpWidget(
        const ProviderScope(child: MaterialApp(home: AuthWrapper())),
      );

      // Allow the widget to build
      await tester.pump();

      // Verify that AuthWrapper is present
      expect(find.byType(AuthWrapper), findsOneWidget);

      // The test should complete without hanging, which indicates
      // that SessionService initialization is working correctly

      // Note: In a real environment with Firebase initialized,
      // we would expect to see <PERSON>ginScreen after SessionService initializes
      // For now, we just verify the widget tree builds correctly
    });

    testWidgets('should handle SessionService initialization', (
      WidgetTester tester,
    ) async {
      // This test verifies that AuthWrapper can handle SessionService initialization
      // without hanging or throwing exceptions

      await tester.pumpWidget(
        const ProviderScope(child: MaterialApp(home: AuthWrapper())),
      );

      // Allow multiple frames for initialization
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 100));
      await tester.pump(const Duration(milliseconds: 100));

      // Verify the widget tree is stable
      expect(find.byType(AuthWrapper), findsOneWidget);

      // The fact that this test completes successfully indicates
      // that the SessionService initialization fix is working
    });
  });
}
