import 'package:budapp/features/settings/presentation/screens/currency_settings_screen.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/services/currency_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../helpers/test_wrapper.dart';

// Mock classes
class MockCurrencyPreferenceNotifier extends StateNotifier<String>
    implements CurrencyPreferenceNotifier {
  MockCurrencyPreferenceNotifier(super.initialCurrency);

  bool _shouldSucceed = true;
  bool _shouldThrow = false;

  void setMockBehavior({bool shouldSucceed = true, bool shouldThrow = false}) {
    _shouldSucceed = shouldSucceed;
    _shouldThrow = shouldThrow;
  }

  @override
  Future<bool> setCurrency(String currencyCode) async {
    if (_shouldThrow) {
      throw Exception('Mock error');
    }
    if (_shouldSucceed) {
      state = currencyCode;
      return true;
    }
    return false;
  }

  @override
  Future<bool> resetToDefault() async {
    if (_shouldSucceed) {
      state = CurrencyService.defaultCurrencyCode;
      return true;
    }
    return false;
  }

  @override
  String getCurrentCurrencySymbol() {
    return CurrencyService.getCurrencySymbol(state);
  }

  @override
  String getCurrentCurrencyDisplayName() {
    return CurrencyService.getCurrencyDisplayName(state);
  }
}

void main() {
  group('CurrencySettingsScreen Tests', () {
    late MockCurrencyPreferenceNotifier mockNotifier;

    setUp(() {
      // Initialize SharedPreferences with mock values
      SharedPreferences.setMockInitialValues({
        'user_currency_preference': 'USD',
      });

      mockNotifier = MockCurrencyPreferenceNotifier('USD');
    });

    testWidgets('should display currency settings screen correctly', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const CurrencySettingsScreen(),
          overrides: [
            currencyPreferenceProvider.overrideWith((ref) => mockNotifier),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify main elements are present
      expect(find.text('Currency Settings'), findsOneWidget);
      expect(find.text('Select Currency'), findsOneWidget);
      expect(find.text('Available Currencies'), findsOneWidget);
      expect(find.text('Preview'), findsOneWidget);
    });

    testWidgets('should display currency preview section', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const CurrencySettingsScreen(),
          overrides: [
            currencyPreferenceProvider.overrideWith((ref) => mockNotifier),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify preview section shows sample amounts
      expect(find.text('Income'), findsOneWidget);
      expect(find.text('Expense'), findsOneWidget);
      expect(find.text('Balance'), findsOneWidget);
      expect(find.byIcon(Icons.preview_outlined), findsOneWidget);
    });

    testWidgets('should display supported currencies list', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const CurrencySettingsScreen(),
          overrides: [
            currencyPreferenceProvider.overrideWith((ref) => mockNotifier),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify currency list is displayed
      expect(find.text('USD'), findsOneWidget);
      expect(find.text('EUR'), findsOneWidget);
      expect(find.text('GBP'), findsOneWidget);
      expect(
        find.text('Current'),
        findsOneWidget,
      ); // Current currency indicator
    });

    testWidgets('should select currency when tapped', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const CurrencySettingsScreen(),
          overrides: [
            currencyPreferenceProvider.overrideWith((ref) => mockNotifier),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap on EUR currency
      final eurTile = find.ancestor(
        of: find.text('EUR'),
        matching: find.byType(ListTile),
      );
      await tester.tap(eurTile);
      await tester.pumpAndSettle();

      // Verify selection indicator appears
      expect(find.byIcon(Icons.check_circle), findsOneWidget);
    });

    testWidgets('should show save button when currency selection changes', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const CurrencySettingsScreen(),
          overrides: [
            currencyPreferenceProvider.overrideWith((ref) => mockNotifier),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Initially no save button should be visible
      expect(find.text('Save'), findsNothing);

      // Tap on different currency
      final eurTile = find.ancestor(
        of: find.text('EUR'),
        matching: find.byType(ListTile),
      );
      await tester.tap(eurTile);
      await tester.pumpAndSettle();

      // Save button should now be visible
      expect(find.text('Save'), findsOneWidget);
    });

    testWidgets('should save currency preference and show success message', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const CurrencySettingsScreen(),
          overrides: [
            currencyPreferenceProvider.overrideWith((ref) => mockNotifier),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Select different currency
      final eurTile = find.ancestor(
        of: find.text('EUR'),
        matching: find.byType(ListTile),
      );
      await tester.tap(eurTile);
      await tester.pumpAndSettle();

      // Save button should be visible when different currency is selected
      expect(find.text('Save'), findsOneWidget);

      // Currency state hasn't changed yet (only after save)
      expect(mockNotifier.state, equals('USD'));
    });

    testWidgets('should display currency symbols and names correctly', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const CurrencySettingsScreen(),
          overrides: [
            currencyPreferenceProvider.overrideWith((ref) => mockNotifier),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify currency symbols are displayed
      expect(find.text(r'$'), findsOneWidget); // USD symbol
      expect(find.text('€'), findsOneWidget); // EUR symbol
      expect(find.text('£'), findsOneWidget); // GBP symbol

      // Verify currency codes are displayed
      expect(find.text('USD'), findsOneWidget);
      expect(find.text('EUR'), findsOneWidget);
      expect(find.text('GBP'), findsOneWidget);
    });
  });
}
