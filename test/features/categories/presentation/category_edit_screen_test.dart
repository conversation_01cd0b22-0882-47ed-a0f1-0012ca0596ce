import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/categories/presentation/screens/category_edit_screen.dart';
import 'package:budapp/providers/providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/mock_data_factory.dart';
import '../../../helpers/mock_providers.dart' as mp;
import '../../../helpers/test_wrapper.dart';

void main() {
  group('CategoryEditScreen Tests', () {
    late mp.MockCategoryRepository mockCategoryRepository;
    late mp.MockAuthService mockAuthService;
    late MockUser mockUser;

    setUpAll(() {
      registerFallbackValue(
        Category(
          id: 'test-id',
          userId: 'test-user',
          name: 'Test Category',
          type: CategoryType.expense,
          parentId: null,
          description: 'Test description',
          color: '#FF0000',
          icon: 'category',
          isActive: true,
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          metadata: {},
        ),
      );
    });

    setUp(() {
      mockCategoryRepository = mp.MockCategoryRepository();
      mockAuthService = mp.MockAuthService();
      mockUser = MockUser();

      // Setup default mock behaviors for authentication
      when(() => mockUser.uid).thenReturn('test-user-123');
      when(() => mockUser.email).thenReturn('<EMAIL>');
      when(() => mockUser.emailVerified).thenReturn(true);
      when(() => mockAuthService.currentUser).thenReturn(mockUser);
    });

    Widget buildTestWidget(String categoryId, {List<Override>? overrides}) {
      return TestWrapper.createCategoryTestWidgetWithRouter(
        CategoryEditScreen(categoryId: categoryId),
        initialLocation: '/categories/edit/$categoryId',
        overrides: [
          categoryRepositoryProvider.overrideWithValue(mockCategoryRepository),
          authServiceProvider.overrideWithValue(mockAuthService),
          currentUserProvider.overrideWithValue(mockUser),
          ...?overrides,
        ],
      );
    }

    testWidgets('should show loading state initially', (tester) async {
      when(
        () => mockCategoryRepository.watchCategoryForUser(any(), any()),
      ).thenAnswer((_) => Stream.value(null));

      await tester.pumpWidget(buildTestWidget('test-category-id'));

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Edit Category'), findsOneWidget);
    });

    testWidgets('should show error when category not found', (tester) async {
      when(
        () => mockCategoryRepository.watchCategoryForUser(any(), any()),
      ).thenAnswer((_) => Stream.value(null));

      await tester.pumpWidget(buildTestWidget('non-existent-id'));
      await tester.pumpAndSettle();

      expect(find.text('Category Not Found'), findsOneWidget);
      expect(
        find.text('The requested category could not be found.'),
        findsOneWidget,
      );
    });

    testWidgets('should display edit form with existing category data', (
      tester,
    ) async {
      final testCategory = MockDataFactory.createCategory(
        id: 'test-category-id',
        name: 'Existing Category',
        type: CategoryType.expense,
        description: 'Existing description',
      );

      when(
        () => mockCategoryRepository.watchCategoryForUser(any(), any()),
      ).thenAnswer((_) => Stream.value(testCategory));

      await tester.pumpWidget(buildTestWidget('test-category-id'));
      await tester.pumpAndSettle();

      // Verify form elements are present with existing data
      expect(find.text('Edit Category'), findsOneWidget);

      // With Material 3 floating labels, check for form fields instead of static text
      expect(
        find.byType(TextFormField),
        findsAtLeastNWidgets(2),
      ); // Name and Description fields
      expect(
        find.byType(DropdownButtonFormField<CategoryType>),
        findsOneWidget,
      );

      // Check that existing values are populated in the form fields
      expect(find.text('Existing Category'), findsOneWidget);
      expect(find.text('Existing description'), findsOneWidget);
    });

    testWidgets('should show validation error for empty name', (tester) async {
      final testCategory = MockDataFactory.createCategory(
        id: 'test-category-id',
        name: 'Existing Category',
        type: CategoryType.expense,
      );

      when(
        () => mockCategoryRepository.watchCategoryForUser(any(), any()),
      ).thenAnswer((_) => Stream.value(testCategory));

      await tester.pumpWidget(buildTestWidget('test-category-id'));
      await tester.pumpAndSettle();

      // Clear the name field
      final nameField = find.byType(TextFormField).first;
      await tester.enterText(nameField, '');
      await tester.pumpAndSettle();

      // Scroll down to make submit button visible
      await tester.drag(find.byType(Scrollable).first, const Offset(0, -300));
      await tester.pumpAndSettle();

      // Try to submit
      final submitButton = find.widgetWithText(ElevatedButton, 'Save').last;
      await tester.tap(submitButton);
      await tester.pumpAndSettle();

      // Should show validation error(s) - there may be multiple required fields
      expect(find.text('Category name is required'), findsAtLeastNWidgets(1));
    });

    testWidgets('should update category with valid data', (tester) async {
      final testCategory = MockDataFactory.createCategory(
        id: 'test-category-id',
        name: 'Original Category',
        type: CategoryType.expense,
      );

      when(
        () => mockCategoryRepository.watchCategoryForUser(any(), any()),
      ).thenAnswer((_) => Stream.value(testCategory));
      when(
        () => mockCategoryRepository.updateCategory(any(), any()),
      ).thenAnswer((_) async {});

      await tester.pumpWidget(buildTestWidget('test-category-id'));
      await tester.pumpAndSettle();

      // Update the name
      final nameField = find.byType(TextFormField).first;
      await tester.enterText(nameField, 'Updated Category');
      await tester.pumpAndSettle();

      // Scroll down to make submit button visible
      await tester.drag(find.byType(Scrollable).first, const Offset(0, -300));
      await tester.pumpAndSettle();

      // Submit form
      final submitButton = find.widgetWithText(ElevatedButton, 'Save').last;
      await tester.tap(submitButton);
      await tester.pumpAndSettle();

      // Verify repository method was called
      verify(
        () => mockCategoryRepository.updateCategory(any(), any()),
      ).called(1);
    });

    testWidgets('should show delete button for existing category', (
      tester,
    ) async {
      final testCategory = MockDataFactory.createCategory(
        id: 'test-category-id',
        name: 'Category to Delete',
        type: CategoryType.expense,
      );

      when(
        () => mockCategoryRepository.watchCategoryForUser(any(), any()),
      ).thenAnswer((_) => Stream.value(testCategory));

      await tester.pumpWidget(buildTestWidget('test-category-id'));
      await tester.pumpAndSettle();

      // Should show delete button in AppBar
      expect(find.byIcon(Icons.delete_outline), findsOneWidget);
    });

    testWidgets('should handle delete action', (tester) async {
      final testCategory = MockDataFactory.createCategory(
        id: 'test-category-id',
        name: 'Category to Delete',
        type: CategoryType.expense,
      );

      when(
        () => mockCategoryRepository.watchCategoryForUser(any(), any()),
      ).thenAnswer((_) => Stream.value(testCategory));
      when(
        () => mockCategoryRepository.deactivateCategory(any()),
      ).thenAnswer((_) async {});

      await tester.pumpWidget(buildTestWidget('test-category-id'));
      await tester.pumpAndSettle();

      // Tap delete button in AppBar
      final deleteButton = find.byIcon(Icons.delete_outline);
      await tester.tap(deleteButton);
      await tester.pumpAndSettle();

      // Should show confirmation dialog
      expect(find.text('Confirm Delete'), findsOneWidget);

      // Confirm deletion
      final confirmButton = find.text('Delete').last;
      await tester.tap(confirmButton);
      await tester.pumpAndSettle();

      // Verify repository method was called
      verify(() => mockCategoryRepository.deactivateCategory(any())).called(1);
    });

    testWidgets('should handle subcategory editing', (tester) async {
      final parentCategory = MockDataFactory.createCategory(
        id: 'parent-id',
        name: 'Parent Category',
        type: CategoryType.expense,
        parentId: null, // Root category
      );

      final testSubcategory = MockDataFactory.createCategory(
        id: 'test-subcategory-id',
        name: 'Test Subcategory',
        type: CategoryType.expense,
        parentId: 'parent-id',
      );

      when(
        () => mockCategoryRepository.watchCategoryForUser(any(), any()),
      ).thenAnswer((_) => Stream.value(testSubcategory));

      // Mock getActiveCategories to return the parent category for the dropdown
      when(
        () => mockCategoryRepository.getActiveCategories(),
      ).thenAnswer((_) async => [parentCategory]);

      await tester.pumpWidget(buildTestWidget('test-subcategory-id'));
      await tester.pumpAndSettle();

      // Should show subcategory edit form
      expect(find.text('Edit Subcategory'), findsOneWidget);
      expect(find.text('Parent Category'), findsOneWidget);
    });

    testWidgets('should validate description length limit', (tester) async {
      final testCategory = MockDataFactory.createCategory(
        id: 'test-category-id',
        name: 'Test Category',
        type: CategoryType.expense,
      );

      when(
        () => mockCategoryRepository.watchCategoryForUser(any(), any()),
      ).thenAnswer((_) => Stream.value(testCategory));

      await tester.pumpWidget(buildTestWidget('test-category-id'));
      await tester.pumpAndSettle();

      // Enter description that's too long
      final descriptionField = find.byType(TextFormField).last;
      await tester.enterText(descriptionField, 'A' * 501); // 501 characters
      await tester.pumpAndSettle();

      // Scroll down to make submit button visible
      await tester.drag(find.byType(Scrollable).first, const Offset(0, -300));
      await tester.pumpAndSettle();

      // Try to submit
      final submitButton = find.widgetWithText(ElevatedButton, 'Save').last;
      await tester.tap(submitButton);
      await tester.pumpAndSettle();

      // Should show validation error
      expect(
        find.text('Description must be 500 characters or less'),
        findsOneWidget,
      );
    });
  });
}
