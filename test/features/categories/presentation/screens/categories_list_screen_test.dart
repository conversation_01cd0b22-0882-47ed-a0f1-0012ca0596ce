import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/categories/presentation/screens/categories_list_screen.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CategoriesListScreen', () {
    late List<Category> mockCategories;

    setUp(() {
      mockCategories = [
        Category(
          id: '1',
          userId: 'test-user',
          name: 'Food & Dining',
          type: CategoryType.expense,
          isActive: true,
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Category(
          id: '2',
          userId: 'test-user',
          name: 'Salary',
          type: CategoryType.income,
          isActive: true,
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Category(
          id: '3',
          userId: 'test-user',
          name: 'Groceries',
          type: CategoryType.expense,
          isActive: true,
          parentId: '1',
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];
    });

    Widget createTestWidget({List<Override> overrides = const []}) {
      return ProviderScope(
        overrides: [
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
          ...overrides,
        ],
        child: const MaterialApp(
          localizationsDelegates: [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: [Locale('en')],
          home: CategoriesListScreen(),
        ),
      );
    }

    testWidgets('displays categories list when data is available', (
      tester,
    ) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Verify screen title in app bar
      expect(find.byType(AppBar), findsOneWidget);

      // Verify tabs are present
      expect(find.byType(TabBar), findsOneWidget);
      expect(find.byType(Tab), findsNWidgets(2));

      // Verify expense categories are displayed in the expense tab (default tab - now first)
      expect(find.text('Food & Dining'), findsOneWidget);

      // Tap income tab to check income categories
      final tabs = find.byType(Tab);
      await tester.tap(tabs.at(1)); // Second tab (Income)
      await tester.pumpAndSettle();

      // Verify income categories are displayed
      expect(find.text('Salary'), findsOneWidget);

      // Switch back to expense tab to verify expense categories
      await tester.tap(tabs.at(0)); // First tab (Expense)
      await tester.pumpAndSettle();

      expect(find.text('Food & Dining'), findsOneWidget);
      expect(find.text('Groceries'), findsOneWidget);
    });

    testWidgets('displays loading indicator when data is loading', (
      tester,
    ) async {
      await tester.pumpWidget(
        createTestWidget(
          overrides: [
            categoryListProvider.overrideWith((ref) => const Stream.empty()),
          ],
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('displays error state when data loading fails', (tester) async {
      const error = 'Failed to load categories';
      await tester.pumpWidget(
        createTestWidget(
          overrides: [
            categoryListProvider.overrideWith((ref) => Stream.error(error)),
          ],
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text('Error Loading Categories'), findsOneWidget);
      expect(find.text(error), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('displays empty state when no categories available', (
      tester,
    ) async {
      await tester.pumpWidget(
        createTestWidget(
          overrides: [
            categoryListProvider.overrideWith(
              (ref) => Stream.value(<Category>[]),
            ),
          ],
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text('No categories yet'), findsOneWidget);
      expect(
        find.text(
          'Add your first category to start organizing your transactions',
        ),
        findsOneWidget,
      );
      expect(find.text('Create First Category'), findsOneWidget);
    });

    testWidgets('displays categories without filter functionality', (
      tester,
    ) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Verify filter button is not present (removed functionality)
      expect(find.byIcon(Icons.filter_list), findsNothing);

      // Verify categories are displayed without filtering
      expect(find.text('Food & Dining'), findsOneWidget);
    });

    testWidgets('displays tabs with correct labels', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Verify tabs are present with correct labels
      expect(find.byType(TabBar), findsOneWidget);
      expect(find.byType(Tab), findsNWidgets(2));

      // Verify tab view is present
      expect(find.byType(TabBarView), findsOneWidget);
    });

    testWidgets('can refresh categories by pulling down', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Perform pull-to-refresh on the current tab (Expense tab by default)
      await tester.fling(
        find.byType(RefreshIndicator).first,
        const Offset(0, 300),
        1000,
      );
      await tester.pump();
      await tester.pump(const Duration(seconds: 1));

      // Verify the expense category is still displayed (refresh completed)
      expect(find.text('Food & Dining'), findsOneWidget);
    });
  });
}
