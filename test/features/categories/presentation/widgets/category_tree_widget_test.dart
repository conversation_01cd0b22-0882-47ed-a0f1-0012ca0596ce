import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/categories/presentation/widgets/category_tree_widget.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/mock_data_factory.dart';

// Mock callback classes for testing
class MockCategoryCallback extends Mock {
  void call(Category category);
}

class MockMenuActionCallback extends Mock {
  void call(Category category, String action);
}

void main() {
  group('CategoryTreeWidget', () {
    late Category parentCategory;
    late List<Category> subcategories;
    late MockCategoryCallback mockCategoryTap;
    late MockCategoryCallback mockSubcategoryTap;
    late MockMenuActionCallback mockCategoryMenuAction;
    late MockMenuActionCallback mockSubcategoryMenuAction;

    setUp(() {
      // Create test data
      parentCategory = MockDataFactory.createCategory(
        id: 'parent-1',
        name: 'Food & Dining',
        type: CategoryType.expense,
        color: '#FF6B6B',
        icon: 'restaurant',
      );

      subcategories = [
        MockDataFactory.createCategory(
          id: 'sub-1',
          name: 'Restaurants',
          type: CategoryType.expense,
          parentId: 'parent-1',
        ),
        MockDataFactory.createCategory(
          id: 'sub-2',
          name: 'Groceries',
          type: CategoryType.expense,
          parentId: 'parent-1',
        ),
        MockDataFactory.createCategory(
          id: 'sub-3',
          name: 'Fast Food',
          type: CategoryType.expense,
          parentId: 'parent-1',
        ),
      ];

      // Create mock callbacks
      mockCategoryTap = MockCategoryCallback();
      mockSubcategoryTap = MockCategoryCallback();
      mockCategoryMenuAction = MockMenuActionCallback();
      mockSubcategoryMenuAction = MockMenuActionCallback();
    });

    Widget createTestWidget({
      required Category category,
      required List<Category> subcategories,
      bool initiallyExpanded = false,
      void Function(Category)? onCategoryTap,
      void Function(Category)? onSubcategoryTap,
      void Function(Category, String)? onCategoryMenuAction,
      void Function(Category, String)? onSubcategoryMenuAction,
    }) {
      return MaterialApp(
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [Locale('en')],
        home: Scaffold(
          body: CategoryTreeWidget(
            category: category,
            subcategories: subcategories,
            initiallyExpanded: initiallyExpanded,
            onCategoryTap: onCategoryTap,
            onSubcategoryTap: onSubcategoryTap,
            onCategoryMenuAction: onCategoryMenuAction,
            onSubcategoryMenuAction: onSubcategoryMenuAction,
          ),
        ),
      );
    }

    group('Widget Initialization & Lifecycle', () {
      testWidgets('should initialize with collapsed state by default', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Widget should render without errors
        expect(find.byType(CategoryTreeWidget), findsOneWidget);

        // Subcategories are built but clipped (heightFactor = 0)
        // Check for ClipRect and Align presence in the expansion area
        expect(find.byType(ClipRect), findsOneWidget);
        expect(find.byType(Align), findsOneWidget);

        // Parent category should be visible
        expect(find.text('Food & Dining'), findsOneWidget);
      });

      testWidgets(
        'should initialize with expanded state when initiallyExpanded is true',
        (tester) async {
          await tester.pumpWidget(
            createTestWidget(
              category: parentCategory,
              subcategories: subcategories,
              initiallyExpanded: true,
            ),
          );

          await tester.pumpAndSettle();

          // Subcategories should be visible when initially expanded
          expect(find.text('Restaurants'), findsOneWidget);
          expect(find.text('Groceries'), findsOneWidget);
          expect(find.text('Fast Food'), findsOneWidget);
        },
      );

      testWidgets(
        'should initialize SingleTickerProviderStateMixin correctly',
        (tester) async {
          await tester.pumpWidget(
            createTestWidget(
              category: parentCategory,
              subcategories: subcategories,
            ),
          );

          // Verify the widget builds without animation errors
          expect(tester.takeException(), isNull);
          expect(find.byType(CategoryTreeWidget), findsOneWidget);
        },
      );

      testWidgets('should setup animation controller with correct duration', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Animation controller should be properly initialized (tested through behavior)
        final expandButton = find.byIcon(Icons.chevron_right);
        expect(expandButton, findsOneWidget);

        // Test animation duration by tapping and verifying smooth animation
        await tester.tap(expandButton);
        await tester.pump(const Duration(milliseconds: 100));

        // Animation should be in progress (partial expansion)
        expect(tester.takeException(), isNull);
      });

      testWidgets('should dispose animation controller without errors', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Verify widget is built
        expect(find.byType(CategoryTreeWidget), findsOneWidget);

        // Remove widget to trigger dispose
        await tester.pumpWidget(
          const MaterialApp(home: Scaffold(body: SizedBox())),
        );

        // Should not throw any exceptions during disposal
        expect(tester.takeException(), isNull);
      });

      testWidgets('should setup CurvedAnimation with easeInOut curve', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Test smooth animation curve by expanding and measuring progress
        final expandButton = find.byIcon(Icons.chevron_right);
        await tester.tap(expandButton);

        // Test multiple animation frames
        await tester.pump(const Duration(milliseconds: 50));
        await tester.pump(const Duration(milliseconds: 100));
        await tester.pump(const Duration(milliseconds: 150));

        // Animation should complete smoothly
        await tester.pumpAndSettle();
        expect(find.text('Restaurants'), findsOneWidget);
      });

      testWidgets(
        'should handle animation controller state when initially expanded',
        (tester) async {
          await tester.pumpWidget(
            createTestWidget(
              category: parentCategory,
              subcategories: subcategories,
              initiallyExpanded: true,
            ),
          );

          // Animation controller should be set to value 1 when initially expanded
          await tester.pumpAndSettle();

          // Subcategories should be immediately visible
          expect(find.text('Restaurants'), findsOneWidget);
          expect(find.text('Groceries'), findsOneWidget);
          expect(find.text('Fast Food'), findsOneWidget);
        },
      );

      testWidgets('should handle lifecycle correctly with widget rebuilds', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Expand the widget
        final expandButton = find.byIcon(Icons.chevron_right);
        await tester.tap(expandButton);
        await tester.pumpAndSettle();

        // Verify expanded state
        expect(find.text('Restaurants'), findsOneWidget);

        // Rebuild widget with same state
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Should maintain proper state and not throw errors
        expect(tester.takeException(), isNull);
        expect(find.byType(CategoryTreeWidget), findsOneWidget);
      });
    });

    group('Expand/Collapse Functionality', () {
      testWidgets('should expand subcategories when expand button is tapped', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Verify initial collapsed state (clipping widgets present)
        expect(find.byType(ClipRect), findsOneWidget);
        expect(find.byType(Align), findsOneWidget);

        // Tap expand button
        final expandButton = find.byIcon(Icons.chevron_right);
        await tester.tap(expandButton);
        await tester.pumpAndSettle();

        // Should now be expanded - subcategories fully visible
        expect(find.text('Restaurants'), findsOneWidget);
        expect(find.text('Groceries'), findsOneWidget);
        expect(find.text('Fast Food'), findsOneWidget);
      });

      testWidgets(
        'should collapse subcategories when expand button is tapped again',
        (tester) async {
          await tester.pumpWidget(
            createTestWidget(
              category: parentCategory,
              subcategories: subcategories,
              initiallyExpanded: true,
            ),
          );

          await tester.pumpAndSettle();

          // Initially expanded
          expect(find.text('Restaurants'), findsOneWidget);

          // Tap expand button to collapse
          final expandButton = find.byIcon(Icons.chevron_right);
          await tester.tap(expandButton);
          await tester.pumpAndSettle();

          // Should now be collapsed - clipping widgets still present but content clipped
          expect(find.byType(ClipRect), findsOneWidget);
          expect(find.byType(Align), findsOneWidget);
        },
      );

      testWidgets('should animate expand button rotation on state change', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Test AnimatedRotation behavior
        final expandButton = find.byIcon(Icons.chevron_right);
        expect(expandButton, findsOneWidget);

        // Tap to expand
        await tester.tap(expandButton);
        await tester.pump(const Duration(milliseconds: 100));
        await tester.pumpAndSettle();

        // Expand button should still be present (rotated)
        expect(expandButton, findsOneWidget);
      });

      testWidgets('should handle multiple rapid expand/collapse operations', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        final expandButton = find.byIcon(Icons.chevron_right);

        // Rapid taps
        await tester.tap(expandButton);
        await tester.pump(const Duration(milliseconds: 50));
        await tester.tap(expandButton);
        await tester.pump(const Duration(milliseconds: 50));
        await tester.tap(expandButton);
        await tester.pumpAndSettle();

        // Should handle rapid state changes without errors
        expect(tester.takeException(), isNull);
        expect(find.byType(CategoryTreeWidget), findsOneWidget);
      });

      testWidgets('should animate height using ClipRect and Align', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Verify animation structure - ClipRect and Align are more specific
        expect(find.byType(ClipRect), findsOneWidget);
        expect(find.byType(Align), findsOneWidget);

        final expandButton = find.byIcon(Icons.chevron_right);
        await tester.tap(expandButton);

        // Test animation frames
        await tester.pump(const Duration(milliseconds: 50));
        expect(find.byType(ClipRect), findsOneWidget);

        await tester.pump(const Duration(milliseconds: 100));
        expect(find.byType(Align), findsOneWidget);

        await tester.pumpAndSettle();
        expect(find.text('Restaurants'), findsOneWidget);
      });

      testWidgets('should handle animation interruption gracefully', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        final expandButton = find.byIcon(Icons.chevron_right);

        // Start expansion
        await tester.tap(expandButton);
        await tester.pump(const Duration(milliseconds: 50));

        // Interrupt with collapse
        await tester.tap(expandButton);
        await tester.pumpAndSettle();

        // Should handle interruption without errors
        expect(tester.takeException(), isNull);
        // Animation should be stable
        expect(find.byType(CategoryTreeWidget), findsOneWidget);
      });

      testWidgets('should maintain state consistency during animation', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        final expandButton = find.byIcon(Icons.chevron_right);

        // Test state during animation
        await tester.tap(expandButton);
        await tester.pump(const Duration(milliseconds: 100));

        // Animation should be in progress but widget stable
        expect(find.byType(CategoryTreeWidget), findsOneWidget);
        expect(tester.takeException(), isNull);

        await tester.pumpAndSettle();
        expect(find.text('Restaurants'), findsOneWidget);
      });

      testWidgets('should handle empty subcategories list during animation', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(category: parentCategory, subcategories: []),
        );

        // No expand button should be present with empty subcategories
        expect(find.byIcon(Icons.chevron_right), findsNothing);

        // Only parent category should be visible
        expect(find.text('Food & Dining'), findsOneWidget);
      });

      testWidgets('should set correct heightFactor in Align widget', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Verify Align widget exists with heightFactor animation
        expect(find.byType(Align), findsOneWidget);

        final expandButton = find.byIcon(Icons.chevron_right);
        await tester.tap(expandButton);

        // During animation, Align should still be present
        await tester.pump(const Duration(milliseconds: 100));
        expect(find.byType(Align), findsOneWidget);

        await tester.pumpAndSettle();
        expect(find.byType(Align), findsOneWidget);
      });

      testWidgets('should use topCenter alignment in Align widget', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        final expandButton = find.byIcon(Icons.chevron_right);
        await tester.tap(expandButton);
        await tester.pumpAndSettle();

        // Verify Align widget configuration through successful rendering
        expect(find.text('Restaurants'), findsOneWidget);
        expect(find.text('Groceries'), findsOneWidget);
        expect(find.text('Fast Food'), findsOneWidget);
      });

      testWidgets('should handle animation duration of 200ms correctly', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        final expandButton = find.byIcon(Icons.chevron_right);

        // Test specific timing
        await tester.tap(expandButton);
        await tester.pump(const Duration(milliseconds: 200));
        await tester.pump();

        // Animation should be complete or nearly complete
        expect(find.text('Restaurants'), findsOneWidget);
      });
    });

    group('UI Rendering & Layout', () {
      testWidgets('should render parent category with CategoryCard', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Parent category should be visible
        expect(find.text('Food & Dining'), findsOneWidget);

        // CategoryCard should be rendered
        expect(find.byType(CategoryTreeWidget), findsOneWidget);
      });

      testWidgets(
        'should render subcategories as CategoryCard widgets when expanded',
        (tester) async {
          await tester.pumpWidget(
            createTestWidget(
              category: parentCategory,
              subcategories: subcategories,
              initiallyExpanded: true,
            ),
          );

          await tester.pumpAndSettle();

          // All subcategories should be rendered
          expect(find.text('Restaurants'), findsOneWidget);
          expect(find.text('Groceries'), findsOneWidget);
          expect(find.text('Fast Food'), findsOneWidget);
        },
      );

      testWidgets('should render expand button when subcategories exist', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Expand button should be present and accessible
        expect(find.byIcon(Icons.chevron_right), findsOneWidget);
      });

      testWidgets('should not render expand button when no subcategories', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(category: parentCategory, subcategories: []),
        );

        // No expand button should be present
        expect(find.byIcon(Icons.chevron_right), findsNothing);
      });

      testWidgets('should render parent category in Stack widget', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Parent category layout should be present (Stack widgets may be multiple)
        expect(find.byType(CategoryTreeWidget), findsOneWidget);

        // Parent category name should be visible
        expect(find.text('Food & Dining'), findsOneWidget);
      });

      testWidgets('should position expand button correctly in Stack', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Positioned widget should be present for expand button
        expect(find.byType(Positioned), findsOneWidget);
        expect(find.byIcon(Icons.chevron_right), findsOneWidget);
      });

      testWidgets('should render connection lines when expanded', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
            initiallyExpanded: true,
          ),
        );

        await tester.pumpAndSettle();

        // Connection lines should be rendered as Container widgets
        final containers = find.byType(Container);
        expect(containers, findsWidgets);

        // Subcategories should be visible
        expect(find.text('Restaurants'), findsOneWidget);
        expect(find.text('Groceries'), findsOneWidget);
        expect(find.text('Fast Food'), findsOneWidget);
      });

      testWidgets(
        'should render different connection line height for last subcategory',
        (tester) async {
          await tester.pumpWidget(
            createTestWidget(
              category: parentCategory,
              subcategories: subcategories,
              initiallyExpanded: true,
            ),
          );

          await tester.pumpAndSettle();

          // Connection lines logic should handle last item differently
          // Verify through presence of subcategories (connection lines rendered)
          expect(find.text('Fast Food'), findsOneWidget); // Last subcategory
          expect(find.text('Restaurants'), findsOneWidget); // First subcategory
        },
      );

      testWidgets('should render expand button with correct styling', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Expand button container should have specific styling
        final expandButton = find.byIcon(Icons.chevron_right);
        expect(expandButton, findsOneWidget);

        // Container with border radius should be present
        final containers = find.byType(Container);
        expect(containers, findsWidgets);
      });

      testWidgets('should render main Column layout correctly', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Main Column widget should be present
        expect(find.byType(Column), findsWidgets);

        // Parent category should be visible
        expect(find.text('Food & Dining'), findsOneWidget);
      });
    });

    group('Touch Interactions', () {
      testWidgets('should call onCategoryTap when parent category is tapped', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
            onCategoryTap: mockCategoryTap.call,
          ),
        );

        // Tap on parent category
        await tester.tap(find.text('Food & Dining'));
        await tester.pump();

        // Verify callback was called with correct category
        verify(() => mockCategoryTap.call(parentCategory)).called(1);
      });

      testWidgets('should call onSubcategoryTap when subcategory is tapped', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
            initiallyExpanded: true,
            onSubcategoryTap: mockSubcategoryTap.call,
          ),
        );

        await tester.pumpAndSettle();

        // Tap on first subcategory
        await tester.tap(find.text('Restaurants'));
        await tester.pump();

        // Verify callback was called with correct subcategory
        verify(() => mockSubcategoryTap.call(subcategories[0])).called(1);
      });

      testWidgets(
        'should call onCategoryMenuAction when category menu is used',
        (tester) async {
          await tester.pumpWidget(
            createTestWidget(
              category: parentCategory,
              subcategories: subcategories,
              onCategoryMenuAction: mockCategoryMenuAction.call,
            ),
          );

          // Note: Testing menu actions requires more complex interaction
          // For now, verify the widget renders without errors
          expect(find.byType(CategoryTreeWidget), findsOneWidget);
          expect(find.text('Food & Dining'), findsOneWidget);
        },
      );

      testWidgets(
        'should call onSubcategoryMenuAction when subcategory menu is used',
        (tester) async {
          await tester.pumpWidget(
            createTestWidget(
              category: parentCategory,
              subcategories: subcategories,
              initiallyExpanded: true,
              onSubcategoryMenuAction: mockSubcategoryMenuAction.call,
            ),
          );

          await tester.pumpAndSettle();

          // Verify subcategories are rendered for menu testing
          expect(find.text('Restaurants'), findsOneWidget);
          expect(find.text('Groceries'), findsOneWidget);
          expect(find.text('Fast Food'), findsOneWidget);
        },
      );

      testWidgets('should toggle expansion when expand button is tapped', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
          ),
        );

        // Initially collapsed - verify clipping widgets are present
        expect(find.byType(ClipRect), findsOneWidget);
        expect(find.byType(Align), findsOneWidget);

        // Tap expand button
        final expandButton = find.byIcon(Icons.chevron_right);
        await tester.tap(expandButton);
        await tester.pumpAndSettle();

        // Should be expanded
        expect(find.text('Restaurants'), findsOneWidget);

        // Tap again to collapse
        await tester.tap(expandButton);
        await tester.pumpAndSettle();

        // Should be collapsed again - content is clipped but widgets still built
        expect(find.byType(ClipRect), findsOneWidget);
        expect(find.byType(Align), findsOneWidget);
      });

      testWidgets(
        'should handle touch interactions without callbacks gracefully',
        (tester) async {
          await tester.pumpWidget(
            createTestWidget(
              category: parentCategory,
              subcategories: subcategories,
              initiallyExpanded: true,
            ),
          );

          await tester.pumpAndSettle();

          // Tap on parent category (no callback provided)
          await tester.tap(find.text('Food & Dining'));
          await tester.pump();

          // Tap on subcategory (no callback provided)
          await tester.tap(find.text('Restaurants'));
          await tester.pump();

          // Should not throw errors
          expect(tester.takeException(), isNull);
        },
      );
    });

    group('Edge Cases', () {
      testWidgets('should handle empty subcategories list', (tester) async {
        await tester.pumpWidget(
          createTestWidget(category: parentCategory, subcategories: []),
        );

        // Only parent category should be visible
        expect(find.text('Food & Dining'), findsOneWidget);

        // No expand button
        expect(find.byIcon(Icons.chevron_right), findsNothing);

        // No clipping widgets when no subcategories
        expect(find.byType(ClipRect), findsNothing);
      });

      testWidgets('should handle null callbacks without errors', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
            onCategoryTap: null,
            onSubcategoryTap: null,
            onCategoryMenuAction: null,
            onSubcategoryMenuAction: null,
          ),
        );

        // Widget should render without errors
        expect(find.byType(CategoryTreeWidget), findsOneWidget);
        expect(find.text('Food & Dining'), findsOneWidget);

        // Should handle null callbacks gracefully
        await tester.tap(find.text('Food & Dining'));
        await tester.pump();
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle single subcategory correctly', (tester) async {
        final singleSubcategory = [subcategories.first];

        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: singleSubcategory,
            initiallyExpanded: true,
          ),
        );

        await tester.pumpAndSettle();

        // Should render single subcategory
        expect(find.text('Restaurants'), findsOneWidget);
        expect(find.text('Groceries'), findsNothing);
        expect(find.text('Fast Food'), findsNothing);
      });

      testWidgets('should handle category without color or icon', (
        tester,
      ) async {
        final basicCategory = MockDataFactory.createCategory(
          id: 'basic-1',
          name: 'Basic Category',
          type: CategoryType.expense,
          color: null,
          icon: null,
        );

        await tester.pumpWidget(
          createTestWidget(category: basicCategory, subcategories: []),
        );

        // Should render with default styling
        expect(find.text('Basic Category'), findsOneWidget);
        expect(find.byType(CategoryTreeWidget), findsOneWidget);
      });

      testWidgets('should handle very long category names', (tester) async {
        final longNameCategory = MockDataFactory.createCategory(
          id: 'long-1',
          name:
              'This is a very long category name that might cause layout issues in the UI',
          type: CategoryType.expense,
        );

        await tester.pumpWidget(
          createTestWidget(category: longNameCategory, subcategories: []),
        );

        // Should handle long names gracefully
        expect(
          find.textContaining('This is a very long category name'),
          findsOneWidget,
        );
        expect(find.byType(CategoryTreeWidget), findsOneWidget);
      });

      testWidgets('should handle rebuild with different subcategories', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: subcategories,
            initiallyExpanded: true,
          ),
        );

        await tester.pumpAndSettle();
        expect(find.text('Restaurants'), findsOneWidget);

        // Rebuild with different subcategories
        final newSubcategories = [subcategories.first];
        await tester.pumpWidget(
          createTestWidget(
            category: parentCategory,
            subcategories: newSubcategories,
            initiallyExpanded: true,
          ),
        );

        await tester.pumpAndSettle();

        // Should show only new subcategories
        expect(find.text('Restaurants'), findsOneWidget);
        // Other subcategories not present in newSubcategories
        expect(find.text('Groceries'), findsNothing);
        expect(find.text('Fast Food'), findsNothing);
      });
    });
  });

  group('SimpleCategoryTreeWidget', () {
    late CategoryTree categoryTree;

    setUp(() {
      // Create a simple category tree for testing
      final rootCategory = MockDataFactory.createCategory(
        id: 'root-1',
        name: 'Root Category',
        type: CategoryType.expense,
      );

      final childCategory = MockDataFactory.createCategory(
        id: 'child-1',
        name: 'Child Category',
        type: CategoryType.expense,
        parentId: 'root-1',
      );

      final grandchildCategory = MockDataFactory.createCategory(
        id: 'grandchild-1',
        name: 'Grandchild Category',
        type: CategoryType.expense,
        parentId: 'child-1',
      );

      // Create nested category tree structure
      final grandchildTree = CategoryTree(
        category: grandchildCategory,
        children: [],
        depth: 2,
      );

      final childTree = CategoryTree(
        category: childCategory,
        children: [grandchildTree],
        depth: 1,
      );

      categoryTree = CategoryTree(
        category: rootCategory,
        children: [childTree],
        depth: 0,
      );
    });

    Widget createSimpleTestWidget({
      required CategoryTree categoryTree,
      void Function(Category)? onCategoryTap,
      int maxDepth = 3,
    }) {
      return MaterialApp(
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [Locale('en')],
        home: Scaffold(
          body: SimpleCategoryTreeWidget(
            categoryTree: categoryTree,
            onCategoryTap: onCategoryTap,
            maxDepth: maxDepth,
          ),
        ),
      );
    }

    testWidgets('should render root category and children', (tester) async {
      await tester.pumpWidget(
        createSimpleTestWidget(categoryTree: categoryTree),
      );

      // All categories should be visible (within maxDepth)
      expect(find.text('Root Category'), findsOneWidget);
      expect(find.text('Child Category'), findsOneWidget);
      expect(find.text('Grandchild Category'), findsOneWidget);
    });

    testWidgets('should respect maxDepth limitation', (tester) async {
      await tester.pumpWidget(
        createSimpleTestWidget(categoryTree: categoryTree, maxDepth: 1),
      );

      // Only root and first level child should be visible due to maxDepth = 1
      expect(find.text('Root Category'), findsOneWidget);
      expect(find.text('Child Category'), findsOneWidget);
      expect(find.text('Grandchild Category'), findsNothing);
    });

    testWidgets('should call onCategoryTap when category is tapped', (
      tester,
    ) async {
      final mockTap = MockCategoryCallback();

      await tester.pumpWidget(
        createSimpleTestWidget(
          categoryTree: categoryTree,
          onCategoryTap: mockTap.call,
        ),
      );

      // Tap on root category
      await tester.tap(find.text('Root Category'));
      await tester.pump();

      // Verify callback was called
      verify(() => mockTap.call(categoryTree.category)).called(1);
    });

    testWidgets('should handle empty children list', (tester) async {
      final emptyTree = CategoryTree(
        category: MockDataFactory.createCategory(
          id: 'empty-1',
          name: 'Empty Category',
          type: CategoryType.expense,
        ),
        children: [],
        depth: 0,
      );

      await tester.pumpWidget(createSimpleTestWidget(categoryTree: emptyTree));

      // Only root category should be visible
      expect(find.text('Empty Category'), findsOneWidget);
    });

    testWidgets('should not show menu button by default', (tester) async {
      await tester.pumpWidget(
        createSimpleTestWidget(categoryTree: categoryTree),
      );

      // SimpleCategoryTreeWidget sets showMenuButton = false
      expect(find.byType(SimpleCategoryTreeWidget), findsOneWidget);
      expect(find.text('Root Category'), findsOneWidget);
    });
  });

  group('CategoryTreeStats', () {
    late CategoryTree categoryTree;

    setUp(() {
      // Create a more complex tree for statistics testing
      final rootCategory = MockDataFactory.createCategory(
        id: 'stats-root',
        name: 'Statistics Root',
        type: CategoryType.expense,
      );

      // Create multiple levels of children
      final children = List.generate(3, (i) {
        final childCategory = MockDataFactory.createCategory(
          id: 'stats-child-$i',
          name: 'Child $i',
          type: CategoryType.expense,
          parentId: 'stats-root',
        );

        // Some children have grandchildren
        final grandchildren = i < 2
            ? [
                CategoryTree(
                  category: MockDataFactory.createCategory(
                    id: 'stats-grandchild-$i',
                    name: 'Grandchild $i',
                    type: CategoryType.expense,
                    parentId: 'stats-child-$i',
                  ),
                  children: [],
                  depth: 2,
                ),
              ]
            : <CategoryTree>[];

        return CategoryTree(
          category: childCategory,
          children: grandchildren,
          depth: 1,
        );
      });

      categoryTree = CategoryTree(
        category: rootCategory,
        children: children,
        depth: 0,
      );
    });

    Widget createStatsTestWidget({required CategoryTree categoryTree}) {
      return MaterialApp(
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [Locale('en')],
        home: Scaffold(body: CategoryTreeStats(categoryTree: categoryTree)),
      );
    }

    testWidgets('should display total category count', (tester) async {
      await tester.pumpWidget(
        createStatsTestWidget(categoryTree: categoryTree),
      );

      // Should show total count (1 root + 3 children + 2 grandchildren = 6)
      expect(find.text('6'), findsOneWidget);

      // Should show categories label (using icon to verify presence)
      expect(find.byIcon(Icons.category), findsOneWidget);
    });

    testWidgets('should display maximum depth', (tester) async {
      await tester.pumpWidget(
        createStatsTestWidget(categoryTree: categoryTree),
      );

      // Maximum depth should be 3 (root=1, children=2, grandchildren=3)
      expect(find.text('3'), findsOneWidget);

      // Should show depth information (using icon to verify presence)
      expect(find.byIcon(Icons.account_tree), findsOneWidget);
    });

    testWidgets('should display subcategory count', (tester) async {
      await tester.pumpWidget(
        createStatsTestWidget(categoryTree: categoryTree),
      );

      // Subcategories = total - 1 (exclude root) = 5
      expect(find.text('5'), findsOneWidget);

      // Should show subcategories label (using icon to verify presence)
      expect(find.byIcon(Icons.subdirectory_arrow_right), findsOneWidget);
    });

    testWidgets('should render stat items with icons', (tester) async {
      await tester.pumpWidget(
        createStatsTestWidget(categoryTree: categoryTree),
      );

      // Should have category icon
      expect(find.byIcon(Icons.category), findsOneWidget);

      // Should have tree structure icon
      expect(find.byIcon(Icons.account_tree), findsOneWidget);

      // Should have arrow icon for subcategories
      expect(find.byIcon(Icons.subdirectory_arrow_right), findsOneWidget);
    });

    testWidgets('should handle single category tree correctly', (tester) async {
      final singleTree = CategoryTree(
        category: MockDataFactory.createCategory(
          id: 'single',
          name: 'Single Category',
          type: CategoryType.expense,
        ),
        children: [],
        depth: 0,
      );

      await tester.pumpWidget(createStatsTestWidget(categoryTree: singleTree));

      // Multiple "1"s are expected for both total count and depth
      expect(find.text('1'), findsWidgets);

      // Subcategories should be 0 (1 - 1)
      expect(find.text('0'), findsOneWidget);
    });
  });
}
