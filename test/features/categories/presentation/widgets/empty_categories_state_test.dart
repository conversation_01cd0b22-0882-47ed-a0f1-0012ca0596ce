import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/categories/presentation/widgets/empty_categories_state.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/test_wrapper.dart';

void main() {
  group('EmptyCategoriesState Widget Tests', () {
    testWidgets('should render basic widget structure', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const EmptyCategoriesState()),
      );

      // Verify main structure - find the EmptyCategoriesState widget specifically
      expect(find.byType(EmptyCategoriesState), findsOneWidget);
    });

    testWidgets('should render with all required UI elements', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const EmptyCategoriesState()),
      );

      // Verify main structure
      expect(find.byType(EmptyCategoriesState), findsOneWidget);
      expect(find.byType(Padding), findsWidgets);
      expect(find.byType(Column), findsWidgets);

      // Verify illustration container
      expect(find.byType(Container), findsWidgets);
      expect(find.byIcon(Icons.category_outlined), findsOneWidget);

      // Verify text elements (using localized strings)
      final context = tester.element(find.byType(EmptyCategoriesState));
      final l10n = AppLocalizations.of(context)!;

      expect(find.text(l10n.noCategoriesYet), findsOneWidget);
      expect(find.text(l10n.noCategoriesDescription), findsOneWidget);

      // Verify action button - look for ElevatedButton.icon
      expect(
        find.byWidgetPredicate(
          (widget) => widget is ElevatedButton && widget.child != null,
        ),
        findsOneWidget,
      );
      expect(find.text(l10n.createFirstCategory), findsOneWidget);
      expect(find.byIcon(Icons.add), findsOneWidget);

      // Verify help button - look for TextButton.icon
      expect(
        find.byWidgetPredicate(
          (widget) => widget is TextButton && widget.child != null,
        ),
        findsOneWidget,
      );
      expect(find.text(l10n.learnAboutCategories), findsOneWidget);
      expect(find.byIcon(Icons.help_outline), findsOneWidget);
    });

    testWidgets('should display correct localized text', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const EmptyCategoriesState()),
      );

      final context = tester.element(find.byType(EmptyCategoriesState));
      final l10n = AppLocalizations.of(context)!;

      // Verify all localized strings are displayed
      expect(find.text(l10n.noCategoriesYet), findsOneWidget);
      expect(find.text(l10n.noCategoriesDescription), findsOneWidget);
      expect(find.text(l10n.createFirstCategory), findsOneWidget);
      expect(find.text(l10n.learnAboutCategories), findsOneWidget);
    });

    testWidgets('should apply correct styling and spacing', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const EmptyCategoriesState()),
      );

      // Verify main padding
      final mainPadding = tester.widget<Padding>(
        find
            .descendant(of: find.byType(Center), matching: find.byType(Padding))
            .first,
      );
      expect(mainPadding.padding, const EdgeInsets.all(AppSpacing.xl));

      // Verify column alignment
      final column = tester.widget<Column>(find.byType(Column).first);
      expect(column.mainAxisAlignment, MainAxisAlignment.center);

      // Verify SizedBox spacing
      expect(find.byType(SizedBox), findsWidgets);
    });

    testWidgets('should render illustration with correct properties', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const EmptyCategoriesState()),
      );

      // Find the illustration container
      expect(find.byType(Container), findsWidgets);

      // Verify icon properties
      final icon = tester.widget<Icon>(find.byIcon(Icons.category_outlined));
      expect(icon.size, 64);
      expect(icon.icon, Icons.category_outlined);
    });

    testWidgets('should render title with correct styling', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const EmptyCategoriesState()),
      );

      final context = tester.element(find.byType(EmptyCategoriesState));
      final theme = Theme.of(context);
      final l10n = AppLocalizations.of(context)!;

      // Find title text widget
      final titleFinder = find.text(l10n.noCategoriesYet);
      expect(titleFinder, findsOneWidget);

      final titleWidget = tester.widget<Text>(titleFinder);
      expect(titleWidget.textAlign, TextAlign.center);
      expect(titleWidget.style?.fontWeight, FontWeight.w600);
      expect(titleWidget.style?.color, theme.colorScheme.onSurface);
    });

    testWidgets('should render description with correct styling', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const EmptyCategoriesState()),
      );

      final context = tester.element(find.byType(EmptyCategoriesState));
      final l10n = AppLocalizations.of(context)!;

      // Find description text widget
      final descriptionFinder = find.text(l10n.noCategoriesDescription);
      expect(descriptionFinder, findsOneWidget);

      final descriptionWidget = tester.widget<Text>(descriptionFinder);
      expect(descriptionWidget.textAlign, TextAlign.center);
    });

    testWidgets('should render action button with correct properties', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const EmptyCategoriesState()),
      );

      final context = tester.element(find.byType(EmptyCategoriesState));
      final l10n = AppLocalizations.of(context)!;

      // Find the elevated button using widget predicate
      final buttonFinder = find.byWidgetPredicate(
        (widget) => widget is ElevatedButton && widget.child != null,
      );
      expect(buttonFinder, findsOneWidget);

      // Verify button is full width
      final sizedBox = tester.widget<SizedBox>(
        find.ancestor(of: buttonFinder, matching: find.byType(SizedBox)).first,
      );
      expect(sizedBox.width, double.infinity);

      // Verify button has icon and text
      expect(find.byIcon(Icons.add), findsOneWidget);
      expect(find.text(l10n.createFirstCategory), findsOneWidget);
    });

    testWidgets('should show help dialog when help button is tapped', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const EmptyCategoriesState()),
      );

      final context = tester.element(find.byType(EmptyCategoriesState));
      final l10n = AppLocalizations.of(context)!;

      // Find and tap the help button using widget predicate
      final helpButton = find.byWidgetPredicate(
        (widget) => widget is TextButton && widget.child != null,
      );
      expect(helpButton, findsOneWidget);

      await tester.tap(helpButton);
      await tester.pumpAndSettle();

      // Verify dialog is shown
      expect(find.byType(AlertDialog), findsOneWidget);
      expect(find.text(l10n.aboutCategories), findsOneWidget);
    });

    testWidgets('should display help dialog with correct content', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const EmptyCategoriesState()),
      );

      // Tap help button to show dialog
      final helpButton = find.byWidgetPredicate(
        (widget) => widget is TextButton && widget.child != null,
      );
      await tester.tap(helpButton);
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(AlertDialog));
      final l10n = AppLocalizations.of(context)!;

      // Verify dialog content
      expect(find.text(l10n.aboutCategories), findsOneWidget);
      expect(find.text(l10n.categoriesHelpDescription), findsOneWidget);
      expect(find.text(l10n.categoryTypesTitle), findsOneWidget);
      expect(find.text(l10n.income), findsOneWidget);
      expect(find.text(l10n.expense), findsOneWidget);
      expect(find.text(l10n.subcategories), findsOneWidget);
      expect(find.text(l10n.gotIt), findsOneWidget);

      // Verify help icons
      expect(find.byIcon(Icons.trending_up), findsOneWidget);
      expect(find.byIcon(Icons.trending_down), findsOneWidget);
      expect(find.byIcon(Icons.subdirectory_arrow_right), findsOneWidget);
    });

    testWidgets('should close help dialog when "Got it" is tapped', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const EmptyCategoriesState()),
      );

      final context = tester.element(find.byType(EmptyCategoriesState));
      final l10n = AppLocalizations.of(context)!;

      // Show dialog
      final helpButton = find.byWidgetPredicate(
        (widget) => widget is TextButton && widget.child != null,
      );
      await tester.tap(helpButton);
      await tester.pumpAndSettle();

      expect(find.byType(AlertDialog), findsOneWidget);

      // Tap "Got it" button
      await tester.tap(find.text(l10n.gotIt));
      await tester.pumpAndSettle();

      // Verify dialog is closed
      expect(find.byType(AlertDialog), findsNothing);
    });

    testWidgets('should render help items with correct styling', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const EmptyCategoriesState()),
      );

      // Show help dialog
      final helpButton = find.byWidgetPredicate(
        (widget) => widget is TextButton && widget.child != null,
      );
      await tester.tap(helpButton);
      await tester.pumpAndSettle();

      // Verify help item structure
      expect(find.byType(Row), findsWidgets);
      expect(find.byType(Expanded), findsWidgets);

      // Verify help item containers exist
      expect(find.byType(Container), findsWidgets);
    });

    testWidgets('should handle theme changes correctly', (tester) async {
      // Test with light theme
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const EmptyCategoriesState(),
          theme: ThemeData.light(),
        ),
      );

      expect(find.byType(EmptyCategoriesState), findsOneWidget);

      // Test with dark theme
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const EmptyCategoriesState(),
          theme: ThemeData.dark(),
        ),
      );

      expect(find.byType(EmptyCategoriesState), findsOneWidget);
    });

    testWidgets('should be accessible with proper semantics', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const EmptyCategoriesState()),
      );

      final context = tester.element(find.byType(EmptyCategoriesState));
      final l10n = AppLocalizations.of(context)!;

      // Verify semantic structure
      expect(find.byType(EmptyCategoriesState), findsOneWidget);

      // Check that text elements are accessible
      expect(find.text(l10n.noCategoriesYet), findsOneWidget);
      expect(find.text(l10n.createFirstCategory), findsOneWidget);
      expect(find.text(l10n.learnAboutCategories), findsOneWidget);

      // Verify buttons are tappable using widget predicates
      expect(
        find.byWidgetPredicate(
          (widget) => widget is ElevatedButton && widget.child != null,
        ),
        findsOneWidget,
      );
      expect(
        find.byWidgetPredicate(
          (widget) => widget is TextButton && widget.child != null,
        ),
        findsOneWidget,
      );
    });

    testWidgets('should handle edge cases gracefully', (tester) async {
      // Test with minimal widget tree
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(body: EmptyCategoriesState()),
          localizationsDelegates: [AppLocalizations.delegate],
          supportedLocales: AppLocalizations.supportedLocales,
        ),
      );

      expect(find.byType(EmptyCategoriesState), findsOneWidget);
    });
  });
}
