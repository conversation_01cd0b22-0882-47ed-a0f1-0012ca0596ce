import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/interfaces/category_repository.dart';
import 'package:budapp/data/repositories/interfaces/transaction_repository.dart';
import 'package:budapp/features/categories/services/category_deletion_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockCategoryRepository extends Mock implements ICategoryRepository {}

class MockTransactionRepository extends Mock
    implements ITransactionRepository {}

// Test data factory
class CategoryDeletionTestData {
  static Category createCategory({
    String? id,
    required String userId,
    required String name,
    required CategoryType type,
    bool isActive = true,
    String? parentId,
  }) {
    return Category.create(
      userId: userId,
      name: name,
      type: type,
      parentId: parentId,
    ).copyWith(
      id: id ?? 'category-${DateTime.now().millisecondsSinceEpoch}',
      isActive: isActive,
    );
  }

  static Transaction createTransaction({
    String? id,
    required String userId,
    required String categoryId,
    String? fromAccountId,
    String? toAccountId,
    TransactionType type = TransactionType.expense,
    int amountCents = 10000,
  }) {
    return Transaction(
      id: id ?? 'transaction-${DateTime.now().millisecondsSinceEpoch}',
      userId: userId,
      type: type,
      status: TransactionStatus.completed,
      amountCents: amountCents,
      fromAccountId: fromAccountId,
      toAccountId: toAccountId,
      categoryId: categoryId,
      description: 'Test transaction',
      transactionDate: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}

void main() {
  group('CategoryDeletionService', () {
    late CategoryDeletionService service;
    late MockCategoryRepository mockCategoryRepository;
    late MockTransactionRepository mockTransactionRepository;

    const userId = 'test-user-id';
    const categoryId = 'test-category-id';
    const subcategoryId = 'test-subcategory-id';
    const parentId = 'test-parent-id';
    const newCategoryId = 'new-category-id';

    setUp(() {
      mockCategoryRepository = MockCategoryRepository();
      mockTransactionRepository = MockTransactionRepository();
      service = CategoryDeletionService(
        mockCategoryRepository,
        mockTransactionRepository,
      );

      // Register fallback values for mocktail
      registerFallbackValue(
        CategoryDeletionTestData.createCategory(
          userId: 'fallback',
          name: 'Fallback',
          type: CategoryType.expense,
        ),
      );
      registerFallbackValue(
        CategoryDeletionTestData.createTransaction(
          userId: 'fallback',
          categoryId: 'fallback',
        ),
      );
    });

    group('canDeleteCategory', () {
      test('returns success when category can be deleted', () async {
        // Arrange
        final category = CategoryDeletionTestData.createCategory(
          id: categoryId,
          userId: userId,
          name: 'Test Category',
          type: CategoryType.expense,
        );

        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => category);
        when(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            categoryId,
          ),
        ).thenAnswer((_) async => false);
        when(
          () =>
              mockCategoryRepository.hasChildSubcategories(userId, categoryId),
        ).thenAnswer((_) async => false);

        // Act
        final result = await service.canDeleteCategory(userId, categoryId);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.errorMessage, isNull);
        verify(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).called(1);
        verify(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            categoryId,
          ),
        ).called(1);
        verify(
          () =>
              mockCategoryRepository.hasChildSubcategories(userId, categoryId),
        ).called(1);
      });

      test('returns error when category not found', () async {
        // Arrange
        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => null);

        // Act
        final result = await service.canDeleteCategory(userId, categoryId);

        // Assert
        expect(result.isError, isTrue);
        expect(result.errorMessage, equals('Category not found'));
        verify(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).called(1);
        verifyNever(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            any(),
            any(),
          ),
        );
        verifyNever(
          () => mockCategoryRepository.hasChildSubcategories(any(), any()),
        );
      });

      test('returns error when category has dependent transactions', () async {
        // Arrange
        final category = CategoryDeletionTestData.createCategory(
          id: categoryId,
          userId: userId,
          name: 'Test Category',
          type: CategoryType.expense,
        );

        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => category);
        when(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            categoryId,
          ),
        ).thenAnswer((_) async => true);

        // Act
        final result = await service.canDeleteCategory(userId, categoryId);

        // Assert
        expect(result.isError, isTrue);
        expect(
          result.errorMessage,
          contains(
            'Cannot delete category "Test Category": it has associated transactions',
          ),
        );
        expect(
          result.errorMessage,
          contains('Please reassign or delete the transactions first'),
        );
        verify(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).called(1);
        verify(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            categoryId,
          ),
        ).called(1);
        verifyNever(
          () => mockCategoryRepository.hasChildSubcategories(any(), any()),
        );
      });

      test('returns error when category has child subcategories', () async {
        // Arrange
        final category = CategoryDeletionTestData.createCategory(
          id: categoryId,
          userId: userId,
          name: 'Parent Category',
          type: CategoryType.expense,
        );

        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => category);
        when(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            categoryId,
          ),
        ).thenAnswer((_) async => false);
        when(
          () =>
              mockCategoryRepository.hasChildSubcategories(userId, categoryId),
        ).thenAnswer((_) async => true);

        // Act
        final result = await service.canDeleteCategory(userId, categoryId);

        // Assert
        expect(result.isError, isTrue);
        expect(
          result.errorMessage,
          contains(
            'Cannot delete category "Parent Category": it has subcategories',
          ),
        );
        expect(
          result.errorMessage,
          contains('Please delete or move the subcategories first'),
        );
        verify(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).called(1);
        verify(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            categoryId,
          ),
        ).called(1);
        verify(
          () =>
              mockCategoryRepository.hasChildSubcategories(userId, categoryId),
        ).called(1);
      });

      test('returns error when exception occurs', () async {
        // Arrange
        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenThrow(Exception('Database connection failed'));

        // Act
        final result = await service.canDeleteCategory(userId, categoryId);

        // Assert
        expect(result.isError, isTrue);
        expect(
          result.errorMessage,
          contains(
            'Error checking deletion constraints: Exception: Database connection failed',
          ),
        );
        verify(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).called(1);
      });
    });

    group('canDeleteSubcategory', () {
      test('returns success when subcategory can be deleted', () async {
        // Arrange
        final subcategory = CategoryDeletionTestData.createCategory(
          id: subcategoryId,
          userId: userId,
          name: 'Test Subcategory',
          type: CategoryType.expense,
          parentId: parentId,
        );

        when(
          () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
        ).thenAnswer((_) async => subcategory);
        when(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            subcategoryId,
          ),
        ).thenAnswer((_) async => false);
        when(
          () => mockCategoryRepository.hasChildSubcategories(
            userId,
            subcategoryId,
          ),
        ).thenAnswer((_) async => false);

        // Act
        final result = await service.canDeleteSubcategory(
          userId,
          subcategoryId,
          parentId,
        );

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.errorMessage, isNull);
        verify(
          () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
        ).called(1);
        verify(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            subcategoryId,
          ),
        ).called(1);
        verify(
          () => mockCategoryRepository.hasChildSubcategories(
            userId,
            subcategoryId,
          ),
        ).called(1);
      });

      test('returns error when subcategory not found', () async {
        // Arrange
        when(
          () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
        ).thenAnswer((_) async => null);

        // Act
        final result = await service.canDeleteSubcategory(
          userId,
          subcategoryId,
          parentId,
        );

        // Assert
        expect(result.isError, isTrue);
        expect(result.errorMessage, equals('Subcategory not found'));
        verify(
          () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
        ).called(1);
        verifyNever(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            any(),
            any(),
          ),
        );
        verifyNever(
          () => mockCategoryRepository.hasChildSubcategories(any(), any()),
        );
      });

      test(
        'returns error when subcategory does not belong to parent',
        () async {
          // Arrange
          final subcategory = CategoryDeletionTestData.createCategory(
            id: subcategoryId,
            userId: userId,
            name: 'Test Subcategory',
            type: CategoryType.expense,
            parentId: 'different-parent-id',
          );

          when(
            () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
          ).thenAnswer((_) async => subcategory);

          // Act
          final result = await service.canDeleteSubcategory(
            userId,
            subcategoryId,
            parentId,
          );

          // Assert
          expect(result.isError, isTrue);
          expect(
            result.errorMessage,
            equals(
              'Subcategory does not belong to the specified parent category',
            ),
          );
          verify(
            () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
          ).called(1);
          verifyNever(
            () => mockCategoryRepository.hasDependentTransactionsForUser(
              any(),
              any(),
            ),
          );
          verifyNever(
            () => mockCategoryRepository.hasChildSubcategories(any(), any()),
          );
        },
      );

      test('returns error when subcategory has dependent transactions', () async {
        // Arrange
        final subcategory = CategoryDeletionTestData.createCategory(
          id: subcategoryId,
          userId: userId,
          name: 'Test Subcategory',
          type: CategoryType.expense,
          parentId: parentId,
        );

        when(
          () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
        ).thenAnswer((_) async => subcategory);
        when(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            subcategoryId,
          ),
        ).thenAnswer((_) async => true);

        // Act
        final result = await service.canDeleteSubcategory(
          userId,
          subcategoryId,
          parentId,
        );

        // Assert
        expect(result.isError, isTrue);
        expect(
          result.errorMessage,
          contains(
            'Cannot delete subcategory "Test Subcategory": it has associated transactions',
          ),
        );
        expect(
          result.errorMessage,
          contains('Please reassign or delete the transactions first'),
        );
        verify(
          () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
        ).called(1);
        verify(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            subcategoryId,
          ),
        ).called(1);
        verifyNever(
          () => mockCategoryRepository.hasChildSubcategories(any(), any()),
        );
      });

      test('returns error when subcategory has child subcategories', () async {
        // Arrange
        final subcategory = CategoryDeletionTestData.createCategory(
          id: subcategoryId,
          userId: userId,
          name: 'Parent Subcategory',
          type: CategoryType.expense,
          parentId: parentId,
        );

        when(
          () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
        ).thenAnswer((_) async => subcategory);
        when(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            subcategoryId,
          ),
        ).thenAnswer((_) async => false);
        when(
          () => mockCategoryRepository.hasChildSubcategories(
            userId,
            subcategoryId,
          ),
        ).thenAnswer((_) async => true);

        // Act
        final result = await service.canDeleteSubcategory(
          userId,
          subcategoryId,
          parentId,
        );

        // Assert
        expect(result.isError, isTrue);
        expect(
          result.errorMessage,
          contains(
            'Cannot delete subcategory "Parent Subcategory": it has child subcategories',
          ),
        );
        expect(
          result.errorMessage,
          contains('Please delete or move the child subcategories first'),
        );
        verify(
          () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
        ).called(1);
        verify(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            subcategoryId,
          ),
        ).called(1);
        verify(
          () => mockCategoryRepository.hasChildSubcategories(
            userId,
            subcategoryId,
          ),
        ).called(1);
      });

      test('returns error when exception occurs', () async {
        // Arrange
        when(
          () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
        ).thenThrow(Exception('Network timeout'));

        // Act
        final result = await service.canDeleteSubcategory(
          userId,
          subcategoryId,
          parentId,
        );

        // Assert
        expect(result.isError, isTrue);
        expect(
          result.errorMessage,
          contains(
            'Error checking deletion constraints: Exception: Network timeout',
          ),
        );
        verify(
          () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
        ).called(1);
      });
    });

    group('getTransactionsForCategory', () {
      test('returns transactions for category', () async {
        // Arrange
        final transactions = [
          Transaction(
            id: 'test-transaction-1',
            userId: userId,
            type: TransactionType.income,
            status: TransactionStatus.completed,
            amountCents: 10000,

            toAccountId: 'account-1',
            categoryId: categoryId,
            description: 'Test transaction',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        when(
          () => mockTransactionRepository.getTransactionsByCategory(
            userId,
            categoryId,
          ),
        ).thenAnswer((_) async => transactions);

        // Act
        final result = await service.getTransactionsForCategory(
          userId,
          categoryId,
        );

        // Assert
        expect(result, equals(transactions));
        verify(
          () => mockTransactionRepository.getTransactionsByCategory(
            userId,
            categoryId,
          ),
        ).called(1);
      });

      test('returns empty list on error', () async {
        // Arrange
        when(
          () => mockTransactionRepository.getTransactionsByCategory(
            userId,
            categoryId,
          ),
        ).thenThrow(Exception('Database error'));

        // Act
        final result = await service.getTransactionsForCategory(
          userId,
          categoryId,
        );

        // Assert
        expect(result, isEmpty);
      });
    });

    group('deleteCategory', () {
      test('successfully deletes category when constraints are met', () async {
        // Arrange
        final category = CategoryDeletionTestData.createCategory(
          id: categoryId,
          userId: userId,
          name: 'Test Category',
          type: CategoryType.expense,
        );

        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => category);
        when(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            categoryId,
          ),
        ).thenAnswer((_) async => false);
        when(
          () =>
              mockCategoryRepository.hasChildSubcategories(userId, categoryId),
        ).thenAnswer((_) async => false);
        when(
          () => mockCategoryRepository.deleteCategoryWithConstraints(
            userId,
            categoryId,
          ),
        ).thenAnswer((_) async {});

        // Act
        final result = await service.deleteCategory(userId, categoryId);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.errorMessage, isNull);
        verify(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).called(1);
        verify(
          () => mockCategoryRepository.deleteCategoryWithConstraints(
            userId,
            categoryId,
          ),
        ).called(1);
      });

      test('returns error when constraints are not met', () async {
        // Arrange
        final category = CategoryDeletionTestData.createCategory(
          id: categoryId,
          userId: userId,
          name: 'Test Category',
          type: CategoryType.expense,
        );

        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => category);
        when(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            categoryId,
          ),
        ).thenAnswer((_) async => true);

        // Act
        final result = await service.deleteCategory(userId, categoryId);

        // Assert
        expect(result.isError, isTrue);
        expect(
          result.errorMessage,
          contains(
            'Cannot delete category "Test Category": it has associated transactions',
          ),
        );
        verify(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).called(1);
        verifyNever(
          () => mockCategoryRepository.deleteCategoryWithConstraints(
            any(),
            any(),
          ),
        );
      });

      test('returns error when deletion fails', () async {
        // Arrange
        final category = CategoryDeletionTestData.createCategory(
          id: categoryId,
          userId: userId,
          name: 'Test Category',
          type: CategoryType.expense,
        );

        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => category);
        when(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            categoryId,
          ),
        ).thenAnswer((_) async => false);
        when(
          () =>
              mockCategoryRepository.hasChildSubcategories(userId, categoryId),
        ).thenAnswer((_) async => false);
        when(
          () => mockCategoryRepository.deleteCategoryWithConstraints(
            userId,
            categoryId,
          ),
        ).thenThrow(Exception('Database error'));

        // Act
        final result = await service.deleteCategory(userId, categoryId);

        // Assert
        expect(result.isError, isTrue);
        expect(
          result.errorMessage,
          contains('Failed to delete category: Exception: Database error'),
        );
        verify(
          () => mockCategoryRepository.deleteCategoryWithConstraints(
            userId,
            categoryId,
          ),
        ).called(1);
      });
    });

    group('deleteSubcategory', () {
      test(
        'successfully deletes subcategory when constraints are met',
        () async {
          // Arrange
          final subcategory = CategoryDeletionTestData.createCategory(
            id: subcategoryId,
            userId: userId,
            name: 'Test Subcategory',
            type: CategoryType.expense,
            parentId: parentId,
          );

          when(
            () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
          ).thenAnswer((_) async => subcategory);
          when(
            () => mockCategoryRepository.hasDependentTransactionsForUser(
              userId,
              subcategoryId,
            ),
          ).thenAnswer((_) async => false);
          when(
            () => mockCategoryRepository.hasChildSubcategories(
              userId,
              subcategoryId,
            ),
          ).thenAnswer((_) async => false);
          when(
            () => mockCategoryRepository.deleteSubcategoryWithConstraints(
              userId,
              subcategoryId,
              parentId,
            ),
          ).thenAnswer((_) async {});

          // Act
          final result = await service.deleteSubcategory(
            userId,
            subcategoryId,
            parentId,
          );

          // Assert
          expect(result.isSuccess, isTrue);
          expect(result.errorMessage, isNull);
          verify(
            () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
          ).called(1);
          verify(
            () => mockCategoryRepository.deleteSubcategoryWithConstraints(
              userId,
              subcategoryId,
              parentId,
            ),
          ).called(1);
        },
      );

      test('returns error when constraints are not met', () async {
        // Arrange
        final subcategory = CategoryDeletionTestData.createCategory(
          id: subcategoryId,
          userId: userId,
          name: 'Test Subcategory',
          type: CategoryType.expense,
          parentId: parentId,
        );

        when(
          () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
        ).thenAnswer((_) async => subcategory);
        when(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            subcategoryId,
          ),
        ).thenAnswer((_) async => true);

        // Act
        final result = await service.deleteSubcategory(
          userId,
          subcategoryId,
          parentId,
        );

        // Assert
        expect(result.isError, isTrue);
        expect(
          result.errorMessage,
          contains(
            'Cannot delete subcategory "Test Subcategory": it has associated transactions',
          ),
        );
        verify(
          () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
        ).called(1);
        verifyNever(
          () => mockCategoryRepository.deleteSubcategoryWithConstraints(
            any(),
            any(),
            any(),
          ),
        );
      });

      test('returns error when deletion fails', () async {
        // Arrange
        final subcategory = CategoryDeletionTestData.createCategory(
          id: subcategoryId,
          userId: userId,
          name: 'Test Subcategory',
          type: CategoryType.expense,
          parentId: parentId,
        );

        when(
          () => mockCategoryRepository.getCategoryById(userId, subcategoryId),
        ).thenAnswer((_) async => subcategory);
        when(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            subcategoryId,
          ),
        ).thenAnswer((_) async => false);
        when(
          () => mockCategoryRepository.hasChildSubcategories(
            userId,
            subcategoryId,
          ),
        ).thenAnswer((_) async => false);
        when(
          () => mockCategoryRepository.deleteSubcategoryWithConstraints(
            userId,
            subcategoryId,
            parentId,
          ),
        ).thenThrow(Exception('Permission denied'));

        // Act
        final result = await service.deleteSubcategory(
          userId,
          subcategoryId,
          parentId,
        );

        // Assert
        expect(result.isError, isTrue);
        expect(
          result.errorMessage,
          contains(
            'Failed to delete subcategory: Exception: Permission denied',
          ),
        );
        verify(
          () => mockCategoryRepository.deleteSubcategoryWithConstraints(
            userId,
            subcategoryId,
            parentId,
          ),
        ).called(1);
      });
    });

    group('validateReassignmentTarget', () {
      test('returns success for valid reassignment', () async {
        // Arrange
        final oldCategory = Category.create(
          userId: userId,
          name: 'Old Category',
          type: CategoryType.expense,
        ).copyWith(id: categoryId);

        final newCategory = Category.create(
          userId: userId,
          name: 'New Category',
          type: CategoryType.expense,
        ).copyWith(id: newCategoryId);

        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => oldCategory);
        when(
          () => mockCategoryRepository.getCategoryById(userId, newCategoryId),
        ).thenAnswer((_) async => newCategory);

        // Act
        final result = await service.validateReassignmentTarget(
          userId,
          categoryId,
          newCategoryId,
        );

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.oldCategoryName, equals('Old Category'));
        expect(result.newCategoryName, equals('New Category'));
      });

      test('returns error for same category', () async {
        // Act
        final result = await service.validateReassignmentTarget(
          userId,
          categoryId,
          categoryId,
        );

        // Assert
        expect(result.isError, isTrue);
        expect(
          result.errorMessage,
          contains('Cannot reassign to the same category'),
        );
      });

      test('returns error for different types', () async {
        // Arrange
        final oldCategory = Category.create(
          userId: userId,
          name: 'Old Category',
          type: CategoryType.expense,
        ).copyWith(id: categoryId);

        final newCategory = Category.create(
          userId: userId,
          name: 'New Category',
          type: CategoryType.income,
        ).copyWith(id: newCategoryId);

        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => oldCategory);
        when(
          () => mockCategoryRepository.getCategoryById(userId, newCategoryId),
        ).thenAnswer((_) async => newCategory);

        // Act
        final result = await service.validateReassignmentTarget(
          userId,
          categoryId,
          newCategoryId,
        );

        // Assert
        expect(result.isError, isTrue);
        expect(
          result.errorMessage,
          contains('Cannot reassign from expense category to income category'),
        );
      });

      test('returns error for inactive target category', () async {
        // Arrange
        final oldCategory = Category.create(
          userId: userId,
          name: 'Old Category',
          type: CategoryType.expense,
        ).copyWith(id: categoryId);

        final newCategory = Category.create(
          userId: userId,
          name: 'New Category',
          type: CategoryType.expense,
        ).copyWith(id: newCategoryId, isActive: false);

        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => oldCategory);
        when(
          () => mockCategoryRepository.getCategoryById(userId, newCategoryId),
        ).thenAnswer((_) async => newCategory);

        // Act
        final result = await service.validateReassignmentTarget(
          userId,
          categoryId,
          newCategoryId,
        );

        // Assert
        expect(result.isError, isTrue);
        expect(
          result.errorMessage,
          contains('Cannot reassign to inactive category'),
        );
      });

      test('returns error when source category not found', () async {
        // Arrange
        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => null);
        when(
          () => mockCategoryRepository.getCategoryById(userId, newCategoryId),
        ).thenAnswer(
          (_) async => CategoryDeletionTestData.createCategory(
            id: newCategoryId,
            userId: userId,
            name: 'New Category',
            type: CategoryType.expense,
          ),
        );

        // Act
        final result = await service.validateReassignmentTarget(
          userId,
          categoryId,
          newCategoryId,
        );

        // Assert
        expect(result.isError, isTrue);
        expect(result.errorMessage, equals('Source category not found'));
        verify(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).called(1);
        verify(
          () => mockCategoryRepository.getCategoryById(userId, newCategoryId),
        ).called(1);
      });

      test('returns error when target category not found', () async {
        // Arrange
        final oldCategory = CategoryDeletionTestData.createCategory(
          id: categoryId,
          userId: userId,
          name: 'Old Category',
          type: CategoryType.expense,
        );

        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => oldCategory);
        when(
          () => mockCategoryRepository.getCategoryById(userId, newCategoryId),
        ).thenAnswer((_) async => null);

        // Act
        final result = await service.validateReassignmentTarget(
          userId,
          categoryId,
          newCategoryId,
        );

        // Assert
        expect(result.isError, isTrue);
        expect(result.errorMessage, equals('Target category not found'));
        verify(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).called(1);
        verify(
          () => mockCategoryRepository.getCategoryById(userId, newCategoryId),
        ).called(1);
      });

      test(
        'returns error when target category belongs to different user',
        () async {
          // Arrange
          final oldCategory = CategoryDeletionTestData.createCategory(
            id: categoryId,
            userId: userId,
            name: 'Old Category',
            type: CategoryType.expense,
          );

          final newCategory = CategoryDeletionTestData.createCategory(
            id: newCategoryId,
            userId: 'different-user-id',
            name: 'New Category',
            type: CategoryType.expense,
          );

          when(
            () => mockCategoryRepository.getCategoryById(userId, categoryId),
          ).thenAnswer((_) async => oldCategory);
          when(
            () => mockCategoryRepository.getCategoryById(userId, newCategoryId),
          ).thenAnswer((_) async => newCategory);

          // Act
          final result = await service.validateReassignmentTarget(
            userId,
            categoryId,
            newCategoryId,
          );

          // Assert
          expect(result.isError, isTrue);
          expect(
            result.errorMessage,
            equals('Target category does not belong to the current user'),
          );
          verify(
            () => mockCategoryRepository.getCategoryById(userId, categoryId),
          ).called(1);
          verify(
            () => mockCategoryRepository.getCategoryById(userId, newCategoryId),
          ).called(1);
        },
      );

      test('returns error when exception occurs during validation', () async {
        // Arrange
        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenThrow(Exception('Database timeout'));

        // Act
        final result = await service.validateReassignmentTarget(
          userId,
          categoryId,
          newCategoryId,
        );

        // Assert
        expect(result.isError, isTrue);
        expect(
          result.errorMessage,
          contains(
            'Error validating reassignment target: Exception: Database timeout',
          ),
        );
        verify(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).called(1);
      });
    });

    group('reassignCategoryTransactions', () {
      test('successfully reassigns transactions', () async {
        // Arrange
        final oldCategory = Category.create(
          userId: userId,
          name: 'Old Category',
          type: CategoryType.expense,
        ).copyWith(id: categoryId);

        final newCategory = Category.create(
          userId: userId,
          name: 'New Category',
          type: CategoryType.expense,
        ).copyWith(id: newCategoryId);

        final transactions = [
          Transaction(
            id: 'test-transaction-2',
            userId: userId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 10000,

            fromAccountId: 'account-1',
            categoryId: categoryId,
            description: 'Test transaction',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => oldCategory);
        when(
          () => mockCategoryRepository.getCategoryById(userId, newCategoryId),
        ).thenAnswer((_) async => newCategory);
        when(
          () => mockTransactionRepository.getTransactionsByCategory(
            userId,
            categoryId,
          ),
        ).thenAnswer((_) async => transactions);
        when(
          () => mockTransactionRepository.reassignTransactionsToCategoryForUser(
            userId,
            categoryId,
            newCategoryId,
          ),
        ).thenAnswer((_) async {});

        // Act
        final result = await service.reassignCategoryTransactions(
          userId,
          categoryId,
          newCategoryId,
        );

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.transactionCount, equals(1));
        expect(result.transactionIds, hasLength(1));
        expect(
          result.successMessage,
          contains('Successfully reassigned 1 transaction'),
        );

        verify(
          () => mockTransactionRepository.reassignTransactionsToCategoryForUser(
            userId,
            categoryId,
            newCategoryId,
          ),
        ).called(1);
      });

      test('handles no transactions to reassign', () async {
        // Arrange
        final oldCategory = Category.create(
          userId: userId,
          name: 'Old Category',
          type: CategoryType.expense,
        ).copyWith(id: categoryId);

        final newCategory = Category.create(
          userId: userId,
          name: 'New Category',
          type: CategoryType.expense,
        ).copyWith(id: newCategoryId);

        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => oldCategory);
        when(
          () => mockCategoryRepository.getCategoryById(userId, newCategoryId),
        ).thenAnswer((_) async => newCategory);
        when(
          () => mockTransactionRepository.getTransactionsByCategory(
            userId,
            categoryId,
          ),
        ).thenAnswer((_) async => []);

        // Act
        final result = await service.reassignCategoryTransactions(
          userId,
          categoryId,
          newCategoryId,
        );

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.transactionCount, equals(0));
        expect(result.successMessage, equals('No transactions to reassign'));

        verifyNever(
          () => mockTransactionRepository.reassignTransactionsToCategoryForUser(
            any(),
            any(),
            any(),
          ),
        );
      });

      test('returns error when validation fails', () async {
        // Arrange
        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => null);
        when(
          () => mockCategoryRepository.getCategoryById(userId, newCategoryId),
        ).thenAnswer(
          (_) async => CategoryDeletionTestData.createCategory(
            id: newCategoryId,
            userId: userId,
            name: 'New Category',
            type: CategoryType.expense,
          ),
        );

        // Act
        final result = await service.reassignCategoryTransactions(
          userId,
          categoryId,
          newCategoryId,
        );

        // Assert
        expect(result.isError, isTrue);
        expect(result.errorMessage, equals('Source category not found'));
        verify(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).called(1);
        verify(
          () => mockCategoryRepository.getCategoryById(userId, newCategoryId),
        ).called(1);
        verifyNever(
          () =>
              mockTransactionRepository.getTransactionsByCategory(any(), any()),
        );
      });

      test('returns error when reassignment operation fails', () async {
        // Arrange
        final oldCategory = CategoryDeletionTestData.createCategory(
          id: categoryId,
          userId: userId,
          name: 'Old Category',
          type: CategoryType.expense,
        );

        final newCategory = CategoryDeletionTestData.createCategory(
          id: newCategoryId,
          userId: userId,
          name: 'New Category',
          type: CategoryType.expense,
        );

        final transactions = [
          CategoryDeletionTestData.createTransaction(
            id: 'transaction-1',
            userId: userId,
            categoryId: categoryId,
          ),
        ];

        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => oldCategory);
        when(
          () => mockCategoryRepository.getCategoryById(userId, newCategoryId),
        ).thenAnswer((_) async => newCategory);
        when(
          () => mockTransactionRepository.getTransactionsByCategory(
            userId,
            categoryId,
          ),
        ).thenAnswer((_) async => transactions);
        when(
          () => mockTransactionRepository.reassignTransactionsToCategoryForUser(
            userId,
            categoryId,
            newCategoryId,
          ),
        ).thenThrow(Exception('Reassignment failed'));

        // Act
        final result = await service.reassignCategoryTransactions(
          userId,
          categoryId,
          newCategoryId,
        );

        // Assert
        expect(result.isError, isTrue);
        expect(
          result.errorMessage,
          contains(
            'Failed to reassign transactions: Exception: Reassignment failed',
          ),
        );
        verify(
          () => mockTransactionRepository.reassignTransactionsToCategoryForUser(
            userId,
            categoryId,
            newCategoryId,
          ),
        ).called(1);
      });

      test(
        'handles error when getting transactions fails gracefully',
        () async {
          // Arrange
          final oldCategory = CategoryDeletionTestData.createCategory(
            id: categoryId,
            userId: userId,
            name: 'Old Category',
            type: CategoryType.expense,
          );

          final newCategory = CategoryDeletionTestData.createCategory(
            id: newCategoryId,
            userId: userId,
            name: 'New Category',
            type: CategoryType.expense,
          );

          when(
            () => mockCategoryRepository.getCategoryById(userId, categoryId),
          ).thenAnswer((_) async => oldCategory);
          when(
            () => mockCategoryRepository.getCategoryById(userId, newCategoryId),
          ).thenAnswer((_) async => newCategory);
          when(
            () => mockTransactionRepository.getTransactionsByCategory(
              userId,
              categoryId,
            ),
          ).thenThrow(Exception('Database error'));

          // Act
          final result = await service.reassignCategoryTransactions(
            userId,
            categoryId,
            newCategoryId,
          );

          // Assert
          // The service handles exceptions gracefully by returning empty list
          expect(result.isSuccess, isTrue);
          expect(result.transactionCount, equals(0));
          expect(result.successMessage, equals('No transactions to reassign'));
          verify(
            () => mockTransactionRepository.getTransactionsByCategory(
              userId,
              categoryId,
            ),
          ).called(1);
          verifyNever(
            () => mockTransactionRepository
                .reassignTransactionsToCategoryForUser(any(), any(), any()),
          );
        },
      );
    });

    group('deleteWithReassignment', () {
      test('successfully deletes category after reassignment', () async {
        // Arrange
        final oldCategory = Category.create(
          userId: userId,
          name: 'Old Category',
          type: CategoryType.expense,
        ).copyWith(id: categoryId);

        final newCategory = Category.create(
          userId: userId,
          name: 'New Category',
          type: CategoryType.expense,
        ).copyWith(id: newCategoryId);

        final transactions = [
          Transaction(
            id: 'test-transaction-3',
            userId: userId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 10000,

            fromAccountId: 'account-1',
            categoryId: categoryId,
            description: 'Test transaction',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        when(
          () => mockCategoryRepository.getCategoryById(userId, categoryId),
        ).thenAnswer((_) async => oldCategory);
        when(
          () => mockCategoryRepository.getCategoryById(userId, newCategoryId),
        ).thenAnswer((_) async => newCategory);
        when(
          () => mockTransactionRepository.getTransactionsByCategory(
            userId,
            categoryId,
          ),
        ).thenAnswer((_) async => transactions);
        when(
          () => mockTransactionRepository.reassignTransactionsToCategoryForUser(
            userId,
            categoryId,
            newCategoryId,
          ),
        ).thenAnswer((_) async {});
        when(
          () => mockCategoryRepository.hasDependentTransactionsForUser(
            userId,
            categoryId,
          ),
        ).thenAnswer((_) async => false);
        when(
          () =>
              mockCategoryRepository.hasChildSubcategories(userId, categoryId),
        ).thenAnswer((_) async => false);
        when(
          () => mockCategoryRepository.deleteCategoryWithConstraints(
            userId,
            categoryId,
          ),
        ).thenAnswer((_) async {});

        // Act
        final result = await service.deleteWithReassignment(
          userId,
          categoryId,
          newCategoryId,
        );

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.transactionCount, equals(1));

        verify(
          () => mockTransactionRepository.reassignTransactionsToCategoryForUser(
            userId,
            categoryId,
            newCategoryId,
          ),
        ).called(1);
        verify(
          () => mockCategoryRepository.deleteCategoryWithConstraints(
            userId,
            categoryId,
          ),
        ).called(1);
      });
    });
  });
}
