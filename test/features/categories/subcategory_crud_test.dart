import 'package:budapp/data/models/category.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Subcategory CRUD Operations', () {
    group('Category Model', () {
      test('should create subcategory with parent reference', () {
        // Arrange
        const userId = 'user-123';
        const parentId = 'parent-456';

        // Act
        final subcategory = Category.create(
          userId: userId,
          name: 'Test Subcategory',
          type: CategoryType.expense,
          parentId: parentId,
        );

        // Assert
        expect(subcategory.userId, userId);
        expect(subcategory.name, 'Test Subcategory');
        expect(subcategory.type, CategoryType.expense);
        expect(subcategory.parentId, parentId);
        expect(subcategory.isSubcategory, true);
        expect(subcategory.isRoot, false);
      });

      test('should identify root categories correctly', () {
        // Arrange & Act
        final rootCategory = Category.create(
          userId: 'user-123',
          name: 'Root Category',
          type: CategoryType.expense,
        );

        // Assert
        expect(rootCategory.isRoot, true);
        expect(rootCategory.isSubcategory, false);
        expect(rootCategory.parentId, null);
      });

      test('should serialize and deserialize correctly', () {
        // Arrange
        final subcategory = Category.create(
          userId: 'user-123',
          name: 'Test Subcategory',
          type: CategoryType.expense,
          parentId: 'parent-456',
        ).copyWith(id: 'subcategory-789');

        // Act
        final json = subcategory.toJson();
        final deserialized = Category.fromJson(json);

        // Assert
        expect(deserialized.id, subcategory.id);
        expect(deserialized.userId, subcategory.userId);
        expect(deserialized.name, subcategory.name);
        expect(deserialized.type, subcategory.type);
        expect(deserialized.parentId, subcategory.parentId);
        expect(deserialized.isSubcategory, subcategory.isSubcategory);
      });
    });

    group('Subcategory Business Logic', () {
      test('should validate hierarchy depth limits', () {
        // Test that we can identify when categories are at different depths
        final rootCategory = Category.create(
          userId: 'user-123',
          name: 'Root',
          type: CategoryType.expense,
        );

        final subcategory = Category.create(
          userId: 'user-123',
          name: 'Subcategory',
          type: CategoryType.expense,
          parentId: 'root-id',
        );

        expect(rootCategory.isRoot, true);
        expect(subcategory.isSubcategory, true);
      });

      test('should maintain type consistency', () {
        // Test that subcategories should match parent type
        final expenseSubcategory = Category.create(
          userId: 'user-123',
          name: 'Expense Subcategory',
          type: CategoryType.expense,
          parentId: 'parent-id',
        );

        final incomeSubcategory = Category.create(
          userId: 'user-123',
          name: 'Income Subcategory',
          type: CategoryType.income,
          parentId: 'parent-id',
        );

        expect(expenseSubcategory.type, CategoryType.expense);
        expect(incomeSubcategory.type, CategoryType.income);
        expect(expenseSubcategory.type != incomeSubcategory.type, true);
      });

      test('should support user-created subcategories', () {
        final customSubcategory = Category.create(
          userId: 'user-123',
          name: 'Custom Subcategory',
          type: CategoryType.expense,
          parentId: 'parent-id',
        );

        expect(customSubcategory.isSubcategory, true);
      });

      test('should validate subcategory creation requirements', () {
        // Test that subcategories require proper parent reference
        final subcategory = Category.create(
          userId: 'user-123',
          name: 'Test Subcategory',
          type: CategoryType.expense,
          parentId: 'parent-id',
        );

        // Should have all required fields for subcategory
        expect(subcategory.parentId, isNotNull);
        expect(subcategory.isSubcategory, true);
        expect(subcategory.userId, 'user-123');
        expect(subcategory.name, 'Test Subcategory');
        expect(subcategory.type, CategoryType.expense);
      });
    });
  });
}
