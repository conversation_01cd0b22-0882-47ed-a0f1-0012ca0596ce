import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/features/categories/services/category_deletion_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Category Providers Generated Code', () {
    group('Generated Provider Creation', () {
      test('categoryCreateNotifierProvider should be created', () {
        expect(
          categoryCreateNotifierProvider,
          isA<AutoDisposeAsyncNotifierProvider<CategoryCreateNotifier, void>>(),
        );
        expect(
          categoryCreateNotifierProvider.name,
          'categoryCreateNotifierProvider',
        );
      });

      test('categoryUpdateNotifierProvider should be created', () {
        expect(
          categoryUpdateNotifierProvider,
          isA<AutoDisposeAsyncNotifierProvider<CategoryUpdateNotifier, void>>(),
        );
        expect(
          categoryUpdateNotifierProvider.name,
          'categoryUpdateNotifierProvider',
        );
      });

      test('categoryValidationNotifierProvider should be created', () {
        expect(
          categoryValidationNotifierProvider,
          isA<
            AutoDisposeAsyncNotifierProvider<CategoryValidationNotifier, bool>
          >(),
        );
        expect(
          categoryValidationNotifierProvider.name,
          'categoryValidationNotifierProvider',
        );
      });

      test('subcategoryCreateNotifierProvider should be created', () {
        expect(
          subcategoryCreateNotifierProvider,
          isA<
            AutoDisposeAsyncNotifierProvider<SubcategoryCreateNotifier, void>
          >(),
        );
        expect(
          subcategoryCreateNotifierProvider.name,
          'subcategoryCreateNotifierProvider',
        );
      });

      test('subcategoryUpdateNotifierProvider should be created', () {
        expect(
          subcategoryUpdateNotifierProvider,
          isA<
            AutoDisposeAsyncNotifierProvider<SubcategoryUpdateNotifier, void>
          >(),
        );
        expect(
          subcategoryUpdateNotifierProvider.name,
          'subcategoryUpdateNotifierProvider',
        );
      });

      test('subcategoryValidationNotifierProvider should be created', () {
        expect(
          subcategoryValidationNotifierProvider,
          isA<
            AutoDisposeAsyncNotifierProvider<
              SubcategoryValidationNotifier,
              bool
            >
          >(),
        );
        expect(
          subcategoryValidationNotifierProvider.name,
          'subcategoryValidationNotifierProvider',
        );
      });
    });

    group('Generated Provider Properties', () {
      test('providers should have correct names and types', () {
        // Test that the generated providers have the expected properties
        expect(
          categoryCreateNotifierProvider.name,
          'categoryCreateNotifierProvider',
        );
        expect(
          categoryUpdateNotifierProvider.name,
          'categoryUpdateNotifierProvider',
        );
        expect(
          categoryValidationNotifierProvider.name,
          'categoryValidationNotifierProvider',
        );
        expect(
          subcategoryCreateNotifierProvider.name,
          'subcategoryCreateNotifierProvider',
        );
        expect(
          subcategoryUpdateNotifierProvider.name,
          'subcategoryUpdateNotifierProvider',
        );
        expect(
          subcategoryValidationNotifierProvider.name,
          'subcategoryValidationNotifierProvider',
        );
      });

      test('providers should have consistent structure', () {
        // Test that providers have consistent structure and properties
        expect(categoryCreateNotifierProvider.name, isNotEmpty);
        expect(categoryUpdateNotifierProvider.name, isNotEmpty);
        expect(categoryValidationNotifierProvider.name, isNotEmpty);
        expect(subcategoryCreateNotifierProvider.name, isNotEmpty);
        expect(subcategoryUpdateNotifierProvider.name, isNotEmpty);
        expect(subcategoryValidationNotifierProvider.name, isNotEmpty);
      });
    });

    group('Generated Typedefs', () {
      test('typedefs should be properly defined', () {
        // These are compile-time checks - if the code compiles, the typedefs are correct
        expect(true, isTrue); // Placeholder test
      });
    });
  });

  group('Category Providers Integration', () {
    test('categoryDeletionServiceProvider should be a Provider', () {
      expect(
        categoryDeletionServiceProvider,
        isA<Provider<CategoryDeletionService>>(),
      );
    });

    test('categoryListProvider should be a StreamProvider', () {
      expect(categoryListProvider, isA<StreamProvider<List<Category>>>());
    });

    test('categoryProvider should be a family StreamProvider', () {
      expect(categoryProvider, isA<StreamProviderFamily<Category?, String>>());
    });

    test('activeCategoriesProvider should be a FutureProvider', () {
      expect(activeCategoriesProvider, isA<FutureProvider<List<Category>>>());
    });

    test('incomeCategoriesProvider should be a FutureProvider', () {
      expect(incomeCategoriesProvider, isA<FutureProvider<List<Category>>>());
    });

    test('expenseCategoriesProvider should be a FutureProvider', () {
      expect(expenseCategoriesProvider, isA<FutureProvider<List<Category>>>());
    });

    test('rootCategoriesProvider should be a FutureProvider', () {
      expect(rootCategoriesProvider, isA<FutureProvider<List<Category>>>());
    });

    test('rootCategoriesByTypeProvider should be a family FutureProvider', () {
      expect(
        rootCategoriesByTypeProvider,
        isA<FutureProviderFamily<List<Category>, CategoryType>>(),
      );
    });

    test('categoryStatsProvider should be a FutureProvider', () {
      expect(
        categoryStatsProvider,
        isA<FutureProvider<Map<String, dynamic>>>(),
      );
    });

    test('subcategoriesProvider should be a family StreamProvider', () {
      expect(
        subcategoriesProvider,
        isA<StreamProviderFamily<List<Category>, String>>(),
      );
    });

    test('subcategoryCountProvider should be a family FutureProvider', () {
      expect(
        subcategoryCountProvider,
        isA<FutureProviderFamily<int, String>>(),
      );
    });

    test('canHaveSubcategoriesProvider should be a family FutureProvider', () {
      expect(
        canHaveSubcategoriesProvider,
        isA<FutureProviderFamily<bool, String>>(),
      );
    });
  });
}
