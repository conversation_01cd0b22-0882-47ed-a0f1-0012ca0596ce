import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/repositories/interfaces/category_repository.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/mock_data_factory.dart';

// Mock classes
class MockCategoryRepository extends Mock implements ICategoryRepository {}

class MockAuthService extends Mock implements AuthService {}

class MockUser extends Mock implements User {}

void main() {
  setUpAll(() {
    // Use MockDataFactory to create fallback values instead of Fake classes
    registerFallbackValue(MockDataFactory.createCategory());
  });
  late MockCategoryRepository mockCategoryRepository;
  late MockAuthService mockAuthService;
  late MockUser mockUser;
  late ProviderContainer container;

  setUp(() {
    mockCategoryRepository = MockCategoryRepository();
    mockAuthService = MockAuthService();
    mockUser = MockUser();

    // Setup mock user
    when(() => mockUser.uid).thenReturn('test-user-id');
    when(() => mockAuthService.currentUser).thenReturn(mockUser);

    container = ProviderContainer(
      overrides: [
        categoryRepositoryProvider.overrideWithValue(mockCategoryRepository),
        authServiceProvider.overrideWithValue(mockAuthService),
      ],
    );
  });

  tearDown(() {
    container.dispose();
  });

  group('Category Providers - Basic Functionality', () {
    group('Authentication Helper', () {
      test('should handle authenticated user correctly', () {
        // The provider should call the auth service when accessed
        // Use listen() to force the provider to be evaluated
        container.listen(categoryListProvider, (previous, next) {});
        verify(() => mockAuthService.currentUser).called(1);
      });

      test('should handle unauthenticated user', () {
        when(() => mockAuthService.currentUser).thenReturn(null);

        // Reading the provider should not crash but may produce an error state
        final asyncValue = container.read(categoryListProvider);
        expect(asyncValue, isNotNull);

        // If it's not loading and has a value, it should be an error
        if (!asyncValue.isLoading) {
          expect(asyncValue.hasError, isTrue);
        }
      });
    });

    group('CategoryCreateNotifier', () {
      test('should have correct initial state', () {
        final notifier = container.read(
          categoryCreateNotifierProvider.notifier,
        );
        final state = container.read(categoryCreateNotifierProvider);

        expect(notifier, isA<CategoryCreateNotifier>());
        expect(state.isLoading, isFalse);
      });

      test('should handle creation success', () async {
        final category = Category.create(
          userId: 'test-user-id',
          name: 'Test Category',
          type: CategoryType.expense,
          color: '#FF5733',
        );

        when(
          () => mockCategoryRepository.createCategory(any()),
        ).thenAnswer((_) async => 'new-id');

        final notifier = container.read(
          categoryCreateNotifierProvider.notifier,
        );
        await notifier.createCategory(category);

        final state = container.read(categoryCreateNotifierProvider);
        expect(state.hasValue, isTrue);
      });

      test('should handle creation error', () async {
        final category = Category.create(
          userId: 'test-user-id',
          name: 'Test Category',
          type: CategoryType.expense,
          color: '#FF5733',
        );

        when(
          () => mockCategoryRepository.createCategory(any()),
        ).thenThrow(Exception('Creation failed'));

        final notifier = container.read(
          categoryCreateNotifierProvider.notifier,
        );
        await notifier.createCategory(category);

        final state = container.read(categoryCreateNotifierProvider);
        expect(state.hasError, isTrue);
      });
    });

    group('CategoryUpdateNotifier', () {
      test('should have correct initial state', () {
        final notifier = container.read(
          categoryUpdateNotifierProvider.notifier,
        );
        final state = container.read(categoryUpdateNotifierProvider);

        expect(notifier, isA<CategoryUpdateNotifier>());
        expect(state.isLoading, isFalse);
      });

      test('should handle update success', () async {
        final category = Category.create(
          userId: 'test-user-id',
          name: 'Updated Category',
          type: CategoryType.expense,
          color: '#FF5733',
        ).copyWith(id: 'test-id');

        when(
          () => mockCategoryRepository.updateCategory('test-id', any()),
        ).thenAnswer((_) async {});

        final notifier = container.read(
          categoryUpdateNotifierProvider.notifier,
        );
        await notifier.updateCategory('test-id', category);

        final state = container.read(categoryUpdateNotifierProvider);
        expect(state.hasValue, isTrue);
      });

      test('should handle deactivation', () async {
        final category = Category.create(
          userId: 'test-user-id',
          name: 'Test Category',
          type: CategoryType.expense,
          color: '#FF5733',
        ).copyWith(id: 'test-id');

        when(
          () => mockCategoryRepository.updateCategory('test-id', any()),
        ).thenAnswer((_) async {});

        final notifier = container.read(
          categoryUpdateNotifierProvider.notifier,
        );
        await notifier.deactivateCategory('test-id', category);

        final state = container.read(categoryUpdateNotifierProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockCategoryRepository.updateCategory('test-id', any()),
        ).called(1);
      });

      test('should handle reactivation', () async {
        final category = Category.create(
          userId: 'test-user-id',
          name: 'Test Category',
          type: CategoryType.expense,
          color: '#FF5733',
        ).copyWith(id: 'test-id');

        when(
          () => mockCategoryRepository.updateCategory('test-id', any()),
        ).thenAnswer((_) async {});

        final notifier = container.read(
          categoryUpdateNotifierProvider.notifier,
        );
        await notifier.reactivateCategory('test-id', category);

        final state = container.read(categoryUpdateNotifierProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockCategoryRepository.updateCategory('test-id', any()),
        ).called(1);
      });
    });

    group('CategoryValidationNotifier', () {
      test('should have correct initial state', () {
        final notifier = container.read(
          categoryValidationNotifierProvider.notifier,
        );
        final state = container.read(categoryValidationNotifierProvider);

        expect(notifier, isA<CategoryValidationNotifier>());
        expect(state.value, isTrue); // Default initial value
      });

      test('should validate category name uniqueness', () async {
        when(
          () => mockCategoryRepository.isCategoryNameUnique(
            'test-user-id',
            'Unique Name',
            excludeCategoryId: null,
          ),
        ).thenAnswer((_) async => true);

        final notifier = container.read(
          categoryValidationNotifierProvider.notifier,
        );
        final result = await notifier.validateCategoryName(
          'test-user-id',
          'Unique Name',
        );

        expect(result, isTrue);
        verify(
          () => mockCategoryRepository.isCategoryNameUnique(
            'test-user-id',
            'Unique Name',
            excludeCategoryId: null,
          ),
        ).called(1);
      });

      test('should validate complete category', () async {
        final category = Category.create(
          userId: 'test-user-id',
          name: 'Valid Category',
          type: CategoryType.expense,
          color: '#FF5733',
        );

        when(
          () => mockCategoryRepository.validateCategory(category),
        ).thenAnswer((_) async => true);

        final notifier = container.read(
          categoryValidationNotifierProvider.notifier,
        );
        final result = await notifier.validateCategory(category);

        expect(result, isTrue);
        verify(
          () => mockCategoryRepository.validateCategory(category),
        ).called(1);
      });

      test('should handle validation errors gracefully', () async {
        // Create a fresh container for this test to avoid state pollution
        final testContainer = ProviderContainer(
          overrides: [
            categoryRepositoryProvider.overrideWithValue(
              mockCategoryRepository,
            ),
            authServiceProvider.overrideWithValue(mockAuthService),
          ],
        );

        when(
          () => mockCategoryRepository.isCategoryNameUnique(
            'test-user-id',
            'Invalid Name',
            excludeCategoryId: null,
          ),
        ).thenThrow(Exception('Validation error'));

        final notifier = testContainer.read(
          categoryValidationNotifierProvider.notifier,
        );

        // Verify the mock is called and throws
        expect(
          () async => mockCategoryRepository.isCategoryNameUnique(
            'test-user-id',
            'Invalid Name',
            excludeCategoryId: null,
          ),
          throwsA(isA<Exception>()),
        );

        final result = await notifier.validateCategoryName(
          'test-user-id',
          'Invalid Name',
        );

        // Debug: Check the actual state
        final state = testContainer.read(categoryValidationNotifierProvider);
        // Debug: Result: $result, State hasError: ${state.hasError}, State value: ${state.value}, error: ${state.error}

        expect(result, isFalse); // Should return false on error
        expect(state.hasError, isTrue);

        testContainer.dispose();
      });
    });

    group('SubcategoryCreateNotifier', () {
      test('should have correct initial state', () {
        final notifier = container.read(
          subcategoryCreateNotifierProvider.notifier,
        );
        final state = container.read(subcategoryCreateNotifierProvider);

        expect(notifier, isA<SubcategoryCreateNotifier>());
        expect(state.isLoading, isFalse);
      });

      test('should handle subcategory creation', () async {
        final subcategory = Category.create(
          userId: 'test-user-id',
          name: 'Test Subcategory',
          type: CategoryType.expense,
          color: '#FF5733',
        ).copyWith(parentId: 'parent-id');

        when(
          () => mockCategoryRepository.createSubcategory(
            subcategory,
            'parent-id',
          ),
        ).thenAnswer((_) async => 'new-sub-id');

        final notifier = container.read(
          subcategoryCreateNotifierProvider.notifier,
        );
        await notifier.createSubcategory(subcategory, 'parent-id');

        final state = container.read(subcategoryCreateNotifierProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockCategoryRepository.createSubcategory(
            subcategory,
            'parent-id',
          ),
        ).called(1);
      });
    });

    group('SubcategoryUpdateNotifier', () {
      test('should have correct initial state', () {
        final notifier = container.read(
          subcategoryUpdateNotifierProvider.notifier,
        );
        final state = container.read(subcategoryUpdateNotifierProvider);

        expect(notifier, isA<SubcategoryUpdateNotifier>());
        expect(state.isLoading, isFalse);
      });

      test('should handle subcategory update', () async {
        final subcategory = Category.create(
          userId: 'test-user-id',
          name: 'Updated Subcategory',
          type: CategoryType.expense,
          color: '#FF5733',
        ).copyWith(id: 'sub-id', parentId: 'parent-id');

        when(
          () => mockCategoryRepository.updateSubcategory('sub-id', subcategory),
        ).thenAnswer((_) async {});

        final notifier = container.read(
          subcategoryUpdateNotifierProvider.notifier,
        );
        await notifier.updateSubcategory('sub-id', subcategory);

        final state = container.read(subcategoryUpdateNotifierProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockCategoryRepository.updateSubcategory('sub-id', subcategory),
        ).called(1);
      });

      test('should handle subcategory deletion', () async {
        when(
          () => mockCategoryRepository.deleteSubcategory('sub-id', 'parent-id'),
        ).thenAnswer((_) async {});

        final notifier = container.read(
          subcategoryUpdateNotifierProvider.notifier,
        );
        await notifier.deleteSubcategory('sub-id', 'parent-id');

        final state = container.read(subcategoryUpdateNotifierProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockCategoryRepository.deleteSubcategory('sub-id', 'parent-id'),
        ).called(1);
      });

      test('should handle subcategory move', () async {
        when(
          () => mockCategoryRepository.moveSubcategoryToParent(
            'sub-id',
            'old-parent',
            'new-parent',
          ),
        ).thenAnswer((_) async {});

        final notifier = container.read(
          subcategoryUpdateNotifierProvider.notifier,
        );
        await notifier.moveSubcategoryToParent(
          'sub-id',
          'old-parent',
          'new-parent',
        );

        final state = container.read(subcategoryUpdateNotifierProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockCategoryRepository.moveSubcategoryToParent(
            'sub-id',
            'old-parent',
            'new-parent',
          ),
        ).called(1);
      });
    });

    group('SubcategoryValidationNotifier', () {
      test('should have correct initial state', () {
        final notifier = container.read(
          subcategoryValidationNotifierProvider.notifier,
        );
        final state = container.read(subcategoryValidationNotifierProvider);

        expect(notifier, isA<SubcategoryValidationNotifier>());
        expect(state.value, isTrue); // Default initial value
      });

      test('should validate subcategory name uniqueness', () async {
        when(
          () => mockCategoryRepository.isSubcategoryNameUnique(
            'test-user-id',
            'parent-id',
            'Unique Sub',
            excludeSubcategoryId: null,
          ),
        ).thenAnswer((_) async => true);

        final notifier = container.read(
          subcategoryValidationNotifierProvider.notifier,
        );
        final result = await notifier.validateSubcategoryName(
          'test-user-id',
          'parent-id',
          'Unique Sub',
        );

        expect(result, isTrue);
        verify(
          () => mockCategoryRepository.isSubcategoryNameUnique(
            'test-user-id',
            'parent-id',
            'Unique Sub',
            excludeSubcategoryId: null,
          ),
        ).called(1);
      });

      test('should validate subcategory hierarchy', () async {
        when(
          () => mockCategoryRepository.validateSubcategoryHierarchy(
            'parent-id',
            'sub-id',
          ),
        ).thenAnswer((_) async => true);

        final notifier = container.read(
          subcategoryValidationNotifierProvider.notifier,
        );
        final result = await notifier.validateSubcategoryHierarchy(
          'parent-id',
          'sub-id',
        );

        expect(result, isTrue);
        verify(
          () => mockCategoryRepository.validateSubcategoryHierarchy(
            'parent-id',
            'sub-id',
          ),
        ).called(1);
      });

      test('should get category depth', () async {
        when(
          () => mockCategoryRepository.getCategoryDepth(
            'test-user-id',
            'category-id',
          ),
        ).thenAnswer((_) async => 2);

        final notifier = container.read(
          subcategoryValidationNotifierProvider.notifier,
        );
        final depth = await notifier.getCategoryDepth(
          'test-user-id',
          'category-id',
        );

        expect(depth, equals(2));
        verify(
          () => mockCategoryRepository.getCategoryDepth(
            'test-user-id',
            'category-id',
          ),
        ).called(1);
      });
    });

    group('Helper Methods', () {
      test(
        'CategoryCreateNotifier should have _invalidateCategoryProviders method',
        () {
          final notifier = container.read(
            categoryCreateNotifierProvider.notifier,
          );
          // This method is private, but we can test its effects indirectly through createCategory
          expect(notifier, isA<CategoryCreateNotifier>());
        },
      );

      test(
        'CategoryUpdateNotifier should have _invalidateBasicCategory method',
        () {
          final notifier = container.read(
            categoryUpdateNotifierProvider.notifier,
          );
          // This method is private, but we can test its effects indirectly through update methods
          expect(notifier, isA<CategoryUpdateNotifier>());
        },
      );

      test(
        'SubcategoryUpdateNotifier should have invalidation helper methods',
        () {
          final notifier = container.read(
            subcategoryUpdateNotifierProvider.notifier,
          );
          // These methods are private, but we can test their effects indirectly
          expect(notifier, isA<SubcategoryUpdateNotifier>());
        },
      );
    });
  });

  group('Provider Integration Tests', () {
    test('categoryStatsProvider should handle repository call', () {
      when(
        () => mockCategoryRepository.getUserCategorySummary('test-user-id'),
      ).thenAnswer((_) async => {'total': 5});

      // Reading the provider should not crash
      final provider = container.read(categoryStatsProvider);
      expect(provider, isNotNull);
    });

    // Skip Firebase-dependent test for now
    test(
      'categoryDeletionServiceProvider should create service with dependencies',
      () {
        // This test requires Firebase initialization which is complex in unit tests
        // TODO(test): Add proper Firebase mocking or move to integration tests
        expect(true, isTrue); // Placeholder
      },
      skip: 'Firebase initialization required',
    );
  });
}
