import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/repositories/interfaces/category_repository.dart';
import 'package:budapp/data/repositories/interfaces/transaction_repository.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/features/categories/services/category_deletion_service.dart';
import 'package:budapp/providers/repository_providers.dart';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockCategoryRepository extends Mock implements ICategoryRepository {}

class MockTransactionRepository extends Mock
    implements ITransactionRepository {}

class MockAuthService extends Mock implements AuthService {}

class MockUser extends Mock implements User {}

// Removed MockProviderContainer - using regular ProviderContainer instead

void main() {
  late MockCategoryRepository mockCategoryRepository;
  late MockTransactionRepository mockTransactionRepository;
  late MockAuthService mockAuthService;
  late MockUser mockUser;
  late ProviderContainer container;

  setUpAll(() {
    registerFallbackValue(
      Category.create(
        userId: 'fallback-user',
        name: 'Fallback Category',
        type: CategoryType.expense,
        color: '#FFFFFF',
      ),
    );
  });

  setUp(() {
    mockCategoryRepository = MockCategoryRepository();
    mockTransactionRepository = MockTransactionRepository();
    mockAuthService = MockAuthService();
    mockUser = MockUser();

    // Setup mock user
    when(() => mockUser.uid).thenReturn('test-user-id');
    when(() => mockAuthService.currentUser).thenReturn(mockUser);

    container = ProviderContainer(
      overrides: [
        categoryRepositoryProvider.overrideWithValue(mockCategoryRepository),
        transactionRepositoryProvider.overrideWithValue(
          mockTransactionRepository,
        ),
        authServiceProvider.overrideWithValue(mockAuthService),
      ],
    );
  });

  tearDown(() {
    container.dispose();
  });

  group('Category Providers - Authentication', () {
    test(
      '_getAuthenticatedUserId should return user ID when authenticated',
      () {
        final userId = container.read(categoryListProvider);
        expect(userId, isNotNull);
        verify(() => mockAuthService.currentUser).called(1);
      },
    );

    test(
      '_getAuthenticatedUserId should throw when user is not authenticated',
      () {
        when(() => mockAuthService.currentUser).thenReturn(null);

        final result = container.read(categoryListProvider);

        expect(result.hasError, isTrue);
        expect(result.error, isA<Exception>());
        expect(result.error.toString(), contains('User not authenticated'));
      },
    );
  });

  group('Category List Provider', () {
    test('categoryListProvider should stream user categories', () async {
      final testCategories = [
        Category.create(
          userId: 'test-user-id',
          name: 'Food',
          type: CategoryType.expense,
          color: '#FF5733',
        ).copyWith(id: 'cat1'),
        Category.create(
          userId: 'test-user-id',
          name: 'Salary',
          type: CategoryType.income,
          color: '#33FF57',
        ).copyWith(id: 'cat2'),
      ];

      when(
        () => mockCategoryRepository.watchUserCategories('test-user-id'),
      ).thenAnswer((_) => Stream.value(testCategories));

      final asyncValue = await container.read(categoryListProvider.future);

      expect(asyncValue, equals(testCategories));
      verify(
        () => mockCategoryRepository.watchUserCategories('test-user-id'),
      ).called(1);
    });

    test('categoryListProvider should handle repository errors', () async {
      when(
        () => mockCategoryRepository.watchUserCategories('test-user-id'),
      ).thenAnswer((_) => Stream.error(Exception('Database error')));

      expect(
        () => container.read(categoryListProvider.future),
        throwsA(isA<Exception>()),
      );
    });
  });

  group('Category Provider (Family)', () {
    test('categoryProvider should stream specific category', () async {
      const categoryId = 'test-category-id';
      final testCategory = Category.create(
        userId: 'test-user-id',
        name: 'Food',
        type: CategoryType.expense,
        color: '#FF5733',
      ).copyWith(id: categoryId);

      when(
        () => mockCategoryRepository.watchCategoryForUser(
          'test-user-id',
          categoryId,
        ),
      ).thenAnswer((_) => Stream.value(testCategory));

      final asyncValue = await container.read(
        categoryProvider(categoryId).future,
      );

      expect(asyncValue, equals(testCategory));
      verify(
        () => mockCategoryRepository.watchCategoryForUser(
          'test-user-id',
          categoryId,
        ),
      ).called(1);
    });

    test('categoryProvider should handle null category', () async {
      const categoryId = 'non-existent-id';

      when(
        () => mockCategoryRepository.watchCategoryForUser(
          'test-user-id',
          categoryId,
        ),
      ).thenAnswer((_) => Stream.value(null));

      final asyncValue = await container.read(
        categoryProvider(categoryId).future,
      );

      expect(asyncValue, isNull);
    });
  });

  group('Filtered Category Providers', () {
    final testCategories = [
      Category.create(
        userId: 'test-user-id',
        name: 'Food',
        type: CategoryType.expense,
        color: '#FF5733',
      ).copyWith(id: 'cat1', isActive: true),
      Category.create(
        userId: 'test-user-id',
        name: 'Salary',
        type: CategoryType.income,
        color: '#33FF57',
      ).copyWith(id: 'cat2', isActive: true),
      Category.create(
        userId: 'test-user-id',
        name: 'Inactive',
        type: CategoryType.expense,
        color: '#FF3333',
      ).copyWith(id: 'cat3', isActive: false),
    ];

    setUp(() {
      when(
        () => mockCategoryRepository.watchUserCategories('test-user-id'),
      ).thenAnswer((_) => Stream.value(testCategories));
    });

    test(
      'activeCategoriesProvider should filter active categories only',
      () async {
        final asyncValue = await container.read(
          activeCategoriesProvider.future,
        );

        expect(asyncValue.length, equals(2));
        expect(asyncValue.every((cat) => cat.isActive), isTrue);
        expect(asyncValue.map((cat) => cat.id), containsAll(['cat1', 'cat2']));
      },
    );

    test(
      'incomeCategoriesProvider should filter active income categories',
      () async {
        final asyncValue = await container.read(
          incomeCategoriesProvider.future,
        );

        expect(asyncValue.length, equals(1));
        expect(asyncValue.first.type, equals(CategoryType.income));
        expect(asyncValue.first.isActive, isTrue);
        expect(asyncValue.first.id, equals('cat2'));
      },
    );

    test(
      'expenseCategoriesProvider should filter active expense categories',
      () async {
        final asyncValue = await container.read(
          expenseCategoriesProvider.future,
        );

        expect(asyncValue.length, equals(1));
        expect(asyncValue.first.type, equals(CategoryType.expense));
        expect(asyncValue.first.isActive, isTrue);
        expect(asyncValue.first.id, equals('cat1'));
      },
    );

    test('rootCategoriesProvider should filter root categories', () async {
      final rootCategories = testCategories
          .where((cat) => cat.parentId == null && cat.isActive)
          .toList();

      final asyncValue = await container.read(rootCategoriesProvider.future);

      expect(asyncValue.length, equals(rootCategories.length));
      expect(asyncValue.every((cat) => cat.parentId == null), isTrue);
      expect(asyncValue.every((cat) => cat.isActive), isTrue);
    });

    test('rootCategoriesByTypeProvider should filter by type', () async {
      final asyncValue = await container.read(
        rootCategoriesByTypeProvider(CategoryType.expense).future,
      );

      expect(asyncValue.length, equals(1));
      expect(asyncValue.first.type, equals(CategoryType.expense));
      expect(asyncValue.first.parentId, isNull);
    });
  });

  group('Category Stats Provider', () {
    test('categoryStatsProvider should get user category summary', () async {
      final testStats = {
        'totalCategories': 5,
        'activeCategories': 4,
        'incomeCategories': 2,
        'expenseCategories': 3,
      };

      when(
        () => mockCategoryRepository.getUserCategorySummary('test-user-id'),
      ).thenAnswer((_) async => testStats);

      final asyncValue = await container.read(categoryStatsProvider.future);

      expect(asyncValue, equals(testStats));
      verify(
        () => mockCategoryRepository.getUserCategorySummary('test-user-id'),
      ).called(1);
    });

    test('categoryStatsProvider should handle repository errors', () async {
      when(
        () => mockCategoryRepository.getUserCategorySummary('test-user-id'),
      ).thenThrow(Exception('Stats error'));

      expect(
        () => container.read(categoryStatsProvider.future),
        throwsA(isA<Exception>()),
      );
    });
  });

  group('CategoryCreateNotifier', () {
    test('should create category successfully', () async {
      final testCategory = Category.create(
        userId: 'test-user-id',
        name: 'New Category',
        type: CategoryType.expense,
        color: '#FF5733',
      ).copyWith(id: 'new-category');

      when(
        () => mockCategoryRepository.createCategory(testCategory),
      ).thenAnswer((_) async => 'new-category');

      final notifier = container.read(categoryCreateNotifierProvider.notifier);
      await notifier.createCategory(testCategory);

      final state = container.read(categoryCreateNotifierProvider);
      expect(state.hasValue, isTrue);
      verify(
        () => mockCategoryRepository.createCategory(testCategory),
      ).called(1);
    });

    test('should handle creation errors', () async {
      final testCategory = Category.create(
        userId: 'test-user-id',
        name: 'New Category',
        type: CategoryType.expense,
        color: '#FF5733',
      ).copyWith(id: 'new-category');

      when(
        () => mockCategoryRepository.createCategory(testCategory),
      ).thenThrow(Exception('Creation failed'));

      final notifier = container.read(categoryCreateNotifierProvider.notifier);
      await notifier.createCategory(testCategory);

      final state = container.read(categoryCreateNotifierProvider);
      expect(state.hasError, isTrue);
      expect(state.error.toString(), contains('Creation failed'));
    });
  });

  group('CategoryUpdateNotifier', () {
    final testCategory = Category.create(
      userId: 'test-user-id',
      name: 'Updated Category',
      type: CategoryType.expense,
      color: '#FF5733',
    ).copyWith(id: 'test-category');

    test('should update category successfully', () async {
      when(
        () => mockCategoryRepository.updateCategory(
          'test-category',
          testCategory,
        ),
      ).thenAnswer((_) async {});

      final notifier = container.read(categoryUpdateNotifierProvider.notifier);
      await notifier.updateCategory('test-category', testCategory);

      final state = container.read(categoryUpdateNotifierProvider);
      expect(state.hasValue, isTrue);
      verify(
        () => mockCategoryRepository.updateCategory(
          'test-category',
          testCategory,
        ),
      ).called(1);
    });

    test('should deactivate category successfully', () async {
      when(
        () => mockCategoryRepository.updateCategory('test-category', any()),
      ).thenAnswer((_) async {});

      final notifier = container.read(categoryUpdateNotifierProvider.notifier);
      await notifier.deactivateCategory('test-category', testCategory);

      final state = container.read(categoryUpdateNotifierProvider);
      expect(state.hasValue, isTrue);
      verify(
        () => mockCategoryRepository.updateCategory('test-category', any()),
      ).called(1);
    });

    test('should reactivate category successfully', () async {
      when(
        () => mockCategoryRepository.updateCategory('test-category', any()),
      ).thenAnswer((_) async {});

      final notifier = container.read(categoryUpdateNotifierProvider.notifier);
      await notifier.reactivateCategory('test-category', testCategory);

      final state = container.read(categoryUpdateNotifierProvider);
      expect(state.hasValue, isTrue);
      verify(
        () => mockCategoryRepository.updateCategory('test-category', any()),
      ).called(1);
    });

    test('should handle update errors', () async {
      when(
        () => mockCategoryRepository.updateCategory(
          'test-category',
          testCategory,
        ),
      ).thenThrow(Exception('Update failed'));

      final notifier = container.read(categoryUpdateNotifierProvider.notifier);
      await notifier.updateCategory('test-category', testCategory);

      final state = container.read(categoryUpdateNotifierProvider);
      expect(state.hasError, isTrue);
      expect(state.error.toString(), contains('Update failed'));
    });
  });

  group('CategoryValidationNotifier', () {
    test('should validate category name uniqueness', () async {
      when(
        () => mockCategoryRepository.isCategoryNameUnique(
          'test-user-id',
          'Unique Name',
          excludeCategoryId: null,
        ),
      ).thenAnswer((_) async => true);

      final notifier = container.read(
        categoryValidationNotifierProvider.notifier,
      );
      final result = await notifier.validateCategoryName(
        'test-user-id',
        'Unique Name',
      );

      expect(result, isTrue);
      verify(
        () => mockCategoryRepository.isCategoryNameUnique(
          'test-user-id',
          'Unique Name',
          excludeCategoryId: null,
        ),
      ).called(1);
    });

    test('should validate complete category', () async {
      final testCategory = Category.create(
        userId: 'test-user-id',
        name: 'Valid Category',
        type: CategoryType.expense,
        color: '#FF5733',
      ).copyWith(id: 'test-category');

      when(
        () => mockCategoryRepository.validateCategory(testCategory),
      ).thenAnswer((_) async => true);

      final notifier = container.read(
        categoryValidationNotifierProvider.notifier,
      );
      final result = await notifier.validateCategory(testCategory);

      expect(result, isTrue);
      verify(
        () => mockCategoryRepository.validateCategory(testCategory),
      ).called(1);
    });

    test('should handle validation errors', () async {
      when(
        () => mockCategoryRepository.isCategoryNameUnique(
          'test-user-id',
          'Invalid Name',
          excludeCategoryId: null,
        ),
      ).thenThrow(Exception('Validation error'));

      final notifier = container.read(
        categoryValidationNotifierProvider.notifier,
      );
      final result = await notifier.validateCategoryName(
        'test-user-id',
        'Invalid Name',
      );

      expect(result, isFalse);
      final state = container.read(categoryValidationNotifierProvider);
      expect(state.hasError, isTrue);
    });
  });

  group('Subcategory Providers', () {
    const parentId = 'parent-category-id';
    final testSubcategories = [
      Category.create(
        userId: 'test-user-id',
        name: 'Subcategory 1',
        type: CategoryType.expense,
        color: '#FF5733',
      ).copyWith(id: 'sub1', parentId: parentId),
      Category.create(
        userId: 'test-user-id',
        name: 'Subcategory 2',
        type: CategoryType.expense,
        color: '#33FF57',
      ).copyWith(id: 'sub2', parentId: parentId),
    ];

    test('subcategoriesProvider should stream subcategories', () async {
      when(
        () =>
            mockCategoryRepository.watchSubcategories('test-user-id', parentId),
      ).thenAnswer((_) => Stream.value(testSubcategories));

      final asyncValue = await container.read(
        subcategoriesProvider(parentId).future,
      );

      expect(asyncValue, equals(testSubcategories));
      verify(
        () =>
            mockCategoryRepository.watchSubcategories('test-user-id', parentId),
      ).called(1);
    });

    test('subcategoryCountProvider should return count', () async {
      when(
        () => mockCategoryRepository.getSubcategoryCount(
          'test-user-id',
          parentId,
        ),
      ).thenAnswer((_) async => 2);

      final count = await container.read(
        subcategoryCountProvider(parentId).future,
      );

      expect(count, equals(2));
      verify(
        () => mockCategoryRepository.getSubcategoryCount(
          'test-user-id',
          parentId,
        ),
      ).called(1);
    });

    test('canHaveSubcategoriesProvider should check capability', () async {
      when(
        () => mockCategoryRepository.canHaveSubcategories(
          'test-user-id',
          parentId,
        ),
      ).thenAnswer((_) async => true);

      final canHave = await container.read(
        canHaveSubcategoriesProvider(parentId).future,
      );

      expect(canHave, isTrue);
      verify(
        () => mockCategoryRepository.canHaveSubcategories(
          'test-user-id',
          parentId,
        ),
      ).called(1);
    });
  });

  group('SubcategoryCreateNotifier', () {
    test('should create subcategory successfully', () async {
      final testSubcategory = Category.create(
        userId: 'test-user-id',
        name: 'New Subcategory',
        type: CategoryType.expense,
        color: '#FF5733',
      ).copyWith(id: 'new-sub', parentId: 'parent-id');

      when(
        () => mockCategoryRepository.createSubcategory(
          testSubcategory,
          'parent-id',
        ),
      ).thenAnswer((_) async => 'new-sub');

      final notifier = container.read(
        subcategoryCreateNotifierProvider.notifier,
      );
      await notifier.createSubcategory(testSubcategory, 'parent-id');

      final state = container.read(subcategoryCreateNotifierProvider);
      expect(state.hasValue, isTrue);
      verify(
        () => mockCategoryRepository.createSubcategory(
          testSubcategory,
          'parent-id',
        ),
      ).called(1);
    });
  });

  group('SubcategoryUpdateNotifier', () {
    test('should update subcategory successfully', () async {
      final testSubcategory = Category.create(
        userId: 'test-user-id',
        name: 'Updated Subcategory',
        type: CategoryType.expense,
        color: '#FF5733',
      ).copyWith(id: 'sub-id', parentId: 'parent-id');

      when(
        () =>
            mockCategoryRepository.updateSubcategory('sub-id', testSubcategory),
      ).thenAnswer((_) async {});

      final notifier = container.read(
        subcategoryUpdateNotifierProvider.notifier,
      );
      await notifier.updateSubcategory('sub-id', testSubcategory);

      final state = container.read(subcategoryUpdateNotifierProvider);
      expect(state.hasValue, isTrue);
      verify(
        () =>
            mockCategoryRepository.updateSubcategory('sub-id', testSubcategory),
      ).called(1);
    });

    test('should delete subcategory successfully', () async {
      when(
        () => mockCategoryRepository.deleteSubcategory('sub-id', 'parent-id'),
      ).thenAnswer((_) async {});

      final notifier = container.read(
        subcategoryUpdateNotifierProvider.notifier,
      );
      await notifier.deleteSubcategory('sub-id', 'parent-id');

      final state = container.read(subcategoryUpdateNotifierProvider);
      expect(state.hasValue, isTrue);
      verify(
        () => mockCategoryRepository.deleteSubcategory('sub-id', 'parent-id'),
      ).called(1);
    });

    test('should move subcategory successfully', () async {
      when(
        () => mockCategoryRepository.moveSubcategoryToParent(
          'sub-id',
          'old-parent',
          'new-parent',
        ),
      ).thenAnswer((_) async {});

      final notifier = container.read(
        subcategoryUpdateNotifierProvider.notifier,
      );
      await notifier.moveSubcategoryToParent(
        'sub-id',
        'old-parent',
        'new-parent',
      );

      final state = container.read(subcategoryUpdateNotifierProvider);
      expect(state.hasValue, isTrue);
      verify(
        () => mockCategoryRepository.moveSubcategoryToParent(
          'sub-id',
          'old-parent',
          'new-parent',
        ),
      ).called(1);
    });
  });

  group('SubcategoryValidationNotifier', () {
    test('should validate subcategory name uniqueness', () async {
      when(
        () => mockCategoryRepository.isSubcategoryNameUnique(
          'test-user-id',
          'parent-id',
          'Unique Sub',
          excludeSubcategoryId: null,
        ),
      ).thenAnswer((_) async => true);

      final notifier = container.read(
        subcategoryValidationNotifierProvider.notifier,
      );
      final result = await notifier.validateSubcategoryName(
        'test-user-id',
        'parent-id',
        'Unique Sub',
      );

      expect(result, isTrue);
    });

    test('should validate subcategory hierarchy', () async {
      when(
        () => mockCategoryRepository.validateSubcategoryHierarchy(
          'parent-id',
          'sub-id',
        ),
      ).thenAnswer((_) async => true);

      final notifier = container.read(
        subcategoryValidationNotifierProvider.notifier,
      );
      final result = await notifier.validateSubcategoryHierarchy(
        'parent-id',
        'sub-id',
      );

      expect(result, isTrue);
    });

    test('should get category depth', () async {
      when(
        () => mockCategoryRepository.getCategoryDepth(
          'test-user-id',
          'category-id',
        ),
      ).thenAnswer((_) async => 2);

      final notifier = container.read(
        subcategoryValidationNotifierProvider.notifier,
      );
      final depth = await notifier.getCategoryDepth(
        'test-user-id',
        'category-id',
      );

      expect(depth, equals(2));
    });
  });

  group('CategoryDeletionService Provider', () {
    test('should create CategoryDeletionService with dependencies', () {
      final service = container.read(categoryDeletionServiceProvider);

      expect(service, isA<CategoryDeletionService>());
    });
  });

  group('Provider Error Handling', () {
    test('stream providers should handle loading state', () {
      when(
        () => mockCategoryRepository.watchUserCategories('test-user-id'),
      ).thenAnswer((_) => const Stream.empty());

      final asyncValue = container.read(activeCategoriesProvider);

      // Initial state should be loading or provide empty stream
      expect(asyncValue.isLoading || asyncValue.hasValue, isTrue);
    });

    test('stream providers should handle error state properly', () async {
      when(
        () => mockCategoryRepository.watchUserCategories('test-user-id'),
      ).thenAnswer((_) => Stream.error(Exception('Stream error')));

      expect(
        () => container.read(activeCategoriesProvider.future),
        throwsA(isA<Exception>()),
      );
    });
  });
}
