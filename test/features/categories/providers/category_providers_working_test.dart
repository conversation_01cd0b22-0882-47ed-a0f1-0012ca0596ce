import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/repositories/interfaces/category_repository.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/mock_data_factory.dart';

// Mock classes
class MockCategoryRepository extends Mock implements ICategoryRepository {}

class MockAuthService extends Mock implements AuthService {}

class MockUser extends Mock implements User {}

void main() {
  late MockCategoryRepository mockCategoryRepository;
  late MockAuthService mockAuthService;
  late MockUser mockUser;
  late ProviderContainer container;

  setUpAll(() {
    // Use MockDataFactory to create fallback values instead of Fake classes
    registerFallbackValue(MockDataFactory.createCategory());
  });

  setUp(() {
    mockCategoryRepository = MockCategoryRepository();
    mockAuthService = MockAuthService();
    mockUser = MockUser();

    // Setup mock user
    when(() => mockUser.uid).thenReturn('test-user-id');
    when(() => mockAuthService.currentUser).thenReturn(mockUser);

    container = ProviderContainer(
      overrides: [
        categoryRepositoryProvider.overrideWithValue(mockCategoryRepository),
        authServiceProvider.overrideWithValue(mockAuthService),
      ],
    );
  });

  tearDown(() {
    container.dispose();
  });

  group('Category Providers - AsyncNotifier Tests', () {
    group('CategoryCreateNotifier', () {
      test('should have correct initial state', () {
        final notifier = container.read(
          categoryCreateNotifierProvider.notifier,
        );
        final state = container.read(categoryCreateNotifierProvider);

        expect(notifier, isA<CategoryCreateNotifier>());
        expect(state.isLoading, isFalse);
      });

      test('should handle creation success', () async {
        final category = Category.create(
          userId: 'test-user-id',
          name: 'Test Category',
          type: CategoryType.expense,
          color: '#FF5733',
        );

        when(
          () => mockCategoryRepository.createCategory(any()),
        ).thenAnswer((_) async => 'new-id');

        final notifier = container.read(
          categoryCreateNotifierProvider.notifier,
        );
        await notifier.createCategory(category);

        final state = container.read(categoryCreateNotifierProvider);
        expect(state.hasValue, isTrue);
        verify(() => mockCategoryRepository.createCategory(any())).called(1);
      });

      test('should handle creation error', () async {
        final category = Category.create(
          userId: 'test-user-id',
          name: 'Test Category',
          type: CategoryType.expense,
          color: '#FF5733',
        );

        when(
          () => mockCategoryRepository.createCategory(any()),
        ).thenThrow(Exception('Creation failed'));

        final notifier = container.read(
          categoryCreateNotifierProvider.notifier,
        );
        await notifier.createCategory(category);

        final state = container.read(categoryCreateNotifierProvider);
        expect(state.hasError, isTrue);
        expect(state.error.toString(), contains('Creation failed'));
      });

      test(
        'should test private _invalidateCategoryProviders method effect',
        () async {
          // We can't directly test private methods, but we can test their effects
          final category = Category.create(
            userId: 'test-user-id',
            name: 'Test Category',
            type: CategoryType.expense,
            color: '#FF5733',
          );

          when(
            () => mockCategoryRepository.createCategory(any()),
          ).thenAnswer((_) async => 'new-id');

          final notifier = container.read(
            categoryCreateNotifierProvider.notifier,
          );

          // The private method should be called during createCategory
          await notifier.createCategory(category);

          // Verify the method had an effect by checking the result
          final state = container.read(categoryCreateNotifierProvider);
          expect(state.hasValue, isTrue);
        },
      );
    });

    group('CategoryUpdateNotifier', () {
      test('should have correct initial state', () {
        final notifier = container.read(
          categoryUpdateNotifierProvider.notifier,
        );
        final state = container.read(categoryUpdateNotifierProvider);

        expect(notifier, isA<CategoryUpdateNotifier>());
        expect(state.isLoading, isFalse);
      });

      test('should handle update success', () async {
        final category = Category.create(
          userId: 'test-user-id',
          name: 'Updated Category',
          type: CategoryType.expense,
          color: '#FF5733',
        ).copyWith(id: 'test-id');

        when(
          () => mockCategoryRepository.updateCategory(any(), any()),
        ).thenAnswer((_) async {});

        final notifier = container.read(
          categoryUpdateNotifierProvider.notifier,
        );
        await notifier.updateCategory('test-id', category);

        final state = container.read(categoryUpdateNotifierProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockCategoryRepository.updateCategory(any(), any()),
        ).called(1);
      });

      test('should handle deactivation', () async {
        final category = Category.create(
          userId: 'test-user-id',
          name: 'Test Category',
          type: CategoryType.expense,
          color: '#FF5733',
        ).copyWith(id: 'test-id');

        when(
          () => mockCategoryRepository.updateCategory(any(), any()),
        ).thenAnswer((_) async {});

        final notifier = container.read(
          categoryUpdateNotifierProvider.notifier,
        );
        await notifier.deactivateCategory('test-id', category);

        final state = container.read(categoryUpdateNotifierProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockCategoryRepository.updateCategory(any(), any()),
        ).called(1);
      });

      test('should handle reactivation', () async {
        final category = Category.create(
          userId: 'test-user-id',
          name: 'Test Category',
          type: CategoryType.expense,
          color: '#FF5733',
        ).copyWith(id: 'test-id');

        when(
          () => mockCategoryRepository.updateCategory(any(), any()),
        ).thenAnswer((_) async {});

        final notifier = container.read(
          categoryUpdateNotifierProvider.notifier,
        );
        await notifier.reactivateCategory('test-id', category);

        final state = container.read(categoryUpdateNotifierProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockCategoryRepository.updateCategory(any(), any()),
        ).called(1);
      });

      test(
        'should test private _invalidateBasicCategory method effect',
        () async {
          final category = Category.create(
            userId: 'test-user-id',
            name: 'Test Category',
            type: CategoryType.expense,
            color: '#FF5733',
          ).copyWith(id: 'test-id');

          when(
            () => mockCategoryRepository.updateCategory(any(), any()),
          ).thenAnswer((_) async {});

          final notifier = container.read(
            categoryUpdateNotifierProvider.notifier,
          );

          // The private method should be called during updateCategory
          await notifier.updateCategory('test-id', category);

          // Verify the method had an effect by checking the result
          final state = container.read(categoryUpdateNotifierProvider);
          expect(state.hasValue, isTrue);
        },
      );
    });

    group('CategoryValidationNotifier', () {
      test('should have correct initial state', () {
        final notifier = container.read(
          categoryValidationNotifierProvider.notifier,
        );
        final state = container.read(categoryValidationNotifierProvider);

        expect(notifier, isA<CategoryValidationNotifier>());
        expect(state.value, isTrue); // Default initial value
      });

      test('should validate category name uniqueness', () async {
        when(
          () => mockCategoryRepository.isCategoryNameUnique(
            any(),
            any(),
            excludeCategoryId: any(named: 'excludeCategoryId'),
          ),
        ).thenAnswer((_) async => true);

        final notifier = container.read(
          categoryValidationNotifierProvider.notifier,
        );
        final result = await notifier.validateCategoryName(
          'test-user-id',
          'Unique Name',
        );

        expect(result, isTrue);
        verify(
          () => mockCategoryRepository.isCategoryNameUnique(
            any(),
            any(),
            excludeCategoryId: any(named: 'excludeCategoryId'),
          ),
        ).called(1);
      });

      test('should validate complete category', () async {
        when(
          () => mockCategoryRepository.validateCategory(any()),
        ).thenAnswer((_) async => true);

        final category = Category.create(
          userId: 'test-user-id',
          name: 'Valid Category',
          type: CategoryType.expense,
          color: '#FF5733',
        );

        final notifier = container.read(
          categoryValidationNotifierProvider.notifier,
        );
        final result = await notifier.validateCategory(category);

        expect(result, isTrue);
        verify(() => mockCategoryRepository.validateCategory(any())).called(1);
      });

      test('should handle validation errors gracefully', () async {
        when(
          () => mockCategoryRepository.isCategoryNameUnique(
            any(),
            any(),
            excludeCategoryId: any(named: 'excludeCategoryId'),
          ),
        ).thenThrow(Exception('Validation error'));

        final notifier = container.read(
          categoryValidationNotifierProvider.notifier,
        );
        final result = await notifier.validateCategoryName(
          'test-user-id',
          'Invalid Name',
        );

        expect(result, isFalse); // Should return false on error
        final state = container.read(categoryValidationNotifierProvider);
        expect(state.hasError, isTrue);
      });
    });

    group('SubcategoryCreateNotifier', () {
      test('should have correct initial state', () {
        final notifier = container.read(
          subcategoryCreateNotifierProvider.notifier,
        );
        final state = container.read(subcategoryCreateNotifierProvider);

        expect(notifier, isA<SubcategoryCreateNotifier>());
        expect(state.isLoading, isFalse);
      });

      test('should handle subcategory creation', () async {
        when(
          () => mockCategoryRepository.createSubcategory(any(), any()),
        ).thenAnswer((_) async => 'new-sub-id');

        final subcategory = Category.create(
          userId: 'test-user-id',
          name: 'Test Subcategory',
          type: CategoryType.expense,
          color: '#FF5733',
        ).copyWith(parentId: 'parent-id');

        final notifier = container.read(
          subcategoryCreateNotifierProvider.notifier,
        );
        await notifier.createSubcategory(subcategory, 'parent-id');

        final state = container.read(subcategoryCreateNotifierProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockCategoryRepository.createSubcategory(any(), any()),
        ).called(1);
      });

      test(
        'should test private _invalidateSubcategoryCreation method effect',
        () async {
          when(
            () => mockCategoryRepository.createSubcategory(any(), any()),
          ).thenAnswer((_) async => 'new-sub-id');

          final subcategory = Category.create(
            userId: 'test-user-id',
            name: 'Test Subcategory',
            type: CategoryType.expense,
            color: '#FF5733',
          ).copyWith(parentId: 'parent-id');

          final notifier = container.read(
            subcategoryCreateNotifierProvider.notifier,
          );

          // The private method should be called during createSubcategory
          await notifier.createSubcategory(subcategory, 'parent-id');

          // Verify the method had an effect by checking the result
          final state = container.read(subcategoryCreateNotifierProvider);
          expect(state.hasValue, isTrue);
        },
      );
    });

    group('SubcategoryUpdateNotifier', () {
      test('should have correct initial state', () {
        final notifier = container.read(
          subcategoryUpdateNotifierProvider.notifier,
        );
        final state = container.read(subcategoryUpdateNotifierProvider);

        expect(notifier, isA<SubcategoryUpdateNotifier>());
        expect(state.isLoading, isFalse);
      });

      test('should handle subcategory update', () async {
        when(
          () => mockCategoryRepository.updateSubcategory(any(), any()),
        ).thenAnswer((_) async {});

        final subcategory = Category.create(
          userId: 'test-user-id',
          name: 'Updated Subcategory',
          type: CategoryType.expense,
          color: '#FF5733',
        ).copyWith(id: 'sub-id', parentId: 'parent-id');

        final notifier = container.read(
          subcategoryUpdateNotifierProvider.notifier,
        );
        await notifier.updateSubcategory('sub-id', subcategory);

        final state = container.read(subcategoryUpdateNotifierProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockCategoryRepository.updateSubcategory(any(), any()),
        ).called(1);
      });

      test('should handle subcategory deletion', () async {
        when(
          () => mockCategoryRepository.deleteSubcategory(any(), any()),
        ).thenAnswer((_) async {});

        final notifier = container.read(
          subcategoryUpdateNotifierProvider.notifier,
        );
        await notifier.deleteSubcategory('sub-id', 'parent-id');

        final state = container.read(subcategoryUpdateNotifierProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockCategoryRepository.deleteSubcategory(any(), any()),
        ).called(1);
      });

      test('should handle subcategory move', () async {
        when(
          () => mockCategoryRepository.moveSubcategoryToParent(
            any(),
            any(),
            any(),
          ),
        ).thenAnswer((_) async {});

        final notifier = container.read(
          subcategoryUpdateNotifierProvider.notifier,
        );
        await notifier.moveSubcategoryToParent(
          'sub-id',
          'old-parent',
          'new-parent',
        );

        final state = container.read(subcategoryUpdateNotifierProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockCategoryRepository.moveSubcategoryToParent(
            any(),
            any(),
            any(),
          ),
        ).called(1);
      });

      test('should test private helper methods effects', () async {
        // Test all the private invalidation methods through their public usage

        // Test _invalidateSubcategoryUpdate
        when(
          () => mockCategoryRepository.updateSubcategory(any(), any()),
        ).thenAnswer((_) async {});

        final subcategory = Category.create(
          userId: 'test-user-id',
          name: 'Test Sub',
          type: CategoryType.expense,
          color: '#FF5733',
        ).copyWith(id: 'sub-id', parentId: 'parent-id');

        final notifier = container.read(
          subcategoryUpdateNotifierProvider.notifier,
        );
        await notifier.updateSubcategory('sub-id', subcategory);

        expect(
          container.read(subcategoryUpdateNotifierProvider).hasValue,
          isTrue,
        );

        // Test _invalidateSubcategoryDeletion
        when(
          () => mockCategoryRepository.deleteSubcategory(any(), any()),
        ).thenAnswer((_) async {});

        await notifier.deleteSubcategory('sub-id', 'parent-id');
        expect(
          container.read(subcategoryUpdateNotifierProvider).hasValue,
          isTrue,
        );

        // Test _invalidateSubcategoryMove
        when(
          () => mockCategoryRepository.moveSubcategoryToParent(
            any(),
            any(),
            any(),
          ),
        ).thenAnswer((_) async {});

        await notifier.moveSubcategoryToParent(
          'sub-id',
          'old-parent',
          'new-parent',
        );
        expect(
          container.read(subcategoryUpdateNotifierProvider).hasValue,
          isTrue,
        );
      });
    });

    group('SubcategoryValidationNotifier', () {
      test('should have correct initial state', () {
        final notifier = container.read(
          subcategoryValidationNotifierProvider.notifier,
        );
        final state = container.read(subcategoryValidationNotifierProvider);

        expect(notifier, isA<SubcategoryValidationNotifier>());
        expect(state.value, isTrue); // Default initial value
      });

      test('should validate subcategory name uniqueness', () async {
        when(
          () => mockCategoryRepository.isSubcategoryNameUnique(
            any(),
            any(),
            any(),
            excludeSubcategoryId: any(named: 'excludeSubcategoryId'),
          ),
        ).thenAnswer((_) async => true);

        final notifier = container.read(
          subcategoryValidationNotifierProvider.notifier,
        );
        final result = await notifier.validateSubcategoryName(
          'test-user-id',
          'parent-id',
          'Unique Sub',
        );

        expect(result, isTrue);
        verify(
          () => mockCategoryRepository.isSubcategoryNameUnique(
            any(),
            any(),
            any(),
            excludeSubcategoryId: any(named: 'excludeSubcategoryId'),
          ),
        ).called(1);
      });

      test('should validate subcategory hierarchy', () async {
        when(
          () =>
              mockCategoryRepository.validateSubcategoryHierarchy(any(), any()),
        ).thenAnswer((_) async => true);

        final notifier = container.read(
          subcategoryValidationNotifierProvider.notifier,
        );
        final result = await notifier.validateSubcategoryHierarchy(
          'parent-id',
          'sub-id',
        );

        expect(result, isTrue);
        verify(
          () =>
              mockCategoryRepository.validateSubcategoryHierarchy(any(), any()),
        ).called(1);
      });

      test('should get category depth', () async {
        when(
          () => mockCategoryRepository.getCategoryDepth(any(), any()),
        ).thenAnswer((_) async => 2);

        final notifier = container.read(
          subcategoryValidationNotifierProvider.notifier,
        );
        final depth = await notifier.getCategoryDepth(
          'test-user-id',
          'category-id',
        );

        expect(depth, equals(2));
        verify(
          () => mockCategoryRepository.getCategoryDepth(any(), any()),
        ).called(1);
      });
    });
  });

  group('Error Handling Tests', () {
    test(
      'CategoryCreateNotifier should handle AsyncValue.guard properly',
      () async {
        final category = Category.create(
          userId: 'test-user-id',
          name: 'Test Category',
          type: CategoryType.expense,
          color: '#FF5733',
        );

        when(
          () => mockCategoryRepository.createCategory(any()),
        ).thenThrow(Exception('Repository error'));

        final notifier = container.read(
          categoryCreateNotifierProvider.notifier,
        );
        await notifier.createCategory(category);

        final state = container.read(categoryCreateNotifierProvider);
        expect(state.hasError, isTrue);
        expect(state.asError?.error, isA<Exception>());
      },
    );

    test(
      'CategoryUpdateNotifier should handle AsyncValue.guard properly',
      () async {
        final category = Category.create(
          userId: 'test-user-id',
          name: 'Test Category',
          type: CategoryType.expense,
          color: '#FF5733',
        ).copyWith(id: 'test-id');

        when(
          () => mockCategoryRepository.updateCategory(any(), any()),
        ).thenThrow(Exception('Update error'));

        final notifier = container.read(
          categoryUpdateNotifierProvider.notifier,
        );
        await notifier.updateCategory('test-id', category);

        final state = container.read(categoryUpdateNotifierProvider);
        expect(state.hasError, isTrue);
        expect(state.asError?.error, isA<Exception>());
      },
    );
  });
}
