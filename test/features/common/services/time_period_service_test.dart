import 'package:budapp/features/common/models/time_period.dart';
import 'package:budapp/features/common/services/time_period_service.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('TimePeriodService', () {
    test('should get current month period', () {
      final currentMonth = TimePeriodService.getCurrentMonth();
      final now = DateTime.now();

      expect(currentMonth.type, PeriodType.monthly);
      expect(currentMonth.year, now.year);
      expect(currentMonth.month, now.month);
      expect(currentMonth.isCurrent, true);
      expect(currentMonth.startDate.day, 1);
    });

    test('should get specific month period', () {
      final period = TimePeriodService.getMonthPeriod(2024, 12);

      expect(period.type, PeriodType.monthly);
      expect(period.year, 2024);
      expect(period.month, 12);
      expect(period.startDate, DateTime(2024, 12, 1));
      expect(period.endDate, DateTime(2024, 12, 31));
      expect(period.displayName, 'December 2024');
      expect(period.dateRangeText, '01 Dec - 31 Dec');
    });

    test('should get previous month correctly', () {
      final currentPeriod = TimePeriodService.getMonthPeriod(2024, 12);
      final previousPeriod = TimePeriodService.getPreviousMonth(currentPeriod);

      expect(previousPeriod.year, 2024);
      expect(previousPeriod.month, 11);
    });

    test('should get previous month across year boundary', () {
      final currentPeriod = TimePeriodService.getMonthPeriod(2024, 1);
      final previousPeriod = TimePeriodService.getPreviousMonth(currentPeriod);

      expect(previousPeriod.year, 2023);
      expect(previousPeriod.month, 12);
    });

    test('should get next month correctly', () {
      final currentPeriod = TimePeriodService.getMonthPeriod(2024, 11);
      final nextPeriod = TimePeriodService.getNextMonth(currentPeriod);

      expect(nextPeriod.year, 2024);
      expect(nextPeriod.month, 12);
    });

    test('should get next month across year boundary', () {
      final currentPeriod = TimePeriodService.getMonthPeriod(2024, 12);
      final nextPeriod = TimePeriodService.getNextMonth(currentPeriod);

      expect(nextPeriod.year, 2025);
      expect(nextPeriod.month, 1);
    });

    test('should throw error for non-monthly period navigation', () {
      final weeklyPeriod = TimePeriod(
        type: PeriodType.weekly,
        startDate: DateTime(2024, 12, 1),
        endDate: DateTime(2024, 12, 7),
        displayName: 'Week 1',
        dateRangeText: '01 Dec - 07 Dec',
        year: 2024,
      );

      expect(
        () => TimePeriodService.getPreviousMonth(weeklyPeriod),
        throwsArgumentError,
      );
      expect(
        () => TimePeriodService.getNextMonth(weeklyPeriod),
        throwsArgumentError,
      );
    });

    test('should get available periods', () {
      final periods = TimePeriodService.getAvailablePeriods();

      expect(periods.isNotEmpty, true);
      expect(periods.length, greaterThan(20)); // Should have at least 24 months

      // Should be sorted in descending order (newest first)
      for (var i = 0; i < periods.length - 1; i++) {
        expect(
          periods[i].startDate.isAfter(periods[i + 1].startDate) ||
              periods[i].startDate.isAtSameMomentAs(periods[i + 1].startDate),
          true,
        );
      }
    });

    test('should check if period is selectable', () {
      final now = DateTime.now();
      final currentMonth = TimePeriodService.getMonthPeriod(
        now.year,
        now.month,
      );
      final pastMonth = TimePeriodService.getMonthPeriod(
        now.year,
        now.month - 1,
      );
      final futureMonth = TimePeriodService.getMonthPeriod(
        now.year,
        now.month + 1,
      );
      final farFutureMonth = TimePeriodService.getMonthPeriod(
        now.year + 6, // 6 years in the future (beyond the 5-year limit)
        now.month,
      );

      expect(TimePeriodService.isPeriodSelectable(currentMonth), true);
      expect(TimePeriodService.isPeriodSelectable(pastMonth), true);
      expect(
        TimePeriodService.isPeriodSelectable(futureMonth),
        true,
      ); // Now allows future periods
      expect(
        TimePeriodService.isPeriodSelectable(farFutureMonth),
        false,
      ); // Beyond 5-year limit
    });

    test('should convert period to storage string', () {
      final period = TimePeriodService.getMonthPeriod(2024, 12);
      final storageString = TimePeriodService.periodToStorageString(period);

      expect(storageString, '2024_12');
    });

    test('should convert storage string to period', () {
      final period = TimePeriodService.periodFromStorageString('2024_12');

      expect(period, isNotNull);
      expect(period!.year, 2024);
      expect(period.month, 12);
    });

    test('should return null for invalid storage string', () {
      expect(TimePeriodService.periodFromStorageString(null), isNull);
      expect(TimePeriodService.periodFromStorageString(''), isNull);
      expect(TimePeriodService.periodFromStorageString('invalid'), isNull);
      expect(TimePeriodService.periodFromStorageString('2024'), isNull);
    });

    test('should get current week period', () {
      final currentWeek = TimePeriodService.getCurrentWeek();

      expect(currentWeek.type, PeriodType.weekly);
      expect(currentWeek.isCurrent, true);
      expect(currentWeek.durationInDays, 7);
    });

    test('should get current year period', () {
      final currentYear = TimePeriodService.getCurrentYear();
      final now = DateTime.now();

      expect(currentYear.type, PeriodType.yearly);
      expect(currentYear.year, now.year);
      expect(currentYear.isCurrent, true);
      expect(currentYear.startDate, DateTime(now.year, 1, 1));
      expect(currentYear.endDate, DateTime(now.year, 12, 31));
    });
  });
}
