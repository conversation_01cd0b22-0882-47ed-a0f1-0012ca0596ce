import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/common/widgets/app_bar_helpers.dart';
import 'package:budapp/features/common/widgets/time_period_selector.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../helpers/test_wrapper.dart';

void main() {
  group('AppBarHelpers', () {
    group('createTimePeriodAppBar', () {
      testWidgets('creates AppBar with TimePeriodSelector and title', (
        tester,
      ) async {
        final appBar = AppBarHelpers.createTimePeriodAppBar(
          title: 'Test Screen',
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(appBar: appBar, body: const SizedBox()),
          ),
        );

        // Should find the AppBar
        expect(find.byType(AppBar), findsOneWidget);

        // Should find TimePeriodSelector
        expect(find.byType(TimePeriodSelector), findsOneWidget);

        // Should find the title text
        expect(find.text('Test Screen'), findsOneWidget);

        // Should find the Row layout in title
        expect(find.byType(Row), findsAtLeastNWidgets(1));
      });

      testWidgets('includes actions when provided', (tester) async {
        final appBar = AppBarHelpers.createTimePeriodAppBar(
          title: 'Test Screen',
          actions: [
            IconButton(onPressed: () {}, icon: const Icon(Icons.search)),
          ],
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(appBar: appBar, body: const SizedBox()),
          ),
        );

        // Should find the action button
        expect(find.byIcon(Icons.search), findsOneWidget);
      });

      testWidgets('includes bottom widget when provided', (tester) async {
        final appBar = AppBarHelpers.createTimePeriodAppBar(
          title: 'Test Screen',
          bottom: const TabBar(
            tabs: [
              Tab(text: 'Tab 1'),
              Tab(text: 'Tab 2'),
            ],
          ),
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DefaultTabController(
              length: 2,
              child: Scaffold(appBar: appBar, body: const SizedBox()),
            ),
          ),
        );

        // Should find the TabBar
        expect(find.byType(TabBar), findsOneWidget);
        expect(find.text('Tab 1'), findsOneWidget);
        expect(find.text('Tab 2'), findsOneWidget);
      });
    });

    group('createTimePeriodScrollableAppBar', () {
      testWidgets('creates AppBar with TimePeriodSelector and title', (
        tester,
      ) async {
        final appBar = AppBarHelpers.createTimePeriodScrollableAppBar(
          title: 'Test Screen',
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(
              appBar: appBar,
              body: Container(height: 1000, color: Colors.blue),
            ),
          ),
        );

        // Should find the AppBar
        expect(find.byType(AppBar), findsOneWidget);

        // Should find TimePeriodSelector
        expect(find.byType(TimePeriodSelector), findsOneWidget);

        // Should find the title text
        expect(find.text('Test Screen'), findsOneWidget);
      });

      testWidgets('creates AppBar with proper configuration', (tester) async {
        final appBar = AppBarHelpers.createTimePeriodScrollableAppBar(
          title: 'Test Screen',
        );

        // Should be an AppBar instance
        expect(appBar, isA<AppBar>());

        // Should have the correct title
        expect(appBar.title, isA<Widget>());

        // Should have proper elevation
        expect(appBar.elevation, equals(AppElevation.sm));
      });
    });

    group('createStandardAppBar', () {
      testWidgets('creates AppBar with title only', (tester) async {
        final appBar = AppBarHelpers.createStandardAppBar(title: 'Profile');

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(appBar: appBar, body: const SizedBox()),
          ),
        );

        // Should find the AppBar
        expect(find.byType(AppBar), findsOneWidget);

        // Should find the title text
        expect(find.text('Profile'), findsOneWidget);

        // Should NOT find TimePeriodSelector
        expect(find.byType(TimePeriodSelector), findsNothing);
      });

      testWidgets('centers title by default', (tester) async {
        final appBar = AppBarHelpers.createStandardAppBar(title: 'Profile');

        // Check that centerTitle is true by default
        expect(appBar.centerTitle, isTrue);
      });

      testWidgets('includes actions when provided', (tester) async {
        final appBar = AppBarHelpers.createStandardAppBar(
          title: 'Profile',
          actions: [
            IconButton(onPressed: () {}, icon: const Icon(Icons.settings)),
          ],
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(appBar: appBar, body: const SizedBox()),
          ),
        );

        // Should find the action button
        expect(find.byIcon(Icons.settings), findsOneWidget);
      });
    });

    group('createStandardScrollableAppBar', () {
      testWidgets('creates AppBar with title only', (tester) async {
        final appBar = AppBarHelpers.createStandardScrollableAppBar(
          title: 'Categories',
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(
              appBar: appBar,
              body: Container(height: 1000, color: Colors.blue),
            ),
          ),
        );

        // Should find the AppBar
        expect(find.byType(AppBar), findsOneWidget);

        // Should find the title text
        expect(find.text('Categories'), findsOneWidget);

        // Should NOT find TimePeriodSelector
        expect(find.byType(TimePeriodSelector), findsNothing);
      });

      testWidgets('creates AppBar with proper configuration', (tester) async {
        final appBar = AppBarHelpers.createStandardScrollableAppBar(
          title: 'Categories',
        );

        // Should be an AppBar instance
        expect(appBar, isA<AppBar>());

        // Should have the correct title
        expect(appBar.title, isA<Widget>());

        // Should have proper elevation
        expect(appBar.elevation, equals(AppElevation.sm));
      });
    });
  });
}
