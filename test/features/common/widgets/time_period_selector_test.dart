import 'package:budapp/features/common/services/time_period_service.dart';
import 'package:budapp/features/common/widgets/time_period_selector.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('TimePeriodSelector', () {
    Widget createTestWidget({
      bool showNavigation = false,
      bool showDateRange = true,
      bool compact = false,
      VoidCallback? onPeriodChanged,
    }) {
      return ProviderScope(
        child: MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('en')],
          home: Scaffold(
            body: TimePeriodSelector(
              showNavigation: showNavigation,
              showDateRange: showDateRange,
              compact: compact,
              onPeriodChanged: onPeriodChanged,
            ),
          ),
        ),
      );
    }

    testWidgets('should display current period information', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should display the current month
      final currentMonth = TimePeriodService.getCurrentMonth();
      expect(find.text(currentMonth.displayName), findsOneWidget);
      expect(find.text(currentMonth.dateRangeText), findsOneWidget);
    });

    testWidgets('should display in compact mode', (tester) async {
      await tester.pumpWidget(createTestWidget(compact: true));
      await tester.pumpAndSettle();

      // In compact mode, should show period and date range in single line
      final currentMonth = TimePeriodService.getCurrentMonth();
      final compactText =
          '${currentMonth.displayName} • ${currentMonth.dateRangeText}';
      expect(find.text(compactText), findsOneWidget);
    });

    testWidgets('should hide date range when showDateRange is false', (
      tester,
    ) async {
      await tester.pumpWidget(createTestWidget(showDateRange: false));
      await tester.pumpAndSettle();

      final currentMonth = TimePeriodService.getCurrentMonth();
      expect(find.text(currentMonth.displayName), findsOneWidget);
      expect(find.text(currentMonth.dateRangeText), findsNothing);
    });

    testWidgets('should show navigation buttons when enabled', (tester) async {
      await tester.pumpWidget(createTestWidget(showNavigation: true));
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.chevron_left), findsOneWidget);
      expect(find.byIcon(Icons.chevron_right), findsOneWidget);
    });

    testWidgets('should hide navigation buttons when disabled', (tester) async {
      await tester.pumpWidget(createTestWidget(showNavigation: false));
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.chevron_left), findsNothing);
      expect(find.byIcon(Icons.chevron_right), findsNothing);
    });

    testWidgets('should show dropdown icon', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.expand_more), findsOneWidget);
    });

    testWidgets('should be tappable', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find the InkWell widget and tap it
      final inkWell = find.byType(InkWell).first;
      await tester.tap(inkWell);
      await tester.pumpAndSettle();

      // Should show the modal (we can't easily test the modal opening in unit tests)
      // But we can verify the widget is tappable
      expect(inkWell, findsOneWidget);
    });

    testWidgets('should have proper semantics', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Check for semantics - the Semantics widget should exist
      expect(find.byType(Semantics), findsWidgets);
    });
  });

  group('CompactTimePeriodSelector', () {
    Widget createTestWidget({VoidCallback? onPeriodChanged}) {
      return ProviderScope(
        child: MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('en')],
          home: Scaffold(
            body: CompactTimePeriodSelector(onPeriodChanged: onPeriodChanged),
          ),
        ),
      );
    }

    testWidgets('should display in compact mode by default', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should display the current month in compact format
      final currentMonth = TimePeriodService.getCurrentMonth();
      final compactText =
          '${currentMonth.displayName} • ${currentMonth.dateRangeText}';
      expect(find.text(compactText), findsOneWidget);
    });

    testWidgets('should not show navigation buttons', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.chevron_left), findsNothing);
      expect(find.byIcon(Icons.chevron_right), findsNothing);
    });
  });
}
