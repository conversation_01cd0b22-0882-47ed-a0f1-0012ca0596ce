import 'package:budapp/features/common/models/time_period.dart';
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/features/common/services/time_period_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('TimePeriodProviders Generated Code Tests', () {
    late ProviderContainer container;

    setUp(() {
      SharedPreferences.setMockInitialValues({});
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    group('availablePeriodsProvider', () {
      test('should provide list of available periods', () {
        final periods = container.read(availablePeriodsProvider);

        expect(periods, isA<List<TimePeriod>>());
        expect(periods.isNotEmpty, true);
        expect(periods.every((p) => p.type == PeriodType.monthly), true);
      });

      test('should return same instance on multiple reads', () {
        final periods1 = container.read(availablePeriodsProvider);
        final periods2 = container.read(availablePeriodsProvider);

        expect(identical(periods1, periods2), true);
      });

      test('should have correct provider name', () {
        expect(availablePeriodsProvider.name, 'availablePeriodsProvider');
      });

      test('should be auto dispose provider', () {
        expect(
          availablePeriodsProvider,
          isA<AutoDisposeProvider<List<TimePeriod>>>(),
        );
      });
    });

    group('isCurrentMonthProvider', () {
      test('should return true when selected period is current month', () {
        final currentMonth = TimePeriodService.getCurrentMonth();
        container.read(timePeriodNotifierProvider.notifier).state =
            currentMonth;

        final isCurrentMonth = container.read(isCurrentMonthProvider);
        expect(isCurrentMonth, true);
      });

      test('should return false when selected period is not current month', () {
        final pastMonth = TimePeriodService.getMonthPeriod(2023, 1);
        container.read(timePeriodNotifierProvider.notifier).state = pastMonth;

        final isCurrentMonth = container.read(isCurrentMonthProvider);
        expect(isCurrentMonth, false);
      });

      test('should have correct provider name', () {
        expect(isCurrentMonthProvider.name, 'isCurrentMonthProvider');
      });

      test('should be auto dispose provider', () {
        expect(isCurrentMonthProvider, isA<AutoDisposeProvider<bool>>());
      });
    });

    group('isFuturePeriodProvider', () {
      test('should return false when selected period is current month', () {
        final currentMonth = TimePeriodService.getCurrentMonth();
        container.read(timePeriodNotifierProvider.notifier).state =
            currentMonth;

        final isFuture = container.read(isFuturePeriodProvider);
        expect(isFuture, false);
      });

      test('should return false when selected period is past month', () {
        final pastMonth = TimePeriodService.getMonthPeriod(2023, 1);
        container.read(timePeriodNotifierProvider.notifier).state = pastMonth;

        final isFuture = container.read(isFuturePeriodProvider);
        expect(isFuture, false);
      });

      test('should return true when selected period is future month', () {
        final now = DateTime.now();
        final futureMonth = TimePeriodService.getMonthPeriod(
          now.year + 1,
          now.month,
        );
        container.read(timePeriodNotifierProvider.notifier).state = futureMonth;

        final isFuture = container.read(isFuturePeriodProvider);
        expect(isFuture, true);
      });

      test('should have correct provider name', () {
        expect(isFuturePeriodProvider.name, 'isFuturePeriodProvider');
      });

      test('should be auto dispose provider', () {
        expect(isFuturePeriodProvider, isA<AutoDisposeProvider<bool>>());
      });
    });

    group('currentPeriodDisplayTextProvider', () {
      test('should return display text for current period', () {
        final testPeriod = TimePeriodService.getMonthPeriod(2024, 12);
        container.read(timePeriodNotifierProvider.notifier).state = testPeriod;

        final displayText = container.read(currentPeriodDisplayTextProvider);
        expect(displayText, testPeriod.displayName);
        expect(displayText, 'December 2024');
      });

      test('should update when period changes', () {
        final period1 = TimePeriodService.getMonthPeriod(2024, 11);
        final period2 = TimePeriodService.getMonthPeriod(2024, 12);

        container.read(timePeriodNotifierProvider.notifier).state = period1;
        final text1 = container.read(currentPeriodDisplayTextProvider);

        container.read(timePeriodNotifierProvider.notifier).state = period2;
        final text2 = container.read(currentPeriodDisplayTextProvider);

        expect(text1, 'November 2024');
        expect(text2, 'December 2024');
        expect(text1, isNot(equals(text2)));
      });

      test('should have correct provider name', () {
        expect(
          currentPeriodDisplayTextProvider.name,
          'currentPeriodDisplayTextProvider',
        );
      });

      test('should be auto dispose provider', () {
        expect(
          currentPeriodDisplayTextProvider,
          isA<AutoDisposeProvider<String>>(),
        );
      });
    });

    group('currentPeriodDateRangeTextProvider', () {
      test('should return date range text for current period', () {
        final testPeriod = TimePeriodService.getMonthPeriod(2024, 12);
        container.read(timePeriodNotifierProvider.notifier).state = testPeriod;

        final dateRangeText = container.read(
          currentPeriodDateRangeTextProvider,
        );
        expect(dateRangeText, testPeriod.dateRangeText);
        expect(dateRangeText, '01 Dec - 31 Dec');
      });

      test('should update when period changes', () {
        final period1 = TimePeriodService.getMonthPeriod(
          2024,
          2,
        ); // February (28 days)
        final period2 = TimePeriodService.getMonthPeriod(
          2024,
          3,
        ); // March (31 days)

        container.read(timePeriodNotifierProvider.notifier).state = period1;
        final text1 = container.read(currentPeriodDateRangeTextProvider);

        container.read(timePeriodNotifierProvider.notifier).state = period2;
        final text2 = container.read(currentPeriodDateRangeTextProvider);

        expect(text1, '01 Feb - 29 Feb'); // 2024 is leap year
        expect(text2, '01 Mar - 31 Mar'); // March has 31 days
        expect(text1, isNot(equals(text2)));
      });

      test('should have correct provider name', () {
        expect(
          currentPeriodDateRangeTextProvider.name,
          'currentPeriodDateRangeTextProvider',
        );
      });

      test('should be auto dispose provider', () {
        expect(
          currentPeriodDateRangeTextProvider,
          isA<AutoDisposeProvider<String>>(),
        );
      });
    });

    group('selectedPeriodDateRangeProvider', () {
      test('should return start and end dates for current period', () {
        final testPeriod = TimePeriodService.getMonthPeriod(2024, 12);
        container.read(timePeriodNotifierProvider.notifier).state = testPeriod;

        final dateRange = container.read(selectedPeriodDateRangeProvider);
        expect(dateRange.startDate, DateTime(2024, 12, 1));
        expect(dateRange.endDate, DateTime(2024, 12, 31));
      });

      test('should update when period changes', () {
        final period1 = TimePeriodService.getMonthPeriod(2024, 11);
        final period2 = TimePeriodService.getMonthPeriod(2024, 12);

        container.read(timePeriodNotifierProvider.notifier).state = period1;
        final range1 = container.read(selectedPeriodDateRangeProvider);

        container.read(timePeriodNotifierProvider.notifier).state = period2;
        final range2 = container.read(selectedPeriodDateRangeProvider);

        expect(range1.startDate, DateTime(2024, 11, 1));
        expect(range1.endDate, DateTime(2024, 11, 30));
        expect(range2.startDate, DateTime(2024, 12, 1));
        expect(range2.endDate, DateTime(2024, 12, 31));
      });

      test('should have correct provider name', () {
        expect(
          selectedPeriodDateRangeProvider.name,
          'selectedPeriodDateRangeProvider',
        );
      });

      test('should be auto dispose provider', () {
        expect(
          selectedPeriodDateRangeProvider,
          isA<AutoDisposeProvider<({DateTime startDate, DateTime endDate})>>(),
        );
      });
    });

    group('isDateInSelectedPeriodProvider Family', () {
      test('should return true for date within selected period', () {
        final testPeriod = TimePeriodService.getMonthPeriod(2024, 12);
        container.read(timePeriodNotifierProvider.notifier).state = testPeriod;

        final dateInPeriod = DateTime(2024, 12, 15);
        final isInPeriod = container.read(
          isDateInSelectedPeriodProvider(dateInPeriod),
        );

        expect(isInPeriod, true);
      });

      test('should return false for date outside selected period', () {
        final testPeriod = TimePeriodService.getMonthPeriod(2024, 12);
        container.read(timePeriodNotifierProvider.notifier).state = testPeriod;

        final dateOutsidePeriod = DateTime(2024, 11, 15);
        final isInPeriod = container.read(
          isDateInSelectedPeriodProvider(dateOutsidePeriod),
        );

        expect(isInPeriod, false);
      });

      test('should return true for first day of period', () {
        final testPeriod = TimePeriodService.getMonthPeriod(2024, 12);
        container.read(timePeriodNotifierProvider.notifier).state = testPeriod;

        final firstDay = DateTime(2024, 12, 1);
        final isInPeriod = container.read(
          isDateInSelectedPeriodProvider(firstDay),
        );

        expect(isInPeriod, true);
      });

      test('should return true for last day of period', () {
        final testPeriod = TimePeriodService.getMonthPeriod(2024, 12);
        container.read(timePeriodNotifierProvider.notifier).state = testPeriod;

        final lastDay = DateTime(2024, 12, 31);
        final isInPeriod = container.read(
          isDateInSelectedPeriodProvider(lastDay),
        );

        expect(isInPeriod, true);
      });

      test('should handle different dates with same provider family', () {
        final testPeriod = TimePeriodService.getMonthPeriod(2024, 12);
        container.read(timePeriodNotifierProvider.notifier).state = testPeriod;

        final date1 = DateTime(2024, 12, 15);
        final date2 = DateTime(2024, 11, 15);

        final result1 = container.read(isDateInSelectedPeriodProvider(date1));
        final result2 = container.read(isDateInSelectedPeriodProvider(date2));

        expect(result1, true);
        expect(result2, false);
      });

      test('should update when period changes', () {
        final date = DateTime(2024, 12, 15);

        // Set period to December 2024
        final period1 = TimePeriodService.getMonthPeriod(2024, 12);
        container.read(timePeriodNotifierProvider.notifier).state = period1;
        final result1 = container.read(isDateInSelectedPeriodProvider(date));

        // Change period to November 2024
        final period2 = TimePeriodService.getMonthPeriod(2024, 11);
        container.read(timePeriodNotifierProvider.notifier).state = period2;
        final result2 = container.read(isDateInSelectedPeriodProvider(date));

        expect(result1, true); // Date is in December
        expect(result2, false); // Date is not in November
      });

      test('should have correct family name', () {
        expect(
          isDateInSelectedPeriodProvider.name,
          'isDateInSelectedPeriodProvider',
        );
      });

      test(
        'should create different provider instances for different dates',
        () {
          final date1 = DateTime(2024, 12, 15);
          final date2 = DateTime(2024, 11, 15);

          final provider1 = isDateInSelectedPeriodProvider(date1);
          final provider2 = isDateInSelectedPeriodProvider(date2);

          expect(provider1, isNot(equals(provider2)));
          expect(provider1.date, date1);
          expect(provider2.date, date2);
        },
      );

      test('should create same provider instance for same date', () {
        final date = DateTime(2024, 12, 15);

        final provider1 = isDateInSelectedPeriodProvider(date);
        final provider2 = isDateInSelectedPeriodProvider(date);

        expect(provider1, equals(provider2));
        expect(provider1.hashCode, equals(provider2.hashCode));
      });
    });

    group('timePeriodNotifierProvider', () {
      test('should initialize with current month', () {
        final currentMonth = TimePeriodService.getCurrentMonth();
        final state = container.read(timePeriodNotifierProvider);

        expect(state.type, PeriodType.monthly);
        expect(state.year, currentMonth.year);
        expect(state.month, currentMonth.month);
      });

      test('should allow state updates', () {
        final newPeriod = TimePeriodService.getMonthPeriod(2024, 12);
        final notifier = container.read(timePeriodNotifierProvider.notifier);

        notifier.state = newPeriod;
        final updatedState = container.read(timePeriodNotifierProvider);

        expect(updatedState.year, 2024);
        expect(updatedState.month, 12);
      });

      test('should have correct provider name', () {
        expect(timePeriodNotifierProvider.name, 'timePeriodNotifierProvider');
      });

      test('should be auto dispose notifier provider', () {
        expect(
          timePeriodNotifierProvider,
          isA<AutoDisposeNotifierProvider<TimePeriodNotifier, TimePeriod>>(),
        );
      });

      test('should provide access to notifier methods', () {
        final notifier = container.read(timePeriodNotifierProvider.notifier);

        expect(notifier, isA<TimePeriodNotifier>());
        expect(notifier.canNavigateToPrevious, isA<bool>());
        expect(notifier.canNavigateToNext, isA<bool>());
      });
    });

    group('Provider Hash Functions', () {
      test('should have consistent hash values', () {
        // Test that hash functions return consistent values
        expect(availablePeriodsProvider.name, isNotNull);
        expect(isCurrentMonthProvider.name, isNotNull);
        expect(isFuturePeriodProvider.name, isNotNull);
        expect(currentPeriodDisplayTextProvider.name, isNotNull);
        expect(currentPeriodDateRangeTextProvider.name, isNotNull);
        expect(selectedPeriodDateRangeProvider.name, isNotNull);
        expect(timePeriodNotifierProvider.name, isNotNull);
      });

      test('should handle provider dependencies correctly', () {
        // Test that providers have correct dependency structure
        expect(availablePeriodsProvider.dependencies, isNull);
        expect(isCurrentMonthProvider.dependencies, isNull);
        expect(isFuturePeriodProvider.dependencies, isNull);
        expect(currentPeriodDisplayTextProvider.dependencies, isNull);
        expect(currentPeriodDateRangeTextProvider.dependencies, isNull);
        expect(selectedPeriodDateRangeProvider.dependencies, isNull);
        expect(timePeriodNotifierProvider.dependencies, isNull);
      });
    });

    group('Provider Element Creation', () {
      test('should create provider elements correctly', () {
        final date = DateTime(2024, 12, 15);
        final provider = isDateInSelectedPeriodProvider(date);
        final element = provider.createElement();

        expect(element, isA<AutoDisposeProviderElement<bool>>());
        expect(element.provider, equals(provider));
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle leap year dates correctly', () {
        final leapYearPeriod = TimePeriodService.getMonthPeriod(
          2024,
          2,
        ); // February 2024 (leap year)
        container.read(timePeriodNotifierProvider.notifier).state =
            leapYearPeriod;

        final leapDay = DateTime(2024, 2, 29);
        final isInPeriod = container.read(
          isDateInSelectedPeriodProvider(leapDay),
        );

        expect(isInPeriod, true);
      });

      test('should handle year boundary correctly', () {
        final decemberPeriod = TimePeriodService.getMonthPeriod(2024, 12);
        container.read(timePeriodNotifierProvider.notifier).state =
            decemberPeriod;

        final newYearDate = DateTime(2025, 1, 1);
        final isInPeriod = container.read(
          isDateInSelectedPeriodProvider(newYearDate),
        );

        expect(isInPeriod, false);
      });

      test('should handle extreme dates correctly', () {
        final testPeriod = TimePeriodService.getMonthPeriod(2024, 12);
        container.read(timePeriodNotifierProvider.notifier).state = testPeriod;

        final veryOldDate = DateTime(1900, 1, 1);
        final veryFutureDate = DateTime(2100, 12, 31);

        final isOldInPeriod = container.read(
          isDateInSelectedPeriodProvider(veryOldDate),
        );
        final isFutureInPeriod = container.read(
          isDateInSelectedPeriodProvider(veryFutureDate),
        );

        expect(isOldInPeriod, false);
        expect(isFutureInPeriod, false);
      });
    });
  });
}
