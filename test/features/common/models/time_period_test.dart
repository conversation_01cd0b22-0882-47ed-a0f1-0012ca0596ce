import 'package:budapp/features/common/models/time_period.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('TimePeriod', () {
    test('should create a valid monthly period', () {
      final period = TimePeriod(
        type: PeriodType.monthly,
        startDate: DateTime(2024, 12, 1),
        endDate: DateTime(2024, 12, 31),
        displayName: 'December 2024',
        dateRangeText: '01 Dec - 31 Dec',
        year: 2024,
        month: 12,
        isPast: false,
        isCurrent: true,
      );

      expect(period.type, PeriodType.monthly);
      expect(period.year, 2024);
      expect(period.month, 12);
      expect(period.isCurrent, true);
      expect(period.isPast, false);
    });

    test('should check if period contains a date', () {
      final period = TimePeriod(
        type: PeriodType.monthly,
        startDate: DateTime(2024, 12, 1),
        endDate: DateTime(2024, 12, 31),
        displayName: 'December 2024',
        dateRangeText: '01 Dec - 31 Dec',
        year: 2024,
        month: 12,
      );

      expect(period.containsDate(DateTime(2024, 12, 15)), true);
      expect(period.containsDate(DateTime(2024, 12, 1)), true);
      expect(period.containsDate(DateTime(2024, 12, 31)), true);
      expect(period.containsDate(DateTime(2024, 11, 30)), false);
      expect(period.containsDate(DateTime(2025, 1, 1)), false);
    });

    test('should calculate duration in days correctly', () {
      final period = TimePeriod(
        type: PeriodType.monthly,
        startDate: DateTime(2024, 12, 1),
        endDate: DateTime(2024, 12, 31),
        displayName: 'December 2024',
        dateRangeText: '01 Dec - 31 Dec',
        year: 2024,
        month: 12,
      );

      expect(period.durationInDays, 31);
    });

    test('should validate period correctly', () {
      final validPeriod = TimePeriod(
        type: PeriodType.monthly,
        startDate: DateTime(2024, 12, 1),
        endDate: DateTime(2024, 12, 31),
        displayName: 'December 2024',
        dateRangeText: '01 Dec - 31 Dec',
        year: 2024,
        month: 12,
      );

      final invalidPeriod = TimePeriod(
        type: PeriodType.monthly,
        startDate: DateTime(2024, 12, 31),
        endDate: DateTime(2024, 12, 1),
        displayName: 'Invalid Period',
        dateRangeText: '31 Dec - 01 Dec',
        year: 2024,
        month: 12,
      );

      expect(validPeriod.isValid, true);
      expect(invalidPeriod.isValid, false);
    });

    test('should generate correct identifier for monthly period', () {
      final period = TimePeriod(
        type: PeriodType.monthly,
        startDate: DateTime(2024, 12, 1),
        endDate: DateTime(2024, 12, 31),
        displayName: 'December 2024',
        dateRangeText: '01 Dec - 31 Dec',
        year: 2024,
        month: 12,
      );

      expect(period.identifier, '2024_12');
    });

    test('should serialize to and from JSON', () {
      final period = TimePeriod(
        type: PeriodType.monthly,
        startDate: DateTime(2024, 12, 1),
        endDate: DateTime(2024, 12, 31),
        displayName: 'December 2024',
        dateRangeText: '01 Dec - 31 Dec',
        year: 2024,
        month: 12,
        isPast: false,
        isCurrent: true,
      );

      final json = period.toJson();
      final deserializedPeriod = TimePeriod.fromJson(json);

      expect(deserializedPeriod.type, period.type);
      expect(deserializedPeriod.year, period.year);
      expect(deserializedPeriod.month, period.month);
      expect(deserializedPeriod.displayName, period.displayName);
      expect(deserializedPeriod.dateRangeText, period.dateRangeText);
      expect(deserializedPeriod.isCurrent, period.isCurrent);
      expect(deserializedPeriod.isPast, period.isPast);
    });
  });

  group('PeriodTypeExtension', () {
    test('should provide correct display names', () {
      expect(PeriodType.monthly.displayName, 'Monthly');
      expect(PeriodType.weekly.displayName, 'Weekly');
      expect(PeriodType.quarterly.displayName, 'Quarterly');
      expect(PeriodType.yearly.displayName, 'Yearly');
      expect(PeriodType.custom.displayName, 'Custom');
    });
  });
}
