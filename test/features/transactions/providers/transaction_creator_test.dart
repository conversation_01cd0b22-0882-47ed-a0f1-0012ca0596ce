import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/repositories/implementations/transaction_repository_impl.dart';
import 'package:budapp/data/repositories/interfaces/transaction_repository.dart';
import 'package:budapp/features/budgets/services/budget_transaction_service.dart';
import 'package:budapp/features/transactions/providers/transaction_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/mock_data_factory.dart';

// Mock classes for testing
class MockBudgetTransactionService extends Mock
    implements BudgetTransactionService {}

class MockTransactionRepository extends Mock
    implements ITransactionRepository {}

void main() {
  setUpAll(() {
    // Use MockDataFactory to create fallback values instead of Fake classes
    registerFallbackValue(MockDataFactory.createTransaction());
  });

  group('TransactionCreator Provider Tests', () {
    late ProviderContainer container;
    late FakeFirebaseFirestore fakeFirestore;
    late MockBudgetTransactionService mockBudgetTransactionService;
    late String testUserId;
    late String testAccountId1;
    late String testAccountId2;

    setUp(() async {
      fakeFirestore = FakeFirebaseFirestore();
      mockBudgetTransactionService = MockBudgetTransactionService();
      testUserId = 'test-user-123';
      testAccountId1 = 'account-1';
      testAccountId2 = 'account-2';

      // Setup mock budget transaction service
      when(
        () => mockBudgetTransactionService.updateBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      when(
        () => mockBudgetTransactionService.revertBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async {});

      when(
        () => mockBudgetTransactionService.updateBudgetsForTransactionUpdate(
          any(),
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      // Create test accounts
      final account1 = Account(
        id: testAccountId1,
        userId: testUserId,
        name: 'Test Account 1',
        type: AccountType.checking,
        classification: AccountClassification.asset,
        initialBalanceCents: 100000,
        currentBalanceCents: 100000, // $1000.00

        isActive: true,
        isPrimary: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final account2 = Account(
        id: testAccountId2,
        userId: testUserId,
        name: 'Test Account 2',
        type: AccountType.savings,
        classification: AccountClassification.asset,
        initialBalanceCents: 50000,
        currentBalanceCents: 50000, // $500.00

        isActive: true,
        isPrimary: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId1)
          .set(account1.toJson());

      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId2)
          .set(account2.toJson());

      // Create container with overridden providers
      container = ProviderContainer(
        overrides: [
          transactionRepositoryProvider.overrideWithValue(
            TransactionRepositoryImpl(
              FirestoreService(fakeFirestore),
              mockBudgetTransactionService,
            ),
          ),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('createIncomeTransaction', () {
      test('should create income transaction successfully', () async {
        final creator = container.read(transactionCreatorProvider.notifier);

        final transactionId = await creator.createIncomeTransaction(
          userId: testUserId,
          toAccountId: testAccountId1,
          amountCents: 10000,

          transactionDate: DateTime.now(),
          description: 'Test income',
          categoryId: 'income-category',
        );

        expect(transactionId, isNotEmpty);

        // Verify state is success
        final state = container.read(transactionCreatorProvider);
        expect(state.hasError, isFalse);
        expect(state.isLoading, isFalse);
      });

      test('should handle error for invalid account', () async {
        final creator = container.read(transactionCreatorProvider.notifier);

        expect(
          () => creator.createIncomeTransaction(
            userId: testUserId,
            toAccountId: 'invalid-account',
            amountCents: 10000,

            transactionDate: DateTime.now(),
          ),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('createExpenseTransaction', () {
      test('should create expense transaction successfully', () async {
        final creator = container.read(transactionCreatorProvider.notifier);

        final transactionId = await creator.createExpenseTransaction(
          userId: testUserId,
          fromAccountId: testAccountId1,
          amountCents: 5000,

          transactionDate: DateTime.now(),
          description: 'Test expense',
          categoryId: 'expense-category',
        );

        expect(transactionId, isNotEmpty);

        // Verify state is success
        final state = container.read(transactionCreatorProvider);
        expect(state.hasError, isFalse);
        expect(state.isLoading, isFalse);
      });

      test('should handle error for invalid account', () async {
        final creator = container.read(transactionCreatorProvider.notifier);

        expect(
          () => creator.createExpenseTransaction(
            userId: testUserId,
            fromAccountId: 'invalid-account',
            amountCents: 5000,

            transactionDate: DateTime.now(),
          ),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('createTransferTransaction', () {
      test('should create transfer transaction successfully', () async {
        final creator = container.read(transactionCreatorProvider.notifier);

        final transactionId = await creator.createTransferTransaction(
          userId: testUserId,
          fromAccountId: testAccountId1,
          toAccountId: testAccountId2,
          amountCents: 7500,

          transactionDate: DateTime.now(),
          description: 'Test transfer',
        );

        expect(transactionId, isNotEmpty);

        // Verify state is success
        final state = container.read(transactionCreatorProvider);
        expect(state.hasError, isFalse);
        expect(state.isLoading, isFalse);
      });

      test('should handle error for same accounts', () async {
        final creator = container.read(transactionCreatorProvider.notifier);

        expect(
          () => creator.createTransferTransaction(
            userId: testUserId,
            fromAccountId: testAccountId1,
            toAccountId: testAccountId1, // Same account
            amountCents: 7500,

            transactionDate: DateTime.now(),
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should handle error for invalid accounts', () async {
        final creator = container.read(transactionCreatorProvider.notifier);

        expect(
          () => creator.createTransferTransaction(
            userId: testUserId,
            fromAccountId: 'invalid-account',
            toAccountId: testAccountId2,
            amountCents: 7500,

            transactionDate: DateTime.now(),
          ),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('state management', () {
      test('should set loading state during transaction creation', () async {
        final creator = container.read(transactionCreatorProvider.notifier);

        // Start async operation
        final future = creator.createIncomeTransaction(
          userId: testUserId,
          toAccountId: testAccountId1,
          amountCents: 10000,

          transactionDate: DateTime.now(),
        );

        // Check loading state (might be too fast to catch in tests)
        await future;

        // Verify final state
        final state = container.read(transactionCreatorProvider);
        expect(state.hasError, isFalse);
        expect(state.isLoading, isFalse);
      });

      test('should set error state on failure', () async {
        final mockRepository = MockTransactionRepository();

        // Override repository to throw an error
        when(
          () => mockRepository.createIncomeTransaction(
            userId: any(named: 'userId'),
            toAccountId: any(named: 'toAccountId'),
            amountCents: any(named: 'amountCents'),
            transactionDate: any(named: 'transactionDate'),
            categoryId: any(named: 'categoryId'),
            description: any(named: 'description'),
            notes: any(named: 'notes'),
            tags: any(named: 'tags'),
            metadata: any(named: 'metadata'),
          ),
        ).thenThrow(Exception('Test error'));

        // Create new container with failing repository
        final errorContainer = ProviderContainer(
          overrides: [
            transactionRepositoryProvider.overrideWithValue(mockRepository),
          ],
        );

        final errorCreator = errorContainer.read(
          transactionCreatorProvider.notifier,
        );

        try {
          await errorCreator.createIncomeTransaction(
            userId: testUserId,
            toAccountId: 'invalid-account',
            amountCents: 10000,
            transactionDate: DateTime.now(),
          );
          fail('Expected exception was not thrown');
        } on Exception {
          // Expected error
        }

        // Verify error state
        final state = errorContainer.read(transactionCreatorProvider);
        expect(state.hasError, isTrue);
        expect(state.isLoading, isFalse);

        errorContainer.dispose();
      });
    });
  });
}
