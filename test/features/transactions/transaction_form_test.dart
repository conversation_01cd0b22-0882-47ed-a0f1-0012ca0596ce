import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/transactions/presentation/providers/transaction_form_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../helpers/test_wrapper.dart';

void main() {
  group('TransactionForm', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should manage expense transaction form state correctly', () {
      // Set up form state for expense transaction
      container.read(transactionFormProvider.notifier)
        ..setTransactionType(TransactionType.expense)
        ..setAmount(5000) // $50.00 in cents
        ..setAccountId('test-account-id')
        ..setDate(DateTime(2024, 1, 15))
        ..setCategoryId('test-category-id')
        ..setDescription('Test expense')
        ..setNotes('Test notes');

      // Verify form state
      final state = container.read(transactionFormProvider);
      expect(state.transactionType, equals(TransactionType.expense));
      expect(state.amount, equals(5000));
      expect(state.accountId, equals('test-account-id'));
      expect(state.categoryId, equals('test-category-id'));
      expect(state.description, equals('Test expense'));
      expect(state.notes, equals('Test notes'));
      expect(state.date, equals(DateTime(2024, 1, 15)));
    });

    test('should manage income transaction form state correctly', () {
      // Set up form state for income transaction
      container.read(transactionFormProvider.notifier)
        ..setTransactionType(TransactionType.income)
        ..setAmount(10000) // $100.00 in cents
        ..setAccountId('test-account-id')
        ..setDate(DateTime(2024, 1, 15))
        ..setCategoryId('test-category-id')
        ..setDescription('Test income');

      // Verify form state
      final state = container.read(transactionFormProvider);
      expect(state.transactionType, equals(TransactionType.income));
      expect(state.amount, equals(10000));
      expect(state.accountId, equals('test-account-id'));
      expect(state.categoryId, equals('test-category-id'));
      expect(state.description, equals('Test income'));
      expect(state.date, equals(DateTime(2024, 1, 15)));
    });

    test('should manage transfer transaction form state correctly', () {
      // Set up form state for transfer transaction
      container.read(transactionFormProvider.notifier)
        ..setTransactionType(TransactionType.transfer)
        ..setAmount(7500) // $75.00 in cents
        ..setAccountId('test-from-account-id')
        ..setDate(DateTime(2024, 1, 15))
        ..setDescription('Test transfer');

      // Verify form state
      final state = container.read(transactionFormProvider);
      expect(state.transactionType, equals(TransactionType.transfer));
      expect(state.amount, equals(7500));
      expect(state.accountId, equals('test-from-account-id'));
      expect(state.description, equals('Test transfer'));
      expect(state.date, equals(DateTime(2024, 1, 15)));
      // Transfer transactions should not have categories
      expect(state.categoryId, isNull);
    });

    testWidgets('should validate form correctly', (WidgetTester tester) async {
      late BuildContext context;

      final widget = TestWrapper.createTestWidget(
        Builder(
          builder: (BuildContext ctx) {
            context = ctx;
            return const SizedBox.shrink();
          },
        ),
      );

      await tester.pumpWidget(widget);

      final notifier = container.read(transactionFormProvider.notifier);

      // Initially invalid
      expect(notifier.validateForm(context), isFalse);

      // Set required fields
      notifier
        ..setAmount(5000)
        ..setAccountId('test-account-id')
        ..setCategoryId('test-category-id') // Required for expense transactions
        ..setDate(DateTime.now());

      // Now valid
      expect(notifier.validateForm(context), isTrue);

      // Ensure all async operations complete
      await tester.pumpAndSettle();
    });
  });
}
