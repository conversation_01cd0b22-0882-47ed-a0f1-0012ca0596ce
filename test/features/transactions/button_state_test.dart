import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/transactions/presentation/providers/transaction_form_providers.dart';
import 'package:budapp/features/transactions/presentation/widgets/transaction_form.dart'
    as widgets;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../helpers/mock_providers.dart';
import '../../helpers/test_wrapper.dart';

void main() {
  group('TransactionForm Button State Tests', () {
    testWidgets('Create Transaction button should be disabled initially', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const Scaffold(
            body: SingleChildScrollView(child: widgets.TransactionForm()),
          ),
          overrides: [
            // Add authentication overrides to prevent "User not authenticated" errors
            ...MockProviders.authenticatedUserOverrides(),
          ],
        ),
      );
      await tester.pumpAndSettle();

      // Find the Create Transaction button
      final button = find.byType(ElevatedButton);
      expect(button, findsOneWidget);

      // Button should be disabled initially
      final elevatedButton = tester.widget<ElevatedButton>(button);
      expect(elevatedButton.onPressed, isNull);
    });

    testWidgets(
      'Create Transaction button should be enabled when all required fields are filled',
      (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const Scaffold(
              body: SingleChildScrollView(child: widgets.TransactionForm()),
            ),
            overrides: [
              // Add authentication overrides to prevent "User not authenticated" errors
              ...MockProviders.authenticatedUserOverrides(),
            ],
          ),
        );
        await tester.pumpAndSettle();

        // Get the container from the widget tree
        final container = ProviderScope.containerOf(
          tester.element(find.byType(widgets.TransactionForm)),
        );
        // Fill all required fields for expense transaction
        container.read(transactionFormProvider.notifier)
          ..setTransactionType(TransactionType.expense)
          ..setAccountId('account-1')
          ..setCategoryId('category-1') // Required for expense transactions
          ..setDate(DateTime.now());

        // Fill amount field by typing in the text field
        final amountField = find.byType(TextFormField).first;
        await tester.enterText(amountField, '100.50');
        await tester.pumpAndSettle();

        // Button should now be enabled
        final button = find.byType(ElevatedButton);
        expect(button, findsOneWidget);

        final elevatedButton = tester.widget<ElevatedButton>(button);
        expect(
          elevatedButton.onPressed,
          isNotNull,
          reason:
              'Button should be enabled when all required fields are filled',
        );
      },
    );
  });
}
