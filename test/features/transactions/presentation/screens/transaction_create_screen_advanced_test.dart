import 'dart:async';

import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/features/transactions/presentation/screens/transaction_create_screen.dart';
import 'package:budapp/features/transactions/presentation/widgets/transaction_form.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/mock_data_factory.dart';
import '../../../../helpers/test_wrapper.dart';

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(
      MockDataFactory.createTransaction(
        id: 'fallback-transaction-id',
        userId: 'fallback-user-id',
        fromAccountId: 'fallback-account-id',
        amountCents: 1000,
        description: 'Fallback transaction',
        type: TransactionType.expense,
        categoryId: 'fallback-category-id',
      ),
    );
  });

  group('TransactionCreateScreen Error Handling and Edge Cases', () {
    late List<Account> mockAccounts;
    late List<Category> mockCategories;

    setUp(() {
      mockAccounts = [
        MockDataFactory.createAccount(
          id: 'account1',
          userId: 'user1',
          name: 'Checking Account',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 100000,
        ),
        MockDataFactory.createAccount(
          id: 'account2',
          userId: 'user1',
          name: 'Savings Account',
          type: AccountType.savings,
          classification: AccountClassification.asset,
          initialBalanceCents: 50000,
        ),
      ];

      mockCategories = [
        MockDataFactory.createCategory(
          id: 'category1',
          userId: 'user1',
          name: 'Food & Dining',
          type: CategoryType.expense,
          icon: 'restaurant',
          color: 'red',
          schemaVersion: 1,
        ),
        MockDataFactory.createCategory(
          id: 'category2',
          userId: 'user1',
          name: 'Salary',
          type: CategoryType.income,
          icon: 'work',
          color: 'green',
          schemaVersion: 1,
        ),
      ];
    });

    group('Form Validation Edge Cases', () {
      testWidgets('should handle very large amounts', (tester) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        // Test very large amount
        final amountField = find.byType(TextFormField).first;
        await tester.enterText(amountField, '*********.99');
        await tester.pumpAndSettle();

        expect(find.text('*********.99'), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle special characters in descriptions', (
        tester,
      ) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        // Test special characters in description
        final textFields = find.byType(TextFormField);
        if (textFields.evaluate().length > 1) {
          const specialChars = '!@#\$%^&*()[]{}|\\:";\'<>?,./`~🤑💰💸';
          await tester.enterText(textFields.at(1), specialChars);
          await tester.pumpAndSettle();

          expect(find.text(specialChars), findsOneWidget);
          expect(tester.takeException(), isNull);
        }
      });

      testWidgets('should handle very long descriptions', (tester) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        // Test very long description
        final textFields = find.byType(TextFormField);
        if (textFields.evaluate().length > 1) {
          final longDescription = 'A' * 1000; // Very long description
          await tester.enterText(textFields.at(1), longDescription);
          await tester.pumpAndSettle();

          expect(tester.takeException(), isNull);
        }
      });
    });

    group('Provider Error Handling', () {
      testWidgets('should handle account provider errors', (tester) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith(
            (ref) => Stream.error(Exception('Account loading failed')),
          ),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        // Should render without crashing
        expect(find.byType(TransactionCreateScreen), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle category provider errors', (tester) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.error(Exception('Category loading failed')),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        // Should render without crashing
        expect(find.byType(TransactionCreateScreen), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle both provider errors simultaneously', (
        tester,
      ) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith(
            (ref) => Stream.error(Exception('Account loading failed')),
          ),
          categoryListProvider.overrideWith(
            (ref) => Stream.error(Exception('Category loading failed')),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        // Should render without crashing
        expect(find.byType(TransactionCreateScreen), findsOneWidget);
        expect(tester.takeException(), isNull);
      });
    });

    group('Transaction Type Switching Edge Cases', () {
      testWidgets('should handle rapid transaction type switching', (
        tester,
      ) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        // Rapidly switch between transaction types
        await tester.tap(find.text('Income'));
        await tester.pump();

        await tester.tap(find.text('Transfer'));
        await tester.pump();

        await tester.tap(find.text('Expense'));
        await tester.pump();

        await tester.pumpAndSettle();

        expect(find.byType(TransactionCreateScreen), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should preserve form data during type switching', (
        tester,
      ) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        // Enter amount
        final amountField = find.byType(TextFormField).first;
        await tester.enterText(amountField, '100.00');
        await tester.pumpAndSettle();

        // Switch transaction type
        await tester.tap(find.text('Income'));
        await tester.pumpAndSettle();

        // Verify amount is preserved
        expect(find.text('100.00'), findsOneWidget);
        expect(tester.takeException(), isNull);
      });
    });

    group('Widget Disposal Edge Cases', () {
      testWidgets('should handle widget disposal during async operations', (
        tester,
      ) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        // Dispose the widget by navigating away (simulated)
        await tester.pumpWidget(Container());
        await tester.pumpAndSettle();

        // Should not throw errors
        expect(tester.takeException(), isNull);
      });
    });

    group('Accessibility and Usability Edge Cases', () {
      testWidgets('should handle screen reader interactions', (tester) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        // Verify semantic information is available
        expect(find.byType(TransactionCreateScreen), findsOneWidget);
        expect(find.byType(TransactionForm), findsOneWidget);

        // Check for accessibility widgets
        expect(find.byType(Semantics), findsAtLeast(1));
      });
    });

    group('Performance Edge Cases', () {
      testWidgets('should handle large account lists efficiently', (
        tester,
      ) async {
        // Arrange - Create large list of accounts
        final largeAccountList = List.generate(
          100,
          (index) => MockDataFactory.createAccount(
            id: 'account_$index',
            userId: 'user1',
            name: 'Account $index',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 100000 + index * 1000,
          ),
        );

        final overrides = [
          accountListProvider.overrideWith(
            (ref) => Stream.value(largeAccountList),
          ),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        expect(find.byType(TransactionCreateScreen), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle large category lists efficiently', (
        tester,
      ) async {
        // Arrange - Create large list of categories
        final largeCategoryList = List.generate(
          100,
          (index) => MockDataFactory.createCategory(
            id: 'category_$index',
            userId: 'user1',
            name: 'Category $index',
            type: CategoryType.expense,
            icon: 'category',
            color: 'blue',
            schemaVersion: 1,
          ),
        );

        final overrides = [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(largeCategoryList),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        expect(find.byType(TransactionCreateScreen), findsOneWidget);
        expect(tester.takeException(), isNull);
      });
    });

    group('Memory Management Edge Cases', () {
      testWidgets('should properly dispose resources', (tester) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
        ];

        // Create and dispose multiple times
        for (var i = 0; i < 5; i++) {
          await tester.pumpWidget(
            TestWrapper.createTestWidget(
              const TransactionCreateScreen(),
              overrides: overrides,
            ),
          );
          await tester.pumpAndSettle();

          await tester.pumpWidget(Container());
          await tester.pumpAndSettle();
        }

        expect(tester.takeException(), isNull);
      });
    });

    group('Empty Data Handling', () {
      testWidgets('should handle empty accounts list gracefully', (
        tester,
      ) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith((ref) => Stream.value(<Account>[])),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
        ];

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        // Assert - Should render without errors
        expect(find.byType(TransactionCreateScreen), findsOneWidget);
        expect(find.byType(TransactionForm), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle empty categories list gracefully', (
        tester,
      ) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(<Category>[]),
          ),
        ];

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        // Assert - Should render without errors
        expect(find.byType(TransactionCreateScreen), findsOneWidget);
        expect(find.byType(TransactionForm), findsOneWidget);
        expect(tester.takeException(), isNull);
      });
    });

    group('Theme Handling', () {
      testWidgets('should render correctly with different themes', (
        tester,
      ) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
        ];

        // Test with default theme
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        expect(find.byType(TransactionCreateScreen), findsOneWidget);
        expect(tester.takeException(), isNull);
      });
    });

    group('Boundary Value Testing', () {
      testWidgets('should handle zero amount input', (tester) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        // Test zero amount
        final amountField = find.byType(TextFormField).first;
        await tester.enterText(amountField, '0.00');
        await tester.pumpAndSettle();

        expect(find.text('0.00'), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle negative amount input', (tester) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        // Test negative amount
        final amountField = find.byType(TextFormField).first;
        await tester.enterText(amountField, '-100.00');
        await tester.pumpAndSettle();

        // The form might format or validate the input differently
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle decimal precision edge cases', (tester) async {
        // Arrange
        final overrides = [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionCreateScreen(),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        // Test high precision decimal
        final amountField = find.byType(TextFormField).first;
        await tester.enterText(amountField, '123.456789');
        await tester.pumpAndSettle();

        // The form might format or validate the input differently
        expect(tester.takeException(), isNull);
      });
    });
  });
}
