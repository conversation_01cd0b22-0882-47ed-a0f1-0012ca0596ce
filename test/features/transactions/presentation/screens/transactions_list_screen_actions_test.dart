import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/common/models/time_period.dart';
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/features/transactions/presentation/screens/transactions_list_screen.dart';
import 'package:budapp/features/transactions/presentation/widgets/transaction_card.dart';
import 'package:budapp/features/transactions/providers/transaction_providers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';

/// Test notifier that returns January 2024 period to match test transaction dates
class TestTimePeriodNotifier extends TimePeriodNotifier {
  @override
  TimePeriod build() {
    return TimePeriod(
      type: PeriodType.monthly,
      year: 2024,
      month: 1,
      startDate: DateTime(2024, 1, 1),
      endDate: DateTime(2024, 1, 31, 23, 59, 59),
      displayName: 'January 2024',
      dateRangeText: '01 Jan - 31 Jan',
      isPast: true,
    );
  }
}

void main() {
  group('TransactionsListScreen Action Integration', () {
    late List<Transaction> testTransactions;

    setUp(() {
      testTransactions = [
        Transaction(
          id: 'transaction-1',
          userId: 'test-user-1',
          type: TransactionType.expense,
          status: TransactionStatus.completed,
          amountCents: 5000,

          transactionDate: DateTime(2024, 1, 15),
          description: 'Test Expense 1',
          fromAccountId: 'account-1',
          categoryId: 'category-1',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Transaction(
          id: 'transaction-2',
          userId: 'test-user-1',
          type: TransactionType.income,
          status: TransactionStatus.completed,
          amountCents: 10000,

          transactionDate: DateTime(2024, 1, 16),
          description: 'Test Income 1',
          toAccountId: 'account-1',
          categoryId: 'category-2',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];
    });

    Widget createTestWidget() {
      return ProviderScope(
        overrides: [
          transactionListProvider.overrideWith(
            (ref) => Stream.value(testTransactions),
          ),
          // Override time period to match test transaction dates (January 2024)
          timePeriodNotifierProvider.overrideWith(TestTimePeriodNotifier.new),
        ],
        child: MaterialApp.router(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('en')],
          routerConfig: GoRouter(
            initialLocation: '/transactions',
            routes: [
              GoRoute(
                path: '/transactions',
                builder: (context, state) => const TransactionsListScreen(),
              ),
              GoRoute(
                path: '/transactions/:id/edit',
                builder: (context, state) => Scaffold(
                  appBar: AppBar(title: const Text('Edit Transaction')),
                  body: Center(
                    child: Text(
                      'Edit Transaction ${state.pathParameters['id']}',
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    testWidgets('shows action buttons on transaction cards', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should find transaction cards with action menus
      expect(find.byType(TransactionCard), findsAtLeast(1));
      expect(find.byType(PopupMenuButton<String>), findsAtLeast(1));
    });

    testWidgets('navigates to edit screen when edit action is tapped', (
      tester,
    ) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find the first popup menu button and tap it
      final popupMenuButton = find.byType(PopupMenuButton<String>).first;
      await tester.tap(popupMenuButton);
      await tester.pumpAndSettle();

      // Tap the edit menu item
      await tester.tap(find.text('Edit'));
      await tester.pumpAndSettle();

      // Should navigate to edit screen (first transaction in list is transaction-2)
      expect(find.text('Edit Transaction transaction-2'), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
    });

    testWidgets('shows delete confirmation when delete action is tapped', (
      tester,
    ) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find the first popup menu button and tap it
      final popupMenuButton = find.byType(PopupMenuButton<String>).first;
      await tester.tap(popupMenuButton);
      await tester.pumpAndSettle();

      // Tap the delete menu item
      await tester.tap(find.text('Delete'));
      await tester.pumpAndSettle();

      // Should show delete confirmation dialog
      expect(find.byType(AlertDialog), findsOneWidget);
      expect(find.text('Delete Transaction'), findsOneWidget);
    });
  });
}
