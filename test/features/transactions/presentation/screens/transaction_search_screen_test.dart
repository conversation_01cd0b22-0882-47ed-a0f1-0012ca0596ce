import 'package:budapp/features/transactions/presentation/screens/transaction_search_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/test_wrapper.dart';

void main() {
  group('TransactionSearchScreen Tests', () {
    testWidgets('should display search screen with app bar', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionSearchScreen()),
      );

      await tester.pumpAndSettle();

      // Verify AppBar is present
      expect(find.byType(AppBar), findsOneWidget);

      // Verify search field is present
      expect(find.byType(TextField), findsOneWidget);
      expect(
        find.widgetWithText(TextField, 'Search transactions...'),
        findsOneWidget,
      );

      // Verify Search button in app bar actions
      expect(find.text('Search'), findsOneWidget);
    });

    testWidgets('should display recent searches section when empty search', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionSearchScreen()),
      );

      await tester.pumpAndSettle();

      // Verify recent searches section
      expect(find.text('Recent Searches'), findsOneWidget);
      expect(find.text('Clear'), findsOneWidget);

      // Verify some recent search chips (may appear in both sections)
      expect(find.text('Grocery'), findsAtLeastNWidgets(1));
      expect(find.text('Gas station'), findsAtLeastNWidgets(1));
      expect(find.text('Coffee'), findsAtLeastNWidgets(1));
    });

    testWidgets('should display suggestions section when empty search', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionSearchScreen()),
      );

      await tester.pumpAndSettle();

      // Verify suggestions section
      expect(find.text('Suggestions'), findsOneWidget);

      // Verify some suggestions
      expect(find.text('Grocery store'), findsOneWidget);
      expect(find.text('Coffee shop'), findsOneWidget);
      expect(find.text('Amazon purchase'), findsOneWidget);
    });

    testWidgets('should show clear button when text is entered', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionSearchScreen()),
      );

      await tester.pumpAndSettle();

      final searchField = find.byType(TextField);
      await tester.enterText(searchField, 'grocery');
      await tester.pumpAndSettle();

      // Clear button should appear
      expect(find.byIcon(Icons.clear), findsOneWidget);
    });

    testWidgets('should clear search text when clear button tapped', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionSearchScreen()),
      );

      await tester.pumpAndSettle();

      // Enter text
      final searchField = find.byType(TextField);
      await tester.enterText(searchField, 'grocery');
      await tester.pumpAndSettle();

      // Tap clear button
      await tester.tap(find.byIcon(Icons.clear));
      await tester.pumpAndSettle();

      // Text should be cleared
      final textFieldWidget = tester.widget<TextField>(searchField);
      expect(textFieldWidget.controller?.text, isEmpty);
    });

    testWidgets('should show search results section when typing', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionSearchScreen()),
      );

      await tester.pumpAndSettle();

      // Enter search text
      final searchField = find.byType(TextField);
      await tester.enterText(searchField, 'grocery');
      await tester.pumpAndSettle();

      // Should show Search Results section
      expect(find.text('Search Results'), findsOneWidget);

      // Recent searches and suggestions sections should be hidden
      expect(find.text('Recent Searches'), findsNothing);
      expect(find.text('Suggestions'), findsNothing);
    });

    testWidgets('should highlight search terms in filtered suggestions', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionSearchScreen()),
      );

      await tester.pumpAndSettle();

      // Enter search text
      final searchField = find.byType(TextField);
      await tester.enterText(searchField, 'grocery');
      await tester.pumpAndSettle();

      // Should show RichText with highlighting
      expect(find.byType(RichText), findsAtLeastNWidgets(1));
    });

    testWidgets('should show no results message when no matches found', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionSearchScreen()),
      );

      await tester.pumpAndSettle();

      // Enter text that won't match any suggestions
      final searchField = find.byType(TextField);
      await tester.enterText(searchField, 'xxxxxxxxx');
      await tester.pumpAndSettle();

      // Should show no results message
      expect(find.text('No suggestions found'), findsOneWidget);
      expect(find.text('Try a different search term'), findsOneWidget);
      expect(find.byIcon(Icons.search_off), findsOneWidget);
    });

    testWidgets('should update search field text when chip tapped', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionSearchScreen()),
      );

      await tester.pumpAndSettle();

      // Verify ActionChip is present
      expect(find.byType(ActionChip), findsAtLeastNWidgets(1));
      expect(find.byIcon(Icons.history), findsAtLeastNWidgets(1));
    });

    testWidgets('should show suggestion list with proper structure', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionSearchScreen()),
      );

      await tester.pumpAndSettle();

      // Verify suggestion ListTiles are present
      expect(find.byType(ListTile), findsAtLeastNWidgets(1));
      expect(find.text('Grocery store'), findsOneWidget);
      expect(find.text('Amazon purchase'), findsOneWidget);
    });

    testWidgets('should clear recent searches when Clear button tapped', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionSearchScreen()),
      );

      await tester.pumpAndSettle();

      // Verify recent searches exist initially
      expect(find.text('Recent Searches'), findsOneWidget);

      // Tap Clear button
      final clearButton = find.ancestor(
        of: find.text('Clear'),
        matching: find.byType(TextButton),
      );
      await tester.tap(clearButton);
      await tester.pumpAndSettle();

      // Recent searches should be cleared - verify by checking empty state
      expect(find.byType(ActionChip), findsNothing);
    });

    testWidgets('should handle empty search submission gracefully', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionSearchScreen()),
      );

      await tester.pumpAndSettle();

      // Submit empty search
      await tester.testTextInput.receiveAction(TextInputAction.search);
      await tester.pumpAndSettle();

      // Should not crash and remain on screen
      expect(find.byType(TransactionSearchScreen), findsOneWidget);
    });

    testWidgets('should handle text input changes correctly', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionSearchScreen()),
      );

      await tester.pumpAndSettle();

      final searchField = find.byType(TextField);

      // Enter partial text
      await tester.enterText(searchField, 'gr');
      await tester.pumpAndSettle();

      // Should show search results section
      expect(find.text('Search Results'), findsOneWidget);

      // Continue typing
      await tester.enterText(searchField, 'grocery');
      await tester.pumpAndSettle();

      // Should still show search results
      expect(find.text('Search Results'), findsOneWidget);
    });

    testWidgets('should display search icons in suggestions', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionSearchScreen()),
      );

      await tester.pumpAndSettle();

      // Should show search icons in suggestion list
      expect(find.byIcon(Icons.search), findsAtLeastNWidgets(1));

      // Should show history icons in recent searches
      expect(find.byIcon(Icons.history), findsAtLeastNWidgets(1));
    });

    testWidgets('should maintain focus on search field', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionSearchScreen()),
      );

      // Allow for post-frame callback to execute
      await tester.pumpAndSettle();

      // Search field should have focus after initial setup
      final searchField = find.byType(TextField);
      final textFieldWidget = tester.widget<TextField>(searchField);
      expect(textFieldWidget.focusNode?.hasFocus, isTrue);
    });

    testWidgets('should handle widget disposal without error', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionSearchScreen()),
      );

      await tester.pumpAndSettle();

      // Navigate away to trigger dispose
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const Scaffold(body: Text('Different Screen')),
        ),
      );

      await tester.pumpAndSettle();

      // Should not throw any disposal errors
      expect(find.text('Different Screen'), findsOneWidget);
    });
  });
}
