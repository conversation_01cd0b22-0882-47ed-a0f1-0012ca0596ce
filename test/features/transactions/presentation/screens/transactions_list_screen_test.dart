import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/common/models/time_period.dart';
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/features/common/widgets/time_period_selector.dart';
import 'package:budapp/features/transactions/presentation/screens/transactions_list_screen.dart';
import 'package:budapp/features/transactions/presentation/widgets/empty_transactions_state.dart';
import 'package:budapp/features/transactions/presentation/widgets/transaction_card.dart';
import 'package:budapp/features/transactions/providers/transaction_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/mock_data_factory.dart';
import '../../../../helpers/mock_providers.dart';
import '../../../../helpers/test_wrapper.dart';

/// Test notifier that returns January 2024 period to match test data
class TestTimePeriodNotifier extends TimePeriodNotifier {
  @override
  TimePeriod build() {
    return TimePeriod(
      type: PeriodType.monthly,
      year: 2024,
      month: 1,
      startDate: DateTime(2024, 1, 1),
      endDate: DateTime(2024, 1, 31, 23, 59, 59),
      displayName: 'January 2024',
      dateRangeText: '01 Jan - 31 Jan',
      isPast: true,
    );
  }
}

void main() {
  group('TransactionsListScreen', () {
    // Ignore overflow errors in tests
    setUp(() {
      FlutterError.onError = (FlutterErrorDetails details) {
        if (details.toString().contains('RenderFlex overflowed')) {
          // Ignore overflow errors in tests
          return;
        }
        FlutterError.presentError(details);
      };
    });
    testWidgets('displays AppBar with TimePeriodSelector for default view', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TransactionsListScreen(),
          overrides: MockProviders.transactionsOverrides,
        ),
      );

      await tester.pumpAndSettle();

      // Should display AppBar
      expect(find.byType(AppBar), findsOneWidget);

      // Should show TimePeriodSelector for default view
      expect(find.byType(TimePeriodSelector), findsOneWidget);

      // Should display transactions title
      expect(find.text('Transactions'), findsOneWidget);
    });

    testWidgets('displays AppBar with category filter for category view', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TransactionsListScreen(categoryId: 'test-category-id'),
          overrides: MockProviders.transactionsOverrides,
        ),
      );

      await tester.pumpAndSettle();

      // Should display AppBar
      expect(find.byType(AppBar), findsOneWidget);

      // Should NOT show TimePeriodSelector for filtered view
      expect(find.byType(TimePeriodSelector), findsNothing);

      // Should display category-filtered title
      expect(find.textContaining('Transactions - '), findsOneWidget);

      // Should have clear filter action
      expect(find.byIcon(Icons.clear), findsOneWidget);
    });

    testWidgets('displays AppBar with account filter for account view', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TransactionsListScreen(accountId: 'test-account-id'),
          overrides: MockProviders.transactionsOverrides,
        ),
      );

      await tester.pumpAndSettle();

      // Should display AppBar
      expect(find.byType(AppBar), findsOneWidget);

      // Should NOT show TimePeriodSelector for filtered view
      expect(find.byType(TimePeriodSelector), findsNothing);

      // Should display account-filtered title
      expect(find.textContaining('Transactions - '), findsOneWidget);

      // Should have clear filter action
      expect(find.byIcon(Icons.clear), findsOneWidget);
    });

    testWidgets('header remains visible during scroll', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TransactionsListScreen(),
          overrides: MockProviders.transactionsOverrides,
        ),
      );

      await tester.pumpAndSettle();

      // Find the header
      final headerFinder = find.byType(AppBar);
      expect(headerFinder, findsOneWidget);

      // Scroll down
      await tester.drag(
        find.byType(SingleChildScrollView),
        const Offset(0, -500),
      );
      await tester.pumpAndSettle();

      // Header should still be visible (always visible with regular AppBar)
      expect(headerFinder, findsOneWidget);
    });

    testWidgets('displays filter and search actions for default view', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TransactionsListScreen(),
          overrides: MockProviders.transactionsOverrides,
        ),
      );

      await tester.pumpAndSettle();

      // Should have filter action
      expect(find.byIcon(Icons.filter_list), findsOneWidget);

      // Should have search action
      expect(find.byIcon(Icons.search), findsOneWidget);
    });

    testWidgets('header uses correct Material 3 styling', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TransactionsListScreen(),
          overrides: MockProviders.transactionsOverrides,
        ),
      );

      await tester.pumpAndSettle();

      // Find the AppBar
      final appBar = tester.widget<AppBar>(find.byType(AppBar));

      // Verify it's using Material 3 styling
      // Note: Regular AppBar doesn't have pinned property, it's always visible
      expect(appBar, isNotNull);
    });

    testWidgets('displays transactions list when data is available', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TransactionsListScreen(),
          overrides: MockProviders.transactionsOverrides,
        ),
      );

      await tester.pumpAndSettle();

      // Should display the transactions list content
      // Note: Specific transaction content depends on mock data
      expect(find.byType(SingleChildScrollView), findsOneWidget);
    });

    group('Loading and Error States', () {
      testWidgets('shows loading indicator while data is loading', (
        tester,
      ) async {
        // Create overrides that simulate loading state
        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => const Stream<List<Transaction>>.empty(),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        // Pump once to start loading
        await tester.pump();

        // Verify loading indicator is shown
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });

      testWidgets('shows error state when data loading fails', (tester) async {
        final testError = Exception('Failed to load transactions');

        // Create overrides that return error futures
        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => Stream<List<Transaction>>.error(testError),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        await tester.pumpAndSettle();

        // Verify error state is shown
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
        expect(find.text('Error loading transactions'), findsOneWidget);
        expect(find.text('Retry'), findsOneWidget);
      });

      testWidgets('retry button is tappable after error', (tester) async {
        final testError = Exception('Failed to load transactions');

        // Create overrides that return error futures
        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => Stream<List<Transaction>>.error(testError),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        await tester.pumpAndSettle();

        // Verify error state is shown first
        expect(find.text('Retry'), findsOneWidget);

        // Tap retry button - should not throw
        await tester.tap(find.text('Retry'));
        await tester.pump();

        // Button should still be present (since provider override persists)
        expect(find.text('Retry'), findsOneWidget);
      });
    });

    group('Empty States', () {
      testWidgets('shows empty state when no transactions exist', (
        tester,
      ) async {
        // Create overrides with empty transaction list
        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => Stream.value(<Transaction>[]),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        await tester.pumpAndSettle();

        // Verify empty state is shown
        expect(find.byType(EmptyTransactionsState), findsOneWidget);
      });

      testWidgets('shows no results state when filters return empty', (
        tester,
      ) async {
        // Create test transactions that won't match filters
        final testTransactions = [
          MockDataFactory.createTransaction(
            id: 'transaction-1',
            type: TransactionType.expense,
            description: 'Test Expense',
            transactionDate: DateTime(2024, 1, 15),
          ),
        ];

        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => Stream.value(testTransactions),
          ),
          timePeriodNotifierProvider.overrideWith(TestTimePeriodNotifier.new),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        await tester.pumpAndSettle();

        // Open filter bottom sheet
        await tester.tap(find.byIcon(Icons.filter_list));
        await tester.pumpAndSettle();

        // Select income filter (should filter out expense transaction)
        await tester.tap(find.text('Income'));
        await tester.pumpAndSettle();

        // Should show no results state
        expect(find.byIcon(Icons.search_off), findsOneWidget);
        expect(find.text('No transactions found'), findsOneWidget);
        expect(find.text('Clear Filters'), findsOneWidget);
      });

      testWidgets('clear filters button resets filters', (tester) async {
        // Create test transactions
        final testTransactions = [
          MockDataFactory.createTransaction(
            id: 'transaction-1',
            type: TransactionType.expense,
            description: 'Test Expense',
            transactionDate: DateTime(2024, 1, 15),
          ),
        ];

        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => Stream.value(testTransactions),
          ),
          timePeriodNotifierProvider.overrideWith(TestTimePeriodNotifier.new),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        await tester.pumpAndSettle();

        // Apply filter that results in no matches
        await tester.tap(find.byIcon(Icons.filter_list));
        await tester.pumpAndSettle();
        await tester.tap(find.text('Income'));
        await tester.pumpAndSettle();

        // Should show no results
        expect(find.text('No transactions found'), findsOneWidget);

        // Tap clear filters
        await tester.tap(find.text('Clear Filters'));
        await tester.pumpAndSettle();

        // Should show transactions again
        expect(find.byType(TransactionCard), findsWidgets);
      });
    });

    group('Filter and Search Functionality', () {
      testWidgets('opens filter bottom sheet when filter icon is tapped', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: MockProviders.transactionsOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Tap filter icon
        await tester.tap(find.byIcon(Icons.filter_list));
        await tester.pumpAndSettle();

        // Verify filter bottom sheet is shown with filter options
        expect(find.text('All'), findsOneWidget);
        expect(find.text('Income'), findsOneWidget);
        expect(find.text('Expense'), findsOneWidget);
        expect(find.text('Transfer'), findsOneWidget);
      });

      testWidgets('opens search bottom sheet when search icon is tapped', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: MockProviders.transactionsOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Tap search icon
        await tester.tap(find.byIcon(Icons.search));
        await tester.pumpAndSettle();

        // Verify search bottom sheet is shown
        expect(find.byType(TextField), findsOneWidget);
        expect(find.text('Search'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);
      });

      testWidgets('filter by expense type works correctly', (tester) async {
        final testTransactions = [
          MockDataFactory.createTransaction(
            id: 'transaction-1',
            type: TransactionType.expense,
            description: 'Test Expense',
            transactionDate: DateTime(2024, 1, 15),
          ),
          MockDataFactory.createTransaction(
            id: 'transaction-2',
            type: TransactionType.income,
            description: 'Test Income',
            transactionDate: DateTime(2024, 1, 16),
          ),
        ];

        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => Stream.value(testTransactions),
          ),
          timePeriodNotifierProvider.overrideWith(TestTimePeriodNotifier.new),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        await tester.pumpAndSettle();

        // Initially should show both transactions
        expect(find.byType(TransactionCard), findsNWidgets(2));

        // Apply expense filter
        await tester.tap(find.byIcon(Icons.filter_list));
        await tester.pumpAndSettle();
        await tester.tap(find.text('Expense'));
        await tester.pumpAndSettle();

        // Should only show expense transaction
        expect(find.byType(TransactionCard), findsOneWidget);
        expect(find.text('Test Expense'), findsOneWidget);
        expect(find.text('Test Income'), findsNothing);
      });

      testWidgets('filter by income type works correctly', (tester) async {
        final testTransactions = [
          MockDataFactory.createTransaction(
            id: 'transaction-1',
            type: TransactionType.expense,
            description: 'Test Expense',
            transactionDate: DateTime(2024, 1, 15),
          ),
          MockDataFactory.createTransaction(
            id: 'transaction-2',
            type: TransactionType.income,
            description: 'Test Income',
            transactionDate: DateTime(2024, 1, 16),
          ),
        ];

        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => Stream.value(testTransactions),
          ),
          timePeriodNotifierProvider.overrideWith(TestTimePeriodNotifier.new),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        await tester.pumpAndSettle();

        // Apply income filter
        await tester.tap(find.byIcon(Icons.filter_list));
        await tester.pumpAndSettle();
        await tester.tap(find.text('Income'));
        await tester.pumpAndSettle();

        // Should only show income transaction
        expect(find.byType(TransactionCard), findsOneWidget);
        expect(find.text('Test Income'), findsOneWidget);
        expect(find.text('Test Expense'), findsNothing);
      });

      testWidgets('filter by transfer type works correctly', (tester) async {
        final testTransactions = [
          MockDataFactory.createTransaction(
            id: 'transaction-1',
            type: TransactionType.transfer,
            description: 'Test Transfer',
            transactionDate: DateTime(2024, 1, 15),
          ),
          MockDataFactory.createTransaction(
            id: 'transaction-2',
            type: TransactionType.income,
            description: 'Test Income',
            transactionDate: DateTime(2024, 1, 16),
          ),
        ];

        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => Stream.value(testTransactions),
          ),
          timePeriodNotifierProvider.overrideWith(TestTimePeriodNotifier.new),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        await tester.pumpAndSettle();

        // Apply transfer filter
        await tester.tap(find.byIcon(Icons.filter_list));
        await tester.pumpAndSettle();
        await tester.tap(find.text('Transfer'));
        await tester.pumpAndSettle();

        // Should only show transfer transaction
        expect(find.byType(TransactionCard), findsOneWidget);
        expect(find.text('Test Transfer'), findsOneWidget);
        expect(find.text('Test Income'), findsNothing);
      });

      testWidgets('search functionality filters transactions correctly', (
        tester,
      ) async {
        final testTransactions = [
          MockDataFactory.createTransaction(
            id: 'transaction-1',
            type: TransactionType.expense,
            description: 'Grocery Shopping',
            transactionDate: DateTime(2024, 1, 15),
          ),
          MockDataFactory.createTransaction(
            id: 'transaction-2',
            type: TransactionType.income,
            description: 'Salary Payment',
            transactionDate: DateTime(2024, 1, 16),
          ),
        ];

        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => Stream.value(testTransactions),
          ),
          timePeriodNotifierProvider.overrideWith(TestTimePeriodNotifier.new),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        await tester.pumpAndSettle();

        // Initially should show both transactions
        expect(find.byType(TransactionCard), findsNWidgets(2));

        // Open search and enter query
        await tester.tap(find.byIcon(Icons.search));
        await tester.pumpAndSettle();

        await tester.enterText(find.byType(TextField), 'Grocery');
        await tester.tap(find.text('Search'));
        await tester.pumpAndSettle();

        // Should only show matching transaction
        expect(find.byType(TransactionCard), findsOneWidget);
        expect(find.text('Grocery Shopping'), findsOneWidget);
        expect(find.text('Salary Payment'), findsNothing);
      });
    });

    group('Navigation and Actions', () {
      testWidgets('clear filter button exists for filtered views', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(categoryId: 'test-category-id'),
            overrides: MockProviders.transactionsOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Verify clear filter button exists
        expect(find.byIcon(Icons.clear), findsOneWidget);

        // Verify it has a tooltip
        final clearButton = tester.widget<IconButton>(
          find.ancestor(
            of: find.byIcon(Icons.clear),
            matching: find.byType(IconButton),
          ),
        );
        expect(clearButton.tooltip, equals('Clear filter'));
      });

      testWidgets('transaction cards have proper navigation callbacks', (
        tester,
      ) async {
        final testTransactions = [
          MockDataFactory.createTransaction(
            id: 'transaction-1',
            type: TransactionType.expense,
            description: 'Test Transaction',
            transactionDate: DateTime(2024, 1, 15),
          ),
        ];

        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => Stream.value(testTransactions),
          ),
          timePeriodNotifierProvider.overrideWith(TestTimePeriodNotifier.new),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        await tester.pumpAndSettle();

        // Find transaction card and verify it has navigation callback
        expect(find.byType(TransactionCard), findsOneWidget);

        final transactionCard = tester.widget<TransactionCard>(
          find.byType(TransactionCard),
        );
        expect(transactionCard.onTap, isNotNull);
      });
    });

    group('Pull-to-Refresh Functionality', () {
      testWidgets('pull-to-refresh invalidates transaction provider', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: MockProviders.transactionsOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Find RefreshIndicator
        expect(find.byType(RefreshIndicator), findsOneWidget);

        // Perform pull-to-refresh gesture
        await tester.fling(
          find.byType(SingleChildScrollView),
          const Offset(0, 300),
          1000,
        );
        await tester.pump();
        await tester.pump(const Duration(seconds: 1));
        await tester.pumpAndSettle();

        // Refresh should complete without errors
        expect(find.byType(RefreshIndicator), findsOneWidget);
      });

      testWidgets('refresh indicator shows during pull-to-refresh', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: MockProviders.transactionsOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Start pull-to-refresh
        await tester.fling(
          find.byType(SingleChildScrollView),
          const Offset(0, 300),
          1000,
        );
        await tester.pump();

        // Should show refresh indicator
        expect(find.byType(RefreshProgressIndicator), findsOneWidget);
      });
    });

    group('Transaction Actions and Error Handling', () {
      testWidgets('handles transaction edit action correctly', (tester) async {
        final testTransactions = [
          MockDataFactory.createTransaction(
            id: 'transaction-1',
            type: TransactionType.expense,
            description: 'Test Transaction',
            transactionDate: DateTime(2024, 1, 15),
          ),
        ];

        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => Stream.value(testTransactions),
          ),
          timePeriodNotifierProvider.overrideWith(TestTimePeriodNotifier.new),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        await tester.pumpAndSettle();

        // Find transaction card and verify it has actions
        expect(find.byType(TransactionCard), findsOneWidget);

        // The TransactionCard should be configured with showActions: true
        final transactionCard = tester.widget<TransactionCard>(
          find.byType(TransactionCard),
        );
        expect(transactionCard.showActions, isTrue);
        expect(transactionCard.onEdit, isNotNull);
        expect(transactionCard.onDelete, isNotNull);
      });

      testWidgets('handles transaction deletion with confirmation', (
        tester,
      ) async {
        final testTransactions = [
          MockDataFactory.createTransaction(
            id: 'transaction-1',
            type: TransactionType.expense,
            description: 'Test Transaction',
            transactionDate: DateTime(2024, 1, 15),
          ),
        ];

        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => Stream.value(testTransactions),
          ),
          timePeriodNotifierProvider.overrideWith(TestTimePeriodNotifier.new),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        await tester.pumpAndSettle();

        // Verify transaction card has delete callback
        final transactionCard = tester.widget<TransactionCard>(
          find.byType(TransactionCard),
        );
        expect(transactionCard.onDelete, isNotNull);
      });

      testWidgets('shows error snackbar when deletion fails', (tester) async {
        final testTransactions = [
          MockDataFactory.createTransaction(
            id: 'transaction-1',
            type: TransactionType.expense,
            description: 'Test Transaction',
            transactionDate: DateTime(2024, 1, 15),
          ),
        ];

        // Create overrides with failing deletion
        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => Stream.value(testTransactions),
          ),
          timePeriodNotifierProvider.overrideWith(TestTimePeriodNotifier.new),
          // Add failing transaction deleter
          transactionDeleterProvider.overrideWith(
            () => throw Exception('Network error'),
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        await tester.pumpAndSettle();

        // The error handling is internal to the delete callback
        // We can verify the structure is correct
        expect(find.byType(TransactionCard), findsOneWidget);
      });
    });

    group('Time Period Integration', () {
      testWidgets('rebuilds when time period changes', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: MockProviders.transactionsOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Verify TimePeriodSelector is present
        expect(find.byType(TimePeriodSelector), findsOneWidget);

        // The screen watches timePeriodNotifierProvider to trigger rebuilds
        // This is tested by the provider watching behavior
        expect(find.byType(TransactionsListScreen), findsOneWidget);
      });

      testWidgets('filters transactions by current time period', (
        tester,
      ) async {
        final testTransactions = [
          MockDataFactory.createTransaction(
            id: 'transaction-1',
            type: TransactionType.expense,
            description: 'January Transaction',
            transactionDate: DateTime(2024, 1, 15), // Within period
          ),
          MockDataFactory.createTransaction(
            id: 'transaction-2',
            type: TransactionType.expense,
            description: 'February Transaction',
            transactionDate: DateTime(2024, 2, 15), // Outside period
          ),
        ];

        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => Stream.value(testTransactions),
          ),
          timePeriodNotifierProvider.overrideWith(TestTimePeriodNotifier.new),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        await tester.pumpAndSettle();

        // Should show transactions (filtering is done by the provider)
        expect(find.byType(TransactionCard), findsWidgets);
      });
    });

    group('Widget Lifecycle and State Management', () {
      testWidgets('properly initializes state variables', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: MockProviders.transactionsOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Verify widget is properly initialized
        expect(find.byType(TransactionsListScreen), findsOneWidget);
        expect(find.byType(Scaffold), findsWidgets);
        expect(find.byType(RefreshIndicator), findsOneWidget);
      });

      testWidgets('handles widget disposal correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: MockProviders.transactionsOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Verify widget is built
        expect(find.byType(TransactionsListScreen), findsOneWidget);

        // Remove widget to trigger disposal
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const SizedBox.shrink(),
            overrides: MockProviders.transactionsOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Verify widget is removed
        expect(find.byType(TransactionsListScreen), findsNothing);
      });

      testWidgets('maintains state during rebuilds', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: MockProviders.transactionsOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Apply a filter
        await tester.tap(find.byIcon(Icons.filter_list));
        await tester.pumpAndSettle();
        await tester.tap(find.text('Expense'));
        await tester.pumpAndSettle();

        // Trigger rebuild by hot reload simulation
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: MockProviders.transactionsOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Widget should maintain its structure
        expect(find.byType(TransactionsListScreen), findsOneWidget);
      });
    });

    group('Edge Cases and Error Scenarios', () {
      testWidgets('handles null category gracefully', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(categoryId: 'non-existent-category'),
            overrides: MockProviders.transactionsOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Should still display AppBar with fallback title
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.text('Transactions'), findsOneWidget);
      });

      testWidgets('handles null account gracefully', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(accountId: 'non-existent-account'),
            overrides: MockProviders.transactionsOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Should still display AppBar with fallback title
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.text('Transactions'), findsOneWidget);
      });

      testWidgets('handles empty search query correctly', (tester) async {
        final testTransactions = [
          MockDataFactory.createTransaction(
            id: 'transaction-1',
            type: TransactionType.expense,
            description: 'Test Transaction',
            transactionDate: DateTime(2024, 1, 15),
          ),
        ];

        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => Stream.value(testTransactions),
          ),
          timePeriodNotifierProvider.overrideWith(TestTimePeriodNotifier.new),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        await tester.pumpAndSettle();

        // Open search with empty query
        await tester.tap(find.byIcon(Icons.search));
        await tester.pumpAndSettle();

        await tester.enterText(find.byType(TextField), '');
        await tester.tap(find.text('Search'));
        await tester.pumpAndSettle();

        // Should show all transactions (empty search shows all)
        expect(find.byType(TransactionCard), findsOneWidget);
      });

      testWidgets('handles very long transaction descriptions', (tester) async {
        final testTransactions = [
          MockDataFactory.createTransaction(
            id: 'transaction-1',
            type: TransactionType.expense,
            description:
                'This is a very long transaction description that might cause layout issues if not handled properly in the UI components',
            transactionDate: DateTime(2024, 1, 15),
          ),
        ];

        final overrides = [
          ...MockProviders.authenticatedUserOverrides(),
          transactionListProvider.overrideWith(
            (ref) => Stream.value(testTransactions),
          ),
          timePeriodNotifierProvider.overrideWith(TestTimePeriodNotifier.new),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TransactionsListScreen(),
            overrides: overrides,
          ),
        );

        await tester.pumpAndSettle();

        // Should handle long descriptions gracefully
        expect(find.byType(TransactionCard), findsOneWidget);
      });
    });
  });
}
