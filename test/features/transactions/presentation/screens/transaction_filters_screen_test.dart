import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/transactions/presentation/screens/transaction_filters_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/test_wrapper.dart';

void main() {
  group('TransactionFiltersScreen Tests', () {
    testWidgets('should render screen with all UI components', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionFiltersScreen()),
      );

      await tester.pumpAndSettle();

      // Verify AppBar
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.text('Filters'), findsOneWidget);
      expect(find.text('Clear'), findsOneWidget);
      expect(find.text('Apply'), findsOneWidget);

      // Verify Transaction Type section
      expect(find.text('Transaction Type'), findsOneWidget);
      expect(find.byType(SegmentedButton<TransactionType?>), findsOneWidget);

      // Verify Date Range section
      expect(find.text('Date Range'), findsOneWidget);
      expect(find.text('Select date range'), findsOneWidget);
      expect(find.byIcon(Icons.date_range), findsOneWidget);

      // Verify Amount Range section
      expect(find.text('Amount Range'), findsOneWidget);
      expect(find.text('Min Amount'), findsOneWidget);
      expect(find.text('Max Amount'), findsOneWidget);

      // Verify Quick Filters section
      expect(find.text('Quick Filters'), findsOneWidget);
      expect(find.text('This Week'), findsOneWidget);
      expect(find.text('This Month'), findsOneWidget);
      expect(find.text('Last 30 Days'), findsOneWidget);
      expect(find.text('This Year'), findsOneWidget);
    });

    testWidgets('should display transaction type segments correctly', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionFiltersScreen()),
      );

      await tester.pumpAndSettle();

      // Verify all transaction type segments
      expect(find.text('All'), findsOneWidget);
      expect(find.text('Income'), findsOneWidget);
      expect(find.text('Expense'), findsOneWidget);
      expect(find.text('Transfer'), findsOneWidget);

      // Verify icons
      expect(find.byIcon(Icons.trending_up), findsOneWidget);
      expect(find.byIcon(Icons.trending_down), findsOneWidget);
      expect(find.byIcon(Icons.swap_horiz), findsOneWidget);
    });

    testWidgets('should handle transaction type selection', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionFiltersScreen()),
      );

      await tester.pumpAndSettle();

      // Tap on Income segment
      await tester.tap(find.text('Income'));
      await tester.pumpAndSettle();

      // Tap on Expense segment
      await tester.tap(find.text('Expense'));
      await tester.pumpAndSettle();

      // Tap on Transfer segment
      await tester.tap(find.text('Transfer'));
      await tester.pumpAndSettle();

      // Tap on All segment
      await tester.tap(find.text('All'));
      await tester.pumpAndSettle();
    });

    testWidgets('should handle amount input changes', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionFiltersScreen()),
      );

      await tester.pumpAndSettle();

      // Find min and max amount fields
      final minAmountField = find.widgetWithText(TextFormField, 'Min Amount');
      final maxAmountField = find.widgetWithText(TextFormField, 'Max Amount');

      expect(minAmountField, findsOneWidget);
      expect(maxAmountField, findsOneWidget);

      // Enter values in amount fields
      await tester.enterText(minAmountField, '10.50');
      await tester.pumpAndSettle();

      await tester.enterText(maxAmountField, '100.00');
      await tester.pumpAndSettle();
    });

    testWidgets('should handle quick filter chip selections', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionFiltersScreen()),
      );

      await tester.pumpAndSettle();

      // Test This Week filter
      await tester.tap(find.text('This Week'));
      await tester.pumpAndSettle();

      // Test This Month filter
      await tester.tap(find.text('This Month'));
      await tester.pumpAndSettle();

      // Test Last 30 Days filter
      await tester.tap(find.text('Last 30 Days'));
      await tester.pumpAndSettle();

      // Test This Year filter
      await tester.tap(find.text('This Year'));
      await tester.pumpAndSettle();
    });

    testWidgets('should clear all filters when Clear button is tapped', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionFiltersScreen()),
      );

      await tester.pumpAndSettle();

      // Set some filters first
      await tester.tap(find.text('Income'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('This Week'));
      await tester.pumpAndSettle();

      // Enter amount values
      final minAmountField = find.widgetWithText(TextFormField, 'Min Amount');
      await tester.enterText(minAmountField, '10.50');
      await tester.pumpAndSettle();

      // Clear filters
      await tester.tap(find.text('Clear'));
      await tester.pumpAndSettle();

      // Verify date range is cleared
      expect(find.text('Select date range'), findsOneWidget);
    });
  });

  group('TransactionFiltersScreen Date Range Tests', () {
    testWidgets('should show date range picker when date range card is tapped', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionFiltersScreen()),
      );

      await tester.pumpAndSettle();

      // Tap on date range card
      final dateRangeCard = find.byType(ListTile);
      await tester.tap(dateRangeCard);
      await tester.pumpAndSettle();

      // Note: DateRangePicker is a system dialog, so we can't easily test its appearance
      // but we can verify the tap doesn't cause errors
    });

    testWidgets('should display formatted date range when selected', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionFiltersScreen()),
      );

      await tester.pumpAndSettle();

      // Initially should show "Select date range"
      expect(find.text('Select date range'), findsOneWidget);
      expect(find.byIcon(Icons.chevron_right), findsOneWidget);
    });
  });

  group('TransactionFiltersScreen Navigation Tests', () {
    testWidgets('should have Apply and Clear buttons in app bar', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionFiltersScreen()),
      );

      await tester.pumpAndSettle();

      // Verify Apply and Clear buttons exist
      expect(find.text('Apply'), findsOneWidget);
      expect(find.text('Clear'), findsOneWidget);

      // Verify they are TextButtons
      expect(find.widgetWithText(TextButton, 'Apply'), findsOneWidget);
      expect(find.widgetWithText(TextButton, 'Clear'), findsOneWidget);
    });
  });

  group('TransactionFiltersScreen Edge Cases', () {
    testWidgets('should handle invalid amount input gracefully', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionFiltersScreen()),
      );

      await tester.pumpAndSettle();

      // Enter invalid amount values
      final minAmountField = find.widgetWithText(TextFormField, 'Min Amount');
      await tester.enterText(minAmountField, 'invalid');
      await tester.pumpAndSettle();

      final maxAmountField = find.widgetWithText(TextFormField, 'Max Amount');
      await tester.enterText(maxAmountField, 'also invalid');
      await tester.pumpAndSettle();

      // Should not crash
      expect(find.byType(TransactionFiltersScreen), findsOneWidget);
    });

    testWidgets('should handle empty amount input', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const TransactionFiltersScreen()),
      );

      await tester.pumpAndSettle();

      // Enter and clear amount values
      final minAmountField = find.widgetWithText(TextFormField, 'Min Amount');
      await tester.enterText(minAmountField, '10.50');
      await tester.pumpAndSettle();

      await tester.enterText(minAmountField, '');
      await tester.pumpAndSettle();

      // Should not crash
      expect(find.byType(TransactionFiltersScreen), findsOneWidget);
    });
  });
}
