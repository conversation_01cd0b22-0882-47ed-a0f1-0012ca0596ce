import 'package:budapp/features/transactions/presentation/widgets/amount_input_field.dart';
import 'package:budapp/features/transactions/presentation/widgets/date_picker_field.dart';
import 'package:budapp/features/transactions/presentation/widgets/description_field.dart';
import 'package:budapp/features/transactions/presentation/widgets/notes_field.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/services/currency_preferences_service.dart';
import 'package:budapp/widgets/common/app_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:intl/intl.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';

// Mock classes
class MockCurrencyPreferencesService extends Mock
    implements CurrencyPreferencesService {}

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(DateTime.now());
  });

  group('AmountInputField Advanced Tests', () {
    late MockCurrencyPreferencesService mockCurrencyService;
    late TextEditingController controller;

    setUp(() {
      mockCurrencyService = MockCurrencyPreferencesService();
      when(() => mockCurrencyService.getCurrentCurrency()).thenReturn('USD');
      when(
        () => mockCurrencyService.getCurrentCurrencySymbol(),
      ).thenReturn(r'$');
      controller = TextEditingController();
    });

    tearDown(() {
      controller.dispose();
    });

    Widget createAmountInputWidget({
      String? errorText,
      bool enabled = true,
      ValueChanged<String>? onChanged,
      String currencyCode = 'USD',
    }) {
      return TestWrapper.createTestWidget(
        AmountInputField(
          controller: controller,
          errorText: errorText,
          enabled: enabled,
          onChanged: onChanged,
        ),
        overrides: [
          currencyPreferencesServiceProvider.overrideWithValue(
            mockCurrencyService,
          ),
          currencyFormatterProvider.overrideWithValue(
            CurrencyFormatter(currencyCode),
          ),
        ],
      );
    }

    group('Currency Display Tests', () {
      testWidgets('should display different currency symbols correctly', (
        tester,
      ) async {
        // Test with Euro
        await tester.pumpWidget(createAmountInputWidget(currencyCode: 'EUR'));
        await tester.pumpAndSettle();

        expect(find.text('€'), findsOneWidget);
      });

      testWidgets('should display multi-character currency symbols', (
        tester,
      ) async {
        // Test with GBP
        await tester.pumpWidget(createAmountInputWidget(currencyCode: 'GBP'));
        await tester.pumpAndSettle();

        expect(find.text('£'), findsOneWidget);
      });

      testWidgets('should handle long currency symbols', (tester) async {
        // Test with default USD currency
        await tester.pumpWidget(createAmountInputWidget(currencyCode: 'USD'));
        await tester.pumpAndSettle();

        expect(find.text(r'$'), findsOneWidget);
      });
    });

    group('Input Validation and Formatting Tests', () {
      testWidgets('should prevent invalid character input', (tester) async {
        await tester.pumpWidget(createAmountInputWidget());
        await tester.pumpAndSettle();

        final textField = find.byType(TextFormField);

        // Test alphabet characters
        await tester.enterText(textField, 'abc');
        expect(controller.text, equals(''));

        // Test special characters (except decimal point)
        await tester.enterText(textField, r'@#$%');
        expect(controller.text, equals(''));

        // Test spaces (should be filtered out completely)
        await tester.enterText(textField, ' 123 ');
        expect(controller.text, equals(''));

        // Test valid numeric input
        await tester.enterText(textField, '123');
        expect(controller.text, equals('123'));
      });

      testWidgets('should handle decimal point validation', (tester) async {
        await tester.pumpWidget(createAmountInputWidget());
        await tester.pumpAndSettle();

        final textField = find.byType(TextFormField);

        // Test single decimal point
        await tester.enterText(textField, '123.45');
        expect(controller.text, equals('123.45'));

        // Test multiple decimal points (should prevent)
        await tester.enterText(textField, '123.45.67');
        expect(controller.text, isNot(equals('123.45.67')));

        // Test decimal point at start
        await tester.enterText(textField, '.50');
        expect(controller.text, equals('.50'));
      });

      testWidgets('should enforce maximum decimal places', (tester) async {
        await tester.pumpWidget(createAmountInputWidget());
        await tester.pumpAndSettle();

        final textField = find.byType(TextFormField);

        // Test exactly 2 decimal places (should be allowed)
        await tester.enterText(textField, '123.45');
        expect(controller.text, equals('123.45'));

        // Test more than 2 decimal places (should be truncated)
        await tester.enterText(textField, '123.456');
        expect(controller.text, equals('123.45'));

        // Test with 3+ decimal places
        await tester.enterText(textField, '123.9999');
        expect(controller.text, equals('123.99'));
      });

      testWidgets('should handle large numbers correctly', (tester) async {
        await tester.pumpWidget(createAmountInputWidget());
        await tester.pumpAndSettle();

        final textField = find.byType(TextFormField);

        // Test large whole number
        await tester.enterText(textField, '1234567890');
        expect(controller.text, equals('1234567890'));

        // Test large number with decimals
        await tester.enterText(textField, '1234567890.99');
        expect(controller.text, equals('1234567890.99'));
      });

      testWidgets('should handle edge case inputs', (tester) async {
        await tester.pumpWidget(createAmountInputWidget());
        await tester.pumpAndSettle();

        final textField = find.byType(TextFormField);

        // Test zero
        await tester.enterText(textField, '0');
        expect(controller.text, equals('0'));

        // Test zero with decimals
        await tester.enterText(textField, '0.00');
        expect(controller.text, equals('0.00'));

        // Test leading zeros
        await tester.enterText(textField, '000123.45');
        expect(controller.text, equals('000123.45'));

        // Test just decimal point
        await tester.enterText(textField, '.');
        expect(controller.text, equals('.'));
      });
    });

    group('Interaction and State Tests', () {
      testWidgets('should trigger onChanged callback with correct values', (
        tester,
      ) async {
        final changedValues = <String>[];

        await tester.pumpWidget(
          createAmountInputWidget(onChanged: changedValues.add),
        );
        await tester.pumpAndSettle();

        final textField = find.byType(TextFormField);

        await tester.enterText(textField, '1');
        expect(changedValues.last, equals('1'));

        await tester.enterText(textField, '12');
        expect(changedValues.last, equals('12'));

        await tester.enterText(textField, '12.5');
        expect(changedValues.last, equals('12.5'));

        await tester.enterText(textField, '12.50');
        expect(changedValues.last, equals('12.50'));
      });

      testWidgets('should handle text selection and editing', (tester) async {
        await tester.pumpWidget(createAmountInputWidget());
        await tester.pumpAndSettle();

        final textField = find.byType(TextFormField);

        // Enter initial text
        await tester.enterText(textField, '123.45');
        expect(controller.text, equals('123.45'));

        // Clear and enter new text
        await tester.enterText(textField, '');
        expect(controller.text, equals(''));

        await tester.enterText(textField, '67.89');
        expect(controller.text, equals('67.89'));
      });

      testWidgets('should maintain cursor position correctly', (tester) async {
        await tester.pumpWidget(createAmountInputWidget());
        await tester.pumpAndSettle();

        final textField = find.byType(TextFormField);

        // Enter text and check cursor is at end
        await tester.enterText(textField, '123.45');
        expect(controller.selection.baseOffset, equals(6));
        expect(controller.selection.extentOffset, equals(6));
      });

      testWidgets('should handle disabled state correctly', (tester) async {
        await tester.pumpWidget(createAmountInputWidget(enabled: false));
        await tester.pumpAndSettle();

        final appTextFormField = tester.widget<AppTextFormField>(
          find.byType(AppTextFormField),
        );
        expect(appTextFormField.enabled, isFalse);

        // Should not accept input when disabled
        final textField = find.byType(TextFormField);
        await tester.enterText(textField, '123.45');

        // Text should not be entered when disabled
        expect(controller.text, equals(''));
      });
    });

    group('Visual and Styling Tests', () {
      testWidgets('should apply correct text alignment', (tester) async {
        await tester.pumpWidget(createAmountInputWidget());
        await tester.pumpAndSettle();

        final appTextFormField = tester.widget<AppTextFormField>(
          find.byType(AppTextFormField),
        );
        expect(appTextFormField.textAlign, equals(TextAlign.end));
      });

      testWidgets('should display error styling when error text provided', (
        tester,
      ) async {
        const errorText = 'Invalid amount';
        await tester.pumpWidget(createAmountInputWidget(errorText: errorText));
        await tester.pumpAndSettle();

        // Currency container should reflect error state
        final containers = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(Container),
        );
        expect(containers, findsAtLeast(1));

        // Error text should be displayed
        expect(find.text(errorText), findsOneWidget);
      });

      testWidgets('should use correct keyboard type', (tester) async {
        await tester.pumpWidget(createAmountInputWidget());
        await tester.pumpAndSettle();

        final appTextFormField = tester.widget<AppTextFormField>(
          find.byType(AppTextFormField),
        );
        expect(
          appTextFormField.keyboardType,
          equals(
            const TextInputType.numberWithOptions(decimal: true, signed: false),
          ),
        );
      });

      testWidgets('should have correct input formatters', (tester) async {
        await tester.pumpWidget(createAmountInputWidget());
        await tester.pumpAndSettle();

        final appTextFormField = tester.widget<AppTextFormField>(
          find.byType(AppTextFormField),
        );
        expect(appTextFormField.inputFormatters, isNotNull);
        expect(appTextFormField.inputFormatters, hasLength(1));
        expect(
          appTextFormField.inputFormatters!.first,
          isA<FilteringTextInputFormatter>(),
        );
      });
    });
  });

  group('DatePickerField Advanced Tests', () {
    group('Date Display and Formatting Tests', () {
      testWidgets('should format different dates correctly', (tester) async {
        final testCases = [
          DateTime(2024, 1, 1),
          DateTime(2024, 12, 31),
          DateTime(2023, 6, 15),
          DateTime(2025, 3, 10),
        ];

        for (final date in testCases) {
          await tester.pumpWidget(
            TestWrapper.createTestWidget(
              DatePickerField(selectedDate: date, onChanged: (_) {}),
            ),
          );
          await tester.pumpAndSettle();

          // Should display formatted date
          final formattedDate = DateFormat.yMMMd().format(date);
          expect(
            find.textContaining(formattedDate.split(' ')[0]),
            findsOneWidget,
          ); // Month part
        }
      });

      testWidgets('should handle different locales for date formatting', (
        tester,
      ) async {
        final testDate = DateTime(2024, 3, 15);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DatePickerField(selectedDate: testDate, onChanged: (_) {}),
          ),
        );
        await tester.pumpAndSettle();

        // Should display date in default locale format
        expect(find.textContaining('Mar'), findsOneWidget);
        expect(find.textContaining('15'), findsOneWidget);
        expect(find.textContaining('2024'), findsOneWidget);
      });

      testWidgets('should handle edge case dates', (tester) async {
        // Test leap year date
        final leapYearDate = DateTime(2024, 2, 29);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DatePickerField(selectedDate: leapYearDate, onChanged: (_) {}),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.textContaining('Feb'), findsOneWidget);
        expect(find.textContaining('29'), findsOneWidget);
      });
    });

    group('Date Picker Interaction Tests', () {
      testWidgets('should open date picker with correct initial date', (
        tester,
      ) async {
        final initialDate = DateTime(2024, 6, 15);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DatePickerField(selectedDate: initialDate, onChanged: (_) {}),
          ),
        );
        await tester.pumpAndSettle();

        // Tap to open date picker
        await tester.tap(find.byType(InkWell));
        await tester.pumpAndSettle();

        // Date picker should be shown
        expect(find.byType(DatePickerDialog), findsOneWidget);
      });

      testWidgets(
        'should open date picker with current date when no date selected',
        (tester) async {
          await tester.pumpWidget(
            TestWrapper.createTestWidget(
              DatePickerField(selectedDate: null, onChanged: (_) {}),
            ),
          );
          await tester.pumpAndSettle();

          // Tap to open date picker
          await tester.tap(find.byType(InkWell));
          await tester.pumpAndSettle();

          // Date picker should be shown with current date
          expect(find.byType(DatePickerDialog), findsOneWidget);
        },
      );

      testWidgets('should handle date selection correctly', (tester) async {
        DateTime? selectedDate;

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DatePickerField(
              selectedDate: null,
              onChanged: (date) {
                selectedDate = date;
              },
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Open date picker
        await tester.tap(find.byType(InkWell));
        await tester.pumpAndSettle();

        // Confirm date selection (this will select the initial date shown)
        await tester.tap(find.text('OK'));
        await tester.pumpAndSettle();

        expect(selectedDate, isNotNull);
      });

      testWidgets('should cancel date selection correctly', (tester) async {
        DateTime? selectedDate;
        final initialDate = DateTime(2024, 1, 1);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DatePickerField(
              selectedDate: initialDate,
              onChanged: (date) {
                selectedDate = date;
              },
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Open date picker
        await tester.tap(find.byType(InkWell));
        await tester.pumpAndSettle();

        // Cancel date selection
        await tester.tap(find.text('Cancel'));
        await tester.pumpAndSettle();

        // Selected date should remain unchanged
        expect(selectedDate, isNull);
      });

      testWidgets('should respect date picker constraints', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DatePickerField(selectedDate: null, onChanged: (_) {}),
          ),
        );
        await tester.pumpAndSettle();

        // Open date picker
        await tester.tap(find.byType(InkWell));
        await tester.pumpAndSettle();

        // Date picker should be shown with proper constraints
        expect(find.byType(DatePickerDialog), findsOneWidget);

        // Should have year range (current year - 10 to current year + 1)
        // The exact year buttons might not be visible, but dialog should be present
        expect(find.byType(DatePickerDialog), findsOneWidget);
      });

      testWidgets('should handle multiple date picker interactions', (
        tester,
      ) async {
        final selectedDates = <DateTime>[];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DatePickerField(selectedDate: null, onChanged: selectedDates.add),
          ),
        );
        await tester.pumpAndSettle();

        // First interaction
        await tester.tap(find.byType(InkWell));
        await tester.pumpAndSettle();
        await tester.tap(find.text('OK'));
        await tester.pumpAndSettle();

        expect(selectedDates, hasLength(1));

        // Second interaction
        await tester.tap(find.byType(InkWell));
        await tester.pumpAndSettle();
        await tester.tap(find.text('OK'));
        await tester.pumpAndSettle();

        expect(selectedDates, hasLength(2));
      });
    });

    group('Error Handling and Edge Cases', () {
      testWidgets('should handle disabled state correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DatePickerField(
              selectedDate: null,
              onChanged: (_) {},
              enabled: false,
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Should not open date picker when disabled
        await tester.tap(find.byType(InkWell));
        await tester.pumpAndSettle();

        expect(find.byType(DatePickerDialog), findsNothing);
      });

      testWidgets('should display error styling when error provided', (
        tester,
      ) async {
        const errorText = 'Please select a date';

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DatePickerField(
              selectedDate: null,
              onChanged: (_) {},
              errorText: errorText,
            ),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text(errorText), findsOneWidget);

        // Container should have error styling
        final container = tester.widget<Container>(
          find
              .descendant(
                of: find.byType(DatePickerField),
                matching: find.byType(Container),
              )
              .last,
        );
        expect(container.decoration, isA<BoxDecoration>());
      });

      testWidgets('should handle rapid tap interactions', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DatePickerField(selectedDate: null, onChanged: (_) {}),
          ),
        );
        await tester.pumpAndSettle();

        // Find the specific InkWell for the DatePickerField
        final datePickerInkWell = find.descendant(
          of: find.byType(DatePickerField),
          matching: find.byType(InkWell),
        );

        // Rapid taps should not cause issues
        await tester.tap(datePickerInkWell);
        await tester.pump();
        await tester.tap(datePickerInkWell);
        await tester.pump();
        await tester.tap(datePickerInkWell);
        await tester.pumpAndSettle();

        // Should still work properly
        expect(find.byType(DatePickerDialog), findsOneWidget);
      });
    });

    group('Visual and Accessibility Tests', () {
      testWidgets('should display correct icons', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DatePickerField(selectedDate: null, onChanged: (_) {}),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.byIcon(Icons.calendar_today), findsOneWidget);
        expect(find.byIcon(Icons.arrow_drop_down), findsOneWidget);
      });

      testWidgets('should show proper visual states for enabled/disabled', (
        tester,
      ) async {
        // Test enabled state
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DatePickerField(
              selectedDate: null,
              onChanged: (_) {},
              enabled: true,
            ),
          ),
        );
        await tester.pumpAndSettle();

        final enabledIcons = find.byIcon(Icons.calendar_today);
        expect(enabledIcons, findsOneWidget);

        // Test disabled state
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DatePickerField(
              selectedDate: null,
              onChanged: (_) {},
              enabled: false,
            ),
          ),
        );
        await tester.pumpAndSettle();

        final disabledIcons = find.byIcon(Icons.calendar_today);
        expect(disabledIcons, findsOneWidget);

        // Icons should still be present but with different styling
        final iconWidget = tester.widget<Icon>(disabledIcons);
        expect(iconWidget.color, isNotNull);
      });

      testWidgets('should handle different themes correctly', (tester) async {
        // Test with light theme
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DatePickerField(
              selectedDate: DateTime(2024, 1, 15),
              onChanged: (_) {},
            ),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.byType(DatePickerField), findsOneWidget);
        expect(tester.takeException(), isNull);
      });
    });
  });

  group('DescriptionField Tests', () {
    testWidgets('should display correct label and placeholder', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          DescriptionField(controller: TextEditingController()),
        ),
      );
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(DescriptionField));
      final l10n = AppLocalizations.of(context)!;
      // The actual label includes "(optional)" suffix
      expect(
        find.text('${l10n.description} (${l10n.optional})'),
        findsOneWidget,
      );
    });

    testWidgets('should accept text input correctly', (tester) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestWrapper.createTestWidget(DescriptionField(controller: controller)),
      );
      await tester.pumpAndSettle();

      final textField = find.byType(TextFormField);
      await tester.enterText(textField, 'Test description');

      expect(controller.text, equals('Test description'));
    });

    testWidgets('should display error text when provided', (tester) async {
      const errorText = 'Description is too long';

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          DescriptionField(
            controller: TextEditingController(),
            errorText: errorText,
          ),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text(errorText), findsOneWidget);
    });
  });

  group('NotesField Tests', () {
    testWidgets('should display correct label and be multiline', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          NotesField(controller: TextEditingController()),
        ),
      );
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(NotesField));
      final l10n = AppLocalizations.of(context)!;
      // The actual label includes "(optional)" suffix
      expect(find.text('${l10n.notes} (${l10n.optional})'), findsOneWidget);

      // Should be multiline
      final textField = tester.widget<AppTextFormField>(
        find.byType(AppTextFormField),
      );
      expect(textField.maxLines, greaterThan(1));
    });

    testWidgets('should accept multiline text input', (tester) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestWrapper.createTestWidget(NotesField(controller: controller)),
      );
      await tester.pumpAndSettle();

      final textField = find.byType(TextFormField);
      const multilineText = 'Line 1\nLine 2\nLine 3';
      await tester.enterText(textField, multilineText);

      expect(controller.text, equals(multilineText));
    });

    testWidgets('should display error text when provided', (tester) async {
      const errorText = 'Notes are too long';

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          NotesField(controller: TextEditingController(), errorText: errorText),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text(errorText), findsOneWidget);
    });
  });
}
