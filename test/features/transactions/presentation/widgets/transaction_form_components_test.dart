import 'dart:async';

import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/features/transactions/presentation/widgets/account_selector.dart';
import 'package:budapp/features/transactions/presentation/widgets/amount_input_field.dart';
import 'package:budapp/features/transactions/presentation/widgets/category_selector.dart';
import 'package:budapp/features/transactions/presentation/widgets/date_picker_field.dart';
import 'package:budapp/features/transactions/presentation/widgets/description_field.dart';
import 'package:budapp/features/transactions/presentation/widgets/notes_field.dart';
import 'package:budapp/features/transactions/presentation/widgets/transaction_form.dart';
import 'package:budapp/features/transactions/presentation/widgets/transaction_type_selector.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/services/currency_preferences_service.dart';
import 'package:budapp/widgets/common/app_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/mock_data_factory.dart';
import '../../../../helpers/mock_providers.dart';
import '../../../../helpers/test_wrapper.dart';

// Mock classes
class MockCurrencyPreferencesService extends Mock
    implements CurrencyPreferencesService {}

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(DateTime.now());
    registerFallbackValue(TransactionType.expense);
  });

  group('AmountInputField Tests', () {
    late MockCurrencyPreferencesService mockCurrencyService;
    late TextEditingController controller;

    setUp(() {
      mockCurrencyService = MockCurrencyPreferencesService();
      when(() => mockCurrencyService.getCurrentCurrency()).thenReturn('USD');
      when(
        () => mockCurrencyService.getCurrentCurrencySymbol(),
      ).thenReturn(r'$');
      controller = TextEditingController();
    });

    tearDown(() {
      controller.dispose();
    });

    Widget createAmountInputWidget({
      String? errorText,
      bool enabled = true,
      ValueChanged<String>? onChanged,
    }) {
      return TestWrapper.createTestWidget(
        AmountInputField(
          controller: controller,
          errorText: errorText,
          enabled: enabled,
          onChanged: onChanged,
        ),
        overrides: [
          currencyPreferencesServiceProvider.overrideWithValue(
            mockCurrencyService,
          ),
        ],
      );
    }

    testWidgets('should display currency symbol', (tester) async {
      await tester.pumpWidget(createAmountInputWidget());
      await tester.pumpAndSettle();

      expect(find.text(r'$'), findsOneWidget);
    });

    testWidgets('should display amount label with required asterisk', (
      tester,
    ) async {
      await tester.pumpWidget(createAmountInputWidget());
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(AmountInputField));
      final l10n = AppLocalizations.of(context)!;
      expect(find.text('${l10n.amount} *'), findsOneWidget);
    });

    testWidgets('should accept numeric input with decimal places', (
      tester,
    ) async {
      await tester.pumpWidget(createAmountInputWidget());
      await tester.pumpAndSettle();

      final textField = find.byType(TextFormField);
      await tester.enterText(textField, '123.45');
      await tester.pumpAndSettle();

      expect(controller.text, equals('123.45'));
    });

    testWidgets('should restrict input to valid decimal format', (
      tester,
    ) async {
      await tester.pumpWidget(createAmountInputWidget());
      await tester.pumpAndSettle();

      final textField = find.byType(TextFormField);

      // Test valid inputs
      await tester.enterText(textField, '123');
      expect(controller.text, equals('123'));

      await tester.enterText(textField, '123.45');
      expect(controller.text, equals('123.45'));

      await tester.enterText(textField, '123.5');
      expect(controller.text, equals('123.5'));

      // Test invalid inputs that should be filtered
      await tester.enterText(
        textField,
        '123.456',
      ); // More than 2 decimal places
      expect(controller.text, equals('123.45')); // Should truncate

      await tester.enterText(textField, 'abc');
      expect(controller.text, equals('')); // Should clear invalid input
    });

    testWidgets('should call onChanged callback when text changes', (
      tester,
    ) async {
      String? changedValue;
      await tester.pumpWidget(
        createAmountInputWidget(onChanged: (value) => changedValue = value),
      );
      await tester.pumpAndSettle();

      final textField = find.byType(TextFormField);
      await tester.enterText(textField, '50.25');
      await tester.pumpAndSettle();

      expect(changedValue, equals('50.25'));
    });

    testWidgets('should display error text when provided', (tester) async {
      const errorText = 'Amount is required';
      await tester.pumpWidget(createAmountInputWidget(errorText: errorText));
      await tester.pumpAndSettle();

      expect(find.text(errorText), findsOneWidget);
    });

    testWidgets('should show error styling when error text is provided', (
      tester,
    ) async {
      const errorText = 'Amount is required';
      await tester.pumpWidget(createAmountInputWidget(errorText: errorText));
      await tester.pumpAndSettle();

      // Currency container should have error border color
      final container = tester.widget<Container>(
        find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(Container),
        ),
      );
      expect(container.decoration, isA<BoxDecoration>());
      final decoration = container.decoration! as BoxDecoration;
      expect(decoration.border, isA<Border>());
    });

    testWidgets('should be disabled when enabled is false', (tester) async {
      await tester.pumpWidget(createAmountInputWidget(enabled: false));
      await tester.pumpAndSettle();

      final textField = tester.widget<AppTextFormField>(
        find.byType(AppTextFormField),
      );
      expect(textField.enabled, isFalse);
    });

    testWidgets('should align text to the right', (tester) async {
      await tester.pumpWidget(createAmountInputWidget());
      await tester.pumpAndSettle();

      final textField = tester.widget<AppTextFormField>(
        find.byType(AppTextFormField),
      );
      expect(textField.textAlign, equals(TextAlign.end));
    });

    testWidgets('should have correct keyboard type', (tester) async {
      await tester.pumpWidget(createAmountInputWidget());
      await tester.pumpAndSettle();

      final textField = tester.widget<AppTextFormField>(
        find.byType(AppTextFormField),
      );
      expect(
        textField.keyboardType,
        equals(
          const TextInputType.numberWithOptions(decimal: true, signed: false),
        ),
      );
    });

    testWidgets('should have correct input formatters', (tester) async {
      await tester.pumpWidget(createAmountInputWidget());
      await tester.pumpAndSettle();

      final textField = tester.widget<AppTextFormField>(
        find.byType(AppTextFormField),
      );
      expect(textField.inputFormatters, isNotEmpty);
      expect(
        textField.inputFormatters?.first,
        isA<FilteringTextInputFormatter>(),
      );
    });
  });

  group('DatePickerField Tests', () {
    testWidgets('should display date label with required asterisk', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          DatePickerField(selectedDate: null, onChanged: (_) {}),
        ),
      );
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(DatePickerField));
      final l10n = AppLocalizations.of(context)!;
      expect(find.text(l10n.date), findsOneWidget);
      expect(find.text('*'), findsOneWidget);
    });

    testWidgets('should display "Select Date" when no date is selected', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          DatePickerField(selectedDate: null, onChanged: (_) {}),
        ),
      );
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(DatePickerField));
      final l10n = AppLocalizations.of(context)!;
      expect(find.text(l10n.selectDate), findsOneWidget);
    });

    testWidgets('should display formatted date when date is selected', (
      tester,
    ) async {
      final selectedDate = DateTime(2024, 1, 15);
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          DatePickerField(selectedDate: selectedDate, onChanged: (_) {}),
        ),
      );
      await tester.pumpAndSettle();

      // Should display formatted date (format: yMMMd)
      expect(find.textContaining('Jan'), findsOneWidget);
      expect(find.textContaining('15'), findsOneWidget);
      expect(find.textContaining('2024'), findsOneWidget);
    });

    testWidgets('should display calendar icon', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          DatePickerField(selectedDate: null, onChanged: (_) {}),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.calendar_today), findsOneWidget);
      expect(find.byIcon(Icons.arrow_drop_down), findsOneWidget);
    });

    testWidgets('should open date picker when tapped', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          DatePickerField(selectedDate: null, onChanged: (_) {}),
        ),
      );
      await tester.pumpAndSettle();

      // Tap the date picker field
      final datePickerInkWell = find.descendant(
        of: find.byType(DatePickerField),
        matching: find.byType(InkWell),
      );
      await tester.tap(datePickerInkWell);
      await tester.pumpAndSettle();

      // Should show date picker dialog
      expect(find.byType(DatePickerDialog), findsOneWidget);
    });

    testWidgets('should display error text when provided', (tester) async {
      const errorText = 'Date is required';
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          DatePickerField(
            selectedDate: null,
            onChanged: (_) {},
            errorText: errorText,
          ),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text(errorText), findsOneWidget);
    });

    testWidgets('should be disabled when enabled is false', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          DatePickerField(
            selectedDate: null,
            onChanged: (_) {},
            enabled: false,
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Should not respond to tap when disabled
      final datePickerInkWell = find.descendant(
        of: find.byType(DatePickerField),
        matching: find.byType(InkWell),
      );
      await tester.tap(datePickerInkWell);
      await tester.pumpAndSettle();

      // Date picker should not open
      expect(find.byType(DatePickerDialog), findsNothing);
    });

    testWidgets('should show error styling when error text provided', (
      tester,
    ) async {
      const errorText = 'Date is required';
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          DatePickerField(
            selectedDate: null,
            onChanged: (_) {},
            errorText: errorText,
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Container should have error border styling
      final container = tester.widget<Container>(
        find
            .descendant(
              of: find.byType(DatePickerField),
              matching: find.byType(Container),
            )
            .last, // Get the main container, not the column container
      );
      expect(container.decoration, isA<BoxDecoration>());
    });
  });

  group('TransactionTypeSelector Tests', () {
    testWidgets('should display all transaction types', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          TransactionTypeSelector(
            selectedType: TransactionType.expense,
            onChanged: (_) {},
          ),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text('Expense'), findsOneWidget);
      expect(find.text('Income'), findsOneWidget);
      expect(find.text('Transfer'), findsOneWidget);
    });

    testWidgets('should highlight selected transaction type', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          TransactionTypeSelector(
            selectedType: TransactionType.income,
            onChanged: (_) {},
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Income should be selected in the SegmentedButton
      final segmentedButton = tester.widget<SegmentedButton<TransactionType>>(
        find.byType(SegmentedButton<TransactionType>),
      );
      expect(segmentedButton.selected, contains(TransactionType.income));
    });

    testWidgets('should call onChanged when different type is selected', (
      tester,
    ) async {
      TransactionType? changedType;
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          TransactionTypeSelector(
            selectedType: TransactionType.expense,
            onChanged: (type) => changedType = type,
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Tap on Income tab
      await tester.tap(find.text('Income'));
      await tester.pumpAndSettle();

      expect(changedType, equals(TransactionType.income));
    });

    testWidgets('should handle tab switching correctly', (tester) async {
      var selectedType = TransactionType.expense;
      late StateSetter setState;

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          StatefulBuilder(
            builder: (context, stateSetter) {
              setState = stateSetter;
              return TransactionTypeSelector(
                selectedType: selectedType,
                onChanged: (type) {
                  setState(() {
                    selectedType = type;
                  });
                },
              );
            },
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Initially Expense should be selected
      expect(find.text('Expense'), findsOneWidget);

      // Switch to Transfer
      await tester.tap(find.text('Transfer'));
      await tester.pumpAndSettle();

      expect(selectedType, equals(TransactionType.transfer));
    });
  });

  group('TransactionForm Integration Tests', () {
    late List<Account> mockAccounts;
    late List<Category> mockCategories;
    late MockCurrencyPreferencesService mockCurrencyService;

    setUp(() {
      mockAccounts = [
        MockDataFactory.createAccount(
          id: 'account1',
          userId: 'user1',
          name: 'Checking Account',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 100000,
        ),
        MockDataFactory.createAccount(
          id: 'account2',
          userId: 'user1',
          name: 'Savings Account',
          type: AccountType.savings,
          classification: AccountClassification.asset,
          initialBalanceCents: 50000,
        ),
      ];

      mockCategories = [
        MockDataFactory.createCategory(
          id: 'category1',
          userId: 'user1',
          name: 'Food & Dining',
          type: CategoryType.expense,
          icon: 'restaurant',
          color: 'red',
          schemaVersion: 1,
        ),
        MockDataFactory.createCategory(
          id: 'category2',
          userId: 'user1',
          name: 'Salary',
          type: CategoryType.income,
          icon: 'work',
          color: 'green',
          schemaVersion: 1,
        ),
      ];

      mockCurrencyService = MockCurrencyPreferencesService();
      when(() => mockCurrencyService.getCurrentCurrency()).thenReturn('USD');
      when(
        () => mockCurrencyService.getCurrentCurrencySymbol(),
      ).thenReturn(r'$');
    });

    Widget createTransactionFormWidget({
      Transaction? initialTransaction,
      Future<void> Function(Transaction)? onSubmit,
    }) {
      return TestWrapper.createTestWidget(
        SingleChildScrollView(
          child: TransactionForm(
            initialTransaction: initialTransaction,
            onSubmit: onSubmit,
          ),
        ),
        overrides: [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
          currencyPreferencesServiceProvider.overrideWithValue(
            mockCurrencyService,
          ),
          // Add authentication overrides to prevent "User not authenticated" errors
          ...MockProviders.authenticatedUserOverrides(),
        ],
      );
    }

    testWidgets('should render all form components for expense transaction', (
      tester,
    ) async {
      await tester.pumpWidget(createTransactionFormWidget());
      await tester.pumpAndSettle();

      // Check all form components are present
      expect(find.byType(TransactionTypeSelector), findsOneWidget);
      expect(find.byType(AmountInputField), findsOneWidget);
      expect(find.byType(AccountSelector), findsOneWidget);
      expect(find.byType(CategorySelector), findsOneWidget);
      expect(find.byType(DatePickerField), findsOneWidget);
      expect(find.byType(DescriptionField), findsOneWidget);
      expect(find.byType(NotesField), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('should show correct account selector label for expense', (
      tester,
    ) async {
      await tester.pumpWidget(createTransactionFormWidget());
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(TransactionForm));
      final l10n = AppLocalizations.of(context)!;
      expect(find.text(l10n.fromAccount), findsOneWidget);
    });

    testWidgets('should switch to income layout when income type selected', (
      tester,
    ) async {
      await tester.pumpWidget(createTransactionFormWidget());
      await tester.pumpAndSettle();

      // Switch to income
      await tester.tap(find.text('Income'));
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(TransactionForm));
      final l10n = AppLocalizations.of(context)!;
      expect(find.text(l10n.toAccount), findsOneWidget);
      expect(find.byType(CategorySelector), findsOneWidget);
    });

    testWidgets(
      'should switch to transfer layout when transfer type selected',
      (tester) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Switch to transfer
        await tester.tap(find.text('Transfer'));
        await tester.pumpAndSettle();

        final context = tester.element(find.byType(TransactionForm));
        final l10n = AppLocalizations.of(context)!;
        expect(find.text(l10n.fromAccount), findsOneWidget);
        expect(find.text(l10n.toAccount), findsOneWidget);
        expect(
          find.byType(CategorySelector),
          findsNothing,
        ); // No category for transfers
      },
    );

    testWidgets('should have disabled submit button initially', (tester) async {
      await tester.pumpWidget(createTransactionFormWidget());
      await tester.pumpAndSettle();

      final submitButton = tester.widget<ElevatedButton>(
        find.byType(ElevatedButton),
      );
      expect(submitButton.onPressed, isNull); // Disabled
    });

    testWidgets('should enable submit button when required fields filled', (
      tester,
    ) async {
      await tester.pumpWidget(createTransactionFormWidget());
      await tester.pumpAndSettle();

      // Fill amount
      final amountField = find.descendant(
        of: find.byType(AmountInputField),
        matching: find.byType(TextFormField),
      );
      await tester.enterText(amountField, '25.50');
      await tester.pumpAndSettle();

      // Select account
      final accountDropdown = find.descendant(
        of: find.byType(AccountSelector),
        matching: find.byType(DropdownButtonFormField<String>),
      );
      await tester.tap(accountDropdown);
      await tester.pumpAndSettle();
      await tester.tap(find.text(r'Checking Account - $1,000.00').last);
      await tester.pumpAndSettle();

      // Select category
      final categoryDropdown = find.descendant(
        of: find.byType(CategorySelector),
        matching: find.byType(DropdownButtonFormField<String>),
      );
      await tester.tap(categoryDropdown);
      await tester.pumpAndSettle();
      await tester.tap(find.text('Food & Dining').last);
      await tester.pumpAndSettle();

      // Select date
      final datePickerInkWell = find.descendant(
        of: find.byType(DatePickerField),
        matching: find.byType(InkWell),
      );
      await tester.tap(datePickerInkWell);
      await tester.pumpAndSettle();
      await tester.tap(find.text('OK'));
      await tester.pumpAndSettle();

      // Submit button should now be enabled
      final submitButton = tester.widget<ElevatedButton>(
        find.byType(ElevatedButton),
      );
      expect(submitButton.onPressed, isNotNull); // Enabled
    });

    testWidgets(
      'should enable submit button when all required fields are filled',
      (tester) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Initially submit button should be disabled
        final submitButton = find.byType(ElevatedButton);
        expect(tester.widget<ElevatedButton>(submitButton).onPressed, isNull);

        // Fill required fields
        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(amountField, '25.50');
        await tester.pumpAndSettle();

        // Select account
        final accountDropdown = find.descendant(
          of: find.byType(AccountSelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(accountDropdown);
        await tester.pumpAndSettle();
        await tester.tap(find.text(r'Checking Account - $1,000.00').last);
        await tester.pumpAndSettle();

        // Select category
        final categoryDropdown = find.descendant(
          of: find.byType(CategorySelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(categoryDropdown);
        await tester.pumpAndSettle();
        await tester.tap(find.text('Food & Dining').last);
        await tester.pumpAndSettle();

        // Select date
        final datePickerInkWell = find.descendant(
          of: find.byType(DatePickerField),
          matching: find.byType(InkWell),
        );
        await tester.tap(datePickerInkWell);
        await tester.pumpAndSettle();
        await tester.tap(find.text('OK'));
        await tester.pumpAndSettle();

        // Now submit button should be enabled (form is complete)
        expect(
          tester.widget<ElevatedButton>(submitButton).onPressed,
          isNotNull,
        );
      },
    );

    testWidgets('should initialize form with existing transaction data', (
      tester,
    ) async {
      final existingTransaction = MockDataFactory.createTransaction(
        id: 'existing-1',
        userId: 'user1',
        type: TransactionType.income,
        amountCents: 5000,
        description: 'Salary payment',
        notes: 'Monthly salary',
        fromAccountId: null,
        toAccountId: 'account1',
        categoryId: 'category2',
        transactionDate: DateTime(2024, 1, 15),
      );

      await tester.pumpWidget(
        createTransactionFormWidget(initialTransaction: existingTransaction),
      );
      await tester.pumpAndSettle();

      // Should show income type selected in the SegmentedButton
      final segmentedButton = tester.widget<SegmentedButton<TransactionType>>(
        find.byType(SegmentedButton<TransactionType>),
      );
      expect(segmentedButton.selected, contains(TransactionType.income));

      // Description field should be filled
      final descriptionField = find.descendant(
        of: find.byType(DescriptionField),
        matching: find.byType(TextFormField),
      );
      final descriptionWidget = tester.widget<TextFormField>(descriptionField);
      expect(descriptionWidget.controller?.text, equals('Salary payment'));

      // Notes field should be filled
      final notesField = find.descendant(
        of: find.byType(NotesField),
        matching: find.byType(TextFormField),
      );
      final notesWidget = tester.widget<TextFormField>(notesField);
      expect(notesWidget.controller?.text, equals('Monthly salary'));
    });

    testWidgets('should show correct button text for new transaction', (
      tester,
    ) async {
      await tester.pumpWidget(createTransactionFormWidget());
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(TransactionForm));
      final l10n = AppLocalizations.of(context)!;
      expect(find.text(l10n.createTransaction), findsOneWidget);
    });

    testWidgets('should show correct button text for editing transaction', (
      tester,
    ) async {
      final existingTransaction = MockDataFactory.createTransaction(
        id: 'existing-1',
        userId: 'user1',
        type: TransactionType.expense,
        amountCents: 5000,
        description: 'Test transaction',
        fromAccountId: 'account1',
        categoryId: 'category1',
      );

      await tester.pumpWidget(
        createTransactionFormWidget(initialTransaction: existingTransaction),
      );
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(TransactionForm));
      final l10n = AppLocalizations.of(context)!;
      expect(find.text(l10n.updateTransaction), findsOneWidget);
    });
  });
}
