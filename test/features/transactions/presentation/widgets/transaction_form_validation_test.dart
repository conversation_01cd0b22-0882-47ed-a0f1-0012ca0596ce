import 'dart:async';

import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/features/transactions/presentation/widgets/account_selector.dart';
import 'package:budapp/features/transactions/presentation/widgets/amount_input_field.dart';
import 'package:budapp/features/transactions/presentation/widgets/category_selector.dart';
import 'package:budapp/features/transactions/presentation/widgets/date_picker_field.dart';
import 'package:budapp/features/transactions/presentation/widgets/description_field.dart';
import 'package:budapp/features/transactions/presentation/widgets/notes_field.dart';
import 'package:budapp/features/transactions/presentation/widgets/transaction_form.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/services/currency_preferences_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/mock_data_factory.dart';
import '../../../../helpers/mock_providers.dart';
import '../../../../helpers/test_wrapper.dart';

// Mock classes
class MockCurrencyPreferencesService extends Mock
    implements CurrencyPreferencesService {}

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(DateTime.now());
    registerFallbackValue(TransactionType.expense);
  });

  group('TransactionForm Validation Tests', () {
    late List<Account> mockAccounts;
    late List<Category> mockCategories;
    late MockCurrencyPreferencesService mockCurrencyService;

    setUp(() {
      mockAccounts = [
        MockDataFactory.createAccount(
          id: 'account1',
          userId: 'user1',
          name: 'Checking Account',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 100000,
        ),
        MockDataFactory.createAccount(
          id: 'account2',
          userId: 'user1',
          name: 'Savings Account',
          type: AccountType.savings,
          classification: AccountClassification.asset,
          initialBalanceCents: 50000,
        ),
      ];

      mockCategories = [
        MockDataFactory.createCategory(
          id: 'category1',
          userId: 'user1',
          name: 'Food & Dining',
          type: CategoryType.expense,
          icon: 'restaurant',
          color: 'red',
          schemaVersion: 1,
        ),
        MockDataFactory.createCategory(
          id: 'category2',
          userId: 'user1',
          name: 'Salary',
          type: CategoryType.income,
          icon: 'work',
          color: 'green',
          schemaVersion: 1,
        ),
      ];

      mockCurrencyService = MockCurrencyPreferencesService();
      when(() => mockCurrencyService.getCurrentCurrency()).thenReturn('USD');
      when(
        () => mockCurrencyService.getCurrentCurrencySymbol(),
      ).thenReturn(r'$');
    });

    Widget createTransactionFormWidget({
      Transaction? initialTransaction,
      Future<void> Function(Transaction)? onSubmit,
    }) {
      return TestWrapper.createTestWidget(
        SingleChildScrollView(
          child: TransactionForm(
            initialTransaction: initialTransaction,
            onSubmit: onSubmit,
          ),
        ),
        overrides: [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
          categoryListProvider.overrideWith(
            (ref) => Stream.value(mockCategories),
          ),
          currencyPreferencesServiceProvider.overrideWithValue(
            mockCurrencyService,
          ),
          // Add authentication overrides to prevent "User not authenticated" errors
          ...MockProviders.authenticatedUserOverrides(),
        ],
      );
    }

    group('Amount Validation', () {
      testWidgets('should show validation error for empty amount', (
        tester,
      ) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Try to submit without amount - ensure button is visible first
        await tester.ensureVisible(find.byType(ElevatedButton));
        await tester.tap(find.byType(ElevatedButton), warnIfMissed: false);
        await tester.pumpAndSettle();

        // Submit button should remain disabled
        final submitButton = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(submitButton.onPressed, isNull);
      });

      testWidgets('should show validation error for zero amount', (
        tester,
      ) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Enter zero amount
        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(amountField, '0');
        await tester.pumpAndSettle();

        // Submit button should remain disabled
        final submitButton = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(submitButton.onPressed, isNull);
      });

      testWidgets('should show validation error for negative amount', (
        tester,
      ) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Enter negative amount (should be prevented by input formatter)
        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(amountField, '-50');
        await tester.pumpAndSettle();

        // Input formatter should prevent negative input
        final controller = tester.widget<TextFormField>(amountField).controller;
        expect(controller?.text, isNot(contains('-')));
      });

      testWidgets('should accept valid positive amount', (tester) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Enter valid amount
        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(amountField, '25.50');
        await tester.pumpAndSettle();

        // Controller should have the amount
        final controller = tester.widget<TextFormField>(amountField).controller;
        expect(controller?.text, equals('25.50'));
      });

      testWidgets('should validate decimal places correctly', (tester) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );

        // Test valid decimal formats
        await tester.enterText(amountField, '25');
        expect(
          tester.widget<TextFormField>(amountField).controller?.text,
          equals('25'),
        );

        await tester.enterText(amountField, '25.5');
        expect(
          tester.widget<TextFormField>(amountField).controller?.text,
          equals('25.5'),
        );

        await tester.enterText(amountField, '25.50');
        expect(
          tester.widget<TextFormField>(amountField).controller?.text,
          equals('25.50'),
        );

        // Test invalid decimal format (more than 2 decimal places)
        await tester.enterText(amountField, '25.999');
        expect(
          tester.widget<TextFormField>(amountField).controller?.text,
          isNot(equals('25.999')),
        );
      });
    });

    group('Account Validation', () {
      testWidgets('should require account selection for expense', (
        tester,
      ) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Fill amount but not account
        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(amountField, '25.50');
        await tester.pumpAndSettle();

        // Submit button should remain disabled
        final submitButton = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(submitButton.onPressed, isNull);
      });

      testWidgets('should require account selection for income', (
        tester,
      ) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Switch to income
        await tester.tap(find.text('Income'));
        await tester.pumpAndSettle();

        // Fill amount but not account
        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(amountField, '25.50');
        await tester.pumpAndSettle();

        // Submit button should remain disabled
        final submitButton = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(submitButton.onPressed, isNull);
      });

      testWidgets('should require both accounts for transfer', (tester) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Switch to transfer
        await tester.tap(find.text('Transfer'));
        await tester.pumpAndSettle();

        // Fill amount
        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(amountField, '25.50');
        await tester.pumpAndSettle();

        // Select only from account
        final fromAccountDropdown = find.descendant(
          of: find.byType(AccountSelector).first,
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(fromAccountDropdown);
        await tester.pumpAndSettle();
        await tester.tap(find.text(r'Checking Account - $1,000.00').last);
        await tester.pumpAndSettle();

        // Submit button should still be disabled (missing to account)
        final submitButton = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(submitButton.onPressed, isNull);
      });

      testWidgets('should prevent same account selection for transfer', (
        tester,
      ) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Switch to transfer
        await tester.tap(find.text('Transfer'));
        await tester.pumpAndSettle();

        // Fill amount
        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(amountField, '25.50');
        await tester.pumpAndSettle();

        // Select same account for both from and to
        final accountDropdowns = find.descendant(
          of: find.byType(AccountSelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );

        // Select from account
        await tester.tap(accountDropdowns.first);
        await tester.pumpAndSettle();
        await tester.tap(find.text(r'Checking Account - $1,000.00').last);
        await tester.pumpAndSettle();

        // Try to select same account for to account
        await tester.tap(accountDropdowns.last);
        await tester.pumpAndSettle();
        await tester.tap(find.text(r'Checking Account - $1,000.00').last);
        await tester.pumpAndSettle();

        // Submit button should remain disabled (same account for both)
        final submitButton = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(submitButton.onPressed, isNull);
      });
    });

    group('Category Validation', () {
      testWidgets('should require category for expense transactions', (
        tester,
      ) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Fill amount and account but not category
        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(amountField, '25.50');
        await tester.pumpAndSettle();

        // Select account
        final accountDropdown = find.descendant(
          of: find.byType(AccountSelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(accountDropdown);
        await tester.pumpAndSettle();
        await tester.tap(find.text(r'Checking Account - $1,000.00').last);
        await tester.pumpAndSettle();

        // Submit button should remain disabled without category
        final submitButton = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(submitButton.onPressed, isNull);
      });

      testWidgets('should require category for income transactions', (
        tester,
      ) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Switch to income
        await tester.tap(find.text('Income'));
        await tester.pumpAndSettle();

        // Fill amount and account but not category
        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(amountField, '25.50');
        await tester.pumpAndSettle();

        // Select account
        final accountDropdown = find.descendant(
          of: find.byType(AccountSelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(accountDropdown);
        await tester.pumpAndSettle();
        await tester.tap(find.text(r'Checking Account - $1,000.00').last);
        await tester.pumpAndSettle();

        // Submit button should remain disabled without category
        final submitButton = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(submitButton.onPressed, isNull);
      });

      testWidgets('should not require category for transfer transactions', (
        tester,
      ) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Switch to transfer
        await tester.tap(find.text('Transfer'));
        await tester.pumpAndSettle();

        // Fill amount
        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(amountField, '25.50');
        await tester.pumpAndSettle();

        // Select different accounts
        final accountDropdowns = find.descendant(
          of: find.byType(AccountSelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );

        // Select from account
        await tester.tap(accountDropdowns.first);
        await tester.pumpAndSettle();
        await tester.tap(find.text(r'Checking Account - $1,000.00').last);
        await tester.pumpAndSettle();

        // Select to account
        await tester.tap(accountDropdowns.last);
        await tester.pumpAndSettle();
        await tester.tap(find.text(r'Savings Account - $500.00').last);
        await tester.pumpAndSettle();

        // Select date
        final datePickerInkWell = find.descendant(
          of: find.byType(DatePickerField),
          matching: find.byType(InkWell),
        );
        await tester.tap(datePickerInkWell);
        await tester.pumpAndSettle();
        await tester.tap(find.text('OK'));
        await tester.pumpAndSettle();

        // Submit button should be enabled without category
        final submitButton = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(submitButton.onPressed, isNotNull);
      });

      testWidgets('should filter categories by transaction type', (
        tester,
      ) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // For expense, should show expense categories
        final categoryDropdownExpense = find.descendant(
          of: find.byType(CategorySelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(categoryDropdownExpense);
        await tester.pumpAndSettle();

        expect(find.text('Food & Dining'), findsAtLeast(1));
        // Close dropdown
        await tester.tap(find.text('Food & Dining').last);
        await tester.pumpAndSettle();

        // Switch to income
        await tester.tap(find.text('Income'));
        await tester.pumpAndSettle();

        // For income, should show income categories
        final categoryDropdownIncome = find.descendant(
          of: find.byType(CategorySelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(categoryDropdownIncome);
        await tester.pumpAndSettle();

        expect(find.text('Salary'), findsAtLeast(1));
      });
    });

    group('Date Validation', () {
      testWidgets(
        'should enable submit button with valid date (defaults to today)',
        (tester) async {
          await tester.pumpWidget(createTransactionFormWidget());
          await tester.pumpAndSettle();

          // Fill all required fields (date defaults to today)
          final amountField = find.descendant(
            of: find.byType(AmountInputField),
            matching: find.byType(TextFormField),
          );
          await tester.enterText(amountField, '25.50');
          await tester.pumpAndSettle();

          // Select account
          final accountDropdown = find.descendant(
            of: find.byType(AccountSelector),
            matching: find.byType(DropdownButtonFormField<String>),
          );
          await tester.tap(accountDropdown);
          await tester.pumpAndSettle();
          await tester.tap(find.text(r'Checking Account - $1,000.00').last);
          await tester.pumpAndSettle();

          // Select category
          final categoryDropdown = find.descendant(
            of: find.byType(CategorySelector),
            matching: find.byType(DropdownButtonFormField<String>),
          );
          await tester.tap(categoryDropdown);
          await tester.pumpAndSettle();
          await tester.tap(find.text('Food & Dining').last);
          await tester.pumpAndSettle();

          // Submit button should be enabled with all required fields filled
          final submitButton = tester.widget<ElevatedButton>(
            find.byType(ElevatedButton),
          );
          expect(submitButton.onPressed, isNotNull);
        },
      );

      testWidgets('should accept valid date selection', (tester) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Fill all required fields including date
        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(amountField, '25.50');
        await tester.pumpAndSettle();

        // Select account
        final accountDropdown = find.descendant(
          of: find.byType(AccountSelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(accountDropdown);
        await tester.pumpAndSettle();
        await tester.tap(find.text(r'Checking Account - $1,000.00').last);
        await tester.pumpAndSettle();

        // Select category
        final categoryDropdown = find.descendant(
          of: find.byType(CategorySelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(categoryDropdown);
        await tester.pumpAndSettle();
        await tester.tap(find.text('Food & Dining').last);
        await tester.pumpAndSettle();

        // Select date
        final datePickerInkWell = find.descendant(
          of: find.byType(DatePickerField),
          matching: find.byType(InkWell),
        );
        await tester.tap(datePickerInkWell);
        await tester.pumpAndSettle();
        await tester.tap(find.text('OK'));
        await tester.pumpAndSettle();

        // Submit button should now be enabled
        final submitButton = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(submitButton.onPressed, isNotNull);
      });
    });

    group('Description and Notes Validation', () {
      testWidgets('should accept empty description', (tester) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Fill required fields but leave description empty
        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(amountField, '25.50');
        await tester.pumpAndSettle();

        // Select account
        final accountDropdown = find.descendant(
          of: find.byType(AccountSelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(accountDropdown);
        await tester.pumpAndSettle();
        await tester.tap(find.text(r'Checking Account - $1,000.00').last);
        await tester.pumpAndSettle();

        // Select category
        final categoryDropdown = find.descendant(
          of: find.byType(CategorySelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(categoryDropdown);
        await tester.pumpAndSettle();
        await tester.tap(find.text('Food & Dining').last);
        await tester.pumpAndSettle();

        // Select date
        final datePickerInkWell = find.descendant(
          of: find.byType(DatePickerField),
          matching: find.byType(InkWell),
        );
        await tester.tap(datePickerInkWell);
        await tester.pumpAndSettle();
        await tester.tap(find.text('OK'));
        await tester.pumpAndSettle();

        // Submit button should be enabled even with empty description
        final submitButton = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(submitButton.onPressed, isNotNull);
      });

      testWidgets('should accept empty notes', (tester) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Fill required fields but leave notes empty
        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(amountField, '25.50');
        await tester.pumpAndSettle();

        // Select account
        final accountDropdown = find.descendant(
          of: find.byType(AccountSelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(accountDropdown);
        await tester.pumpAndSettle();
        await tester.tap(find.text(r'Checking Account - $1,000.00').last);
        await tester.pumpAndSettle();

        // Select category
        final categoryDropdown = find.descendant(
          of: find.byType(CategorySelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(categoryDropdown);
        await tester.pumpAndSettle();
        await tester.tap(find.text('Food & Dining').last);
        await tester.pumpAndSettle();

        // Select date
        final datePickerInkWell = find.descendant(
          of: find.byType(DatePickerField),
          matching: find.byType(InkWell),
        );
        await tester.tap(datePickerInkWell);
        await tester.pumpAndSettle();
        await tester.tap(find.text('OK'));
        await tester.pumpAndSettle();

        // Submit button should be enabled even with empty notes
        final submitButton = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(submitButton.onPressed, isNotNull);
      });

      testWidgets('should accept description and notes input', (tester) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Fill description
        final descriptionField = find.descendant(
          of: find.byType(DescriptionField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(descriptionField, 'Lunch at restaurant');
        await tester.pumpAndSettle();

        // Fill notes
        final notesField = find.descendant(
          of: find.byType(NotesField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(notesField, 'Business lunch meeting');
        await tester.pumpAndSettle();

        // Verify input was accepted
        final descriptionController = tester
            .widget<TextFormField>(descriptionField)
            .controller;
        final notesController = tester
            .widget<TextFormField>(notesField)
            .controller;

        expect(descriptionController?.text, equals('Lunch at restaurant'));
        expect(notesController?.text, equals('Business lunch meeting'));
      });

      testWidgets('should handle long description text', (tester) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        final longDescription = 'A' * 100; // Max allowed description length

        final descriptionField = find.descendant(
          of: find.byType(DescriptionField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(descriptionField, longDescription);
        await tester.pumpAndSettle();

        // Should accept text up to max length
        final controller = tester
            .widget<TextFormField>(descriptionField)
            .controller;
        expect(controller?.text, equals(longDescription));
      });

      testWidgets('should handle long notes text', (tester) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        final longNotes = 'Note ' * 100; // Very long notes

        final notesField = find.descendant(
          of: find.byType(NotesField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(notesField, longNotes);
        await tester.pumpAndSettle();

        // Should accept long text (no hard limit in form)
        final controller = tester.widget<TextFormField>(notesField).controller;
        expect(controller?.text, equals(longNotes));
      });
    });

    group('Form Submission Validation', () {
      testWidgets('should prevent submission with incomplete form', (
        tester,
      ) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Try to submit incomplete form - ensure button is visible first
        await tester.ensureVisible(find.byType(ElevatedButton));
        await tester.tap(find.byType(ElevatedButton), warnIfMissed: false);
        await tester.pumpAndSettle();

        // Button should remain disabled
        final submitButton = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(submitButton.onPressed, isNull);
      });

      testWidgets('should enable submit button with complete valid form', (
        tester,
      ) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Initially submit button should be disabled
        final submitButton = find.byType(ElevatedButton);
        expect(tester.widget<ElevatedButton>(submitButton).onPressed, isNull);

        // Fill all required fields
        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(amountField, '25.50');
        await tester.pumpAndSettle();

        // Select account
        final accountDropdown = find.descendant(
          of: find.byType(AccountSelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(accountDropdown);
        await tester.pumpAndSettle();
        await tester.tap(find.text(r'Checking Account - $1,000.00').last);
        await tester.pumpAndSettle();

        // Select category
        final categoryDropdown = find.descendant(
          of: find.byType(CategorySelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(categoryDropdown);
        await tester.pumpAndSettle();
        await tester.tap(find.text('Food & Dining').last);
        await tester.pumpAndSettle();

        // Select date
        final datePickerInkWell = find.descendant(
          of: find.byType(DatePickerField),
          matching: find.byType(InkWell),
        );
        await tester.tap(datePickerInkWell);
        await tester.pumpAndSettle();
        await tester.tap(find.text('OK'));
        await tester.pumpAndSettle();

        // Now submit button should be enabled
        expect(
          tester.widget<ElevatedButton>(submitButton).onPressed,
          isNotNull,
        );
      });

      testWidgets('should accept valid decimal amount input', (tester) async {
        await tester.pumpWidget(createTransactionFormWidget());
        await tester.pumpAndSettle();

        // Fill amount with decimal
        final amountField = find.descendant(
          of: find.byType(AmountInputField),
          matching: find.byType(TextFormField),
        );
        await tester.enterText(amountField, '123.45');
        await tester.pumpAndSettle();

        // Verify the amount field accepts the decimal input
        expect(
          tester.widget<TextFormField>(amountField).controller?.text,
          equals('123.45'),
        );

        // Fill other required fields to enable submit button
        final accountDropdown = find.descendant(
          of: find.byType(AccountSelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(accountDropdown);
        await tester.pumpAndSettle();
        await tester.tap(find.text(r'Checking Account - $1,000.00').last);
        await tester.pumpAndSettle();

        final categoryDropdown = find.descendant(
          of: find.byType(CategorySelector),
          matching: find.byType(DropdownButtonFormField<String>),
        );
        await tester.tap(categoryDropdown);
        await tester.pumpAndSettle();
        await tester.tap(find.text('Food & Dining').last);
        await tester.pumpAndSettle();

        // Verify submit button becomes enabled with valid decimal amount
        final submitButton = find.byType(ElevatedButton);
        expect(
          tester.widget<ElevatedButton>(submitButton).onPressed,
          isNotNull,
        );
      });
    });

    group('Error Display Tests', () {
      testWidgets('should display amount error when provided', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AmountInputField(
              controller: TextEditingController(),
              errorText: 'Amount is required',
            ),
            overrides: [
              currencyPreferencesServiceProvider.overrideWithValue(
                mockCurrencyService,
              ),
            ],
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Amount is required'), findsOneWidget);
      });

      testWidgets('should display date error when provided', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DatePickerField(
              selectedDate: null,
              onChanged: (_) {},
              errorText: 'Date is required',
            ),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Date is required'), findsOneWidget);
      });

      testWidgets('should handle multiple validation errors', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Column(
              children: [
                AmountInputField(
                  controller: TextEditingController(),
                  errorText: 'Amount is required',
                ),
                const SizedBox(height: 16),
                DatePickerField(
                  selectedDate: null,
                  onChanged: (_) {},
                  errorText: 'Date is required',
                ),
              ],
            ),
            overrides: [
              currencyPreferencesServiceProvider.overrideWithValue(
                mockCurrencyService,
              ),
            ],
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Amount is required'), findsOneWidget);
        expect(find.text('Date is required'), findsOneWidget);
      });
    });
  });
}
