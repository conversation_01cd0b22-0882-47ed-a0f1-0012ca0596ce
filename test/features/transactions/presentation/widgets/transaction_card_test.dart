import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/features/transactions/presentation/widgets/transaction_card.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/services/currency_preferences_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/mock_data_factory.dart';

// Global test variables accessible to all groups
late Transaction testTransaction;
late Category testCategory;
late Account testFromAccount;
late Account testToAccount;
late MockCurrencyPreferencesService mockCurrencyService;

void main() {
  setUp(() {
    testTransaction = MockDataFactory.createTransaction(
      id: 'test-transaction-1',
      userId: 'test-user-1',
      type: TransactionType.expense,
      status: TransactionStatus.completed,
      amountCents: 5000, // $50.00
      transactionDate: DateTime(2024, 1, 15),
      description: 'Test Expense',
      fromAccountId: 'account-1',
      categoryId: 'category-1',
    );

    testCategory = MockDataFactory.createCategory(
      id: 'category-1',
      name: 'Food & Dining',
      type: CategoryType.expense,
      color: '#FF5722',
      icon: 'restaurant',
    );

    testFromAccount = MockDataFactory.createAccount(
      id: 'account-1',
      name: 'Checking Account',
      type: AccountType.checking,
      initialBalanceCents: 100000,
    );

    testToAccount = MockDataFactory.createAccount(
      id: 'account-2',
      name: 'Savings Account',
      type: AccountType.savings,
      initialBalanceCents: 50000,
    );

    mockCurrencyService = MockCurrencyPreferencesService();
    when(() => mockCurrencyService.getCurrentCurrency()).thenReturn('USD');
    when(() => mockCurrencyService.getCurrentCurrencySymbol()).thenReturn(r'$');
  });

  group('TransactionCard Comprehensive Tests', () {
    Widget createTestWidget({
      bool showActions = false,
      VoidCallback? onEdit,
      VoidCallback? onDelete,
      VoidCallback? onTap,
    }) {
      return ProviderScope(
        child: MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('en')],
          home: Scaffold(
            body: TransactionCard(
              transaction: testTransaction,
              showActions: showActions,
              onEdit: onEdit,
              onDelete: onDelete,
              onTap: onTap,
            ),
          ),
        ),
      );
    }

    testWidgets('does not show action menu by default', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should not find the popup menu button
      expect(find.byType(PopupMenuButton<String>), findsNothing);
    });

    testWidgets('does not show action menu when showActions is false', (
      tester,
    ) async {
      await tester.pumpWidget(createTestWidget(showActions: false));
      await tester.pumpAndSettle();

      // Should not find the popup menu button
      expect(find.byType(PopupMenuButton<String>), findsNothing);
    });

    testWidgets('shows action menu when showActions is true', (tester) async {
      await tester.pumpWidget(createTestWidget(showActions: true));
      await tester.pumpAndSettle();

      // Should find the popup menu button
      expect(find.byType(PopupMenuButton<String>), findsOneWidget);
    });

    testWidgets('calls onEdit when edit menu item is tapped', (tester) async {
      var editCalled = false;

      await tester.pumpWidget(
        createTestWidget(showActions: true, onEdit: () => editCalled = true),
      );
      await tester.pumpAndSettle();

      // Tap the popup menu button
      await tester.tap(find.byType(PopupMenuButton<String>));
      await tester.pumpAndSettle();

      // Tap the edit menu item
      await tester.tap(find.text('Edit'));
      await tester.pumpAndSettle();

      expect(editCalled, isTrue);
    });

    testWidgets('calls onDelete when delete menu item is tapped', (
      tester,
    ) async {
      var deleteCalled = false;

      await tester.pumpWidget(
        createTestWidget(
          showActions: true,
          onDelete: () => deleteCalled = true,
        ),
      );
      await tester.pumpAndSettle();

      // Tap the popup menu button
      await tester.tap(find.byType(PopupMenuButton<String>));
      await tester.pumpAndSettle();

      // Tap the delete menu item
      await tester.tap(find.text('Delete'));
      await tester.pumpAndSettle();

      expect(deleteCalled, isTrue);
    });

    testWidgets('still calls onTap when card is tapped and actions are shown', (
      tester,
    ) async {
      var tapCalled = false;

      await tester.pumpWidget(
        createTestWidget(showActions: true, onTap: () => tapCalled = true),
      );
      await tester.pumpAndSettle();

      // Tap the card content area (not the menu button)
      await tester.tap(find.text('Test Expense'));
      await tester.pumpAndSettle();

      expect(tapCalled, isTrue);
    });
  });

  group('TransactionCard Visual Elements', () {
    Widget createRouterApp({
      required Transaction transaction,
      bool showActions = false,
      VoidCallback? onEdit,
      VoidCallback? onDelete,
      VoidCallback? onTap,
    }) {
      // Create a router with the routes that TransactionCard navigates to
      final router = GoRouter(
        initialLocation: '/test',
        routes: [
          GoRoute(
            path: '/test',
            builder: (context, state) => Scaffold(
              body: TransactionCard(
                transaction: transaction,
                showActions: showActions,
                onEdit: onEdit,
                onDelete: onDelete,
                onTap: onTap,
              ),
            ),
          ),
          // Routes that TransactionCard navigates to
          GoRoute(
            path: '/transactions/category/:categoryId',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('Category Transactions')),
            ),
          ),
          GoRoute(
            path: '/transactions/account/:accountId',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('Account Transactions')),
            ),
          ),
        ],
      );

      return MaterialApp.router(
        routerConfig: router,
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [Locale('en')],
      );
    }

    Widget createTestWidgetWithProviders({
      Transaction? transaction,
      bool showActions = false,
      VoidCallback? onEdit,
      VoidCallback? onDelete,
      VoidCallback? onTap,
    }) {
      return ProviderScope(
        overrides: [
          categoryProvider(
            transaction?.categoryId ?? 'category-1',
          ).overrideWith((ref) => Stream.value(testCategory)),
          accountProvider(
            transaction?.fromAccountId ?? 'account-1',
          ).overrideWith((ref) => Stream.value(testFromAccount)),
          accountProvider(
            transaction?.toAccountId ?? 'account-2',
          ).overrideWith((ref) => Stream.value(testToAccount)),
          currencyPreferencesServiceProvider.overrideWithValue(
            mockCurrencyService,
          ),
        ],
        child: createRouterApp(
          transaction: transaction ?? testTransaction,
          showActions: showActions,
          onEdit: onEdit,
          onDelete: onDelete,
          onTap: onTap,
        ),
      );
    }

    testWidgets('should render basic transaction card structure', (
      tester,
    ) async {
      await tester.pumpWidget(createTestWidgetWithProviders());
      await tester.pumpAndSettle();

      // Verify basic structure
      expect(find.byType(Card), findsOneWidget);
      expect(find.byType(InkWell), findsOneWidget);
      expect(find.byType(Row), findsAtLeast(1));
      expect(find.byType(Column), findsAtLeast(1));
    });

    testWidgets('should show transaction title correctly', (tester) async {
      final transactionWithDescription = testTransaction.copyWith(
        description: 'Custom Description',
      );

      await tester.pumpWidget(
        createTestWidgetWithProviders(transaction: transactionWithDescription),
      );
      await tester.pumpAndSettle();

      expect(find.text('Custom Description'), findsOneWidget);
    });

    testWidgets('should show category name when no description', (
      tester,
    ) async {
      final transactionWithoutDescription = testTransaction.copyWith(
        description: null,
      );

      await tester.pumpWidget(
        createTestWidgetWithProviders(
          transaction: transactionWithoutDescription,
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text('Food & Dining'), findsOneWidget);
    });

    testWidgets('should show amount with correct formatting for expense', (
      tester,
    ) async {
      await tester.pumpWidget(createTestWidgetWithProviders());
      await tester.pumpAndSettle();

      // Expense should show negative amount
      expect(find.textContaining('-'), findsAtLeast(1));
      expect(find.textContaining('50'), findsAtLeast(1));
    });

    testWidgets('should show amount with correct formatting for income', (
      tester,
    ) async {
      final incomeTransaction = testTransaction.copyWith(
        type: TransactionType.income,
        toAccountId: 'account-1',
        fromAccountId: null,
      );

      await tester.pumpWidget(
        createTestWidgetWithProviders(transaction: incomeTransaction),
      );
      await tester.pumpAndSettle();

      // Income should show positive amount with plus sign
      expect(find.textContaining('+'), findsAtLeast(1));
      expect(find.textContaining('50'), findsAtLeast(1));
    });

    testWidgets('should show amount without sign for transfer', (tester) async {
      final transferTransaction = testTransaction.copyWith(
        type: TransactionType.transfer,
        toAccountId: 'account-2',
        fromAccountId: 'account-1',
        categoryId: null,
      );

      await tester.pumpWidget(
        createTestWidgetWithProviders(transaction: transferTransaction),
      );
      await tester.pumpAndSettle();

      // Transfer should show amount without sign
      expect(find.textContaining('50'), findsAtLeast(1));
    });

    testWidgets(
      'should display correct icons for different transaction types',
      (tester) async {
        // Test expense icon
        await tester.pumpWidget(createTestWidgetWithProviders());
        await tester.pumpAndSettle();
        expect(find.byIcon(Icons.restaurant), findsOneWidget);

        // Test income icon (without category to show default type icon)
        final incomeTransaction = testTransaction.copyWith(
          type: TransactionType.income,
          toAccountId: 'account-1',
          fromAccountId: null,
          categoryId: null, // Remove category to show default type icon
        );
        await tester.pumpWidget(
          createTestWidgetWithProviders(transaction: incomeTransaction),
        );
        await tester.pumpAndSettle();
        expect(find.byIcon(Icons.trending_up), findsOneWidget);

        // Test transfer icon
        final transferTransaction = testTransaction.copyWith(
          type: TransactionType.transfer,
          toAccountId: 'account-2',
          fromAccountId: 'account-1',
          categoryId: null,
        );
        await tester.pumpWidget(
          createTestWidgetWithProviders(transaction: transferTransaction),
        );
        await tester.pumpAndSettle();
        expect(find.byIcon(Icons.swap_horiz), findsOneWidget);
      },
    );

    testWidgets('should show notes section when notes are present', (
      tester,
    ) async {
      final transactionWithNotes = testTransaction.copyWith(
        notes: 'This is a test note',
      );

      await tester.pumpWidget(
        createTestWidgetWithProviders(transaction: transactionWithNotes),
      );
      await tester.pumpAndSettle();

      expect(find.text('This is a test note'), findsOneWidget);
      expect(find.byIcon(Icons.note_outlined), findsOneWidget);
    });

    testWidgets('should hide notes section when notes are empty', (
      tester,
    ) async {
      final transactionWithoutNotes = testTransaction.copyWith(notes: null);

      await tester.pumpWidget(
        createTestWidgetWithProviders(transaction: transactionWithoutNotes),
      );
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.note_outlined), findsNothing);
    });

    testWidgets('should format date correctly', (tester) async {
      await tester.pumpWidget(createTestWidgetWithProviders());
      await tester.pumpAndSettle();

      // Check that date is formatted (specific format may vary)
      expect(find.textContaining('Jan'), findsOneWidget);
    });

    testWidgets('should show "Today" for today\'s transactions', (
      tester,
    ) async {
      final todayTransaction = testTransaction.copyWith(
        transactionDate: DateTime.now(),
      );

      await tester.pumpWidget(
        createTestWidgetWithProviders(transaction: todayTransaction),
      );
      await tester.pumpAndSettle();

      expect(find.text('Today'), findsOneWidget);
    });

    testWidgets('should show "Yesterday" for yesterday\'s transactions', (
      tester,
    ) async {
      final yesterdayTransaction = testTransaction.copyWith(
        transactionDate: DateTime.now().subtract(const Duration(days: 1)),
      );

      await tester.pumpWidget(
        createTestWidgetWithProviders(transaction: yesterdayTransaction),
      );
      await tester.pumpAndSettle();

      expect(find.text('Yesterday'), findsOneWidget);
    });
  });

  group('TransactionCard Interaction Tests', () {
    Widget createRouterApp({
      required Transaction transaction,
      bool showActions = false,
      VoidCallback? onEdit,
      VoidCallback? onDelete,
      VoidCallback? onTap,
    }) {
      // Create a router with the routes that TransactionCard navigates to
      final router = GoRouter(
        initialLocation: '/test',
        routes: [
          GoRoute(
            path: '/test',
            builder: (context, state) => Scaffold(
              body: TransactionCard(
                transaction: transaction,
                showActions: showActions,
                onEdit: onEdit,
                onDelete: onDelete,
                onTap: onTap,
              ),
            ),
          ),
          // Routes that TransactionCard navigates to
          GoRoute(
            path: '/transactions/category/:categoryId',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('Category Transactions')),
            ),
          ),
          GoRoute(
            path: '/transactions/account/:accountId',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('Account Transactions')),
            ),
          ),
        ],
      );

      return MaterialApp.router(
        routerConfig: router,
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [Locale('en')],
      );
    }

    Widget createTestWidgetWithProviders({
      Transaction? transaction,
      bool showActions = false,
      VoidCallback? onEdit,
      VoidCallback? onDelete,
      VoidCallback? onTap,
    }) {
      return ProviderScope(
        overrides: [
          categoryProvider(
            transaction?.categoryId ?? 'category-1',
          ).overrideWith((ref) => Stream.value(testCategory)),
          accountProvider(
            transaction?.fromAccountId ?? 'account-1',
          ).overrideWith((ref) => Stream.value(testFromAccount)),
          accountProvider(
            transaction?.toAccountId ?? 'account-2',
          ).overrideWith((ref) => Stream.value(testToAccount)),
          currencyPreferencesServiceProvider.overrideWithValue(
            mockCurrencyService,
          ),
        ],
        child: createRouterApp(
          transaction: transaction ?? testTransaction,
          showActions: showActions,
          onEdit: onEdit,
          onDelete: onDelete,
          onTap: onTap,
        ),
      );
    }

    testWidgets('should handle card tap correctly', (tester) async {
      var tapCalled = false;

      await tester.pumpWidget(
        createTestWidgetWithProviders(onTap: () => tapCalled = true),
      );
      await tester.pumpAndSettle();

      await tester.tap(find.byType(InkWell));
      await tester.pumpAndSettle();

      expect(tapCalled, isTrue);
    });

    testWidgets('should handle category icon tap for navigation', (
      tester,
    ) async {
      await tester.pumpWidget(createTestWidgetWithProviders());
      await tester.pumpAndSettle();

      final categoryIcon = find.byIcon(Icons.restaurant);
      expect(categoryIcon, findsOneWidget);

      // Tap the category icon
      await tester.tap(categoryIcon);
      await tester.pumpAndSettle();

      // Should not throw an error
    });

    testWidgets('should handle transfer icon tap for navigation', (
      tester,
    ) async {
      final transferTransaction = testTransaction.copyWith(
        type: TransactionType.transfer,
        toAccountId: 'account-2',
        fromAccountId: 'account-1',
        categoryId: null,
      );

      await tester.pumpWidget(
        createTestWidgetWithProviders(transaction: transferTransaction),
      );
      await tester.pumpAndSettle();

      final transferIcon = find.byIcon(Icons.swap_horiz);
      expect(transferIcon, findsOneWidget);

      // Tap the transfer icon
      await tester.tap(transferIcon);
      await tester.pumpAndSettle();

      // Should not throw an error
    });
  });

  group('TransactionCard Error Handling', () {
    Widget createTestWidgetWithErrors({
      Transaction? transaction,
      bool categoryError = false,
      bool accountError = false,
    }) {
      return ProviderScope(
        overrides: [
          if (categoryError)
            categoryProvider(
              transaction?.categoryId ?? 'category-1',
            ).overrideWith(
              (ref) => Stream.error(Exception('Category load error')),
            )
          else
            categoryProvider(
              transaction?.categoryId ?? 'category-1',
            ).overrideWith((ref) => Stream.value(testCategory)),
          if (accountError)
            accountProvider(
              transaction?.fromAccountId ?? 'account-1',
            ).overrideWith(
              (ref) => Stream.error(Exception('Account load error')),
            )
          else
            accountProvider(
              transaction?.fromAccountId ?? 'account-1',
            ).overrideWith((ref) => Stream.value(testFromAccount)),
          currencyPreferencesServiceProvider.overrideWithValue(
            mockCurrencyService,
          ),
        ],
        child: MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('en')],
          home: Scaffold(
            body: TransactionCard(
              transaction: transaction ?? testTransaction,
              showActions: false,
            ),
          ),
        ),
      );
    }

    testWidgets('should handle category loading error gracefully', (
      tester,
    ) async {
      await tester.pumpWidget(createTestWidgetWithErrors(categoryError: true));
      await tester.pumpAndSettle();

      // Should still render the card
      expect(find.byType(TransactionCard), findsOneWidget);
      expect(find.byType(Card), findsOneWidget);
    });

    testWidgets('should handle account loading error gracefully', (
      tester,
    ) async {
      await tester.pumpWidget(createTestWidgetWithErrors(accountError: true));
      await tester.pumpAndSettle();

      // Should still render the card
      expect(find.byType(TransactionCard), findsOneWidget);
      expect(find.byType(Card), findsOneWidget);
    });

    testWidgets('should handle null category gracefully', (tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            categoryProvider(
              testTransaction.categoryId!,
            ).overrideWith((ref) => Stream.value(null)),
            currencyPreferencesServiceProvider.overrideWithValue(
              mockCurrencyService,
            ),
          ],
          child: MaterialApp(
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [Locale('en')],
            home: Scaffold(
              body: TransactionCard(
                transaction: testTransaction,
                showActions: false,
              ),
            ),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Verify the widget handles null category gracefully
      expect(find.byType(TransactionCard), findsOneWidget);
    });

    testWidgets('should handle missing category ID gracefully', (tester) async {
      final transactionWithoutCategory = testTransaction.copyWith(
        categoryId: null,
      );

      await tester.pumpWidget(
        createTestWidgetWithErrors(transaction: transactionWithoutCategory),
      );
      await tester.pumpAndSettle();

      // Should still render the card with default icon
      expect(find.byType(TransactionCard), findsOneWidget);
      expect(find.byType(Card), findsOneWidget);
    });
  });

  group('TransactionCard Theme and Styling', () {
    Widget createTestWidgetWithTheme({
      ThemeData? theme,
      Transaction? transaction,
    }) {
      return ProviderScope(
        overrides: [
          categoryProvider(
            transaction?.categoryId ?? 'category-1',
          ).overrideWith((ref) => Stream.value(testCategory)),
          accountProvider(
            transaction?.fromAccountId ?? 'account-1',
          ).overrideWith((ref) => Stream.value(testFromAccount)),
          currencyPreferencesServiceProvider.overrideWithValue(
            mockCurrencyService,
          ),
        ],
        child: MaterialApp(
          theme: theme ?? ThemeData.light(),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('en')],
          home: Scaffold(
            body: TransactionCard(
              transaction: transaction ?? testTransaction,
              showActions: false,
            ),
          ),
        ),
      );
    }

    testWidgets('should apply correct theme colors', (tester) async {
      await tester.pumpWidget(createTestWidgetWithTheme());
      await tester.pumpAndSettle();

      // Verify card is rendered with theme colors
      final card = tester.widget<Card>(find.byType(Card));
      expect(card.elevation, isNotNull);
    });

    testWidgets('should work with dark theme', (tester) async {
      await tester.pumpWidget(
        createTestWidgetWithTheme(theme: ThemeData.dark()),
      );
      await tester.pumpAndSettle();

      // Should render without errors in dark theme
      expect(find.byType(TransactionCard), findsOneWidget);
      expect(find.byType(Card), findsOneWidget);
    });

    testWidgets('should show correct colors for different transaction types', (
      tester,
    ) async {
      // Test income color
      final incomeTransaction = testTransaction.copyWith(
        type: TransactionType.income,
        toAccountId: 'account-1',
        fromAccountId: null,
      );

      await tester.pumpWidget(
        createTestWidgetWithTheme(transaction: incomeTransaction),
      );
      await tester.pumpAndSettle();

      expect(find.byType(TransactionCard), findsOneWidget);

      // Test expense color (default transaction)
      await tester.pumpWidget(createTestWidgetWithTheme());
      await tester.pumpAndSettle();

      expect(find.byType(TransactionCard), findsOneWidget);
    });
  });

  group('TransactionCard Loading States', () {
    Widget createTestWidgetWithLoading({
      bool categoryLoading = false,
      bool accountLoading = false,
    }) {
      return ProviderScope(
        overrides: [
          if (categoryLoading)
            categoryProvider(
              testTransaction.categoryId!,
            ).overrideWith((ref) => const Stream.empty())
          else
            categoryProvider(
              testTransaction.categoryId!,
            ).overrideWith((ref) => Stream.value(testCategory)),
          if (accountLoading)
            accountProvider(
              testTransaction.fromAccountId!,
            ).overrideWith((ref) => const Stream.empty())
          else
            accountProvider(
              testTransaction.fromAccountId!,
            ).overrideWith((ref) => Stream.value(testFromAccount)),
          currencyPreferencesServiceProvider.overrideWithValue(
            mockCurrencyService,
          ),
        ],
        child: MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('en')],
          home: Scaffold(
            body: TransactionCard(
              transaction: testTransaction,
              showActions: false,
            ),
          ),
        ),
      );
    }

    testWidgets('should handle category loading state', (tester) async {
      await tester.pumpWidget(
        createTestWidgetWithLoading(categoryLoading: true),
      );
      await tester.pump(); // Don't wait for loading to complete

      // Should show loading state or default icon
      expect(find.byType(TransactionCard), findsOneWidget);
    });

    testWidgets('should handle account loading state', (tester) async {
      await tester.pumpWidget(
        createTestWidgetWithLoading(accountLoading: true),
      );
      await tester.pump(); // Don't wait for loading to complete

      // Should render card even during loading
      expect(find.byType(TransactionCard), findsOneWidget);
    });
  });
}

// Mock classes
class MockCurrencyPreferencesService extends Mock
    implements CurrencyPreferencesService {}
