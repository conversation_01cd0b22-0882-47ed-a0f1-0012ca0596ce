import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/features/transactions/presentation/providers/transaction_form_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../helpers/test_wrapper.dart';

void main() {
  group('Transfer Transaction UI Components', () {
    late List<Account> mockAccounts;

    setUp(() {
      mockAccounts = [
        Account(
          id: 'account1',
          userId: 'test-user',
          name: 'Checking Account',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 100000,
          currentBalanceCents: 100000, // $1000

          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Account(
          id: 'account2',
          userId: 'test-user',
          name: 'Savings Account',
          type: AccountType.savings,
          classification: AccountClassification.asset,
          initialBalanceCents: 50000,
          currentBalanceCents: 50000, // $500

          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];
    });

    test(
      'shows two account selectors for transfer transactions (logic test)',
      () {
        final container = ProviderContainer();
        addTearDown(container.dispose);

        // Set transaction type to transfer
        container
            .read(transactionFormProvider.notifier)
            .setTransactionType(TransactionType.transfer);

        final state = container.read(transactionFormProvider);

        // Verify that the state is set up for transfer transactions
        expect(state.transactionType, TransactionType.transfer);

        // The UI logic would show two account selectors when transactionType is transfer
        // This is tested by the conditional rendering in TransactionForm:
        // if (formState.transactionType == TransactionType.transfer) ...
        expect(state.transactionType == TransactionType.transfer, true);
      },
    );

    testWidgets(
      'validates that source and destination accounts are different',
      (tester) async {
        late BuildContext context;

        final widget = TestWrapper.createTestWidget(
          Builder(
            builder: (BuildContext ctx) {
              context = ctx;
              return const SizedBox.shrink();
            },
          ),
        );

        await tester.pumpWidget(widget);

        final container = ProviderContainer(
          overrides: [
            accountListProvider.overrideWith(
              (ref) => Stream.value(mockAccounts),
            ),
          ],
        );
        addTearDown(container.dispose);

        // Set up a transfer transaction
        final formNotifier = container.read(transactionFormProvider.notifier)
          ..setTransactionType(TransactionType.transfer)
          ..setAccountId('account1')
          ..setToAccountId('account1') // Same account
          ..setAmount(5000) // $50
          ..setDate(DateTime.now());

        // Validate the form
        final isValid = formNotifier.validateForm(context);
        final state = container.read(transactionFormProvider);

        expect(isValid, false);
        expect(
          state.toAccountError,
          'Source and destination accounts must be different',
        );

        // Ensure all async operations complete
        await tester.pumpAndSettle();
      },
    );

    testWidgets('allows valid transfer with different accounts', (
      tester,
    ) async {
      late BuildContext context;

      final widget = TestWrapper.createTestWidget(
        Builder(
          builder: (BuildContext ctx) {
            context = ctx;
            return const SizedBox.shrink();
          },
        ),
      );

      await tester.pumpWidget(widget);

      final container = ProviderContainer(
        overrides: [
          accountListProvider.overrideWith((ref) => Stream.value(mockAccounts)),
        ],
      );
      addTearDown(container.dispose);

      // Set up a valid transfer transaction
      final formNotifier = container.read(transactionFormProvider.notifier)
        ..setTransactionType(TransactionType.transfer)
        ..setAccountId('account1') // Source
        ..setToAccountId('account2') // Destination
        ..setAmount(5000) // $50
        ..setDate(DateTime.now());

      // Validate the form
      final isValid = formNotifier.validateForm(context);
      final state = container.read(transactionFormProvider);

      expect(isValid, true);
      expect(state.toAccountError, null);

      // Ensure all async operations complete
      await tester.pumpAndSettle();
    });

    test('buildTransaction creates correct transfer transaction', () {
      final container = ProviderContainer();
      // Set up a transfer transaction
      final formNotifier = container.read(transactionFormProvider.notifier)
        ..setTransactionType(TransactionType.transfer)
        ..setAccountId('account1') // Source
        ..setToAccountId('account2') // Destination
        ..setAmount(5000) // $50
        ..setDate(DateTime.now());

      // Build the transaction
      final transaction = formNotifier.buildTransaction(
        userId: 'test-user',
        existingId: 'test-transaction-id',
      );

      expect(transaction.type, TransactionType.transfer);
      expect(transaction.fromAccountId, 'account1');
      expect(transaction.toAccountId, 'account2');
      expect(transaction.amountCents, 5000);
      expect(transaction.categoryId, null); // Transfers don't have categories

      container.dispose();
    });

    test('initializeWithTransaction handles transfer correctly', () {
      final container = ProviderContainer();
      final formNotifier = container.read(transactionFormProvider.notifier);

      final transferTransaction = Transaction(
        id: 'test-id',
        userId: 'test-user',
        type: TransactionType.transfer,
        status: TransactionStatus.completed,
        amountCents: 7500,

        fromAccountId: 'account1',
        toAccountId: 'account2',
        transactionDate: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Initialize with transfer transaction
      formNotifier.initializeWithTransaction(transferTransaction);
      final state = container.read(transactionFormProvider);

      expect(state.transactionType, TransactionType.transfer);
      expect(state.accountId, 'account1'); // Source account
      expect(state.toAccountId, 'account2'); // Destination account
      expect(state.amount, 7500);

      container.dispose();
    });

    testWidgets('clears toAccountId when switching away from transfer', (
      tester,
    ) async {
      final container = ProviderContainer();
      addTearDown(container.dispose);

      // Set up a transfer with both accounts
      final formNotifier = container.read(transactionFormProvider.notifier)
        ..setTransactionType(TransactionType.transfer)
        ..setAccountId('account1')
        ..setToAccountId('account2');

      var state = container.read(transactionFormProvider);
      expect(state.toAccountId, 'account2');

      // Switch to expense
      formNotifier.setTransactionType(TransactionType.expense);
      state = container.read(transactionFormProvider);

      expect(state.toAccountId, null); // Should be cleared

      // Ensure all async operations complete
      await tester.pumpAndSettle();
    });
  });
}
