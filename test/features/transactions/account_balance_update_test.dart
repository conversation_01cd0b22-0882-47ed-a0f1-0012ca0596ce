import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/implementations/account_repository_impl.dart';
import 'package:budapp/data/repositories/implementations/transaction_repository_impl.dart';
import 'package:budapp/features/budgets/services/budget_transaction_service.dart';
import 'package:budapp/features/transactions/providers/transaction_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/services/cache_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:budapp/services/performance_rollout_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/firebase_test_setup.dart';
import '../../helpers/mock_data_factory.dart';

// Mock classes for testing
class MockBudgetTransactionService extends Mock
    implements BudgetTransactionService {}

class MockCacheService extends Mock implements CacheService {}

class MockPerformanceRolloutService extends Mock
    implements PerformanceRolloutService {}

/// Test suite to verify that account balances are updated when transactions are created
///
/// This test demonstrates the TDD approach to fixing the account balance update issue:
/// 1. Tests should initially FAIL (except the generic method test)
/// 2. After fixing the TransactionCreator to use type-specific methods, tests should PASS
void main() {
  setUpAll(() {
    // Use MockDataFactory to create fallback values instead of Fake classes
    registerFallbackValue(MockDataFactory.createTransaction());
  });

  group('Account Balance Update Tests (TDD)', () {
    late ProviderContainer container;
    late FirebaseTestSetup testSetup;
    late MockBudgetTransactionService mockBudgetTransactionService;
    late String testUserId;
    late String testAccountId1;
    late String testAccountId2;
    late AccountRepositoryImpl accountRepository;

    setUp(() async {
      testUserId = 'test-user-balance-update';
      testAccountId1 = 'account-balance-1';
      testAccountId2 = 'account-balance-2';

      // Create Firebase test setup with authenticated user
      testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
        uid: testUserId,
        email: '<EMAIL>',
      );

      mockBudgetTransactionService = MockBudgetTransactionService();

      // Setup mock budget transaction service
      when(
        () => mockBudgetTransactionService.updateBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      when(
        () => mockBudgetTransactionService.revertBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async {});

      when(
        () => mockBudgetTransactionService.updateBudgetsForTransactionUpdate(
          any(),
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      final firestoreService = FirestoreService(testSetup.firestore);
      final mockCacheService = MockCacheService();
      final mockPerformanceRolloutService = MockPerformanceRolloutService();
      accountRepository = AccountRepositoryImpl(
        firestoreService,
        testSetup.auth,
        mockCacheService,
        mockPerformanceRolloutService,
      );

      // Create test accounts with known initial balances
      final account1 = Account(
        id: testAccountId1,
        userId: testUserId,
        name: 'Test Checking Account',
        type: AccountType.checking,
        classification: AccountClassification.asset,
        initialBalanceCents: 10000, // $100.00
        currentBalanceCents: 10000, // $100.00

        isActive: true,
        isPrimary: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final account2 = Account(
        id: testAccountId2,
        userId: testUserId,
        name: 'Test Savings Account',
        type: AccountType.savings,
        classification: AccountClassification.asset,
        initialBalanceCents: 5000, // $50.00
        currentBalanceCents: 5000, // $50.00

        isActive: true,
        isPrimary: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await testSetup.firestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId1)
          .set(account1.toJson());

      await testSetup.firestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId2)
          .set(account2.toJson());

      // Create container with overridden providers
      container = ProviderContainer(
        overrides: [
          transactionRepositoryProvider.overrideWithValue(
            TransactionRepositoryImpl(
              firestoreService,
              mockBudgetTransactionService,
            ),
          ),
          accountRepositoryProvider.overrideWithValue(accountRepository),
        ],
      );
    });

    tearDown(() async {
      container.dispose();
      await testSetup.dispose();
    });

    /// Helper method to get current account balance
    Future<int> getAccountBalance(String accountId) async {
      final account = await accountRepository.getAccountById(
        testUserId,
        accountId,
      );
      return account?.currentBalanceCents ?? 0;
    }

    group('Income Transaction Balance Updates', () {
      test(
        'should increase account balance when income transaction is created via UI',
        () async {
          // Arrange: Get initial balance
          final initialBalance = await getAccountBalance(testAccountId1);
          expect(initialBalance, equals(10000)); // $100.00

          // Create income transaction using the UI path (TransactionCreator.createTransaction)
          final transaction = Transaction(
            id: 'test-income-transaction',
            userId: testUserId,
            type: TransactionType.income,
            status: TransactionStatus.completed,
            amountCents: 5000, // $50.00

            toAccountId: testAccountId1,
            description: 'Test income transaction',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          // Act: Create transaction via UI layer
          final creator = container.read(transactionCreatorProvider.notifier);
          await creator.createTransaction(transaction);

          // Assert: Account balance should be increased
          final finalBalance = await getAccountBalance(testAccountId1);
          expect(finalBalance, equals(15000)); // $100.00 + $50.00 = $150.00
        },
      );
    });

    group('Expense Transaction Balance Updates', () {
      test(
        'should decrease account balance when expense transaction is created via UI',
        () async {
          // Arrange: Get initial balance
          final initialBalance = await getAccountBalance(testAccountId1);
          expect(initialBalance, equals(10000)); // $100.00

          // Create expense transaction using the UI path
          final transaction = Transaction(
            id: 'test-expense-transaction',
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 3000, // $30.00

            fromAccountId: testAccountId1,
            description: 'Test expense transaction',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          // Act: Create transaction via UI layer
          final creator = container.read(transactionCreatorProvider.notifier);
          await creator.createTransaction(transaction);

          // Assert: Account balance should be decreased
          final finalBalance = await getAccountBalance(testAccountId1);
          expect(finalBalance, equals(7000)); // $100.00 - $30.00 = $70.00
        },
      );
    });

    group('Transfer Transaction Balance Updates', () {
      test(
        'should update both account balances when transfer transaction is created via UI',
        () async {
          // Arrange: Get initial balances
          final initialBalance1 = await getAccountBalance(testAccountId1);
          final initialBalance2 = await getAccountBalance(testAccountId2);
          expect(initialBalance1, equals(10000)); // $100.00
          expect(initialBalance2, equals(5000)); // $50.00

          // Create transfer transaction using the UI path
          final transaction = Transaction(
            id: 'test-transfer-transaction',
            userId: testUserId,
            type: TransactionType.transfer,
            status: TransactionStatus.completed,
            amountCents: 2500, // $25.00

            fromAccountId: testAccountId1,
            toAccountId: testAccountId2,
            description: 'Test transfer transaction',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          // Act: Create transaction via UI layer
          final creator = container.read(transactionCreatorProvider.notifier);
          await creator.createTransaction(transaction);

          // Assert: Both account balances should be updated
          final finalBalance1 = await getAccountBalance(testAccountId1);
          final finalBalance2 = await getAccountBalance(testAccountId2);
          expect(finalBalance1, equals(7500)); // $100.00 - $25.00 = $75.00
          expect(finalBalance2, equals(7500)); // $50.00 + $25.00 = $75.00
        },
      );
    });

    group('Generic Transaction Method (Current Bug)', () {
      test(
        'generic createTransaction should NOT update balances (demonstrates current bug)',
        () async {
          // This test should PASS initially, demonstrating that the generic method
          // doesn't update balances, which is the root cause of the bug

          // Arrange: Get initial balance
          final initialBalance = await getAccountBalance(testAccountId1);
          expect(initialBalance, equals(10000)); // $100.00

          // Create transaction using the repository's generic method directly
          final transactionRepository = container.read(
            transactionRepositoryProvider,
          );
          final transaction = Transaction(
            id: 'test-generic-transaction',
            userId: testUserId,
            type: TransactionType.income,
            status: TransactionStatus.completed,
            amountCents: 5000, // $50.00

            toAccountId: testAccountId1,
            description: 'Test generic transaction',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          // Act: Create transaction via generic repository method
          await transactionRepository.createTransaction(transaction);

          // Assert: Account balance should NOT be updated (this demonstrates the bug)
          final finalBalance = await getAccountBalance(testAccountId1);
          expect(
            finalBalance,
            equals(10000),
          ); // Balance unchanged - this is the bug!
        },
      );
    });
  });
}
