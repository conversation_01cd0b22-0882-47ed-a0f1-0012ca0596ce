import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AuthErrorService Tests', () {
    group('Firebase Auth Exception Handling', () {
      test('handles user-not-found error correctly', () {
        final exception = FirebaseAuthException(
          code: 'user-not-found',
          message: 'There is no user record corresponding to this identifier.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);

        expect(error.code, equals('user-not-found'));
        expect(error.userMessage, contains('No account found'));
        expect(error.severity, equals(ErrorSeverity.error));
        expect(error.category, equals(ErrorCategory.authentication));
        expect(error.isRetryable, isFalse);
      });

      test('handles wrong-password error correctly', () {
        final exception = FirebaseAuthException(
          code: 'wrong-password',
          message: 'The password is invalid.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);

        expect(error.code, equals('wrong-password'));
        expect(error.userMessage, contains('Incorrect password'));
        expect(error.severity, equals(ErrorSeverity.error));
        expect(error.category, equals(ErrorCategory.authentication));
        expect(error.isRetryable, isTrue);
      });

      test('handles email-already-in-use error correctly', () {
        final exception = FirebaseAuthException(
          code: 'email-already-in-use',
          message: 'The email address is already in use.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);

        expect(error.code, equals('email-already-in-use'));
        expect(error.userMessage, contains('account already exists'));
        expect(error.severity, equals(ErrorSeverity.error));
        expect(error.category, equals(ErrorCategory.authentication));
        expect(error.isRetryable, isFalse);
      });

      test('handles weak-password error correctly', () {
        final exception = FirebaseAuthException(
          code: 'weak-password',
          message: 'The password is too weak.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);

        expect(error.code, equals('weak-password'));
        expect(error.userMessage, contains('Password is too weak'));
        expect(error.severity, equals(ErrorSeverity.error));
        expect(error.category, equals(ErrorCategory.validation));
        expect(error.isRetryable, isTrue);
      });

      test('handles too-many-requests error correctly', () {
        final exception = FirebaseAuthException(
          code: 'too-many-requests',
          message: 'Too many requests.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);

        expect(error.code, equals('too-many-requests'));
        expect(error.userMessage, contains('Too many failed attempts'));
        expect(error.severity, equals(ErrorSeverity.warning));
        expect(error.category, equals(ErrorCategory.rateLimit));
        expect(error.isRetryable, isTrue);
      });

      test('handles network-request-failed error correctly', () {
        final exception = FirebaseAuthException(
          code: 'network-request-failed',
          message: 'Network error.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);

        expect(error.code, equals('network-request-failed'));
        expect(error.userMessage, contains('Network error'));
        expect(error.severity, equals(ErrorSeverity.error));
        expect(error.category, equals(ErrorCategory.network));
        expect(error.isRetryable, isTrue);
      });

      test('handles user-disabled error correctly', () {
        final exception = FirebaseAuthException(
          code: 'user-disabled',
          message: 'The user account has been disabled.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);

        expect(error.code, equals('user-disabled'));
        expect(error.userMessage, contains('account has been disabled'));
        expect(error.severity, equals(ErrorSeverity.critical));
        expect(error.category, equals(ErrorCategory.permission));
        expect(error.isRetryable, isFalse);
      });

      test('handles unknown Firebase Auth error correctly', () {
        final exception = FirebaseAuthException(
          code: 'unknown-error',
          message: 'An unknown error occurred.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);

        expect(error.code, equals('unknown-error'));
        expect(error.userMessage, contains('unexpected error'));
        expect(error.severity, equals(ErrorSeverity.error));
        expect(error.category, equals(ErrorCategory.system));
        expect(error.isRetryable, isTrue);
        expect(error.metadata['originalCode'], equals('unknown-error'));
      });
    });

    group('General Exception Handling', () {
      test('handles general exceptions correctly', () {
        final exception = Exception('General error');

        final error = AuthErrorService.handleGeneralException(exception);

        expect(error.code, equals('general_error'));
        expect(error.userMessage, contains('unexpected error'));
        expect(error.severity, equals(ErrorSeverity.error));
        expect(error.category, equals(ErrorCategory.system));
        expect(error.isRetryable, isTrue);
        expect(error.metadata['originalError'], contains('General error'));
      });

      test('handles string exceptions correctly', () {
        const exception = 'String error message';

        final error = AuthErrorService.handleGeneralException(exception);

        expect(error.code, equals('general_error'));
        expect(error.userMessage, contains('unexpected error'));
        expect(error.technicalMessage, equals('String error message'));
      });
    });

    group('Contextual Messages', () {
      test('provides contextual message for login context', () {
        final exception = FirebaseAuthException(
          code: 'user-not-found',
          message: 'User not found.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);
        final contextualMessage = AuthErrorService.getContextualMessage(
          error,
          'login',
        );

        expect(contextualMessage, contains('No account found'));
        expect(contextualMessage, contains('create a new account'));
      });

      test('provides contextual message for signup context', () {
        final exception = FirebaseAuthException(
          code: 'email-already-in-use',
          message: 'Email already in use.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);
        final contextualMessage = AuthErrorService.getContextualMessage(
          error,
          'signup',
        );

        expect(contextualMessage, contains('account already exists'));
        expect(contextualMessage, contains('sign in instead'));
      });

      test('provides contextual message for password reset context', () {
        final exception = FirebaseAuthException(
          code: 'user-not-found',
          message: 'User not found.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);
        final contextualMessage = AuthErrorService.getContextualMessage(
          error,
          'password_reset',
        );

        expect(contextualMessage, contains('No account found'));
      });

      test('falls back to default message for unknown context', () {
        final exception = FirebaseAuthException(
          code: 'user-not-found',
          message: 'User not found.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);
        final contextualMessage = AuthErrorService.getContextualMessage(
          error,
          'unknown_context',
        );

        expect(contextualMessage, equals(error.userMessage));
      });
    });

    group('Suggested Actions', () {
      test('provides suggested actions for user-not-found error', () {
        final exception = FirebaseAuthException(
          code: 'user-not-found',
          message: 'User not found.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);
        final actions = AuthErrorService.getSuggestedActions(error);

        expect(actions, contains('Check your email address'));
        expect(actions, contains('Create a new account'));
      });

      test('provides suggested actions for wrong-password error', () {
        final exception = FirebaseAuthException(
          code: 'wrong-password',
          message: 'Wrong password.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);
        final actions = AuthErrorService.getSuggestedActions(error);

        expect(actions, contains('Try again'));
        expect(actions, contains('Reset your password'));
      });

      test('provides suggested actions for weak-password error', () {
        final exception = FirebaseAuthException(
          code: 'weak-password',
          message: 'Weak password.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);
        final actions = AuthErrorService.getSuggestedActions(error);

        expect(actions, contains('Use at least 6 characters'));
        expect(actions, contains('Include numbers and symbols'));
      });

      test('provides default suggested actions for unknown error', () {
        final exception = FirebaseAuthException(
          code: 'unknown-error',
          message: 'Unknown error.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);
        final actions = AuthErrorService.getSuggestedActions(error);

        expect(actions, contains('Try again'));
        expect(actions, contains('Contact support if the problem persists'));
      });
    });

    group('Alternative Authentication Suggestions', () {
      test(
        'suggests alternative auth for account-exists-with-different-credential',
        () {
          final exception = FirebaseAuthException(
            code: 'account-exists-with-different-credential',
            message: 'Account exists with different credential.',
          );

          final error = AuthErrorService.handleFirebaseAuthException(exception);
          final shouldSuggest = AuthErrorService.shouldSuggestAlternativeAuth(
            error,
          );

          expect(shouldSuggest, isTrue);
        },
      );

      test('suggests alternative auth for google_sign_in_failed', () {
        final exception = FirebaseAuthException(
          code: 'google_sign_in_failed',
          message: 'Google sign in failed.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);
        final shouldSuggest = AuthErrorService.shouldSuggestAlternativeAuth(
          error,
        );

        expect(shouldSuggest, isTrue);
      });

      test('does not suggest alternative auth for regular errors', () {
        final exception = FirebaseAuthException(
          code: 'wrong-password',
          message: 'Wrong password.',
        );

        final error = AuthErrorService.handleFirebaseAuthException(exception);
        final shouldSuggest = AuthErrorService.shouldSuggestAlternativeAuth(
          error,
        );

        expect(shouldSuggest, isFalse);
      });
    });
  });

  group('AuthError Model Tests', () {
    test('AuthError creates correctly', () {
      const error = AuthError(
        code: 'test-error',
        userMessage: 'Test user message',
        technicalMessage: 'Test technical message',
        severity: ErrorSeverity.error,
        category: ErrorCategory.authentication,
        isRetryable: true,
        metadata: {'key': 'value'},
      );

      expect(error.code, equals('test-error'));
      expect(error.userMessage, equals('Test user message'));
      expect(error.technicalMessage, equals('Test technical message'));
      expect(error.severity, equals(ErrorSeverity.error));
      expect(error.category, equals(ErrorCategory.authentication));
      expect(error.isRetryable, isTrue);
      expect(error.metadata['key'], equals('value'));
    });

    test('AuthError toString works correctly', () {
      const error = AuthError(
        code: 'test-error',
        userMessage: 'Test message',
        technicalMessage: 'Technical message',
        severity: ErrorSeverity.error,
        category: ErrorCategory.authentication,
      );

      final stringRepresentation = error.toString();
      expect(stringRepresentation, contains('test-error'));
      expect(stringRepresentation, contains('Test message'));
    });
  });
}
