import 'package:budapp/data/repositories/interfaces/repositories.dart';
import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:budapp/features/auth/services/biometric_service.dart';
import 'package:budapp/services/secure_storage_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_auth_mocks/firebase_auth_mocks.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/test_auth_helper.dart';

// Mock classes (keeping non-auth mocks as mocktail)
class MockGoogleSignIn extends Mock implements GoogleSignIn {}

class MockGoogleSignInAccount extends Mock implements GoogleSignInAccount {}

class MockGoogleSignInAuthentication extends Mock
    implements GoogleSignInAuthentication {}

class MockGoogleSignInAuthorizationClient extends Mock
    implements GoogleSignInAuthorizationClient {}

class MockGoogleSignInClientAuthorization extends Mock
    implements GoogleSignInClientAuthorization {}

class MockUserRepository extends Mock implements IUserRepository {}

class MockBiometricService extends Mock implements BiometricService {}

class MockSecureStorageService extends Mock implements SecureStorageService {}

class FakeUser extends Fake implements User {}

class MockUserInfo extends Mock implements UserInfo {
  MockUserInfo({
    required String providerId,
    required String uid,
    String? email,
    String? displayName,
  }) : _providerId = providerId,
       _uid = uid,
       _email = email,
       _displayName = displayName;

  final String _providerId;
  final String _uid;
  final String? _email;
  final String? _displayName;

  @override
  String get providerId => _providerId;

  @override
  String get uid => _uid;

  @override
  String? get email => _email;

  @override
  String? get displayName => _displayName;

  @override
  String? get phoneNumber => null;

  @override
  String? get photoURL => null;
}

class FakeUserInfo extends Fake implements UserInfo {}

void main() {
  setUpAll(() {
    registerFallbackValue(FakeUser());
    registerFallbackValue(FakeUserInfo());
  });

  group('AuthService', () {
    late AuthService authService;
    late MockFirebaseAuth mockAuth;
    late MockGoogleSignIn mockGoogleSignIn;
    late MockUserRepository mockUserRepository;
    late MockBiometricService mockBiometricService;
    late MockSecureStorageService mockSecureStorageService;

    setUp(() {
      // Use firebase_auth_mocks for Firebase Auth
      mockAuth = TestAuthHelper.createUnauthenticatedMockAuth();

      // Keep using mocktail for other services
      mockGoogleSignIn = MockGoogleSignIn();
      mockUserRepository = MockUserRepository();
      mockBiometricService = MockBiometricService();
      mockSecureStorageService = MockSecureStorageService();

      // Set up default mock behaviors
      when(
        () => mockUserRepository.createOrUpdateFromFirebaseUser(any()),
      ).thenAnswer((_) async {});

      authService = AuthService(
        auth: mockAuth,
        googleSignIn: mockGoogleSignIn,
        userRepository: mockUserRepository,
        biometricService: mockBiometricService,
        secureStorage: mockSecureStorageService,
      );
    });

    test('AuthService provides all expected instance methods', () {
      // Test that AuthService has all the expected instance methods
      expect(authService.signInWithEmailAndPassword, isA<Function>());
      expect(authService.createUserWithEmailAndPassword, isA<Function>());
      expect(authService.signInWithGoogle, isA<Function>());
      expect(authService.signUpWithGoogle, isA<Function>());
      expect(authService.sendPasswordResetEmail, isA<Function>());
      expect(authService.sendEmailVerification, isA<Function>());
      expect(authService.signOut, isA<Function>());
      expect(authService.reloadUser, isA<Function>());
      expect(authService.deleteAccount, isA<Function>());
      expect(authService.updateDisplayName, isA<Function>());
      expect(authService.updateEmail, isA<Function>());
      expect(authService.updatePassword, isA<Function>());
      expect(authService.reauthenticateWithEmailAndPassword, isA<Function>());
      expect(authService.reauthenticateWithGoogle, isA<Function>());
      expect(authService.getUserProviders, isA<Function>());
    });

    test('AuthService provides all expected getters', () {
      // Test that AuthService has all the expected getters
      expect(() => authService.currentUser, returnsNormally);
      expect(() => authService.isSignedIn, returnsNormally);
      expect(() => authService.isEmailVerified, returnsNormally);
      expect(() => authService.authStateChanges, returnsNormally);
      expect(() => authService.isSignedInWithGoogle, returnsNormally);
      expect(() => authService.isSignedInWithEmailPassword, returnsNormally);
    });

    group('Email/Password Authentication', () {
      test(
        'signInWithEmailAndPassword succeeds with valid credentials',
        () async {
          // Arrange
          const email = '<EMAIL>';
          const password = 'password123';

          // Act
          final result = await authService.signInWithEmailAndPassword(
            email: email,
            password: password,
          );

          // Assert
          expect(result.user, isNotNull);
          // Note: firebase_auth_mocks creates a default user, so we just verify the call was made
          verify(
            () => mockUserRepository.createOrUpdateFromFirebaseUser(any()),
          ).called(1);
        },
      );

      test('createUserWithEmailAndPassword succeeds with valid data', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';

        // Act
        final result = await authService.createUserWithEmailAndPassword(
          email: email,
          password: password,
        );

        // Assert
        expect(result.user, isNotNull);
        // Note: firebase_auth_mocks creates a default user, so we just verify the call was made
        verify(
          () => mockUserRepository.createOrUpdateFromFirebaseUser(any()),
        ).called(1);
      });

      test('signInWithEmailAndPassword calls performance tracking', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';

        // Act
        final result = await authService.signInWithEmailAndPassword(
          email: email,
          password: password,
        );

        // Assert
        expect(result.user, isNotNull);
        // Performance tracking is called (we can see this in the debug output)
      });
    });

    group('Google Sign-In', () {
      test('signInWithGoogle succeeds with valid Google account', () async {
        // Arrange
        final mockGoogleAccount = MockGoogleSignInAccount();
        final mockGoogleAuth = MockGoogleSignInAuthentication();
        final mockAuthClient = MockGoogleSignInAuthorizationClient();
        final mockAuthorization = MockGoogleSignInClientAuthorization();

        when(() => mockGoogleSignIn.initialize()).thenAnswer((_) async {});
        when(
          () =>
              mockGoogleSignIn.authenticate(scopeHint: any(named: 'scopeHint')),
        ).thenAnswer((_) async => mockGoogleAccount);
        when(() => mockGoogleAccount.authentication).thenReturn(mockGoogleAuth);
        when(
          () => mockGoogleSignIn.authorizationClient,
        ).thenReturn(mockAuthClient);
        when(
          () => mockAuthClient.authorizationForScopes(any()),
        ).thenAnswer((_) async => mockAuthorization);
        when(() => mockAuthorization.accessToken).thenReturn('access-token');
        when(() => mockGoogleAuth.idToken).thenReturn('id-token');

        // Act
        final result = await authService.signInWithGoogle();

        // Assert
        expect(result.user, isNotNull);
        verify(
          () => mockUserRepository.createOrUpdateFromFirebaseUser(any()),
        ).called(1);
      });

      test('signInWithGoogle handles user cancellation', () async {
        // Arrange
        when(() => mockGoogleSignIn.initialize()).thenAnswer((_) async {});
        when(
          () =>
              mockGoogleSignIn.authenticate(scopeHint: any(named: 'scopeHint')),
        ).thenThrow(
          const GoogleSignInException(
            code: GoogleSignInExceptionCode.canceled,
            description: 'User canceled',
          ),
        );

        // Act & Assert
        expect(
          () => authService.signInWithGoogle(),
          throwsA(
            isA<FirebaseAuthException>().having(
              (e) => e.code,
              'code',
              'canceled',
            ),
          ),
        );
      });

      test('signUpWithGoogle delegates to signInWithGoogle', () async {
        // Arrange
        final mockGoogleAccount = MockGoogleSignInAccount();
        final mockGoogleAuth = MockGoogleSignInAuthentication();
        final mockAuthClient = MockGoogleSignInAuthorizationClient();
        final mockAuthorization = MockGoogleSignInClientAuthorization();

        when(() => mockGoogleSignIn.initialize()).thenAnswer((_) async {});
        when(
          () =>
              mockGoogleSignIn.authenticate(scopeHint: any(named: 'scopeHint')),
        ).thenAnswer((_) async => mockGoogleAccount);
        when(() => mockGoogleAccount.authentication).thenReturn(mockGoogleAuth);
        when(
          () => mockGoogleSignIn.authorizationClient,
        ).thenReturn(mockAuthClient);
        when(
          () => mockAuthClient.authorizationForScopes(any()),
        ).thenAnswer((_) async => mockAuthorization);
        when(() => mockAuthorization.accessToken).thenReturn('access-token');
        when(() => mockGoogleAuth.idToken).thenReturn('id-token');

        // Act
        final result = await authService.signUpWithGoogle();

        // Assert
        expect(result.user, isNotNull);
        verify(
          () => mockUserRepository.createOrUpdateFromFirebaseUser(any()),
        ).called(1);
      });
    });

    group('Password Reset', () {
      test('sendPasswordResetEmail succeeds with valid email', () async {
        // Arrange
        const email = '<EMAIL>';

        // Act
        await authService.sendPasswordResetEmail(email: email);

        // Assert - no exception thrown means success
        // firebase_auth_mocks handles the actual call
      });

      test('sendPasswordResetEmail handles FirebaseAuthException', () async {
        // Arrange
        const email = '<EMAIL>';
        mockAuth = MockFirebaseAuth(mockUser: null, signedIn: false);
        // Recreate service with new auth that will throw
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act & Assert
        // Note: firebase_auth_mocks doesn't throw exceptions for sendPasswordResetEmail
        // This test verifies the error handling structure is in place
        await authService.sendPasswordResetEmail(email: email);
      });
    });

    group('Email Verification', () {
      test('sendEmailVerification succeeds with unverified user', () async {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          isEmailVerified: false, // Key: not verified
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act
        await authService.sendEmailVerification();

        // Assert - no exception thrown means success
      });

      test('sendEmailVerification throws when no user signed in', () async {
        // Arrange
        mockAuth = MockFirebaseAuth(mockUser: null, signedIn: false);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act & Assert
        expect(
          () => authService.sendEmailVerification(),
          throwsA(isA<FirebaseAuthException>()),
        );
      });

      test(
        'sendEmailVerification throws when email already verified',
        () async {
          // Arrange
          final mockUser = MockUser(
            isAnonymous: false,
            uid: 'test-uid',
            email: '<EMAIL>',
            displayName: 'Test User',
            isEmailVerified: true, // Key: already verified
          );
          mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
          authService = AuthService(
            auth: mockAuth,
            googleSignIn: mockGoogleSignIn,
            userRepository: mockUserRepository,
            biometricService: mockBiometricService,
            secureStorage: mockSecureStorageService,
          );

          // Act & Assert
          expect(
            () => authService.sendEmailVerification(),
            throwsA(isA<FirebaseAuthException>()),
          );
        },
      );
    });

    group('Sign Out', () {
      test('signOut succeeds with Google and Firebase sign out', () async {
        // Arrange
        when(() => mockGoogleSignIn.signOut()).thenAnswer((_) async {});

        // Act
        await authService.signOut();

        // Assert
        verify(() => mockGoogleSignIn.signOut()).called(1);
      });

      test('signOut handles Google sign out exceptions gracefully', () async {
        // Arrange
        when(
          () => mockGoogleSignIn.signOut(),
        ).thenThrow(Exception('Network error'));

        // Act & Assert
        expect(() => authService.signOut(), throwsA(isA<Exception>()));

        verify(() => mockGoogleSignIn.signOut()).called(1);
      });
    });

    group('User Info Management', () {
      test('updateDisplayName succeeds', () async {
        // Arrange
        const displayName = 'New Display Name';
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Old Name',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act
        await authService.updateDisplayName(displayName);

        // Assert - no exception thrown means success
      });

      test('updateEmail succeeds', () async {
        // Arrange
        const newEmail = '<EMAIL>';
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act
        await authService.updateEmail(newEmail);

        // Assert - no exception thrown means success
      });

      test('updatePassword succeeds', () async {
        // Arrange
        const newPassword = 'newpassword123';
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act
        await authService.updatePassword(newPassword);

        // Assert - no exception thrown means success
      });

      test('reloadUser succeeds with current user', () async {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act
        await authService.reloadUser();

        // Assert - no exception thrown means success
      });

      test('reloadUser handles no current user gracefully', () async {
        // Arrange
        mockAuth = MockFirebaseAuth(mockUser: null, signedIn: false);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act
        await authService.reloadUser();

        // Assert - no exception thrown, handles null user gracefully
      });
    });

    group('Account Deletion', () {
      test('deleteAccount succeeds with current user', () async {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        when(
          () => mockUserRepository.deleteUserProfile(any()),
        ).thenAnswer((_) async {});
        when(() => mockGoogleSignIn.signOut()).thenAnswer((_) async {});

        // Act
        await authService.deleteAccount();

        // Assert
        verify(
          () => mockUserRepository.deleteUserProfile('test-uid'),
        ).called(1);
      });

      test('deleteAccount throws when no current user', () async {
        // Arrange
        mockAuth = MockFirebaseAuth(mockUser: null, signedIn: false);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act & Assert
        expect(
          () => authService.deleteAccount(),
          throwsA(isA<FirebaseAuthException>()),
        );
      });

      test('deleteAccountWithReauth succeeds', () async {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        when(
          () => mockUserRepository.deleteUserProfile(any()),
        ).thenAnswer((_) async {});
        when(() => mockGoogleSignIn.signOut()).thenAnswer((_) async {});

        // Act
        await authService.deleteAccountWithReauth('password123');

        // Assert
        verify(
          () => mockUserRepository.deleteUserProfile('test-uid'),
        ).called(1);
      });
    });

    group('Reauthentication', () {
      test('reauthenticateWithEmailAndPassword succeeds', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: email,
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act
        await authService.reauthenticateWithEmailAndPassword(
          email: email,
          password: password,
        );

        // Assert - no exception thrown means success
      });

      test('reauthenticate succeeds with current user email', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: email,
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act
        await authService.reauthenticate(password);

        // Assert - no exception thrown means success
      });

      test('reauthenticate throws when no user email', () async {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: null, // No email
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act & Assert
        expect(
          () => authService.reauthenticate('password123'),
          throwsA(isA<FirebaseAuthException>()),
        );
      });

      test('reauthenticateWithGoogle succeeds', () async {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        final mockGoogleAccount = MockGoogleSignInAccount();
        final mockGoogleAuth = MockGoogleSignInAuthentication();
        final mockAuthClient = MockGoogleSignInAuthorizationClient();
        final mockAuthorization = MockGoogleSignInClientAuthorization();

        when(() => mockGoogleSignIn.initialize()).thenAnswer((_) async {});
        when(
          () =>
              mockGoogleSignIn.authenticate(scopeHint: any(named: 'scopeHint')),
        ).thenAnswer((_) async => mockGoogleAccount);
        when(() => mockGoogleAccount.authentication).thenReturn(mockGoogleAuth);
        when(
          () => mockGoogleSignIn.authorizationClient,
        ).thenReturn(mockAuthClient);
        when(
          () => mockAuthClient.authorizationForScopes(any()),
        ).thenAnswer((_) async => mockAuthorization);
        when(() => mockAuthorization.accessToken).thenReturn('access-token');
        when(() => mockGoogleAuth.idToken).thenReturn('id-token');

        // Act
        await authService.reauthenticateWithGoogle();

        // Assert - no exception thrown means success
      });

      test('reauthenticateWithGoogle handles user cancellation', () async {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        when(() => mockGoogleSignIn.initialize()).thenAnswer((_) async {});
        when(
          () =>
              mockGoogleSignIn.authenticate(scopeHint: any(named: 'scopeHint')),
        ).thenThrow(
          const GoogleSignInException(
            code: GoogleSignInExceptionCode.canceled,
            description: 'User canceled',
          ),
        );

        // Act & Assert
        expect(
          () => authService.reauthenticateWithGoogle(),
          throwsA(isA<FirebaseAuthException>()),
        );
      });
    });

    group('Property Getters', () {
      test('currentUser returns current user', () {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act & Assert
        expect(authService.currentUser, equals(mockUser));
      });

      test('isSignedIn returns true when user exists', () {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act & Assert
        expect(authService.isSignedIn, isTrue);
      });

      test('isSignedIn returns false when no user', () {
        // Arrange
        mockAuth = MockFirebaseAuth(mockUser: null, signedIn: false);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act & Assert
        expect(authService.isSignedIn, isFalse);
      });

      test('isEmailVerified returns true when user email is verified', () {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          isEmailVerified: true,
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act & Assert
        expect(authService.isEmailVerified, isTrue);
      });

      test('isEmailVerified returns false when user email not verified', () {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          isEmailVerified: false,
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act & Assert
        expect(authService.isEmailVerified, isFalse);
      });

      test('isEmailVerified returns false when no user', () {
        // Arrange
        mockAuth = MockFirebaseAuth(mockUser: null, signedIn: false);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act & Assert
        expect(authService.isEmailVerified, isFalse);
      });

      test('authStateChanges returns auth state stream', () {
        // Act & Assert
        expect(authService.authStateChanges, isA<Stream<User?>>());
      });

      test('getUserProviders returns provider list', () {
        // Arrange
        final mockUserInfo = MockUserInfo(
          providerId: 'google.com',
          uid: 'google-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          providerData: [mockUserInfo],
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act
        final providers = authService.getUserProviders();

        // Assert
        expect(providers, contains('google.com'));
      });

      test('getUserProviders returns empty list when no user', () {
        // Arrange
        mockAuth = MockFirebaseAuth(mockUser: null, signedIn: false);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act
        final providers = authService.getUserProviders();

        // Assert
        expect(providers, isEmpty);
      });

      test('isSignedInWithGoogle returns true for Google provider', () {
        // Arrange
        final mockUserInfo = MockUserInfo(
          providerId: 'google.com',
          uid: 'google-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          providerData: [mockUserInfo],
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act & Assert
        expect(authService.isSignedInWithGoogle, isTrue);
      });

      test(
        'isSignedInWithEmailPassword returns true for password provider',
        () {
          // Arrange
          final mockUserInfo = MockUserInfo(
            providerId: 'password',
            uid: 'password-uid',
            email: '<EMAIL>',
            displayName: 'Test User',
          );
          final mockUser = MockUser(
            isAnonymous: false,
            uid: 'test-uid',
            email: '<EMAIL>',
            displayName: 'Test User',
            providerData: [mockUserInfo],
          );
          mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
          authService = AuthService(
            auth: mockAuth,
            googleSignIn: mockGoogleSignIn,
            userRepository: mockUserRepository,
            biometricService: mockBiometricService,
            secureStorage: mockSecureStorageService,
          );

          // Act & Assert
          expect(authService.isSignedInWithEmailPassword, isTrue);
        },
      );
    });

    group('Biometric Authentication', () {
      test('isBiometricAuthAvailable returns true when available', () async {
        // Arrange
        when(
          () => mockBiometricService.checkBiometricAvailability(),
        ).thenAnswer((_) async => BiometricAvailability.available);

        // Act
        final result = await authService.isBiometricAuthAvailable();

        // Assert
        expect(result, isTrue);
      });

      test(
        'isBiometricAuthAvailable returns false when not available',
        () async {
          // Arrange
          when(
            () => mockBiometricService.checkBiometricAvailability(),
          ).thenAnswer((_) async => BiometricAvailability.notSupported);

          // Act
          final result = await authService.isBiometricAuthAvailable();

          // Assert
          expect(result, isFalse);
        },
      );

      test('isBiometricAuthEnabled returns true when enabled', () async {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        when(
          () => mockSecureStorageService.getBiometricEnabled(),
        ).thenAnswer((_) async => true);

        // Act
        final result = await authService.isBiometricAuthEnabled();

        // Assert
        expect(result, isTrue);
      });

      test('isBiometricAuthEnabled returns false when no user', () async {
        // Arrange
        mockAuth = MockFirebaseAuth(mockUser: null, signedIn: false);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act
        final result = await authService.isBiometricAuthEnabled();

        // Assert
        expect(result, isFalse);
      });

      test('signInWithBiometrics succeeds with current user', () async {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        when(
          () => mockBiometricService.checkBiometricAvailability(),
        ).thenAnswer((_) async => BiometricAvailability.available);
        when(
          () => mockSecureStorageService.getBiometricEnabled(),
        ).thenAnswer((_) async => true);
        when(
          () => mockBiometricService.authenticate(
            localizedReason: any(named: 'localizedReason'),
            biometricOnly: any(named: 'biometricOnly'),
          ),
        ).thenAnswer((_) async => BiometricAuthResult.success());

        // Act
        final result = await authService.signInWithBiometrics();

        // Assert
        expect(result, equals(mockUser));
      });

      test('signInWithBiometrics throws when not available', () async {
        // Arrange
        when(
          () => mockBiometricService.checkBiometricAvailability(),
        ).thenAnswer((_) async => BiometricAvailability.notSupported);

        // Act & Assert
        expect(
          () => authService.signInWithBiometrics(),
          throwsA(isA<Exception>()),
        );
      });

      test('signInWithBiometrics throws when not enabled', () async {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        when(
          () => mockBiometricService.checkBiometricAvailability(),
        ).thenAnswer((_) async => BiometricAvailability.available);
        when(
          () => mockSecureStorageService.getBiometricEnabled(),
        ).thenAnswer((_) async => false);

        // Act & Assert
        expect(
          () => authService.signInWithBiometrics(),
          throwsA(isA<Exception>()),
        );
      });

      test('signInWithBiometrics throws when authentication fails', () async {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        when(
          () => mockBiometricService.checkBiometricAvailability(),
        ).thenAnswer((_) async => BiometricAvailability.available);
        when(
          () => mockSecureStorageService.getBiometricEnabled(),
        ).thenAnswer((_) async => true);
        when(
          () => mockBiometricService.authenticate(
            localizedReason: any(named: 'localizedReason'),
            biometricOnly: any(named: 'biometricOnly'),
          ),
        ).thenAnswer(
          (_) async => BiometricAuthResult.failure(
            BiometricAuthError.unknown,
            'Authentication failed',
          ),
        );

        // Act & Assert
        expect(
          () => authService.signInWithBiometrics(),
          throwsA(isA<Exception>()),
        );
      });

      test('enableBiometricAuth succeeds', () async {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);

        // Reset the service with the signed-in user
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        when(
          () => mockBiometricService.checkBiometricAvailability(),
        ).thenAnswer((_) async => BiometricAvailability.available);
        when(
          () => mockBiometricService.authenticate(
            localizedReason: any(named: 'localizedReason'),
            biometricOnly: any(named: 'biometricOnly'),
          ),
        ).thenAnswer((_) async => BiometricAuthResult.success());
        when(
          () => mockSecureStorageService.setBiometricEnabled(enabled: true),
        ).thenAnswer((_) async {});

        // Act
        await authService.enableBiometricAuth();

        // Assert
        verify(
          () => mockSecureStorageService.setBiometricEnabled(enabled: true),
        ).called(1);
      });

      test('enableBiometricAuth throws when no user', () async {
        // Arrange
        mockAuth = MockFirebaseAuth(mockUser: null, signedIn: false);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        // Act & Assert
        expect(
          () => authService.enableBiometricAuth(),
          throwsA(isA<Exception>()),
        );
      });

      test('enableBiometricAuth throws when not available', () async {
        // Arrange
        final mockUser = MockUser(
          isAnonymous: false,
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
        );
        mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);
        authService = AuthService(
          auth: mockAuth,
          googleSignIn: mockGoogleSignIn,
          userRepository: mockUserRepository,
          biometricService: mockBiometricService,
          secureStorage: mockSecureStorageService,
        );

        when(
          () => mockBiometricService.checkBiometricAvailability(),
        ).thenAnswer((_) async => BiometricAvailability.notSupported);

        // Act & Assert
        expect(
          () => authService.enableBiometricAuth(),
          throwsA(isA<Exception>()),
        );
      });

      test('disableBiometricAuth succeeds', () async {
        // Arrange
        when(
          () => mockSecureStorageService.setBiometricEnabled(enabled: false),
        ).thenAnswer((_) async {});

        // Act
        await authService.disableBiometricAuth();

        // Assert
        verify(
          () => mockSecureStorageService.setBiometricEnabled(enabled: false),
        ).called(1);
      });
    });

    group('Error Handling', () {
      test('handleAuthError handles FirebaseAuthException', () {
        // Arrange
        final exception = FirebaseAuthException(
          code: 'user-not-found',
          message: 'No user found for that email.',
        );

        // Act
        final result = authService.handleAuthError(exception);

        // Assert
        expect(result, isA<AuthError>());
      });

      test('handleAuthError handles general exception', () {
        // Arrange
        final exception = Exception('Network error');

        // Act
        final result = authService.handleAuthError(exception);

        // Assert
        expect(result, isA<AuthError>());
      });

      test('getContextualErrorMessage returns contextual message', () {
        // Arrange
        final exception = FirebaseAuthException(
          code: 'user-not-found',
          message: 'No user found for that email.',
        );
        const context = 'sign_in';

        // Act
        final result = authService.getContextualErrorMessage(
          exception,
          context,
        );

        // Assert
        expect(result, isA<String>());
        expect(result.isNotEmpty, isTrue);
      });
    });
  });
}
