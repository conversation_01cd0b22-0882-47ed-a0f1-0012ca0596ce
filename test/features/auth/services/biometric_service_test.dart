import 'package:budapp/features/auth/services/biometric_service.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:local_auth/error_codes.dart' as auth_error;
import 'package:local_auth/local_auth.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('BiometricService', () {
    late BiometricService biometricService;

    setUp(() {
      biometricService = BiometricService();
    });

    tearDown(() {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
            const MethodChannel('plugins.flutter.io/local_auth'),
            null,
          );
    });

    group('canCheckBiometrics', () {
      test('returns false when no biometrics available', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
              const MethodChannel('plugins.flutter.io/local_auth'),
              (MethodCall methodCall) async {
                switch (methodCall.method) {
                  case 'getAvailableBiometrics':
                    return <String>[];
                  default:
                    return null;
                }
              },
            );
        final result = await biometricService.canCheckBiometrics();
        expect(result, isFalse);
      });

      test('returns true when biometrics available', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
              const MethodChannel('plugins.flutter.io/local_auth'),
              (MethodCall methodCall) async {
                switch (methodCall.method) {
                  case 'getAvailableBiometrics':
                    return ['face'];
                  default:
                    return null;
                }
              },
            );
        final result = await biometricService.canCheckBiometrics();
        expect(result, isTrue);
      });
    });

    group('isDeviceSupported', () {
      test('returns false when device does not support biometrics', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
              const MethodChannel('plugins.flutter.io/local_auth'),
              (MethodCall methodCall) async {
                switch (methodCall.method) {
                  case 'isDeviceSupported':
                    return false;
                  default:
                    return null;
                }
              },
            );
        final result = await biometricService.isDeviceSupported();
        expect(result, isFalse);
      });

      test('returns true when device supports biometrics', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
              const MethodChannel('plugins.flutter.io/local_auth'),
              (MethodCall methodCall) async {
                switch (methodCall.method) {
                  case 'isDeviceSupported':
                    return true;
                  default:
                    return null;
                }
              },
            );
        final result = await biometricService.isDeviceSupported();
        expect(result, isTrue);
      });
    });

    group('getAvailableBiometrics', () {
      test('returns empty list when no biometrics available', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
              const MethodChannel('plugins.flutter.io/local_auth'),
              (MethodCall methodCall) async {
                switch (methodCall.method) {
                  case 'getAvailableBiometrics':
                    return <String>[];
                  default:
                    return null;
                }
              },
            );
        final result = await biometricService.getAvailableBiometrics();
        expect(result, isEmpty);
      });

      test('returns list of available biometrics', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
              const MethodChannel('plugins.flutter.io/local_auth'),
              (MethodCall methodCall) async {
                switch (methodCall.method) {
                  case 'getAvailableBiometrics':
                    return ['face', 'fingerprint'];
                  default:
                    return null;
                }
              },
            );
        final result = await biometricService.getAvailableBiometrics();
        expect(result, hasLength(2));
        expect(result, contains(BiometricType.face));
      });
    });

    group('checkBiometricAvailability', () {
      test(
        'returns notSupported when device does not support biometrics',
        () async {
          TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
              .setMockMethodCallHandler(
                const MethodChannel('plugins.flutter.io/local_auth'),
                (MethodCall methodCall) async {
                  switch (methodCall.method) {
                    case 'isDeviceSupported':
                      return false;
                    default:
                      return null;
                  }
                },
              );
          final result = await biometricService.checkBiometricAvailability();
          expect(result, equals(BiometricAvailability.notSupported));
        },
      );

      test(
        'returns notEnrolled when device supports but no biometrics enrolled',
        () async {
          TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
              .setMockMethodCallHandler(
                const MethodChannel('plugins.flutter.io/local_auth'),
                (MethodCall methodCall) async {
                  switch (methodCall.method) {
                    case 'isDeviceSupported':
                      return true;
                    case 'getAvailableBiometrics':
                      return <String>[];
                    default:
                      return null;
                  }
                },
              );
          final result = await biometricService.checkBiometricAvailability();
          expect(result, equals(BiometricAvailability.notEnrolled));
        },
      );

      test('returns available when biometrics are available', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
              const MethodChannel('plugins.flutter.io/local_auth'),
              (MethodCall methodCall) async {
                switch (methodCall.method) {
                  case 'isDeviceSupported':
                    return true;
                  case 'getAvailableBiometrics':
                    return ['face'];
                  default:
                    return null;
                }
              },
            );
        final result = await biometricService.checkBiometricAvailability();
        expect(result, equals(BiometricAvailability.available));
      });
    });

    group('authenticate', () {
      test('returns failure when biometrics not available', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
              const MethodChannel('plugins.flutter.io/local_auth'),
              (MethodCall methodCall) async {
                switch (methodCall.method) {
                  case 'isDeviceSupported':
                    return false;
                  default:
                    return null;
                }
              },
            );
        final result = await biometricService.authenticate(
          localizedReason: 'Test authentication',
        );
        expect(result.isSuccess, isFalse);
        expect(result.error, equals(BiometricAuthError.notAvailable));
      });

      test('returns success when authentication succeeds', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
              const MethodChannel('plugins.flutter.io/local_auth'),
              (MethodCall methodCall) async {
                switch (methodCall.method) {
                  case 'isDeviceSupported':
                    return true;
                  case 'getAvailableBiometrics':
                    return ['face'];
                  case 'authenticate':
                    return true;
                  default:
                    return null;
                }
              },
            );
        final result = await biometricService.authenticate();
        expect(result.isSuccess, isTrue);
        expect(result.error, isNull);
      });

      test('returns failure when authentication is cancelled', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
              const MethodChannel('plugins.flutter.io/local_auth'),
              (MethodCall methodCall) async {
                switch (methodCall.method) {
                  case 'isDeviceSupported':
                    return true;
                  case 'getAvailableBiometrics':
                    return ['face'];
                  case 'authenticate':
                    return false;
                  default:
                    return null;
                }
              },
            );
        final result = await biometricService.authenticate();
        expect(result.isSuccess, isFalse);
        expect(result.error, equals(BiometricAuthError.userCancel));
      });

      test('handles PlatformException errors', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
              const MethodChannel('plugins.flutter.io/local_auth'),
              (MethodCall methodCall) async {
                switch (methodCall.method) {
                  case 'isDeviceSupported':
                    return true;
                  case 'getAvailableBiometrics':
                    return ['face'];
                  case 'authenticate':
                    throw PlatformException(
                      code: auth_error.notAvailable,
                      message: 'Not available',
                    );
                  default:
                    return null;
                }
              },
            );
        final result = await biometricService.authenticate();
        expect(result.isSuccess, isFalse);
        expect(result.error, equals(BiometricAuthError.notAvailable));
      });
    });

    group('BiometricAvailability enum', () {
      test('has expected values', () {
        expect(BiometricAvailability.values, hasLength(3));
        expect(
          BiometricAvailability.values,
          contains(BiometricAvailability.available),
        );
        expect(
          BiometricAvailability.values,
          contains(BiometricAvailability.notSupported),
        );
        expect(
          BiometricAvailability.values,
          contains(BiometricAvailability.notEnrolled),
        );
      });
    });

    group('BiometricAuthError enum', () {
      test('has expected values', () {
        expect(BiometricAuthError.values, hasLength(7));
        expect(
          BiometricAuthError.values,
          contains(BiometricAuthError.notAvailable),
        );
        expect(
          BiometricAuthError.values,
          contains(BiometricAuthError.userCancel),
        );
        expect(BiometricAuthError.values, contains(BiometricAuthError.unknown));
      });
    });

    group('BiometricAuthResult', () {
      test('creates success result', () {
        final result = BiometricAuthResult.success();
        expect(result.isSuccess, isTrue);
        expect(result.error, isNull);
        expect(result.errorMessage, isNull);
      });

      test('creates failure result', () {
        final result = BiometricAuthResult.failure(
          BiometricAuthError.userCancel,
          'User cancelled',
        );
        expect(result.isSuccess, isFalse);
        expect(result.error, equals(BiometricAuthError.userCancel));
        expect(result.errorMessage, equals('User cancelled'));
      });
    });
  });
}
