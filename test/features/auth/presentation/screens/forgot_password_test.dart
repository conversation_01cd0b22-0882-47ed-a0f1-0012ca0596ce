import 'package:budapp/config/app_theme.dart';
import 'package:budapp/features/auth/presentation/screens/forgot_password_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/mock_data_factory.dart';
import '../../../../helpers/mock_providers.dart';
import '../../../../helpers/test_wrapper.dart';

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(MockProviders.mockUser);
    registerFallbackValue(MockDataFactory.createTransaction());
    registerFallbackValue(MockDataFactory.createAccount());
    registerFallbackValue(MockDataFactory.createCategory());
    registerFallbackValue(MockDataFactory.createUserProfile());
  });

  setUp(() {
    MockProviders.resetMocks();
    MockProviders.setupDefaultMocks();
  });

  group('Forgot Password Screen Tests', () {
    testWidgets('ForgotPasswordScreen displays correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
          overrides: MockProviders.unauthenticatedUserOverrides,
        ),
      );

      // Verify the screen displays correctly
      expect(find.text('Reset Password'), findsOneWidget);
      expect(
        find.text('Enter your email to receive a password reset link'),
        findsOneWidget,
      );
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Send Reset Link'), findsOneWidget);
      expect(find.text('Back to Sign In'), findsOneWidget);

      // Verify email input field exists
      expect(find.byType(TextFormField), findsOneWidget);

      // Verify back button exists
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
    });

    testWidgets('Email validation works correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
          overrides: MockProviders.unauthenticatedUserOverrides,
        ),
      );

      // Try to submit without email
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      // Should show validation error
      expect(find.text('Email is required'), findsOneWidget);

      // Enter invalid email
      await tester.enterText(find.byType(TextFormField), 'invalid-email');
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      // Should show invalid email error
      expect(find.text('Please enter a valid email address'), findsOneWidget);
    });

    testWidgets('Back button navigation works', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidgetWithRouter(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
        ),
      );

      // Tap back button
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pump();

      // Should navigate back (in real app, this would pop the route)
      // In test, we just verify the button is tappable
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
    });

    testWidgets('Back to Sign In button works', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidgetWithRouter(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
        ),
      );

      // Tap "Back to Sign In" button
      await tester.tap(find.text('Back to Sign In'));
      await tester.pump();

      // Should be tappable (navigation would happen in real app)
      expect(find.text('Back to Sign In'), findsOneWidget);
    });

    testWidgets('Valid email input enables send button', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
          overrides: MockProviders.unauthenticatedUserOverrides,
        ),
      );

      // Enter valid email
      await tester.enterText(find.byType(TextFormField), '<EMAIL>');
      await tester.pump();

      // Send button should be enabled
      final sendButton = find.text('Send Reset Link');
      expect(sendButton, findsOneWidget);

      // Verify the button is tappable
      await tester.tap(sendButton);
      await tester.pump();
    });

    testWidgets('Screen shows success state after email sent', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
          overrides: MockProviders.unauthenticatedUserOverrides,
        ),
      );

      // Enter valid email
      await tester.enterText(find.byType(TextFormField), '<EMAIL>');

      // Note: In a real test with Firebase mocking, we would mock the
      // sendPasswordResetEmail call to succeed and verify the success state.
      // For now, we just verify the initial state is correct.

      expect(find.text('Reset Password'), findsOneWidget);
      expect(find.text('Send Reset Link'), findsOneWidget);
    });
  });

  group('Provider Integration Tests', () {
    testWidgets('Widget renders with different provider states', (
      WidgetTester tester,
    ) async {
      // Test with loading state
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
          overrides: [...MockProviders.unauthenticatedUserOverrides],
        ),
      );

      // The form should render correctly
      expect(find.text('Send Reset Link'), findsOneWidget);
      expect(find.text('Reset Password'), findsOneWidget);
      expect(find.byType(TextFormField), findsOneWidget);
    });

    testWidgets('Handles form submission without errors', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
          overrides: MockProviders.unauthenticatedUserOverrides,
        ),
      );

      // Enter valid email and submit
      await tester.enterText(find.byType(TextFormField), '<EMAIL>');

      // Verify button exists before clicking
      expect(find.text('Send Reset Link'), findsOneWidget);
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      // Widget should still be present after submission attempt
      expect(find.byType(ForgotPasswordScreen), findsOneWidget);
    });

    testWidgets('Widget handles provider state changes gracefully', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
          overrides: MockProviders.unauthenticatedUserOverrides,
        ),
      );

      // Widget should render without errors
      expect(find.byType(ForgotPasswordScreen), findsOneWidget);
      await tester.pump();
    });
  });

  group('Success State Tests', () {
    testWidgets('Displays initial form state correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
          overrides: MockProviders.unauthenticatedUserOverrides,
        ),
      );

      // Verify initial state displays correctly
      expect(find.text('Reset Password'), findsOneWidget);
      expect(find.text('Send Reset Link'), findsOneWidget);
      expect(find.byType(Form), findsOneWidget);
    });

    testWidgets('Shows resend functionality in success state', (
      WidgetTester tester,
    ) async {
      // This test would require mocking the success state transition
      // For now, verify the widget can be built
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
          overrides: MockProviders.unauthenticatedUserOverrides,
        ),
      );

      expect(find.byType(ForgotPasswordScreen), findsOneWidget);
    });
  });

  group('Form and Input Tests', () {
    testWidgets('Handles email input correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
          overrides: MockProviders.unauthenticatedUserOverrides,
        ),
      );

      // Enter email with whitespace
      await tester.enterText(
        find.byType(TextFormField),
        '  <EMAIL>  ',
      );

      // Verify button is present before clicking
      expect(find.text('Send Reset Link'), findsOneWidget);
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      // Widget should still be present after submission attempt
      expect(find.byType(ForgotPasswordScreen), findsOneWidget);
    });

    testWidgets('Responds to Enter key press', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
          overrides: MockProviders.unauthenticatedUserOverrides,
        ),
      );

      // Enter valid email
      await tester.enterText(find.byType(TextFormField), '<EMAIL>');

      // Verify form field accepts input
      expect(find.text('<EMAIL>'), findsOneWidget);

      // Press Enter key (this triggers the form submission)
      await tester.testTextInput.receiveAction(TextInputAction.done);
      await tester.pump();

      // Widget should handle the key press
      expect(find.byType(ForgotPasswordScreen), findsOneWidget);
    });

    testWidgets('Validates email format correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
          overrides: MockProviders.unauthenticatedUserOverrides,
        ),
      );

      // Test invalid email format (simplified version like other working tests)
      await tester.enterText(find.byType(TextFormField), 'invalid-email');
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      // Should show validation error
      expect(find.text('Please enter a valid email address'), findsOneWidget);

      // Test empty email
      await tester.enterText(find.byType(TextFormField), '');
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      // Should show required error
      expect(find.text('Email is required'), findsOneWidget);
    });
  });

  group('Error Handling Tests', () {
    testWidgets('Widget renders with error states gracefully', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
          overrides: MockProviders.unauthenticatedUserOverrides,
        ),
      );

      // Widget should render without errors
      expect(find.byType(ForgotPasswordScreen), findsOneWidget);
      expect(find.text('Reset Password'), findsOneWidget);
      expect(find.text('Send Reset Link'), findsOneWidget);
    });

    testWidgets('Handles UI state changes during errors', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
          overrides: MockProviders.unauthenticatedUserOverrides,
        ),
      );

      // Enter invalid email to trigger validation
      await tester.enterText(find.byType(TextFormField), 'invalid-email');
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      // Should show validation error
      expect(find.text('Please enter a valid email address'), findsOneWidget);
    });
  });

  group('Navigation Tests', () {
    testWidgets('Back button navigation works', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidgetWithRouter(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
        ),
      );

      // Tap back button
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pump();

      // Should navigate back (in real app, this would pop the route)
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
    });

    testWidgets('Back to Sign In button navigation works', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidgetWithRouter(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
        ),
      );

      // Tap "Back to Sign In" button
      await tester.tap(find.text('Back to Sign In'));
      await tester.pump();

      // Should be tappable (navigation would happen in real app)
      expect(find.text('Back to Sign In'), findsOneWidget);
    });
  });

  group('Widget Lifecycle Tests', () {
    testWidgets('Properly disposes controllers', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
          overrides: MockProviders.unauthenticatedUserOverrides,
        ),
      );

      // Widget should build successfully
      expect(find.byType(ForgotPasswordScreen), findsOneWidget);

      // Dispose the widget
      await tester.pumpWidget(Container());

      // Should dispose without errors
      expect(find.byType(ForgotPasswordScreen), findsNothing);
    });

    testWidgets('Handles mounted state checks properly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          theme: AppTheme.lightTheme,
          overrides: MockProviders.unauthenticatedUserOverrides,
        ),
      );

      expect(find.byType(ForgotPasswordScreen), findsOneWidget);

      // Verify the widget remains stable
      await tester.pump(const Duration(milliseconds: 100));
      expect(find.byType(ForgotPasswordScreen), findsOneWidget);
    });
  });
}
