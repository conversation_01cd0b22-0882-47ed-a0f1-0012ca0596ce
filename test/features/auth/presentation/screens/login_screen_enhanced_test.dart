import 'package:budapp/features/auth/presentation/screens/login_screen.dart';
import 'package:budapp/features/auth/presentation/widgets/auth_form_field.dart';
import 'package:budapp/features/auth/presentation/widgets/biometric_auth_button.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/providers/biometric_providers.dart';
import 'package:budapp/providers/providers.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';

// Mock classes
class MockUser extends Mock implements User {}

class MockFirebaseAuthException extends Mock implements FirebaseAuthException {}

class MockLoginNotifier extends Mock implements LoginNotifier {}

class MockGoogleSignInNotifier extends Mock implements GoogleSignInNotifier {}

// Test implementation for biometric auth notifier
class TestBiometricAuthNotifier extends BiometricAuthNotifier {
  BiometricAuthState _state = const BiometricAuthState.initial();

  @override
  BiometricAuthState get state => _state;

  @override
  set state(BiometricAuthState newState) {
    _state = newState;
  }

  @override
  BiometricAuthState build() => _state;

  @override
  Future<void> authenticate() async {
    state = const BiometricAuthState.loading();
    await Future<void>.delayed(const Duration(milliseconds: 50));
    state = const BiometricAuthState.success();
  }

  @override
  Future<void> enableBiometric() async {
    state = const BiometricAuthState.loading();
    await Future<void>.delayed(const Duration(milliseconds: 50));
    state = const BiometricAuthState.success();
  }

  @override
  Future<void> disableBiometric() async {
    state = const BiometricAuthState.loading();
    await Future<void>.delayed(const Duration(milliseconds: 50));
    state = const BiometricAuthState.success();
  }

  @override
  void reset() {
    state = const BiometricAuthState.initial();
  }
}

class MockGoRouter extends Mock implements GoRouter {}

class MockBuildContext extends Mock implements BuildContext {}

// Test implementation for login notifier
class TestLoginNotifier extends LoginNotifier {
  TestLoginNotifier() : super();

  AsyncValue<void> _state = const AsyncValue.data(null);
  Exception? error;
  User? user;

  @override
  AsyncValue<void> get state => _state;

  @override
  set state(AsyncValue<void> newState) {
    _state = newState;
  }

  @override
  AsyncValue<void> build() => _state;

  @override
  Future<void> signInWithEmail(String email, String password) async {
    state = const AsyncValue.loading();

    await Future<void>.delayed(const Duration(milliseconds: 100));

    if (error != null) {
      state = AsyncValue.error(error!, StackTrace.current);
      return;
    }

    state = const AsyncValue.data(null);
  }

  void clearError() => error = null;

  // Getter and setter for error state for testing
  Exception? get errorState => error;

  set errorState(Exception? exception) {
    error = exception;
    if (exception != null) {
      state = AsyncValue.error(exception, StackTrace.current);
    }
  }
}

// Test implementation for Google sign-in notifier
class TestGoogleSignInNotifier extends GoogleSignInNotifier {
  TestGoogleSignInNotifier() : super();

  Exception? error;
  User? user;

  @override
  AsyncValue<void> build() {
    if (error != null) {
      return AsyncValue.error(error!, StackTrace.current);
    }
    return const AsyncValue.data(null);
  }

  @override
  Future<void> signInWithGoogle() async {
    state = const AsyncValue.loading();

    await Future<void>.delayed(const Duration(milliseconds: 100));

    if (error != null) {
      state = AsyncValue.error(error!, StackTrace.current);
      return;
    }

    state = const AsyncValue.data(null);
  }

  // Getter and setter for error state for testing
  Exception? get errorState => error;

  set errorState(Exception? exception) {
    error = exception;
  }
}

void main() {
  group('LoginScreen Enhanced Tests', () {
    late MockUser mockUser;
    late TestLoginNotifier testLoginNotifier;
    late TestGoogleSignInNotifier testGoogleSignInNotifier;
    late TestBiometricAuthNotifier testBiometricAuthNotifier;

    setUp(() {
      mockUser = MockUser();
      testLoginNotifier = TestLoginNotifier();
      testGoogleSignInNotifier = TestGoogleSignInNotifier();
      testBiometricAuthNotifier = TestBiometricAuthNotifier();

      when(() => mockUser.uid).thenReturn('test-uid');
      when(() => mockUser.email).thenReturn('<EMAIL>');
      when(() => mockUser.emailVerified).thenReturn(true);

      // Biometric auth notifier is already initialized with default state
    });

    group('Form Validation Edge Cases', () {
      testWidgets('should validate email with various invalid formats', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final emailField = find.byType(AuthFormField).first;
        final signInButton = find.text('Sign In');

        // Test various invalid email formats
        final invalidEmails = [
          'invalid',
          '@example.com',
          'test@',
          'test.example.com',
          'test@.com',
          'test <EMAIL>',
        ];

        for (final email in invalidEmails) {
          await tester.enterText(emailField, email);
          await tester.tap(signInButton);
          await tester.pumpAndSettle();

          expect(
            find.text('Please enter a valid email address'),
            findsOneWidget,
            reason: 'Should show validation error for invalid email: $email',
          );

          // Clear the field for next test
          await tester.enterText(emailField, '');
        }
      });

      testWidgets('should validate password requirements', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final emailField = find.byType(AuthFormField).first;
        final passwordField = find.byType(AuthFormField).last;
        final signInButton = find.text('Sign In');

        // Enter valid email
        await tester.enterText(emailField, '<EMAIL>');

        // Test empty password
        await tester.enterText(passwordField, '');
        await tester.tap(signInButton);
        await tester.pumpAndSettle();

        expect(find.text('Password is required'), findsOneWidget);
      });

      testWidgets('should handle form submission with Enter key', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final emailField = find.byType(AuthFormField).first;
        final passwordField = find.byType(AuthFormField).last;

        // Enter valid credentials
        await tester.enterText(emailField, '<EMAIL>');
        await tester.enterText(passwordField, 'password123');

        // Press Enter on password field
        await tester.testTextInput.receiveAction(TextInputAction.done);
        await tester.pumpAndSettle();

        // Should trigger form submission
        expect(find.text('Email is required'), findsNothing);
        expect(find.text('Password is required'), findsNothing);
      });
    });

    group('Authentication Error Handling', () {
      testWidgets('should handle Firebase auth form submission', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final emailField = find.byType(AuthFormField).first;
        final passwordField = find.byType(AuthFormField).last;
        final signInButton = find.text('Sign In');

        // Enter valid credentials and submit
        await tester.enterText(emailField, '<EMAIL>');
        await tester.enterText(passwordField, 'password123');
        await tester.tap(signInButton);
        await tester.pumpAndSettle();

        // Should not crash and should still show the login screen
        expect(find.byType(LoginScreen), findsOneWidget);
      });

      testWidgets('should display Google sign-in button correctly', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
            surfaceSize: const Size(
              800,
              1200,
            ), // Larger screen to fit all content
          ),
        );

        await tester.pumpAndSettle();

        // Scroll down to make Google button visible
        await tester.drag(
          find.byType(SingleChildScrollView),
          const Offset(0, -400),
        );
        await tester.pumpAndSettle();

        // Should show Google sign-in button
        expect(find.text('Continue with Google'), findsOneWidget);

        // Should be able to tap the button without errors
        final googleButton = find.text('Continue with Google').first;
        await tester.tap(googleButton, warnIfMissed: false);
        await tester.pumpAndSettle();

        // Should not crash
        expect(find.byType(LoginScreen), findsOneWidget);
      });

      testWidgets('should handle biometric authentication correctly', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Should show login screen without crashing
        expect(find.byType(LoginScreen), findsOneWidget);

        // Should have biometric authentication components available
        expect(
          find.byType(BiometricAuthNotifier),
          findsNothing,
        ); // This is a provider, not a widget
      });

      testWidgets('should handle form validation correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final emailField = find.byType(AuthFormField).first;
        final signInButton = find.text('Sign In');

        // Try to submit with empty fields
        await tester.tap(signInButton);
        await tester.pumpAndSettle();

        // Should still show the login screen (form validation should prevent submission)
        expect(find.byType(LoginScreen), findsOneWidget);

        // Enter invalid email
        await tester.enterText(emailField, 'invalid-email');
        await tester.tap(signInButton);
        await tester.pumpAndSettle();

        // Should still show the login screen
        expect(find.byType(LoginScreen), findsOneWidget);
      });

      testWidgets('should handle screen navigation elements', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Should show login screen elements
        expect(find.byType(LoginScreen), findsOneWidget);
        expect(find.text('Sign In'), findsOneWidget);
        expect(
          find.byType(AuthFormField),
          findsNWidgets(2),
        ); // Email and password fields

        // Should be able to interact with form fields
        final emailField = find.byType(AuthFormField).first;
        await tester.enterText(emailField, '<EMAIL>');
        await tester.pumpAndSettle();

        // Should not crash
        expect(find.byType(LoginScreen), findsOneWidget);
      });
    });

    group('Loading States and UI Feedback', () {
      testWidgets('should handle email login form submission', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final emailField = find.byType(AuthFormField).first;
        final passwordField = find.byType(AuthFormField).last;
        final signInButton = find.text('Sign In');

        // Enter valid credentials
        await tester.enterText(emailField, '<EMAIL>');
        await tester.enterText(passwordField, 'password123');

        // Tap sign in button
        await tester.tap(signInButton);
        await tester.pumpAndSettle(); // Wait for completion

        // Should not crash and should still show the login screen
        expect(find.byType(LoginScreen), findsOneWidget);
      });

      testWidgets('should handle Google sign-in button interaction', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
            surfaceSize: const Size(
              800,
              1200,
            ), // Larger screen to fit all content
          ),
        );

        await tester.pumpAndSettle();

        // Scroll to make Google button visible
        await tester.drag(
          find.byType(SingleChildScrollView),
          const Offset(0, -400),
        );
        await tester.pumpAndSettle();

        final googleButton = find.text('Continue with Google').first;

        // Tap Google sign-in button
        await tester.tap(googleButton, warnIfMissed: false);
        await tester.pumpAndSettle();

        // Should not crash and should still show the login screen
        expect(find.byType(LoginScreen), findsOneWidget);
      });

      testWidgets('should show loading state during Apple sign-in', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
              globalLoadingProvider.overrideWith((ref) => false),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final appleButton = find.text('Continue with Apple');

        // Tap Apple sign-in button
        await tester.tap(appleButton);
        await tester.pump(); // Don't settle to catch loading state

        // Should trigger Apple sign-in flow (currently shows TODO)
        expect(appleButton, findsOneWidget);
      });

      testWidgets('should handle successful login process', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final emailField = find.byType(AuthFormField).first;
        final passwordField = find.byType(AuthFormField).last;
        final signInButton = find.text('Sign In');

        // Enter valid credentials and submit
        await tester.enterText(emailField, '<EMAIL>');
        await tester.enterText(passwordField, 'password123');
        await tester.tap(signInButton);
        await tester.pumpAndSettle();

        // Should complete without crashing
        expect(find.byType(LoginScreen), findsOneWidget);
      });

      testWidgets('should handle successful Google sign-in process', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
            surfaceSize: const Size(
              800,
              1200,
            ), // Larger screen to fit all content
          ),
        );

        await tester.pumpAndSettle();

        // Scroll to make Google button visible
        await tester.drag(
          find.byType(SingleChildScrollView),
          const Offset(0, -400),
        );
        await tester.pumpAndSettle();

        final googleButton = find.text('Continue with Google').first;
        await tester.tap(googleButton, warnIfMissed: false);
        await tester.pumpAndSettle();

        // Should complete without crashing
        expect(find.byType(LoginScreen), findsOneWidget);
      });
    });

    group('Biometric Authentication Integration', () {
      testWidgets('should show biometric auth button when available', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Should show biometric auth button
        expect(find.byType(BiometricAuthButton), findsOneWidget);
      });

      testWidgets('should handle biometric authentication components', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Should show login screen with biometric components available
        expect(find.byType(LoginScreen), findsOneWidget);

        // Should not crash when biometric authentication is available
        expect(
          find.byType(BiometricAuthNotifier),
          findsNothing,
        ); // This is a provider, not a widget
      });

      testWidgets('should handle successful biometric authentication', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final biometricButton = find.byType(BiometricAuthButton);

        // Simulate successful biometric auth by tapping the button
        // The button's onSuccess callback should be triggered
        final biometricButtonWidget = tester.widget<BiometricAuthButton>(
          biometricButton,
        );

        // Test that the button has proper callbacks
        expect(biometricButtonWidget.onSuccess, isNotNull);
        expect(biometricButtonWidget.onError, isNotNull);
      });
    });

    group('Navigation Functionality', () {
      testWidgets('should navigate to signup when signup link is tapped', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
            initialLocation: '/test',
          ),
        );

        await tester.pumpAndSettle();

        final signUpLink = find.text('Sign Up');
        expect(signUpLink, findsOneWidget);

        // Tapping should trigger navigation (tested through widget structure)
        await tester.tap(signUpLink);
        await tester.pumpAndSettle();
      });

      testWidgets('should navigate to forgot password when link is tapped', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
            initialLocation: '/test',
          ),
        );

        await tester.pumpAndSettle();

        final forgotPasswordLink = find.text('Forgot Password?');
        expect(forgotPasswordLink, findsOneWidget);

        // Tapping should trigger navigation (tested through widget structure)
        await tester.tap(forgotPasswordLink);
        await tester.pumpAndSettle();
      });
    });

    group('Accessibility and Usability', () {
      testWidgets('should have proper focus flow between form fields', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final formFields = find.byType(AuthFormField);
        final emailField = formFields.first;
        final passwordField = formFields.last;

        // Test focus on email field
        await tester.tap(emailField);
        await tester.pump();

        // Test focus movement to password field
        await tester.testTextInput.receiveAction(TextInputAction.next);
        await tester.pump();

        // Both fields should be focusable
        expect(emailField, findsOneWidget);
        expect(passwordField, findsOneWidget);
      });

      testWidgets('should have proper semantic labels', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Check that labels are present
        expect(find.text('Email'), findsOneWidget);
        expect(find.text('Password'), findsOneWidget);
        expect(find.text('Sign In'), findsOneWidget);
      });

      testWidgets('should handle large text sizes properly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
            theme: ThemeData(
              textTheme: const TextTheme(
                bodyLarge: TextStyle(fontSize: 24),
                bodyMedium: TextStyle(fontSize: 20),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should render without overflow
        expect(find.byType(LoginScreen), findsOneWidget);
        expect(find.byType(SingleChildScrollView), findsOneWidget);
      });
    });

    group('Edge Cases and Error Recovery', () {
      testWidgets('should handle rapid button taps gracefully', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final emailField = find.byType(AuthFormField).first;
        final passwordField = find.byType(AuthFormField).last;
        final signInButton = find.text('Sign In');

        // Enter valid credentials
        await tester.enterText(emailField, '<EMAIL>');
        await tester.enterText(passwordField, 'password123');

        // Rapidly tap sign in button
        await tester.tap(signInButton);
        await tester.pump();
        await tester.tap(signInButton);
        await tester.pump();
        await tester.tap(signInButton);
        await tester.pumpAndSettle();

        // Should handle gracefully without crashes
        expect(find.byType(LoginScreen), findsOneWidget);
      });

      testWidgets('should handle keyboard dismissal properly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final emailField = find.byType(AuthFormField).first;

        // Focus and then unfocus
        await tester.tap(emailField);
        await tester.pump();

        // Tap outside to dismiss keyboard
        await tester.tapAt(const Offset(10, 10));
        await tester.pump();

        // Should handle gracefully
        expect(find.byType(LoginScreen), findsOneWidget);
      });

      testWidgets('should handle widget disposal correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify widget is rendered
        expect(find.byType(LoginScreen), findsOneWidget);

        // Replace with empty widget to trigger disposal
        await tester.pumpWidget(Container());
        await tester.pumpAndSettle();

        // Should dispose cleanly
        expect(find.byType(LoginScreen), findsNothing);
      });
    });

    group('Apple Sign-In Placeholder', () {
      testWidgets('should handle Apple sign-in button interaction', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
              globalLoadingProvider.overrideWith((ref) => false),
            ],
            surfaceSize: const Size(
              800,
              1200,
            ), // Larger screen to fit all content
          ),
        );

        await tester.pumpAndSettle();

        // Scroll to make Apple button visible
        await tester.drag(
          find.byType(SingleChildScrollView),
          const Offset(0, -400),
        );
        await tester.pumpAndSettle();

        final appleButton = find.text('Continue with Apple').first;
        expect(appleButton, findsOneWidget);

        // Tap Apple button
        await tester.tap(appleButton, warnIfMissed: false);
        await tester.pumpAndSettle();

        // Should not crash and should still show the login screen
        expect(find.byType(LoginScreen), findsOneWidget);
      });

      testWidgets('should handle Apple sign-in errors', (tester) async {
        // Override with error-throwing Apple sign-in
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            overrides: [
              loginNotifierProvider.overrideWith(() => testLoginNotifier),
              googleSignInNotifierProvider.overrideWith(
                () => testGoogleSignInNotifier,
              ),
              biometricAuthNotifierProvider.overrideWith(
                () => testBiometricAuthNotifier,
              ),
              globalLoadingProvider.overrideWith((ref) => false),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final appleButton = find.text('Continue with Apple');

        // Test that Apple button exists and is tappable
        expect(appleButton, findsOneWidget);
        await tester.tap(appleButton);
        await tester.pumpAndSettle();
      });
    });
  });
}
