import 'package:budapp/features/auth/presentation/screens/login_screen.dart';
import 'package:budapp/features/auth/presentation/widgets/auth_button.dart';
import 'package:budapp/features/auth/presentation/widgets/auth_form_field.dart';
import 'package:budapp/features/auth/presentation/widgets/social_login_button.dart';
import 'package:budapp/widgets/performance_tracked_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/test_wrapper.dart';

void main() {
  group('LoginScreen Tests', () {
    group('UI Rendering Tests', () {
      testWidgets('should render login screen with essential elements', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Verify app logo and title are present
        expect(find.byIcon(Icons.account_balance_wallet), findsOneWidget);
        expect(find.text('Welcome Back'), findsOneWidget);

        // Verify form fields exist
        expect(
          find.byType(AuthFormField),
          findsNWidgets(2),
        ); // Email and password

        // Verify buttons exist
        expect(find.byType(AuthButton), findsOneWidget); // Sign In button
        expect(
          find.byType(SocialLoginButton),
          findsNWidgets(2),
        ); // Google and Apple
        expect(find.text('Sign In'), findsOneWidget);

        // Verify navigation links
        expect(find.text('Forgot Password?'), findsOneWidget);
        expect(find.text('Sign Up'), findsOneWidget);
      });

      testWidgets('should show performance tracking wrapper', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Should be wrapped in PerformanceTrackedScreen
        expect(find.byType(PerformanceTrackedScreen), findsOneWidget);
      });

      testWidgets('should render SafeArea and SingleChildScrollView', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Verify proper layout structure
        expect(find.byType(SafeArea), findsOneWidget);
        expect(find.byType(SingleChildScrollView), findsOneWidget);
        expect(find.byType(Column), findsWidgets);
      });
    });

    group('Form Validation Tests', () {
      testWidgets('should validate empty email field', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Tap sign in button without entering email
        final signInButton = find.text('Sign In');
        await tester.tap(signInButton);
        await tester.pumpAndSettle();

        // Should show email validation error
        expect(find.text('Email is required'), findsOneWidget);
      });

      testWidgets('should validate invalid email format', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Enter invalid email
        final emailFields = find.byType(AuthFormField);
        final emailField = emailFields.first;
        await tester.enterText(emailField, 'invalid-email');

        // Tap sign in button
        final signInButton = find.text('Sign In');
        await tester.tap(signInButton);
        await tester.pumpAndSettle();

        // Should show email format validation error
        expect(find.text('Please enter a valid email address'), findsOneWidget);
      });

      testWidgets('should validate empty password field', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Enter valid email but no password
        final formFields = find.byType(AuthFormField);
        await tester.enterText(formFields.first, '<EMAIL>');

        // Tap sign in button
        final signInButton = find.text('Sign In');
        await tester.tap(signInButton);
        await tester.pumpAndSettle();

        // Should show password validation error
        expect(find.text('Password is required'), findsOneWidget);
      });

      testWidgets('should accept valid form data', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Enter valid credentials
        final formFields = find.byType(AuthFormField);
        await tester.enterText(formFields.first, '<EMAIL>');
        await tester.enterText(formFields.last, 'password123');

        // Tap sign in button
        final signInButton = find.text('Sign In');
        await tester.tap(signInButton);
        await tester.pumpAndSettle();

        // Should attempt authentication (no validation errors shown)
        expect(find.text('Email is required'), findsNothing);
        expect(find.text('Password is required'), findsNothing);
        expect(find.text('Please enter a valid email address'), findsNothing);
      });
    });

    group('Navigation Tests', () {
      testWidgets('should show forgot password link', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Should show forgot password link
        final forgotPasswordLink = find.text('Forgot Password?');
        expect(forgotPasswordLink, findsOneWidget);
      });

      testWidgets('should show create account link', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Should show create account link
        final createAccountLink = find.text('Sign Up');
        expect(createAccountLink, findsOneWidget);
      });
    });

    group('Social Authentication Tests', () {
      testWidgets('should show Google login button', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Should show Google login button
        expect(find.text('Continue with Google'), findsOneWidget);

        // Button should be tappable
        final googleButton = find.text('Continue with Google');
        await tester.tap(googleButton);
        await tester.pumpAndSettle();
      });

      testWidgets('should show Apple login button', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Should show Apple login button
        expect(find.text('Continue with Apple'), findsOneWidget);

        // Button should be tappable
        final appleButton = find.text('Continue with Apple');
        await tester.tap(appleButton);
        await tester.pumpAndSettle();
      });
    });

    group('Widget Structure Tests', () {
      testWidgets('should have proper form structure', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Should have Form widget
        expect(find.byType(Form), findsOneWidget);

        // Should have proper text controllers
        expect(find.byType(AuthFormField), findsNWidgets(2));
      });

      testWidgets('should maintain widget hierarchy', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Verify the widget tree structure (note: TestWrapper adds its own Scaffold)
        expect(
          find.byType(Scaffold),
          findsNWidgets(2),
        ); // TestWrapper + LoginScreen Scaffolds
        expect(find.byType(SafeArea), findsOneWidget);
        expect(find.byType(SingleChildScrollView), findsOneWidget);
        expect(find.byType(Column), findsAtLeastNWidgets(1));
      });

      testWidgets('should dispose controllers properly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Verify widget is rendered
        expect(find.byType(LoginScreen), findsOneWidget);

        // Remove widget to trigger disposal
        await tester.pumpWidget(Container());
        await tester.pumpAndSettle();

        // Widget should be cleanly removed
        expect(find.byType(LoginScreen), findsNothing);
      });
    });

    group('Theme and Styling Tests', () {
      testWidgets('should apply proper styling to logo container', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Should have properly styled logo container
        final logoContainer = find.byType(Container).first;
        expect(logoContainer, findsOneWidget);
      });

      testWidgets('should display proper text content', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Verify all text content is present
        expect(find.text('Welcome Back'), findsOneWidget);
        expect(find.text('Sign in to your account'), findsOneWidget);
        expect(find.text('Email'), findsOneWidget);
        expect(find.text('Password'), findsOneWidget);
        expect(find.text('Sign In'), findsOneWidget);
        expect(find.text('Continue with Google'), findsOneWidget);
        expect(find.text('Continue with Apple'), findsOneWidget);
        expect(find.text('Forgot Password?'), findsOneWidget);
        expect(find.text('Sign Up'), findsOneWidget);
      });
    });

    group('Widget Interaction Tests', () {
      testWidgets('should handle form field input', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Test email field input
        final formFields = find.byType(AuthFormField);
        await tester.enterText(formFields.first, '<EMAIL>');
        expect(find.text('<EMAIL>'), findsOneWidget);

        // Test password field input
        await tester.enterText(formFields.last, 'password123');
        expect(find.text('password123'), findsOneWidget);
      });

      testWidgets('should render interactive buttons correctly', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const LoginScreen(),
            surfaceSize: const Size(
              400,
              800,
            ), // Taller viewport for scrollable content
          ),
        );

        await tester.pumpAndSettle();

        // Test sign in button exists and is tappable
        final signInButton = find.text('Sign In');
        expect(signInButton, findsOneWidget);

        // Test social login buttons exist
        final googleButton = find.text('Continue with Google');
        expect(googleButton, findsOneWidget);

        final appleButton = find.text('Continue with Apple');
        expect(appleButton, findsOneWidget);

        // Test that buttons are actually interactive (not just decoration)
        final signInButtonWidget = tester.widget<ElevatedButton>(
          find.ancestor(
            of: signInButton,
            matching: find.byType(ElevatedButton),
          ),
        );
        expect(signInButtonWidget.onPressed, isNotNull);
      });

      testWidgets('should handle non-navigation interactions', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const LoginScreen()),
        );

        await tester.pumpAndSettle();

        // Test form field interactions
        final formFields = find.byType(AuthFormField);
        expect(formFields, findsNWidgets(2));

        // Test that fields can receive focus
        await tester.tap(formFields.first);
        await tester.pump();
      });
    });
  });
}
