import 'package:budapp/features/auth/presentation/screens/email_verification_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/mock_data_factory.dart';
import '../../../../helpers/mock_providers.dart';
import '../../../../helpers/test_wrapper.dart';

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(MockProviders.mockUser);
    registerFallbackValue(MockDataFactory.createTransaction());
    registerFallbackValue(MockDataFactory.createAccount());
    registerFallbackValue(MockDataFactory.createCategory());
    registerFallbackValue(MockDataFactory.createUserProfile());
  });

  setUp(() {
    MockProviders.resetMocks();
    MockProviders.setupDefaultMocks();
  });

  group('EmailVerificationScreen Tests', () {
    testWidgets('should instantiate without errors', (
      WidgetTester tester,
    ) async {
      // Simply test that the widget can be created without crashing
      const widget = EmailVerificationScreen();
      expect(widget, isA<EmailVerificationScreen>());
      expect(widget.key, isNull);
    });

    testWidgets('should be a ConsumerStatefulWidget', (
      WidgetTester tester,
    ) async {
      // Test the widget type hierarchy
      const widget = EmailVerificationScreen();
      expect(widget, isA<StatefulWidget>());
    });

    testWidgets('should create state correctly', (WidgetTester tester) async {
      // Test that createState returns the correct state type
      const widget = EmailVerificationScreen();
      final state = widget.createState();
      expect(state, isNotNull);
      expect(state.toString().contains('EmailVerificationScreenState'), isTrue);
    });

    testWidgets('should render basic UI elements', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const EmailVerificationScreen(),
          overrides: MockProviders.authenticatedUserOverrides(),
        ),
      );

      // Verify basic UI elements are present
      expect(find.text('Verify Email'), findsOneWidget);
      expect(find.text('Sign Out'), findsOneWidget);
      expect(find.byIcon(Icons.mark_email_unread_outlined), findsOneWidget);
    });

    testWidgets('should handle widget lifecycle correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const EmailVerificationScreen(),
          overrides: MockProviders.authenticatedUserOverrides(),
        ),
      );

      // Verify the widget renders without errors
      expect(find.byType(EmailVerificationScreen), findsOneWidget);

      // Test widget disposal
      await tester.pumpWidget(Container());
      expect(find.byType(EmailVerificationScreen), findsNothing);
    });
  });
}
