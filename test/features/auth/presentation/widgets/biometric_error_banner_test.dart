import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/auth/presentation/widgets/biometric_error_banner.dart';
import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:budapp/features/auth/services/biometric_error_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/test_wrapper.dart';

void main() {
  group('BiometricErrorBanner', () {
    // Helper method to create AuthError instances for testing
    AuthError createTestError({
      String code = 'biometric_test_error',
      String userMessage = 'Test error message',
      String technicalMessage = 'Technical test error',
      ErrorSeverity severity = ErrorSeverity.error,
      ErrorCategory category = ErrorCategory.biometric,
      bool isRetryable = true,
    }) {
      return AuthError(
        code: code,
        userMessage: userMessage,
        technicalMessage: technicalMessage,
        severity: severity,
        category: category,
        isRetryable: isRetryable,
      );
    }

    Widget createWidget({
      required AuthError error,
      VoidCallback? onRetry,
      VoidCallback? onDismiss,
      VoidCallback? onSetupBiometrics,
      bool showActions = true,
    }) {
      return TestWrapper.createTestWidget(
        Scaffold(
          body: BiometricErrorBanner(
            error: error,
            onRetry: onRetry,
            onDismiss: onDismiss,
            onSetupBiometrics: onSetupBiometrics,
            showActions: showActions,
          ),
        ),
      );
    }

    group('Rendering', () {
      testWidgets('should display error message', (tester) async {
        final error = createTestError(
          userMessage: 'Biometric authentication failed',
        );

        await tester.pumpWidget(createWidget(error: error));

        expect(find.text('Biometric authentication failed'), findsOneWidget);
      });

      testWidgets('should display error icon', (tester) async {
        final error = createTestError();

        await tester.pumpWidget(createWidget(error: error));

        // Check that icon is present (emoji text)
        final iconText = BiometricErrorService.getErrorIcon(error.code);
        expect(find.text(iconText), findsOneWidget);
      });

      testWidgets('should display dismiss button when onDismiss provided', (
        tester,
      ) async {
        var dismissCalled = false;
        final error = createTestError();

        await tester.pumpWidget(
          createWidget(error: error, onDismiss: () => dismissCalled = true),
        );

        expect(find.byIcon(Icons.close), findsOneWidget);

        await tester.tap(find.byIcon(Icons.close));
        expect(dismissCalled, isTrue);
      });

      testWidgets(
        'should not display dismiss button when onDismiss not provided',
        (tester) async {
          final error = createTestError();

          await tester.pumpWidget(createWidget(error: error));

          expect(find.byIcon(Icons.close), findsNothing);
        },
      );

      testWidgets('should not show actions when showActions is false', (
        tester,
      ) async {
        final error = createTestError(isRetryable: true);

        await tester.pumpWidget(
          createWidget(error: error, showActions: false, onRetry: () {}),
        );

        expect(find.text('Try Again'), findsNothing);
      });
    });

    group('Error Titles', () {
      testWidgets('should display correct title for passcode not set', (
        tester,
      ) async {
        final error = createTestError(code: 'biometric_passcode_not_set');

        await tester.pumpWidget(createWidget(error: error));

        expect(find.text('Passcode Required'), findsOneWidget);
      });

      testWidgets('should display correct title for not enrolled', (
        tester,
      ) async {
        final error = createTestError(code: 'biometric_not_enrolled');

        await tester.pumpWidget(createWidget(error: error));

        expect(find.text('Biometric Setup Required'), findsOneWidget);
      });

      testWidgets('should display correct title for not available', (
        tester,
      ) async {
        final error = createTestError(code: 'biometric_not_available');

        await tester.pumpWidget(createWidget(error: error));

        expect(find.text('Biometrics Unavailable'), findsOneWidget);
      });

      testWidgets('should display correct title for locked out', (
        tester,
      ) async {
        final error = createTestError(code: 'biometric_locked_out');

        await tester.pumpWidget(createWidget(error: error));

        expect(find.text('Biometrics Locked'), findsOneWidget);
      });

      testWidgets('should display default title for unknown error', (
        tester,
      ) async {
        final error = createTestError(code: 'unknown_error');

        await tester.pumpWidget(createWidget(error: error));

        expect(find.text('Biometric Authentication'), findsOneWidget);
      });
    });

    group('Color Themes by Severity', () {
      testWidgets('should apply error colors for error severity', (
        tester,
      ) async {
        final error = createTestError(severity: ErrorSeverity.error);

        await tester.pumpWidget(createWidget(error: error));

        final container = tester.widget<Container>(find.byType(Container));
        final decoration = container.decoration! as BoxDecoration;

        expect(decoration.color, isNotNull);
        expect(decoration.border, isA<Border>());
      });

      testWidgets('should apply warning colors for warning severity', (
        tester,
      ) async {
        final error = createTestError(severity: ErrorSeverity.warning);

        await tester.pumpWidget(createWidget(error: error));

        final container = tester.widget<Container>(find.byType(Container));
        final decoration = container.decoration! as BoxDecoration;

        expect(decoration.color, isNotNull);
        expect(decoration.border, isA<Border>());
      });

      testWidgets('should apply info colors for info severity', (tester) async {
        final error = createTestError(severity: ErrorSeverity.info);

        await tester.pumpWidget(createWidget(error: error));

        final container = tester.widget<Container>(find.byType(Container));
        final decoration = container.decoration! as BoxDecoration;

        expect(decoration.color, isNotNull);
        expect(decoration.border, isA<Border>());
      });

      testWidgets('should apply critical colors for critical severity', (
        tester,
      ) async {
        final error = createTestError(severity: ErrorSeverity.critical);

        await tester.pumpWidget(createWidget(error: error));

        final container = tester.widget<Container>(find.byType(Container));
        final decoration = container.decoration! as BoxDecoration;

        expect(decoration.color, isNotNull);
        expect(decoration.border, isA<Border>());
      });
    });

    group('Action Buttons', () {
      testWidgets('should show retry button for retryable errors', (
        tester,
      ) async {
        var retryCalled = false;
        final error = createTestError(isRetryable: true);

        await tester.pumpWidget(
          createWidget(error: error, onRetry: () => retryCalled = true),
        );

        expect(find.text('Try Again'), findsOneWidget);

        await tester.tap(find.text('Try Again'));
        expect(retryCalled, isTrue);
      });

      testWidgets('should not show retry button for non-retryable errors', (
        tester,
      ) async {
        final error = createTestError(isRetryable: false);

        await tester.pumpWidget(createWidget(error: error, onRetry: () {}));

        expect(find.text('Try Again'), findsNothing);
      });

      testWidgets(
        'should show setup passcode button for setupPasscode action',
        (tester) async {
          final error = createTestError(code: 'biometric_passcode_not_set');

          await tester.pumpWidget(createWidget(error: error));

          expect(find.text('Go to Settings'), findsOneWidget);
        },
      );

      testWidgets(
        'should show setup biometrics button for enrollBiometric action',
        (tester) async {
          var setupCalled = false;
          final error = createTestError(code: 'biometric_not_enrolled');

          await tester.pumpWidget(
            createWidget(
              error: error,
              onSetupBiometrics: () => setupCalled = true,
            ),
          );

          expect(find.text('Set up biometrics'), findsOneWidget);

          await tester.tap(find.text('Set up biometrics'));
          expect(setupCalled, isTrue);
        },
      );

      testWidgets(
        'should show try again later button for waitAndRetry action',
        (tester) async {
          var retryCalled = false;
          final error = createTestError(code: 'biometric_locked_out');

          await tester.pumpWidget(
            createWidget(error: error, onRetry: () => retryCalled = true),
          );

          expect(find.text('Try Again Later'), findsOneWidget);

          await tester.tap(find.text('Try Again Later'));
          expect(retryCalled, isTrue);
        },
      );

      testWidgets('should handle useAlternativeAuth action', (tester) async {
        final error = createTestError(code: 'biometric_permanently_locked_out');

        await tester.pumpWidget(createWidget(error: error));

        // This action doesn't add specific buttons, just verifies no crash
        expect(find.byType(BiometricErrorBanner), findsOneWidget);
      });
    });

    group('Layout and Styling', () {
      testWidgets('should have proper margin and padding', (tester) async {
        final error = createTestError();

        await tester.pumpWidget(createWidget(error: error));

        final container = tester.widget<Container>(find.byType(Container));
        expect(
          container.margin,
          equals(const EdgeInsets.only(bottom: AppSpacing.md)),
        );
        expect(container.padding, equals(const EdgeInsets.all(AppSpacing.md)));
      });

      testWidgets('should have rounded corners', (tester) async {
        final error = createTestError();

        await tester.pumpWidget(createWidget(error: error));

        final container = tester.widget<Container>(find.byType(Container));
        final decoration = container.decoration! as BoxDecoration;
        expect(
          decoration.borderRadius,
          equals(BorderRadius.circular(AppBorderRadius.md)),
        );
      });

      testWidgets('should position action buttons at the end', (tester) async {
        final error = createTestError(isRetryable: true);

        await tester.pumpWidget(createWidget(error: error, onRetry: () {}));

        final row = tester.widget<Row>(find.byType(Row).last);
        expect(row.mainAxisAlignment, equals(MainAxisAlignment.end));
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle null callbacks gracefully', (tester) async {
        final error = createTestError(isRetryable: true);

        await tester.pumpWidget(createWidget(error: error));

        // Should not show retry button when callback is null
        expect(find.text('Try Again'), findsNothing);
      });

      testWidgets('should handle empty error message', (tester) async {
        final error = createTestError(userMessage: '');

        await tester.pumpWidget(createWidget(error: error));

        expect(
          find.text(''),
          findsAtLeast(1),
        ); // Empty text should still be rendered
      });

      testWidgets('should handle very long error messages', (tester) async {
        final longMessage = 'A' * 500;
        final error = createTestError(userMessage: longMessage);

        await tester.pumpWidget(createWidget(error: error));

        expect(find.text(longMessage), findsOneWidget);
      });

      testWidgets(
        'should not show actions when no action required and not retryable',
        (tester) async {
          final error = createTestError(
            isRetryable: false,
            code: 'biometric_unknown_error', // This won't have specific actions
          );

          await tester.pumpWidget(createWidget(error: error));

          // Should not find any action buttons
          expect(find.text('Try Again'), findsNothing);
          expect(find.text('Go to Settings'), findsNothing);
          expect(find.text('Set up biometrics'), findsNothing);
        },
      );
    });

    group('Action Button Widget', () {
      testWidgets('should render primary button correctly', (tester) async {
        final error = createTestError(code: 'biometric_passcode_not_set');

        await tester.pumpWidget(createWidget(error: error));

        expect(find.byType(FilledButton), findsOneWidget);
        expect(find.text('Go to Settings'), findsOneWidget);
      });

      testWidgets('should render secondary button correctly', (tester) async {
        final error = createTestError(code: 'biometric_locked_out');

        await tester.pumpWidget(createWidget(error: error, onRetry: () {}));

        expect(find.byType(OutlinedButton), findsOneWidget);
        expect(find.text('Try Again Later'), findsOneWidget);
      });

      testWidgets('should handle button press correctly', (tester) async {
        var buttonPressed = false;
        final error = createTestError(code: 'biometric_passcode_not_set');

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(
              body: BiometricErrorBanner(
                error: error,
                onSetupBiometrics: () => buttonPressed = true,
              ),
            ),
          ),
        );

        await tester.tap(find.text('Go to Settings'));
        expect(
          buttonPressed,
          isFalse,
        ); // Should call _openDeviceSettings, not onSetupBiometrics

        // Test that the button exists and is tappable
        expect(find.byType(FilledButton), findsOneWidget);
      });
    });
  });
}
