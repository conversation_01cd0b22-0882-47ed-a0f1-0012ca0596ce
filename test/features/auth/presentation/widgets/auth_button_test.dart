import 'package:budapp/features/auth/presentation/widgets/auth_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/test_wrapper.dart';

void main() {
  group('AuthButton Tests', () {
    group('Primary Button', () {
      testWidgets('should render primary button correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const AuthButton(text: 'Sign In')),
        );

        expect(find.byType(ElevatedButton), findsOneWidget);
        expect(find.text('Sign In'), findsOneWidget);
      });

      testWidgets('should handle tap when enabled', (tester) async {
        var tapped = false;

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthButton(text: 'Sign In', onPressed: () => tapped = true),
          ),
        );

        await tester.tap(find.byType(ElevatedButton));
        expect(tapped, isTrue);
      });

      testWidgets('should be disabled when onPressed is null', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const AuthButton(text: 'Sign In')),
        );

        final button = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(button.onPressed, isNull);
      });

      testWidgets('should show loading indicator when loading', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthButton(text: 'Sign In', isLoading: true, onPressed: () {}),
          ),
        );

        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.text('Sign In'), findsNothing);
      });

      testWidgets('should be disabled when loading', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthButton(text: 'Sign In', isLoading: true, onPressed: () {}),
          ),
        );

        final button = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(button.onPressed, isNull);
      });

      testWidgets('should display icon when provided', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthButton(text: 'Sign In', icon: Icons.login, onPressed: () {}),
          ),
        );

        expect(find.byIcon(Icons.login), findsOneWidget);
        expect(find.text('Sign In'), findsOneWidget);
      });

      testWidgets('should have correct dimensions', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthButton(text: 'Sign In', onPressed: () {}),
          ),
        );

        final sizedBox = tester.widget<SizedBox>(
          find.ancestor(
            of: find.byType(ElevatedButton),
            matching: find.byType(SizedBox),
          ),
        );

        expect(sizedBox.width, equals(double.infinity));
        expect(sizedBox.height, equals(48));
      });
    });

    group('Secondary Button', () {
      testWidgets('should render secondary button correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthButton(text: 'Cancel', isSecondary: true, onPressed: () {}),
          ),
        );

        expect(find.byType(OutlinedButton), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);
      });

      testWidgets('should handle tap when enabled', (tester) async {
        var tapped = false;

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthButton(
              text: 'Cancel',
              isSecondary: true,
              onPressed: () => tapped = true,
            ),
          ),
        );

        await tester.tap(find.byType(OutlinedButton));
        expect(tapped, isTrue);
      });

      testWidgets('should show loading indicator when loading', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthButton(
              text: 'Cancel',
              isSecondary: true,
              isLoading: true,
              onPressed: () {},
            ),
          ),
        );

        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.text('Cancel'), findsNothing);
      });

      testWidgets('should display icon when provided', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthButton(
              text: 'Cancel',
              isSecondary: true,
              icon: Icons.cancel,
              onPressed: () {},
            ),
          ),
        );

        expect(find.byIcon(Icons.cancel), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);
      });
    });

    group('Button Content Layout', () {
      testWidgets('should layout icon and text correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthButton(
              text: 'Sign In with Google',
              icon: Icons.login,
              onPressed: () {},
            ),
          ),
        );

        // Should have Row layout when icon is present
        expect(find.byType(Row), findsOneWidget);
        expect(find.byIcon(Icons.login), findsOneWidget);
        expect(find.text('Sign In with Google'), findsOneWidget);
      });

      testWidgets('should show only text when no icon', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthButton(text: 'Sign In', onPressed: () {}),
          ),
        );

        // Should have Text widget only
        expect(find.byType(Row), findsNothing);
        expect(find.text('Sign In'), findsOneWidget);
      });

      testWidgets('should show loading indicator with correct color', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthButton(text: 'Sign In', isLoading: true, onPressed: () {}),
          ),
        );

        final progressIndicator = tester.widget<CircularProgressIndicator>(
          find.byType(CircularProgressIndicator),
        );

        expect(progressIndicator.strokeWidth, equals(2));
        expect(
          progressIndicator.valueColor,
          isA<AlwaysStoppedAnimation<Color>>(),
        );
      });
    });

    group('Widget Structure Tests', () {
      testWidgets('should maintain consistent structure', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthButton(text: 'Test Button', onPressed: () {}),
          ),
        );

        // Verify widget hierarchy
        expect(find.byType(SizedBox), findsOneWidget);
        expect(find.byType(ElevatedButton), findsOneWidget);
      });

      testWidgets('should handle state changes correctly', (tester) async {
        var isLoading = false;

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            StatefulBuilder(
              builder: (context, setState) => AuthButton(
                text: 'Dynamic Button',
                isLoading: isLoading,
                onPressed: () {
                  setState(() {
                    isLoading = !isLoading;
                  });
                },
              ),
            ),
          ),
        );

        // Initially not loading
        expect(find.text('Dynamic Button'), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsNothing);

        // Tap to start loading
        await tester.tap(find.byType(ElevatedButton));
        await tester.pump();

        // Should show loading state
        expect(find.text('Dynamic Button'), findsNothing);
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });
    });
  });

  group('AuthTextButton Tests', () {
    testWidgets('should render text button correctly', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const AuthTextButton(text: 'Forgot Password?'),
        ),
      );

      expect(find.byType(TextButton), findsOneWidget);
      expect(find.text('Forgot Password?'), findsOneWidget);
    });

    testWidgets('should handle tap when enabled', (tester) async {
      var tapped = false;

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          AuthTextButton(
            text: 'Forgot Password?',
            onPressed: () => tapped = true,
          ),
        ),
      );

      await tester.tap(find.byType(TextButton));
      expect(tapped, isTrue);
    });

    testWidgets('should be disabled when onPressed is null', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const AuthTextButton(text: 'Forgot Password?'),
        ),
      );

      final button = tester.widget<TextButton>(find.byType(TextButton));
      expect(button.onPressed, isNull);
    });

    testWidgets('should show underline when isUnderlined is true', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          AuthTextButton(
            text: 'Forgot Password?',
            isUnderlined: true,
            onPressed: () {},
          ),
        ),
      );

      // Find the Text widget and check its style
      final textWidget = tester.widget<Text>(find.text('Forgot Password?'));
      expect(textWidget.style?.decoration, equals(TextDecoration.underline));
    });

    testWidgets('should not show underline when isUnderlined is false', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          AuthTextButton(
            text: 'Forgot Password?',
            isUnderlined: false,
            onPressed: () {},
          ),
        ),
      );

      // Find the Text widget and check its style
      final textWidget = tester.widget<Text>(find.text('Forgot Password?'));
      expect(
        textWidget.style?.decoration,
        isNot(equals(TextDecoration.underline)),
      );
    });

    testWidgets('should use correct styling', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          AuthTextButton(text: 'Test Link', onPressed: () {}),
        ),
      );

      final textWidget = tester.widget<Text>(find.text('Test Link'));
      expect(textWidget.style, isNotNull);
    });

    testWidgets('should have minimal tap target', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          AuthTextButton(text: 'Test Link', onPressed: () {}),
        ),
      );

      final textButton = tester.widget<TextButton>(find.byType(TextButton));
      expect(textButton.style, isNotNull);
    });
  });

  group('Edge Cases and Error Handling', () {
    testWidgets('should handle empty text gracefully', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(AuthButton(text: '', onPressed: () {})),
      );

      expect(find.byType(ElevatedButton), findsOneWidget);
      expect(find.text(''), findsOneWidget);
    });

    testWidgets('should handle very long text', (tester) async {
      const longText =
          'This is a very long button text that might cause layout issues if not handled properly';

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          AuthButton(text: longText, onPressed: () {}),
        ),
      );

      expect(find.byType(ElevatedButton), findsOneWidget);
      expect(find.text(longText), findsOneWidget);
    });

    testWidgets('should handle multiple state changes', (tester) async {
      var callCount = 0;

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          AuthButton(text: 'Multi Tap', onPressed: () => callCount++),
        ),
      );

      // Multiple taps
      await tester.tap(find.byType(ElevatedButton));
      await tester.tap(find.byType(ElevatedButton));
      await tester.tap(find.byType(ElevatedButton));

      expect(callCount, equals(3));
    });

    testWidgets('should handle rebuild correctly', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          AuthButton(text: 'Test', onPressed: () {}),
        ),
      );

      // Initial render
      expect(find.text('Test'), findsOneWidget);

      // Rebuild with different text
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          AuthButton(text: 'Updated Test', onPressed: () {}),
        ),
      );

      expect(find.text('Updated Test'), findsOneWidget);
      expect(find.text('Test'), findsNothing);
    });
  });
}
