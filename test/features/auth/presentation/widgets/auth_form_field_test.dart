import 'package:budapp/features/auth/presentation/widgets/auth_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/test_wrapper.dart';

void main() {
  group('AuthFormField Tests', () {
    late TextEditingController controller;

    setUp(() {
      controller = TextEditingController();
    });

    tearDown(() {
      controller.dispose();
    });

    group('Basic Functionality', () {
      testWidgets('should render form field with floating label', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(label: 'Email', controller: controller),
          ),
        );

        expect(find.byType(TextFormField), findsOneWidget);
        expect(find.text('Email'), findsOneWidget);
      });

      testWidgets('should render form field with static label', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(
              label: 'Email',
              controller: controller,
              useFloatingLabel: false,
            ),
          ),
        );

        expect(find.byType(TextFormField), findsOneWidget);
        // With static label, there are two "Email" texts: the label and the hint
        expect(find.text('Email'), findsNWidgets(2));
        expect(find.byType(Column), findsOneWidget);
      });

      testWidgets('should handle text input', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(label: 'Email', controller: controller),
          ),
        );

        await tester.enterText(find.byType(TextFormField), '<EMAIL>');
        expect(controller.text, equals('<EMAIL>'));
      });

      testWidgets('should display hint text when provided', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(
              label: 'Email',
              hintText: 'Enter your email address',
              controller: controller,
            ),
          ),
        );

        expect(find.text('Enter your email address'), findsOneWidget);
      });

      testWidgets('should show prefix icon when provided', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(
              label: 'Email',
              controller: controller,
              prefixIcon: const Icon(Icons.email),
            ),
          ),
        );

        expect(find.byIcon(Icons.email), findsOneWidget);
      });

      testWidgets('should show suffix icon when provided', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(
              label: 'Username',
              controller: controller,
              suffixIcon: const Icon(Icons.check),
            ),
          ),
        );

        expect(find.byIcon(Icons.check), findsOneWidget);
      });
    });

    group('Password Field Functionality', () {
      testWidgets('should obscure text for password field', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(
              label: 'Password',
              controller: controller,
              isPassword: true,
            ),
          ),
        );

        // Check that password field has visibility toggle
        expect(find.byIcon(Icons.visibility), findsOneWidget);
      });

      testWidgets('should show visibility toggle for password field', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(
              label: 'Password',
              controller: controller,
              isPassword: true,
            ),
          ),
        );

        expect(find.byIcon(Icons.visibility), findsOneWidget);
      });

      testWidgets('should toggle password visibility when icon is tapped', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(
              label: 'Password',
              controller: controller,
              isPassword: true,
            ),
          ),
        );

        // Initially shows visibility icon (obscured)
        expect(find.byIcon(Icons.visibility), findsOneWidget);

        // Tap visibility toggle
        await tester.tap(find.byIcon(Icons.visibility));
        await tester.pumpAndSettle();

        // Should show visibility_off icon (visible)
        expect(find.byIcon(Icons.visibility_off), findsOneWidget);

        // Tap again to hide
        await tester.tap(find.byIcon(Icons.visibility_off));
        await tester.pumpAndSettle();

        // Should show visibility icon again (obscured)
        expect(find.byIcon(Icons.visibility), findsOneWidget);
      });

      testWidgets(
        'should not override suffix icon with visibility toggle for non-password fields',
        (tester) async {
          await tester.pumpWidget(
            TestWrapper.createTestWidget(
              AuthFormField(
                label: 'Username',
                controller: controller,
                isPassword: false,
                suffixIcon: const Icon(Icons.check),
              ),
            ),
          );

          expect(find.byIcon(Icons.check), findsOneWidget);
          expect(find.byIcon(Icons.visibility), findsNothing);
        },
      );
    });

    group('Validation', () {
      testWidgets('should display validation error', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Form(
              child: AuthFormField(
                label: 'Email',
                controller: controller,
                validator: (value) => 'Email is required',
              ),
            ),
          ),
        );

        // Validate form
        final formState = tester.state<FormState>(find.byType(Form));
        formState.validate();
        await tester.pumpAndSettle();

        expect(find.text('Email is required'), findsOneWidget);
      });

      testWidgets('should clear validation error when input is valid', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Form(
              child: AuthFormField(
                label: 'Email',
                controller: controller,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Email is required';
                  }
                  return null;
                },
              ),
            ),
          ),
        );

        // Validate empty field
        final formState = tester.state<FormState>(find.byType(Form));
        formState.validate();
        await tester.pumpAndSettle();

        expect(find.text('Email is required'), findsOneWidget);

        // Enter valid text
        await tester.enterText(find.byType(TextFormField), '<EMAIL>');
        formState.validate();
        await tester.pumpAndSettle();

        expect(find.text('Email is required'), findsNothing);
      });
    });

    group('Keyboard and Input Configuration', () {
      testWidgets('should render field with email keyboard type', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(
              label: 'Email',
              controller: controller,
              keyboardType: TextInputType.emailAddress,
            ),
          ),
        );

        // Just verify the field renders correctly
        expect(find.byType(TextFormField), findsOneWidget);
        expect(find.text('Email'), findsOneWidget);
      });

      testWidgets('should render field with done action', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(
              label: 'Password',
              controller: controller,
              textInputAction: TextInputAction.done,
            ),
          ),
        );

        // Just verify the field renders correctly
        expect(find.byType(TextFormField), findsOneWidget);
        expect(find.text('Password'), findsOneWidget);
      });

      testWidgets('should handle field submission', (tester) async {
        var submitted = false;

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(
              label: 'Email',
              controller: controller,
              onFieldSubmitted: (_) => submitted = true,
            ),
          ),
        );

        await tester.enterText(find.byType(TextFormField), '<EMAIL>');
        await tester.testTextInput.receiveAction(TextInputAction.done);

        expect(submitted, isTrue);
      });

      testWidgets('should render field with autofocus', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(
              label: 'Email',
              controller: controller,
              autofocus: true,
            ),
          ),
        );

        // Just verify the field renders correctly
        expect(find.byType(TextFormField), findsOneWidget);
        expect(find.text('Email'), findsOneWidget);
      });
    });

    group('Enabled/Disabled State', () {
      testWidgets('should render enabled field by default', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(label: 'Email', controller: controller),
          ),
        );

        // Just verify the field renders correctly
        expect(find.byType(TextFormField), findsOneWidget);
        expect(find.text('Email'), findsOneWidget);
      });

      testWidgets('should render disabled field when enabled is false', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(
              label: 'Email',
              controller: controller,
              enabled: false,
            ),
          ),
        );

        // Just verify the field renders correctly
        expect(find.byType(TextFormField), findsOneWidget);
        expect(find.text('Email'), findsOneWidget);
      });
    });

    group('Layout Modes', () {
      testWidgets('should use floating label layout by default', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(label: 'Email', controller: controller),
          ),
        );

        // Should not have Column wrapper for floating label
        expect(find.byType(Column), findsNothing);
        expect(find.byType(TextFormField), findsOneWidget);
      });

      testWidgets(
        'should use static label layout when useFloatingLabel is false',
        (tester) async {
          await tester.pumpWidget(
            TestWrapper.createTestWidget(
              AuthFormField(
                label: 'Email',
                controller: controller,
                useFloatingLabel: false,
              ),
            ),
          );

          // Should have Column wrapper for static label
          expect(find.byType(Column), findsOneWidget);
          expect(find.byType(TextFormField), findsOneWidget);

          // Label should be separate Text widget
          final textWidgets = find.byType(Text);
          expect(textWidgets, findsAtLeastNWidgets(1));
        },
      );
    });

    group('Widget Structure Tests', () {
      testWidgets('should maintain consistent structure', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(label: 'Test Field', controller: controller),
          ),
        );

        expect(find.byType(AuthFormField), findsOneWidget);
        expect(find.byType(TextFormField), findsOneWidget);
      });

      testWidgets('should handle rebuild correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(label: 'Original Label', controller: controller),
          ),
        );

        expect(find.text('Original Label'), findsOneWidget);

        // Rebuild with different label
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthFormField(label: 'Updated Label', controller: controller),
          ),
        );

        expect(find.text('Updated Label'), findsOneWidget);
        expect(find.text('Original Label'), findsNothing);
      });
    });
  });

  group('AuthValidators Tests', () {
    late BuildContext context;

    setUp(() {
      // We'll get context from the widget tests
    });

    group('Email Validation', () {
      testWidgets('should return error for empty email', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (BuildContext ctx) {
                context = ctx;
                return Container();
              },
            ),
          ),
        );

        final result = AuthValidators.email('', context);
        expect(result, isNotNull);
        expect(result, contains('required'));
      });

      testWidgets('should return error for null email', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (BuildContext ctx) {
                context = ctx;
                return Container();
              },
            ),
          ),
        );

        final result = AuthValidators.email(null, context);
        expect(result, isNotNull);
      });

      testWidgets('should return error for invalid email format', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (BuildContext ctx) {
                context = ctx;
                return Container();
              },
            ),
          ),
        );

        final invalidEmails = [
          'invalid-email',
          '@invalid.com',
          'invalid@',
          'invalid@.com',
          'invalid.com',
          '<EMAIL>',
        ];

        for (final email in invalidEmails) {
          final result = AuthValidators.email(email, context);
          expect(result, isNotNull, reason: 'Should reject email: $email');
        }
      });

      testWidgets('should return null for valid email', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (BuildContext ctx) {
                context = ctx;
                return Container();
              },
            ),
          ),
        );

        final validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        for (final email in validEmails) {
          final result = AuthValidators.email(email, context);
          expect(result, isNull, reason: 'Should accept email: $email');
        }
      });
    });

    group('Password Validation', () {
      testWidgets('should return error for empty password', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (BuildContext ctx) {
                context = ctx;
                return Container();
              },
            ),
          ),
        );

        final result = AuthValidators.password('', context);
        expect(result, isNotNull);
      });

      testWidgets('should return error for null password', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (BuildContext ctx) {
                context = ctx;
                return Container();
              },
            ),
          ),
        );

        final result = AuthValidators.password(null, context);
        expect(result, isNotNull);
      });

      testWidgets('should return error for short password', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (BuildContext ctx) {
                context = ctx;
                return Container();
              },
            ),
          ),
        );

        final shortPasswords = ['1', '12', '123', '1234', '12345'];

        for (final password in shortPasswords) {
          final result = AuthValidators.password(password, context);
          expect(
            result,
            isNotNull,
            reason: 'Should reject password: $password',
          );
        }
      });

      testWidgets('should return null for valid password', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (BuildContext ctx) {
                context = ctx;
                return Container();
              },
            ),
          ),
        );

        final validPasswords = [
          '123456',
          'password',
          'Password123',
          'ComplexP@ssw0rd!',
          'simple123',
        ];

        for (final password in validPasswords) {
          final result = AuthValidators.password(password, context);
          expect(result, isNull, reason: 'Should accept password: $password');
        }
      });
    });

    group('Confirm Password Validation', () {
      testWidgets('should return error for empty confirm password', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (BuildContext ctx) {
                context = ctx;
                return Container();
              },
            ),
          ),
        );

        final result = AuthValidators.confirmPassword(
          '',
          'password123',
          context,
        );
        expect(result, isNotNull);
      });

      testWidgets('should return error for null confirm password', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (BuildContext ctx) {
                context = ctx;
                return Container();
              },
            ),
          ),
        );

        final result = AuthValidators.confirmPassword(
          null,
          'password123',
          context,
        );
        expect(result, isNotNull);
      });

      testWidgets('should return error for mismatched passwords', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (BuildContext ctx) {
                context = ctx;
                return Container();
              },
            ),
          ),
        );

        final result = AuthValidators.confirmPassword(
          'password123',
          'password456',
          context,
        );
        expect(result, isNotNull);
      });

      testWidgets('should return null for matching passwords', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (BuildContext ctx) {
                context = ctx;
                return Container();
              },
            ),
          ),
        );

        final result = AuthValidators.confirmPassword(
          'password123',
          'password123',
          context,
        );
        expect(result, isNull);
      });
    });

    group('Required Field Validation', () {
      testWidgets('should return error for empty field', (tester) async {
        final result = AuthValidators.required('', 'Username');
        expect(result, equals('Username is required'));
      });

      testWidgets('should return error for null field', (tester) async {
        final result = AuthValidators.required(null, 'Username');
        expect(result, equals('Username is required'));
      });

      testWidgets('should return null for non-empty field', (tester) async {
        final result = AuthValidators.required('testuser', 'Username');
        expect(result, isNull);
      });
    });
  });

  group('Edge Cases and Error Handling', () {
    late TextEditingController controller;

    setUp(() {
      controller = TextEditingController();
    });

    tearDown(() {
      controller.dispose();
    });

    testWidgets('should handle empty label gracefully', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          AuthFormField(label: '', controller: controller),
        ),
      );

      expect(find.byType(TextFormField), findsOneWidget);
    });

    testWidgets('should handle very long labels', (tester) async {
      const longLabel =
          'This is a very long label that might cause layout issues if not handled properly';

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          AuthFormField(label: longLabel, controller: controller),
        ),
      );

      expect(find.text(longLabel), findsOneWidget);
    });

    testWidgets('should handle multiple password visibility toggles', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          AuthFormField(
            label: 'Password',
            controller: controller,
            isPassword: true,
          ),
        ),
      );

      // Toggle multiple times
      for (var i = 0; i < 5; i++) {
        await tester.tap(find.byType(IconButton));
        await tester.pumpAndSettle();
      }

      // Should still work correctly - check for visibility_off icon (visible state)
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });
  });
}
