import 'package:budapp/features/auth/presentation/widgets/biometric_auth_button.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/providers/biometric_providers.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:budapp/features/auth/services/biometric_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';

class MockBiometricService extends Mock implements BiometricService {}

class MockAuthService extends Mock implements AuthService {}

class MockUser extends Mock implements User {}

void main() {
  group('BiometricAuthButton Tests', () {
    late MockBiometricService mockBiometricService;
    late MockAuthService mockAuthService;

    setUp(() {
      mockBiometricService = MockBiometricService();
      mockAuthService = MockAuthService();

      // Set up default mock behaviors
      when(
        () => mockBiometricService.checkBiometricAvailability(),
      ).thenAnswer((_) async => BiometricAvailability.available);
      when(
        () => mockAuthService.isBiometricAuthEnabled(),
      ).thenAnswer((_) async => true);
      when(
        () => mockAuthService.signInWithBiometrics(),
      ).thenAnswer((_) async => null);
    });

    Widget createWidget({
      VoidCallback? onSuccess,
      VoidCallback? onError,
      String? customText,
      bool enabled = true,
    }) {
      return TestWrapper.createTestWidget(
        BiometricAuthButton(
          onSuccess: onSuccess,
          onError: onError,
          customText: customText,
          enabled: enabled,
        ),
        overrides: [
          biometricServiceProvider.overrideWithValue(mockBiometricService),
          authServiceProvider.overrideWithValue(mockAuthService),
        ],
      );
    }

    group('Widget Creation', () {
      testWidgets('should create widget without crashing', (tester) async {
        // Act & Assert - Just verify the widget can be created
        await tester.pumpWidget(createWidget());

        // Basic assertion that the widget tree is built
        expect(find.byType(BiometricAuthButton), findsOneWidget);
      });

      testWidgets('should handle not supported availability', (tester) async {
        // Arrange
        when(
          () => mockBiometricService.checkBiometricAvailability(),
        ).thenAnswer((_) async => BiometricAvailability.notSupported);

        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Assert - Widget should be created without crashing
        expect(find.byType(BiometricAuthButton), findsOneWidget);
      });

      testWidgets('should handle biometric auth disabled', (tester) async {
        // Arrange
        when(
          () => mockAuthService.isBiometricAuthEnabled(),
        ).thenAnswer((_) async => false);

        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Assert - Widget should be created without crashing
        expect(find.byType(BiometricAuthButton), findsOneWidget);
      });

      testWidgets('should handle custom text parameter', (tester) async {
        // Act
        await tester.pumpWidget(createWidget(customText: 'Custom Auth Text'));
        await tester.pumpAndSettle();

        // Assert - Widget should be created without crashing
        expect(find.byType(BiometricAuthButton), findsOneWidget);
      });

      testWidgets('should handle enabled parameter', (tester) async {
        // Act
        await tester.pumpWidget(createWidget(enabled: false));
        await tester.pumpAndSettle();

        // Assert - Widget should be created without crashing
        expect(find.byType(BiometricAuthButton), findsOneWidget);
      });

      testWidgets('should handle callback parameters', (tester) async {
        // Arrange
        var successCalled = false;
        var errorCalled = false;

        // Act
        await tester.pumpWidget(
          createWidget(
            onSuccess: () => successCalled = true,
            onError: () => errorCalled = true,
          ),
        );
        await tester.pumpAndSettle();

        // Assert - Widget should be created without crashing
        expect(find.byType(BiometricAuthButton), findsOneWidget);
        expect(successCalled, isFalse); // Callbacks not called yet
        expect(errorCalled, isFalse);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle availability check error', (tester) async {
        // Arrange
        when(
          () => mockBiometricService.checkBiometricAvailability(),
        ).thenThrow(Exception('Availability check failed'));

        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Assert - Widget should handle error gracefully
        expect(find.byType(BiometricAuthButton), findsOneWidget);
      });

      testWidgets('should handle auth enabled check error', (tester) async {
        // Arrange
        when(
          () => mockAuthService.isBiometricAuthEnabled(),
        ).thenThrow(Exception('Auth check failed'));

        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Assert - Widget should handle error gracefully
        expect(find.byType(BiometricAuthButton), findsOneWidget);
      });

      testWidgets('should handle sign in error', (tester) async {
        // Arrange
        when(
          () => mockAuthService.signInWithBiometrics(),
        ).thenThrow(Exception('Sign in failed'));

        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Assert - Widget should handle error gracefully
        expect(find.byType(BiometricAuthButton), findsOneWidget);
      });
    });

    group('Service Integration', () {
      testWidgets('should call biometric availability check', (tester) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Assert - Service method should be called
        verify(
          () => mockBiometricService.checkBiometricAvailability(),
        ).called(greaterThan(0));
      });

      testWidgets('should call auth enabled check', (tester) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Assert - Service method should be called
        verify(
          () => mockAuthService.isBiometricAuthEnabled(),
        ).called(greaterThan(0));
      });

      testWidgets('should handle different availability states', (
        tester,
      ) async {
        // Test each availability state
        final availabilityStates = [
          BiometricAvailability.available,
          BiometricAvailability.notSupported,
          BiometricAvailability.notEnrolled,
        ];

        for (final state in availabilityStates) {
          // Arrange
          when(
            () => mockBiometricService.checkBiometricAvailability(),
          ).thenAnswer((_) async => state);

          // Act
          await tester.pumpWidget(createWidget());
          await tester.pumpAndSettle();

          // Assert - Widget should handle all states gracefully
          expect(find.byType(BiometricAuthButton), findsOneWidget);
        }
      });
    });
  });

  group('BiometricIconButton Tests', () {
    late MockBiometricService mockBiometricService;
    late MockAuthService mockAuthService;

    setUp(() {
      mockBiometricService = MockBiometricService();
      mockAuthService = MockAuthService();

      // Set up default mock behaviors
      when(
        () => mockBiometricService.checkBiometricAvailability(),
      ).thenAnswer((_) async => BiometricAvailability.available);
      when(
        () => mockAuthService.isBiometricAuthEnabled(),
      ).thenAnswer((_) async => true);
      when(
        () => mockAuthService.signInWithBiometrics(),
      ).thenAnswer((_) async => null);
    });

    Widget createIconWidget({
      VoidCallback? onSuccess,
      VoidCallback? onError,
      double? size,
    }) {
      return TestWrapper.createTestWidget(
        BiometricIconButton(onSuccess: onSuccess, onError: onError, size: size),
        overrides: [
          biometricServiceProvider.overrideWithValue(mockBiometricService),
          authServiceProvider.overrideWithValue(mockAuthService),
        ],
      );
    }

    group('Icon Widget Creation', () {
      testWidgets('should create icon widget without crashing', (tester) async {
        // Act & Assert - Just verify the widget can be created
        await tester.pumpWidget(createIconWidget());

        // Basic assertion that the widget tree is built
        expect(find.byType(BiometricIconButton), findsOneWidget);
      });

      testWidgets('should handle size parameter', (tester) async {
        // Act
        await tester.pumpWidget(createIconWidget(size: 32));
        await tester.pumpAndSettle();

        // Assert - Widget should be created without crashing
        expect(find.byType(BiometricIconButton), findsOneWidget);
      });

      testWidgets('should handle callback parameters', (tester) async {
        // Arrange
        var successCalled = false;
        var errorCalled = false;

        // Act
        await tester.pumpWidget(
          createIconWidget(
            onSuccess: () => successCalled = true,
            onError: () => errorCalled = true,
          ),
        );
        await tester.pumpAndSettle();

        // Assert - Widget should be created without crashing
        expect(find.byType(BiometricIconButton), findsOneWidget);
        expect(successCalled, isFalse); // Callbacks not called yet
        expect(errorCalled, isFalse);
      });

      testWidgets('should handle null callbacks gracefully', (tester) async {
        // Act
        await tester.pumpWidget(createIconWidget());
        await tester.pumpAndSettle();

        // Assert - Widget should handle null callbacks
        expect(find.byType(BiometricIconButton), findsOneWidget);
      });
    });
  });
}
