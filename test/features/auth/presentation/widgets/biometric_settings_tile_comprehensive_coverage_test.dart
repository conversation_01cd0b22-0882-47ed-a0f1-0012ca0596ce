import 'dart:async';

import 'package:budapp/features/auth/presentation/widgets/biometric_settings_tile.dart';
import 'package:budapp/features/auth/providers/biometric_providers.dart';
import 'package:budapp/features/auth/services/biometric_service.dart'
    show BiometricAvailability, BiometricService;
import 'package:budapp/providers/providers.dart';
import 'package:budapp/services/secure_storage_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';

class MockBiometricService extends Mock implements BiometricService {}

class MockSecureStorageService extends Mock implements SecureStorageService {}

// Comprehensive mock for BiometricAuthNotifier with state management
class MockBiometricAuthNotifier extends BiometricAuthNotifier {
  BiometricAuthState _state = const BiometricAuthState.initial();
  bool _disposed = false;

  @override
  BiometricAuthState get state => _state;

  bool _hasElement = false;

  @override
  set state(BiometricAuthState newState) {
    if (!_disposed) {
      _state = newState;
      // Only call super.state if the element is initialized
      if (_hasElement) {
        super.state = newState; // Call super to notify listeners
      }
    }
  }

  @override
  BiometricAuthState build() {
    _hasElement = true;
    return _state;
  }

  @override
  Future<void> enableBiometric() async {
    if (_disposed) return;
    state = const BiometricAuthState.loading();
    await Future<void>.delayed(const Duration(milliseconds: 10));
    if (!_disposed) {
      state = const BiometricAuthState.success();
    }
  }

  @override
  Future<void> disableBiometric() async {
    if (_disposed) return;
    state = const BiometricAuthState.loading();
    await Future<void>.delayed(const Duration(milliseconds: 10));
    if (!_disposed) {
      state = const BiometricAuthState.success();
    }
  }

  @override
  Future<void> authenticate() async {
    if (_disposed) return;
    state = const BiometricAuthState.loading();
    await Future<void>.delayed(const Duration(milliseconds: 10));
    if (!_disposed) {
      state = const BiometricAuthState.success();
    }
  }

  @override
  void reset() {
    if (!_disposed) {
      state = const BiometricAuthState.initial();
    }
  }

  void dispose() {
    _disposed = true;
  }

  // Helper methods for testing
  void setErrorState(String message) {
    if (!_disposed) {
      state = BiometricAuthState.error(message);
    }
  }

  void setLoadingState() {
    if (!_disposed) {
      state = const BiometricAuthState.loading();
    }
  }

  void setSuccessState() {
    if (!_disposed) {
      state = const BiometricAuthState.success();
    }
  }
}

void main() {
  group('BiometricSettingsTile Comprehensive Coverage Enhancement', () {
    late MockBiometricService mockBiometricService;
    late MockSecureStorageService mockSecureStorageService;
    late MockBiometricAuthNotifier mockNotifier;

    setUp(() {
      mockBiometricService = MockBiometricService();
      mockSecureStorageService = MockSecureStorageService();
      mockNotifier = MockBiometricAuthNotifier();
    });

    Widget createWidgetWithMocks({
      BiometricAvailability availability = BiometricAvailability.available,
      bool isEnabled = false,
      VoidCallback? onChanged,
    }) {
      when(
        () => mockBiometricService.checkBiometricAvailability(),
      ).thenAnswer((_) async => availability);
      when(
        () => mockSecureStorageService.read('biometric_enabled'),
      ).thenAnswer((_) async => isEnabled.toString());

      return TestWrapper.createTestWidget(
        BiometricSettingsTile(onChanged: onChanged),
        overrides: [
          biometricServiceProvider.overrideWithValue(mockBiometricService),
          secureStorageServiceProvider.overrideWithValue(
            mockSecureStorageService,
          ),
          biometricAvailabilityProvider.overrideWith(
            (ref) async => availability,
          ),
          biometricPreferenceProvider.overrideWith(
            (ref) => Future.value(isEnabled),
          ),
          biometricAuthNotifierProvider.overrideWith(() => mockNotifier),
        ],
      );
    }

    // TDD APPROACH: Test failures first to prove real functionality testing
    group('TDD Coverage Enhancement - Critical Uncovered Areas', () {
      testWidgets('COVERAGE: should handle enrollment dialog complete flow', (
        tester,
      ) async {
        // TDD: This test will initially fail, proving it tests real functionality
        // Ensure notifier is not in loading state to allow interaction
        mockNotifier.reset(); // Set to initial state (not loading)

        await tester.pumpWidget(
          createWidgetWithMocks(
            availability: BiometricAvailability.notEnrolled,
            isEnabled: false,
          ),
        );
        await tester.pumpAndSettle();

        // Test complete dialog interaction flow
        expect(find.text('Set up'), findsOneWidget);
        await tester.tap(find.text('Set up'));
        await tester.pumpAndSettle();

        // Verify dialog appears
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.text('Set up biometric authentication'), findsOneWidget);
        expect(
          find.text(
            'To use biometric authentication, you need to set up fingerprint or face recognition in your device settings first.',
          ),
          findsOneWidget,
        );

        // Test dialog dismissal
        expect(find.text('OK'), findsOneWidget);
        await tester.tap(find.text('OK'));
        await tester.pumpAndSettle();

        // Verify dialog is dismissed
        expect(find.byType(AlertDialog), findsNothing);
        expect(find.text('Set up biometric authentication'), findsNothing);
      });

      testWidgets(
        'COVERAGE: should verify ListTile onTap behavior for notEnrolled state',
        (tester) async {
          // TDD: This test targets uncovered ListTile onTap functionality
          // Test that the ListTile onTap is configured correctly for notEnrolled state
          mockNotifier.reset(); // Set to initial state (not loading)

          await tester.pumpWidget(
            createWidgetWithMocks(
              availability: BiometricAvailability.notEnrolled,
              isEnabled: false,
            ),
          );
          await tester.pumpAndSettle();

          // Verify ListTile configuration for notEnrolled state
          final listTile = find.byType(ListTile);
          expect(listTile, findsOneWidget);

          final listTileWidget = tester.widget<ListTile>(listTile);
          expect(
            listTileWidget.onTap,
            isNotNull,
            reason: 'ListTile should have onTap callback for notEnrolled state',
          );

          // Verify ListTile enabled state (should be disabled for notEnrolled)
          expect(
            listTileWidget.enabled,
            false,
            reason:
                'ListTile should be disabled for notEnrolled state - users should use TextButton instead',
          );

          // Verify the subtitle text is correct for notEnrolled
          expect(
            find.text('No biometrics enrolled. Set up in device settings'),
            findsOneWidget,
          );

          // Verify the trailing widget is TextButton "Set up"
          expect(find.text('Set up'), findsOneWidget);
          expect(find.byType(TextButton), findsOneWidget);
        },
      );

      testWidgets('COVERAGE: should test available+enabled subtitle text', (
        tester,
      ) async {
        // Test available + enabled subtitle text
        await tester.pumpWidget(
          createWidgetWithMocks(
            availability: BiometricAvailability.available,
            isEnabled: true,
          ),
        );
        await tester.pumpAndSettle();
        expect(
          find.text('Use fingerprint or face recognition to sign in'),
          findsOneWidget,
        );
      });

      testWidgets('COVERAGE: should test available+disabled subtitle text', (
        tester,
      ) async {
        // Test available + disabled subtitle text
        await tester.pumpWidget(
          createWidgetWithMocks(
            availability: BiometricAvailability.available,
            isEnabled: false,
          ),
        );
        await tester.pumpAndSettle();
        expect(
          find.text('Enable biometric authentication for quick access'),
          findsOneWidget,
        );
      });

      testWidgets('COVERAGE: should test notEnrolled subtitle text', (
        tester,
      ) async {
        // Test not enrolled subtitle text
        await tester.pumpWidget(
          createWidgetWithMocks(
            availability: BiometricAvailability.notEnrolled,
            isEnabled: false,
          ),
        );
        await tester.pumpAndSettle();
        expect(
          find.text('No biometrics enrolled. Set up in device settings'),
          findsOneWidget,
        );
      });

      testWidgets(
        'COVERAGE: should test all icon color variations comprehensively',
        (tester) async {
          // Test all _getIconColor method branches

          // Test available + enabled (primary color)
          await tester.pumpWidget(
            createWidgetWithMocks(
              availability: BiometricAvailability.available,
              isEnabled: true,
            ),
          );
          await tester.pumpAndSettle();

          var icon = tester.widget<Icon>(find.byIcon(Icons.fingerprint));
          expect(icon.color, isNotNull);
          expect(icon.icon, Icons.fingerprint);

          // Test available + disabled (surface color)
          await tester.pumpWidget(
            createWidgetWithMocks(
              availability: BiometricAvailability.available,
              isEnabled: false,
            ),
          );
          await tester.pumpAndSettle();

          icon = tester.widget<Icon>(find.byIcon(Icons.fingerprint));
          expect(icon.color, isNotNull);
          expect(icon.icon, Icons.fingerprint);

          // Test not enrolled (0.6 alpha)
          await tester.pumpWidget(
            createWidgetWithMocks(
              availability: BiometricAvailability.notEnrolled,
              isEnabled: false,
            ),
          );
          await tester.pumpAndSettle();

          icon = tester.widget<Icon>(find.byIcon(Icons.fingerprint));
          expect(icon.color, isNotNull);
          expect(icon.icon, Icons.fingerprint);
        },
      );

      testWidgets(
        'COVERAGE: should test available state trailing widget (Switch)',
        (tester) async {
          // Test available state should show Switch widget
          await tester.pumpWidget(
            createWidgetWithMocks(
              availability: BiometricAvailability.available,
              isEnabled: false,
            ),
          );
          await tester.pumpAndSettle();
          expect(find.byType(Switch), findsOneWidget);
          expect(find.text('Set up'), findsNothing);
          expect(find.byIcon(Icons.block), findsNothing);
        },
      );

      testWidgets(
        'COVERAGE: should test notEnrolled state trailing widget (TextButton)',
        (tester) async {
          // Test not enrolled state should show "Set up" TextButton
          await tester.pumpWidget(
            createWidgetWithMocks(
              availability: BiometricAvailability.notEnrolled,
              isEnabled: false,
            ),
          );
          await tester.pumpAndSettle();
          expect(find.text('Set up'), findsOneWidget);
          expect(find.byType(TextButton), findsOneWidget);
          expect(find.byType(Switch), findsNothing);
        },
      );

      testWidgets(
        'COVERAGE: should test loading tile comprehensive rendering',
        (tester) async {
          // Test _buildLoadingTile method comprehensive coverage
          final completer = Completer<BiometricAvailability>();
          when(
            () => mockBiometricService.checkBiometricAvailability(),
          ).thenAnswer((_) => completer.future);

          await tester.pumpWidget(
            TestWrapper.createTestWidget(
              const BiometricSettingsTile(),
              overrides: [
                biometricServiceProvider.overrideWithValue(
                  mockBiometricService,
                ),
                secureStorageServiceProvider.overrideWithValue(
                  mockSecureStorageService,
                ),
              ],
            ),
          );

          // Test loading state comprehensive elements
          expect(find.byType(BiometricSettingsTile), findsOneWidget);
          expect(find.text('Loading...'), findsOneWidget);
          expect(find.byType(CircularProgressIndicator), findsOneWidget);
          expect(find.byIcon(Icons.fingerprint), findsOneWidget);
          expect(find.text('Biometric Authentication'), findsOneWidget);

          // Test loading state specific widget properties
          final progressIndicator = tester.widget<CircularProgressIndicator>(
            find.byType(CircularProgressIndicator),
          );
          expect(progressIndicator.strokeWidth, 2);

          final sizedBox = tester.widget<SizedBox>(
            find.ancestor(
              of: find.byType(CircularProgressIndicator),
              matching: find.byType(SizedBox),
            ),
          );
          expect(sizedBox.width, 20);
          expect(sizedBox.height, 20);

          // Complete to cleanup
          completer.complete(BiometricAvailability.available);
          await tester.pumpAndSettle();
        },
      );

      testWidgets('COVERAGE: should test error tile comprehensive rendering', (
        tester,
      ) async {
        // Test _buildErrorTile method comprehensive coverage
        const errorMessage = 'Test biometric error';
        when(
          () => mockBiometricService.checkBiometricAvailability(),
        ).thenThrow(Exception(errorMessage));

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const BiometricSettingsTile(),
            overrides: [
              biometricServiceProvider.overrideWithValue(mockBiometricService),
              secureStorageServiceProvider.overrideWithValue(
                mockSecureStorageService,
              ),
            ],
          ),
        );
        await tester.pumpAndSettle();

        // Test error state comprehensive elements
        expect(find.byType(BiometricSettingsTile), findsOneWidget);
        expect(find.text('Biometric Authentication'), findsOneWidget);
        expect(find.textContaining('Error:'), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);

        // Test specific error message display
        expect(find.textContaining(errorMessage), findsOneWidget);
      });

      testWidgets(
        'COVERAGE: should test switch disabled state (isEnabled=false)',
        (tester) async {
          // Test Switch widget with isEnabled=false
          await tester.pumpWidget(
            createWidgetWithMocks(
              availability: BiometricAvailability.available,
              isEnabled: false,
            ),
          );
          await tester.pumpAndSettle();

          final switchWidget = tester.widget<Switch>(find.byType(Switch));
          expect(switchWidget.value, false);
          expect(switchWidget.onChanged, isNotNull);
        },
      );

      testWidgets(
        'COVERAGE: should test switch enabled state (isEnabled=true)',
        (tester) async {
          // Test Switch widget with isEnabled=true
          await tester.pumpWidget(
            createWidgetWithMocks(
              availability: BiometricAvailability.available,
              isEnabled: true,
            ),
          );
          await tester.pumpAndSettle();

          final enabledSwitchWidget = tester.widget<Switch>(
            find.byType(Switch),
          );
          expect(enabledSwitchWidget.value, true);
          expect(enabledSwitchWidget.onChanged, isNotNull);
        },
      );

      testWidgets(
        'COVERAGE: should test ListTile properties comprehensive validation',
        (tester) async {
          // Test ListTile properties comprehensive coverage for all states
          await tester.pumpWidget(
            createWidgetWithMocks(
              availability: BiometricAvailability.available,
              isEnabled: false,
            ),
          );
          await tester.pumpAndSettle();

          // Test ListTile structure and properties
          final listTile = tester.widget<ListTile>(find.byType(ListTile));
          expect(listTile.leading, isA<Icon>());
          expect(listTile.title, isA<Text>());
          expect(listTile.subtitle, isA<Text>());
          expect(listTile.trailing, isA<Switch>());
          expect(listTile.enabled, true);
          expect(listTile.onTap, isNotNull);

          // Test disabled state
          mockNotifier.setLoadingState();
          await tester.pumpWidget(
            createWidgetWithMocks(
              availability: BiometricAvailability.available,
              isEnabled: false,
            ),
          );
          await tester.pumpAndSettle();

          final disabledListTile = tester.widget<ListTile>(
            find.byType(ListTile),
          );
          expect(disabledListTile.enabled, false);
        },
      );

      testWidgets(
        'COVERAGE: should test onTap logic for all availability states',
        (tester) async {
          // Test ListTile onTap behavior for all availability states

          // Reset notifier state before testing
          mockNotifier.reset();

          // Test not enrolled state onTap (should have callback)
          await tester.pumpWidget(
            createWidgetWithMocks(
              availability: BiometricAvailability.notEnrolled,
              isEnabled: false,
            ),
          );
          await tester.pumpAndSettle();

          final notEnrolledListTile = tester.widget<ListTile>(
            find.byType(ListTile),
          );
          expect(
            notEnrolledListTile.onTap,
            isNotNull,
            reason: 'ListTile should have onTap for notEnrolled state',
          );
          expect(
            notEnrolledListTile.enabled,
            false,
            reason: 'ListTile should be disabled for notEnrolled state',
          );

          // Verify the correct UI elements are displayed for notEnrolled state
          expect(
            find.text('No biometrics enrolled. Set up in device settings'),
            findsOneWidget,
            reason: 'Should display notEnrolled subtitle text',
          );
          expect(
            find.text('Set up'),
            findsOneWidget,
            reason: 'Should display Set up button for notEnrolled state',
          );
        },
      );

      testWidgets(
        'COVERAGE: should test switch disabled during loading state',
        (tester) async {
          // Test switch disabled state during loading comprehensive coverage
          mockNotifier.setLoadingState();
          await tester.pumpWidget(
            createWidgetWithMocks(
              availability: BiometricAvailability.available,
              isEnabled: false,
            ),
          );
          await tester.pumpAndSettle();

          // Test switch is disabled during loading
          final switchWidget = tester.widget<Switch>(find.byType(Switch));
          expect(switchWidget.onChanged, isNull);

          // Test ListTile is also disabled during loading
          final listTile = tester.widget<ListTile>(find.byType(ListTile));
          expect(listTile.enabled, false);
        },
      );

      testWidgets('COVERAGE: should display Switch for available state', (
        tester,
      ) async {
        mockNotifier.reset();

        await tester.pumpWidget(
          createWidgetWithMocks(
            availability: BiometricAvailability.available,
            isEnabled: false,
          ),
        );
        await tester.pumpAndSettle();

        expect(find.byType(Switch), findsOneWidget);
        expect(find.text('Set up'), findsNothing);
      });

      testWidgets(
        'COVERAGE: should display Set up button for notEnrolled state',
        (tester) async {
          mockNotifier.reset();

          await tester.pumpWidget(
            createWidgetWithMocks(
              availability: BiometricAvailability.notEnrolled,
              isEnabled: false,
            ),
          );
          await tester.pumpAndSettle();

          expect(find.text('Set up'), findsOneWidget);
          expect(find.byType(Switch), findsNothing);
        },
      );

      testWidgets(
        'COVERAGE: should display enabled Switch for available+enabled state',
        (tester) async {
          mockNotifier.reset();

          await tester.pumpWidget(
            createWidgetWithMocks(
              availability: BiometricAvailability.available,
              isEnabled: true,
            ),
          );
          await tester.pumpAndSettle();

          expect(find.byType(Switch), findsOneWidget);
          final switchWidget = tester.widget<Switch>(find.byType(Switch));
          expect(switchWidget.value, true);
        },
      );

      testWidgets('COVERAGE: should test callback invocation edge cases', (
        tester,
      ) async {
        // Test onChanged callback comprehensive edge cases
        var callbackCount = 0;
        void testCallback() {
          callbackCount++;
        }

        mockNotifier.setSuccessState();
        await tester.pumpWidget(
          createWidgetWithMocks(
            availability: BiometricAvailability.available,
            isEnabled: false,
            onChanged: testCallback,
          ),
        );
        await tester.pumpAndSettle();

        // Test callback is called on successful toggle
        await tester.tap(find.byType(Switch));
        await tester.pumpAndSettle();
        expect(callbackCount, 1);

        // Test callback is called multiple times
        await tester.tap(find.byType(Switch));
        await tester.pumpAndSettle();
        expect(callbackCount, 2);
      });

      testWidgets(
        'COVERAGE: should test preference loading error comprehensive handling',
        (tester) async {
          // Test preference loading error comprehensive coverage
          when(
            () => mockBiometricService.checkBiometricAvailability(),
          ).thenAnswer((_) async => BiometricAvailability.available);
          when(
            () => mockSecureStorageService.read('biometric_enabled'),
          ).thenThrow(Exception('Storage read failed'));

          await tester.pumpWidget(
            TestWrapper.createTestWidget(
              const BiometricSettingsTile(),
              overrides: [
                biometricServiceProvider.overrideWithValue(
                  mockBiometricService,
                ),
                secureStorageServiceProvider.overrideWithValue(
                  mockSecureStorageService,
                ),
                biometricAvailabilityProvider.overrideWith(
                  (ref) => Future.value(BiometricAvailability.available),
                ),
              ],
            ),
          );
          await tester.pumpAndSettle();

          // Test error tile is shown for preference loading error
          expect(find.byType(BiometricSettingsTile), findsOneWidget);
          expect(find.text('Biometric Authentication'), findsOneWidget);
          expect(find.textContaining('Error:'), findsOneWidget);
          expect(find.byIcon(Icons.error_outline), findsOneWidget);
        },
      );

      testWidgets(
        'COVERAGE: should test theme integration comprehensive validation',
        (tester) async {
          // Test theme integration comprehensive coverage with custom theme
          await tester.pumpWidget(
            TestWrapper.createTestWidget(
              Theme(
                data: ThemeData.dark(),
                child: BiometricSettingsTile(onChanged: () {}),
              ),
              overrides: [
                biometricServiceProvider.overrideWithValue(
                  mockBiometricService,
                ),
                secureStorageServiceProvider.overrideWithValue(
                  mockSecureStorageService,
                ),
                biometricAvailabilityProvider.overrideWith(
                  (ref) => Future.value(BiometricAvailability.available),
                ),
                biometricPreferenceProvider.overrideWith(
                  (ref) => Future.value(false),
                ),
                biometricAuthNotifierProvider.overrideWith(() => mockNotifier),
              ],
            ),
          );
          await tester.pumpAndSettle();

          // Verify widget renders with dark theme
          expect(find.byType(BiometricSettingsTile), findsOneWidget);
          expect(find.text('Biometric Authentication'), findsOneWidget);
          expect(find.byIcon(Icons.fingerprint), findsOneWidget);
        },
      );

      testWidgets(
        'COVERAGE: should test not supported state comprehensive behavior',
        (tester) async {
          // Test not supported state comprehensive coverage
          await tester.pumpWidget(
            createWidgetWithMocks(
              availability: BiometricAvailability.notSupported,
              isEnabled: false,
            ),
          );
          await tester.pumpAndSettle();

          // Test widget is hidden for not supported state
          expect(find.byType(BiometricSettingsTile), findsOneWidget);
          expect(find.byType(SizedBox), findsOneWidget);
          expect(find.text('Biometric Authentication'), findsNothing);
          expect(find.byType(ListTile), findsNothing);

          // Verify SizedBox.shrink behavior
          final sizedBox = tester.widget<SizedBox>(find.byType(SizedBox));
          expect(sizedBox.width, 0.0);
          expect(sizedBox.height, 0.0);
        },
      );

      testWidgets('COVERAGE: should test all BiometricAuthState transitions', (
        tester,
      ) async {
        // Test all possible BiometricAuthState transitions comprehensive coverage
        await tester.pumpWidget(
          createWidgetWithMocks(
            availability: BiometricAvailability.available,
            isEnabled: false,
          ),
        );
        await tester.pumpAndSettle();

        // Test initial state
        expect(find.byType(Switch), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsNothing);

        // Test transition to loading state
        mockNotifier.setLoadingState();
        await tester.pump();

        final listTile = tester.widget<ListTile>(find.byType(ListTile));
        expect(listTile.enabled, false);

        // Test transition to success state
        mockNotifier.setSuccessState();
        await tester.pump();

        final enabledListTile = tester.widget<ListTile>(find.byType(ListTile));
        expect(enabledListTile.enabled, true);

        // Test transition to error state
        mockNotifier.setErrorState('Test error');
        await tester.pump();

        final errorListTile = tester.widget<ListTile>(find.byType(ListTile));
        expect(errorListTile.enabled, true);
      });
    });
  });
}
