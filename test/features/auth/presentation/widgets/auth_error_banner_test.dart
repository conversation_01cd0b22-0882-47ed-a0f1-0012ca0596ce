import 'package:budapp/features/auth/presentation/widgets/auth_error_banner.dart';
import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/test_wrapper.dart';

// Helper method to create AuthError instances for testing
AuthError createTestError({
  String code = 'test_error',
  String userMessage = 'Test error message',
  String technicalMessage = 'Technical test error',
  ErrorSeverity severity = ErrorSeverity.error,
  ErrorCategory category = ErrorCategory.authentication,
  bool isRetryable = true,
}) {
  return AuthError(
    code: code,
    userMessage: userMessage,
    technicalMessage: technicalMessage,
    severity: severity,
    category: category,
    isRetryable: isRetryable,
  );
}

void main() {
  group('AuthErrorBanner Tests', () {
    group('Basic Rendering Tests', () {
      testWidgets('should render error banner with basic configuration', (
        tester,
      ) async {
        final error = createTestError();

        await tester.pumpWidget(
          TestWrapper.createTestWidget(AuthErrorBanner(error: error)),
        );

        await tester.pumpAndSettle();

        // Verify basic structure
        expect(find.byType(AuthErrorBanner), findsOneWidget);
        expect(find.text('Test error message'), findsOneWidget);
        expect(find.text('Error'), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
      });

      testWidgets('should render error message correctly', (tester) async {
        const customMessage = 'Custom error message for testing';
        final error = createTestError(userMessage: customMessage);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(AuthErrorBanner(error: error)),
        );

        await tester.pumpAndSettle();

        expect(find.text(customMessage), findsOneWidget);
      });

      testWidgets('should render with custom context message', (tester) async {
        final error = createTestError(code: 'user-not-found');

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthErrorBanner(error: error, context: 'login'),
          ),
        );

        await tester.pumpAndSettle();

        // Should show contextual message instead of default
        expect(
          find.textContaining('No account found with this email'),
          findsOneWidget,
        );
      });
    });

    group('Severity-Based Styling Tests', () {
      testWidgets('should render info severity correctly', (tester) async {
        final error = createTestError(severity: ErrorSeverity.info);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(AuthErrorBanner(error: error)),
        );

        await tester.pumpAndSettle();

        expect(find.text('Information'), findsOneWidget);
        expect(find.byIcon(Icons.info_outline), findsOneWidget);
      });

      testWidgets('should render warning severity correctly', (tester) async {
        final error = createTestError(severity: ErrorSeverity.warning);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(AuthErrorBanner(error: error)),
        );

        await tester.pumpAndSettle();

        expect(find.text('Warning'), findsOneWidget);
        expect(find.byIcon(Icons.warning_amber_outlined), findsOneWidget);
      });

      testWidgets('should render error severity correctly', (tester) async {
        final error = createTestError(severity: ErrorSeverity.error);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(AuthErrorBanner(error: error)),
        );

        await tester.pumpAndSettle();

        expect(find.text('Error'), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
      });

      testWidgets('should render critical severity correctly', (tester) async {
        final error = createTestError(severity: ErrorSeverity.critical);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(AuthErrorBanner(error: error)),
        );

        await tester.pumpAndSettle();

        expect(find.text('Critical Error'), findsOneWidget);
        expect(find.byIcon(Icons.dangerous_outlined), findsOneWidget);
      });
    });

    group('Suggested Actions Tests', () {
      testWidgets('should show suggested actions when enabled', (tester) async {
        final error = createTestError(code: 'user-not-found');

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthErrorBanner(error: error, showSuggestedActions: true),
          ),
        );

        await tester.pumpAndSettle();

        // Note: Suggestions text might not be found due to localization in tests
        // Instead, verify the suggested actions are shown
        expect(find.textContaining('Check your email address'), findsOneWidget);
        expect(find.text('Check your email address'), findsOneWidget);
        expect(find.text('Create a new account'), findsOneWidget);
      });

      testWidgets('should hide suggested actions when disabled', (
        tester,
      ) async {
        final error = createTestError(code: 'user-not-found');

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthErrorBanner(error: error, showSuggestedActions: false),
          ),
        );

        await tester.pumpAndSettle();

        // Verify suggested actions are not shown
        expect(find.text('Check your email address'), findsNothing);
        expect(find.text('Check your email address'), findsNothing);
      });

      testWidgets(
        'should show different suggestions for different error codes',
        (tester) async {
          final error = createTestError(code: 'wrong-password');

          await tester.pumpWidget(
            TestWrapper.createTestWidget(
              AuthErrorBanner(error: error, showSuggestedActions: true),
            ),
          );

          await tester.pumpAndSettle();

          expect(find.text('Try again'), findsOneWidget);
          expect(find.text('Reset your password'), findsOneWidget);
        },
      );
    });

    group('Retry Button Tests', () {
      testWidgets('should show retry button when error is retryable', (
        tester,
      ) async {
        var retryPressed = false;
        final error = createTestError(isRetryable: true);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthErrorBanner(
              error: error,
              showRetryButton: true,
              onRetry: () => retryPressed = true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('Try Again'), findsOneWidget);
        expect(find.byIcon(Icons.refresh), findsOneWidget);

        // Test retry button functionality
        await tester.tap(find.text('Try Again'));
        await tester.pumpAndSettle();

        expect(retryPressed, isTrue);
      });

      testWidgets('should hide retry button when error is not retryable', (
        tester,
      ) async {
        final error = createTestError(isRetryable: false);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthErrorBanner(
              error: error,
              showRetryButton: true,
              onRetry: () {},
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('Try Again'), findsNothing);
        expect(find.byIcon(Icons.refresh), findsNothing);
      });

      testWidgets('should hide retry button when showRetryButton is false', (
        tester,
      ) async {
        final error = createTestError(isRetryable: true);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthErrorBanner(
              error: error,
              showRetryButton: false,
              onRetry: () {},
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('Try Again'), findsNothing);
      });

      testWidgets('should hide retry button when onRetry is null', (
        tester,
      ) async {
        final error = createTestError(isRetryable: true);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthErrorBanner(error: error, showRetryButton: true, onRetry: null),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('Try Again'), findsNothing);
      });
    });

    group('Dismiss Button Tests', () {
      testWidgets('should show dismiss button when onDismiss is provided', (
        tester,
      ) async {
        var dismissPressed = false;
        final error = createTestError();

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthErrorBanner(
              error: error,
              onDismiss: () => dismissPressed = true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.byIcon(Icons.close), findsOneWidget);

        // Test dismiss button functionality
        await tester.tap(find.byIcon(Icons.close));
        await tester.pumpAndSettle();

        expect(dismissPressed, isTrue);
      });

      testWidgets('should hide dismiss button when onDismiss is null', (
        tester,
      ) async {
        final error = createTestError();

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AuthErrorBanner(error: error, onDismiss: null),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.byIcon(Icons.close), findsNothing);
      });
    });
  });

  group('AuthErrorSnackBar Tests', () {
    testWidgets('should show snackbar with error message', (tester) async {
      final error = createTestError();

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () => AuthErrorSnackBar.show(context, error),
                  child: const Text('Show SnackBar'),
                );
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap button to show snackbar
      await tester.tap(find.text('Show SnackBar'));
      await tester.pumpAndSettle();

      // Verify snackbar content
      expect(find.byType(SnackBar), findsOneWidget);
      expect(find.text('Test error message'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('should show snackbar with contextual message', (tester) async {
      final error = createTestError(code: 'user-not-found');

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () => AuthErrorSnackBar.show(
                    context,
                    error,
                    contextName: 'login',
                  ),
                  child: const Text('Show SnackBar'),
                );
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap button to show snackbar
      await tester.tap(find.text('Show SnackBar'));
      await tester.pumpAndSettle();

      // Verify contextual message
      expect(
        find.textContaining('No account found with this email'),
        findsOneWidget,
      );
    });

    testWidgets('should show retry action for retryable errors', (
      tester,
    ) async {
      var retryPressed = false;
      final error = createTestError(isRetryable: true);

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () => AuthErrorSnackBar.show(
                    context,
                    error,
                    onRetry: () => retryPressed = true,
                  ),
                  child: const Text('Show SnackBar'),
                );
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap button to show snackbar
      await tester.tap(find.text('Show SnackBar'));
      await tester.pumpAndSettle();

      // Verify retry action exists
      expect(find.text('Retry'), findsOneWidget);

      // Test retry functionality
      await tester.tap(find.text('Retry'));
      await tester.pumpAndSettle();

      expect(retryPressed, isTrue);
    });

    testWidgets('should not show retry action for non-retryable errors', (
      tester,
    ) async {
      final error = createTestError(isRetryable: false);

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () => AuthErrorSnackBar.show(context, error),
                  child: const Text('Show SnackBar'),
                );
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap button to show snackbar
      await tester.tap(find.text('Show SnackBar'));
      await tester.pumpAndSettle();

      // Verify no retry action
      expect(find.text('Retry'), findsNothing);
    });

    testWidgets('should show info icon for info severity', (tester) async {
      final infoError = createTestError(severity: ErrorSeverity.info);

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () => AuthErrorSnackBar.show(context, infoError),
                  child: const Text('Show Info'),
                );
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Test info icon
      await tester.tap(find.text('Show Info'));
      await tester.pumpAndSettle();
      expect(find.byIcon(Icons.info_outline), findsOneWidget);
    });

    testWidgets('should show warning icon for warning severity', (
      tester,
    ) async {
      final warningError = createTestError(severity: ErrorSeverity.warning);

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () =>
                      AuthErrorSnackBar.show(context, warningError),
                  child: const Text('Show Warning'),
                );
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Test warning icon
      await tester.tap(find.text('Show Warning'));
      await tester.pumpAndSettle();
      expect(find.byIcon(Icons.warning_amber_outlined), findsOneWidget);
    });
  });

  group('Edge Cases and Error Handling', () {
    testWidgets('should handle empty error message gracefully', (tester) async {
      final error = createTestError(userMessage: '');

      await tester.pumpWidget(
        TestWrapper.createTestWidget(AuthErrorBanner(error: error)),
      );

      await tester.pumpAndSettle();

      // Should not crash with empty message
      expect(find.byType(AuthErrorBanner), findsOneWidget);
    });

    testWidgets('should handle very long error messages', (tester) async {
      final longMessage =
          'This is a very long error message that should wrap properly and not cause any layout issues. ' *
          5;
      final error = createTestError(userMessage: longMessage);

      await tester.pumpWidget(
        TestWrapper.createTestWidget(AuthErrorBanner(error: error)),
      );

      await tester.pumpAndSettle();

      expect(find.byType(AuthErrorBanner), findsOneWidget);
      expect(
        find.textContaining('This is a very long error message'),
        findsOneWidget,
      );
    });

    testWidgets('should handle null context gracefully', (tester) async {
      final error = createTestError();

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          AuthErrorBanner(error: error, context: null),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(AuthErrorBanner), findsOneWidget);
      expect(find.text('Test error message'), findsOneWidget);
    });

    testWidgets('should handle unknown error codes gracefully', (tester) async {
      final error = createTestError(code: 'unknown-error-code');

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          AuthErrorBanner(error: error, showSuggestedActions: true),
        ),
      );

      await tester.pumpAndSettle();

      // Should show default suggestions for unknown codes
      expect(find.text('Try again'), findsOneWidget);
      expect(
        find.text('Contact support if the problem persists'),
        findsOneWidget,
      );
    });
  });
}
