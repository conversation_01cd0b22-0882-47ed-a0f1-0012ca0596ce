import 'package:budapp/features/auth/presentation/widgets/auth_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../helpers/test_wrapper.dart';

void main() {
  group('AuthValidators Tests', () {
    // Note: These tests now validate the actual production AuthValidators code
    // with proper BuildContext support for localization

    testWidgets('Email validation should work correctly', (tester) async {
      late BuildContext testContext;

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Builder(
            builder: (context) {
              testContext = context;
              return const SizedBox.shrink();
            },
          ),
        ),
      );

      // Test valid emails
      expect(AuthValidators.email('<EMAIL>', testContext), isNull);
      expect(
        AuthValidators.email('<EMAIL>', testContext),
        isNull,
      );

      // Test invalid emails
      expect(
        AuthValidators.email('', testContext),
        equals('Email is required'),
      );
      expect(
        AuthValidators.email(null, testContext),
        equals('Email is required'),
      );
      expect(
        AuthValidators.email('invalid-email', testContext),
        equals('Please enter a valid email address'),
      );
      expect(
        AuthValidators.email('@domain.com', testContext),
        equals('Please enter a valid email address'),
      );
      expect(
        AuthValidators.email('user@', testContext),
        equals('Please enter a valid email address'),
      );
    });

    testWidgets('Password validation should work correctly', (tester) async {
      late BuildContext testContext;

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Builder(
            builder: (context) {
              testContext = context;
              return const SizedBox.shrink();
            },
          ),
        ),
      );

      // Test valid passwords
      expect(AuthValidators.password('123456', testContext), isNull);
      expect(AuthValidators.password('password123', testContext), isNull);
      expect(AuthValidators.password('MySecurePassword!', testContext), isNull);

      // Test invalid passwords
      expect(
        AuthValidators.password('', testContext),
        equals('Password is required'),
      );
      expect(
        AuthValidators.password(null, testContext),
        equals('Password is required'),
      );
      expect(
        AuthValidators.password('12345', testContext),
        equals('Password must be at least 6 characters'),
      );
      expect(
        AuthValidators.password('abc', testContext),
        equals('Password must be at least 6 characters'),
      );
    });

    testWidgets('Confirm password validation should work correctly', (
      tester,
    ) async {
      late BuildContext testContext;

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Builder(
            builder: (context) {
              testContext = context;
              return const SizedBox.shrink();
            },
          ),
        ),
      );

      const originalPassword = 'password123';

      // Test matching passwords
      expect(
        AuthValidators.confirmPassword(
          'password123',
          originalPassword,
          testContext,
        ),
        isNull,
      );

      // Test non-matching passwords
      expect(
        AuthValidators.confirmPassword(
          'different',
          originalPassword,
          testContext,
        ),
        equals('Passwords do not match'),
      );
      expect(
        AuthValidators.confirmPassword('', originalPassword, testContext),
        equals('Please confirm your password'),
      );
      expect(
        AuthValidators.confirmPassword(null, originalPassword, testContext),
        equals('Please confirm your password'),
      );
    });

    testWidgets('Required field validation should work correctly', (
      tester,
    ) async {
      // Test valid input
      expect(AuthValidators.required('valid input', 'Name'), isNull);
      expect(AuthValidators.required('test', 'Field'), isNull);

      // Test invalid inputs
      expect(AuthValidators.required('', 'Name'), equals('Name is required'));
      expect(
        AuthValidators.required(null, 'Field'),
        equals('Field is required'),
      );
    });
  });

  group('AuthFormField Widget Tests', () {
    testWidgets('AuthFormField renders correctly with basic properties', (
      tester,
    ) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Scaffold(
            body: Padding(
              padding: const EdgeInsets.all(16),
              child: AuthFormField(
                label: 'Email',
                hintText:
                    'Enter your email', // Explicit hintText to avoid duplication
                controller: controller,
              ),
            ),
          ),
        ),
      );

      // Verify the label is displayed
      expect(find.text('Email'), findsOneWidget);
      // Verify the TextFormField is present
      expect(find.byType(TextFormField), findsOneWidget);
      // Verify the AuthFormField widget itself is present
      expect(find.byType(AuthFormField), findsOneWidget);
    });

    testWidgets('AuthFormField handles password field correctly', (
      tester,
    ) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Scaffold(
            body: Padding(
              padding: const EdgeInsets.all(16),
              child: AuthFormField(
                label: 'Password',
                hintText:
                    'Enter your password', // Explicit hintText to avoid duplication
                controller: controller,
                isPassword: true,
              ),
            ),
          ),
        ),
      );

      // Verify the label is displayed
      expect(find.text('Password'), findsOneWidget);
      // Verify the TextFormField is present
      expect(find.byType(TextFormField), findsOneWidget);
      // Verify the AuthFormField widget itself is present
      expect(find.byType(AuthFormField), findsOneWidget);

      // Verify visibility toggle icon is present
      expect(find.byIcon(Icons.visibility), findsOneWidget);

      // Verify that the password is initially obscured
      // We can't directly access obscureText from TextFormField, so we test through input decoration
      await tester.enterText(find.byType(TextFormField), 'test123');
      await tester.pump();

      // In a password field, the text should be obscured (shown as bullets/dots)
      // We verify this by checking that the visibility icon is present, which indicates password mode
      expect(find.byIcon(Icons.visibility), findsOneWidget);
    });

    testWidgets('AuthFormField toggles password visibility correctly', (
      tester,
    ) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Scaffold(
            body: Padding(
              padding: const EdgeInsets.all(16),
              child: AuthFormField(
                label: 'Password',
                hintText: 'Enter your password',
                controller: controller,
                isPassword: true,
              ),
            ),
          ),
        ),
      );

      // Initially, password should be obscured and visibility icon should be present
      expect(find.byIcon(Icons.visibility), findsOneWidget);

      // Tap the visibility icon to show password
      await tester.tap(find.byIcon(Icons.visibility));
      await tester.pumpAndSettle();

      // Now the icon should change to visibility_off and password should be visible
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
      expect(find.byIcon(Icons.visibility), findsNothing);

      // Tap again to hide password
      await tester.tap(find.byIcon(Icons.visibility_off));
      await tester.pumpAndSettle();

      // Should be back to initial state
      expect(find.byIcon(Icons.visibility), findsOneWidget);
      expect(find.byIcon(Icons.visibility_off), findsNothing);
    });

    testWidgets('AuthFormField calls validator correctly', (tester) async {
      final controller = TextEditingController();
      var validatorCalled = false;

      String? testValidator(String? value) {
        validatorCalled = true;
        return value == null || value.isEmpty ? 'Field is required' : null;
      }

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Scaffold(
            body: Padding(
              padding: const EdgeInsets.all(16),
              child: Form(
                child: AuthFormField(
                  label: 'Test Field',
                  controller: controller,
                  validator: testValidator,
                ),
              ),
            ),
          ),
        ),
      );

      // Trigger validation by submitting the form
      tester.state<FormState>(find.byType(Form)).validate();

      expect(validatorCalled, isTrue);
    });
  });
}
