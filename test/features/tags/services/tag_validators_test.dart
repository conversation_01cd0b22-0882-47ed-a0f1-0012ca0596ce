import 'package:budapp/data/models/tag.dart';
import 'package:budapp/features/tags/services/tag_validators.dart';
import 'package:budapp/utils/validation_result.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('TagValidators', () {
    late TagValidators validator;

    setUp(() {
      validator = TagValidators();
    });

    group('validateTagCreation', () {
      test('should pass validation with valid name and color', () {
        final result = validator.validateTagCreation(
          name: 'Valid Tag',
          color: '#FF5722',
        );

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('should fail validation with invalid name', () {
        final result = validator.validateTagCreation(
          name: '',
          color: '#FF5722',
        );

        expect(result.isValid, isFalse);
        expect(result.errors, isNotEmpty);
        expect(result.errors.first, contains('empty'));
      });

      test('should fail validation with invalid color', () {
        final result = validator.validateTagCreation(
          name: 'Valid Tag',
          color: 'invalid',
        );

        expect(result.isValid, isFalse);
        expect(result.errors, isNotEmpty);
        expect(result.errors.first, contains('hex color'));
      });

      test('should accumulate multiple validation errors', () {
        final result = validator.validateTagCreation(
          name: '',
          color: 'invalid',
        );

        expect(result.isValid, isFalse);
        expect(result.errors, hasLength(2));
      });
    });

    group('validateTagUpdate', () {
      late Tag currentTag;

      setUp(() {
        currentTag = Tag.create(
          name: 'Current Tag',
          color: '#2196F3',
          userId: 'user123',
        );
      });

      test('should pass validation when no changes are made', () {
        final result = validator.validateTagUpdate(
          currentTag: currentTag,
          newName: currentTag.name,
          newColor: currentTag.color,
        );

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('should validate name only when changed', () {
        final result = validator.validateTagUpdate(
          currentTag: currentTag,
          newName: 'New Valid Name',
          newColor: currentTag.color,
        );

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('should validate color only when changed', () {
        final result = validator.validateTagUpdate(
          currentTag: currentTag,
          newName: currentTag.name,
          newColor: '#4CAF50',
        );

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('should fail validation when new name is invalid', () {
        final result = validator.validateTagUpdate(
          currentTag: currentTag,
          newName: '',
          newColor: currentTag.color,
        );

        expect(result.isValid, isFalse);
        expect(result.errors, isNotEmpty);
      });

      test('should fail validation when new color is invalid', () {
        final result = validator.validateTagUpdate(
          currentTag: currentTag,
          newName: currentTag.name,
          newColor: 'invalid',
        );

        expect(result.isValid, isFalse);
        expect(result.errors, isNotEmpty);
      });

      test('should validate both name and color when both changed', () {
        final result = validator.validateTagUpdate(
          currentTag: currentTag,
          newName: 'New Name',
          newColor: '#4CAF50',
        );

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });
    });

    group('validateTagName', () {
      test('should pass validation with valid name', () {
        final result = validator.validateTagName('Valid Tag');

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('should fail validation with empty name', () {
        final result = validator.validateTagName('');

        expect(result.isValid, isFalse);
        expect(result.errors, contains('Tag name cannot be empty'));
      });

      test('should fail validation with whitespace-only name', () {
        final result = validator.validateTagName('   ');

        expect(result.isValid, isFalse);
        expect(result.errors, contains('Tag name cannot be empty'));
      });

      test('should fail validation with name too short', () {
        final result = validator.validateTagName('A');

        expect(result.isValid, isFalse);
        expect(
          result.errors,
          contains('Tag name must be at least 2 characters long'),
        );
      });

      test('should fail validation with name too long', () {
        final result = validator.validateTagName('A' * 31);

        expect(result.isValid, isFalse);
        expect(result.errors, contains('Tag name cannot exceed 30 characters'));
      });

      test('should pass validation with minimum valid length', () {
        final result = validator.validateTagName('AB');

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('should pass validation with maximum valid length', () {
        final result = validator.validateTagName('A' * 30);

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('should fail validation with invalid characters', () {
        final invalidChars = ['<', '>', '"', '/', r'\', '|', '?', '*'];

        for (final char in invalidChars) {
          final result = validator.validateTagName('Tag$char');
          expect(result.isValid, isFalse);
          expect(
            result.errors,
            contains('Tag name contains invalid characters'),
          );
        }
      });

      test('should pass validation with valid special characters', () {
        final validNames = [
          'Tag-Name',
          'Tag_Name',
          'Tag.Name',
          'Tag Name',
          'Tag123',
          'Tag@Email',
          'Tag#Hash',
          'Tag&More',
        ];

        for (final name in validNames) {
          final result = validator.validateTagName(name);
          expect(result.isValid, isTrue, reason: 'Failed for name: $name');
        }
      });

      test('should fail validation with only spaces after trimming', () {
        final result = validator.validateTagName('A   B   ');

        expect(result.isValid, isTrue); // This should be valid
      });

      test('should fail validation with tabs and spaces only', () {
        final result = validator.validateTagName('\t   \n   ');

        expect(result.isValid, isFalse);
        expect(result.errors, contains('Tag name cannot be empty'));
      });

      test('should trim whitespace before validation', () {
        final result = validator.validateTagName('  Valid Tag  ');

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });
    });

    group('validateTagColor', () {
      test('should pass validation with valid hex color', () {
        final result = validator.validateTagColor('#FF5722');

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('should fail validation with empty color', () {
        final result = validator.validateTagColor('');

        expect(result.isValid, isFalse);
        expect(result.errors, contains('Tag color cannot be empty'));
      });

      test('should fail validation with invalid hex format', () {
        final invalidColors = [
          'FF5722', // Missing #
          '#FF572', // Too short
          '#FF57222', // Too long
          '#GG5722', // Invalid hex characters
          'red', // Color name
          'rgb(255,87,34)', // RGB format
        ];

        for (final color in invalidColors) {
          final result = validator.validateTagColor(color);
          expect(result.isValid, isFalse, reason: 'Failed for color: $color');
          expect(
            result.errors,
            contains('Tag color must be a valid hex color (e.g., #FF5722)'),
          );
        }
      });

      test('should pass validation with various valid hex colors', () {
        final validColors = [
          '#FF5722',
          '#000000',
          '#FFFFFF',
          '#123456',
          '#ABCDEF',
          '#987654',
        ];

        for (final color in validColors) {
          final result = validator.validateTagColor(color);
          expect(result.isValid, isTrue, reason: 'Failed for color: $color');
          expect(result.errors, isEmpty);
        }
      });

      test('should accept both uppercase and lowercase hex', () {
        final uppercaseResult = validator.validateTagColor('#ABCDEF');
        final lowercaseResult = validator.validateTagColor('#abcdef');
        final mixedResult = validator.validateTagColor('#AbCdEf');

        expect(uppercaseResult.isValid, isTrue);
        expect(lowercaseResult.isValid, isTrue);
        expect(mixedResult.isValid, isTrue);
      });
    });

    group('validateTagDeletion', () {
      late Tag tag;

      setUp(() {
        tag = Tag.create(name: 'Test Tag', color: '#FF5722', userId: 'user123');
      });

      test('should allow deletion with no usage', () {
        final result = validator.validateTagDeletion(tag: tag, usageCount: 0);

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('should allow deletion with usage but show warning', () {
        final result = validator.validateTagDeletion(tag: tag, usageCount: 5);

        expect(result.isValid, isTrue); // Still allows deletion
        expect(result.errors, isNotEmpty);
        expect(result.errors.first, contains('5 transaction'));
      });

      test('should handle singular usage count correctly', () {
        final result = validator.validateTagDeletion(tag: tag, usageCount: 1);

        expect(result.isValid, isTrue);
        expect(result.errors.first, contains('1 transaction'));
      });

      test('should handle large usage counts', () {
        final result = validator.validateTagDeletion(tag: tag, usageCount: 100);

        expect(result.isValid, isTrue);
        expect(result.errors.first, contains('100 transaction'));
      });
    });

    group('validateTagIdsForTransaction', () {
      test('should pass validation with empty list', () {
        final result = validator.validateTagIdsForTransaction([]);

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('should pass validation with valid tag IDs', () {
        final result = validator.validateTagIdsForTransaction([
          'tag1',
          'tag2',
          'tag3',
        ]);

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('should fail validation with too many tags', () {
        final tagIds = List.generate(11, (index) => 'tag$index');
        final result = validator.validateTagIdsForTransaction(tagIds);

        expect(result.isValid, isFalse);
        expect(
          result.errors,
          contains('Cannot assign more than 10 tags to a transaction'),
        );
      });

      test('should pass validation with exactly 10 tags', () {
        final tagIds = List.generate(10, (index) => 'tag$index');
        final result = validator.validateTagIdsForTransaction(tagIds);

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('should fail validation with duplicate tag IDs', () {
        final result = validator.validateTagIdsForTransaction([
          'tag1',
          'tag2',
          'tag1', // Duplicate
        ]);

        expect(result.isValid, isFalse);
        expect(result.errors, contains('Duplicate tags are not allowed'));
      });

      test('should fail validation with empty tag IDs', () {
        final result = validator.validateTagIdsForTransaction([
          'tag1',
          '',
          'tag3',
        ]);

        expect(result.isValid, isFalse);
        expect(result.errors, contains('Tag IDs cannot be empty'));
      });

      test('should fail validation with whitespace-only tag IDs', () {
        final result = validator.validateTagIdsForTransaction([
          'tag1',
          '   ',
          'tag3',
        ]);

        expect(result.isValid, isFalse);
        expect(result.errors, contains('Tag IDs cannot be empty'));
      });

      test('should accumulate multiple validation errors', () {
        final tagIds = List.generate(11, (index) => index == 5 ? '' : 'tag1');
        final result = validator.validateTagIdsForTransaction(tagIds);

        expect(result.isValid, isFalse);
        expect(result.errors.length, greaterThanOrEqualTo(2));
      });
    });

    group('getSuggestedColors', () {
      test('should return a non-empty list of colors', () {
        final colors = validator.getSuggestedColors();

        expect(colors, isNotEmpty);
        expect(colors.length, greaterThan(10));
      });

      test('should return colors in valid hex format', () {
        final colors = validator.getSuggestedColors();

        for (final color in colors) {
          expect(color, matches(r'^#[0-9A-Fa-f]{6}$'));
        }
      });

      test('should return specific expected colors', () {
        final colors = validator.getSuggestedColors();

        expect(colors, contains('#F44336')); // Red
        expect(colors, contains('#2196F3')); // Blue
        expect(colors, contains('#4CAF50')); // Green
        expect(colors, contains('#FF5722')); // Deep Orange
      });

      test('should return consistent results', () {
        final colors1 = validator.getSuggestedColors();
        final colors2 = validator.getSuggestedColors();

        expect(colors1, equals(colors2));
      });
    });

    group('isSuggestedColor', () {
      test('should return true for suggested colors', () {
        final suggestedColors = validator.getSuggestedColors();

        for (final color in suggestedColors) {
          expect(validator.isSuggestedColor(color), isTrue);
        }
      });

      test('should return false for non-suggested colors', () {
        final nonSuggestedColors = ['#111111', '#222222', '#AAAAAA', '#BBBBBB'];

        for (final color in nonSuggestedColors) {
          expect(validator.isSuggestedColor(color), isFalse);
        }
      });

      test('should handle case-insensitive comparison', () {
        // isSuggestedColor converts input to uppercase for comparison
        expect(validator.isSuggestedColor('#f44336'), isTrue);
        expect(validator.isSuggestedColor('#F44336'), isTrue);
        expect(
          validator.isSuggestedColor('#AbCdEf'),
          isFalse,
        ); // Not in suggested list
      });

      test('should return false for invalid format colors', () {
        expect(validator.isSuggestedColor('red'), isFalse);
        expect(validator.isSuggestedColor('FF5722'), isFalse);
        expect(validator.isSuggestedColor('#FF572'), isFalse);
      });
    });

    group('Integration Tests', () {
      test('should handle complete tag creation workflow', () {
        // Valid creation
        final validResult = validator.validateTagCreation(
          name: 'Personal',
          color: '#4CAF50',
        );
        expect(validResult.isValid, isTrue);

        // Invalid creation
        final invalidResult = validator.validateTagCreation(
          name: 'A',
          color: 'invalid',
        );
        expect(invalidResult.isValid, isFalse);
        expect(invalidResult.errors.length, equals(2));
      });

      test('should handle complete tag update workflow', () {
        final tag = Tag.create(
          name: 'Work',
          color: '#2196F3',
          userId: 'user123',
        );

        // No changes
        final noChanges = validator.validateTagUpdate(
          currentTag: tag,
          newName: tag.name,
          newColor: tag.color,
        );
        expect(noChanges.isValid, isTrue);

        // Valid changes
        final validChanges = validator.validateTagUpdate(
          currentTag: tag,
          newName: 'Business',
          newColor: '#FF5722',
        );
        expect(validChanges.isValid, isTrue);

        // Invalid changes
        final invalidChanges = validator.validateTagUpdate(
          currentTag: tag,
          newName: '',
          newColor: 'invalid',
        );
        expect(invalidChanges.isValid, isFalse);
      });

      test('should provide consistent validation across methods', () {
        const testName = 'Test Tag';
        const testColor = '#FF5722';

        final creationResult = validator.validateTagCreation(
          name: testName,
          color: testColor,
        );

        final nameResult = validator.validateTagName(testName);
        final colorResult = validator.validateTagColor(testColor);

        expect(
          creationResult.isValid,
          equals(nameResult.isValid && colorResult.isValid),
        );
      });
    });

    group('Provider Integration Tests', () {
      test('should create TagValidators through provider', () {
        // Test the generated provider code path
        final ref = ProviderContainer();

        // Act - Access validator through provider
        final validatorFromProvider = ref.read(tagValidatorsProvider);

        // Assert - Should create instance successfully
        expect(validatorFromProvider, isNotNull);
        expect(validatorFromProvider, isA<TagValidators>());

        // Test that the validator works correctly
        final result = validatorFromProvider.validateTagName('Test Tag');
        expect(result.isValid, isTrue);

        ref.dispose();
      });

      test('should provide same instance through provider', () {
        final ref = ProviderContainer();

        // Act - Access provider multiple times
        final validator1 = ref.read(tagValidatorsProvider);
        final validator2 = ref.read(tagValidatorsProvider);

        // Assert - Should be the same instance (due to AutoDispose caching)
        expect(validator1, same(validator2));

        ref.dispose();
      });

      test('should handle provider disposal correctly', () {
        final ref = ProviderContainer();

        // Act - Read provider and dispose container
        final validator = ref.read(tagValidatorsProvider);
        expect(validator, isNotNull);

        ref.dispose();

        // Assert - Should not throw during disposal
        expect(ref.dispose, returnsNormally);
      });

      test('should work with provider overrides', () {
        // Arrange - Create custom validator for testing
        final customValidator = TagValidators();

        final ref = ProviderContainer(
          overrides: [tagValidatorsProvider.overrideWithValue(customValidator)],
        );

        // Act - Access overridden provider
        final validator = ref.read(tagValidatorsProvider);

        // Assert - Should return the overridden instance
        expect(validator, same(customValidator));

        ref.dispose();
      });
    });

    group('Generated Code Coverage Tests', () {
      test('should test provider metadata', () {
        // Test provider name and other metadata
        expect(tagValidatorsProvider.name, equals('tagValidatorsProvider'));
        expect(tagValidatorsProvider.dependencies, isNull);
        expect(tagValidatorsProvider.allTransitiveDependencies, isNull);
      });

      test('should access provider through generated interface', () {
        final ref = ProviderContainer();

        // Test accessing through the provider interface
        final validator = ref.read(tagValidatorsProvider);
        expect(validator, isA<TagValidators>());

        // Test that it implements expected functionality
        final result = validator.getSuggestedColors();
        expect(result, isNotEmpty);

        ref.dispose();
      });

      test('should exercise generated provider internal methods', () {
        // This tests various paths through the generated code
        final ref = ProviderContainer();

        // Test multiple accesses to trigger caching paths
        final validator1 = ref.read(tagValidatorsProvider);
        final validator2 = ref.read(tagValidatorsProvider);
        final validator3 = ref.read(tagValidatorsProvider);

        // All should return the same instance
        expect(validator1, same(validator2));
        expect(validator2, same(validator3));

        // Test that functionality works through all instances
        expect(validator1.validateTagName('Test'), isA<ValidationResult>());
        expect(validator2.validateTagColor('#FF5722'), isA<ValidationResult>());
        expect(validator3.getSuggestedColors(), isA<List<String>>());

        ref.dispose();
      });

      test('should test AutoDispose provider behavior', () {
        final ref = ProviderContainer();

        // Access the provider to create instance
        final validator = ref.read(tagValidatorsProvider);
        expect(validator, isNotNull);

        // AutoDispose providers should clean up properly
        ref.dispose();

        // Create new container and verify fresh instance
        final ref2 = ProviderContainer();
        final validator2 = ref2.read(tagValidatorsProvider);
        expect(validator2, isNotNull);

        ref2.dispose();
      });
    });
  });
}
