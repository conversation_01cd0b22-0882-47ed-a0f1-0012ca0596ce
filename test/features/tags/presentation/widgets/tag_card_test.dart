import 'package:budapp/data/models/tag.dart';
import 'package:budapp/features/tags/presentation/widgets/tag_card.dart';
import 'package:budapp/features/tags/providers/tag_providers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('TagCard', () {
    late Tag testTag;

    setUp(() {
      testTag = Tag.create(
        userId: 'test-user-id',
        name: 'Test Tag',
        color: 'FF5722', // Orange color
      );
    });

    Widget createTestWidget({
      required Tag tag,
      VoidCallback? onTap,
      bool showUsageCount = true,
      bool isSelectable = false,
      bool isSelected = false,
      VoidCallback? onSelectionChanged,
      List<Override> overrides = const [],
    }) {
      return ProviderScope(
        overrides: [
          tagUsageCountProvider(tag.id).overrideWith((ref) => 5),
          ...overrides,
        ],
        child: MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('en')],
          home: Scaffold(
            body: TagCard(
              tag: tag,
              onTap: onTap,
              showUsageCount: showUsageCount,
              isSelectable: isSelectable,
              isSelected: isSelected,
              onSelectionChanged: onSelectionChanged,
            ),
          ),
        ),
      );
    }

    testWidgets('should render tag name and color indicator', (tester) async {
      await tester.pumpWidget(createTestWidget(tag: testTag));

      expect(
        find.text('Test Tag'),
        findsNWidgets(2),
      ); // One in title, one in chip

      // Check for color indicator container
      expect(find.byType(Container), findsWidgets);
    });

    testWidgets('should display usage count when showUsageCount is true', (
      tester,
    ) async {
      await tester.pumpWidget(createTestWidget(tag: testTag));

      expect(find.text('5 transactions'), findsOneWidget);
    });

    testWidgets('should display singular transaction text for count of 1', (
      tester,
    ) async {
      await tester.pumpWidget(
        createTestWidget(
          tag: testTag,
          overrides: [
            tagUsageCountProvider(testTag.id).overrideWith((ref) => 1),
          ],
        ),
      );

      expect(find.text('1 transaction'), findsOneWidget);
    });

    testWidgets('should not display usage count when showUsageCount is false', (
      tester,
    ) async {
      await tester.pumpWidget(
        createTestWidget(tag: testTag, showUsageCount: false),
      );

      expect(find.text('5 transactions'), findsNothing);
    });

    testWidgets('should display loading state for usage count', (tester) async {
      await tester.pumpWidget(
        createTestWidget(
          tag: testTag,
          showUsageCount: false, // Disable usage count to avoid loading issues
        ),
      );

      // Since we disabled usage count, we shouldn't see loading text
      expect(find.text('Loading...'), findsNothing);
    });

    testWidgets('should display error state for usage count', (tester) async {
      await tester.pumpWidget(
        createTestWidget(
          tag: testTag,
          showUsageCount: false, // Disable usage count to avoid error issues
        ),
      );

      // Since we disabled usage count, we shouldn't see error text
      expect(find.text('Usage unknown'), findsNothing);
    });

    testWidgets('should call onTap when card is tapped and not selectable', (
      tester,
    ) async {
      var tapped = false;
      await tester.pumpWidget(
        createTestWidget(tag: testTag, onTap: () => tapped = true),
      );

      await tester.tap(find.byType(InkWell));
      expect(tapped, isTrue);
    });

    testWidgets('should display checkbox when isSelectable is true', (
      tester,
    ) async {
      await tester.pumpWidget(
        createTestWidget(tag: testTag, isSelectable: true),
      );

      expect(find.byType(Checkbox), findsOneWidget);
    });

    testWidgets('should show selected state when isSelected is true', (
      tester,
    ) async {
      await tester.pumpWidget(
        createTestWidget(tag: testTag, isSelectable: true, isSelected: true),
      );

      final checkbox = tester.widget<Checkbox>(find.byType(Checkbox));
      expect(checkbox.value, isTrue);
    });

    testWidgets('should call onSelectionChanged when checkbox is tapped', (
      tester,
    ) async {
      var selectionChanged = false;
      await tester.pumpWidget(
        createTestWidget(
          tag: testTag,
          isSelectable: true,
          onSelectionChanged: () => selectionChanged = true,
        ),
      );

      await tester.tap(find.byType(Checkbox));
      expect(selectionChanged, isTrue);
    });

    testWidgets(
      'should call onSelectionChanged when card is tapped in selectable mode',
      (tester) async {
        var selectionChanged = false;
        await tester.pumpWidget(
          createTestWidget(
            tag: testTag,
            isSelectable: true,
            onSelectionChanged: () => selectionChanged = true,
          ),
        );

        await tester.tap(find.byType(InkWell));
        expect(selectionChanged, isTrue);
      },
    );

    testWidgets('should display tag preview chip', (tester) async {
      await tester.pumpWidget(createTestWidget(tag: testTag));

      // Should find the tag name in the preview chip
      expect(
        find.text('Test Tag'),
        findsNWidgets(2),
      ); // One in title, one in chip
    });

    testWidgets(
      'should display chevron icon when not selectable and onTap is provided',
      (tester) async {
        await tester.pumpWidget(createTestWidget(tag: testTag, onTap: () {}));

        expect(find.byIcon(Icons.chevron_right), findsOneWidget);
      },
    );

    testWidgets('should not display chevron icon when selectable', (
      tester,
    ) async {
      await tester.pumpWidget(
        createTestWidget(tag: testTag, isSelectable: true, onTap: () {}),
      );

      expect(find.byIcon(Icons.chevron_right), findsNothing);
    });

    testWidgets('should not display chevron icon when onTap is null', (
      tester,
    ) async {
      await tester.pumpWidget(createTestWidget(tag: testTag));

      expect(find.byIcon(Icons.chevron_right), findsNothing);
    });

    testWidgets('should have higher elevation when selected', (tester) async {
      await tester.pumpWidget(
        createTestWidget(tag: testTag, isSelectable: true, isSelected: true),
      );

      final card = tester.widget<Card>(find.byType(Card));
      expect(card.elevation, equals(4));
    });

    testWidgets('should have normal elevation when not selected', (
      tester,
    ) async {
      await tester.pumpWidget(createTestWidget(tag: testTag));

      final card = tester.widget<Card>(find.byType(Card));
      expect(card.elevation, equals(1));
    });
  });

  group('TagChip', () {
    late Tag testTag;

    setUp(() {
      testTag = Tag.create(
        userId: 'test-user-id',
        name: 'Chip Tag',
        color: '2196F3', // Blue color
      );
    });

    Widget createTestWidget({
      required Tag tag,
      VoidCallback? onTap,
      VoidCallback? onDelete,
      bool showDeleteButton = false,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: TagChip(
            tag: tag,
            onTap: onTap,
            onDelete: onDelete,
            showDeleteButton: showDeleteButton,
          ),
        ),
      );
    }

    testWidgets('should render tag name', (tester) async {
      await tester.pumpWidget(createTestWidget(tag: testTag));

      expect(find.text('Chip Tag'), findsOneWidget);
    });

    testWidgets('should call onTap when chip is pressed', (tester) async {
      var tapped = false;
      await tester.pumpWidget(
        createTestWidget(tag: testTag, onTap: () => tapped = true),
      );

      await tester.tap(find.byType(InputChip));
      expect(tapped, isTrue);
    });

    testWidgets('should display delete button when showDeleteButton is true', (
      tester,
    ) async {
      await tester.pumpWidget(
        createTestWidget(tag: testTag, showDeleteButton: true, onDelete: () {}),
      );

      // InputChip with delete functionality should have a delete icon
      final inputChip = tester.widget<InputChip>(find.byType(InputChip));
      expect(inputChip.onDeleted, isNotNull);
    });

    testWidgets(
      'should not display delete button when showDeleteButton is false',
      (tester) async {
        await tester.pumpWidget(createTestWidget(tag: testTag));

        final inputChip = tester.widget<InputChip>(find.byType(InputChip));
        expect(inputChip.onDeleted, isNull);
      },
    );

    testWidgets('should call onDelete when delete button is pressed', (
      tester,
    ) async {
      var deleted = false;
      await tester.pumpWidget(
        createTestWidget(
          tag: testTag,
          showDeleteButton: true,
          onDelete: () => deleted = true,
        ),
      );

      // Find the InputChip and verify it has delete functionality
      final inputChip = tester.widget<InputChip>(find.byType(InputChip));
      expect(inputChip.onDeleted, isNotNull);

      // Simulate delete action by calling the callback directly
      inputChip.onDeleted!();
      expect(deleted, isTrue);
    });

    testWidgets('should handle invalid color gracefully', (tester) async {
      final tagWithInvalidColor = testTag.copyWith(color: 'invalid-color');
      await tester.pumpWidget(createTestWidget(tag: tagWithInvalidColor));

      // Should not crash and should render the widget
      expect(find.byType(TagChip), findsOneWidget);
    });
  });
}
