import 'package:budapp/features/tags/presentation/widgets/tags_empty_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/mock_data_factory.dart';
import '../../../../helpers/mock_providers.dart';
import '../../../../helpers/test_wrapper.dart';

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(MockProviders.mockUser);
    registerFallbackValue(MockDataFactory.createTransaction());
    registerFallbackValue(MockDataFactory.createAccount());
    registerFallbackValue(MockDataFactory.createCategory());
    registerFallbackValue(MockDataFactory.createUserProfile());
    registerFallbackValue(Uri());
  });

  setUp(() {
    MockProviders.resetMocks();
    MockProviders.setupDefaultMocks();
  });

  group('TagsEmptyState Tests', () {
    testWidgets('should instantiate without errors', (
      WidgetTester tester,
    ) async {
      const widget = TagsEmptyState();
      expect(widget, isA<TagsEmptyState>());
      expect(widget.key, isNull);
    });

    testWidgets('should be a StatelessWidget', (WidgetTester tester) async {
      const widget = TagsEmptyState();
      expect(widget, isA<StatelessWidget>());
    });

    testWidgets('should render basic UI elements', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TagsEmptyState(),
          overrides: MockProviders.authenticatedUserOverrides(),
        ),
      );

      // Verify main UI elements are present
      expect(find.text('No Tags Yet'), findsOneWidget);
      expect(
        find.text(
          'Tags help you organize and categorize your transactions for better tracking and analysis.',
        ),
        findsOneWidget,
      );
      expect(find.text('Create Your First Tag'), findsOneWidget);
      expect(find.byIcon(Icons.label_outline), findsOneWidget);
    });

    testWidgets('should display benefits section', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TagsEmptyState(),
          overrides: MockProviders.authenticatedUserOverrides(),
        ),
      );

      // Verify benefits section
      expect(find.text('Benefits of Using Tags'), findsOneWidget);

      // Verify individual benefits
      expect(find.text('Filter Transactions'), findsOneWidget);
      expect(
        find.text('Quickly find transactions by specific tags'),
        findsOneWidget,
      );
      expect(find.byIcon(Icons.filter_list), findsOneWidget);

      expect(find.text('Better Analysis'), findsOneWidget);
      expect(
        find.text('Analyze spending patterns by categories'),
        findsOneWidget,
      );
      expect(find.byIcon(Icons.analytics_outlined), findsOneWidget);

      expect(find.text('Stay Organized'), findsOneWidget);
      expect(
        find.text('Keep your finances well-organized and structured'),
        findsOneWidget,
      );
      expect(find.byIcon(Icons.folder_outlined), findsOneWidget);
    });

    testWidgets('should handle action button tap', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TagsEmptyState(),
          overrides: MockProviders.authenticatedUserOverrides(),
        ),
      );

      // Find and tap the action button
      final actionButton = find.text('Create Your First Tag');
      expect(actionButton, findsOneWidget);

      await tester.tap(actionButton);
      await tester.pump();

      // Note: Testing navigation requires more complex setup with GoRouter
      // This test verifies the widget structure is correct
      expect(find.byType(TagsEmptyState), findsOneWidget);
    });

    testWidgets('should render with correct styling', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TagsEmptyState(),
          overrides: MockProviders.authenticatedUserOverrides(),
        ),
      );

      // Verify Card widget is present
      expect(find.byType(Card), findsOneWidget);

      // Verify benefit containers are present (main icon container + 3 benefit icon containers)
      expect(
        find.byType(Container),
        findsNWidgets(4),
      ); // Main icon + 3 benefit icons

      // Verify proper layout structure
      expect(
        find.byType(Column),
        findsNWidgets(5),
      ); // Main column + 4 benefit columns
      expect(
        find.byType(Row),
        findsNWidgets(4),
      ); // One for each benefit + action button row
    });

    testWidgets('should handle widget lifecycle correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TagsEmptyState(),
          overrides: MockProviders.authenticatedUserOverrides(),
        ),
      );

      // Verify the widget renders without errors
      expect(find.byType(TagsEmptyState), findsOneWidget);

      // Test widget disposal
      await tester.pumpWidget(Container());
      expect(find.byType(TagsEmptyState), findsNothing);
    });

    testWidgets('should display all required text content', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TagsEmptyState(),
          overrides: MockProviders.authenticatedUserOverrides(),
        ),
      );

      // Verify all text content is present
      final expectedTexts = [
        'No Tags Yet',
        'Tags help you organize and categorize your transactions for better tracking and analysis.',
        'Create Your First Tag',
        'Benefits of Using Tags',
        'Filter Transactions',
        'Quickly find transactions by specific tags',
        'Better Analysis',
        'Analyze spending patterns by categories',
        'Stay Organized',
        'Keep your finances well-organized and structured',
      ];

      for (final text in expectedTexts) {
        expect(
          find.text(text),
          findsOneWidget,
          reason: 'Text "$text" should be present',
        );
      }
    });

    testWidgets('should display all required icons', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TagsEmptyState(),
          overrides: MockProviders.authenticatedUserOverrides(),
        ),
      );

      // Verify all icons are present
      final expectedIcons = [
        Icons.label_outline,
        Icons.filter_list,
        Icons.analytics_outlined,
        Icons.folder_outlined,
      ];

      for (final icon in expectedIcons) {
        expect(
          find.byIcon(icon),
          findsOneWidget,
          reason: 'Icon $icon should be present',
        );
      }
    });
  });
}
