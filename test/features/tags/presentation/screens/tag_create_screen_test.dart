import 'package:budapp/data/models/tag.dart';
import 'package:budapp/data/repositories/interfaces/tag_repository.dart';
import 'package:budapp/features/tags/presentation/screens/tag_create_screen.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/widgets/forms/screens/generic_form_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';

// Mock classes
class MockTagRepository extends Mock implements TagRepository {}

void main() {
  group('TagCreateScreen Widget Tests', () {
    late MockTagRepository mockTagRepository;

    setUp(() {
      mockTagRepository = MockTagRepository();

      // Register fallback values for mocktail
      registerFallbackValue(
        Tag.create(userId: 'test-user-id', name: 'Test Tag', color: '#FF0000'),
      );
    });

    group('Widget Rendering', () {
      testWidgets('should render TagCreateScreen widget', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        expect(find.byType(TagCreateScreen), findsOneWidget);
      });

      testWidgets('should render GenericFormScreen with correct type', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        expect(find.byType(GenericFormScreen<Tag>), findsOneWidget);
      });

      testWidgets('should create TagFormConfig with create mode', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify the form screen is present and properly configured
        final genericFormScreen = tester.widget<GenericFormScreen<Tag>>(
          find.byType(GenericFormScreen<Tag>),
        );

        expect(genericFormScreen.config, isNotNull);
        expect(genericFormScreen.config.title, equals('Create Tag'));
      });
    });

    group('Provider Integration', () {
      testWidgets('should watch tagRepositoryProvider', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // The widget should have been built without errors, indicating provider was accessed
        expect(find.byType(TagCreateScreen), findsOneWidget);
      });

      testWidgets('should pass repository to TagFormConfig', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify the GenericFormScreen was created successfully with the config
        expect(find.byType(GenericFormScreen<Tag>), findsOneWidget);
      });
    });

    group('Form Configuration', () {
      testWidgets('should create form config with correct title', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final genericFormScreen = tester.widget<GenericFormScreen<Tag>>(
          find.byType(GenericFormScreen<Tag>),
        );

        expect(genericFormScreen.config.title, equals('Create Tag'));
      });

      testWidgets('should configure form with onFieldChanged callback', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final genericFormScreen = tester.widget<GenericFormScreen<Tag>>(
          find.byType(GenericFormScreen<Tag>),
        );

        expect(genericFormScreen.config.onFieldChanged, isNotNull);
      });

      testWidgets('should have form fields configured', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final genericFormScreen = tester.widget<GenericFormScreen<Tag>>(
          find.byType(GenericFormScreen<Tag>),
        );

        expect(genericFormScreen.config.fields, isNotEmpty);
        expect(
          genericFormScreen.config.fields.length,
          equals(2),
        ); // name and color fields
      });
    });

    group('Widget Structure', () {
      testWidgets('should be a ConsumerWidget', (tester) async {
        const widget = TagCreateScreen();
        expect(widget, isA<ConsumerWidget>());
      });

      testWidgets('should have correct build method structure', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        // Verify that the widget builds successfully and contains expected components
        expect(find.byType(TagCreateScreen), findsOneWidget);
        expect(find.byType(GenericFormScreen<Tag>), findsOneWidget);
      });

      testWidgets('should handle repository access through WidgetRef', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Widget should build successfully indicating WidgetRef works correctly
        expect(find.byType(TagCreateScreen), findsOneWidget);
      });
    });

    group('Debug Functionality', () {
      testWidgets('should handle onFieldChanged callback for debugging', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final genericFormScreen = tester.widget<GenericFormScreen<Tag>>(
          find.byType(GenericFormScreen<Tag>),
        );

        // Verify the callback is properly configured
        expect(genericFormScreen.config.onFieldChanged, isNotNull);

        // Test that the callback can be called (simulating field change)
        expect(
          () => genericFormScreen.config.onFieldChanged?.call('name', 'test'),
          returnsNormally,
        );
      });
    });

    group('Widget Properties', () {
      testWidgets('should have correct key property', (tester) async {
        const testKey = Key('test-tag-create-screen');

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(key: testKey),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        expect(find.byKey(testKey), findsOneWidget);
      });

      testWidgets('should work with default constructor', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        expect(find.byType(TagCreateScreen), findsOneWidget);
      });
    });

    group('Integration with Generic Form System', () {
      testWidgets('should pass correct Tag type to GenericFormScreen', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify correct generic type usage
        expect(find.byType(GenericFormScreen<Tag>), findsOneWidget);
      });

      testWidgets('should provide TagFormConfig.create configuration', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        await tester.pumpAndSettle();

        final genericFormScreen = tester.widget<GenericFormScreen<Tag>>(
          find.byType(GenericFormScreen<Tag>),
        );

        // Verify it's using create configuration (not edit)
        expect(genericFormScreen.config.title, equals('Create Tag'));
        expect(genericFormScreen.config.initialData, isNull);
        expect(genericFormScreen.config.showDeleteButton, isFalse);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle repository provider properly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        // Widget should build successfully with repository
        expect(find.byType(TagCreateScreen), findsOneWidget);
      });
    });

    group('Widget Lifecycle', () {
      testWidgets('should build and dispose without errors', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const TagCreateScreen(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        await tester.pumpAndSettle();
        expect(find.byType(TagCreateScreen), findsOneWidget);

        // Remove widget
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const SizedBox(),
            overrides: [
              tagRepositoryProvider.overrideWithValue(mockTagRepository),
            ],
          ),
        );

        expect(find.byType(TagCreateScreen), findsNothing);
      });
    });
  });
}
