import 'dart:async';

import 'package:budapp/data/models/tag.dart';
import 'package:budapp/data/repositories/interfaces/tag_repository.dart';
import 'package:budapp/features/tags/providers/tag_providers.dart';
import 'package:budapp/features/tags/services/tag_validators.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/utils/validation_result.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

void main() {
  group('Tag Providers Tests', () {
    late ProviderContainer container;
    late MockTagRepository mockRepository;
    late MockTagValidators mockValidators;

    const testTagId = 'test-tag-123';
    const testTransactionId = 'test-transaction-123';

    setUp(() {
      mockRepository = MockTagRepository();
      mockValidators = MockTagValidators();

      container = ProviderContainer(
        overrides: [
          tagRepositoryProvider.overrideWithValue(mockRepository),
          tagValidatorsProvider.overrideWithValue(mockValidators),
        ],
      );

      // Register fallback values
      registerFallbackValue(_createTestTag());
      registerFallbackValue(const ValidationResult(isValid: true, errors: []));
    });

    tearDown(() {
      container.dispose();
    });

    group('Data Provider Tests', () {
      test('userTags should return stream of user tags', () async {
        // Arrange
        final mockTags = [
          _createTestTag(id: 'tag-1', name: 'Food'),
          _createTestTag(id: 'tag-2', name: 'Transport'),
        ];
        when(
          () => mockRepository.watchUserTags(),
        ).thenAnswer((_) => Stream.value(mockTags));

        // Act
        final result = await container.read(userTagsProvider.future);

        // Assert
        expect(result, equals(mockTags));
        verify(() => mockRepository.watchUserTags()).called(1);
      });

      test('tagById should return specific tag', () async {
        // Arrange
        final tag = _createTestTag(id: testTagId);
        when(
          () => mockRepository.getTagById(testTagId),
        ).thenAnswer((_) async => tag);

        // Act
        final result = await container.read(tagByIdProvider(testTagId).future);

        // Assert
        expect(result, equals(tag));
        verify(() => mockRepository.getTagById(testTagId)).called(1);
      });

      test('tagById should return null for non-existent tag', () async {
        // Arrange
        when(
          () => mockRepository.getTagById(testTagId),
        ).thenAnswer((_) async => null);

        // Act
        final result = await container.read(tagByIdProvider(testTagId).future);

        // Assert
        expect(result, isNull);
        verify(() => mockRepository.getTagById(testTagId)).called(1);
      });

      test('tagsForTransaction should return tags for transaction', () async {
        // Arrange
        final mockTags = [_createTestTag(id: 'tag-1', name: 'Food')];
        when(
          () => mockRepository.getTagsForTransaction(testTransactionId),
        ).thenAnswer((_) async => mockTags);

        // Act
        final result = await container.read(
          tagsForTransactionProvider(testTransactionId).future,
        );

        // Assert
        expect(result, equals(mockTags));
        verify(
          () => mockRepository.getTagsForTransaction(testTransactionId),
        ).called(1);
      });

      test('tagUsageCount should return usage count', () async {
        // Arrange
        const usageCount = 5;
        when(
          () => mockRepository.getTagUsageCount(testTagId),
        ).thenAnswer((_) async => usageCount);

        // Act
        final result = await container.read(
          tagUsageCountProvider(testTagId).future,
        );

        // Assert
        expect(result, equals(usageCount));
        verify(() => mockRepository.getTagUsageCount(testTagId)).called(1);
      });
    });

    group('Tag Search Tests', () {
      test('TagSearch should initially return all tags', () async {
        // Arrange
        final mockTags = [_createTestTag(id: 'tag-1', name: 'Food')];
        when(
          () => mockRepository.searchTags(''),
        ).thenAnswer((_) async => mockTags);

        // Act
        final result = await container.read(tagSearchProvider.future);

        // Assert
        expect(result, equals(mockTags));
        verify(() => mockRepository.searchTags('')).called(1);
      });

      test('TagSearch should search tags by term', () async {
        // Arrange
        final mockTags = [_createTestTag(id: 'tag-1', name: 'Food')];
        when(
          () => mockRepository.searchTags('food'),
        ).thenAnswer((_) async => mockTags);

        final notifier = container.read(tagSearchProvider.notifier);

        // Act
        await notifier.searchTags('food');

        // Assert
        final state = container.read(tagSearchProvider);
        expect(state.hasValue, isTrue);
        expect(state.value, equals(mockTags));
        verify(() => mockRepository.searchTags('food')).called(1);
      });

      // TODO(test): Fix this test - the error state is not being properly captured
      // test('TagSearch should handle search errors', () async {
      //   // Arrange
      //   final error = Exception('Search failed');
      //   when(() => mockRepository.searchTags('')).thenAnswer((_) async => []);
      //   when(() => mockRepository.searchTags('error')).thenThrow(error);

      //   final notifier = container.read(tagSearchProvider.notifier);

      //   // Act
      //   await notifier.searchTags('error');

      //   // Assert
      //   final state = container.read(tagSearchProvider);
      //   expect(state.hasError, isTrue);
      //   expect(state.error, equals(error));
      // });

      test('TagSearch should clear search and return all tags', () async {
        // Arrange
        final mockTags = [_createTestTag(id: 'tag-1', name: 'Food')];
        when(
          () => mockRepository.searchTags(''),
        ).thenAnswer((_) async => mockTags);

        final notifier = container.read(tagSearchProvider.notifier);

        // Act
        await notifier.clearSearch();

        // Assert
        final state = container.read(tagSearchProvider);
        expect(state.hasValue, isTrue);
        expect(state.value, equals(mockTags));
        verify(
          () => mockRepository.searchTags(''),
        ).called(2); // Once in build, once in clearSearch
      });

      test('TagSearch should track loading state', () async {
        // Arrange
        final completer = Completer<List<Tag>>();
        when(
          () => mockRepository.searchTags('loading'),
        ).thenAnswer((_) => completer.future);

        final notifier = container.read(tagSearchProvider.notifier);

        // Act
        final searchFuture = notifier.searchTags('loading');

        // Assert loading state
        final loadingState = container.read(tagSearchProvider);
        expect(loadingState.isLoading, isTrue);

        // Complete operation
        completer.complete([_createTestTag()]);
        await searchFuture;

        // Assert final state
        final finalState = container.read(tagSearchProvider);
        expect(finalState.hasValue, isTrue);
      });
    });

    group('Tag Creation Tests', () {
      test('TagCreator should create tag successfully', () async {
        // Arrange
        const name = 'Food';
        const color = '#FF5722';
        const validationResult = ValidationResult(isValid: true, errors: []);
        final createdTag = _createTestTag(name: name, color: color);

        when(
          () => mockValidators.validateTagCreation(name: name, color: color),
        ).thenReturn(validationResult);
        when(
          () => mockRepository.createTag(any()),
        ).thenAnswer((_) async => createdTag);

        final notifier = container.read(tagCreatorProvider.notifier);

        // Act
        final result = await notifier.createTag(name: name, color: color);

        // Assert
        expect(result, equals(createdTag));
        final state = container.read(tagCreatorProvider);
        expect(state.hasValue, isTrue);
        expect(state.value, equals(createdTag));

        verify(
          () => mockValidators.validateTagCreation(name: name, color: color),
        ).called(1);
        verify(() => mockRepository.createTag(any())).called(1);
      });

      test('TagCreator should handle validation errors', () async {
        // Arrange
        const name = '';
        const color = '#FF5722';
        const validationResult = ValidationResult(
          isValid: false,
          errors: ['Tag name cannot be empty'],
        );

        when(
          () => mockValidators.validateTagCreation(name: name, color: color),
        ).thenReturn(validationResult);

        final notifier = container.read(tagCreatorProvider.notifier);

        // Act & Assert
        expect(
          () => notifier.createTag(name: name, color: color),
          throwsA(isA<TagValidationException>()),
        );

        verify(
          () => mockValidators.validateTagCreation(name: name, color: color),
        ).called(1);
        verifyNever(() => mockRepository.createTag(any()));
      });

      test('TagCreator should handle repository errors', () async {
        // Arrange
        const name = 'Food';
        const color = '#FF5722';
        const validationResult = ValidationResult(isValid: true, errors: []);
        final error = Exception('Repository error');

        when(
          () => mockValidators.validateTagCreation(name: name, color: color),
        ).thenReturn(validationResult);
        when(() => mockRepository.createTag(any())).thenThrow(error);

        final notifier = container.read(tagCreatorProvider.notifier);

        // Act & Assert
        expect(
          () => notifier.createTag(name: name, color: color),
          throwsA(equals(error)),
        );

        final state = container.read(tagCreatorProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(error));
      });

      test('TagCreator should track loading state', () async {
        // Arrange
        const name = 'Food';
        const color = '#FF5722';
        const validationResult = ValidationResult(isValid: true, errors: []);
        final completer = Completer<Tag>();

        when(
          () => mockValidators.validateTagCreation(name: name, color: color),
        ).thenReturn(validationResult);
        when(
          () => mockRepository.createTag(any()),
        ).thenAnswer((_) => completer.future);

        final notifier = container.read(tagCreatorProvider.notifier);

        // Act
        final createFuture = notifier.createTag(name: name, color: color);

        // Assert loading state
        final loadingState = container.read(tagCreatorProvider);
        expect(loadingState.isLoading, isTrue);

        // Complete operation
        completer.complete(_createTestTag());
        await createFuture;

        // Assert final state
        final finalState = container.read(tagCreatorProvider);
        expect(finalState.hasValue, isTrue);
      });

      test(
        'TagCreator should invalidate related providers after creation',
        () async {
          // Arrange
          const name = 'Food';
          const color = '#FF5722';
          const validationResult = ValidationResult(isValid: true, errors: []);
          final createdTag = _createTestTag(name: name, color: color);

          when(
            () => mockValidators.validateTagCreation(name: name, color: color),
          ).thenReturn(validationResult);
          when(
            () => mockRepository.createTag(any()),
          ).thenAnswer((_) async => createdTag);

          // Setup mock responses for providers that will be invalidated
          when(
            () => mockRepository.watchUserTags(),
          ).thenAnswer((_) => Stream.value([]));
          when(() => mockRepository.searchTags('')).thenAnswer((_) async => []);

          final notifier = container.read(tagCreatorProvider.notifier);

          // Act
          await notifier.createTag(name: name, color: color);

          // Assert - verify creation was called
          verify(() => mockRepository.createTag(any())).called(1);
        },
      );
    });

    group('Tag Update Tests', () {
      test('TagUpdater should update tag successfully', () async {
        // Arrange
        const name = 'Updated Food';
        const color = '#FF9800';
        final currentTag = _createTestTag(
          id: testTagId,
          name: 'Food',
          color: '#FF5722',
        );
        const validationResult = ValidationResult(isValid: true, errors: []);
        final updatedTag = currentTag.copyWith(name: name, color: color);

        when(
          () => mockRepository.getTagById(testTagId),
        ).thenAnswer((_) async => currentTag);
        when(
          () => mockValidators.validateTagUpdate(
            currentTag: currentTag,
            newName: name,
            newColor: color,
          ),
        ).thenReturn(validationResult);
        when(
          () => mockRepository.updateTag(any()),
        ).thenAnswer((_) async => updatedTag);

        final notifier = container.read(tagUpdaterProvider.notifier);

        // Reset mock call counts
        reset(mockRepository);
        reset(mockValidators);

        // Re-setup mocks after reset
        when(
          () => mockRepository.getTagById(testTagId),
        ).thenAnswer((_) async => currentTag);
        when(
          () => mockValidators.validateTagUpdate(
            currentTag: currentTag,
            newName: name,
            newColor: color,
          ),
        ).thenReturn(validationResult);
        when(
          () => mockRepository.updateTag(any()),
        ).thenAnswer((_) async => updatedTag);

        // Act
        final result = await notifier.updateTag(
          tagId: testTagId,
          name: name,
          color: color,
        );

        // Assert
        expect(result, equals(updatedTag));
        final state = container.read(tagUpdaterProvider);
        expect(state.hasValue, isTrue);
        expect(state.value, equals(updatedTag));

        verify(
          () => mockRepository.getTagById(testTagId),
        ).called(greaterThan(0));
        verify(
          () => mockValidators.validateTagUpdate(
            currentTag: currentTag,
            newName: name,
            newColor: color,
          ),
        ).called(1);
        verify(() => mockRepository.updateTag(any())).called(1);
      });

      test('TagUpdater should handle tag not found', () async {
        // Arrange
        const name = 'Updated Food';
        const color = '#FF9800';

        when(
          () => mockRepository.getTagById(testTagId),
        ).thenAnswer((_) async => null);

        final notifier = container.read(tagUpdaterProvider.notifier);

        // Act & Assert
        expect(
          () => notifier.updateTag(tagId: testTagId, name: name, color: color),
          throwsA(isA<TagNotFoundException>()),
        );

        verify(() => mockRepository.getTagById(testTagId)).called(1);
        verifyNever(
          () => mockValidators.validateTagUpdate(
            currentTag: any(named: 'currentTag'),
            newName: any(named: 'newName'),
            newColor: any(named: 'newColor'),
          ),
        );
        verifyNever(() => mockRepository.updateTag(any()));
      });

      test('TagUpdater should handle validation errors', () async {
        // Arrange
        const name = '';
        const color = '#FF9800';
        final currentTag = _createTestTag(id: testTagId);
        const validationResult = ValidationResult(
          isValid: false,
          errors: ['Tag name cannot be empty'],
        );

        // Create a fresh container for this test
        final testContainer = ProviderContainer(
          overrides: [
            tagRepositoryProvider.overrideWithValue(mockRepository),
            tagValidatorsProvider.overrideWithValue(mockValidators),
          ],
        );

        when(
          () => mockRepository.getTagById(testTagId),
        ).thenAnswer((_) async => currentTag);
        when(
          () => mockValidators.validateTagUpdate(
            currentTag: currentTag,
            newName: name,
            newColor: color,
          ),
        ).thenReturn(validationResult);

        final notifier = testContainer.read(tagUpdaterProvider.notifier);

        // Act & Assert
        await expectLater(
          () => notifier.updateTag(tagId: testTagId, name: name, color: color),
          throwsA(isA<TagValidationException>()),
        );

        testContainer.dispose();
      });

      test('TagUpdater should track loading state', () async {
        // Arrange
        const name = 'Updated Food';
        const color = '#FF9800';
        final currentTag = _createTestTag(id: testTagId);
        const validationResult = ValidationResult(isValid: true, errors: []);
        final completer = Completer<Tag>();

        when(
          () => mockRepository.getTagById(testTagId),
        ).thenAnswer((_) async => currentTag);
        when(
          () => mockValidators.validateTagUpdate(
            currentTag: currentTag,
            newName: name,
            newColor: color,
          ),
        ).thenReturn(validationResult);
        when(
          () => mockRepository.updateTag(any()),
        ).thenAnswer((_) => completer.future);

        final notifier = container.read(tagUpdaterProvider.notifier);

        // Act
        final updateFuture = notifier.updateTag(
          tagId: testTagId,
          name: name,
          color: color,
        );

        // Assert loading state
        final loadingState = container.read(tagUpdaterProvider);
        expect(loadingState.isLoading, isTrue);

        // Complete operation
        completer.complete(_createTestTag());
        await updateFuture;

        // Assert final state
        final finalState = container.read(tagUpdaterProvider);
        expect(finalState.hasValue, isTrue);
      });
    });

    group('Tag Deletion Tests', () {
      test('TagDeleter should delete tag successfully', () async {
        // Arrange
        when(
          () => mockRepository.deleteTag(testTagId),
        ).thenAnswer((_) async {});

        final notifier = container.read(tagDeleterProvider.notifier);

        // Act
        await notifier.deleteTag(testTagId);

        // Assert
        final state = container.read(tagDeleterProvider);
        expect(state.hasValue, isTrue);
        verify(() => mockRepository.deleteTag(testTagId)).called(1);
      });

      test('TagDeleter should handle deletion errors', () async {
        // Arrange
        final error = Exception('Deletion failed');
        when(() => mockRepository.deleteTag(testTagId)).thenThrow(error);

        final notifier = container.read(tagDeleterProvider.notifier);

        // Act & Assert
        expect(() => notifier.deleteTag(testTagId), throwsA(equals(error)));

        final state = container.read(tagDeleterProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(error));
      });

      test('TagDeleter should track loading state', () async {
        // Arrange
        final completer = Completer<void>();
        when(
          () => mockRepository.deleteTag(testTagId),
        ).thenAnswer((_) => completer.future);

        final notifier = container.read(tagDeleterProvider.notifier);

        // Act
        final deleteFuture = notifier.deleteTag(testTagId);

        // Assert loading state
        final loadingState = container.read(tagDeleterProvider);
        expect(loadingState.isLoading, isTrue);

        // Complete operation
        completer.complete();
        await deleteFuture;

        // Assert final state
        final finalState = container.read(tagDeleterProvider);
        expect(finalState.hasValue, isTrue);
      });

      test('TagDeleter should get tag usage count', () async {
        // Arrange
        const usageCount = 3;
        when(
          () => mockRepository.getTagUsageCount(testTagId),
        ).thenAnswer((_) async => usageCount);

        final notifier = container.read(tagDeleterProvider.notifier);

        // Act
        final result = await notifier.getTagUsageCount(testTagId);

        // Assert
        expect(result, equals(usageCount));
        verify(() => mockRepository.getTagUsageCount(testTagId)).called(1);
      });

      test(
        'TagDeleter should invalidate related providers after deletion',
        () async {
          // Arrange
          when(
            () => mockRepository.deleteTag(testTagId),
          ).thenAnswer((_) async {});

          // Setup mock responses for providers that will be invalidated
          when(
            () => mockRepository.watchUserTags(),
          ).thenAnswer((_) => Stream.value([]));
          when(() => mockRepository.searchTags('')).thenAnswer((_) async => []);
          when(
            () => mockRepository.getTagById(testTagId),
          ).thenAnswer((_) async => null);

          final notifier = container.read(tagDeleterProvider.notifier);

          // Act
          await notifier.deleteTag(testTagId);

          // Assert - verify deletion was called
          verify(() => mockRepository.deleteTag(testTagId)).called(1);
        },
      );
    });
  });
}

// Helper function to create test tags
Tag _createTestTag({
  String? id,
  String? userId,
  String? name,
  String? color,
  int? usageCount,
}) {
  final now = Timestamp.now();
  return Tag(
    id: id ?? 'test-tag-id',
    userId: userId ?? 'test-user-123',
    name: name ?? 'Test Tag',
    color: color ?? '#FF5722',
    usageCount: usageCount ?? 0,
    createdAt: now,
    updatedAt: now,
  );
}

// Mock classes
class MockTagRepository extends Mock implements TagRepository {}

class MockTagValidators extends Mock implements TagValidators {}
