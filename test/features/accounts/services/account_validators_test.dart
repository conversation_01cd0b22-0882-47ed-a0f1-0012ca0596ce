import 'package:budapp/data/models/account.dart';
import 'package:budapp/features/accounts/services/account_validators.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../helpers/test_wrapper.dart';

void main() {
  group('AccountValidators', () {
    testWidgets('accountName validation', (WidgetTester tester) async {
      late BuildContext context;

      final widget = TestWrapper.createTestWidget(
        Builder(
          builder: (BuildContext ctx) {
            context = ctx;
            return const SizedBox.shrink();
          },
        ),
      );

      await tester.pumpWidget(widget);

      // Test valid account names
      final validNames = [
        'My Checking Account',
        'Savings-2024',
        'Cash & Change',
        'Primary (Main)',
        'Investment_Portfolio',
        'Emergency.Fund',
      ];

      for (final name in validNames) {
        expect(
          AccountValidators.accountName(name, context),
          isNull,
          reason: 'Failed for valid name: $name',
        );
      }

      // Test invalid names
      expect(
        AccountValidators.accountName(null, context),
        equals(AppLocalizations.of(context)!.accountNameRequired),
      );
      expect(
        AccountValidators.accountName('', context),
        equals(AppLocalizations.of(context)!.accountNameRequired),
      );
      expect(
        AccountValidators.accountName('   ', context),
        equals(AppLocalizations.of(context)!.accountNameRequired),
      );
      expect(
        AccountValidators.accountName('A', context),
        equals(AppLocalizations.of(context)!.accountNameMinLength),
      );

      final longName = 'A' * 51;
      expect(
        AccountValidators.accountName(longName, context),
        equals(AppLocalizations.of(context)!.accountNameMaxLength),
      );

      // Test invalid characters
      expect(
        AccountValidators.accountName('Account@Home', context),
        equals(AppLocalizations.of(context)!.accountNameInvalidCharacters),
      );
    });

    testWidgets('accountDescription validation', (WidgetTester tester) async {
      late BuildContext context;

      final widget = TestWrapper.createTestWidget(
        Builder(
          builder: (BuildContext ctx) {
            context = ctx;
            return const SizedBox.shrink();
          },
        ),
      );

      await tester.pumpWidget(widget);

      // Test valid descriptions
      expect(
        AccountValidators.accountDescription('A valid description', context),
        isNull,
      );
      expect(AccountValidators.accountDescription(null, context), isNull);
      expect(AccountValidators.accountDescription('', context), isNull);

      // Test invalid description
      final longDescription = 'A' * 201;
      expect(
        AccountValidators.accountDescription(longDescription, context),
        equals(AppLocalizations.of(context)!.accountDescriptionMaxLength),
      );
    });

    testWidgets('initialBalance validation', (WidgetTester tester) async {
      late BuildContext context;

      final widget = TestWrapper.createTestWidget(
        Builder(
          builder: (BuildContext ctx) {
            context = ctx;
            return const SizedBox.shrink();
          },
        ),
      );

      await tester.pumpWidget(widget);

      // Test valid balances
      final validBalances = [
        '0',
        '0.00',
        '100',
        '100.50',
        '1000.99',
        '-500.00',
        '*********.99',
      ];

      for (final balance in validBalances) {
        expect(
          AccountValidators.initialBalance(balance, context),
          isNull,
          reason: 'Failed for valid balance: $balance',
        );
      }

      // Test invalid balances
      expect(
        AccountValidators.initialBalance(null, context),
        equals(AppLocalizations.of(context)!.initialBalanceRequired),
      );
      expect(
        AccountValidators.initialBalance('', context),
        equals(AppLocalizations.of(context)!.initialBalanceRequired),
      );
      expect(
        AccountValidators.initialBalance('abc', context),
        equals(AppLocalizations.of(context)!.initialBalanceInvalid),
      );
      expect(
        AccountValidators.initialBalance('100.123', context),
        equals(AppLocalizations.of(context)!.initialBalanceMaxDecimals),
      );
      expect(
        AccountValidators.initialBalance('**********.00', context),
        equals(AppLocalizations.of(context)!.initialBalanceOutOfRange),
      );
    });

    // Currency validation removed - using global currency preference

    testWidgets('accountType validation', (WidgetTester tester) async {
      late BuildContext context;

      final widget = TestWrapper.createTestWidget(
        Builder(
          builder: (BuildContext ctx) {
            context = ctx;
            return const SizedBox.shrink();
          },
        ),
      );

      await tester.pumpWidget(widget);

      // Test valid types
      for (final type in AccountType.values) {
        expect(AccountValidators.accountType(type, context), isNull);
      }

      // Test invalid type
      expect(
        AccountValidators.accountType(null, context),
        equals(AppLocalizations.of(context)!.accountTypeRequired),
      );
    });

    testWidgets('type-classification pairing validation', (
      WidgetTester tester,
    ) async {
      late BuildContext context;

      final widget = TestWrapper.createTestWidget(
        Builder(
          builder: (BuildContext ctx) {
            context = ctx;
            return const SizedBox.shrink();
          },
        ),
      );

      await tester.pumpWidget(widget);

      // Test valid pairs
      expect(
        AccountValidators.validateTypeClassificationPair(
          AccountType.checking,
          AccountClassification.asset,
          context,
        ),
        isNull,
      );
      expect(
        AccountValidators.validateTypeClassificationPair(
          AccountType.creditCard,
          AccountClassification.liability,
          context,
        ),
        isNull,
      );

      // Test invalid pairs
      expect(
        AccountValidators.validateTypeClassificationPair(
          AccountType.checking,
          AccountClassification.liability,
          context,
        ),
        equals(AppLocalizations.of(context)!.accountTypeClassificationMismatch),
      );
    });

    testWidgets('comprehensive account validation', (
      WidgetTester tester,
    ) async {
      late BuildContext context;

      final widget = TestWrapper.createTestWidget(
        Builder(
          builder: (BuildContext ctx) {
            context = ctx;
            return const SizedBox.shrink();
          },
        ),
      );

      await tester.pumpWidget(widget);

      // Test valid account data
      final validErrors = AccountValidators.validateAccount(
        name: 'Test Account',
        description: 'A test account',
        initialBalance: '1000.50',

        type: AccountType.checking,
        classification: AccountClassification.asset,
        colorHex: '#FF5733',
        iconName: 'account_balance',
        context: context,
      );
      expect(validErrors, isEmpty);

      // Test invalid account data
      final invalidErrors = AccountValidators.validateAccount(
        name: '', // Invalid: empty
        description: 'A' * 201, // Invalid: too long
        initialBalance: 'invalid', // Invalid: not a number
        type: null, // Invalid: null
        classification: null, // Invalid: null
        colorHex: 'invalid', // Invalid: not hex
        iconName: '123invalid', // Invalid: starts with number
        context: context,
      );
      expect(
        invalidErrors,
        hasLength(7),
      ); // Correct count after currency removal

      // Test form validity helpers
      expect(
        AccountValidators.isAccountFormValid(
          name: 'Test Account',
          initialBalance: '1000.50',

          type: AccountType.checking,
          classification: AccountClassification.asset,
          context: context,
        ),
        isTrue,
      );

      expect(
        AccountValidators.isAccountFormValid(
          name: '', // Invalid
          initialBalance: '1000.50',

          type: AccountType.checking,
          classification: AccountClassification.asset,
          context: context,
        ),
        isFalse,
      );
    });

    test('balance conversion utilities', () {
      // Test parseBalanceToCents
      expect(AccountValidators.parseBalanceToCents('0'), equals(0));
      expect(AccountValidators.parseBalanceToCents('1'), equals(100));
      expect(AccountValidators.parseBalanceToCents('1.50'), equals(150));
      expect(AccountValidators.parseBalanceToCents('100.99'), equals(10099));
      expect(AccountValidators.parseBalanceToCents('-50.25'), equals(-5025));
      expect(AccountValidators.parseBalanceToCents(null), isNull);
      expect(AccountValidators.parseBalanceToCents(''), isNull);
      expect(AccountValidators.parseBalanceToCents('invalid'), isNull);

      // Test formatCentsToBalance
      expect(AccountValidators.formatCentsToBalance(0), equals('0.00'));
      expect(AccountValidators.formatCentsToBalance(100), equals('1.00'));
      expect(AccountValidators.formatCentsToBalance(150), equals('1.50'));
      expect(AccountValidators.formatCentsToBalance(10099), equals('100.99'));
      expect(AccountValidators.formatCentsToBalance(-5025), equals('-50.25'));
    });
  });
}
