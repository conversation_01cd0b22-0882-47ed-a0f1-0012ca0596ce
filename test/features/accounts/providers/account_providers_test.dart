import 'dart:async';

import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/repositories/interfaces/repositories.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/mock_data_factory.dart';

void main() {
  group('Account Providers Simplified Tests', () {
    late ProviderContainer container;
    late MockAccountRepository mockAccountRepository;
    late MockAuthService mockAuthService;
    late MockUser mockUser;

    setUp(() {
      mockAccountRepository = MockAccountRepository();
      mockAuthService = MockAuthService();
      mockUser = MockUser();

      // Setup mock user
      when(() => mockUser.uid).thenReturn('test-uid');
      when(() => mockAuthService.currentUser).thenReturn(mockUser);

      container = ProviderContainer(
        overrides: [
          accountRepositoryProvider.overrideWithValue(mockAccountRepository),
          authServiceProvider.overrideWithValue(mockAuthService),
        ],
      );

      // Register fallback values
      registerFallbackValue(MockDataFactory.createAccount());
    });

    tearDown(() {
      container.dispose();
    });

    group('Core Provider Tests', () {
      test('accountListProvider should return user accounts', () async {
        // Arrange
        final mockAccounts = [
          MockDataFactory.createAccount(id: 'account-1'),
          MockDataFactory.createAccount(id: 'account-2'),
        ];
        when(
          () => mockAccountRepository.watchUserAccounts('test-uid'),
        ).thenAnswer((_) => Stream.value(mockAccounts));

        // Act
        final accounts = await container.read(accountListProvider.future);

        // Assert
        expect(accounts, equals(mockAccounts));
        verify(
          () => mockAccountRepository.watchUserAccounts('test-uid'),
        ).called(1);
      });

      test('accountProvider should return specific account', () async {
        // Arrange
        const accountId = 'account-123';
        final account = MockDataFactory.createAccount(id: accountId);
        when(
          () =>
              mockAccountRepository.watchAccountForUser('test-uid', accountId),
        ).thenAnswer((_) => Stream.value(account));

        // Act
        final result = await container.read(accountProvider(accountId).future);

        // Assert
        expect(result, equals(account));
      });

      test('primaryAccountProvider should return primary account', () async {
        // Arrange
        final primaryAccount = MockDataFactory.createAccount(id: 'primary');
        when(
          () => mockAccountRepository.getPrimaryAccount('test-uid'),
        ).thenAnswer((_) async => primaryAccount);

        // Act
        final result = await container.read(primaryAccountProvider.future);

        // Assert
        expect(result, equals(primaryAccount));
      });
    });

    group('Creation and Update Tests', () {
      test('AccountCreationNotifier should create account', () async {
        // Arrange
        final account = MockDataFactory.createAccount();
        when(
          () => mockAccountRepository.createAccount(account),
        ).thenAnswer((_) async => 'created-id');

        final notifier = container.read(accountCreationProvider.notifier);

        // Act
        await notifier.createAccount(account);

        // Assert
        final state = container.read(accountCreationProvider);
        expect(state.hasValue, isTrue);
        verify(() => mockAccountRepository.createAccount(account)).called(1);
      });

      test('AccountUpdateNotifier should update account', () async {
        // Arrange
        final account = MockDataFactory.createAccount(id: 'account-123');
        when(
          () => mockAccountRepository.updateAccount('account-123', account),
        ).thenAnswer((_) async {});

        final notifier = container.read(accountUpdateProvider.notifier);

        // Act
        await notifier.updateAccount(account);

        // Assert
        final state = container.read(accountUpdateProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockAccountRepository.updateAccount('account-123', account),
        ).called(1);
      });

      test('AccountUpdateNotifier should delete account', () async {
        // Arrange
        const accountId = 'account-123';
        when(
          () => mockAccountRepository.deleteAccount(accountId),
        ).thenAnswer((_) async {});

        final notifier = container.read(accountUpdateProvider.notifier);

        // Act
        await notifier.deleteAccount(accountId);

        // Assert
        final state = container.read(accountUpdateProvider);
        expect(state.hasValue, isTrue);
        verify(() => mockAccountRepository.deleteAccount(accountId)).called(1);
      });

      test('AccountUpdateNotifier should set primary account', () async {
        // Arrange
        const accountId = 'account-123';
        when(
          () => mockAccountRepository.setPrimaryAccount('test-uid', accountId),
        ).thenAnswer((_) async {});

        final notifier = container.read(accountUpdateProvider.notifier);

        // Act
        await notifier.setPrimaryAccount(accountId);

        // Assert
        final state = container.read(accountUpdateProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockAccountRepository.setPrimaryAccount('test-uid', accountId),
        ).called(1);
      });
    });

    group('Error Handling Tests', () {
      test('should handle authentication errors', () async {
        // Arrange
        when(() => mockAuthService.currentUser).thenReturn(null);
        final unauthContainer = ProviderContainer(
          overrides: [
            accountRepositoryProvider.overrideWithValue(mockAccountRepository),
            authServiceProvider.overrideWithValue(mockAuthService),
          ],
        );

        // Act & Assert
        expect(
          () => unauthContainer.read(accountListProvider.future),
          throwsA(isA<Exception>()),
        );

        unauthContainer.dispose();
      });

      test('should handle repository errors', () async {
        // Arrange
        when(
          () => mockAccountRepository.watchUserAccounts('test-uid'),
        ).thenAnswer((_) => Stream.error(Exception('Database error')));

        // Act & Assert
        expect(
          () => container.read(accountListProvider.future),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle creation errors', () async {
        // Arrange
        final account = MockDataFactory.createAccount();
        final error = Exception('Creation failed');
        when(
          () => mockAccountRepository.createAccount(account),
        ).thenThrow(error);

        final notifier = container.read(accountCreationProvider.notifier);

        // Act
        await notifier.createAccount(account);

        // Assert
        final state = container.read(accountCreationProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(error));
      });
    });

    group('Loading State Tests', () {
      test('should track loading state during creation', () async {
        // Arrange
        final account = MockDataFactory.createAccount();
        final completer = Completer<String>();
        when(
          () => mockAccountRepository.createAccount(account),
        ).thenAnswer((_) => completer.future);

        final notifier = container.read(accountCreationProvider.notifier);

        // Act
        final createFuture = notifier.createAccount(account);

        // Assert loading state
        final loadingState = container.read(accountCreationProvider);
        expect(loadingState.isLoading, isTrue);

        // Complete operation
        completer.complete('created-id');
        await createFuture;

        // Assert final state
        final finalState = container.read(accountCreationProvider);
        expect(finalState.hasValue, isTrue);
      });

      test('should track loading state during update', () async {
        // Arrange
        final account = MockDataFactory.createAccount(id: 'account-123');
        final completer = Completer<void>();
        when(
          () => mockAccountRepository.updateAccount('account-123', account),
        ).thenAnswer((_) => completer.future);

        final notifier = container.read(accountUpdateProvider.notifier);

        // Act
        final updateFuture = notifier.updateAccount(account);

        // Assert loading state
        final loadingState = container.read(accountUpdateProvider);
        expect(loadingState.isLoading, isTrue);

        // Complete operation
        completer.complete();
        await updateFuture;

        // Assert final state
        final finalState = container.read(accountUpdateProvider);
        expect(finalState.hasValue, isTrue);
      });
    });

    group('Filtering Logic Tests', () {
      test('should filter active accounts correctly', () {
        // Arrange
        final accounts = [
          MockDataFactory.createAccount(id: 'active-1', isActive: true),
          MockDataFactory.createAccount(id: 'inactive-1', isActive: false),
          MockDataFactory.createAccount(id: 'active-2', isActive: true),
        ];

        // Act
        final activeAccounts = accounts
            .where((account) => account.isActive)
            .toList();

        // Assert
        expect(activeAccounts.length, equals(2));
        expect(activeAccounts.every((account) => account.isActive), isTrue);
        expect(
          activeAccounts.map((a) => a.id),
          containsAll(['active-1', 'active-2']),
        );
      });

      test('should filter asset accounts correctly', () {
        // Arrange
        final accounts = [
          MockDataFactory.createAccount(
            id: 'asset-1',
            classification: AccountClassification.asset,
          ),
          MockDataFactory.createAccount(
            id: 'liability-1',
            classification: AccountClassification.liability,
          ),
          MockDataFactory.createAccount(
            id: 'asset-2',
            classification: AccountClassification.asset,
          ),
        ];

        // Act
        final assetAccounts = accounts
            .where(
              (account) =>
                  account.classification == AccountClassification.asset,
            )
            .toList();

        // Assert
        expect(assetAccounts.length, equals(2));
        expect(
          assetAccounts.every(
            (account) => account.classification == AccountClassification.asset,
          ),
          isTrue,
        );
        expect(
          assetAccounts.map((a) => a.id),
          containsAll(['asset-1', 'asset-2']),
        );
      });

      test('should filter liability accounts correctly', () {
        // Arrange
        final accounts = [
          MockDataFactory.createAccount(
            id: 'asset-1',
            classification: AccountClassification.asset,
          ),
          MockDataFactory.createAccount(
            id: 'liability-1',
            classification: AccountClassification.liability,
          ),
          MockDataFactory.createAccount(
            id: 'liability-2',
            classification: AccountClassification.liability,
          ),
        ];

        // Act
        final liabilityAccounts = accounts
            .where(
              (account) =>
                  account.classification == AccountClassification.liability,
            )
            .toList();

        // Assert
        expect(liabilityAccounts.length, equals(2));
        expect(
          liabilityAccounts.every(
            (account) =>
                account.classification == AccountClassification.liability,
          ),
          isTrue,
        );
        expect(
          liabilityAccounts.map((a) => a.id),
          containsAll(['liability-1', 'liability-2']),
        );
      });

      test('should handle empty account lists', () {
        // Arrange
        final accounts = <Account>[];

        // Act
        final activeAccounts = accounts
            .where((account) => account.isActive)
            .toList();
        final assetAccounts = accounts
            .where(
              (account) =>
                  account.classification == AccountClassification.asset,
            )
            .toList();
        final liabilityAccounts = accounts
            .where(
              (account) =>
                  account.classification == AccountClassification.liability,
            )
            .toList();

        // Assert
        expect(activeAccounts, isEmpty);
        expect(assetAccounts, isEmpty);
        expect(liabilityAccounts, isEmpty);
      });

      test('should handle mixed classifications and activity states', () {
        // Arrange
        final accounts = [
          MockDataFactory.createAccount(
            id: 'active-asset',
            classification: AccountClassification.asset,
            isActive: true,
          ),
          MockDataFactory.createAccount(
            id: 'inactive-asset',
            classification: AccountClassification.asset,
            isActive: false,
          ),
          MockDataFactory.createAccount(
            id: 'active-liability',
            classification: AccountClassification.liability,
            isActive: true,
          ),
          MockDataFactory.createAccount(
            id: 'inactive-liability',
            classification: AccountClassification.liability,
            isActive: false,
          ),
        ];

        // Act
        final activeAccounts = accounts
            .where((account) => account.isActive)
            .toList();
        final assetAccounts = accounts
            .where(
              (account) =>
                  account.classification == AccountClassification.asset,
            )
            .toList();
        final liabilityAccounts = accounts
            .where(
              (account) =>
                  account.classification == AccountClassification.liability,
            )
            .toList();

        // Assert
        expect(activeAccounts.length, equals(2));
        expect(
          activeAccounts.map((a) => a.id),
          containsAll(['active-asset', 'active-liability']),
        );

        expect(assetAccounts.length, equals(2));
        expect(
          assetAccounts.map((a) => a.id),
          containsAll(['active-asset', 'inactive-asset']),
        );

        expect(liabilityAccounts.length, equals(2));
        expect(
          liabilityAccounts.map((a) => a.id),
          containsAll(['active-liability', 'inactive-liability']),
        );
      });
    });

    group('Account Stats Calculation Tests', () {
      test('should calculate correct statistics for mixed accounts', () {
        // Arrange
        final accounts = [
          MockDataFactory.createAccount(
            id: 'asset-1',
            classification: AccountClassification.asset,
            initialBalanceCents: 100000, // $1000
            isActive: true,
          ),
          MockDataFactory.createAccount(
            id: 'asset-2',
            classification: AccountClassification.asset,
            initialBalanceCents: 50000, // $500
            isActive: true,
          ),
          MockDataFactory.createAccount(
            id: 'liability-1',
            classification: AccountClassification.liability,
            initialBalanceCents: 30000, // $300
            isActive: true,
          ),
          MockDataFactory.createAccount(
            id: 'inactive-asset',
            classification: AccountClassification.asset,
            initialBalanceCents: 200000, // $2000 - should be excluded
            isActive: false,
          ),
        ];

        // Act - Simulate the stats calculation logic
        final activeAccounts = accounts.where((a) => a.isActive).toList();
        final assetAccounts = activeAccounts.where(
          (a) => a.classification == AccountClassification.asset,
        );
        final liabilityAccounts = activeAccounts.where(
          (a) => a.classification == AccountClassification.liability,
        );

        final totalAssets = assetAccounts.fold<int>(
          0,
          (sum, account) => sum + account.currentBalanceCents,
        );
        final totalLiabilities = liabilityAccounts.fold<int>(
          0,
          (sum, account) => sum + account.currentBalanceCents,
        );
        final netWorth = totalAssets - totalLiabilities;

        final stats = AccountStats(
          totalAccounts: activeAccounts.length,
          totalAssets: totalAssets,
          totalLiabilities: totalLiabilities,
          netWorth: netWorth,
          assetAccountCount: assetAccounts.length,
          liabilityAccountCount: liabilityAccounts.length,
        );

        // Assert
        expect(stats.totalAccounts, equals(3)); // Only active accounts
        expect(stats.totalAssets, equals(150000)); // $1000 + $500
        expect(stats.totalLiabilities, equals(30000)); // $300
        expect(stats.netWorth, equals(120000)); // $1500 - $300 = $1200
        expect(stats.assetAccountCount, equals(2));
        expect(stats.liabilityAccountCount, equals(1));
      });

      test('should handle zero balances correctly', () {
        // Arrange
        final accounts = [
          MockDataFactory.createAccount(
            id: 'zero-asset',
            classification: AccountClassification.asset,
            initialBalanceCents: 0,
            isActive: true,
          ),
          MockDataFactory.createAccount(
            id: 'zero-liability',
            classification: AccountClassification.liability,
            initialBalanceCents: 0,
            isActive: true,
          ),
        ];

        // Act - Simulate the stats calculation logic
        final activeAccounts = accounts.where((a) => a.isActive).toList();
        final assetAccounts = activeAccounts.where(
          (a) => a.classification == AccountClassification.asset,
        );
        final liabilityAccounts = activeAccounts.where(
          (a) => a.classification == AccountClassification.liability,
        );

        final totalAssets = assetAccounts.fold<int>(
          0,
          (sum, account) => sum + account.currentBalanceCents,
        );
        final totalLiabilities = liabilityAccounts.fold<int>(
          0,
          (sum, account) => sum + account.currentBalanceCents,
        );
        final netWorth = totalAssets - totalLiabilities;

        final stats = AccountStats(
          totalAccounts: activeAccounts.length,
          totalAssets: totalAssets,
          totalLiabilities: totalLiabilities,
          netWorth: netWorth,
          assetAccountCount: assetAccounts.length,
          liabilityAccountCount: liabilityAccounts.length,
        );

        // Assert
        expect(stats.totalAccounts, equals(2));
        expect(stats.totalAssets, equals(0));
        expect(stats.totalLiabilities, equals(0));
        expect(stats.netWorth, equals(0));
        expect(stats.assetAccountCount, equals(1));
        expect(stats.liabilityAccountCount, equals(1));
      });

      test('should handle negative net worth correctly', () {
        // Arrange
        final accounts = [
          MockDataFactory.createAccount(
            id: 'small-asset',
            classification: AccountClassification.asset,
            initialBalanceCents: 10000, // $100
            isActive: true,
          ),
          MockDataFactory.createAccount(
            id: 'large-liability',
            classification: AccountClassification.liability,
            initialBalanceCents: 50000, // $500
            isActive: true,
          ),
        ];

        // Act - Simulate the stats calculation logic
        final activeAccounts = accounts.where((a) => a.isActive).toList();
        final assetAccounts = activeAccounts.where(
          (a) => a.classification == AccountClassification.asset,
        );
        final liabilityAccounts = activeAccounts.where(
          (a) => a.classification == AccountClassification.liability,
        );

        final totalAssets = assetAccounts.fold<int>(
          0,
          (sum, account) => sum + account.currentBalanceCents,
        );
        final totalLiabilities = liabilityAccounts.fold<int>(
          0,
          (sum, account) => sum + account.currentBalanceCents,
        );
        final netWorth = totalAssets - totalLiabilities;

        final stats = AccountStats(
          totalAccounts: activeAccounts.length,
          totalAssets: totalAssets,
          totalLiabilities: totalLiabilities,
          netWorth: netWorth,
          assetAccountCount: assetAccounts.length,
          liabilityAccountCount: liabilityAccounts.length,
        );

        // Assert
        expect(stats.totalAccounts, equals(2));
        expect(stats.totalAssets, equals(10000));
        expect(stats.totalLiabilities, equals(50000));
        expect(stats.netWorth, equals(-40000)); // $100 - $500 = -$400
        expect(stats.assetAccountCount, equals(1));
        expect(stats.liabilityAccountCount, equals(1));
      });

      test('should handle empty account list correctly', () {
        // Arrange
        final accounts = <Account>[];

        // Act - Simulate the stats calculation logic
        final activeAccounts = accounts.where((a) => a.isActive).toList();
        final assetAccounts = activeAccounts.where(
          (a) => a.classification == AccountClassification.asset,
        );
        final liabilityAccounts = activeAccounts.where(
          (a) => a.classification == AccountClassification.liability,
        );

        final totalAssets = assetAccounts.fold<int>(
          0,
          (sum, account) => sum + account.currentBalanceCents,
        );
        final totalLiabilities = liabilityAccounts.fold<int>(
          0,
          (sum, account) => sum + account.currentBalanceCents,
        );
        final netWorth = totalAssets - totalLiabilities;

        final stats = AccountStats(
          totalAccounts: activeAccounts.length,
          totalAssets: totalAssets,
          totalLiabilities: totalLiabilities,
          netWorth: netWorth,
          assetAccountCount: assetAccounts.length,
          liabilityAccountCount: liabilityAccounts.length,
        );

        // Assert
        expect(stats.totalAccounts, equals(0));
        expect(stats.totalAssets, equals(0));
        expect(stats.totalLiabilities, equals(0));
        expect(stats.netWorth, equals(0));
        expect(stats.assetAccountCount, equals(0));
        expect(stats.liabilityAccountCount, equals(0));
      });

      test('should handle only inactive accounts correctly', () {
        // Arrange
        final accounts = [
          MockDataFactory.createAccount(
            id: 'inactive-asset',
            classification: AccountClassification.asset,
            initialBalanceCents: 100000,
            isActive: false,
          ),
          MockDataFactory.createAccount(
            id: 'inactive-liability',
            classification: AccountClassification.liability,
            initialBalanceCents: 50000,
            isActive: false,
          ),
        ];

        // Act - Simulate the stats calculation logic
        final activeAccounts = accounts.where((a) => a.isActive).toList();
        final assetAccounts = activeAccounts.where(
          (a) => a.classification == AccountClassification.asset,
        );
        final liabilityAccounts = activeAccounts.where(
          (a) => a.classification == AccountClassification.liability,
        );

        final totalAssets = assetAccounts.fold<int>(
          0,
          (sum, account) => sum + account.currentBalanceCents,
        );
        final totalLiabilities = liabilityAccounts.fold<int>(
          0,
          (sum, account) => sum + account.currentBalanceCents,
        );
        final netWorth = totalAssets - totalLiabilities;

        final stats = AccountStats(
          totalAccounts: activeAccounts.length,
          totalAssets: totalAssets,
          totalLiabilities: totalLiabilities,
          netWorth: netWorth,
          assetAccountCount: assetAccounts.length,
          liabilityAccountCount: liabilityAccounts.length,
        );

        // Assert
        expect(stats.totalAccounts, equals(0)); // No active accounts
        expect(stats.totalAssets, equals(0));
        expect(stats.totalLiabilities, equals(0));
        expect(stats.netWorth, equals(0));
        expect(stats.assetAccountCount, equals(0));
        expect(stats.liabilityAccountCount, equals(0));
      });

      test('should handle large balance amounts correctly', () {
        // Arrange
        final accounts = [
          MockDataFactory.createAccount(
            id: 'large-asset',
            classification: AccountClassification.asset,
            initialBalanceCents: *********, // Very large amount
            isActive: true,
          ),
          MockDataFactory.createAccount(
            id: 'large-liability',
            classification: AccountClassification.liability,
            initialBalanceCents: *********, // Large amount
            isActive: true,
          ),
        ];

        // Act - Simulate the stats calculation logic
        final activeAccounts = accounts.where((a) => a.isActive).toList();
        final assetAccounts = activeAccounts.where(
          (a) => a.classification == AccountClassification.asset,
        );
        final liabilityAccounts = activeAccounts.where(
          (a) => a.classification == AccountClassification.liability,
        );

        final totalAssets = assetAccounts.fold<int>(
          0,
          (sum, account) => sum + account.currentBalanceCents,
        );
        final totalLiabilities = liabilityAccounts.fold<int>(
          0,
          (sum, account) => sum + account.currentBalanceCents,
        );
        final netWorth = totalAssets - totalLiabilities;

        final stats = AccountStats(
          totalAccounts: activeAccounts.length,
          totalAssets: totalAssets,
          totalLiabilities: totalLiabilities,
          netWorth: netWorth,
          assetAccountCount: assetAccounts.length,
          liabilityAccountCount: liabilityAccounts.length,
        );

        // Assert
        expect(stats.totalAccounts, equals(2));
        expect(stats.totalAssets, equals(*********));
        expect(stats.totalLiabilities, equals(*********));
        expect(stats.netWorth, equals(*********)); // ********* - *********
        expect(stats.assetAccountCount, equals(1));
        expect(stats.liabilityAccountCount, equals(1));
      });

      test('should handle negative balance amounts correctly', () {
        // Arrange
        final accounts = [
          MockDataFactory.createAccount(
            id: 'negative-asset',
            classification: AccountClassification.asset,
            initialBalanceCents: -50000, // Negative asset (overdraft)
            isActive: true,
          ),
          MockDataFactory.createAccount(
            id: 'negative-liability',
            classification: AccountClassification.liability,
            initialBalanceCents: -30000, // Negative liability (credit balance)
            isActive: true,
          ),
        ];

        // Act - Simulate the stats calculation logic
        final activeAccounts = accounts.where((a) => a.isActive).toList();
        final assetAccounts = activeAccounts.where(
          (a) => a.classification == AccountClassification.asset,
        );
        final liabilityAccounts = activeAccounts.where(
          (a) => a.classification == AccountClassification.liability,
        );

        final totalAssets = assetAccounts.fold<int>(
          0,
          (sum, account) => sum + account.currentBalanceCents,
        );
        final totalLiabilities = liabilityAccounts.fold<int>(
          0,
          (sum, account) => sum + account.currentBalanceCents,
        );
        final netWorth = totalAssets - totalLiabilities;

        final stats = AccountStats(
          totalAccounts: activeAccounts.length,
          totalAssets: totalAssets,
          totalLiabilities: totalLiabilities,
          netWorth: netWorth,
          assetAccountCount: assetAccounts.length,
          liabilityAccountCount: liabilityAccounts.length,
        );

        // Assert
        expect(stats.totalAccounts, equals(2));
        expect(stats.totalAssets, equals(-50000));
        expect(stats.totalLiabilities, equals(-30000));
        expect(stats.netWorth, equals(-20000)); // -50000 - (-30000) = -20000
        expect(stats.assetAccountCount, equals(1));
        expect(stats.liabilityAccountCount, equals(1));
      });
    });

    group('AccountStats Class Tests', () {
      test('AccountStats should create with correct values', () {
        // Act
        const stats = AccountStats(
          totalAccounts: 10,
          totalAssets: 50000,
          totalLiabilities: 20000,
          netWorth: 30000,
          assetAccountCount: 6,
          liabilityAccountCount: 4,
        );

        // Assert
        expect(stats.totalAccounts, equals(10));
        expect(stats.totalAssets, equals(50000));
        expect(stats.totalLiabilities, equals(20000));
        expect(stats.netWorth, equals(30000));
        expect(stats.assetAccountCount, equals(6));
        expect(stats.liabilityAccountCount, equals(4));
      });

      test('AccountStats should support equality comparison', () {
        // Arrange
        const stats1 = AccountStats(
          totalAccounts: 5,
          totalAssets: 25000,
          totalLiabilities: 10000,
          netWorth: 15000,
          assetAccountCount: 3,
          liabilityAccountCount: 2,
        );
        const stats2 = AccountStats(
          totalAccounts: 5,
          totalAssets: 25000,
          totalLiabilities: 10000,
          netWorth: 15000,
          assetAccountCount: 3,
          liabilityAccountCount: 2,
        );
        const stats3 = AccountStats(
          totalAccounts: 6,
          totalAssets: 25000,
          totalLiabilities: 10000,
          netWorth: 15000,
          assetAccountCount: 3,
          liabilityAccountCount: 2,
        );

        // Assert
        expect(stats1, equals(stats2));
        expect(stats1, isNot(equals(stats3)));
        expect(stats1.hashCode, equals(stats2.hashCode));
        expect(stats1.hashCode, isNot(equals(stats3.hashCode)));
      });

      test('AccountStats should have proper toString representation', () {
        // Arrange
        const stats = AccountStats(
          totalAccounts: 10,
          totalAssets: 50000,
          totalLiabilities: 20000,
          netWorth: 30000,
          assetAccountCount: 6,
          liabilityAccountCount: 4,
        );

        // Act
        final stringRepresentation = stats.toString();

        // Assert - AccountStats doesn't have custom toString, so just verify it's a string
        expect(stringRepresentation, isA<String>());
        expect(stringRepresentation, isNotEmpty);
        expect(stringRepresentation, contains('AccountStats'));
      });
    });

    group('Additional AccountUpdateNotifier Tests', () {
      test('AccountUpdateNotifier should deactivate account', () async {
        // Arrange
        const accountId = 'account-123';
        when(
          () => mockAccountRepository.deactivateAccount(accountId),
        ).thenAnswer((_) async {});

        final notifier = container.read(accountUpdateProvider.notifier);

        // Act
        await notifier.deactivateAccount(accountId);

        // Assert
        final state = container.read(accountUpdateProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockAccountRepository.deactivateAccount(accountId),
        ).called(1);
      });

      test(
        'AccountUpdateNotifier should handle concurrent operations',
        () async {
          // Arrange
          final account1 = MockDataFactory.createAccount(id: 'account-1');
          final account2 = MockDataFactory.createAccount(id: 'account-2');
          const accountId3 = 'account-3';

          when(
            () => mockAccountRepository.updateAccount('account-1', account1),
          ).thenAnswer((_) async {});
          when(
            () => mockAccountRepository.updateAccount('account-2', account2),
          ).thenAnswer((_) async {});
          when(
            () => mockAccountRepository.deleteAccount(accountId3),
          ).thenAnswer((_) async {});

          final notifier = container.read(accountUpdateProvider.notifier);

          // Act - Perform concurrent operations
          final futures = await Future.wait([
            notifier.updateAccount(account1),
            notifier.updateAccount(account2),
            notifier.deleteAccount(accountId3),
          ]);

          // Assert
          final state = container.read(accountUpdateProvider);
          expect(state.hasValue, isTrue);
          expect(futures.length, equals(3));
        },
      );

      test(
        'AccountUpdateNotifier should handle rapid successive operations',
        () async {
          // Arrange
          const accountId = 'rapid-account';
          when(
            () => mockAccountRepository.deactivateAccount(accountId),
          ).thenAnswer((_) async {});

          final notifier = container.read(accountUpdateProvider.notifier);

          // Act - Perform rapid successive operations
          for (var i = 0; i < 10; i++) {
            await notifier.deactivateAccount(accountId);
          }

          // Assert
          final state = container.read(accountUpdateProvider);
          expect(state.hasValue, isTrue);
          verify(
            () => mockAccountRepository.deactivateAccount(accountId),
          ).called(10);
        },
      );

      test('AccountUpdateNotifier should handle deactivation errors', () async {
        // Arrange
        const accountId = 'account-123';
        final error = Exception('Deactivation failed');
        when(
          () => mockAccountRepository.deactivateAccount(accountId),
        ).thenThrow(error);

        final notifier = container.read(accountUpdateProvider.notifier);

        // Act
        await notifier.deactivateAccount(accountId);

        // Assert
        final state = container.read(accountUpdateProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(error));
      });

      test(
        'AccountUpdateNotifier should track loading state during deactivation',
        () async {
          // Arrange
          const accountId = 'account-123';
          final completer = Completer<void>();
          when(
            () => mockAccountRepository.deactivateAccount(accountId),
          ).thenAnswer((_) => completer.future);

          final notifier = container.read(accountUpdateProvider.notifier);

          // Act
          final deactivateFuture = notifier.deactivateAccount(accountId);

          // Assert loading state
          final loadingState = container.read(accountUpdateProvider);
          expect(loadingState.isLoading, isTrue);

          // Complete operation
          completer.complete();
          await deactivateFuture;

          // Assert final state
          final finalState = container.read(accountUpdateProvider);
          expect(finalState.hasValue, isTrue);
        },
      );

      test(
        'AccountUpdateNotifier should handle setPrimaryAccount errors',
        () async {
          // Arrange
          const accountId = 'account-123';
          final error = Exception('Set primary failed');
          when(
            () =>
                mockAccountRepository.setPrimaryAccount('test-uid', accountId),
          ).thenThrow(error);

          final notifier = container.read(accountUpdateProvider.notifier);

          // Act
          await notifier.setPrimaryAccount(accountId);

          // Assert
          final state = container.read(accountUpdateProvider);
          expect(state.hasError, isTrue);
          expect(state.error, equals(error));
        },
      );

      test('AccountUpdateNotifier should handle update errors', () async {
        // Arrange
        final account = MockDataFactory.createAccount(id: 'account-123');
        final error = Exception('Update failed');
        when(
          () => mockAccountRepository.updateAccount('account-123', account),
        ).thenThrow(error);

        final notifier = container.read(accountUpdateProvider.notifier);

        // Act
        await notifier.updateAccount(account);

        // Assert
        final state = container.read(accountUpdateProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(error));
      });

      test('AccountUpdateNotifier should handle delete errors', () async {
        // Arrange
        const accountId = 'account-123';
        final error = Exception('Delete failed');
        when(
          () => mockAccountRepository.deleteAccount(accountId),
        ).thenThrow(error);

        final notifier = container.read(accountUpdateProvider.notifier);

        // Act
        await notifier.deleteAccount(accountId);

        // Assert
        final state = container.read(accountUpdateProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(error));
      });

      test(
        'AccountUpdateNotifier should handle authentication errors in setPrimaryAccount',
        () async {
          // Arrange
          const accountId = 'account-123';
          when(() => mockAuthService.currentUser).thenReturn(null);
          final unauthContainer = ProviderContainer(
            overrides: [
              accountRepositoryProvider.overrideWithValue(
                mockAccountRepository,
              ),
              authServiceProvider.overrideWithValue(mockAuthService),
            ],
          );

          final notifier = unauthContainer.read(accountUpdateProvider.notifier);

          // Act
          await notifier.setPrimaryAccount(accountId);

          // Assert
          final state = unauthContainer.read(accountUpdateProvider);
          expect(state.hasError, isTrue);
          expect(state.error, isA<Exception>());

          unauthContainer.dispose();
        },
      );
    });

    group('Provider Dependency Invalidation Tests', () {
      test('should invalidate account list provider after creation', () async {
        // Arrange
        final account = MockDataFactory.createAccount();
        when(
          () => mockAccountRepository.createAccount(account),
        ).thenAnswer((_) async => 'created-id');

        final notifier = container.read(accountCreationProvider.notifier);

        // Act
        await notifier.createAccount(account);

        // Assert - Provider invalidation should occur
        final state = container.read(accountCreationProvider);
        expect(state.hasValue, isTrue);
        verify(() => mockAccountRepository.createAccount(account)).called(1);

        // Verify that the provider was properly set up for invalidation
        expect(container.read(accountCreationProvider.notifier), isNotNull);
      });

      test('should invalidate multiple providers after update', () async {
        // Arrange
        final account = MockDataFactory.createAccount(id: 'test-account');
        when(
          () => mockAccountRepository.updateAccount('test-account', account),
        ).thenAnswer((_) async {});

        final notifier = container.read(accountUpdateProvider.notifier);

        // Act
        await notifier.updateAccount(account);

        // Assert
        final state = container.read(accountUpdateProvider);
        expect(state.hasValue, isTrue);
        verify(
          () => mockAccountRepository.updateAccount('test-account', account),
        ).called(1);
      });

      test(
        'should handle provider invalidation during concurrent operations',
        () async {
          // Arrange
          final account1 = MockDataFactory.createAccount(id: 'account-1');
          final account2 = MockDataFactory.createAccount(id: 'account-2');

          when(
            () => mockAccountRepository.createAccount(account1),
          ).thenAnswer((_) async => 'created-1');
          when(
            () => mockAccountRepository.createAccount(account2),
          ).thenAnswer((_) async => 'created-2');

          final creationNotifier = container.read(
            accountCreationProvider.notifier,
          );

          // Act - Perform concurrent creations
          await Future.wait([
            creationNotifier.createAccount(account1),
            creationNotifier.createAccount(account2),
          ]);

          // Assert
          final state = container.read(accountCreationProvider);
          expect(state.hasValue, isTrue);
          verify(() => mockAccountRepository.createAccount(account1)).called(1);
          verify(() => mockAccountRepository.createAccount(account2)).called(1);
        },
      );
    });

    group('Provider Stream Behavior Tests', () {
      test('should handle stream subscription changes', () async {
        // Arrange
        final streamController = StreamController<List<Account>>();
        when(
          () => mockAccountRepository.watchUserAccounts('test-uid'),
        ).thenAnswer((_) => streamController.stream);

        // Act - Subscribe to provider
        final subscription = container.listen(accountListProvider, (
          previous,
          next,
        ) {
          // Track provider changes
        });

        // Emit multiple values
        final accounts1 = [MockDataFactory.createAccount(id: 'account-1')];
        final accounts2 = [
          MockDataFactory.createAccount(id: 'account-1'),
          MockDataFactory.createAccount(id: 'account-2'),
        ];

        streamController.add(accounts1);
        await Future<void>.delayed(Duration.zero); // Allow provider to update

        streamController.add(accounts2);
        await Future<void>.delayed(Duration.zero); // Allow provider to update

        // Clean up
        subscription.close();
        await streamController.close();

        // Assert
        expect(subscription, isNotNull);
      });

      test('should handle stream errors gracefully', () async {
        // Arrange
        final streamController = StreamController<List<Account>>();
        when(
          () => mockAccountRepository.watchUserAccounts('test-uid'),
        ).thenAnswer((_) => streamController.stream);

        // Act - Subscribe and emit error
        streamController.addError(Exception('Stream error'));

        // Assert - Should handle error without crashing
        expect(
          () => container.read(accountListProvider.future),
          throwsA(isA<Exception>()),
        );

        await streamController.close();
      });

      test('should handle rapid stream updates', () async {
        // Arrange
        final streamController = StreamController<List<Account>>();
        when(
          () => mockAccountRepository.watchUserAccounts('test-uid'),
        ).thenAnswer((_) => streamController.stream);

        // Get the provider to start listening
        final provider = container.read(accountListProvider);

        // Act - Emit rapid updates (reduced count to prevent timeout)
        for (var i = 0; i < 10; i++) {
          final accounts = [MockDataFactory.createAccount(id: 'account-$i')];
          streamController.add(accounts);
          // Small delay to allow processing
          await Future<void>.delayed(const Duration(milliseconds: 1));
        }

        // Wait for the last update to be processed
        await Future<void>.delayed(const Duration(milliseconds: 10));

        // Clean up
        await streamController.close();

        // Assert - Should handle rapid updates without issues
        expect(streamController.isClosed, isTrue);
        // Verify the provider processed the updates
        expect(provider, isA<AsyncValue<List<Account>>>());
      });
    });

    group('Provider Filtering Integration Tests', () {
      test(
        'should filter accounts correctly through multiple providers',
        () async {
          // Arrange
          final mixedAccounts = [
            MockDataFactory.createAccount(
              id: 'active-asset',
              classification: AccountClassification.asset,
              isActive: true,
            ),
            MockDataFactory.createAccount(
              id: 'inactive-asset',
              classification: AccountClassification.asset,
              isActive: false,
            ),
            MockDataFactory.createAccount(
              id: 'active-liability',
              classification: AccountClassification.liability,
              isActive: true,
            ),
            MockDataFactory.createAccount(
              id: 'inactive-liability',
              classification: AccountClassification.liability,
              isActive: false,
            ),
          ];

          when(
            () => mockAccountRepository.watchUserAccounts('test-uid'),
          ).thenAnswer((_) => Stream.value(mixedAccounts));

          // Act & Assert - Test all filtered providers
          final allAccounts = await container.read(accountListProvider.future);
          expect(allAccounts.length, equals(4));

          final activeAccounts = await container.read(
            activeAccountsProvider.future,
          );
          expect(activeAccounts.length, equals(2));
          expect(activeAccounts.every((a) => a.isActive), isTrue);

          final assetAccounts = await container.read(
            assetAccountsProvider.future,
          );
          expect(assetAccounts.length, equals(1)); // Only active assets
          expect(
            assetAccounts.every(
              (a) =>
                  a.classification == AccountClassification.asset && a.isActive,
            ),
            isTrue,
          );

          final liabilityAccounts = await container.read(
            liabilityAccountsProvider.future,
          );
          expect(
            liabilityAccounts.length,
            equals(1),
          ); // Only active liabilities
          expect(
            liabilityAccounts.every(
              (a) =>
                  a.classification == AccountClassification.liability &&
                  a.isActive,
            ),
            isTrue,
          );
        },
      );

      test('should handle empty filter results', () async {
        // Arrange - Accounts that don't match any filters
        final emptyFilterAccounts = [
          MockDataFactory.createAccount(
            id: 'inactive-account',
            classification: AccountClassification.asset,
            isActive: false,
          ),
        ];

        when(
          () => mockAccountRepository.watchUserAccounts('test-uid'),
        ).thenAnswer((_) => Stream.value(emptyFilterAccounts));

        // Act & Assert - Wait for the provider to process the stream
        await Future<void>.delayed(const Duration(milliseconds: 10));

        final activeAccountsAsync = container.read(activeAccountsProvider);
        final activeAccounts = activeAccountsAsync.when(
          data: (accounts) => accounts,
          loading: () => <Account>[],
          error: (_, _) => <Account>[],
        );
        expect(activeAccounts, isEmpty);
      });

      test('should maintain filter consistency across provider updates', () async {
        // Arrange - Test with final account set (simpler approach)
        final accounts = [
          MockDataFactory.createAccount(
            id: 'asset-1',
            classification: AccountClassification.asset,
            isActive: true,
          ),
          MockDataFactory.createAccount(
            id: 'liability-1',
            classification: AccountClassification.liability,
            isActive: true,
          ),
        ];

        when(
          () => mockAccountRepository.watchUserAccounts('test-uid'),
        ).thenAnswer((_) => Stream.value(accounts));

        // Act & Assert - Wait for base provider first, then test filtering consistency
        await container.read(accountListProvider.future);

        // Wait for derived providers to process the data by polling until they have data
        late List<Account> assetAccounts;
        late List<Account> liabilityAccounts;

        // Poll for asset accounts
        for (var i = 0; i < 10; i++) {
          final assetAccountsAsync = container.read(assetAccountsProvider);
          assetAccounts = assetAccountsAsync.when(
            data: (accounts) => accounts,
            loading: () => <Account>[],
            error: (_, _) => <Account>[],
          );
          if (assetAccounts.isNotEmpty) break;
          await Future<void>.delayed(const Duration(milliseconds: 10));
        }

        // Poll for liability accounts
        for (var i = 0; i < 10; i++) {
          final liabilityAccountsAsync = container.read(
            liabilityAccountsProvider,
          );
          liabilityAccounts = liabilityAccountsAsync.when(
            data: (accounts) => accounts,
            loading: () => <Account>[],
            error: (_, _) => <Account>[],
          );
          if (liabilityAccounts.isNotEmpty) break;
          await Future<void>.delayed(const Duration(milliseconds: 10));
        }

        expect(assetAccounts.length, equals(1));
        expect(
          assetAccounts.first.classification,
          equals(AccountClassification.asset),
        );
        expect(liabilityAccounts.length, equals(1));
        expect(
          liabilityAccounts.first.classification,
          equals(AccountClassification.liability),
        );
      });
    });

    group('AccountStats Integration Tests', () {
      test(
        'should calculate stats correctly with real-world scenarios',
        () async {
          // Arrange - Real-world account scenario
          final realWorldAccounts = [
            MockDataFactory.createAccount(
              id: 'checking',
              name: 'Main Checking',
              classification: AccountClassification.asset,
              initialBalanceCents: 250000, // $2,500
              isActive: true,
            ),
            MockDataFactory.createAccount(
              id: 'savings',
              name: 'Emergency Fund',
              classification: AccountClassification.asset,
              initialBalanceCents: 1000000, // $10,000
              isActive: true,
            ),
            MockDataFactory.createAccount(
              id: 'credit-card',
              name: 'Credit Card',
              classification: AccountClassification.liability,
              initialBalanceCents: 150000, // $1,500 debt
              isActive: true,
            ),
            MockDataFactory.createAccount(
              id: 'old-account',
              name: 'Closed Account',
              classification: AccountClassification.asset,
              initialBalanceCents: 500000, // $5,000 - should be excluded
              isActive: false,
            ),
          ];

          when(
            () => mockAccountRepository.watchUserAccounts('test-uid'),
          ).thenAnswer((_) => Stream.value(realWorldAccounts));

          // Mock the getUserAccountSummary method that accountStatsProvider uses
          when(
            () => mockAccountRepository.getUserAccountSummary('test-uid'),
          ).thenAnswer(
            (_) async => {
              'activeAccounts': 3,
              'totalAssets': 1250000, // $2,500 + $10,000
              'totalLiabilities': 150000, // $1,500
              'netWorth': 1100000, // $12,500 - $1,500 = $11,000
              'assetAccounts': 2,
              'liabilityAccounts': 1,
            },
          );

          // Act - Wait for base provider first, then poll for stats
          await container.read(accountListProvider.future);

          // Poll for stats until we get actual data
          late AccountStats stats;
          for (var i = 0; i < 10; i++) {
            final statsAsync = container.read(accountStatsProvider);
            stats = statsAsync.when(
              data: (stats) => stats,
              loading: () => const AccountStats(
                totalAccounts: 0,
                totalAssets: 0,
                totalLiabilities: 0,
                netWorth: 0,
                assetAccountCount: 0,
                liabilityAccountCount: 0,
              ),
              error: (_, _) => const AccountStats(
                totalAccounts: 0,
                totalAssets: 0,
                totalLiabilities: 0,
                netWorth: 0,
                assetAccountCount: 0,
                liabilityAccountCount: 0,
              ),
            );
            if (stats.totalAccounts > 0) break;
            await Future<void>.delayed(const Duration(milliseconds: 10));
          }

          // Assert
          expect(stats.totalAccounts, equals(3)); // Only active accounts
          expect(stats.totalAssets, equals(1250000)); // $2,500 + $10,000
          expect(stats.totalLiabilities, equals(150000)); // $1,500
          expect(stats.netWorth, equals(1100000)); // $12,500 - $1,500 = $11,000
          expect(stats.assetAccountCount, equals(2));
          expect(stats.liabilityAccountCount, equals(1));
        },
      );

      test('should handle edge cases in stats calculation', () async {
        // Arrange - Edge cases
        final edgeCaseAccounts = [
          MockDataFactory.createAccount(
            id: 'zero-balance',
            classification: AccountClassification.asset,
            initialBalanceCents: 0,
            isActive: true,
          ),
          MockDataFactory.createAccount(
            id: 'negative-asset',
            classification: AccountClassification.asset,
            initialBalanceCents: -50000, // Overdraft
            isActive: true,
          ),
        ];

        when(
          () => mockAccountRepository.watchUserAccounts('test-uid'),
        ).thenAnswer((_) => Stream.value(edgeCaseAccounts));

        // Mock the getUserAccountSummary method for edge cases
        when(
          () => mockAccountRepository.getUserAccountSummary('test-uid'),
        ).thenAnswer(
          (_) async => {
            'activeAccounts': 2,
            'totalAssets': -50000, // 0 + (-50000)
            'totalLiabilities': 0,
            'netWorth': -50000,
            'assetAccounts': 2,
            'liabilityAccounts': 0,
          },
        );

        // Act - Wait for base provider first, then poll for stats
        await container.read(accountListProvider.future);

        // Poll for stats until we get actual data
        late AccountStats stats;
        for (var i = 0; i < 10; i++) {
          final statsAsync = container.read(accountStatsProvider);
          stats = statsAsync.when(
            data: (stats) => stats,
            loading: () => const AccountStats(
              totalAccounts: 0,
              totalAssets: 0,
              totalLiabilities: 0,
              netWorth: 0,
              assetAccountCount: 0,
              liabilityAccountCount: 0,
            ),
            error: (_, _) => const AccountStats(
              totalAccounts: 0,
              totalAssets: 0,
              totalLiabilities: 0,
              netWorth: 0,
              assetAccountCount: 0,
              liabilityAccountCount: 0,
            ),
          );
          if (stats.totalAccounts > 0) break;
          await Future<void>.delayed(const Duration(milliseconds: 10));
        }

        // Assert
        expect(stats.totalAccounts, equals(2));
        expect(stats.totalAssets, equals(-50000)); // 0 + (-50000)
        expect(stats.totalLiabilities, equals(0));
        expect(stats.netWorth, equals(-50000));
        expect(stats.assetAccountCount, equals(2));
        expect(stats.liabilityAccountCount, equals(0));
      });

      test('should update stats when accounts change', () async {
        // Arrange - Test with multiple accounts
        final accounts = [
          MockDataFactory.createAccount(
            id: 'account-1',
            classification: AccountClassification.asset,
            initialBalanceCents: 100000,
            isActive: true,
          ),
          MockDataFactory.createAccount(
            id: 'account-2',
            classification: AccountClassification.asset,
            initialBalanceCents: 200000,
            isActive: true,
          ),
        ];

        when(
          () => mockAccountRepository.watchUserAccounts('test-uid'),
        ).thenAnswer((_) => Stream.value(accounts));

        // Mock the getUserAccountSummary method for account changes test
        when(
          () => mockAccountRepository.getUserAccountSummary('test-uid'),
        ).thenAnswer(
          (_) async => {
            'activeAccounts': 2,
            'totalAssets': 300000, // 100000 + 200000
            'totalLiabilities': 0,
            'netWorth': 300000,
            'assetAccounts': 2,
            'liabilityAccounts': 0,
          },
        );

        // Act - Wait for base provider first, then poll for stats
        await container.read(accountListProvider.future);

        // Poll for stats until we get actual data
        late AccountStats stats;
        for (var i = 0; i < 10; i++) {
          final statsAsync = container.read(accountStatsProvider);
          stats = statsAsync.when(
            data: (stats) => stats,
            loading: () => const AccountStats(
              totalAccounts: 0,
              totalAssets: 0,
              totalLiabilities: 0,
              netWorth: 0,
              assetAccountCount: 0,
              liabilityAccountCount: 0,
            ),
            error: (_, _) => const AccountStats(
              totalAccounts: 0,
              totalAssets: 0,
              totalLiabilities: 0,
              netWorth: 0,
              assetAccountCount: 0,
              liabilityAccountCount: 0,
            ),
          );
          if (stats.totalAccounts > 0) break;
          await Future<void>.delayed(const Duration(milliseconds: 10));
        }

        // Assert
        expect(stats.totalAssets, equals(300000));
        expect(stats.totalAccounts, equals(2));
        expect(stats.assetAccountCount, equals(2));
        expect(stats.liabilityAccountCount, equals(0));
        expect(stats.netWorth, equals(300000));
      });
    });

    group('Basic Integration Tests', () {
      test('should handle basic account operations', () async {
        // Arrange
        final mockAccounts = [
          MockDataFactory.createAccount(id: 'account-1'),
          MockDataFactory.createAccount(id: 'account-2'),
        ];
        when(
          () => mockAccountRepository.watchUserAccounts('test-uid'),
        ).thenAnswer((_) => Stream.value(mockAccounts));

        // Act
        final accounts = await container.read(accountListProvider.future);

        // Assert
        expect(accounts.length, equals(2));
        expect(accounts.first.id, equals('account-1'));
        expect(accounts.last.id, equals('account-2'));
      });

      test('should handle null returns', () async {
        // Arrange
        const accountId = 'non-existent';
        when(
          () =>
              mockAccountRepository.watchAccountForUser('test-uid', accountId),
        ).thenAnswer((_) => Stream.value(null));

        // Act
        final result = await container.read(accountProvider(accountId).future);

        // Assert
        expect(result, isNull);
      });

      test('should handle provider cleanup and disposal', () async {
        // Arrange
        final tempContainer = ProviderContainer(
          overrides: [
            accountRepositoryProvider.overrideWithValue(mockAccountRepository),
            authServiceProvider.overrideWithValue(mockAuthService),
          ],
        );

        final mockAccounts = [MockDataFactory.createAccount()];
        when(
          () => mockAccountRepository.watchUserAccounts('test-uid'),
        ).thenAnswer((_) => Stream.value(mockAccounts));

        // Act
        final accounts = await tempContainer.read(accountListProvider.future);

        // Clean up
        tempContainer.dispose();

        // Assert
        expect(accounts, isNotEmpty);
      });
    });
  });
}

// Mock classes for testing
class MockAccountRepository extends Mock implements IAccountRepository {}

class MockAuthService extends Mock implements AuthService {}

class MockUser extends Mock implements User {}
