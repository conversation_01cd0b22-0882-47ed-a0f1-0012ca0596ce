import 'package:budapp/features/accounts/presentation/widgets/empty_accounts_state.dart';
import 'package:budapp/widgets/common/empty_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/test_wrapper.dart';

void main() {
  group('EmptyAccountsState Tests', () {
    Widget createWidget() {
      return TestWrapper.createTestWidget(
        const EmptyAccountsState(),
        overrides: [],
      );
    }

    group('Widget Rendering', () {
      testWidgets('should create widget without crashing', (tester) async {
        // Act & Assert - Just verify the widget can be created
        await tester.pumpWidget(createWidget());

        // Basic assertion that the widget tree is built
        expect(find.byType(EmptyAccountsState), findsOneWidget);
      });

      testWidgets('should render EmptyState widget', (tester) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(EmptyState), findsOneWidget);
      });

      testWidgets('should display correct icon', (tester) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(
          find.byIcon(Icons.account_balance_wallet_outlined),
          findsOneWidget,
        );
      });

      testWidgets('should display localized text content', (tester) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Assert - Check that EmptyState has text content
        expect(find.byType(EmptyState), findsOneWidget);
        expect(find.byType(Text), findsWidgets);
        expect(find.byType(TextButton), findsWidgets);
      });

      testWidgets('should render help button', (tester) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Assert - Check that we have the expected widgets
        expect(find.byType(TextButton), findsOneWidget);
        expect(find.byType(EmptyState), findsOneWidget);
        // Check that we have some interactive elements
        expect(find.byType(InkWell), findsWidgets);
      });
    });

    group('User Interactions', () {
      testWidgets('should show help dialog when help button is tapped', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Find and tap the help button (the only TextButton)
        final helpButton = find.byType(TextButton);
        expect(helpButton, findsOneWidget);

        // Tap the TextButton (which is the help button)
        await tester.tap(helpButton);
        await tester.pumpAndSettle();

        // Assert - Dialog should be shown
        expect(find.byType(AlertDialog), findsOneWidget);
      });

      testWidgets('should display help dialog with correct content', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Tap help button to show dialog
        await tester.tap(find.byType(TextButton));
        await tester.pumpAndSettle();

        // Assert - Dialog content
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.byType(Text), findsWidgets);
        expect(find.byType(TextButton), findsWidgets);
      });

      testWidgets('should display account type help items in dialog', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Tap help button to show dialog
        await tester.tap(find.byType(TextButton));
        await tester.pumpAndSettle();

        // Assert - Account type icons should be present
        expect(find.byIcon(Icons.account_balance), findsOneWidget);
        expect(find.byIcon(Icons.savings), findsOneWidget);
        expect(find.byIcon(Icons.credit_card), findsOneWidget);
        expect(find.byIcon(Icons.account_balance_wallet), findsOneWidget);
      });

      testWidgets('should close dialog when Got it button is tapped', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Show dialog
        await tester.tap(find.byType(TextButton));
        await tester.pumpAndSettle();

        // Verify dialog is shown
        expect(find.byType(AlertDialog), findsOneWidget);

        // Tap the dialog's action button (Got it button)
        final dialogButtons = find.descendant(
          of: find.byType(AlertDialog),
          matching: find.byType(TextButton),
        );
        await tester.tap(dialogButtons.first);
        await tester.pumpAndSettle();

        // Assert - Dialog should be closed
        expect(find.byType(AlertDialog), findsNothing);
      });
    });

    group('Navigation Integration', () {
      testWidgets('should handle create account action', (tester) async {
        // Note: Testing navigation requires more complex setup with GoRouter
        // For now, we test that the widget renders without navigation errors

        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Assert - Widget should render without navigation errors
        expect(find.byType(EmptyAccountsState), findsOneWidget);
        expect(find.byType(EmptyState), findsOneWidget);
      });
    });

    group('Theme Integration', () {
      testWidgets('should apply theme colors correctly', (tester) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Show dialog to test theme integration
        await tester.tap(find.byType(TextButton));
        await tester.pumpAndSettle();

        // Assert - Dialog and content should be rendered with theme
        expect(find.byType(AlertDialog), findsOneWidget);

        // Check that icons are rendered (theme colors are applied internally)
        expect(find.byIcon(Icons.account_balance), findsOneWidget);
        expect(find.byIcon(Icons.savings), findsOneWidget);
        expect(find.byIcon(Icons.credit_card), findsOneWidget);
        expect(find.byIcon(Icons.account_balance_wallet), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('should provide semantic information', (tester) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Assert - Check for semantic elements
        expect(find.byType(EmptyState), findsOneWidget);
        expect(find.byType(TextButton), findsWidgets);

        // Icons should be present for screen readers
        expect(
          find.byIcon(Icons.account_balance_wallet_outlined),
          findsOneWidget,
        );
      });

      testWidgets('should support keyboard navigation in dialog', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Show dialog
        await tester.tap(find.byType(TextButton));
        await tester.pumpAndSettle();

        // Assert - Dialog should be accessible
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.byType(TextButton), findsWidgets);
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle multiple rapid taps gracefully', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Tap help button multiple times rapidly
        final helpButton = find.byType(TextButton).first;
        await tester.tap(helpButton);
        await tester.pump();
        // After first tap, dialog is shown, so we need to be more specific
        final helpButtonAfterDialog = find.descendant(
          of: find.byType(EmptyAccountsState),
          matching: find.byType(TextButton),
        );
        if (helpButtonAfterDialog.evaluate().isNotEmpty) {
          await tester.tap(helpButtonAfterDialog);
          await tester.pump();
          await tester.tap(helpButtonAfterDialog);
        }
        await tester.pumpAndSettle();

        // Assert - Should handle gracefully without crashing
        expect(find.byType(EmptyAccountsState), findsOneWidget);
        // Only one dialog should be shown
        expect(find.byType(AlertDialog), findsOneWidget);
      });

      testWidgets('should handle dialog dismissal via barrier tap', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Show dialog
        await tester.tap(find.byType(TextButton));
        await tester.pumpAndSettle();

        // Verify dialog is shown
        expect(find.byType(AlertDialog), findsOneWidget);

        // Tap outside dialog (barrier)
        await tester.tapAt(const Offset(10, 10));
        await tester.pumpAndSettle();

        // Assert - Dialog should be closed
        expect(find.byType(AlertDialog), findsNothing);
      });
    });

    group('Widget Structure', () {
      testWidgets('should have correct widget hierarchy', (tester) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Assert - Check widget structure
        expect(find.byType(EmptyAccountsState), findsOneWidget);
        expect(find.byType(EmptyState), findsOneWidget);
        expect(find.byType(TextButton), findsWidgets);
      });

      testWidgets('should render all account type help items', (tester) async {
        // Act
        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Show dialog
        await tester.tap(find.byType(TextButton));
        await tester.pumpAndSettle();

        // Assert - All account type icons should be present
        expect(find.byIcon(Icons.account_balance), findsOneWidget);
        expect(find.byIcon(Icons.savings), findsOneWidget);
        expect(find.byIcon(Icons.credit_card), findsOneWidget);
        expect(find.byIcon(Icons.account_balance_wallet), findsOneWidget);

        // Dialog structure
        expect(find.byType(Column), findsWidgets);
        expect(find.byType(Row), findsWidgets);
        expect(find.byType(Expanded), findsWidgets);
      });
    });
  });
}
