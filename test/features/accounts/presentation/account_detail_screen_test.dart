import 'package:budapp/data/models/account.dart';
import 'package:budapp/features/accounts/presentation/screens/account_detail_screen.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../helpers/mock_data_factory.dart';
import '../../../helpers/test_wrapper.dart';

// Mock classes for testing
class MockAccountUpdateNotifier extends AccountUpdateNotifier {
  @override
  Future<void> build() async {
    // Mock implementation
  }

  @override
  Future<void> setPrimaryAccount(String accountId) async {
    // Mock implementation - just track the call
  }

  @override
  Future<void> deactivateAccount(String accountId) async {
    // Mock implementation - just track the call
  }

  @override
  Future<void> deleteAccount(String accountId) async {
    // Mock implementation - just track the call
  }
}

void main() {
  group('AccountDetailScreen', () {
    late MockAccountUpdateNotifier mockAccountUpdateNotifier;

    setUp(() {
      mockAccountUpdateNotifier = MockAccountUpdateNotifier();
    });

    testWidgets('displays loading state initially', (
      WidgetTester tester,
    ) async {
      const accountId = 'test-account-id';

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const AccountDetailScreen(accountId: accountId),
          overrides: [
            accountProvider(accountId).overrideWith((ref) {
              return const Stream<Account?>.empty();
            }),
            accountUpdateProvider.overrideWith(() => mockAccountUpdateNotifier),
          ],
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('displays account details when data is loaded', (
      WidgetTester tester,
    ) async {
      const accountId = 'test-account-id';
      final mockAccount = MockDataFactory.createAccount(
        id: accountId,
        name: 'Test Checking Account',
        type: AccountType.checking,
      );

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const AccountDetailScreen(accountId: accountId),
          overrides: [
            accountProvider(accountId).overrideWith((ref) {
              return Stream.value(mockAccount);
            }),
            accountUpdateProvider.overrideWith(() => mockAccountUpdateNotifier),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('Test Checking Account'), findsOneWidget);
      expect(find.text('Account Details'), findsWidgets);
      expect(find.text('Current Balance'), findsOneWidget);
    });

    testWidgets('displays not found state when account is null', (
      WidgetTester tester,
    ) async {
      const accountId = 'non-existent-account-id';

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const AccountDetailScreen(accountId: accountId),
          overrides: [
            accountProvider(accountId).overrideWith((ref) {
              return Stream.value(null);
            }),
            accountUpdateProvider.overrideWith(() => mockAccountUpdateNotifier),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('Account Not Found'), findsOneWidget);
      expect(find.text('Go Back'), findsOneWidget);
    });

    testWidgets('displays error state when stream has error', (
      WidgetTester tester,
    ) async {
      const accountId = 'error-account-id';

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const AccountDetailScreen(accountId: accountId),
          overrides: [
            accountProvider(accountId).overrideWith((ref) {
              return Stream.error(Exception('Failed to load account'));
            }),
            accountUpdateProvider.overrideWith(() => mockAccountUpdateNotifier),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('Error Loading Account'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('shows menu options', (WidgetTester tester) async {
      const accountId = 'test-account-id';
      final mockAccount = MockDataFactory.createAccount(
        id: accountId,
        name: 'Test Account',
      );

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const AccountDetailScreen(accountId: accountId),
          overrides: [
            accountProvider(accountId).overrideWith((ref) {
              return Stream.value(mockAccount);
            }),
            accountUpdateProvider.overrideWith(() => mockAccountUpdateNotifier),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the menu button
      final menuButton = find.byIcon(Icons.more_vert);
      expect(menuButton, findsOneWidget);
      await tester.tap(menuButton);
      await tester.pumpAndSettle();

      // Check menu items
      expect(find.text('Edit Account'), findsOneWidget);
      expect(find.text('Set as Primary'), findsOneWidget);
      expect(find.text('Deactivate Account'), findsOneWidget);
      expect(find.text('Delete Account'), findsOneWidget);
    });

    group('Account Detail Sections', () {
      testWidgets('displays account header with correct information', (
        WidgetTester tester,
      ) async {
        const accountId = 'test-account-id';
        final mockAccount = MockDataFactory.createAccount(
          id: accountId,
          name: 'Premium Savings',
          type: AccountType.savings,
          classification: AccountClassification.asset,
          initialBalanceCents: 250000, // $2,500.00
          colorHex: '#4CAF50',
          iconName: 'savings',
          isPrimary: true,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AccountDetailScreen(accountId: accountId),
            overrides: [
              accountProvider(accountId).overrideWith((ref) {
                return Stream.value(mockAccount);
              }),
              accountUpdateProvider.overrideWith(
                () => mockAccountUpdateNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Check account header information
        expect(find.text('Premium Savings'), findsOneWidget);
        expect(find.text('Current Balance'), findsOneWidget);
        expect(find.byIcon(Icons.star), findsOneWidget); // Primary indicator
      });

      testWidgets('displays account details section correctly', (
        WidgetTester tester,
      ) async {
        const accountId = 'test-account-id';
        final mockAccount = MockDataFactory.createAccount(
          id: accountId,
          name: 'Business Checking',
          type: AccountType.checking,
          classification: AccountClassification.asset,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AccountDetailScreen(accountId: accountId),
            overrides: [
              accountProvider(accountId).overrideWith((ref) {
                return Stream.value(mockAccount);
              }),
              accountUpdateProvider.overrideWith(
                () => mockAccountUpdateNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Check details section
        expect(find.text('Account Details'), findsWidgets);
        expect(find.text('Account Type'), findsOneWidget);
        expect(find.text('Checking'), findsAtLeastNWidgets(1));
        expect(find.text('Classification'), findsOneWidget);
        expect(find.text('Asset'), findsAtLeastNWidgets(1));
      });

      testWidgets('displays different account types correctly', (
        WidgetTester tester,
      ) async {
        const accountId = 'credit-card-id';
        final mockAccount = MockDataFactory.createAccount(
          id: accountId,
          name: 'Rewards Credit Card',
          type: AccountType.creditCard,
          classification: AccountClassification.liability,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AccountDetailScreen(accountId: accountId),
            overrides: [
              accountProvider(accountId).overrideWith((ref) {
                return Stream.value(mockAccount);
              }),
              accountUpdateProvider.overrideWith(
                () => mockAccountUpdateNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Check credit card specific display
        expect(find.text('Credit Card'), findsAtLeastNWidgets(1));
        expect(find.text('Liability'), findsAtLeastNWidgets(1));
      });
    });

    group('Menu Actions', () {
      testWidgets('shows menu items with correct icons and text', (
        WidgetTester tester,
      ) async {
        const accountId = 'test-account-id';
        final mockAccount = MockDataFactory.createAccount(
          id: accountId,
          name: 'Test Account',
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AccountDetailScreen(accountId: accountId),
            overrides: [
              accountProvider(accountId).overrideWith((ref) {
                return Stream.value(mockAccount);
              }),
              accountUpdateProvider.overrideWith(
                () => mockAccountUpdateNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Open menu
        await tester.tap(find.byIcon(Icons.more_vert));
        await tester.pumpAndSettle();

        // Check all menu items are present with correct icons
        expect(find.text('Edit Account'), findsOneWidget);
        expect(find.text('Set as Primary'), findsOneWidget);
        expect(find.text('Deactivate Account'), findsOneWidget);
        expect(find.text('Delete Account'), findsOneWidget);

        // Check icons are present
        expect(find.byIcon(Icons.edit), findsOneWidget);
        expect(find.byIcon(Icons.star), findsOneWidget);
        expect(find.byIcon(Icons.visibility_off), findsOneWidget);
        expect(find.byIcon(Icons.delete), findsOneWidget);
      });

      testWidgets('displays account settings section', (
        WidgetTester tester,
      ) async {
        const accountId = 'test-account-id';
        final mockAccount = MockDataFactory.createAccount(
          id: accountId,
          name: 'Test Account',
          isPrimary: true,
          isActive: true,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AccountDetailScreen(accountId: accountId),
            overrides: [
              accountProvider(accountId).overrideWith((ref) {
                return Stream.value(mockAccount);
              }),
              accountUpdateProvider.overrideWith(
                () => mockAccountUpdateNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Check settings section elements
        expect(find.text('Account Settings'), findsOneWidget);
        expect(find.text('Active'), findsOneWidget);
        expect(find.text('Primary Account'), findsOneWidget);
      });

      testWidgets('displays recent transactions section placeholder', (
        WidgetTester tester,
      ) async {
        const accountId = 'test-account-id';
        final mockAccount = MockDataFactory.createAccount(
          id: accountId,
          name: 'Test Account',
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AccountDetailScreen(accountId: accountId),
            overrides: [
              accountProvider(accountId).overrideWith((ref) {
                return Stream.value(mockAccount);
              }),
              accountUpdateProvider.overrideWith(
                () => mockAccountUpdateNotifier,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Check recent transactions section
        expect(find.text('Recent Transactions'), findsOneWidget);
        expect(find.text('View All'), findsOneWidget);
      });
    });
  });
}
