import 'package:budapp/data/models/account.dart';
import 'package:budapp/features/accounts/presentation/screens/account_create_screen.dart';
import 'package:budapp/features/accounts/presentation/widgets/account_type_selector.dart';
import 'package:budapp/providers/providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/test_wrapper.dart';

void main() {
  group('AccountCreateScreen Tests', () {
    late MockAccountRepository mockAccountRepository;
    late MockUser mockUser;

    setUpAll(() {
      registerFallbackValue(
        Account(
          id: 'test-id',
          userId: 'test-user',
          name: 'Test Account',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 0,
          currentBalanceCents: 0,

          isPrimary: false,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          metadata: {},
        ),
      );
    });

    setUp(() {
      mockAccountRepository = MockAccountRepository();
      mockUser = MockUser();

      // Setup default mock behaviors
      when(() => mockUser.uid).thenReturn('test-user-123');
      when(() => mockUser.email).thenReturn('<EMAIL>');
      when(() => mockUser.emailVerified).thenReturn(true);
    });

    Widget buildTestWidget({List<Override>? overrides}) {
      return TestWrapper.createRepositoryTestWidget(
        const AccountCreateScreen(),
        accountRepository: mockAccountRepository,
        additionalOverrides: [
          currentUserProvider.overrideWithValue(mockUser),
          ...?overrides,
        ],
      );
    }

    testWidgets('should display basic form structure', (tester) async {
      await tester.pumpWidget(buildTestWidget());
      await tester.pumpAndSettle();

      // Check for essential components that should be there
      // Note: TestWrapper creates a MaterialApp with Scaffold, and AccountCreateScreen
      // also creates a Scaffold, so we expect 2 Scaffolds
      expect(find.byType(Scaffold), findsNWidgets(2));

      // With Material 3 floating labels, the labels are inside InputDecoration
      // Check for form fields instead of static text labels
      expect(
        find.byType(TextFormField),
        findsAtLeastNWidgets(2),
      ); // Name and Initial Balance fields

      // Check for the submit button
      expect(find.text('Create Account'), findsOneWidget);

      // Check for account type selector (custom widget)
      expect(find.byType(AccountTypeSelector), findsOneWidget);
    });

    testWidgets('should handle form submission', (tester) async {
      await tester.pumpWidget(buildTestWidget());
      await tester.pumpAndSettle();

      // Try to submit empty form
      final createButton = find.text('Create Account');
      await tester.tap(createButton);
      await tester.pumpAndSettle();

      // The button should exist but may be disabled with empty form
      expect(createButton, findsOneWidget);
    });

    testWidgets('should allow text input in name field', (tester) async {
      await tester.pumpWidget(buildTestWidget());
      await tester.pumpAndSettle();

      // Find the name field
      final nameFields = find.byType(TextFormField);
      expect(nameFields, findsAtLeastNWidgets(1));

      // Test entering text in the name field
      await tester.enterText(nameFields.first, 'Test Account Name');
      await tester.pump();

      // Verify text was entered
      expect(find.text('Test Account Name'), findsOneWidget);
    });

    testWidgets('should show account type options', (tester) async {
      await tester.pumpWidget(buildTestWidget());
      await tester.pumpAndSettle();

      // Should show account type selector with options
      expect(find.text('Account Type'), findsOneWidget);
      expect(find.text('Checking Account'), findsOneWidget);
      expect(find.text('Savings Account'), findsOneWidget);
      expect(find.text('Credit Card Account'), findsOneWidget);
    });

    testWidgets('should allow account type selection', (tester) async {
      await tester.pumpWidget(buildTestWidget());
      await tester.pumpAndSettle();

      // Tap on checking account type
      final checkingOption = find.text('Checking Account');
      expect(checkingOption, findsOneWidget);

      await tester.tap(checkingOption);
      await tester.pumpAndSettle();

      // Should complete without error
      expect(find.text('Checking Account'), findsOneWidget);
    });

    testWidgets('should attempt account creation with repository', (
      tester,
    ) async {
      // Setup mock to return success
      when(
        () => mockAccountRepository.createAccount(any()),
      ).thenAnswer((_) async => 'new-account-id');

      await tester.pumpWidget(buildTestWidget());
      await tester.pumpAndSettle();

      // Fill in the name field (the one field we know works)
      final nameFields = find.byType(TextFormField);
      await tester.enterText(nameFields.first, 'Test Account');
      await tester.pump();

      // Select account type
      final checkingOption = find.text('Checking Account');
      await tester.tap(checkingOption);
      await tester.pumpAndSettle();

      // The test validates the UI components are there and functional
      expect(find.text('Test Account'), findsOneWidget);
      expect(find.text('Checking Account'), findsOneWidget);
      expect(find.text('Create Account'), findsOneWidget);
    });

    testWidgets('should render all account type options', (tester) async {
      await tester.pumpWidget(buildTestWidget());
      await tester.pumpAndSettle();

      // Should show all account types in the selector
      expect(find.text('Assets'), findsOneWidget);
      expect(find.text('Liabilities'), findsOneWidget);
      expect(find.text('Checking Account'), findsOneWidget);
      expect(find.text('Savings Account'), findsOneWidget);
      expect(find.text('Cash Account'), findsOneWidget);
      expect(find.text('Investment Account'), findsOneWidget);
      expect(find.text('Credit Card Account'), findsOneWidget);
      expect(find.text('Loan Account'), findsOneWidget);
    });
  });
}
