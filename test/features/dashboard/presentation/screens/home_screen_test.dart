import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/common/widgets/time_period_selector.dart';
import 'package:budapp/features/dashboard/presentation/screens/home_screen.dart';
import 'package:budapp/features/dashboard/providers/dashboard_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/mock_data_factory.dart';
import '../../../../helpers/mock_providers.dart';
import '../../../../helpers/test_wrapper.dart';

class MockGoRouter extends Mock implements GoRouter {}

void main() {
  group('HomeScreen', () {
    late MockGoRouter mockGoRouter;

    setUp(() {
      mockGoRouter = MockGoRouter();
      when(() => mockGoRouter.push(any())).thenAnswer((_) async => null);
    });

    group('Basic UI Components', () {
      testWidgets('displays AppBar with TimePeriodSelector', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: MockProviders.dashboardOverrides,
          ),
        );

        // Verify AppBar is present
        expect(find.byType(AppBar), findsOneWidget);

        // Verify TimePeriodSelector is present within the header
        expect(find.byType(TimePeriodSelector), findsOneWidget);
      });

      testWidgets('header remains visible during scroll', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: MockProviders.dashboardOverrides,
          ),
        );

        // Verify header is visible initially
        expect(find.byType(AppBar), findsOneWidget);

        // Scroll down significantly
        await tester.drag(
          find.byType(SingleChildScrollView),
          const Offset(0, -1000),
        );
        await tester.pumpAndSettle();

        // Header should still be visible (pinned)
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(TimePeriodSelector), findsOneWidget);
      });

      testWidgets('displays home title correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: MockProviders.dashboardOverrides,
          ),
        );

        // The title should be present in the header
        // Note: The exact title depends on EnvironmentConfig.homePageTitle
        expect(find.byType(AppBar), findsOneWidget);
      });

      testWidgets('displays main dashboard sections', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: MockProviders.dashboardOverrides,
          ),
        );

        // Wait for the widget to settle
        await tester.pumpAndSettle();

        // Verify all main sections are present
        expect(find.text('This Month'), findsOneWidget);
        expect(find.text('Recent Transactions'), findsOneWidget);
        expect(find.text('Top Expenses'), findsOneWidget);
        expect(find.text('Top Income'), findsOneWidget);
        expect(find.text('Financial Overview'), findsOneWidget);
      });

      testWidgets('displays RefreshIndicator', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: MockProviders.dashboardOverrides,
          ),
        );

        // Verify RefreshIndicator is present
        expect(find.byType(RefreshIndicator), findsOneWidget);
      });
    });

    group('Current Period Balance Card', () {
      testWidgets('displays balance data correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: MockProviders.dashboardOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Verify balance card content (from MockProviders.dashboardOverrides)
        expect(find.text('This Month'), findsOneWidget);
        expect(find.text('Income'), findsOneWidget);
        expect(find.text('Expenses'), findsOneWidget);
        expect(find.text('Net'), findsOneWidget);

        // Verify formatted currency amounts
        expect(find.text(r'$5000.00'), findsOneWidget); // Income
        expect(find.text(r'$3000.00'), findsOneWidget); // Expenses
        expect(find.text(r'$2000.00'), findsOneWidget); // Net
      });

      testWidgets('navigates to transactions when tapped', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            InheritedGoRouter(
              goRouter: mockGoRouter,
              child: const HomeScreen(),
            ),
            overrides: MockProviders.dashboardOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Find and tap the balance card
        final balanceCard = find.ancestor(
          of: find.text('This Month'),
          matching: find.byType(InkWell),
        );
        expect(balanceCard, findsOneWidget);

        await tester.tap(balanceCard);
        await tester.pumpAndSettle();

        // Verify navigation was called
        verify(() => mockGoRouter.push('/transactions')).called(1);
      });

      testWidgets('displays loading state', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: [
              ...MockProviders.authenticatedUserOverrides(),
              currentPeriodBalanceProvider.overrideWith(
                (ref) => Future.delayed(
                  const Duration(milliseconds: 100),
                  () => throw Exception('Test error'),
                ),
              ),
            ],
          ),
        );

        // Should show loading indicator initially
        expect(find.byType(CircularProgressIndicator), findsAtLeastNWidgets(1));

        // Wait for the future to complete to avoid pending timers
        await tester.pump(const Duration(milliseconds: 200));
      });

      testWidgets('displays error state', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: [
              ...MockProviders.authenticatedUserOverrides(),
              currentPeriodBalanceProvider.overrideWith(
                (ref) async => throw Exception('Test error'),
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Should show error message
        expect(find.textContaining('Error loading balance'), findsOneWidget);
      });
    });

    group('Recent Transactions Section', () {
      testWidgets('displays recent transactions with data', (tester) async {
        final mockTransactions = [
          MockDataFactory.createTransaction(
            id: 'tx1',
            description: 'Test Transaction 1',
            amountCents: 5000,
            type: TransactionType.expense,
          ),
          MockDataFactory.createTransaction(
            id: 'tx2',
            description: 'Test Transaction 2',
            amountCents: 3000,
            type: TransactionType.income,
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: [
              ...MockProviders.authenticatedUserOverrides(),
              recentTransactionsForDashboardProvider.overrideWith(
                (ref) async => mockTransactions,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify section title
        expect(find.text('Recent Transactions'), findsOneWidget);

        // Verify transaction items are displayed
        expect(find.text('Test Transaction 1'), findsOneWidget);
        expect(find.text('Test Transaction 2'), findsOneWidget);
      });

      testWidgets('displays empty state when no transactions', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: [
              ...MockProviders.authenticatedUserOverrides(),
              recentTransactionsForDashboardProvider.overrideWith(
                (ref) async => <Transaction>[],
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify section title
        expect(find.text('Recent Transactions'), findsOneWidget);

        // Verify empty state message
        expect(find.textContaining('No recent transactions'), findsOneWidget);
      });

      testWidgets('navigates to transactions list when "View All" tapped', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            InheritedGoRouter(
              goRouter: mockGoRouter,
              child: const HomeScreen(),
            ),
            overrides: MockProviders.dashboardOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Find and tap "View All" button
        final viewAllButton = find.text('View All');
        expect(viewAllButton, findsOneWidget);

        await tester.tap(viewAllButton);
        await tester.pumpAndSettle();

        // Verify navigation was called
        verify(() => mockGoRouter.push('/transactions')).called(1);
      });
    });

    group('Top Categories Sections', () {
      testWidgets('displays top expense categories', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: MockProviders.dashboardOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Verify section title
        expect(find.text('Top Expenses'), findsOneWidget);
      });

      testWidgets('displays top income categories', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: MockProviders.dashboardOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Verify section title
        expect(find.text('Top Income'), findsOneWidget);
      });
    });

    group('Financial Overview Section', () {
      testWidgets('displays financial overview', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: MockProviders.dashboardOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Verify section title
        expect(find.text('Financial Overview'), findsOneWidget);

        // Verify financial data (from MockProviders.dashboardOverrides)
        expect(find.text('Total Assets'), findsOneWidget);
        expect(find.text('Net Worth'), findsOneWidget);
        expect(find.text(r'$10000.00'), findsOneWidget); // Total Assets
        expect(find.text(r'$8000.00'), findsOneWidget); // Net Worth
      });
    });

    group('Refresh Functionality', () {
      testWidgets('triggers refresh when pulled down', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: MockProviders.dashboardOverrides,
          ),
        );

        await tester.pumpAndSettle();

        // Perform pull-to-refresh gesture
        await tester.fling(
          find.byType(RefreshIndicator),
          const Offset(0, 300),
          1000,
        );
        await tester.pump();

        // Verify refresh indicator appears
        expect(find.byType(RefreshIndicator), findsOneWidget);

        // Wait for refresh to complete
        await tester.pumpAndSettle();
      });
    });

    group('Error Handling', () {
      testWidgets('handles multiple provider errors gracefully', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: [
              ...MockProviders.authenticatedUserOverrides(),
              currentPeriodBalanceProvider.overrideWith(
                (ref) async => throw Exception('Balance error'),
              ),
              recentTransactionsForDashboardProvider.overrideWith(
                (ref) async => throw Exception('Transactions error'),
              ),
              topExpenseCategoriesSelectedPeriodProvider.overrideWith(
                (ref) async => throw Exception('Categories error'),
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Should still display the main structure
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(TimePeriodSelector), findsOneWidget);

        // Should show error messages for failed sections
        expect(find.textContaining('Error'), findsAtLeastNWidgets(1));
      });
    });

    group('Responsive Layout', () {
      testWidgets('adapts to different screen sizes', (tester) async {
        // Test with small screen size
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: MockProviders.dashboardOverrides,
            surfaceSize: const Size(320, 568), // iPhone SE size
          ),
        );

        await tester.pumpAndSettle();

        // Verify main components are still present
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(TimePeriodSelector), findsOneWidget);
        expect(find.text('This Month'), findsOneWidget);
      });

      testWidgets('handles large screen layout', (tester) async {
        // Test with large screen size
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const HomeScreen(),
            overrides: MockProviders.dashboardOverrides,
            surfaceSize: const Size(1024, 768), // Tablet size
          ),
        );

        await tester.pumpAndSettle();

        // Verify main components are present
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(TimePeriodSelector), findsOneWidget);
        expect(find.text('This Month'), findsOneWidget);
      });
    });
  });
}
