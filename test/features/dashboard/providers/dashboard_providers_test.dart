import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/interfaces/repositories.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/features/common/services/time_period_service.dart';
import 'package:budapp/features/dashboard/providers/dashboard_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/mock_data_factory.dart';

void main() {
  group('Dashboard Providers Tests', () {
    late ProviderContainer container;
    late MockAccountRepository mockAccountRepository;
    late MockTransactionRepository mockTransactionRepository;
    late MockCategoryRepository mockCategoryRepository;
    late MockAuthService mockAuthService;
    late MockUser mockUser;

    setUp(() {
      mockAccountRepository = MockAccountRepository();
      mockTransactionRepository = MockTransactionRepository();
      mockCategoryRepository = MockCategoryRepository();
      mockAuthService = MockAuthService();
      mockUser = MockUser();

      // Setup mock user
      when(() => mockUser.uid).thenReturn('test-uid');
      when(() => mockAuthService.currentUser).thenReturn(mockUser);

      // Setup default time period
      final defaultPeriod = TimePeriodService.getCurrentMonth();

      container = ProviderContainer(
        overrides: [
          accountRepositoryProvider.overrideWithValue(mockAccountRepository),
          transactionRepositoryProvider.overrideWithValue(
            mockTransactionRepository,
          ),
          categoryRepositoryProvider.overrideWithValue(mockCategoryRepository),
          authServiceProvider.overrideWithValue(mockAuthService),
          timePeriodNotifierProvider.overrideWith(TimePeriodNotifier.new),
        ],
      );

      // Set the initial state for the time period notifier
      container.read(timePeriodNotifierProvider.notifier).state = defaultPeriod;

      // Register fallback values
      registerFallbackValue(MockDataFactory.createAccount());
      registerFallbackValue(MockDataFactory.createTransaction());
      registerFallbackValue(MockDataFactory.createCategory());
      registerFallbackValue(DateTime.now());
    });

    tearDown(() {
      container.dispose();
    });

    group('Asset and Liability Calculations', () {
      test(
        'totalAssetsProvider should calculate total assets correctly',
        () async {
          // Arrange
          final mockAccounts = [
            MockDataFactory.createAccount(
              id: 'asset-1',
              classification: AccountClassification.asset,
              initialBalanceCents: 100000,
            ),
            MockDataFactory.createAccount(
              id: 'asset-2',
              classification: AccountClassification.asset,
              initialBalanceCents: 50000,
            ),
            MockDataFactory.createAccount(
              id: 'liability-1',
              classification: AccountClassification.liability,
              initialBalanceCents: 30000,
            ),
          ];
          when(
            () => mockAccountRepository.watchUserAccounts('test-uid'),
          ).thenAnswer((_) => Stream.value(mockAccounts));

          // Act
          final totalAssets = await container.read(totalAssetsProvider.future);

          // Assert
          expect(totalAssets, equals(150000)); // 100000 + 50000
        },
      );

      test(
        'totalLiabilitiesProvider should calculate total liabilities correctly',
        () async {
          // Arrange
          final mockAccounts = [
            MockDataFactory.createAccount(
              id: 'asset-1',
              classification: AccountClassification.asset,
              initialBalanceCents: 100000,
            ),
            MockDataFactory.createAccount(
              id: 'liability-1',
              classification: AccountClassification.liability,
              initialBalanceCents: 30000,
            ),
            MockDataFactory.createAccount(
              id: 'liability-2',
              classification: AccountClassification.liability,
              initialBalanceCents: 20000,
            ),
          ];
          when(
            () => mockAccountRepository.watchUserAccounts('test-uid'),
          ).thenAnswer((_) => Stream.value(mockAccounts));

          // Act
          final totalLiabilities = await container.read(
            totalLiabilitiesProvider.future,
          );

          // Assert
          expect(totalLiabilities, equals(50000)); // 30000 + 20000
        },
      );

      test('netWorthProvider should calculate net worth correctly', () async {
        // Arrange
        final mockAccounts = [
          MockDataFactory.createAccount(
            id: 'asset-1',
            classification: AccountClassification.asset,
            initialBalanceCents: 200000,
          ),
          MockDataFactory.createAccount(
            id: 'liability-1',
            classification: AccountClassification.liability,
            initialBalanceCents: 75000,
          ),
        ];
        when(
          () => mockAccountRepository.watchUserAccounts('test-uid'),
        ).thenAnswer((_) => Stream.value(mockAccounts));

        // Act
        final netWorth = await container.read(netWorthProvider.future);

        // Assert
        expect(netWorth, equals(125000)); // 200000 - 75000
      });

      test('should handle empty account lists', () async {
        // Arrange
        when(
          () => mockAccountRepository.watchUserAccounts('test-uid'),
        ).thenAnswer((_) => Stream.value([]));

        // Act
        final totalAssets = await container.read(totalAssetsProvider.future);
        final totalLiabilities = await container.read(
          totalLiabilitiesProvider.future,
        );
        final netWorth = await container.read(netWorthProvider.future);

        // Assert
        expect(totalAssets, equals(0));
        expect(totalLiabilities, equals(0));
        expect(netWorth, equals(0));
      });
    });

    group('Current Period Balance', () {
      test(
        'currentPeriodBalanceProvider should return period balance',
        () async {
          // Arrange
          final selectedPeriod = TimePeriodService.getCurrentMonth();
          container.read(timePeriodNotifierProvider.notifier).state =
              selectedPeriod;

          final mockSummary = {
            'income': 150000,
            'expenses': 75000,
            'netIncome': 75000,
          };
          when(
            () => mockTransactionRepository.getIncomeExpenseSummary(
              'test-uid',
              any(),
              any(),
            ),
          ).thenAnswer((_) async => mockSummary);

          // Act
          final balance = await container.read(
            currentPeriodBalanceProvider.future,
          );

          // Assert
          expect(balance.totalIncome, equals(150000));
          expect(balance.totalExpenses, equals(75000));
          expect(balance.netIncome, equals(75000));
          expect(balance.periodStart, equals(selectedPeriod.startDate));
          expect(balance.periodEnd, equals(selectedPeriod.endDate));
        },
      );

      test(
        'currentPeriodBalanceProvider should handle null values in summary',
        () async {
          // Arrange
          final selectedPeriod = TimePeriodService.getCurrentMonth();
          container.read(timePeriodNotifierProvider.notifier).state =
              selectedPeriod;

          final mockSummary = <String, int>{}; // Empty summary
          when(
            () => mockTransactionRepository.getIncomeExpenseSummary(
              'test-uid',
              any(),
              any(),
            ),
          ).thenAnswer((_) async => mockSummary);

          // Act
          final balance = await container.read(
            currentPeriodBalanceProvider.future,
          );

          // Assert
          expect(balance.totalIncome, equals(0));
          expect(balance.totalExpenses, equals(0));
          expect(balance.netIncome, equals(0));
        },
      );

      test(
        'currentPeriodBalanceProvider should throw when user not authenticated',
        () async {
          // Arrange
          when(() => mockAuthService.currentUser).thenReturn(null);
          final unauthContainer = ProviderContainer(
            overrides: [
              authServiceProvider.overrideWithValue(mockAuthService),
              transactionRepositoryProvider.overrideWithValue(
                mockTransactionRepository,
              ),
              timePeriodNotifierProvider.overrideWith(TimePeriodNotifier.new),
            ],
          );

          // Set initial state for unauthenticated container
          unauthContainer.read(timePeriodNotifierProvider.notifier).state =
              TimePeriodService.getCurrentMonth();

          // Act & Assert
          expect(
            () => unauthContainer.read(currentPeriodBalanceProvider.future),
            throwsA(isA<Exception>()),
          );

          unauthContainer.dispose();
        },
      );
    });

    group('Recent Transactions', () {
      test(
        'recentTransactionsForDashboardProvider should return recent transactions',
        () async {
          // Arrange
          final mockTransactions = [
            MockDataFactory.createTransaction(id: 'trans-1'),
            MockDataFactory.createTransaction(id: 'trans-2'),
            MockDataFactory.createTransaction(id: 'trans-3'),
          ];
          when(
            () => mockTransactionRepository.getRecentTransactions(
              'test-uid',
              limit: 3,
            ),
          ).thenAnswer((_) async => mockTransactions);

          // Act
          final transactions = await container.read(
            recentTransactionsForDashboardProvider.future,
          );

          // Assert
          expect(transactions.length, equals(3));
          expect(transactions.first.id, equals('trans-1'));
          verify(
            () => mockTransactionRepository.getRecentTransactions(
              'test-uid',
              limit: 3,
            ),
          ).called(1);
        },
      );

      test(
        'recentTransactionsForDashboardProvider should handle empty transactions',
        () async {
          // Arrange
          when(
            () => mockTransactionRepository.getRecentTransactions(
              'test-uid',
              limit: 3,
            ),
          ).thenAnswer((_) async => []);

          // Act
          final transactions = await container.read(
            recentTransactionsForDashboardProvider.future,
          );

          // Assert
          expect(transactions, isEmpty);
        },
      );

      test(
        'recentTransactionsForDashboardProvider should throw when user not authenticated',
        () async {
          // Arrange
          when(() => mockAuthService.currentUser).thenReturn(null);
          final unauthContainer = ProviderContainer(
            overrides: [
              authServiceProvider.overrideWithValue(mockAuthService),
              transactionRepositoryProvider.overrideWithValue(
                mockTransactionRepository,
              ),
            ],
          );

          // Act & Assert
          expect(
            () => unauthContainer.read(
              recentTransactionsForDashboardProvider.future,
            ),
            throwsA(isA<Exception>()),
          );

          unauthContainer.dispose();
        },
      );
    });

    group('Top Expense Categories', () {
      test(
        'topExpenseCategoriesSelectedPeriodProvider should return top categories',
        () async {
          // Arrange
          final selectedPeriod = TimePeriodService.getCurrentMonth();
          container.read(timePeriodNotifierProvider.notifier).state =
              selectedPeriod;

          final mockCategories = [
            MockDataFactory.createCategory(id: 'cat-1', name: 'Food'),
            MockDataFactory.createCategory(id: 'cat-2', name: 'Transport'),
            MockDataFactory.createCategory(id: 'cat-3', name: 'Entertainment'),
          ];
          when(
            () => mockCategoryRepository.getActiveCategories(),
          ).thenAnswer((_) async => mockCategories);
          when(
            () => mockCategoryRepository.watchUserCategories(any()),
          ).thenAnswer((_) => Stream.value(mockCategories));

          final mockSpending = {
            'cat-1': 50000, // Food - highest
            'cat-2': 30000, // Transport - second
            'cat-3': 20000, // Entertainment - third
          };
          when(
            () => mockTransactionRepository.getSpendingByCategory(
              'test-uid',
              any(),
              any(),
            ),
          ).thenAnswer((_) async => mockSpending);

          // Act
          final topCategories = await container.read(
            topExpenseCategoriesSelectedPeriodProvider.future,
          );

          // Assert
          expect(topCategories.length, equals(3));
          expect(topCategories[0].categoryName, equals('Food'));
          expect(topCategories[0].totalAmount, equals(50000));
          expect(topCategories[1].categoryName, equals('Transport'));
          expect(topCategories[1].totalAmount, equals(30000));
          expect(topCategories[2].categoryName, equals('Entertainment'));
          expect(topCategories[2].totalAmount, equals(20000));
        },
      );

      test(
        'topExpenseCategoriesSelectedPeriodProvider should filter out unknown categories',
        () async {
          // Arrange
          final selectedPeriod = TimePeriodService.getCurrentMonth();
          container.read(timePeriodNotifierProvider.notifier).state =
              selectedPeriod;

          final mockCategories = [
            MockDataFactory.createCategory(id: 'cat-1', name: 'Food'),
          ];
          when(
            () => mockCategoryRepository.getActiveCategories(),
          ).thenAnswer((_) async => mockCategories);
          when(
            () => mockCategoryRepository.watchUserCategories(any()),
          ).thenAnswer((_) => Stream.value(mockCategories));

          final mockSpending = {
            'cat-1': 50000, // Known category
            'unknown-cat': 30000, // Unknown category - should be filtered out
          };
          when(
            () => mockTransactionRepository.getSpendingByCategory(
              'test-uid',
              any(),
              any(),
            ),
          ).thenAnswer((_) async => mockSpending);

          // Act
          final topCategories = await container.read(
            topExpenseCategoriesSelectedPeriodProvider.future,
          );

          // Assert
          expect(topCategories.length, equals(1));
          expect(topCategories[0].categoryName, equals('Food'));
        },
      );

      test(
        'topExpenseCategoriesSelectedPeriodProvider should limit to top 3',
        () async {
          // Arrange
          final selectedPeriod = TimePeriodService.getCurrentMonth();
          container.read(timePeriodNotifierProvider.notifier).state =
              selectedPeriod;

          final mockCategories = List.generate(
            5,
            (i) => MockDataFactory.createCategory(
              id: 'cat-$i',
              name: 'Category $i',
            ),
          );
          when(
            () => mockCategoryRepository.getActiveCategories(),
          ).thenAnswer((_) async => mockCategories);
          when(
            () => mockCategoryRepository.watchUserCategories(any()),
          ).thenAnswer((_) => Stream.value(mockCategories));

          final mockSpending = {
            for (int i = 0; i < 5; i++)
              'cat-$i': (5 - i) * 10000, // Decreasing amounts
          };
          when(
            () => mockTransactionRepository.getSpendingByCategory(
              'test-uid',
              any(),
              any(),
            ),
          ).thenAnswer((_) async => mockSpending);

          // Act
          final topCategories = await container.read(
            topExpenseCategoriesSelectedPeriodProvider.future,
          );

          // Assert
          expect(topCategories.length, equals(3)); // Limited to top 3
          expect(topCategories[0].totalAmount, equals(50000)); // Highest amount
          expect(topCategories[1].totalAmount, equals(40000)); // Second highest
          expect(topCategories[2].totalAmount, equals(30000)); // Third highest
        },
      );
    });

    group('Top Income Categories', () {
      test(
        'topIncomeCategoriesSelectedPeriodProvider should return top income categories',
        () async {
          // Arrange
          final selectedPeriod = TimePeriodService.getCurrentMonth();
          container.read(timePeriodNotifierProvider.notifier).state =
              selectedPeriod;

          final mockCategories = [
            MockDataFactory.createCategory(id: 'income-1', name: 'Salary'),
            MockDataFactory.createCategory(id: 'income-2', name: 'Freelance'),
          ];
          when(
            () => mockCategoryRepository.getActiveCategories(),
          ).thenAnswer((_) async => mockCategories);
          when(
            () => mockCategoryRepository.watchUserCategories(any()),
          ).thenAnswer((_) => Stream.value(mockCategories));

          final mockTransactions = [
            MockDataFactory.createTransaction(
              id: 'trans-1',
              type: TransactionType.income,
              status: TransactionStatus.completed,
              categoryId: 'income-1',
              amountCents: 500000,
            ),
            MockDataFactory.createTransaction(
              id: 'trans-2',
              type: TransactionType.income,
              status: TransactionStatus.completed,
              categoryId: 'income-2',
              amountCents: 200000,
            ),
            MockDataFactory.createTransaction(
              id: 'trans-3',
              type: TransactionType.expense, // Should be ignored
              status: TransactionStatus.completed,
              categoryId: 'income-1',
              amountCents: 100000,
            ),
          ];
          when(
            () => mockTransactionRepository.getTransactionsByDateRange(
              'test-uid',
              any(),
              any(),
            ),
          ).thenAnswer((_) async => mockTransactions);

          // Act
          final topIncomeCategories = await container.read(
            topIncomeCategoriesSelectedPeriodProvider.future,
          );

          // Assert
          expect(topIncomeCategories.length, equals(2));
          expect(topIncomeCategories[0].categoryName, equals('Salary'));
          expect(topIncomeCategories[0].totalAmount, equals(500000));
          expect(topIncomeCategories[1].categoryName, equals('Freelance'));
          expect(topIncomeCategories[1].totalAmount, equals(200000));
        },
      );

      test(
        'topIncomeCategoriesSelectedPeriodProvider should filter by status and type',
        () async {
          // Arrange
          final selectedPeriod = TimePeriodService.getCurrentMonth();
          container.read(timePeriodNotifierProvider.notifier).state =
              selectedPeriod;

          final mockCategories = [
            MockDataFactory.createCategory(id: 'income-1', name: 'Salary'),
          ];
          when(
            () => mockCategoryRepository.getActiveCategories(),
          ).thenAnswer((_) async => mockCategories);
          when(
            () => mockCategoryRepository.watchUserCategories(any()),
          ).thenAnswer((_) => Stream.value(mockCategories));

          final mockTransactions = [
            MockDataFactory.createTransaction(
              id: 'trans-1',
              type: TransactionType.income,
              status: TransactionStatus.completed,
              categoryId: 'income-1',
              amountCents: 500000,
            ),
            MockDataFactory.createTransaction(
              id: 'trans-2',
              type: TransactionType.income,
              status: TransactionStatus.pending, // Should be ignored
              categoryId: 'income-1',
              amountCents: 200000,
            ),
            MockDataFactory.createTransaction(
              id: 'trans-3',
              type: TransactionType.expense, // Should be ignored
              status: TransactionStatus.completed,
              categoryId: 'income-1',
              amountCents: 100000,
            ),
          ];
          when(
            () => mockTransactionRepository.getTransactionsByDateRange(
              'test-uid',
              any(),
              any(),
            ),
          ).thenAnswer((_) async => mockTransactions);

          // Act
          final topIncomeCategories = await container.read(
            topIncomeCategoriesSelectedPeriodProvider.future,
          );

          // Assert
          expect(topIncomeCategories.length, equals(1));
          expect(
            topIncomeCategories[0].totalAmount,
            equals(500000),
          ); // Only completed income
        },
      );

      test(
        'topIncomeCategoriesSelectedPeriodProvider should handle transactions without categories',
        () async {
          // Arrange
          final selectedPeriod = TimePeriodService.getCurrentMonth();
          container.read(timePeriodNotifierProvider.notifier).state =
              selectedPeriod;

          when(
            () => mockCategoryRepository.getActiveCategories(),
          ).thenAnswer((_) async => []);
          when(
            () => mockCategoryRepository.watchUserCategories(any()),
          ).thenAnswer((_) => Stream.value([]));

          final mockTransactions = [
            MockDataFactory.createTransaction(
              id: 'trans-1',
              type: TransactionType.income,
              status: TransactionStatus.completed,
              categoryId: null, // No category - should be ignored
              amountCents: 500000,
            ),
          ];
          when(
            () => mockTransactionRepository.getTransactionsByDateRange(
              'test-uid',
              any(),
              any(),
            ),
          ).thenAnswer((_) async => mockTransactions);

          // Act
          final topIncomeCategories = await container.read(
            topIncomeCategoriesSelectedPeriodProvider.future,
          );

          // Assert
          expect(topIncomeCategories, isEmpty);
        },
      );
    });

    group('Error Handling', () {
      test('should handle repository errors gracefully', () async {
        // Arrange
        when(
          () => mockAccountRepository.watchUserAccounts('test-uid'),
        ).thenAnswer((_) => Stream.error(Exception('Database error')));

        // Act & Assert
        expect(
          () => container.read(totalAssetsProvider.future),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle transaction repository errors', () async {
        // Arrange
        when(
          () => mockTransactionRepository.getRecentTransactions(
            'test-uid',
            limit: 3,
          ),
        ).thenThrow(Exception('Transaction error'));

        // Act & Assert
        expect(
          () => container.read(recentTransactionsForDashboardProvider.future),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('Data Model Tests', () {
      test('CurrentPeriodBalance copyWith should work correctly', () {
        // Arrange
        final original = CurrentPeriodBalance(
          totalIncome: 100000,
          totalExpenses: 50000,
          netIncome: 50000,
          periodStart: DateTime(2024, 1, 1),
          periodEnd: DateTime(2024, 1, 31),
        );

        // Act
        final updated = original.copyWith(
          totalIncome: 150000,
          netIncome: 100000,
        );

        // Assert
        expect(updated.totalIncome, equals(150000));
        expect(updated.totalExpenses, equals(50000)); // Unchanged
        expect(updated.netIncome, equals(100000));
        expect(updated.periodStart, equals(DateTime(2024, 1, 1))); // Unchanged
        expect(updated.periodEnd, equals(DateTime(2024, 1, 31))); // Unchanged
      });

      test('TopCategorySpending should store data correctly', () {
        // Arrange & Act
        const topCategory = TopCategorySpending(
          categoryId: 'cat-1',
          categoryName: 'Food',
          totalAmount: 50000,
          categoryIcon: 'food_icon',
          categoryColor: 0xFF4CAF50,
        );

        // Assert
        expect(topCategory.categoryId, equals('cat-1'));
        expect(topCategory.categoryName, equals('Food'));
        expect(topCategory.totalAmount, equals(50000));
        expect(topCategory.categoryIcon, equals('food_icon'));
        expect(topCategory.categoryColor, equals(0xFF4CAF50));
      });
    });
  });
}

// Mock classes for testing
class MockAccountRepository extends Mock implements IAccountRepository {}

class MockTransactionRepository extends Mock
    implements ITransactionRepository {}

class MockCategoryRepository extends Mock implements ICategoryRepository {}

class MockAuthService extends Mock implements AuthService {}

class MockUser extends Mock implements User {}
