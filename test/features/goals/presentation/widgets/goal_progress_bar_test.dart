import 'package:budapp/data/models/goal.dart';
import 'package:budapp/features/goals/presentation/widgets/goal_progress_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/test_wrapper.dart';

void main() {
  group('GoalProgressBar', () {
    late Goal testGoal;

    setUp(() {
      testGoal = Goal(
        id: '1',
        userId: 'user1',
        name: 'Test Goal',
        targetAmountCents: 100000,
        currentAmountCents: 50000,
        status: GoalStatus.active,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        schemaVersion: 1,
      );
    });

    group('Linear Progress Type', () {
      testWidgets('displays linear progress bar with correct progress', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: testGoal,
              type: GoalProgressType.linear,
              showPercentage: true,
              height: 8, // Default height, should show percentage below
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify progress bar container is present
        expect(find.byType(Container), findsAtLeast(1));
        expect(
          find.text('50.0%'),
          findsOneWidget,
        ); // Shows with decimal when height < 20
      });

      testWidgets('shows percentage text when showPercentage is true', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: testGoal,
              type: GoalProgressType.linear,
              showPercentage: true,
              height: 20,
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('50%'), findsOneWidget);
      });

      testWidgets('hides percentage text when showPercentage is false', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: testGoal,
              type: GoalProgressType.linear,
              showPercentage: false,
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('50%'), findsNothing);
      });

      testWidgets('shows amounts when showAmounts is true', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: testGoal,
              type: GoalProgressType.linear,
              showAmounts: true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('500 / 1000'), findsOneWidget);
      });

      testWidgets('uses custom height when provided', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: testGoal,
              type: GoalProgressType.linear,
              height: 12,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify custom height is applied
        final containers = tester.widgetList<Container>(find.byType(Container));
        expect(
          containers.any(
            (c) =>
                c.constraints?.maxHeight == 12.0 ||
                c.constraints?.minHeight == 12,
          ),
          isTrue,
        );
      });

      testWidgets('displays animated progress when animated is true', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: testGoal,
              type: GoalProgressType.linear,
              animated: true,
            ),
          ),
        );

        expect(find.byType(AnimatedContainer), findsOneWidget);
      });

      testWidgets('displays static progress when animated is false', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: testGoal,
              type: GoalProgressType.linear,
              animated: false,
            ),
          ),
        );

        expect(find.byType(AnimatedContainer), findsNothing);
      });
    });

    group('Circular Progress Type', () {
      testWidgets('displays circular progress bar with correct progress', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: testGoal,
              type: GoalProgressType.circular,
              showPercentage: true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.byType(CircularProgressIndicator), findsAtLeast(1));
        expect(find.text('50%'), findsOneWidget);
      });

      testWidgets('shows percentage in center when showPercentage is true', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: testGoal,
              type: GoalProgressType.circular,
              showPercentage: true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('50%'), findsOneWidget);
      });

      testWidgets('shows amounts in center when showAmounts is true', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: testGoal,
              type: GoalProgressType.circular,
              showPercentage: true,
              showAmounts: true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('50%'), findsOneWidget);
        expect(find.text('500'), findsOneWidget);
      });

      testWidgets('uses custom size when provided', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: testGoal,
              type: GoalProgressType.circular,
              size: 64,
            ),
          ),
        );

        await tester.pumpAndSettle();

        final sizedBoxes = tester.widgetList<SizedBox>(find.byType(SizedBox));
        expect(
          sizedBoxes.any((s) => s.width == 64.0 && s.height == 64),
          isTrue,
        );
      });

      testWidgets('displays animated circular progress when animated is true', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: testGoal,
              type: GoalProgressType.circular,
              animated: true,
            ),
          ),
        );

        expect(find.byType(TweenAnimationBuilder<double>), findsOneWidget);
      });

      testWidgets('displays static circular progress when animated is false', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: testGoal,
              type: GoalProgressType.circular,
              animated: false,
            ),
          ),
        );

        expect(find.byType(TweenAnimationBuilder<double>), findsNothing);
      });
    });

    group('Compact Progress Type', () {
      testWidgets(
        'displays compact progress bar with mini circular indicator',
        (tester) async {
          await tester.pumpWidget(
            TestWrapper.createTestWidget(
              GoalProgressBar(
                goal: testGoal,
                type: GoalProgressType.compact,
                showPercentage: true,
              ),
            ),
          );

          await tester.pumpAndSettle();

          expect(find.byType(CircularProgressIndicator), findsOneWidget);
          expect(find.text('50.0% complete'), findsOneWidget);
        },
      );

      testWidgets('shows percentage text when showPercentage is true', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: testGoal,
              type: GoalProgressType.compact,
              showPercentage: true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('50.0% complete'), findsOneWidget);
      });

      testWidgets('shows amounts when showAmounts is true', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: testGoal,
              type: GoalProgressType.compact,
              showAmounts: true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('500 of 1000'), findsOneWidget);
      });

      testWidgets(
        'displays both percentage and amounts when both are enabled',
        (tester) async {
          await tester.pumpWidget(
            TestWrapper.createTestWidget(
              GoalProgressBar(
                goal: testGoal,
                type: GoalProgressType.compact,
                showPercentage: true,
                showAmounts: true,
              ),
            ),
          );

          await tester.pumpAndSettle();

          expect(find.text('50.0% complete'), findsOneWidget);
          expect(find.text('500 of 1000'), findsOneWidget);
        },
      );
    });

    group('Progress Color Logic', () {
      testWidgets('uses custom goal color when provided', (tester) async {
        final customColorGoal = testGoal.copyWith(colorHex: '#FF5722');

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: customColorGoal,
              type: GoalProgressType.linear,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify custom color is applied (color parsing logic)
        expect(find.byType(Container), findsAtLeast(1));
      });

      testWidgets('falls back to theme color when no custom color provided', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(goal: testGoal, type: GoalProgressType.linear),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.byType(Container), findsAtLeast(1));
      });

      testWidgets('handles different progress levels correctly', (
        tester,
      ) async {
        // Test low progress (< 50%)
        final lowProgressGoal = testGoal.copyWith(currentAmountCents: 30000);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: lowProgressGoal,
              type: GoalProgressType.linear,
              showPercentage: true,
              height: 8,
            ),
          ),
        );

        await tester.pumpAndSettle();
        expect(find.text('30.0%'), findsOneWidget);

        // Test high progress (>= 80%)
        final highProgressGoal = testGoal.copyWith(currentAmountCents: 90000);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: highProgressGoal,
              type: GoalProgressType.linear,
              showPercentage: true,
              height: 8,
            ),
          ),
        );

        await tester.pumpAndSettle();
        expect(find.text('90.0%'), findsOneWidget);

        // Test completed progress (100%)
        final completedGoal = testGoal.copyWith(currentAmountCents: 100000);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: completedGoal,
              type: GoalProgressType.linear,
              showPercentage: true,
              height: 8,
            ),
          ),
        );

        await tester.pumpAndSettle();
        expect(find.text('100.0%'), findsOneWidget);
      });
    });

    group('Edge Cases', () {
      testWidgets('handles zero progress correctly', (tester) async {
        final zeroProgressGoal = testGoal.copyWith(currentAmountCents: 0);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: zeroProgressGoal,
              type: GoalProgressType.linear,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify widget renders without error
        expect(find.byType(GoalProgressBar), findsOneWidget);
      });

      testWidgets('handles over 100% progress correctly', (tester) async {
        final overProgressGoal = testGoal.copyWith(currentAmountCents: 150000);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: overProgressGoal,
              type: GoalProgressType.linear,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify widget renders without error
        expect(find.byType(GoalProgressBar), findsOneWidget);
      });

      testWidgets('handles invalid custom color gracefully', (tester) async {
        final invalidColorGoal = testGoal.copyWith(colorHex: 'invalid-color');

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GoalProgressBar(
              goal: invalidColorGoal,
              type: GoalProgressType.linear,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should not crash and should fall back to theme color
        expect(find.byType(Container), findsAtLeast(1));
      });
    });
  });
}
