import 'package:budapp/data/models/goal_contribution.dart';
import 'package:budapp/features/goals/presentation/widgets/contribution_card.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';

class MockCurrencyFormatter extends Mock implements CurrencyFormatter {}

void main() {
  group('ContributionCard', () {
    late MockCurrencyFormatter mockCurrencyFormatter;
    late GoalContribution testContribution;

    setUp(() {
      mockCurrencyFormatter = MockCurrencyFormatter();
      when(
        () => mockCurrencyFormatter.formatAmountWithSign(
          any(),
          showPositiveSign: any(named: 'showPositiveSign'),
        ),
      ).thenAnswer((invocation) {
        final cents = invocation.positionalArguments[0] as int;
        final showPositiveSign =
            invocation.namedArguments[#showPositiveSign] as bool? ?? false;
        final amount = (cents / 100).toStringAsFixed(2);
        return showPositiveSign ? '+\$$amount' : '\$$amount';
      });

      testContribution = GoalContribution(
        id: '1',
        userId: 'user1',
        goalId: 'goal1',
        amountCents: 50000,
        contributionDate: DateTime(2024, 1, 15, 10, 30),
        description: 'Test contribution',
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        schemaVersion: 1,
      );
    });

    testWidgets('displays contribution amount with positive sign', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          ContributionCard(contribution: testContribution),
          overrides: [
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text(r'+$500.00'), findsOneWidget);
      verify(
        () => mockCurrencyFormatter.formatAmountWithSign(
          50000,
          showPositiveSign: true,
        ),
      ).called(1);
    });

    testWidgets('displays contribution icon', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          ContributionCard(contribution: testContribution),
          overrides: [
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
    });

    testWidgets('displays formatted contribution date', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          ContributionCard(contribution: testContribution),
          overrides: [
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Should display formatted date (will depend on test execution time)
      expect(find.textContaining('1/15/2024'), findsOneWidget);
    });

    testWidgets('displays description when available', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          ContributionCard(contribution: testContribution),
          overrides: [
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('Test contribution'), findsOneWidget);
    });

    testWidgets('hides description when not available', (tester) async {
      final contributionWithoutDescription = testContribution.copyWith(
        description: null,
      );

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          ContributionCard(contribution: contributionWithoutDescription),
          overrides: [
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('Test contribution'), findsNothing);
    });

    testWidgets('displays popup menu when callbacks are provided', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          ContributionCard(
            contribution: testContribution,
            onEdit: () {},
            onDelete: () {},
          ),
          overrides: [
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(PopupMenuButton<String>), findsOneWidget);
      expect(find.byIcon(Icons.more_vert), findsOneWidget);

      // Tap menu button
      await tester.tap(find.byType(PopupMenuButton<String>));
      await tester.pumpAndSettle();

      expect(find.text('Edit'), findsOneWidget);
      expect(find.text('Delete'), findsOneWidget);
    });

    testWidgets('hides popup menu when no callbacks are provided', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          ContributionCard(contribution: testContribution),
          overrides: [
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(PopupMenuButton<String>), findsNothing);
      expect(find.byIcon(Icons.more_vert), findsNothing);
    });

    testWidgets('shows only edit menu item when only onEdit is provided', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          ContributionCard(contribution: testContribution, onEdit: () {}),
          overrides: [
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Tap menu button
      await tester.tap(find.byType(PopupMenuButton<String>));
      await tester.pumpAndSettle();

      expect(find.text('Edit'), findsOneWidget);
      expect(find.text('Delete'), findsNothing);
    });

    testWidgets('shows only delete menu item when only onDelete is provided', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          ContributionCard(contribution: testContribution, onDelete: () {}),
          overrides: [
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Tap menu button
      await tester.tap(find.byType(PopupMenuButton<String>));
      await tester.pumpAndSettle();

      expect(find.text('Edit'), findsNothing);
      expect(find.text('Delete'), findsOneWidget);
    });

    testWidgets('calls onEdit when edit menu item is tapped', (tester) async {
      var editCalled = false;

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          ContributionCard(
            contribution: testContribution,
            onEdit: () => editCalled = true,
          ),
          overrides: [
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Tap menu button
      await tester.tap(find.byType(PopupMenuButton<String>));
      await tester.pumpAndSettle();

      // Tap edit menu item
      await tester.tap(find.text('Edit'));
      await tester.pumpAndSettle();

      expect(editCalled, isTrue);
    });

    testWidgets('calls onEdit when card is tapped', (tester) async {
      var editCalled = false;

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          ContributionCard(
            contribution: testContribution,
            onEdit: () => editCalled = true,
          ),
          overrides: [
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Tap the card
      await tester.tap(find.byType(ContributionCard));
      await tester.pumpAndSettle();

      expect(editCalled, isTrue);
    });

    testWidgets('shows delete confirmation dialog when delete is tapped', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidgetWithRouter(
          ContributionCard(contribution: testContribution, onDelete: () {}),
          overrides: [
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
          initialLocation: '/test',
        ),
      );

      await tester.pumpAndSettle();

      // Tap menu button
      await tester.tap(find.byType(PopupMenuButton<String>));
      await tester.pumpAndSettle();

      // Tap delete menu item
      await tester.tap(find.text('Delete'));
      await tester.pumpAndSettle();

      expect(find.byType(AlertDialog), findsOneWidget);
      expect(find.text('Delete Contribution'), findsOneWidget);
      expect(
        find.text(
          'Are you sure you want to delete this contribution? This action cannot be undone.',
        ),
        findsOneWidget,
      );
      expect(find.text('Cancel'), findsOneWidget);
      expect(
        find.text('Delete'),
        findsOneWidget,
      ); // Only in dialog (menu is hidden when dialog is shown)
    });

    testWidgets('calls onDelete when delete is confirmed', (tester) async {
      var deleteCalled = false;

      await tester.pumpWidget(
        TestWrapper.createTestWidgetWithRouter(
          ContributionCard(
            contribution: testContribution,
            onDelete: () => deleteCalled = true,
          ),
          overrides: [
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
          initialLocation: '/test',
        ),
      );

      await tester.pumpAndSettle();

      // Tap menu button
      await tester.tap(find.byType(PopupMenuButton<String>));
      await tester.pumpAndSettle();

      // Tap delete menu item
      await tester.tap(find.text('Delete'));
      await tester.pumpAndSettle();

      // Confirm deletion
      await tester.tap(find.widgetWithText(FilledButton, 'Delete'));
      await tester.pumpAndSettle();

      expect(deleteCalled, isTrue);
    });

    testWidgets('does not call onDelete when delete is cancelled', (
      tester,
    ) async {
      var deleteCalled = false;

      await tester.pumpWidget(
        TestWrapper.createTestWidgetWithRouter(
          ContributionCard(
            contribution: testContribution,
            onDelete: () => deleteCalled = true,
          ),
          overrides: [
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
          initialLocation: '/test',
        ),
      );

      await tester.pumpAndSettle();

      // Tap menu button
      await tester.tap(find.byType(PopupMenuButton<String>));
      await tester.pumpAndSettle();

      // Tap delete menu item
      await tester.tap(find.text('Delete'));
      await tester.pumpAndSettle();

      // Cancel deletion
      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      expect(deleteCalled, isFalse);
    });

    group('Date Formatting', () {
      testWidgets('displays "Today" for today\'s contribution', (tester) async {
        final todayContribution = testContribution.copyWith(
          contributionDate: DateTime.now(),
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ContributionCard(contribution: todayContribution),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        expect(find.textContaining('Today at'), findsOneWidget);
      });

      testWidgets('displays "Yesterday" for yesterday\'s contribution', (
        tester,
      ) async {
        final yesterdayContribution = testContribution.copyWith(
          contributionDate: DateTime.now().subtract(const Duration(days: 1)),
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ContributionCard(contribution: yesterdayContribution),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        expect(find.textContaining('Yesterday at'), findsOneWidget);
      });

      testWidgets('displays weekday for contributions within the week', (
        tester,
      ) async {
        final weekdayContribution = testContribution.copyWith(
          contributionDate: DateTime.now().subtract(const Duration(days: 3)),
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ContributionCard(contribution: weekdayContribution),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        expect(find.textContaining('at'), findsOneWidget);
      });
    });

    group('Date Helper Badges', () {
      testWidgets('shows "Today" badge for today\'s contribution', (
        tester,
      ) async {
        final todayContribution = testContribution.copyWith(
          contributionDate: DateTime.now(),
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ContributionCard(contribution: todayContribution),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('Today'), findsOneWidget);
      });

      testWidgets('shows "This Week" badge for this week\'s contribution', (
        tester,
      ) async {
        final now = DateTime.now();
        // Create a date that's definitely within this week but not today
        // Start from the beginning of this week (Monday) and add 1 day if today is Monday
        final weekStart = now.subtract(Duration(days: now.weekday - 1));
        final thisWeekDate = now.weekday == 1
            ? weekStart.add(
                const Duration(days: 1),
              ) // Tuesday if today is Monday
            : weekStart; // Monday otherwise
        final thisWeekContribution = testContribution.copyWith(
          contributionDate: thisWeekDate,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ContributionCard(contribution: thisWeekContribution),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('This Week'), findsOneWidget);
      });

      testWidgets('shows "This Month" badge for this month\'s contribution', (
        tester,
      ) async {
        // Create a contribution from this month but not this week
        final now = DateTime.now();

        // Try to find a date in this month that's not in this week
        DateTime? validDate;
        for (
          var day = 1;
          day <= DateTime(now.year, now.month + 1, 0).day;
          day++
        ) {
          final testDate = DateTime(now.year, now.month, day);
          final weekStart = now.subtract(Duration(days: now.weekday - 1));
          final weekStartDate = DateTime(
            weekStart.year,
            weekStart.month,
            weekStart.day,
          );
          final checkDate = DateTime(
            testDate.year,
            testDate.month,
            testDate.day,
          );

          // Check if this date is before current week AND not today
          if (checkDate.isBefore(weekStartDate) &&
              checkDate != DateTime(now.year, now.month, now.day)) {
            validDate = testDate;
            break;
          }
        }

        // Skip this test if we can't find a suitable date
        // This happens when current week spans the entire month so far
        if (validDate == null) {
          return; // Skip test
        }

        final thisMonthContribution = testContribution.copyWith(
          contributionDate: validDate,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ContributionCard(contribution: thisMonthContribution),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('This Month'), findsOneWidget);
      });
    });

    testWidgets('has proper styling and layout', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          ContributionCard(contribution: testContribution),
          overrides: [
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify card structure
      expect(find.byType(Card), findsOneWidget);
      expect(find.byType(InkWell), findsOneWidget);
      expect(find.byType(Container), findsAtLeast(1));

      // Verify layout components
      expect(find.byType(Row), findsAtLeast(1));
      expect(find.byType(Column), findsAtLeast(1));
      expect(find.byType(Expanded), findsOneWidget);
    });
  });
}
