import 'package:budapp/data/models/goal_contribution.dart';
import 'package:budapp/features/goals/presentation/screens/goal_contributions_list_screen.dart';
import 'package:budapp/features/goals/presentation/widgets/contribution_card.dart';
import 'package:budapp/features/goals/providers/goal_contribution_providers.dart';
import 'package:budapp/widgets/common/empty_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/test_wrapper.dart';

void main() {
  group('GoalContributionsListScreen', () {
    const testGoalId = 'test-goal-id';

    testWidgets('displays loading indicator when contributions are loading', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionsListScreen(goalId: testGoalId),
          overrides: [
            activeGoalContributionsProvider(testGoalId).overrideWith(
              (ref) =>
                  Future<List<GoalContribution>>.value(<GoalContribution>[]),
            ),
          ],
        ),
      );

      // Initially should show loading
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Contributions'), findsOneWidget);

      // Wait for the future to complete
      await tester.pumpAndSettle();
    });

    testWidgets('displays empty state when no contributions exist', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionsListScreen(goalId: testGoalId),
          overrides: [
            activeGoalContributionsProvider(
              testGoalId,
            ).overrideWith((ref) => Future.value(<GoalContribution>[])),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(EmptyState), findsOneWidget);
      expect(find.text('No Contributions Yet'), findsOneWidget);
      expect(
        find.text(
          'Start tracking your progress by adding your first contribution to this goal.',
        ),
        findsOneWidget,
      );
      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
    });

    testWidgets('displays contributions list when contributions exist', (
      tester,
    ) async {
      final testContributions = [
        GoalContribution(
          id: '1',
          userId: 'user1',
          goalId: testGoalId,
          amountCents: 50000,
          contributionDate: DateTime(2024, 1, 15),
          description: 'Monthly contribution',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          schemaVersion: 1,
        ),
        GoalContribution(
          id: '2',
          userId: 'user1',
          goalId: testGoalId,
          amountCents: 25000,
          contributionDate: DateTime(2024, 1, 10),
          description: 'Bonus contribution',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          schemaVersion: 1,
        ),
      ];

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionsListScreen(goalId: testGoalId),
          overrides: [
            activeGoalContributionsProvider(
              testGoalId,
            ).overrideWith((ref) => Future.value(testContributions)),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(ContributionCard), findsNWidgets(2));
      expect(find.text('Monthly contribution'), findsOneWidget);
      expect(find.text('Bonus contribution'), findsOneWidget);
    });

    testWidgets('displays error state when contributions fail to load', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionsListScreen(goalId: testGoalId),
          overrides: [
            activeGoalContributionsProvider(
              testGoalId,
            ).overrideWith((ref) => Future.error('Failed to load')),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      expect(find.text('Failed to load contributions'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('has floating action button for creating new contributions', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionsListScreen(goalId: testGoalId),
          overrides: [
            activeGoalContributionsProvider(
              testGoalId,
            ).overrideWith((ref) => Future.value(<GoalContribution>[])),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(FloatingActionButton), findsOneWidget);
      expect(find.byIcon(Icons.add), findsOneWidget);
    });

    testWidgets('sorts contributions by date (newest first)', (tester) async {
      final testContributions = [
        GoalContribution(
          id: '1',
          userId: 'user1',
          goalId: testGoalId,
          amountCents: 50000,
          contributionDate: DateTime(2024, 1, 10), // Older date
          description: 'Older contribution',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          schemaVersion: 1,
        ),
        GoalContribution(
          id: '2',
          userId: 'user1',
          goalId: testGoalId,
          amountCents: 25000,
          contributionDate: DateTime(2024, 1, 15), // Newer date
          description: 'Newer contribution',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          schemaVersion: 1,
        ),
      ];

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionsListScreen(goalId: testGoalId),
          overrides: [
            activeGoalContributionsProvider(
              testGoalId,
            ).overrideWith((ref) => Future.value(testContributions)),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify contributions are sorted by date (newest first)
      final contributionCards = tester.widgetList<ContributionCard>(
        find.byType(ContributionCard),
      );

      expect(
        contributionCards.first.contribution.description,
        'Newer contribution',
      );
      expect(
        contributionCards.last.contribution.description,
        'Older contribution',
      );
    });

    testWidgets('supports pull-to-refresh functionality', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionsListScreen(goalId: testGoalId),
          overrides: [
            activeGoalContributionsProvider(
              testGoalId,
            ).overrideWith((ref) => Future.value(<GoalContribution>[])),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(RefreshIndicator), findsOneWidget);

      // Test pull-to-refresh gesture
      await tester.drag(
        find.byType(RefreshIndicator),
        const Offset(0, 300),
        warnIfMissed: false,
      );
      await tester.pumpAndSettle();
    });

    testWidgets('displays contributions in a scrollable list', (tester) async {
      final testContributions = List.generate(
        10,
        (index) => GoalContribution(
          id: 'contribution-$index',
          userId: 'user1',
          goalId: testGoalId,
          amountCents: 10000 + (index * 5000),
          contributionDate: DateTime(2024, 1, index + 1),
          description: 'Contribution $index',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          schemaVersion: 1,
        ),
      );

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionsListScreen(goalId: testGoalId),
          overrides: [
            activeGoalContributionsProvider(
              testGoalId,
            ).overrideWith((ref) => Future.value(testContributions)),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(ListView), findsOneWidget);

      // ListView.builder only renders visible items, so we need to scroll to see all
      // First, check that some ContributionCards are visible
      expect(find.byType(ContributionCard), findsWidgets);

      // Scroll to the bottom to ensure all items can be rendered
      await tester.scrollUntilVisible(
        find.text('Contribution 9'), // Last item
        500,
        scrollable: find.byType(Scrollable),
      );

      // Now we should be able to find all 10 items (though not all visible at once)
      // Let's verify by checking the ListView's itemCount through its semantics
      final listView = tester.widget<ListView>(find.byType(ListView));
      expect(listView.semanticChildCount, equals(10));
    });

    testWidgets('handles retry action when contributions fail to load', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionsListScreen(goalId: testGoalId),
          overrides: [
            activeGoalContributionsProvider(
              testGoalId,
            ).overrideWith((ref) => Future.error('Failed to load')),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Tap retry button
      await tester.tap(find.text('Retry'));
      await tester.pumpAndSettle();

      // Verify retry button exists and is tappable
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('displays contributions with proper padding', (tester) async {
      final testContribution = GoalContribution(
        id: '1',
        userId: 'user1',
        goalId: testGoalId,
        amountCents: 50000,
        contributionDate: DateTime(2024, 1, 15),
        description: 'Test contribution',
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        schemaVersion: 1,
      );

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionsListScreen(goalId: testGoalId),
          overrides: [
            activeGoalContributionsProvider(
              testGoalId,
            ).overrideWith((ref) => Future.value([testContribution])),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify ListView has proper padding
      final listView = tester.widget<ListView>(find.byType(ListView));
      expect(listView.padding, const EdgeInsets.all(16));

      // Verify contribution cards have proper spacing
      final paddingWidgets = tester.widgetList<Padding>(
        find.ancestor(
          of: find.byType(ContributionCard),
          matching: find.byType(Padding),
        ),
      );
      expect(
        paddingWidgets.any(
          (p) => p.padding == const EdgeInsets.only(bottom: 12),
        ),
        isTrue,
      );
    });
  });
}
