import 'package:budapp/data/models/goal.dart';
import 'package:budapp/features/goals/presentation/screens/goals_list_screen.dart';
import 'package:budapp/features/goals/providers/goal_providers.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/widgets/common/empty_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';

void main() {
  group('GoalsListScreen', () {
    late MockCurrencyFormatter mockCurrencyFormatter;

    setUp(() {
      mockCurrencyFormatter = MockCurrencyFormatter();
      when(() => mockCurrencyFormatter.formatAmount(any())).thenAnswer((
        invocation,
      ) {
        final cents = invocation.positionalArguments[0] as int;
        return '\$${(cents / 100).toStringAsFixed(2)}';
      });
    });

    testWidgets('displays loading indicator when goals are loading', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalsListScreen(),
          overrides: [
            activeUserGoalsProvider.overrideWith(
              (ref) => Future<List<Goal>>.value(<Goal>[]),
            ),
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      // Initially should show loading
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Goals'), findsOneWidget);

      // Wait for the future to complete
      await tester.pumpAndSettle();
    });

    testWidgets('displays empty state when no goals exist', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalsListScreen(),
          overrides: [
            activeUserGoalsProvider.overrideWith(
              (ref) => Future.value(<Goal>[]),
            ),
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(EmptyState), findsOneWidget);
      expect(find.text('No Goals Yet'), findsOneWidget);
      expect(
        find.text(
          'Create your first financial goal to start tracking your progress.',
        ),
        findsOneWidget,
      );
      expect(find.byIcon(Icons.flag_outlined), findsOneWidget);
    });

    testWidgets('displays goals list when goals exist', (tester) async {
      final testGoals = [
        Goal(
          id: '1',
          userId: 'user1',
          name: 'Emergency Fund',
          targetAmountCents: 100000,
          currentAmountCents: 50000,
          status: GoalStatus.active,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          schemaVersion: 1,
        ),
        Goal(
          id: '2',
          userId: 'user1',
          name: 'Vacation',
          targetAmountCents: 200000,
          currentAmountCents: 150000,
          status: GoalStatus.active,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          schemaVersion: 1,
        ),
      ];

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalsListScreen(),
          overrides: [
            activeUserGoalsProvider.overrideWith(
              (ref) => Future.value(testGoals),
            ),
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('Emergency Fund'), findsOneWidget);
      expect(find.text('Vacation'), findsOneWidget);
      expect(find.text('Active'), findsNWidgets(2));
    });

    testWidgets('displays error state when goals fail to load', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalsListScreen(),
          overrides: [
            activeUserGoalsProvider.overrideWith(
              (ref) => Future.error('Failed to load'),
            ),
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      expect(find.text('Failed to load goals'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('has floating action button for creating new goals', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalsListScreen(),
          overrides: [
            activeUserGoalsProvider.overrideWith(
              (ref) => Future.value(<Goal>[]),
            ),
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(FloatingActionButton), findsOneWidget);
      expect(find.byIcon(Icons.add), findsOneWidget);
    });

    testWidgets('displays goal cards with correct information', (tester) async {
      final testGoal = Goal(
        id: '1',
        userId: 'user1',
        name: 'Emergency Fund',
        targetAmountCents: 100000,
        currentAmountCents: 50000,
        status: GoalStatus.active,
        description: 'Fund for emergencies',
        targetDate: DateTime(2024, 12, 31),
        iconName: 'savings',
        colorHex: '#FF5722',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        schemaVersion: 1,
      );

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalsListScreen(),
          overrides: [
            activeUserGoalsProvider.overrideWith(
              (ref) => Future.value([testGoal]),
            ),
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('Emergency Fund'), findsOneWidget);
      expect(find.text('Active'), findsOneWidget);
      expect(find.text('Fund for emergencies'), findsOneWidget);
      expect(find.textContaining('Target: 31/12/2024'), findsOneWidget);
      expect(find.byIcon(Icons.savings), findsOneWidget);
      expect(find.text(r'$500.00'), findsOneWidget);
      expect(find.text(r'of $1000.00'), findsOneWidget);
    });

    testWidgets('displays popup menu with correct options', (tester) async {
      final testGoal = Goal(
        id: '1',
        userId: 'user1',
        name: 'Emergency Fund',
        targetAmountCents: 100000,
        currentAmountCents: 50000,
        status: GoalStatus.active,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        schemaVersion: 1,
      );

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalsListScreen(),
          overrides: [
            activeUserGoalsProvider.overrideWith(
              (ref) => Future.value([testGoal]),
            ),
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Tap the popup menu button
      await tester.tap(find.byType(PopupMenuButton<String>));
      await tester.pumpAndSettle();

      expect(find.text('Add Contribution'), findsOneWidget);
      expect(find.text('View Contributions'), findsOneWidget);
      expect(find.text('Edit'), findsOneWidget);
      expect(find.text('Delete'), findsOneWidget);
    });

    testWidgets('displays different status chips correctly', (tester) async {
      final testGoals = [
        Goal(
          id: '1',
          userId: 'user1',
          name: 'Active Goal',
          targetAmountCents: 100000,
          currentAmountCents: 50000,
          status: GoalStatus.active,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          schemaVersion: 1,
        ),
        Goal(
          id: '2',
          userId: 'user1',
          name: 'Completed Goal',
          targetAmountCents: 100000,
          currentAmountCents: 100000,
          status: GoalStatus.completed,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          schemaVersion: 1,
        ),
        Goal(
          id: '3',
          userId: 'user1',
          name: 'Paused Goal',
          targetAmountCents: 100000,
          currentAmountCents: 30000,
          status: GoalStatus.paused,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          schemaVersion: 1,
        ),
        Goal(
          id: '4',
          userId: 'user1',
          name: 'Cancelled Goal',
          targetAmountCents: 100000,
          currentAmountCents: 20000,
          status: GoalStatus.cancelled,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          schemaVersion: 1,
        ),
      ];

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalsListScreen(),
          overrides: [
            activeUserGoalsProvider.overrideWith(
              (ref) => Future.value(testGoals),
            ),
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Check each status individually by scrolling to find them
      // First, scroll to top to find Active
      await tester.scrollUntilVisible(
        find.text('Active Goal'),
        -500,
        scrollable: find.byType(Scrollable),
      );
      expect(find.text('Active'), findsOneWidget);

      // Scroll to find Completed
      await tester.scrollUntilVisible(
        find.text('Completed Goal'),
        500,
        scrollable: find.byType(Scrollable),
      );
      expect(find.text('Completed'), findsOneWidget);

      // Scroll to find Paused
      await tester.scrollUntilVisible(
        find.text('Paused Goal'),
        500,
        scrollable: find.byType(Scrollable),
      );
      expect(find.text('Paused'), findsOneWidget);

      // Scroll to find Cancelled
      await tester.scrollUntilVisible(
        find.text('Cancelled Goal'),
        500,
        scrollable: find.byType(Scrollable),
      );
      expect(find.text('Cancelled'), findsOneWidget);
    });

    testWidgets('supports pull-to-refresh functionality', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalsListScreen(),
          overrides: [
            activeUserGoalsProvider.overrideWith(
              (ref) => Future.value(<Goal>[]),
            ),
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(RefreshIndicator), findsOneWidget);

      // Test pull-to-refresh gesture
      await tester.drag(
        find.byType(RefreshIndicator),
        const Offset(0, 300),
        warnIfMissed: false,
      );
      await tester.pumpAndSettle();
    });

    testWidgets('displays progress bars for each goal', (tester) async {
      final testGoal = Goal(
        id: '1',
        userId: 'user1',
        name: 'Emergency Fund',
        targetAmountCents: 100000,
        currentAmountCents: 75000,
        status: GoalStatus.active,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        schemaVersion: 1,
      );

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalsListScreen(),
          overrides: [
            activeUserGoalsProvider.overrideWith(
              (ref) => Future.value([testGoal]),
            ),
            currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify progress bar is present
      expect(find.byType(AnimatedContainer), findsOneWidget);
      expect(find.text('75.0%'), findsOneWidget);
    });
  });
}

class MockCurrencyFormatter extends Mock implements CurrencyFormatter {}
