import 'dart:async';

import 'package:budapp/data/models/goal.dart';
import 'package:budapp/data/repositories/interfaces/goal_repository.dart';
import 'package:budapp/features/goals/presentation/screens/goal_edit_screen.dart';
import 'package:budapp/features/goals/providers/goal_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/widgets/forms/screens/generic_form_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';

class MockGoalRepository extends Mock implements IGoalRepository {}

void main() {
  group('GoalEditScreen', () {
    late MockGoalRepository mockGoalRepository;
    late Goal testGoal;

    setUp(() {
      mockGoalRepository = MockGoalRepository();
      testGoal = Goal(
        id: '1',
        userId: 'user1',
        name: 'Test Goal',
        targetAmountCents: 100000,
        currentAmountCents: 50000,
        status: GoalStatus.active,
        description: 'Test description',
        targetDate: DateTime(2024, 12, 31),
        iconName: 'savings',
        colorHex: '#FF5722',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        schemaVersion: 1,
      );
    });

    testWidgets('displays loading indicator when goal is loading', (
      tester,
    ) async {
      // Create a completer to control when the future completes
      final completer = Completer<Goal?>();

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalEditScreen(goalId: '1'),
          overrides: [
            goalProvider('1').overrideWith((ref) => completer.future),
            goalRepositoryProvider.overrideWithValue(mockGoalRepository),
          ],
        ),
      );

      // Pump once to trigger the loading state
      await tester.pump();

      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Complete the future to clean up
      completer.complete(null);
      await tester.pumpAndSettle();
    });

    testWidgets('displays error state when goal fails to load', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalEditScreen(goalId: '1'),
          overrides: [
            goalProvider(
              '1',
            ).overrideWith((ref) => Future.error('Failed to load')),
            goalRepositoryProvider.overrideWithValue(mockGoalRepository),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      expect(find.text('Failed to load goal'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('displays not found state when goal is null', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalEditScreen(goalId: '1'),
          overrides: [
            goalProvider('1').overrideWith((ref) => Future.value(null)),
            goalRepositoryProvider.overrideWithValue(mockGoalRepository),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('Goal Not Found'), findsOneWidget);
      expect(
        find.text('The requested goal could not be found.'),
        findsOneWidget,
      );
    });

    testWidgets('displays edit form when goal is loaded successfully', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalEditScreen(goalId: '1'),
          overrides: [
            goalProvider('1').overrideWith((ref) => Future.value(testGoal)),
            goalRepositoryProvider.overrideWithValue(mockGoalRepository),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(GenericFormScreen<Goal>), findsOneWidget);
    });

    testWidgets('has correct app bar title for edit mode', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalEditScreen(goalId: '1'),
          overrides: [
            goalProvider('1').overrideWith((ref) => Future.value(testGoal)),
            goalRepositoryProvider.overrideWithValue(mockGoalRepository),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('Edit Goal'), findsOneWidget);
    });

    testWidgets('pre-populates form with existing goal data', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalEditScreen(goalId: '1'),
          overrides: [
            goalProvider('1').overrideWith((ref) => Future.value(testGoal)),
            goalRepositoryProvider.overrideWithValue(mockGoalRepository),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify form fields are pre-populated by checking the TextFormField controllers
      final textFormFields = find.byType(TextFormField);
      expect(
        textFormFields,
        findsNWidgets(3),
      ); // name, description, targetAmount

      // Get the TextFormField widgets and verify their controller values
      final nameFieldWidget =
          textFormFields.at(0).evaluate().first.widget as TextFormField;
      final descriptionFieldWidget =
          textFormFields.at(1).evaluate().first.widget as TextFormField;
      final targetAmountFieldWidget =
          textFormFields.at(2).evaluate().first.widget as TextFormField;

      expect(nameFieldWidget.controller?.text, equals('Test Goal'));
      expect(
        descriptionFieldWidget.controller?.text,
        equals('Test description'),
      );
      expect(
        targetAmountFieldWidget.controller?.text,
        equals('1000.00'),
      ); // Should be formatted with 2 decimal places
    });

    testWidgets('displays Save button instead of Create button', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalEditScreen(goalId: '1'),
          overrides: [
            goalProvider('1').overrideWith((ref) => Future.value(testGoal)),
            goalRepositoryProvider.overrideWithValue(mockGoalRepository),
          ],
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('Save'), findsOneWidget);
      expect(find.text('Create'), findsNothing);
    });

    testWidgets('handles retry action when goal fails to load', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalEditScreen(goalId: '1'),
          overrides: [
            goalProvider(
              '1',
            ).overrideWith((ref) => Future.error('Failed to load')),
            goalRepositoryProvider.overrideWithValue(mockGoalRepository),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Tap retry button
      await tester.tap(find.text('Retry'));
      await tester.pumpAndSettle();

      // Verify retry button exists and is tappable
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('shows goal status selection with current status', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalEditScreen(goalId: '1'),
          overrides: [
            goalProvider('1').overrideWith((ref) => Future.value(testGoal)),
            goalRepositoryProvider.overrideWithValue(mockGoalRepository),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify status field is present
      expect(find.text('Status'), findsOneWidget);

      // Look for DropdownButtonFormField (the status dropdown should be present)
      final dropdownFinder = find.byType(DropdownButtonFormField);
      expect(dropdownFinder, findsAtLeastNWidgets(1));
    });

    testWidgets('displays target date field with current date', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalEditScreen(goalId: '1'),
          overrides: [
            goalProvider('1').overrideWith((ref) => Future.value(testGoal)),
            goalRepositoryProvider.overrideWithValue(mockGoalRepository),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify target date field is present
      expect(find.text('Target Date'), findsOneWidget);

      // The date format might be different (MM/dd/yyyy vs dd/MM/yyyy)
      // Look for the date in US format as seen in debug output
      expect(find.text('12/31/2024'), findsOneWidget);
    });

    testWidgets('displays icon selection with current icon', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalEditScreen(goalId: '1'),
          overrides: [
            goalProvider('1').overrideWith((ref) => Future.value(testGoal)),
            goalRepositoryProvider.overrideWithValue(mockGoalRepository),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify icon field is present
      expect(find.text('Goal Icon'), findsOneWidget);
      expect(find.byIcon(Icons.savings), findsOneWidget);
    });

    testWidgets('displays color selection with current color', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalEditScreen(goalId: '1'),
          overrides: [
            goalProvider('1').overrideWith((ref) => Future.value(testGoal)),
            goalRepositoryProvider.overrideWithValue(mockGoalRepository),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify color field is present
      expect(find.text('Goal Color'), findsOneWidget);
    });

    testWidgets('allows editing all form fields', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalEditScreen(goalId: '1'),
          overrides: [
            goalProvider('1').overrideWith((ref) => Future.value(testGoal)),
            goalRepositoryProvider.overrideWithValue(mockGoalRepository),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Test editing goal name
      final nameField = find.widgetWithText(TextFormField, 'Test Goal');
      await tester.tap(nameField);
      await tester.enterText(nameField, 'Updated Goal Name');
      await tester.pumpAndSettle();

      expect(find.text('Updated Goal Name'), findsOneWidget);

      // Test editing target amount
      final amountField = find.widgetWithText(TextFormField, '1000.00');
      await tester.tap(amountField);
      await tester.enterText(amountField, '2000.00');
      await tester.pumpAndSettle();

      expect(find.text('2000.00'), findsOneWidget);

      // Test editing description
      final descriptionField = find.widgetWithText(
        TextFormField,
        'Test description',
      );
      await tester.tap(descriptionField);
      await tester.enterText(descriptionField, 'Updated description');
      await tester.pumpAndSettle();

      expect(find.text('Updated description'), findsOneWidget);
    });
  });
}
