import 'package:budapp/data/repositories/interfaces/i_goal_contribution_repository.dart';
import 'package:budapp/features/goals/presentation/screens/goal_contribution_create_screen.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';

// Mock classes
class MockGoalContributionRepository extends Mock
    implements IGoalContributionRepository {}

class MockFirebaseAuth extends Mock implements FirebaseAuth {}

class MockFirestore extends Mock implements FirebaseFirestore {}

class MockFirestoreService extends Mock implements FirestoreService {}

class MockUser extends Mock implements User {}

void main() {
  group('GoalContributionCreateScreen', () {
    late MockGoalContributionRepository mockRepository;
    late MockFirebaseAuth mockAuth;
    late MockFirestore mockFirestore;
    late MockFirestoreService mockFirestoreService;
    late MockUser mockUser;
    const testGoalId = 'test-goal-id';

    setUp(() {
      mockRepository = MockGoalContributionRepository();
      mockAuth = MockFirebaseAuth();
      mockFirestore = MockFirestore();
      mockFirestoreService = MockFirestoreService();
      mockUser = MockUser();

      // Setup mock behavior
      when(() => mockAuth.currentUser).thenReturn(mockUser);
      when(() => mockUser.uid).thenReturn('test-user-id');
      when(() => mockUser.email).thenReturn('<EMAIL>');
    });

    testWidgets('displays contribution creation form with all required fields', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionCreateScreen(goalId: testGoalId),
          overrides: [
            goalContributionRepositoryProvider.overrideWithValue(
              mockRepository,
            ),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify the screen title
      expect(find.text('Add Contribution'), findsOneWidget);

      // Verify required form fields are present (check for labels without asterisks first)
      expect(find.textContaining('Contribution Amount'), findsOneWidget);
      expect(find.textContaining('Contribution Date'), findsOneWidget);

      // Verify optional fields are present
      expect(find.textContaining('Description'), findsOneWidget);

      // Verify Create button is present
      expect(find.text('Create'), findsOneWidget);
    });

    testWidgets('Create button is enabled when form is empty', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionCreateScreen(goalId: testGoalId),
          overrides: [
            goalContributionRepositoryProvider.overrideWithValue(
              mockRepository,
            ),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Find the Create button
      final createButton = find.text('Create');
      expect(createButton, findsOneWidget);

      // Verify button is enabled (not null onPressed)
      final elevatedButton = tester.widget<ElevatedButton>(
        find.ancestor(of: createButton, matching: find.byType(ElevatedButton)),
      );
      expect(elevatedButton.onPressed, isNotNull);
    });

    testWidgets('allows filling out contribution creation form', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionCreateScreen(goalId: testGoalId),
          overrides: [
            goalContributionRepositoryProvider.overrideWithValue(
              mockRepository,
            ),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Fill out the contribution amount
      await tester.enterText(
        find.widgetWithText(TextFormField, '0.00'),
        '100.00',
      );

      // Fill out the description
      await tester.enterText(
        find.widgetWithText(
          TextFormField,
          'Optional description for this contribution',
        ),
        'Monthly savings contribution',
      );

      await tester.pumpAndSettle();

      // Verify the text was entered
      expect(find.text('100.00'), findsOneWidget);
      expect(find.text('Monthly savings contribution'), findsOneWidget);
    });

    testWidgets('passes correct goalId to form config', (tester) async {
      const customGoalId = 'custom-goal-id';

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionCreateScreen(goalId: customGoalId),
          overrides: [
            goalContributionRepositoryProvider.overrideWithValue(
              mockRepository,
            ),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify the screen renders without errors (goalId is passed correctly)
      expect(find.text('Add Contribution'), findsOneWidget);
      expect(find.text('Contribution Amount *'), findsOneWidget);
    });
  });
}
