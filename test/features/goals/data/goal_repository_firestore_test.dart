import 'package:budapp/data/models/goal.dart';
import 'package:budapp/data/repositories/implementations/goal_repository_impl.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:firebase_auth_mocks/firebase_auth_mocks.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('GoalRepository Firestore Integration Tests', () {
    late FakeFirebaseFirestore fakeFirestore;
    late MockFirebaseAuth mockAuth;
    late MockUser mockUser;
    late FirestoreService firestoreService;
    late GoalRepositoryImpl goalRepository;

    const testUserId = 'test-user-123';

    setUp(() {
      // Set up fake Firestore
      fakeFirestore = FakeFirebaseFirestore();

      // Set up mock authentication
      mockUser = MockUser(
        uid: testUserId,
        email: '<EMAIL>',
        displayName: 'Test User',
      );
      mockAuth = MockFirebaseAuth(mockUser: mockUser, signedIn: true);

      // Set up services
      firestoreService = FirestoreService(fakeFirestore);
      goalRepository = GoalRepositoryImpl(firestoreService, mockAuth);
    });

    group('Goal Creation Tests', () {
      testWidgets('should create goal with all required fields', (
        tester,
      ) async {
        // Arrange
        final goal = Goal.create(
          userId: testUserId,
          name: 'Emergency Fund',
          targetAmountCents: 100000, // $1000
          targetDate: DateTime.now().add(const Duration(days: 365)),
          status: GoalStatus.active,
          colorHex: '#2196F3',
          iconName: 'account_balance',
        );

        // Act
        final createdGoalId = await goalRepository.createGoal(goal);

        // Assert
        expect(createdGoalId, isNotEmpty);

        // Verify the goal was saved to Firestore
        final savedGoal = await goalRepository.getGoalById(createdGoalId);
        expect(savedGoal, isNotNull);
        expect(savedGoal!.name, equals('Emergency Fund'));
        expect(savedGoal.targetAmountCents, equals(100000));
        expect(savedGoal.userId, equals(testUserId));
        expect(savedGoal.status, equals(GoalStatus.active));
        expect(savedGoal.colorHex, equals('#2196F3'));
        expect(savedGoal.iconName, equals('account_balance'));
      });

      testWidgets('should create goal with minimal required fields only', (
        tester,
      ) async {
        // Arrange
        final goal = Goal.create(
          userId: testUserId,
          name: 'Vacation Fund',
          targetAmountCents: 50000, // $500
        );

        // Act
        final createdGoalId = await goalRepository.createGoal(goal);

        // Assert
        expect(createdGoalId, isNotEmpty);

        // Verify the goal was saved
        final savedGoal = await goalRepository.getGoalById(createdGoalId);
        expect(savedGoal, isNotNull);
        expect(savedGoal!.name, equals('Vacation Fund'));
        expect(savedGoal.targetAmountCents, equals(50000));
        expect(savedGoal.userId, equals(testUserId));
        expect(savedGoal.targetDate, isNull);
        expect(savedGoal.description, isNull);
        expect(savedGoal.colorHex, isNull);
        expect(savedGoal.iconName, isNull);
      });

      testWidgets('should set correct timestamps on goal creation', (
        tester,
      ) async {
        // Arrange
        final beforeCreation = DateTime.now();
        final goal = Goal.create(
          userId: testUserId,
          name: 'Test Goal',
          targetAmountCents: 25000,
        );

        // Act
        final createdGoalId = await goalRepository.createGoal(goal);
        final afterCreation = DateTime.now();

        // Assert
        final savedGoal = await goalRepository.getGoalById(createdGoalId);
        expect(savedGoal, isNotNull);
        expect(savedGoal!.createdAt, isNotNull);
        expect(savedGoal.updatedAt, isNotNull);

        // Verify timestamps are within reasonable range
        expect(
          savedGoal.createdAt?.isAfter(
                beforeCreation.subtract(const Duration(seconds: 1)),
              ) ??
              false,
          isTrue,
        );
        expect(
          savedGoal.createdAt?.isBefore(
                afterCreation.add(const Duration(seconds: 1)),
              ) ??
              false,
          isTrue,
        );
        expect(
          savedGoal.updatedAt?.isAfter(
                beforeCreation.subtract(const Duration(seconds: 1)),
              ) ??
              false,
          isTrue,
        );
        expect(
          savedGoal.updatedAt?.isBefore(
                afterCreation.add(const Duration(seconds: 1)),
              ) ??
              false,
          isTrue,
        );
      });

      testWidgets('should throw exception when user is not authenticated', (
        tester,
      ) async {
        // Arrange
        final unauthenticatedAuth = MockFirebaseAuth();
        final unauthenticatedRepository = GoalRepositoryImpl(
          firestoreService,
          unauthenticatedAuth,
        );

        final goal = Goal.create(
          userId: testUserId,
          name: 'Test Goal',
          targetAmountCents: 25000,
        );

        // Act & Assert
        expect(
          () => unauthenticatedRepository.createGoal(goal),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('User not authenticated'),
            ),
          ),
        );
      });
    });

    group('Goal Data Validation Tests', () {
      testWidgets('should handle goal with description', (tester) async {
        // Arrange
        final goal = Goal.create(
          userId: testUserId,
          name: 'Car Fund',
          description: 'Saving for a new car down payment',
          targetAmountCents: 500000, // $5000
        );

        // Act
        final createdGoalId = await goalRepository.createGoal(goal);

        // Assert
        final savedGoal = await goalRepository.getGoalById(createdGoalId);
        expect(savedGoal, isNotNull);
        expect(
          savedGoal!.description,
          equals('Saving for a new car down payment'),
        );
      });

      testWidgets('should handle goal with target date', (tester) async {
        // Arrange
        final targetDate = DateTime(2025, 12, 31);
        final goal = Goal.create(
          userId: testUserId,
          name: 'Year End Goal',
          targetAmountCents: 100000,
          targetDate: targetDate,
        );

        // Act
        final createdGoalId = await goalRepository.createGoal(goal);

        // Assert
        final savedGoal = await goalRepository.getGoalById(createdGoalId);
        expect(savedGoal, isNotNull);
        expect(savedGoal!.targetDate, equals(targetDate));
      });

      testWidgets('should handle different goal statuses', (tester) async {
        // Test each status
        for (final status in GoalStatus.values) {
          final goal = Goal.create(
            userId: testUserId,
            name: 'Test Goal ${status.name}',
            targetAmountCents: 10000,
            status: status,
          );

          final createdGoalId = await goalRepository.createGoal(goal);
          final savedGoal = await goalRepository.getGoalById(createdGoalId);

          expect(savedGoal, isNotNull);
          expect(savedGoal!.status, equals(status));
        }
      });
    });

    group('Firestore Document Structure Tests', () {
      testWidgets('should save goal to correct Firestore path', (tester) async {
        // Arrange
        final goal = Goal.create(
          userId: testUserId,
          name: 'Path Test Goal',
          targetAmountCents: 15000,
        );

        // Act
        final createdGoalId = await goalRepository.createGoal(goal);

        // Assert
        // Verify the document exists at the expected path
        final docSnapshot = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('goals')
            .doc(createdGoalId)
            .get();

        expect(docSnapshot.exists, isTrue);
        expect(docSnapshot.data(), isNotNull);

        final data = docSnapshot.data()!;
        expect(data['name'], equals('Path Test Goal'));
        expect(data['userId'], equals(testUserId));
        expect(data['targetAmountCents'], equals(15000));
      });

      testWidgets('should include all required fields in Firestore document', (
        tester,
      ) async {
        // Arrange
        final goal = Goal.create(
          userId: testUserId,
          name: 'Complete Goal',
          description: 'A goal with all fields',
          targetAmountCents: 200000,
          targetDate: DateTime(2025, 6, 15),
          status: GoalStatus.active,
          colorHex: '#4CAF50',
          iconName: 'savings',
        );

        // Act
        final createdGoalId = await goalRepository.createGoal(goal);

        // Assert
        final docSnapshot = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('goals')
            .doc(createdGoalId)
            .get();

        final data = docSnapshot.data()!;

        // Check all required fields are present
        expect(data['id'], equals(createdGoalId));
        expect(data['userId'], equals(testUserId));
        expect(data['name'], equals('Complete Goal'));
        expect(data['targetAmountCents'], equals(200000));
        expect(data['createdAt'], isA<Timestamp>());
        expect(data['updatedAt'], isA<Timestamp>());

        // Check optional fields
        expect(data['description'], equals('A goal with all fields'));
        expect(data['targetDate'], isA<Timestamp>());
        expect(data['status'], equals('active'));
        expect(data['colorHex'], equals('#4CAF50'));
        expect(data['iconName'], equals('savings'));
        expect(data['isActive'], equals(true));
        expect(data['currentAmountCents'], equals(0));
        expect(data['isCompleted'], equals(false));
      });
    });
  });
}
