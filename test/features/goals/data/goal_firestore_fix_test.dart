import 'package:budapp/data/models/goal.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Goal Firestore Fix Tests', () {
    test('should create Goal with proper Firestore Timestamp format', () {
      // Create a goal as it would be created by the repository
      final goal = Goal.create(
        userId: 'test-user-123',
        name: 'Emergency Fund',
        targetAmountCents: 100000,
      );

      // Simulate what the repository does
      final goalWithIdAndTimestamps = goal.copyWith(
        id: 'test-goal-456',
        userId: 'test-user-123',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Convert to Firestore format as it would be sent to Firestore
      final goalJson = goalWithIdAndTimestamps.toFirestore();

      // Verify all required fields are present and non-empty
      expect(goalJson['id'], isNotEmpty, reason: 'id must not be empty');
      expect(
        goalJson['userId'],
        isNotEmpty,
        reason: 'userId must not be empty',
      );
      expect(goalJson['name'], isNotEmpty, reason: 'name must not be empty');
      expect(
        goalJson['targetAmountCents'],
        isA<int>(),
        reason: 'targetAmountCents must be int',
      );
      expect(
        goalJson['targetAmountCents'],
        greaterThan(0),
        reason: 'targetAmountCents must be > 0',
      );

      // Verify timestamps are Firestore Timestamp objects, not strings
      expect(
        goalJson['createdAt'],
        isA<Timestamp>(),
        reason: 'createdAt must be Firestore Timestamp',
      );
      expect(
        goalJson['updatedAt'],
        isA<Timestamp>(),
        reason: 'updatedAt must be Firestore Timestamp',
      );

      // Verify field types match Firestore rules expectations
      expect(goalJson['id'], isA<String>());
      expect(goalJson['userId'], isA<String>());
      expect(goalJson['name'], isA<String>());
      expect(goalJson['targetAmountCents'], isA<int>());
      expect(goalJson['status'], isA<String>());
      expect(goalJson['isActive'], isA<bool>());
      expect(goalJson['schemaVersion'], isA<int>());

      // Verify constraints
      expect((goalJson['name'] as String).length, greaterThanOrEqualTo(2));
      expect((goalJson['name'] as String).length, lessThanOrEqualTo(100));
      expect(
        goalJson['targetAmountCents'] as int,
        lessThanOrEqualTo(999999999999),
      );
    });

    test('should handle Goal with targetDate properly', () {
      final targetDate = DateTime(2025, 12, 31);
      final goal = Goal.create(
        userId: 'test-user-123',
        name: 'Year End Goal',
        targetAmountCents: 50000,
        targetDate: targetDate,
      );

      final goalWithIdAndTimestamps = goal.copyWith(
        id: 'test-goal-789',
        userId: 'test-user-123',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final goalJson = goalWithIdAndTimestamps.toFirestore();

      // Verify targetDate is also converted to Firestore Timestamp
      expect(
        goalJson['targetDate'],
        isA<Timestamp>(),
        reason: 'targetDate must be Firestore Timestamp',
      );

      // Verify the timestamp represents the correct date
      final targetTimestamp = goalJson['targetDate'] as Timestamp;
      final convertedDate = targetTimestamp.toDate();
      expect(convertedDate.year, equals(2025));
      expect(convertedDate.month, equals(12));
      expect(convertedDate.day, equals(31));
    });

    test('should roundtrip Goal through JSON conversion properly', () {
      // Create a goal
      final originalGoal = Goal.create(
        userId: 'test-user-123',
        name: 'Roundtrip Test',
        description: 'Test description',
        targetAmountCents: 75000,
        targetDate: DateTime(2025, 6, 15),
        status: GoalStatus.active,
        colorHex: '#4CAF50',
        iconName: 'savings',
      );

      final goalWithIdAndTimestamps = originalGoal.copyWith(
        id: 'test-goal-roundtrip',
        userId: 'test-user-123',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Convert to JSON (as would be sent to Firestore)
      final goalJson = goalWithIdAndTimestamps.toJson();

      // Convert back from JSON (as would be received from Firestore)
      final reconstructedGoal = Goal.fromJson(goalJson);

      // Verify the roundtrip preserves all data
      expect(reconstructedGoal.id, equals(goalWithIdAndTimestamps.id));
      expect(reconstructedGoal.userId, equals(goalWithIdAndTimestamps.userId));
      expect(reconstructedGoal.name, equals(goalWithIdAndTimestamps.name));
      expect(
        reconstructedGoal.description,
        equals(goalWithIdAndTimestamps.description),
      );
      expect(
        reconstructedGoal.targetAmountCents,
        equals(goalWithIdAndTimestamps.targetAmountCents),
      );
      expect(reconstructedGoal.status, equals(goalWithIdAndTimestamps.status));
      expect(
        reconstructedGoal.colorHex,
        equals(goalWithIdAndTimestamps.colorHex),
      );
      expect(
        reconstructedGoal.iconName,
        equals(goalWithIdAndTimestamps.iconName),
      );

      // Verify timestamps are preserved (within reasonable tolerance)
      expect(
        reconstructedGoal.createdAt
                ?.difference(
                  goalWithIdAndTimestamps.createdAt ?? DateTime.now(),
                )
                .inSeconds
                .abs() ??
            0,
        lessThan(1),
        reason: 'createdAt should be preserved through roundtrip',
      );
      expect(
        reconstructedGoal.updatedAt
                ?.difference(
                  goalWithIdAndTimestamps.updatedAt ?? DateTime.now(),
                )
                .inSeconds
                .abs() ??
            0,
        lessThan(1),
        reason: 'updatedAt should be preserved through roundtrip',
      );
      expect(
        reconstructedGoal.targetDate,
        equals(goalWithIdAndTimestamps.targetDate),
      );
    });

    test(
      'should validate form data conversion produces Firestore-compatible Goal',
      () {
        // Simulate the exact form data from the debug output
        final formData = {
          'name': 'test',
          'description': null,
          'targetAmount': 123,
          'targetDate': null,
          'status': GoalStatus.active,
          'color': '#2196F3',
          'icon': 'account_balance',
        };

        // Convert using the data mapper logic
        final targetAmountValue = formData['targetAmount'];
        var targetAmount = 0.0;

        if (targetAmountValue is String) {
          targetAmount = double.tryParse(targetAmountValue) ?? 0;
        } else if (targetAmountValue is num) {
          targetAmount = targetAmountValue.toDouble();
        }

        final targetAmountCents = (targetAmount * 100).round();

        final goal = Goal.create(
          userId: '', // Will be set by repository
          name: formData['name'] as String? ?? '',
          description: formData['description'] as String?,
          targetAmountCents: targetAmountCents,
          targetDate: formData['targetDate'] as DateTime?,
          status: formData['status'] as GoalStatus? ?? GoalStatus.active,
          colorHex: formData['color'] as String?,
          iconName: formData['icon'] as String?,
        );

        // Simulate repository processing
        final goalWithIdAndTimestamps = goal.copyWith(
          id: 'generated-goal-id',
          userId: 'authenticated-user-id',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final goalJson = goalWithIdAndTimestamps.toFirestore();

        // Verify this would pass Firestore rules validation
        expect(goalJson['id'], isNotEmpty);
        expect(goalJson['userId'], isNotEmpty);
        expect(goalJson['name'], isNotEmpty);
        expect(goalJson['targetAmountCents'], equals(12300)); // 123 * 100
        expect(goalJson['createdAt'], isA<Timestamp>());
        expect(goalJson['updatedAt'], isA<Timestamp>());
        expect(goalJson['status'], equals('active'));
        expect(goalJson['colorHex'], equals('#2196F3'));
        expect(goalJson['iconName'], equals('account_balance'));
      },
    );
  });
}
