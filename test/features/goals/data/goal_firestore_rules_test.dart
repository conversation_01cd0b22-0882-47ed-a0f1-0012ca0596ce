import 'package:budapp/data/models/goal.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Goal Firestore Rules Validation Tests', () {
    group('Goal Data Structure Validation', () {
      test('should validate goal data matches Firestore rules requirements', () {
        // Test the exact data structure that gets sent to Firestore
        final goal = Goal.create(
          userId: 'test-user-123',
          name: 'Emergency Fund',
          targetAmountCents: 100000,
          targetDate: DateTime.now().add(const Duration(days: 365)),
          status: GoalStatus.active,
          colorHex: '#2196F3',
          iconName: 'account_balance',
        );

        // Convert to JSON as it would be sent to Firestore
        final goalJson = goal.toJson();

        // Validate required fields according to Firestore rules
        // From firestore.rules line 650: hasRequiredFields(data, ['id', 'userId', 'name', 'targetAmountCents', 'createdAt', 'updatedAt'])
        expect(
          goalJson.containsKey('id'),
          isTrue,
          reason: 'Missing required field: id',
        );
        expect(
          goalJson.containsKey('userId'),
          isTrue,
          reason: 'Missing required field: userId',
        );
        expect(
          goalJson.containsKey('name'),
          isTrue,
          reason: 'Missing required field: name',
        );
        expect(
          goalJson.containsKey('targetAmountCents'),
          isTrue,
          reason: 'Missing required field: targetAmountCents',
        );
        expect(
          goalJson.containsKey('createdAt'),
          isTrue,
          reason: 'Missing required field: createdAt',
        );
        expect(
          goalJson.containsKey('updatedAt'),
          isTrue,
          reason: 'Missing required field: updatedAt',
        );

        // Validate field types according to Firestore rules
        expect(goalJson['id'], isA<String>(), reason: 'id must be string');
        expect(
          goalJson['userId'],
          isA<String>(),
          reason: 'userId must be string',
        );
        expect(goalJson['name'], isA<String>(), reason: 'name must be string');
        expect(
          goalJson['targetAmountCents'],
          isA<int>(),
          reason: 'targetAmountCents must be int',
        );

        // Validate string length constraints
        // From firestore.rules line 653: isValidStringLength(data.name, 2, 100)
        final name = goalJson['name'] as String;
        expect(
          name.length,
          greaterThanOrEqualTo(2),
          reason: 'name must be at least 2 characters',
        );
        expect(
          name.length,
          lessThanOrEqualTo(100),
          reason: 'name must be at most 100 characters',
        );

        // Validate amount constraints
        // From firestore.rules line 654: data.targetAmountCents is int && data.targetAmountCents > 0 && data.targetAmountCents <= 999999999999
        final targetAmount = goalJson['targetAmountCents'] as int;
        expect(
          targetAmount,
          greaterThan(0),
          reason: 'targetAmountCents must be greater than 0',
        );
        expect(
          targetAmount,
          lessThanOrEqualTo(999999999999),
          reason: 'targetAmountCents must be <= 999999999999',
        );

        // Validate optional fields if present
        if (goalJson.containsKey('description')) {
          final description = goalJson['description'];
          if (description != null) {
            expect(
              description,
              isA<String>(),
              reason: 'description must be string if present',
            );
            expect(
              (description as String).length,
              lessThanOrEqualTo(500),
              reason: 'description must be <= 500 characters',
            );
          }
        }

        if (goalJson.containsKey('colorHex')) {
          final colorHex = goalJson['colorHex'];
          if (colorHex != null) {
            expect(
              colorHex,
              isA<String>(),
              reason: 'colorHex must be string if present',
            );
            // Validate hex color format: ^#[0-9A-Fa-f]{6}$
            expect(
              RegExp(r'^#[0-9A-Fa-f]{6}$').hasMatch(colorHex as String),
              isTrue,
              reason: 'colorHex must be valid hex format #RRGGBB',
            );
          }
        }

        if (goalJson.containsKey('iconName')) {
          final iconName = goalJson['iconName'];
          if (iconName != null) {
            expect(
              iconName,
              isA<String>(),
              reason: 'iconName must be string if present',
            );
            // Validate icon name format: ^[a-zA-Z][a-zA-Z0-9_]*$
            expect(
              RegExp(r'^[a-zA-Z][a-zA-Z0-9_]*$').hasMatch(iconName as String),
              isTrue,
              reason: r'iconName must match pattern ^[a-zA-Z][a-zA-Z0-9_]*$',
            );
            expect(
              iconName.length,
              lessThanOrEqualTo(50),
              reason: 'iconName must be <= 50 characters',
            );
          }
        }

        if (goalJson.containsKey('status')) {
          final status = goalJson['status'];
          if (status != null) {
            expect(
              status,
              isA<String>(),
              reason: 'status must be string if present',
            );
            expect(
              ['active', 'paused', 'completed', 'cancelled'].contains(status),
              isTrue,
              reason:
                  'status must be one of: active, paused, completed, cancelled',
            );
          }
        }
      });

      test('should validate goal with minimal required fields', () {
        final goal = Goal.create(
          userId: 'test-user-123',
          name: 'Minimal Goal',
          targetAmountCents: 50000,
        );

        final goalJson = goal.toJson();

        // Validate all required fields are present
        expect(goalJson.containsKey('id'), isTrue);
        expect(goalJson.containsKey('userId'), isTrue);
        expect(goalJson.containsKey('name'), isTrue);
        expect(goalJson.containsKey('targetAmountCents'), isTrue);
        expect(goalJson.containsKey('createdAt'), isTrue);
        expect(goalJson.containsKey('updatedAt'), isTrue);

        // Validate field types
        expect(goalJson['id'], isA<String>());
        expect(goalJson['userId'], isA<String>());
        expect(goalJson['name'], isA<String>());
        expect(goalJson['targetAmountCents'], isA<int>());
      });

      test('should identify missing required fields', () {
        // Create a goal and manually remove required fields to test validation
        final goal = Goal.create(
          userId: 'test-user-123',
          name: 'Test Goal',
          targetAmountCents: 25000,
        );

        final goalJson = goal.toJson();

        // Test each required field
        final requiredFields = [
          'id',
          'userId',
          'name',
          'targetAmountCents',
          'createdAt',
          'updatedAt',
        ];

        for (final field in requiredFields) {
          final modifiedJson = Map<String, dynamic>.from(goalJson)
            ..remove(field);

          // This should fail Firestore rules validation
          expect(
            modifiedJson.containsKey(field),
            isFalse,
            reason: 'Field $field should be missing for this test',
          );
        }
      });

      test('should validate field type mismatches', () {
        final goal = Goal.create(
          userId: 'test-user-123',
          name: 'Type Test Goal',
          targetAmountCents: 75000,
        );

        final goalJson = goal.toJson();

        // Test type validations that would fail in Firestore rules
        final typeTests = {
          'targetAmountCents': [
            {
              'value': '75000',
              'type': 'string',
              'shouldFail': true,
            }, // Should be int
            {
              'value': 75000.5,
              'type': 'double',
              'shouldFail': true,
            }, // Should be int
            {
              'value': 75000,
              'type': 'int',
              'shouldFail': false,
            }, // Correct type
          ],
          'name': [
            {
              'value': 123,
              'type': 'int',
              'shouldFail': true,
            }, // Should be string
            {
              'value': 'Valid Name',
              'type': 'string',
              'shouldFail': false,
            }, // Correct type
          ],
        };

        for (final entry in typeTests.entries) {
          final field = entry.key;
          final tests = entry.value;
          for (final test in tests) {
            final modifiedJson = Map<String, dynamic>.from(goalJson);
            modifiedJson[field] = test['value'];

            if (test['shouldFail']! as bool) {
              // Validate that the type is incorrect for Firestore rules
              expect(
                modifiedJson[field].runtimeType,
                isNot(equals(goalJson[field].runtimeType)),
                reason:
                    'Field $field should have incorrect type for validation test',
              );
            } else {
              // Validate that the type is correct for Firestore rules
              expect(
                modifiedJson[field].runtimeType,
                equals(goalJson[field].runtimeType),
                reason: 'Field $field should have correct type',
              );
            }
          }
        }
      });

      test('should validate amount constraints', () {
        // Test boundary values for targetAmountCents
        final testCases = [
          {'amount': 0, 'shouldFail': true, 'reason': 'amount must be > 0'},
          {'amount': -1, 'shouldFail': true, 'reason': 'amount must be > 0'},
          {'amount': 1, 'shouldFail': false, 'reason': 'minimum valid amount'},
          {
            'amount': 999999999999,
            'shouldFail': false,
            'reason': 'maximum valid amount',
          },
          {
            'amount': 1000000000000,
            'shouldFail': true,
            'reason': 'amount exceeds maximum',
          },
        ];

        for (final testCase in testCases) {
          if (testCase['shouldFail']! as bool) {
            // Expected to fail Firestore rules validation
          } else {
            // Expected to pass Firestore rules validation

            // Create goal with this amount to verify it works
            final goal = Goal.create(
              userId: 'test-user-123',
              name: 'Amount Test Goal',
              targetAmountCents: testCase['amount']! as int,
            );

            final goalJson = goal.toJson();
            expect(goalJson['targetAmountCents'], equals(testCase['amount']));
          }
        }
      });
    });
  });
}
