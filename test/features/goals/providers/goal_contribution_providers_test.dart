import 'dart:async';

import 'package:budapp/data/models/goal_contribution.dart';
import 'package:budapp/data/repositories/interfaces/repositories.dart';
import 'package:budapp/features/goals/providers/goal_contribution_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

void main() {
  group('Goal Contribution Providers Tests', () {
    late ProviderContainer container;
    late MockGoalContributionRepository mockRepository;

    const testGoalId = 'test-goal-123';
    const testContributionId = 'test-contribution-123';

    setUp(() {
      mockRepository = MockGoalContributionRepository();

      container = ProviderContainer(
        overrides: [
          goalContributionRepositoryProvider.overrideWithValue(mockRepository),
        ],
      );

      // Register fallback values
      registerFallbackValue(_createTestContribution());
    });

    tearDown(() {
      container.dispose();
    });

    group('Data Provider Tests', () {
      test('goalContributions should return contributions for goal', () async {
        // Arrange
        final mockContributions = [
          _createTestContribution(id: 'contrib-1', amountCents: 5000),
          _createTestContribution(id: 'contrib-2', amountCents: 3000),
        ];
        when(
          () => mockRepository.getContributionsForGoal(testGoalId),
        ).thenAnswer((_) async => mockContributions);

        // Act
        final contributions = await container.read(
          goalContributionsProvider(testGoalId).future,
        );

        // Assert
        expect(contributions, equals(mockContributions));
        verify(
          () => mockRepository.getContributionsForGoal(testGoalId),
        ).called(1);
      });

      test(
        'activeGoalContributions should return active contributions',
        () async {
          // Arrange
          final mockContributions = [
            _createTestContribution(id: 'contrib-1', amountCents: 5000),
          ];
          when(
            () => mockRepository.getActiveContributionsForGoal(testGoalId),
          ).thenAnswer((_) async => mockContributions);

          // Act
          final contributions = await container.read(
            activeGoalContributionsProvider(testGoalId).future,
          );

          // Assert
          expect(contributions, equals(mockContributions));
          verify(
            () => mockRepository.getActiveContributionsForGoal(testGoalId),
          ).called(1);
        },
      );

      test('goalContribution should return specific contribution', () async {
        // Arrange
        final contribution = _createTestContribution(id: testContributionId);
        when(
          () => mockRepository.getContributionById(
            testGoalId,
            testContributionId,
          ),
        ).thenAnswer((_) async => contribution);

        // Act
        final result = await container.read(
          goalContributionProvider(testGoalId, testContributionId).future,
        );

        // Assert
        expect(result, equals(contribution));
        verify(
          () => mockRepository.getContributionById(
            testGoalId,
            testContributionId,
          ),
        ).called(1);
      });

      test(
        'watchGoalContributions should return stream of contributions',
        () async {
          // Arrange
          final mockContributions = [_createTestContribution(id: 'contrib-1')];
          when(
            () => mockRepository.watchContributionsForGoal(testGoalId),
          ).thenAnswer((_) => Stream.value(mockContributions));

          // Act
          final result = await container.read(
            watchGoalContributionsProvider(testGoalId).future,
          );

          // Assert
          expect(result, equals(mockContributions));
          verify(
            () => mockRepository.watchContributionsForGoal(testGoalId),
          ).called(1);
        },
      );

      test(
        'watchActiveGoalContributions should return stream of active contributions',
        () async {
          // Arrange
          final mockContributions = [_createTestContribution(id: 'contrib-1')];
          when(
            () => mockRepository.watchActiveContributionsForGoal(testGoalId),
          ).thenAnswer((_) => Stream.value(mockContributions));

          // Act
          final result = await container.read(
            watchActiveGoalContributionsProvider(testGoalId).future,
          );

          // Assert
          expect(result, equals(mockContributions));
          verify(
            () => mockRepository.watchActiveContributionsForGoal(testGoalId),
          ).called(1);
        },
      );

      test(
        'goalContributionStats should return contribution statistics',
        () async {
          // Arrange
          final stats = {
            'totalAmount': 8000,
            'count': 2,
            'averageAmount': 4000,
          };
          when(
            () => mockRepository.getContributionStatistics(testGoalId),
          ).thenAnswer((_) async => stats);

          // Act
          final result = await container.read(
            goalContributionStatsProvider(testGoalId).future,
          );

          // Assert
          expect(result, equals(stats));
          verify(
            () => mockRepository.getContributionStatistics(testGoalId),
          ).called(1);
        },
      );

      test('totalGoalContributionAmount should return total amount', () async {
        // Arrange
        const totalAmount = 15000;
        when(
          () => mockRepository.getTotalContributionAmountForGoal(testGoalId),
        ).thenAnswer((_) async => totalAmount);

        // Act
        final result = await container.read(
          totalGoalContributionAmountProvider(testGoalId).future,
        );

        // Assert
        expect(result, equals(totalAmount));
        verify(
          () => mockRepository.getTotalContributionAmountForGoal(testGoalId),
        ).called(1);
      });

      test('goalContributionCount should return contribution count', () async {
        // Arrange
        const count = 5;
        when(
          () => mockRepository.getContributionCountForGoal(testGoalId),
        ).thenAnswer((_) async => count);

        // Act
        final result = await container.read(
          goalContributionCountProvider(testGoalId).future,
        );

        // Assert
        expect(result, equals(count));
        verify(
          () => mockRepository.getContributionCountForGoal(testGoalId),
        ).called(1);
      });

      test(
        'recentGoalContributions should return recent contributions with default limit',
        () async {
          // Arrange
          final mockContributions = [
            _createTestContribution(id: 'contrib-1'),
            _createTestContribution(id: 'contrib-2'),
          ];
          when(
            () => mockRepository.getRecentContributions(testGoalId, limit: 5),
          ).thenAnswer((_) async => mockContributions);

          // Act
          final result = await container.read(
            recentGoalContributionsProvider(testGoalId).future,
          );

          // Assert
          expect(result, equals(mockContributions));
          verify(
            () => mockRepository.getRecentContributions(testGoalId, limit: 5),
          ).called(1);
        },
      );

      test(
        'recentGoalContributions should return recent contributions with custom limit',
        () async {
          // Arrange
          final mockContributions = [_createTestContribution(id: 'contrib-1')];
          when(
            () => mockRepository.getRecentContributions(testGoalId, limit: 3),
          ).thenAnswer((_) async => mockContributions);

          // Act
          final result = await container.read(
            recentGoalContributionsProvider(testGoalId, limit: 3).future,
          );

          // Assert
          expect(result, equals(mockContributions));
          verify(
            () => mockRepository.getRecentContributions(testGoalId, limit: 3),
          ).called(1);
        },
      );
    });

    group('Goal Contribution Creation Tests', () {
      test(
        'GoalContributionCreation should create contribution successfully',
        () async {
          // Arrange
          final contribution = _createTestContribution();
          when(
            () => mockRepository.createContribution(testGoalId, contribution),
          ).thenAnswer((_) async => 'created-id');

          final notifier = container.read(
            goalContributionCreationProvider.notifier,
          );

          // Act
          await notifier.createContribution(testGoalId, contribution);

          // Assert
          final state = container.read(goalContributionCreationProvider);
          expect(state.hasValue, isTrue);
          verify(
            () => mockRepository.createContribution(testGoalId, contribution),
          ).called(1);
        },
      );

      test('GoalContributionCreation should handle creation errors', () async {
        // Arrange
        final contribution = _createTestContribution();
        final error = Exception('Creation failed');
        when(
          () => mockRepository.createContribution(testGoalId, contribution),
        ).thenThrow(error);

        final notifier = container.read(
          goalContributionCreationProvider.notifier,
        );

        // Act
        await notifier.createContribution(testGoalId, contribution);

        // Assert
        final state = container.read(goalContributionCreationProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(error));
      });

      test('GoalContributionCreation should track loading state', () async {
        // Arrange
        final contribution = _createTestContribution();
        final completer = Completer<String>();
        when(
          () => mockRepository.createContribution(testGoalId, contribution),
        ).thenAnswer((_) => completer.future);

        final notifier = container.read(
          goalContributionCreationProvider.notifier,
        );

        // Act
        final createFuture = notifier.createContribution(
          testGoalId,
          contribution,
        );

        // Assert loading state
        final loadingState = container.read(goalContributionCreationProvider);
        expect(loadingState.isLoading, isTrue);

        // Complete operation
        completer.complete('created-id');
        await createFuture;

        // Assert final state
        final finalState = container.read(goalContributionCreationProvider);
        expect(finalState.hasValue, isTrue);
      });

      test(
        'GoalContributionCreation should invalidate related providers after creation',
        () async {
          // Arrange
          final contribution = _createTestContribution();
          when(
            () => mockRepository.createContribution(testGoalId, contribution),
          ).thenAnswer((_) async => 'created-id');

          // Setup mock responses for providers that will be invalidated
          when(
            () => mockRepository.getContributionsForGoal(testGoalId),
          ).thenAnswer((_) async => []);
          when(
            () => mockRepository.getActiveContributionsForGoal(testGoalId),
          ).thenAnswer((_) async => []);
          when(
            () => mockRepository.getContributionStatistics(testGoalId),
          ).thenAnswer((_) async => {});
          when(
            () => mockRepository.getTotalContributionAmountForGoal(testGoalId),
          ).thenAnswer((_) async => 0);
          when(
            () => mockRepository.getContributionCountForGoal(testGoalId),
          ).thenAnswer((_) async => 0);
          when(
            () => mockRepository.getRecentContributions(testGoalId, limit: 5),
          ).thenAnswer((_) async => []);

          final notifier = container.read(
            goalContributionCreationProvider.notifier,
          );

          // Act
          await notifier.createContribution(testGoalId, contribution);

          // Assert - verify creation was called
          verify(
            () => mockRepository.createContribution(testGoalId, contribution),
          ).called(1);
        },
      );
    });

    group('Goal Contribution Update Tests', () {
      test(
        'GoalContributionUpdate should update contribution successfully',
        () async {
          // Arrange
          final contribution = _createTestContribution(id: testContributionId);
          when(
            () => mockRepository.updateContribution(
              testGoalId,
              testContributionId,
              contribution,
            ),
          ).thenAnswer((_) async {});

          final notifier = container.read(
            goalContributionUpdateProvider.notifier,
          );

          // Act
          await notifier.updateContribution(
            testGoalId,
            testContributionId,
            contribution,
          );

          // Assert
          final state = container.read(goalContributionUpdateProvider);
          expect(state.hasValue, isTrue);
          verify(
            () => mockRepository.updateContribution(
              testGoalId,
              testContributionId,
              contribution,
            ),
          ).called(1);
        },
      );

      test('GoalContributionUpdate should handle update errors', () async {
        // Arrange
        final contribution = _createTestContribution(id: testContributionId);
        final error = Exception('Update failed');
        when(
          () => mockRepository.updateContribution(
            testGoalId,
            testContributionId,
            contribution,
          ),
        ).thenThrow(error);

        final notifier = container.read(
          goalContributionUpdateProvider.notifier,
        );

        // Act
        await notifier.updateContribution(
          testGoalId,
          testContributionId,
          contribution,
        );

        // Assert
        final state = container.read(goalContributionUpdateProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(error));
      });

      test('GoalContributionUpdate should track loading state', () async {
        // Arrange
        final contribution = _createTestContribution(id: testContributionId);
        final completer = Completer<void>();
        when(
          () => mockRepository.updateContribution(
            testGoalId,
            testContributionId,
            contribution,
          ),
        ).thenAnswer((_) => completer.future);

        final notifier = container.read(
          goalContributionUpdateProvider.notifier,
        );

        // Act
        final updateFuture = notifier.updateContribution(
          testGoalId,
          testContributionId,
          contribution,
        );

        // Assert loading state
        final loadingState = container.read(goalContributionUpdateProvider);
        expect(loadingState.isLoading, isTrue);

        // Complete operation
        completer.complete();
        await updateFuture;

        // Assert final state
        final finalState = container.read(goalContributionUpdateProvider);
        expect(finalState.hasValue, isTrue);
      });
    });

    group('Goal Contribution Deletion Tests', () {
      test(
        'GoalContributionDeletion should delete contribution successfully',
        () async {
          // Arrange
          when(
            () => mockRepository.deleteContribution(
              testGoalId,
              testContributionId,
            ),
          ).thenAnswer((_) async {});

          final notifier = container.read(
            goalContributionDeletionProvider.notifier,
          );

          // Act
          await notifier.deleteContribution(testGoalId, testContributionId);

          // Assert
          final state = container.read(goalContributionDeletionProvider);
          expect(state.hasValue, isTrue);
          verify(
            () => mockRepository.deleteContribution(
              testGoalId,
              testContributionId,
            ),
          ).called(1);
        },
      );

      test('GoalContributionDeletion should handle deletion errors', () async {
        // Arrange
        final error = Exception('Deletion failed');
        when(
          () =>
              mockRepository.deleteContribution(testGoalId, testContributionId),
        ).thenThrow(error);

        final notifier = container.read(
          goalContributionDeletionProvider.notifier,
        );

        // Act
        await notifier.deleteContribution(testGoalId, testContributionId);

        // Assert
        final state = container.read(goalContributionDeletionProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(error));
      });

      test('GoalContributionDeletion should track loading state', () async {
        // Arrange
        final completer = Completer<void>();
        when(
          () =>
              mockRepository.deleteContribution(testGoalId, testContributionId),
        ).thenAnswer((_) => completer.future);

        final notifier = container.read(
          goalContributionDeletionProvider.notifier,
        );

        // Act
        final deleteFuture = notifier.deleteContribution(
          testGoalId,
          testContributionId,
        );

        // Assert loading state
        final loadingState = container.read(goalContributionDeletionProvider);
        expect(loadingState.isLoading, isTrue);

        // Complete operation
        completer.complete();
        await deleteFuture;

        // Assert final state
        final finalState = container.read(goalContributionDeletionProvider);
        expect(finalState.hasValue, isTrue);
      });
    });
  });
}

// Helper function to create test contributions
GoalContribution _createTestContribution({
  String? id,
  String? userId,
  String? goalId,
  int? amountCents,
  DateTime? contributionDate,
  String? description,
}) {
  return GoalContribution.create(
    userId: userId ?? 'test-user-123',
    goalId: goalId ?? 'test-goal-123',
    amountCents: amountCents ?? 5000,
    contributionDate: contributionDate ?? DateTime.now(),
    description: description ?? 'Test contribution',
  ).copyWith(id: id ?? 'test-contribution-id');
}

// Mock classes
class MockGoalContributionRepository extends Mock
    implements IGoalContributionRepository {}
