import 'dart:async';

import 'package:budapp/data/models/goal.dart';
import 'package:budapp/data/repositories/interfaces/repositories.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:budapp/features/goals/providers/goal_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

void main() {
  group('Goal Providers Tests', () {
    late ProviderContainer container;
    late MockGoalRepository mockGoalRepository;
    late MockAuthService mockAuthService;
    late MockUser mockUser;

    const testUserId = 'test-user-123';
    const testGoalId = 'test-goal-123';

    setUp(() {
      mockGoalRepository = MockGoalRepository();
      mockAuthService = MockAuthService();
      mockUser = MockUser();

      // Setup mock user
      when(() => mockUser.uid).thenReturn(testUserId);
      when(() => mockAuthService.currentUser).thenReturn(mockUser);

      container = ProviderContainer(
        overrides: [
          goalRepositoryProvider.overrideWithValue(mockGoalRepository),
          authServiceProvider.overrideWithValue(mockAuthService),
        ],
      );

      // Register fallback values
      registerFallbackValue(_createTestGoal());
    });

    tearDown(() {
      container.dispose();
    });

    group('Data Provider Tests', () {
      test('activeUserGoals should return active goals', () async {
        // Arrange
        final mockGoals = [
          _createTestGoal(id: 'goal-1', name: 'Emergency Fund'),
          _createTestGoal(id: 'goal-2', name: 'Vacation Fund'),
        ];
        when(
          () => mockGoalRepository.getActiveGoals(),
        ).thenAnswer((_) async => mockGoals);

        // Act
        final goals = await container.read(activeUserGoalsProvider.future);

        // Assert
        expect(goals, equals(mockGoals));
        verify(() => mockGoalRepository.getActiveGoals()).called(1);
      });

      test('goal provider should return specific goal', () async {
        // Arrange
        final goal = _createTestGoal(id: testGoalId, name: 'Test Goal');
        when(
          () => mockGoalRepository.getGoalById(testGoalId),
        ).thenAnswer((_) async => goal);

        // Act
        final result = await container.read(goalProvider(testGoalId).future);

        // Assert
        expect(result, equals(goal));
        verify(() => mockGoalRepository.getGoalById(testGoalId)).called(1);
      });

      test('watchUserGoals should return stream of user goals', () async {
        // Arrange
        final mockGoals = [_createTestGoal(id: 'goal-1')];
        when(
          () => mockGoalRepository.watchUserGoals(testUserId),
        ).thenAnswer((_) => Stream.value(mockGoals));

        // Act
        final asyncValue = await container.read(
          watchUserGoalsProvider(testUserId).future,
        );

        // Assert
        expect(asyncValue, equals(mockGoals));
        verify(() => mockGoalRepository.watchUserGoals(testUserId)).called(1);
      });

      test('watchGoal should return stream of specific goal', () async {
        // Arrange
        final goal = _createTestGoal(id: testGoalId);
        when(
          () => mockGoalRepository.watchGoal(testGoalId),
        ).thenAnswer((_) => Stream.value(goal));

        // Act
        final result = await container.read(
          watchGoalProvider(testGoalId).future,
        );

        // Assert
        expect(result, equals(goal));
        verify(() => mockGoalRepository.watchGoal(testGoalId)).called(1);
      });

      test('goalStats should return goal statistics', () async {
        // Arrange
        final stats = {'progress': 0.5, 'daysRemaining': 30};
        when(
          () => mockGoalRepository.getGoalStats(testGoalId),
        ).thenAnswer((_) async => stats);

        // Act
        final result = await container.read(
          goalStatsProvider(testGoalId).future,
        );

        // Assert
        expect(result, equals(stats));
        verify(() => mockGoalRepository.getGoalStats(testGoalId)).called(1);
      });

      test('userGoalSummary should return user goal summary', () async {
        // Arrange
        final summary = {'totalGoals': 3, 'completedGoals': 1};
        when(
          () => mockGoalRepository.getUserGoalSummary(testUserId),
        ).thenAnswer((_) async => summary);

        // Act
        final result = await container.read(
          userGoalSummaryProvider(testUserId).future,
        );

        // Assert
        expect(result, equals(summary));
        verify(
          () => mockGoalRepository.getUserGoalSummary(testUserId),
        ).called(1);
      });
    });

    group('Goal Creation Tests', () {
      test('GoalCreation should create goal successfully', () async {
        // Arrange
        final goal = _createTestGoal();
        when(
          () => mockGoalRepository.createGoal(goal),
        ).thenAnswer((_) async => 'created-id');

        final notifier = container.read(goalCreationProvider.notifier);

        // Act
        await notifier.createGoal(goal);

        // Assert
        final state = container.read(goalCreationProvider);
        expect(state.hasValue, isTrue);
        verify(() => mockGoalRepository.createGoal(goal)).called(1);
      });

      test('GoalCreation should handle creation errors', () async {
        // Arrange
        final goal = _createTestGoal();
        final error = Exception('Creation failed');
        when(() => mockGoalRepository.createGoal(goal)).thenThrow(error);

        final notifier = container.read(goalCreationProvider.notifier);

        // Act
        await notifier.createGoal(goal);

        // Assert
        final state = container.read(goalCreationProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(error));
      });

      test('GoalCreation should track loading state', () async {
        // Arrange
        final goal = _createTestGoal();
        final completer = Completer<String>();
        when(
          () => mockGoalRepository.createGoal(goal),
        ).thenAnswer((_) => completer.future);

        final notifier = container.read(goalCreationProvider.notifier);

        // Act
        final createFuture = notifier.createGoal(goal);

        // Assert loading state
        final loadingState = container.read(goalCreationProvider);
        expect(loadingState.isLoading, isTrue);

        // Complete operation
        completer.complete('created-id');
        await createFuture;

        // Assert final state
        final finalState = container.read(goalCreationProvider);
        expect(finalState.hasValue, isTrue);
      });
    });

    group('Goal Update Tests', () {
      test('GoalUpdate should update goal successfully', () async {
        // Arrange
        final goal = _createTestGoal(id: testGoalId);
        when(
          () => mockGoalRepository.updateGoal(testGoalId, goal),
        ).thenAnswer((_) async {});

        final notifier = container.read(goalUpdateProvider.notifier);

        // Act
        await notifier.updateGoal(testGoalId, goal);

        // Assert
        final state = container.read(goalUpdateProvider);
        expect(state.hasValue, isTrue);
        verify(() => mockGoalRepository.updateGoal(testGoalId, goal)).called(1);
      });

      test('GoalUpdate should handle update errors', () async {
        // Arrange
        final goal = _createTestGoal(id: testGoalId);
        final error = Exception('Update failed');
        when(
          () => mockGoalRepository.updateGoal(testGoalId, goal),
        ).thenThrow(error);

        final notifier = container.read(goalUpdateProvider.notifier);

        // Act
        await notifier.updateGoal(testGoalId, goal);

        // Assert
        final state = container.read(goalUpdateProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(error));
      });
    });

    group('Goal Deletion Tests', () {
      test('GoalDeletion should delete goal successfully', () async {
        // Arrange
        when(
          () => mockGoalRepository.deleteGoal(testGoalId),
        ).thenAnswer((_) async {});

        final notifier = container.read(goalDeletionProvider.notifier);

        // Act
        await notifier.deleteGoal(testGoalId);

        // Assert
        final state = container.read(goalDeletionProvider);
        expect(state.hasValue, isTrue);
        verify(() => mockGoalRepository.deleteGoal(testGoalId)).called(1);
      });

      test('GoalDeletion should handle deletion errors', () async {
        // Arrange
        final error = Exception('Deletion failed');
        when(() => mockGoalRepository.deleteGoal(testGoalId)).thenThrow(error);

        final notifier = container.read(goalDeletionProvider.notifier);

        // Act
        await notifier.deleteGoal(testGoalId);

        // Assert
        final state = container.read(goalDeletionProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(error));
      });
    });
  });
}

// Helper function to create test goals
Goal _createTestGoal({
  String? id,
  String? userId,
  String? name,
  int? targetAmountCents,
  int? currentAmountCents,
  DateTime? targetDate,
  GoalStatus? status,
  String? colorHex,
  String? iconName,
}) {
  final now = DateTime.now();
  return Goal.create(
    userId: userId ?? 'test-user-123',
    name: name ?? 'Test Goal',
    targetAmountCents: targetAmountCents ?? 100000,
    targetDate: targetDate ?? now.add(const Duration(days: 365)),
    status: status ?? GoalStatus.active,
    colorHex: colorHex ?? '#2196F3',
    iconName: iconName ?? 'account_balance',
    currentAmountCents: currentAmountCents ?? 0,
  ).copyWith(id: id ?? 'test-goal-id');
}

// Mock classes
class MockGoalRepository extends Mock implements IGoalRepository {}

class MockAuthService extends Mock implements AuthService {}

class MockUser extends Mock implements User {}
