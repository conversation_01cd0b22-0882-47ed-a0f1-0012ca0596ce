import 'dart:async';

import 'package:budapp/data/models/goal_contribution.dart';
import 'package:budapp/data/repositories/interfaces/repositories.dart';
import 'package:budapp/features/goals/providers/goal_contribution_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

void main() {
  group('Goal Contribution Providers Generated Code Tests', () {
    late ProviderContainer container;
    late MockGoalContributionRepository mockRepository;

    const testGoalId = 'test-goal-123';
    const testContributionId = 'test-contribution-123';

    setUp(() {
      mockRepository = MockGoalContributionRepository();

      container = ProviderContainer(
        overrides: [
          goalContributionRepositoryProvider.overrideWithValue(mockRepository),
        ],
      );

      // Register fallback values for mocktail
      registerFallbackValue(_createTestContribution());
    });

    tearDown(() {
      container.dispose();
    });

    group('Provider Families - Generated Code', () {
      test(
        'goalContributionsProvider family should create provider instances',
        () {
          // Test the generated GoalContributionsFamily class
          const family = goalContributionsProvider;
          const goalId1 = 'goal-1';
          const goalId2 = 'goal-2';

          // Create different instances
          final provider1 = family(goalId1);
          final provider2 = family(goalId2);

          // Verify instances are different for different parameters
          expect(provider1, isNot(equals(provider2)));
          expect(provider1.goalId, equals(goalId1));
          expect(provider2.goalId, equals(goalId2));
        },
      );

      test(
        'goalContributionsProvider should handle same parameters consistently',
        () {
          const goalId = 'test-goal-id';
          final provider1 = goalContributionsProvider(goalId);
          final provider2 = goalContributionsProvider(goalId);

          // Same parameters should create equivalent providers
          expect(provider1.goalId, equals(provider2.goalId));
          expect(provider1, equals(provider2));
        },
      );

      test('activeGoalContributionsProvider family should work', () {
        const family = activeGoalContributionsProvider;
        const goalId = 'test-goal-id';

        final provider = family(goalId);
        expect(provider.goalId, equals(goalId));
      });

      test('goalContributionProvider family should work', () {
        const family = goalContributionProvider;
        const goalId = 'test-goal-id';
        const contributionId = 'test-contribution-id';

        final provider = family(goalId, contributionId);
        expect(provider.goalId, equals(goalId));
        expect(provider.contributionId, equals(contributionId));
      });

      test('watchGoalContributionsProvider family should work', () {
        const family = watchGoalContributionsProvider;
        const goalId = 'test-goal-id';

        final provider = family(goalId);
        expect(provider.goalId, equals(goalId));
      });

      test('watchActiveGoalContributionsProvider family should work', () {
        const family = watchActiveGoalContributionsProvider;
        const goalId = 'test-goal-id';

        final provider = family(goalId);
        expect(provider.goalId, equals(goalId));
      });
    });

    group('Provider Dependencies and Injection', () {
      test(
        'goalContributionsProvider should inject repository correctly',
        () async {
          // Arrange
          final mockContributions = [_createTestContribution()];
          when(
            () => mockRepository.getContributionsForGoal(testGoalId),
          ).thenAnswer((_) async => mockContributions);

          // Act
          final result = await container.read(
            goalContributionsProvider(testGoalId).future,
          );

          // Assert
          expect(result, equals(mockContributions));
          verify(
            () => mockRepository.getContributionsForGoal(testGoalId),
          ).called(1);
        },
      );

      test(
        'activeGoalContributionsProvider should inject repository correctly',
        () async {
          // Arrange
          final mockContributions = [_createTestContribution(isActive: true)];
          when(
            () => mockRepository.getActiveContributionsForGoal(testGoalId),
          ).thenAnswer((_) async => mockContributions);

          // Act
          final result = await container.read(
            activeGoalContributionsProvider(testGoalId).future,
          );

          // Assert
          expect(result, equals(mockContributions));
          verify(
            () => mockRepository.getActiveContributionsForGoal(testGoalId),
          ).called(1);
        },
      );

      test(
        'goalContributionProvider should inject repository correctly',
        () async {
          // Arrange
          final mockContribution = _createTestContribution();
          when(
            () => mockRepository.getContributionById(
              testGoalId,
              testContributionId,
            ),
          ).thenAnswer((_) async => mockContribution);

          // Act
          final result = await container.read(
            goalContributionProvider(testGoalId, testContributionId).future,
          );

          // Assert
          expect(result, equals(mockContribution));
          verify(
            () => mockRepository.getContributionById(
              testGoalId,
              testContributionId,
            ),
          ).called(1);
        },
      );
    });

    group('State Transitions and Caching', () {
      test('Provider families should cache instances for same parameters', () {
        const goalId = 'test-goal-id';

        // Act - create providers with same parameters
        final provider1 = goalContributionsProvider(goalId);
        final provider2 = goalContributionsProvider(goalId);

        // Assert - should have equivalent behavior
        expect(provider1.goalId, equals(provider2.goalId));
        expect(provider1, equals(provider2));
      });

      test('Provider should maintain state across reads', () async {
        // Arrange
        final mockContributions = [_createTestContribution()];
        when(
          () => mockRepository.getContributionsForGoal(testGoalId),
        ).thenAnswer((_) async => mockContributions);

        // Act - read multiple times
        final result1 = await container.read(
          goalContributionsProvider(testGoalId).future,
        );
        final result2 = await container.read(
          goalContributionsProvider(testGoalId).future,
        );

        // Assert - should be same result due to caching
        expect(result1, equals(result2));
        expect(result1, equals(mockContributions));

        // Repository should only be called once due to caching
        verify(
          () => mockRepository.getContributionsForGoal(testGoalId),
        ).called(1);
      });
    });

    group('Error Handling in Generated Code', () {
      test(
        'goalContributionsProvider should handle repository errors gracefully',
        () async {
          // Arrange
          when(
            () => mockRepository.getContributionsForGoal(testGoalId),
          ).thenThrow(Exception('Repository error'));

          // Act & Assert
          expect(
            () => container.read(goalContributionsProvider(testGoalId).future),
            throwsA(isA<Exception>()),
          );

          verify(
            () => mockRepository.getContributionsForGoal(testGoalId),
          ).called(1);
        },
      );

      test(
        'activeGoalContributionsProvider should handle repository errors gracefully',
        () async {
          // Arrange
          when(
            () => mockRepository.getActiveContributionsForGoal(testGoalId),
          ).thenThrow(Exception('Repository error'));

          // Act & Assert
          expect(
            () => container.read(
              activeGoalContributionsProvider(testGoalId).future,
            ),
            throwsA(isA<Exception>()),
          );
        },
      );

      test(
        'goalContributionProvider should handle repository errors gracefully',
        () async {
          // Arrange
          when(
            () => mockRepository.getContributionById(
              testGoalId,
              testContributionId,
            ),
          ).thenThrow(Exception('Repository error'));

          // Act & Assert
          expect(
            () => container.read(
              goalContributionProvider(testGoalId, testContributionId).future,
            ),
            throwsA(isA<Exception>()),
          );
        },
      );

      test(
        'goalContributionProvider should handle null contribution gracefully',
        () async {
          // Arrange - contribution not found
          when(
            () => mockRepository.getContributionById(
              testGoalId,
              testContributionId,
            ),
          ).thenAnswer((_) async => null);

          // Act
          final result = await container.read(
            goalContributionProvider(testGoalId, testContributionId).future,
          );

          // Assert
          expect(result, isNull);
          verify(
            () => mockRepository.getContributionById(
              testGoalId,
              testContributionId,
            ),
          ).called(1);
        },
      );
    });

    group('Provider Override Mechanisms', () {
      test('goalContributionsProvider should support overrides', () async {
        // Arrange
        const goalId = 'test-goal-id';
        final mockContributions = [_createTestContribution()];

        final overriddenContainer = ProviderContainer(
          overrides: [
            goalContributionsProvider(
              goalId,
            ).overrideWith((ref) async => mockContributions),
          ],
        );
        addTearDown(overriddenContainer.dispose);

        // Act
        final result = await overriddenContainer.read(
          goalContributionsProvider(goalId).future,
        );

        // Assert
        expect(result, equals(mockContributions));
      });

      test(
        'activeGoalContributionsProvider should support overrides',
        () async {
          // Arrange
          const goalId = 'test-goal-id';
          final mockContributions = [_createTestContribution(isActive: true)];

          final overriddenContainer = ProviderContainer(
            overrides: [
              activeGoalContributionsProvider(
                goalId,
              ).overrideWith((ref) async => mockContributions),
            ],
          );
          addTearDown(overriddenContainer.dispose);

          // Act
          final result = await overriddenContainer.read(
            activeGoalContributionsProvider(goalId).future,
          );

          // Assert
          expect(result, equals(mockContributions));
        },
      );

      test('goalContributionProvider should support overrides', () async {
        // Arrange
        const goalId = 'test-goal-id';
        const contributionId = 'test-contribution-id';
        final mockContribution = _createTestContribution();

        final overriddenContainer = ProviderContainer(
          overrides: [
            goalContributionProvider(
              goalId,
              contributionId,
            ).overrideWith((ref) async => mockContribution),
          ],
        );
        addTearDown(overriddenContainer.dispose);

        // Act
        final result = await overriddenContainer.read(
          goalContributionProvider(goalId, contributionId).future,
        );

        // Assert
        expect(result, equals(mockContribution));
      });
    });

    group('Stream Provider Testing', () {
      test(
        'watchGoalContributionsProvider should handle stream data',
        () async {
          // Arrange
          final mockContributions = [_createTestContribution()];

          when(
            () => mockRepository.watchContributionsForGoal(testGoalId),
          ).thenAnswer((_) => Stream.value(mockContributions));

          // Act
          final result = await container.read(
            watchGoalContributionsProvider(testGoalId).future,
          );

          // Assert
          expect(result, equals(mockContributions));
          verify(
            () => mockRepository.watchContributionsForGoal(testGoalId),
          ).called(1);
        },
      );

      test(
        'watchActiveGoalContributionsProvider should handle stream data',
        () async {
          // Arrange
          final mockContributions = [_createTestContribution(isActive: true)];

          when(
            () => mockRepository.watchActiveContributionsForGoal(testGoalId),
          ).thenAnswer((_) => Stream.value(mockContributions));

          // Act
          final result = await container.read(
            watchActiveGoalContributionsProvider(testGoalId).future,
          );

          // Assert
          expect(result, equals(mockContributions));
          verify(
            () => mockRepository.watchActiveContributionsForGoal(testGoalId),
          ).called(1);
        },
      );

      test(
        'watchGoalContributionsProvider should handle stream errors',
        () async {
          // Arrange
          when(
            () => mockRepository.watchContributionsForGoal(testGoalId),
          ).thenAnswer((_) => Stream.error(Exception('Stream error')));

          // Act & Assert
          expect(
            () => container.read(
              watchGoalContributionsProvider(testGoalId).future,
            ),
            throwsA(isA<Exception>()),
          );

          verify(
            () => mockRepository.watchContributionsForGoal(testGoalId),
          ).called(1);
        },
      );
    });

    group('Generated Provider Element Creation', () {
      test('provider families should create elements correctly', () {
        // Arrange
        const goalId = 'test-goal-id';
        final provider = goalContributionsProvider(goalId);

        // Act
        final element = provider.createElement();

        // Assert
        expect(element, isNotNull);
        expect(element.provider, equals(provider));
      });

      test(
        'two-parameter provider families should create elements correctly',
        () {
          // Arrange
          const goalId = 'test-goal-id';
          const contributionId = 'test-contribution-id';
          final provider = goalContributionProvider(goalId, contributionId);

          // Act
          final element = provider.createElement();

          // Assert
          expect(element, isNotNull);
          expect(element.provider, equals(provider));
        },
      );

      test('stream provider families should create elements correctly', () {
        // Arrange
        const goalId = 'test-goal-id';
        final provider = watchGoalContributionsProvider(goalId);

        // Act
        final element = provider.createElement();

        // Assert
        expect(element, isNotNull);
        expect(element.provider, equals(provider));
      });
    });

    group('Hash Code and Equality Testing', () {
      test('providers with same parameters should be equal', () {
        // Arrange
        const goalId = 'test-goal-id';
        final provider1 = goalContributionsProvider(goalId);
        final provider2 = goalContributionsProvider(goalId);

        // Assert
        expect(provider1, equals(provider2));
        expect(provider1.hashCode, equals(provider2.hashCode));
      });

      test('providers with different parameters should not be equal', () {
        // Arrange
        const goalId1 = 'test-goal-id-1';
        const goalId2 = 'test-goal-id-2';
        final provider1 = goalContributionsProvider(goalId1);
        final provider2 = goalContributionsProvider(goalId2);

        // Assert
        expect(provider1, isNot(equals(provider2)));
        expect(provider1.hashCode, isNot(equals(provider2.hashCode)));
      });

      test('two-parameter providers should handle equality correctly', () {
        // Arrange
        const goalId = 'test-goal-id';
        const contributionId1 = 'test-contribution-id-1';
        const contributionId2 = 'test-contribution-id-2';

        final provider1 = goalContributionProvider(goalId, contributionId1);
        final provider2 = goalContributionProvider(goalId, contributionId1);
        final provider3 = goalContributionProvider(goalId, contributionId2);

        // Assert
        expect(provider1, equals(provider2));
        expect(provider1.hashCode, equals(provider2.hashCode));
        expect(provider1, isNot(equals(provider3)));
        expect(provider1.hashCode, isNot(equals(provider3.hashCode)));
      });
    });

    group('Provider Family Name and Dependencies', () {
      test('provider families should have correct names', () {
        // Test GoalContributionsFamily
        const goalContributionsFamily = goalContributionsProvider;
        expect(
          goalContributionsFamily.name,
          equals('goalContributionsProvider'),
        );

        // Test that dependencies are properly configured
        expect(goalContributionsFamily.dependencies, isNull);
        expect(goalContributionsFamily.allTransitiveDependencies, isNull);
      });

      test('provider getProviderOverride should work correctly', () {
        // Arrange
        const goalId = 'test-goal-id';
        const family = goalContributionsProvider;
        final originalProvider = family(goalId);

        // Act
        final overrideProvider = family.getProviderOverride(originalProvider);

        // Assert
        expect(overrideProvider.goalId, equals(originalProvider.goalId));
        expect(overrideProvider, equals(originalProvider));
      });
    });

    group('Complex Provider Integration Testing', () {
      test('multiple providers should work independently', () async {
        // Arrange
        const goalId1 = 'goal-1';
        const goalId2 = 'goal-2';
        final contributions1 = [_createTestContribution(id: 'contrib-1')];
        final contributions2 = [_createTestContribution(id: 'contrib-2')];

        when(
          () => mockRepository.getContributionsForGoal(goalId1),
        ).thenAnswer((_) async => contributions1);

        when(
          () => mockRepository.getContributionsForGoal(goalId2),
        ).thenAnswer((_) async => contributions2);

        // Act
        final result1 = await container.read(
          goalContributionsProvider(goalId1).future,
        );
        final result2 = await container.read(
          goalContributionsProvider(goalId2).future,
        );

        // Assert
        expect(result1, equals(contributions1));
        expect(result2, equals(contributions2));
        expect(result1, isNot(equals(result2)));
      });

      test('providers should handle concurrent access correctly', () async {
        // Arrange
        final mockContributions = [_createTestContribution()];
        when(
          () => mockRepository.getContributionsForGoal(testGoalId),
        ).thenAnswer((_) async {
          await Future<void>.delayed(const Duration(milliseconds: 10));
          return mockContributions;
        });

        // Act - concurrent access
        final futures = List.generate(
          5,
          (_) => container.read(goalContributionsProvider(testGoalId).future),
        );
        final results = await Future.wait(futures);

        // Assert - all should return same result
        for (final result in results) {
          expect(result, equals(mockContributions));
        }

        // Repository should only be called once due to caching
        verify(
          () => mockRepository.getContributionsForGoal(testGoalId),
        ).called(1);
      });
    });

    group('Additional Provider Families - Statistics and Counts', () {
      test('goalContributionStatsProvider family should work correctly', () {
        const family = goalContributionStatsProvider;
        const goalId = 'test-goal-id';

        final provider = family(goalId);
        expect(provider.goalId, equals(goalId));
        expect(family.name, equals('goalContributionStatsProvider'));
      });

      test(
        'totalGoalContributionAmountProvider family should work correctly',
        () {
          const family = totalGoalContributionAmountProvider;
          const goalId = 'test-goal-id';

          final provider = family(goalId);
          expect(provider.goalId, equals(goalId));
          expect(family.name, equals('totalGoalContributionAmountProvider'));
        },
      );

      test('goalContributionCountProvider family should work correctly', () {
        const family = goalContributionCountProvider;
        const goalId = 'test-goal-id';

        final provider = family(goalId);
        expect(provider.goalId, equals(goalId));
        expect(family.name, equals('goalContributionCountProvider'));
      });

      test('recentGoalContributionsProvider family should work correctly', () {
        const family = recentGoalContributionsProvider;
        const goalId = 'test-goal-id';
        const limit = 10;

        final provider = family(goalId, limit: limit);
        expect(provider.goalId, equals(goalId));
        expect(provider.limit, equals(limit));
        expect(family.name, equals('recentGoalContributionsProvider'));
      });
    });

    group('Statistics Provider Testing', () {
      test(
        'goalContributionStatsProvider should inject repository correctly',
        () async {
          // Arrange
          final mockStats = {'total': 5000, 'count': 3, 'average': 1666};
          when(
            () => mockRepository.getContributionStatistics(testGoalId),
          ).thenAnswer((_) async => mockStats);

          // Act
          final result = await container.read(
            goalContributionStatsProvider(testGoalId).future,
          );

          // Assert
          expect(result, equals(mockStats));
          verify(
            () => mockRepository.getContributionStatistics(testGoalId),
          ).called(1);
        },
      );

      test(
        'goalContributionStatsProvider should handle errors gracefully',
        () async {
          // Arrange
          when(
            () => mockRepository.getContributionStatistics(testGoalId),
          ).thenThrow(Exception('Stats error'));

          // Act & Assert
          expect(
            () => container.read(
              goalContributionStatsProvider(testGoalId).future,
            ),
            throwsA(isA<Exception>()),
          );
        },
      );

      test('goalContributionStatsProvider should support overrides', () async {
        // Arrange
        const goalId = 'test-goal-id';
        final mockStats = {'total': 10000, 'count': 5};

        final overriddenContainer = ProviderContainer(
          overrides: [
            goalContributionStatsProvider(
              goalId,
            ).overrideWith((ref) async => mockStats),
          ],
        );
        addTearDown(overriddenContainer.dispose);

        // Act
        final result = await overriddenContainer.read(
          goalContributionStatsProvider(goalId).future,
        );

        // Assert
        expect(result, equals(mockStats));
      });
    });

    group('Total Amount Provider Testing', () {
      test(
        'totalGoalContributionAmountProvider should inject repository correctly',
        () async {
          // Arrange
          const mockTotal = 15000;
          when(
            () => mockRepository.getTotalContributionAmountForGoal(testGoalId),
          ).thenAnswer((_) async => mockTotal);

          // Act
          final result = await container.read(
            totalGoalContributionAmountProvider(testGoalId).future,
          );

          // Assert
          expect(result, equals(mockTotal));
          verify(
            () => mockRepository.getTotalContributionAmountForGoal(testGoalId),
          ).called(1);
        },
      );

      test(
        'totalGoalContributionAmountProvider should handle errors gracefully',
        () async {
          // Arrange
          when(
            () => mockRepository.getTotalContributionAmountForGoal(testGoalId),
          ).thenThrow(Exception('Total amount error'));

          // Act & Assert
          expect(
            () => container.read(
              totalGoalContributionAmountProvider(testGoalId).future,
            ),
            throwsA(isA<Exception>()),
          );
        },
      );

      test(
        'totalGoalContributionAmountProvider should support overrides',
        () async {
          // Arrange
          const goalId = 'test-goal-id';
          const mockTotal = 25000;

          final overriddenContainer = ProviderContainer(
            overrides: [
              totalGoalContributionAmountProvider(
                goalId,
              ).overrideWith((ref) async => mockTotal),
            ],
          );
          addTearDown(overriddenContainer.dispose);

          // Act
          final result = await overriddenContainer.read(
            totalGoalContributionAmountProvider(goalId).future,
          );

          // Assert
          expect(result, equals(mockTotal));
        },
      );
    });

    group('Count Provider Testing', () {
      test(
        'goalContributionCountProvider should inject repository correctly',
        () async {
          // Arrange
          const mockCount = 7;
          when(
            () => mockRepository.getContributionCountForGoal(testGoalId),
          ).thenAnswer((_) async => mockCount);

          // Act
          final result = await container.read(
            goalContributionCountProvider(testGoalId).future,
          );

          // Assert
          expect(result, equals(mockCount));
          verify(
            () => mockRepository.getContributionCountForGoal(testGoalId),
          ).called(1);
        },
      );

      test(
        'goalContributionCountProvider should handle errors gracefully',
        () async {
          // Arrange
          when(
            () => mockRepository.getContributionCountForGoal(testGoalId),
          ).thenThrow(Exception('Count error'));

          // Act & Assert
          expect(
            () => container.read(
              goalContributionCountProvider(testGoalId).future,
            ),
            throwsA(isA<Exception>()),
          );
        },
      );

      test('goalContributionCountProvider should support overrides', () async {
        // Arrange
        const goalId = 'test-goal-id';
        const mockCount = 12;

        final overriddenContainer = ProviderContainer(
          overrides: [
            goalContributionCountProvider(
              goalId,
            ).overrideWith((ref) async => mockCount),
          ],
        );
        addTearDown(overriddenContainer.dispose);

        // Act
        final result = await overriddenContainer.read(
          goalContributionCountProvider(goalId).future,
        );

        // Assert
        expect(result, equals(mockCount));
      });
    });

    group('Recent Contributions Provider Testing', () {
      test(
        'recentGoalContributionsProvider should inject repository correctly',
        () async {
          // Arrange
          final mockContributions = [
            _createTestContribution(id: 'recent-1'),
            _createTestContribution(id: 'recent-2'),
          ];
          const limit = 5;
          when(
            () =>
                mockRepository.getRecentContributions(testGoalId, limit: limit),
          ).thenAnswer((_) async => mockContributions);

          // Act
          final result = await container.read(
            recentGoalContributionsProvider(testGoalId, limit: limit).future,
          );

          // Assert
          expect(result, equals(mockContributions));
          verify(
            () =>
                mockRepository.getRecentContributions(testGoalId, limit: limit),
          ).called(1);
        },
      );

      test(
        'recentGoalContributionsProvider should handle default limit',
        () async {
          // Arrange
          final mockContributions = [_createTestContribution()];
          when(
            () => mockRepository.getRecentContributions(testGoalId, limit: 5),
          ).thenAnswer((_) async => mockContributions);

          // Act
          final result = await container.read(
            recentGoalContributionsProvider(testGoalId).future,
          );

          // Assert
          expect(result, equals(mockContributions));
          verify(
            () => mockRepository.getRecentContributions(testGoalId, limit: 5),
          ).called(1);
        },
      );

      test(
        'recentGoalContributionsProvider should handle errors gracefully',
        () async {
          // Arrange
          when(
            () => mockRepository.getRecentContributions(
              testGoalId,
              limit: any(named: 'limit'),
            ),
          ).thenThrow(Exception('Recent contributions error'));

          // Act & Assert
          expect(
            () => container.read(
              recentGoalContributionsProvider(testGoalId).future,
            ),
            throwsA(isA<Exception>()),
          );
        },
      );

      test(
        'recentGoalContributionsProvider should support overrides',
        () async {
          // Arrange
          const goalId = 'test-goal-id';
          const limit = 3;
          final mockContributions = [
            _createTestContribution(id: 'override-1'),
            _createTestContribution(id: 'override-2'),
            _createTestContribution(id: 'override-3'),
          ];

          final overriddenContainer = ProviderContainer(
            overrides: [
              recentGoalContributionsProvider(
                goalId,
                limit: limit,
              ).overrideWith((ref) async => mockContributions),
            ],
          );
          addTearDown(overriddenContainer.dispose);

          // Act
          final result = await overriddenContainer.read(
            recentGoalContributionsProvider(goalId, limit: limit).future,
          );

          // Assert
          expect(result, equals(mockContributions));
        },
      );

      test(
        'recentGoalContributionsProvider should handle different limits',
        () async {
          // Arrange
          const goalId = 'test-goal-id';
          const limit1 = 3;
          const limit2 = 10;
          final mockContributions1 = [_createTestContribution(id: 'limit-3-1')];
          final mockContributions2 = [
            _createTestContribution(id: 'limit-10-1'),
            _createTestContribution(id: 'limit-10-2'),
          ];

          when(
            () => mockRepository.getRecentContributions(goalId, limit: limit1),
          ).thenAnswer((_) async => mockContributions1);

          when(
            () => mockRepository.getRecentContributions(goalId, limit: limit2),
          ).thenAnswer((_) async => mockContributions2);

          // Act
          final result1 = await container.read(
            recentGoalContributionsProvider(goalId, limit: limit1).future,
          );
          final result2 = await container.read(
            recentGoalContributionsProvider(goalId, limit: limit2).future,
          );

          // Assert
          expect(result1, equals(mockContributions1));
          expect(result2, equals(mockContributions2));
          expect(result1, isNot(equals(result2)));
        },
      );
    });

    group('AsyncNotifier Provider Testing', () {
      group('GoalContributionCreation', () {
        test('should have initial data state', () {
          final state = container.read(goalContributionCreationProvider);

          expect(state, isA<AsyncData<void>>());
          expect(state.hasValue, isTrue);
        });

        test('should create contribution successfully', () async {
          // Arrange
          final contribution = _createTestContribution();
          const contributionId = 'new-contrib-123';
          when(
            () => mockRepository.createContribution(testGoalId, contribution),
          ).thenAnswer((_) async => contributionId);

          final notifier = container.read(
            goalContributionCreationProvider.notifier,
          );

          // Act
          await notifier.createContribution(testGoalId, contribution);

          // Assert
          final state = container.read(goalContributionCreationProvider);
          expect(state, isA<AsyncData<void>>());
          verify(
            () => mockRepository.createContribution(testGoalId, contribution),
          ).called(1);
        });

        test('should handle creation errors', () async {
          // Arrange
          final contribution = _createTestContribution();
          final error = Exception('Creation failed');
          when(
            () => mockRepository.createContribution(testGoalId, contribution),
          ).thenThrow(error);

          final notifier = container.read(
            goalContributionCreationProvider.notifier,
          );

          // Act
          await notifier.createContribution(testGoalId, contribution);

          // Assert
          final state = container.read(goalContributionCreationProvider);
          expect(state, isA<AsyncError<void>>());
          expect(state.error, equals(error));
        });

        test('should show loading state during creation', () async {
          // Arrange
          final contribution = _createTestContribution();
          final completer = Completer<String>();
          when(
            () => mockRepository.createContribution(testGoalId, contribution),
          ).thenAnswer((_) => completer.future);

          final notifier = container.read(
            goalContributionCreationProvider.notifier,
          );

          // Act
          final future = notifier.createContribution(testGoalId, contribution);

          // Assert - should be loading
          final loadingState = container.read(goalContributionCreationProvider);
          expect(loadingState, isA<AsyncLoading<void>>());

          // Complete the operation
          completer.complete('contrib-123');
          await future;

          // Assert - should be data
          final dataState = container.read(goalContributionCreationProvider);
          expect(dataState, isA<AsyncData<void>>());
        });

        test('should invalidate related providers after creation', () async {
          // Arrange
          final contribution = _createTestContribution();
          const contributionId = 'new-contrib-123';
          when(
            () => mockRepository.createContribution(testGoalId, contribution),
          ).thenAnswer((_) async => contributionId);

          // Pre-populate some providers to test invalidation
          when(
            () => mockRepository.getContributionsForGoal(testGoalId),
          ).thenAnswer((_) async => [contribution]);

          // Read providers to populate cache
          await container.read(goalContributionsProvider(testGoalId).future);

          final notifier = container.read(
            goalContributionCreationProvider.notifier,
          );

          // Act
          await notifier.createContribution(testGoalId, contribution);

          // Assert - providers should be invalidated (this is implicit in the implementation)
          verify(
            () => mockRepository.createContribution(testGoalId, contribution),
          ).called(1);
        });
      });

      group('GoalContributionUpdate', () {
        test('should have initial data state', () {
          final state = container.read(goalContributionUpdateProvider);

          expect(state, isA<AsyncData<void>>());
          expect(state.hasValue, isTrue);
        });

        test('should update contribution successfully', () async {
          // Arrange
          const contributionId = 'contrib-123';
          final contribution = _createTestContribution(id: contributionId);
          when(
            () => mockRepository.updateContribution(
              testGoalId,
              contributionId,
              contribution,
            ),
          ).thenAnswer((_) async {});

          final notifier = container.read(
            goalContributionUpdateProvider.notifier,
          );

          // Act
          await notifier.updateContribution(
            testGoalId,
            contributionId,
            contribution,
          );

          // Assert
          final state = container.read(goalContributionUpdateProvider);
          expect(state, isA<AsyncData<void>>());
          verify(
            () => mockRepository.updateContribution(
              testGoalId,
              contributionId,
              contribution,
            ),
          ).called(1);
        });

        test('should handle update errors', () async {
          // Arrange
          const contributionId = 'contrib-123';
          final contribution = _createTestContribution(id: contributionId);
          final error = Exception('Update failed');
          when(
            () => mockRepository.updateContribution(
              testGoalId,
              contributionId,
              contribution,
            ),
          ).thenThrow(error);

          final notifier = container.read(
            goalContributionUpdateProvider.notifier,
          );

          // Act
          await notifier.updateContribution(
            testGoalId,
            contributionId,
            contribution,
          );

          // Assert
          final state = container.read(goalContributionUpdateProvider);
          expect(state, isA<AsyncError<void>>());
          expect(state.error, equals(error));
        });

        test('should show loading state during update', () async {
          // Arrange
          const contributionId = 'contrib-123';
          final contribution = _createTestContribution(id: contributionId);
          final completer = Completer<void>();
          when(
            () => mockRepository.updateContribution(
              testGoalId,
              contributionId,
              contribution,
            ),
          ).thenAnswer((_) => completer.future);

          final notifier = container.read(
            goalContributionUpdateProvider.notifier,
          );

          // Act
          final future = notifier.updateContribution(
            testGoalId,
            contributionId,
            contribution,
          );

          // Assert - should be loading
          final loadingState = container.read(goalContributionUpdateProvider);
          expect(loadingState, isA<AsyncLoading<void>>());

          // Complete the operation
          completer.complete();
          await future;

          // Assert - should be data
          final dataState = container.read(goalContributionUpdateProvider);
          expect(dataState, isA<AsyncData<void>>());
        });
      });

      group('GoalContributionDeletion', () {
        test('should have initial data state', () {
          final state = container.read(goalContributionDeletionProvider);

          expect(state, isA<AsyncData<void>>());
          expect(state.hasValue, isTrue);
        });

        test('should delete contribution successfully', () async {
          // Arrange
          const contributionId = 'contrib-123';
          when(
            () => mockRepository.deleteContribution(testGoalId, contributionId),
          ).thenAnswer((_) async {});

          final notifier = container.read(
            goalContributionDeletionProvider.notifier,
          );

          // Act
          await notifier.deleteContribution(testGoalId, contributionId);

          // Assert
          final state = container.read(goalContributionDeletionProvider);
          expect(state, isA<AsyncData<void>>());
          verify(
            () => mockRepository.deleteContribution(testGoalId, contributionId),
          ).called(1);
        });

        test('should handle deletion errors', () async {
          // Arrange
          const contributionId = 'contrib-123';
          final error = Exception('Deletion failed');
          when(
            () => mockRepository.deleteContribution(testGoalId, contributionId),
          ).thenThrow(error);

          final notifier = container.read(
            goalContributionDeletionProvider.notifier,
          );

          // Act
          await notifier.deleteContribution(testGoalId, contributionId);

          // Assert
          final state = container.read(goalContributionDeletionProvider);
          expect(state, isA<AsyncError<void>>());
          expect(state.error, equals(error));
        });

        test('should show loading state during deletion', () async {
          // Arrange
          const contributionId = 'contrib-123';
          final completer = Completer<void>();
          when(
            () => mockRepository.deleteContribution(testGoalId, contributionId),
          ).thenAnswer((_) => completer.future);

          final notifier = container.read(
            goalContributionDeletionProvider.notifier,
          );

          // Act
          final future = notifier.deleteContribution(
            testGoalId,
            contributionId,
          );

          // Assert - should be loading
          final loadingState = container.read(goalContributionDeletionProvider);
          expect(loadingState, isA<AsyncLoading<void>>());

          // Complete the operation
          completer.complete();
          await future;

          // Assert - should be data
          final dataState = container.read(goalContributionDeletionProvider);
          expect(dataState, isA<AsyncData<void>>());
        });

        test('should invalidate related providers after deletion', () async {
          // Arrange
          const contributionId = 'contrib-123';
          when(
            () => mockRepository.deleteContribution(testGoalId, contributionId),
          ).thenAnswer((_) async {});

          // Pre-populate some providers to test invalidation
          final contribution = _createTestContribution(id: contributionId);
          when(
            () => mockRepository.getContributionsForGoal(testGoalId),
          ).thenAnswer((_) async => [contribution]);

          // Read providers to populate cache
          await container.read(goalContributionsProvider(testGoalId).future);

          final notifier = container.read(
            goalContributionDeletionProvider.notifier,
          );

          // Act
          await notifier.deleteContribution(testGoalId, contributionId);

          // Assert - providers should be invalidated (this is implicit in the implementation)
          verify(
            () => mockRepository.deleteContribution(testGoalId, contributionId),
          ).called(1);
        });
      });

      group('AsyncNotifier Provider Families', () {
        test('goalContributionCreationProvider should have correct name', () {
          expect(
            goalContributionCreationProvider.name,
            equals('goalContributionCreationProvider'),
          );
        });

        test('goalContributionUpdateProvider should have correct name', () {
          expect(
            goalContributionUpdateProvider.name,
            equals('goalContributionUpdateProvider'),
          );
        });

        test('goalContributionDeletionProvider should have correct name', () {
          expect(
            goalContributionDeletionProvider.name,
            equals('goalContributionDeletionProvider'),
          );
        });

        test('AsyncNotifier providers should support overrides', () async {
          // Arrange
          final overriddenContainer = ProviderContainer(
            overrides: [
              goalContributionCreationProvider.overrideWith(
                () => throw UnimplementedError('Override test'),
              ),
            ],
          );
          addTearDown(overriddenContainer.dispose);

          // Act & Assert
          expect(
            () => overriddenContainer.read(goalContributionCreationProvider),
            throwsA(isA<UnimplementedError>()),
          );
        });

        test('AsyncNotifier providers should be independent', () async {
          // Arrange
          final contribution = _createTestContribution();
          const contributionId = 'contrib-123';

          when(
            () => mockRepository.createContribution(testGoalId, contribution),
          ).thenAnswer((_) async => contributionId);

          when(
            () => mockRepository.updateContribution(
              testGoalId,
              contributionId,
              contribution,
            ),
          ).thenAnswer((_) async {});

          // Act
          final creationNotifier = container.read(
            goalContributionCreationProvider.notifier,
          );
          final updateNotifier = container.read(
            goalContributionUpdateProvider.notifier,
          );

          await creationNotifier.createContribution(testGoalId, contribution);
          await updateNotifier.updateContribution(
            testGoalId,
            contributionId,
            contribution,
          );

          // Assert - both should be in data state
          final creationState = container.read(
            goalContributionCreationProvider,
          );
          final updateState = container.read(goalContributionUpdateProvider);

          expect(creationState, isA<AsyncData<void>>());
          expect(updateState, isA<AsyncData<void>>());
        });
      });
    });
  });
}

// Mock classes and helper functions
class MockGoalContributionRepository extends Mock
    implements IGoalContributionRepository {}

GoalContribution _createTestContribution({
  String? id,
  String? goalId,
  int? amountCents,
  bool? isActive,
}) {
  final now = DateTime.now();
  return GoalContribution.create(
    userId: 'test-user-123',
    goalId: goalId ?? 'test-goal-123',
    amountCents: amountCents ?? 5000,
    contributionDate: now,
    description: 'Test contribution',
  ).copyWith(id: id ?? 'test-contribution-123', isActive: isActive ?? true);
}
