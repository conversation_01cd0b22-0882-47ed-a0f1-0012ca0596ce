import 'package:budapp/data/models/budget.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Budget Period Management Tests', () {
    test('Budget model should have periodStart field', () {
      final now = DateTime.now();
      final periodStart = DateTime(now.year, now.month, 1);

      final budget = Budget.create(
        userId: 'test-user',
        type: BudgetType.expense,
        plannedAmountCents: 50000, // $500

        period: BudgetPeriod.monthly,
        periodStart: periodStart,
        categoryId: 'test-category',
      );

      expect(budget.periodStart, equals(periodStart));
      expect(budget.schemaVersion, equals(1));
    });

    test('Budget should calculate correct period end', () {
      // Monthly budget
      final monthlyStart = DateTime(2024, 3, 1);
      final monthlyBudget = Budget.create(
        userId: 'test-user',
        type: BudgetType.expense,
        plannedAmountCents: 50000,

        period: BudgetPeriod.monthly,
        periodStart: monthlyStart,
      );

      final expectedMonthlyEnd = DateTime(2024, 3, 31);
      expect(monthlyBudget.periodEnd, equals(expectedMonthlyEnd));

      // Yearly budget
      final yearlyStart = DateTime(2024, 1, 1);
      final yearlyBudget = Budget.create(
        userId: 'test-user',
        type: BudgetType.expense,
        plannedAmountCents: 600000,

        period: BudgetPeriod.yearly,
        periodStart: yearlyStart,
      );

      final expectedYearlyEnd = DateTime(2024, 12, 31);
      expect(yearlyBudget.periodEnd, equals(expectedYearlyEnd));
    });

    test('Budget should correctly identify if it belongs to a period', () {
      final periodStart = DateTime(2024, 3, 1);
      final budget = Budget.create(
        userId: 'test-user',
        type: BudgetType.expense,
        plannedAmountCents: 50000,

        period: BudgetPeriod.monthly,
        periodStart: periodStart,
      );

      // Same period
      expect(budget.isForPeriod(DateTime(2024, 3, 15)), isTrue);
      expect(budget.isForPeriod(DateTime(2024, 3, 1)), isTrue);
      expect(budget.isForPeriod(DateTime(2024, 3, 31)), isTrue);

      // Different periods
      expect(budget.isForPeriod(DateTime(2024, 2, 15)), isFalse);
      expect(budget.isForPeriod(DateTime(2024, 4, 1)), isFalse);
      expect(budget.isForPeriod(DateTime(2025, 3, 1)), isFalse);
    });

    test('Budget should generate correct period key', () {
      // Monthly budget
      final monthlyBudget = Budget.create(
        userId: 'test-user',
        type: BudgetType.expense,
        plannedAmountCents: 50000,

        period: BudgetPeriod.monthly,
        periodStart: DateTime(2024, 3, 1),
      );

      expect(monthlyBudget.periodKey, equals('2024-03'));

      // Yearly budget
      final yearlyBudget = Budget.create(
        userId: 'test-user',
        type: BudgetType.expense,
        plannedAmountCents: 600000,

        period: BudgetPeriod.yearly,
        periodStart: DateTime(2024, 1, 1),
      );

      expect(yearlyBudget.periodKey, equals('2024'));
    });

    test('Budget JSON serialization should include periodStart', () {
      final periodStart = DateTime(2024, 3, 1);
      final budget = Budget.create(
        userId: 'test-user',
        type: BudgetType.expense,
        plannedAmountCents: 50000,

        period: BudgetPeriod.monthly,
        periodStart: periodStart,
      );

      final json = budget.toJson();
      expect(json['periodStart'], isNotNull);
      expect(json['schemaVersion'], equals(1));

      // Test deserialization
      final deserializedBudget = Budget.fromJson(json);
      expect(deserializedBudget.periodStart, equals(periodStart));
      expect(deserializedBudget.schemaVersion, equals(1));
    });

    test(
      'Budget uniqueness should be based on category, period type, and period start',
      () {
        final periodStart1 = DateTime(2024, 3, 1);
        final periodStart2 = DateTime(2024, 4, 1);

        final budget1 = Budget.create(
          userId: 'test-user',
          type: BudgetType.expense,
          plannedAmountCents: 50000,

          period: BudgetPeriod.monthly,
          periodStart: periodStart1,
          categoryId: 'category-1',
        );

        final budget2 = Budget.create(
          userId: 'test-user',
          type: BudgetType.expense,
          plannedAmountCents: 60000,

          period: BudgetPeriod.monthly,
          periodStart: periodStart2,
          categoryId: 'category-1',
        );

        // Same category, same period type, but different period start - should be allowed
        expect(budget1.categoryId, equals(budget2.categoryId));
        expect(budget1.period, equals(budget2.period));
        expect(budget1.periodStart, isNot(equals(budget2.periodStart)));
        expect(budget1.periodKey, isNot(equals(budget2.periodKey)));
      },
    );
  });
}
