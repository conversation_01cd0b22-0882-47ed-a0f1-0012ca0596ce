import 'dart:async';

import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/models/budget_progress.dart';
import 'package:budapp/data/repositories/interfaces/repositories.dart';
import 'package:budapp/features/budgets/providers/budget_providers.dart';
import 'package:budapp/features/budgets/services/budget_copy_service.dart';
import 'package:budapp/features/budgets/services/budget_progress_service.dart';
import 'package:budapp/features/budgets/services/bulk_budget_service.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/mock_data_factory.dart';
import '../../../helpers/mock_providers.dart';

void main() {
  group('Budget Providers Simple Tests', () {
    late ProviderContainer container;
    late MockBudgetRepository mockBudgetRepository;
    late MockBudgetProgressService mockBudgetProgressService;

    setUp(() {
      mockBudgetRepository = MockBudgetRepository();
      mockBudgetProgressService = MockBudgetProgressService();

      container = ProviderContainer(
        overrides: [
          ...MockProviders.authenticatedUserOverrides(),
          budgetRepositoryProvider.overrideWithValue(mockBudgetRepository),
          budgetProgressServiceProvider.overrideWithValue(
            mockBudgetProgressService,
          ),
        ],
      );

      // Register fallback values
      registerFallbackValue(DateTime.now());
      registerFallbackValue(BudgetPeriod.monthly);
      registerFallbackValue(BudgetType.expense);
      registerFallbackValue(MockDataFactory.createBudget());
    });

    tearDown(() {
      container.dispose();
    });

    group('budgetsProvider', () {
      test('should return stream of all budgets', () async {
        // Arrange
        final mockBudgets = [
          MockDataFactory.createBudget(id: 'budget-1'),
          MockDataFactory.createBudget(id: 'budget-2'),
        ];
        when(
          () => mockBudgetRepository.watchBudgets(),
        ).thenAnswer((_) => Stream.value(mockBudgets));

        // Act
        final budgets = await container.read(budgetsProvider.future);

        // Assert
        expect(budgets, equals(mockBudgets));
        verify(() => mockBudgetRepository.watchBudgets()).called(1);
      });

      test('should handle empty budget list', () async {
        // Arrange
        when(
          () => mockBudgetRepository.watchBudgets(),
        ).thenAnswer((_) => Stream.value([]));

        // Act
        final budgets = await container.read(budgetsProvider.future);

        // Assert
        expect(budgets, isEmpty);
      });

      test('should handle stream errors', () async {
        // Arrange
        when(
          () => mockBudgetRepository.watchBudgets(),
        ).thenAnswer((_) => Stream.error(Exception('Database error')));

        // Act & Assert
        expect(
          () => container.read(budgetsProvider.future),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('budgetsByMonthProvider', () {
      test('should return budgets for specific month', () async {
        // Arrange
        final testMonth = DateTime(2024, 3, 1);
        final mockBudgets = [MockDataFactory.createBudget()];
        when(
          () => mockBudgetRepository.watchBudgetsByMonth(testMonth),
        ).thenAnswer((_) => Stream.value(mockBudgets));

        // Act
        final budgets = await container.read(
          budgetsByMonthProvider(testMonth).future,
        );

        // Assert
        expect(budgets, equals(mockBudgets));
        verify(
          () => mockBudgetRepository.watchBudgetsByMonth(testMonth),
        ).called(1);
      });

      test('should handle different months', () async {
        // Arrange
        final month1 = DateTime(2024, 1, 1);
        final month2 = DateTime(2024, 2, 1);
        final budgets1 = [MockDataFactory.createBudget(id: 'budget-1')];
        final budgets2 = [MockDataFactory.createBudget(id: 'budget-2')];

        when(
          () => mockBudgetRepository.watchBudgetsByMonth(month1),
        ).thenAnswer((_) => Stream.value(budgets1));
        when(
          () => mockBudgetRepository.watchBudgetsByMonth(month2),
        ).thenAnswer((_) => Stream.value(budgets2));

        // Act
        final result1 = await container.read(
          budgetsByMonthProvider(month1).future,
        );
        final result2 = await container.read(
          budgetsByMonthProvider(month2).future,
        );

        // Assert
        expect(result1, equals(budgets1));
        expect(result2, equals(budgets2));
      });
    });

    group('budgetProvider', () {
      test('should return specific budget by ID', () async {
        // Arrange
        const budgetId = 'budget-123';
        final budget = MockDataFactory.createBudget(id: budgetId);
        when(
          () => mockBudgetRepository.getBudgetById(budgetId),
        ).thenAnswer((_) async => budget);

        // Act
        final result = await container.read(budgetProvider(budgetId).future);

        // Assert
        expect(result, equals(budget));
        verify(() => mockBudgetRepository.getBudgetById(budgetId)).called(1);
      });

      test('should return null for non-existent budget', () async {
        // Arrange
        const budgetId = 'non-existent';
        when(
          () => mockBudgetRepository.getBudgetById(budgetId),
        ).thenAnswer((_) async => null);

        // Act
        final result = await container.read(budgetProvider(budgetId).future);

        // Assert
        expect(result, isNull);
      });

      test('should handle repository errors', () async {
        // Arrange
        const budgetId = 'error-budget';
        when(
          () => mockBudgetRepository.getBudgetById(budgetId),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => container.read(budgetProvider(budgetId).future),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('budgetProgressProvider', () {
      test('should calculate budget progress correctly', () async {
        // Arrange
        const budgetId = 'budget-123';
        final testMonth = DateTime(2024, 3, 1);
        final budget = MockDataFactory.createBudget(id: budgetId);
        const progress = BudgetProgress(
          budgetId: budgetId,
          spent: 500,
          remaining: 500,
          percentage: 50,
          statusColor: Color(0xFF4CAF50),
          isOverBudget: false,
          status: BudgetProgressStatus.onTrack,
        );

        when(
          () => mockBudgetRepository.getBudgetById(budgetId),
        ).thenAnswer((_) async => budget);
        when(
          () => mockBudgetProgressService.calculateBudgetProgress(
            budget,
            testMonth,
          ),
        ).thenAnswer((_) async => progress);

        // Act
        final result = await container.read(
          budgetProgressProvider(budgetId, testMonth).future,
        );

        // Assert
        expect(result, equals(progress));
        verify(
          () => mockBudgetProgressService.calculateBudgetProgress(
            budget,
            testMonth,
          ),
        ).called(1);
      });

      test('should throw exception when budget not found', () async {
        // Arrange
        const budgetId = 'non-existent';
        final testMonth = DateTime(2024, 3, 1);

        when(
          () => mockBudgetRepository.getBudgetById(budgetId),
        ).thenAnswer((_) async => null);

        // Act & Assert
        expect(
          () => container.read(
            budgetProgressProvider(budgetId, testMonth).future,
          ),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle progress calculation errors', () async {
        // Arrange
        const budgetId = 'budget-123';
        final testMonth = DateTime(2024, 3, 1);
        final budget = MockDataFactory.createBudget(id: budgetId);

        when(
          () => mockBudgetRepository.getBudgetById(budgetId),
        ).thenAnswer((_) async => budget);
        when(
          () => mockBudgetProgressService.calculateBudgetProgress(
            budget,
            testMonth,
          ),
        ).thenThrow(Exception('Calculation error'));

        // Act & Assert
        expect(
          () => container.read(
            budgetProgressProvider(budgetId, testMonth).future,
          ),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('totalSpendingProvider', () {
      test('should return total spending for month', () async {
        // Arrange
        final testMonth = DateTime(2024, 3, 1);
        const expectedTotal = 500.0;

        when(
          () =>
              mockBudgetProgressService.getTotalSpending('test-uid', testMonth),
        ).thenAnswer((_) async => expectedTotal);

        // Act
        final result = await container.read(
          totalSpendingProvider(testMonth).future,
        );

        // Assert
        expect(result, equals(expectedTotal));
        verify(
          () =>
              mockBudgetProgressService.getTotalSpending('test-uid', testMonth),
        ).called(1);
      });

      test('should throw exception when user not authenticated', () async {
        // Arrange
        final unauthenticatedContainer = ProviderContainer(
          overrides: MockProviders.unauthenticatedUserOverrides,
        );

        final testMonth = DateTime(2024, 3, 1);

        // Act & Assert
        expect(
          () => unauthenticatedContainer.read(
            totalSpendingProvider(testMonth).future,
          ),
          throwsA(isA<Exception>()),
        );

        unauthenticatedContainer.dispose();
      });

      test('should handle service errors', () async {
        // Arrange
        final testMonth = DateTime(2024, 3, 1);

        when(
          () =>
              mockBudgetProgressService.getTotalSpending('test-uid', testMonth),
        ).thenThrow(Exception('Service error'));

        // Act & Assert
        expect(
          () => container.read(totalSpendingProvider(testMonth).future),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('spendingBreakdownProvider', () {
      test('should return spending breakdown for categories', () async {
        // Arrange
        final categoryIds = ['cat-1', 'cat-2'];
        final testMonth = DateTime(2024, 3, 1);
        final expectedBreakdown = {'cat-1': 100.0, 'cat-2': 200.0};

        when(
          () => mockBudgetProgressService.getSpendingBreakdown(
            'test-uid',
            categoryIds,
            testMonth,
          ),
        ).thenAnswer((_) async => expectedBreakdown);

        // Act
        final result = await container.read(
          spendingBreakdownProvider(categoryIds, testMonth).future,
        );

        // Assert
        expect(result, equals(expectedBreakdown));
        verify(
          () => mockBudgetProgressService.getSpendingBreakdown(
            'test-uid',
            categoryIds,
            testMonth,
          ),
        ).called(1);
      });

      test('should handle empty category list', () async {
        // Arrange
        final categoryIds = <String>[];
        final testMonth = DateTime(2024, 3, 1);
        final expectedBreakdown = <String, double>{};

        when(
          () => mockBudgetProgressService.getSpendingBreakdown(
            'test-uid',
            categoryIds,
            testMonth,
          ),
        ).thenAnswer((_) async => expectedBreakdown);

        // Act
        final result = await container.read(
          spendingBreakdownProvider(categoryIds, testMonth).future,
        );

        // Assert
        expect(result, equals(expectedBreakdown));
      });
    });

    group('spendingTrendProvider', () {
      test('should return spending trend over multiple months', () async {
        // Arrange
        const categoryId = 'cat-1';
        final months = [DateTime(2024, 1, 1), DateTime(2024, 2, 1)];
        final expectedTrend = {
          DateTime(2024, 1, 1): 100.0,
          DateTime(2024, 2, 1): 150.0,
        };

        when(
          () => mockBudgetProgressService.getSpendingTrend(
            'test-uid',
            categoryId,
            months,
          ),
        ).thenAnswer((_) async => expectedTrend);

        // Act
        final result = await container.read(
          spendingTrendProvider(categoryId, months).future,
        );

        // Assert
        expect(result, equals(expectedTrend));
        verify(
          () => mockBudgetProgressService.getSpendingTrend(
            'test-uid',
            categoryId,
            months,
          ),
        ).called(1);
      });

      test('should handle null category ID', () async {
        // Arrange
        const String? categoryId = null;
        final months = [DateTime(2024, 1, 1)];
        final expectedTrend = {DateTime(2024, 1, 1): 200.0};

        when(
          () => mockBudgetProgressService.getSpendingTrend(
            'test-uid',
            categoryId,
            months,
          ),
        ).thenAnswer((_) async => expectedTrend);

        // Act
        final result = await container.read(
          spendingTrendProvider(categoryId, months).future,
        );

        // Assert
        expect(result, equals(expectedTrend));
      });
    });

    group('BudgetsController', () {
      test('should update budget successfully', () async {
        // Arrange
        final budget = MockDataFactory.createBudget();
        when(
          () => mockBudgetRepository.updateBudget(budget),
        ).thenAnswer((_) async => budget);

        final controller = container.read(budgetsControllerProvider.notifier);

        // Act
        await controller.updateBudget(budget);

        // Assert
        final state = container.read(budgetsControllerProvider);
        expect(state.hasValue, isTrue);
        verify(() => mockBudgetRepository.updateBudget(budget)).called(1);
      });

      test('should delete budget successfully', () async {
        // Arrange
        const budgetId = 'budget-123';
        when(
          () => mockBudgetRepository.deleteBudget(budgetId),
        ).thenAnswer((_) async {});

        final controller = container.read(budgetsControllerProvider.notifier);

        // Act
        await controller.deleteBudget(budgetId);

        // Assert
        final state = container.read(budgetsControllerProvider);
        expect(state.hasValue, isTrue);
        verify(() => mockBudgetRepository.deleteBudget(budgetId)).called(1);
      });

      test('should handle update budget error', () async {
        // Arrange
        final budget = MockDataFactory.createBudget();
        final error = Exception('Update failed');
        when(() => mockBudgetRepository.updateBudget(budget)).thenThrow(error);

        final controller = container.read(budgetsControllerProvider.notifier);

        // Act
        await controller.updateBudget(budget);

        // Assert
        final state = container.read(budgetsControllerProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(error));
      });

      test('should handle delete budget error', () async {
        // Arrange
        const budgetId = 'budget-123';
        final error = Exception('Delete failed');
        when(
          () => mockBudgetRepository.deleteBudget(budgetId),
        ).thenThrow(error);

        final controller = container.read(budgetsControllerProvider.notifier);

        // Act
        await controller.deleteBudget(budgetId);

        // Assert
        final state = container.read(budgetsControllerProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(error));
      });

      test('should track loading state during operations', () async {
        // Arrange
        final budget = MockDataFactory.createBudget();
        final completer = Completer<Budget>();
        when(
          () => mockBudgetRepository.updateBudget(budget),
        ).thenAnswer((_) => completer.future);

        final controller = container.read(budgetsControllerProvider.notifier);

        // Act
        final updateFuture = controller.updateBudget(budget);

        // Assert loading state
        final loadingState = container.read(budgetsControllerProvider);
        expect(loadingState.isLoading, isTrue);

        // Complete the operation
        completer.complete(budget);
        await updateFuture;

        // Assert final state
        final finalState = container.read(budgetsControllerProvider);
        expect(finalState.hasValue, isTrue);
      });
    });

    group('currentBudgetPeriodProvider', () {
      test('should return monthly budget period', () {
        // Act
        final result = container.read(currentBudgetPeriodProvider);

        // Assert
        expect(result, equals(BudgetPeriod.monthly));
      });
    });

    group('templateBudgetsForFuturePeriod', () {
      test('should return template budgets from most recent period', () async {
        // Arrange
        final futurePeriod = DateTime(2024, 3);
        final existingBudgets = [
          MockDataFactory.createBudget(
            id: 'budget-1',
            plannedAmountCents: 100000,
            currentAmountCents: 50000,
          ),
          MockDataFactory.createBudget(
            id: 'budget-2',
            plannedAmountCents: 200000,
            currentAmountCents: 75000,
          ),
        ];

        // Mock the repository to return budgets for the current month
        when(
          () => mockBudgetRepository.getBudgetsByPeriod(
            any(),
            BudgetPeriod.monthly,
          ),
        ).thenAnswer((invocation) async {
          final period = invocation.positionalArguments[0] as DateTime;
          // Return budgets for the current month (first call)
          if (period.year == DateTime.now().year &&
              period.month == DateTime.now().month) {
            return existingBudgets;
          }
          return [];
        });

        // Act
        final templates = await container.read(
          templateBudgetsForFuturePeriodProvider(futurePeriod).future,
        );

        // Assert
        expect(templates.length, equals(2));
        expect(templates[0].id, isEmpty); // Template budgets have no ID
        expect(
          templates[0].currentAmountCents,
          equals(0),
        ); // Reset current amount
        expect(
          templates[0].plannedAmountCents,
          equals(100000),
        ); // Keep planned amount
        expect(templates[1].plannedAmountCents, equals(200000));
      });

      test('should return empty list when no budgets found', () async {
        // Arrange
        final futurePeriod = DateTime(2024, 3);
        when(
          () => mockBudgetRepository.getBudgetsByPeriod(any(), any()),
        ).thenAnswer((_) async => []);

        // Act
        final templates = await container.read(
          templateBudgetsForFuturePeriodProvider(futurePeriod).future,
        );

        // Assert
        expect(templates, isEmpty);
      });
    });

    group('totalBudgetByMonthAndType', () {
      test('should return total budget for specific type', () async {
        // Arrange
        final month = DateTime(2024, 2);
        final totalExpenseBudget = MockDataFactory.createBudget(
          id: 'total-expense',
          type: BudgetType.expense,
          categoryId: null, // Total budget has no category
        );
        final totalIncomeBudget = MockDataFactory.createBudget(
          id: 'total-income',
          type: BudgetType.income,
          categoryId: null,
        );
        final categoryBudget = MockDataFactory.createBudget(
          id: 'category-budget',
          type: BudgetType.expense,
          categoryId: 'category-1',
        );

        when(() => mockBudgetRepository.watchBudgetsByMonth(month)).thenAnswer(
          (_) => Stream.value([
            totalExpenseBudget,
            totalIncomeBudget,
            categoryBudget,
          ]),
        );

        // Act
        final result = await container.read(
          totalBudgetByMonthAndTypeProvider(month, BudgetType.expense).future,
        );

        // Assert
        expect(result, equals(totalExpenseBudget));
      });

      test('should return null when no total budget found', () async {
        // Arrange
        final month = DateTime(2024, 2);
        final categoryBudget = MockDataFactory.createBudget(
          id: 'category-budget',
          type: BudgetType.expense,
          categoryId: 'category-1', // Not a total budget
        );

        when(
          () => mockBudgetRepository.watchBudgetsByMonth(month),
        ).thenAnswer((_) => Stream.value([categoryBudget]));

        // Act
        final result = await container.read(
          totalBudgetByMonthAndTypeProvider(month, BudgetType.expense).future,
        );

        // Assert
        expect(result, isNull);
      });
    });

    group('totalBudgetsByMonth', () {
      test('should return all total budgets for month', () async {
        // Arrange
        final month = DateTime(2024, 2);
        final totalExpenseBudget = MockDataFactory.createBudget(
          id: 'total-expense',
          type: BudgetType.expense,
          categoryId: null,
        );
        final totalIncomeBudget = MockDataFactory.createBudget(
          id: 'total-income',
          type: BudgetType.income,
          categoryId: null,
        );
        final categoryBudget = MockDataFactory.createBudget(
          id: 'category-budget',
          categoryId: 'category-1',
        );

        when(() => mockBudgetRepository.watchBudgetsByMonth(month)).thenAnswer(
          (_) => Stream.value([
            totalExpenseBudget,
            totalIncomeBudget,
            categoryBudget,
          ]),
        );

        // Act
        final result = await container.read(
          totalBudgetsByMonthProvider(month).future,
        );

        // Assert
        expect(result.length, equals(2));
        expect(result, contains(totalExpenseBudget));
        expect(result, contains(totalIncomeBudget));
        expect(result, isNot(contains(categoryBudget)));
      });
    });

    group('budgetCopyService', () {
      test('should provide BudgetCopyService instance', () {
        // Act
        final service = container.read(budgetCopyServiceProvider);

        // Assert
        expect(service, isA<BudgetCopyService>());
      });
    });

    group('bulkBudgetService', () {
      test('should provide BulkBudgetService instance', () {
        // Act
        final service = container.read(bulkBudgetServiceProvider);

        // Assert
        expect(service, isA<BulkBudgetService>());
      });
    });

    group('currentTimePeriod', () {
      test('should return current time period as DateTime', () {
        // Act
        final timePeriod = container.read(currentTimePeriodProvider);

        // Assert
        expect(timePeriod, isA<DateTime>());
        expect(timePeriod.day, equals(1)); // Should be first day of month
      });
    });

    group('BudgetEdit', () {
      test('should initialize with budget data', () async {
        // Arrange
        const budgetId = 'budget-123';
        final budget = MockDataFactory.createBudget(id: budgetId);
        when(
          () => mockBudgetRepository.getBudgetById(budgetId),
        ).thenAnswer((_) async => budget);

        // Act
        final budgetEdit = container.read(
          budgetEditProvider(budgetId).notifier,
        );
        await container.read(budgetProvider(budgetId).future);

        // Assert
        expect(budgetEdit.budget, equals(budget));
      });

      test('should update amount correctly', () async {
        // Arrange
        const budgetId = 'budget-123';
        final budget = MockDataFactory.createBudget(
          id: budgetId,
          plannedAmountCents: 100000,
        );
        when(
          () => mockBudgetRepository.getBudgetById(budgetId),
        ).thenAnswer((_) async => budget);

        final budgetEdit = container.read(
          budgetEditProvider(budgetId).notifier,
        );
        await container.read(budgetProvider(budgetId).future);

        // Act
        budgetEdit.updateAmount(200000);

        // Assert
        expect(budgetEdit.budget?.plannedAmountCents, equals(200000));
      });

      test('should update type correctly', () async {
        // Arrange
        const budgetId = 'budget-123';
        final budget = MockDataFactory.createBudget(
          id: budgetId,
          type: BudgetType.expense,
        );
        when(
          () => mockBudgetRepository.getBudgetById(budgetId),
        ).thenAnswer((_) async => budget);

        final budgetEdit = container.read(
          budgetEditProvider(budgetId).notifier,
        );
        await container.read(budgetProvider(budgetId).future);

        // Act
        budgetEdit.updateType(BudgetType.income);

        // Assert
        expect(budgetEdit.budget?.type, equals(BudgetType.income));
      });

      test('should update period correctly', () async {
        // Arrange
        const budgetId = 'budget-123';
        final budget = MockDataFactory.createBudget(
          id: budgetId,
          period: BudgetPeriod.monthly,
        );
        when(
          () => mockBudgetRepository.getBudgetById(budgetId),
        ).thenAnswer((_) async => budget);

        final budgetEdit = container.read(
          budgetEditProvider(budgetId).notifier,
        );
        await container.read(budgetProvider(budgetId).future);

        // Act
        budgetEdit.updatePeriod(BudgetPeriod.yearly);

        // Assert
        expect(budgetEdit.budget?.period, equals(BudgetPeriod.yearly));
      });

      test('should update category correctly', () async {
        // Arrange
        const budgetId = 'budget-123';
        final budget = MockDataFactory.createBudget(
          id: budgetId,
          categoryId: 'old-category',
        );
        when(
          () => mockBudgetRepository.getBudgetById(budgetId),
        ).thenAnswer((_) async => budget);

        final budgetEdit = container.read(
          budgetEditProvider(budgetId).notifier,
        );
        await container.read(budgetProvider(budgetId).future);

        // Act
        budgetEdit.updateCategory('new-category');

        // Assert
        expect(budgetEdit.budget?.categoryId, equals('new-category'));
      });

      test('should update parent budget correctly', () async {
        // Arrange
        const budgetId = 'budget-123';
        final budget = MockDataFactory.createBudget(
          id: budgetId,
          parentBudgetId: 'old-parent',
        );
        when(
          () => mockBudgetRepository.getBudgetById(budgetId),
        ).thenAnswer((_) async => budget);

        final budgetEdit = container.read(
          budgetEditProvider(budgetId).notifier,
        );
        await container.read(budgetProvider(budgetId).future);

        // Act
        budgetEdit.updateParentBudget('new-parent');

        // Assert
        expect(budgetEdit.budget?.parentBudgetId, equals('new-parent'));
      });

      test('should save budget successfully', () async {
        // Arrange
        const budgetId = 'budget-123';
        final budget = MockDataFactory.createBudget(id: budgetId);
        when(
          () => mockBudgetRepository.getBudgetById(budgetId),
        ).thenAnswer((_) async => budget);
        when(
          () => mockBudgetRepository.updateBudget(any()),
        ).thenAnswer((_) async => budget);

        final budgetEdit = container.read(
          budgetEditProvider(budgetId).notifier,
        );
        await container.read(budgetProvider(budgetId).future);

        // Act
        await budgetEdit.saveBudget();

        // Assert
        verify(() => mockBudgetRepository.updateBudget(budget)).called(1);
      });

      test('should delete budget successfully', () async {
        // Arrange
        const budgetId = 'budget-123';
        final budget = MockDataFactory.createBudget(id: budgetId);
        when(
          () => mockBudgetRepository.getBudgetById(budgetId),
        ).thenAnswer((_) async => budget);
        when(
          () => mockBudgetRepository.deleteBudget(budgetId),
        ).thenAnswer((_) async {});

        final budgetEdit = container.read(
          budgetEditProvider(budgetId).notifier,
        );
        await container.read(budgetProvider(budgetId).future);

        // Act
        await budgetEdit.deleteBudget();

        // Assert
        verify(() => mockBudgetRepository.deleteBudget(budgetId)).called(1);
      });

      test('should handle null state gracefully in update methods', () {
        // Arrange
        const budgetId = 'budget-123';
        final budgetEdit = container.read(
          budgetEditProvider(budgetId).notifier,
        );

        // Act & Assert - should not throw
        budgetEdit.updateAmount(100000);
        budgetEdit.updateType(BudgetType.income);
        budgetEdit.updatePeriod(BudgetPeriod.yearly);
        budgetEdit.updateCategory('category-id');
        budgetEdit.updateParentBudget('parent-id');

        expect(budgetEdit.budget, isNull);
      });
    });

    group('BudgetCopyController', () {
      late MockBudgetCopyService mockBudgetCopyService;

      setUp(() {
        mockBudgetCopyService = MockBudgetCopyService();
        container = ProviderContainer(
          overrides: [
            ...MockProviders.authenticatedUserOverrides(),
            budgetRepositoryProvider.overrideWithValue(mockBudgetRepository),
            budgetProgressServiceProvider.overrideWithValue(
              mockBudgetProgressService,
            ),
            budgetCopyServiceProvider.overrideWithValue(mockBudgetCopyService),
          ],
        );
      });

      test('should copy budgets from previous period successfully', () async {
        // Arrange
        final sourcePeriod = DateTime(2024, 1);
        final targetPeriod = DateTime(2024, 2);
        const periodType = BudgetPeriod.monthly;
        final copyResult = BudgetCopyResult.success(
          copiedCount: 5,
          skippedCount: 0,
        );

        when(
          () => mockBudgetCopyService.copyBudgetsFromPreviousPeriod(
            sourcePeriod: sourcePeriod,
            targetPeriod: targetPeriod,
            periodType: periodType,
          ),
        ).thenAnswer((_) async => copyResult);

        final controller = container.read(
          budgetCopyControllerProvider.notifier,
        );

        // Act
        await controller.copyBudgetsFromPreviousPeriod(
          sourcePeriod: sourcePeriod,
          targetPeriod: targetPeriod,
          periodType: periodType,
        );

        // Assert
        final state = container.read(budgetCopyControllerProvider);
        expect(state.hasValue, isTrue);
        expect(state.value, equals(copyResult));
        verify(
          () => mockBudgetCopyService.copyBudgetsFromPreviousPeriod(
            sourcePeriod: sourcePeriod,
            targetPeriod: targetPeriod,
            periodType: periodType,
          ),
        ).called(1);
      });

      test('should handle copy budgets error', () async {
        // Arrange
        final sourcePeriod = DateTime(2024, 1);
        final targetPeriod = DateTime(2024, 2);
        const periodType = BudgetPeriod.monthly;
        final exception = Exception('Copy failed');

        when(
          () => mockBudgetCopyService.copyBudgetsFromPreviousPeriod(
            sourcePeriod: sourcePeriod,
            targetPeriod: targetPeriod,
            periodType: periodType,
          ),
        ).thenThrow(exception);

        final controller = container.read(
          budgetCopyControllerProvider.notifier,
        );

        // Act
        await controller.copyBudgetsFromPreviousPeriod(
          sourcePeriod: sourcePeriod,
          targetPeriod: targetPeriod,
          periodType: periodType,
        );

        // Assert
        final state = container.read(budgetCopyControllerProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(exception));
      });

      test('should get available source periods', () async {
        // Arrange
        final currentPeriod = DateTime(2024, 3);
        const periodType = BudgetPeriod.monthly;
        final availablePeriods = [DateTime(2024, 1), DateTime(2024, 2)];

        when(
          () => mockBudgetCopyService.getAvailableSourcePeriods(
            currentPeriod: currentPeriod,
            periodType: periodType,
          ),
        ).thenAnswer((_) async => availablePeriods);

        final controller = container.read(
          budgetCopyControllerProvider.notifier,
        );

        // Act
        final result = await controller.getAvailableSourcePeriods(
          currentPeriod: currentPeriod,
          periodType: periodType,
        );

        // Assert
        expect(result, equals(availablePeriods));
        verify(
          () => mockBudgetCopyService.getAvailableSourcePeriods(
            currentPeriod: currentPeriod,
            periodType: periodType,
          ),
        ).called(1);
      });

      test('should format period for display', () {
        // Arrange
        final period = DateTime(2024, 3);
        const periodType = BudgetPeriod.monthly;
        const formattedPeriod = 'March 2024';

        when(
          () => mockBudgetCopyService.formatPeriod(period, periodType),
        ).thenReturn(formattedPeriod);

        final controller = container.read(
          budgetCopyControllerProvider.notifier,
        );

        // Act
        final result = controller.formatPeriod(period, periodType);

        // Assert
        expect(result, equals(formattedPeriod));
        verify(
          () => mockBudgetCopyService.formatPeriod(period, periodType),
        ).called(1);
      });

      test('should clear result', () {
        // Arrange
        final controller = container.read(
          budgetCopyControllerProvider.notifier,
        );

        // Act
        controller.clearResult();

        // Assert
        final state = container.read(budgetCopyControllerProvider);
        expect(state.hasValue, isTrue);
        expect(state.value, isNull);
      });
    });

    group('BulkBudgetController', () {
      late MockBulkBudgetService mockBulkBudgetService;

      setUp(() {
        mockBulkBudgetService = MockBulkBudgetService();
        container = ProviderContainer(
          overrides: [
            ...MockProviders.authenticatedUserOverrides(),
            budgetRepositoryProvider.overrideWithValue(mockBudgetRepository),
            budgetProgressServiceProvider.overrideWithValue(
              mockBudgetProgressService,
            ),
            bulkBudgetServiceProvider.overrideWithValue(mockBulkBudgetService),
          ],
        );
      });

      test('should adjust budgets by percentage successfully', () async {
        // Arrange
        final budgetIds = ['budget-1', 'budget-2'];
        const percentageChange = 10.0;
        final operationResult = BulkOperationResult.success(processedCount: 2);

        when(
          () => mockBulkBudgetService.adjustBudgetsByPercentage(
            budgetIds: budgetIds,
            percentageChange: percentageChange,
          ),
        ).thenAnswer((_) async => operationResult);

        final controller = container.read(
          bulkBudgetControllerProvider.notifier,
        );

        // Act
        await controller.adjustBudgetsByPercentage(
          budgetIds: budgetIds,
          percentageChange: percentageChange,
        );

        // Assert
        final state = container.read(bulkBudgetControllerProvider);
        expect(state.hasValue, isTrue);
        expect(state.value, equals(operationResult));
        verify(
          () => mockBulkBudgetService.adjustBudgetsByPercentage(
            budgetIds: budgetIds,
            percentageChange: percentageChange,
          ),
        ).called(1);
      });

      test('should handle adjust budgets error', () async {
        // Arrange
        final budgetIds = ['budget-1', 'budget-2'];
        const percentageChange = 10.0;
        final exception = Exception('Adjustment failed');

        when(
          () => mockBulkBudgetService.adjustBudgetsByPercentage(
            budgetIds: budgetIds,
            percentageChange: percentageChange,
          ),
        ).thenThrow(exception);

        final controller = container.read(
          bulkBudgetControllerProvider.notifier,
        );

        // Act
        await controller.adjustBudgetsByPercentage(
          budgetIds: budgetIds,
          percentageChange: percentageChange,
        );

        // Assert
        final state = container.read(bulkBudgetControllerProvider);
        expect(state.hasError, isTrue);
        expect(state.error, equals(exception));
      });

      test('should change budget status successfully', () async {
        // Arrange
        final budgetIds = ['budget-1', 'budget-2'];
        const isActive = false;
        final operationResult = BulkOperationResult.success(processedCount: 2);

        when(
          () => mockBulkBudgetService.changeBudgetStatus(
            budgetIds: budgetIds,
            isActive: isActive,
          ),
        ).thenAnswer((_) async => operationResult);

        final controller = container.read(
          bulkBudgetControllerProvider.notifier,
        );

        // Act
        await controller.changeBudgetStatus(
          budgetIds: budgetIds,
          isActive: isActive,
        );

        // Assert
        final state = container.read(bulkBudgetControllerProvider);
        expect(state.hasValue, isTrue);
        expect(state.value, equals(operationResult));
        verify(
          () => mockBulkBudgetService.changeBudgetStatus(
            budgetIds: budgetIds,
            isActive: isActive,
          ),
        ).called(1);
      });

      test('should delete budgets successfully', () async {
        // Arrange
        final budgetIds = ['budget-1', 'budget-2'];
        final operationResult = BulkOperationResult.success(processedCount: 2);

        when(
          () => mockBulkBudgetService.deleteBudgets(budgetIds: budgetIds),
        ).thenAnswer((_) async => operationResult);

        final controller = container.read(
          bulkBudgetControllerProvider.notifier,
        );

        // Act
        await controller.deleteBudgets(budgetIds: budgetIds);

        // Assert
        final state = container.read(bulkBudgetControllerProvider);
        expect(state.hasValue, isTrue);
        expect(state.value, equals(operationResult));
        verify(
          () => mockBulkBudgetService.deleteBudgets(budgetIds: budgetIds),
        ).called(1);
      });

      test('should clear result', () {
        // Arrange
        final controller = container.read(
          bulkBudgetControllerProvider.notifier,
        );

        // Act
        controller.clearResult();

        // Assert
        final state = container.read(bulkBudgetControllerProvider);
        expect(state.hasValue, isTrue);
        expect(state.value, isNull);
      });
    });
  });
}

// Mock classes for testing
class MockBudgetRepository extends Mock implements BudgetRepository {}

class MockBudgetProgressService extends Mock implements BudgetProgressService {}

class MockBudgetCopyService extends Mock implements BudgetCopyService {}

class MockBulkBudgetService extends Mock implements BulkBudgetService {}
