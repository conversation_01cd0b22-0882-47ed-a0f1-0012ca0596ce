import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/repositories/interfaces/budget_repository.dart';
import 'package:budapp/features/budgets/services/bulk_budget_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes for testing
class MockBudgetRepository extends Mock implements BudgetRepository {}

void main() {
  group('BulkBudgetService Tests', () {
    late BulkBudgetService service;
    late MockBudgetRepository mockBudgetRepository;

    setUp(() {
      mockBudgetRepository = MockBudgetRepository();
      service = BulkBudgetService(budgetRepository: mockBudgetRepository);
    });

    group('adjustBudgetsByPercentage', () {
      test(
        'should increase budgets by positive percentage successfully',
        () async {
          // Arrange
          final budgetIds = ['budget1', 'budget2'];
          final existingBudgets = [
            Budget(
              id: 'budget1',
              userId: 'user1',
              type: BudgetType.expense,
              plannedAmountCents: 10000, // $100.00 in cents
              currentAmountCents: 0,
              period: BudgetPeriod.monthly,

              isActive: true,
              periodStart: DateTime(
                DateTime.now().year,
                DateTime.now().month,
                1,
              ),
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              schemaVersion: 1,
            ),
            Budget(
              id: 'budget2',
              userId: 'user1',
              type: BudgetType.expense,
              plannedAmountCents: 20000, // $200.00 in cents
              currentAmountCents: 0,
              period: BudgetPeriod.monthly,

              isActive: true,
              periodStart: DateTime(
                DateTime.now().year,
                DateTime.now().month,
                1,
              ),
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              schemaVersion: 1,
            ),
          ];

          when(
            () => mockBudgetRepository.getBudgetById('budget1'),
          ).thenAnswer((_) async => existingBudgets[0]);
          when(
            () => mockBudgetRepository.getBudgetById('budget2'),
          ).thenAnswer((_) async => existingBudgets[1]);
          when(
            () => mockBudgetRepository.batchUpdateBudgets(any<List<Budget>>()),
          ).thenAnswer((_) async {});

          // Act
          final result = await service.adjustBudgetsByPercentage(
            budgetIds: budgetIds,
            percentageChange: 10, // +10%
          );

          // Assert
          expect(result.success, true);
          expect(result.processedCount, 2);
          expect(result.failedCount, 0);
          expect(result.errors, isEmpty);

          // Verify the correct budgets were updated with new amounts
          final capturedBudgets =
              verify(
                    () => mockBudgetRepository.batchUpdateBudgets(
                      captureAny<List<Budget>>(),
                    ),
                  ).captured.first
                  as List<Budget>;

          expect(capturedBudgets.length, 2);
          expect(
            capturedBudgets[0].plannedAmountCents,
            11000,
          ); // $110.00 in cents (100 + 10%)
          expect(
            capturedBudgets[1].plannedAmountCents,
            22000,
          ); // $220.00 in cents (200 + 10%)
        },
      );

      test(
        'should decrease budgets by negative percentage successfully',
        () async {
          // Arrange
          final budgetIds = ['budget1'];
          final existingBudget = Budget(
            id: 'budget1',
            userId: 'user1',
            type: BudgetType.expense,
            plannedAmountCents: 10000, // $100.00 in cents
            currentAmountCents: 0,
            period: BudgetPeriod.monthly,

            isActive: true,
            periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            schemaVersion: 1,
          );

          when(
            () => mockBudgetRepository.getBudgetById('budget1'),
          ).thenAnswer((_) async => existingBudget);
          when(
            () => mockBudgetRepository.batchUpdateBudgets(any<List<Budget>>()),
          ).thenAnswer((_) async {});

          // Act
          final result = await service.adjustBudgetsByPercentage(
            budgetIds: budgetIds,
            percentageChange: -20, // -20%
          );

          // Assert
          expect(result.success, true);
          expect(result.processedCount, 1);
          expect(result.failedCount, 0);

          final capturedBudgets =
              verify(
                    () => mockBudgetRepository.batchUpdateBudgets(
                      captureAny<List<Budget>>(),
                    ),
                  ).captured.first
                  as List<Budget>;

          expect(
            capturedBudgets[0].plannedAmountCents,
            8000,
          ); // $80.00 in cents (100 - 20%)
        },
      );

      test('should enforce minimum amount limit', () async {
        // Arrange
        final budgetIds = ['budget1'];
        final existingBudget = Budget(
          id: 'budget1',
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100, // $1.00 in cents
          currentAmountCents: 0,
          period: BudgetPeriod.monthly,

          isActive: true,
          periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          schemaVersion: 1,
        );

        when(
          () => mockBudgetRepository.getBudgetById('budget1'),
        ).thenAnswer((_) async => existingBudget);
        when(
          () => mockBudgetRepository.batchUpdateBudgets(any<List<Budget>>()),
        ).thenAnswer((_) async {});

        // Act
        final result = await service.adjustBudgetsByPercentage(
          budgetIds: budgetIds,
          percentageChange: -99, // -99% would make it $0.01
        );

        // Assert
        expect(result.success, true);
        final capturedBudgets =
            verify(
                  () => mockBudgetRepository.batchUpdateBudgets(
                    captureAny<List<Budget>>(),
                  ),
                ).captured.first
                as List<Budget>;

        expect(
          capturedBudgets[0].plannedAmountCents,
          1,
        ); // Minimum amount enforced (1 cent)
      });

      test('should reject invalid percentage values', () async {
        // Arrange
        final budgetIds = ['budget1'];

        // Act & Assert - Test percentage too low
        expect(
          () => service.adjustBudgetsByPercentage(
            budgetIds: budgetIds,
            percentageChange: -100, // -100% would make budgets $0
          ),
          throwsA(isA<ArgumentError>()),
        );

        // Act & Assert - Test percentage too high
        expect(
          () => service.adjustBudgetsByPercentage(
            budgetIds: budgetIds,
            percentageChange: 1000, // +1000% is unreasonable
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should handle repository errors gracefully', () async {
        // Arrange
        final budgetIds = ['budget1'];

        when(
          () => mockBudgetRepository.getBudgetById('budget1'),
        ).thenThrow(Exception('Database error'));

        // Act
        final result = await service.adjustBudgetsByPercentage(
          budgetIds: budgetIds,
          percentageChange: 10,
        );

        // Assert
        expect(result.success, false);
        expect(result.processedCount, 0);
        expect(result.failedCount, 1);
        expect(result.errors, isNotEmpty);
        expect(result.errors.first, contains('Database error'));
      });
    });

    group('changeBudgetStatus', () {
      test('should activate budgets successfully', () async {
        // Arrange
        final budgetIds = ['budget1', 'budget2'];
        final existingBudgets = [
          Budget(
            id: 'budget1',
            userId: 'user1',
            type: BudgetType.expense,
            plannedAmountCents: 10000, // $100.00 in cents
            currentAmountCents: 0,
            period: BudgetPeriod.monthly,

            isActive: false, // Currently inactive
            periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            schemaVersion: 1,
          ),
          Budget(
            id: 'budget2',
            userId: 'user1',
            type: BudgetType.expense,
            plannedAmountCents: 20000, // $200.00 in cents
            currentAmountCents: 0,
            period: BudgetPeriod.monthly,

            isActive: false, // Currently inactive
            periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            schemaVersion: 1,
          ),
        ];

        when(
          () => mockBudgetRepository.getBudgetById('budget1'),
        ).thenAnswer((_) async => existingBudgets[0]);
        when(
          () => mockBudgetRepository.getBudgetById('budget2'),
        ).thenAnswer((_) async => existingBudgets[1]);
        when(
          () => mockBudgetRepository.batchUpdateBudgets(any<List<Budget>>()),
        ).thenAnswer((_) async {});

        // Act
        final result = await service.changeBudgetStatus(
          budgetIds: budgetIds,
          isActive: true,
        );

        // Assert
        expect(result.success, true);
        expect(result.processedCount, 2);
        expect(result.failedCount, 0);

        final capturedBudgets =
            verify(
                  () => mockBudgetRepository.batchUpdateBudgets(
                    captureAny<List<Budget>>(),
                  ),
                ).captured.first
                as List<Budget>;

        expect(capturedBudgets.length, 2);
        expect(capturedBudgets[0].isActive, true);
        expect(capturedBudgets[1].isActive, true);
      });

      test('should deactivate budgets successfully', () async {
        // Arrange
        final budgetIds = ['budget1'];
        final existingBudget = Budget(
          id: 'budget1',
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 10000, // $100.00 in cents
          currentAmountCents: 0,
          period: BudgetPeriod.monthly,

          isActive: true, // Currently active
          periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          schemaVersion: 1,
        );

        when(
          () => mockBudgetRepository.getBudgetById('budget1'),
        ).thenAnswer((_) async => existingBudget);
        when(
          () => mockBudgetRepository.batchUpdateBudgets(any<List<Budget>>()),
        ).thenAnswer((_) async {});

        // Act
        final result = await service.changeBudgetStatus(
          budgetIds: budgetIds,
          isActive: false,
        );

        // Assert
        expect(result.success, true);
        expect(result.processedCount, 1);

        final capturedBudgets =
            verify(
                  () => mockBudgetRepository.batchUpdateBudgets(
                    captureAny<List<Budget>>(),
                  ),
                ).captured.first
                as List<Budget>;

        expect(capturedBudgets[0].isActive, false);
      });
    });

    group('deleteBudgets', () {
      test('should delete budgets successfully', () async {
        // Arrange
        final budgetIds = ['budget1', 'budget2'];

        when(
          () => mockBudgetRepository.batchDeleteBudgets(budgetIds),
        ).thenAnswer((_) async {});

        // Act
        final result = await service.deleteBudgets(budgetIds: budgetIds);

        // Assert
        expect(result.success, true);
        expect(result.processedCount, 2);
        expect(result.failedCount, 0);
        expect(result.errors, isEmpty);

        verify(
          () => mockBudgetRepository.batchDeleteBudgets(budgetIds),
        ).called(1);
      });

      test('should handle deletion errors gracefully', () async {
        // Arrange
        final budgetIds = ['budget1'];

        when(
          () => mockBudgetRepository.batchDeleteBudgets(budgetIds),
        ).thenThrow(Exception('Deletion failed'));

        // Act
        final result = await service.deleteBudgets(budgetIds: budgetIds);

        // Assert
        expect(result.success, false);
        expect(result.processedCount, 0);
        expect(result.failedCount, 1);
        expect(result.errors, isNotEmpty);
        expect(result.errors.first, contains('Deletion failed'));
      });

      test('should handle empty budget list', () async {
        // Act
        final result = await service.deleteBudgets(budgetIds: []);

        // Assert
        expect(result.success, true);
        expect(result.processedCount, 0);
        expect(result.failedCount, 0);
        expect(result.warnings, contains('No budgets provided'));

        verifyNever(() => mockBudgetRepository.batchDeleteBudgets(any()));
      });
    });
  });
}
