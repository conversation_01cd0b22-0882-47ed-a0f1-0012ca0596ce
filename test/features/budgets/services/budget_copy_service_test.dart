import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/repositories/interfaces/budget_repository.dart';
import 'package:budapp/data/repositories/interfaces/category_repository.dart';
import 'package:budapp/features/budgets/services/budget_copy_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/mock_data_factory.dart';

// Mock classes for testing
class MockBudgetRepository extends Mock implements BudgetRepository {}

class MockICategoryRepository extends Mock implements ICategoryRepository {}

void main() {
  group('BudgetCopyService Tests', () {
    late BudgetCopyService budgetCopyService;
    late MockBudgetRepository mockBudgetRepository;
    late MockICategoryRepository mockCategoryRepository;

    setUp(() {
      mockBudgetRepository = MockBudgetRepository();
      mockCategoryRepository = MockICategoryRepository();
      budgetCopyService = BudgetCopyService(
        budgetRepository: mockBudgetRepository,
        categoryRepository: mockCategoryRepository,
      );

      // Register fallback values for mocktail
      registerFallbackValue(MockDataFactory.createBudget());
    });

    group('copyBudgetsFromPreviousPeriod', () {
      test(
        'should copy monthly budgets from previous month successfully',
        () async {
          // Arrange
          final currentPeriod = DateTime(2024, 2, 1); // February 2024
          final previousPeriod = DateTime(2024, 1, 1); // January 2024

          final category1 = MockDataFactory.createCategory(
            id: 'cat1',
            name: 'Groceries',
            type: CategoryType.expense,
          );
          final category2 = MockDataFactory.createCategory(
            id: 'cat2',
            name: 'Entertainment',
            type: CategoryType.expense,
          );

          final previousBudgets = [
            MockDataFactory.createBudget(
              id: 'budget1',
              categoryId: 'cat1',
              plannedAmountCents: 50000, // $500.00 in cents
              period: BudgetPeriod.monthly,
            ),
            MockDataFactory.createBudget(
              id: 'budget2',
              categoryId: 'cat2',
              plannedAmountCents: 20000, // $200.00 in cents
              period: BudgetPeriod.monthly,
            ),
          ];

          final activeCategories = [category1, category2];

          // Mock repository calls
          when(
            () => mockBudgetRepository.getBudgetsByPeriod(
              previousPeriod,
              BudgetPeriod.monthly,
            ),
          ).thenAnswer((_) async => previousBudgets);
          when(
            () => mockBudgetRepository.getBudgetsByPeriod(
              currentPeriod,
              BudgetPeriod.monthly,
            ),
          ).thenAnswer(
            (_) async => [],
          ); // No existing budgets in current period
          when(
            () => mockCategoryRepository.getActiveCategories(),
          ).thenAnswer((_) async => activeCategories);
          when(
            () => mockBudgetRepository.batchCreateBudgets(any<List<Budget>>()),
          ).thenAnswer((_) async {});

          // Act
          final result = await budgetCopyService.copyBudgetsFromPreviousPeriod(
            sourcePeriod: previousPeriod,
            targetPeriod: currentPeriod,
            periodType: BudgetPeriod.monthly,
          );

          // Assert
          expect(result.success, isTrue);
          expect(result.copiedCount, equals(2));
          expect(result.skippedCount, equals(0));
          expect(result.errors, isEmpty);

          // Verify batchCreateBudgets was called once with 2 budgets
          verify(
            () => mockBudgetRepository.batchCreateBudgets(any<List<Budget>>()),
          ).called(1);
        },
      );

      test('should skip budgets for inactive categories', () async {
        // Arrange
        final currentPeriod = DateTime(2024, 2, 1);
        final previousPeriod = DateTime(2024, 1, 1);

        final activeCategory = MockDataFactory.createCategory(
          id: 'cat1',
          name: 'Groceries',
          type: CategoryType.expense,
        );

        final previousBudgets = [
          MockDataFactory.createBudget(
            id: 'budget1',
            categoryId: 'cat1', // Active category
            plannedAmountCents: 50000, // $500.00 in cents
            period: BudgetPeriod.monthly,
          ),
          MockDataFactory.createBudget(
            id: 'budget2',
            categoryId: 'cat2', // Inactive category
            plannedAmountCents: 20000, // $200.00 in cents
            period: BudgetPeriod.monthly,
          ),
        ];

        // Mock repository calls
        when(
          () => mockBudgetRepository.getBudgetsByPeriod(
            previousPeriod,
            BudgetPeriod.monthly,
          ),
        ).thenAnswer((_) async => previousBudgets);
        when(
          () => mockBudgetRepository.getBudgetsByPeriod(
            currentPeriod,
            BudgetPeriod.monthly,
          ),
        ).thenAnswer((_) async => []);
        when(
          () => mockCategoryRepository.getActiveCategories(),
        ).thenAnswer((_) async => [activeCategory]); // Only cat1 is active
        when(
          () => mockBudgetRepository.batchCreateBudgets(any<List<Budget>>()),
        ).thenAnswer((_) async {});

        // Act
        final result = await budgetCopyService.copyBudgetsFromPreviousPeriod(
          sourcePeriod: previousPeriod,
          targetPeriod: currentPeriod,
          periodType: BudgetPeriod.monthly,
        );

        // Assert
        expect(result.success, isTrue);
        expect(result.copiedCount, equals(1)); // Only one budget copied
        expect(result.skippedCount, equals(1)); // One budget skipped
        expect(
          result.skippedReasons,
          contains('Category no longer active: cat2'),
        );

        // Verify batchCreateBudgets was called once with 1 budget
        verify(
          () => mockBudgetRepository.batchCreateBudgets(any<List<Budget>>()),
        ).called(1);
      });

      test('should skip budgets that already exist in target period', () async {
        // Arrange
        final currentPeriod = DateTime(2024, 2, 1);
        final previousPeriod = DateTime(2024, 1, 1);

        final category = MockDataFactory.createCategory(
          id: 'cat1',
          name: 'Groceries',
          type: CategoryType.expense,
        );

        final previousBudgets = [
          MockDataFactory.createBudget(
            id: 'budget1',
            categoryId: 'cat1',
            plannedAmountCents: 50000, // $500.00 in cents
            period: BudgetPeriod.monthly,
          ),
        ];

        final existingBudgets = [
          MockDataFactory.createBudget(
            id: 'existing1',
            categoryId: 'cat1', // Same category already has budget
            plannedAmountCents: 60000, // $600.00 in cents
            period: BudgetPeriod.monthly,
          ),
        ];

        // Mock repository calls
        when(
          () => mockBudgetRepository.getBudgetsByPeriod(
            previousPeriod,
            BudgetPeriod.monthly,
          ),
        ).thenAnswer((_) async => previousBudgets);
        when(
          () => mockBudgetRepository.getBudgetsByPeriod(
            currentPeriod,
            BudgetPeriod.monthly,
          ),
        ).thenAnswer((_) async => existingBudgets);
        when(
          () => mockCategoryRepository.getActiveCategories(),
        ).thenAnswer((_) async => [category]);

        // Act
        final result = await budgetCopyService.copyBudgetsFromPreviousPeriod(
          sourcePeriod: previousPeriod,
          targetPeriod: currentPeriod,
          periodType: BudgetPeriod.monthly,
        );

        // Assert
        expect(result.success, isTrue);
        expect(result.copiedCount, equals(0));
        expect(result.skippedCount, equals(1));
        expect(
          result.skippedReasons,
          contains('Budget already exists for category: cat1'),
        );

        // Verify batchCreateBudgets was never called
        verifyNever(
          () => mockBudgetRepository.batchCreateBudgets(any<List<Budget>>()),
        );
      });

      test('should handle repository errors gracefully', () async {
        // Arrange
        final currentPeriod = DateTime(2024, 2, 1);
        final previousPeriod = DateTime(2024, 1, 1);

        // Mock repository to throw error
        when(
          () => mockBudgetRepository.getBudgetsByPeriod(
            previousPeriod,
            BudgetPeriod.monthly,
          ),
        ).thenThrow(Exception('Firestore error'));

        // Act
        final result = await budgetCopyService.copyBudgetsFromPreviousPeriod(
          sourcePeriod: previousPeriod,
          targetPeriod: currentPeriod,
          periodType: BudgetPeriod.monthly,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.copiedCount, equals(0));
        expect(result.errors, isNotEmpty);
        expect(result.errors.first, contains('Firestore error'));
      });

      test('should work with yearly budgets', () async {
        // Arrange
        final currentPeriod = DateTime(2024, 1, 1); // 2024
        final previousPeriod = DateTime(2023, 1, 1); // 2023

        final category = MockDataFactory.createCategory(
          id: 'cat1',
          name: 'Annual Vacation',
          type: CategoryType.expense,
        );

        final previousBudgets = [
          MockDataFactory.createBudget(
            id: 'budget1',
            categoryId: 'cat1',
            plannedAmountCents: 500000, // $5000.00 in cents
            period: BudgetPeriod.yearly,
          ),
        ];

        // Mock repository calls
        when(
          () => mockBudgetRepository.getBudgetsByPeriod(
            previousPeriod,
            BudgetPeriod.yearly,
          ),
        ).thenAnswer((_) async => previousBudgets);
        when(
          () => mockBudgetRepository.getBudgetsByPeriod(
            currentPeriod,
            BudgetPeriod.yearly,
          ),
        ).thenAnswer((_) async => []);
        when(
          () => mockCategoryRepository.getActiveCategories(),
        ).thenAnswer((_) async => [category]);
        when(
          () => mockBudgetRepository.batchCreateBudgets(any<List<Budget>>()),
        ).thenAnswer((_) async {});

        // Act
        final result = await budgetCopyService.copyBudgetsFromPreviousPeriod(
          sourcePeriod: previousPeriod,
          targetPeriod: currentPeriod,
          periodType: BudgetPeriod.yearly,
        );

        // Assert
        expect(result.success, isTrue);
        expect(result.copiedCount, equals(1));
        expect(result.skippedCount, equals(0));

        verify(
          () => mockBudgetRepository.batchCreateBudgets(any<List<Budget>>()),
        ).called(1);
      });
    });

    group('getAvailableSourcePeriods', () {
      test('should return available monthly periods', () async {
        // Arrange
        final currentPeriod = DateTime(2024, 3, 1); // March 2024
        final budgets = [
          MockDataFactory.createBudget(
            period: BudgetPeriod.monthly,
            createdAt: DateTime(2024, 2, 15), // February 2024
          ),
          MockDataFactory.createBudget(
            period: BudgetPeriod.monthly,
            createdAt: DateTime(2024, 1, 10), // January 2024
          ),
          MockDataFactory.createBudget(
            period: BudgetPeriod.monthly,
            createdAt: DateTime(
              2024,
              3,
              5,
            ), // March 2024 (current - should be excluded)
          ),
        ];

        when(
          () => mockBudgetRepository.getAllBudgets(),
        ).thenAnswer((_) async => budgets);

        // Act
        final periods = await budgetCopyService.getAvailableSourcePeriods(
          currentPeriod: currentPeriod,
          periodType: BudgetPeriod.monthly,
        );

        // Assert
        expect(periods, isNotEmpty);
        // Should include previous months but not current month
        expect(periods, contains(DateTime(2024, 2, 1))); // February
        expect(periods, contains(DateTime(2024, 1, 1))); // January
        expect(periods, isNot(contains(currentPeriod))); // Not current month
      });
    });
  });
}
