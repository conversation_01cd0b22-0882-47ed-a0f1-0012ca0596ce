import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/repositories/implementations/budget_repository_impl.dart';
import 'package:budapp/data/repositories/implementations/transaction_repository_impl.dart';
import 'package:budapp/features/budgets/services/budget_transaction_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../helpers/firebase_test_setup.dart';

/// Integration test to verify automatic budget tracking functionality
///
/// This test verifies that budget currentAmountCents fields are automatically
/// updated when transactions are created, modified, or deleted.
void main() {
  group('Budget Transaction Integration Tests', () {
    late FirebaseTestSetup testSetup;
    late FirestoreService firestoreService;
    late BudgetRepositoryImpl budgetRepository;
    late TransactionRepositoryImpl transactionRepository;
    late BudgetTransactionService budgetTransactionService;

    const testUserId = 'test-user-budget-integration';
    const testAccountId = 'test-account-1';
    const testCategoryId = 'test-category-1';

    setUp(() async {
      // Create Firebase test setup with authenticated user
      testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
        uid: testUserId,
        email: '<EMAIL>',
        displayName: 'Test User',
      );

      firestoreService = FirestoreService(testSetup.firestore);

      budgetRepository = BudgetRepositoryImpl(
        firestoreService: firestoreService,
        firebaseAuth: testSetup.auth,
      );
      budgetTransactionService = BudgetTransactionService(
        budgetRepository,
        firestoreService,
      );
      transactionRepository = TransactionRepositoryImpl(
        firestoreService,
        budgetTransactionService,
      );

      // Create test account
      await testSetup.firestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId)
          .set({
            'id': testAccountId,
            'userId': testUserId,
            'name': 'Test Account',
            'type': 'checking',
            'classification': 'asset',
            'initialBalanceCents': 100000, // $1000
            'currentBalanceCents': 100000,
            'isPrimary': false,
            'isActive': true,
            'createdAt': DateTime.now().toIso8601String(),
            'updatedAt': DateTime.now().toIso8601String(),
            'metadata': <String, dynamic>{},
          });
    });

    tearDown(() async {
      await testSetup.dispose();
    });

    group('Transaction Creation Budget Updates', () {
      test(
        'should update expense budget when expense transaction is created',
        () async {
          // Create an expense budget for the category
          final budget = Budget.create(
            userId: testUserId,
            type: BudgetType.expense,
            plannedAmountCents: 50000, // $500
            categoryId: testCategoryId,
            periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
          );

          final createdBudget = await budgetRepository.createBudget(budget);
          final budgetId = createdBudget.id;

          // Create an expense transaction
          await transactionRepository.createExpenseTransaction(
            userId: testUserId,
            fromAccountId: testAccountId,
            amountCents: 10000, // $100

            transactionDate: DateTime.now(),
            categoryId: testCategoryId,
            description: 'Grocery shopping',
          );

          // Verify budget was updated
          final updatedBudget = await budgetRepository.getBudgetById(budgetId);
          expect(updatedBudget, isNotNull);
          expect(
            updatedBudget!.currentAmountCents,
            equals(10000),
          ); // $100 spent
        },
      );

      test(
        'should update income budget when income transaction is created',
        () async {
          // Create an income budget for the category
          final budget = Budget.create(
            userId: testUserId,
            type: BudgetType.income,
            plannedAmountCents: 300000, // $3000
            categoryId: testCategoryId,
            periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
          );

          final createdBudget = await budgetRepository.createBudget(budget);
          final budgetId = createdBudget.id;

          // Create an income transaction
          await transactionRepository.createIncomeTransaction(
            userId: testUserId,
            toAccountId: testAccountId,
            amountCents: 250000, // $2500

            transactionDate: DateTime.now(),
            categoryId: testCategoryId,
            description: 'Monthly salary',
          );

          // Verify budget was updated
          final updatedBudget = await budgetRepository.getBudgetById(budgetId);
          expect(updatedBudget, isNotNull);
          expect(
            updatedBudget!.currentAmountCents,
            equals(250000),
          ); // $2500 received
        },
      );
    });

    group('Transaction Update Budget Updates', () {
      test(
        'should update budgets when transaction amount is modified',
        () async {
          // Create an expense budget
          final budget = Budget.create(
            userId: testUserId,
            type: BudgetType.expense,
            plannedAmountCents: 30000, // $300
            categoryId: testCategoryId,
            periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
          );

          final createdBudget = await budgetRepository.createBudget(budget);
          final budgetId = createdBudget.id;

          // Create initial expense transaction
          final transactionId = await transactionRepository
              .createExpenseTransaction(
                userId: testUserId,
                fromAccountId: testAccountId,
                amountCents: 5000, // $50

                transactionDate: DateTime.now(),
                categoryId: testCategoryId,
                description: 'Restaurant dinner',
              );

          // Verify initial budget state
          var updatedBudget = await budgetRepository.getBudgetById(budgetId);
          expect(updatedBudget!.currentAmountCents, equals(5000)); // $50 spent

          // Update transaction amount
          final originalTransaction = await transactionRepository
              .getTransactionById(testUserId, transactionId);
          final updatedTransaction = originalTransaction!.copyWith(
            amountCents: 8000, // Changed from $50 to $80
            updatedAt: DateTime.now(),
          );

          await transactionRepository.updateTransactionWithBalanceAdjustment(
            testUserId,
            transactionId,
            updatedTransaction,
          );

          // Verify budget was updated correctly
          // Should revert $50 and apply $80 = $80 total
          updatedBudget = await budgetRepository.getBudgetById(budgetId);
          expect(updatedBudget!.currentAmountCents, equals(8000)); // $80 spent
        },
      );

      test(
        'should update budgets when transaction category is changed',
        () async {
          const category1Id = 'category-1';
          const category2Id = 'category-2';

          // Create budgets for both categories
          final budget1 = Budget.create(
            userId: testUserId,
            type: BudgetType.expense,
            plannedAmountCents: 20000, // $200
            categoryId: category1Id,
            periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
          );

          final budget2 = Budget.create(
            userId: testUserId,
            type: BudgetType.expense,
            plannedAmountCents: 30000, // $300
            categoryId: category2Id,
            periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
          );

          final createdBudget1 = await budgetRepository.createBudget(budget1);
          final createdBudget2 = await budgetRepository.createBudget(budget2);
          final budget1Id = createdBudget1.id;
          final budget2Id = createdBudget2.id;

          // Create transaction in category 1
          final transactionId = await transactionRepository
              .createExpenseTransaction(
                userId: testUserId,
                fromAccountId: testAccountId,
                amountCents: 7500, // $75

                transactionDate: DateTime.now(),
                categoryId: category1Id,
                description: 'Initial purchase',
              );

          // Verify initial budget states
          var budget1Updated = await budgetRepository.getBudgetById(budget1Id);
          var budget2Updated = await budgetRepository.getBudgetById(budget2Id);
          expect(
            budget1Updated!.currentAmountCents,
            equals(7500),
          ); // $75 in category 1
          expect(
            budget2Updated!.currentAmountCents,
            equals(0),
          ); // $0 in category 2

          // Update transaction to move to category 2
          final originalTransaction = await transactionRepository
              .getTransactionById(testUserId, transactionId);
          final updatedTransaction = originalTransaction!.copyWith(
            categoryId: category2Id, // Changed from category1 to category2
            updatedAt: DateTime.now(),
          );

          await transactionRepository.updateTransactionWithBalanceAdjustment(
            testUserId,
            transactionId,
            updatedTransaction,
          );

          // Verify budgets were updated correctly
          // Category 1 should have $75 removed, Category 2 should have $75 added
          budget1Updated = await budgetRepository.getBudgetById(budget1Id);
          budget2Updated = await budgetRepository.getBudgetById(budget2Id);
          expect(
            budget1Updated!.currentAmountCents,
            equals(0),
          ); // $0 in category 1 (removed)
          expect(
            budget2Updated!.currentAmountCents,
            equals(7500),
          ); // $75 in category 2 (added)
        },
      );
    });

    group('Transaction Deletion Budget Updates', () {
      test(
        'should revert budget when expense transaction is deleted',
        () async {
          // Create an expense budget
          final budget = Budget.create(
            userId: testUserId,
            type: BudgetType.expense,
            plannedAmountCents: 15000, // $150
            categoryId: testCategoryId,
            periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
          );

          final createdBudget = await budgetRepository.createBudget(budget);
          final budgetId = createdBudget.id;

          // Create expense transaction
          final transactionId = await transactionRepository
              .createExpenseTransaction(
                userId: testUserId,
                fromAccountId: testAccountId,
                amountCents: 6000, // $60

                transactionDate: DateTime.now(),
                categoryId: testCategoryId,
                description: 'Movie tickets',
              );

          // Verify budget was updated
          var updatedBudget = await budgetRepository.getBudgetById(budgetId);
          expect(updatedBudget!.currentAmountCents, equals(6000)); // $60 spent

          // Delete the transaction
          await transactionRepository.deleteTransaction(
            testUserId,
            transactionId,
          );

          // Verify budget was reverted
          updatedBudget = await budgetRepository.getBudgetById(budgetId);
          expect(
            updatedBudget!.currentAmountCents,
            equals(0),
          ); // $0 spent (reverted)
        },
      );
    });

    group('Automatic Budget Creation', () {
      test(
        'should automatically create budget when transaction is created for category with no existing budget',
        () async {
          // Setup test account
          final account = Account(
            id: testAccountId,
            userId: testUserId,
            name: 'Test Account',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 100000, // $1000
            currentBalanceCents: 100000, // $1000

            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await testSetup.firestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .set(account.toJson());

          // Verify no budgets exist initially
          final initialBudgets = await budgetRepository.getAllBudgets();
          expect(initialBudgets, isEmpty);

          // Create an expense transaction for a category with no existing budget
          await transactionRepository.createExpenseTransaction(
            userId: testUserId,
            fromAccountId: testAccountId,
            amountCents: 5000, // $50

            transactionDate: DateTime.now(),
            categoryId: testCategoryId,
            description: 'Grocery shopping',
          );

          // Verify that budgets were automatically created (category + total)
          final budgetsAfterTransaction = await budgetRepository
              .getAllBudgets();
          expect(budgetsAfterTransaction, hasLength(2));

          // Find the category budget
          final categoryBudget = budgetsAfterTransaction.firstWhere(
            (budget) => budget.categoryId == testCategoryId,
          );
          expect(categoryBudget.type, equals(BudgetType.expense));
          expect(categoryBudget.categoryId, equals(testCategoryId));
          expect(categoryBudget.period, equals(BudgetPeriod.monthly));
          expect(categoryBudget.isActive, isTrue);
          expect(
            categoryBudget.currentAmountCents,
            equals(5000),
          ); // Updated with transaction amount

          // Find the total budget
          final totalBudget = budgetsAfterTransaction.firstWhere(
            (budget) => budget.categoryId == null,
          );
          expect(totalBudget.type, equals(BudgetType.expense));
          expect(totalBudget.categoryId, isNull);
          expect(totalBudget.period, equals(BudgetPeriod.monthly));
          expect(totalBudget.isActive, isTrue);
          expect(
            totalBudget.currentAmountCents,
            equals(5000),
          ); // Updated with transaction amount
          // Verify category budget properties
          expect(
            categoryBudget.plannedAmountCents,
            equals(0),
          ); // Default planned amount

          // Verify total budget properties
          expect(
            totalBudget.plannedAmountCents,
            equals(0),
          ); // Default planned amount

          // Verify the category budget exists in Firebase
          final categoryBudgetDoc = await testSetup.firestore
              .collection('users')
              .doc(testUserId)
              .collection('budgets')
              .doc(categoryBudget.id)
              .get();
          expect(categoryBudgetDoc.exists, isTrue);

          // Verify the total budget exists in Firebase
          final totalBudgetDoc = await testSetup.firestore
              .collection('users')
              .doc(testUserId)
              .collection('budgets')
              .doc(totalBudget.id)
              .get();
          expect(totalBudgetDoc.exists, isTrue);
        },
      );

      test(
        'should automatically create income budget when income transaction is created',
        () async {
          // Setup test account
          final account = Account(
            id: testAccountId,
            userId: testUserId,
            name: 'Test Account',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 100000, // $1000
            currentBalanceCents: 100000, // $1000

            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await testSetup.firestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .set(account.toJson());

          // Verify no budgets exist initially
          final initialBudgets = await budgetRepository.getAllBudgets();
          expect(initialBudgets, isEmpty);

          // Create an income transaction for a category with no existing budget
          await transactionRepository.createIncomeTransaction(
            userId: testUserId,
            toAccountId: testAccountId,
            amountCents: 300000, // $3000

            transactionDate: DateTime.now(),
            categoryId: testCategoryId,
            description: 'Monthly salary',
          );

          // Verify that income budgets were automatically created (category + total)
          final budgetsAfterTransaction = await budgetRepository
              .getAllBudgets();
          expect(budgetsAfterTransaction, hasLength(2));

          // Find the category budget
          final categoryBudget = budgetsAfterTransaction.firstWhere(
            (budget) => budget.categoryId == testCategoryId,
          );
          expect(categoryBudget.type, equals(BudgetType.income));
          expect(categoryBudget.categoryId, equals(testCategoryId));
          expect(categoryBudget.period, equals(BudgetPeriod.monthly));
          expect(categoryBudget.isActive, isTrue);
          expect(
            categoryBudget.currentAmountCents,
            equals(300000),
          ); // Updated with transaction amount
          expect(
            categoryBudget.plannedAmountCents,
            equals(0),
          ); // Default planned amount

          // Find the total budget
          final totalBudget = budgetsAfterTransaction.firstWhere(
            (budget) => budget.categoryId == null,
          );
          expect(totalBudget.type, equals(BudgetType.income));
          expect(totalBudget.categoryId, isNull);
          expect(totalBudget.period, equals(BudgetPeriod.monthly));
          expect(totalBudget.isActive, isTrue);
          expect(
            totalBudget.currentAmountCents,
            equals(300000),
          ); // Updated with transaction amount
          expect(
            totalBudget.plannedAmountCents,
            equals(0),
          ); // Default planned amount
        },
      );
    });
  });
}
