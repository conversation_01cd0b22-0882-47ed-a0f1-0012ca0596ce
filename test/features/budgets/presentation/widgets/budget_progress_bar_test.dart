import 'package:budapp/data/models/budget_progress.dart';
import 'package:budapp/features/budgets/presentation/widgets/budget_progress_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/test_wrapper.dart';

void main() {
  group('BudgetProgressBar', () {
    late BudgetProgress onTrackProgress;
    late BudgetProgress warningProgress;
    late BudgetProgress overBudgetProgress;

    setUp(() {
      onTrackProgress = BudgetProgress.fromBudgetAndSpent(
        budgetId: '1',
        budgetAmount: 1000,
        spentAmount: 500, // 50%
      );

      warningProgress = BudgetProgress.fromBudgetAndSpent(
        budgetId: '2',
        budgetAmount: 1000,
        spentAmount: 850, // 85%
      );

      overBudgetProgress = BudgetProgress.fromBudgetAndSpent(
        budgetId: '3',
        budgetAmount: 1000,
        spentAmount: 1200, // 120%
      );
    });

    group('Basic Display', () {
      testWidgets('displays progress bar with correct progress', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(progress: onTrackProgress),
          ),
        );

        await tester.pumpAndSettle();

        // Verify animated container is present
        expect(find.byType(AnimatedContainer), findsOneWidget);
        expect(find.byType(Stack), findsAtLeast(1));
      });

      testWidgets('displays percentage text when height is small', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(
              progress: onTrackProgress,
              height: 8,
              showPercentage: true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Percentage should be shown below the bar
        expect(find.text('50.0%'), findsOneWidget);
      });

      testWidgets('displays percentage text on bar when height is large', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(
              progress: onTrackProgress,
              height: 25,
              showPercentage: true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Percentage should be shown on the bar
        expect(find.text('50%'), findsOneWidget);
      });

      testWidgets('hides percentage text when showPercentage is false', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(progress: onTrackProgress, showPercentage: false),
          ),
        );

        await tester.pumpAndSettle();

        // No percentage text should be shown
        expect(find.text('50.0%'), findsNothing);
        expect(find.text('50%'), findsNothing);
      });

      testWidgets('uses custom height when provided', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(progress: onTrackProgress, height: 15),
          ),
        );

        await tester.pumpAndSettle();

        // Verify custom height is applied
        final containers = tester.widgetList<Container>(find.byType(Container));
        expect(containers.isNotEmpty, isTrue);
      });
    });

    group('Progress Status Colors', () {
      testWidgets('displays green color for on-track progress', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(progress: onTrackProgress, showPercentage: true),
          ),
        );

        await tester.pumpAndSettle();

        // Verify on-track status
        expect(onTrackProgress.status, BudgetProgressStatus.onTrack);
        expect(onTrackProgress.statusColor, Colors.green);
      });

      testWidgets('displays orange color for warning progress', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(progress: warningProgress, showPercentage: true),
          ),
        );

        await tester.pumpAndSettle();

        // Verify warning status
        expect(warningProgress.status, BudgetProgressStatus.warning);
        expect(warningProgress.statusColor, Colors.orange);
      });

      testWidgets('displays red color for over-budget progress', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(
              progress: overBudgetProgress,
              showPercentage: true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify over-budget status
        expect(overBudgetProgress.status, BudgetProgressStatus.overBudget);
        expect(overBudgetProgress.statusColor, Colors.red);
      });
    });

    group('Status Chip Display', () {
      testWidgets('displays "On Track" status chip', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(
              progress: onTrackProgress,
              height: 8,
              showPercentage: true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('On Track'), findsOneWidget);
      });

      testWidgets('displays "Warning" status chip', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(
              progress: warningProgress,
              height: 8,
              showPercentage: true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('Warning'), findsOneWidget);
      });

      testWidgets('displays "Over Budget" status chip', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(
              progress: overBudgetProgress,
              height: 8,
              showPercentage: true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('Over Budget'), findsOneWidget);
      });
    });

    group('Edge Cases', () {
      testWidgets('handles zero progress correctly', (tester) async {
        final zeroProgress = BudgetProgress.fromBudgetAndSpent(
          budgetId: '1',
          budgetAmount: 1000,
          spentAmount: 0,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(progress: zeroProgress),
          ),
        );

        await tester.pumpAndSettle();

        // Should not crash
        expect(find.byType(BudgetProgressBar), findsOneWidget);
      });

      testWidgets('handles 100% progress correctly', (tester) async {
        final fullProgress = BudgetProgress.fromBudgetAndSpent(
          budgetId: '1',
          budgetAmount: 1000,
          spentAmount: 1000,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(progress: fullProgress),
          ),
        );

        await tester.pumpAndSettle();

        // Should not crash
        expect(find.byType(BudgetProgressBar), findsOneWidget);
      });

      testWidgets('handles extremely over-budget progress', (tester) async {
        final extremeProgress = BudgetProgress.fromBudgetAndSpent(
          budgetId: '1',
          budgetAmount: 1000,
          spentAmount: 5000, // 500%
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(progress: extremeProgress),
          ),
        );

        await tester.pumpAndSettle();

        // Should not crash and should cap at 100% width
        expect(find.byType(BudgetProgressBar), findsOneWidget);
      });

      testWidgets('handles very small height', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(progress: onTrackProgress, height: 1),
          ),
        );

        await tester.pumpAndSettle();

        // Should not crash
        expect(find.byType(BudgetProgressBar), findsOneWidget);
      });

      testWidgets('handles very large height', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(progress: onTrackProgress, height: 100),
          ),
        );

        await tester.pumpAndSettle();

        // Should not crash
        expect(find.byType(BudgetProgressBar), findsOneWidget);
      });
    });

    group('Animation', () {
      testWidgets('uses AnimatedContainer for smooth progress updates', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(progress: onTrackProgress),
          ),
        );

        await tester.pumpAndSettle();

        // Verify AnimatedContainer is used
        expect(find.byType(AnimatedContainer), findsOneWidget);
      });

      testWidgets('animates progress changes smoothly', (tester) async {
        // Create initial progress
        final initialProgress = BudgetProgress.fromBudgetAndSpent(
          budgetId: '1',
          budgetAmount: 1000,
          spentAmount: 200,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(progress: initialProgress),
          ),
        );

        await tester.pumpAndSettle();

        // Update progress
        final updatedProgress = BudgetProgress.fromBudgetAndSpent(
          budgetId: '1',
          budgetAmount: 1000,
          spentAmount: 600,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetProgressBar(progress: updatedProgress),
          ),
        );

        // Animation should be in progress
        await tester.pump();
        expect(find.byType(AnimatedContainer), findsOneWidget);

        // Wait for animation to complete
        await tester.pumpAndSettle();
        expect(find.byType(BudgetProgressBar), findsOneWidget);
      });
    });
  });

  group('DetailedBudgetProgressBar', () {
    late BudgetProgress testProgress;

    setUp(() {
      testProgress = BudgetProgress.fromBudgetAndSpent(
        budgetId: '1',
        budgetAmount: 1000,
        spentAmount: 600,
      );
    });

    testWidgets('displays detailed progress information', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          DetailedBudgetProgressBar(progress: testProgress, currency: 'USD'),
        ),
      );

      await tester.pumpAndSettle();

      // Verify main progress bar is displayed
      expect(find.byType(BudgetProgressBar), findsOneWidget);

      // Verify detailed information is displayed
      expect(find.text('Spent'), findsOneWidget);
      expect(find.text('Remaining'), findsOneWidget);
      expect(find.text('60.0%'), findsOneWidget);
    });

    testWidgets('displays over-budget information correctly', (tester) async {
      final overBudgetProgress = BudgetProgress.fromBudgetAndSpent(
        budgetId: '1',
        budgetAmount: 1000,
        spentAmount: 1200,
      );

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          DetailedBudgetProgressBar(
            progress: overBudgetProgress,
            currency: 'USD',
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should show "Over by" instead of "Remaining"
      expect(find.text('Over by'), findsOneWidget);
      expect(find.text('120.0%'), findsOneWidget);
    });

    testWidgets('formats currency amounts correctly', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          DetailedBudgetProgressBar(progress: testProgress, currency: 'USD'),
        ),
      );

      await tester.pumpAndSettle();

      // Verify currency formatting
      expect(find.text(r'$600.00'), findsOneWidget); // Spent
      expect(find.text(r'$400.00'), findsOneWidget); // Remaining
    });

    testWidgets('handles zero remaining amount', (tester) async {
      final exactProgress = BudgetProgress.fromBudgetAndSpent(
        budgetId: '1',
        budgetAmount: 1000,
        spentAmount: 1000,
      );

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          DetailedBudgetProgressBar(progress: exactProgress, currency: 'USD'),
        ),
      );

      await tester.pumpAndSettle();

      // Should not crash
      expect(find.byType(DetailedBudgetProgressBar), findsOneWidget);
    });

    testWidgets('displays status text correctly', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          DetailedBudgetProgressBar(progress: testProgress, currency: 'USD'),
        ),
      );

      await tester.pumpAndSettle();

      // Should display status based on progress
      expect(find.text('On Track'), findsOneWidget);
    });
  });
}
