import 'package:budapp/features/budgets/presentation/widgets/bulk_budget_operations_dialog.dart';
import 'package:budapp/features/budgets/providers/budget_providers.dart';
import 'package:budapp/features/budgets/services/bulk_budget_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';

class MockBulkBudgetService extends Mock implements BulkBudgetService {}

void main() {
  group('BulkBudgetOperationsDialog', () {
    late MockBulkBudgetService mockBulkBudgetService;
    late List<Override> overrides;

    setUp(() {
      mockBulkBudgetService = MockBulkBudgetService();
      overrides = [
        bulkBudgetServiceProvider.overrideWithValue(mockBulkBudgetService),
      ];
    });

    Widget createWidget() {
      return TestWrapper.createTestWidget(
        BulkBudgetOperationsDialog(
          selectedBudgetIds: const ['budget1', 'budget2', 'budget3'],
          onOperationComplete: () {
            // Operation completed callback
          },
        ),
        overrides: overrides,
      );
    }

    group('Dialog Rendering', () {
      testWidgets('should display dialog title and selected budget count', (
        tester,
      ) async {
        await tester.pumpWidget(createWidget());

        expect(find.text('Bulk Budget Operations'), findsOneWidget);
        expect(find.text('3 budgets selected'), findsOneWidget);
        expect(find.byIcon(Icons.edit_outlined), findsOneWidget);
        expect(find.byIcon(Icons.info_outline), findsOneWidget);
      });

      testWidgets('should display all operation options', (tester) async {
        await tester.pumpWidget(createWidget());

        expect(find.text('Adjust by Percentage'), findsOneWidget);
        expect(
          find.text('Increase or decrease budget amounts by a percentage'),
          findsOneWidget,
        );
        expect(find.text('Deactivate Budgets'), findsOneWidget);
        expect(find.text('Hide budgets from active lists'), findsOneWidget);
        expect(find.text('Activate Budgets'), findsOneWidget);
        expect(find.text('Show budgets in active lists'), findsOneWidget);
        expect(find.text('Delete Budgets'), findsOneWidget);
        expect(
          find.text('Permanently remove budgets (cannot be undone)'),
          findsOneWidget,
        );
      });

      testWidgets(
        'should display percentage input field when adjust option is selected',
        (tester) async {
          await tester.pumpWidget(createWidget());

          // Percentage adjustment is selected by default
          expect(find.text('Percentage Change'), findsOneWidget);
          expect(find.text('e.g., 10 for +10%, -20 for -20%'), findsOneWidget);
          expect(find.text('%'), findsOneWidget);
        },
      );

      testWidgets(
        'should hide percentage input when other options are selected',
        (tester) async {
          await tester.pumpWidget(createWidget());

          // Select deactivate option
          await tester.tap(find.byType(RadioListTile<BulkOperationType>).at(1));
          await tester.pump();

          expect(find.text('Percentage Change'), findsNothing);
        },
      );

      testWidgets('should display action buttons', (tester) async {
        await tester.pumpWidget(createWidget());

        expect(find.text('Cancel'), findsOneWidget);
        expect(find.text('Adjust Budgets'), findsOneWidget);
      });
    });

    group('Operation Selection', () {
      testWidgets(
        'should update selected operation when radio button is tapped',
        (tester) async {
          await tester.pumpWidget(createWidget());

          // Initially adjust percentage is selected
          expect(find.text('Adjust Budgets'), findsOneWidget);

          // Tap deactivate option
          await tester.tap(find.byType(RadioListTile<BulkOperationType>).at(1));
          await tester.pump();

          // Should find the text in both the radio button and the action button
          expect(find.text('Deactivate Budgets'), findsNWidgets(2));
        },
      );

      testWidgets(
        'should show delete button with error styling when delete is selected',
        (tester) async {
          await tester.pumpWidget(createWidget());

          // Initially should show "Adjust Budgets" button
          expect(find.text('Adjust Budgets'), findsOneWidget);

          // Ensure the dialog is fully rendered
          await tester.pumpAndSettle();

          // Find all radio buttons and verify we have 4
          final radioButtons = find.byType(RadioListTile<BulkOperationType>);
          expect(radioButtons, findsNWidgets(4));

          // Tap the delete radio button (last one)
          await tester.ensureVisible(radioButtons.at(3));
          await tester.tap(radioButtons.at(3));
          await tester.pump();

          // After selecting delete, button text should change to "Delete Budgets"
          expect(
            find.text('Delete Budgets'),
            findsNWidgets(2),
          ); // Radio title + button text

          // Find the FilledButton and check if it has error styling
          final deleteButton = tester.widget<FilledButton>(
            find.byType(FilledButton),
          );
          expect(deleteButton.style, isNotNull);
        },
      );

      testWidgets('should show delete text in error color', (tester) async {
        await tester.pumpWidget(createWidget());

        // The delete option title should have error color
        final deleteRadio = find.byType(RadioListTile<BulkOperationType>).at(3);
        expect(deleteRadio, findsOneWidget);
      });
    });

    group('Form Validation', () {
      testWidgets('should validate percentage input - empty value', (
        tester,
      ) async {
        await tester.pumpWidget(createWidget());

        // Clear the percentage field
        await tester.enterText(find.byType(TextFormField), '');

        // Tap the action button
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        expect(find.text('Percentage is required'), findsOneWidget);
      });

      testWidgets('should prevent invalid characters from being entered', (
        tester,
      ) async {
        await tester.pumpWidget(createWidget());

        // Try to enter invalid characters - they should be completely rejected
        await tester.enterText(find.byType(TextFormField), 'abc');

        // The field should remain empty since invalid input is rejected
        final textField = tester.widget<TextFormField>(
          find.byType(TextFormField),
        );
        expect(textField.controller?.text, '');

        // Tap the action button - should show validation error for empty field
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Should show validation error for empty input
        expect(find.text('Percentage is required'), findsOneWidget);
      });

      testWidgets('should validate percentage input - too low value', (
        tester,
      ) async {
        await tester.pumpWidget(createWidget());

        // Enter percentage too low
        await tester.enterText(find.byType(TextFormField), '-100');

        // Tap the action button
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        expect(
          find.text('Percentage cannot be -100% or lower'),
          findsOneWidget,
        );
      });

      testWidgets('should validate percentage input - too high value', (
        tester,
      ) async {
        await tester.pumpWidget(createWidget());

        // Enter percentage too high
        await tester.enterText(find.byType(TextFormField), '600');

        // Tap the action button
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        expect(find.text('Percentage cannot exceed +500%'), findsOneWidget);
      });

      testWidgets('should accept valid percentage values', (tester) async {
        await tester.pumpWidget(createWidget());

        // Mock successful operation
        when(
          () => mockBulkBudgetService.adjustBudgetsByPercentage(
            budgetIds: any(named: 'budgetIds'),
            percentageChange: any(named: 'percentageChange'),
          ),
        ).thenAnswer(
          (_) async => BulkOperationResult.success(processedCount: 3),
        );

        // Enter valid percentage
        await tester.enterText(find.byType(TextFormField), '10.5');

        // Tap the action button
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Should not show validation errors
        expect(find.text('Percentage is required'), findsNothing);
        expect(find.text('Please enter a valid percentage'), findsNothing);
        expect(find.text('Percentage cannot be -100% or lower'), findsNothing);
        expect(find.text('Percentage cannot exceed +500%'), findsNothing);
      });
    });

    group('Operation Execution', () {
      testWidgets('should call adjust percentage operation', (tester) async {
        await tester.pumpWidget(createWidget());

        // Mock successful operation
        when(
          () => mockBulkBudgetService.adjustBudgetsByPercentage(
            budgetIds: any(named: 'budgetIds'),
            percentageChange: any(named: 'percentageChange'),
          ),
        ).thenAnswer(
          (_) async => BulkOperationResult.success(processedCount: 3),
        );

        // Enter percentage and submit
        await tester.enterText(find.byType(TextFormField), '15');
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Wait for async operation
        await tester.pumpAndSettle();

        verify(
          () => mockBulkBudgetService.adjustBudgetsByPercentage(
            budgetIds: ['budget1', 'budget2', 'budget3'],
            percentageChange: 15,
          ),
        ).called(1);
      });

      testWidgets('should call activate operation', (tester) async {
        await tester.pumpWidget(createWidget());

        // Mock successful operation
        when(
          () => mockBulkBudgetService.changeBudgetStatus(
            budgetIds: any(named: 'budgetIds'),
            isActive: any(named: 'isActive'),
          ),
        ).thenAnswer(
          (_) async => BulkOperationResult.success(processedCount: 3),
        );

        // Select activate option
        await tester.tap(find.byType(RadioListTile<BulkOperationType>).at(2));
        await tester.pump();

        // Submit
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Wait for async operation
        await tester.pumpAndSettle();

        verify(
          () => mockBulkBudgetService.changeBudgetStatus(
            budgetIds: ['budget1', 'budget2', 'budget3'],
            isActive: true,
          ),
        ).called(1);
      });

      testWidgets('should call deactivate operation', (tester) async {
        await tester.pumpWidget(createWidget());

        // Mock successful operation
        when(
          () => mockBulkBudgetService.changeBudgetStatus(
            budgetIds: any(named: 'budgetIds'),
            isActive: any(named: 'isActive'),
          ),
        ).thenAnswer(
          (_) async => BulkOperationResult.success(processedCount: 3),
        );

        // Select deactivate option
        await tester.tap(find.byType(RadioListTile<BulkOperationType>).at(1));
        await tester.pump();

        // Submit
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Wait for async operation
        await tester.pumpAndSettle();

        verify(
          () => mockBulkBudgetService.changeBudgetStatus(
            budgetIds: ['budget1', 'budget2', 'budget3'],
            isActive: false,
          ),
        ).called(1);
      });

      testWidgets('should call delete operation', (tester) async {
        // Set a larger test surface to accommodate the dialog
        tester.view.physicalSize = const Size(1200, 1600);
        tester.view.devicePixelRatio = 1.0;

        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Mock successful operation
        when(
          () => mockBulkBudgetService.deleteBudgets(
            budgetIds: any(named: 'budgetIds'),
          ),
        ).thenAnswer(
          (_) async => BulkOperationResult.success(processedCount: 3),
        );

        // Find and scroll to the delete option
        final deleteRadio = find.byType(RadioListTile<BulkOperationType>).at(3);
        await tester.ensureVisible(deleteRadio);
        await tester.tap(deleteRadio, warnIfMissed: false);
        await tester.pump();

        // Find and tap the submit button
        final submitButton = find.byType(FilledButton);
        await tester.ensureVisible(submitButton);
        await tester.tap(submitButton, warnIfMissed: false);
        await tester.pump();

        // Wait for async operation
        await tester.pumpAndSettle();

        verify(
          () => mockBulkBudgetService.deleteBudgets(
            budgetIds: ['budget1', 'budget2', 'budget3'],
          ),
        ).called(1);

        // Reset test view
        addTearDown(() {
          tester.view.resetPhysicalSize();
          tester.view.resetDevicePixelRatio();
        });
      });
    });

    group('Loading State', () {
      testWidgets('should show loading indicator during operation', (
        tester,
      ) async {
        // Set a larger test surface to accommodate the dialog
        tester.view.physicalSize = const Size(1200, 1600);
        tester.view.devicePixelRatio = 1.0;

        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Create a completer to control when the mock returns
        when(
          () => mockBulkBudgetService.adjustBudgetsByPercentage(
            budgetIds: any(named: 'budgetIds'),
            percentageChange: any(named: 'percentageChange'),
          ),
        ).thenAnswer((_) async {
          // Simulate delay
          await Future<void>.delayed(const Duration(milliseconds: 100));
          return BulkOperationResult.success(processedCount: 3);
        });

        // Enter percentage and submit
        await tester.enterText(find.byType(TextFormField), '10');
        final submitButton = find.byType(FilledButton);
        await tester.ensureVisible(submitButton);
        await tester.tap(submitButton, warnIfMissed: false);
        await tester.pump();

        // Should show loading indicator
        expect(find.byType(CircularProgressIndicator), findsOneWidget);

        // Should disable buttons
        final cancelButton = tester.widget<TextButton>(
          find.widgetWithText(TextButton, 'Cancel'),
        );
        expect(cancelButton.onPressed, isNull);

        // Wait for the async operation to complete to clean up timers
        await tester.pumpAndSettle();

        // Reset test view
        addTearDown(() {
          tester.view.resetPhysicalSize();
          tester.view.resetDevicePixelRatio();
        });
      });

      testWidgets('should hide loading indicator after operation completes', (
        tester,
      ) async {
        await tester.pumpWidget(createWidget());

        // Mock successful operation
        when(
          () => mockBulkBudgetService.adjustBudgetsByPercentage(
            budgetIds: any(named: 'budgetIds'),
            percentageChange: any(named: 'percentageChange'),
          ),
        ).thenAnswer(
          (_) async => BulkOperationResult.success(processedCount: 3),
        );

        // Enter percentage and submit
        await tester.enterText(find.byType(TextFormField), '10');
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Wait for operation to complete
        await tester.pumpAndSettle();

        // Loading indicator should be gone
        expect(find.byType(CircularProgressIndicator), findsNothing);
      });
    });

    group('Error Handling', () {
      testWidgets('should show error snackbar when operation fails', (
        tester,
      ) async {
        // Set a larger test surface to accommodate the dialog
        tester.view.physicalSize = const Size(1200, 1600);
        tester.view.devicePixelRatio = 1.0;

        await tester.pumpWidget(createWidget());
        await tester.pumpAndSettle();

        // Mock failed operation
        when(
          () => mockBulkBudgetService.adjustBudgetsByPercentage(
            budgetIds: any(named: 'budgetIds'),
            percentageChange: any(named: 'percentageChange'),
          ),
        ).thenThrow(Exception('Operation failed'));

        // Enter percentage and submit
        await tester.enterText(find.byType(TextFormField), '10');
        final submitButton = find.byType(FilledButton);
        await tester.ensureVisible(submitButton);
        await tester.tap(submitButton, warnIfMissed: false);
        await tester.pump();

        // Wait for operation to complete
        await tester.pumpAndSettle();

        // Should show error message - look for it anywhere in the widget tree
        expect(find.text('Bulk operation failed'), findsOneWidget);

        // Reset test view
        addTearDown(() {
          tester.view.resetPhysicalSize();
          tester.view.resetDevicePixelRatio();
        });
      });
    });

    group('Success Messages', () {
      testWidgets('should complete adjust operation successfully', (
        tester,
      ) async {
        // Set a larger test surface to accommodate the dialog
        tester.view.physicalSize = const Size(1200, 1600);
        tester.view.devicePixelRatio = 1.0;

        var operationCompleted = false;

        final widget = TestWrapper.createTestWidget(
          BulkBudgetOperationsDialog(
            selectedBudgetIds: const ['budget1', 'budget2', 'budget3'],
            onOperationComplete: () {
              operationCompleted = true;
            },
          ),
          overrides: overrides,
        );

        await tester.pumpWidget(widget);
        await tester.pumpAndSettle();

        // Mock successful operation
        when(
          () => mockBulkBudgetService.adjustBudgetsByPercentage(
            budgetIds: any(named: 'budgetIds'),
            percentageChange: any(named: 'percentageChange'),
          ),
        ).thenAnswer(
          (_) async => BulkOperationResult.success(processedCount: 3),
        );

        // Enter percentage and submit
        await tester.enterText(find.byType(TextFormField), '10');
        final submitButton = find.byType(FilledButton);
        await tester.ensureVisible(submitButton);
        await tester.tap(submitButton, warnIfMissed: false);
        await tester.pump();

        // Wait for operation to complete
        await tester.pumpAndSettle();

        // Should complete the operation and call callback
        expect(operationCompleted, isTrue);

        // Dialog should be closed (no longer in widget tree)
        expect(find.byType(BulkBudgetOperationsDialog), findsNothing);

        // Reset test view
        addTearDown(() {
          tester.view.resetPhysicalSize();
          tester.view.resetDevicePixelRatio();
        });
      });

      testWidgets('should complete activate operation successfully', (
        tester,
      ) async {
        // Set a larger test surface to accommodate the dialog
        tester.view.physicalSize = const Size(1200, 1600);
        tester.view.devicePixelRatio = 1.0;

        var operationCompleted = false;

        final widget = TestWrapper.createTestWidget(
          BulkBudgetOperationsDialog(
            selectedBudgetIds: const ['budget1', 'budget2', 'budget3'],
            onOperationComplete: () {
              operationCompleted = true;
            },
          ),
          overrides: overrides,
        );

        await tester.pumpWidget(widget);
        await tester.pumpAndSettle();

        // Mock successful operation
        when(
          () => mockBulkBudgetService.changeBudgetStatus(
            budgetIds: any(named: 'budgetIds'),
            isActive: any(named: 'isActive'),
          ),
        ).thenAnswer(
          (_) async => BulkOperationResult.success(processedCount: 3),
        );

        // Select activate and submit
        final activateRadio = find
            .byType(RadioListTile<BulkOperationType>)
            .at(2);
        await tester.ensureVisible(activateRadio);
        await tester.tap(activateRadio, warnIfMissed: false);
        await tester.pump();

        final submitButton = find.byType(FilledButton);
        await tester.ensureVisible(submitButton);
        await tester.tap(submitButton, warnIfMissed: false);
        await tester.pump();

        // Wait for operation to complete
        await tester.pumpAndSettle();

        // Should complete the operation and call callback
        expect(operationCompleted, isTrue);

        // Dialog should be closed (no longer in widget tree)
        expect(find.byType(BulkBudgetOperationsDialog), findsNothing);

        // Reset test view
        addTearDown(() {
          tester.view.resetPhysicalSize();
          tester.view.resetDevicePixelRatio();
        });
      });
    });

    group('Navigation', () {
      testWidgets('should close dialog on cancel', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog<void>(
                    context: context,
                    builder: (context) => BulkBudgetOperationsDialog(
                      selectedBudgetIds: const ['budget1'],
                      onOperationComplete: () {},
                    ),
                  );
                },
                child: const Text('Open Dialog'),
              ),
            ),
            overrides: overrides,
          ),
        );

        // Open dialog
        await tester.tap(find.text('Open Dialog'));
        await tester.pumpAndSettle();

        // Tap cancel
        await tester.tap(find.text('Cancel'));
        await tester.pumpAndSettle();

        // Dialog should be closed
        expect(find.byType(BulkBudgetOperationsDialog), findsNothing);
      });

      testWidgets('should close dialog after successful operation', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog<void>(
                    context: context,
                    builder: (context) => BulkBudgetOperationsDialog(
                      selectedBudgetIds: const ['budget1'],
                      onOperationComplete: () {},
                    ),
                  );
                },
                child: const Text('Open Dialog'),
              ),
            ),
            overrides: overrides,
          ),
        );

        // Mock successful operation
        when(
          () => mockBulkBudgetService.adjustBudgetsByPercentage(
            budgetIds: any(named: 'budgetIds'),
            percentageChange: any(named: 'percentageChange'),
          ),
        ).thenAnswer(
          (_) async => BulkOperationResult.success(processedCount: 1),
        );

        // Open dialog
        await tester.tap(find.text('Open Dialog'));
        await tester.pumpAndSettle();

        // Enter percentage and submit
        await tester.enterText(find.byType(TextFormField), '10');
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Wait for operation to complete
        await tester.pumpAndSettle();

        // Dialog should be closed
        expect(find.byType(BulkBudgetOperationsDialog), findsNothing);
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle empty budget list', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BulkBudgetOperationsDialog(
              selectedBudgetIds: const [],
              onOperationComplete: () {},
            ),
            overrides: overrides,
          ),
        );

        expect(find.text('0 budgets selected'), findsOneWidget);
      });

      testWidgets('should handle single budget', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BulkBudgetOperationsDialog(
              selectedBudgetIds: const ['budget1'],
              onOperationComplete: () {},
            ),
            overrides: overrides,
          ),
        );

        expect(find.text('1 budget selected'), findsOneWidget);
      });

      testWidgets('should accept negative percentages within bounds', (
        tester,
      ) async {
        await tester.pumpWidget(createWidget());

        // Mock successful operation
        when(
          () => mockBulkBudgetService.adjustBudgetsByPercentage(
            budgetIds: any(named: 'budgetIds'),
            percentageChange: any(named: 'percentageChange'),
          ),
        ).thenAnswer(
          (_) async => BulkOperationResult.success(processedCount: 3),
        );

        // Enter valid negative percentage
        await tester.enterText(find.byType(TextFormField), '-50.5');
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Wait for async operation
        await tester.pumpAndSettle();

        verify(
          () => mockBulkBudgetService.adjustBudgetsByPercentage(
            budgetIds: ['budget1', 'budget2', 'budget3'],
            percentageChange: -50.5,
          ),
        ).called(1);
      });

      testWidgets('should accept decimal percentages', (tester) async {
        await tester.pumpWidget(createWidget());

        // Mock successful operation
        when(
          () => mockBulkBudgetService.adjustBudgetsByPercentage(
            budgetIds: any(named: 'budgetIds'),
            percentageChange: any(named: 'percentageChange'),
          ),
        ).thenAnswer(
          (_) async => BulkOperationResult.success(processedCount: 3),
        );

        // Enter decimal percentage
        await tester.enterText(find.byType(TextFormField), '12.75');
        await tester.tap(find.byType(FilledButton));
        await tester.pump();

        // Wait for async operation
        await tester.pumpAndSettle();

        verify(
          () => mockBulkBudgetService.adjustBudgetsByPercentage(
            budgetIds: ['budget1', 'budget2', 'budget3'],
            percentageChange: 12.75,
          ),
        ).called(1);
      });
    });
  });
}
