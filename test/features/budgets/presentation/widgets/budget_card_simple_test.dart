import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/models/budget_progress.dart';
import 'package:budapp/data/repositories/interfaces/budget_repository.dart';
import 'package:budapp/data/repositories/interfaces/transaction_repository.dart';
import 'package:budapp/features/budgets/presentation/widgets/budget_card.dart';
import 'package:budapp/features/budgets/services/budget_progress_service.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';

// Mock classes for testing
class MockCurrencyFormatter extends Mock implements CurrencyFormatter {}

class MockBudgetRepository extends Mock implements BudgetRepository {}

class MockTransactionRepository extends Mock
    implements ITransactionRepository {}

class MockBudgetProgressService extends Mo<PERSON> implements BudgetProgressService {}

/// Helper function to safely pump and settle with timeout protection
Future<void> safelyPumpAndSettle(
  WidgetTester tester, {
  Duration timeout = const Duration(milliseconds: 100),
}) async {
  try {
    // Use a very short timeout to avoid hanging
    await tester.pumpAndSettle(timeout);
  } on Object {
    // If pumpAndSettle times out or throws any error, just pump once to render the widget
    await tester.pump();
    await tester.pump(const Duration(milliseconds: 16)); // One frame
  }
}

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(
      Budget(
        id: 'test',
        userId: 'test',
        type: BudgetType.expense,
        plannedAmountCents: 100000,
        currentAmountCents: 50000,
        period: BudgetPeriod.monthly,
        periodStart: DateTime(2024, 1, 1),
        categoryId: 'test',
        isActive: true,
        schemaVersion: 1,
        createdAt: DateTime(2024, 1, 1),
        updatedAt: DateTime(2024, 1, 1),
      ),
    );
    registerFallbackValue(DateTime(2024, 1, 1));
  });

  group('BudgetCard', () {
    late Budget testBudget;
    late MockCurrencyFormatter mockCurrencyFormatter;
    late MockBudgetRepository mockBudgetRepository;
    late MockTransactionRepository mockTransactionRepository;
    late MockBudgetProgressService mockBudgetProgressService;
    late List<Override> providerOverrides;

    setUp(() {
      // Test budget setup first
      testBudget = Budget(
        id: '1',
        userId: 'user1',
        type: BudgetType.expense,
        plannedAmountCents: 100000, // $1000
        currentAmountCents: 50000, // $500
        period: BudgetPeriod.monthly,
        periodStart: DateTime(2024, 1, 1),
        categoryId: 'cat1',
        isActive: true,
        schemaVersion: 1,
        createdAt: DateTime(2024, 1, 1),
        updatedAt: DateTime(2024, 1, 1),
      );

      // Initialize mocks
      mockCurrencyFormatter = MockCurrencyFormatter();
      mockBudgetRepository = MockBudgetRepository();
      mockTransactionRepository = MockTransactionRepository();
      mockBudgetProgressService = MockBudgetProgressService();

      // Setup default mock behaviors
      when(
        () => mockCurrencyFormatter.formatAmount(any()),
      ).thenReturn(r'$10.00');
      when(
        () => mockCurrencyFormatter.formatAmount(100000),
      ).thenReturn(r'$1,000.00');
      when(
        () => mockCurrencyFormatter.formatAmount(50000),
      ).thenReturn(r'$500.00');
      when(
        () => mockCurrencyFormatter.formatAmount(5000),
      ).thenReturn(r'$50.00');
      when(() => mockCurrencyFormatter.formatAmount(0)).thenReturn(r'$0.00');

      // Setup budget repository mock
      when(
        () => mockBudgetRepository.getBudgetById(any()),
      ).thenAnswer((_) async => testBudget);

      // Setup transaction repository mock
      when(
        () => mockTransactionRepository.watchTransactionsByMonthAndCategories(
          userId: any(named: 'userId'),
          month: any(named: 'month'),
          categoryIds: any(named: 'categoryIds'),
        ),
      ).thenAnswer((_) => Stream.value([]));

      // Setup budget progress service mock
      when(
        () => mockBudgetProgressService.calculateBudgetProgress(any(), any()),
      ).thenAnswer(
        (_) async => BudgetProgress.fromBudgetAndSpent(
          budgetId: '1',
          budgetAmount: 1000,
          spentAmount: 500,
        ),
      );

      // Setup comprehensive provider overrides
      providerOverrides = [
        currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
        budgetRepositoryProvider.overrideWithValue(mockBudgetRepository),
        transactionRepositoryProvider.overrideWithValue(
          mockTransactionRepository,
        ),
        budgetProgressServiceProvider.overrideWithValue(
          mockBudgetProgressService,
        ),
      ];
    });

    group('Basic Display', () {
      testWidgets('renders without crashing', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: testBudget,
              currentDate: DateTime(2024, 1, 15), // Fixed date for stable tests
            ),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Verify basic structure
        expect(find.byType(BudgetCard), findsOneWidget);
        expect(find.byType(Card), findsOneWidget);
        expect(find.byType(InkWell), findsOneWidget);
      });

      testWidgets('displays budget type icons correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: testBudget, currentDate: DateTime(2024, 1, 15)),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Verify expense icon
        expect(find.byIcon(Icons.trending_down), findsOneWidget);
        expect(find.byIcon(Icons.schedule), findsOneWidget);
      });

      testWidgets('displays income budget type correctly', (tester) async {
        final incomeBudget = testBudget.copyWith(type: BudgetType.income);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: incomeBudget,
              currentDate: DateTime(2024, 1, 15),
            ),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Verify income icon
        expect(find.byIcon(Icons.trending_up), findsOneWidget);
      });

      testWidgets('displays monthly period correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: testBudget, currentDate: DateTime(2024, 1, 15)),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Verify period labels
        expect(find.text('Monthly'), findsOneWidget);
        expect(find.text('Expense'), findsOneWidget);
      });

      testWidgets('displays yearly period correctly', (tester) async {
        final yearlyBudget = testBudget.copyWith(period: BudgetPeriod.yearly);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: yearlyBudget,
              currentDate: DateTime(2024, 1, 15),
            ),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Verify yearly period
        expect(find.text('Yearly'), findsOneWidget);
      });
    });

    group('User Interactions', () {
      testWidgets('handles tap interaction', (tester) async {
        var tapCalled = false;

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: testBudget,
              currentDate: DateTime(2024, 1, 15),
              onTap: () => tapCalled = true,
            ),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Tap the card
        await tester.tap(find.byType(InkWell));
        await tester.pump();

        expect(tapCalled, isTrue);
      });

      testWidgets('shows popup menu with edit and delete options', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: testBudget,
              currentDate: DateTime(2024, 1, 15),
              onEdit: () {},
              onDelete: () {},
            ),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Verify popup menu is present
        expect(find.byType(PopupMenuButton<String>), findsOneWidget);

        // Open popup menu
        await tester.tap(find.byType(PopupMenuButton<String>));
        await safelyPumpAndSettle(tester);

        // Verify menu items
        expect(find.text('Edit'), findsOneWidget);
        expect(find.text('Delete'), findsOneWidget);
        expect(find.byIcon(Icons.edit), findsOneWidget);
        expect(find.byIcon(Icons.delete), findsOneWidget);
      });

      testWidgets('handles edit menu item tap', (tester) async {
        var editCalled = false;

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: testBudget,
              onEdit: () => editCalled = true,
              onDelete: () {},
            ),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Open popup menu
        await tester.tap(find.byType(PopupMenuButton<String>));
        await safelyPumpAndSettle(tester);

        // Tap edit menu item
        await tester.tap(find.text('Edit'));
        await tester.pump();

        expect(editCalled, isTrue);
      });

      testWidgets('handles delete menu item tap', (tester) async {
        var deleteCalled = false;

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: testBudget,
              onEdit: () {},
              onDelete: () => deleteCalled = true,
            ),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Open popup menu
        await tester.tap(find.byType(PopupMenuButton<String>));
        await safelyPumpAndSettle(tester);

        // Tap delete menu item
        await tester.tap(find.text('Delete'));
        await tester.pump();

        expect(deleteCalled, isTrue);
      });

      testWidgets('hides popup menu when no callbacks provided', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: testBudget, currentDate: DateTime(2024, 1, 15)),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Should not show popup menu
        expect(find.byType(PopupMenuButton<String>), findsNothing);
      });
    });

    group('Progress States', () {
      testWidgets('displays basic card structure', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: testBudget, currentDate: DateTime(2024, 1, 15)),
            overrides: providerOverrides,
          ),
        );

        await tester.pump(); // Use single pump to avoid hanging

        // Should show basic card structure
        expect(find.byType(BudgetCard), findsOneWidget);
        expect(find.byType(Card), findsOneWidget);
      });
    });

    group('Edge Cases', () {
      testWidgets('handles budget with null category', (tester) async {
        final noCategoryBudget = testBudget.copyWith(categoryId: null);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: noCategoryBudget,
              currentDate: DateTime(2024, 1, 15),
            ),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Should not crash
        expect(find.byType(BudgetCard), findsOneWidget);
      });

      testWidgets('handles budget with null parent', (tester) async {
        final noParentBudget = testBudget.copyWith(parentBudgetId: null);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: noParentBudget,
              currentDate: DateTime(2024, 1, 15),
            ),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Should not crash
        expect(find.byType(BudgetCard), findsOneWidget);
      });

      testWidgets('handles zero amount budget', (tester) async {
        final zeroAmountBudget = testBudget.copyWith(plannedAmountCents: 0);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: zeroAmountBudget,
              currentDate: DateTime(2024, 1, 15),
            ),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Should not crash
        expect(find.byType(BudgetCard), findsOneWidget);
      });

      testWidgets('handles very large amount budget', (tester) async {
        final largeAmountBudget = testBudget.copyWith(
          plannedAmountCents: 99999999,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: largeAmountBudget,
              currentDate: DateTime(2024, 1, 15),
            ),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Should not crash
        expect(find.byType(BudgetCard), findsOneWidget);
      });
    });

    group('Layout Structure', () {
      testWidgets('has proper card layout', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: testBudget, currentDate: DateTime(2024, 1, 15)),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Verify layout structure
        expect(find.byType(Card), findsOneWidget);
        expect(find.byType(InkWell), findsOneWidget);
        expect(find.byType(Padding), findsAtLeast(1));
        expect(find.byType(Column), findsAtLeast(1));
        expect(find.byType(Row), findsAtLeast(1));
      });

      testWidgets('displays budget name correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: testBudget, currentDate: DateTime(2024, 1, 15)),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Budget name should be displayed
        expect(find.text(testBudget.getDisplayName()), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('provides semantic labels', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: testBudget,
              currentDate: DateTime(2024, 1, 15),
              onTap: () {},
            ),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // Verify accessibility elements
        expect(find.byType(InkWell), findsOneWidget);
        expect(find.byType(Card), findsOneWidget);
      });

      testWidgets('supports keyboard navigation', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: testBudget,
              currentDate: DateTime(2024, 1, 15),
              onTap: () {},
            ),
            overrides: providerOverrides,
          ),
        );

        await safelyPumpAndSettle(tester);

        // InkWell should be focusable
        expect(find.byType(InkWell), findsOneWidget);
      });
    });
  });
}
