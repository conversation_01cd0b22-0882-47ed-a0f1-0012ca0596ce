import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/models/budget_progress.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/budgets/data/models/category_budget_info.dart';
import 'package:budapp/features/budgets/presentation/widgets/budget_progress_bar.dart';
import 'package:budapp/features/budgets/presentation/widgets/category_budget_card.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/widgets/common/app_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';

// Mock classes
class MockCurrencyFormatter extends Mock implements CurrencyFormatter {}

void main() {
  group('CategoryBudgetCard', () {
    late Category testCategory;
    late Budget testBudget;
    late BudgetProgress testProgress;
    late CategoryBudgetInfo categoryBudgetInfo;
    late CategoryBudgetInfo noBudgetInfo;
    late CategoryBudgetInfo overBudgetInfo;
    late CategoryBudgetInfo warningInfo;
    late MockCurrencyFormatter mockCurrencyFormatter;
    late TextEditingController budgetController;

    late bool onTapCalled;
    late bool onEditBudgetCalled;
    late bool onSelectionToggleCalled;
    late String? budgetAmountChanged;

    setUp(() {
      testCategory = Category(
        id: 'cat1',
        userId: 'user1',
        name: 'Food',
        type: CategoryType.expense,
        icon: 'restaurant',
        color: '#FF5722',
        isActive: true,
        schemaVersion: 1,
        createdAt: DateTime(2024, 1, 1),
        updatedAt: DateTime(2024, 1, 1),
      );

      testBudget = Budget(
        id: 'budget1',
        userId: 'user1',
        type: BudgetType.expense,
        plannedAmountCents: 100000, // $1000
        currentAmountCents: 50000, // $500
        period: BudgetPeriod.monthly,
        periodStart: DateTime(2024, 1, 1),
        categoryId: testCategory.id,
        isActive: true,
        schemaVersion: 1,
        createdAt: DateTime(2024, 1, 1),
        updatedAt: DateTime(2024, 1, 1),
      );

      testProgress = BudgetProgress.fromBudgetAndSpent(
        budgetId: testBudget.id,
        budgetAmount: 1000,
        spentAmount: 500,
      );

      categoryBudgetInfo = CategoryBudgetInfo(
        category: testCategory,
        budget: testBudget,
        progress: testProgress,
        actualSpent: 500,
        isFallbackBudget: false,
      );

      noBudgetInfo = CategoryBudgetInfo(
        category: testCategory,
        budget: null,
        progress: null,
        actualSpent: 200,
        isFallbackBudget: false,
      );

      overBudgetInfo = CategoryBudgetInfo(
        category: testCategory,
        budget: testBudget,
        progress: BudgetProgress.fromBudgetAndSpent(
          budgetId: testBudget.id,
          budgetAmount: 1000,
          spentAmount: 1200,
        ),
        actualSpent: 1200,
        isFallbackBudget: false,
      );

      warningInfo = CategoryBudgetInfo(
        category: testCategory,
        budget: testBudget,
        progress: BudgetProgress.fromBudgetAndSpent(
          budgetId: testBudget.id,
          budgetAmount: 1000,
          spentAmount: 800,
        ),
        actualSpent: 800,
        isFallbackBudget: false,
      );

      mockCurrencyFormatter = MockCurrencyFormatter();
      when(
        () => mockCurrencyFormatter.formatAmount(any()),
      ).thenReturn(r'$5000');
      when(() => mockCurrencyFormatter.currencyCode).thenReturn('USD');

      budgetController = TextEditingController();

      onTapCalled = false;
      onEditBudgetCalled = false;
      onSelectionToggleCalled = false;
      budgetAmountChanged = null;
    });

    tearDown(() {
      budgetController.dispose();
    });

    group('Basic Display', () {
      testWidgets('displays category with budget information', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(
              categoryBudgetInfo: categoryBudgetInfo,
              onTap: () => onTapCalled = true,
              onEditBudget: () => onEditBudgetCalled = true,
            ),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify category name and icon
        expect(find.text('Food'), findsOneWidget);
        expect(find.byType(Icon), findsAtLeast(1));

        // Verify spent amount
        expect(find.textContaining('Spent:'), findsOneWidget);

        // Verify budget information
        expect(find.textContaining('Budget:'), findsOneWidget);
        expect(find.textContaining('Remaining:'), findsOneWidget);
        expect(find.byType(BudgetProgressBar), findsOneWidget);

        // Verify progress percentage
        expect(find.textContaining('% used'), findsOneWidget);

        // Verify edit button
        expect(find.byIcon(Icons.edit_outlined), findsOneWidget);
      });

      testWidgets('displays category without budget information', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(
              categoryBudgetInfo: noBudgetInfo,
              onTap: () => onTapCalled = true,
            ),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify category name
        expect(find.text('Food'), findsOneWidget);

        // Verify no budget message
        expect(find.textContaining('No budget set'), findsOneWidget);

        // Should not show budget information
        expect(find.textContaining('Budget:'), findsNothing);
        expect(find.textContaining('Remaining:'), findsNothing);
        expect(find.byType(BudgetProgressBar), findsNothing);

        // Should not show edit button
        expect(find.byIcon(Icons.edit_outlined), findsNothing);
      });

      testWidgets('displays category with no spending and no budget', (
        tester,
      ) async {
        final noSpendingInfo = noBudgetInfo.copyWith(actualSpent: 0);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(
              categoryBudgetInfo: noSpendingInfo,
              onTap: () => onTapCalled = true,
            ),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify no spending message
        expect(
          find.textContaining('No budget set and no spending'),
          findsOneWidget,
        );
        expect(find.byIcon(Icons.schedule_outlined), findsOneWidget);
      });
    });

    group('Budget Status Display', () {
      testWidgets('displays over budget status', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(categoryBudgetInfo: overBudgetInfo),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify over budget status
        expect(find.text('Over budget'), findsOneWidget);
      });

      testWidgets('displays warning status', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(categoryBudgetInfo: warningInfo),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify warning status
        expect(find.text('Warning'), findsOneWidget);
      });

      testWidgets('displays fallback budget indicator', (tester) async {
        final fallbackInfo = categoryBudgetInfo.copyWith(
          isFallbackBudget: true,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(categoryBudgetInfo: fallbackInfo),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify fallback indicator
        expect(find.byIcon(Icons.history), findsOneWidget);
      });
    });

    group('Selection Mode', () {
      testWidgets('displays checkbox in selection mode', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(
              categoryBudgetInfo: categoryBudgetInfo,
              isSelectionMode: true,
              isSelected: false,
              onSelectionToggle: () => onSelectionToggleCalled = true,
            ),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify checkbox is displayed
        expect(find.byType(Checkbox), findsOneWidget);

        // Verify edit button is hidden
        expect(find.byIcon(Icons.edit_outlined), findsNothing);
      });

      testWidgets('displays selected state correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(
              categoryBudgetInfo: categoryBudgetInfo,
              isSelectionMode: true,
              isSelected: true,
              onSelectionToggle: () => onSelectionToggleCalled = true,
            ),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify selected state
        final checkbox = tester.widget<Checkbox>(find.byType(Checkbox));
        expect(checkbox.value, isTrue);

        // Verify card background color change
        expect(find.byType(Card), findsOneWidget);
      });

      testWidgets('handles selection toggle', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(
              categoryBudgetInfo: categoryBudgetInfo,
              isSelectionMode: true,
              isSelected: false,
              onSelectionToggle: () => onSelectionToggleCalled = true,
            ),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Tap checkbox
        await tester.tap(find.byType(Checkbox));
        await tester.pump();

        expect(onSelectionToggleCalled, isTrue);
      });

      testWidgets('handles tap in selection mode', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(
              categoryBudgetInfo: categoryBudgetInfo,
              isSelectionMode: true,
              isSelected: false,
              onTap: () => onTapCalled = true,
              onSelectionToggle: () => onSelectionToggleCalled = true,
            ),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Tap card
        await tester.tap(find.byType(InkWell));
        await tester.pump();

        // Should call selection toggle, not onTap
        expect(onSelectionToggleCalled, isTrue);
        expect(onTapCalled, isFalse);
      });
    });

    group('Edit Mode', () {
      testWidgets('displays editable budget amount in edit mode', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(
              categoryBudgetInfo: categoryBudgetInfo,
              isEditMode: true,
              budgetController: budgetController,
              onBudgetAmountChanged: (value) => budgetAmountChanged = value,
            ),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify editable field is displayed
        expect(find.byType(AppTextFormField), findsOneWidget);
        expect(find.text('Budget Amount'), findsOneWidget);
        expect(find.text('USD'), findsOneWidget);
      });

      testWidgets('handles budget amount changes', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(
              categoryBudgetInfo: categoryBudgetInfo,
              isEditMode: true,
              budgetController: budgetController,
              onBudgetAmountChanged: (value) => budgetAmountChanged = value,
            ),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Enter new amount
        await tester.enterText(find.byType(AppTextFormField), '1500');
        await tester.pump();

        expect(budgetAmountChanged, '1500');
      });

      testWidgets('disables tap in edit mode', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(
              categoryBudgetInfo: categoryBudgetInfo,
              isEditMode: true,
              budgetController: budgetController,
              onTap: () => onTapCalled = true,
            ),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Tap card
        await tester.tap(find.byType(InkWell));
        await tester.pump();

        // Should not call onTap
        expect(onTapCalled, isFalse);
      });

      testWidgets('validates budget amount input', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(
              categoryBudgetInfo: categoryBudgetInfo,
              isEditMode: true,
              budgetController: budgetController,
            ),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Find the form field
        final formField = tester.widget<AppTextFormField>(
          find.byType(AppTextFormField),
        );

        // Test validation
        expect(formField.validator!(''), 'Required');
        expect(formField.validator!('invalid'), 'Invalid amount');
        expect(formField.validator!('-100'), 'Invalid amount');
        expect(formField.validator!('100'), null);
      });
    });

    group('User Interactions', () {
      testWidgets('handles card tap', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(
              categoryBudgetInfo: categoryBudgetInfo,
              onTap: () => onTapCalled = true,
            ),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Tap card - use first InkWell (the main card area)
        await tester.tap(find.byType(InkWell).first);
        await tester.pump();

        expect(onTapCalled, isTrue);
      });

      testWidgets('handles edit button tap', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(
              categoryBudgetInfo: categoryBudgetInfo,
              onEditBudget: () => onEditBudgetCalled = true,
            ),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Tap edit button
        await tester.tap(find.byIcon(Icons.edit_outlined));
        await tester.pump();

        expect(onEditBudgetCalled, isTrue);
      });

      testWidgets('works without callbacks', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(categoryBudgetInfo: categoryBudgetInfo),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Should not crash without callbacks
        expect(find.byType(CategoryBudgetCard), findsOneWidget);
      });
    });

    group('Icon and Color Display', () {
      testWidgets('displays custom category icon and color', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(categoryBudgetInfo: categoryBudgetInfo),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify icon is displayed
        expect(find.byType(Icon), findsAtLeast(1));
      });

      testWidgets('displays default icon for category without custom icon', (
        tester,
      ) async {
        final categoryWithoutIcon = testCategory.copyWith(icon: null);
        final infoWithoutIcon = categoryBudgetInfo.copyWith(
          category: categoryWithoutIcon,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(categoryBudgetInfo: infoWithoutIcon),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Should use default icon
        expect(find.byIcon(Icons.trending_down), findsOneWidget);
      });

      testWidgets('displays income category icon', (tester) async {
        final incomeCategory = testCategory.copyWith(
          type: CategoryType.income,
          icon: null,
        );
        final incomeInfo = categoryBudgetInfo.copyWith(
          category: incomeCategory,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(categoryBudgetInfo: incomeInfo),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Should use income icon
        expect(find.byIcon(Icons.trending_up), findsOneWidget);
      });

      testWidgets('handles invalid custom color gracefully', (tester) async {
        final categoryWithInvalidColor = testCategory.copyWith(
          color: 'invalid-color',
        );
        final infoWithInvalidColor = categoryBudgetInfo.copyWith(
          category: categoryWithInvalidColor,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(categoryBudgetInfo: infoWithInvalidColor),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Should not crash and should use default color
        expect(find.byType(CategoryBudgetCard), findsOneWidget);
      });
    });

    group('Edge Cases', () {
      testWidgets('handles zero remaining amount', (tester) async {
        final zeroRemainingInfo = categoryBudgetInfo.copyWith(
          progress: BudgetProgress.fromBudgetAndSpent(
            budgetId: testBudget.id,
            budgetAmount: 1000,
            spentAmount: 1000,
          ),
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(categoryBudgetInfo: zeroRemainingInfo),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Should not crash
        expect(find.byType(CategoryBudgetCard), findsOneWidget);
      });

      testWidgets('handles negative remaining amount', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(categoryBudgetInfo: overBudgetInfo),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Should display over budget info
        expect(find.text('Over budget'), findsOneWidget);
      });

      testWidgets('handles category with null values', (tester) async {
        final minimalCategory = testCategory.copyWith(icon: null, color: null);
        final minimalInfo = categoryBudgetInfo.copyWith(
          category: minimalCategory,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(categoryBudgetInfo: minimalInfo),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Should not crash
        expect(find.byType(CategoryBudgetCard), findsOneWidget);
      });
    });

    group('Layout and Styling', () {
      testWidgets('applies proper card styling', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(categoryBudgetInfo: categoryBudgetInfo),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify card structure - expect 2 InkWells (card + edit button)
        expect(find.byType(Card), findsOneWidget);
        expect(find.byType(InkWell), findsNWidgets(2));
        expect(find.byType(Padding), findsAtLeast(1));
      });

      testWidgets('uses proper spacing and layout', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(categoryBudgetInfo: categoryBudgetInfo),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify layout structure
        expect(find.byType(Column), findsAtLeast(1));
        expect(find.byType(Row), findsAtLeast(1));
        expect(find.byType(SizedBox), findsAtLeast(1));
      });
    });

    group('Accessibility', () {
      testWidgets('provides proper semantic information', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(
              categoryBudgetInfo: categoryBudgetInfo,
              onTap: () => onTapCalled = true,
            ),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify accessibility elements - expect 2 InkWells (card + edit button)
        expect(find.byType(InkWell), findsNWidgets(2));
        expect(find.byType(Tooltip), findsOneWidget);
      });

      testWidgets('provides tooltip for edit button', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CategoryBudgetCard(
              categoryBudgetInfo: categoryBudgetInfo,
              onEditBudget: () => onEditBudgetCalled = true,
            ),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify tooltip
        final iconButton = tester.widget<IconButton>(find.byType(IconButton));
        expect(iconButton.tooltip, 'Edit Budget');
      });
    });
  });
}
