import 'dart:async';

import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/models/budget_progress.dart';
import 'package:budapp/features/budgets/presentation/widgets/budget_card.dart';
import 'package:budapp/features/budgets/presentation/widgets/budget_progress_bar.dart';
import 'package:budapp/features/budgets/providers/budget_providers.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';
// Note: Using provider-level mocking instead of service-level mocking
// This follows 2024 Riverpod testing best practices

// Mock classes - Following modern Riverpod testing patterns
class MockCurrencyFormatter extends Mock implements CurrencyFormatter {}

void main() {
  setUpAll(() {
    registerFallbackValue(
      Budget(
        id: 'test',
        userId: 'test',
        type: BudgetType.expense,
        plannedAmountCents: 100000,
        currentAmountCents: 50000,
        period: BudgetPeriod.monthly,
        periodStart: DateTime(2024, 1, 1),
        isActive: true,
        schemaVersion: 1,
        createdAt: DateTime(2024, 1, 1),
        updatedAt: DateTime(2024, 1, 1),
      ),
    );
    registerFallbackValue(DateTime.now());
  });

  group('BudgetCard', () {
    late Budget testBudget;
    late BudgetProgress testProgress;
    late MockCurrencyFormatter mockCurrencyFormatter;
    late DateTime fixedDateTime;
    // Removed service-level mocks - using provider-level mocking instead
    late bool onTapCalled;
    late bool onEditCalled;
    late bool onDeleteCalled;

    setUp(() {
      fixedDateTime = DateTime(2024, 1, 15); // Use a fixed date for consistency

      testBudget = Budget(
        id: '1',
        userId: 'user1',
        type: BudgetType.expense,
        plannedAmountCents: 100000, // $1000
        currentAmountCents: 50000, // $500
        period: BudgetPeriod.monthly,
        periodStart: DateTime(2024, 1, 1),
        categoryId: 'cat1',
        isActive: true,
        schemaVersion: 1,
        createdAt: DateTime(2024, 1, 1),
        updatedAt: DateTime(2024, 1, 1),
      );

      testProgress = BudgetProgress.fromBudgetAndSpent(
        budgetId: '1',
        budgetAmount: 1000,
        spentAmount: 500,
      );

      mockCurrencyFormatter = MockCurrencyFormatter();
      when(
        () => mockCurrencyFormatter.formatAmount(any()),
      ).thenReturn(r'$500.00');

      // Service mocks removed - using provider-level overrides instead

      onTapCalled = false;
      onEditCalled = false;
      onDeleteCalled = false;
    });

    // Helper functions for provider-level mocking (modern Riverpod pattern)
    List<Override> createSuccessOverrides() {
      return [
        currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
        budgetProgressProvider(
          testBudget.id,
          fixedDateTime,
        ).overrideWith((ref) async => testProgress),
      ];
    }

    List<Override> createLoadingOverrides() {
      return [
        currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
        budgetProgressProvider(testBudget.id, fixedDateTime).overrideWith((
          ref,
        ) async {
          // Create a completer that won't complete immediately for loading state
          final completer = Completer<BudgetProgress>();
          // Don't complete it to keep it in loading state
          return completer.future;
        }),
      ];
    }

    List<Override> createErrorOverrides(String errorMessage) {
      return [
        currencyFormatterProvider.overrideWithValue(mockCurrencyFormatter),
        budgetProgressProvider(
          testBudget.id,
          fixedDateTime,
        ).overrideWith((ref) async => throw Exception(errorMessage)),
      ];
    }

    group('Basic Display', () {
      testWidgets('displays budget information correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: testBudget,
              currentDate: fixedDateTime,
              onTap: () => onTapCalled = true,
            ),
            overrides: createSuccessOverrides(),
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        // Verify budget name is displayed
        expect(find.text(testBudget.getDisplayName()), findsOneWidget);

        // Verify progress bar is displayed
        expect(find.byType(BudgetProgressBar), findsOneWidget);

        // Verify metadata is displayed
        expect(find.byIcon(Icons.trending_down), findsOneWidget);
        expect(find.byIcon(Icons.schedule), findsOneWidget);
      });

      testWidgets('displays formatted amount correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: testBudget, currentDate: fixedDateTime),
            overrides: createSuccessOverrides(),
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        // Verify currency formatter is called and displayed
        verify(
          () =>
              mockCurrencyFormatter.formatAmount(testBudget.plannedAmountCents),
        ).called(greaterThan(0));
        expect(find.text(r'$500.00'), findsAtLeast(1));
      });
    });

    group('Budget Type Display', () {
      testWidgets('displays expense budget type correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: testBudget, currentDate: fixedDateTime),
            overrides: createSuccessOverrides(),
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        expect(find.byIcon(Icons.trending_down), findsOneWidget);
        expect(find.text('Expense'), findsOneWidget);
      });

      testWidgets('displays income budget type correctly', (tester) async {
        final incomeBudget = testBudget.copyWith(type: BudgetType.income);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: incomeBudget, currentDate: fixedDateTime),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
              budgetProgressProvider(
                incomeBudget.id,
                fixedDateTime,
              ).overrideWith((ref) async => testProgress),
            ],
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        expect(find.byIcon(Icons.trending_up), findsOneWidget);
        expect(find.text('Income'), findsOneWidget);
      });
    });

    group('Budget Period Display', () {
      testWidgets('displays monthly period correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: testBudget, currentDate: fixedDateTime),
            overrides: createSuccessOverrides(),
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        expect(find.text('Monthly'), findsOneWidget);
      });

      testWidgets('displays yearly period correctly', (tester) async {
        final yearlyBudget = testBudget.copyWith(period: BudgetPeriod.yearly);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: yearlyBudget, currentDate: fixedDateTime),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
              budgetProgressProvider(
                yearlyBudget.id,
                fixedDateTime,
              ).overrideWith((ref) async => testProgress),
            ],
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        expect(find.text('Yearly'), findsOneWidget);
      });
    });

    group('Progress States', () {
      testWidgets('displays loading state correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: testBudget, currentDate: fixedDateTime),
            overrides: createLoadingOverrides(),
          ),
        );

        // Use pump() for loading states - no async operations needed
        await tester.pump(); // Initial render with loading state

        expect(find.byType(LinearProgressIndicator), findsOneWidget);
      });

      testWidgets('displays error state correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: testBudget, currentDate: fixedDateTime),
            overrides: createErrorOverrides('Test error'),
          ),
        );

        // Use pump() for error states - no async operations needed
        await tester.pump(); // Initial render with error state

        expect(find.text('Error loading progress'), findsOneWidget);
      });

      testWidgets('displays progress data correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: testBudget, currentDate: fixedDateTime),
            overrides: createSuccessOverrides(),
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        expect(find.byType(BudgetProgressBar), findsOneWidget);
        expect(find.textContaining('Spent'), findsOneWidget);
        expect(find.textContaining('Remaining'), findsOneWidget);
      });
    });

    group('User Interactions', () {
      testWidgets('handles tap interaction correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: testBudget,
              currentDate: fixedDateTime,
              onTap: () => onTapCalled = true,
            ),
            overrides: createSuccessOverrides(),
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        // Tap the card
        await tester.tap(find.byType(InkWell));
        await tester.pump();

        expect(onTapCalled, isTrue);
      });

      testWidgets('displays popup menu when edit/delete callbacks provided', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: testBudget,
              currentDate: fixedDateTime,
              onEdit: () => onEditCalled = true,
              onDelete: () => onDeleteCalled = true,
            ),
            overrides: createSuccessOverrides(),
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        expect(find.byType(PopupMenuButton<String>), findsOneWidget);
      });

      testWidgets('handles edit menu item tap', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: testBudget,
              currentDate: fixedDateTime,
              onEdit: () => onEditCalled = true,
              onDelete: () => onDeleteCalled = true,
            ),
            overrides: createSuccessOverrides(),
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        // Open popup menu
        await tester.tap(find.byType(PopupMenuButton<String>));
        await tester.pumpAndSettle(); // Wait for menu to fully open

        // Tap edit menu item
        await tester.tap(find.text('Edit'));
        await tester.pumpAndSettle(); // Wait for menu action to complete

        expect(onEditCalled, isTrue);
      });

      testWidgets('handles delete menu item tap', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: testBudget,
              currentDate: fixedDateTime,
              onEdit: () => onEditCalled = true,
              onDelete: () => onDeleteCalled = true,
            ),
            overrides: createSuccessOverrides(),
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        // Open popup menu
        await tester.tap(find.byType(PopupMenuButton<String>));
        await tester.pumpAndSettle(); // Wait for menu to fully open

        // Tap delete menu item
        await tester.tap(find.text('Delete'));
        await tester.pumpAndSettle(); // Wait for menu action to complete

        expect(onDeleteCalled, isTrue);
      });

      testWidgets('hides popup menu when no callbacks provided', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: testBudget, currentDate: fixedDateTime),
            overrides: createSuccessOverrides(),
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        expect(find.byType(PopupMenuButton<String>), findsNothing);
      });
    });

    group('Edge Cases', () {
      testWidgets('handles budget with null category', (tester) async {
        final noCategoryBudget = testBudget.copyWith(categoryId: null);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: noCategoryBudget, currentDate: fixedDateTime),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
              budgetProgressProvider(
                noCategoryBudget.id,
                fixedDateTime,
              ).overrideWith((ref) async => testProgress),
            ],
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        // Should not crash
        expect(find.byType(BudgetCard), findsOneWidget);
      });

      testWidgets('handles budget with null parent', (tester) async {
        final noParentBudget = testBudget.copyWith(parentBudgetId: null);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: noParentBudget, currentDate: fixedDateTime),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
              budgetProgressProvider(
                noParentBudget.id,
                fixedDateTime,
              ).overrideWith((ref) async => testProgress),
            ],
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        // Should not crash
        expect(find.byType(BudgetCard), findsOneWidget);
      });

      testWidgets('handles zero amount budget', (tester) async {
        final zeroAmountBudget = testBudget.copyWith(plannedAmountCents: 0);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: zeroAmountBudget, currentDate: fixedDateTime),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
              budgetProgressProvider(
                zeroAmountBudget.id,
                fixedDateTime,
              ).overrideWith((ref) async => testProgress),
            ],
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        // Should not crash
        expect(find.byType(BudgetCard), findsOneWidget);
      });

      testWidgets('handles very large amount budget', (tester) async {
        final largeAmountBudget = testBudget.copyWith(
          plannedAmountCents: 99999999,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(budget: largeAmountBudget, currentDate: fixedDateTime),
            overrides: [
              currencyFormatterProvider.overrideWithValue(
                mockCurrencyFormatter,
              ),
              budgetProgressProvider(
                largeAmountBudget.id,
                fixedDateTime,
              ).overrideWith((ref) async => testProgress),
            ],
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        // Should not crash
        expect(find.byType(BudgetCard), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('provides semantic labels for screen readers', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: testBudget,
              currentDate: fixedDateTime,
              onTap: () => onTapCalled = true,
            ),
            overrides: createSuccessOverrides(),
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        // Verify that the card is accessible
        expect(find.byType(InkWell), findsOneWidget);
        expect(find.byType(Card), findsOneWidget);
      });

      testWidgets('supports keyboard navigation', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetCard(
              budget: testBudget,
              currentDate: fixedDateTime,
              onTap: () => onTapCalled = true,
            ),
            overrides: createSuccessOverrides(),
          ),
        );

        // Use pump() + runAsync pattern instead of pumpAndSettle
        await tester.pump(); // Initial render
        await tester.runAsync(() async {
          await Future<void>.delayed(const Duration(milliseconds: 50));
        });
        await tester.pump(); // Process async updates

        // The InkWell should be focusable
        final inkWell = find.byType(InkWell);
        expect(inkWell, findsOneWidget);
      });
    });
  });
}
