import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/budgets/data/models/category_budget_info.dart';
import 'package:budapp/features/budgets/presentation/widgets/budget_overview_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/test_wrapper.dart';

void main() {
  group('BudgetOverviewCard', () {
    late CategoryTypeBudgetSummary expenseSummary;
    late CategoryTypeBudgetSummary incomeSummary;
    late CategoryTypeBudgetSummary emptySummary;
    late CategoryTypeBudgetSummary overBudgetSummary;
    late CategoryTypeBudgetSummary warningSummary;
    late bool onTapCalled;

    setUp(() {
      expenseSummary = const CategoryTypeBudgetSummary(
        categoryType: CategoryType.expense,
        totalBudgetAmount: 1000,
        totalActualAmount: 500,
        currency: 'USD',
        categoriesWithBudgets: 3,
        totalCategories: 5,
      );

      incomeSummary = const CategoryTypeBudgetSummary(
        categoryType: CategoryType.income,
        totalBudgetAmount: 2000,
        totalActualAmount: 1800,
        currency: 'USD',
        categoriesWithBudgets: 2,
        totalCategories: 3,
      );

      emptySummary = const CategoryTypeBudgetSummary(
        categoryType: CategoryType.expense,
        totalBudgetAmount: 0,
        totalActualAmount: 0,
        currency: 'USD',
        categoriesWithBudgets: 0,
        totalCategories: 5,
      );

      overBudgetSummary = const CategoryTypeBudgetSummary(
        categoryType: CategoryType.expense,
        totalBudgetAmount: 1000,
        totalActualAmount: 1200,
        currency: 'USD',
        categoriesWithBudgets: 3,
        totalCategories: 5,
      );

      warningSummary = const CategoryTypeBudgetSummary(
        categoryType: CategoryType.expense,
        totalBudgetAmount: 1000,
        totalActualAmount: 800, // 80%
        currency: 'USD',
        categoriesWithBudgets: 3,
        totalCategories: 5,
      );

      onTapCalled = false;
    });

    group('Basic Display', () {
      testWidgets('displays expense budget overview correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(
              summary: expenseSummary,
              onTap: () => onTapCalled = true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify header elements
        expect(find.byIcon(Icons.trending_down), findsOneWidget);
        expect(find.text('Expense Budgets'), findsOneWidget);
        expect(find.text('3/5'), findsOneWidget);

        // Verify amount columns
        expect(find.text('Budgeted'), findsOneWidget);
        expect(find.text('Spent'), findsOneWidget);
        expect(find.text('Remaining'), findsOneWidget);

        // Verify formatted amounts
        expect(find.text(r'$1000.00'), findsOneWidget);
        expect(
          find.text(r'$500.00'),
          findsNWidgets(2),
        ); // Spent and Remaining are both $500.00

        // Verify progress percentage
        expect(find.text('50.0% used'), findsOneWidget);
      });

      testWidgets('displays income budget overview correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(
              summary: incomeSummary,
              onTap: () => onTapCalled = true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify header elements
        expect(find.byIcon(Icons.trending_up), findsOneWidget);
        expect(find.text('Income Budgets'), findsOneWidget);
        expect(find.text('2/3'), findsOneWidget);

        // Verify income-specific labels
        expect(find.text('Earned'), findsOneWidget);
      });

      testWidgets('displays empty state when no budgets set', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(
              summary: emptySummary,
              onTap: () => onTapCalled = true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify empty state
        expect(find.text('No budgets set'), findsOneWidget);
        expect(
          find.text(
            'Set budgets for your expense categories to track your spending.',
          ),
          findsOneWidget,
        );

        // Should not show budget amounts or progress
        expect(find.text('Budgeted'), findsNothing);
        expect(find.text('Spent'), findsNothing);
        expect(find.text('Remaining'), findsNothing);
      });
    });

    group('Progress Display', () {
      testWidgets('displays progress bar with correct width', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: expenseSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Verify progress bar is present
        expect(find.byType(LayoutBuilder), findsOneWidget);

        // Verify progress percentage text
        expect(find.text('50.0% used'), findsOneWidget);
      });

      testWidgets('displays over budget status correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: overBudgetSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Verify over budget status
        expect(find.text('Over budget'), findsOneWidget);
        expect(find.text('120.0% used'), findsOneWidget);
      });

      testWidgets('displays warning status correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: warningSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Verify warning status
        expect(find.text('Warning'), findsOneWidget);
        expect(find.text('80.0% used'), findsOneWidget);
      });

      testWidgets('clamps progress bar to 100% width', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: overBudgetSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Should not crash with over 100% progress
        expect(find.byType(BudgetOverviewCard), findsOneWidget);
        expect(find.text('120.0% used'), findsOneWidget);
      });
    });

    group('Color Theming', () {
      testWidgets('uses correct gradient colors for expense budgets', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: expenseSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Verify gradient container is present
        final containerFinder = find.byType(Container);
        expect(containerFinder, findsAtLeast(1));

        // Find the main container with gradient
        final containers = tester.widgetList<Container>(containerFinder);
        expect(
          containers.any(
            (container) =>
                container.decoration is BoxDecoration &&
                (container.decoration as BoxDecoration?)?.gradient != null,
          ),
          isTrue,
        );
      });

      testWidgets('uses correct gradient colors for income budgets', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: incomeSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Verify gradient container is present
        final containerFinder = find.byType(Container);
        expect(containerFinder, findsAtLeast(1));

        // Find the main container with gradient
        final containers = tester.widgetList<Container>(containerFinder);
        expect(
          containers.any(
            (container) =>
                container.decoration is BoxDecoration &&
                (container.decoration as BoxDecoration?)?.gradient != null,
          ),
          isTrue,
        );
      });

      testWidgets('uses warning colors for warning state', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: warningSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Should render without error
        expect(find.byType(BudgetOverviewCard), findsOneWidget);
      });

      testWidgets('uses error colors for over budget state', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: overBudgetSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Should render without error
        expect(find.byType(BudgetOverviewCard), findsOneWidget);
      });
    });

    group('Currency Formatting', () {
      testWidgets('formats USD currency correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: expenseSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Verify USD formatting
        expect(find.text(r'$1000.00'), findsOneWidget);
        expect(
          find.text(r'$500.00'),
          findsNWidgets(2),
        ); // Spent and Remaining are both $500.00
      });

      testWidgets('formats EUR currency correctly', (tester) async {
        final eurSummary = expenseSummary.copyWith(currency: 'EUR');

        await tester.pumpWidget(
          TestWrapper.createTestWidget(BudgetOverviewCard(summary: eurSummary)),
        );

        await tester.pumpAndSettle();

        // Verify EUR formatting
        expect(find.text('€1000.00'), findsOneWidget);
        expect(
          find.text('€500.00'),
          findsNWidgets(2),
        ); // Spent and Remaining are both €500.00
      });

      testWidgets('formats GBP currency correctly', (tester) async {
        final gbpSummary = expenseSummary.copyWith(currency: 'GBP');

        await tester.pumpWidget(
          TestWrapper.createTestWidget(BudgetOverviewCard(summary: gbpSummary)),
        );

        await tester.pumpAndSettle();

        // Verify GBP formatting
        expect(find.text('£1000.00'), findsOneWidget);
        expect(
          find.text('£500.00'),
          findsNWidgets(2),
        ); // Spent and Remaining are both £500.00
      });

      testWidgets('formats unknown currency correctly', (tester) async {
        final unknownSummary = expenseSummary.copyWith(currency: 'JPY');

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: unknownSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Verify unknown currency formatting
        expect(find.text('JPY 1000.00'), findsOneWidget);
        expect(
          find.text('JPY 500.00'),
          findsNWidgets(2),
        ); // Spent and Remaining are both JPY 500.00
      });
    });

    group('User Interactions', () {
      testWidgets('handles tap interaction correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(
              summary: expenseSummary,
              onTap: () => onTapCalled = true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Tap the card
        await tester.tap(find.byType(InkWell));
        await tester.pump();

        expect(onTapCalled, isTrue);
      });

      testWidgets('works without tap callback', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: expenseSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Should not crash without tap callback
        expect(find.byType(BudgetOverviewCard), findsOneWidget);
      });
    });

    group('Layout and Styling', () {
      testWidgets('applies proper card elevation and styling', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: expenseSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Verify card is present
        expect(find.byType(Card), findsOneWidget);
        expect(find.byType(InkWell), findsOneWidget);
      });

      testWidgets('uses proper spacing and padding', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: expenseSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Verify layout structure
        expect(find.byType(Column), findsAtLeast(1));
        expect(find.byType(Row), findsAtLeast(1));
        expect(find.byType(SizedBox), findsAtLeast(1));
      });

      testWidgets('displays category count badge when budgets exist', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: expenseSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Verify count badge is displayed
        expect(find.text('3/5'), findsOneWidget);
      });

      testWidgets('hides category count badge when no budgets', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: emptySummary),
          ),
        );

        await tester.pumpAndSettle();

        // Should not show count badge
        expect(find.text('0/5'), findsNothing);
      });
    });

    group('Edge Cases', () {
      testWidgets('handles zero budget amount', (tester) async {
        final zeroSummary = expenseSummary.copyWith(totalBudgetAmount: 0);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: zeroSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Should not crash
        expect(find.byType(BudgetOverviewCard), findsOneWidget);
      });

      testWidgets('handles negative remaining amount', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: overBudgetSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Should display negative remaining amount
        expect(find.text(r'$-200.00'), findsOneWidget);
      });

      testWidgets('handles very large amounts', (tester) async {
        final largeSummary = expenseSummary.copyWith(
          totalBudgetAmount: 999999.99,
          totalActualAmount: 500000,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: largeSummary),
          ),
        );

        await tester.pumpAndSettle();

        // Should not crash with large amounts
        expect(find.byType(BudgetOverviewCard), findsOneWidget);
      });

      testWidgets('handles zero categories', (tester) async {
        final zeroCategories = expenseSummary.copyWith(
          categoriesWithBudgets: 0,
          totalCategories: 0,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: zeroCategories),
          ),
        );

        await tester.pumpAndSettle();

        // Should not crash
        expect(find.byType(BudgetOverviewCard), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('provides proper semantic information', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(
              summary: expenseSummary,
              onTap: () => onTapCalled = true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify accessibility elements
        expect(find.byType(InkWell), findsOneWidget);
        expect(find.byType(Card), findsOneWidget);
      });

      testWidgets('supports keyboard navigation', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(
              summary: expenseSummary,
              onTap: () => onTapCalled = true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // InkWell should be focusable
        final inkWell = find.byType(InkWell);
        expect(inkWell, findsOneWidget);
      });

      testWidgets('provides proper contrast for text elements', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            BudgetOverviewCard(summary: expenseSummary),
          ),
        );

        await tester.pumpAndSettle();

        // All text should be white on colored background
        final textWidgets = tester.widgetList<Text>(find.byType(Text));
        expect(textWidgets.isNotEmpty, isTrue);
      });
    });
  });
}
