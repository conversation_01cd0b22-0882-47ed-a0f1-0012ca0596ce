import 'package:budapp/features/profile/services/profile_validators.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ProfileValidators Tests', () {
    group('validateDisplayName', () {
      test('should return null for valid display name', () {
        expect(ProfileValidators.validateDisplayName('<PERSON>'), null);
        expect(ProfileValidators.validateDisplayName('<PERSON><PERSON>Smith'), null);
        expect(ProfileValidators.validateDisplayName('User-123'), null);
        expect(ProfileValidators.validateDisplayName('AB'), null);
        expect(ProfileValidators.validateDisplayName('A' * 50), null);
      });

      test('should return error for null or empty display name', () {
        expect(
          ProfileValidators.validateDisplayName(null),
          'Display name is required',
        );
        expect(
          ProfileValidators.validateDisplayName(''),
          'Display name is required',
        );
        expect(
          ProfileValidators.validateDisplayName('   '),
          'Display name is required',
        );
      });

      test('should return error for display name too short', () {
        expect(
          ProfileValidators.validateDisplayName('A'),
          'Display name must be at least 2 characters long',
        );
        expect(
          ProfileValidators.validateDisplayName(' A '),
          'Display name must be at least 2 characters long',
        );
      });

      test('should return error for display name too long', () {
        final longName = 'A' * 51;
        expect(
          ProfileValidators.validateDisplayName(longName),
          'Display name must be less than 50 characters',
        );
      });

      test('should return error for invalid characters', () {
        expect(
          ProfileValidators.validateDisplayName('John@Doe'),
          'Display name can only contain letters, numbers, spaces, hyphens, and underscores',
        );
        expect(
          ProfileValidators.validateDisplayName('User#123'),
          'Display name can only contain letters, numbers, spaces, hyphens, and underscores',
        );
        expect(
          ProfileValidators.validateDisplayName('Test!'),
          'Display name can only contain letters, numbers, spaces, hyphens, and underscores',
        );
      });

      test('should handle whitespace properly', () {
        expect(ProfileValidators.validateDisplayName('  John Doe  '), null);
        expect(ProfileValidators.validateDisplayName('\tJane\n'), null);
      });
    });

    group('validateEmail', () {
      test('should return null for valid email addresses', () {
        expect(ProfileValidators.validateEmail('<EMAIL>'), null);
        expect(ProfileValidators.validateEmail('<EMAIL>'), null);
        expect(ProfileValidators.validateEmail('<EMAIL>'), null);
        expect(ProfileValidators.validateEmail('<EMAIL>'), null);
        expect(
          ProfileValidators.validateEmail('<EMAIL>'),
          null,
        );
      });

      test('should return error for null or empty email', () {
        expect(ProfileValidators.validateEmail(null), 'Email is required');
        expect(ProfileValidators.validateEmail(''), 'Email is required');
        expect(ProfileValidators.validateEmail('   '), 'Email is required');
      });

      test('should return error for invalid email formats', () {
        expect(
          ProfileValidators.validateEmail('invalid-email'),
          'Please enter a valid email address',
        );
        expect(
          ProfileValidators.validateEmail('test@'),
          'Please enter a valid email address',
        );
        expect(
          ProfileValidators.validateEmail('@example.com'),
          'Please enter a valid email address',
        );
        expect(
          ProfileValidators.validateEmail('test@com'),
          'Please enter a valid email address',
        );
        expect(
          ProfileValidators.validateEmail('test@.com'),
          'Please enter a valid email address',
        );
      });

      test('should accept some edge cases as valid', () {
        // The regex allows consecutive dots in local part
        expect(ProfileValidators.validateEmail('<EMAIL>'), null);
      });

      test('should return error for email too long', () {
        final longEmail = '${'a' * 250}@example.com';
        expect(
          ProfileValidators.validateEmail(longEmail),
          'Email address is too long',
        );
      });

      test('should handle whitespace in email', () {
        expect(ProfileValidators.validateEmail('  <EMAIL>  '), null);
      });
    });

    group('validatePassword', () {
      test('should return null for valid strong password', () {
        expect(ProfileValidators.validatePassword('StrongPass123!'), null);
        expect(ProfileValidators.validatePassword('MyPassword#456'), null);
        expect(ProfileValidators.validatePassword('ComplexP@ss789'), null);
      });

      test('should return error for null or empty password', () {
        expect(
          ProfileValidators.validatePassword(null),
          'Password is required',
        );
        expect(ProfileValidators.validatePassword(''), 'Password is required');
      });

      test('should return error for password too short', () {
        expect(
          ProfileValidators.validatePassword('Short1!'),
          'Password must be at least 8 characters long',
        );
        expect(
          ProfileValidators.validatePassword('A1!'),
          'Password must be at least 8 characters long',
        );
      });

      test('should return error for password too long', () {
        final longPassword = 'A1!${'a' * 126}';
        expect(
          ProfileValidators.validatePassword(longPassword),
          'Password must be less than 128 characters',
        );
      });

      test('should return error for missing uppercase letter', () {
        expect(
          ProfileValidators.validatePassword('lowercase123!'),
          'Password must contain at least one uppercase letter',
        );
      });

      test('should return error for missing lowercase letter', () {
        expect(
          ProfileValidators.validatePassword('UPPERCASE123!'),
          'Password must contain at least one lowercase letter',
        );
      });

      test('should return error for missing number', () {
        expect(
          ProfileValidators.validatePassword('Password!'),
          'Password must contain at least one number',
        );
      });

      test('should return error for missing special character', () {
        expect(
          ProfileValidators.validatePassword('Password123'),
          'Password must contain at least one special character',
        );
      });

      test('should accept various special characters', () {
        const specialChars = r'!@#$%^&*(),.?":{}|<>';
        for (var i = 0; i < specialChars.length; i++) {
          final char = specialChars[i];
          expect(ProfileValidators.validatePassword('Password1$char'), null);
        }
      });
    });

    group('validatePasswordConfirmation', () {
      test('should return null when passwords match', () {
        expect(
          ProfileValidators.validatePasswordConfirmation(
            'StrongPass123!',
            'StrongPass123!',
          ),
          null,
        );
      });

      test('should return error for null or empty confirmation', () {
        expect(
          ProfileValidators.validatePasswordConfirmation(null, 'password'),
          'Password confirmation is required',
        );
        expect(
          ProfileValidators.validatePasswordConfirmation('', 'password'),
          'Password confirmation is required',
        );
      });

      test('should return error when passwords do not match', () {
        expect(
          ProfileValidators.validatePasswordConfirmation(
            'Different123!',
            'StrongPass123!',
          ),
          'Passwords do not match',
        );
      });

      test('should handle null original password', () {
        expect(
          ProfileValidators.validatePasswordConfirmation(
            'StrongPass123!',
            null,
          ),
          'Passwords do not match',
        );
      });
    });

    group('validateCurrentPassword', () {
      test('should return null for non-empty password', () {
        expect(ProfileValidators.validateCurrentPassword('anypassword'), null);
        expect(ProfileValidators.validateCurrentPassword('123'), null);
      });

      test('should return error for null or empty password', () {
        expect(
          ProfileValidators.validateCurrentPassword(null),
          'Current password is required',
        );
        expect(
          ProfileValidators.validateCurrentPassword(''),
          'Current password is required',
        );
      });
    });

    group('getPasswordStrength', () {
      test('should return 0 for empty password', () {
        expect(ProfileValidators.getPasswordStrength(''), 0);
      });

      test('should return correct scores for various combinations', () {
        expect(
          ProfileValidators.getPasswordStrength('password'),
          2,
        ); // length + lowercase
        expect(
          ProfileValidators.getPasswordStrength('Password'),
          3,
        ); // length + lowercase + uppercase
        expect(
          ProfileValidators.getPasswordStrength('Password1'),
          4,
        ); // length + lower + upper + numbers
        expect(
          ProfileValidators.getPasswordStrength('password1'),
          3,
        ); // length + lower + numbers
        expect(
          ProfileValidators.getPasswordStrength('PASSWORD1'),
          3,
        ); // length + upper + numbers
      });

      test('should return 5 for password with all requirements', () {
        expect(ProfileValidators.getPasswordStrength('Password1!'), 5);
        expect(ProfileValidators.getPasswordStrength('MyPass@123'), 5);
      });

      test('should handle edge cases', () {
        expect(
          ProfileValidators.getPasswordStrength('1234567'),
          1,
        ); // has numbers but not 8+ chars
        expect(
          ProfileValidators.getPasswordStrength('12345678'),
          2,
        ); // length + numbers
        expect(
          ProfileValidators.getPasswordStrength('ABCDEFGH'),
          2,
        ); // length + uppercase
        expect(
          ProfileValidators.getPasswordStrength('abcdefgh'),
          2,
        ); // length + lowercase
        expect(
          ProfileValidators.getPasswordStrength(r'!@#$%^&*'),
          2,
        ); // length + special
      });
    });

    group('getPasswordStrengthDescription', () {
      test('should return correct descriptions for all strength levels', () {
        expect(
          ProfileValidators.getPasswordStrengthDescription(0),
          'Very Weak',
        );
        expect(
          ProfileValidators.getPasswordStrengthDescription(1),
          'Very Weak',
        );
        expect(ProfileValidators.getPasswordStrengthDescription(2), 'Weak');
        expect(ProfileValidators.getPasswordStrengthDescription(3), 'Fair');
        expect(ProfileValidators.getPasswordStrengthDescription(4), 'Good');
        expect(ProfileValidators.getPasswordStrengthDescription(5), 'Strong');
      });

      test('should return Unknown for invalid strength levels', () {
        expect(ProfileValidators.getPasswordStrengthDescription(-1), 'Unknown');
        expect(ProfileValidators.getPasswordStrengthDescription(6), 'Unknown');
        expect(
          ProfileValidators.getPasswordStrengthDescription(100),
          'Unknown',
        );
      });
    });

    group('getPasswordStrengthColor', () {
      test('should return correct colors for all strength levels', () {
        expect(ProfileValidators.getPasswordStrengthColor(0), 'red');
        expect(ProfileValidators.getPasswordStrengthColor(1), 'red');
        expect(ProfileValidators.getPasswordStrengthColor(2), 'orange');
        expect(ProfileValidators.getPasswordStrengthColor(3), 'yellow');
        expect(ProfileValidators.getPasswordStrengthColor(4), 'green');
        expect(ProfileValidators.getPasswordStrengthColor(5), 'green');
      });

      test('should return gray for invalid strength levels', () {
        expect(ProfileValidators.getPasswordStrengthColor(-1), 'gray');
        expect(ProfileValidators.getPasswordStrengthColor(6), 'gray');
        expect(ProfileValidators.getPasswordStrengthColor(100), 'gray');
      });
    });

    group('validateProfileForm', () {
      test('should return no errors for valid profile data', () {
        final result = ProfileValidators.validateProfileForm(
          displayName: 'John Doe',
          email: '<EMAIL>',
        );

        expect(result['displayName'], null);
        expect(result['email'], null);
      });

      test('should return errors for invalid profile data', () {
        final result = ProfileValidators.validateProfileForm(
          displayName: null,
          email: 'invalid-email',
        );

        expect(result['displayName'], 'Display name is required');
        expect(result['email'], 'Please enter a valid email address');
      });

      test('should validate both fields independently', () {
        final result = ProfileValidators.validateProfileForm(
          displayName: 'A',
          email: '<EMAIL>',
        );

        expect(
          result['displayName'],
          'Display name must be at least 2 characters long',
        );
        expect(result['email'], null);
      });
    });

    group('validatePasswordChangeForm', () {
      test('should return no errors for valid password change data', () {
        final result = ProfileValidators.validatePasswordChangeForm(
          currentPassword: 'currentpass',
          newPassword: 'NewPassword123!',
          confirmPassword: 'NewPassword123!',
        );

        expect(result['currentPassword'], null);
        expect(result['newPassword'], null);
        expect(result['confirmPassword'], null);
      });

      test('should return errors for invalid password change data', () {
        final result = ProfileValidators.validatePasswordChangeForm(
          currentPassword: null,
          newPassword: 'weak',
          confirmPassword: 'different',
        );

        expect(result['currentPassword'], 'Current password is required');
        expect(
          result['newPassword'],
          'Password must be at least 8 characters long',
        );
        expect(result['confirmPassword'], 'Passwords do not match');
      });

      test('should validate all fields independently', () {
        final result = ProfileValidators.validatePasswordChangeForm(
          currentPassword: 'current',
          newPassword: 'weakpass',
          confirmPassword: 'weakpass',
        );

        expect(result['currentPassword'], null);
        expect(
          result['newPassword'],
          'Password must contain at least one uppercase letter',
        );
        expect(result['confirmPassword'], null);
      });
    });
  });
}
