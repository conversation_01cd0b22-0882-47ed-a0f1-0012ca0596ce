import 'package:budapp/data/models/user_profile.dart';
import 'package:budapp/data/repositories/interfaces/user_repository.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:budapp/features/profile/providers/profile_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockUserRepository extends Mock implements IUserRepository {}

class MockAuthService extends Mock implements AuthService {}

void main() {
  group('Profile Providers Comprehensive Tests', () {
    late ProviderContainer container;
    late MockUserRepository mockUserRepository;
    late MockAuthService mockAuthService;

    setUp(() {
      mockUserRepository = MockUserRepository();
      mockAuthService = MockAuthService();

      container = ProviderContainer(
        overrides: [
          userRepositoryProvider.overrideWithValue(mockUserRepository),
          authServiceProvider.overrideWithValue(mockAuthService),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('userProfile Provider', () {
      test('should return user profile from repository', () async {
        // Arrange
        final expectedProfile = UserProfile(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
        );
        when(
          () => mockUserRepository.getCurrentUserProfile(),
        ).thenAnswer((_) async => expectedProfile);

        // Act
        final result = await container.read(userProfileProvider.future);

        // Assert
        expect(result, equals(expectedProfile));
        verify(() => mockUserRepository.getCurrentUserProfile()).called(1);
      });

      test('should return null when no user profile exists', () async {
        // Arrange
        when(
          () => mockUserRepository.getCurrentUserProfile(),
        ).thenAnswer((_) async => null);

        // Act
        final result = await container.read(userProfileProvider.future);

        // Assert
        expect(result, isNull);
        verify(() => mockUserRepository.getCurrentUserProfile()).called(1);
      });

      test('should handle repository errors', () async {
        // Arrange
        when(
          () => mockUserRepository.getCurrentUserProfile(),
        ).thenThrow(Exception('Repository error'));

        // Act & Assert
        expect(
          () => container.read(userProfileProvider.future),
          throwsA(isA<Exception>()),
        );
        verify(() => mockUserRepository.getCurrentUserProfile()).called(1);
      });
    });

    group('ProfileUpdate Provider', () {
      test('should update display name successfully', () async {
        // Arrange
        const displayName = 'New Display Name';
        when(
          () => mockUserRepository.updateDisplayName(displayName),
        ).thenAnswer((_) async {});

        final notifier = container.read(profileUpdateProvider.notifier);

        // Act
        await notifier.updateDisplayName(displayName);

        // Assert
        final state = container.read(profileUpdateProvider);
        expect(state, isA<AsyncData<void>>());
        verify(
          () => mockUserRepository.updateDisplayName(displayName),
        ).called(1);
      });

      test('should handle display name update errors', () async {
        // Arrange
        const displayName = 'New Display Name';
        final exception = Exception('Update failed');
        when(
          () => mockUserRepository.updateDisplayName(displayName),
        ).thenThrow(exception);

        final notifier = container.read(profileUpdateProvider.notifier);

        // Act & Assert
        try {
          await notifier.updateDisplayName(displayName);
          fail('Expected exception to be thrown');
        } on Exception catch (e) {
          expect(e, equals(exception));
        }

        final state = container.read(profileUpdateProvider);
        expect(state, isA<AsyncError<void>>());
        expect(state.error, equals(exception));
      });

      test('should update email successfully', () async {
        // Arrange
        const email = '<EMAIL>';
        when(
          () => mockUserRepository.updateEmail(email),
        ).thenAnswer((_) async {});

        final notifier = container.read(profileUpdateProvider.notifier);

        // Act
        await notifier.updateEmail(email);

        // Assert
        final state = container.read(profileUpdateProvider);
        expect(state, isA<AsyncData<void>>());
        verify(() => mockUserRepository.updateEmail(email)).called(1);
      });

      test('should handle email update errors', () async {
        // Arrange
        const email = '<EMAIL>';
        final exception = Exception('Email update failed');
        when(() => mockUserRepository.updateEmail(email)).thenThrow(exception);

        final notifier = container.read(profileUpdateProvider.notifier);

        // Act & Assert
        try {
          await notifier.updateEmail(email);
          fail('Expected exception to be thrown');
        } on Exception catch (e) {
          expect(e, equals(exception));
        }

        final state = container.read(profileUpdateProvider);
        expect(state, isA<AsyncError<void>>());
        expect(state.error, equals(exception));
      });

      test('should update complete profile successfully', () async {
        // Arrange
        final userProfile = UserProfile(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Updated User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
        );
        when(
          () => mockUserRepository.updateUserProfile(userProfile),
        ).thenAnswer((_) async {});

        final notifier = container.read(profileUpdateProvider.notifier);

        // Act
        await notifier.updateProfile(userProfile);

        // Assert
        final state = container.read(profileUpdateProvider);
        expect(state, isA<AsyncData<void>>());
        verify(
          () => mockUserRepository.updateUserProfile(userProfile),
        ).called(1);
      });

      test('should handle complete profile update errors', () async {
        // Arrange
        final userProfile = UserProfile(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Updated User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
        );
        final exception = Exception('Profile update failed');
        when(
          () => mockUserRepository.updateUserProfile(userProfile),
        ).thenThrow(exception);

        final notifier = container.read(profileUpdateProvider.notifier);

        // Act & Assert
        try {
          await notifier.updateProfile(userProfile);
          fail('Expected exception to be thrown');
        } on Exception catch (e) {
          expect(e, equals(exception));
        }

        final state = container.read(profileUpdateProvider);
        expect(state, isA<AsyncError<void>>());
        expect(state.error, equals(exception));
      });

      test(
        'should invalidate userProfile provider after successful updates',
        () async {
          // Arrange
          const displayName = 'New Display Name';
          when(
            () => mockUserRepository.updateDisplayName(displayName),
          ).thenAnswer((_) async {});

          final notifier = container.read(profileUpdateProvider.notifier);

          // Act
          await notifier.updateDisplayName(displayName);

          // Assert - userProfile provider should be invalidated
          // This is tested by verifying the provider was called again
          verify(
            () => mockUserRepository.updateDisplayName(displayName),
          ).called(1);
        },
      );

      test('should complete operations successfully', () async {
        // Arrange
        const displayName = 'New Display Name';
        when(
          () => mockUserRepository.updateDisplayName(displayName),
        ).thenAnswer((_) async {});

        final notifier = container.read(profileUpdateProvider.notifier);

        // Act
        await notifier.updateDisplayName(displayName);

        // Assert - Should be in data state after completion
        final completedState = container.read(profileUpdateProvider);
        expect(completedState, isA<AsyncData<void>>());
        verify(
          () => mockUserRepository.updateDisplayName(displayName),
        ).called(1);
      });
    });

    group('PasswordChange Provider', () {
      test('should change password successfully', () async {
        // Arrange
        const currentPassword = 'current123';
        const newPassword = 'new123';
        when(
          () => mockAuthService.reauthenticate(currentPassword),
        ).thenAnswer((_) async {});
        when(
          () => mockAuthService.updatePassword(newPassword),
        ).thenAnswer((_) async {});

        final notifier = container.read(passwordChangeProvider.notifier);

        // Act
        await notifier.changePassword(
          currentPassword: currentPassword,
          newPassword: newPassword,
        );

        // Assert
        final state = container.read(passwordChangeProvider);
        expect(state, isA<AsyncData<void>>());
        verify(() => mockAuthService.reauthenticate(currentPassword)).called(1);
        verify(() => mockAuthService.updatePassword(newPassword)).called(1);
      });

      test('should handle reauthentication errors', () async {
        // Arrange
        const currentPassword = 'wrong123';
        const newPassword = 'new123';
        final exception = Exception('Reauthentication failed');
        when(
          () => mockAuthService.reauthenticate(currentPassword),
        ).thenThrow(exception);

        final notifier = container.read(passwordChangeProvider.notifier);

        // Act & Assert
        try {
          await notifier.changePassword(
            currentPassword: currentPassword,
            newPassword: newPassword,
          );
          fail('Expected exception to be thrown');
        } on Exception catch (e) {
          expect(e, equals(exception));
        }

        final state = container.read(passwordChangeProvider);
        expect(state, isA<AsyncError<void>>());
        expect(state.error, equals(exception));
        verify(() => mockAuthService.reauthenticate(currentPassword)).called(1);
        verifyNever(() => mockAuthService.updatePassword(any()));
      });

      test('should handle password update errors', () async {
        // Arrange
        const currentPassword = 'current123';
        const newPassword = 'new123';
        final exception = Exception('Password update failed');
        when(
          () => mockAuthService.reauthenticate(currentPassword),
        ).thenAnswer((_) async {});
        when(
          () => mockAuthService.updatePassword(newPassword),
        ).thenThrow(exception);

        final notifier = container.read(passwordChangeProvider.notifier);

        // Act & Assert
        try {
          await notifier.changePassword(
            currentPassword: currentPassword,
            newPassword: newPassword,
          );
          fail('Expected exception to be thrown');
        } on Exception catch (e) {
          expect(e, equals(exception));
        }

        final state = container.read(passwordChangeProvider);
        expect(state, isA<AsyncError<void>>());
        expect(state.error, equals(exception));
        verify(() => mockAuthService.reauthenticate(currentPassword)).called(1);
        verify(() => mockAuthService.updatePassword(newPassword)).called(1);
      });

      test('should complete password change operations successfully', () async {
        // Arrange
        const currentPassword = 'current123';
        const newPassword = 'new123';
        when(
          () => mockAuthService.reauthenticate(currentPassword),
        ).thenAnswer((_) async {});
        when(
          () => mockAuthService.updatePassword(newPassword),
        ).thenAnswer((_) async {});

        final notifier = container.read(passwordChangeProvider.notifier);

        // Act
        await notifier.changePassword(
          currentPassword: currentPassword,
          newPassword: newPassword,
        );

        // Assert - Should be in data state after completion
        final completedState = container.read(passwordChangeProvider);
        expect(completedState, isA<AsyncData<void>>());
        verify(() => mockAuthService.reauthenticate(currentPassword)).called(1);
        verify(() => mockAuthService.updatePassword(newPassword)).called(1);
      });
    });

    group('AccountDeletion Provider', () {
      test('should delete account successfully', () async {
        // Arrange
        const password = 'password123';
        when(
          () => mockAuthService.deleteAccountWithReauth(password),
        ).thenAnswer((_) async {});

        final notifier = container.read(accountDeletionProvider.notifier);

        // Act
        await notifier.deleteAccount(password);

        // Assert
        final state = container.read(accountDeletionProvider);
        expect(state, isA<AsyncData<void>>());
        verify(
          () => mockAuthService.deleteAccountWithReauth(password),
        ).called(1);
      });

      test('should handle account deletion errors', () async {
        // Arrange
        const password = 'wrong123';
        final exception = Exception('Account deletion failed');
        when(
          () => mockAuthService.deleteAccountWithReauth(password),
        ).thenThrow(exception);

        final notifier = container.read(accountDeletionProvider.notifier);

        // Act & Assert
        try {
          await notifier.deleteAccount(password);
          fail('Expected exception to be thrown');
        } on Exception catch (e) {
          expect(e, equals(exception));
        }

        final state = container.read(accountDeletionProvider);
        expect(state, isA<AsyncError<void>>());
        expect(state.error, equals(exception));
        verify(
          () => mockAuthService.deleteAccountWithReauth(password),
        ).called(1);
      });

      test(
        'should complete account deletion operations successfully',
        () async {
          // Arrange
          const password = 'password123';
          when(
            () => mockAuthService.deleteAccountWithReauth(password),
          ).thenAnswer((_) async {});

          final notifier = container.read(accountDeletionProvider.notifier);

          // Act
          await notifier.deleteAccount(password);

          // Assert - Should be in data state after completion
          final completedState = container.read(accountDeletionProvider);
          expect(completedState, isA<AsyncData<void>>());
          verify(
            () => mockAuthService.deleteAccountWithReauth(password),
          ).called(1);
        },
      );
    });

    group('PasswordReset Provider', () {
      test('should send password reset email successfully', () async {
        // Arrange
        const email = '<EMAIL>';
        when(
          () => mockAuthService.sendPasswordResetEmail(email: email),
        ).thenAnswer((_) async {});

        final notifier = container.read(passwordResetProvider.notifier);

        // Act
        await notifier.sendPasswordResetEmail(email);

        // Assert
        final state = container.read(passwordResetProvider);
        expect(state, isA<AsyncData<void>>());
        verify(
          () => mockAuthService.sendPasswordResetEmail(email: email),
        ).called(1);
      });

      test('should handle password reset email errors', () async {
        // Arrange
        const email = '<EMAIL>';
        final exception = Exception('Password reset failed');
        when(
          () => mockAuthService.sendPasswordResetEmail(email: email),
        ).thenThrow(exception);

        final notifier = container.read(passwordResetProvider.notifier);

        // Act & Assert
        try {
          await notifier.sendPasswordResetEmail(email);
          fail('Expected exception to be thrown');
        } on Exception catch (e) {
          expect(e, equals(exception));
        }

        final state = container.read(passwordResetProvider);
        expect(state, isA<AsyncError<void>>());
        expect(state.error, equals(exception));
        verify(
          () => mockAuthService.sendPasswordResetEmail(email: email),
        ).called(1);
      });

      test('should complete password reset operations successfully', () async {
        // Arrange
        const email = '<EMAIL>';
        when(
          () => mockAuthService.sendPasswordResetEmail(email: email),
        ).thenAnswer((_) async {});

        final notifier = container.read(passwordResetProvider.notifier);

        // Act
        await notifier.sendPasswordResetEmail(email);

        // Assert - Should be in data state after completion
        final completedState = container.read(passwordResetProvider);
        expect(completedState, isA<AsyncData<void>>());
        verify(
          () => mockAuthService.sendPasswordResetEmail(email: email),
        ).called(1);
      });
    });

    group('Provider State Management', () {
      test(
        'should have initial null state for all AsyncNotifier providers',
        () {
          // Act & Assert
          final profileUpdateState = container.read(profileUpdateProvider);
          final passwordChangeState = container.read(passwordChangeProvider);
          final accountDeletionState = container.read(accountDeletionProvider);
          final passwordResetState = container.read(passwordResetProvider);

          expect(profileUpdateState, isA<AsyncData<void>>());
          expect(passwordChangeState, isA<AsyncData<void>>());
          expect(accountDeletionState, isA<AsyncData<void>>());
          expect(passwordResetState, isA<AsyncData<void>>());
        },
      );

      test('should handle multiple operations on same provider', () async {
        // Arrange
        const displayName1 = 'First Name';
        const displayName2 = 'Second Name';
        when(
          () => mockUserRepository.updateDisplayName(any()),
        ).thenAnswer((_) async {});

        final notifier = container.read(profileUpdateProvider.notifier);

        // Act
        await notifier.updateDisplayName(displayName1);
        await notifier.updateDisplayName(displayName2);

        // Assert
        final state = container.read(profileUpdateProvider);
        expect(state, isA<AsyncData<void>>());
        verify(
          () => mockUserRepository.updateDisplayName(displayName1),
        ).called(1);
        verify(
          () => mockUserRepository.updateDisplayName(displayName2),
        ).called(1);
      });

      test('should handle sequential operations correctly', () async {
        // Arrange
        const displayName = 'Test Name';
        const email = '<EMAIL>';
        when(
          () => mockUserRepository.updateDisplayName(displayName),
        ).thenAnswer((_) async {});
        when(
          () => mockUserRepository.updateEmail(email),
        ).thenAnswer((_) async {});

        final notifier = container.read(profileUpdateProvider.notifier);

        // Act - Sequential operations
        await notifier.updateDisplayName(displayName);
        await notifier.updateEmail(email);

        // Assert - Both operations should complete successfully
        final state = container.read(profileUpdateProvider);
        expect(state, isA<AsyncData<void>>());
        verify(
          () => mockUserRepository.updateDisplayName(displayName),
        ).called(1);
        verify(() => mockUserRepository.updateEmail(email)).called(1);
      });

      test('should maintain separate states for different providers', () async {
        // Arrange
        const displayName = 'Test Name';
        const password = 'password123';
        when(
          () => mockUserRepository.updateDisplayName(displayName),
        ).thenAnswer((_) async {});
        when(
          () => mockAuthService.deleteAccountWithReauth(password),
        ).thenThrow(Exception('Deletion failed'));

        final profileNotifier = container.read(profileUpdateProvider.notifier);
        final deletionNotifier = container.read(
          accountDeletionProvider.notifier,
        );

        // Act
        await profileNotifier.updateDisplayName(displayName);
        try {
          await deletionNotifier.deleteAccount(password);
        } on Exception catch (_) {
          // Expected to fail
        }

        // Assert - Profile update should succeed, deletion should fail
        final profileState = container.read(profileUpdateProvider);
        final deletionState = container.read(accountDeletionProvider);

        expect(profileState, isA<AsyncData<void>>());
        expect(deletionState, isA<AsyncError<void>>());
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle null/empty parameters gracefully', () async {
        // Arrange
        when(
          () => mockUserRepository.updateDisplayName(''),
        ).thenAnswer((_) async {});

        final notifier = container.read(profileUpdateProvider.notifier);

        // Act & Assert - Should not throw for empty string
        await expectLater(
          () => notifier.updateDisplayName(''),
          returnsNormally,
        );
      });

      test('should preserve stack trace in error states', () async {
        // Arrange
        const displayName = 'Test Name';
        final exception = Exception('Update failed');
        when(
          () => mockUserRepository.updateDisplayName(displayName),
        ).thenThrow(exception);

        final notifier = container.read(profileUpdateProvider.notifier);

        // Act
        try {
          await notifier.updateDisplayName(displayName);
        } on Exception catch (_) {
          // Expected to fail
        }

        // Assert
        final state = container.read(profileUpdateProvider);
        expect(state, isA<AsyncError<void>>());
        expect(state.error, equals(exception));
        expect(state.stackTrace, isNotNull);
      });

      test('should handle provider disposal gracefully', () {
        // Arrange
        final notifier = container.read(profileUpdateProvider.notifier);

        // Act & Assert - Should not throw when disposing
        expect(() => container.dispose(), returnsNormally);
        expect(notifier, isA<ProfileUpdate>());
      });
    });
  });
}
