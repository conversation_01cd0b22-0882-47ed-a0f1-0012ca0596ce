import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:budapp/features/profile/presentation/widgets/re_authentication_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';

// Mock classes
class MockAuthService extends Mock implements AuthService {}

void main() {
  group('ReAuthenticationDialog', () {
    late MockAuthService mockAuthService;

    setUp(() {
      mockAuthService = MockAuthService();
    });

    tearDown(() {
      // Clean up mock state after each test
      reset(mockAuthService);
    });

    group('Basic Display', () {
      testWidgets('renders dialog with default title and message', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Verify default title and message
        expect(find.text('Confirm Identity'), findsOneWidget);
        expect(
          find.text('Please enter your current password to continue.'),
          findsOneWidget,
        );
      });

      testWidgets('renders dialog with custom title and message', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(
              title: 'Custom Title',
              message: 'Custom message for testing',
              confirmButtonText: 'Custom Button',
            ),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Verify custom title and message
        expect(find.text('Custom Title'), findsOneWidget);
        expect(find.text('Custom message for testing'), findsOneWidget);
        expect(find.text('Custom Button'), findsOneWidget);
      });

      testWidgets('displays password field with correct properties', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Verify password field
        expect(find.byType(TextFormField), findsOneWidget);
        expect(find.text('Current Password'), findsOneWidget);

        // Verify password field exists and is properly configured
        final passwordField = tester.widget<TextFormField>(
          find.byType(TextFormField),
        );
        expect(passwordField.enabled, isTrue);
      });

      testWidgets('displays visibility toggle button', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Verify visibility toggle button
        expect(find.byIcon(Icons.visibility), findsOneWidget);
        expect(find.byIcon(Icons.visibility_off), findsNothing);
      });

      testWidgets('displays cancel and confirm buttons', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Verify buttons
        expect(find.text('Cancel'), findsOneWidget);
        expect(find.text('Confirm'), findsOneWidget);
        expect(find.byType(TextButton), findsOneWidget);
        expect(find.byType(ElevatedButton), findsOneWidget);
      });
    });

    group('User Interactions', () {
      testWidgets('toggles password visibility when icon is tapped', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Initially password should be obscured
        expect(find.byIcon(Icons.visibility), findsOneWidget);
        expect(find.byIcon(Icons.visibility_off), findsNothing);

        // Tap visibility toggle
        await tester.tap(find.byIcon(Icons.visibility));
        await tester.pumpAndSettle();

        // Password should now be visible
        expect(find.byIcon(Icons.visibility), findsNothing);
        expect(find.byIcon(Icons.visibility_off), findsOneWidget);

        // Tap again to hide
        await tester.tap(find.byIcon(Icons.visibility_off));
        await tester.pumpAndSettle();

        // Password should be obscured again
        expect(find.byIcon(Icons.visibility), findsOneWidget);
        expect(find.byIcon(Icons.visibility_off), findsNothing);
      });

      testWidgets('handles cancel button tap', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Tap cancel button
        await tester.tap(find.text('Cancel'));
        await tester.pumpAndSettle();

        // Dialog should close
        expect(find.byType(ReAuthenticationDialog), findsNothing);
      });

      testWidgets('handles text input in password field', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Enter text in password field
        await tester.enterText(find.byType(TextFormField), 'testpassword');
        await tester.pumpAndSettle();

        // Verify text was entered
        expect(find.text('testpassword'), findsOneWidget);
      });

      testWidgets('validates empty password field', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Try to submit without entering password
        await tester.tap(find.text('Confirm'));
        await tester.pumpAndSettle();

        // Should show validation error
        expect(find.byType(ReAuthenticationDialog), findsOneWidget);
      });
    });

    group('Form Validation', () {
      testWidgets('shows validation error for empty password', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Try to submit with empty password
        await tester.tap(find.text('Confirm'));
        await tester.pumpAndSettle();

        // Form should validate and show error
        expect(find.byType(ReAuthenticationDialog), findsOneWidget);
      });

      testWidgets('accepts valid password input', (tester) async {
        when(
          () => mockAuthService.reauthenticate(any()),
        ).thenAnswer((_) async {});

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Enter valid password
        await tester.enterText(find.byType(TextFormField), 'validpassword');
        await tester.pumpAndSettle();

        // Submit form
        await tester.tap(find.text('Confirm'));
        await tester.pumpAndSettle();

        // Should call auth service
        verify(() => mockAuthService.reauthenticate('validpassword')).called(1);
      });
    });

    group('Loading States', () {
      testWidgets('shows loading state when authenticating', (tester) async {
        // Mock a delay to simulate loading
        when(
          () => mockAuthService.reauthenticate(any()),
        ).thenAnswer((_) => Future.delayed(const Duration(milliseconds: 100)));

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Enter password
        await tester.enterText(find.byType(TextFormField), 'testpassword');
        await tester.pumpAndSettle();

        // Submit form
        await tester.tap(find.text('Confirm'));
        await tester.pump();

        // Should show loading indicator
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.text('Confirm'), findsNothing);

        // Wait for completion
        await tester.pumpAndSettle();
      });

      testWidgets('disables buttons and field when loading', (tester) async {
        // Mock a delay to simulate loading
        when(
          () => mockAuthService.reauthenticate(any()),
        ).thenAnswer((_) => Future.delayed(const Duration(milliseconds: 100)));

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Enter password
        await tester.enterText(find.byType(TextFormField), 'testpassword');
        await tester.pumpAndSettle();

        // Submit form
        await tester.tap(find.text('Confirm'));
        await tester.pump();

        // Field should be disabled during loading
        final passwordField = tester.widget<TextFormField>(
          find.byType(TextFormField),
        );
        expect(passwordField.enabled, isFalse);

        // Cancel button should be disabled
        final cancelButton = tester.widget<TextButton>(find.byType(TextButton));
        expect(cancelButton.onPressed, isNull);

        // Confirm button should be disabled
        final confirmButton = tester.widget<ElevatedButton>(
          find.byType(ElevatedButton),
        );
        expect(confirmButton.onPressed, isNull);

        // Wait for completion
        await tester.pumpAndSettle();
      });
    });

    group('Error Handling', () {
      testWidgets('displays error message for wrong password', (tester) async {
        when(
          () => mockAuthService.reauthenticate(any()),
        ).thenThrow(Exception('wrong-password'));

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Enter password
        await tester.enterText(find.byType(TextFormField), 'wrongpassword');
        await tester.pumpAndSettle();

        // Submit form
        await tester.tap(find.text('Confirm'));
        await tester.pumpAndSettle();

        // Should show error message
        expect(
          find.text('Incorrect password. Please try again.'),
          findsOneWidget,
        );
      });

      testWidgets('displays error message for too many requests', (
        tester,
      ) async {
        when(
          () => mockAuthService.reauthenticate(any()),
        ).thenThrow(Exception('too-many-requests'));

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Enter password
        await tester.enterText(find.byType(TextFormField), 'testpassword');
        await tester.pumpAndSettle();

        // Submit form
        await tester.tap(find.text('Confirm'));
        await tester.pumpAndSettle();

        // Should show error message
        expect(
          find.text('Too many failed attempts. Please try again later.'),
          findsOneWidget,
        );
      });

      testWidgets('displays generic error message for unknown errors', (
        tester,
      ) async {
        when(
          () => mockAuthService.reauthenticate(any()),
        ).thenThrow(Exception('unknown-error'));

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Enter password
        await tester.enterText(find.byType(TextFormField), 'testpassword');
        await tester.pumpAndSettle();

        // Submit form
        await tester.tap(find.text('Confirm'));
        await tester.pumpAndSettle();

        // Should show generic error message
        expect(
          find.text('Authentication failed. Please try again.'),
          findsOneWidget,
        );
      });
    });

    group('Accessibility', () {
      testWidgets('provides proper semantic labels', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Verify accessibility elements
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.byType(Form), findsOneWidget);
        expect(find.byType(TextFormField), findsOneWidget);
        expect(find.byType(TextButton), findsOneWidget);
        expect(find.byType(ElevatedButton), findsOneWidget);
      });

      testWidgets('maintains proper focus order', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Password field should be properly configured
        final passwordField = tester.widget<TextFormField>(
          find.byType(TextFormField),
        );
        expect(passwordField.enabled, isTrue);
      });
    });

    group('Edge Cases', () {
      testWidgets('handles widget disposal correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Widget should render correctly
        expect(find.byType(ReAuthenticationDialog), findsOneWidget);

        // Close dialog
        await tester.tap(find.text('Cancel'));
        await tester.pumpAndSettle();

        // Widget should be disposed
        expect(find.byType(ReAuthenticationDialog), findsNothing);
      });

      testWidgets('handles form submission via Enter key', (tester) async {
        when(
          () => mockAuthService.reauthenticate(any()),
        ).thenAnswer((_) async {});

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ReAuthenticationDialog(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        // Enter password
        await tester.enterText(find.byType(TextFormField), 'testpassword');
        await tester.pumpAndSettle();

        // Submit via Enter key
        await tester.testTextInput.receiveAction(TextInputAction.done);
        await tester.pumpAndSettle();

        // Should call auth service
        verify(() => mockAuthService.reauthenticate('testpassword')).called(1);
      });
    });
  });

  group('showReAuthenticationDialog', () {
    testWidgets('returns false when dialog is cancelled', (tester) async {
      bool? result;

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Builder(
            builder: (context) => ElevatedButton(
              onPressed: () async {
                result = await showReAuthenticationDialog(context);
              },
              child: const Text('Show Dialog'),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap button to show dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Verify dialog is shown
      expect(find.byType(ReAuthenticationDialog), findsOneWidget);

      // Cancel the dialog
      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      // Should return false
      expect(result, false);
    });

    testWidgets('uses custom parameters when provided', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Builder(
            builder: (context) => ElevatedButton(
              onPressed: () async {
                await showReAuthenticationDialog(
                  context,
                  title: 'Custom Title',
                  message: 'Custom Message',
                  confirmButtonText: 'Custom Confirm',
                );
              },
              child: const Text('Show Dialog'),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap button to show dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Verify custom parameters are used
      expect(find.text('Custom Title'), findsOneWidget);
      expect(find.text('Custom Message'), findsOneWidget);
      expect(find.text('Custom Confirm'), findsOneWidget);
    });

    testWidgets('prevents dismissal by barrier tap', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Builder(
            builder: (context) => ElevatedButton(
              onPressed: () async {
                await showReAuthenticationDialog(context);
              },
              child: const Text('Show Dialog'),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap button to show dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Verify dialog is shown
      expect(find.byType(ReAuthenticationDialog), findsOneWidget);

      // Try to tap outside the dialog (barrier)
      await tester.tapAt(const Offset(10, 10));
      await tester.pumpAndSettle();

      // Dialog should still be present (not dismissible)
      expect(find.byType(ReAuthenticationDialog), findsOneWidget);
    });
  });
}
