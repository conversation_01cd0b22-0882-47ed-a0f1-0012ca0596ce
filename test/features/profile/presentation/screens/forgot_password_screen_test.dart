import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/profile/presentation/screens/forgot_password_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/mock_providers.dart';
import '../../../../helpers/test_wrapper.dart';

void main() {
  group('ForgotPasswordScreen Tests', () {
    late MockAuthService mockAuthService;

    setUp(() {
      mockAuthService = MockAuthService();

      // Setup default successful behavior
      when(
        () =>
            mockAuthService.sendPasswordResetEmail(email: any(named: 'email')),
      ).thenAnswer((_) async {});
    });

    testWidgets('should render forgot password form correctly', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
        ),
      );

      await tester.pumpAndSettle();

      // Verify AppBar
      expect(find.text('Forgot Password'), findsOneWidget);
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);

      // Verify form title and description
      expect(find.text('Reset Your Password'), findsOneWidget);
      expect(
        find.text(
          "Enter your email address and we'll send you a link to reset your password.",
        ),
        findsOneWidget,
      );

      // Verify email form field
      expect(find.byType(TextFormField), findsOneWidget);
      expect(find.text('Email Address'), findsOneWidget);
      expect(find.byIcon(Icons.email), findsOneWidget);

      // Verify buttons
      expect(find.text('Send Reset Email'), findsOneWidget);
      expect(find.text('Back to Login'), findsOneWidget);
    });

    testWidgets('should validate email field correctly', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
        ),
      );

      await tester.pumpAndSettle();

      // Find the send button and tap it without entering email
      final sendButton = find.text('Send Reset Email');
      await tester.tap(sendButton);
      await tester.pumpAndSettle();

      // Should show validation error for empty email
      expect(find.text('Email is required'), findsOneWidget);

      // Enter invalid email
      final emailField = find.byType(TextFormField);
      await tester.enterText(emailField, 'invalid-email');
      await tester.tap(sendButton);
      await tester.pumpAndSettle();

      // Should show validation error for invalid email format
      expect(find.text('Please enter a valid email address'), findsOneWidget);
    });

    testWidgets('should send password reset email successfully', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
        ),
      );

      await tester.pumpAndSettle();

      // Enter valid email
      final emailField = find.byType(TextFormField);
      await tester.enterText(emailField, '<EMAIL>');
      await tester.pumpAndSettle();

      // Tap send button
      final sendButton = find.text('Send Reset Email');
      await tester.tap(sendButton);
      await tester.pumpAndSettle();

      // Verify auth service was called
      verify(
        () => mockAuthService.sendPasswordResetEmail(email: '<EMAIL>'),
      ).called(1);

      // Should show success message
      expect(find.byIcon(Icons.check_circle), findsOneWidget);
      expect(find.text('Email Sent!'), findsOneWidget);
      expect(
        find.text("We've sent a password reset <NAME_EMAIL>"),
        findsOneWidget,
      );
      expect(
        find.text(
          'Check your email and follow the instructions to reset your password.',
        ),
        findsOneWidget,
      );

      // Should show success screen buttons
      expect(find.text('Back to Login'), findsOneWidget);
      expect(find.text('Send Another Email'), findsOneWidget);
    });

    testWidgets('should handle password reset errors correctly', (
      tester,
    ) async {
      // Setup auth service to throw error
      when(
        () =>
            mockAuthService.sendPasswordResetEmail(email: any(named: 'email')),
      ).thenThrow(Exception('user-not-found'));

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
        ),
      );

      await tester.pumpAndSettle();

      // Enter valid email
      final emailField = find.byType(TextFormField);
      await tester.enterText(emailField, '<EMAIL>');
      await tester.pumpAndSettle();

      // Tap send button
      final sendButton = find.text('Send Reset Email');
      await tester.tap(sendButton);
      await tester.pumpAndSettle();

      // Should show error snackbar
      expect(
        find.text('No account found with this email address.'),
        findsOneWidget,
      );
      expect(find.byType(SnackBar), findsOneWidget);
    });

    testWidgets('should show loading state during password reset', (
      tester,
    ) async {
      // Setup auth service with delay to test loading state
      when(
        () =>
            mockAuthService.sendPasswordResetEmail(email: any(named: 'email')),
      ).thenAnswer((_) async {
        await Future<void>.delayed(const Duration(milliseconds: 100));
      });

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
        ),
      );

      await tester.pumpAndSettle();

      // Enter valid email
      final emailField = find.byType(TextFormField);
      await tester.enterText(emailField, '<EMAIL>');
      await tester.pumpAndSettle();

      // Tap send button
      final sendButton = find.text('Send Reset Email');
      await tester.tap(sendButton);
      await tester.pump(); // Don't settle to catch loading state

      // Should show loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Email field should be disabled during loading
      final textFormField = tester.widget<TextFormField>(
        find.byType(TextFormField),
      );
      expect(textFormField.enabled, isFalse);

      // Wait for completion
      await tester.pumpAndSettle();
    });

    testWidgets('should handle form submission via keyboard', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
        ),
      );

      await tester.pumpAndSettle();

      // Enter valid email
      final emailField = find.byType(TextFormField);
      await tester.enterText(emailField, '<EMAIL>');
      await tester.pumpAndSettle();

      // Submit form via keyboard
      await tester.testTextInput.receiveAction(TextInputAction.done);
      await tester.pumpAndSettle();

      // Verify auth service was called
      verify(
        () => mockAuthService.sendPasswordResetEmail(email: '<EMAIL>'),
      ).called(1);
    });

    testWidgets('should navigate back when back button is pressed', (
      tester,
    ) async {
      final mockGoRouter = MockGoRouter();

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          InheritedGoRouter(
            goRouter: mockGoRouter,
            child: const ForgotPasswordScreen(),
          ),
          overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
        ),
      );

      await tester.pumpAndSettle();

      // Tap back button in AppBar
      final backButton = find.byIcon(Icons.arrow_back);
      await tester.tap(backButton);
      await tester.pumpAndSettle();

      // Verify navigation
      verify(mockGoRouter.pop).called(1);
    });

    testWidgets('should navigate back from "Back to Login" button', (
      tester,
    ) async {
      final mockGoRouter = MockGoRouter();

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          InheritedGoRouter(
            goRouter: mockGoRouter,
            child: const ForgotPasswordScreen(),
          ),
          overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
        ),
      );

      await tester.pumpAndSettle();

      // Tap "Back to Login" button
      final backToLoginButton = find.text('Back to Login');
      await tester.tap(backToLoginButton);
      await tester.pumpAndSettle();

      // Verify navigation
      verify(mockGoRouter.pop).called(1);
    });

    testWidgets('should allow sending another email from success screen', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const ForgotPasswordScreen(),
          overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
        ),
      );

      await tester.pumpAndSettle();

      // Enter valid email and send reset email
      final emailField = find.byType(TextFormField);
      await tester.enterText(emailField, '<EMAIL>');
      await tester.pumpAndSettle();

      final sendButton = find.text('Send Reset Email');
      await tester.tap(sendButton);
      await tester.pumpAndSettle();

      // Should be on success screen
      expect(find.text('Email Sent!'), findsOneWidget);

      // Tap "Send Another Email" button
      final sendAnotherButton = find.text('Send Another Email');
      await tester.tap(sendAnotherButton);
      await tester.pumpAndSettle();

      // Should return to email form
      expect(find.text('Reset Your Password'), findsOneWidget);
      expect(find.byType(TextFormField), findsOneWidget);
    });

    group('Error Message Tests', () {
      testWidgets('should show correct error for user-not-found', (
        tester,
      ) async {
        when(
          () => mockAuthService.sendPasswordResetEmail(
            email: any(named: 'email'),
          ),
        ).thenThrow(Exception('user-not-found'));

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ForgotPasswordScreen(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        final emailField = find.byType(TextFormField);
        await tester.enterText(emailField, '<EMAIL>');
        await tester.pumpAndSettle();

        final sendButton = find.text('Send Reset Email');
        await tester.tap(sendButton);
        await tester.pumpAndSettle();

        expect(
          find.text('No account found with this email address.'),
          findsOneWidget,
        );
      });

      testWidgets('should show correct error for invalid-email', (
        tester,
      ) async {
        when(
          () => mockAuthService.sendPasswordResetEmail(
            email: any(named: 'email'),
          ),
        ).thenThrow(Exception('invalid-email'));

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ForgotPasswordScreen(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        final emailField = find.byType(TextFormField);
        await tester.enterText(emailField, '<EMAIL>');
        await tester.pumpAndSettle();

        final sendButton = find.text('Send Reset Email');
        await tester.tap(sendButton);
        await tester.pumpAndSettle();

        expect(
          find.text('Please enter a valid email address.'),
          findsOneWidget,
        );
      });

      testWidgets('should show correct error for too-many-requests', (
        tester,
      ) async {
        when(
          () => mockAuthService.sendPasswordResetEmail(
            email: any(named: 'email'),
          ),
        ).thenThrow(Exception('too-many-requests'));

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ForgotPasswordScreen(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        final emailField = find.byType(TextFormField);
        await tester.enterText(emailField, '<EMAIL>');
        await tester.pumpAndSettle();

        final sendButton = find.text('Send Reset Email');
        await tester.tap(sendButton);
        await tester.pumpAndSettle();

        expect(
          find.text('Too many requests. Please try again later.'),
          findsOneWidget,
        );
      });

      testWidgets('should show correct error for network error', (
        tester,
      ) async {
        when(
          () => mockAuthService.sendPasswordResetEmail(
            email: any(named: 'email'),
          ),
        ).thenThrow(Exception('network error'));

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ForgotPasswordScreen(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        final emailField = find.byType(TextFormField);
        await tester.enterText(emailField, '<EMAIL>');
        await tester.pumpAndSettle();

        final sendButton = find.text('Send Reset Email');
        await tester.tap(sendButton);
        await tester.pumpAndSettle();

        expect(
          find.text('Network error. Please check your connection.'),
          findsOneWidget,
        );
      });

      testWidgets('should show generic error for unknown errors', (
        tester,
      ) async {
        when(
          () => mockAuthService.sendPasswordResetEmail(
            email: any(named: 'email'),
          ),
        ).thenThrow(Exception('unknown error'));

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ForgotPasswordScreen(),
            overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
          ),
        );

        await tester.pumpAndSettle();

        final emailField = find.byType(TextFormField);
        await tester.enterText(emailField, '<EMAIL>');
        await tester.pumpAndSettle();

        final sendButton = find.text('Send Reset Email');
        await tester.tap(sendButton);
        await tester.pumpAndSettle();

        expect(
          find.text('Failed to send password reset email. Please try again.'),
          findsOneWidget,
        );
      });
    });
  });
}

// Mock classes for testing
class MockGoRouter extends Mock implements GoRouter {}
