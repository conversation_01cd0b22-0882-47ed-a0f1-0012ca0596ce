import 'package:budapp/data/models/goal.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Goal Model Tests', () {
    late Goal testGoal;

    setUp(() {
      testGoal = Goal.create(
        userId: 'user123',
        name: 'Test Goal',
        description: 'A test goal for validation',
        targetAmountCents: 100000, // $1,000.00
        currentAmountCents: 25000, // $250.00
        targetDate: DateTime.now().add(
          const Duration(days: 365),
        ), // Future date
        colorHex: '#4CAF50',
        iconName: 'savings',
      );
    });

    group('Goal Creation', () {
      test('should create goal with all required fields', () {
        final goal = Goal.create(
          userId: 'user123',
          name: 'Vacation Fund',
          targetAmountCents: 200000,
        );

        expect(goal.userId, equals('user123'));
        expect(goal.name, equals('Vacation Fund'));
        expect(goal.targetAmountCents, equals(200000));
        expect(goal.currentAmountCents, equals(0));
        expect(goal.status, equals(GoalStatus.active));
        expect(goal.isActive, isTrue);
        expect(goal.isCompleted, isFalse);
        expect(goal.schemaVersion, equals(1));
        expect(goal.createdAt, isNotNull);
        expect(goal.updatedAt, isNotNull);
        expect(goal.metadata, isEmpty);
      });

      test('should create goal with optional fields', () {
        final goal = Goal.create(
          userId: 'user123',
          name: 'Emergency Fund',
          description: 'For unexpected expenses',
          targetAmountCents: 500000,
          currentAmountCents: 100000,
          targetDate: DateTime(2024, 6, 1),
          colorHex: '#FF5722',
          iconName: 'emergency',
          status: GoalStatus.active,
        );

        expect(goal.description, equals('For unexpected expenses'));
        expect(goal.currentAmountCents, equals(100000));
        expect(goal.targetDate, equals(DateTime(2024, 6, 1)));
        expect(goal.colorHex, equals('#FF5722'));
        expect(goal.iconName, equals('emergency'));
        expect(goal.status, equals(GoalStatus.active));
      });
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final json = testGoal.toJson();

        expect(json['userId'], equals('user123'));
        expect(json['name'], equals('Test Goal'));
        expect(json['description'], equals('A test goal for validation'));
        expect(json['targetAmountCents'], equals(100000));
        expect(json['currentAmountCents'], equals(25000));
        expect(json['colorHex'], equals('#4CAF50'));
        expect(json['iconName'], equals('savings'));
        expect(json['status'], equals('active'));
        expect(json['isActive'], isTrue);
        expect(json['isCompleted'], isFalse);
        expect(json['schemaVersion'], equals(1));
        expect(json['metadata'], isEmpty);
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'id': 'goal123',
          'userId': 'user456',
          'name': 'Car Fund',
          'description': 'Save for new car',
          'targetAmountCents': 1500000,
          'currentAmountCents': 300000,
          'targetDate': '2024-08-15T00:00:00.000Z',
          'isCompleted': false,
          'colorHex': '#2196F3',
          'iconName': 'car',
          'status': 'active',
          'isActive': true,
          'schemaVersion': 1,
          'createdAt': '2024-01-01T10:00:00.000Z',
          'updatedAt': '2024-01-15T15:30:00.000Z',
          'metadata': <String, dynamic>{},
        };

        final goal = Goal.fromJson(json);

        expect(goal.id, equals('goal123'));
        expect(goal.userId, equals('user456'));
        expect(goal.name, equals('Car Fund'));
        expect(goal.description, equals('Save for new car'));
        expect(goal.targetAmountCents, equals(1500000));
        expect(goal.currentAmountCents, equals(300000));
        expect(
          goal.targetDate,
          equals(DateTime.parse('2024-08-15T00:00:00.000Z')),
        );
        expect(goal.isCompleted, isFalse);
        expect(goal.colorHex, equals('#2196F3'));
        expect(goal.iconName, equals('car'));
        expect(goal.status, equals(GoalStatus.active));
        expect(goal.isActive, isTrue);
        expect(goal.schemaVersion, equals(1));
        expect(goal.metadata, isEmpty);
      });

      test('should handle missing optional fields in JSON', () {
        final json = {
          'id': 'goal123',
          'userId': 'user456',
          'name': 'Simple Goal',
          'targetAmountCents': 50000,
          'currentAmountCents': 10000,
          'isCompleted': false,
          'status': 'active',
          'isActive': true,
          'schemaVersion': 1,
          'createdAt': '2024-01-01T10:00:00.000Z',
          'updatedAt': '2024-01-15T15:30:00.000Z',
          'metadata': <String, dynamic>{},
        };

        final goal = Goal.fromJson(json);

        expect(goal.description, isNull);
        expect(goal.targetDate, isNull);
        expect(goal.colorHex, isNull);
        expect(goal.iconName, isNull);
      });

      test('should handle backward compatibility for missing status', () {
        final json = {
          'id': 'goal123',
          'userId': 'user456',
          'name': 'Legacy Goal',
          'targetAmountCents': 50000,
          'currentAmountCents': 50000,
          'isCompleted': true,
          'isActive': true,
          'schemaVersion': 1,
          'createdAt': '2024-01-01T10:00:00.000Z',
          'updatedAt': '2024-01-15T15:30:00.000Z',
          'metadata': <String, dynamic>{},
        };

        final goal = Goal.fromJson(json);

        expect(goal.status, equals(GoalStatus.completed));
        expect(goal.isCompleted, isTrue);
      });
    });

    group('Progress Calculations', () {
      test('should calculate progress percentage correctly', () {
        expect(
          testGoal.progressPercentage,
          equals(0.25),
        ); // 25000/100000 = 0.25
      });

      test('should calculate remaining amount correctly', () {
        expect(testGoal.remainingAmountCents, equals(75000)); // 100000 - 25000
      });

      test('should detect goal achievement', () {
        final achievedGoal = testGoal.copyWith(currentAmountCents: 100000);
        expect(achievedGoal.isAchieved, isTrue);

        final overAchievedGoal = testGoal.copyWith(currentAmountCents: 120000);
        expect(overAchievedGoal.isAchieved, isTrue);

        expect(testGoal.isAchieved, isFalse);
      });

      test('should handle zero target amount', () {
        final zeroTargetGoal = testGoal.copyWith(targetAmountCents: 0);
        expect(zeroTargetGoal.progressPercentage, equals(0));
      });

      test('should clamp progress percentage to 1.0', () {
        final overAchievedGoal = testGoal.copyWith(currentAmountCents: 200000);
        expect(overAchievedGoal.progressPercentage, equals(1.0));
      });
    });

    group('Date Calculations', () {
      test('should detect if goal has deadline', () {
        expect(testGoal.hasDeadline, isTrue);

        final noDeadlineGoal = testGoal.copyWith(targetDate: null);
        expect(noDeadlineGoal.hasDeadline, isFalse);
      });

      test('should calculate days remaining', () {
        final futureDate = DateTime.now().add(const Duration(days: 30));
        final futureGoal = testGoal.copyWith(targetDate: futureDate);

        expect(futureGoal.daysRemaining, isNotNull);
        expect(
          futureGoal.daysRemaining! >= 29,
          isTrue,
        ); // Allow for timing differences
      });

      test('should return null days remaining for no target date', () {
        final noDateGoal = testGoal.copyWith(targetDate: null);
        expect(noDateGoal.daysRemaining, isNull);
      });

      test('should calculate required daily savings', () {
        final futureDate = DateTime.now().add(const Duration(days: 30));
        final futureGoal = testGoal.copyWith(targetDate: futureDate);

        expect(futureGoal.requiredDailySavingsCents, isNotNull);
        expect(futureGoal.requiredDailySavingsCents! > 0, isTrue);
      });
    });

    group('Validation', () {
      test('should validate correct goal', () {
        expect(testGoal.isValid, isTrue);
        expect(testGoal.validate(), isEmpty);
      });

      test('should validate name requirements', () {
        final emptyNameGoal = testGoal.copyWith(name: '');
        expect(emptyNameGoal.isValid, isFalse);
        expect(emptyNameGoal.validate(), contains('Goal name is required'));

        final shortNameGoal = testGoal.copyWith(name: 'A');
        expect(shortNameGoal.isValid, isFalse);
        expect(
          shortNameGoal.validate(),
          contains('Goal name must be at least 2 characters'),
        );

        final longNameGoal = testGoal.copyWith(name: 'A' * 101);
        expect(longNameGoal.isValid, isFalse);
        expect(
          longNameGoal.validate(),
          contains('Goal name must be 100 characters or less'),
        );
      });

      test('should validate target amount', () {
        final zeroAmountGoal = testGoal.copyWith(targetAmountCents: 0);
        expect(zeroAmountGoal.isValid, isFalse);
        expect(
          zeroAmountGoal.validate(),
          contains('Goal target amount must be greater than 0'),
        );

        final negativeAmountGoal = testGoal.copyWith(targetAmountCents: -1000);
        expect(negativeAmountGoal.isValid, isFalse);
        expect(
          negativeAmountGoal.validate(),
          contains('Goal target amount must be greater than 0'),
        );
      });

      test('should validate current amount', () {
        final negativeCurrentGoal = testGoal.copyWith(
          currentAmountCents: -1000,
        );
        expect(negativeCurrentGoal.isValid, isFalse);
        expect(
          negativeCurrentGoal.validate(),
          contains('Goal current amount cannot be negative'),
        );
      });

      test('should validate color format', () {
        final invalidColorGoal = testGoal.copyWith(colorHex: 'invalid');
        expect(invalidColorGoal.isValid, isFalse);
        expect(
          invalidColorGoal.validate(),
          contains('Goal color must be a valid hex color (e.g., #FF0000)'),
        );

        final validColorGoal = testGoal.copyWith(colorHex: '#FF0000');
        expect(validColorGoal.isValid, isTrue);
      });

      test('should validate icon name format', () {
        final invalidIconGoal = testGoal.copyWith(iconName: '123invalid');
        expect(invalidIconGoal.isValid, isFalse);
        expect(
          invalidIconGoal.validate(),
          contains(
            'Goal icon name must be a valid identifier (letters, numbers, underscores, starting with letter)',
          ),
        );

        final validIconGoal = testGoal.copyWith(iconName: 'valid_icon');
        expect(validIconGoal.isValid, isTrue);
      });
    });

    group('Utility Methods', () {
      test('should update timestamp', () {
        final originalUpdatedAt = testGoal.updatedAt;

        // Wait a bit to ensure timestamp difference
        final updatedGoal = testGoal.updated();

        expect(
          updatedGoal.updatedAt != null &&
              originalUpdatedAt != null &&
              updatedGoal.updatedAt!.isAfter(originalUpdatedAt),
          isTrue,
        );
        expect(updatedGoal.id, equals(testGoal.id));
        expect(updatedGoal.name, equals(testGoal.name));
      });

      test('should provide status string', () {
        expect(testGoal.statusString, equals('Active'));

        final completedGoal = testGoal.copyWith(status: GoalStatus.completed);
        expect(completedGoal.statusString, equals('Completed'));
      });

      test('should provide progress message', () {
        expect(testGoal.progressMessage, equals('25.0% complete'));

        final achievedGoal = testGoal.copyWith(currentAmountCents: 100000);
        expect(achievedGoal.progressMessage, equals('Goal achieved!'));
      });
    });
  });
}
