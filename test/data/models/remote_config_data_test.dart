import 'package:budapp/data/models/remote_config_data.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PredefinedCategories', () {
    test('should create with default values', () {
      const categories = PredefinedCategories();

      expect(categories.incomeCategories, isEmpty);
      expect(categories.expenseCategories, isEmpty);
    });

    test('should create with custom values', () {
      const categories = PredefinedCategories(
        incomeCategories: ['Salary', 'Bonus'],
        expenseCategories: ['Food', 'Transport'],
      );

      expect(categories.incomeCategories, ['Salary', 'Bonus']);
      expect(categories.expenseCategories, ['Food', 'Transport']);
    });

    test('defaults() should return predefined categories', () {
      final categories = PredefinedCategories.defaults();

      expect(categories.incomeCategories, isNotEmpty);
      expect(categories.expenseCategories, isNotEmpty);
      expect(categories.incomeCategories, contains('Salary'));
      expect(categories.incomeCategories, contains('Freelance'));
      expect(categories.expenseCategories, contains('Food & Dining'));
      expect(categories.expenseCategories, contains('Transportation'));
    });

    test('should serialize to and from JSON', () {
      const original = PredefinedCategories(
        incomeCategories: ['Salary', 'Investment'],
        expenseCategories: ['Food', 'Transport', 'Entertainment'],
      );

      final json = original.toJson();
      final restored = PredefinedCategories.fromJson(json);

      expect(restored, equals(original));
      expect(restored.incomeCategories, equals(original.incomeCategories));
      expect(restored.expenseCategories, equals(original.expenseCategories));
    });

    test('should handle empty JSON', () {
      final categories = PredefinedCategories.fromJson({});

      expect(categories.incomeCategories, isEmpty);
      expect(categories.expenseCategories, isEmpty);
    });
  });

  group('PremiumLimits', () {
    test('should create with default values', () {
      const limits = PremiumLimits();

      expect(limits.maxAccountsFree, 3);
      expect(limits.maxAccountsPremium, 50);
      expect(limits.maxCustomCategoriesFree, 5);
      expect(limits.maxCustomCategoriesPremium, 100);
      expect(limits.maxBudgetsFree, 10);
      expect(limits.maxBudgetsPremium, 100);
      expect(limits.maxGoalsFree, 5);
      expect(limits.maxGoalsPremium, 50);
    });

    test('should create with custom values', () {
      const limits = PremiumLimits(
        maxAccountsFree: 5,
        maxAccountsPremium: 100,
        maxCustomCategoriesFree: 10,
        maxCustomCategoriesPremium: 200,
        maxBudgetsFree: 15,
        maxBudgetsPremium: 150,
        maxGoalsFree: 8,
        maxGoalsPremium: 80,
      );

      expect(limits.maxAccountsFree, 5);
      expect(limits.maxAccountsPremium, 100);
      expect(limits.maxCustomCategoriesFree, 10);
      expect(limits.maxCustomCategoriesPremium, 200);
      expect(limits.maxBudgetsFree, 15);
      expect(limits.maxBudgetsPremium, 150);
      expect(limits.maxGoalsFree, 8);
      expect(limits.maxGoalsPremium, 80);
    });

    test('defaults() should return default limits', () {
      final limits = PremiumLimits.defaults();

      expect(limits.maxAccountsFree, 3);
      expect(limits.maxAccountsPremium, 50);
      expect(limits.maxCustomCategoriesFree, 5);
      expect(limits.maxCustomCategoriesPremium, 100);
    });

    test('should serialize to and from JSON', () {
      const original = PremiumLimits(
        maxAccountsFree: 2,
        maxAccountsPremium: 25,
        maxCustomCategoriesFree: 3,
        maxCustomCategoriesPremium: 50,
        maxBudgetsFree: 5,
        maxBudgetsPremium: 75,
        maxGoalsFree: 3,
        maxGoalsPremium: 25,
      );

      final json = original.toJson();
      final restored = PremiumLimits.fromJson(json);

      expect(restored, equals(original));
      expect(restored.maxAccountsFree, equals(original.maxAccountsFree));
      expect(restored.maxAccountsPremium, equals(original.maxAccountsPremium));
    });
  });

  group('RemoteConfigData', () {
    test('should create with required values', () {
      final data = RemoteConfigData(
        categories: PredefinedCategories.defaults(),
        premiumLimits: PremiumLimits.defaults(),
      );

      expect(data.categories, isA<PredefinedCategories>());
      expect(data.premiumLimits, isA<PremiumLimits>());
      expect(data.enableFeatureX, false);
      expect(data.enableBetaFeatures, false);
      expect(data.schemaVersion, 1);
    });

    test('should create with custom values', () {
      const categories = PredefinedCategories(
        incomeCategories: ['Custom Income'],
        expenseCategories: ['Custom Expense'],
      );
      const limits = PremiumLimits(maxAccountsFree: 5);

      const data = RemoteConfigData(
        categories: categories,
        premiumLimits: limits,
        enableFeatureX: true,
        enableBetaFeatures: true,
        schemaVersion: 2,
      );

      expect(data.categories, equals(categories));
      expect(data.premiumLimits, equals(limits));
      expect(data.enableFeatureX, true);
      expect(data.enableBetaFeatures, true);
      expect(data.schemaVersion, 2);
    });

    test('defaults() should return default configuration', () {
      final data = RemoteConfigData.defaults();

      expect(data.categories, isA<PredefinedCategories>());
      expect(data.premiumLimits, isA<PremiumLimits>());
      expect(data.enableFeatureX, false);
      expect(data.enableBetaFeatures, false);
      expect(data.schemaVersion, 1);
    });

    test('should create from properly structured JSON', () {
      final json = {
        'categories': {
          'incomeCategories': ['Test Income'],
          'expenseCategories': ['Test Expense'],
        },
        'premiumLimits': {
          'maxAccountsFree': 2,
          'maxAccountsPremium': 50,
          'maxCustomCategoriesFree': 5,
          'maxCustomCategoriesPremium': 100,
          'maxBudgetsFree': 10,
          'maxBudgetsPremium': 100,
          'maxGoalsFree': 5,
          'maxGoalsPremium': 50,
        },
        'enableFeatureX': true,
        'enableBetaFeatures': false,
        'schemaVersion': 2,
      };

      final data = RemoteConfigData.fromJson(json);

      expect(data.categories.incomeCategories, ['Test Income']);
      expect(data.categories.expenseCategories, ['Test Expense']);
      expect(data.premiumLimits.maxAccountsFree, 2);
      expect(data.enableFeatureX, true);
      expect(data.enableBetaFeatures, false);
      expect(data.schemaVersion, 2);
    });

    test('should handle backward compatibility for missing schemaVersion', () {
      final json = {
        'categories': {
          'incomeCategories': ['Income'],
          'expenseCategories': ['Expense'],
        },
        'premiumLimits': {
          'maxAccountsFree': 3,
          'maxAccountsPremium': 50,
          'maxCustomCategoriesFree': 5,
          'maxCustomCategoriesPremium': 100,
          'maxBudgetsFree': 10,
          'maxBudgetsPremium': 100,
          'maxGoalsFree': 5,
          'maxGoalsPremium': 50,
        },
        'enableFeatureX': false,
        'enableBetaFeatures': false,
        // schemaVersion is missing
      };

      final data = RemoteConfigData.fromJson(json);

      expect(data.schemaVersion, 1); // Should default to 1
    });

    test('should handle complex nested JSON', () {
      final json = {
        'categories': {
          'incomeCategories': ['Salary', 'Freelance', 'Investment'],
          'expenseCategories': ['Food', 'Transport', 'Entertainment', 'Bills'],
        },
        'premiumLimits': {
          'maxAccountsFree': 5,
          'maxAccountsPremium': 100,
          'maxCustomCategoriesFree': 10,
          'maxCustomCategoriesPremium': 200,
          'maxBudgetsFree': 15,
          'maxBudgetsPremium': 150,
          'maxGoalsFree': 8,
          'maxGoalsPremium': 80,
        },
        'enableFeatureX': true,
        'enableBetaFeatures': true,
        'schemaVersion': 3,
      };

      final data = RemoteConfigData.fromJson(json);

      expect(data.categories.incomeCategories, hasLength(3));
      expect(data.categories.expenseCategories, hasLength(4));
      expect(data.premiumLimits.maxAccountsFree, 5);
      expect(data.enableFeatureX, true);
      expect(data.enableBetaFeatures, true);
      expect(data.schemaVersion, 3);
    });
  });
}
