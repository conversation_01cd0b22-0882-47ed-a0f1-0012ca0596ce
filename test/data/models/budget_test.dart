import 'package:budapp/data/models/budget.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Budget Model Tests', () {
    const testUserId = 'test-user-id';
    const testCategoryId = 'test-category-id';
    const testParentBudgetId = 'test-parent-budget-id';
    final testPeriodStart = DateTime(2024, 1, 1);

    group('Factory Constructor', () {
      test('Budget.create() should create budget with default values', () {
        final budget = Budget.create(
          userId: testUserId,
          plannedAmountCents: 100000,
          periodStart: testPeriodStart,
        );

        expect(budget.userId, equals(testUserId));
        expect(budget.type, equals(BudgetType.expense));
        expect(budget.plannedAmountCents, equals(100000));
        expect(budget.currentAmountCents, equals(0));
        expect(budget.period, equals(BudgetPeriod.monthly));
        expect(budget.periodStart, equals(testPeriodStart));
        expect(budget.categoryId, isNull);
        expect(budget.parentBudgetId, isNull);
        expect(budget.isActive, isTrue);
        expect(budget.schemaVersion, equals(1));
        expect(budget.id, equals(''));
        expect(budget.createdAt, isNotNull);
        expect(budget.updatedAt, isNotNull);
        expect(budget.metadata, isEmpty);
      });

      test('Budget.create() should create budget with custom values', () {
        final budget = Budget.create(
          userId: testUserId,
          type: BudgetType.income,
          plannedAmountCents: 200000,
          currentAmountCents: 50000,
          period: BudgetPeriod.yearly,
          periodStart: testPeriodStart,
          categoryId: testCategoryId,
          parentBudgetId: testParentBudgetId,
        );

        expect(budget.userId, equals(testUserId));
        expect(budget.type, equals(BudgetType.income));
        expect(budget.plannedAmountCents, equals(200000));
        expect(budget.currentAmountCents, equals(50000));
        expect(budget.period, equals(BudgetPeriod.yearly));
        expect(budget.periodStart, equals(testPeriodStart));
        expect(budget.categoryId, equals(testCategoryId));
        expect(budget.parentBudgetId, equals(testParentBudgetId));
        expect(budget.isActive, isTrue);
        expect(budget.schemaVersion, equals(1));
      });
    });

    group('BudgetValidation Extension', () {
      test('validate() should return empty list for valid budget', () {
        final budget = Budget.create(
          userId: testUserId,
          plannedAmountCents: 100000,
          periodStart: testPeriodStart,
        );

        final errors = budget.validate();
        expect(errors, isEmpty);
      });

      test('validate() should return error for zero planned amount', () {
        final budget = Budget.create(
          userId: testUserId,
          plannedAmountCents: 0,
          periodStart: testPeriodStart,
        );

        final errors = budget.validate();
        expect(
          errors,
          contains('Budget planned amount must be greater than 0'),
        );
      });

      test('validate() should return error for negative planned amount', () {
        final budget = Budget.create(
          userId: testUserId,
          plannedAmountCents: -1000,
          periodStart: testPeriodStart,
        );

        final errors = budget.validate();
        expect(
          errors,
          contains('Budget planned amount must be greater than 0'),
        );
      });

      test(
        'validate() should return error when budget is parent of itself',
        () {
          final budget = Budget(
            id: 'test-id',
            userId: testUserId,
            plannedAmountCents: 100000,
            periodStart: testPeriodStart,
            parentBudgetId: 'test-id', // Same as id
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          final errors = budget.validate();
          expect(errors, contains('Budget cannot be its own parent'));
        },
      );

      test('isValid should return true for valid budget', () {
        final budget = Budget.create(
          userId: testUserId,
          plannedAmountCents: 100000,
          periodStart: testPeriodStart,
        );

        expect(budget.isValid, isTrue);
      });

      test('isValid should return false for invalid budget', () {
        final budget = Budget.create(
          userId: testUserId,
          plannedAmountCents: 0,
          periodStart: testPeriodStart,
        );

        expect(budget.isValid, isFalse);
      });

      test('periodString should return correct string for monthly period', () {
        final budget = Budget.create(
          userId: testUserId,
          plannedAmountCents: 100000,
          period: BudgetPeriod.monthly,
          periodStart: testPeriodStart,
        );

        expect(budget.periodString, equals('Monthly'));
      });

      test('periodString should return correct string for yearly period', () {
        final budget = Budget.create(
          userId: testUserId,
          plannedAmountCents: 100000,
          period: BudgetPeriod.yearly,
          periodStart: testPeriodStart,
        );

        expect(budget.periodString, equals('Yearly'));
      });

      test('typeString should return correct string for expense type', () {
        final budget = Budget.create(
          userId: testUserId,
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          periodStart: testPeriodStart,
        );

        expect(budget.typeString, equals('Expense'));
      });

      test('typeString should return correct string for income type', () {
        final budget = Budget.create(
          userId: testUserId,
          type: BudgetType.income,
          plannedAmountCents: 100000,
          periodStart: testPeriodStart,
        );

        expect(budget.typeString, equals('Income'));
      });

      test('isOverallBudget should return true when categoryId is null', () {
        final budget = Budget.create(
          userId: testUserId,
          plannedAmountCents: 100000,
          periodStart: testPeriodStart,
          categoryId: null,
        );

        expect(budget.isOverallBudget, isTrue);
      });

      test(
        'isOverallBudget should return false when categoryId is not null',
        () {
          final budget = Budget.create(
            userId: testUserId,
            plannedAmountCents: 100000,
            periodStart: testPeriodStart,
            categoryId: testCategoryId,
          );

          expect(budget.isOverallBudget, isFalse);
        },
      );

      test(
        'isChildBudget should return true when parentBudgetId is not null',
        () {
          final budget = Budget.create(
            userId: testUserId,
            plannedAmountCents: 100000,
            periodStart: testPeriodStart,
            parentBudgetId: testParentBudgetId,
          );

          expect(budget.isChildBudget, isTrue);
        },
      );

      test('isChildBudget should return false when parentBudgetId is null', () {
        final budget = Budget.create(
          userId: testUserId,
          plannedAmountCents: 100000,
          periodStart: testPeriodStart,
          parentBudgetId: null,
        );

        expect(budget.isChildBudget, isFalse);
      });

      test(
        'canHaveChildren should return true when categoryId is not null',
        () {
          final budget = Budget.create(
            userId: testUserId,
            plannedAmountCents: 100000,
            periodStart: testPeriodStart,
            categoryId: testCategoryId,
          );

          expect(budget.canHaveChildren, isTrue);
        },
      );

      test('canHaveChildren should return false when categoryId is null', () {
        final budget = Budget.create(
          userId: testUserId,
          plannedAmountCents: 100000,
          periodStart: testPeriodStart,
          categoryId: null,
        );

        expect(budget.canHaveChildren, isFalse);
      });
    });
  });
}
