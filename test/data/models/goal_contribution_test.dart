import 'package:budapp/data/models/goal_contribution.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('GoalContribution Model Tests', () {
    late DateTime testDate;
    late DateTime createdAt;
    late DateTime updatedAt;

    setUp(() {
      testDate = DateTime(2025, 1, 15, 10, 30);
      createdAt = DateTime(2025, 1, 15, 9, 0);
      updatedAt = DateTime(2025, 1, 15, 9, 0);
    });

    group('Factory Constructor Tests', () {
      test('should create GoalContribution with all required fields', () {
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: testDate,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        expect(contribution.id, 'contrib123');
        expect(contribution.userId, 'user123');
        expect(contribution.goalId, 'goal456');
        expect(contribution.amountCents, 5000);
        expect(contribution.contributionDate, testDate);
        expect(contribution.description, isNull);
        expect(contribution.isActive, true);
        expect(contribution.schemaVersion, 1);
        expect(contribution.createdAt, createdAt);
        expect(contribution.updatedAt, updatedAt);
        expect(contribution.metadata, isEmpty);
      });

      test('should create GoalContribution with optional fields', () {
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: testDate,
          description: 'Monthly savings',
          isActive: false,
          schemaVersion: 2,
          createdAt: createdAt,
          updatedAt: updatedAt,
          metadata: {'source': 'manual'},
        );

        expect(contribution.description, 'Monthly savings');
        expect(contribution.isActive, false);
        expect(contribution.schemaVersion, 2);
        expect(contribution.metadata, {'source': 'manual'});
      });
    });

    group('Factory Method Tests', () {
      test('GoalContribution.create should create with defaults', () {
        final contribution = GoalContribution.create(
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: testDate,
        );

        expect(contribution.id, isEmpty);
        expect(contribution.userId, 'user123');
        expect(contribution.goalId, 'goal456');
        expect(contribution.amountCents, 5000);
        expect(contribution.contributionDate, testDate);
        expect(contribution.description, isNull);
        expect(contribution.isActive, true);
        expect(contribution.schemaVersion, 1);
        expect(contribution.metadata, isEmpty);
        // createdAt and updatedAt should be set to current time
        expect(contribution.createdAt, isNotNull);
        expect(contribution.updatedAt, isNotNull);
      });

      test('GoalContribution.create should accept optional description', () {
        final contribution = GoalContribution.create(
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: testDate,
          description: 'Bonus contribution',
        );

        expect(contribution.description, 'Bonus contribution');
      });
    });

    group('JSON Serialization Tests', () {
      test('should serialize to JSON correctly', () {
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: testDate,
          description: 'Test contribution',
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        final json = contribution.toJson();

        expect(json['id'], 'contrib123');
        expect(json['userId'], 'user123');
        expect(json['goalId'], 'goal456');
        expect(json['amountCents'], 5000);
        expect(json['contributionDate'], testDate.toIso8601String());
        expect(json['description'], 'Test contribution');
        expect(json['isActive'], true);
        expect(json['schemaVersion'], 1);
        expect(json['createdAt'], createdAt.toIso8601String());
        expect(json['updatedAt'], updatedAt.toIso8601String());
        expect(json['metadata'], isEmpty);
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'id': 'contrib123',
          'userId': 'user123',
          'goalId': 'goal456',
          'amountCents': 5000,
          'contributionDate': testDate.toIso8601String(),
          'description': 'Test contribution',
          'isActive': true,
          'schemaVersion': 1,
          'createdAt': createdAt.toIso8601String(),
          'updatedAt': updatedAt.toIso8601String(),
          'metadata': {'source': 'test'},
        };

        final contribution = GoalContribution.fromJson(json);

        expect(contribution.id, 'contrib123');
        expect(contribution.userId, 'user123');
        expect(contribution.goalId, 'goal456');
        expect(contribution.amountCents, 5000);
        expect(contribution.contributionDate, testDate);
        expect(contribution.description, 'Test contribution');
        expect(contribution.isActive, true);
        expect(contribution.schemaVersion, 1);
        expect(contribution.createdAt, createdAt);
        expect(contribution.updatedAt, updatedAt);
        expect(contribution.metadata, {'source': 'test'});
      });

      test('should handle missing optional fields in JSON', () {
        final json = {
          'id': 'contrib123',
          'userId': 'user123',
          'goalId': 'goal456',
          'amountCents': 5000,
          'contributionDate': testDate.toIso8601String(),
          'isActive': true,
          'schemaVersion': 1,
          'createdAt': createdAt.toIso8601String(),
          'updatedAt': updatedAt.toIso8601String(),
          'metadata': <String, dynamic>{},
        };

        final contribution = GoalContribution.fromJson(json);

        expect(contribution.description, isNull);
        expect(contribution.metadata, isEmpty);
      });

      test('should handle missing schema version with default', () {
        final json = {
          'id': 'contrib123',
          'userId': 'user123',
          'goalId': 'goal456',
          'amountCents': 5000,
          'contributionDate': testDate.toIso8601String(),
          'isActive': true,
          'createdAt': createdAt.toIso8601String(),
          'updatedAt': updatedAt.toIso8601String(),
          'metadata': <String, dynamic>{},
        };

        final contribution = GoalContribution.fromJson(json);

        expect(contribution.schemaVersion, 1);
      });

      test('should handle missing metadata with default', () {
        final json = {
          'id': 'contrib123',
          'userId': 'user123',
          'goalId': 'goal456',
          'amountCents': 5000,
          'contributionDate': testDate.toIso8601String(),
          'isActive': true,
          'schemaVersion': 1,
          'createdAt': createdAt.toIso8601String(),
          'updatedAt': updatedAt.toIso8601String(),
        };

        final contribution = GoalContribution.fromJson(json);

        expect(contribution.metadata, isEmpty);
      });
    });

    group('Utility Method Tests', () {
      test('updated() should return copy with new timestamp', () {
        final original = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: testDate,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        final updated = original.updated();

        expect(updated.id, original.id);
        expect(updated.userId, original.userId);
        expect(updated.goalId, original.goalId);
        expect(updated.amountCents, original.amountCents);
        expect(updated.contributionDate, original.contributionDate);
        expect(updated.createdAt, original.createdAt);
        expect(updated.updatedAt, isNot(original.updatedAt));
        expect(
          updated.updatedAt != null &&
              original.updatedAt != null &&
              updated.updatedAt!.isAfter(original.updatedAt!),
          true,
        );
      });

      test('hasDescription should return correct values', () {
        final withDescription = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: testDate,
          description: 'Test description',
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        final withoutDescription = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: testDate,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        final withEmptyDescription = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: testDate,
          description: '',
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        expect(withDescription.hasDescription, true);
        expect(withoutDescription.hasDescription, false);
        expect(withEmptyDescription.hasDescription, false);
      });
    });

    group('Date Helper Tests', () {
      test("isMadeToday should return true for today's date", () {
        final today = DateTime.now();
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: today,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        expect(contribution.isMadeToday, true);
      });

      test('isMadeToday should return false for yesterday', () {
        final yesterday = DateTime.now().subtract(const Duration(days: 1));
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: yesterday,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        expect(contribution.isMadeToday, false);
      });

      test('isMadeThisWeek should return true for this week', () {
        final now = DateTime.now();
        final thisWeek = now.subtract(Duration(days: now.weekday - 1));
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: thisWeek,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        expect(contribution.isMadeThisWeek, true);
      });

      test('isMadeThisMonth should return true for this month', () {
        final now = DateTime.now();
        final thisMonth = DateTime(now.year, now.month, 1);
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: thisMonth,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        expect(contribution.isMadeThisMonth, true);
      });

      test('dateDescription should return correct descriptions', () {
        final now = DateTime.now();

        // Today
        final todayContrib = GoalContribution(
          id: 'contrib1',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: now,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );
        expect(todayContrib.dateDescription, 'Today');

        // Specific date
        final specificDate = DateTime(2025, 3, 15);
        final specificContrib = GoalContribution(
          id: 'contrib2',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: specificDate,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );
        expect(specificContrib.dateDescription, 'Mar 15, 2025');
      });

      test('summaryMessage should format correctly', () {
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000, // $50.00
          contributionDate: DateTime.now(),
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        expect(contribution.summaryMessage, r'Contributed $50.00 on Today');
      });
    });

    group('Validation Tests', () {
      test('should validate successfully with valid data', () {
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: DateTime.now().subtract(const Duration(days: 1)),
          description: 'Valid description',
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        final errors = contribution.validate();
        expect(errors, isEmpty);
        expect(contribution.isValid, true);
      });

      test('should fail validation with empty user ID', () {
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: '',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: testDate,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        final errors = contribution.validate();
        expect(errors, contains('User ID is required'));
        expect(contribution.isValid, false);
      });

      test('should fail validation with empty goal ID', () {
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: '',
          amountCents: 5000,
          contributionDate: testDate,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        final errors = contribution.validate();
        expect(errors, contains('Goal ID is required'));
        expect(contribution.isValid, false);
      });

      test('should fail validation with zero amount', () {
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 0,
          contributionDate: testDate,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        final errors = contribution.validate();
        expect(errors, contains('Contribution amount must be greater than 0'));
        expect(contribution.isValid, false);
      });

      test('should fail validation with negative amount', () {
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: -1000,
          contributionDate: testDate,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        final errors = contribution.validate();
        expect(errors, contains('Contribution amount must be greater than 0'));
        expect(contribution.isValid, false);
      });

      test('should fail validation with too large amount', () {
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 10000000000, // > $100M
          contributionDate: testDate,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        final errors = contribution.validate();
        expect(errors, contains('Contribution amount is too large'));
        expect(contribution.isValid, false);
      });

      test('should fail validation with future date', () {
        final futureDate = DateTime.now().add(const Duration(days: 1));
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: futureDate,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        final errors = contribution.validate();
        expect(errors, contains('Contribution date cannot be in the future'));
        expect(contribution.isValid, false);
      });

      test('should fail validation with too old date', () {
        final tooOldDate = DateTime.now().subtract(
          const Duration(days: 3651),
        ); // > 10 years
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: tooOldDate,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        final errors = contribution.validate();
        expect(
          errors,
          contains('Contribution date cannot be more than 10 years ago'),
        );
        expect(contribution.isValid, false);
      });

      test('should fail validation with too long description', () {
        final longDescription = 'a' * 501; // > 500 characters
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: testDate,
          description: longDescription,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        final errors = contribution.validate();
        expect(
          errors,
          contains('Contribution description must be 500 characters or less'),
        );
        expect(contribution.isValid, false);
      });

      test('should pass validation with maximum valid description length', () {
        final maxDescription = 'a' * 500; // Exactly 500 characters
        final contribution = GoalContribution(
          id: 'contrib123',
          userId: 'user123',
          goalId: 'goal456',
          amountCents: 5000,
          contributionDate: testDate,
          description: maxDescription,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        final errors = contribution.validate();
        expect(errors, isEmpty);
        expect(contribution.isValid, true);
      });
    });
  });
}
