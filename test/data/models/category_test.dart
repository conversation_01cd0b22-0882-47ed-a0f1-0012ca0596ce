import 'package:budapp/data/models/category.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Category Model Tests', () {
    const testUserId = 'test-user-id';
    const testCategoryId = 'test-category-id';
    const testParentId = 'test-parent-id';
    final testDateTime = DateTime(2024, 1, 15, 10, 30);

    group('Factory Constructors', () {
      group('Category.create', () {
        test('should create category with required fields', () {
          final category = Category.create(
            userId: testUserId,
            name: 'Test Category',
            type: CategoryType.expense,
          );

          expect(category.userId, equals(testUserId));
          expect(category.name, equals('Test Category'));
          expect(category.type, equals(CategoryType.expense));
          expect(category.id, equals(''));
          expect(category.parentId, isNull);
          expect(category.description, isNull);
          expect(category.color, isNull);
          expect(category.icon, isNull);
          expect(category.isActive, isTrue);
          expect(category.sortOrder, equals(0));
          expect(category.schemaVersion, equals(1));
          expect(category.metadata, isEmpty);
          expect(category.createdAt, isA<DateTime>());
          expect(category.updatedAt, isA<DateTime>());
        });

        test('should create category with all optional fields', () {
          final category = Category.create(
            userId: testUserId,
            name: 'Test Category',
            type: CategoryType.income,
            parentId: testParentId,
            description: 'Test description',
            color: '#FF5722',
            icon: 'shopping_cart',
            sortOrder: 5,
          );

          expect(category.userId, equals(testUserId));
          expect(category.name, equals('Test Category'));
          expect(category.type, equals(CategoryType.income));
          expect(category.parentId, equals(testParentId));
          expect(category.description, equals('Test description'));
          expect(category.color, equals('#FF5722'));
          expect(category.icon, equals('shopping_cart'));
          expect(category.sortOrder, equals(5));
          expect(category.isActive, isTrue);
          expect(category.schemaVersion, equals(1));
        });

        test('should create category with current timestamp', () {
          final beforeCreation = DateTime.now();
          final category = Category.create(
            userId: testUserId,
            name: 'Test Category',
            type: CategoryType.expense,
          );
          final afterCreation = DateTime.now();

          expect(
            category.createdAt != null &&
                (category.createdAt!.isAfter(beforeCreation) ||
                    category.createdAt!.isAtSameMomentAs(beforeCreation)),
            isTrue,
          );
          expect(
            category.createdAt != null &&
                (category.createdAt!.isBefore(afterCreation) ||
                    category.createdAt!.isAtSameMomentAs(afterCreation)),
            isTrue,
          );
          expect(category.updatedAt, equals(category.createdAt));
        });
      });

      group('Category.fromJson', () {
        test('should create category from JSON with all fields', () {
          final json = {
            'id': testCategoryId,
            'userId': testUserId,
            'name': 'JSON Category',
            'type': 'expense',
            'parentId': testParentId,
            'description': 'From JSON',
            'color': '#4CAF50',
            'icon': 'money',
            'isActive': true,
            'sortOrder': 2,
            'schemaVersion': 1,
            'createdAt': testDateTime.toIso8601String(),
            'updatedAt': testDateTime.toIso8601String(),
            'metadata': {'source': 'json'},
          };

          final category = Category.fromJson(json);

          expect(category.id, equals(testCategoryId));
          expect(category.userId, equals(testUserId));
          expect(category.name, equals('JSON Category'));
          expect(category.type, equals(CategoryType.expense));
          expect(category.parentId, equals(testParentId));
          expect(category.description, equals('From JSON'));
          expect(category.color, equals('#4CAF50'));
          expect(category.icon, equals('money'));
          expect(category.isActive, isTrue);
          expect(category.sortOrder, equals(2));
          expect(category.schemaVersion, equals(1));
          expect(category.createdAt, equals(testDateTime));
          expect(category.updatedAt, equals(testDateTime));
          expect(category.metadata, equals({'source': 'json'}));
        });

        test('should handle JSON with null metadata', () {
          final json = {
            'id': testCategoryId,
            'userId': testUserId,
            'name': 'No Metadata Category',
            'type': 'income',
            'isActive': true,
            'sortOrder': 0,
            'schemaVersion': 1,
            'createdAt': testDateTime.toIso8601String(),
            'updatedAt': testDateTime.toIso8601String(),
            'metadata': null,
          };

          final category = Category.fromJson(json);

          expect(category.metadata, isEmpty);
        });

        test('should handle JSON with non-Map metadata', () {
          final json = {
            'id': testCategoryId,
            'userId': testUserId,
            'name': 'Invalid Metadata Category',
            'type': 'expense',
            'isActive': true,
            'sortOrder': 0,
            'schemaVersion': 1,
            'createdAt': testDateTime.toIso8601String(),
            'updatedAt': testDateTime.toIso8601String(),
            'metadata': 'invalid',
          };

          final category = Category.fromJson(json);

          expect(category.metadata, isEmpty);
        });

        test('should convert Map metadata to proper type', () {
          final json = {
            'id': testCategoryId,
            'userId': testUserId,
            'name': 'Map Metadata Category',
            'type': 'expense',
            'isActive': true,
            'sortOrder': 0,
            'schemaVersion': 1,
            'createdAt': testDateTime.toIso8601String(),
            'updatedAt': testDateTime.toIso8601String(),
            'metadata': {
              'nested': {'key': 'value'},
            },
          };

          final category = Category.fromJson(json);

          expect(category.metadata, isA<Map<String, dynamic>>());
          expect(category.metadata['nested'], equals({'key': 'value'}));
        });
      });
    });

    group('Serialization Methods', () {
      late Category testCategory;

      setUp(() {
        testCategory = Category(
          id: testCategoryId,
          userId: testUserId,
          name: 'Test Category',
          type: CategoryType.expense,
          parentId: testParentId,
          description: 'Test description',
          color: '#FF9800',
          icon: 'category_icon',
          isActive: true,
          sortOrder: 1,
          schemaVersion: 1,
          createdAt: testDateTime,
          updatedAt: testDateTime,
          metadata: {'test': 'data'},
        );
      });

      test('toJson should serialize category correctly', () {
        final json = testCategory.toJson();

        expect(json['id'], equals(testCategoryId));
        expect(json['userId'], equals(testUserId));
        expect(json['name'], equals('Test Category'));
        expect(json['type'], equals('expense'));
        expect(json['parentId'], equals(testParentId));
        expect(json['description'], equals('Test description'));
        expect(json['color'], equals('#FF9800'));
        expect(json['icon'], equals('category_icon'));
        expect(json['isActive'], isTrue);
        expect(json['sortOrder'], equals(1));
        expect(json['schemaVersion'], equals(1));
        expect(json['createdAt'], equals(testDateTime.toIso8601String()));
        expect(json['updatedAt'], equals(testDateTime.toIso8601String()));
        expect(json['metadata'], equals({'test': 'data'}));
      });

      test('toFirestore should convert DateTime to Timestamp', () {
        final firestoreData = testCategory.toFirestore();

        expect(firestoreData['createdAt'], isA<Timestamp>());
        expect(firestoreData['updatedAt'], isA<Timestamp>());
        expect(
          (firestoreData['createdAt'] as Timestamp).toDate(),
          equals(testDateTime),
        );
        expect(
          (firestoreData['updatedAt'] as Timestamp).toDate(),
          equals(testDateTime),
        );
      });

      test('toFirestore should preserve other fields', () {
        final firestoreData = testCategory.toFirestore();

        expect(firestoreData['id'], equals(testCategoryId));
        expect(firestoreData['userId'], equals(testUserId));
        expect(firestoreData['name'], equals('Test Category'));
        expect(firestoreData['type'], equals('expense'));
        expect(firestoreData['metadata'], equals({'test': 'data'}));
      });

      test('toFirestore should handle string timestamps', () {
        final categoryWithStringTimestamp = testCategory.copyWith(
          createdAt: DateTime.parse('2024-01-15T10:30:00.000Z'),
          updatedAt: DateTime.parse('2024-01-15T10:30:00.000Z'),
        );

        final firestoreData = categoryWithStringTimestamp.toFirestore();

        expect(firestoreData['createdAt'], isA<Timestamp>());
        expect(firestoreData['updatedAt'], isA<Timestamp>());
      });
    });

    group('Computed Properties', () {
      test('isRoot should return true when parentId is null', () {
        final rootCategory = Category.create(
          userId: testUserId,
          name: 'Root Category',
          type: CategoryType.expense,
        );

        expect(rootCategory.isRoot, isTrue);
        expect(rootCategory.isSubcategory, isFalse);
      });

      test('isSubcategory should return true when parentId is not null', () {
        final subcategory = Category.create(
          userId: testUserId,
          name: 'Subcategory',
          type: CategoryType.expense,
          parentId: testParentId,
        );

        expect(subcategory.isSubcategory, isTrue);
        expect(subcategory.isRoot, isFalse);
      });

      test('updated should return category with new updatedAt timestamp', () {
        final originalCategory = Category.create(
          userId: testUserId,
          name: 'Original Category',
          type: CategoryType.expense,
        );

        // Wait a small amount to ensure timestamp difference
        final updatedCategory = originalCategory.updated();

        expect(updatedCategory.id, equals(originalCategory.id));
        expect(updatedCategory.name, equals(originalCategory.name));
        expect(updatedCategory.createdAt, equals(originalCategory.createdAt));
        expect(
          updatedCategory.updatedAt != null &&
              originalCategory.updatedAt != null &&
              updatedCategory.updatedAt!.isAfter(originalCategory.updatedAt!),
          isTrue,
        );
      });
    });

    group('CategoryType Enum', () {
      test('should serialize to correct JSON values', () {
        expect(CategoryType.income.name, equals('income'));
        expect(CategoryType.expense.name, equals('expense'));
      });

      test('should work in category creation', () {
        final incomeCategory = Category.create(
          userId: testUserId,
          name: 'Income Category',
          type: CategoryType.income,
        );

        final expenseCategory = Category.create(
          userId: testUserId,
          name: 'Expense Category',
          type: CategoryType.expense,
        );

        expect(incomeCategory.type, equals(CategoryType.income));
        expect(expenseCategory.type, equals(CategoryType.expense));
      });
    });
  });

  group('CategoryTree Model Tests', () {
    late Category rootCategory;
    late Category childCategory1;
    late Category childCategory2;
    late Category grandchildCategory;

    setUp(() {
      rootCategory = Category.create(
        userId: 'test-user',
        name: 'Root Category',
        type: CategoryType.expense,
      );

      childCategory1 = Category.create(
        userId: 'test-user',
        name: 'Child 1',
        type: CategoryType.expense,
        parentId: 'root-id',
      );

      childCategory2 = Category.create(
        userId: 'test-user',
        name: 'Child 2',
        type: CategoryType.expense,
        parentId: 'root-id',
      );

      grandchildCategory = Category.create(
        userId: 'test-user',
        name: 'Grandchild',
        type: CategoryType.expense,
        parentId: 'child1-id',
      );
    });

    group('Factory Constructor', () {
      test('should create CategoryTree with default values', () {
        final tree = CategoryTree(category: rootCategory);

        expect(tree.category, equals(rootCategory));
        expect(tree.children, isEmpty);
        expect(tree.depth, equals(0));
      });

      test('should create CategoryTree with custom values', () {
        final childTree = CategoryTree(category: childCategory1, depth: 1);

        final tree = CategoryTree(
          category: rootCategory,
          children: [childTree],
          depth: 0,
        );

        expect(tree.category, equals(rootCategory));
        expect(tree.children, hasLength(1));
        expect(tree.children.first, equals(childTree));
        expect(tree.depth, equals(0));
      });
    });

    group('Computed Properties', () {
      test('hasChildren should return false for empty children', () {
        final tree = CategoryTree(category: rootCategory);

        expect(tree.hasChildren, isFalse);
      });

      test('hasChildren should return true for non-empty children', () {
        final childTree = CategoryTree(category: childCategory1);
        final tree = CategoryTree(
          category: rootCategory,
          children: [childTree],
        );

        expect(tree.hasChildren, isTrue);
      });

      test('totalCount should return 1 for single node', () {
        final tree = CategoryTree(category: rootCategory);

        expect(tree.totalCount, equals(1));
      });

      test('totalCount should count all nodes in tree', () {
        final grandchildTree = CategoryTree(category: grandchildCategory);
        final childTree1 = CategoryTree(
          category: childCategory1,
          children: [grandchildTree],
        );
        final childTree2 = CategoryTree(category: childCategory2);
        final tree = CategoryTree(
          category: rootCategory,
          children: [childTree1, childTree2],
        );

        expect(
          tree.totalCount,
          equals(4),
        ); // root + child1 + child2 + grandchild
      });
    });

    group('getAllDescendants', () {
      test('should return empty list for leaf node', () {
        final tree = CategoryTree(category: rootCategory);

        final descendants = tree.getAllDescendants();

        expect(descendants, isEmpty);
      });

      test('should return direct children only', () {
        final childTree1 = CategoryTree(category: childCategory1);
        final childTree2 = CategoryTree(category: childCategory2);
        final tree = CategoryTree(
          category: rootCategory,
          children: [childTree1, childTree2],
        );

        final descendants = tree.getAllDescendants();

        expect(descendants, hasLength(2));
        expect(descendants, contains(childCategory1));
        expect(descendants, contains(childCategory2));
      });

      test('should return all descendants recursively', () {
        final grandchildTree = CategoryTree(category: grandchildCategory);
        final childTree1 = CategoryTree(
          category: childCategory1,
          children: [grandchildTree],
        );
        final childTree2 = CategoryTree(category: childCategory2);
        final tree = CategoryTree(
          category: rootCategory,
          children: [childTree1, childTree2],
        );

        final descendants = tree.getAllDescendants();

        expect(descendants, hasLength(3));
        expect(descendants, contains(childCategory1));
        expect(descendants, contains(childCategory2));
        expect(descendants, contains(grandchildCategory));
      });

      test('should maintain correct order in descendants', () {
        final grandchildTree = CategoryTree(category: grandchildCategory);
        final childTree1 = CategoryTree(
          category: childCategory1,
          children: [grandchildTree],
        );
        final childTree2 = CategoryTree(category: childCategory2);
        final tree = CategoryTree(
          category: rootCategory,
          children: [childTree1, childTree2],
        );

        final descendants = tree.getAllDescendants();

        expect(descendants[0], equals(childCategory1));
        expect(descendants[1], equals(grandchildCategory));
        expect(descendants[2], equals(childCategory2));
      });
    });

    group('Serialization', () {
      test('should create CategoryTree structure correctly', () {
        final childTree = CategoryTree(
          category: childCategory1,
          depth: 1,
        );

        final tree = CategoryTree(
          category: rootCategory,
          children: [childTree],
          depth: 0,
        );

        expect(tree.category.name, equals(rootCategory.name));
        expect(tree.children, hasLength(1));
        expect(tree.children.first.category.name, equals(childCategory1.name));
        expect(tree.depth, equals(0));
      });
    });
  });
}
