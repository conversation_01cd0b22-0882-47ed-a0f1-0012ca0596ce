import 'package:budapp/data/models/user_profile.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes for Firebase User
class MockUser extends Mock implements User {}

class MockUserMetadata extends Mock implements UserMetadata {}

class MockUserInfo extends Mock implements UserInfo {}

void main() {
  group('UserProfile Model Tests', () {
    const testUid = 'test-uid-123';
    const testEmail = '<EMAIL>';
    const testDisplayName = 'Test User';
    const testPhotoURL = 'https://example.com/photo.jpg';
    final testCreatedAt = DateTime(2024, 1, 1, 10, 0);
    final testLastLoginAt = DateTime(2024, 1, 15, 14, 30);

    group('Factory Constructor', () {
      test('should create UserProfile with all required fields', () {
        final userProfile = UserProfile(
          uid: testUid,
          email: testEmail,
          displayName: testDisplayName,
          photoURL: testPhotoURL,
          createdAt: testCreatedAt,
          lastLoginAt: testLastLoginAt,
          isEmailVerified: true,
          authProviders: ['google.com', 'password'],
          preferences: {'theme': 'dark'},
          schemaVersion: 2,
        );

        expect(userProfile.uid, equals(testUid));
        expect(userProfile.email, equals(testEmail));
        expect(userProfile.displayName, equals(testDisplayName));
        expect(userProfile.photoURL, equals(testPhotoURL));
        expect(userProfile.createdAt, equals(testCreatedAt));
        expect(userProfile.lastLoginAt, equals(testLastLoginAt));
        expect(userProfile.isEmailVerified, isTrue);
        expect(userProfile.authProviders, equals(['google.com', 'password']));
        expect(userProfile.preferences, equals({'theme': 'dark'}));
        expect(userProfile.schemaVersion, equals(2));
      });

      test('should create UserProfile with default values', () {
        final userProfile = UserProfile(
          uid: testUid,
          createdAt: testCreatedAt,
          lastLoginAt: testLastLoginAt,
        );

        expect(userProfile.uid, equals(testUid));
        expect(userProfile.email, isNull);
        expect(userProfile.displayName, isNull);
        expect(userProfile.photoURL, isNull);
        expect(userProfile.createdAt, equals(testCreatedAt));
        expect(userProfile.lastLoginAt, equals(testLastLoginAt));
        expect(userProfile.isEmailVerified, isFalse);
        expect(userProfile.authProviders, isEmpty);
        expect(userProfile.preferences, isEmpty);
        expect(userProfile.schemaVersion, equals(1));
      });
    });

    group('fromFirebaseUser Factory', () {
      test('should create UserProfile from Firebase User with all data', () {
        final mockMetadata = MockUserMetadata();
        when(() => mockMetadata.creationTime).thenReturn(testCreatedAt);
        when(() => mockMetadata.lastSignInTime).thenReturn(testLastLoginAt);

        final mockUserInfo1 = MockUserInfo();
        when(() => mockUserInfo1.providerId).thenReturn('google.com');

        final mockUserInfo2 = MockUserInfo();
        when(() => mockUserInfo2.providerId).thenReturn('password');

        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn(testUid);
        when(() => mockUser.email).thenReturn(testEmail);
        when(() => mockUser.displayName).thenReturn(testDisplayName);
        when(() => mockUser.photoURL).thenReturn(testPhotoURL);
        when(() => mockUser.emailVerified).thenReturn(true);
        when(() => mockUser.metadata).thenReturn(mockMetadata);
        when(
          () => mockUser.providerData,
        ).thenReturn([mockUserInfo1, mockUserInfo2]);

        final userProfile = UserProfile.fromFirebaseUser(mockUser);

        expect(userProfile.uid, equals(testUid));
        expect(userProfile.email, equals(testEmail));
        expect(userProfile.displayName, equals(testDisplayName));
        expect(userProfile.photoURL, equals(testPhotoURL));
        expect(userProfile.createdAt, equals(testCreatedAt));
        expect(userProfile.lastLoginAt, equals(testLastLoginAt));
        expect(userProfile.isEmailVerified, isTrue);
        expect(userProfile.authProviders, equals(['google.com', 'password']));
        expect(userProfile.preferences, isEmpty);
        expect(userProfile.schemaVersion, equals(1));
      });

      test(
        'should create UserProfile from Firebase User with minimal data',
        () {
          final mockMetadata = MockUserMetadata();
          when(() => mockMetadata.creationTime).thenReturn(null);
          when(() => mockMetadata.lastSignInTime).thenReturn(null);

          final mockUser = MockUser();
          when(() => mockUser.uid).thenReturn(testUid);
          when(() => mockUser.email).thenReturn(null);
          when(() => mockUser.displayName).thenReturn(null);
          when(() => mockUser.photoURL).thenReturn(null);
          when(() => mockUser.emailVerified).thenReturn(false);
          when(() => mockUser.metadata).thenReturn(mockMetadata);
          when(() => mockUser.providerData).thenReturn([]);

          final userProfile = UserProfile.fromFirebaseUser(mockUser);

          expect(userProfile.uid, equals(testUid));
          expect(userProfile.email, isNull);
          expect(userProfile.displayName, isNull);
          expect(userProfile.photoURL, isNull);
          expect(userProfile.isEmailVerified, isFalse);
          expect(userProfile.authProviders, isEmpty);
          expect(userProfile.preferences, isEmpty);
          expect(userProfile.schemaVersion, equals(1));
          // Should use DateTime.now() when metadata times are null
          expect(userProfile.createdAt, isNotNull);
          expect(userProfile.lastLoginAt, isNotNull);
        },
      );

      test('should handle null metadata times with DateTime.now()', () {
        final mockMetadata = MockUserMetadata();
        when(() => mockMetadata.creationTime).thenReturn(null);
        when(() => mockMetadata.lastSignInTime).thenReturn(null);

        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn(testUid);
        when(() => mockUser.email).thenReturn(null);
        when(() => mockUser.displayName).thenReturn(null);
        when(() => mockUser.photoURL).thenReturn(null);
        when(() => mockUser.emailVerified).thenReturn(false);
        when(() => mockUser.metadata).thenReturn(mockMetadata);
        when(() => mockUser.providerData).thenReturn([]);

        final userProfile = UserProfile.fromFirebaseUser(mockUser);
        final now = DateTime.now();

        expect(userProfile.uid, equals(testUid));
        // Should be close to now since it uses DateTime.now() as fallback
        expect(
          userProfile.createdAt?.difference(now).inSeconds.abs() ?? 0,
          lessThan(5),
        );
        expect(
          userProfile.lastLoginAt?.difference(now).inSeconds.abs() ?? 0,
          lessThan(5),
        );
      });
    });

    group('JSON Serialization', () {
      test('should serialize and deserialize correctly', () {
        final originalProfile = UserProfile(
          uid: testUid,
          email: testEmail,
          displayName: testDisplayName,
          photoURL: testPhotoURL,
          createdAt: testCreatedAt,
          lastLoginAt: testLastLoginAt,
          isEmailVerified: true,
          authProviders: ['google.com', 'password'],
          preferences: {'theme': 'dark', 'language': 'en'},
          schemaVersion: 2,
        );

        final json = originalProfile.toJson();
        final deserializedProfile = UserProfile.fromJson(json);

        expect(deserializedProfile.uid, equals(originalProfile.uid));
        expect(deserializedProfile.email, equals(originalProfile.email));
        expect(
          deserializedProfile.displayName,
          equals(originalProfile.displayName),
        );
        expect(deserializedProfile.photoURL, equals(originalProfile.photoURL));
        expect(
          deserializedProfile.createdAt,
          equals(originalProfile.createdAt),
        );
        expect(
          deserializedProfile.lastLoginAt,
          equals(originalProfile.lastLoginAt),
        );
        expect(
          deserializedProfile.isEmailVerified,
          equals(originalProfile.isEmailVerified),
        );
        expect(
          deserializedProfile.authProviders,
          equals(originalProfile.authProviders),
        );
        expect(
          deserializedProfile.preferences,
          equals(originalProfile.preferences),
        );
        expect(
          deserializedProfile.schemaVersion,
          equals(originalProfile.schemaVersion),
        );
      });

      test(
        'should handle backward compatibility for missing schemaVersion',
        () {
          final jsonWithoutSchemaVersion = {
            'uid': testUid,
            'email': testEmail,
            'displayName': testDisplayName,
            'photoURL': testPhotoURL,
            'createdAt': testCreatedAt.toIso8601String(),
            'lastLoginAt': testLastLoginAt.toIso8601String(),
            'isEmailVerified': true,
            'authProviders': ['google.com'],
            'preferences': {'theme': 'light'},
            // Note: schemaVersion is missing
          };

          final userProfile = UserProfile.fromJson(jsonWithoutSchemaVersion);

          expect(userProfile.uid, equals(testUid));
          expect(userProfile.email, equals(testEmail));
          expect(userProfile.schemaVersion, equals(1)); // Should default to 1
        },
      );

      test('should handle null values in JSON', () {
        final jsonWithNulls = {
          'uid': testUid,
          'email': null,
          'displayName': null,
          'photoURL': null,
          'createdAt': testCreatedAt.toIso8601String(),
          'lastLoginAt': testLastLoginAt.toIso8601String(),
          'isEmailVerified': false,
          'authProviders': <String>[],
          'preferences': <String, dynamic>{},
          'schemaVersion': 1,
        };

        final userProfile = UserProfile.fromJson(jsonWithNulls);

        expect(userProfile.uid, equals(testUid));
        expect(userProfile.email, isNull);
        expect(userProfile.displayName, isNull);
        expect(userProfile.photoURL, isNull);
        expect(userProfile.isEmailVerified, isFalse);
        expect(userProfile.authProviders, isEmpty);
        expect(userProfile.preferences, isEmpty);
        expect(userProfile.schemaVersion, equals(1));
      });
    });

    group('Edge Cases', () {
      test('should handle empty provider data list', () {
        final mockMetadata = MockUserMetadata();
        when(() => mockMetadata.creationTime).thenReturn(testCreatedAt);
        when(() => mockMetadata.lastSignInTime).thenReturn(testLastLoginAt);

        final mockUser = MockUser();
        when(() => mockUser.uid).thenReturn(testUid);
        when(() => mockUser.email).thenReturn(testEmail);
        when(() => mockUser.displayName).thenReturn(testDisplayName);
        when(() => mockUser.photoURL).thenReturn(testPhotoURL);
        when(() => mockUser.emailVerified).thenReturn(false);
        when(() => mockUser.metadata).thenReturn(mockMetadata);
        when(() => mockUser.providerData).thenReturn([]); // Empty list

        final userProfile = UserProfile.fromFirebaseUser(mockUser);

        expect(userProfile.authProviders, isEmpty);
      });

      test('should handle complex preferences object', () {
        final complexPreferences = {
          'theme': 'dark',
          'notifications': {'email': true, 'push': false},
          'settings': {
            'currency': 'USD',
            'language': 'en',
            'features': ['budgets', 'goals'],
          },
        };

        final userProfile = UserProfile(
          uid: testUid,
          createdAt: testCreatedAt,
          lastLoginAt: testLastLoginAt,
          preferences: complexPreferences,
        );

        final json = userProfile.toJson();
        final deserializedProfile = UserProfile.fromJson(json);

        expect(deserializedProfile.preferences, equals(complexPreferences));
      });
    });
  });
}
