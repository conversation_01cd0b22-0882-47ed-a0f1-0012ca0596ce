import 'package:budapp/data/repositories/implementations/transaction_repository_impl.dart';
import 'package:budapp/features/budgets/services/budget_transaction_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/mock_data_factory.dart';

// Mock classes for testing
class MockBudgetTransactionService extends Mock
    implements BudgetTransactionService {}

void main() {
  setUpAll(() {
    // Use MockDataFactory to create fallback values instead of Fake classes
    registerFallbackValue(MockDataFactory.createTransaction());
  });
  group('Transaction Deletion with Balance Adjustments', () {
    late FakeFirebaseFirestore fakeFirestore;
    late FirestoreService firestoreService;
    late MockBudgetTransactionService mockBudgetTransactionService;
    late TransactionRepositoryImpl repository;
    late String testUserId;
    late String testAccountId;
    late String testToAccountId;

    setUp(() async {
      fakeFirestore = FakeFirebaseFirestore();
      firestoreService = FirestoreService(fakeFirestore);
      mockBudgetTransactionService = MockBudgetTransactionService();
      repository = TransactionRepositoryImpl(
        firestoreService,
        mockBudgetTransactionService,
      );

      // Setup mock budget transaction service
      when(
        () => mockBudgetTransactionService.updateBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      when(
        () => mockBudgetTransactionService.revertBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async {});

      when(
        () => mockBudgetTransactionService.updateBudgetsForTransactionUpdate(
          any(),
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      testUserId = 'test-user-123';
      testAccountId = 'test-account-123';
      testToAccountId = 'test-account-456';

      // Create test accounts
      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId)
          .set({
            'id': testAccountId,
            'userId': testUserId,
            'name': 'Test Account',
            'type': 'checking',
            'classification': 'asset',
            'currencyCode': 'USD',
            'initialBalanceCents': 100000, // $1000.00
            'currentBalanceCents': 100000, // $1000.00
            'isPrimary': false,
            'isActive': true,
            'createdAt': DateTime.now().toIso8601String(),
            'updatedAt': DateTime.now().toIso8601String(),
            'metadata': <String, dynamic>{},
          });

      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testToAccountId)
          .set({
            'id': testToAccountId,
            'userId': testUserId,
            'name': 'Test To Account',
            'type': 'savings',
            'classification': 'asset',
            'currencyCode': 'USD',
            'initialBalanceCents': 50000, // $500.00
            'currentBalanceCents': 50000, // $500.00
            'isPrimary': false,
            'isActive': true,
            'createdAt': DateTime.now().toIso8601String(),
            'updatedAt': DateTime.now().toIso8601String(),
            'metadata': <String, dynamic>{},
          });
    });

    group('Income Transaction Deletion', () {
      test(
        'should delete income transaction and reverse balance adjustment',
        () async {
          // Create an income transaction that adds $100 to testToAccountId
          final transactionId = await repository.createIncomeTransaction(
            userId: testUserId,
            toAccountId: testToAccountId,
            amountCents: 10000, // $100.00

            transactionDate: DateTime.now(),
            description: 'Test Income',
          );

          // Verify account balance increased
          final accountAfterIncome = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testToAccountId)
              .get();
          expect(
            accountAfterIncome.data()!['currentBalanceCents'],
            60000,
          ); // $500 + $100

          // Delete the transaction
          await repository.deleteTransaction(testUserId, transactionId);

          // Verify transaction is deleted
          final deletedTransaction = await repository.getTransactionById(
            testUserId,
            transactionId,
          );
          expect(deletedTransaction, isNull);

          // Verify account balance is restored
          final accountAfterDeletion = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testToAccountId)
              .get();
          expect(
            accountAfterDeletion.data()!['currentBalanceCents'],
            50000,
          ); // Back to $500
        },
      );
    });

    group('Expense Transaction Deletion', () {
      test(
        'should delete expense transaction and reverse balance adjustment',
        () async {
          // Create an expense transaction that subtracts $50 from testAccountId
          final transactionId = await repository.createExpenseTransaction(
            userId: testUserId,
            fromAccountId: testAccountId,
            amountCents: 5000, // $50.00

            transactionDate: DateTime.now(),
            description: 'Test Expense',
          );

          // Verify account balance decreased
          final accountAfterExpense = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();
          expect(
            accountAfterExpense.data()!['currentBalanceCents'],
            95000,
          ); // $1000 - $50

          // Delete the transaction
          await repository.deleteTransaction(testUserId, transactionId);

          // Verify transaction is deleted
          final deletedTransaction = await repository.getTransactionById(
            testUserId,
            transactionId,
          );
          expect(deletedTransaction, isNull);

          // Verify account balance is restored
          final accountAfterDeletion = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();
          expect(
            accountAfterDeletion.data()!['currentBalanceCents'],
            100000,
          ); // Back to $1000
        },
      );
    });

    group('Transfer Transaction Deletion', () {
      test(
        'should delete transfer transaction and reverse both balance adjustments',
        () async {
          // Create a transfer transaction that moves $75 from testAccountId to testToAccountId
          final transactionId = await repository.createTransferTransaction(
            userId: testUserId,
            fromAccountId: testAccountId,
            toAccountId: testToAccountId,
            amountCents: 7500, // $75.00

            transactionDate: DateTime.now(),
            description: 'Test Transfer',
          );

          // Verify balances changed
          final fromAccountAfterTransfer = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();
          final toAccountAfterTransfer = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testToAccountId)
              .get();

          expect(
            fromAccountAfterTransfer.data()!['currentBalanceCents'],
            92500,
          ); // $1000 - $75
          expect(
            toAccountAfterTransfer.data()!['currentBalanceCents'],
            57500,
          ); // $500 + $75

          // Delete the transaction
          await repository.deleteTransaction(testUserId, transactionId);

          // Verify transaction is deleted
          final deletedTransaction = await repository.getTransactionById(
            testUserId,
            transactionId,
          );
          expect(deletedTransaction, isNull);

          // Verify both account balances are restored
          final fromAccountAfterDeletion = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();
          final toAccountAfterDeletion = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testToAccountId)
              .get();

          expect(
            fromAccountAfterDeletion.data()!['currentBalanceCents'],
            100000,
          ); // Back to $1000
          expect(
            toAccountAfterDeletion.data()!['currentBalanceCents'],
            50000,
          ); // Back to $500
        },
      );
    });

    group('Error Cases', () {
      test('should throw error when transaction not found', () async {
        expect(
          () => repository.deleteTransaction(testUserId, 'non-existent-id'),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should throw error when user does not match', () async {
        // Create transaction for testUserId
        final transactionId = await repository.createIncomeTransaction(
          userId: testUserId,
          toAccountId: testToAccountId,
          amountCents: 10000,

          transactionDate: DateTime.now(),
        );

        // Try to delete with different userId
        expect(
          () => repository.deleteTransaction('different-user', transactionId),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('Atomic Operations', () {
      test(
        'should ensure transaction deletion and balance updates are atomic',
        () async {
          // This test verifies that if balance update fails, transaction is not deleted
          // We'll simulate this by creating a transaction and then removing the account
          final transactionId = await repository.createIncomeTransaction(
            userId: testUserId,
            toAccountId: testToAccountId,
            amountCents: 10000,

            transactionDate: DateTime.now(),
          );

          // Remove the account to cause balance update to fail
          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testToAccountId)
              .delete();

          // Deletion should fail due to missing account
          expect(
            () => repository.deleteTransaction(testUserId, transactionId),
            throwsA(isA<ArgumentError>()),
          );

          // Transaction should still exist since operation failed
          final transaction = await repository.getTransactionById(
            testUserId,
            transactionId,
          );
          expect(transaction, isNotNull);
        },
      );
    });
  });
}
