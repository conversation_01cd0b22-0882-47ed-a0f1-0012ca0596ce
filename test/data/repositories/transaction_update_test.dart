import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/implementations/transaction_repository_impl.dart';
import 'package:budapp/features/budgets/services/budget_transaction_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/mock_data_factory.dart';

// Mock classes for testing
class MockBudgetTransactionService extends Mock
    implements BudgetTransactionService {}

void main() {
  setUpAll(() {
    // Use MockDataFactory to create fallback values instead of Fake classes
    registerFallbackValue(MockDataFactory.createTransaction());
  });
  group('Transaction Update with Balance Adjustments', () {
    late FakeFirebaseFirestore fakeFirestore;
    late FirestoreService firestoreService;
    late MockBudgetTransactionService mockBudgetTransactionService;
    late TransactionRepositoryImpl repository;
    late String testUserId;
    late String testAccountId;
    late String testToAccountId;
    late String testTransactionId;

    setUp(() async {
      fakeFirestore = FakeFirebaseFirestore();
      firestoreService = FirestoreService(fakeFirestore);
      mockBudgetTransactionService = MockBudgetTransactionService();
      repository = TransactionRepositoryImpl(
        firestoreService,
        mockBudgetTransactionService,
      );

      // Setup mock budget transaction service
      when(
        () => mockBudgetTransactionService.updateBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      when(
        () => mockBudgetTransactionService.revertBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async {});

      when(
        () => mockBudgetTransactionService.updateBudgetsForTransactionUpdate(
          any(),
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      testUserId = 'test-user-123';
      testAccountId = 'test-account-123';
      testToAccountId = 'test-account-456';
      testTransactionId = 'test-transaction-123';

      // Create test accounts
      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId)
          .set({
            'id': testAccountId,
            'userId': testUserId,
            'name': 'Test Account',
            'type': 'checking',
            'classification': 'asset',
            'currencyCode': 'USD',
            'initialBalanceCents': 100000, // $1000.00
            'currentBalanceCents': 100000, // $1000.00
            'isPrimary': false,
            'isActive': true,
            'createdAt': DateTime.now().toIso8601String(),
            'updatedAt': DateTime.now().toIso8601String(),
            'metadata': <String, dynamic>{},
          });

      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testToAccountId)
          .set({
            'id': testToAccountId,
            'userId': testUserId,
            'name': 'Test To Account',
            'type': 'savings',
            'classification': 'asset',
            'currencyCode': 'USD',
            'initialBalanceCents': 50000, // $500.00
            'currentBalanceCents': 50000, // $500.00
            'isPrimary': false,
            'isActive': true,
            'createdAt': DateTime.now().toIso8601String(),
            'updatedAt': DateTime.now().toIso8601String(),
            'metadata': <String, dynamic>{},
          });
    });

    group('Income Transaction Updates', () {
      setUp(() async {
        // Create an initial income transaction
        final transaction = Transaction(
          id: testTransactionId,
          userId: testUserId,
          type: TransactionType.income,
          status: TransactionStatus.completed,
          amountCents: 25000, // $250.00

          toAccountId: testAccountId,
          description: 'Initial income',
          transactionDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('transactions')
            .doc(testTransactionId)
            .set(transaction.toJson());

        // Update account balance to reflect the transaction
        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testAccountId)
            .update({'currentBalanceCents': 125000}); // $1000 + $250 = $1250
      });

      test(
        'should update income amount and adjust account balance correctly',
        () async {
          // Update the transaction amount from $250 to $300
          final updatedTransaction = Transaction(
            id: testTransactionId,
            userId: testUserId,
            type: TransactionType.income,
            status: TransactionStatus.completed,
            amountCents: 30000, // $300.00 (was $250.00)

            toAccountId: testAccountId,
            description: 'Updated income',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await repository.updateTransactionWithBalanceAdjustment(
            testUserId,
            testTransactionId,
            updatedTransaction,
          );

          // Verify transaction was updated
          final transactionDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(testTransactionId)
              .get();

          expect(transactionDoc.exists, true);
          final savedTransaction = Transaction.fromJson(transactionDoc.data()!);
          expect(savedTransaction.amountCents, 30000);
          expect(savedTransaction.description, 'Updated income');

          // Verify account balance was adjusted correctly
          // Original: $1000 + $250 = $1250
          // After update: $1000 + $300 = $1300 (difference of +$50)
          final accountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();

          final updatedAccount = Account.fromJson(accountDoc.data()!);
          expect(updatedAccount.currentBalanceCents, 130000); // $1300.00
        },
      );

      test('should handle account change for income transaction', () async {
        // Change the destination account from testAccountId to testToAccountId
        final updatedTransaction = Transaction(
          id: testTransactionId,
          userId: testUserId,
          type: TransactionType.income,
          status: TransactionStatus.completed,
          amountCents: 25000, // Same amount

          toAccountId: testToAccountId, // Changed account
          description: 'Income to different account',
          transactionDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await repository.updateTransactionWithBalanceAdjustment(
          testUserId,
          testTransactionId,
          updatedTransaction,
        );

        // Verify original account balance was reduced
        final originalAccountDoc = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testAccountId)
            .get();

        final originalAccount = Account.fromJson(originalAccountDoc.data()!);
        expect(originalAccount.currentBalanceCents, 100000); // Back to $1000

        // Verify new account balance was increased
        final newAccountDoc = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testToAccountId)
            .get();

        final newAccount = Account.fromJson(newAccountDoc.data()!);
        expect(newAccount.currentBalanceCents, 75000); // $500 + $250 = $750
      });
    });

    group('Expense Transaction Updates', () {
      setUp(() async {
        // Create an initial expense transaction
        final transaction = Transaction(
          id: testTransactionId,
          userId: testUserId,
          type: TransactionType.expense,
          status: TransactionStatus.completed,
          amountCents: 15000, // $150.00

          fromAccountId: testAccountId,
          description: 'Initial expense',
          transactionDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('transactions')
            .doc(testTransactionId)
            .set(transaction.toJson());

        // Update account balance to reflect the transaction
        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testAccountId)
            .update({'currentBalanceCents': 85000}); // $1000 - $150 = $850
      });

      test(
        'should update expense amount and adjust account balance correctly',
        () async {
          // Update the transaction amount from $150 to $200
          final updatedTransaction = Transaction(
            id: testTransactionId,
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 20000, // $200.00 (was $150.00)

            fromAccountId: testAccountId,
            description: 'Updated expense',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await repository.updateTransactionWithBalanceAdjustment(
            testUserId,
            testTransactionId,
            updatedTransaction,
          );

          // Verify account balance was adjusted correctly
          // Original: $1000 - $150 = $850
          // After update: $1000 - $200 = $800 (difference of -$50)
          final accountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();

          final updatedAccount = Account.fromJson(accountDoc.data()!);
          expect(updatedAccount.currentBalanceCents, 80000); // $800.00
        },
      );
    });

    group('Transfer Transaction Updates', () {
      setUp(() async {
        // Create an initial transfer transaction
        final transaction = Transaction(
          id: testTransactionId,
          userId: testUserId,
          type: TransactionType.transfer,
          status: TransactionStatus.completed,
          amountCents: 20000, // $200.00

          fromAccountId: testAccountId,
          toAccountId: testToAccountId,
          description: 'Initial transfer',
          transactionDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('transactions')
            .doc(testTransactionId)
            .set(transaction.toJson());

        // Update account balances to reflect the transaction
        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testAccountId)
            .update({'currentBalanceCents': 80000}); // $1000 - $200 = $800

        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testToAccountId)
            .update({'currentBalanceCents': 70000}); // $500 + $200 = $700
      });

      test(
        'should update transfer amount and adjust both account balances correctly',
        () async {
          // Update the transaction amount from $200 to $300
          final updatedTransaction = Transaction(
            id: testTransactionId,
            userId: testUserId,
            type: TransactionType.transfer,
            status: TransactionStatus.completed,
            amountCents: 30000, // $300.00 (was $200.00)

            fromAccountId: testAccountId,
            toAccountId: testToAccountId,
            description: 'Updated transfer',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await repository.updateTransactionWithBalanceAdjustment(
            testUserId,
            testTransactionId,
            updatedTransaction,
          );

          // Verify from account balance was adjusted correctly
          // Original: $1000 - $200 = $800
          // After update: $1000 - $300 = $700 (difference of -$100)
          final fromAccountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();

          final fromAccount = Account.fromJson(fromAccountDoc.data()!);
          expect(fromAccount.currentBalanceCents, 70000); // $700.00

          // Verify to account balance was adjusted correctly
          // Original: $500 + $200 = $700
          // After update: $500 + $300 = $800 (difference of +$100)
          final toAccountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testToAccountId)
              .get();

          final toAccount = Account.fromJson(toAccountDoc.data()!);
          expect(toAccount.currentBalanceCents, 80000); // $800.00
        },
      );
    });

    group('Error Handling', () {
      test('should throw error when transaction not found', () async {
        final updatedTransaction = Transaction(
          id: 'non-existent-id',
          userId: testUserId,
          type: TransactionType.income,
          status: TransactionStatus.completed,
          amountCents: 10000,

          toAccountId: testAccountId,
          description: 'Test',
          transactionDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(
          () => repository.updateTransactionWithBalanceAdjustment(
            testUserId,
            'non-existent-id',
            updatedTransaction,
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should throw error when account does not exist', () async {
        // Create a transaction first
        final transaction = Transaction(
          id: testTransactionId,
          userId: testUserId,
          type: TransactionType.income,
          status: TransactionStatus.completed,
          amountCents: 10000,

          toAccountId: testAccountId,
          description: 'Test',
          transactionDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('transactions')
            .doc(testTransactionId)
            .set(transaction.toJson());

        // Try to update with non-existent account
        final updatedTransaction = transaction.copyWith(
          toAccountId: 'non-existent-account',
        );

        expect(
          () => repository.updateTransactionWithBalanceAdjustment(
            testUserId,
            testTransactionId,
            updatedTransaction,
          ),
          throwsA(isA<ArgumentError>()),
        );
      });
    });
  });
}
