import 'package:budapp/data/models/goal.dart';
import 'package:budapp/data/repositories/implementations/goal_repository_impl.dart';
import 'package:budapp/data/repositories/interfaces/goal_repository.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../helpers/firebase_test_setup.dart';

void main() {
  group('GoalRepositoryImpl Tests', () {
    late FirebaseTestSetup testSetup;
    late FirestoreService firestoreService;
    late IGoalRepository goalRepository;
    late String testUserId;

    setUp(() async {
      // Create Firebase test setup with authenticated user
      testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
        uid: 'test-user-1',
        email: '<EMAIL>',
      );

      firestoreService = FirestoreService(testSetup.firestore);
      goalRepository = GoalRepositoryImpl(firestoreService, testSetup.auth);
      testUserId = 'test-user-1';
    });

    tearDown(() async {
      await testSetup.dispose();
    });

    /// Helper method to create a test goal
    Goal createTestGoal({
      String? id,
      String? userId,
      String? name,
      String? description,
      int? targetAmountCents,
      int? currentAmountCents,
      DateTime? targetDate,
      GoalStatus? status,
      String? colorHex,
      String? iconName,
      bool? isActive,
      bool? isCompleted,
    }) {
      return Goal(
        id: id ?? 'test-goal-${DateTime.now().millisecondsSinceEpoch}',
        userId: userId ?? testUserId,
        name: name ?? 'Test Goal',
        description: description ?? 'Test goal description',
        targetAmountCents: targetAmountCents ?? 100000, // $1000.00
        currentAmountCents: currentAmountCents ?? 0,
        targetDate: targetDate ?? DateTime.now().add(const Duration(days: 365)),
        status: status ?? GoalStatus.active,
        colorHex: colorHex ?? '#00FF00',
        iconName: iconName ?? 'savings',
        isActive: isActive ?? true,
        isCompleted: isCompleted ?? false,
        schemaVersion: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }

    group('Basic CRUD Operations', () {
      test('create should store goal in Firestore and return ID', () async {
        final goal = createTestGoal();

        final result = await goalRepository.create(goal);

        expect(result, isNotEmpty);
        expect(result, isA<String>());
        expect(result, equals(goal.id));

        // Verify goal was stored
        final retrievedGoal = await goalRepository.getGoalById(goal.id);
        expect(retrievedGoal, isNotNull);
        expect(retrievedGoal!.name, equals(goal.name));
        expect(retrievedGoal.userId, equals(testUserId));
      });

      test('update should modify existing goal', () async {
        final goal = createTestGoal();
        await goalRepository.create(goal);

        final updatedGoal = goal.copyWith(
          name: 'Updated Goal Name',
          description: 'Updated description',
          targetAmountCents: 150000,
        );

        await goalRepository.update(goal.id, updatedGoal);

        final retrievedGoal = await goalRepository.getGoalById(goal.id);
        expect(retrievedGoal!.name, equals('Updated Goal Name'));
        expect(retrievedGoal.description, equals('Updated description'));
        expect(retrievedGoal.targetAmountCents, equals(150000));
        expect(
          retrievedGoal.updatedAt?.isAfter(goal.updatedAt ?? DateTime.now()) ??
              false,
          isTrue,
        );
      });

      test('getGoalById should return goal for current user', () async {
        final goal = createTestGoal();
        await goalRepository.create(goal);

        final retrievedGoal = await goalRepository.getGoalById(goal.id);

        expect(retrievedGoal, isNotNull);
        expect(retrievedGoal!.id, equals(goal.id));
        expect(retrievedGoal.userId, equals(testUserId));
      });

      test('getGoalById should return null for non-existent goal', () async {
        final result = await goalRepository.getGoalById('non-existent-id');
        expect(result, isNull);
      });

      test('getGoalByIdForUser should return goal for specific user', () async {
        final goal = createTestGoal();
        await goalRepository.create(goal);

        final retrievedGoal = await goalRepository.getGoalByIdForUser(
          testUserId,
          goal.id,
        );

        expect(retrievedGoal, isNotNull);
        expect(retrievedGoal!.id, equals(goal.id));
        expect(retrievedGoal.userId, equals(testUserId));
      });

      test('getGoalsByUserId should return all goals for user', () async {
        final goal1 = createTestGoal(name: 'Goal 1');
        final goal2 = createTestGoal(name: 'Goal 2');
        final goal3 = createTestGoal(name: 'Goal 3', isActive: false);

        await goalRepository.createGoal(goal1);
        await goalRepository.createGoal(goal2);
        await goalRepository.createGoal(goal3);

        final goals = await goalRepository.getGoalsByUserId(testUserId);

        expect(goals, hasLength(3));
        expect(
          goals.map((g) => g.name),
          containsAll(['Goal 1', 'Goal 2', 'Goal 3']),
        );
        // Should be ordered by creation date (newest first)
        expect(
          goals.first.createdAt?.isAfter(
                goals.last.createdAt ?? DateTime.now(),
              ) ??
              false,
          isTrue,
        );
      });

      test('getActiveGoalsByUserId should return only active goals', () async {
        final activeGoal1 = createTestGoal(
          name: 'Active Goal 1',
          isActive: true,
        );
        final activeGoal2 = createTestGoal(
          name: 'Active Goal 2',
          isActive: true,
        );
        final inactiveGoal = createTestGoal(
          name: 'Inactive Goal',
          isActive: false,
        );

        await goalRepository.createGoal(activeGoal1);
        await goalRepository.createGoal(activeGoal2);
        await goalRepository.createGoal(inactiveGoal);

        final activeGoals = await goalRepository.getActiveGoalsByUserId(
          testUserId,
        );

        expect(activeGoals, hasLength(2));
        expect(activeGoals.every((g) => g.isActive), isTrue);
        expect(
          activeGoals.map((g) => g.name),
          containsAll(['Active Goal 1', 'Active Goal 2']),
        );
      });

      test(
        'getActiveGoals should return active goals for current user',
        () async {
          final activeGoal = createTestGoal(
            name: 'Active Goal',
            isActive: true,
          );
          final inactiveGoal = createTestGoal(
            name: 'Inactive Goal',
            isActive: false,
          );

          await goalRepository.createGoal(activeGoal);
          await goalRepository.createGoal(inactiveGoal);

          final activeGoals = await goalRepository.getActiveGoals();

          expect(activeGoals, hasLength(1));
          expect(activeGoals.first.name, equals('Active Goal'));
          expect(activeGoals.first.isActive, isTrue);
        },
      );

      test('getGoalsByStatus should filter goals by status', () async {
        final activeGoal = createTestGoal(
          name: 'Active Goal',
          status: GoalStatus.active,
        );
        final completedGoal = createTestGoal(
          name: 'Completed Goal',
          status: GoalStatus.completed,
        );
        final pausedGoal = createTestGoal(
          name: 'Paused Goal',
          status: GoalStatus.paused,
        );

        await goalRepository.createGoal(activeGoal);
        await goalRepository.createGoal(completedGoal);
        await goalRepository.createGoal(pausedGoal);

        final activeGoals = await goalRepository.getGoalsByStatus(
          testUserId,
          GoalStatus.active,
        );
        final completedGoals = await goalRepository.getGoalsByStatus(
          testUserId,
          GoalStatus.completed,
        );

        expect(activeGoals, hasLength(1));
        expect(activeGoals.first.status, equals(GoalStatus.active));
        expect(completedGoals, hasLength(1));
        expect(completedGoals.first.status, equals(GoalStatus.completed));
      });
    });

    group('Goal Management Operations', () {
      test('createGoal should create goal with auto-generated ID', () async {
        final goal = createTestGoal();

        final createdId = await goalRepository.createGoal(goal);

        expect(createdId, isNotEmpty);
        expect(createdId, isA<String>());

        final retrievedGoal = await goalRepository.getGoalById(createdId);
        expect(retrievedGoal, isNotNull);
        expect(retrievedGoal!.id, equals(createdId));
        expect(retrievedGoal.userId, equals(testUserId));
      });

      test('updateGoal should update existing goal', () async {
        final goal = createTestGoal();
        final goalId = await goalRepository.createGoal(goal);

        final updatedGoal = goal.copyWith(
          name: 'Updated Name',
          targetAmountCents: 200000,
        );

        await goalRepository.updateGoal(goalId, updatedGoal);

        final retrievedGoal = await goalRepository.getGoalById(goalId);
        expect(retrievedGoal!.name, equals('Updated Name'));
        expect(retrievedGoal.targetAmountCents, equals(200000));
        expect(retrievedGoal.userId, equals(testUserId));
      });

      test('deactivateGoal should set isActive to false', () async {
        final goal = createTestGoal(isActive: true);
        final goalId = await goalRepository.createGoal(goal);

        await goalRepository.deactivateGoal(goalId);

        final retrievedGoal = await goalRepository.getGoalById(goalId);
        expect(retrievedGoal!.isActive, isFalse);
      });

      test('deleteGoal should permanently remove goal', () async {
        final goal = createTestGoal();
        final goalId = await goalRepository.createGoal(goal);

        await goalRepository.deleteGoal(goalId);

        final retrievedGoal = await goalRepository.getGoalById(goalId);
        expect(retrievedGoal, isNull);
      });

      test('reactivateGoal should set isActive to true', () async {
        final goal = createTestGoal(isActive: false);
        final goalId = await goalRepository.createGoal(goal);

        await goalRepository.reactivateGoal(goalId);

        final retrievedGoal = await goalRepository.getGoalById(goalId);
        expect(retrievedGoal!.isActive, isTrue);
      });

      test('updateGoalProgress should update currentAmountCents', () async {
        final goal = createTestGoal(currentAmountCents: 0);
        final goalId = await goalRepository.createGoal(goal);

        await goalRepository.updateGoalProgress(goalId, 50000);

        final retrievedGoal = await goalRepository.getGoalById(goalId);
        expect(retrievedGoal!.currentAmountCents, equals(50000));
      });

      test(
        'updateGoalProgress should mark goal as completed when target reached',
        () async {
          final goal = createTestGoal(
            targetAmountCents: 100000,
            currentAmountCents: 0,
            isCompleted: false,
            status: GoalStatus.active,
          );
          final goalId = await goalRepository.createGoal(goal);

          await goalRepository.updateGoalProgress(goalId, 100000);

          final retrievedGoal = await goalRepository.getGoalById(goalId);
          expect(retrievedGoal!.currentAmountCents, equals(100000));
          expect(retrievedGoal.isCompleted, isTrue);
          expect(retrievedGoal.status, equals(GoalStatus.completed));
        },
      );

      test(
        'updateGoalProgress should reactivate completed goal when progress decreases',
        () async {
          final goal = createTestGoal(
            targetAmountCents: 100000,
            currentAmountCents: 100000,
            isCompleted: true,
            status: GoalStatus.completed,
          );
          final goalId = await goalRepository.createGoal(goal);

          await goalRepository.updateGoalProgress(goalId, 50000);

          final retrievedGoal = await goalRepository.getGoalById(goalId);
          expect(retrievedGoal!.currentAmountCents, equals(50000));
          expect(retrievedGoal.isCompleted, isFalse);
          expect(retrievedGoal.status, equals(GoalStatus.active));
        },
      );

      test('markGoalAsCompleted should set completion status', () async {
        final goal = createTestGoal(
          isCompleted: false,
          status: GoalStatus.active,
        );
        final goalId = await goalRepository.createGoal(goal);

        await goalRepository.markGoalAsCompleted(goalId);

        final retrievedGoal = await goalRepository.getGoalById(goalId);
        expect(retrievedGoal!.isCompleted, isTrue);
        expect(retrievedGoal.status, equals(GoalStatus.completed));
      });

      test('markGoalAsActive should set active status', () async {
        final goal = createTestGoal(
          isCompleted: true,
          status: GoalStatus.completed,
        );
        final goalId = await goalRepository.createGoal(goal);

        await goalRepository.markGoalAsActive(goalId);

        final retrievedGoal = await goalRepository.getGoalById(goalId);
        expect(retrievedGoal!.isCompleted, isFalse);
        expect(retrievedGoal.status, equals(GoalStatus.active));
      });
    });

    group('Search and Analytics', () {
      test('searchGoalsByName should find goals by name', () async {
        final goal1 = createTestGoal(name: 'Emergency Fund');
        final goal2 = createTestGoal(name: 'Vacation Savings');
        final goal3 = createTestGoal(name: 'House Down Payment');

        await goalRepository.createGoal(goal1);
        await goalRepository.createGoal(goal2);
        await goalRepository.createGoal(goal3);

        final results = await goalRepository.searchGoalsByName(
          testUserId,
          'Emergency',
        );

        expect(results, hasLength(1));
        expect(results.first.name, equals('Emergency Fund'));
      });

      test('searchGoalsByName should find goals by description', () async {
        final goal1 = createTestGoal(
          name: 'Goal 1',
          description: 'Emergency fund for unexpected expenses',
        );
        final goal2 = createTestGoal(
          name: 'Goal 2',
          description: 'Vacation to Europe',
        );

        await goalRepository.createGoal(goal1);
        await goalRepository.createGoal(goal2);

        final results = await goalRepository.searchGoalsByName(
          testUserId,
          'Emergency',
        );

        expect(results, hasLength(1));
        expect(results.first.description, contains('Emergency'));
      });

      test('searchGoalsByName should be case insensitive', () async {
        final goal = createTestGoal(name: 'Emergency Fund');
        await goalRepository.createGoal(goal);

        final results = await goalRepository.searchGoalsByName(
          testUserId,
          'emergency',
        );

        expect(results, hasLength(1));
        expect(results.first.name, equals('Emergency Fund'));
      });

      test('getGoalStats should return goal statistics', () async {
        final goal = createTestGoal(
          targetAmountCents: 100000,
          currentAmountCents: 25000,
          targetDate: DateTime.now().add(const Duration(days: 100)),
        );
        final goalId = await goalRepository.createGoal(goal);

        final stats = await goalRepository.getGoalStats(goalId);

        expect(stats, containsPair('progressPercentage', 0.25));
        expect(stats, containsPair('remainingAmountCents', 75000));
        expect(stats, containsPair('isAchieved', false));
        expect(stats.containsKey('daysRemaining'), isTrue);
        expect(stats.containsKey('requiredDailySavingsCents'), isTrue);
      });

      test('getGoalStats should throw for non-existent goal', () async {
        expect(
          () => goalRepository.getGoalStats('non-existent-goal'),
          throwsA(isA<Exception>()),
        );
      });

      test('getUserGoalSummary should return user statistics', () async {
        final activeGoal1 = createTestGoal(
          name: 'Active Goal 1',
          targetAmountCents: 100000,
          currentAmountCents: 25000,
          isActive: true,
        );
        final activeGoal2 = createTestGoal(
          name: 'Active Goal 2',
          targetAmountCents: 200000,
          currentAmountCents: 50000,
          isActive: true,
        );
        final completedGoal = createTestGoal(
          name: 'Completed Goal',
          targetAmountCents: 50000,
          currentAmountCents: 50000,
          isCompleted: true,
          status: GoalStatus.completed,
          isActive: false,
        );

        await goalRepository.createGoal(activeGoal1);
        await goalRepository.createGoal(activeGoal2);
        await goalRepository.createGoal(completedGoal);

        final summary = await goalRepository.getUserGoalSummary(testUserId);

        expect(summary['totalGoals'], equals(3));
        expect(summary['activeGoals'], equals(2));
        expect(summary['completedGoals'], equals(1));
        expect(summary['totalTargetAmountCents'], equals(300000));
        expect(summary['totalCurrentAmountCents'], equals(75000));
        expect(summary['overallProgressPercentage'], equals(25));
      });

      test('getDeletableGoals should return active goals', () async {
        final activeGoal = createTestGoal(name: 'Active Goal', isActive: true);
        final inactiveGoal = createTestGoal(
          name: 'Inactive Goal',
          isActive: false,
        );

        await goalRepository.createGoal(activeGoal);
        await goalRepository.createGoal(inactiveGoal);

        final deletableGoals = await goalRepository.getDeletableGoals(
          testUserId,
        );

        expect(deletableGoals, hasLength(1));
        expect(deletableGoals.first.name, equals('Active Goal'));
        expect(deletableGoals.first.isActive, isTrue);
      });
    });

    group('Real-time Streams', () {
      test('watchUserGoals should emit active goals', () async {
        final stream = goalRepository.watchUserGoals(testUserId);
        final streamTest = expectLater(
          stream.take(2),
          emitsInOrder([
            isEmpty, // Initial empty state
            hasLength(1), // After adding goal
          ]),
        );

        // Wait for stream to initialize
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Add a goal
        await goalRepository.createGoal(createTestGoal(isActive: true));

        await streamTest;
      });

      test('watchAllUserGoals should emit all goals', () async {
        final stream = goalRepository.watchAllUserGoals(testUserId);
        final streamTest = expectLater(
          stream.take(3),
          emitsInOrder([
            isEmpty, // Initial empty state
            hasLength(1), // After adding first goal
            hasLength(2), // After adding second goal
          ]),
        );

        // Wait for stream to initialize
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Add goals one by one to get separate emissions
        await goalRepository.createGoal(
          createTestGoal(name: 'Active Goal', isActive: true),
        );
        await Future<void>.delayed(const Duration(milliseconds: 100));
        await goalRepository.createGoal(
          createTestGoal(name: 'Inactive Goal', isActive: false),
        );

        await streamTest;
      });

      test('watchGoal should emit specific goal updates', () async {
        final goal = createTestGoal(name: 'Original Name');
        final goalId = await goalRepository.createGoal(goal);

        final stream = goalRepository.watchGoal(goalId);
        final streamTest = expectLater(
          stream.take(2),
          emitsInOrder([
            predicate<Goal?>((g) => g?.name == 'Original Name'),
            predicate<Goal?>((g) => g?.name == 'Updated Name'),
          ]),
        );

        // Wait for stream to initialize
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Update the goal
        await goalRepository.updateGoal(
          goalId,
          goal.copyWith(name: 'Updated Name'),
        );

        await streamTest;
      });

      test(
        'watchGoalForUser should emit goal updates for specific user',
        () async {
          final goal = createTestGoal(name: 'User Goal');
          final goalId = await goalRepository.createGoal(goal);

          final stream = goalRepository.watchGoalForUser(testUserId, goalId);
          final streamTest = expectLater(
            stream.take(1),
            emitsInOrder([
              predicate<Goal?>(
                (g) => g?.name == 'User Goal' && g?.userId == testUserId,
              ),
            ]),
          );

          await streamTest;
        },
      );

      test('watchGoal should emit null for non-existent goal', () async {
        final stream = goalRepository.watchGoal('non-existent-goal');
        final streamTest = expectLater(stream.take(1), emitsInOrder([isNull]));

        await streamTest;
      });
    });

    group('Validation and Business Logic', () {
      test('validateGoal should validate correct goal', () async {
        final validGoal = createTestGoal();

        final isValid = await goalRepository.validateGoal(validGoal);

        expect(isValid, isTrue);
      });

      test('validateGoal should throw for invalid goal', () async {
        final invalidGoal = createTestGoal(name: ''); // Empty name

        expect(
          () => goalRepository.validateGoal(invalidGoal),
          throwsA(isA<Exception>()),
        );
      });

      test('isGoalNameUnique should return true for unique name', () async {
        final isUnique = await goalRepository.isGoalNameUnique(
          testUserId,
          'Unique Goal Name',
        );

        expect(isUnique, isTrue);
      });

      test('isGoalNameUnique should return false for existing name', () async {
        final goal = createTestGoal(name: 'Existing Goal');
        await goalRepository.createGoal(goal);

        final isUnique = await goalRepository.isGoalNameUnique(
          testUserId,
          'Existing Goal',
        );

        expect(isUnique, isFalse);
      });

      test('isGoalNameUnique should exclude specific goal ID', () async {
        final goal = createTestGoal(name: 'Test Goal');
        final goalId = await goalRepository.createGoal(goal);

        final isUnique = await goalRepository.isGoalNameUnique(
          testUserId,
          'Test Goal',
          excludeGoalId: goalId,
        );

        expect(isUnique, isTrue);
      });

      test('getCompletedGoals should return only completed goals', () async {
        final activeGoal = createTestGoal(
          name: 'Active Goal',
          status: GoalStatus.active,
        );
        final completedGoal = createTestGoal(
          name: 'Completed Goal',
          status: GoalStatus.completed,
        );

        await goalRepository.createGoal(activeGoal);
        await goalRepository.createGoal(completedGoal);

        final completedGoals = await goalRepository.getCompletedGoals(
          testUserId,
        );

        expect(completedGoals, hasLength(1));
        expect(completedGoals.first.name, equals('Completed Goal'));
        expect(completedGoals.first.status, equals(GoalStatus.completed));
      });
    });

    group('Date-based Operations', () {
      test(
        'getGoalsNearingDeadline should return goals within time range',
        () async {
          final now = DateTime.now();
          final nearDeadlineGoal = createTestGoal(
            name: 'Near Deadline Goal',
            targetDate: now.add(const Duration(days: 5)),
            isActive: true,
          );
          final farDeadlineGoal = createTestGoal(
            name: 'Far Deadline Goal',
            targetDate: now.add(const Duration(days: 50)),
            isActive: true,
          );

          await goalRepository.createGoal(nearDeadlineGoal);
          await goalRepository.createGoal(farDeadlineGoal);

          final nearingGoals = await goalRepository.getGoalsNearingDeadline(
            testUserId,
            10,
          );

          expect(nearingGoals, hasLength(1));
          expect(nearingGoals.first.name, equals('Near Deadline Goal'));
        },
      );

      test(
        'getGoalsNearingDeadline should exclude goals without target date',
        () async {
          final goalWithoutDate = createTestGoal(
            name: 'No Date Goal',
            targetDate: null,
          );
          await goalRepository.createGoal(goalWithoutDate);

          final nearingGoals = await goalRepository.getGoalsNearingDeadline(
            testUserId,
            30,
          );

          expect(nearingGoals, isEmpty);
        },
      );

      test(
        'getGoalsByDateRange should return goals within date range',
        () async {
          final now = DateTime.now();
          final startDate = now.subtract(const Duration(days: 10));
          final endDate = now.add(const Duration(days: 10));

          final goalInRange = createTestGoal(name: 'In Range Goal');
          await goalRepository.createGoal(goalInRange);

          // Wait a bit to ensure different creation times
          await Future<void>.delayed(const Duration(milliseconds: 10));

          final goalOutOfRange = createTestGoal(name: 'Out of Range Goal');
          await goalRepository.createGoal(goalOutOfRange);

          final goalsInRange = await goalRepository.getGoalsByDateRange(
            testUserId,
            startDate,
            endDate,
          );

          expect(
            goalsInRange,
            hasLength(2),
          ); // Both goals should be in range for this test
          expect(goalsInRange.every((g) => g.userId == testUserId), isTrue);
        },
      );
    });

    group('Error Handling', () {
      test('should throw exception when user not authenticated', () async {
        // Create repository with unauthenticated setup
        final unauthenticatedSetup = await FirebaseTestSetup.create();
        final unauthenticatedRepository = GoalRepositoryImpl(
          FirestoreService(unauthenticatedSetup.firestore),
          unauthenticatedSetup.auth,
        );

        expect(
          unauthenticatedRepository.getActiveGoals,
          throwsA(isA<Exception>()),
        );

        await unauthenticatedSetup.dispose();
      });

      test('update should handle non-existent goal gracefully', () async {
        final goal = createTestGoal();

        // fake_cloud_firestore throws errors for non-existent documents
        // In production, this would fail silently or with proper error handling
        expect(
          () => goalRepository.update('non-existent-goal', goal),
          throwsA(anything),
        );
      });

      test('deactivateGoal should handle non-existent goal gracefully', () async {
        // fake_cloud_firestore throws errors for non-existent documents
        // In production, this would fail silently or with proper error handling
        expect(
          () => goalRepository.deactivateGoal('non-existent-goal'),
          throwsA(anything),
        );
      });

      test(
        'updateGoalProgress should handle non-existent goal gracefully',
        () async {
          // fake_cloud_firestore throws errors for non-existent documents
          // In production, this would fail silently or with proper error handling
          expect(
            () => goalRepository.updateGoalProgress('non-existent-goal', 50000),
            throwsA(anything),
          );
        },
      );

      test(
        'markGoalAsCompleted should handle non-existent goal gracefully',
        () async {
          // fake_cloud_firestore throws errors for non-existent documents
          // In production, this would fail silently or with proper error handling
          expect(
            () => goalRepository.markGoalAsCompleted('non-existent-goal'),
            throwsA(anything),
          );
        },
      );

      test(
        'markGoalAsActive should handle non-existent goal gracefully',
        () async {
          // fake_cloud_firestore throws errors for non-existent documents
          // In production, this would fail silently or with proper error handling
          expect(
            () => goalRepository.markGoalAsActive('non-existent-goal'),
            throwsA(anything),
          );
        },
      );
    });

    group('Edge Cases and Integration', () {
      test('should handle concurrent goal creation', () async {
        final goals = List.generate(5, (i) => createTestGoal(name: 'Goal $i'));

        final futures = goals.map((goal) => goalRepository.createGoal(goal));
        final results = await Future.wait(futures);

        expect(results, hasLength(5));
        expect(results.every((id) => id.isNotEmpty), isTrue);

        final allGoals = await goalRepository.getGoalsByUserId(testUserId);
        expect(allGoals, hasLength(5));
      });

      test(
        'should handle goal updates with concurrent progress updates',
        () async {
          final goal = createTestGoal(currentAmountCents: 0);
          final goalId = await goalRepository.createGoal(goal);

          // Simulate concurrent progress updates
          final futures = [
            goalRepository.updateGoalProgress(goalId, 10000),
            goalRepository.updateGoalProgress(goalId, 20000),
            goalRepository.updateGoalProgress(goalId, 30000),
          ];

          await Future.wait(futures);

          final retrievedGoal = await goalRepository.getGoalById(goalId);
          expect(retrievedGoal!.currentAmountCents, greaterThan(0));
        },
      );

      test('should handle large result sets', () async {
        // Create many goals
        final goals = List.generate(20, (i) => createTestGoal(name: 'Goal $i'));

        for (final goal in goals) {
          await goalRepository.createGoal(goal);
        }

        final allGoals = await goalRepository.getGoalsByUserId(testUserId);
        expect(allGoals, hasLength(20));
      });

      test('should handle empty search results', () async {
        final results = await goalRepository.searchGoalsByName(
          testUserId,
          'NonExistentGoal',
        );
        expect(results, isEmpty);
      });

      test('should handle goal with null optional fields', () async {
        final goal = Goal(
          id: 'test-goal-${DateTime.now().millisecondsSinceEpoch}',
          userId: testUserId,
          name: 'Test Goal',
          description: null, // Explicitly null
          targetAmountCents: 100000,
          currentAmountCents: 0,
          targetDate: null, // Explicitly null
          status: GoalStatus.active,
          colorHex: '#00FF00',
          iconName: 'savings',
          isActive: true,
          isCompleted: false,
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final goalId = await goalRepository.createGoal(goal);
        final retrievedGoal = await goalRepository.getGoalById(goalId);

        expect(retrievedGoal, isNotNull);
        expect(retrievedGoal!.description, isNull);
        expect(retrievedGoal.targetDate, isNull);
      });

      test('should handle goal progress calculations correctly', () async {
        final goal = createTestGoal(
          targetAmountCents: 100000,
          currentAmountCents: 25000,
          targetDate: DateTime.now().add(const Duration(days: 100)),
        );
        final goalId = await goalRepository.createGoal(goal);

        final stats = await goalRepository.getGoalStats(goalId);

        expect(
          stats['progressPercentage'],
          equals(0.25),
        ); // Returns as decimal fraction
        expect(stats['remainingAmountCents'], equals(75000));
        expect(stats['isAchieved'], isFalse);
      });
    });
  });
}
