import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/implementations/transaction_repository_impl.dart';
import 'package:budapp/data/repositories/interfaces/transaction_repository.dart';
import 'package:budapp/features/budgets/providers/budget_providers.dart';
import 'package:budapp/features/budgets/services/budget_transaction_service.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/firebase_test_setup.dart';
import '../../helpers/mock_data_factory.dart';

// Mock classes for testing
class MockFirestoreService extends Mock implements FirestoreService {}

class MockBudgetTransactionService extends Mock
    implements BudgetTransactionService {}

void main() {
  setUpAll(() {
    // Use MockDataFactory to create fallback values instead of Fake classes
    registerFallbackValue(MockDataFactory.createTransaction());
  });
  group('TransactionRepositoryImpl Tests', () {
    late FirebaseTestSetup testSetup;
    late FirestoreService firestoreService;
    late MockBudgetTransactionService mockBudgetTransactionService;
    late TransactionRepositoryImpl transactionRepository;

    setUp(() async {
      // Create Firebase test setup with authenticated user
      testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
        uid: 'test-user-1',
        email: '<EMAIL>',
      );

      firestoreService = FirestoreService(testSetup.firestore);
      mockBudgetTransactionService = MockBudgetTransactionService();

      // Setup mock budget transaction service
      when(
        () => mockBudgetTransactionService.updateBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      when(
        () => mockBudgetTransactionService.revertBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async {});

      when(
        () => mockBudgetTransactionService.updateBudgetsForTransactionUpdate(
          any(),
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      transactionRepository = TransactionRepositoryImpl(
        firestoreService,
        mockBudgetTransactionService,
      );
    });

    tearDown(() async {
      await testSetup.dispose();
    });

    group('Basic CRUD Operations', () {
      test(
        'create transaction should store transaction in Firestore',
        () async {
          final transaction = Transaction(
            id: 'test-transaction-1',
            userId: 'test-user-1',
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 5000, // $50.00

            fromAccountId: 'account-1',
            categoryId: 'category-1',
            description: 'Test expense',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          final result = await transactionRepository.create(transaction);

          expect(result, equals('test-transaction-1'));

          // Verify the transaction was stored in Firestore
          final doc = await testSetup.firestore
              .collection('users/test-user-1/transactions')
              .doc('test-transaction-1')
              .get();

          expect(doc.exists, isTrue);
          expect(doc.data()!['amountCents'], equals(5000));
          expect(doc.data()!['type'], equals('expense'));
        },
      );

      test(
        'getTransactionById should retrieve transaction from Firestore',
        () async {
          // First, add a transaction to Firestore
          final transactionData = {
            'id': 'test-transaction-2',
            'userId': 'test-user-1',
            'type': 'income',
            'status': 'completed',
            'amountCents': 10000,
            'currencyCode': 'USD',
            'toAccountId': 'account-1',
            'categoryId': 'category-1',
            'description': 'Test income',
            'notes': null,
            'tags': <String>[],
            'transactionDate': DateTime.now().toIso8601String(),
            'createdAt': DateTime.now().toIso8601String(),
            'updatedAt': DateTime.now().toIso8601String(),
            'metadata': <String, dynamic>{},
          };

          await testSetup.firestore
              .collection('users/test-user-1/transactions')
              .doc('test-transaction-2')
              .set(transactionData);

          final result = await transactionRepository.getTransactionById(
            'test-user-1',
            'test-transaction-2',
          );

          expect(result, isNotNull);
          expect(result!.id, equals('test-transaction-2'));
          expect(result.amountCents, equals(10000));
          expect(result.type, equals(TransactionType.income));
        },
      );

      test(
        'getTransactionById should return null for non-existent transaction',
        () async {
          final result = await transactionRepository.getTransactionById(
            'test-user-1',
            'non-existent-transaction',
          );

          expect(result, isNull);
        },
      );

      test('update should modify existing transaction', () async {
        // First, create a transaction
        final originalTransaction = Transaction(
          id: 'test-transaction-3',
          userId: 'test-user-1',
          type: TransactionType.expense,
          status: TransactionStatus.pending,
          amountCents: 3000,

          fromAccountId: 'account-1',
          description: 'Original description',
          transactionDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await transactionRepository.create(originalTransaction);

        // Update the transaction
        final updatedTransaction = originalTransaction.copyWith(
          status: TransactionStatus.completed,
          description: 'Updated description',
          amountCents: 3500,
        );

        await transactionRepository.update(
          'test-transaction-3',
          updatedTransaction,
        );

        // Verify the update
        final result = await transactionRepository.getTransactionById(
          'test-user-1',
          'test-transaction-3',
        );

        expect(result, isNotNull);
        expect(result!.status, equals(TransactionStatus.completed));
        expect(result.description, equals('Updated description'));
        expect(result.amountCents, equals(3500));
      });
    });

    group('Query Operations', () {
      setUp(() async {
        // Add test transactions
        final transactions = [
          Transaction(
            id: 'income-1',
            userId: 'test-user-1',
            type: TransactionType.income,
            status: TransactionStatus.completed,
            amountCents: 50000,

            toAccountId: 'account-1',
            categoryId: 'salary',
            description: 'Salary payment',
            transactionDate: DateTime(2024, 1, 15),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Transaction(
            id: 'expense-1',
            userId: 'test-user-1',
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 2500,

            fromAccountId: 'account-1',
            categoryId: 'food',
            description: 'Grocery shopping',
            transactionDate: DateTime(2024, 1, 16),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Transaction(
            id: 'transfer-1',
            userId: 'test-user-1',
            type: TransactionType.transfer,
            status: TransactionStatus.completed,
            amountCents: 10000,

            fromAccountId: 'account-1',
            toAccountId: 'account-2',
            description: 'Transfer to savings',
            transactionDate: DateTime(2024, 1, 17),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        for (final transaction in transactions) {
          await transactionRepository.create(transaction);
        }
      });

      test(
        'getTransactionsByUserId should return all user transactions',
        () async {
          final result = await transactionRepository.getTransactionsByUserId(
            'test-user-1',
          );

          expect(result.length, equals(3));
          expect(
            result.map((t) => t.id),
            containsAll(['income-1', 'expense-1', 'transfer-1']),
          );
        },
      );

      test('getTransactionsByType should filter by transaction type', () async {
        final incomeTransactions = await transactionRepository
            .getTransactionsByType('test-user-1', TransactionType.income);

        expect(incomeTransactions.length, equals(1));
        expect(incomeTransactions.first.id, equals('income-1'));
        expect(incomeTransactions.first.type, equals(TransactionType.income));

        final expenseTransactions = await transactionRepository
            .getTransactionsByType('test-user-1', TransactionType.expense);

        expect(expenseTransactions.length, equals(1));
        expect(expenseTransactions.first.id, equals('expense-1'));
      });

      test('getTransactionsByStatus should filter by status', () async {
        final completedTransactions = await transactionRepository
            .getTransactionsByStatus(
              'test-user-1',
              TransactionStatus.completed,
            );

        expect(completedTransactions.length, equals(3));
        expect(
          completedTransactions.every(
            (t) => t.status == TransactionStatus.completed,
          ),
          isTrue,
        );
      });

      test('getTransactionsByCategory should filter by category', () async {
        final foodTransactions = await transactionRepository
            .getTransactionsByCategory('test-user-1', 'food');

        expect(foodTransactions.length, equals(1));
        expect(foodTransactions.first.categoryId, equals('food'));
      });

      test(
        'getTransactionsByAccountIdForUser should filter by account',
        () async {
          final account1Transactions = await transactionRepository
              .getTransactionsByAccountIdForUser('test-user-1', 'account-1');

          // Should include expense-1 (fromAccountId), transfer-1 (fromAccountId), and income-1 (toAccountId)
          expect(account1Transactions.length, equals(3));

          final account2Transactions = await transactionRepository
              .getTransactionsByAccountIdForUser('test-user-1', 'account-2');

          // Should include transfer-1 (toAccountId)
          expect(account2Transactions.length, equals(1));
          expect(account2Transactions.first.id, equals('transfer-1'));
        },
      );
    });

    group('Statistics and Analysis', () {
      setUp(() async {
        // Add test transactions for statistics
        final transactions = [
          Transaction(
            id: 'stat-income-1',
            userId: 'test-user-1',
            type: TransactionType.income,
            status: TransactionStatus.completed,
            amountCents: 100000, // $1000

            toAccountId: 'account-1',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Transaction(
            id: 'stat-expense-1',
            userId: 'test-user-1',
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 25000, // $250

            fromAccountId: 'account-1',
            categoryId: 'food',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Transaction(
            id: 'stat-expense-2',
            userId: 'test-user-1',
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 15000, // $150

            fromAccountId: 'account-1',
            categoryId: 'transport',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        for (final transaction in transactions) {
          await transactionRepository.create(transaction);
        }
      });

      test('getTransactionStats should return correct statistics', () async {
        final stats = await transactionRepository.getTransactionStats(
          'test-user-1',
        );

        expect(stats['totalTransactions'], equals(3));
        expect(stats['totalIncome'], equals(100000));
        expect(stats['totalExpenses'], equals(40000)); // 25000 + 15000
        expect(stats['totalTransfers'], equals(0));
        expect(stats['largestTransaction'], equals(100000));
        expect(stats['smallestTransaction'], equals(15000));
      });

      test('getSpendingByCategory should return spending breakdown', () async {
        final startDate = DateTime.now().subtract(const Duration(days: 1));
        final endDate = DateTime.now().add(const Duration(days: 1));

        final spending = await transactionRepository.getSpendingByCategory(
          'test-user-1',
          startDate,
          endDate,
        );

        expect(spending['food'], equals(25000));
        expect(spending['transport'], equals(15000));
        expect(spending.length, equals(2));
      });

      test(
        'getIncomeExpenseSummary should return income vs expense summary',
        () async {
          final startDate = DateTime.now().subtract(const Duration(days: 1));
          final endDate = DateTime.now().add(const Duration(days: 1));

          final summary = await transactionRepository.getIncomeExpenseSummary(
            'test-user-1',
            startDate,
            endDate,
          );

          expect(summary['income'], equals(100000));
          expect(summary['expenses'], equals(40000));
          expect(summary['netIncome'], equals(60000));
        },
      );
    });

    group('Repository Provider Tests', () {
      test(
        'transactionRepositoryProvider provides TransactionRepositoryImpl instance',
        () async {
          final testSetup =
              await FirebaseTestSetup.createWithAuthenticatedUser();
          final firestoreService = FirestoreService(testSetup.firestore);
          final mockBudgetTransactionService = MockBudgetTransactionService();

          // Setup mock budget transaction service
          when(
            () => mockBudgetTransactionService.updateBudgetsForTransaction(
              any(),
              any(),
            ),
          ).thenAnswer((_) async => <BudgetUpdate>[]);

          final container = ProviderContainer(
            overrides: [
              firestoreServiceProvider.overrideWithValue(firestoreService),
              budgetTransactionServiceProvider.overrideWithValue(
                mockBudgetTransactionService,
              ),
            ],
          );

          final transactionRepo = container.read(transactionRepositoryProvider);
          expect(transactionRepo, isA<TransactionRepositoryImpl>());

          container.dispose();
          await testSetup.dispose();
        },
      );

      test(
        'transactionRepositoryProvider uses injected FirestoreService instance',
        () async {
          final testSetup =
              await FirebaseTestSetup.createWithAuthenticatedUser();
          final firestoreService = FirestoreService(testSetup.firestore);
          final mockBudgetTransactionService = MockBudgetTransactionService();

          // Setup mock budget transaction service
          when(
            () => mockBudgetTransactionService.updateBudgetsForTransaction(
              any(),
              any(),
            ),
          ).thenAnswer((_) async => <BudgetUpdate>[]);

          final container = ProviderContainer(
            overrides: [
              firestoreServiceProvider.overrideWithValue(firestoreService),
              budgetTransactionServiceProvider.overrideWithValue(
                mockBudgetTransactionService,
              ),
            ],
          );

          final transactionRepo = container.read(transactionRepositoryProvider);
          expect(transactionRepo, isA<ITransactionRepository>());

          container.dispose();
          await testSetup.dispose();
        },
      );
    });

    group('UnimplementedError Methods Tests', () {
      test('getById should throw UnimplementedError', () async {
        expect(
          () => transactionRepository.getById('test-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('delete should throw UnimplementedError', () async {
        expect(
          () => transactionRepository.delete('test-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('exists should throw UnimplementedError', () async {
        expect(
          () => transactionRepository.exists('test-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('getAll should throw UnimplementedError', () async {
        expect(
          () => transactionRepository.getAll(),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('getPaginated should throw UnimplementedError', () async {
        expect(
          () => transactionRepository.getPaginated(),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('watchById should throw UnimplementedError', () async {
        expect(
          () => transactionRepository.watchById('test-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('watchAll should throw UnimplementedError', () async {
        expect(
          () => transactionRepository.watchAll(),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('batchDelete should throw UnimplementedError', () async {
        expect(
          () => transactionRepository.batchDelete(['test-id']),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('query should throw UnimplementedError', () async {
        expect(
          () => transactionRepository.query(),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('count should throw UnimplementedError', () async {
        expect(
          () => transactionRepository.count(),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test(
        'getTransactionsByAccountId should throw UnimplementedError',
        () async {
          expect(
            () =>
                transactionRepository.getTransactionsByAccountId('account-id'),
            throwsA(isA<UnimplementedError>()),
          );
        },
      );

      test('updateTransactionStatus should throw UnimplementedError', () async {
        expect(
          () => transactionRepository.updateTransactionStatus(
            'transaction-id',
            TransactionStatus.completed,
          ),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('calculateAccountBalance should throw UnimplementedError', () async {
        expect(
          () => transactionRepository.calculateAccountBalance('account-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test(
        'watchAccountTransactions should throw UnimplementedError',
        () async {
          expect(
            () => transactionRepository.watchAccountTransactions('account-id'),
            throwsA(isA<UnimplementedError>()),
          );
        },
      );

      test('watchTransaction should throw UnimplementedError', () async {
        expect(
          () => transactionRepository.watchTransaction('transaction-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test(
        'getTransactionsReferencingAccount should throw UnimplementedError',
        () async {
          expect(
            () => transactionRepository.getTransactionsReferencingAccount(
              'account-id',
            ),
            throwsA(isA<UnimplementedError>()),
          );
        },
      );

      test(
        'getTransactionsReferencingCategory should throw UnimplementedError',
        () async {
          expect(
            () => transactionRepository.getTransactionsReferencingCategory(
              'category-id',
            ),
            throwsA(isA<UnimplementedError>()),
          );
        },
      );

      test(
        'reassignTransactionsToAccount should throw UnimplementedError',
        () async {
          expect(
            () => transactionRepository.reassignTransactionsToAccount(
              'old-id',
              'new-id',
            ),
            throwsA(isA<UnimplementedError>()),
          );
        },
      );

      test(
        'reassignTransactionsToCategory should throw UnimplementedError',
        () async {
          expect(
            () => transactionRepository.reassignTransactionsToCategory(
              'old-id',
              'new-id',
            ),
            throwsA(isA<UnimplementedError>()),
          );
        },
      );
    });

    group('Additional Methods Tests', () {
      test(
        'transactionExists should return true for existing transaction',
        () async {
          // Create a transaction first
          final transaction = Transaction(
            id: 'test-transaction-exists',
            userId: 'test-user-1',
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 5000,
            fromAccountId: 'account-1',
            categoryId: 'category-1',
            description: 'Test expense',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await transactionRepository.create(transaction);

          final exists = await transactionRepository.transactionExists(
            'test-user-1',
            'test-transaction-exists',
          );

          expect(exists, isTrue);
        },
      );

      test(
        'transactionExists should return false for non-existing transaction',
        () async {
          final exists = await transactionRepository.transactionExists(
            'test-user-1',
            'non-existing-transaction',
          );

          expect(exists, isFalse);
        },
      );

      test('getUserTransactionCount should return correct count', () async {
        final count = await transactionRepository.getUserTransactionCount(
          'test-user-1',
        );
        expect(count, greaterThanOrEqualTo(0));
      });

      test(
        'getUserTransactionsPaginated should return paginated results',
        () async {
          final transactions = await transactionRepository
              .getUserTransactionsPaginated('test-user-1', limit: 5);

          expect(transactions, isA<List<Transaction>>());
          expect(transactions.length, lessThanOrEqualTo(5));
        },
      );

      test('batchCreate should create multiple transactions', () async {
        final transactions = [
          Transaction(
            id: 'batch-1',
            userId: 'test-user-1',
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 1000,
            fromAccountId: 'account-1',
            categoryId: 'category-1',
            description: 'Batch test 1',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Transaction(
            id: 'batch-2',
            userId: 'test-user-1',
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 2000,
            fromAccountId: 'account-1',
            categoryId: 'category-1',
            description: 'Batch test 2',
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        await transactionRepository.batchCreate(transactions);

        // Verify transactions were created
        final transaction1 = await transactionRepository.getTransactionById(
          'test-user-1',
          'batch-1',
        );
        final transaction2 = await transactionRepository.getTransactionById(
          'test-user-1',
          'batch-2',
        );

        expect(transaction1, isNotNull);
        expect(transaction2, isNotNull);
        expect(transaction1!.description, equals('Batch test 1'));
        expect(transaction2!.description, equals('Batch test 2'));
      });

      test('batchCreate should handle empty list', () async {
        await transactionRepository.batchCreate([]);
        // Should not throw any error
      });

      test('batchUpdate should update multiple transactions', () async {
        // Create transactions first
        final transaction1 = Transaction(
          id: 'update-1',
          userId: 'test-user-1',
          type: TransactionType.expense,
          status: TransactionStatus.completed,
          amountCents: 1000,
          fromAccountId: 'account-1',
          categoryId: 'category-1',
          description: 'Original description 1',
          transactionDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final transaction2 = Transaction(
          id: 'update-2',
          userId: 'test-user-1',
          type: TransactionType.expense,
          status: TransactionStatus.completed,
          amountCents: 2000,
          fromAccountId: 'account-1',
          categoryId: 'category-1',
          description: 'Original description 2',
          transactionDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await transactionRepository.create(transaction1);
        await transactionRepository.create(transaction2);

        // Update transactions
        final updatedTransactions = {
          'update-1': transaction1.copyWith(
            description: 'Updated description 1',
          ),
          'update-2': transaction2.copyWith(
            description: 'Updated description 2',
          ),
        };

        await transactionRepository.batchUpdate(updatedTransactions);

        // Verify updates
        final updated1 = await transactionRepository.getTransactionById(
          'test-user-1',
          'update-1',
        );
        final updated2 = await transactionRepository.getTransactionById(
          'test-user-1',
          'update-2',
        );

        expect(updated1!.description, equals('Updated description 1'));
        expect(updated2!.description, equals('Updated description 2'));
      });

      test('batchUpdate should handle empty map', () async {
        await transactionRepository.batchUpdate({});
        // Should not throw any error
      });
    });
  });
}
