import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/repositories/implementations/account_repository_impl.dart';
import 'package:budapp/data/repositories/interfaces/account_repository.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/services/cache_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:budapp/services/performance_rollout_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/firebase_test_setup.dart';

// Mock classes for testing
class MockFirestoreService extends Mock implements FirestoreService {}

class MockCacheService extends Mock implements CacheService {
  @override
  Future<void> set<T>(
    String key,
    T data, {
    Duration? ttl,
    bool persistToDisk = false,
  }) async {
    // Just return a completed future for testing
    return Future<void>.value();
  }
}

class MockPerformanceRolloutService extends Mock
    implements PerformanceRolloutService {}

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(const Duration(minutes: 5));
    registerFallbackValue(false);
    registerFallbackValue(<String, dynamic>{});
  });

  group('AccountRepositoryImpl Tests', () {
    late FirebaseTestSetup testSetup;
    late FirestoreService firestoreService;
    late MockCacheService mockCacheService;
    late MockPerformanceRolloutService mockPerformanceRolloutService;
    late AccountRepositoryImpl accountRepository;

    setUp(() async {
      // Create Firebase test setup with authenticated user
      testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
        uid: 'test-user-1',
        email: '<EMAIL>',
      );

      firestoreService = FirestoreService(testSetup.firestore);
      mockCacheService = MockCacheService();
      mockPerformanceRolloutService = MockPerformanceRolloutService();

      // Configure mock services to return appropriate values
      when(
        () => mockCacheService.get<List<Map<String, dynamic>>>(any()),
      ).thenAnswer((_) async => null);
      when(
        () => mockCacheService.get<Map<String, dynamic>>(any()),
      ).thenAnswer((_) async => null);

      // The set method is overridden in MockCacheService, no need to mock it

      // Configure performance rollout service to return a valid config
      when(
        () => mockPerformanceRolloutService.getConfigForUser(any()),
      ).thenReturn(
        const PerformanceConfig(
          intelligentCachingEnabled: true,
          queryOptimizationLevel: 2,
          cacheBalancesTtl: Duration(minutes: 5),
          cacheSummaryTtl: Duration(minutes: 10),
          cacheSearchTtl: Duration(minutes: 10),
          performanceMonitoringEnabled: true,
          isInRollout: true,
        ),
      );

      accountRepository = AccountRepositoryImpl(
        firestoreService,
        testSetup.auth,
        mockCacheService,
        mockPerformanceRolloutService,
      );
    });

    tearDown(() async {
      await testSetup.dispose();
    });

    group('Basic CRUD Operations', () {
      test('create account should store account in Firestore', () async {
        final account = Account(
          id: 'test-account-1',
          userId: 'test-user-1',
          name: 'Test Checking Account',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          description: 'Primary checking account',
          initialBalanceCents: 100000,
          currentBalanceCents: 100000, // $1000.00

          isPrimary: true,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final result = await accountRepository.create(account);

        expect(result, equals('test-account-1'));

        // Verify the account was stored in Firestore
        final doc = await testSetup.firestore
            .collection('users/test-user-1/accounts')
            .doc('test-account-1')
            .get();

        expect(doc.exists, isTrue);
        expect(doc.data()!['name'], equals('Test Checking Account'));
        expect(doc.data()!['type'], equals('checking'));
        expect(doc.data()!['initialBalanceCents'], equals(100000));
      });

      test('getAccountById should retrieve account from Firestore', () async {
        // First, add an account to Firestore
        final accountData = {
          'id': 'test-account-2',
          'userId': 'test-user-1',
          'name': 'Test Savings Account',
          'type': 'savings',
          'classification': 'asset',
          'description': 'Emergency savings',
          'iconName': null,
          'colorHex': null,
          'initialBalanceCents': 50000,
          'currencyCode': 'USD',
          'isPrimary': false,
          'isActive': true,
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
          'metadata': <String, dynamic>{},
        };

        await testSetup.firestore
            .collection('users/test-user-1/accounts')
            .doc('test-account-2')
            .set(accountData);

        final result = await accountRepository.getAccountById(
          'test-user-1',
          'test-account-2',
        );

        expect(result, isNotNull);
        expect(result!.id, equals('test-account-2'));
        expect(result.name, equals('Test Savings Account'));
        expect(result.type, equals(AccountType.savings));
        expect(result.initialBalanceCents, equals(50000));
      });

      test(
        'getAccountById should return null for non-existent account',
        () async {
          final result = await accountRepository.getAccountById(
            'test-user-1',
            'non-existent-account',
          );

          expect(result, isNull);
        },
      );

      test('update should modify existing account', () async {
        // First, create an account
        final originalAccount = Account(
          id: 'test-account-3',
          userId: 'test-user-1',
          name: 'Original Name',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 0,
          currentBalanceCents: 0,

          isPrimary: false,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await accountRepository.create(originalAccount);

        // Update the account
        final updatedAccount = originalAccount.copyWith(
          name: 'Updated Name',
          description: 'Updated description',
          isPrimary: true,
        );

        await accountRepository.update('test-account-3', updatedAccount);

        // Verify the update
        final result = await accountRepository.getAccountById(
          'test-user-1',
          'test-account-3',
        );

        expect(result, isNotNull);
        expect(result!.name, equals('Updated Name'));
        expect(result.description, equals('Updated description'));
        expect(result.isPrimary, isTrue);
      });
    });

    group('Query Operations', () {
      setUp(() async {
        // Add test accounts
        final accounts = [
          Account(
            id: 'checking-1',
            userId: 'test-user-1',
            name: 'Primary Checking',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 100000,
            currentBalanceCents: 100000,

            isPrimary: true,
            isActive: true,
            createdAt: DateTime(2024, 1, 1),
            updatedAt: DateTime.now(),
          ),
          Account(
            id: 'savings-1',
            userId: 'test-user-1',
            name: 'Emergency Savings',
            type: AccountType.savings,
            classification: AccountClassification.asset,
            initialBalanceCents: 500000,
            currentBalanceCents: 500000,

            isPrimary: false,
            isActive: true,
            createdAt: DateTime(2024, 1, 2),
            updatedAt: DateTime.now(),
          ),
          Account(
            id: 'credit-1',
            userId: 'test-user-1',
            name: 'Credit Card',
            type: AccountType.creditCard,
            classification: AccountClassification.liability,
            initialBalanceCents: -25000,
            currentBalanceCents: -25000,

            isPrimary: false,
            isActive: true,
            createdAt: DateTime(2024, 1, 3),
            updatedAt: DateTime.now(),
          ),
          Account(
            id: 'old-account',
            userId: 'test-user-1',
            name: 'Old Account',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 0,
            currentBalanceCents: 0,

            isPrimary: false,
            isActive: false,
            createdAt: DateTime(2023, 12, 1),
            updatedAt: DateTime.now(),
          ),
        ];

        for (final account in accounts) {
          await accountRepository.create(account);
        }
      });

      test('getAccountsByUserId should return all user accounts', () async {
        final result = await accountRepository.getAccountsByUserId(
          'test-user-1',
        );

        expect(result.length, equals(4));
        expect(
          result.map((a) => a.id),
          containsAll(['checking-1', 'savings-1', 'credit-1', 'old-account']),
        );
      });

      test(
        'getActiveAccountsByUserId should return only active accounts',
        () async {
          final result = await accountRepository.getActiveAccountsByUserId(
            'test-user-1',
          );

          expect(result.length, equals(3));
          expect(result.every((a) => a.isActive), isTrue);
          expect(
            result.map((a) => a.id),
            containsAll(['checking-1', 'savings-1', 'credit-1']),
          );
        },
      );

      test('getAccountsByType should filter by account type', () async {
        final checkingAccounts = await accountRepository.getAccountsByType(
          'test-user-1',
          AccountType.checking,
        );

        expect(checkingAccounts.length, equals(2));
        expect(
          checkingAccounts.every((a) => a.type == AccountType.checking),
          isTrue,
        );

        final savingsAccounts = await accountRepository.getAccountsByType(
          'test-user-1',
          AccountType.savings,
        );

        expect(savingsAccounts.length, equals(1));
        expect(savingsAccounts.first.id, equals('savings-1'));
      });

      test(
        'getAccountsByClassification should filter by classification',
        () async {
          final assetAccounts = await accountRepository
              .getAccountsByClassification(
                'test-user-1',
                AccountClassification.asset,
              );

          expect(assetAccounts.length, equals(3));
          expect(
            assetAccounts.every(
              (a) => a.classification == AccountClassification.asset,
            ),
            isTrue,
          );

          final liabilityAccounts = await accountRepository
              .getAccountsByClassification(
                'test-user-1',
                AccountClassification.liability,
              );

          expect(liabilityAccounts.length, equals(1));
          expect(liabilityAccounts.first.id, equals('credit-1'));
        },
      );

      test('getPrimaryAccount should return the primary account', () async {
        final result = await accountRepository.getPrimaryAccount('test-user-1');

        expect(result, isNotNull);
        expect(result!.id, equals('checking-1'));
        expect(result.isPrimary, isTrue);
      });

      test('searchAccountsByName should find accounts by name', () async {
        final result = await accountRepository.searchAccountsByName(
          'test-user-1',
          'checking',
        );

        expect(result.length, equals(1)); // Primary Checking only
        expect(
          result.every((a) => a.name.toLowerCase().contains('checking')),
          isTrue,
        );

        final emergencyResult = await accountRepository.searchAccountsByName(
          'test-user-1',
          'emergency',
        );

        expect(emergencyResult.length, equals(1));
        expect(emergencyResult.first.id, equals('savings-1'));
      });
    });

    group('Primary Account Management', () {
      setUp(() async {
        // Add test accounts
        final accounts = [
          Account(
            id: 'account-1',
            userId: 'test-user-1',
            name: 'Account 1',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 100000,
            currentBalanceCents: 100000,

            isPrimary: true,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Account(
            id: 'account-2',
            userId: 'test-user-1',
            name: 'Account 2',
            type: AccountType.savings,
            classification: AccountClassification.asset,
            initialBalanceCents: 200000,
            currentBalanceCents: 200000,

            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        for (final account in accounts) {
          await accountRepository.create(account);
        }
      });

      test(
        'setPrimaryAccount should update primary account correctly',
        () async {
          // Verify initial state
          final initialPrimary = await accountRepository.getPrimaryAccount(
            'test-user-1',
          );
          expect(initialPrimary!.id, equals('account-1'));

          // Set account-2 as primary
          await accountRepository.setPrimaryAccount('test-user-1', 'account-2');

          // Verify the change
          final newPrimary = await accountRepository.getPrimaryAccount(
            'test-user-1',
          );
          expect(newPrimary!.id, equals('account-2'));

          // Verify account-1 is no longer primary
          final account1 = await accountRepository.getAccountById(
            'test-user-1',
            'account-1',
          );
          expect(account1!.isPrimary, isFalse);
        },
      );
    });

    group('Account Statistics', () {
      setUp(() async {
        // Add test accounts for statistics
        final accounts = [
          Account(
            id: 'asset-1',
            userId: 'test-user-1',
            name: 'Checking',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 100000,
            currentBalanceCents: 100000,

            isPrimary: true,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Account(
            id: 'asset-2',
            userId: 'test-user-1',
            name: 'Savings',
            type: AccountType.savings,
            classification: AccountClassification.asset,
            initialBalanceCents: 500000,
            currentBalanceCents: 500000,

            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Account(
            id: 'liability-1',
            userId: 'test-user-1',
            name: 'Credit Card',
            type: AccountType.creditCard,
            classification: AccountClassification.liability,
            initialBalanceCents: -25000,
            currentBalanceCents: -25000,

            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        for (final account in accounts) {
          await accountRepository.create(account);
        }
      });

      test('getUserAccountSummary should return correct summary', () async {
        final summary = await accountRepository.getUserAccountSummary(
          'test-user-1',
        );

        expect(summary['totalAccounts'], equals(3));
        expect(summary['activeAccounts'], equals(3));
        expect(summary['inactiveAccounts'], equals(0));
        expect(summary['assetAccounts'], equals(2));
        expect(summary['liabilityAccounts'], equals(1));
        expect(summary['totalAssets'], equals(600000)); // 100000 + 500000
        expect(summary['totalLiabilities'], equals(-25000));
        expect(summary['netWorth'], equals(625000)); // 600000 - (-25000)

        final accountsByType = summary['accountsByType'] as Map<String, int>;
        expect(accountsByType['checking'], equals(1));
        expect(accountsByType['savings'], equals(1));
        expect(accountsByType['creditCard'], equals(1));

        final primaryAccount = summary['primaryAccount'] as Account?;
        expect(primaryAccount, isNotNull);
        expect(primaryAccount!.id, equals('asset-1'));
      });
    });

    group('Validation', () {
      test('validateAccount should validate account correctly', () async {
        final validAccount = Account(
          id: 'valid-account',
          userId: 'test-user-1',
          name: 'Valid Account',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 0,
          currentBalanceCents: 0,

          isPrimary: false,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final isValid = await accountRepository.validateAccount(validAccount);
        expect(isValid, isTrue);

        final invalidAccount = validAccount.copyWith(name: '');
        final isInvalid = await accountRepository.validateAccount(
          invalidAccount,
        );
        expect(isInvalid, isFalse);
      });

      test('isAccountNameUnique should check name uniqueness', () async {
        // Create an account first
        final account = Account(
          id: 'existing-account',
          userId: 'test-user-1',
          name: 'Existing Account',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 0,
          currentBalanceCents: 0,

          isPrimary: false,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await accountRepository.create(account);

        // Test uniqueness
        final isUnique = await accountRepository.isAccountNameUnique(
          'test-user-1',
          'New Account Name',
        );
        expect(isUnique, isTrue);

        final isNotUnique = await accountRepository.isAccountNameUnique(
          'test-user-1',
          'Existing Account',
        );
        expect(isNotUnique, isFalse);

        // Test exclusion
        final isUniqueWithExclusion = await accountRepository
            .isAccountNameUnique(
              'test-user-1',
              'Existing Account',
              excludeAccountId: 'existing-account',
            );
        expect(isUniqueWithExclusion, isTrue);
      });
    });

    group('Batch Operations', () {
      test('batchCreate should create multiple accounts', () async {
        final accounts = [
          Account(
            id: 'batch-1',
            userId: 'test-user-1',
            name: 'Batch Account 1',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 10000,
            currentBalanceCents: 10000,
            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Account(
            id: 'batch-2',
            userId: 'test-user-1',
            name: 'Batch Account 2',
            type: AccountType.savings,
            classification: AccountClassification.asset,
            initialBalanceCents: 20000,
            currentBalanceCents: 20000,
            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        await accountRepository.batchCreate(accounts);

        // Verify accounts were created
        final account1 = await accountRepository.getAccountById(
          'test-user-1',
          'batch-1',
        );
        final account2 = await accountRepository.getAccountById(
          'test-user-1',
          'batch-2',
        );

        expect(account1, isNotNull);
        expect(account1!.name, equals('Batch Account 1'));
        expect(account2, isNotNull);
        expect(account2!.name, equals('Batch Account 2'));
      });

      test('batchCreate should handle empty list', () async {
        await accountRepository.batchCreate([]);
        // Should not throw any errors
      });

      test('batchCreate should throw error for mixed user IDs', () async {
        final accounts = [
          Account(
            id: 'batch-mixed-1',
            userId: 'test-user-1',
            name: 'User 1 Account',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 10000,
            currentBalanceCents: 10000,
            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Account(
            id: 'batch-mixed-2',
            userId: 'test-user-2',
            name: 'User 2 Account',
            type: AccountType.savings,
            classification: AccountClassification.asset,
            initialBalanceCents: 20000,
            currentBalanceCents: 20000,
            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        expect(
          () => accountRepository.batchCreate(accounts),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('batchUpdate should update multiple accounts', () async {
        // First create accounts
        final accounts = [
          Account(
            id: 'update-1',
            userId: 'test-user-1',
            name: 'Original Name 1',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 10000,
            currentBalanceCents: 10000,
            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Account(
            id: 'update-2',
            userId: 'test-user-1',
            name: 'Original Name 2',
            type: AccountType.savings,
            classification: AccountClassification.asset,
            initialBalanceCents: 20000,
            currentBalanceCents: 20000,
            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        await accountRepository.batchCreate(accounts);

        // Update accounts
        final updates = {
          'update-1': accounts[0].copyWith(name: 'Updated Name 1'),
          'update-2': accounts[1].copyWith(name: 'Updated Name 2'),
        };

        await accountRepository.batchUpdate(updates);

        // Verify updates
        final updated1 = await accountRepository.getAccountById(
          'test-user-1',
          'update-1',
        );
        final updated2 = await accountRepository.getAccountById(
          'test-user-1',
          'update-2',
        );

        expect(updated1!.name, equals('Updated Name 1'));
        expect(updated2!.name, equals('Updated Name 2'));
      });

      test('batchDeleteUserAccounts should delete multiple accounts', () async {
        // First create accounts
        final accounts = [
          Account(
            id: 'delete-1',
            userId: 'test-user-1',
            name: 'Delete Account 1',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 10000,
            currentBalanceCents: 10000,
            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Account(
            id: 'delete-2',
            userId: 'test-user-1',
            name: 'Delete Account 2',
            type: AccountType.savings,
            classification: AccountClassification.asset,
            initialBalanceCents: 20000,
            currentBalanceCents: 20000,
            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        await accountRepository.batchCreate(accounts);

        // Delete accounts
        await accountRepository.batchDeleteUserAccounts('test-user-1', [
          'delete-1',
          'delete-2',
        ]);

        // Verify deletion
        final deleted1 = await accountRepository.getAccountById(
          'test-user-1',
          'delete-1',
        );
        final deleted2 = await accountRepository.getAccountById(
          'test-user-1',
          'delete-2',
        );

        expect(deleted1, isNull);
        expect(deleted2, isNull);
      });
    });

    group('Balance Operations', () {
      setUp(() async {
        // Create test accounts with different balances
        final accounts = [
          Account(
            id: 'balance-1',
            userId: 'test-user-1',
            name: 'Balance Account 1',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 100000,
            currentBalanceCents: 150000,
            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Account(
            id: 'balance-2',
            userId: 'test-user-1',
            name: 'Balance Account 2',
            type: AccountType.savings,
            classification: AccountClassification.asset,
            initialBalanceCents: 200000,
            currentBalanceCents: 250000,
            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        await accountRepository.batchCreate(accounts);
      });

      test('getAccountBalanceForUser should return current balance', () async {
        final balance = await accountRepository.getAccountBalanceForUser(
          'test-user-1',
          'balance-1',
        );

        expect(balance, equals(150000));
      });

      test(
        'getAccountBalanceForUser should return 0 for non-existent account',
        () async {
          final balance = await accountRepository.getAccountBalanceForUser(
            'test-user-1',
            'non-existent',
          );

          expect(balance, equals(0));
        },
      );

      test(
        'getUserAccountBalances should return all account balances',
        () async {
          final balances = await accountRepository.getUserAccountBalances(
            'test-user-1',
          );

          expect(balances, isA<Map<String, int>>());
          expect(balances['balance-1'], equals(150000));
          expect(balances['balance-2'], equals(250000));
        },
      );

      test(
        'getAccountStatsForUser should return detailed account statistics',
        () async {
          final stats = await accountRepository.getAccountStatsForUser(
            'test-user-1',
            'balance-1',
          );

          expect(stats['accountId'], equals('balance-1'));
          expect(stats['accountName'], equals('Balance Account 1'));
          expect(stats['accountType'], equals('checking'));
          expect(stats['classification'], equals('asset'));
          expect(stats['currentBalance'], equals(150000));
          expect(stats['initialBalance'], equals(100000));
          expect(stats['balanceChange'], equals(50000));
          expect(stats['isActive'], isTrue);
          expect(stats['isPrimary'], isFalse);
          expect(stats['createdAt'], isA<String>());
          expect(stats['daysSinceCreation'], isA<int>());
        },
      );

      test(
        'getAccountStatsForUser should return empty map for non-existent account',
        () async {
          final stats = await accountRepository.getAccountStatsForUser(
            'test-user-1',
            'non-existent',
          );

          expect(stats, isEmpty);
        },
      );
    });

    group('Account Lifecycle Management', () {
      setUp(() async {
        // Create test account for lifecycle operations
        final account = Account(
          id: 'lifecycle-1',
          userId: 'test-user-1',
          name: 'Lifecycle Account',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 100000,
          currentBalanceCents: 100000,
          isPrimary: false,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await accountRepository.create(account);
      });

      test('deactivateAccountForUser should set account as inactive', () async {
        await accountRepository.deactivateAccountForUser(
          'test-user-1',
          'lifecycle-1',
        );

        final account = await accountRepository.getAccountById(
          'test-user-1',
          'lifecycle-1',
        );

        expect(account, isNotNull);
        expect(account!.isActive, isFalse);
      });

      test('reactivateAccountForUser should set account as active', () async {
        // First deactivate
        await accountRepository.deactivateAccountForUser(
          'test-user-1',
          'lifecycle-1',
        );

        // Then reactivate
        await accountRepository.reactivateAccountForUser(
          'test-user-1',
          'lifecycle-1',
        );

        final account = await accountRepository.getAccountById(
          'test-user-1',
          'lifecycle-1',
        );

        expect(account, isNotNull);
        expect(account!.isActive, isTrue);
      });

      test('deleteAccountForUser should remove account completely', () async {
        await accountRepository.deleteAccountForUser(
          'test-user-1',
          'lifecycle-1',
        );

        final account = await accountRepository.getAccountById(
          'test-user-1',
          'lifecycle-1',
        );

        expect(account, isNull);
      });

      test(
        'createAccount should create account with current user ID',
        () async {
          final account = Account(
            id: 'new-account',
            userId: 'different-user', // This should be overridden
            name: 'New Account',
            type: AccountType.savings,
            classification: AccountClassification.asset,
            initialBalanceCents: 50000,
            currentBalanceCents: 50000,
            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          final accountId = await accountRepository.createAccount(account);

          expect(accountId, equals('new-account'));

          final createdAccount = await accountRepository.getAccountById(
            'test-user-1',
            'new-account',
          );

          expect(createdAccount, isNotNull);
          expect(createdAccount!.userId, equals('test-user-1'));
          expect(createdAccount.name, equals('New Account'));
        },
      );

      test('updateAccount should update account details', () async {
        final updatedAccount = Account(
          id: 'lifecycle-1',
          userId: 'test-user-1',
          name: 'Updated Lifecycle Account',
          type: AccountType.savings,
          classification: AccountClassification.asset,
          initialBalanceCents: 100000,
          currentBalanceCents: 100000,
          isPrimary: true,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await accountRepository.updateAccount('lifecycle-1', updatedAccount);

        final account = await accountRepository.getAccountById(
          'test-user-1',
          'lifecycle-1',
        );

        expect(account, isNotNull);
        expect(account!.name, equals('Updated Lifecycle Account'));
        expect(account.type, equals(AccountType.savings));
        expect(account.isPrimary, isTrue);
      });
    });

    group('Stream Operations', () {
      test('watchUserAccounts should emit account updates', () async {
        // Create initial account
        final account = Account(
          id: 'stream-1',
          userId: 'test-user-1',
          name: 'Stream Account',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 100000,
          currentBalanceCents: 100000,
          isPrimary: false,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await accountRepository.create(account);

        final stream = accountRepository.watchUserAccounts('test-user-1');
        final accounts = await stream.first;

        expect(accounts, isNotEmpty);
        expect(accounts.any((a) => a.id == 'stream-1'), isTrue);
      });

      test(
        'watchAccountForUser should emit specific account updates',
        () async {
          // Create account
          final account = Account(
            id: 'watch-1',
            userId: 'test-user-1',
            name: 'Watch Account',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 100000,
            currentBalanceCents: 100000,
            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await accountRepository.create(account);

          final stream = accountRepository.watchAccountForUser(
            'test-user-1',
            'watch-1',
          );
          final watchedAccount = await stream.first;

          expect(watchedAccount, isNotNull);
          expect(watchedAccount!.id, equals('watch-1'));
          expect(watchedAccount.name, equals('Watch Account'));
        },
      );

      test(
        'watchAccountForUser should emit null for non-existent account',
        () async {
          final stream = accountRepository.watchAccountForUser(
            'test-user-1',
            'non-existent',
          );
          final watchedAccount = await stream.first;

          expect(watchedAccount, isNull);
        },
      );
    });

    group('User Context Methods', () {
      test('accountExists should check if account exists', () async {
        // Create account
        final account = Account(
          id: 'exists-1',
          userId: 'test-user-1',
          name: 'Exists Account',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 100000,
          currentBalanceCents: 100000,
          isPrimary: false,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await accountRepository.create(account);

        final exists = await accountRepository.accountExists(
          'test-user-1',
          'exists-1',
        );
        final notExists = await accountRepository.accountExists(
          'test-user-1',
          'non-existent',
        );

        expect(exists, isTrue);
        expect(notExists, isFalse);
      });

      test(
        'getUserAccountsPaginated should return paginated results',
        () async {
          // Create multiple accounts
          final accounts = List.generate(
            5,
            (index) => Account(
              id: 'paginated-$index',
              userId: 'test-user-1',
              name: 'Paginated Account $index',
              type: AccountType.checking,
              classification: AccountClassification.asset,
              initialBalanceCents: 10000 * (index + 1),
              currentBalanceCents: 10000 * (index + 1),
              isPrimary: false,
              isActive: true,
              createdAt: DateTime.now().subtract(Duration(days: index)),
              updatedAt: DateTime.now(),
            ),
          );

          await accountRepository.batchCreate(accounts);

          final paginatedAccounts = await accountRepository
              .getUserAccountsPaginated('test-user-1', limit: 3);

          expect(paginatedAccounts.length, equals(3));
          // Should be ordered by createdAt descending (most recent first)
          expect(paginatedAccounts[0].id, equals('paginated-0'));
        },
      );

      test('getUserAccountCount should return correct count', () async {
        // Create test accounts
        final accounts = List.generate(
          3,
          (index) => Account(
            id: 'count-$index',
            userId: 'test-user-1',
            name: 'Count Account $index',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 10000,
            currentBalanceCents: 10000,
            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        await accountRepository.batchCreate(accounts);

        final count = await accountRepository.getUserAccountCount(
          'test-user-1',
        );

        expect(count, greaterThanOrEqualTo(3));
      });

      test('getDeletableAccounts should return inactive accounts', () async {
        // Create active and inactive accounts
        final accounts = [
          Account(
            id: 'deletable-active',
            userId: 'test-user-1',
            name: 'Active Account',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 10000,
            currentBalanceCents: 10000,
            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Account(
            id: 'deletable-inactive',
            userId: 'test-user-1',
            name: 'Inactive Account',
            type: AccountType.savings,
            classification: AccountClassification.asset,
            initialBalanceCents: 20000,
            currentBalanceCents: 20000,
            isPrimary: false,
            isActive: false,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        await accountRepository.batchCreate(accounts);

        final deletableAccounts = await accountRepository.getDeletableAccounts(
          'test-user-1',
        );

        expect(
          deletableAccounts.any((a) => a.id == 'deletable-inactive'),
          isTrue,
        );
        expect(
          deletableAccounts.any((a) => a.id == 'deletable-active'),
          isFalse,
        );
      });
    });

    group('Error Handling', () {
      test('should throw exception when user not authenticated', () async {
        // Create repository with unauthenticated auth
        final unauthenticatedSetup = await FirebaseTestSetup.create();
        final unauthenticatedRepo = AccountRepositoryImpl(
          firestoreService,
          unauthenticatedSetup.auth,
          mockCacheService,
          mockPerformanceRolloutService,
        );

        final account = Account(
          id: 'test-account',
          userId: 'any-user',
          name: 'Test Account',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 100000,
          currentBalanceCents: 100000,
          isPrimary: false,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(
          () => unauthenticatedRepo.createAccount(account),
          throwsA(isA<Exception>()),
        );

        await unauthenticatedSetup.dispose();
      });

      test('batchUpdate should handle empty map', () async {
        await accountRepository.batchUpdate({});
        // Should not throw any errors
      });

      test('batchUpdate should throw error for mixed user IDs', () async {
        final updates = {
          'account-1': Account(
            id: 'account-1',
            userId: 'test-user-1',
            name: 'Account 1',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 10000,
            currentBalanceCents: 10000,
            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          'account-2': Account(
            id: 'account-2',
            userId: 'test-user-2',
            name: 'Account 2',
            type: AccountType.savings,
            classification: AccountClassification.asset,
            initialBalanceCents: 20000,
            currentBalanceCents: 20000,
            isPrimary: false,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        };

        expect(
          () => accountRepository.batchUpdate(updates),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('batchDeleteUserAccounts should handle empty list', () async {
        await accountRepository.batchDeleteUserAccounts('test-user-1', []);
        // Should not throw any errors
      });
    });

    group('UnimplementedError Methods', () {
      test('batchDelete should throw UnimplementedError', () async {
        expect(
          () => accountRepository.batchDelete(['id1', 'id2']),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('query should throw UnimplementedError', () async {
        expect(
          () => accountRepository.query(),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('count should throw UnimplementedError', () async {
        expect(
          () => accountRepository.count(),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('deactivateAccount should throw UnimplementedError', () async {
        expect(
          () => accountRepository.deactivateAccount('account-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('deleteAccount should throw UnimplementedError', () async {
        expect(
          () => accountRepository.deleteAccount('account-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('reactivateAccount should throw UnimplementedError', () async {
        expect(
          () => accountRepository.reactivateAccount('account-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('getAccountBalance should throw UnimplementedError', () async {
        expect(
          () => accountRepository.getAccountBalance('account-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('getAccountStats should throw UnimplementedError', () async {
        expect(
          () => accountRepository.getAccountStats('account-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('watchAccount should throw UnimplementedError', () async {
        expect(
          () => accountRepository.watchAccount('account-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test(
        'transferAccountOwnership should throw UnimplementedError',
        () async {
          expect(
            () => accountRepository.transferAccountOwnership(
              'account-id',
              'new-user',
            ),
            throwsA(isA<UnimplementedError>()),
          );
        },
      );
    });

    group('Repository Provider Tests', () {
      test(
        'accountRepositoryProvider provides AccountRepositoryImpl instance',
        () async {
          final testSetup =
              await FirebaseTestSetup.createWithAuthenticatedUser();
          final firestoreService = FirestoreService(testSetup.firestore);

          final container = ProviderContainer(
            overrides: [
              firestoreServiceProvider.overrideWithValue(firestoreService),
              firebaseAuthProvider.overrideWithValue(testSetup.auth),
            ],
          );

          final accountRepo = container.read(accountRepositoryProvider);
          expect(accountRepo, isA<AccountRepositoryImpl>());

          container.dispose();
          await testSetup.dispose();
        },
      );

      test(
        'accountRepositoryProvider uses injected FirestoreService instance',
        () async {
          final testSetup =
              await FirebaseTestSetup.createWithAuthenticatedUser();
          final firestoreService = FirestoreService(testSetup.firestore);

          final container = ProviderContainer(
            overrides: [
              firestoreServiceProvider.overrideWithValue(firestoreService),
              firebaseAuthProvider.overrideWithValue(testSetup.auth),
            ],
          );

          final accountRepo = container.read(accountRepositoryProvider);
          expect(accountRepo, isA<IAccountRepository>());

          container.dispose();
          await testSetup.dispose();
        },
      );
    });
  });
}
