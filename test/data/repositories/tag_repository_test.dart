import 'package:budapp/data/models/tag.dart';
import 'package:budapp/data/repositories/implementations/tag_repository_impl.dart';
import 'package:budapp/data/repositories/interfaces/tag_repository.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../helpers/firebase_test_setup.dart';

void main() {
  group('TagRepositoryImpl Tests', () {
    late FirebaseTestSetup testSetup;
    late FirestoreService firestoreService;
    late TagRepositoryImpl tagRepository;
    late String testUserId;

    setUp(() async {
      // Create Firebase test setup with authenticated user
      testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
        uid: 'test-user-1',
        email: '<EMAIL>',
      );

      testUserId = 'test-user-1';
      firestoreService = FirestoreService(testSetup.firestore);
      tagRepository = TagRepositoryImpl(
        firestoreService: firestoreService,
        firebaseAuth: testSetup.auth,
      );
    });

    tearDown(() async {
      await testSetup.dispose();
    });

    group('Basic CRUD Operations', () {
      test('createTag should store tag in Firestore', () async {
        final tag = Tag.create(
          userId: testUserId,
          name: 'Test Tag',
          color: '#FF5722',
        );

        final createdTag = await tagRepository.createTag(tag);

        expect(createdTag.id, isNotEmpty);
        expect(createdTag.name, equals('Test Tag'));
        expect(createdTag.color, equals('#FF5722'));
        expect(createdTag.userId, equals(testUserId));
        expect(createdTag.usageCount, equals(0));
        expect(createdTag.schemaVersion, equals(1));
        expect(createdTag.createdAt, isNotNull);
        expect(createdTag.updatedAt, isNotNull);
      });

      test(
        'createTag should throw TagAlreadyExistsException for duplicate names',
        () async {
          final tag1 = Tag.create(
            userId: testUserId,
            name: 'Duplicate Tag',
            color: '#FF5722',
          );
          final tag2 = Tag.create(
            userId: testUserId,
            name: 'Duplicate Tag',
            color: '#2196F3',
          );

          await tagRepository.createTag(tag1);

          expect(
            () => tagRepository.createTag(tag2),
            throwsA(isA<TagAlreadyExistsException>()),
          );
        },
      );

      test('getTagById should return tag when it exists', () async {
        final tag = Tag.create(
          userId: testUserId,
          name: 'Test Tag',
          color: '#FF5722',
        );

        final createdTag = await tagRepository.createTag(tag);
        final retrievedTag = await tagRepository.getTagById(createdTag.id);

        expect(retrievedTag, isNotNull);
        expect(retrievedTag!.id, equals(createdTag.id));
        expect(retrievedTag.name, equals('Test Tag'));
        expect(retrievedTag.color, equals('#FF5722'));
      });

      test('getTagById should return null when tag does not exist', () async {
        final retrievedTag = await tagRepository.getTagById('non-existent-id');
        expect(retrievedTag, isNull);
      });

      test('updateTag should modify existing tag', () async {
        final tag = Tag.create(
          userId: testUserId,
          name: 'Original Tag',
          color: '#FF5722',
        );

        final createdTag = await tagRepository.createTag(tag);

        // Add small delay to ensure timestamps are different
        await Future<void>.delayed(const Duration(milliseconds: 1));

        final updatedTag = createdTag.copyWith(
          name: 'Updated Tag',
          color: '#2196F3',
        );

        final result = await tagRepository.updateTag(updatedTag);

        expect(result.name, equals('Updated Tag'));
        expect(result.color, equals('#2196F3'));
        expect(
          result.updatedAt.millisecondsSinceEpoch,
          greaterThan(createdTag.updatedAt.millisecondsSinceEpoch),
        );
      });

      test(
        'updateTag should throw TagNotFoundException for non-existent tag',
        () async {
          final nonExistentTag = Tag(
            id: 'non-existent-id',
            userId: testUserId,
            name: 'Test Tag',
            color: '#FF5722',
            createdAt: Timestamp.now(),
            updatedAt: Timestamp.now(),
          );

          expect(
            () => tagRepository.updateTag(nonExistentTag),
            throwsA(isA<TagNotFoundException>()),
          );
        },
      );

      test(
        'updateTag should throw TagAlreadyExistsException for duplicate names',
        () async {
          final tag1 = Tag.create(
            userId: testUserId,
            name: 'Tag One',
            color: '#FF5722',
          );
          final tag2 = Tag.create(
            userId: testUserId,
            name: 'Tag Two',
            color: '#2196F3',
          );

          await tagRepository.createTag(tag1);
          final createdTag2 = await tagRepository.createTag(tag2);

          final updatedTag2 = createdTag2.copyWith(name: 'Tag One');

          expect(
            () => tagRepository.updateTag(updatedTag2),
            throwsA(isA<TagAlreadyExistsException>()),
          );
        },
      );

      test('deleteTag should remove tag from Firestore', () async {
        final tag = Tag.create(
          userId: testUserId,
          name: 'Tag to Delete',
          color: '#FF5722',
        );

        final createdTag = await tagRepository.createTag(tag);
        await tagRepository.deleteTag(createdTag.id);

        final retrievedTag = await tagRepository.getTagById(createdTag.id);
        expect(retrievedTag, isNull);
      });

      test(
        'deleteTag should throw TagNotFoundException for non-existent tag',
        () async {
          expect(
            () => tagRepository.deleteTag('non-existent-id'),
            throwsA(isA<TagNotFoundException>()),
          );
        },
      );
    });

    group('Stream Operations', () {
      test('watchUserTags should return stream of user tags', () async {
        final tag1 = Tag.create(
          userId: testUserId,
          name: 'Alpha Tag',
          color: '#FF5722',
        );
        final tag2 = Tag.create(
          userId: testUserId,
          name: 'Beta Tag',
          color: '#2196F3',
        );

        await tagRepository.createTag(tag1);
        await tagRepository.createTag(tag2);

        final stream = tagRepository.watchUserTags();
        final tags = await stream.first;

        expect(tags, hasLength(2));
        expect(tags[0].name, equals('Alpha Tag')); // Should be ordered by name
        expect(tags[1].name, equals('Beta Tag'));
      });

      test(
        'watchUserTags should return empty list when no tags exist',
        () async {
          final stream = tagRepository.watchUserTags();
          final tags = await stream.first;

          expect(tags, isEmpty);
        },
      );
    });

    group('Search Operations', () {
      test(
        'searchTags should return all tags when search term is empty',
        () async {
          final tag1 = Tag.create(
            userId: testUserId,
            name: 'Alpha Tag',
            color: '#FF5722',
          );
          final tag2 = Tag.create(
            userId: testUserId,
            name: 'Beta Tag',
            color: '#2196F3',
          );

          await tagRepository.createTag(tag1);
          await tagRepository.createTag(tag2);

          final results = await tagRepository.searchTags('');

          expect(results, hasLength(2));
          expect(results[0].name, equals('Alpha Tag'));
          expect(results[1].name, equals('Beta Tag'));
        },
      );

      test(
        'searchTags should filter tags by search term case-insensitively',
        () async {
          final tag1 = Tag.create(
            userId: testUserId,
            name: 'Work Related',
            color: '#FF5722',
          );
          final tag2 = Tag.create(
            userId: testUserId,
            name: 'Personal',
            color: '#2196F3',
          );
          final tag3 = Tag.create(
            userId: testUserId,
            name: 'work project',
            color: '#4CAF50',
          );

          await tagRepository.createTag(tag1);
          await tagRepository.createTag(tag2);
          await tagRepository.createTag(tag3);

          final results = await tagRepository.searchTags('work');

          expect(results, hasLength(2));
          expect(results.any((tag) => tag.name == 'Work Related'), isTrue);
          expect(results.any((tag) => tag.name == 'work project'), isTrue);
        },
      );

      test(
        'searchTags should return empty list when no matches found',
        () async {
          final tag = Tag.create(
            userId: testUserId,
            name: 'Test Tag',
            color: '#FF5722',
          );

          await tagRepository.createTag(tag);

          final results = await tagRepository.searchTags('nonexistent');

          expect(results, isEmpty);
        },
      );
    });

    group('Transaction Integration', () {
      test('getTagUsageCount should return correct count', () async {
        final tag = Tag.create(
          userId: testUserId,
          name: 'Usage Test Tag',
          color: '#FF5722',
        );

        final createdTag = await tagRepository.createTag(tag);

        // Create test transactions with the tag
        final transactionsCollection = firestoreService
            .collection('users')
            .doc(testUserId)
            .collection('transactions');

        await transactionsCollection.doc('transaction1').set({
          'tagIds': [createdTag.id],
          'amount': 1000,
          'description': 'Test transaction 1',
          'createdAt': Timestamp.now(),
        });

        await transactionsCollection.doc('transaction2').set({
          'tagIds': [createdTag.id, 'other-tag'],
          'amount': 2000,
          'description': 'Test transaction 2',
          'createdAt': Timestamp.now(),
        });

        await transactionsCollection.doc('transaction3').set({
          'tagIds': ['other-tag'],
          'amount': 3000,
          'description': 'Test transaction 3',
          'createdAt': Timestamp.now(),
        });

        final usageCount = await tagRepository.getTagUsageCount(createdTag.id);

        expect(usageCount, equals(2));
      });

      test('getTagUsageCount should return 0 for unused tag', () async {
        final tag = Tag.create(
          userId: testUserId,
          name: 'Unused Tag',
          color: '#FF5722',
        );

        final createdTag = await tagRepository.createTag(tag);
        final usageCount = await tagRepository.getTagUsageCount(createdTag.id);

        expect(usageCount, equals(0));
      });

      test(
        'getTagsForTransaction should return tags for transaction',
        () async {
          final tag1 = Tag.create(
            userId: testUserId,
            name: 'Tag One',
            color: '#FF5722',
          );
          final tag2 = Tag.create(
            userId: testUserId,
            name: 'Tag Two',
            color: '#2196F3',
          );

          final createdTag1 = await tagRepository.createTag(tag1);
          final createdTag2 = await tagRepository.createTag(tag2);

          // Create test transaction with tags
          final transactionsCollection = firestoreService
              .collection('users')
              .doc(testUserId)
              .collection('transactions');

          const transactionId = 'test-transaction';
          await transactionsCollection.doc(transactionId).set({
            'tagIds': [createdTag1.id, createdTag2.id],
            'amount': 1000,
            'description': 'Test transaction',
            'createdAt': Timestamp.now(),
          });

          final tags = await tagRepository.getTagsForTransaction(transactionId);

          expect(tags, hasLength(2));
          expect(tags.any((tag) => tag.id == createdTag1.id), isTrue);
          expect(tags.any((tag) => tag.id == createdTag2.id), isTrue);
        },
      );

      test(
        'getTagsForTransaction should return empty list for non-existent transaction',
        () async {
          final tags = await tagRepository.getTagsForTransaction(
            'non-existent-transaction',
          );
          expect(tags, isEmpty);
        },
      );

      test(
        'getTagsForTransaction should return empty list for transaction with no tags',
        () async {
          // Create test transaction without tags
          final transactionsCollection = firestoreService
              .collection('users')
              .doc(testUserId)
              .collection('transactions');

          const transactionId = 'no-tags-transaction';
          await transactionsCollection.doc(transactionId).set({
            'amount': 1000,
            'description': 'Test transaction without tags',
            'createdAt': Timestamp.now(),
          });

          final tags = await tagRepository.getTagsForTransaction(transactionId);
          expect(tags, isEmpty);
        },
      );

      test(
        'batchRemoveTagFromTransactions should remove tag from all transactions',
        () async {
          final tag = Tag.create(
            userId: testUserId,
            name: 'Tag to Remove',
            color: '#FF5722',
          );

          final createdTag = await tagRepository.createTag(tag);

          // Create test transactions with the tag
          final transactionsCollection = firestoreService
              .collection('users')
              .doc(testUserId)
              .collection('transactions');

          await transactionsCollection.doc('transaction1').set({
            'tagIds': [createdTag.id, 'other-tag'],
            'amount': 1000,
            'description': 'Test transaction 1',
            'createdAt': Timestamp.now(),
          });

          await transactionsCollection.doc('transaction2').set({
            'tagIds': [createdTag.id],
            'amount': 2000,
            'description': 'Test transaction 2',
            'createdAt': Timestamp.now(),
          });

          await tagRepository.batchRemoveTagFromTransactions(createdTag.id);

          // Verify tag was removed from transactions
          final transaction1 = await transactionsCollection
              .doc('transaction1')
              .get();
          final transaction2 = await transactionsCollection
              .doc('transaction2')
              .get();

          final transaction1TagIds = List<String>.from(
            (transaction1.data()!['tagIds'] as List<dynamic>?) ?? [],
          );
          final transaction2TagIds = List<String>.from(
            (transaction2.data()!['tagIds'] as List<dynamic>?) ?? [],
          );

          expect(transaction1TagIds, equals(['other-tag']));
          expect(transaction2TagIds, isEmpty);
        },
      );

      test(
        'deleteTag should remove tag from transactions atomically',
        () async {
          final tag = Tag.create(
            userId: testUserId,
            name: 'Tag to Delete with Transactions',
            color: '#FF5722',
          );

          final createdTag = await tagRepository.createTag(tag);

          // Create test transaction with the tag
          final transactionsCollection = firestoreService
              .collection('users')
              .doc(testUserId)
              .collection('transactions');

          await transactionsCollection.doc('transaction1').set({
            'tagIds': [createdTag.id, 'other-tag'],
            'amount': 1000,
            'description': 'Test transaction',
            'createdAt': Timestamp.now(),
          });

          await tagRepository.deleteTag(createdTag.id);

          // Verify tag was deleted
          final retrievedTag = await tagRepository.getTagById(createdTag.id);
          expect(retrievedTag, isNull);

          // Verify tag was removed from transaction
          final transaction = await transactionsCollection
              .doc('transaction1')
              .get();
          final tagIds = List<String>.from(
            (transaction.data()!['tagIds'] as List<dynamic>?) ?? [],
          );
          expect(tagIds, equals(['other-tag']));
        },
      );
    });

    group('Error Handling', () {
      test('should handle Firestore exceptions gracefully', () async {
        // Test with invalid user authentication
        await testSetup.auth.signOut();

        expect(
          () => tagRepository.createTag(
            Tag.create(userId: testUserId, name: 'Test Tag', color: '#FF5722'),
          ),
          throwsA(isA<Exception>()),
        );
      });

      test('getTagUsageCount should handle exceptions', () async {
        await testSetup.auth.signOut();

        expect(
          () => tagRepository.getTagUsageCount('any-tag-id'),
          throwsA(isA<Exception>()),
        );
      });

      test('searchTags should handle exceptions', () async {
        await testSetup.auth.signOut();

        expect(
          () => tagRepository.searchTags('search term'),
          throwsA(isA<Exception>()),
        );
      });

      test('getTagsForTransaction should handle exceptions', () async {
        await testSetup.auth.signOut();

        expect(
          () => tagRepository.getTagsForTransaction('transaction-id'),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('Edge Cases', () {
      test('updateTag should allow updating tag with same name', () async {
        final tag = Tag.create(
          userId: testUserId,
          name: 'Same Name Tag',
          color: '#FF5722',
        );

        final createdTag = await tagRepository.createTag(tag);
        final updatedTag = createdTag.copyWith(color: '#2196F3');

        final result = await tagRepository.updateTag(updatedTag);

        expect(result.name, equals('Same Name Tag'));
        expect(result.color, equals('#2196F3'));
      });

      test(
        'createTag should successfully create tag with empty name (validation at model level)',
        () async {
          final tag = Tag.create(
            userId: testUserId,
            name: '',
            color: '#FF5722',
          );

          // Repository allows creation, validation happens at model level
          final createdTag = await tagRepository.createTag(tag);

          expect(createdTag.name, equals(''));
          expect(createdTag.color, equals('#FF5722'));
          expect(createdTag.isValid, isFalse); // Model validation should fail
        },
      );

      test(
        'searchTags should handle special characters in search term',
        () async {
          final tag = Tag.create(
            userId: testUserId,
            name: r'Special @#$ Tag',
            color: '#FF5722',
          );

          await tagRepository.createTag(tag);

          final results = await tagRepository.searchTags(r'@#$');

          expect(results, hasLength(1));
          expect(results[0].name, equals(r'Special @#$ Tag'));
        },
      );
    });
  });
}
