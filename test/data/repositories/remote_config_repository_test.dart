import 'package:budapp/data/models/remote_config_data.dart';
import 'package:budapp/data/repositories/implementations/remote_config_repository_impl.dart';
import 'package:budapp/services/remote_config_service.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockRemoteConfigService extends Mock implements RemoteConfigService {}

void main() {
  group('RemoteConfigRepositoryImpl', () {
    late MockRemoteConfigService mockService;
    late RemoteConfigRepositoryImpl repository;

    setUp(() {
      mockService = MockRemoteConfigService();
      repository = RemoteConfigRepositoryImpl(mockService);
    });

    tearDown(() {
      repository.dispose();
    });

    group('initialize', () {
      test('should call service initialize', () async {
        // Arrange
        when(() => mockService.initialize()).thenAnswer((_) async {});

        // Act
        await repository.initialize();

        // Assert
        verify(() => mockService.initialize()).called(1);
      });
    });

    group('fetchAndActivate', () {
      test('should return result from service', () async {
        // Arrange
        when(
          () => mockService.fetchAndActivate(),
        ).thenAnswer((_) async => true);

        // Act
        final result = await repository.fetchAndActivate();

        // Assert
        expect(result, isTrue);
        verify(() => mockService.fetchAndActivate()).called(1);
      });
    });

    group('getCurrentConfig', () {
      test('should return config from service', () {
        // Arrange
        final expectedConfig = RemoteConfigData.defaults();
        when(() => mockService.getCurrentConfig()).thenReturn(expectedConfig);

        // Act
        final result = repository.getCurrentConfig();

        // Assert
        expect(result, equals(expectedConfig));
        verify(() => mockService.getCurrentConfig()).called(1);
      });
    });

    group('getPredefinedCategories', () {
      test('should return categories from current config', () {
        // Arrange
        final config = RemoteConfigData.defaults();
        when(() => mockService.getCurrentConfig()).thenReturn(config);

        // Act
        final result = repository.getPredefinedCategories();

        // Assert
        expect(result, equals(config.categories));
      });
    });

    group('getPremiumLimits', () {
      test('should return limits from current config', () {
        // Arrange
        final config = RemoteConfigData.defaults();
        when(() => mockService.getCurrentConfig()).thenReturn(config);

        // Act
        final result = repository.getPremiumLimits();

        // Assert
        expect(result, equals(config.premiumLimits));
      });
    });

    group('isFeatureEnabled', () {
      test('should return true when feature is enabled', () {
        // Arrange
        when(
          () => mockService.getValue<bool>('test_feature', defaultValue: false),
        ).thenReturn(true);

        // Act
        final result = repository.isFeatureEnabled('test_feature');

        // Assert
        expect(result, isTrue);
      });

      test('should return false when service throws', () {
        // Arrange
        when(
          () => mockService.getValue<bool>('test_feature', defaultValue: false),
        ).thenThrow(Exception('Error'));

        // Act
        final result = repository.isFeatureEnabled('test_feature');

        // Assert
        expect(result, isFalse);
      });
    });

    group('getParameterValue', () {
      test('should return value from service', () {
        // Arrange
        when(
          () => mockService.getValue<String>(
            'test_param',
            defaultValue: 'default',
          ),
        ).thenReturn('test_value');

        // Act
        final result = repository.getParameterValue<String>(
          'test_param',
          'default',
        );

        // Assert
        expect(result, equals('test_value'));
      });
    });

    group('isStale', () {
      test('should return stale status from service', () {
        // Arrange
        when(() => mockService.isStale).thenReturn(true);

        // Act
        final result = repository.isStale;

        // Assert
        expect(result, isTrue);
      });
    });

    group('lastFetchTime', () {
      test('should return last fetch time from service', () {
        // Arrange
        final expectedTime = DateTime.now();
        when(() => mockService.lastFetchTime).thenReturn(expectedTime);

        // Act
        final result = repository.lastFetchTime;

        // Assert
        expect(result, equals(expectedTime));
      });
    });

    group('forceRefresh', () {
      test('should call fetch and activate on service', () async {
        // Arrange
        when(() => mockService.fetch()).thenAnswer((_) async {});
        when(() => mockService.activate()).thenAnswer((_) async => true);

        // Act
        await repository.forceRefresh();

        // Assert
        verify(() => mockService.fetch()).called(1);
        verify(() => mockService.activate()).called(1);
      });

      test('should rethrow when service fails', () async {
        // Arrange
        when(() => mockService.fetch()).thenThrow(Exception('Network error'));

        // Act & Assert
        expect(() => repository.forceRefresh(), throwsException);
      });
    });

    group('getStatus', () {
      test('should return status information', () {
        // Arrange
        final fetchTime = DateTime.now();
        when(
          () => mockService.lastFetchStatus,
        ).thenReturn(RemoteConfigFetchStatus.success);
        when(() => mockService.lastFetchTime).thenReturn(fetchTime);
        when(() => mockService.isStale).thenReturn(false);

        // Act
        final result = repository.getStatus();

        // Assert
        expect(result['lastFetchStatus'], equals('success'));
        expect(result['lastFetchTime'], equals(fetchTime.toIso8601String()));
        expect(result['isStale'], isFalse);
        expect(result['supportsRealTimeUpdates'], isNotNull);
      });
    });
  });
}
