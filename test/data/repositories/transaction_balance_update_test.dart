import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/implementations/transaction_repository_impl.dart';
import 'package:budapp/features/budgets/services/budget_transaction_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/mock_data_factory.dart';

// Mock classes for testing
class MockBudgetTransactionService extends Mock
    implements BudgetTransactionService {}

void main() {
  setUpAll(() {
    // Use MockDataFactory to create fallback values instead of Fake classes
    registerFallbackValue(MockDataFactory.createTransaction());
  });
  group('Transaction Repository Balance Update Tests', () {
    late FakeFirebaseFirestore fakeFirestore;
    late FirestoreService firestoreService;
    late MockBudgetTransactionService mockBudgetTransactionService;
    late TransactionRepositoryImpl repository;
    late String testUserId;
    late String testAccountId;
    late Account testAccount;

    setUp(() async {
      fakeFirestore = FakeFirebaseFirestore();
      firestoreService = FirestoreService(fakeFirestore);
      mockBudgetTransactionService = MockBudgetTransactionService();
      repository = TransactionRepositoryImpl(
        firestoreService,
        mockBudgetTransactionService,
      );

      // Setup mock budget transaction service
      when(
        () => mockBudgetTransactionService.updateBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      when(
        () => mockBudgetTransactionService.revertBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async {});

      when(
        () => mockBudgetTransactionService.updateBudgetsForTransactionUpdate(
          any(),
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      testUserId = 'test-user-123';
      testAccountId = 'account-1';

      // Create test account with initial balance
      testAccount = MockDataFactory.createAccount(
        id: testAccountId,
        userId: testUserId,
        name: 'Test Account',
        initialBalanceCents:
            100000, // $1000.00 (currentBalanceCents will be set to same value)
      );

      // Store account in Firestore
      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId)
          .set(testAccount.toJson());
    });

    group('Income Transaction Balance Updates', () {
      test(
        'should atomically create income transaction and update account balance',
        () async {
          const amountCents = 50000; // $500.00

          final transactionId = await repository.createIncomeTransaction(
            userId: testUserId,
            toAccountId: testAccountId,
            amountCents: amountCents,

            transactionDate: DateTime.now(),
            description: 'Test income',
          );

          // Verify transaction was created
          final transactionDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(transactionId)
              .get();

          expect(transactionDoc.exists, isTrue);
          final transaction = Transaction.fromJson(transactionDoc.data()!);
          expect(transaction.type, TransactionType.income);
          expect(transaction.amountCents, amountCents);
          expect(transaction.toAccountId, testAccountId);

          // Verify account balance was updated
          final accountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();

          expect(accountDoc.exists, isTrue);
          final updatedAccount = Account.fromJson(accountDoc.data()!);
          expect(
            updatedAccount.currentBalanceCents,
            150000,
          ); // $1000 + $500 = $1500
          expect(
            updatedAccount.initialBalanceCents,
            100000,
          ); // Should remain unchanged
        },
      );

      test('should handle multiple income transactions correctly', () async {
        // First income transaction
        await repository.createIncomeTransaction(
          userId: testUserId,
          toAccountId: testAccountId,
          amountCents: 25000, // $250.00

          transactionDate: DateTime.now(),
        );

        // Second income transaction
        await repository.createIncomeTransaction(
          userId: testUserId,
          toAccountId: testAccountId,
          amountCents: 75000, // $750.00

          transactionDate: DateTime.now(),
        );

        // Verify final balance
        final accountDoc = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testAccountId)
            .get();

        final updatedAccount = Account.fromJson(accountDoc.data()!);
        expect(
          updatedAccount.currentBalanceCents,
          200000,
        ); // $1000 + $250 + $750 = $2000
      });
    });

    group('Expense Transaction Balance Updates', () {
      test(
        'should atomically create expense transaction and update account balance',
        () async {
          const amountCents = 30000; // $300.00

          final transactionId = await repository.createExpenseTransaction(
            userId: testUserId,
            fromAccountId: testAccountId,
            amountCents: amountCents,

            transactionDate: DateTime.now(),
            description: 'Test expense',
          );

          // Verify transaction was created
          final transactionDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(transactionId)
              .get();

          expect(transactionDoc.exists, isTrue);
          final transaction = Transaction.fromJson(transactionDoc.data()!);
          expect(transaction.type, TransactionType.expense);
          expect(transaction.amountCents, amountCents);
          expect(transaction.fromAccountId, testAccountId);

          // Verify account balance was updated
          final accountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();

          expect(accountDoc.exists, isTrue);
          final updatedAccount = Account.fromJson(accountDoc.data()!);
          expect(
            updatedAccount.currentBalanceCents,
            70000,
          ); // $1000 - $300 = $700
          expect(
            updatedAccount.initialBalanceCents,
            100000,
          ); // Should remain unchanged
        },
      );

      test('should handle multiple expense transactions correctly', () async {
        // First expense transaction
        await repository.createExpenseTransaction(
          userId: testUserId,
          fromAccountId: testAccountId,
          amountCents: 20000, // $200.00

          transactionDate: DateTime.now(),
        );

        // Second expense transaction
        await repository.createExpenseTransaction(
          userId: testUserId,
          fromAccountId: testAccountId,
          amountCents: 15000, // $150.00

          transactionDate: DateTime.now(),
        );

        // Verify final balance
        final accountDoc = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testAccountId)
            .get();

        final updatedAccount = Account.fromJson(accountDoc.data()!);
        expect(
          updatedAccount.currentBalanceCents,
          65000,
        ); // $1000 - $200 - $150 = $650
      });

      test('should allow negative balances for expense transactions', () async {
        // Create expense larger than current balance
        await repository.createExpenseTransaction(
          userId: testUserId,
          fromAccountId: testAccountId,
          amountCents: 120000, // $1200.00 (more than $1000 balance)

          transactionDate: DateTime.now(),
        );

        // Verify balance went negative
        final accountDoc = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testAccountId)
            .get();

        final updatedAccount = Account.fromJson(accountDoc.data()!);
        expect(
          updatedAccount.currentBalanceCents,
          -20000,
        ); // $1000 - $1200 = -$200
      });
    });

    group('Mixed Transaction Balance Updates', () {
      test(
        'should handle mixed income and expense transactions correctly',
        () async {
          // Income: +$500
          await repository.createIncomeTransaction(
            userId: testUserId,
            toAccountId: testAccountId,
            amountCents: 50000,

            transactionDate: DateTime.now(),
          );

          // Expense: -$300
          await repository.createExpenseTransaction(
            userId: testUserId,
            fromAccountId: testAccountId,
            amountCents: 30000,

            transactionDate: DateTime.now(),
          );

          // Income: +$200
          await repository.createIncomeTransaction(
            userId: testUserId,
            toAccountId: testAccountId,
            amountCents: 20000,

            transactionDate: DateTime.now(),
          );

          // Verify final balance: $1000 + $500 - $300 + $200 = $1400
          final accountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();

          final updatedAccount = Account.fromJson(accountDoc.data()!);
          expect(updatedAccount.currentBalanceCents, 140000);
        },
      );
    });

    group('Transfer Transaction Balance Updates', () {
      late String testToAccountId;
      late Account testToAccount;

      setUp(() async {
        testToAccountId = 'account-2';

        // Create second test account for transfers
        testToAccount = MockDataFactory.createAccount(
          id: testToAccountId,
          userId: testUserId,
          name: 'Test To Account',
          initialBalanceCents:
              50000, // $500.00 (currentBalanceCents will be set to same value)
        );

        // Store second account in Firestore
        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testToAccountId)
            .set(testToAccount.toJson());
      });

      test(
        'should atomically create transfer transaction and update both account balances',
        () async {
          const amountCents = 25000; // $250.00

          final transactionId = await repository.createTransferTransaction(
            userId: testUserId,
            fromAccountId: testAccountId, // From: $1000
            toAccountId: testToAccountId, // To: $500
            amountCents: amountCents,

            transactionDate: DateTime.now(),
            description: 'Test transfer',
          );

          // Verify transaction was created
          final transactionDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(transactionId)
              .get();

          expect(transactionDoc.exists, isTrue);
          final transaction = Transaction.fromJson(transactionDoc.data()!);
          expect(transaction.type, TransactionType.transfer);
          expect(transaction.amountCents, amountCents);
          expect(transaction.fromAccountId, testAccountId);
          expect(transaction.toAccountId, testToAccountId);

          // Verify source account balance was decreased
          final fromAccountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();

          expect(fromAccountDoc.exists, isTrue);
          final updatedFromAccount = Account.fromJson(fromAccountDoc.data()!);
          expect(
            updatedFromAccount.currentBalanceCents,
            75000,
          ); // $1000 - $250 = $750

          // Verify destination account balance was increased
          final toAccountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testToAccountId)
              .get();

          expect(toAccountDoc.exists, isTrue);
          final updatedToAccount = Account.fromJson(toAccountDoc.data()!);
          expect(
            updatedToAccount.currentBalanceCents,
            75000,
          ); // $500 + $250 = $750

          // Verify initial balances remain unchanged
          expect(updatedFromAccount.initialBalanceCents, 100000);
          expect(updatedToAccount.initialBalanceCents, 50000);
        },
      );

      test('should handle multiple transfer transactions correctly', () async {
        // First transfer: $200 from account-1 to account-2
        await repository.createTransferTransaction(
          userId: testUserId,
          fromAccountId: testAccountId,
          toAccountId: testToAccountId,
          amountCents: 20000, // $200.00

          transactionDate: DateTime.now(),
        );

        // Second transfer: $100 from account-2 to account-1
        await repository.createTransferTransaction(
          userId: testUserId,
          fromAccountId: testToAccountId,
          toAccountId: testAccountId,
          amountCents: 10000, // $100.00

          transactionDate: DateTime.now(),
        );

        // Verify final balances
        final fromAccountDoc = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testAccountId)
            .get();

        final toAccountDoc = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testToAccountId)
            .get();

        final updatedFromAccount = Account.fromJson(fromAccountDoc.data()!);
        final updatedToAccount = Account.fromJson(toAccountDoc.data()!);

        // Account-1: $1000 - $200 + $100 = $900
        expect(updatedFromAccount.currentBalanceCents, 90000);
        // Account-2: $500 + $200 - $100 = $600
        expect(updatedToAccount.currentBalanceCents, 60000);
      });

      test('should allow transfers that result in negative balances', () async {
        // Transfer more than available balance
        await repository.createTransferTransaction(
          userId: testUserId,
          fromAccountId: testAccountId, // Has $1000
          toAccountId: testToAccountId,
          amountCents: 120000, // $1200.00 (more than available)

          transactionDate: DateTime.now(),
        );

        // Verify source account went negative
        final fromAccountDoc = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testAccountId)
            .get();

        final updatedFromAccount = Account.fromJson(fromAccountDoc.data()!);
        expect(
          updatedFromAccount.currentBalanceCents,
          -20000,
        ); // $1000 - $1200 = -$200

        // Verify destination account increased correctly
        final toAccountDoc = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testToAccountId)
            .get();

        final updatedToAccount = Account.fromJson(toAccountDoc.data()!);
        expect(
          updatedToAccount.currentBalanceCents,
          170000,
        ); // $500 + $1200 = $1700
      });
    });

    group('Mixed Transaction Types Balance Updates', () {
      late String testToAccountId;
      late Account testToAccount;

      setUp(() async {
        testToAccountId = 'account-3';

        // Create third test account for mixed transaction tests
        testToAccount = MockDataFactory.createAccount(
          id: testToAccountId,
          userId: testUserId,
          name: 'Test Mixed Account',
          initialBalanceCents: 75000, // $750.00
        );

        // Store third account in Firestore
        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testToAccountId)
            .set(testToAccount.toJson());
      });

      test(
        'should handle mixed income, expense, and transfer transactions correctly',
        () async {
          // Income: +$300 to account-1
          await repository.createIncomeTransaction(
            userId: testUserId,
            toAccountId: testAccountId,
            amountCents: 30000,

            transactionDate: DateTime.now(),
          );

          // Transfer: $400 from account-1 to account-3
          await repository.createTransferTransaction(
            userId: testUserId,
            fromAccountId: testAccountId,
            toAccountId: testToAccountId,
            amountCents: 40000,

            transactionDate: DateTime.now(),
          );

          // Expense: -$150 from account-3
          await repository.createExpenseTransaction(
            userId: testUserId,
            fromAccountId: testToAccountId,
            amountCents: 15000,

            transactionDate: DateTime.now(),
          );

          // Verify final balances
          final account1Doc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();

          final account3Doc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testToAccountId)
              .get();

          final updatedAccount1 = Account.fromJson(account1Doc.data()!);
          final updatedAccount3 = Account.fromJson(account3Doc.data()!);

          // Account-1: $1000 + $300 - $400 = $900
          expect(updatedAccount1.currentBalanceCents, 90000);
          // Account-3: $750 + $400 - $150 = $1000
          expect(updatedAccount3.currentBalanceCents, 100000);
        },
      );
    });

    group('Error Handling', () {
      test(
        'should throw error when account does not exist for income transaction',
        () async {
          expect(
            () => repository.createIncomeTransaction(
              userId: testUserId,
              toAccountId: 'non-existent-account',
              amountCents: 50000,

              transactionDate: DateTime.now(),
            ),
            throwsA(isA<ArgumentError>()),
          );
        },
      );

      test(
        'should throw error when account does not exist for expense transaction',
        () async {
          expect(
            () => repository.createExpenseTransaction(
              userId: testUserId,
              fromAccountId: 'non-existent-account',
              amountCents: 50000,

              transactionDate: DateTime.now(),
            ),
            throwsA(isA<ArgumentError>()),
          );
        },
      );

      test(
        'should throw error when source account does not exist for transfer transaction',
        () async {
          expect(
            () => repository.createTransferTransaction(
              userId: testUserId,
              fromAccountId: 'non-existent-account',
              toAccountId: testAccountId,
              amountCents: 50000,

              transactionDate: DateTime.now(),
            ),
            throwsA(isA<ArgumentError>()),
          );
        },
      );

      test(
        'should throw error when destination account does not exist for transfer transaction',
        () async {
          expect(
            () => repository.createTransferTransaction(
              userId: testUserId,
              fromAccountId: testAccountId,
              toAccountId: 'non-existent-account',
              amountCents: 50000,

              transactionDate: DateTime.now(),
            ),
            throwsA(isA<ArgumentError>()),
          );
        },
      );

      test(
        'should throw error when source and destination accounts are the same',
        () async {
          expect(
            () => repository.createTransferTransaction(
              userId: testUserId,
              fromAccountId: testAccountId,
              toAccountId: testAccountId, // Same as source
              amountCents: 50000,

              transactionDate: DateTime.now(),
            ),
            throwsA(isA<ArgumentError>()),
          );
        },
      );
    });
  });
}
