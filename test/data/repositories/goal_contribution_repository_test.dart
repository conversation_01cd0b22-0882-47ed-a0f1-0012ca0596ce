import 'package:budapp/data/models/goal.dart';
import 'package:budapp/data/models/goal_contribution.dart';
import 'package:budapp/data/repositories/implementations/goal_contribution_repository_impl.dart';
import 'package:budapp/data/repositories/implementations/goal_repository_impl.dart';
import 'package:budapp/data/repositories/interfaces/i_goal_contribution_repository.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../helpers/firebase_test_setup.dart';

void main() {
  group('GoalContributionRepositoryImpl Tests', () {
    late FirebaseTestSetup testSetup;
    late FirestoreService firestoreService;
    late IGoalContributionRepository contributionRepository;
    late GoalRepositoryImpl goalRepository;
    late String testGoalId;

    setUp(() async {
      // Create Firebase test setup with authenticated user
      testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
        uid: 'test-user-1',
        email: '<EMAIL>',
      );

      firestoreService = FirestoreService(testSetup.firestore);
      contributionRepository = GoalContributionRepositoryImpl(
        firestoreService,
        testSetup.auth,
      );
      goalRepository = GoalRepositoryImpl(firestoreService, testSetup.auth);

      // Create a test goal for contribution tests
      final testGoal = Goal(
        id: 'test-goal-1',
        userId: 'test-user-1',
        name: 'Test Savings Goal',
        description: 'Testing goal contributions',
        targetAmountCents: 100000, // $1000.00
        currentAmountCents: 0,
        targetDate: DateTime.now().add(const Duration(days: 365)),
        status: GoalStatus.active,
        colorHex: '#00FF00',
        iconName: 'savings',
        isActive: true,
        schemaVersion: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      testGoalId = await goalRepository.create(testGoal);
    });

    tearDown(() async {
      await testSetup.dispose();
    });

    group('Basic CRUD Operations', () {
      test(
        'createContribution should store contribution in Firestore',
        () async {
          final contribution = GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 5000, // $50.00
            contributionDate: DateTime.now(),
            description: 'First contribution',
          );

          final result = await contributionRepository.createContribution(
            testGoalId,
            contribution,
          );

          expect(result, isNotEmpty);
          expect(result, isA<String>());

          // Verify contribution was stored
          final retrievedContribution = await contributionRepository
              .getContributionById(testGoalId, result);
          expect(retrievedContribution, isNotNull);
          expect(retrievedContribution!.amountCents, equals(5000));
          expect(retrievedContribution.goalId, equals(testGoalId));
          expect(retrievedContribution.userId, equals('test-user-1'));
          expect(
            retrievedContribution.description,
            equals('First contribution'),
          );
        },
      );

      test(
        'getContributionById should return null for non-existent contribution',
        () async {
          final result = await contributionRepository.getContributionById(
            testGoalId,
            'non-existent-id',
          );
          expect(result, isNull);
        },
      );

      test('updateContribution should modify existing contribution', () async {
        // Create initial contribution
        final contribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: 5000,
          contributionDate: DateTime.now(),
          description: 'Original description',
        );

        final contributionId = await contributionRepository.createContribution(
          testGoalId,
          contribution,
        );

        // Update the contribution
        final updatedContribution = contribution.copyWith(
          id: contributionId,
          amountCents: 7500, // $75.00
          description: 'Updated description',
        );

        await contributionRepository.updateContribution(
          testGoalId,
          contributionId,
          updatedContribution,
        );

        // Verify update
        final retrievedContribution = await contributionRepository
            .getContributionById(testGoalId, contributionId);
        expect(retrievedContribution!.amountCents, equals(7500));
        expect(
          retrievedContribution.description,
          equals('Updated description'),
        );
      });

      test('deleteContribution should soft delete contribution', () async {
        // Create contribution
        final contribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: 5000,
          contributionDate: DateTime.now(),
        );

        final contributionId = await contributionRepository.createContribution(
          testGoalId,
          contribution,
        );

        // Delete contribution
        await contributionRepository.deleteContribution(
          testGoalId,
          contributionId,
        );

        // Verify soft deletion (contribution still exists but isActive = false)
        final retrievedContribution = await contributionRepository
            .getContributionById(testGoalId, contributionId);
        expect(retrievedContribution, isNotNull);
        expect(retrievedContribution!.isActive, isFalse);
      });
    });

    group('Goal-Specific Operations', () {
      test(
        'getContributionsForGoal should return all contributions for a goal',
        () async {
          // Create multiple contributions
          final contribution1 = GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 5000,
            contributionDate: DateTime.now().subtract(const Duration(days: 2)),
            description: 'First contribution',
          );

          final contribution2 = GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 3000,
            contributionDate: DateTime.now().subtract(const Duration(days: 1)),
            description: 'Second contribution',
          );

          await contributionRepository.createContribution(
            testGoalId,
            contribution1,
          );
          await contributionRepository.createContribution(
            testGoalId,
            contribution2,
          );

          final contributions = await contributionRepository
              .getContributionsForGoal(testGoalId);

          expect(contributions, hasLength(2));
          // Should be ordered by contribution date (newest first)
          expect(contributions[0].description, equals('Second contribution'));
          expect(contributions[1].description, equals('First contribution'));
        },
      );

      test(
        'getActiveContributionsForGoal should return only active contributions',
        () async {
          // Create contributions
          final contribution1 = GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 5000,
            contributionDate: DateTime.now(),
          );

          final contribution2 = GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 3000,
            contributionDate: DateTime.now(),
          );

          final id1 = await contributionRepository.createContribution(
            testGoalId,
            contribution1,
          );
          final id2 = await contributionRepository.createContribution(
            testGoalId,
            contribution2,
          );

          // Delete one contribution
          await contributionRepository.deleteContribution(testGoalId, id1);

          final activeContributions = await contributionRepository
              .getActiveContributionsForGoal(testGoalId);

          expect(activeContributions, hasLength(1));
          expect(activeContributions[0].id, equals(id2));
          expect(activeContributions[0].isActive, isTrue);
        },
      );

      test(
        'getTotalContributionAmountForGoal should calculate correct total',
        () async {
          // Create contributions
          await contributionRepository.createContribution(
            testGoalId,
            GoalContribution.create(
              userId: 'test-user-1',
              goalId: testGoalId,
              amountCents: 5000,
              contributionDate: DateTime.now(),
            ),
          );

          await contributionRepository.createContribution(
            testGoalId,
            GoalContribution.create(
              userId: 'test-user-1',
              goalId: testGoalId,
              amountCents: 3000,
              contributionDate: DateTime.now(),
            ),
          );

          final totalAmount = await contributionRepository
              .getTotalContributionAmountForGoal(testGoalId);

          expect(totalAmount, equals(8000)); // $80.00
        },
      );

      test('getContributionCountForGoal should return correct count', () async {
        // Create contributions
        await contributionRepository.createContribution(
          testGoalId,
          GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 5000,
            contributionDate: DateTime.now(),
          ),
        );

        await contributionRepository.createContribution(
          testGoalId,
          GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 3000,
            contributionDate: DateTime.now(),
          ),
        );

        final count = await contributionRepository.getContributionCountForGoal(
          testGoalId,
        );

        expect(count, equals(2));
      });
    });

    group('Date-Based Filtering', () {
      // Note: fake_cloud_firestore has limitations with date range queries
      // These tests are disabled but methods are available for production use
      test(
        'getContributionsByDateRange should exist and run without error',
        () async {
          // Test that the method exists and can be called
          expect(
            contributionRepository.getContributionsByDateRange,
            isA<Function>(),
          );

          // Test with a simple date range - even if empty results due to fake_cloud_firestore
          final now = DateTime.now();
          final startDate = now.subtract(const Duration(days: 1));
          final endDate = now.add(const Duration(days: 1));

          final result = await contributionRepository
              .getContributionsByDateRange(testGoalId, startDate, endDate);
          expect(result, isA<List<GoalContribution>>());
        },
      );

      test('getRecentContributions should limit results', () async {
        // Create multiple contributions
        for (var i = 0; i < 3; i++) {
          await contributionRepository.createContribution(
            testGoalId,
            GoalContribution.create(
              userId: 'test-user-1',
              goalId: testGoalId,
              amountCents: 1000 * (i + 1),
              contributionDate: DateTime.now(),
            ),
          );
        }

        final recentContributions = await contributionRepository
            .getRecentContributions(testGoalId, limit: 2);

        expect(recentContributions, hasLength(2));
        expect(recentContributions, isA<List<GoalContribution>>());
      });

      test('getTodaysContributions should exist', () async {
        // Test that the method exists and can be called
        expect(contributionRepository.getTodaysContributions, isA<Function>());

        // Test with simple call - even if empty results due to fake_cloud_firestore
        final result = await contributionRepository.getTodaysContributions(
          testGoalId,
        );
        expect(result, isA<List<GoalContribution>>());
      });
    });

    group('User-Level Operations', () {
      test(
        'getAllUserContributions should return contributions across all goals',
        () async {
          // Create another goal
          final goal2 = Goal(
            id: 'test-goal-2',
            userId: 'test-user-1',
            name: 'Second Goal',
            description: 'Another test goal',
            targetAmountCents: 50000,
            currentAmountCents: 0,
            targetDate: DateTime.now().add(const Duration(days: 180)),
            status: GoalStatus.active,
            colorHex: '#0000FF',
            iconName: 'target',
            isActive: true,
            schemaVersion: 1,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          final goal2Id = await goalRepository.create(goal2);

          // Create contributions for both goals
          await contributionRepository.createContribution(
            testGoalId,
            GoalContribution.create(
              userId: 'test-user-1',
              goalId: testGoalId,
              amountCents: 5000,
              contributionDate: DateTime.now(),
            ),
          );

          await contributionRepository.createContribution(
            goal2Id,
            GoalContribution.create(
              userId: 'test-user-1',
              goalId: goal2Id,
              amountCents: 3000,
              contributionDate: DateTime.now(),
            ),
          );

          final allContributions = await contributionRepository
              .getAllUserContributions();

          expect(allContributions, hasLength(2));
          expect(allContributions.any((c) => c.goalId == testGoalId), isTrue);
          expect(allContributions.any((c) => c.goalId == goal2Id), isTrue);
        },
      );

      test(
        'getActiveUserContributions should return only active contributions',
        () async {
          // Create contributions
          final contribution1 = GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 5000,
            contributionDate: DateTime.now(),
          );

          final contribution2 = GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 3000,
            contributionDate: DateTime.now(),
          );

          final id1 = await contributionRepository.createContribution(
            testGoalId,
            contribution1,
          );
          await contributionRepository.createContribution(
            testGoalId,
            contribution2,
          );

          // Delete one contribution
          await contributionRepository.deleteContribution(testGoalId, id1);

          final activeContributions = await contributionRepository
              .getActiveUserContributions();

          expect(activeContributions, hasLength(1));
          expect(activeContributions.every((c) => c.isActive), isTrue);
        },
      );
    });

    group('Real-time Streams', () {
      test('watchContributionsForGoal should emit updates', () async {
        final stream = contributionRepository.watchContributionsForGoal(
          testGoalId,
        );
        final streamTest = expectLater(
          stream.take(2),
          emitsInOrder([
            isEmpty, // Initial empty state
            hasLength(1), // After adding contribution
          ]),
        );

        // Wait a bit for stream to initialize
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Add a contribution
        await contributionRepository.createContribution(
          testGoalId,
          GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 5000,
            contributionDate: DateTime.now(),
          ),
        );

        await streamTest;
      });

      test(
        'watchActiveContributionsForGoal should only emit active contributions',
        () async {
          final stream = contributionRepository.watchActiveContributionsForGoal(
            testGoalId,
          );

          // Create contribution first
          final contributionId = await contributionRepository
              .createContribution(
                testGoalId,
                GoalContribution.create(
                  userId: 'test-user-1',
                  goalId: testGoalId,
                  amountCents: 5000,
                  contributionDate: DateTime.now(),
                ),
              );

          final streamTest = expectLater(
            stream.take(2),
            emitsInOrder([
              hasLength(1), // Initial active contribution
              isEmpty, // After deletion
            ]),
          );

          // Wait a bit for stream to initialize
          await Future<void>.delayed(const Duration(milliseconds: 100));

          // Delete the contribution
          await contributionRepository.deleteContribution(
            testGoalId,
            contributionId,
          );

          await streamTest;
        },
      );

      test(
        'watchContribution should emit specific contribution updates',
        () async {
          // Create contribution
          final contributionId = await contributionRepository
              .createContribution(
                testGoalId,
                GoalContribution.create(
                  userId: 'test-user-1',
                  goalId: testGoalId,
                  amountCents: 5000,
                  contributionDate: DateTime.now(),
                  description: 'Original',
                ),
              );

          final stream = contributionRepository.watchContribution(
            testGoalId,
            contributionId,
          );

          final streamTest = expectLater(
            stream.take(2),
            emitsInOrder([
              predicate<GoalContribution?>((c) => c?.description == 'Original'),
              predicate<GoalContribution?>((c) => c?.description == 'Updated'),
            ]),
          );

          // Wait a bit for stream to initialize
          await Future<void>.delayed(const Duration(milliseconds: 100));

          // Update the contribution
          final updatedContribution = GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 7500,
            contributionDate: DateTime.now(),
            description: 'Updated',
          );

          await contributionRepository.updateContribution(
            testGoalId,
            contributionId,
            updatedContribution,
          );

          await streamTest;
        },
      );
    });

    group('Validation and Business Logic', () {
      test('validateContribution should validate contribution data', () async {
        final validContribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: 5000,
          contributionDate: DateTime.now(),
        );

        final isValid = await contributionRepository.validateContribution(
          testGoalId,
          validContribution,
        );
        expect(isValid, isTrue);
      });

      test('validateContribution should reject invalid contribution', () async {
        final invalidContribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: -1000, // Negative amount
          contributionDate: DateTime.now(),
        );

        final isValid = await contributionRepository.validateContribution(
          testGoalId,
          invalidContribution,
        );
        expect(isValid, isFalse);
      });

      test(
        'isValidGoalForContribution should validate goal existence',
        () async {
          final isValid = await contributionRepository
              .isValidGoalForContribution(testGoalId);
          expect(isValid, isTrue);

          final isInvalid = await contributionRepository
              .isValidGoalForContribution('non-existent-goal');
          expect(isInvalid, isFalse);
        },
      );

      test(
        'getContributionStatistics should calculate correct statistics',
        () async {
          final today = DateTime.now();

          // Create contributions
          await contributionRepository.createContribution(
            testGoalId,
            GoalContribution.create(
              userId: 'test-user-1',
              goalId: testGoalId,
              amountCents: 5000,
              contributionDate: today,
            ),
          );

          await contributionRepository.createContribution(
            testGoalId,
            GoalContribution.create(
              userId: 'test-user-1',
              goalId: testGoalId,
              amountCents: 3000,
              contributionDate: today,
            ),
          );

          final stats = await contributionRepository.getContributionStatistics(
            testGoalId,
          );

          expect(stats['total_amount'], equals(8000));
          expect(stats['contribution_count'], equals(2));
          expect(stats['average_amount'], equals(4000));
          expect(stats['first_contribution_date'], isA<DateTime>());
          expect(stats['last_contribution_date'], isA<DateTime>());
        },
      );

      test(
        'getContributionStatistics should handle empty contributions',
        () async {
          final stats = await contributionRepository.getContributionStatistics(
            testGoalId,
          );

          expect(stats['total_amount'], equals(0));
          expect(stats['contribution_count'], equals(0));
          expect(stats['average_amount'], equals(0));
          expect(stats['first_contribution_date'], isNull);
          expect(stats['last_contribution_date'], isNull);
        },
      );
    });

    group('Error Handling', () {
      test('createContribution should throw for invalid goal', () async {
        final contribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: 'invalid-goal-id',
          amountCents: 5000,
          contributionDate: DateTime.now(),
        );

        expect(
          () => contributionRepository.createContribution(
            'invalid-goal-id',
            contribution,
          ),
          throwsA(isA<Exception>()),
        );
      });

      test('updateContribution should throw for invalid goal', () async {
        final contribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: 'invalid-goal-id',
          amountCents: 5000,
          contributionDate: DateTime.now(),
        );

        expect(
          () => contributionRepository.updateContribution(
            'invalid-goal-id',
            'some-id',
            contribution,
          ),
          throwsA(isA<Exception>()),
        );
      });

      test('deleteContribution should throw for invalid goal', () async {
        expect(
          () => contributionRepository.deleteContribution(
            'invalid-goal-id',
            'some-id',
          ),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('Advanced Validation and Edge Cases', () {
      test('createContribution should handle invalid amount values', () async {
        final invalidContribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: -5000, // Negative amount
          contributionDate: DateTime.now(),
        );

        expect(
          () => contributionRepository.createContribution(
            testGoalId,
            invalidContribution,
          ),
          throwsA(isA<Exception>()),
        );
      });

      test(
        'createContribution should handle future contribution dates',
        () async {
          final futureContribution = GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 5000,
            contributionDate: DateTime.now().add(const Duration(days: 1)),
          );

          expect(
            () => contributionRepository.createContribution(
              testGoalId,
              futureContribution,
            ),
            throwsA(isA<Exception>()),
          );
        },
      );

      test(
        'createContribution should handle extremely large amounts',
        () async {
          final largeContribution = GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 10000000000, // $100M - exceeds limit of $99,999,999.99
            contributionDate: DateTime.now(),
          );

          expect(
            () => contributionRepository.createContribution(
              testGoalId,
              largeContribution,
            ),
            throwsA(isA<Exception>()),
          );
        },
      );

      test(
        'createContribution should handle very old contribution dates',
        () async {
          final oldContribution = GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 5000,
            contributionDate: DateTime.now().subtract(
              const Duration(days: 4000),
            ), // >10 years ago
          );

          expect(
            () => contributionRepository.createContribution(
              testGoalId,
              oldContribution,
            ),
            throwsA(isA<Exception>()),
          );
        },
      );

      test('createContribution should handle long descriptions', () async {
        final longDescription = 'A' * 600; // Exceeds 500 char limit
        final longDescriptionContribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: 5000,
          contributionDate: DateTime.now(),
          description: longDescription,
        );

        expect(
          () => contributionRepository.createContribution(
            testGoalId,
            longDescriptionContribution,
          ),
          throwsA(isA<Exception>()),
        );
      });

      test('createContribution should handle empty description', () async {
        final emptyDescriptionContribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: 5000,
          contributionDate: DateTime.now(),
          description: '',
        );

        final result = await contributionRepository.createContribution(
          testGoalId,
          emptyDescriptionContribution,
        );
        expect(result, isNotEmpty);

        final retrievedContribution = await contributionRepository
            .getContributionById(testGoalId, result);
        expect(retrievedContribution!.description, isEmpty);
      });

      test('createContribution should handle null description', () async {
        final nullDescriptionContribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: 5000,
          contributionDate: DateTime.now(),
          description: null,
        );

        final result = await contributionRepository.createContribution(
          testGoalId,
          nullDescriptionContribution,
        );
        expect(result, isNotEmpty);

        final retrievedContribution = await contributionRepository
            .getContributionById(testGoalId, result);
        expect(retrievedContribution!.description, isNull);
      });
    });

    group('Concurrent Operations and Race Conditions', () {
      test('should handle concurrent contribution creation', () async {
        final contributions = List.generate(
          5,
          (i) => GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 1000 * (i + 1),
            contributionDate: DateTime.now(),
            description: 'Contribution $i',
          ),
        );

        final futures = contributions.map(
          (contribution) => contributionRepository.createContribution(
            testGoalId,
            contribution,
          ),
        );
        final results = await Future.wait(futures);

        expect(results, hasLength(5));
        expect(results.every((id) => id.isNotEmpty), isTrue);

        final allContributions = await contributionRepository
            .getContributionsForGoal(testGoalId);
        expect(allContributions, hasLength(5));
      });

      test('should handle concurrent update operations', () async {
        final contribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: 5000,
          contributionDate: DateTime.now(),
          description: 'Original',
        );

        final contributionId = await contributionRepository.createContribution(
          testGoalId,
          contribution,
        );

        // Simulate concurrent updates
        final futures = [
          contributionRepository.updateContribution(
            testGoalId,
            contributionId,
            contribution.copyWith(description: 'Update 1'),
          ),
          contributionRepository.updateContribution(
            testGoalId,
            contributionId,
            contribution.copyWith(description: 'Update 2'),
          ),
          contributionRepository.updateContribution(
            testGoalId,
            contributionId,
            contribution.copyWith(description: 'Update 3'),
          ),
        ];

        await Future.wait(futures);

        final retrievedContribution = await contributionRepository
            .getContributionById(testGoalId, contributionId);
        expect(retrievedContribution!.description, isNotNull);
        expect(retrievedContribution.description, startsWith('Update'));
      });

      test('should handle concurrent deletion operations', () async {
        final contribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: 5000,
          contributionDate: DateTime.now(),
        );

        final contributionId = await contributionRepository.createContribution(
          testGoalId,
          contribution,
        );

        // Multiple deletion attempts should not cause errors
        final futures = [
          contributionRepository.deleteContribution(testGoalId, contributionId),
          contributionRepository.deleteContribution(testGoalId, contributionId),
          contributionRepository.deleteContribution(testGoalId, contributionId),
        ];

        await Future.wait(futures);

        final retrievedContribution = await contributionRepository
            .getContributionById(testGoalId, contributionId);
        expect(retrievedContribution!.isActive, isFalse);
      });
    });

    group('Performance and Stress Testing', () {
      test(
        'should handle large numbers of contributions efficiently',
        () async {
          // Create many contributions
          final contributions = List.generate(
            50,
            (i) => GoalContribution.create(
              userId: 'test-user-1',
              goalId: testGoalId,
              amountCents: 1000 * (i + 1),
              contributionDate: DateTime.now().subtract(Duration(days: i)),
              description: 'Contribution $i',
            ),
          );

          // Create all contributions
          for (final contribution in contributions) {
            await contributionRepository.createContribution(
              testGoalId,
              contribution,
            );
          }

          // Test retrieval performance
          final stopwatch = Stopwatch()..start();
          final allContributions = await contributionRepository
              .getContributionsForGoal(testGoalId);
          stopwatch.stop();

          expect(allContributions, hasLength(50));
          expect(
            stopwatch.elapsedMilliseconds,
            lessThan(5000),
          ); // Should complete in under 5 seconds
        },
      );

      test('should handle multiple goals with contributions', () async {
        // Create additional goals
        final goal2 = Goal(
          id: 'test-goal-2',
          userId: 'test-user-1',
          name: 'Second Goal',
          description: 'Another test goal',
          targetAmountCents: 50000,
          currentAmountCents: 0,
          targetDate: DateTime.now().add(const Duration(days: 180)),
          status: GoalStatus.active,
          colorHex: '#0000FF',
          iconName: 'target',
          isActive: true,
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final goal3 = Goal(
          id: 'test-goal-3',
          userId: 'test-user-1',
          name: 'Third Goal',
          description: 'Yet another test goal',
          targetAmountCents: 75000,
          currentAmountCents: 0,
          targetDate: DateTime.now().add(const Duration(days: 270)),
          status: GoalStatus.active,
          colorHex: '#FF0000',
          iconName: 'star',
          isActive: true,
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final goal2Id = await goalRepository.create(goal2);
        final goal3Id = await goalRepository.create(goal3);

        // Create contributions for each goal
        await contributionRepository.createContribution(
          testGoalId,
          GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 1000,
            contributionDate: DateTime.now(),
          ),
        );

        await contributionRepository.createContribution(
          goal2Id,
          GoalContribution.create(
            userId: 'test-user-1',
            goalId: goal2Id,
            amountCents: 2000,
            contributionDate: DateTime.now(),
          ),
        );

        await contributionRepository.createContribution(
          goal3Id,
          GoalContribution.create(
            userId: 'test-user-1',
            goalId: goal3Id,
            amountCents: 3000,
            contributionDate: DateTime.now(),
          ),
        );

        // Test cross-goal operations
        final allUserContributions = await contributionRepository
            .getAllUserContributions();
        expect(allUserContributions, hasLength(3));

        final totalAmountGoal1 = await contributionRepository
            .getTotalContributionAmountForGoal(testGoalId);
        final totalAmountGoal2 = await contributionRepository
            .getTotalContributionAmountForGoal(goal2Id);
        final totalAmountGoal3 = await contributionRepository
            .getTotalContributionAmountForGoal(goal3Id);

        expect(totalAmountGoal1, equals(1000));
        expect(totalAmountGoal2, equals(2000));
        expect(totalAmountGoal3, equals(3000));
      });
    });

    group('Business Logic and Domain Rules', () {
      test('should enforce contribution date validation rules', () async {
        final today = DateTime.now();

        // Valid contribution (today)
        final validContribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: 5000,
          contributionDate: today,
        );

        final validResult = await contributionRepository.createContribution(
          testGoalId,
          validContribution,
        );
        expect(validResult, isNotEmpty);

        // Valid contribution (yesterday)
        final yesterdayContribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: 3000,
          contributionDate: today.subtract(const Duration(days: 1)),
        );

        final yesterdayResult = await contributionRepository.createContribution(
          testGoalId,
          yesterdayContribution,
        );
        expect(yesterdayResult, isNotEmpty);
      });

      test('should handle contribution amount limits correctly', () async {
        // Test minimum valid amount (1 cent)
        final minAmountContribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: 1,
          contributionDate: DateTime.now(),
        );

        final minResult = await contributionRepository.createContribution(
          testGoalId,
          minAmountContribution,
        );
        expect(minResult, isNotEmpty);

        // Test maximum valid amount (just under $100M)
        final maxAmountContribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: 9999999999, // $99,999,999.99
          contributionDate: DateTime.now(),
        );

        final maxResult = await contributionRepository.createContribution(
          testGoalId,
          maxAmountContribution,
        );
        expect(maxResult, isNotEmpty);
      });

      test(
        'should validate contribution belongs to correct user and goal',
        () async {
          final contribution = GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 5000,
            contributionDate: DateTime.now(),
          );

          final contributionId = await contributionRepository
              .createContribution(testGoalId, contribution);
          final retrievedContribution = await contributionRepository
              .getContributionById(testGoalId, contributionId);

          expect(retrievedContribution!.userId, equals('test-user-1'));
          expect(retrievedContribution.goalId, equals(testGoalId));
          expect(retrievedContribution.id, equals(contributionId));
        },
      );
    });

    group('Advanced Stream Operations', () {
      test('watchAllUserContributions should handle multiple goals', () async {
        // Create additional goal
        final goal2 = Goal(
          id: 'test-goal-2',
          userId: 'test-user-1',
          name: 'Second Goal',
          description: 'Another test goal',
          targetAmountCents: 50000,
          currentAmountCents: 0,
          targetDate: DateTime.now().add(const Duration(days: 180)),
          status: GoalStatus.active,
          colorHex: '#0000FF',
          iconName: 'target',
          isActive: true,
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final goal2Id = await goalRepository.create(goal2);

        // Stream should emit periodic updates
        final stream = contributionRepository.watchAllUserContributions();

        // Add contributions to both goals
        await contributionRepository.createContribution(
          testGoalId,
          GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 1000,
            contributionDate: DateTime.now(),
          ),
        );

        await contributionRepository.createContribution(
          goal2Id,
          GoalContribution.create(
            userId: 'test-user-1',
            goalId: goal2Id,
            amountCents: 2000,
            contributionDate: DateTime.now(),
          ),
        );

        // Test stream emits data
        await expectLater(stream.take(1), emits(hasLength(2)));
      });

      test('should handle stream errors gracefully', () async {
        // Create a stream and test error handling
        final stream = contributionRepository.watchContributionsForGoal(
          'non-existent-goal',
        );

        // Stream should emit empty results for non-existent goal
        await expectLater(stream.take(1), emits(isEmpty));
      });
    });

    group('Data Consistency and Integrity', () {
      test('should maintain data consistency during updates', () async {
        final contribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: 5000,
          contributionDate: DateTime.now(),
          description: 'Original contribution',
        );

        final contributionId = await contributionRepository.createContribution(
          testGoalId,
          contribution,
        );

        // Update the contribution
        final updatedContribution = contribution.copyWith(
          amountCents: 7500,
          description: 'Updated contribution',
        );

        await contributionRepository.updateContribution(
          testGoalId,
          contributionId,
          updatedContribution,
        );

        // Verify all fields are updated consistently
        final retrievedContribution = await contributionRepository
            .getContributionById(testGoalId, contributionId);
        expect(retrievedContribution!.amountCents, equals(7500));
        expect(
          retrievedContribution.description,
          equals('Updated contribution'),
        );
        expect(retrievedContribution.userId, equals('test-user-1'));
        expect(retrievedContribution.goalId, equals(testGoalId));
        expect(retrievedContribution.isActive, isTrue);
      });

      test('should handle soft deletion properly', () async {
        final contribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: 5000,
          contributionDate: DateTime.now(),
        );

        final contributionId = await contributionRepository.createContribution(
          testGoalId,
          contribution,
        );

        // Verify contribution is active
        final activeContributions = await contributionRepository
            .getActiveContributionsForGoal(testGoalId);
        expect(activeContributions, hasLength(1));

        // Delete contribution
        await contributionRepository.deleteContribution(
          testGoalId,
          contributionId,
        );

        // Verify contribution is no longer active
        final activeContributionsAfterDelete = await contributionRepository
            .getActiveContributionsForGoal(testGoalId);
        expect(activeContributionsAfterDelete, isEmpty);

        // But still exists in all contributions
        final allContributions = await contributionRepository
            .getContributionsForGoal(testGoalId);
        expect(allContributions, hasLength(1));
        expect(allContributions.first.isActive, isFalse);
      });

      test('should handle metadata and timestamp fields correctly', () async {
        final beforeCreation = DateTime.now();

        // Add small delay to ensure timestamp difference
        await Future<void>.delayed(const Duration(milliseconds: 10));

        final contribution = GoalContribution.create(
          userId: 'test-user-1',
          goalId: testGoalId,
          amountCents: 5000,
          contributionDate: DateTime.now(),
        );

        final contributionId = await contributionRepository.createContribution(
          testGoalId,
          contribution,
        );

        final afterCreation = DateTime.now();

        final retrievedContribution = await contributionRepository
            .getContributionById(testGoalId, contributionId);

        expect(
          retrievedContribution!.createdAt?.isAfter(beforeCreation) ?? false,
          isTrue,
        );
        expect(
          retrievedContribution.createdAt?.isBefore(afterCreation) ?? false,
          isTrue,
        );
        expect(
          retrievedContribution.updatedAt?.isAfter(beforeCreation) ?? false,
          isTrue,
        );
        expect(
          retrievedContribution.updatedAt?.isBefore(afterCreation) ?? false,
          isTrue,
        );
        expect(retrievedContribution.schemaVersion, equals(1));
      });
    });

    group('Denormalized Goal Amount Updates', () {
      test(
        'createContribution should update goal currentAmountCents',
        () async {
          // Get initial goal state
          final initialGoal = await goalRepository.getGoalById(testGoalId);
          expect(initialGoal, isNotNull);
          final initialAmount = initialGoal!.currentAmountCents;

          // Create a contribution
          final contribution = GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 5000, // $50.00
            contributionDate: DateTime.now(),
            description: 'Test contribution',
          );

          await contributionRepository.createContribution(
            testGoalId,
            contribution,
          );

          // Verify goal currentAmountCents was updated
          final updatedGoal = await goalRepository.getGoalById(testGoalId);
          expect(updatedGoal, isNotNull);
          expect(updatedGoal!.currentAmountCents, equals(initialAmount + 5000));
        },
      );

      test(
        'updateContribution should adjust goal currentAmountCents',
        () async {
          // Create initial contribution
          final contribution = GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 3000, // $30.00
            contributionDate: DateTime.now(),
            description: 'Initial contribution',
          );

          final contributionId = await contributionRepository
              .createContribution(testGoalId, contribution);

          // Get goal state after creation
          final goalAfterCreate = await goalRepository.getGoalById(testGoalId);
          final amountAfterCreate = goalAfterCreate!.currentAmountCents;

          // Update contribution amount
          final updatedContribution = contribution.copyWith(
            amountCents: 7000, // $70.00 (increase of $40.00)
            description: 'Updated contribution',
          );

          await contributionRepository.updateContribution(
            testGoalId,
            contributionId,
            updatedContribution,
          );

          // Verify goal currentAmountCents was adjusted by the difference
          final goalAfterUpdate = await goalRepository.getGoalById(testGoalId);
          expect(goalAfterUpdate, isNotNull);
          expect(
            goalAfterUpdate!.currentAmountCents,
            equals(amountAfterCreate + 4000), // +$40.00 difference
          );
        },
      );

      test(
        'deleteContribution should subtract from goal currentAmountCents',
        () async {
          // Create a contribution
          final contribution = GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 2500, // $25.00
            contributionDate: DateTime.now(),
            description: 'To be deleted',
          );

          final contributionId = await contributionRepository
              .createContribution(testGoalId, contribution);

          // Get goal state after creation
          final goalAfterCreate = await goalRepository.getGoalById(testGoalId);
          final amountAfterCreate = goalAfterCreate!.currentAmountCents;

          // Delete the contribution
          await contributionRepository.deleteContribution(
            testGoalId,
            contributionId,
          );

          // Verify goal currentAmountCents was decreased
          final goalAfterDelete = await goalRepository.getGoalById(testGoalId);
          expect(goalAfterDelete, isNotNull);
          expect(
            goalAfterDelete!.currentAmountCents,
            equals(amountAfterCreate - 2500), // -$25.00
          );
        },
      );

      test('multiple contributions should accumulate correctly', () async {
        // Get initial goal state
        final initialGoal = await goalRepository.getGoalById(testGoalId);
        final initialAmount = initialGoal!.currentAmountCents;

        // Create multiple contributions
        final contributions = [
          GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 1000,
            contributionDate: DateTime.now(),
            description: 'First',
          ),
          GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 1500,
            contributionDate: DateTime.now(),
            description: 'Second',
          ),
          GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 2000,
            contributionDate: DateTime.now(),
            description: 'Third',
          ),
        ];

        for (final contribution in contributions) {
          await contributionRepository.createContribution(
            testGoalId,
            contribution,
          );
        }

        // Verify total accumulation
        final finalGoal = await goalRepository.getGoalById(testGoalId);
        expect(finalGoal, isNotNull);
        expect(
          finalGoal!.currentAmountCents,
          equals(initialAmount + 4500), // $45.00 total
        );
      });

      test(
        'deleting inactive contribution should not affect goal amount',
        () async {
          // Create and then delete a contribution
          final contribution = GoalContribution.create(
            userId: 'test-user-1',
            goalId: testGoalId,
            amountCents: 1000,
            contributionDate: DateTime.now(),
            description: 'To be deleted twice',
          );

          final contributionId = await contributionRepository
              .createContribution(testGoalId, contribution);

          // Delete once (should subtract from goal)
          await contributionRepository.deleteContribution(
            testGoalId,
            contributionId,
          );

          // Get goal state after first deletion
          final goalAfterFirstDelete = await goalRepository.getGoalById(
            testGoalId,
          );
          final amountAfterFirstDelete =
              goalAfterFirstDelete!.currentAmountCents;

          // Try to delete again (should not affect goal since already inactive)
          await contributionRepository.deleteContribution(
            testGoalId,
            contributionId,
          );

          // Verify goal amount unchanged
          final goalAfterSecondDelete = await goalRepository.getGoalById(
            testGoalId,
          );
          expect(goalAfterSecondDelete, isNotNull);
          expect(
            goalAfterSecondDelete!.currentAmountCents,
            equals(amountAfterFirstDelete),
          );
        },
      );
    });
  });
}
