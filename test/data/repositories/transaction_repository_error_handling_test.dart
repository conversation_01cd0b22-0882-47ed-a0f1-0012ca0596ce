import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/implementations/transaction_repository_impl.dart';
import 'package:budapp/features/budgets/services/budget_transaction_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart' hide Transaction;
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/mock_data_factory.dart';

// Mock classes for testing
class MockBudgetTransactionService extends Mock
    implements BudgetTransactionService {}

class MockFirestoreService extends Mock implements FirestoreService {}

class MockWriteBatch extends Mock implements WriteBatch {}

void main() {
  setUpAll(() {
    // Use MockDataFactory to create fallback values instead of Fake classes
    registerFallbackValue(MockDataFactory.createTransaction());
  });

  group('TransactionRepository Error Handling Tests', () {
    late FakeFirebaseFirestore fakeFirestore;
    late FirestoreService firestoreService;
    late MockBudgetTransactionService mockBudgetTransactionService;
    late TransactionRepositoryImpl repository;
    late String testUserId;
    late String testAccountId1;
    late String testAccountId2;

    setUp(() async {
      fakeFirestore = FakeFirebaseFirestore();
      firestoreService = FirestoreService(fakeFirestore);
      mockBudgetTransactionService = MockBudgetTransactionService();
      repository = TransactionRepositoryImpl(
        firestoreService,
        mockBudgetTransactionService,
      );

      // Setup mock budget transaction service
      when(
        () => mockBudgetTransactionService.updateBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      when(
        () => mockBudgetTransactionService.revertBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async {});

      when(
        () => mockBudgetTransactionService.updateBudgetsForTransactionUpdate(
          any(),
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      testUserId = 'test-user-123';
      testAccountId1 = 'account-1';
      testAccountId2 = 'account-2';

      // Create test accounts
      final account1 = MockDataFactory.createAccount(
        id: testAccountId1,
        userId: testUserId,
        name: 'Test Account 1',
        type: AccountType.checking,
        classification: AccountClassification.asset,
        initialBalanceCents: 100000,
      );

      final account2 = MockDataFactory.createAccount(
        id: testAccountId2,
        userId: testUserId,
        name: 'Test Account 2',
        type: AccountType.savings,
        classification: AccountClassification.asset,
        initialBalanceCents: 50000,
      );

      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId1)
          .set(account1.toJson());

      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId2)
          .set(account2.toJson());
    });

    group('createTransaction Error Handling', () {
      test('should throw ArgumentError for invalid transaction data', () async {
        final invalidTransaction = MockDataFactory.createTransaction(
          id: 'invalid-transaction',
          userId: testUserId,
          fromAccountId: 'non-existent-account',
          amountCents: -1000, // Invalid negative amount
          type: TransactionType.expense,
        );

        expect(
          () => repository.createTransaction(invalidTransaction),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should handle validation failure gracefully', () async {
        final transaction = MockDataFactory.createTransaction(
          id: 'test-transaction',
          userId: testUserId,
          fromAccountId: 'non-existent-account',
          amountCents: 1000,
          type: TransactionType.expense,
        );

        expect(
          () => repository.createTransaction(transaction),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('batchCreate Error Handling', () {
      test('should handle mixed user transactions error', () async {
        final transactions = [
          MockDataFactory.createTransaction(
            id: 'tx1',
            userId: testUserId,
            fromAccountId: testAccountId1,
            amountCents: 1000,
            type: TransactionType.expense,
          ),
          MockDataFactory.createTransaction(
            id: 'tx2',
            userId: 'different-user',
            fromAccountId: testAccountId1,
            amountCents: 2000,
            type: TransactionType.expense,
          ),
        ];

        expect(
          () => repository.batchCreate(transactions),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should handle empty transactions list', () async {
        await repository.batchCreate([]);
        // Should complete without error
      });
    });

    group('batchUpdate Error Handling', () {
      test('should handle mixed user transactions error', () async {
        final transactions = {
          'tx1': MockDataFactory.createTransaction(
            id: 'tx1',
            userId: testUserId,
            fromAccountId: testAccountId1,
            amountCents: 1000,
            type: TransactionType.expense,
          ),
          'tx2': MockDataFactory.createTransaction(
            id: 'tx2',
            userId: 'different-user',
            fromAccountId: testAccountId1,
            amountCents: 2000,
            type: TransactionType.expense,
          ),
        };

        expect(
          () => repository.batchUpdate(transactions),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should handle empty transactions map', () async {
        await repository.batchUpdate({});
        // Should complete without error
      });
    });

    group('Delete Transaction Error Handling', () {
      test('should throw error for non-existent transaction', () async {
        expect(
          () => repository.deleteTransaction(testUserId, 'non-existent-tx'),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should throw error for wrong user transaction', () async {
        // Create transaction for one user
        final transaction = MockDataFactory.createTransaction(
          id: 'user1-transaction',
          userId: testUserId,
          fromAccountId: testAccountId1,
          amountCents: 1000,
          type: TransactionType.expense,
        );

        await repository.create(transaction);

        // Try to delete with different user
        expect(
          () => repository.deleteTransaction(
            'different-user',
            'user1-transaction',
          ),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('Update Transaction Error Handling', () {
      test('should throw error for non-existent transaction', () async {
        final updatedTransaction = MockDataFactory.createTransaction(
          id: 'non-existent-tx',
          userId: testUserId,
          fromAccountId: testAccountId1,
          amountCents: 1000,
          type: TransactionType.expense,
        );

        expect(
          () => repository.updateTransactionWithBalanceAdjustment(
            testUserId,
            'non-existent-tx',
            updatedTransaction,
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should throw error for wrong user transaction', () async {
        // Create transaction for one user
        final originalTransaction = MockDataFactory.createTransaction(
          id: 'original-tx',
          userId: testUserId,
          fromAccountId: testAccountId1,
          amountCents: 1000,
          type: TransactionType.expense,
        );

        await repository.create(originalTransaction);

        // Try to update with different user
        final updatedTransaction = originalTransaction.copyWith(
          userId: 'different-user',
          amountCents: 2000,
        );

        expect(
          () => repository.updateTransactionWithBalanceAdjustment(
            testUserId,
            'original-tx',
            updatedTransaction,
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should handle account validation errors during update', () async {
        // Create transaction
        final originalTransaction = MockDataFactory.createTransaction(
          id: 'update-tx',
          userId: testUserId,
          fromAccountId: testAccountId1,
          amountCents: 1000,
          type: TransactionType.expense,
        );

        await repository.create(originalTransaction);

        // Try to update with non-existent account
        final updatedTransaction = originalTransaction.copyWith(
          fromAccountId: 'non-existent-account',
        );

        expect(
          () => repository.updateTransactionWithBalanceAdjustment(
            testUserId,
            'update-tx',
            updatedTransaction,
          ),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('Validation Error Handling', () {
      test('should fail validation for zero amount', () async {
        final transaction = MockDataFactory.createTransaction(
          id: 'zero-amount-tx',
          userId: testUserId,
          fromAccountId: testAccountId1,
          amountCents: 0,
          type: TransactionType.expense,
        );

        final isValid = await repository.validateTransaction(transaction);
        expect(isValid, isFalse);
      });

      test('should fail validation for negative amount', () async {
        final transaction = MockDataFactory.createTransaction(
          id: 'negative-amount-tx',
          userId: testUserId,
          fromAccountId: testAccountId1,
          amountCents: -1000,
          type: TransactionType.expense,
        );

        final isValid = await repository.validateTransaction(transaction);
        expect(isValid, isFalse);
      });

      test('should fail validation for empty userId', () async {
        final transaction = MockDataFactory.createTransaction(
          id: 'empty-user-tx',
          userId: '',
          fromAccountId: testAccountId1,
          amountCents: 1000,
          type: TransactionType.expense,
        );

        final isValid = await repository.validateTransaction(transaction);
        expect(isValid, isFalse);
      });

      test('should fail validation for income without toAccountId', () async {
        final transaction = Transaction(
          id: 'invalid-income',
          userId: testUserId,
          type: TransactionType.income,
          status: TransactionStatus.completed,
          amountCents: 1000,
          toAccountId: null, // Missing required toAccountId
          transactionDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final isValid = await repository.validateTransaction(transaction);
        expect(isValid, isFalse);
      });

      test(
        'should fail validation for expense without fromAccountId',
        () async {
          final transaction = Transaction(
            id: 'invalid-expense',
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 1000,
            fromAccountId: null, // Missing required fromAccountId
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          final isValid = await repository.validateTransaction(transaction);
          expect(isValid, isFalse);
        },
      );

      test('should fail validation for transfer with same accounts', () async {
        final transaction = Transaction(
          id: 'invalid-transfer',
          userId: testUserId,
          type: TransactionType.transfer,
          status: TransactionStatus.completed,
          amountCents: 1000,
          fromAccountId: testAccountId1,
          toAccountId: testAccountId1, // Same as fromAccountId
          transactionDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final isValid = await repository.validateTransaction(transaction);
        expect(isValid, isFalse);
      });

      test('should fail validation for transfer without accounts', () async {
        final transaction = Transaction(
          id: 'invalid-transfer-2',
          userId: testUserId,
          type: TransactionType.transfer,
          status: TransactionStatus.completed,
          amountCents: 1000,
          fromAccountId: null,
          toAccountId: null,
          transactionDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final isValid = await repository.validateTransaction(transaction);
        expect(isValid, isFalse);
      });
    });

    group('Bulk Operations Error Handling', () {
      test('should handle bulkCreateTransactions validation errors', () async {
        final invalidTransactions = [
          MockDataFactory.createTransaction(
            id: 'valid-tx',
            userId: testUserId,
            fromAccountId: testAccountId1,
            amountCents: 1000,
            type: TransactionType.expense,
          ),
          MockDataFactory.createTransaction(
            id: 'invalid-tx',
            userId: testUserId,
            fromAccountId: 'non-existent-account',
            amountCents: 1000,
            type: TransactionType.expense,
          ),
        ];

        expect(
          () => repository.bulkCreateTransactions(invalidTransactions),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should handle empty bulk create list', () async {
        final result = await repository.bulkCreateTransactions([]);
        expect(result, isEmpty);
      });
    });

    group('Account Balance Calculation Error Handling', () {
      test(
        'should handle non-existent account in balance calculation',
        () async {
          final balance = await repository.calculateAccountBalanceForUser(
            testUserId,
            'non-existent-account',
          );
          expect(balance, equals(0));
        },
      );
    });

    group('Search and Query Error Handling', () {
      test('should handle search with empty query', () async {
        final results = await repository.searchTransactions(testUserId, '');
        expect(results, isA<List<Transaction>>());
      });

      test('should handle search with special characters', () async {
        final results = await repository.searchTransactions(
          testUserId,
          '!@#\$%^&*()[]{}|\\:";\'<>?,./`~',
        );
        expect(results, isA<List<Transaction>>());
      });

      test('should handle getRecentTransactions with large limit', () async {
        final results = await repository.getRecentTransactions(
          testUserId,
          limit: 1000,
        );
        expect(results, isA<List<Transaction>>());
      });

      test('should handle getLargestTransactions with large limit', () async {
        final results = await repository.getLargestTransactions(
          testUserId,
          limit: 1000,
        );
        expect(results, isA<List<Transaction>>());
      });
    });

    group('Transaction Stats Error Handling', () {
      test('should handle stats calculation with empty transactions', () async {
        final stats = await repository.getTransactionStats(testUserId);

        expect(stats['totalTransactions'], equals(0));
        expect(stats['totalIncome'], equals(0));
        expect(stats['totalExpenses'], equals(0));
        expect(stats['totalTransfers'], equals(0));
        expect(stats['averageTransactionAmount'], equals(0));
        expect(stats['largestTransaction'], equals(0));
        expect(stats['smallestTransaction'], equals(0));
      });

      test('should handle spending by category with no transactions', () async {
        final startDate = DateTime.now().subtract(const Duration(days: 30));
        final endDate = DateTime.now();

        final spending = await repository.getSpendingByCategory(
          testUserId,
          startDate,
          endDate,
        );

        expect(spending, isEmpty);
      });

      test(
        'should handle income/expense summary with no transactions',
        () async {
          final startDate = DateTime.now().subtract(const Duration(days: 30));
          final endDate = DateTime.now();

          final summary = await repository.getIncomeExpenseSummary(
            testUserId,
            startDate,
            endDate,
          );

          expect(summary['income'], equals(0));
          expect(summary['expenses'], equals(0));
          expect(summary['netIncome'], equals(0));
        },
      );
    });

    group('Reassignment Error Handling', () {
      test(
        'should handle reassignTransactionsToAccountForUser with no transactions',
        () async {
          await repository.reassignTransactionsToAccountForUser(
            testUserId,
            'non-existent-old-account',
            testAccountId1,
          );
          // Should complete without error
        },
      );

      test(
        'should handle reassignTransactionsToCategoryForUser with no transactions',
        () async {
          await repository.reassignTransactionsToCategoryForUser(
            testUserId,
            'non-existent-old-category',
            'new-category',
          );
          // Should complete without error
        },
      );
    });

    group('Stream Error Handling', () {
      test(
        'should handle watchTransactionsByMonthAndCategories with invalid month',
        () async {
          final invalidMonth = DateTime(2025, 13, 1); // Invalid month

          final stream = repository.watchTransactionsByMonthAndCategories(
            userId: testUserId,
            month: invalidMonth,
          );

          // Stream should still be valid even with invalid month
          expect(stream, isA<Stream<List<Transaction>>>());
        },
      );

      test(
        'should handle watchTransactionsByMonthAndCategories with empty categories',
        () async {
          final stream = repository.watchTransactionsByMonthAndCategories(
            userId: testUserId,
            month: DateTime.now(),
            categoryIds: [],
          );

          expect(stream, isA<Stream<List<Transaction>>>());
        },
      );
    });

    group('Date Range Query Error Handling', () {
      test('should handle invalid date range (end before start)', () async {
        final startDate = DateTime.now();
        final endDate = startDate.subtract(const Duration(days: 1));

        final results = await repository.getTransactionsByDateRange(
          testUserId,
          startDate,
          endDate,
        );

        expect(results, isA<List<Transaction>>());
      });

      test('should handle very large date ranges', () async {
        final startDate = DateTime(1900, 1, 1);
        final endDate = DateTime(2100, 12, 31);

        final results = await repository.getTransactionsByDateRange(
          testUserId,
          startDate,
          endDate,
        );

        expect(results, isA<List<Transaction>>());
      });
    });

    group('Edge Cases and Boundary Conditions', () {
      test('should handle transactions with maximum amount', () async {
        const maxAmount = 9223372036854775807; // Max int64

        final transaction = MockDataFactory.createTransaction(
          id: 'max-amount-tx',
          userId: testUserId,
          fromAccountId: testAccountId1,
          amountCents: maxAmount,
          type: TransactionType.expense,
        );

        final isValid = await repository.validateTransaction(transaction);
        expect(isValid, isTrue);
      });

      test(
        'should handle getUserTransactionsPaginated with zero limit',
        () async {
          final transactions = await repository.getUserTransactionsPaginated(
            testUserId,
            limit: 0,
          );

          expect(transactions, isA<List<Transaction>>());
        },
      );

      test(
        'should handle getUserTransactionsPaginated with large limit',
        () async {
          final transactions = await repository.getUserTransactionsPaginated(
            testUserId,
            limit: 10000,
          );

          expect(transactions, isA<List<Transaction>>());
        },
      );

      test('should handle transactions with very long descriptions', () async {
        final longDescription = 'A' * 10000; // Very long description

        final transaction = MockDataFactory.createTransaction(
          id: 'long-desc-tx',
          userId: testUserId,
          fromAccountId: testAccountId1,
          amountCents: 1000,
          type: TransactionType.expense,
          description: longDescription,
        );

        final result = await repository.create(transaction);
        expect(result, isNotNull);
      });

      test(
        'should handle transactions with special character descriptions',
        () async {
          const specialCharDescription = '🤑💰💸📊💳💵💴💶💷🏦💎💸';

          final transaction = MockDataFactory.createTransaction(
            id: 'special-char-tx',
            userId: testUserId,
            fromAccountId: testAccountId1,
            amountCents: 1000,
            type: TransactionType.expense,
            description: specialCharDescription,
          );

          final result = await repository.create(transaction);
          expect(result, isNotNull);
        },
      );
    });
  });
}
