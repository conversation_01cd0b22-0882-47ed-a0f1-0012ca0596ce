import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/models/budget_progress.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/models/user_profile.dart';
import 'package:budapp/data/repositories/interfaces/repositories.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Repository Interface Tests', () {
    test('repository interfaces are properly defined', () {
      // Test that all repository interfaces are accessible
      expect(IUserRepository, isA<Type>());
      expect(IAccountRepository, isA<Type>());
      expect(ITransactionRepository, isA<Type>());
      expect(ICategoryRepository, isA<Type>());
      // expect(IBudgetRepository, isA<Type>()); // TODO: Add when interface is exported
    });

    test('data models are properly defined', () {
      // Test that all data models are accessible
      expect(UserProfile, isA<Type>());
      expect(Account, isA<Type>());
      expect(Transaction, isA<Type>());
      expect(Category, isA<Type>());
      expect(Budget, isA<Type>());
      expect(BudgetProgress, isA<Type>());
    });

    test('enums are properly defined', () {
      // Test that all enums are accessible
      expect(AccountType.values, isNotEmpty);
      expect(AccountClassification.values, isNotEmpty);
      expect(TransactionType.values, isNotEmpty);
      expect(TransactionStatus.values, isNotEmpty);
      expect(CategoryType.values, isNotEmpty);
      expect(BudgetPeriod.values, isNotEmpty);
      expect(BudgetType.values, isNotEmpty);
    });

    test('account types are correctly defined', () {
      expect(AccountType.values, contains(AccountType.checking));
      expect(AccountType.values, contains(AccountType.savings));
      expect(AccountType.values, contains(AccountType.creditCard));
      expect(AccountType.values, contains(AccountType.cash));
      expect(AccountType.values, contains(AccountType.investment));
      expect(AccountType.values, contains(AccountType.loan));
    });

    test('account classifications are correctly defined', () {
      expect(
        AccountClassification.values,
        contains(AccountClassification.asset),
      );
      expect(
        AccountClassification.values,
        contains(AccountClassification.liability),
      );
    });

    test('transaction types are correctly defined', () {
      expect(TransactionType.values, contains(TransactionType.income));
      expect(TransactionType.values, contains(TransactionType.expense));
      expect(TransactionType.values, contains(TransactionType.transfer));
    });

    test('transaction statuses are correctly defined', () {
      expect(TransactionStatus.values, contains(TransactionStatus.pending));
      expect(TransactionStatus.values, contains(TransactionStatus.completed));
      expect(TransactionStatus.values, contains(TransactionStatus.cancelled));
      expect(TransactionStatus.values, contains(TransactionStatus.failed));
    });

    test('category types are correctly defined', () {
      expect(CategoryType.values, contains(CategoryType.income));
      expect(CategoryType.values, contains(CategoryType.expense));
      expect(CategoryType.values.length, equals(2));
    });

    test('budget periods are correctly defined', () {
      expect(BudgetPeriod.values, contains(BudgetPeriod.monthly));
      expect(BudgetPeriod.values, contains(BudgetPeriod.yearly));
    });

    test('budget types are correctly defined', () {
      expect(BudgetType.values, contains(BudgetType.expense));
      expect(BudgetType.values, contains(BudgetType.income));
    });
  });

  group('Data Model Tests', () {
    test('UserProfile can be created and serialized', () {
      final now = DateTime.now();
      final userProfile = UserProfile(
        uid: 'test-uid',
        email: '<EMAIL>',
        displayName: 'Test User',
        createdAt: now,
        lastLoginAt: now,
        isEmailVerified: true,
        authProviders: ['password'],
      );

      expect(userProfile.uid, 'test-uid');
      expect(userProfile.email, '<EMAIL>');
      expect(userProfile.displayName, 'Test User');
      expect(userProfile.isEmailVerified, true);

      // Test JSON serialization
      final json = userProfile.toJson();
      expect(json['uid'], 'test-uid');
      expect(json['email'], '<EMAIL>');

      // Test JSON deserialization
      final fromJson = UserProfile.fromJson(json);
      expect(fromJson.uid, userProfile.uid);
      expect(fromJson.email, userProfile.email);
    });

    test('Account can be created and serialized', () {
      final now = DateTime.now();
      final account = Account(
        id: 'test-account',
        userId: 'test-user',
        name: 'Test Account',
        type: AccountType.checking,
        classification: AccountClassification.asset,
        initialBalanceCents: 100000,
        currentBalanceCents: 100000, // $1000.00

        createdAt: now,
        updatedAt: now,
      );

      expect(account.id, 'test-account');
      expect(account.name, 'Test Account');
      expect(account.type, AccountType.checking);
      expect(account.classification, AccountClassification.asset);
      expect(account.initialBalanceCents, 100000);

      // Test JSON serialization
      final json = account.toJson();
      expect(json['id'], 'test-account');
      expect(json['type'], 'checking');
      expect(json['classification'], 'asset');

      // Test JSON deserialization
      final fromJson = Account.fromJson(json);
      expect(fromJson.id, account.id);
      expect(fromJson.type, account.type);
      expect(fromJson.classification, account.classification);
    });

    test('Transaction can be created and serialized', () {
      final now = DateTime.now();
      final transaction = Transaction(
        id: 'test-transaction',
        userId: 'test-user',
        type: TransactionType.expense,
        status: TransactionStatus.completed,
        amountCents: 5000, // $50.00

        fromAccountId: 'account-1',
        categoryId: 'category-1',
        description: 'Test transaction',
        transactionDate: now,
        createdAt: now,
        updatedAt: now,
      );

      expect(transaction.id, 'test-transaction');
      expect(transaction.type, TransactionType.expense);
      expect(transaction.status, TransactionStatus.completed);
      expect(transaction.amountCents, 5000);

      // Test JSON serialization
      final json = transaction.toJson();
      expect(json['id'], 'test-transaction');
      expect(json['type'], 'expense');
      expect(json['status'], 'completed');

      // Test JSON deserialization
      final fromJson = Transaction.fromJson(json);
      expect(fromJson.id, transaction.id);
      expect(fromJson.type, transaction.type);
      expect(fromJson.status, transaction.status);
    });
  });
}
