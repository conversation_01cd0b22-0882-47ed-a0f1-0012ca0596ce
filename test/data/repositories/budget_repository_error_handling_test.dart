import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/repositories/implementations/budget_repository_impl.dart';
import 'package:budapp/features/budgets/services/budget_error_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/mock_data_factory.dart';

// Mock classes for testing
class MockFirebaseAuth extends Mock implements FirebaseAuth {}

class MockUser extends Mock implements User {}

void main() {
  setUpAll(() {
    // Use MockDataFactory to create fallback values instead of Fake classes
    registerFallbackValue(MockDataFactory.createBudget());
  });

  group('BudgetRepository Error Handling Tests', () {
    late FakeFirebaseFirestore fakeFirestore;
    late FirestoreService firestoreService;
    late MockFirebaseAuth mockFirebaseAuth;
    late MockUser mockUser;
    late BudgetRepositoryImpl repository;
    late String testUserId;

    setUp(() async {
      fakeFirestore = FakeFirebaseFirestore();
      firestoreService = FirestoreService(fakeFirestore);
      mockFirebaseAuth = MockFirebaseAuth();
      mockUser = MockUser();
      testUserId = 'test-user-123';

      // Setup mock Firebase Auth
      when(() => mockUser.uid).thenReturn(testUserId);
      when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);

      repository = BudgetRepositoryImpl(
        firestoreService: firestoreService,
        firebaseAuth: mockFirebaseAuth,
      );
    });

    group('Authentication Error Handling', () {
      test(
        'should throw exception when user not authenticated for getAllBudgets',
        () async {
          // Arrange - No authenticated user
          when(() => mockFirebaseAuth.currentUser).thenReturn(null);

          // Act & Assert
          expect(
            () => repository.getAllBudgets(),
            throwsA(isA<BudgetException>()),
          );
        },
      );

      test(
        'should throw exception when user not authenticated for getBudgetsByPeriod',
        () async {
          // Arrange - No authenticated user
          when(() => mockFirebaseAuth.currentUser).thenReturn(null);

          // Act & Assert
          expect(
            () => repository.getBudgetsByPeriod(
              DateTime.now(),
              BudgetPeriod.monthly,
            ),
            throwsA(isA<BudgetException>()),
          );
        },
      );

      test(
        'should throw exception when user not authenticated for batchCreateBudgets',
        () async {
          // Arrange - No authenticated user
          when(() => mockFirebaseAuth.currentUser).thenReturn(null);

          final budget = Budget(
            id: '',
            userId: testUserId,
            type: BudgetType.expense,
            plannedAmountCents: 100000,
            period: BudgetPeriod.monthly,
            periodStart: DateTime(2024, 1, 1),
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          // Act & Assert
          expect(
            () => repository.batchCreateBudgets([budget]),
            throwsA(isA<BudgetException>()),
          );
        },
      );
    });

    group('Budget Creation Error Handling', () {
      test('should handle empty budget list in batchCreateBudgets', () async {
        // Should complete without error
        await repository.batchCreateBudgets([]);
      });

      test('should throw validation error for invalid budget data', () async {
        final invalidBudget = Budget(
          id: '',
          userId: testUserId,
          type: BudgetType.expense,
          plannedAmountCents: -1000, // Negative amount should fail validation
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2024, 1, 1),
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(
          () => repository.batchCreateBudgets([invalidBudget]),
          throwsA(isA<BudgetException>()),
        );
      });

      test('should handle budget creation with future dates', () async {
        final futureDate = DateTime.now().add(const Duration(days: 365));
        final futureBudget = Budget(
          id: '',
          userId: testUserId,
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.monthly,
          periodStart: DateTime(futureDate.year, futureDate.month, 1),
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Should create successfully
        await repository.batchCreateBudgets([futureBudget]);
      });

      test('should throw validation error for very large amounts', () async {
        final largeBudget = Budget(
          id: '',
          userId: testUserId,
          type: BudgetType.expense,
          plannedAmountCents:
              999999999999, // Very large amount (exceeds validator limit)
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2024, 2, 1),
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Should throw validation error for amount too large
        expect(
          () => repository.batchCreateBudgets([largeBudget]),
          throwsA(isA<BudgetException>()),
        );
      });
    });

    group('Budget Update Error Handling', () {
      test('should handle empty budget list in batchUpdateBudgets', () async {
        // Should complete without error
        await repository.batchUpdateBudgets([]);
      });

      test('should throw validation error for invalid update data', () async {
        // Create existing budget first
        final existingBudget = Budget(
          id: 'update-budget',
          userId: testUserId,
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2024, 3, 1),
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('budgets')
            .doc('update-budget')
            .set(existingBudget.toJson());

        // Try to update with invalid data
        final invalidUpdate = existingBudget.copyWith(
          plannedAmountCents: -5000, // Negative amount should fail validation
        );

        expect(
          () async => repository.batchUpdateBudgets([invalidUpdate]),
          throwsA(isA<BudgetException>()),
        );
      });

      test('should throw error when updating non-existent budget', () async {
        final nonExistentBudget = Budget(
          id: 'non-existent-budget',
          userId: testUserId,
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2024, 4, 1),
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Should throw error for non-existent budget
        expect(
          () => repository.batchUpdateBudgets([nonExistentBudget]),
          throwsA(isA<BudgetException>()),
        );
      });
    });

    group('Budget Retrieval Error Handling', () {
      test('should handle empty budget collection', () async {
        final budgets = await repository.getAllBudgets();
        expect(budgets, isEmpty);
      });

      test(
        'should handle getBudgetsByPeriod with no matching budgets',
        () async {
          final budgets = await repository.getBudgetsByPeriod(
            DateTime(2024, 12, 1),
            BudgetPeriod.monthly,
          );
          expect(budgets, isEmpty);
        },
      );

      test('should handle malformed budget data in Firestore', () async {
        // Add malformed data to Firestore
        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('budgets')
            .doc('malformed-budget')
            .set({
              'id': 'malformed-budget',
              'userId': testUserId,
              'type': 'invalid_type', // Invalid enum value
              'period': 'invalid_period', // Invalid enum value
              'periodStart': 'invalid_date', // Invalid date format
              'plannedAmountCents': 'not_a_number', // Wrong data type
              'isActive': 'not_a_boolean', // Wrong data type
              'createdAt': 'invalid_date',
              'updatedAt': 'invalid_date',
            });

        // Should handle gracefully
        expect(
          () => repository.getAllBudgets(),
          throwsA(isA<BudgetException>()),
        );
      });

      test('should handle budgets with missing required fields', () async {
        // Add incomplete data to Firestore
        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('budgets')
            .doc('incomplete-budget')
            .set({
              'id': 'incomplete-budget',
              // Missing required fields
            });

        expect(
          () => repository.getAllBudgets(),
          throwsA(isA<BudgetException>()),
        );
      });
    });

    group('Period Calculation Error Handling', () {
      test('should handle getBudgetsByPeriod with invalid dates', () async {
        // Test with February 29th on non-leap year
        final invalidDate = DateTime(
          2023,
          2,
          29,
        ); // This will be adjusted by Dart

        final budgets = await repository.getBudgetsByPeriod(
          invalidDate,
          BudgetPeriod.monthly,
        );
        expect(budgets, isA<List<Budget>>());
      });

      test('should handle getBudgetsByPeriod with extreme dates', () async {
        // Test with very old date
        final oldDate = DateTime(1900, 1, 1);

        final budgets = await repository.getBudgetsByPeriod(
          oldDate,
          BudgetPeriod.yearly,
        );
        expect(budgets, isA<List<Budget>>());
      });

      test('should handle getBudgetsByPeriod with future dates', () async {
        // Test with far future date
        final futureDate = DateTime(2100, 12, 31);

        final budgets = await repository.getBudgetsByPeriod(
          futureDate,
          BudgetPeriod.monthly,
        );
        expect(budgets, isA<List<Budget>>());
      });
    });

    group('Batch Operation Error Handling', () {
      test(
        'should handle batchCreateBudgets with mixed valid/invalid data',
        () async {
          final validBudget = Budget(
            id: '',
            userId: testUserId,
            type: BudgetType.expense,
            plannedAmountCents: 100000,
            period: BudgetPeriod.monthly,
            periodStart: DateTime(2024, 5, 1),
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          final invalidBudget = Budget(
            id: '',
            userId: testUserId,
            type: BudgetType.expense,
            plannedAmountCents: -1000, // Negative amount should fail validation
            period: BudgetPeriod.monthly,
            periodStart: DateTime(2024, 6, 1),
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          // Should fail for all budgets if any are invalid
          expect(
            () => repository.batchCreateBudgets([validBudget, invalidBudget]),
            throwsA(isA<BudgetException>()),
          );
        },
      );

      test('should handle very large batch operations', () async {
        // Create many budgets
        final largeBatch = List.generate(
          50,
          (index) => Budget(
            id: '',
            userId: testUserId,
            type: BudgetType.expense,
            plannedAmountCents: 100000 + index * 1000,
            period: BudgetPeriod.monthly,
            periodStart: DateTime(2024, 1, 1).add(Duration(days: index * 31)),
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        // Should handle large batches efficiently
        await repository.batchCreateBudgets(largeBatch);
      });
    });

    group('Edge Cases and Boundary Conditions', () {
      test('should handle budgets with zero planned amount', () async {
        final zeroBudget = Budget(
          id: '',
          userId: testUserId,
          type: BudgetType.expense,
          plannedAmountCents: 0, // Zero amount
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2024, 9, 1),
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Should throw validation error (zero amount fails validation)
        expect(
          () => repository.batchCreateBudgets([zeroBudget]),
          throwsA(isA<BudgetException>()),
        );
      });

      test('should handle leap year date calculations', () async {
        // Test leap year February
        final leapYearBudget = Budget(
          id: '',
          userId: testUserId,
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2024, 2, 1), // 2024 is a leap year
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await repository.batchCreateBudgets([leapYearBudget]);

        final budgets = await repository.getBudgetsByPeriod(
          DateTime(2024, 2, 15),
          BudgetPeriod.monthly,
        );
        expect(budgets, isA<List<Budget>>());
      });

      test('should handle year-end boundary conditions', () async {
        // Test December to January transition
        final yearEndBudget = Budget(
          id: '',
          userId: testUserId,
          type: BudgetType.expense,
          plannedAmountCents: 1200000, // $12,000 yearly budget
          period: BudgetPeriod.yearly,
          periodStart: DateTime(2024, 1, 1),
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await repository.batchCreateBudgets([yearEndBudget]);

        final budgets = await repository.getBudgetsByPeriod(
          DateTime(2024, 12, 31),
          BudgetPeriod.yearly,
        );
        expect(budgets, isA<List<Budget>>());
      });
    });

    group('Concurrent Operation Error Handling', () {
      test('should handle concurrent budget creation attempts', () async {
        final budget1 = Budget(
          id: '',
          userId: testUserId,
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2025, 1, 1),
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final budget2 = Budget(
          id: '',
          userId: testUserId,
          type: BudgetType.expense,
          plannedAmountCents: 110000,
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2025, 2, 1),
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Execute concurrent operations
        final futures = [
          repository.batchCreateBudgets([budget1]),
          repository.batchCreateBudgets([budget2]),
        ];

        // Both should succeed as they don't overlap
        await Future.wait(futures);

        final allBudgets = await repository.getAllBudgets();
        expect(allBudgets.length, equals(2));
      });
    });

    group('Performance Edge Cases', () {
      test(
        'should handle querying with large number of existing budgets',
        () async {
          // Create many existing budgets
          final manyBudgets = List.generate(
            100,
            (index) => Budget(
              id: 'performance-budget-$index',
              userId: testUserId,
              type: BudgetType.expense,
              plannedAmountCents: 100000 + index * 1000,
              period: BudgetPeriod.monthly,
              periodStart: DateTime(2020, 1, 1).add(Duration(days: index * 31)),
              isActive: index.isEven, // Mix of active and inactive
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          );

          // Add them to Firestore
          final batch = fakeFirestore.batch();
          for (final budget in manyBudgets) {
            final docRef = fakeFirestore
                .collection('users')
                .doc(testUserId)
                .collection('budgets')
                .doc(budget.id);
            batch.set(docRef, budget.toJson());
          }
          await batch.commit();

          // Query should handle large dataset efficiently
          final allBudgets = await repository.getAllBudgets();
          expect(allBudgets.length, equals(100));

          final activeBudgets = await repository.getBudgetsByPeriod(
            DateTime(2020, 6, 1),
            BudgetPeriod.monthly,
          );
          expect(activeBudgets, isA<List<Budget>>());
        },
      );
    });
  });
}
