import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/repositories/implementations/category_repository_impl.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../helpers/firebase_test_setup.dart';

void main() {
  group('CategoryRepositoryImpl Tests', () {
    late FirebaseTestSetup testSetup;
    late FirestoreService firestoreService;
    late CategoryRepositoryImpl categoryRepository;
    late String testUserId;

    setUp(() async {
      // Create Firebase test setup with authenticated user
      testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
        uid: 'test-user-1',
        email: '<EMAIL>',
      );

      testUserId = 'test-user-1';
      firestoreService = FirestoreService(testSetup.firestore);
      categoryRepository = CategoryRepositoryImpl(
        firestoreService,
        firebaseAuth: testSetup.auth,
      );
    });

    tearDown(() async {
      await testSetup.dispose();
    });

    group('Basic CRUD Operations', () {
      test('create should store category in Firestore', () async {
        final category = Category.create(
          userId: testUserId,
          name: 'Test Category',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );

        final categoryId = await categoryRepository.create(category);

        expect(categoryId, isNotEmpty);

        // Verify it was stored in Firestore
        final doc = await firestoreService
            .collection('users')
            .doc(testUserId)
            .collection('categories')
            .doc(categoryId)
            .get();

        expect(doc.exists, isTrue);
        final data = doc.data()!;
        expect(data['name'], equals('Test Category'));
        expect(data['type'], equals('expense'));
        expect(data['color'], equals('#FF5722'));
        expect(data['icon'], equals('shopping_cart'));
      });

      test('getCategoryById should return category when it exists', () async {
        final category = Category.create(
          userId: testUserId,
          name: 'Test Category',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );

        final categoryId = await categoryRepository.create(category);
        final retrievedCategory = await categoryRepository.getCategoryById(
          testUserId,
          categoryId,
        );

        expect(retrievedCategory, isNotNull);
        expect(retrievedCategory!.id, equals(categoryId));
        expect(retrievedCategory.name, equals('Test Category'));
        expect(retrievedCategory.type, equals(CategoryType.expense));
        expect(retrievedCategory.color, equals('#FF5722'));
        expect(retrievedCategory.icon, equals('shopping_cart'));
      });

      test(
        'getCategoryById should return null when category does not exist',
        () async {
          final retrievedCategory = await categoryRepository.getCategoryById(
            testUserId,
            'non-existent-id',
          );
          expect(retrievedCategory, isNull);
        },
      );

      test('updateCategory should modify existing category', () async {
        final category = Category.create(
          userId: testUserId,
          name: 'Original Category',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );

        final categoryId = await categoryRepository.create(category);

        final updatedCategory = category.copyWith(
          id: categoryId,
          name: 'Updated Category',
          color: '#2196F3',
          icon: 'restaurant',
        );

        await categoryRepository.updateCategory(categoryId, updatedCategory);

        final retrievedCategory = await categoryRepository.getCategoryById(
          testUserId,
          categoryId,
        );

        expect(retrievedCategory, isNotNull);
        expect(retrievedCategory!.name, equals('Updated Category'));
        expect(retrievedCategory.color, equals('#2196F3'));
        expect(retrievedCategory.icon, equals('restaurant'));
      });

      test(
        'deleteCategoryWithConstraints should remove category from Firestore',
        () async {
          final category = Category.create(
            userId: testUserId,
            name: 'Category to Delete',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final categoryId = await categoryRepository.create(category);
          await categoryRepository.deleteCategoryWithConstraints(
            testUserId,
            categoryId,
          );

          final retrievedCategory = await categoryRepository.getCategoryById(
            testUserId,
            categoryId,
          );
          expect(retrievedCategory, isNull);
        },
      );

      test(
        'deactivateCategoryWithUserId should set isActive to false',
        () async {
          final category = Category.create(
            userId: testUserId,
            name: 'Category to Deactivate',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final categoryId = await categoryRepository.create(category);
          await categoryRepository.deactivateCategoryWithUserId(
            testUserId,
            categoryId,
          );

          final retrievedCategory = await categoryRepository.getCategoryById(
            testUserId,
            categoryId,
          );

          expect(retrievedCategory, isNotNull);
          expect(retrievedCategory!.isActive, isFalse);
        },
      );

      test('reactivateCategory should set isActive to true', () async {
        final category = Category.create(
          userId: testUserId,
          name: 'Category to Reactivate',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );

        final categoryId = await categoryRepository.create(category);
        await categoryRepository.deactivateCategoryWithUserId(
          testUserId,
          categoryId,
        );
        await categoryRepository.reactivateCategory(categoryId);

        final retrievedCategory = await categoryRepository.getCategoryById(
          testUserId,
          categoryId,
        );

        expect(retrievedCategory, isNotNull);
        expect(retrievedCategory!.isActive, isTrue);
      });
    });

    group('Category Listing Operations', () {
      test('getCategoriesByUserId should return all user categories', () async {
        final category1 = Category.create(
          userId: testUserId,
          name: 'Category 1',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );
        final category2 = Category.create(
          userId: testUserId,
          name: 'Category 2',
          type: CategoryType.income,
          color: '#4CAF50',
          icon: 'work',
        );

        await categoryRepository.create(category1);
        await categoryRepository.create(category2);

        final categories = await categoryRepository.getCategoriesByUserId(
          testUserId,
        );

        expect(categories, hasLength(2));
        expect(categories.any((c) => c.name == 'Category 1'), isTrue);
        expect(categories.any((c) => c.name == 'Category 2'), isTrue);
      });

      test(
        'getActiveCategoriesByUserId should return only active categories',
        () async {
          final activeCategory = Category.create(
            userId: testUserId,
            name: 'Active Category',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final inactiveCategory = Category.create(
            userId: testUserId,
            name: 'Inactive Category',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          await categoryRepository.create(activeCategory);
          final inactiveCategoryId = await categoryRepository.create(
            inactiveCategory,
          );
          await categoryRepository.deactivateCategory(inactiveCategoryId);

          final categories = await categoryRepository
              .getActiveCategoriesByUserId(testUserId);

          expect(categories, hasLength(1));
          expect(categories[0].name, equals('Active Category'));
          expect(categories[0].isActive, isTrue);
        },
      );

      test(
        'getCategoriesByType should return categories of specific type',
        () async {
          final expenseCategory = Category.create(
            userId: testUserId,
            name: 'Expense Category',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final incomeCategory = Category.create(
            userId: testUserId,
            name: 'Income Category',
            type: CategoryType.income,
            color: '#4CAF50',
            icon: 'work',
          );

          await categoryRepository.create(expenseCategory);
          await categoryRepository.create(incomeCategory);

          final expenseCategories = await categoryRepository
              .getCategoriesByType(testUserId, CategoryType.expense);
          final incomeCategories = await categoryRepository.getCategoriesByType(
            testUserId,
            CategoryType.income,
          );

          expect(expenseCategories, hasLength(1));
          expect(expenseCategories[0].name, equals('Expense Category'));
          expect(expenseCategories[0].type, equals(CategoryType.expense));

          expect(incomeCategories, hasLength(1));
          expect(incomeCategories[0].name, equals('Income Category'));
          expect(incomeCategories[0].type, equals(CategoryType.income));
        },
      );

      test(
        'getRootCategories should return categories without parent',
        () async {
          final rootCategory = Category.create(
            userId: testUserId,
            name: 'Root Category',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final subcategory = Category.create(
            userId: testUserId,
            name: 'Subcategory',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
            parentId: rootCategory.id,
          );

          await categoryRepository.create(rootCategory);
          await categoryRepository.create(subcategory);

          final rootCategories = await categoryRepository.getRootCategories(
            testUserId,
          );

          expect(rootCategories, hasLength(1));
          expect(rootCategories[0].name, equals('Root Category'));
          expect(rootCategories[0].parentId, isNull);
        },
      );

      test('getSubcategories should return child categories', () async {
        final parentCategory = Category.create(
          userId: testUserId,
          name: 'Parent Category',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );
        final subcategory1 = Category.create(
          userId: testUserId,
          name: 'Subcategory 1',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
          parentId: parentCategory.id,
        );
        final subcategory2 = Category.create(
          userId: testUserId,
          name: 'Subcategory 2',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
          parentId: parentCategory.id,
        );

        await categoryRepository.create(parentCategory);
        await categoryRepository.create(subcategory1);
        await categoryRepository.create(subcategory2);

        final subcategories = await categoryRepository.getSubcategories(
          testUserId,
          parentCategory.id,
        );

        expect(subcategories, hasLength(2));
        expect(subcategories.any((c) => c.name == 'Subcategory 1'), isTrue);
        expect(subcategories.any((c) => c.name == 'Subcategory 2'), isTrue);
        expect(
          subcategories.every((c) => c.parentId == parentCategory.id),
          isTrue,
        );
      });
    });

    group('Search and Validation Operations', () {
      test(
        'searchCategoriesByName should return matching categories',
        () async {
          final category1 = Category.create(
            userId: testUserId,
            name: 'Food & Dining',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'restaurant',
          );
          final category2 = Category.create(
            userId: testUserId,
            name: 'Transportation',
            type: CategoryType.expense,
            color: '#2196F3',
            icon: 'directions_car',
          );
          final category3 = Category.create(
            userId: testUserId,
            name: 'Fast Food',
            type: CategoryType.expense,
            color: '#FF9800',
            icon: 'fastfood',
          );

          await categoryRepository.create(category1);
          await categoryRepository.create(category2);
          await categoryRepository.create(category3);

          final searchResults = await categoryRepository.searchCategoriesByName(
            testUserId,
            'food',
          );

          expect(searchResults, hasLength(2));
          expect(searchResults.any((c) => c.name == 'Food & Dining'), isTrue);
          expect(searchResults.any((c) => c.name == 'Fast Food'), isTrue);
        },
      );

      test(
        'isCategoryNameUnique should return false for duplicate names',
        () async {
          final category = Category.create(
            userId: testUserId,
            name: 'Unique Category',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          await categoryRepository.create(category);

          final isUnique = await categoryRepository.isCategoryNameUnique(
            testUserId,
            'Unique Category',
          );

          expect(isUnique, isFalse);
        },
      );

      test('isCategoryNameUnique should return true for new names', () async {
        final isUnique = await categoryRepository.isCategoryNameUnique(
          testUserId,
          'New Unique Category',
        );

        expect(isUnique, isTrue);
      });

      test(
        'isCategoryNameUnique should exclude specified category ID',
        () async {
          final category = Category.create(
            userId: testUserId,
            name: 'Test Category',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final categoryId = await categoryRepository.create(category);

          final isUnique = await categoryRepository.isCategoryNameUnique(
            testUserId,
            'Test Category',
            excludeCategoryId: categoryId,
          );

          expect(isUnique, isTrue);
        },
      );

      test('validateCategory should return true for valid category', () async {
        final category = Category.create(
          userId: testUserId,
          name: 'Valid Category',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );

        final isValid = await categoryRepository.validateCategory(category);

        expect(isValid, isTrue);
      });
    });

    group('Stream Operations', () {
      test('watchUserCategories should emit category updates', () async {
        final category = Category.create(
          userId: testUserId,
          name: 'Watched Category',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );

        final stream = categoryRepository.watchUserCategories(testUserId);

        // Listen to the stream
        final streamData = <List<Category>>[];
        final subscription = stream.listen(streamData.add);

        // Wait for initial empty state
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Create a category
        await categoryRepository.create(category);

        // Wait for stream update
        await Future<void>.delayed(const Duration(milliseconds: 100));

        await subscription.cancel();

        expect(streamData, isNotEmpty);
        expect(streamData.last, hasLength(1));
        expect(streamData.last[0].name, equals('Watched Category'));
      });

      test(
        'watchCategoryForUser should emit specific category updates',
        () async {
          final category = Category.create(
            userId: testUserId,
            name: 'Specific Category',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final categoryId = await categoryRepository.create(category);

          final stream = categoryRepository.watchCategoryForUser(
            testUserId,
            categoryId,
          );

          // Listen to the stream
          final streamData = <Category?>[];
          final subscription = stream.listen(streamData.add);

          // Wait for initial state
          await Future<void>.delayed(const Duration(milliseconds: 100));

          // Update the category
          final updatedCategory = category.copyWith(
            id: categoryId,
            name: 'Updated Category',
          );
          await categoryRepository.updateCategory(categoryId, updatedCategory);

          // Wait for stream update
          await Future<void>.delayed(const Duration(milliseconds: 100));

          await subscription.cancel();

          expect(streamData, isNotEmpty);
          expect(streamData.last, isNotNull);
          expect(streamData.last!.name, equals('Updated Category'));
        },
      );
    });

    group('Hierarchy Operations', () {
      test(
        'createSubcategory should create category with parent relationship',
        () async {
          final parentCategory = Category.create(
            userId: testUserId,
            name: 'Parent Category',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final parentCategoryId = await categoryRepository.create(
            parentCategory,
          );

          final subcategory = Category.create(
            userId: testUserId,
            name: 'Subcategory',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final subcategoryId = await categoryRepository.createSubcategory(
            subcategory,
            parentCategoryId,
          );

          expect(subcategoryId, isNotEmpty);

          final retrievedSubcategory = await categoryRepository.getCategoryById(
            testUserId,
            subcategoryId,
          );

          expect(retrievedSubcategory, isNotNull);
          expect(retrievedSubcategory!.parentId, equals(parentCategoryId));
          expect(retrievedSubcategory.name, equals('Subcategory'));
        },
      );

      test(
        'hasChildSubcategories should return true when subcategories exist',
        () async {
          final parentCategory = Category.create(
            userId: testUserId,
            name: 'Parent Category',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final subcategory = Category.create(
            userId: testUserId,
            name: 'Subcategory',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
            parentId: parentCategory.id,
          );

          await categoryRepository.create(parentCategory);
          await categoryRepository.create(subcategory);

          final hasChildren = await categoryRepository.hasChildSubcategories(
            testUserId,
            parentCategory.id,
          );

          expect(hasChildren, isTrue);
        },
      );

      test(
        'hasChildSubcategories should return false when no subcategories exist',
        () async {
          final category = Category.create(
            userId: testUserId,
            name: 'Childless Category',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          await categoryRepository.create(category);

          final hasChildren = await categoryRepository.hasChildSubcategories(
            testUserId,
            category.id,
          );

          expect(hasChildren, isFalse);
        },
      );

      test('getSubcategoryCount should return correct count', () async {
        final parentCategory = Category.create(
          userId: testUserId,
          name: 'Parent Category',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );
        final subcategory = Category.create(
          userId: testUserId,
          name: 'Subcategory',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
          parentId: parentCategory.id,
        );

        await categoryRepository.create(parentCategory);
        await categoryRepository.create(subcategory);

        final count = await categoryRepository.getSubcategoryCount(
          testUserId,
          parentCategory.id,
        );

        expect(count, equals(1));
      });
    });

    group('Statistics and Summary Operations', () {
      test('getUserCategoryCount should return correct count', () async {
        final category1 = Category.create(
          userId: testUserId,
          name: 'Category 1',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );
        final category2 = Category.create(
          userId: testUserId,
          name: 'Category 2',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );

        await categoryRepository.create(category1);
        await categoryRepository.create(category2);

        final count = await categoryRepository.getUserCategoryCount(testUserId);

        expect(count, equals(2));
      });

      test('getCategoryStats should return category statistics', () async {
        final category = Category.create(
          userId: testUserId,
          name: 'Stats Category',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );

        await categoryRepository.create(category);

        final stats = await categoryRepository.getCategoryStats(category.id);

        expect(stats, isA<Map<String, dynamic>>());
        expect(stats.containsKey('transactionCount'), isTrue);
        expect(stats.containsKey('totalAmount'), isTrue);
      });

      test('getUserCategorySummary should return user summary', () async {
        final category = Category.create(
          userId: testUserId,
          name: 'Summary Category',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );

        await categoryRepository.create(category);

        final summary = await categoryRepository.getUserCategorySummary(
          testUserId,
        );

        expect(summary, isA<Map<String, dynamic>>());
        expect(summary.containsKey('totalCategories'), isTrue);
        expect(summary.containsKey('activeCategories'), isTrue);
      });
    });

    group('UnimplementedError Methods Tests', () {
      test('deleteCategory should throw UnimplementedError', () async {
        expect(
          () => categoryRepository.deleteCategory('test-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('moveCategoryToParent should throw UnimplementedError', () async {
        expect(
          () => categoryRepository.moveCategoryToParent('test-id', 'parent-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('updateCategorySortOrder should throw UnimplementedError', () async {
        expect(
          () => categoryRepository.updateCategorySortOrder('test-id', 1),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test(
        'hasDependentTransactions should throw UnimplementedError',
        () async {
          expect(
            () => categoryRepository.hasDependentTransactions('test-id'),
            throwsA(isA<UnimplementedError>()),
          );
        },
      );

      test('getCategoryAncestors should throw UnimplementedError', () async {
        expect(
          () => categoryRepository.getCategoryAncestors('test-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('getCategoryDescendants should throw UnimplementedError', () async {
        expect(
          () => categoryRepository.getCategoryDescendants('test-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('deleteSubcategory should throw UnimplementedError', () async {
        expect(
          () => categoryRepository.deleteSubcategory('sub-id', 'parent-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('watchCategory should throw UnimplementedError', () async {
        expect(
          () => categoryRepository.watchCategory('test-id'),
          throwsA(isA<UnimplementedError>()),
        );
      });
    });

    group('Additional Methods Tests', () {
      test(
        'getActiveCategories should return active categories for current user',
        () async {
          final category = Category.create(
            userId: testUserId,
            name: 'Active Category',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          await categoryRepository.create(category);

          final activeCategories = await categoryRepository
              .getActiveCategories();

          expect(activeCategories, isA<List<Category>>());
          expect(activeCategories.length, greaterThanOrEqualTo(1));
          expect(activeCategories.first.name, equals('Active Category'));
        },
      );

      test(
        'createCategory should create category with current user ID',
        () async {
          final category = Category.create(
            userId: 'different-user', // This should be overridden
            name: 'Test Category',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final categoryId = await categoryRepository.createCategory(category);

          expect(categoryId, isNotEmpty);

          // Verify it was created with the current user ID
          final createdCategory = await categoryRepository.getCategoryById(
            testUserId,
            categoryId,
          );
          expect(createdCategory, isNotNull);
          expect(createdCategory!.userId, equals(testUserId));
        },
      );

      test('updateCategory should update category with timestamp', () async {
        final category = Category.create(
          userId: testUserId,
          name: 'Original Name',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );

        final categoryId = await categoryRepository.create(category);

        final updatedCategory = category.copyWith(name: 'Updated Name');

        await categoryRepository.updateCategory(categoryId, updatedCategory);

        final retrieved = await categoryRepository.getCategoryById(
          testUserId,
          categoryId,
        );
        expect(retrieved!.name, equals('Updated Name'));
      });

      test(
        'deactivateCategory should deactivate category for current user',
        () async {
          final category = Category.create(
            userId: testUserId,
            name: 'To Deactivate',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final categoryId = await categoryRepository.create(category);

          await categoryRepository.deactivateCategory(categoryId);

          final deactivated = await categoryRepository.getCategoryById(
            testUserId,
            categoryId,
          );
          expect(deactivated!.isActive, isFalse);
        },
      );

      test('reactivateCategory should reactivate category', () async {
        final category = Category.create(
          userId: testUserId,
          name: 'To Reactivate',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );

        final categoryId = await categoryRepository.create(category);

        // First deactivate it
        await categoryRepository.deactivateCategory(categoryId);

        // Then reactivate it
        await categoryRepository.reactivateCategory(categoryId);

        final reactivated = await categoryRepository.getCategoryById(
          testUserId,
          categoryId,
        );
        expect(reactivated!.isActive, isTrue);
      });

      test(
        'reactivateCategory should throw exception for non-existent category',
        () async {
          expect(
            () => categoryRepository.reactivateCategory('non-existent-id'),
            throwsA(isA<Exception>()),
          );
        },
      );

      test('validateCategory should validate category correctly', () async {
        final validCategory = Category.create(
          userId: testUserId,
          name: 'Valid Category',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );

        final isValid = await categoryRepository.validateCategory(
          validCategory,
        );
        expect(isValid, isTrue);
      });

      test('validateCategory should reject invalid categories', () async {
        final invalidCategory = Category.create(
          userId: '',
          name: '',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );

        final isValid = await categoryRepository.validateCategory(
          invalidCategory,
        );
        expect(isValid, isFalse);
      });

      test(
        'isCategoryNameUnique should return true for unique names',
        () async {
          final isUnique = await categoryRepository.isCategoryNameUnique(
            testUserId,
            'Unique Category Name',
          );

          expect(isUnique, isTrue);
        },
      );

      test(
        'isCategoryNameUnique should return false for duplicate names',
        () async {
          final category = Category.create(
            userId: testUserId,
            name: 'Duplicate Name',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          await categoryRepository.create(category);

          final isUnique = await categoryRepository.isCategoryNameUnique(
            testUserId,
            'Duplicate Name',
          );

          expect(isUnique, isFalse);
        },
      );
    });

    group('Advanced Subcategory Operations', () {
      test(
        'createSubcategory should throw exception when parent is inactive',
        () async {
          final parentCategory = Category.create(
            userId: testUserId,
            name: 'Inactive Parent',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final parentCategoryId = await categoryRepository.create(
            parentCategory,
          );

          // Deactivate the parent
          await categoryRepository.deactivateCategoryWithUserId(
            testUserId,
            parentCategoryId,
          );

          final subcategory = Category.create(
            userId: testUserId,
            name: 'Subcategory',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          expect(
            () => categoryRepository.createSubcategory(
              subcategory,
              parentCategoryId,
            ),
            throwsA(
              predicate(
                (e) =>
                    e is Exception &&
                    e.toString().contains(
                      'Parent category not found or inactive',
                    ),
              ),
            ),
          );
        },
      );

      test(
        'createSubcategory should throw exception when hierarchy depth exceeds maximum',
        () async {
          // Create parent category (depth 0)
          final rootCategory = Category.create(
            userId: testUserId,
            name: 'Root Category',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final rootId = await categoryRepository.create(rootCategory);

          // Create subcategory (depth 1)
          final level1Category = Category.create(
            userId: testUserId,
            name: 'Level 1',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final level1Id = await categoryRepository.createSubcategory(
            level1Category,
            rootId,
          );

          // Create subcategory of subcategory (depth 2)
          final level2Category = Category.create(
            userId: testUserId,
            name: 'Level 2',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final level2Id = await categoryRepository.createSubcategory(
            level2Category,
            level1Id,
          );

          // Try to create level 3 (should fail - exceeds max depth)
          final level3Category = Category.create(
            userId: testUserId,
            name: 'Level 3',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          expect(
            () =>
                categoryRepository.createSubcategory(level3Category, level2Id),
            throwsA(
              predicate(
                (e) =>
                    e is Exception &&
                    e.toString().contains(
                      'Maximum category hierarchy depth exceeded',
                    ),
              ),
            ),
          );
        },
      );

      test(
        'createSubcategory should throw exception when subcategory type does not match parent',
        () async {
          final parentCategory = Category.create(
            userId: testUserId,
            name: 'Expense Parent',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final parentCategoryId = await categoryRepository.create(
            parentCategory,
          );

          final subcategory = Category.create(
            userId: testUserId,
            name: 'Income Subcategory',
            type: CategoryType.income, // Different type from parent
            color: '#4CAF50',
            icon: 'work',
          );

          expect(
            () => categoryRepository.createSubcategory(
              subcategory,
              parentCategoryId,
            ),
            throwsA(
              predicate(
                (e) =>
                    e is Exception &&
                    e.toString().contains(
                      'Subcategory type must match parent category type',
                    ),
              ),
            ),
          );
        },
      );

      test(
        'createSubcategory should throw exception when name is not unique within parent',
        () async {
          final parentCategory = Category.create(
            userId: testUserId,
            name: 'Parent Category',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final parentCategoryId = await categoryRepository.create(
            parentCategory,
          );

          // Create first subcategory
          final subcategory1 = Category.create(
            userId: testUserId,
            name: 'Duplicate Name',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          await categoryRepository.createSubcategory(
            subcategory1,
            parentCategoryId,
          );

          // Try to create second subcategory with same name
          final subcategory2 = Category.create(
            userId: testUserId,
            name: 'Duplicate Name',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          expect(
            () => categoryRepository.createSubcategory(
              subcategory2,
              parentCategoryId,
            ),
            throwsA(
              predicate(
                (e) =>
                    e is Exception &&
                    e.toString().contains(
                      'Subcategory name must be unique within parent category',
                    ),
              ),
            ),
          );
        },
      );

      test(
        'updateSubcategory should validate new parent when parent is changing',
        () async {
          final parentCategory = Category.create(
            userId: testUserId,
            name: 'Original Parent',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final originalParentId = await categoryRepository.create(
            parentCategory,
          );

          final subcategory = Category.create(
            userId: testUserId,
            name: 'Subcategory',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final subcategoryId = await categoryRepository.createSubcategory(
            subcategory,
            originalParentId,
          );

          // Try to update with non-existent parent
          final updatedSubcategory = subcategory.copyWith(
            id: subcategoryId,
            parentId: 'non-existent-parent-id',
          );

          expect(
            () => categoryRepository.updateSubcategory(
              subcategoryId,
              updatedSubcategory,
            ),
            throwsA(
              predicate(
                (e) =>
                    e is Exception &&
                    e.toString().contains(
                      'New parent category not found or inactive',
                    ),
              ),
            ),
          );
        },
      );

      test(
        'updateSubcategory should throw exception when type does not match new parent',
        () async {
          final expenseParent = Category.create(
            userId: testUserId,
            name: 'Expense Parent',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final incomeParent = Category.create(
            userId: testUserId,
            name: 'Income Parent',
            type: CategoryType.income,
            color: '#4CAF50',
            icon: 'work',
          );

          final expenseParentId = await categoryRepository.create(
            expenseParent,
          );
          final incomeParentId = await categoryRepository.create(incomeParent);

          final subcategory = Category.create(
            userId: testUserId,
            name: 'Subcategory',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final subcategoryId = await categoryRepository.createSubcategory(
            subcategory,
            expenseParentId,
          );

          // Try to update with income parent (type mismatch)
          final updatedSubcategory = subcategory.copyWith(
            id: subcategoryId,
            parentId: incomeParentId,
            type:
                CategoryType.expense, // Still expense, but new parent is income
          );

          expect(
            () => categoryRepository.updateSubcategory(
              subcategoryId,
              updatedSubcategory,
            ),
            throwsA(
              predicate(
                (e) =>
                    e is Exception &&
                    e.toString().contains(
                      'Subcategory type must match parent category type',
                    ),
              ),
            ),
          );
        },
      );

      test(
        'deleteSubcategoryWithConstraints should throw exception when subcategory has children',
        () async {
          // Create parent category
          final parentCategory = Category.create(
            userId: testUserId,
            name: 'Parent Category',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final parentId = await categoryRepository.create(parentCategory);

          // Create subcategory
          final subcategory = Category.create(
            userId: testUserId,
            name: 'Middle Subcategory',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final subcategoryId = await categoryRepository.createSubcategory(
            subcategory,
            parentId,
          );

          // Create child of subcategory
          final childSubcategory = Category.create(
            userId: testUserId,
            name: 'Child Subcategory',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          await categoryRepository.createSubcategory(
            childSubcategory,
            subcategoryId,
          );

          // Try to delete subcategory with children
          expect(
            () => categoryRepository.deleteSubcategoryWithConstraints(
              testUserId,
              subcategoryId,
              parentId,
            ),
            throwsA(
              predicate(
                (e) =>
                    e is Exception &&
                    e.toString().contains(
                      'Cannot delete subcategory: it has child subcategories',
                    ),
              ),
            ),
          );
        },
      );

      test(
        'moveSubcategoryToParent should validate all constraints properly',
        () async {
          final parentCategory1 = Category.create(
            userId: testUserId,
            name: 'Parent 1',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final parentCategory2 = Category.create(
            userId: testUserId,
            name: 'Parent 2',
            type: CategoryType.income, // Different type
            color: '#4CAF50',
            icon: 'work',
          );

          final parent1Id = await categoryRepository.create(parentCategory1);
          final parent2Id = await categoryRepository.create(parentCategory2);

          final subcategory = Category.create(
            userId: testUserId,
            name: 'Subcategory',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final subcategoryId = await categoryRepository.createSubcategory(
            subcategory,
            parent1Id,
          );

          // Try to move to parent with different type
          expect(
            () => categoryRepository.moveSubcategoryToParent(
              subcategoryId,
              parent1Id,
              parent2Id,
            ),
            throwsA(
              predicate(
                (e) =>
                    e is Exception &&
                    e.toString().contains(
                      'Subcategory type must match new parent category type',
                    ),
              ),
            ),
          );
        },
      );
    });

    group('Hierarchy Validation and Depth Management', () {
      test(
        'validateSubcategoryHierarchy should detect circular references',
        () async {
          final category1 = Category.create(
            userId: testUserId,
            name: 'Category 1',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final category2 = Category.create(
            userId: testUserId,
            name: 'Category 2',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final category1Id = await categoryRepository.create(category1);
          final category2Id = await categoryRepository.create(category2);

          // Create hierarchy: category1 -> category2
          await categoryRepository.updateCategory(
            category2Id,
            category2.copyWith(parentId: category1Id),
          );

          // Test circular reference: would make category2 -> category1 (circular)
          final isValid = await categoryRepository.validateSubcategoryHierarchy(
            category2Id, // Try to make category2 the parent
            category1Id, // of category1 (which is already parent of category2)
          );

          expect(isValid, isFalse);
        },
      );

      test(
        'getCategoryDepth should calculate depth correctly for nested categories',
        () async {
          // Create root category (depth 0)
          final rootCategory = Category.create(
            userId: testUserId,
            name: 'Root',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final rootId = await categoryRepository.create(rootCategory);

          // Create level 1 subcategory (depth 1)
          final level1Category = Category.create(
            userId: testUserId,
            name: 'Level 1',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final level1Id = await categoryRepository.createSubcategory(
            level1Category,
            rootId,
          );

          // Create level 2 subcategory (depth 2)
          final level2Category = Category.create(
            userId: testUserId,
            name: 'Level 2',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final level2Id = await categoryRepository.createSubcategory(
            level2Category,
            level1Id,
          );

          final rootDepth = await categoryRepository.getCategoryDepth(
            testUserId,
            rootId,
          );
          final level1Depth = await categoryRepository.getCategoryDepth(
            testUserId,
            level1Id,
          );
          final level2Depth = await categoryRepository.getCategoryDepth(
            testUserId,
            level2Id,
          );

          expect(rootDepth, equals(0));
          expect(level1Depth, equals(1));
          expect(level2Depth, equals(2));
        },
      );

      test(
        'getCategoryDepth should return -1 for non-existent categories',
        () async {
          final depth = await categoryRepository.getCategoryDepth(
            testUserId,
            'non-existent-id',
          );

          expect(depth, equals(-1));
        },
      );

      test(
        'canHaveSubcategories should return false at maximum depth',
        () async {
          // Create deep hierarchy
          final rootCategory = Category.create(
            userId: testUserId,
            name: 'Root',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final rootId = await categoryRepository.create(rootCategory);

          final level1Category = Category.create(
            userId: testUserId,
            name: 'Level 1',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final level1Id = await categoryRepository.createSubcategory(
            level1Category,
            rootId,
          );

          final level2Category = Category.create(
            userId: testUserId,
            name: 'Level 2',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final level2Id = await categoryRepository.createSubcategory(
            level2Category,
            level1Id,
          );

          final rootCanHaveChildren = await categoryRepository
              .canHaveSubcategories(testUserId, rootId);
          final level1CanHaveChildren = await categoryRepository
              .canHaveSubcategories(testUserId, level1Id);
          final level2CanHaveChildren = await categoryRepository
              .canHaveSubcategories(testUserId, level2Id);

          expect(rootCanHaveChildren, isTrue); // Depth 0, can have children
          expect(level1CanHaveChildren, isTrue); // Depth 1, can have children
          expect(
            level2CanHaveChildren,
            isFalse,
          ); // Depth 2, cannot have children
        },
      );

      test(
        'isSubcategoryNameUnique should handle excludeSubcategoryId parameter',
        () async {
          final parentCategory = Category.create(
            userId: testUserId,
            name: 'Parent',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final parentId = await categoryRepository.create(parentCategory);

          final subcategory = Category.create(
            userId: testUserId,
            name: 'Test Subcategory',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final subcategoryId = await categoryRepository.createSubcategory(
            subcategory,
            parentId,
          );

          // Should return false when checking without exclusion
          final isUniqueWithoutExclusion = await categoryRepository
              .isSubcategoryNameUnique(
                testUserId,
                parentId,
                'Test Subcategory',
              );

          // Should return true when excluding the existing subcategory
          final isUniqueWithExclusion = await categoryRepository
              .isSubcategoryNameUnique(
                testUserId,
                parentId,
                'Test Subcategory',
                excludeSubcategoryId: subcategoryId,
              );

          expect(isUniqueWithoutExclusion, isFalse);
          expect(isUniqueWithExclusion, isTrue);
        },
      );
    });

    group('Advanced Stream Operations', () {
      test(
        'watchSubcategories should emit updates for subcategory changes',
        () async {
          final parentCategory = Category.create(
            userId: testUserId,
            name: 'Parent',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final parentId = await categoryRepository.create(parentCategory);

          final stream = categoryRepository.watchSubcategories(
            testUserId,
            parentId,
          );

          final streamData = <List<Category>>[];
          final subscription = stream.listen(streamData.add);

          // Wait for initial empty state
          await Future<void>.delayed(const Duration(milliseconds: 100));

          // Create subcategory
          final subcategory = Category.create(
            userId: testUserId,
            name: 'New Subcategory',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          await categoryRepository.createSubcategory(subcategory, parentId);

          // Wait for stream update
          await Future<void>.delayed(const Duration(milliseconds: 100));

          await subscription.cancel();

          expect(streamData, isNotEmpty);
          expect(streamData.last, hasLength(1));
          expect(streamData.last[0].name, equals('New Subcategory'));
          expect(streamData.last[0].parentId, equals(parentId));
        },
      );
    });

    group('Complex Constraint Checking', () {
      test(
        'hasDependentTransactionsForUser should handle repository instantiation',
        () async {
          final category = Category.create(
            userId: testUserId,
            name: 'Category with Transactions',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final categoryId = await categoryRepository.create(category);

          // This method creates temporary repository instances internally
          // We're testing that it doesn't throw an exception during instantiation
          final hasDependents = await categoryRepository
              .hasDependentTransactionsForUser(testUserId, categoryId);

          // Should return false since we haven't created any transactions
          expect(hasDependents, isFalse);
        },
      );
    });

    group('Error Handling and Edge Cases', () {
      test(
        'deleteCategoryWithConstraints should throw exception for non-existent category',
        () async {
          expect(
            () => categoryRepository.deleteCategoryWithConstraints(
              testUserId,
              'non-existent-id',
            ),
            throwsA(
              predicate(
                (e) =>
                    e is Exception &&
                    e.toString().contains('Category not found'),
              ),
            ),
          );
        },
      );

      test(
        'deactivateCategoryWithUserId should throw exception for non-existent category',
        () async {
          expect(
            () => categoryRepository.deactivateCategoryWithUserId(
              testUserId,
              'non-existent-id',
            ),
            throwsA(
              predicate(
                (e) =>
                    e is Exception &&
                    e.toString().contains('Category not found'),
              ),
            ),
          );
        },
      );

      test(
        'updateSubcategory should throw exception for non-existent subcategory',
        () async {
          final subcategory = Category.create(
            userId: testUserId,
            name: 'Non-existent',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          expect(
            () => categoryRepository.updateSubcategory(
              'non-existent-id',
              subcategory,
            ),
            throwsA(
              predicate(
                (e) =>
                    e is Exception &&
                    e.toString().contains('Subcategory not found'),
              ),
            ),
          );
        },
      );

      test(
        'deleteSubcategoryWithConstraints should throw exception when subcategory does not belong to parent',
        () async {
          final parentCategory1 = Category.create(
            userId: testUserId,
            name: 'Parent 1',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final parentCategory2 = Category.create(
            userId: testUserId,
            name: 'Parent 2',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );

          final parent1Id = await categoryRepository.create(parentCategory1);
          final parent2Id = await categoryRepository.create(parentCategory2);

          final subcategory = Category.create(
            userId: testUserId,
            name: 'Subcategory',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final subcategoryId = await categoryRepository.createSubcategory(
            subcategory,
            parent1Id,
          );

          // Try to delete with wrong parent ID
          expect(
            () => categoryRepository.deleteSubcategoryWithConstraints(
              testUserId,
              subcategoryId,
              parent2Id, // Wrong parent
            ),
            throwsA(
              predicate(
                (e) =>
                    e is Exception &&
                    e.toString().contains(
                      'does not belong to specified parent',
                    ),
              ),
            ),
          );
        },
      );

      test(
        'canHaveSubcategories should return false for non-existent category',
        () async {
          final canHave = await categoryRepository.canHaveSubcategories(
            testUserId,
            'non-existent-id',
          );

          expect(canHave, isFalse);
        },
      );

      test(
        'canHaveSubcategories should return false for inactive category',
        () async {
          final category = Category.create(
            userId: testUserId,
            name: 'Inactive Category',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final categoryId = await categoryRepository.create(category);

          // Deactivate the category
          await categoryRepository.deactivateCategoryWithUserId(
            testUserId,
            categoryId,
          );

          final canHave = await categoryRepository.canHaveSubcategories(
            testUserId,
            categoryId,
          );

          expect(canHave, isFalse);
        },
      );
    });

    group('Additional Coverage Tests', () {
      test('create should handle empty ID by generating new ID', () async {
        final category = Category.create(
          userId: testUserId,
          name: 'Test Category',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        ).copyWith(id: ''); // Empty ID to test generation logic

        final categoryId = await categoryRepository.create(category);

        expect(categoryId, isNotEmpty);
        expect(categoryId, isNot(equals('')));

        // Verify it was stored with the generated ID
        final retrievedCategory = await categoryRepository.getCategoryById(
          testUserId,
          categoryId,
        );

        expect(retrievedCategory, isNotNull);
        expect(retrievedCategory!.id, equals(categoryId));
        expect(retrievedCategory.name, equals('Test Category'));
        expect(retrievedCategory.createdAt, isNotNull);
        expect(retrievedCategory.updatedAt, isNotNull);
      });

      test('create should preserve existing ID when provided', () async {
        const existingId = 'predefined-id-123';
        final category = Category.create(
          userId: testUserId,
          name: 'Test Category',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        ).copyWith(id: existingId);

        final categoryId = await categoryRepository.create(category);

        expect(categoryId, equals(existingId));

        // Verify it was stored with the provided ID
        final retrievedCategory = await categoryRepository.getCategoryById(
          testUserId,
          categoryId,
        );

        expect(retrievedCategory, isNotNull);
        expect(retrievedCategory!.id, equals(existingId));
      });

      test('getDeletableCategories should return active categories', () async {
        // Create some test categories
        final category1 = Category.create(
          userId: testUserId,
          name: 'Active Category 1',
          type: CategoryType.expense,
          color: '#FF5722',
          icon: 'shopping_cart',
        );
        final category2 = Category.create(
          userId: testUserId,
          name: 'Active Category 2',
          type: CategoryType.income,
          color: '#4CAF50',
          icon: 'attach_money',
        );

        await categoryRepository.create(category1);
        await categoryRepository.create(category2);

        final deletableCategories = await categoryRepository
            .getDeletableCategories(testUserId);

        expect(deletableCategories, isNotEmpty);
        expect(deletableCategories.length, greaterThanOrEqualTo(2));
        expect(deletableCategories.every((c) => c.isActive), isTrue);
      });

      test(
        'getCategoryTree should return tree structure for root categories',
        () async {
          // Create some root categories
          final rootCategory1 = Category.create(
            userId: testUserId,
            name: 'Root Category 1',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final rootCategory2 = Category.create(
            userId: testUserId,
            name: 'Root Category 2',
            type: CategoryType.income,
            color: '#4CAF50',
            icon: 'attach_money',
          );

          await categoryRepository.create(rootCategory1);
          await categoryRepository.create(rootCategory2);

          final categoryTree = await categoryRepository.getCategoryTree(
            testUserId,
          );

          expect(categoryTree, isNotEmpty);
          expect(categoryTree.length, greaterThanOrEqualTo(2));
          expect(categoryTree.every((tree) => tree.category.isRoot), isTrue);
        },
      );

      test(
        'getCategoryTree should return empty list when no root categories exist',
        () async {
          // Start with a clean slate - no categories exist yet
          final categoryTree = await categoryRepository.getCategoryTree(
            testUserId,
          );

          // Should be empty since no root categories exist
          expect(categoryTree.isEmpty, isTrue);
        },
      );

      test(
        'getCategoryTreeByType should return tree structure for specific type',
        () async {
          // Create categories of different types
          final expenseCategory = Category.create(
            userId: testUserId,
            name: 'Expense Root',
            type: CategoryType.expense,
            color: '#FF5722',
            icon: 'shopping_cart',
          );
          final incomeCategory = Category.create(
            userId: testUserId,
            name: 'Income Root',
            type: CategoryType.income,
            color: '#4CAF50',
            icon: 'attach_money',
          );

          await categoryRepository.create(expenseCategory);
          await categoryRepository.create(incomeCategory);

          final expenseTree = await categoryRepository.getCategoryTreeByType(
            testUserId,
            CategoryType.expense,
          );
          final incomeTree = await categoryRepository.getCategoryTreeByType(
            testUserId,
            CategoryType.income,
          );

          expect(expenseTree, isNotEmpty);
          expect(incomeTree, isNotEmpty);
          expect(
            expenseTree.every(
              (tree) => tree.category.type == CategoryType.expense,
            ),
            isTrue,
          );
          expect(
            incomeTree.every(
              (tree) => tree.category.type == CategoryType.income,
            ),
            isTrue,
          );
        },
      );

      test(
        'validateCategoryHierarchy should return true for valid hierarchy',
        () async {
          final result = await categoryRepository.validateCategoryHierarchy(
            'category-id-1',
            'parent-id-1',
          );

          expect(result, isTrue);
        },
      );

      test(
        'validateCategoryHierarchy should return true for null parent',
        () async {
          final result = await categoryRepository.validateCategoryHierarchy(
            'category-id-1',
            null,
          );

          expect(result, isTrue);
        },
      );
    });
  });
}
