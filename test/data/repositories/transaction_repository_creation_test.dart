import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/implementations/transaction_repository_impl.dart';
import 'package:budapp/features/budgets/services/budget_transaction_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/mock_data_factory.dart';

// Mock classes for testing
class MockBudgetTransactionService extends Mock
    implements BudgetTransactionService {}

void main() {
  setUpAll(() {
    // Use MockDataFactory to create fallback values instead of Fake classes
    registerFallbackValue(MockDataFactory.createTransaction());
  });
  group('TransactionRepository Creation Tests', () {
    late FakeFirebaseFirestore fakeFirestore;
    late FirestoreService firestoreService;
    late MockBudgetTransactionService mockBudgetTransactionService;
    late TransactionRepositoryImpl repository;
    late String testUserId;
    late String testAccountId1;
    late String testAccountId2;

    setUp(() async {
      fakeFirestore = FakeFirebaseFirestore();
      firestoreService = FirestoreService(fakeFirestore);
      mockBudgetTransactionService = MockBudgetTransactionService();
      repository = TransactionRepositoryImpl(
        firestoreService,
        mockBudgetTransactionService,
      );

      // Setup mock budget transaction service
      when(
        () => mockBudgetTransactionService.updateBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      when(
        () => mockBudgetTransactionService.revertBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async {});

      when(
        () => mockBudgetTransactionService.updateBudgetsForTransactionUpdate(
          any(),
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      testUserId = 'test-user-123';
      testAccountId1 = 'account-1';
      testAccountId2 = 'account-2';

      // Create test accounts
      final account1 = Account(
        id: testAccountId1,
        userId: testUserId,
        name: 'Test Account 1',
        type: AccountType.checking,
        classification: AccountClassification.asset,
        initialBalanceCents: 100000,
        currentBalanceCents: 100000, // $1000.00

        isActive: true,
        isPrimary: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final account2 = Account(
        id: testAccountId2,
        userId: testUserId,
        name: 'Test Account 2',
        type: AccountType.savings,
        classification: AccountClassification.asset,
        initialBalanceCents: 50000,
        currentBalanceCents: 50000, // $500.00

        isActive: true,
        isPrimary: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId1)
          .set(account1.toJson());

      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId2)
          .set(account2.toJson());
    });

    group('createIncomeTransaction', () {
      test('should create income transaction successfully', () async {
        final transactionId = await repository.createIncomeTransaction(
          userId: testUserId,
          toAccountId: testAccountId1,
          amountCents: 10000, // $100.00

          transactionDate: DateTime.now(),
          description: 'Test income',
          categoryId: 'income-category',
        );

        expect(transactionId, isNotEmpty);

        // Verify transaction was created in Firestore
        final doc = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('transactions')
            .doc(transactionId)
            .get();

        expect(doc.exists, isTrue);
        final transaction = Transaction.fromJson(doc.data()!);
        expect(transaction.type, TransactionType.income);
        expect(transaction.toAccountId, testAccountId1);
        expect(transaction.amountCents, 10000);
        expect(transaction.description, 'Test income');
      });

      test('should throw error for non-existent account', () async {
        expect(
          () => repository.createIncomeTransaction(
            userId: testUserId,
            toAccountId: 'non-existent-account',
            amountCents: 10000,

            transactionDate: DateTime.now(),
          ),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('createExpenseTransaction', () {
      test('should create expense transaction successfully', () async {
        final transactionId = await repository.createExpenseTransaction(
          userId: testUserId,
          fromAccountId: testAccountId1,
          amountCents: 5000, // $50.00

          transactionDate: DateTime.now(),
          description: 'Test expense',
          categoryId: 'expense-category',
        );

        expect(transactionId, isNotEmpty);

        // Verify transaction was created in Firestore
        final doc = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('transactions')
            .doc(transactionId)
            .get();

        expect(doc.exists, isTrue);
        final transaction = Transaction.fromJson(doc.data()!);
        expect(transaction.type, TransactionType.expense);
        expect(transaction.fromAccountId, testAccountId1);
        expect(transaction.amountCents, 5000);
        expect(transaction.description, 'Test expense');
      });

      test('should throw error for non-existent account', () async {
        expect(
          () => repository.createExpenseTransaction(
            userId: testUserId,
            fromAccountId: 'non-existent-account',
            amountCents: 5000,

            transactionDate: DateTime.now(),
          ),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('createTransferTransaction', () {
      test('should create transfer transaction successfully', () async {
        final transactionId = await repository.createTransferTransaction(
          userId: testUserId,
          fromAccountId: testAccountId1,
          toAccountId: testAccountId2,
          amountCents: 7500, // $75.00

          transactionDate: DateTime.now(),
          description: 'Test transfer',
        );

        expect(transactionId, isNotEmpty);

        // Verify transaction was created in Firestore
        final doc = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('transactions')
            .doc(transactionId)
            .get();

        expect(doc.exists, isTrue);
        final transaction = Transaction.fromJson(doc.data()!);
        expect(transaction.type, TransactionType.transfer);
        expect(transaction.fromAccountId, testAccountId1);
        expect(transaction.toAccountId, testAccountId2);
        expect(transaction.amountCents, 7500);
        expect(transaction.description, 'Test transfer');
      });

      test(
        'should throw error for same source and destination accounts',
        () async {
          expect(
            () => repository.createTransferTransaction(
              userId: testUserId,
              fromAccountId: testAccountId1,
              toAccountId: testAccountId1, // Same account
              amountCents: 7500,

              transactionDate: DateTime.now(),
            ),
            throwsA(isA<ArgumentError>()),
          );
        },
      );

      test('should throw error for non-existent source account', () async {
        expect(
          () => repository.createTransferTransaction(
            userId: testUserId,
            fromAccountId: 'non-existent-account',
            toAccountId: testAccountId2,
            amountCents: 7500,

            transactionDate: DateTime.now(),
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should throw error for non-existent destination account', () async {
        expect(
          () => repository.createTransferTransaction(
            userId: testUserId,
            fromAccountId: testAccountId1,
            toAccountId: 'non-existent-account',
            amountCents: 7500,

            transactionDate: DateTime.now(),
          ),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('Enhanced validation', () {
      test(
        'should validate transaction with account existence check',
        () async {
          final transaction = Transaction(
            id: 'test-transaction',
            userId: testUserId,
            type: TransactionType.income,
            status: TransactionStatus.completed,
            amountCents: 10000,

            toAccountId: testAccountId1,
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          final isValid = await repository.validateTransaction(transaction);
          expect(isValid, isTrue);
        },
      );

      test('should fail validation for non-existent account', () async {
        final transaction = Transaction(
          id: 'test-transaction',
          userId: testUserId,
          type: TransactionType.income,
          status: TransactionStatus.completed,
          amountCents: 10000,

          toAccountId: 'non-existent-account',
          transactionDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final isValid = await repository.validateTransaction(transaction);
        expect(isValid, isFalse);
      });
    });
  });
}
