import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/repositories/implementations/category_repository_impl.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/mock_data_factory.dart';

// Mock classes for testing
class MockFirebaseAuth extends Mock implements FirebaseAuth {}

class MockUser extends Mock implements User {}

class MockFirestoreService extends Mock implements FirestoreService {}

void main() {
  setUpAll(() {
    // Use MockDataFactory to create fallback values instead of Fake classes
    registerFallbackValue(MockDataFactory.createCategory());
  });

  group('CategoryRepository Error Handling Tests', () {
    late FakeFirebaseFirestore fakeFirestore;
    late FirestoreService firestoreService;
    late MockFirebaseAuth mockFirebaseAuth;
    late MockUser mockUser;
    late CategoryRepositoryImpl repository;
    late String testUserId;

    setUp(() async {
      fakeFirestore = FakeFirebaseFirestore();
      firestoreService = FirestoreService(fakeFirestore);
      mockFirebaseAuth = MockFirebaseAuth();
      mockUser = MockUser();
      testUserId = 'test-user-123';

      // Setup mock Firebase Auth
      when(() => mockUser.uid).thenReturn(testUserId);
      when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);

      repository = CategoryRepositoryImpl(
        firestoreService,
        firebaseAuth: mockFirebaseAuth,
      );
    });

    group('Authentication Error Handling', () {
      test(
        'should throw exception when user not authenticated for getActiveCategories',
        () async {
          // Arrange - No authenticated user
          when(() => mockFirebaseAuth.currentUser).thenReturn(null);

          // Act & Assert
          expect(
            () => repository.getActiveCategories(),
            throwsA(isA<Exception>()),
          );
        },
      );

      test(
        'should throw exception when user not authenticated for createCategory',
        () async {
          // Arrange - No authenticated user
          when(() => mockFirebaseAuth.currentUser).thenReturn(null);

          final category = MockDataFactory.createCategory(
            id: '',
            userId: '',
            name: 'Test Category',
            type: CategoryType.expense,
            icon: 'test',
            color: 'blue',
            schemaVersion: 1,
          );

          // Act & Assert
          expect(
            () => repository.createCategory(category),
            throwsA(isA<Exception>()),
          );
        },
      );

      test(
        'should throw exception when user not authenticated for deactivateCategory',
        () async {
          // Arrange - No authenticated user
          when(() => mockFirebaseAuth.currentUser).thenReturn(null);

          // Act & Assert
          expect(
            () => repository.deactivateCategory('test-category-id'),
            throwsA(isA<Exception>()),
          );
        },
      );

      test(
        'should throw exception when user not authenticated for reactivateCategory',
        () async {
          // Arrange - No authenticated user
          when(() => mockFirebaseAuth.currentUser).thenReturn(null);

          final category = MockDataFactory.createCategory(
            id: 'test-category',
            userId: testUserId,
            name: 'Test Category',
            type: CategoryType.expense,
            icon: 'test',
            color: 'blue',
            schemaVersion: 1,
            isActive: false,
          );

          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('categories')
              .doc('test-category')
              .set(category.toJson());

          // Act & Assert
          expect(
            () => repository.reactivateCategory('test-category'),
            throwsA(isA<Exception>()),
          );
        },
      );
    });

    group('Category Creation Error Handling', () {
      test('should handle category creation with empty name', () async {
        final category = MockDataFactory.createCategory(
          id: '',
          userId: testUserId,
          name: '', // Empty name
          type: CategoryType.expense,
          icon: 'test',
          color: 'blue',
          schemaVersion: 1,
        );

        // Should create successfully (validation handled by UI layer)
        final result = await repository.createCategory(category);
        expect(result, isNotEmpty);
      });

      test('should handle category creation with null icon', () async {
        final category = Category(
          id: '',
          userId: testUserId,
          name: 'Test Category',
          type: CategoryType.expense,
          icon: null, // Null icon
          color: 'blue',
          isActive: true,
          sortOrder: 0,
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final result = await repository.createCategory(category);
        expect(result, isNotEmpty);
      });

      test('should handle category creation with very long name', () async {
        final longName = 'A' * 1000; // Very long name
        final category = MockDataFactory.createCategory(
          id: '',
          userId: testUserId,
          name: longName,
          type: CategoryType.expense,
          icon: 'test',
          color: 'blue',
          schemaVersion: 1,
        );

        final result = await repository.createCategory(category);
        expect(result, isNotEmpty);
      });
    });

    group('Category Update Error Handling', () {
      test('should handle updating non-existent category', () async {
        final category = MockDataFactory.createCategory(
          id: 'non-existent-category',
          userId: testUserId,
          name: 'Updated Category',
          type: CategoryType.expense,
          icon: 'test',
          color: 'blue',
          schemaVersion: 1,
        );

        // FakeFirestore throws error for non-existent documents on update
        expect(
          () => repository.updateCategory('non-existent-category', category),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle updating category with invalid userId', () async {
        // First create the category in Firestore
        final existingCategory = MockDataFactory.createCategory(
          id: 'test-category',
          userId: 'different-user',
          name: 'Original Category',
          type: CategoryType.expense,
          icon: 'test',
          color: 'red',
          schemaVersion: 1,
        );

        await fakeFirestore
            .collection('users')
            .doc('different-user')
            .collection('categories')
            .doc('test-category')
            .set(existingCategory.toJson());

        final category = MockDataFactory.createCategory(
          id: 'test-category',
          userId: 'different-user',
          name: 'Updated Category',
          type: CategoryType.expense,
          icon: 'test',
          color: 'blue',
          schemaVersion: 1,
        );

        // Should update the document path based on category.userId
        await repository.updateCategory('test-category', category);
        // No exception expected
      });
    });

    group('Category Deletion Error Handling', () {
      test('should throw UnimplementedError for deleteCategory', () async {
        expect(
          () => repository.deleteCategory('test-category'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test(
        'should throw exception when deleting non-existent category',
        () async {
          expect(
            () => repository.deleteCategoryWithConstraints(
              testUserId,
              'non-existent-category',
            ),
            throwsA(isA<Exception>()),
          );
        },
      );

      test(
        'should throw exception when category has dependent transactions',
        () async {
          // Create category
          final category = MockDataFactory.createCategory(
            id: 'category-with-transactions',
            userId: testUserId,
            name: 'Category with Transactions',
            type: CategoryType.expense,
            icon: 'test',
            color: 'blue',
            schemaVersion: 1,
          );

          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('categories')
              .doc('category-with-transactions')
              .set(category.toJson());

          // Mock hasDependentTransactionsForUser to return true
          // Since this is implementation detail, we'll create a transaction
          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc('test-transaction')
              .set({
                'id': 'test-transaction',
                'userId': testUserId,
                'categoryId': 'category-with-transactions',
                'type': 'expense',
                'status': 'completed',
                'amountCents': 1000,
                'fromAccountId': 'test-account',
                'transactionDate': DateTime.now().toIso8601String(),
                'createdAt': DateTime.now().toIso8601String(),
                'updatedAt': DateTime.now().toIso8601String(),
              });

          expect(
            () => repository.deleteCategoryWithConstraints(
              testUserId,
              'category-with-transactions',
            ),
            throwsA(isA<Exception>()),
          );
        },
      );

      test(
        'should throw exception when category has child subcategories',
        () async {
          // Create parent category
          final parentCategory = MockDataFactory.createCategory(
            id: 'parent-category',
            userId: testUserId,
            name: 'Parent Category',
            type: CategoryType.expense,
            icon: 'test',
            color: 'blue',
            schemaVersion: 1,
          );

          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('categories')
              .doc('parent-category')
              .set(parentCategory.toJson());

          // Create child category
          final childCategory = MockDataFactory.createCategory(
            id: 'child-category',
            userId: testUserId,
            name: 'Child Category',
            type: CategoryType.expense,
            icon: 'test',
            color: 'blue',
            schemaVersion: 1,
            parentId: 'parent-category',
          );

          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('categories')
              .doc('child-category')
              .set(childCategory.toJson());

          expect(
            () => repository.deleteCategoryWithConstraints(
              testUserId,
              'parent-category',
            ),
            throwsA(isA<Exception>()),
          );
        },
      );
    });

    group('Category Deactivation Error Handling', () {
      test(
        'should throw exception when deactivating non-existent category',
        () async {
          expect(
            () => repository.deactivateCategoryWithUserId(
              testUserId,
              'non-existent-category',
            ),
            throwsA(isA<Exception>()),
          );
        },
      );

      test('should handle deactivating already inactive category', () async {
        final category = MockDataFactory.createCategory(
          id: 'inactive-category',
          userId: testUserId,
          name: 'Inactive Category',
          type: CategoryType.expense,
          icon: 'test',
          color: 'blue',
          schemaVersion: 1,
          isActive: false,
        );

        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('categories')
            .doc('inactive-category')
            .set(category.toJson());

        // Should succeed without error
        await repository.deactivateCategoryWithUserId(
          testUserId,
          'inactive-category',
        );
      });
    });

    group('Category Reactivation Error Handling', () {
      test(
        'should throw exception when reactivating non-existent category',
        () async {
          expect(
            () => repository.reactivateCategory('non-existent-category'),
            throwsA(isA<Exception>()),
          );
        },
      );

      test('should handle reactivating already active category', () async {
        final category = MockDataFactory.createCategory(
          id: 'active-category',
          userId: testUserId,
          name: 'Active Category',
          type: CategoryType.expense,
          icon: 'test',
          color: 'blue',
          schemaVersion: 1,
          isActive: true,
        );

        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('categories')
            .doc('active-category')
            .set(category.toJson());

        // Should succeed without error
        await repository.reactivateCategory('active-category');
      });
    });

    group('Category Retrieval Error Handling', () {
      test('should return null for non-existent category', () async {
        final result = await repository.getCategoryById(
          testUserId,
          'non-existent-category',
        );
        expect(result, isNull);
      });

      test('should handle empty categories list', () async {
        final result = await repository.getCategoriesByUserId(testUserId);
        expect(result, isEmpty);
      });

      test('should handle malformed category data', () async {
        // Add malformed data to Firestore
        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('categories')
            .doc('malformed-category')
            .set({
              'id': 'malformed-category',
              'userId': testUserId,
              'name': 'Malformed Category',
              'type': 'invalid_type', // Invalid enum value
              'isActive': 'not_a_boolean', // Wrong data type
              'schemaVersion': 'not_a_number', // Wrong data type
              'createdAt': 'invalid_date', // Invalid date format
            });

        // Should handle gracefully by returning error or null
        expect(
          () => repository.getCategoryById(testUserId, 'malformed-category'),
          throwsA(anything),
        );
      });

      test('should handle categories with missing required fields', () async {
        // Add incomplete data to Firestore
        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('categories')
            .doc('incomplete-category')
            .set({
              'id': 'incomplete-category',
              // Missing required fields - should use defaults
            });

        // Repository should handle gracefully by using default values
        final category = await repository.getCategoryById(
          testUserId,
          'incomplete-category',
        );

        expect(category, isNotNull);
        expect(category!.id, equals('incomplete-category'));
        expect(category.userId, equals('')); // Default value
        expect(category.name, equals('')); // Default value
        expect(category.type, equals(CategoryType.expense)); // Default value
        expect(category.isActive, isTrue); // Default value
      });
    });

    group('Category Search Error Handling', () {
      test('should handle search with empty query', () async {
        final result = await repository.searchCategoriesByName(testUserId, '');
        expect(result, isA<List<Category>>());
      });

      test('should handle search with null query', () async {
        // This would cause a null pointer exception in toLowerCase()
        expect(
          () => repository.searchCategoriesByName(testUserId, ''),
          returnsNormally,
        );
      });

      test('should handle search with special characters', () async {
        final result = await repository.searchCategoriesByName(
          testUserId,
          '!@#\$%^&*()[]{}|\\:";\'<>?,./`~',
        );
        expect(result, isA<List<Category>>());
      });

      test('should handle search with unicode characters', () async {
        final result = await repository.searchCategoriesByName(
          testUserId,
          '🤑💰💸📊💳💵💴💶💷🏦💎💸',
        );
        expect(result, isA<List<Category>>());
      });
    });

    group('Category Stats Error Handling', () {
      test('should return default stats for non-existent category', () async {
        final stats = await repository.getCategoryStats(
          'non-existent-category',
        );

        expect(stats['categoryId'], equals('non-existent-category'));
        expect(stats['transactionCount'], equals(0));
        expect(stats['totalAmount'], equals(0));
        expect(stats['lastUsed'], isNull);
      });

      test('should handle getUserCategorySummary with no categories', () async {
        final summary = await repository.getUserCategorySummary(testUserId);

        expect(summary['totalCategories'], equals(0));
        expect(summary['activeCategories'], equals(0));
        expect(summary['inactiveCategories'], equals(0));
        expect(summary['incomeCategories'], equals(0));
        expect(summary['expenseCategories'], equals(0));
      });

      test(
        'should handle getUserCategorySummary with mixed category types',
        () async {
          // Create test categories
          final categories = [
            MockDataFactory.createCategory(
              id: 'income-cat',
              userId: testUserId,
              name: 'Income Category',
              type: CategoryType.income,
              icon: 'income',
              color: 'green',
              schemaVersion: 1,
              isActive: true,
            ),
            MockDataFactory.createCategory(
              id: 'expense-cat',
              userId: testUserId,
              name: 'Expense Category',
              type: CategoryType.expense,
              icon: 'expense',
              color: 'red',
              schemaVersion: 1,
              isActive: true,
            ),
            MockDataFactory.createCategory(
              id: 'inactive-cat',
              userId: testUserId,
              name: 'Inactive Category',
              type: CategoryType.expense,
              icon: 'inactive',
              color: 'gray',
              schemaVersion: 1,
              isActive: false,
            ),
          ];

          for (final category in categories) {
            await fakeFirestore
                .collection('users')
                .doc(testUserId)
                .collection('categories')
                .doc(category.id)
                .set(category.toJson());
          }

          final summary = await repository.getUserCategorySummary(testUserId);

          expect(summary['totalCategories'], equals(3));
          expect(summary['activeCategories'], equals(2));
          expect(summary['inactiveCategories'], equals(1));
          expect(summary['incomeCategories'], equals(1));
          expect(summary['expenseCategories'], equals(1));
        },
      );
    });

    group('Category Queries Error Handling', () {
      test('should handle getCategoriesByType with invalid type', () async {
        // This would be caught at compile time, but testing enum handling
        final result = await repository.getCategoriesByType(
          testUserId,
          CategoryType.income,
        );
        expect(result, isA<List<Category>>());
      });

      test('should handle getRootCategories with no root categories', () async {
        final result = await repository.getRootCategories(testUserId);
        expect(result, isEmpty);
      });

      test('should handle getSubcategories with non-existent parent', () async {
        final result = await repository.getSubcategories(
          testUserId,
          'non-existent-parent',
        );
        expect(result, isEmpty);
      });

      test(
        'should handle getActiveCategoriesByUserId with all inactive categories',
        () async {
          // Create inactive categories
          final inactiveCategory = MockDataFactory.createCategory(
            id: 'inactive-only',
            userId: testUserId,
            name: 'Inactive Only',
            type: CategoryType.expense,
            icon: 'test',
            color: 'blue',
            schemaVersion: 1,
            isActive: false,
          );

          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('categories')
              .doc('inactive-only')
              .set(inactiveCategory.toJson());

          final result = await repository.getActiveCategoriesByUserId(
            testUserId,
          );
          expect(result, isEmpty);
        },
      );
    });

    group('Stream Error Handling', () {
      test('should handle watchUserCategories stream errors', () async {
        final stream = repository.watchUserCategories(testUserId);
        expect(stream, isA<Stream<List<Category>>>());

        // Listen to stream to verify it doesn't throw
        final subscription = stream.listen(
          (categories) {
            expect(categories, isA<List<Category>>());
          },
          onError: (Object error) {
            fail('Stream should not emit errors: $error');
          },
        );

        await Future<void>.delayed(const Duration(milliseconds: 100));
        await subscription.cancel();
      });

      test('should throw UnimplementedError for watchCategory', () async {
        expect(
          () => repository.watchCategory('test-category'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test(
        'should handle watchCategoryForUser with non-existent category',
        () async {
          final stream = repository.watchCategoryForUser(
            testUserId,
            'non-existent',
          );
          expect(stream, isA<Stream<Category?>>());

          final subscription = stream.listen(
            (category) {
              expect(category, isNull);
            },
            onError: (Object error) {
              fail('Stream should not emit errors: $error');
            },
          );

          await Future<void>.delayed(const Duration(milliseconds: 100));
          await subscription.cancel();
        },
      );
    });

    group('Edge Cases and Boundary Conditions', () {
      test('should handle categories with extreme sort orders', () async {
        final categoryMaxSort = MockDataFactory.createCategory(
          id: 'max-sort',
          userId: testUserId,
          name: 'Max Sort',
          type: CategoryType.expense,
          icon: 'test',
          color: 'blue',
          schemaVersion: 1,
          sortOrder: 2147483647, // Max int32
        );

        final categoryMinSort = MockDataFactory.createCategory(
          id: 'min-sort',
          userId: testUserId,
          name: 'Min Sort',
          type: CategoryType.expense,
          icon: 'test',
          color: 'blue',
          schemaVersion: 1,
          sortOrder: -2147483648, // Min int32
        );

        final id1 = await repository.create(categoryMaxSort);
        final id2 = await repository.create(categoryMinSort);

        expect(id1, isNotEmpty);
        expect(id2, isNotEmpty);
      });

      test('should handle categories with very long descriptions', () async {
        final longDescription = 'A' * 10000; // Very long description
        final category = Category(
          id: '',
          userId: testUserId,
          name: 'Long Description Category',
          type: CategoryType.expense,
          icon: 'test',
          color: 'blue',
          description: longDescription,
          isActive: true,
          sortOrder: 0,
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final result = await repository.create(category);
        expect(result, isNotEmpty);
      });

      test('should handle categories with special character names', () async {
        const specialCharName = '🤑💰💸📊💳💵💴💶💷🏦💎💸';
        final category = MockDataFactory.createCategory(
          id: '',
          userId: testUserId,
          name: specialCharName,
          type: CategoryType.expense,
          icon: 'test',
          color: 'blue',
          schemaVersion: 1,
        );

        final result = await repository.create(category);
        expect(result, isNotEmpty);
      });

      test('should handle categories with future schema version', () async {
        final futureSchemaCategory = MockDataFactory.createCategory(
          id: '',
          userId: testUserId,
          name: 'Future Schema',
          type: CategoryType.expense,
          icon: 'test',
          color: 'blue',
          schemaVersion: 999, // Future schema version
        );

        final result = await repository.create(futureSchemaCategory);
        expect(result, isNotEmpty);
      });

      test(
        'should handle categories with null parent references correctly',
        () async {
          final categoryWithNullParent = Category(
            id: '',
            userId: testUserId,
            name: 'Null Parent Category',
            type: CategoryType.expense,
            icon: 'test',
            color: 'blue',
            parentId: null, // Explicit null parent
            isActive: true,
            sortOrder: 0,
            schemaVersion: 1,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          final result = await repository.create(categoryWithNullParent);
          expect(result, isNotEmpty);
        },
      );
    });

    group('Performance Edge Cases', () {
      test('should handle large number of categories efficiently', () async {
        // Create many categories
        final categories = List.generate(
          100,
          (index) => MockDataFactory.createCategory(
            id: 'category_$index',
            userId: testUserId,
            name: 'Category $index',
            type: index.isEven ? CategoryType.expense : CategoryType.income,
            icon: 'test',
            color: 'blue',
            schemaVersion: 1,
          ),
        );

        // Add them to Firestore
        for (final category in categories) {
          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('categories')
              .doc(category.id)
              .set(category.toJson());
        }

        final result = await repository.getCategoriesByUserId(testUserId);
        expect(result.length, equals(100));
      });

      test('should handle deep category hierarchies', () async {
        // Create nested categories (5 levels deep)
        String? parentId;
        for (var i = 0; i < 5; i++) {
          final category = MockDataFactory.createCategory(
            id: 'level_$i',
            userId: testUserId,
            name: 'Level $i Category',
            type: CategoryType.expense,
            icon: 'test',
            color: 'blue',
            schemaVersion: 1,
            parentId: parentId,
          );

          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('categories')
              .doc(category.id)
              .set(category.toJson());

          parentId = category.id;
        }

        // Test getting subcategories at each level
        final level1Subcategories = await repository.getSubcategories(
          testUserId,
          'level_0',
        );
        expect(level1Subcategories.length, equals(1));
      });
    });
  });
}
