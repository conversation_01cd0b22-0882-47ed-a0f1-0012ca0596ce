import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/implementations/transaction_repository_impl.dart';
import 'package:budapp/features/budgets/services/budget_transaction_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/firebase_test_setup.dart';
import '../../helpers/mock_data_factory.dart';

// Mock classes for testing
class MockBudgetTransactionService extends Mock
    implements BudgetTransactionService {}

void main() {
  setUpAll(() {
    // Use MockDataFactory to create fallback values instead of Fake classes
    registerFallbackValue(MockDataFactory.createTransaction());
  });

  group('TransactionRepository Balance Adjustment Tests', () {
    late FirebaseTestSetup testSetup;
    late FirestoreService firestoreService;
    late MockBudgetTransactionService mockBudgetTransactionService;
    late TransactionRepositoryImpl repository;
    late String testUserId;
    late String testAccountId1;
    late String testAccountId2;

    setUp(() async {
      // Create Firebase test setup with authenticated user
      testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
        uid: 'test-user-1',
        email: '<EMAIL>',
      );

      firestoreService = FirestoreService(testSetup.firestore);
      mockBudgetTransactionService = MockBudgetTransactionService();

      // Setup mock budget transaction service
      when(
        () => mockBudgetTransactionService.updateBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      when(
        () => mockBudgetTransactionService.revertBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async {});

      when(
        () => mockBudgetTransactionService.updateBudgetsForTransactionUpdate(
          any(),
          any(),
          any(),
        ),
      ).thenAnswer((_) async => <BudgetUpdate>[]);

      repository = TransactionRepositoryImpl(
        firestoreService,
        mockBudgetTransactionService,
      );

      testUserId = 'test-user-1';
      testAccountId1 = 'account-1';
      testAccountId2 = 'account-2';

      // Create test accounts with initial balances
      final account1 = Account(
        id: testAccountId1,
        userId: testUserId,
        name: 'Test Account 1',
        type: AccountType.checking,
        classification: AccountClassification.asset,
        initialBalanceCents: 100000,
        currentBalanceCents: 100000, // $1000.00
        isActive: true,
        isPrimary: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final account2 = Account(
        id: testAccountId2,
        userId: testUserId,
        name: 'Test Account 2',
        type: AccountType.savings,
        classification: AccountClassification.asset,
        initialBalanceCents: 50000,
        currentBalanceCents: 50000, // $500.00
        isActive: true,
        isPrimary: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await testSetup.firestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId1)
          .set(account1.toJson());

      await testSetup.firestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId2)
          .set(account2.toJson());
    });

    tearDown(() async {
      await testSetup.dispose();
    });

    group('Transaction Update with Balance Adjustment', () {
      test(
        'should update transaction and adjust account balances correctly',
        () async {
          // Create an initial expense transaction
          final originalTransactionId = await repository
              .createExpenseTransaction(
                userId: testUserId,
                fromAccountId: testAccountId1,
                amountCents: 5000, // $50.00
                transactionDate: DateTime.now(),
                description: 'Original expense',
                categoryId: 'category-1',
              );

          // Verify initial account balance
          final accountDoc1 = await testSetup.firestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId1)
              .get();
          final initialBalance =
              accountDoc1.data()!['currentBalanceCents'] as int;
          expect(initialBalance, equals(95000)); // $950.00 (1000 - 50)

          // Get the original transaction
          final originalTransaction = await repository.getTransactionById(
            testUserId,
            originalTransactionId,
          );
          expect(originalTransaction, isNotNull);

          // Update the transaction to a different amount and account
          final updatedTransaction = originalTransaction!.copyWith(
            amountCents: 7500, // Changed from $50 to $75
            fromAccountId: testAccountId2, // Changed from account1 to account2
            description: 'Updated expense',
          );

          await repository.updateTransactionWithBalanceAdjustment(
            testUserId,
            originalTransactionId,
            updatedTransaction,
          );

          // Verify account balances were adjusted correctly
          // Account 1: Should be back to $1000 (original expense reversed)
          final account1Doc = await testSetup.firestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId1)
              .get();
          expect(
            account1Doc.data()!['currentBalanceCents'],
            equals(100000), // Back to original $1000
          );

          // Account 2: Should be $425 (original $500 - $75)
          final account2Doc = await testSetup.firestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId2)
              .get();
          expect(
            account2Doc.data()!['currentBalanceCents'],
            equals(42500), // $425
          );

          // Verify the transaction was updated
          final updatedTransactionFromDb = await repository.getTransactionById(
            testUserId,
            originalTransactionId,
          );
          expect(updatedTransactionFromDb!.amountCents, equals(7500));
          expect(
            updatedTransactionFromDb.fromAccountId,
            equals(testAccountId2),
          );
          expect(
            updatedTransactionFromDb.description,
            equals('Updated expense'),
          );
        },
      );

      test('should handle transaction type changes correctly', () async {
        // Create an expense transaction
        final transactionId = await repository.createExpenseTransaction(
          userId: testUserId,
          fromAccountId: testAccountId1,
          amountCents: 6000, // $60.00
          transactionDate: DateTime.now(),
          description: 'Original expense',
          categoryId: 'category-1',
        );

        final originalTransaction = await repository.getTransactionById(
          testUserId,
          transactionId,
        );

        // Change it to an income transaction
        final updatedTransaction = Transaction(
          id: originalTransaction!.id,
          userId: testUserId,
          type: TransactionType.income, // Changed type
          status: TransactionStatus.completed,
          amountCents: 8000, // $80.00
          toAccountId: testAccountId2, // Income goes to account 2
          description: 'Updated to income',
          transactionDate: DateTime.now(),
          createdAt: originalTransaction.createdAt,
          updatedAt: DateTime.now(),
        );

        await repository.updateTransactionWithBalanceAdjustment(
          testUserId,
          transactionId,
          updatedTransaction,
        );

        // Account 1: Should be back to original balance (expense reversed)
        final account1Doc = await testSetup.firestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testAccountId1)
            .get();
        expect(
          account1Doc.data()!['currentBalanceCents'],
          equals(100000), // Back to $1000
        );

        // Account 2: Should have income added
        final account2Doc = await testSetup.firestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testAccountId2)
            .get();
        expect(
          account2Doc.data()!['currentBalanceCents'],
          equals(58000), // $580 ($500 + $80)
        );
      });
    });

    group('Transaction Deletion with Balance Reversal', () {
      test('should delete expense transaction and reverse balance', () async {
        // Create an expense transaction
        final transactionId = await repository.createExpenseTransaction(
          userId: testUserId,
          fromAccountId: testAccountId1,
          amountCents: 4000, // $40.00
          transactionDate: DateTime.now(),
          description: 'Test expense',
          categoryId: 'category-1',
        );

        // Verify the expense was deducted
        final accountDocAfterExpense = await testSetup.firestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testAccountId1)
            .get();
        expect(
          accountDocAfterExpense.data()!['currentBalanceCents'],
          equals(96000), // $960 ($1000 - $40)
        );

        // Delete the transaction
        await repository.deleteTransaction(testUserId, transactionId);

        // Verify the transaction was deleted
        final transactionDoc = await testSetup.firestore
            .collection('users')
            .doc(testUserId)
            .collection('transactions')
            .doc(transactionId)
            .get();
        expect(transactionDoc.exists, isFalse);

        // Verify the balance was restored
        final accountDocAfterDeletion = await testSetup.firestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testAccountId1)
            .get();
        expect(
          accountDocAfterDeletion.data()!['currentBalanceCents'],
          equals(100000), // Back to $1000
        );
      });

      test('should delete income transaction and reverse balance', () async {
        // Create an income transaction
        final transactionId = await repository.createIncomeTransaction(
          userId: testUserId,
          toAccountId: testAccountId2,
          amountCents: 15000, // $150.00
          transactionDate: DateTime.now(),
          description: 'Test income',
          categoryId: 'income-category',
        );

        // Verify the income was added
        final accountDocAfterIncome = await testSetup.firestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testAccountId2)
            .get();
        expect(
          accountDocAfterIncome.data()!['currentBalanceCents'],
          equals(65000), // $650 ($500 + $150)
        );

        // Delete the transaction
        await repository.deleteTransaction(testUserId, transactionId);

        // Verify the balance was restored
        final accountDocAfterDeletion = await testSetup.firestore
            .collection('users')
            .doc(testUserId)
            .collection('accounts')
            .doc(testAccountId2)
            .get();
        expect(
          accountDocAfterDeletion.data()!['currentBalanceCents'],
          equals(50000), // Back to $500
        );
      });

      test(
        'should delete transfer transaction and reverse both balances',
        () async {
          // Create a transfer transaction
          final transactionId = await repository.createTransferTransaction(
            userId: testUserId,
            fromAccountId: testAccountId1,
            toAccountId: testAccountId2,
            amountCents: 20000, // $200.00
            transactionDate: DateTime.now(),
            description: 'Test transfer',
          );

          // Verify balances after transfer
          final account1DocAfterTransfer = await testSetup.firestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId1)
              .get();
          expect(
            account1DocAfterTransfer.data()!['currentBalanceCents'],
            equals(80000), // $800 ($1000 - $200)
          );

          final account2DocAfterTransfer = await testSetup.firestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId2)
              .get();
          expect(
            account2DocAfterTransfer.data()!['currentBalanceCents'],
            equals(70000), // $700 ($500 + $200)
          );

          // Delete the transaction
          await repository.deleteTransaction(testUserId, transactionId);

          // Verify both balances were restored
          final account1DocAfterDeletion = await testSetup.firestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId1)
              .get();
          expect(
            account1DocAfterDeletion.data()!['currentBalanceCents'],
            equals(100000), // Back to $1000
          );

          final account2DocAfterDeletion = await testSetup.firestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId2)
              .get();
          expect(
            account2DocAfterDeletion.data()!['currentBalanceCents'],
            equals(50000), // Back to $500
          );
        },
      );
    });

    group('Account Balance Calculation', () {
      test(
        'should calculate account balance correctly from transactions',
        () async {
          // Create multiple transactions affecting account 1
          await repository.createIncomeTransaction(
            userId: testUserId,
            toAccountId: testAccountId1,
            amountCents: 30000, // +$300
            transactionDate: DateTime.now(),
            description: 'Income 1',
          );

          await repository.createExpenseTransaction(
            userId: testUserId,
            fromAccountId: testAccountId1,
            amountCents: 12000, // -$120
            transactionDate: DateTime.now(),
            description: 'Expense 1',
          );

          await repository.createTransferTransaction(
            userId: testUserId,
            fromAccountId: testAccountId1,
            toAccountId: testAccountId2,
            amountCents: 8000, // -$80 from account1, +$80 to account2
            transactionDate: DateTime.now(),
            description: 'Transfer',
          );

          // Calculate balance for account 1
          final calculatedBalance1 = await repository
              .calculateAccountBalanceForUser(testUserId, testAccountId1);

          // Net transaction effect: Income: +$300, Expense: -$120, Transfer out: -$80
          // Expected net: $100 (starting from 0, this method calculates transaction effects only)
          expect(calculatedBalance1, equals(10000));

          // Calculate balance for account 2
          final calculatedBalance2 = await repository
              .calculateAccountBalanceForUser(testUserId, testAccountId2);

          // Net transaction effect: Transfer in: +$80
          // Expected net: $80 (starting from 0)
          expect(calculatedBalance2, equals(8000));
        },
      );

      test(
        'should handle pending transactions correctly in balance calculation',
        () async {
          // Create a completed transaction
          await repository.createExpenseTransaction(
            userId: testUserId,
            fromAccountId: testAccountId1,
            amountCents: 5000,
            transactionDate: DateTime.now(),
            description: 'Completed expense',
          );

          // Create a pending transaction directly (bypass the create methods which set status to completed)
          final pendingTransaction = Transaction(
            id: 'pending-tx',
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.pending,
            amountCents: 10000,
            fromAccountId: testAccountId1,
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await repository.create(pendingTransaction);

          // Balance calculation should only include completed transactions
          final calculatedBalance = await repository
              .calculateAccountBalanceForUser(testUserId, testAccountId1);

          // Should only include the completed expense: -$50 (net transaction effect)
          expect(calculatedBalance, equals(-5000));
        },
      );
    });

    group('Error Handling', () {
      test(
        'should throw error when updating non-existent transaction',
        () async {
          final transaction = Transaction(
            id: 'non-existent',
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 5000,
            fromAccountId: testAccountId1,
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          expect(
            () => repository.updateTransactionWithBalanceAdjustment(
              testUserId,
              'non-existent',
              transaction,
            ),
            throwsA(isA<ArgumentError>()),
          );
        },
      );

      test(
        'should throw error when deleting non-existent transaction',
        () async {
          expect(
            () => repository.deleteTransaction(testUserId, 'non-existent'),
            throwsA(isA<ArgumentError>()),
          );
        },
      );

      test(
        'should throw error when transaction belongs to different user',
        () async {
          final transactionId = await repository.createExpenseTransaction(
            userId: testUserId,
            fromAccountId: testAccountId1,
            amountCents: 5000,
            transactionDate: DateTime.now(),
            description: 'Test expense',
          );

          final transaction = await repository.getTransactionById(
            testUserId,
            transactionId,
          );

          // Try to update with a different userId
          final wrongUserTransaction = transaction!.copyWith(
            userId: 'different-user',
          );

          expect(
            () => repository.updateTransactionWithBalanceAdjustment(
              testUserId,
              transactionId,
              wrongUserTransaction,
            ),
            throwsA(isA<ArgumentError>()),
          );
        },
      );
    });
  });
}
