import 'package:budapp/data/models/user_profile.dart';
import 'package:budapp/data/repositories/implementations/user_repository_impl.dart';
import 'package:budapp/data/repositories/interfaces/user_repository.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/firebase_test_setup.dart';

// Mock classes
class MockUser extends Mock implements User {}

class MockUserMetadata extends Mock implements UserMetadata {}

class MockUserInfo extends Mock implements UserInfo {
  @override
  String providerId = '';
  @override
  String uid = '';
  @override
  String? email = '';
  @override
  String? displayName = '';
}

void main() {
  group('UserRepositoryImpl Tests', () {
    late FirebaseTestSetup testSetup;
    late FirestoreService firestoreService;
    late UserRepositoryImpl userRepository;

    setUp(() async {
      // Create Firebase test setup with authenticated user
      testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
        uid: 'test-uid',
        email: '<EMAIL>',
      );

      firestoreService = FirestoreService(testSetup.firestore);
      userRepository = UserRepositoryImpl(firestoreService, testSetup.auth);
    });

    tearDown(() async {
      await testSetup.dispose();
    });

    group('Basic CRUD Operations', () {
      test('create user profile', () async {
        final userProfile = UserProfile(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        );

        final result = await userRepository.create(userProfile);
        expect(result, 'test-uid');

        // Verify the user was created in Firestore
        final doc = await testSetup.firestore
            .collection('users')
            .doc('test-uid')
            .get();
        expect(doc.exists, true);
        expect(doc.data()!['email'], '<EMAIL>');
      });

      test('get user by id', () async {
        // Create a user first
        final userProfile = UserProfile(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        );

        await userRepository.create(userProfile);

        // Get the user
        final result = await userRepository.getById('test-uid');
        expect(result, isNotNull);
        expect(result!.uid, 'test-uid');
        expect(result.email, '<EMAIL>');
        expect(result.displayName, 'Test User');
      });

      test('get non-existent user returns null', () async {
        final result = await userRepository.getById('non-existent');
        expect(result, isNull);
      });

      test('update user profile', () async {
        // Create a user first
        final userProfile = UserProfile(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: false,
          authProviders: ['password'],
        );

        await userRepository.create(userProfile);

        // Update the user
        final updatedProfile = userProfile.copyWith(
          displayName: 'Updated User',
          isEmailVerified: true,
        );

        await userRepository.update('test-uid', updatedProfile);

        // Verify the update
        final result = await userRepository.getById('test-uid');
        expect(result!.displayName, 'Updated User');
        expect(result.isEmailVerified, true);
      });

      test('delete user profile', () async {
        // Create a user first
        final userProfile = UserProfile(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        );

        await userRepository.create(userProfile);

        // Delete the user
        await userRepository.delete('test-uid');

        // Verify the user is deleted
        final result = await userRepository.getById('test-uid');
        expect(result, isNull);
      });

      test('check if user exists', () async {
        // Check non-existent user
        expect(await userRepository.exists('test-uid'), false);

        // Create a user
        final userProfile = UserProfile(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        );

        await userRepository.create(userProfile);

        // Check existing user
        expect(await userRepository.exists('test-uid'), true);
      });
    });

    group('User-specific Operations', () {
      test('get user by email', () async {
        final userProfile = UserProfile(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        );

        await userRepository.create(userProfile);

        final result = await userRepository.getUserByEmail('<EMAIL>');
        expect(result, isNotNull);
        expect(result!.uid, 'test-uid');
        expect(result.email, '<EMAIL>');
      });

      test('get user by non-existent email returns null', () async {
        final result = await userRepository.getUserByEmail(
          '<EMAIL>',
        );
        expect(result, isNull);
      });

      test('update user preferences', () async {
        final userProfile = UserProfile(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        );

        await userRepository.create(userProfile);

        final preferences = {'theme': 'dark', 'notifications': true};
        await userRepository.updatePreferences('test-uid', preferences);

        final result = await userRepository.getPreferences('test-uid');
        expect(result['theme'], 'dark');
        expect(result['notifications'], true);
      });

      test('update last login time', () async {
        final userProfile = UserProfile(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        );

        await userRepository.create(userProfile);

        final newLoginTime = DateTime.now().add(const Duration(hours: 1));
        await userRepository.updateLastLogin('test-uid', newLoginTime);

        final result = await userRepository.getById('test-uid');
        expect(
          result!.lastLoginAt?.millisecondsSinceEpoch ?? 0,
          closeTo(newLoginTime.millisecondsSinceEpoch, 1000),
        );
      });

      test('update email verification status', () async {
        final userProfile = UserProfile(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: false,
          authProviders: ['password'],
        );

        await userRepository.create(userProfile);

        await userRepository.updateEmailVerificationStatus(
          'test-uid',
          isVerified: true,
        );

        final result = await userRepository.getById('test-uid');
        expect(result!.isEmailVerified, true);
      });

      test('add and remove auth providers', () async {
        final userProfile = UserProfile(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        );

        await userRepository.create(userProfile);

        // Add Google provider
        await userRepository.addAuthProvider('test-uid', 'google.com');

        var result = await userRepository.getById('test-uid');
        expect(result!.authProviders, contains('google.com'));
        expect(result.authProviders, contains('password'));

        // Remove password provider
        await userRepository.removeAuthProvider('test-uid', 'password');

        result = await userRepository.getById('test-uid');
        expect(result!.authProviders, contains('google.com'));
        expect(result.authProviders, isNot(contains('password')));
      });

      test('get user stats', () async {
        final createdAt = DateTime.now().subtract(const Duration(days: 30));
        final userProfile = UserProfile(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: createdAt,
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password', 'google.com'],
          preferences: {'theme': 'dark', 'notifications': true},
        );

        await userRepository.create(userProfile);

        final stats = await userRepository.getUserStats('test-uid');
        expect(stats['uid'], 'test-uid');
        expect(stats['isEmailVerified'], true);
        expect(stats['authProviders'], ['password', 'google.com']);
        expect(stats['preferencesCount'], 2);
        expect(stats['accountAge'], closeTo(30, 1));
      });

      test('export user data', () async {
        final userProfile = UserProfile(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        );

        await userRepository.create(userProfile);

        final exportData = await userRepository.exportUserData('test-uid');
        expect(exportData['profile'], isNotNull);
        expect(exportData['exportedAt'], isNotNull);
        expect(exportData['version'], '1');
      });
    });

    group('Batch Operations', () {
      test('batchCreate should create multiple users', () async {
        final users = [
          UserProfile(
            uid: 'user-1',
            email: '<EMAIL>',
            displayName: 'User 1',
            createdAt: DateTime.now(),
            lastLoginAt: DateTime.now(),
            isEmailVerified: true,
            authProviders: ['password'],
          ),
          UserProfile(
            uid: 'user-2',
            email: '<EMAIL>',
            displayName: 'User 2',
            createdAt: DateTime.now(),
            lastLoginAt: DateTime.now(),
            isEmailVerified: false,
            authProviders: ['google'],
          ),
        ];

        await userRepository.batchCreate(users);

        // Verify both users were created
        final user1 = await userRepository.getById('user-1');
        final user2 = await userRepository.getById('user-2');

        expect(user1, isNotNull);
        expect(user1!.email, '<EMAIL>');
        expect(user2, isNotNull);
        expect(user2!.email, '<EMAIL>');
      });

      test('batchUpdate should update multiple users', () async {
        // Create initial users
        final users = [
          UserProfile(
            uid: 'user-1',
            email: '<EMAIL>',
            displayName: 'User 1',
            createdAt: DateTime.now(),
            lastLoginAt: DateTime.now(),
            isEmailVerified: false,
            authProviders: ['password'],
          ),
          UserProfile(
            uid: 'user-2',
            email: '<EMAIL>',
            displayName: 'User 2',
            createdAt: DateTime.now(),
            lastLoginAt: DateTime.now(),
            isEmailVerified: false,
            authProviders: ['google'],
          ),
        ];

        await userRepository.batchCreate(users);

        // Update both users
        final updatedUsers = {
          'user-1': users[0].copyWith(
            displayName: 'Updated User 1',
            isEmailVerified: true,
          ),
          'user-2': users[1].copyWith(
            displayName: 'Updated User 2',
            isEmailVerified: true,
          ),
        };

        await userRepository.batchUpdate(updatedUsers);

        // Verify updates
        final user1 = await userRepository.getById('user-1');
        final user2 = await userRepository.getById('user-2');

        expect(user1!.displayName, 'Updated User 1');
        expect(user1.isEmailVerified, true);
        expect(user2!.displayName, 'Updated User 2');
        expect(user2.isEmailVerified, true);
      });

      test('batchDelete should delete multiple users', () async {
        // Create users first
        final users = [
          UserProfile(
            uid: 'user-1',
            email: '<EMAIL>',
            displayName: 'User 1',
            createdAt: DateTime.now(),
            lastLoginAt: DateTime.now(),
            isEmailVerified: true,
            authProviders: ['password'],
          ),
          UserProfile(
            uid: 'user-2',
            email: '<EMAIL>',
            displayName: 'User 2',
            createdAt: DateTime.now(),
            lastLoginAt: DateTime.now(),
            isEmailVerified: true,
            authProviders: ['google'],
          ),
        ];

        await userRepository.batchCreate(users);

        // Delete both users
        await userRepository.batchDelete(['user-1', 'user-2']);

        // Verify deletion
        final user1 = await userRepository.getById('user-1');
        final user2 = await userRepository.getById('user-2');

        expect(user1, isNull);
        expect(user2, isNull);
      });
    });
  });

  group('Repository Provider Tests', () {
    test(
      'userRepositoryProvider provides UserRepositoryImpl instance',
      () async {
        final testSetup = await FirebaseTestSetup.createWithAuthenticatedUser();
        final firestoreService = FirestoreService(testSetup.firestore);
        final container = ProviderContainer(
          overrides: [
            firestoreServiceProvider.overrideWithValue(firestoreService),
            firebaseAuthProvider.overrideWithValue(testSetup.auth),
          ],
        );

        final userRepo = container.read(userRepositoryProvider);
        expect(userRepo, isA<UserRepositoryImpl>());

        container.dispose();
        await testSetup.dispose();
      },
    );

    test(
      'userRepositoryProvider uses injected FirestoreService instance',
      () async {
        final testSetup = await FirebaseTestSetup.createWithAuthenticatedUser();
        final firestoreService = FirestoreService(testSetup.firestore);
        final container = ProviderContainer(
          overrides: [
            firestoreServiceProvider.overrideWithValue(firestoreService),
            firebaseAuthProvider.overrideWithValue(testSetup.auth),
          ],
        );

        // The provider should use the FirestoreService instance from firestoreServiceProvider
        final userRepo = container.read(userRepositoryProvider);
        expect(userRepo, isA<IUserRepository>());

        container.dispose();
        await testSetup.dispose();
      },
    );

    test(
      'userRepositoryProvider can perform operations with mocked Firestore',
      () async {
        final testSetup = await FirebaseTestSetup.createWithAuthenticatedUser();
        final firestoreService = FirestoreService(testSetup.firestore);
        final container = ProviderContainer(
          overrides: [
            firestoreServiceProvider.overrideWithValue(firestoreService),
            firebaseAuthProvider.overrideWithValue(testSetup.auth),
          ],
        );

        final userRepo = container.read(userRepositoryProvider);

        // Test that we can create a user through the provider
        final userProfile = UserProfile(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        );

        final result = await userRepo.create(userProfile);
        expect(result, 'test-uid');

        // Test that we can retrieve the user
        final retrievedUser = await userRepo.getById('test-uid');
        expect(retrievedUser, isNotNull);
        expect(retrievedUser!.email, '<EMAIL>');

        container.dispose();
        await testSetup.dispose();
      },
    );
  });

  group('Collection Operations', () {
    late FirebaseTestSetup testSetup;
    late FirestoreService firestoreService;
    late UserRepositoryImpl userRepository;

    setUp(() async {
      testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
        uid: 'test-uid',
        email: '<EMAIL>',
      );
      firestoreService = FirestoreService(testSetup.firestore);
      userRepository = UserRepositoryImpl(firestoreService, testSetup.auth);
    });

    tearDown(() async {
      await testSetup.dispose();
    });

    test('getAll returns all users', () async {
      // Create test users
      final users = [
        UserProfile(
          uid: 'user-1',
          email: '<EMAIL>',
          displayName: 'User 1',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        ),
        UserProfile(
          uid: 'user-2',
          email: '<EMAIL>',
          displayName: 'User 2',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: false,
          authProviders: ['google'],
        ),
      ];

      await userRepository.batchCreate(users);

      final result = await userRepository.getAll();
      expect(result.length, 2);
      expect(result.map((u) => u.uid), containsAll(['user-1', 'user-2']));
    });

    test('getAll with limit returns limited results', () async {
      // Create multiple users
      final users = List.generate(
        5,
        (i) => UserProfile(
          uid: 'user-$i',
          email: 'user$<EMAIL>',
          displayName: 'User $i',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        ),
      );

      await userRepository.batchCreate(users);

      final result = await userRepository.getAll(limit: 3);
      expect(result.length, 3);
    });

    test('getPaginated returns paginated results', () async {
      // Create test users
      final users = List.generate(
        5,
        (i) => UserProfile(
          uid: 'user-$i',
          email: 'user$<EMAIL>',
          displayName: 'User $i',
          createdAt: DateTime.now().subtract(Duration(minutes: i)),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        ),
      );

      await userRepository.batchCreate(users);

      // Get first page
      final firstPage = await userRepository.getPaginated(limit: 2);
      expect(firstPage.length, 2);

      // Get second page (would need proper implementation)
      final secondPage = await userRepository.getPaginated(limit: 2);
      expect(secondPage.length, lessThanOrEqualTo(2));
    });
  });

  group('Query Operations', () {
    late FirebaseTestSetup testSetup;
    late FirestoreService firestoreService;
    late UserRepositoryImpl userRepository;

    setUp(() async {
      testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
        uid: 'test-uid',
        email: '<EMAIL>',
      );
      firestoreService = FirestoreService(testSetup.firestore);
      userRepository = UserRepositoryImpl(firestoreService, testSetup.auth);
    });

    tearDown(() async {
      await testSetup.dispose();
    });

    test('query with where conditions', () async {
      // Create test users
      final users = [
        UserProfile(
          uid: 'user-1',
          email: '<EMAIL>',
          displayName: 'Active User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        ),
        UserProfile(
          uid: 'user-2',
          email: '<EMAIL>',
          displayName: 'Inactive User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: false,
          authProviders: ['google'],
        ),
      ];

      await userRepository.batchCreate(users);

      final result = await userRepository.query(
        where: {'isEmailVerified': true},
      );
      expect(result.length, 1);
      expect(result.first.uid, 'user-1');
    });

    test('query with orderBy and limit', () async {
      // Create test users with different creation times
      final users = List.generate(
        3,
        (i) => UserProfile(
          uid: 'user-$i',
          email: 'user$<EMAIL>',
          displayName: 'User $i',
          createdAt: DateTime.now().subtract(Duration(days: i)),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        ),
      );

      await userRepository.batchCreate(users);

      final result = await userRepository.query(
        orderBy: 'displayName',
        descending: true,
        limit: 2,
      );
      expect(result.length, 2);
    });

    test('count returns total user count', () async {
      // Initially no users
      expect(await userRepository.count(), 0);

      // Create test users
      final users = List.generate(
        3,
        (i) => UserProfile(
          uid: 'user-$i',
          email: 'user$<EMAIL>',
          displayName: 'User $i',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        ),
      );

      await userRepository.batchCreate(users);
      expect(await userRepository.count(), 3);
    });

    test('count with where conditions', () async {
      // Create test users
      final users = [
        UserProfile(
          uid: 'user-1',
          email: '<EMAIL>',
          displayName: 'User 1',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        ),
        UserProfile(
          uid: 'user-2',
          email: '<EMAIL>',
          displayName: 'User 2',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: false,
          authProviders: ['google'],
        ),
      ];

      await userRepository.batchCreate(users);

      final verifiedCount = await userRepository.count(
        where: {'isEmailVerified': true},
      );
      expect(verifiedCount, 1);
    });
  });

  group('Streaming Operations', () {
    late FirebaseTestSetup testSetup;
    late FirestoreService firestoreService;
    late UserRepositoryImpl userRepository;

    setUp(() async {
      testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
        uid: 'test-uid',
        email: '<EMAIL>',
      );
      firestoreService = FirestoreService(testSetup.firestore);
      userRepository = UserRepositoryImpl(firestoreService, testSetup.auth);
    });

    tearDown(() async {
      await testSetup.dispose();
    });

    test('watchById streams user changes', () async {
      final userProfile = UserProfile(
        uid: 'test-uid-watch',
        email: '<EMAIL>',
        displayName: 'Test User',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        isEmailVerified: true,
        authProviders: ['password'],
      );

      // Create user first
      await userRepository.create(userProfile);

      // Then start watching and get the current state
      final stream = userRepository.watchById('test-uid-watch');
      final result = await stream.first;

      expect(result, isNotNull);
      expect(result!.uid, 'test-uid-watch');
    });

    test('watchById streams null for non-existent user', () async {
      final stream = userRepository.watchById('non-existent');
      final result = await stream.first;
      expect(result, isNull);
    });

    test('watchAll streams user list changes', () async {
      // Start watching
      final stream = userRepository.watchAll();
      final future = stream.first;

      // Create a user
      final userProfile = UserProfile(
        uid: 'test-uid-all',
        email: '<EMAIL>',
        displayName: 'Test User',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        isEmailVerified: true,
        authProviders: ['password'],
      );

      await userRepository.create(userProfile);

      final result = await future;
      expect(result.length, greaterThanOrEqualTo(0));
    });

    test('watchAll with limit streams limited results', () async {
      final stream = userRepository.watchAll(limit: 2);
      final result = await stream.first;
      expect(result.length, lessThanOrEqualTo(2));
    });
  });

  group('Advanced User Operations', () {
    late FirebaseTestSetup testSetup;
    late FirestoreService firestoreService;
    late UserRepositoryImpl userRepository;

    setUp(() async {
      testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
        uid: 'test-uid',
        email: '<EMAIL>',
      );
      firestoreService = FirestoreService(testSetup.firestore);
      userRepository = UserRepositoryImpl(firestoreService, testSetup.auth);
    });

    tearDown(() async {
      await testSetup.dispose();
    });

    test('getUsersByAuthProvider returns filtered users', () async {
      // Create users with different auth providers
      final users = [
        UserProfile(
          uid: 'user-1',
          email: '<EMAIL>',
          displayName: 'Google User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['google.com'],
        ),
        UserProfile(
          uid: 'user-2',
          email: '<EMAIL>',
          displayName: 'Password User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        ),
        UserProfile(
          uid: 'user-3',
          email: '<EMAIL>',
          displayName: 'Multi Auth User',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['google.com', 'password'],
        ),
      ];

      await userRepository.batchCreate(users);

      final googleUsers = await userRepository.getUsersByAuthProvider(
        'google.com',
      );
      expect(googleUsers.length, 2);
      expect(googleUsers.map((u) => u.uid), containsAll(['user-1', 'user-3']));

      final passwordUsers = await userRepository.getUsersByAuthProvider(
        'password',
      );
      expect(passwordUsers.length, 2);
      expect(
        passwordUsers.map((u) => u.uid),
        containsAll(['user-2', 'user-3']),
      );
    });

    test('searchUsersByDisplayName returns matching users', () async {
      // Create users with different display names
      final users = [
        UserProfile(
          uid: 'user-1',
          email: '<EMAIL>',
          displayName: 'John Doe',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        ),
        UserProfile(
          uid: 'user-2',
          email: '<EMAIL>',
          displayName: 'Jane Smith',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        ),
        UserProfile(
          uid: 'user-3',
          email: '<EMAIL>',
          displayName: 'John Smith',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          isEmailVerified: true,
          authProviders: ['password'],
        ),
      ];

      await userRepository.batchCreate(users);

      final johnResults = await userRepository.searchUsersByDisplayName('John');
      expect(johnResults.length, 2);
      expect(
        johnResults.map((u) => u.displayName),
        containsAll(['John Doe', 'John Smith']),
      );
    });

    test('createOrUpdateFromFirebaseUser creates new user', () async {
      final mockUser = MockUser();
      final mockMetadata = MockUserMetadata();
      final mockUserInfo = MockUserInfo();

      when(() => mockUser.uid).thenReturn('firebase-uid');
      when(() => mockUser.email).thenReturn('<EMAIL>');
      when(() => mockUser.displayName).thenReturn('Firebase User');
      when(() => mockUser.photoURL).thenReturn('https://example.com/photo.jpg');
      when(() => mockUser.emailVerified).thenReturn(true);
      when(() => mockUser.metadata).thenReturn(mockMetadata);
      when(() => mockUser.providerData).thenReturn([mockUserInfo]);
      when(() => mockMetadata.creationTime).thenReturn(DateTime.now());
      when(() => mockMetadata.lastSignInTime).thenReturn(DateTime.now());
      mockUserInfo.providerId = 'google.com';
      mockUserInfo.uid = 'google-uid';

      await userRepository.createOrUpdateFromFirebaseUser(mockUser);

      final result = await userRepository.getUserById('firebase-uid');
      expect(result, isNotNull);
      expect(result!.email, '<EMAIL>');
      expect(result.displayName, 'Firebase User');
      expect(result.authProviders, contains('google.com'));
    });

    test('createOrUpdateFromFirebaseUser updates existing user', () async {
      // Create existing user
      final existingUser = UserProfile(
        uid: 'firebase-uid',
        email: '<EMAIL>',
        displayName: 'Old Name',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        lastLoginAt: DateTime.now().subtract(const Duration(hours: 1)),
        isEmailVerified: false,
        authProviders: ['password'],
      );

      await userRepository.create(existingUser);

      // Mock Firebase user with updated data
      final mockUser = MockUser();
      final mockMetadata = MockUserMetadata();
      final mockUserInfo = MockUserInfo();

      when(() => mockUser.uid).thenReturn('firebase-uid');
      when(() => mockUser.email).thenReturn('<EMAIL>');
      when(() => mockUser.displayName).thenReturn('Updated Name');
      when(
        () => mockUser.photoURL,
      ).thenReturn('https://example.com/new-photo.jpg');
      when(() => mockUser.emailVerified).thenReturn(true);
      when(() => mockUser.metadata).thenReturn(mockMetadata);
      when(() => mockUser.providerData).thenReturn([mockUserInfo]);
      when(() => mockMetadata.creationTime).thenReturn(existingUser.createdAt);
      when(() => mockMetadata.lastSignInTime).thenReturn(DateTime.now());
      mockUserInfo.providerId = 'google.com';
      mockUserInfo.uid = 'google-uid';

      await userRepository.createOrUpdateFromFirebaseUser(mockUser);

      final result = await userRepository.getUserById('firebase-uid');
      expect(result, isNotNull);
      expect(result!.email, '<EMAIL>');
      expect(result.displayName, 'Updated Name');
      expect(result.isEmailVerified, true);
      expect(result.authProviders, contains('google.com'));
    });
  });

  group('Profile Management Operations', () {
    late FirebaseTestSetup testSetup;
    late FirestoreService firestoreService;
    late UserRepositoryImpl userRepository;

    setUp(() async {
      testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
        uid: 'test-uid',
        email: '<EMAIL>',
      );
      firestoreService = FirestoreService(testSetup.firestore);
      userRepository = UserRepositoryImpl(firestoreService, testSetup.auth);
    });

    tearDown(() async {
      await testSetup.dispose();
    });

    test('updateDisplayName updates Firebase Auth and Firestore', () async {
      // Use the authenticated user from testSetup
      final currentUid = testSetup.currentUser!.uid;

      // Create user profile first
      final userProfile = UserProfile(
        uid: currentUid,
        email: '<EMAIL>',
        displayName: 'Old Name',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        isEmailVerified: true,
        authProviders: ['password'],
      );
      await userRepository.create(userProfile);

      await userRepository.updateDisplayName('New Display Name');

      // Verify Firestore was updated
      final result = await userRepository.getUserById(currentUid);
      expect(result!.displayName, 'New Display Name');
    });

    test('updateDisplayName throws when no authenticated user', () async {
      // Create a new test setup without authenticated user
      final noAuthTestSetup = await FirebaseTestSetup.create();
      final noAuthUserRepository = UserRepositoryImpl(
        FirestoreService(noAuthTestSetup.firestore),
        noAuthTestSetup.auth,
      );

      expect(
        () => noAuthUserRepository.updateDisplayName('New Name'),
        throwsA(isA<Exception>()),
      );

      await noAuthTestSetup.dispose();
    });

    test('updateEmail updates Firebase Auth and Firestore', () async {
      // Use the authenticated user from testSetup
      final currentUid = testSetup.currentUser!.uid;

      // Create user profile first
      final userProfile = UserProfile(
        uid: currentUid,
        email: '<EMAIL>',
        displayName: 'Test User',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        isEmailVerified: true,
        authProviders: ['password'],
      );
      await userRepository.create(userProfile);

      await userRepository.updateEmail('<EMAIL>');

      // Verify Firestore was updated
      final result = await userRepository.getUserById(currentUid);
      expect(result!.email, '<EMAIL>');
      expect(result.isEmailVerified, false); // Should be reset
    });

    test('updateEmail throws when no authenticated user', () async {
      // Create a new test setup without authenticated user
      final noAuthTestSetup = await FirebaseTestSetup.create();
      final noAuthUserRepository = UserRepositoryImpl(
        FirestoreService(noAuthTestSetup.firestore),
        noAuthTestSetup.auth,
      );

      expect(
        () => noAuthUserRepository.updateEmail('<EMAIL>'),
        throwsA(isA<Exception>()),
      );

      await noAuthTestSetup.dispose();
    });

    test('getCurrentUserProfile returns current user data', () async {
      // Use the authenticated user from testSetup
      final currentUid = testSetup.currentUser!.uid;

      // Create user profile
      final userProfile = UserProfile(
        uid: currentUid,
        email: '<EMAIL>',
        displayName: 'Current User',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        isEmailVerified: true,
        authProviders: ['password'],
      );
      await userRepository.create(userProfile);

      final result = await userRepository.getCurrentUserProfile();
      expect(result, isNotNull);
      expect(result!.uid, currentUid);
      expect(result.email, '<EMAIL>');
    });

    test(
      'getCurrentUserProfile returns null when no authenticated user',
      () async {
        // Create a new test setup without authenticated user
        final noAuthTestSetup = await FirebaseTestSetup.create();
        final noAuthUserRepository = UserRepositoryImpl(
          FirestoreService(noAuthTestSetup.firestore),
          noAuthTestSetup.auth,
        );

        final result = await noAuthUserRepository.getCurrentUserProfile();
        expect(result, isNull);

        await noAuthTestSetup.dispose();
      },
    );

    test('updateUserProfile updates current user profile', () async {
      // Use the authenticated user from testSetup
      final currentUid = testSetup.currentUser!.uid;

      // Create initial profile
      final initialProfile = UserProfile(
        uid: currentUid,
        email: '<EMAIL>',
        displayName: 'Initial Name',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now().subtract(const Duration(hours: 1)),
        isEmailVerified: true,
        authProviders: ['password'],
      );
      await userRepository.create(initialProfile);

      // Update profile
      final updatedProfile = initialProfile.copyWith(
        displayName: 'Updated Name',
        preferences: {'theme': 'dark'},
      );

      await userRepository.updateUserProfile(updatedProfile);

      // Verify update
      final result = await userRepository.getUserById(currentUid);
      expect(result!.displayName, 'Updated Name');
      expect(result.preferences['theme'], 'dark');
      expect(
        result.lastLoginAt?.isAfter(
              initialProfile.lastLoginAt ?? DateTime.now(),
            ) ??
            false,
        true,
      );
    });

    test('updateUserProfile throws when no authenticated user', () async {
      // Create a new test setup without authenticated user
      final noAuthTestSetup = await FirebaseTestSetup.create();
      final noAuthUserRepository = UserRepositoryImpl(
        FirestoreService(noAuthTestSetup.firestore),
        noAuthTestSetup.auth,
      );

      final profile = UserProfile(
        uid: 'test-uid',
        email: '<EMAIL>',
        displayName: 'Test User',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        isEmailVerified: true,
        authProviders: ['password'],
      );

      expect(
        () => noAuthUserRepository.updateUserProfile(profile),
        throwsA(isA<Exception>()),
      );

      await noAuthTestSetup.dispose();
    });

    test('watchUserProfile streams current user changes', () async {
      final userProfile = UserProfile(
        uid: 'watch-uid',
        email: '<EMAIL>',
        displayName: 'Watch User',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        isEmailVerified: true,
        authProviders: ['password'],
      );

      // Create user first
      await userRepository.create(userProfile);

      // Then start watching and get first result
      final stream = userRepository.watchUserProfile('watch-uid');
      final result = await stream.first;

      expect(result, isNotNull);
      expect(result!.uid, 'watch-uid');
    });

    test('userExists returns correct existence status', () async {
      // Initially doesn't exist
      expect(await userRepository.userExists('exists-uid'), false);

      // Create user
      final userProfile = UserProfile(
        uid: 'exists-uid',
        email: '<EMAIL>',
        displayName: 'Exists User',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        isEmailVerified: true,
        authProviders: ['password'],
      );
      await userRepository.create(userProfile);

      // Now exists
      expect(await userRepository.userExists('exists-uid'), true);
    });

    test('deleteUserProfile removes user', () async {
      // Create user
      final userProfile = UserProfile(
        uid: 'delete-uid',
        email: '<EMAIL>',
        displayName: 'Delete User',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        isEmailVerified: true,
        authProviders: ['password'],
      );
      await userRepository.create(userProfile);

      // Verify exists
      expect(await userRepository.userExists('delete-uid'), true);

      // Delete
      await userRepository.deleteUserProfile('delete-uid');

      // Verify deleted
      expect(await userRepository.userExists('delete-uid'), false);
    });
  });
}
