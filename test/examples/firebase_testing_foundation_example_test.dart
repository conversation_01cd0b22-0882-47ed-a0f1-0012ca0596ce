import 'package:flutter_test/flutter_test.dart';

import '../helpers/firebase_test_setup.dart';

/// Example tests demonstrating the new Firebase testing foundation
///
/// This test file showcases the standardized patterns for testing with
/// firebase_auth_mocks + fake_cloud_firestore integration.
///
/// Note: This is a simplified version focusing on core functionality
/// without complex helper methods that may have implementation issues.
void main() {
  group('Firebase Testing Foundation Examples', () {
    group('Basic Firebase Test Setup', () {
      testWidgets('should create authenticated test setup', (tester) async {
        // Create test setup with authenticated user
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser(
          uid: 'test-user-123',
          email: '<EMAIL>',
          displayName: 'Test User',
        );

        // Verify user is authenticated
        expect(setup.isSignedIn, isTrue);
        expect(setup.currentUser?.uid, equals('test-user-123'));
        expect(setup.currentUser?.email, equals('<EMAIL>'));

        // Test basic Firestore operations
        await setup.setTestData('test/doc', {'message': 'Hello World'});
        final doc = await setup.doc('test/doc').get();
        expect(doc.exists, isTrue);
        expect(doc.data(), equals({'message': 'Hello World'}));

        await setup.dispose();
      });

      testWidgets('should create unauthenticated test setup', (tester) async {
        final setup = await FirebaseTestSetup.createUnauthenticated();

        expect(setup.isSignedIn, isFalse);
        expect(setup.currentUser, isNull);

        await setup.dispose();
      });

      test('should handle authentication flows', () async {
        final setup = await FirebaseTestSetup.createUnauthenticated();

        // Initially unauthenticated
        expect(setup.isSignedIn, isFalse);

        // Create a user with email and password (firebase_auth_mocks doesn't support signIn on unauthenticated)
        await setup.createUserWithEmailAndPassword(
          email: '<EMAIL>',
          password: 'password123',
        );

        expect(setup.isSignedIn, isTrue);
        expect(setup.currentUser?.email, equals('<EMAIL>'));

        // Sign out
        await setup.signOut();
        expect(setup.isSignedIn, isFalse);

        await setup.dispose();
      });
    });

    group('Basic Auth Testing Examples', () {
      test('should create authenticated user', () async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser(
          uid: 'test-user-123',
          email: '<EMAIL>',
          displayName: 'Test User',
        );

        expect(setup.isSignedIn, isTrue);
        expect(setup.currentUser?.uid, equals('test-user-123'));
        expect(setup.currentUser?.email, equals('<EMAIL>'));
        expect(setup.currentUser?.displayName, equals('Test User'));

        await setup.dispose();
      });

      test('should create unauthenticated setup', () async {
        final setup = await FirebaseTestSetup.createUnauthenticated();

        expect(setup.isSignedIn, isFalse);
        expect(setup.currentUser, isNull);

        await setup.dispose();
      });
    });

    group('Basic Firestore Testing Examples', () {
      test('should test basic Firestore operations', () async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser(
          uid: 'user-1',
          email: '<EMAIL>',
        );

        // Test user can store and retrieve their own data
        await setup.setTestData('users/user-1/accounts/test-account', {
          'id': 'test-account',
          'userId': 'user-1',
          'name': 'Test Account',
          'balance': 1000,
        });

        final doc = await setup.doc('users/user-1/accounts/test-account').get();
        expect(doc.exists, isTrue);
        expect(doc.data(), isA<Map<String, dynamic>>());

        final data = doc.data()! as Map<String, dynamic>;
        expect(data['name'], equals('Test Account'));
        expect(data['userId'], equals('user-1'));

        await setup.dispose();
      });

      test('should test unauthenticated setup', () async {
        final setup = await FirebaseTestSetup.createUnauthenticated();

        expect(setup.isSignedIn, isFalse);
        expect(setup.currentUser, isNull);

        // Can still use Firestore for testing (without security rules)
        await setup.setTestData('test/doc', {'message': 'test'});
        final doc = await setup.doc('test/doc').get();
        expect(doc.exists, isTrue);

        await setup.dispose();
      });
    });

    group('Performance Testing Examples', () {
      test('should create basic Firebase setup', () async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser(
          uid: 'test-user-456',
          email: '<EMAIL>',
        );

        expect(setup.isSignedIn, isTrue);
        expect(setup.currentUser?.uid, equals('test-user-456'));

        // Manually add some test data
        await setup.setTestData('users/test-user-456', {
          'uid': 'test-user-456',
          'email': '<EMAIL>',
          'displayName': 'Test User',
          'createdAt': DateTime.now().millisecondsSinceEpoch,
        });

        // Verify user document exists
        final userDoc = await setup.doc('users/test-user-456').get();
        expect(userDoc.exists, isTrue);

        await setup.dispose();
      });

      test('should create minimal setup for performance', () async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser();

        expect(setup.isSignedIn, isTrue);

        // Should only have basic auth, no Firestore data initially
        expect(setup.currentUser?.uid, isNotNull);

        await setup.dispose();
      });
    });

    group('Data Setup Examples', () {
      test('should setup basic user data', () async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser();

        // Manually setup basic user data
        await setup.setTestData('users/${setup.currentUser!.uid}', {
          'uid': setup.currentUser!.uid,
          'email': setup.currentUser!.email,
          'displayName': setup.currentUser!.displayName,
          'createdAt': DateTime.now().millisecondsSinceEpoch,
        });

        // Verify user document
        final userDoc = await setup
            .doc('users/${setup.currentUser!.uid}')
            .get();
        expect(userDoc.exists, isTrue);

        await setup.dispose();
      });

      test('should setup multiple accounts', () async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser();

        // Manually setup multiple accounts
        for (var i = 0; i < 3; i++) {
          await setup.setTestData(
            'users/${setup.currentUser!.uid}/accounts/account-$i',
            {
              'id': 'account-$i',
              'userId': setup.currentUser!.uid,
              'name': 'Test Account $i',
              'balance': 1000 + (i * 500),
              'type': 'checking',
            },
          );
        }

        final accountsSnapshot = await setup
            .collection('users/${setup.currentUser!.uid}/accounts')
            .get();
        expect(accountsSnapshot.docs.length, equals(3));

        // Verify account data structure
        final firstAccount =
            accountsSnapshot.docs.first.data()! as Map<String, dynamic>;
        expect(firstAccount['userId'], equals(setup.currentUser!.uid));
        expect(firstAccount['name'], contains('Test Account'));

        await setup.dispose();
      });
    });

    group('Error Handling and Edge Cases', () {
      test('should handle Firestore operations gracefully', () async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser();

        // Test basic Firestore operations
        await setup.setTestData('test/valid-path', {'data': 'test'});
        final doc = await setup.doc('test/valid-path').get();
        expect(doc.exists, isTrue);

        await setup.dispose();
      });

      test('should handle cleanup properly', () async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser();

        // Add some test data
        await setup.setTestData('test/cleanup', {'data': 'test'});

        // Verify data exists
        final doc = await setup.doc('test/cleanup').get();
        expect(doc.exists, isTrue);

        // Dispose should clean up resources
        await setup.dispose();

        // Test passes if no exceptions are thrown during disposal
        expect(true, isTrue);
      });

      test('should provide debugging capabilities', () async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser();

        await setup.setTestData('debug/test', {'message': 'debug data'});

        // Test that we can access the data
        final doc = await setup.doc('debug/test').get();
        expect(doc.exists, isTrue);
        expect(doc.data(), isA<Map<String, dynamic>>());

        await setup.dispose();
      });
    });
  });
}
