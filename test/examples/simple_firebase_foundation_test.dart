import 'package:flutter_test/flutter_test.dart';

import '../helpers/firebase_test_setup.dart';
import '../helpers/test_auth_helper.dart';

/// Simple test demonstrating the new Firebase testing foundation
///
/// This test file showcases basic patterns for testing with
/// firebase_auth_mocks + fake_cloud_firestore integration.
void main() {
  group('Simple Firebase Testing Foundation', () {
    group('Basic Setup Tests', () {
      testWidgets('should create unauthenticated setup', (tester) async {
        final setup = await FirebaseTestSetup.createUnauthenticated();

        expect(setup.isSignedIn, isFalse);
        expect(setup.currentUser, isNull);

        await setup.dispose();
      });

      testWidgets('should create authenticated setup', (tester) async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser(
          uid: 'test-user-123',
          email: '<EMAIL>',
          displayName: 'Test User',
        );

        expect(setup.isSignedIn, isTrue);
        expect(setup.currentUser?.uid, equals('test-user-123'));
        expect(setup.currentUser?.email, equals('<EMAIL>'));
        expect(setup.currentUser?.displayName, equals('Test User'));

        await setup.dispose();
      });

      testWidgets('should handle basic Firestore operations', (tester) async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser();

        // Test basic Firestore operations
        await setup.setTestData('test/doc', {'message': 'Hello World'});
        final doc = await setup.doc('test/doc').get();

        expect(doc.exists, isTrue);
        expect(doc.data(), equals({'message': 'Hello World'}));

        await setup.dispose();
      });

      testWidgets('should handle authentication flows', (tester) async {
        final setup = await FirebaseTestSetup.createUnauthenticated();

        // Initially unauthenticated
        expect(setup.isSignedIn, isFalse);

        // Sign in anonymously (firebase_auth_mocks supports this)
        await setup.signInAnonymously();

        expect(setup.isSignedIn, isTrue);
        expect(setup.currentUser?.isAnonymous, isTrue);

        // Sign out
        await setup.signOut();
        expect(setup.isSignedIn, isFalse);

        await setup.dispose();
      });
    });

    group('Test Auth Helper Tests', () {
      testWidgets('should create different auth instances', (tester) async {
        // Create authenticated auth
        final authenticatedAuth = TestAuthHelper.createAuthenticatedMockAuth();
        expect(AuthTestUtils.isUserSignedIn(authenticatedAuth), isTrue);

        // Create unauthenticated auth
        final unauthenticatedAuth =
            TestAuthHelper.createUnauthenticatedMockAuth();
        expect(AuthTestUtils.isUserSignedIn(unauthenticatedAuth), isFalse);

        // Create anonymous auth
        final anonymousAuth = TestAuthHelper.createAnonymousMockAuth();
        expect(AuthTestUtils.isAnonymousUser(anonymousAuth), isTrue);
      });

      testWidgets('should handle auth scenarios', (tester) async {
        final auth = TestAuthHelper.createUnauthenticatedMockAuth();

        // Test anonymous sign in
        await AuthTestScenarios.simulateAnonymousSignIn(auth);
        expect(AuthTestUtils.isUserSignedIn(auth), isTrue);
        expect(AuthTestUtils.isAnonymousUser(auth), isTrue);

        // Test sign out
        await AuthTestScenarios.simulateSignOut(auth);
        expect(AuthTestUtils.isUserSignedIn(auth), isFalse);
      });
    });

    group('Firebase Test Scenarios', () {
      testWidgets('should setup basic user data', (tester) async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser();

        await FirebaseTestScenarios.setupBasicUserData(setup);

        // Verify user document
        final userDoc = await setup
            .doc('users/${setup.currentUser!.uid}')
            .get();
        expect(userDoc.exists, isTrue);

        // Verify account document
        final accountDoc = await setup
            .doc('users/${setup.currentUser!.uid}/accounts/test-account-id')
            .get();
        expect(accountDoc.exists, isTrue);

        // Verify category document
        final categoryDoc = await setup
            .doc('users/${setup.currentUser!.uid}/categories/test-category-id')
            .get();
        expect(categoryDoc.exists, isTrue);

        await setup.dispose();
      });

      testWidgets('should setup multiple accounts', (tester) async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser();

        await FirebaseTestScenarios.setupMultipleAccounts(setup, count: 3);

        final accountsSnapshot = await setup
            .collection('users/${setup.currentUser!.uid}/accounts')
            .get();
        expect(accountsSnapshot.docs.length, equals(3));

        // Verify account data structure
        final firstAccount =
            accountsSnapshot.docs.first.data()! as Map<String, dynamic>;
        expect(firstAccount['userId'], equals(setup.currentUser!.uid));
        expect(firstAccount['name'], equals('Test Account 0'));

        await setup.dispose();
      });

      testWidgets('should setup test transactions', (tester) async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser();

        await FirebaseTestScenarios.setupTestTransactions(setup, count: 5);

        final transactionsSnapshot = await setup
            .collection('users/${setup.currentUser!.uid}/transactions')
            .get();
        expect(transactionsSnapshot.docs.length, equals(5));

        // Verify transaction data structure
        final firstTransaction =
            transactionsSnapshot.docs.first.data()! as Map<String, dynamic>;
        expect(firstTransaction['userId'], equals(setup.currentUser!.uid));
        expect(firstTransaction['description'], equals('Test Transaction 0'));

        await setup.dispose();
      });
    });

    group('Data Factory Integration', () {
      testWidgets('should create test data with factory', (tester) async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser();

        // Create test data using the factory
        final userData = FirebaseTestDataFactory.createUserData(
          uid: setup.currentUser!.uid,
        );
        await setup.setTestData('users/${setup.currentUser!.uid}', userData);

        final accountData = FirebaseTestDataFactory.createAccountData(
          userId: setup.currentUser!.uid,
        );
        await setup.setTestData(
          'users/${setup.currentUser!.uid}/accounts/test-account',
          accountData,
        );

        // Verify data was created
        final userDoc = await setup
            .doc('users/${setup.currentUser!.uid}')
            .get();
        expect(userDoc.exists, isTrue);
        final userDocData = userDoc.data() as Map<String, dynamic>?;
        expect(userDocData?['uid'], equals(setup.currentUser!.uid));

        final accountDoc = await setup
            .doc('users/${setup.currentUser!.uid}/accounts/test-account')
            .get();
        expect(accountDoc.exists, isTrue);
        final accountDocData = accountDoc.data() as Map<String, dynamic>?;
        expect(accountDocData?['userId'], equals(setup.currentUser!.uid));

        await setup.dispose();
      });
    });

    group('Error Handling', () {
      testWidgets('should handle cleanup properly', (tester) async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser();

        // Add some test data
        await setup.setTestData('test/cleanup', {'data': 'test'});

        // Verify data exists
        final doc = await setup.doc('test/cleanup').get();
        expect(doc.exists, isTrue);

        // Dispose should not throw
        expect(setup.dispose, returnsNormally);
      });

      testWidgets('should handle invalid document paths gracefully', (
        tester,
      ) async {
        final setup = await FirebaseTestSetup.createWithAuthenticatedUser();

        // Test with invalid document path should throw
        // Note: fake_cloud_firestore throws AssertionError, not ArgumentError
        expect(() => setup.doc(''), throwsA(isA<AssertionError>()));

        await setup.dispose();
      });
    });
  });
}
