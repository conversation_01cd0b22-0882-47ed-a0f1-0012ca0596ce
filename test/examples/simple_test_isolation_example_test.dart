import 'dart:async';

import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../helpers/mock_provider_manager.dart';
import '../helpers/test_isolation_helper.dart';

// Example mock for demonstration
class MockExampleService extends Mock {
  Future<String> getData();
}

// Simple test to validate the framework works
void main() {
  group('Simple Test Isolation Validation', () {
    test('should create and dispose test isolation helper', () async {
      final testEnv = await TestIsolationHelper.create(
        testName: 'simple-validation-test',
        withFirebase: false, // Skip Firebase for this simple test
      );

      expect(testEnv.testId, isNotEmpty);
      expect(testEnv.container, isNotNull);

      // Should dispose without errors
      await testEnv.dispose();

      expect(true, isTrue);
    });

    test('should manage mock state properly', () async {
      final mockManager = MockProviderManager.create('mock-validation-test');
      final mockService = mockManager.getMock(MockExampleService.new);

      when(mockService.getData).thenAnswer((_) async => 'test-data');

      final result = await mockService.getData();
      expect(result, equals('test-data'));

      verify(mockService.getData).called(1);

      mockManager.dispose();
    });

    test('should handle stream cleanup', () async {
      final testEnv = await TestIsolationHelper.create(
        testName: 'stream-validation-test',
        withFirebase: false,
      );

      final streamController = StreamController<String>();
      final subscription = streamController.stream.listen((_) {});

      testEnv.addSubscription(subscription);

      await testEnv.dispose();
      await streamController.close();

      expect(true, isTrue);
    });
  });

  tearDownAll(() async {
    await TestIsolationHelper.disposeAll();
    MockProviderManager.disposeAll();
  });
}
