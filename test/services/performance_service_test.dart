import 'package:budapp/services/performance_service.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  // Note: Firebase Performance requires native platform channels and can't be
  // properly mocked in unit tests. These tests focus on the service behavior
  // and error handling when Firebase Performance is not available.

  group('PerformanceService Tests', () {
    group('Initialization', () {
      test('should initialize performance collection successfully', () async {
        // Act & Assert - Should complete without throwing
        await expectLater(PerformanceService.initialize(), completes);
      });
    });

    group('Trace Management', () {
      group('Start Trace', () {
        test('should start a new trace successfully', () async {
          // Arrange
          const traceName = 'test_trace';

          // Act & Assert - Should complete without throwing
          await expectLater(
            PerformanceService.startTrace(traceName),
            completes,
          );
        });

        test('should handle starting same trace multiple times', () async {
          // Arrange
          const traceName = 'duplicate_trace';

          // Act - Start same trace twice
          await PerformanceService.startTrace(traceName);

          // Assert - Second start should complete gracefully
          await expectLater(
            PerformanceService.startTrace(traceName),
            completes,
          );
        });

        test('should handle trace start with empty name', () async {
          // Act & Assert - Should complete without throwing
          await expectLater(PerformanceService.startTrace(''), completes);
        });
      });

      group('Stop Trace', () {
        test('should stop active trace successfully', () async {
          // Arrange
          const traceName = 'active_trace';
          await PerformanceService.startTrace(traceName);

          // Act & Assert - Should complete without throwing
          await expectLater(PerformanceService.stopTrace(traceName), completes);
        });

        test('should handle stopping non-existent trace gracefully', () async {
          // Arrange
          const traceName = 'non_existent_trace';

          // Act & Assert - Should complete without throwing
          await expectLater(PerformanceService.stopTrace(traceName), completes);
        });

        test('should handle stopping trace with empty name', () async {
          // Act & Assert - Should complete without throwing
          await expectLater(PerformanceService.stopTrace(''), completes);
        });
      });
    });

    group('Trace Metrics', () {
      group('Set Trace Metric', () {
        test('should set metric for trace', () async {
          // Arrange
          const traceName = 'test_trace';
          const metricName = 'test_metric';
          const value = 42;
          await PerformanceService.startTrace(traceName);

          // Act & Assert - Should complete without throwing
          await expectLater(
            PerformanceService.setTraceMetric(traceName, metricName, value),
            completes,
          );
        });

        test('should handle setting metric for non-existent trace', () async {
          // Arrange
          const traceName = 'non_existent_trace';
          const metricName = 'test_metric';
          const value = 42;

          // Act & Assert - Should complete without throwing
          await expectLater(
            PerformanceService.setTraceMetric(traceName, metricName, value),
            completes,
          );
        });

        test('should handle setting metric with zero value', () async {
          // Arrange
          const traceName = 'zero_metric_trace';
          const metricName = 'zero_metric';
          const value = 0;
          await PerformanceService.startTrace(traceName);

          // Act & Assert - Should complete without throwing
          await expectLater(
            PerformanceService.setTraceMetric(traceName, metricName, value),
            completes,
          );
        });

        test('should handle setting metric with negative value', () async {
          // Arrange
          const traceName = 'negative_metric_trace';
          const metricName = 'negative_metric';
          const value = -10;
          await PerformanceService.startTrace(traceName);

          // Act & Assert - Should complete without throwing
          await expectLater(
            PerformanceService.setTraceMetric(traceName, metricName, value),
            completes,
          );
        });

        test('should handle setting metric with empty names', () async {
          // Act & Assert - Should complete without throwing
          await expectLater(
            PerformanceService.setTraceMetric('', '', 1),
            completes,
          );
        });
      });

      group('Increment Trace Metric', () {
        test('should increment metric with default value', () async {
          // Arrange
          const traceName = 'increment_trace';
          const metricName = 'counter_metric';
          await PerformanceService.startTrace(traceName);

          // Act & Assert - Should complete without throwing
          await expectLater(
            PerformanceService.incrementTraceMetric(traceName, metricName),
            completes,
          );
        });

        test('should increment metric with custom value', () async {
          // Arrange
          const traceName = 'increment_trace';
          const metricName = 'counter_metric';
          const incrementValue = 5;
          await PerformanceService.startTrace(traceName);

          // Act & Assert - Should complete without throwing
          await expectLater(
            PerformanceService.incrementTraceMetric(
              traceName,
              metricName,
              incrementValue,
            ),
            completes,
          );
        });

        test('should increment metric with zero value', () async {
          // Arrange
          const traceName = 'zero_increment_trace';
          const metricName = 'zero_counter';
          const incrementValue = 0;
          await PerformanceService.startTrace(traceName);

          // Act & Assert - Should complete without throwing
          await expectLater(
            PerformanceService.incrementTraceMetric(
              traceName,
              metricName,
              incrementValue,
            ),
            completes,
          );
        });

        test('should increment metric with negative value', () async {
          // Arrange
          const traceName = 'negative_increment_trace';
          const metricName = 'negative_counter';
          const incrementValue = -3;
          await PerformanceService.startTrace(traceName);

          // Act & Assert - Should complete without throwing
          await expectLater(
            PerformanceService.incrementTraceMetric(
              traceName,
              metricName,
              incrementValue,
            ),
            completes,
          );
        });

        test(
          'should handle incrementing metric for non-existent trace',
          () async {
            // Arrange
            const traceName = 'non_existent_trace';
            const metricName = 'counter_metric';

            // Act & Assert - Should complete without throwing
            await expectLater(
              PerformanceService.incrementTraceMetric(traceName, metricName),
              completes,
            );
          },
        );
      });
    });

    group('Trace Attributes', () {
      test('should set attribute for active trace', () async {
        // Arrange
        const traceName = 'attribute_trace';
        const attributeName = 'test_attribute';
        const value = 'test_value';
        await PerformanceService.startTrace(traceName);

        // Act & Assert - Should complete without throwing
        await expectLater(
          PerformanceService.setTraceAttribute(traceName, attributeName, value),
          completes,
        );
      });

      test('should handle setting attribute for non-existent trace', () async {
        // Arrange
        const traceName = 'non_existent_trace';
        const attributeName = 'test_attribute';
        const value = 'test_value';

        // Act & Assert - Should complete without throwing
        await expectLater(
          PerformanceService.setTraceAttribute(traceName, attributeName, value),
          completes,
        );
      });

      test('should handle setting attribute with empty value', () async {
        // Arrange
        const traceName = 'empty_attr_trace';
        const attributeName = 'empty_attribute';
        const value = '';
        await PerformanceService.startTrace(traceName);

        // Act & Assert - Should complete without throwing
        await expectLater(
          PerformanceService.setTraceAttribute(traceName, attributeName, value),
          completes,
        );
      });

      test('should handle setting attribute with empty names', () async {
        // Act & Assert - Should complete without throwing
        await expectLater(
          PerformanceService.setTraceAttribute('', '', 'value'),
          completes,
        );
      });

      test(
        'should handle setting multiple attributes for same trace',
        () async {
          // Arrange
          const traceName = 'multi_attr_trace';
          await PerformanceService.startTrace(traceName);

          // Act - Set multiple attributes
          await PerformanceService.setTraceAttribute(
            traceName,
            'attr1',
            'value1',
          );
          await PerformanceService.setTraceAttribute(
            traceName,
            'attr2',
            'value2',
          );
          await PerformanceService.setTraceAttribute(
            traceName,
            'attr3',
            'value3',
          );

          // Assert - All operations should complete without throwing
          expect(true, isTrue); // Test completed successfully
        },
      );
    });

    group(
      'HTTP Metrics',
      skip:
          'Firebase Performance requires native platform channels not available in unit tests',
      () {
        // Skip HTTP Metrics tests as they require Firebase Performance initialization
        // which is not available in unit test environment
        test('should create HTTP metric with GET method', () {
          // Arrange
          const url = 'https://api.example.com/data';
          const httpMethod = HttpMethod.Get;

          // Act & Assert - Should return HttpMetric instance
          final result = PerformanceService.newHttpMetric(url, httpMethod);
          expect(result, isA<HttpMetric>());
        });

        test('should create HTTP metric with POST method', () {
          // Arrange
          const url = 'https://api.example.com/submit';
          const httpMethod = HttpMethod.Post;

          // Act & Assert - Should return HttpMetric instance
          final result = PerformanceService.newHttpMetric(url, httpMethod);
          expect(result, isA<HttpMetric>());
        });

        test('should create HTTP metric with PUT method', () {
          // Arrange
          const url = 'https://api.example.com/update';
          const httpMethod = HttpMethod.Put;

          // Act & Assert - Should return HttpMetric instance
          final result = PerformanceService.newHttpMetric(url, httpMethod);
          expect(result, isA<HttpMetric>());
        });

        test('should create HTTP metric with DELETE method', () {
          // Arrange
          const url = 'https://api.example.com/delete';
          const httpMethod = HttpMethod.Delete;

          // Act & Assert - Should return HttpMetric instance
          final result = PerformanceService.newHttpMetric(url, httpMethod);
          expect(result, isA<HttpMetric>());
        });

        test('should create HTTP metric with PATCH method', () {
          // Arrange
          const url = 'https://api.example.com/patch';
          const httpMethod = HttpMethod.Patch;

          // Act & Assert - Should return HttpMetric instance
          final result = PerformanceService.newHttpMetric(url, httpMethod);
          expect(result, isA<HttpMetric>());
        });

        test('should handle different URL formats', () {
          // Arrange
          const urls = [
            'https://api.example.com',
            'https://api.example.com/',
            'https://api.example.com/path',
            'https://api.example.com/path/to/resource',
            'https://api.example.com/path?query=value',
            'https://subdomain.example.com/api/v1/resource',
          ];

          // Act & Assert - All URLs should work
          for (final url in urls) {
            final result = PerformanceService.newHttpMetric(
              url,
              HttpMethod.Get,
            );
            expect(result, isA<HttpMetric>());
          }
        });
      },
    );

    group('Convenience Methods', () {
      group('App Startup Tracking', () {
        test('should start and complete app startup trace', () async {
          // Act - Start app startup tracking
          await expectLater(PerformanceService.trackAppStartup(), completes);

          // Act - Complete app startup tracking
          await expectLater(PerformanceService.completeAppStartup(), completes);
        });

        test('should handle multiple app startup calls', () async {
          // Act - Multiple startup calls should be handled gracefully
          await PerformanceService.trackAppStartup();
          await expectLater(PerformanceService.trackAppStartup(), completes);

          await expectLater(PerformanceService.completeAppStartup(), completes);
        });
      });

      group('Screen Load Tracking', () {
        test('should track screen load with screen name', () async {
          // Arrange
          const screenName = 'HomeScreen';

          // Act & Assert
          await expectLater(
            PerformanceService.trackScreenLoad(screenName),
            completes,
          );

          await expectLater(
            PerformanceService.completeScreenLoad(screenName),
            completes,
          );
        });

        test('should handle different screen names', () async {
          // Arrange
          const screenNames = [
            'HomeScreen',
            'LoginScreen',
            'ProfileScreen',
            'TransactionsScreen',
            'BudgetsScreen',
            'CategoriesScreen',
          ];

          // Act & Assert
          for (final screenName in screenNames) {
            await expectLater(
              PerformanceService.trackScreenLoad(screenName),
              completes,
            );
            await expectLater(
              PerformanceService.completeScreenLoad(screenName),
              completes,
            );
          }
        });

        test('should handle empty screen name', () async {
          // Act & Assert
          await expectLater(PerformanceService.trackScreenLoad(''), completes);

          await expectLater(
            PerformanceService.completeScreenLoad(''),
            completes,
          );
        });

        test('should handle screen names with special characters', () async {
          // Arrange
          const specialScreenNames = [
            'Screen_With_Underscores',
            'Screen-With-Dashes',
            'Screen With Spaces',
            'Screen123WithNumbers',
            'ScreenWithUnicodeé',
          ];

          // Act & Assert
          for (final screenName in specialScreenNames) {
            await expectLater(
              PerformanceService.trackScreenLoad(screenName),
              completes,
            );
            await expectLater(
              PerformanceService.completeScreenLoad(screenName),
              completes,
            );
          }
        });
      });

      group('Auth Flow Tracking', () {
        test('should track auth flow with flow type', () async {
          // Arrange
          const flowType = 'google_signin';

          // Act & Assert
          await expectLater(
            PerformanceService.trackAuthFlow(flowType),
            completes,
          );

          await expectLater(
            PerformanceService.completeAuthFlow(flowType),
            completes,
          );
        });

        test('should handle different auth flow types', () async {
          // Arrange
          const flowTypes = [
            'email_password',
            'google_signin',
            'apple_signin',
            'biometric_auth',
            'password_reset',
            'account_creation',
          ];

          // Act & Assert
          for (final flowType in flowTypes) {
            await expectLater(
              PerformanceService.trackAuthFlow(flowType),
              completes,
            );
            await expectLater(
              PerformanceService.completeAuthFlow(flowType),
              completes,
            );
          }
        });

        test('should handle empty flow type', () async {
          // Act & Assert
          await expectLater(PerformanceService.trackAuthFlow(''), completes);

          await expectLater(PerformanceService.completeAuthFlow(''), completes);
        });
      });
    });

    group('Constants and Static Values', () {
      test('should have expected trace name constants', () {
        // Assert
        expect(PerformanceService.appStartupTrace, equals('app_startup'));
        expect(PerformanceService.authInitTrace, equals('auth_initialization'));
        expect(
          PerformanceService.firestoreInitTrace,
          equals('firestore_initialization'),
        );
        expect(
          PerformanceService.remoteConfigTrace,
          equals('remote_config_fetch'),
        );
        expect(PerformanceService.userLoginTrace, equals('user_login'));
        expect(PerformanceService.dataLoadTrace, equals('data_load'));
        expect(PerformanceService.screenLoadTrace, equals('screen_load'));
      });

      test('should have non-empty trace name constants', () {
        // Arrange
        final constants = [
          PerformanceService.appStartupTrace,
          PerformanceService.authInitTrace,
          PerformanceService.firestoreInitTrace,
          PerformanceService.remoteConfigTrace,
          PerformanceService.userLoginTrace,
          PerformanceService.dataLoadTrace,
          PerformanceService.screenLoadTrace,
        ];

        // Assert - All constants should be non-empty
        for (final constant in constants) {
          expect(constant, isNotEmpty);
          expect(constant.trim(), isNotEmpty);
        }
      });

      test('should have unique trace name constants', () {
        // Arrange
        final constants = [
          PerformanceService.appStartupTrace,
          PerformanceService.authInitTrace,
          PerformanceService.firestoreInitTrace,
          PerformanceService.remoteConfigTrace,
          PerformanceService.userLoginTrace,
          PerformanceService.dataLoadTrace,
          PerformanceService.screenLoadTrace,
        ];

        // Assert - All constants should be unique
        final uniqueConstants = constants.toSet();
        expect(uniqueConstants.length, equals(constants.length));
      });

      test('should have properly formatted trace name constants', () {
        // Arrange
        final constants = [
          PerformanceService.appStartupTrace,
          PerformanceService.authInitTrace,
          PerformanceService.firestoreInitTrace,
          PerformanceService.remoteConfigTrace,
          PerformanceService.userLoginTrace,
          PerformanceService.dataLoadTrace,
          PerformanceService.screenLoadTrace,
        ];

        // Assert - All constants should follow naming convention (lowercase with underscores)
        for (final constant in constants) {
          expect(constant, matches(RegExp(r'^[a-z][a-z_]*[a-z]$')));
          expect(constant, isNot(contains(' '))); // No spaces
          expect(constant, isNot(contains('-'))); // No dashes
        }
      });
    });

    group('Complex Integration Scenarios', () {
      test('should handle full trace lifecycle', () async {
        // Arrange
        const traceName = 'lifecycle_trace';
        const metricName = 'test_metric';
        const attributeName = 'test_attr';

        // Act - Full lifecycle
        await PerformanceService.startTrace(traceName);
        await PerformanceService.setTraceMetric(traceName, metricName, 10);
        await PerformanceService.incrementTraceMetric(traceName, metricName, 5);
        await PerformanceService.setTraceAttribute(
          traceName,
          attributeName,
          'test',
        );

        // Assert - Should complete without throwing
        await expectLater(PerformanceService.stopTrace(traceName), completes);
      });

      test('should handle multiple concurrent traces', () async {
        // Arrange
        const traceNames = [
          'concurrent_trace_1',
          'concurrent_trace_2',
          'concurrent_trace_3',
        ];

        // Act - Start multiple traces concurrently
        await Future.wait(traceNames.map(PerformanceService.startTrace));

        // Act - Perform operations on all traces
        for (final traceName in traceNames) {
          await PerformanceService.setTraceMetric(traceName, 'metric', 1);
          await PerformanceService.setTraceAttribute(
            traceName,
            'attr',
            'value',
          );
        }

        // Act - Stop all traces
        await Future.wait(traceNames.map(PerformanceService.stopTrace));

        // Assert - All operations should complete successfully
        expect(true, isTrue);
      });

      test('should handle rapid trace creation and destruction', () async {
        // Arrange
        const baseTraceName = 'rapid_trace';

        // Act - Rapid create/destroy cycle
        for (var i = 0; i < 10; i++) {
          final traceName = '${baseTraceName}_$i';
          await PerformanceService.startTrace(traceName);
          await PerformanceService.setTraceMetric(traceName, 'counter', i);
          await PerformanceService.stopTrace(traceName);
        }

        // Assert - All operations should complete successfully
        expect(true, isTrue);
      });

      test('should handle mixed convenience method calls', () async {
        // Act - Mixed convenience method usage
        await PerformanceService.trackAppStartup();
        await PerformanceService.trackScreenLoad('HomeScreen');
        await PerformanceService.trackAuthFlow('google_signin');

        await PerformanceService.completeScreenLoad('HomeScreen');
        await PerformanceService.completeAuthFlow('google_signin');
        await PerformanceService.completeAppStartup();

        // Assert - All operations should complete successfully
        expect(true, isTrue);
      });
    });

    group('Error Resilience', () {
      test('should be resilient to exception scenarios', () async {
        // This test ensures that the service handles Firebase Performance exceptions gracefully
        // when Firebase Performance is not available or configured incorrectly

        final testOperations = [
          PerformanceService.initialize,
          () => PerformanceService.startTrace('resilience_trace'),
          () => PerformanceService.setTraceMetric(
            'resilience_trace',
            'metric',
            1,
          ),
          () => PerformanceService.incrementTraceMetric(
            'resilience_trace',
            'metric',
          ),
          () => PerformanceService.setTraceAttribute(
            'resilience_trace',
            'attr',
            'value',
          ),
          () => PerformanceService.stopTrace('resilience_trace'),
          PerformanceService.trackAppStartup,
          PerformanceService.completeAppStartup,
          () => PerformanceService.trackScreenLoad('TestScreen'),
          () => PerformanceService.completeScreenLoad('TestScreen'),
          () => PerformanceService.trackAuthFlow('test_auth'),
          () => PerformanceService.completeAuthFlow('test_auth'),
        ];

        // Act & Assert - All operations should complete without throwing
        for (final operation in testOperations) {
          await expectLater(operation(), completes);
        }
      });

      test('should handle operations on stopped traces gracefully', () async {
        // Arrange
        const traceName = 'stopped_trace';
        await PerformanceService.startTrace(traceName);
        await PerformanceService.stopTrace(traceName);

        // Act & Assert - Operations on stopped trace should not throw
        await expectLater(
          PerformanceService.setTraceMetric(traceName, 'metric', 1),
          completes,
        );
        await expectLater(
          PerformanceService.incrementTraceMetric(traceName, 'metric'),
          completes,
        );
        await expectLater(
          PerformanceService.setTraceAttribute(traceName, 'attr', 'value'),
          completes,
        );
      });

      test('should handle extreme values gracefully', () async {
        // Arrange
        const traceName = 'extreme_values_trace';
        await PerformanceService.startTrace(traceName);

        // Act & Assert - Extreme values should be handled gracefully
        await expectLater(
          PerformanceService.setTraceMetric(
            traceName,
            'max_int',
            9223372036854775807,
          ),
          completes,
        );
        await expectLater(
          PerformanceService.setTraceMetric(
            traceName,
            'min_int',
            -9223372036854775808,
          ),
          completes,
        );
        await expectLater(
          PerformanceService.incrementTraceMetric(
            traceName,
            'large_increment',
            1000000,
          ),
          completes,
        );
        await expectLater(
          PerformanceService.setTraceAttribute(
            traceName,
            'long_value',
            'x' * 1000,
          ),
          completes,
        );
      });
    });
  });
}
