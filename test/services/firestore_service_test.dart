import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';

import '../helpers/firebase_test_setup.dart';

void main() {
  group('FirestoreService Tests', () {
    late FirestoreService firestoreService;
    late FirebaseTestSetup testSetup;
    late FakeFirebaseFirestore fakeFirestore;

    setUp(() async {
      testSetup = await FirebaseTestSetup.create();
      fakeFirestore = testSetup.firestore;
      firestoreService = FirestoreService(fakeFirestore);
    });

    tearDown(() async {
      await testSetup.dispose();
    });

    group('Constructor and Instance Access', () {
      test('should initialize with provided Firestore instance', () {
        // Arrange & Act
        final service = FirestoreService(fakeFirestore);

        // Assert
        expect(service.instance, equals(fakeFirestore));
        expect(service.instance, isA<FirebaseFirestore>());
      });

      test('should provide access to underlying Firestore instance', () {
        // Act
        final instance = firestoreService.instance;

        // Assert
        expect(instance, equals(fakeFirestore));
        expect(instance, isA<FakeFirebaseFirestore>());
      });
    });

    group('Collection and Document References', () {
      test('should return collection reference for given path', () {
        // Arrange
        const collectionPath = 'users';

        // Act
        final collectionRef = firestoreService.collection(collectionPath);

        // Assert
        expect(collectionRef, isA<CollectionReference<Map<String, dynamic>>>());
        expect(collectionRef.path, equals(collectionPath));
      });

      test('should return document reference for given path', () {
        // Arrange
        const documentPath = 'users/test-user';

        // Act
        final documentRef = firestoreService.doc(documentPath);

        // Assert
        expect(documentRef, isA<DocumentReference<Map<String, dynamic>>>());
        expect(documentRef.path, equals(documentPath));
      });

      test('should handle nested collection paths', () {
        // Arrange
        const nestedPath = 'users/test-user/accounts';

        // Act
        final collectionRef = firestoreService.collection(nestedPath);

        // Assert
        expect(collectionRef.path, equals(nestedPath));
      });

      test('should handle nested document paths', () {
        // Arrange
        const nestedPath = 'users/test-user/accounts/account-1';

        // Act
        final documentRef = firestoreService.doc(nestedPath);

        // Assert
        expect(documentRef.path, equals(nestedPath));
      });
    });

    group('Batch Operations', () {
      test('should create WriteBatch instance', () {
        // Act
        final batch = firestoreService.batch();

        // Assert
        expect(batch, isA<WriteBatch>());
      });

      test('should allow batch operations to be executed', () async {
        // Arrange
        final batch = firestoreService.batch();
        const docPath = 'test/batch-doc';
        const testData = {'name': 'batch test', 'value': 123};

        // Act
        batch.set(firestoreService.doc(docPath), testData);
        await batch.commit();

        // Assert
        final doc = await firestoreService.doc(docPath).get();
        expect(doc.exists, isTrue);
        expect(doc.data(), equals(testData));
      });
    });

    group('Transaction Operations', () {
      test('should execute transaction with default timeout', () async {
        // Arrange
        const docPath = 'test/transaction-doc';
        const initialData = {'counter': 0};
        await firestoreService.doc(docPath).set(initialData);

        // Act
        final result = await firestoreService.runTransaction<int>((
          transaction,
        ) async {
          final doc = await transaction.get(firestoreService.doc(docPath));
          final currentCounter = doc.data()?['counter'] as int? ?? 0;
          final newCounter = currentCounter + 1;

          transaction.update(firestoreService.doc(docPath), {
            'counter': newCounter,
          });

          return newCounter;
        });

        // Assert
        expect(result, equals(1));
        final finalDoc = await firestoreService.doc(docPath).get();
        expect(finalDoc.data()?['counter'], equals(1));
      });

      test('should execute transaction with custom timeout', () async {
        // Arrange
        const docPath = 'test/transaction-timeout-doc';
        const testData = {'name': 'timeout test'};
        const customTimeout = Duration(seconds: 10);

        // Act
        final result = await firestoreService.runTransaction<String>((
          transaction,
        ) async {
          transaction.set(firestoreService.doc(docPath), testData);
          return 'success';
        }, timeout: customTimeout);

        // Assert
        expect(result, equals('success'));
        final doc = await firestoreService.doc(docPath).get();
        expect(doc.exists, isTrue);
        expect(doc.data(), equals(testData));
      });

      test('should handle transaction failures gracefully', () async {
        // Arrange & Act & Assert
        await expectLater(
          () => firestoreService.runTransaction<void>((transaction) async {
            throw Exception('Transaction test failure');
          }),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('Connectivity Testing', () {
      test('should test connectivity successfully', () async {
        // Act
        final result = await firestoreService.testConnectivity();

        // Assert
        expect(result, isA<Map<String, dynamic>>());
        expect(result['status'], equals('connected'));
        expect(result['canWrite'], isTrue);
        expect(result['canRead'], isTrue);
        expect(result['serverTimestamp'], isNotNull);

        // Verify test document was cleaned up
        final testDoc = await firestoreService
            .collection('_test')
            .doc('connectivity')
            .get();
        expect(testDoc.exists, isFalse);
      });

      test('should handle connectivity test errors gracefully', () async {
        // Arrange - Create a service with null Firestore to trigger error
        final mockFirestore = _MockFirestoreForErrors();
        final errorService = FirestoreService(mockFirestore);

        // Act
        final result = await errorService.testConnectivity();

        // Assert
        expect(result, isA<Map<String, dynamic>>());
        expect(result['status'], equals('error'));
        expect(result['error'], isNotNull);
        expect(result['error'], contains('Test error'));
      });
    });

    group('Network Control Methods', () {
      test('should handle enableNetwork errors gracefully', () async {
        // Arrange
        final mockFirestore = _MockFirestoreForErrors();
        final errorService = FirestoreService(mockFirestore);

        // Act & Assert
        await expectLater(
          errorService.enableNetwork(),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle disableNetwork errors gracefully', () async {
        // Arrange
        final mockFirestore = _MockFirestoreForErrors();
        final errorService = FirestoreService(mockFirestore);

        // Act & Assert
        await expectLater(
          errorService.disableNetwork(),
          throwsA(isA<Exception>()),
        );
      });

      // Note: FakeFirebaseFirestore has limited support for network methods
      // The actual network control methods (enableNetwork/disableNetwork) are tested
      // through error handling scenarios above since they don't properly work with
      // FakeFirebaseFirestore. In production, these methods delegate to the real
      // Firestore instance and work correctly.
    });

    group('Persistence and Lifecycle Methods', () {
      test('should handle clearPersistence errors gracefully', () async {
        // Arrange
        final mockFirestore = _MockFirestoreForErrors();
        final errorService = FirestoreService(mockFirestore);

        // Act & Assert
        await expectLater(
          errorService.clearPersistence,
          throwsA(isA<Exception>()),
        );
      });

      test('should handle terminate errors gracefully', () async {
        // Arrange
        final mockFirestore = _MockFirestoreForErrors();
        final errorService = FirestoreService(mockFirestore);

        // Act & Assert
        await expectLater(errorService.terminate, throwsA(isA<Exception>()));
      });

      test('should handle waitForPendingWrites errors gracefully', () async {
        // Arrange
        final mockFirestore = _MockFirestoreForErrors();
        final errorService = FirestoreService(mockFirestore);

        // Act & Assert
        await expectLater(
          errorService.waitForPendingWrites,
          throwsA(isA<Exception>()),
        );
      });

      // Note: FakeFirebaseFirestore has limited support for persistence and lifecycle methods
      // The actual methods (clearPersistence/terminate/waitForPendingWrites) are tested
      // through error handling scenarios above since they don't properly work with
      // FakeFirebaseFirestore. In production, these methods delegate to the real
      // Firestore instance and work correctly.
    });

    group('Settings Management', () {
      test('should handle configureSettings errors gracefully', () {
        // Arrange
        final mockFirestore = _MockFirestoreForErrors();
        final errorService = FirestoreService(mockFirestore);
        const testSettings = Settings(persistenceEnabled: false);

        // Act & Assert
        expect(
          () => errorService.configureSettings(testSettings),
          throwsA(isA<Exception>()),
        );
      });

      // Note: FakeFirebaseFirestore has limited support for settings management
      // The settings getter and setter are tested through error handling scenarios
      // above since they don't properly work with FakeFirebaseFirestore.
      // In production, these methods delegate to the real Firestore instance.
    });

    group('Integration Testing', () {
      test('should support full CRUD workflow through service', () async {
        // Arrange
        const collectionPath = 'integration-test';
        const docId = 'test-document';
        const initialData = {
          'name': 'Integration Test',
          'value': 42,
          'active': true,
        };
        const updatedData = {
          'name': 'Updated Integration Test',
          'value': 84,
          'active': false,
        };

        // Act - Create
        await firestoreService
            .collection(collectionPath)
            .doc(docId)
            .set(initialData);

        // Assert - Create
        var doc = await firestoreService
            .collection(collectionPath)
            .doc(docId)
            .get();
        expect(doc.exists, isTrue);
        expect(doc.data(), equals(initialData));

        // Act - Update
        await firestoreService
            .collection(collectionPath)
            .doc(docId)
            .update(updatedData);

        // Assert - Update
        doc = await firestoreService
            .collection(collectionPath)
            .doc(docId)
            .get();
        expect(doc.data(), equals(updatedData));

        // Act - Delete
        await firestoreService.collection(collectionPath).doc(docId).delete();

        // Assert - Delete
        doc = await firestoreService
            .collection(collectionPath)
            .doc(docId)
            .get();
        expect(doc.exists, isFalse);
      });

      test('should support complex query operations', () async {
        // Arrange
        const collectionPath = 'query-test';
        final testDocs = [
          {'name': 'Doc A', 'priority': 1, 'active': true},
          {'name': 'Doc B', 'priority': 2, 'active': false},
          {'name': 'Doc C', 'priority': 3, 'active': true},
        ];

        // Add test documents
        for (var i = 0; i < testDocs.length; i++) {
          await firestoreService
              .collection(collectionPath)
              .doc('doc-$i')
              .set(testDocs[i]);
        }

        // Act - Query active documents
        final activeQuery = await firestoreService
            .collection(collectionPath)
            .where('active', isEqualTo: true)
            .get();

        // Assert - Query results
        expect(activeQuery.docs.length, equals(2));
        for (final doc in activeQuery.docs) {
          expect(doc.data()['active'], isTrue);
        }

        // Act - Query with ordering
        final orderedQuery = await firestoreService
            .collection(collectionPath)
            .orderBy('priority', descending: true)
            .get();

        // Assert - Ordered results
        expect(orderedQuery.docs.length, equals(3));
        expect(orderedQuery.docs[0].data()['name'], equals('Doc C'));
        expect(orderedQuery.docs[1].data()['name'], equals('Doc B'));
        expect(orderedQuery.docs[2].data()['name'], equals('Doc A'));
      });
    });
  });
}

/// Mock Firestore implementation that throws errors for testing error handling
class _MockFirestoreForErrors implements FirebaseFirestore {
  @override
  dynamic noSuchMethod(Invocation invocation) {
    // For async methods, return a Future that completes with an error
    if (invocation.memberName.toString().contains('enableNetwork') ||
        invocation.memberName.toString().contains('disableNetwork') ||
        invocation.memberName.toString().contains('clearPersistence') ||
        invocation.memberName.toString().contains('terminate') ||
        invocation.memberName.toString().contains('waitForPendingWrites')) {
      return Future<void>.error(Exception('Test error'));
    }

    // For settings configuration, throw synchronously
    if (invocation.memberName.toString().contains('settings=')) {
      throw Exception('Test error');
    }

    // For collection/doc access that leads to connectivity test
    if (invocation.memberName.toString().contains('collection')) {
      throw Exception('Test error');
    }

    return super.noSuchMethod(invocation);
  }
}

// Removed sealed class implementations - using FakeFirebaseFirestore instead
