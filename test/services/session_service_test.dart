import 'package:budapp/data/models/session_state.dart';
import 'package:budapp/services/session_service.dart';
import 'package:flutter_test/flutter_test.dart';

import '../helpers/mock_providers.dart';

void main() {
  group('SessionService Tests', () {
    late SessionService sessionService;
    late MockAuthService mockAuthService;

    setUp(() {
      mockAuthService = MockAuthService();
      sessionService = SessionService(mockAuthService);
    });

    test('SessionService can be created with dependency injection', () {
      final sessionService1 = SessionService(mockAuthService);
      final sessionService2 = SessionService(mockAuthService);
      expect(identical(sessionService1, sessionService2), isFalse);
      expect(sessionService1, isA<SessionService>());
      expect(sessionService2, isA<SessionService>());
    });

    test('SessionService provides expected methods and properties', () {
      // Test that SessionService has all expected methods
      expect(sessionService.initialize, isA<Function>());
      expect(sessionService.dispose, isA<Function>());
      expect(sessionService.startSession, isA<Function>());
      expect(sessionService.endSession, isA<Function>());
      expect(sessionService.updatePreferences, isA<Function>());
      expect(sessionService.getPreference, isA<Function>());
      expect(sessionService.getSessionAnalytics, isA<Function>());
      expect(sessionService.refreshSession, isA<Function>());

      // Test properties
      expect(sessionService.sessionStream, isA<Stream<SessionInfo>>());
      expect(sessionService.currentSession, isA<SessionInfo>());
      expect(sessionService.isInitialized, isA<bool>());
    });

    test('Initial session state is loading', () {
      expect(sessionService.currentSession.state, equals(SessionState.loading));
      expect(sessionService.currentSession.isLoading, isTrue);
      expect(sessionService.currentSession.user, isNull);
      expect(sessionService.currentSession.error, isNull);
    });

    test('Session analytics returns expected structure', () {
      final analytics = sessionService.getSessionAnalytics();

      expect(analytics, isA<Map<String, dynamic>>());
      expect(analytics.containsKey('session_count'), isTrue);
      expect(analytics.containsKey('current_session_duration_minutes'), isTrue);
      expect(analytics.containsKey('last_login_time'), isTrue);
      expect(analytics.containsKey('last_auth_provider'), isTrue);

      expect(analytics['session_count'], isA<int>());
      expect(analytics['current_session_duration_minutes'], isA<int>());
    });

    test('getPreference returns null for non-existent preference', () {
      final preference = sessionService.getPreference<String>(
        'non_existent_key',
      );
      expect(preference, isNull);
    });
  });

  group('SessionInfo Tests', () {
    test('SessionInfo.loading() creates correct state', () {
      final sessionInfo = SessionInfo.loading();

      expect(sessionInfo.state, equals(SessionState.loading));
      expect(sessionInfo.isLoading, isTrue);
      expect(sessionInfo.user, isNull);
      expect(sessionInfo.error, isNull);
      expect(sessionInfo.preferences, isEmpty);
    });

    test('SessionInfo.unauthenticated() creates correct state', () {
      final sessionInfo = SessionInfo.unauthenticated();

      expect(sessionInfo.state, equals(SessionState.unauthenticated));
      expect(sessionInfo.isUnauthenticated, isTrue);
      expect(sessionInfo.user, isNull);
      expect(sessionInfo.error, isNull);
    });

    test('SessionInfo.error() creates correct state', () {
      const error = SessionError(
        code: 'test_error',
        message: 'Test error message',
      );
      final sessionInfo = SessionInfo.error(error: error);

      expect(sessionInfo.state, equals(SessionState.error));
      expect(sessionInfo.hasError, isTrue);
      expect(sessionInfo.error, equals(error));
      expect(sessionInfo.user, isNull);
    });

    test('SessionInfo copyWith works correctly', () {
      final originalSession = SessionInfo.loading();
      const error = SessionError(code: 'test', message: 'Test');

      final updatedSession = originalSession.copyWith(
        state: SessionState.error,
        error: error,
      );

      expect(updatedSession.state, equals(SessionState.error));
      expect(updatedSession.error, equals(error));
      expect(updatedSession.user, equals(originalSession.user));
      expect(updatedSession.preferences, equals(originalSession.preferences));
    });

    test('SessionInfo equality works correctly', () {
      final session1 = SessionInfo.loading();
      final session2 = SessionInfo.loading();
      final session3 = SessionInfo.unauthenticated();

      expect(session1, equals(session2));
      expect(session1, isNot(equals(session3)));
    });
  });

  group('SessionError Tests', () {
    test('SessionError creates correctly', () {
      const error = SessionError(
        code: 'test_code',
        message: 'Test message',
        originalError: 'Original error',
      );

      expect(error.code, equals('test_code'));
      expect(error.message, equals('Test message'));
      expect(error.originalError, equals('Original error'));
    });

    test('SessionError equality works correctly', () {
      const error1 = SessionError(code: 'test', message: 'Test');
      const error2 = SessionError(code: 'test', message: 'Test');
      const error3 = SessionError(code: 'different', message: 'Test');

      expect(error1, equals(error2));
      expect(error1, isNot(equals(error3)));
    });

    test('SessionError toString works correctly', () {
      const error = SessionError(code: 'test', message: 'Test message');
      expect(error.toString(), contains('test'));
      expect(error.toString(), contains('Test message'));
    });
  });

  group('SessionState Enum Tests', () {
    test('SessionState has all expected values', () {
      expect(SessionState.values, contains(SessionState.loading));
      expect(SessionState.values, contains(SessionState.authenticated));
      expect(SessionState.values, contains(SessionState.unauthenticated));
      expect(SessionState.values, contains(SessionState.emailNotVerified));
      expect(SessionState.values, contains(SessionState.error));
    });

    test('SessionState values are distinct', () {
      final states = SessionState.values.toSet();
      expect(states.length, equals(SessionState.values.length));
    });
  });
}
