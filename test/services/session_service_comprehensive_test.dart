import 'dart:async';

import 'package:budapp/data/models/session_state.dart';
import 'package:budapp/services/session_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../helpers/mock_providers.dart';

void main() {
  group('SessionService Comprehensive Tests', () {
    late SessionService sessionService;
    late MockAuthService mockAuthService;
    late MockUser mockUser;
    late StreamController<User?> authStateController;

    setUp(() async {
      // Initialize SharedPreferences with mock data
      SharedPreferences.setMockInitialValues({});

      // Create mock objects
      mockAuthService = MockAuthService();
      mockUser = MockUser();
      authStateController = StreamController<User?>.broadcast();

      // Setup auth service mock
      when(
        () => mockAuthService.authStateChanges,
      ).thenAnswer((_) => authStateController.stream);
      when(() => mockAuthService.currentUser).thenReturn(null);

      // Create sessionService BEFORE trying to use it
      sessionService = SessionService(mockAuthService);
    });

    tearDown(() async {
      await authStateController.close();
      sessionService.dispose();
    });

    group('Initialization', () {
      test(
        'should initialize successfully and load session analytics',
        () async {
          // Arrange
          SharedPreferences.setMockInitialValues({
            'session_count': 5,
            'last_login_time': '2024-01-01T10:00:00.000Z',
            'last_auth_provider': 'google',
          });

          // Act
          await sessionService.initialize();

          // Assert
          expect(sessionService.isInitialized, isTrue);
          final analytics = sessionService.getSessionAnalytics();
          expect(analytics['session_count'], equals(5));
          expect(
            analytics['last_login_time'],
            equals('2024-01-01T10:00:00.000Z'),
          );
          expect(analytics['last_auth_provider'], equals('google'));
        },
      );

      test('should handle initialization with no existing data', () async {
        // Act
        await sessionService.initialize();

        // Assert
        expect(sessionService.isInitialized, isTrue);
        final analytics = sessionService.getSessionAnalytics();
        expect(analytics['session_count'], equals(0));
        expect(analytics['last_login_time'], isNull);
        expect(analytics['last_auth_provider'], isNull);
      });

      test('should restore session with existing authenticated user', () async {
        // Arrange
        when(() => mockAuthService.currentUser).thenReturn(mockUser);
        SharedPreferences.setMockInitialValues({
          'last_auth_provider': 'email',
          'theme_mode': 'dark',
          'notifications_enabled': false,
        });

        // Act
        await sessionService.initialize();

        // Assert
        final session = sessionService.currentSession;
        expect(session.state, equals(SessionState.authenticated));
        expect(session.user?.uid, equals('test-uid'));
        expect(session.authProvider, equals('email'));
        expect(session.preferences['theme_mode'], equals('dark'));
        expect(session.preferences['notifications_enabled'], equals(false));
      });

      test('should restore session as unauthenticated when no user', () async {
        // Arrange
        when(() => mockAuthService.currentUser).thenReturn(null);

        // Act
        await sessionService.initialize();

        // Assert
        final session = sessionService.currentSession;
        expect(session.state, equals(SessionState.unauthenticated));
        expect(session.user, isNull);
      });

      test('should handle initialization errors gracefully', () async {
        // Arrange
        when(
          () => mockAuthService.authStateChanges,
        ).thenThrow(Exception('Auth initialization failed'));

        // Act
        await sessionService.initialize();

        // Assert
        final session = sessionService.currentSession;
        expect(session.state, equals(SessionState.error));
        expect(session.error?.code, equals('initialization_failed'));
        expect(
          session.error?.message,
          equals('Failed to initialize session service'),
        );
      });
    });

    group('Session Start/End', () {
      setUp(() async {
        await sessionService.initialize();
      });

      test('should start session for verified user successfully', () async {
        // Act
        await sessionService.startSession(
          user: mockUser,
          authProvider: 'google',
        );

        // Assert
        final session = sessionService.currentSession;
        expect(session.state, equals(SessionState.authenticated));
        expect(session.user?.uid, equals('test-uid'));
        expect(session.authProvider, equals('google'));
        expect(session.lastLoginTime, isNotNull);

        final analytics = sessionService.getSessionAnalytics();
        expect(analytics['session_count'], equals(1));
        expect(
          analytics['current_session_duration_minutes'],
          greaterThanOrEqualTo(0),
        );
      });

      test(
        'should start session for unverified user with emailNotVerified state',
        () async {
          // Arrange - create a custom mock user for unverified email
          final unverifiedUser = MockUnverifiedUser();

          // Act
          await sessionService.startSession(
            user: unverifiedUser,
            authProvider: 'email',
          );

          // Assert
          final session = sessionService.currentSession;
          expect(session.state, equals(SessionState.emailNotVerified));
          expect(session.user?.uid, equals('test-uid'));
          expect(session.authProvider, isNull); // Not set for unverified users
        },
      );

      test('should handle session start errors gracefully', () async {
        // Arrange
        final prefs = await SharedPreferences.getInstance();
        await prefs.clear();
        // Clear preferences to cause an error during preference loading

        // Act
        await sessionService.startSession(
          user: mockUser,
          authProvider: 'email',
        );

        // Assert - should not crash and should handle error gracefully
        final session = sessionService.currentSession;
        expect(session.user?.uid, equals('test-uid'));
      });

      test('should end session successfully', () async {
        // Arrange - start a session first
        await sessionService.startSession(
          user: mockUser,
          authProvider: 'email',
        );
        expect(
          sessionService.currentSession.state,
          equals(SessionState.authenticated),
        );

        // Act
        await sessionService.endSession();

        // Assert
        final session = sessionService.currentSession;
        expect(session.state, equals(SessionState.unauthenticated));
        expect(session.user, isNull);
        expect(session.authProvider, isNull);
      });

      test('should handle end session errors gracefully', () async {
        // Act - end session without starting one
        await sessionService.endSession();

        // Assert - should not crash
        final session = sessionService.currentSession;
        expect(session.state, equals(SessionState.unauthenticated));
      });
    });

    group('Preferences Management', () {
      setUp(() async {
        await sessionService.initialize();
        await sessionService.startSession(
          user: mockUser,
          authProvider: 'email',
        );
      });

      test('should update preferences successfully', () async {
        // Act
        await sessionService.updatePreferences({
          'theme_mode': 'dark',
          'notifications_enabled': false,
          'language': 'es',
          'secure_mode': true,
        });

        // Assert
        final session = sessionService.currentSession;
        expect(session.preferences['theme_mode'], equals('dark'));
        expect(session.preferences['notifications_enabled'], equals(false));
        expect(session.preferences['language'], equals('es'));
        expect(session.preferences['secure_mode'], equals(true));

        // Verify persistence
        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getString('theme_mode'), equals('dark'));
        expect(prefs.getBool('notifications_enabled'), equals(false));
        expect(prefs.getString('language'), equals('es'));
        expect(prefs.getBool('secure_mode'), equals(true));
      });

      test('should handle different preference value types', () async {
        // Act
        await sessionService.updatePreferences({
          'string_pref': 'test_string',
          'bool_pref': true,
          'int_pref': 42,
          'double_pref': 3.14,
          'object_pref': {'key': 'value'}, // Should be converted to string
        });

        // Assert
        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getString('string_pref'), equals('test_string'));
        expect(prefs.getBool('bool_pref'), equals(true));
        expect(prefs.getInt('int_pref'), equals(42));
        expect(prefs.getDouble('double_pref'), equals(3.14));
        expect(
          prefs.getString('object_pref'),
          isNotNull,
        ); // Converted to string
      });

      test('should update preferences when no valid user session', () async {
        // Arrange - end session
        await sessionService.endSession();

        // Act
        await sessionService.updatePreferences({'theme_mode': 'light'});

        // Assert - should save to preferences but not update session
        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getString('theme_mode'), equals('light'));

        final session = sessionService.currentSession;
        expect(session.preferences['theme_mode'], isNot(equals('light')));
      });

      test('should get specific preference values correctly', () async {
        // Arrange
        await sessionService.updatePreferences({
          'string_value': 'test',
          'bool_value': true,
          'int_value': 123,
        });

        // Act & Assert
        expect(
          sessionService.getPreference<String>('string_value'),
          equals('test'),
        );
        expect(sessionService.getPreference<bool>('bool_value'), equals(true));
        expect(sessionService.getPreference<int>('int_value'), equals(123));
        expect(sessionService.getPreference<String>('non_existent'), isNull);
      });
    });

    group('Session Refresh', () {
      setUp(() async {
        await sessionService.initialize();
      });

      test('should refresh session successfully with current user', () async {
        // Arrange
        final googleUser = MockGoogleUser();
        when(() => mockAuthService.currentUser).thenReturn(googleUser);

        // Act
        await sessionService.refreshSession();

        // Assert
        final session = sessionService.currentSession;
        expect(session.state, equals(SessionState.authenticated));
        expect(session.user?.uid, equals('test-uid'));
        expect(session.authProvider, equals('google'));
      });

      test('should handle refresh when no current user', () async {
        // Arrange
        when(() => mockAuthService.currentUser).thenReturn(null);

        // Act
        await sessionService.refreshSession();

        // Assert
        final session = sessionService.currentSession;
        expect(session.state, equals(SessionState.unauthenticated));
      });

      test('should handle refresh errors gracefully', () async {
        // Arrange
        final failingUser = MockFailingUser();
        when(() => mockAuthService.currentUser).thenReturn(failingUser);

        // Act
        await sessionService.refreshSession();

        // Assert
        final session = sessionService.currentSession;
        expect(session.state, equals(SessionState.error));
        expect(session.error?.code, equals('session_refresh_failed'));
      });
    });

    group('Auth Provider Detection', () {
      setUp(() async {
        await sessionService.initialize();
      });

      test('should detect Google auth provider', () async {
        // Arrange
        final googleUser = MockGoogleUser();

        // Act
        await sessionService.startSession(
          user: googleUser,
          authProvider: 'google',
        );

        // Assert
        expect(sessionService.currentSession.authProvider, equals('google'));
      });

      test('should detect Apple auth provider', () async {
        // Arrange
        final appleUser = MockAppleUser();

        // Act
        await sessionService.startSession(
          user: appleUser,
          authProvider: 'apple',
        );

        // Assert
        expect(sessionService.currentSession.authProvider, equals('apple'));
      });

      test('should detect email auth provider', () async {
        // Arrange
        final emailUser = MockEmailUser();

        // Act
        await sessionService.startSession(
          user: emailUser,
          authProvider: 'email',
        );

        // Assert
        expect(sessionService.currentSession.authProvider, equals('email'));
      });

      test('should handle unknown auth provider', () async {
        // Act
        await sessionService.startSession(
          user: mockUser,
          authProvider: 'unknown',
        );

        // Assert
        expect(sessionService.currentSession.authProvider, equals('unknown'));
      });

      test('should handle empty provider data', () async {
        // Act
        await sessionService.startSession(
          user: mockUser,
          authProvider: 'unknown',
        );

        // Assert
        expect(sessionService.currentSession.authProvider, equals('unknown'));
      });
    });

    group('Stream Functionality', () {
      setUp(() async {
        await sessionService.initialize();
      });

      test('should emit session state changes through stream', () async {
        // Arrange
        final streamEvents = <SessionInfo>[];
        final subscription = sessionService.sessionStream.listen(
          streamEvents.add,
        );

        // Act
        await sessionService.startSession(
          user: mockUser,
          authProvider: 'email',
        );
        await Future<void>.delayed(
          const Duration(milliseconds: 10),
        ); // Allow stream to emit
        await sessionService.endSession();
        await Future<void>.delayed(
          const Duration(milliseconds: 10),
        ); // Allow stream to emit

        // Clean up
        await subscription.cancel();

        // Assert
        expect(streamEvents.length, greaterThanOrEqualTo(2));
        expect(
          streamEvents.any((s) => s.state == SessionState.authenticated),
          isTrue,
        );
        expect(
          streamEvents.any((s) => s.state == SessionState.unauthenticated),
          isTrue,
        );
      });

      test('should handle auth state changes from auth service', () async {
        // Arrange
        final streamEvents = <SessionInfo>[];
        final subscription = sessionService.sessionStream.listen(
          streamEvents.add,
        );

        // Act - simulate auth state changes
        authStateController.add(mockUser);
        await Future<void>.delayed(const Duration(milliseconds: 10));

        authStateController.add(null);
        await Future<void>.delayed(const Duration(milliseconds: 10));

        // Clean up
        await subscription.cancel();

        // Assert
        expect(streamEvents.length, greaterThanOrEqualTo(2));
      });

      test('should handle auth state errors', () async {
        // Arrange
        final streamEvents = <SessionInfo>[];
        final subscription = sessionService.sessionStream.listen(
          streamEvents.add,
        );

        // Act - simulate auth state error
        authStateController.addError(Exception('Auth state error'));
        await Future<void>.delayed(const Duration(milliseconds: 10));

        // Clean up
        await subscription.cancel();

        // Assert
        expect(streamEvents.any((s) => s.state == SessionState.error), isTrue);
        final errorEvent = streamEvents.firstWhere(
          (s) => s.state == SessionState.error,
        );
        expect(errorEvent.error?.code, equals('auth_state_error'));
      });
    });

    group('Session Analytics', () {
      setUp(() async {
        await sessionService.initialize();
      });

      test('should track session duration correctly', () async {
        // Act - start session and wait a brief moment
        await sessionService.startSession(
          user: mockUser,
          authProvider: 'email',
        );
        await Future<void>.delayed(const Duration(milliseconds: 50));

        // Assert
        final analytics = sessionService.getSessionAnalytics();
        expect(
          analytics['current_session_duration_minutes'],
          greaterThanOrEqualTo(0),
        );
        expect(analytics['session_count'], equals(1));
      });

      test('should persist session count across service instances', () async {
        // Arrange - simulate multiple sessions
        await sessionService.startSession(
          user: mockUser,
          authProvider: 'email',
        );
        await sessionService.startSession(
          user: mockUser,
          authProvider: 'google',
        );
        await sessionService.startSession(
          user: mockUser,
          authProvider: 'apple',
        );

        // Act - create new service instance
        final newService = SessionService(mockAuthService);
        await newService.initialize();

        // Assert
        final analytics = newService.getSessionAnalytics();
        expect(analytics['session_count'], equals(3));

        // Clean up
        newService.dispose();
      });

      test('should track last login information', () async {
        // Act
        await sessionService.startSession(
          user: mockUser,
          authProvider: 'google',
        );

        // Assert
        final analytics = sessionService.getSessionAnalytics();
        expect(analytics['last_auth_provider'], equals('google'));
        expect(analytics['last_login_time'], isNotNull);

        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getString('last_auth_provider'), equals('google'));
        expect(prefs.getString('last_login_time'), isNotNull);
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle dispose without initialization', () {
        // Arrange
        final uninitializedService = SessionService(mockAuthService);

        // Act & Assert - should not throw
        expect(uninitializedService.dispose, returnsNormally);
      });

      test('should handle multiple dispose calls', () async {
        // Arrange
        await sessionService.initialize();

        // Act & Assert - should not throw
        expect(() => sessionService.dispose(), returnsNormally);
        expect(() => sessionService.dispose(), returnsNormally);
      });

      test('should handle operations on disposed service', () async {
        // Arrange
        await sessionService.initialize();
        sessionService.dispose();

        // Act & Assert - should not throw
        expect(() => sessionService.getSessionAnalytics(), returnsNormally);
        expect(
          () => sessionService.getPreference<String>('test'),
          returnsNormally,
        );
      });

      test('should handle concurrent operations safely', () async {
        // Act - run multiple operations concurrently
        await sessionService.initialize();

        final futures = <Future<void>>[];
        for (var i = 0; i < 5; i++) {
          futures
            ..add(sessionService.updatePreferences({'key$i': 'value$i'}))
            ..add(
              sessionService.startSession(
                user: mockUser,
                authProvider: 'email',
              ),
            );
        }

        // Assert - should complete without errors
        await Future.wait(futures);
        expect(
          sessionService.currentSession.state,
          equals(SessionState.authenticated),
        );
      });
    });

    group('Preference Loading Edge Cases', () {
      setUp(() async {
        await sessionService.initialize();
      });

      test('should load default preferences when none exist', () async {
        // Act
        await sessionService.startSession(
          user: mockUser,
          authProvider: 'email',
        );

        // Assert
        final session = sessionService.currentSession;
        expect(session.preferences['theme_mode'], equals('system'));
        expect(session.preferences['language'], equals('en'));
        expect(session.preferences['notifications_enabled'], equals(true));
        expect(session.preferences['biometric_enabled'], equals(false));
        expect(session.preferences['secure_mode'], equals(false));
      });

      test('should override defaults with stored preferences', () async {
        // Arrange
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('theme_mode', 'dark');
        await prefs.setString('language', 'fr');
        await prefs.setBool('notifications_enabled', false);

        // Act
        await sessionService.startSession(
          user: mockUser,
          authProvider: 'email',
        );

        // Assert
        final session = sessionService.currentSession;
        expect(session.preferences['theme_mode'], equals('dark'));
        expect(session.preferences['language'], equals('fr'));
        expect(session.preferences['notifications_enabled'], equals(false));
        expect(
          session.preferences['biometric_enabled'],
          equals(false),
        ); // Still default
      });
    });
  });
}

// Mock UserInfo for provider testing
class MockUserInfo extends Mock implements UserInfo {
  @override
  String providerId = 'mock.provider';
}

// Mock User with unverified email
class MockUnverifiedUser extends Mock implements User {
  @override
  String get uid => 'test-uid';

  @override
  String? get email => '<EMAIL>';

  @override
  String? get displayName => 'Test User';

  @override
  bool get emailVerified => false;

  @override
  List<UserInfo> get providerData => [];

  @override
  Future<void> reload() async {}
}

// Mock User with Google provider
class MockGoogleUser extends Mock implements User {
  @override
  String get uid => 'test-uid';

  @override
  String? get email => '<EMAIL>';

  @override
  String? get displayName => 'Test User';

  @override
  bool get emailVerified => true;

  @override
  List<UserInfo> get providerData => [
    MockUserInfo()..providerId = 'google.com',
  ];

  @override
  Future<void> reload() async {}
}

// Mock User with Apple provider
class MockAppleUser extends Mock implements User {
  @override
  String get uid => 'test-uid';

  @override
  String? get email => '<EMAIL>';

  @override
  String? get displayName => 'Test User';

  @override
  bool get emailVerified => true;

  @override
  List<UserInfo> get providerData => [MockUserInfo()..providerId = 'apple.com'];

  @override
  Future<void> reload() async {}
}

// Mock User with Email provider
class MockEmailUser extends Mock implements User {
  @override
  String get uid => 'test-uid';

  @override
  String? get email => '<EMAIL>';

  @override
  String? get displayName => 'Test User';

  @override
  bool get emailVerified => true;

  @override
  List<UserInfo> get providerData => [MockUserInfo()..providerId = 'password'];

  @override
  Future<void> reload() async {}
}

// Mock User that fails on reload
class MockFailingUser extends Mock implements User {
  @override
  String get uid => 'test-uid';

  @override
  String? get email => '<EMAIL>';

  @override
  String? get displayName => 'Test User';

  @override
  bool get emailVerified => true;

  @override
  List<UserInfo> get providerData => [];

  @override
  Future<void> reload() async {
    throw Exception('Reload failed');
  }
}
