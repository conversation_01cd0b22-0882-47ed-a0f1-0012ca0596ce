import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:budapp/features/auth/services/biometric_error_service.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAppLocalizations extends Mock implements AppLocalizations {}

void main() {
  group('BiometricErrorService', () {
    late MockAppLocalizations mockL10n;

    setUp(() {
      mockL10n = MockAppLocalizations();

      // Setup common mock returns
      when(
        () => mockL10n.biometricUnknownError,
      ).thenReturn('Unknown biometric error');
      when(
        () => mockL10n.biometricPasscodeNotSet,
      ).thenReturn('Passcode not set');
      when(
        () => mockL10n.biometricNotEnrolled,
      ).thenReturn('Biometric not enrolled');
      when(
        () => mockL10n.biometricNotAvailable,
      ).thenReturn('Biometric not available');
      when(() => mockL10n.biometricUserCancelled).thenReturn('User cancelled');
      when(() => mockL10n.biometricUserFallback).thenReturn('User fallback');
      when(
        () => mockL10n.biometricSystemCancelled,
      ).thenReturn('System cancelled');
      when(
        () => mockL10n.biometricInvalidContext,
      ).thenReturn('Invalid context');
      when(() => mockL10n.biometricLockedOut).thenReturn('Locked out');
      when(
        () => mockL10n.biometricPermanentlyLockedOut,
      ).thenReturn('Permanently locked out');
      when(
        () => mockL10n.biometricTooManyAttempts,
      ).thenReturn('Too many attempts');
      when(() => mockL10n.biometricPlatformError).thenReturn('Platform error');
    });

    group('handleBiometricError', () {
      test('should handle PlatformException with BiometricOnlyPin code', () {
        // Arrange
        final error = PlatformException(
          code: 'BiometricOnlyPin',
          message: 'Passcode required',
        );

        // Act
        final result = BiometricErrorService.handleBiometricError(
          error,
          mockL10n,
        );

        // Assert
        expect(result.code, equals('biometric_passcode_not_set'));
        expect(result.userMessage, equals('Passcode not set'));
        expect(result.technicalMessage, equals('Passcode required'));
        expect(result.severity, equals(ErrorSeverity.warning));
        expect(result.category, equals(ErrorCategory.biometric));
        expect(result.isRetryable, isFalse);
      });

      test('should handle PlatformException with PasscodeNotSet code', () {
        // Arrange
        final error = PlatformException(
          code: 'PasscodeNotSet',
          message: 'No passcode set',
        );

        // Act
        final result = BiometricErrorService.handleBiometricError(
          error,
          mockL10n,
        );

        // Assert
        expect(result.code, equals('biometric_passcode_not_set'));
        expect(result.userMessage, equals('Passcode not set'));
        expect(result.technicalMessage, equals('No passcode set'));
        expect(result.severity, equals(ErrorSeverity.warning));
        expect(result.category, equals(ErrorCategory.biometric));
        expect(result.isRetryable, isFalse);
      });

      test('should handle PlatformException with NotEnrolled code', () {
        // Arrange
        final error = PlatformException(
          code: 'NotEnrolled',
          message: 'No biometric enrolled',
        );

        // Act
        final result = BiometricErrorService.handleBiometricError(
          error,
          mockL10n,
        );

        // Assert
        expect(result.code, equals('biometric_not_enrolled'));
        expect(result.userMessage, equals('Biometric not enrolled'));
        expect(result.technicalMessage, equals('No biometric enrolled'));
        expect(result.severity, equals(ErrorSeverity.warning));
        expect(result.category, equals(ErrorCategory.biometric));
        expect(result.isRetryable, isFalse);
      });

      test(
        'should handle PlatformException with BiometricNotEnrolled code',
        () {
          // Arrange
          final error = PlatformException(
            code: 'BiometricNotEnrolled',
            message: 'Biometric not enrolled',
          );

          // Act
          final result = BiometricErrorService.handleBiometricError(
            error,
            mockL10n,
          );

          // Assert
          expect(result.code, equals('biometric_not_enrolled'));
          expect(result.userMessage, equals('Biometric not enrolled'));
          expect(result.technicalMessage, equals('Biometric not enrolled'));
          expect(result.severity, equals(ErrorSeverity.warning));
          expect(result.category, equals(ErrorCategory.biometric));
          expect(result.isRetryable, isFalse);
        },
      );

      test('should handle PlatformException with NotAvailable code', () {
        // Arrange
        final error = PlatformException(
          code: 'NotAvailable',
          message: 'Biometric not available',
        );

        // Act
        final result = BiometricErrorService.handleBiometricError(
          error,
          mockL10n,
        );

        // Assert
        expect(result.code, equals('biometric_not_available'));
        expect(result.userMessage, equals('Biometric not available'));
        expect(result.technicalMessage, equals('Biometric not available'));
        expect(result.severity, equals(ErrorSeverity.error));
        expect(result.category, equals(ErrorCategory.biometric));
        expect(result.isRetryable, isFalse);
      });

      test(
        'should handle PlatformException with BiometricNotAvailable code',
        () {
          // Arrange
          final error = PlatformException(
            code: 'BiometricNotAvailable',
            message: 'Biometric hardware not available',
          );

          // Act
          final result = BiometricErrorService.handleBiometricError(
            error,
            mockL10n,
          );

          // Assert
          expect(result.code, equals('biometric_not_available'));
          expect(result.userMessage, equals('Biometric not available'));
          expect(
            result.technicalMessage,
            equals('Biometric hardware not available'),
          );
          expect(result.severity, equals(ErrorSeverity.error));
          expect(result.category, equals(ErrorCategory.biometric));
          expect(result.isRetryable, isFalse);
        },
      );

      test('should handle PlatformException with UserCancel code', () {
        // Arrange
        final error = PlatformException(
          code: 'UserCancel',
          message: 'User cancelled authentication',
        );

        // Act
        final result = BiometricErrorService.handleBiometricError(
          error,
          mockL10n,
        );

        // Assert
        expect(result.code, equals('biometric_user_cancel'));
        expect(result.userMessage, equals('User cancelled'));
        expect(
          result.technicalMessage,
          equals('User cancelled authentication'),
        );
        expect(result.severity, equals(ErrorSeverity.info));
        expect(result.category, equals(ErrorCategory.biometric));
        expect(result.isRetryable, isTrue);
      });

      test('should handle PlatformException with BiometricUserCancel code', () {
        // Arrange
        final error = PlatformException(
          code: 'BiometricUserCancel',
          message: 'Biometric authentication cancelled by user',
        );

        // Act
        final result = BiometricErrorService.handleBiometricError(
          error,
          mockL10n,
        );

        // Assert
        expect(result.code, equals('biometric_user_cancel'));
        expect(result.userMessage, equals('User cancelled'));
        expect(
          result.technicalMessage,
          equals('Biometric authentication cancelled by user'),
        );
        expect(result.severity, equals(ErrorSeverity.info));
        expect(result.category, equals(ErrorCategory.biometric));
        expect(result.isRetryable, isTrue);
      });

      test('should handle PlatformException with UserFallback code', () {
        // Arrange
        final error = PlatformException(
          code: 'UserFallback',
          message: 'User chose fallback authentication',
        );

        // Act
        final result = BiometricErrorService.handleBiometricError(
          error,
          mockL10n,
        );

        // Assert
        expect(result.code, equals('biometric_user_fallback'));
        expect(result.userMessage, equals('User fallback'));
        expect(
          result.technicalMessage,
          equals('User chose fallback authentication'),
        );
        expect(result.severity, equals(ErrorSeverity.info));
        expect(result.category, equals(ErrorCategory.biometric));
        expect(result.isRetryable, isTrue);
      });

      test(
        'should handle PlatformException with BiometricUserFallback code',
        () {
          // Arrange
          final error = PlatformException(
            code: 'BiometricUserFallback',
            message: 'Biometric fallback chosen',
          );

          // Act
          final result = BiometricErrorService.handleBiometricError(
            error,
            mockL10n,
          );

          // Assert
          expect(result.code, equals('biometric_user_fallback'));
          expect(result.userMessage, equals('User fallback'));
          expect(result.technicalMessage, equals('Biometric fallback chosen'));
          expect(result.severity, equals(ErrorSeverity.info));
          expect(result.category, equals(ErrorCategory.biometric));
          expect(result.isRetryable, isTrue);
        },
      );

      test('should handle PlatformException with SystemCancel code', () {
        // Arrange
        final error = PlatformException(
          code: 'SystemCancel',
          message: 'System cancelled authentication',
        );

        // Act
        final result = BiometricErrorService.handleBiometricError(
          error,
          mockL10n,
        );

        // Assert
        expect(result.code, equals('biometric_system_cancel'));
        expect(result.userMessage, equals('System cancelled'));
        expect(
          result.technicalMessage,
          equals('System cancelled authentication'),
        );
        expect(result.severity, equals(ErrorSeverity.warning));
        expect(result.category, equals(ErrorCategory.biometric));
        expect(result.isRetryable, isTrue);
      });

      test(
        'should handle PlatformException with BiometricSystemCancel code',
        () {
          // Arrange
          final error = PlatformException(
            code: 'BiometricSystemCancel',
            message: 'Biometric system cancelled',
          );

          // Act
          final result = BiometricErrorService.handleBiometricError(
            error,
            mockL10n,
          );

          // Assert
          expect(result.code, equals('biometric_system_cancel'));
          expect(result.userMessage, equals('System cancelled'));
          expect(result.technicalMessage, equals('Biometric system cancelled'));
          expect(result.severity, equals(ErrorSeverity.warning));
          expect(result.category, equals(ErrorCategory.biometric));
          expect(result.isRetryable, isTrue);
        },
      );

      test('should handle PlatformException with InvalidContext code', () {
        // Arrange
        final error = PlatformException(
          code: 'InvalidContext',
          message: 'Invalid authentication context',
        );

        // Act
        final result = BiometricErrorService.handleBiometricError(
          error,
          mockL10n,
        );

        // Assert
        expect(result.code, equals('biometric_invalid_context'));
        expect(result.userMessage, equals('Invalid context'));
        expect(
          result.technicalMessage,
          equals('Invalid authentication context'),
        );
        expect(result.severity, equals(ErrorSeverity.error));
        expect(result.category, equals(ErrorCategory.biometric));
        expect(result.isRetryable, isTrue);
      });

      test(
        'should handle PlatformException with BiometricInvalidContext code',
        () {
          // Arrange
          final error = PlatformException(
            code: 'BiometricInvalidContext',
            message: 'Biometric context invalid',
          );

          // Act
          final result = BiometricErrorService.handleBiometricError(
            error,
            mockL10n,
          );

          // Assert
          expect(result.code, equals('biometric_invalid_context'));
          expect(result.userMessage, equals('Invalid context'));
          expect(result.technicalMessage, equals('Biometric context invalid'));
          expect(result.severity, equals(ErrorSeverity.error));
          expect(result.category, equals(ErrorCategory.biometric));
          expect(result.isRetryable, isTrue);
        },
      );

      test('should handle PlatformException with LockedOut code', () {
        // Arrange
        final error = PlatformException(
          code: 'LockedOut',
          message: 'Biometric authentication locked out',
        );

        // Act
        final result = BiometricErrorService.handleBiometricError(
          error,
          mockL10n,
        );

        // Assert
        expect(result.code, equals('biometric_locked_out'));
        expect(result.userMessage, equals('Locked out'));
        expect(
          result.technicalMessage,
          equals('Biometric authentication locked out'),
        );
        expect(result.severity, equals(ErrorSeverity.error));
        expect(result.category, equals(ErrorCategory.biometric));
        expect(result.isRetryable, isFalse);
      });

      test('should handle PlatformException with BiometricLockedOut code', () {
        // Arrange
        final error = PlatformException(
          code: 'BiometricLockedOut',
          message: 'Biometric locked out temporarily',
        );

        // Act
        final result = BiometricErrorService.handleBiometricError(
          error,
          mockL10n,
        );

        // Assert
        expect(result.code, equals('biometric_locked_out'));
        expect(result.userMessage, equals('Locked out'));
        expect(
          result.technicalMessage,
          equals('Biometric locked out temporarily'),
        );
        expect(result.severity, equals(ErrorSeverity.error));
        expect(result.category, equals(ErrorCategory.biometric));
        expect(result.isRetryable, isFalse);
      });

      test(
        'should handle PlatformException with PermanentlyLockedOut code',
        () {
          // Arrange
          final error = PlatformException(
            code: 'PermanentlyLockedOut',
            message: 'Biometric permanently locked out',
          );

          // Act
          final result = BiometricErrorService.handleBiometricError(
            error,
            mockL10n,
          );

          // Assert
          expect(result.code, equals('biometric_permanently_locked_out'));
          expect(result.userMessage, equals('Permanently locked out'));
          expect(
            result.technicalMessage,
            equals('Biometric permanently locked out'),
          );
          expect(result.severity, equals(ErrorSeverity.error));
          expect(result.category, equals(ErrorCategory.biometric));
          expect(result.isRetryable, isFalse);
        },
      );

      test(
        'should handle PlatformException with BiometricPermanentlyLockedOut code',
        () {
          // Arrange
          final error = PlatformException(
            code: 'BiometricPermanentlyLockedOut',
            message: 'Biometric authentication permanently disabled',
          );

          // Act
          final result = BiometricErrorService.handleBiometricError(
            error,
            mockL10n,
          );

          // Assert
          expect(result.code, equals('biometric_permanently_locked_out'));
          expect(result.userMessage, equals('Permanently locked out'));
          expect(
            result.technicalMessage,
            equals('Biometric authentication permanently disabled'),
          );
          expect(result.severity, equals(ErrorSeverity.error));
          expect(result.category, equals(ErrorCategory.biometric));
          expect(result.isRetryable, isFalse);
        },
      );

      test('should handle PlatformException with TooManyAttempts code', () {
        // Arrange
        final error = PlatformException(
          code: 'TooManyAttempts',
          message: 'Too many failed attempts',
        );

        // Act
        final result = BiometricErrorService.handleBiometricError(
          error,
          mockL10n,
        );

        // Assert
        expect(result.code, equals('biometric_too_many_attempts'));
        expect(result.userMessage, equals('Too many attempts'));
        expect(result.technicalMessage, equals('Too many failed attempts'));
        expect(result.severity, equals(ErrorSeverity.warning));
        expect(result.category, equals(ErrorCategory.biometric));
        expect(result.isRetryable, isFalse);
      });

      test(
        'should handle PlatformException with BiometricTooManyAttempts code',
        () {
          // Arrange
          final error = PlatformException(
            code: 'BiometricTooManyAttempts',
            message: 'Biometric attempts exceeded',
          );

          // Act
          final result = BiometricErrorService.handleBiometricError(
            error,
            mockL10n,
          );

          // Assert
          expect(result.code, equals('biometric_too_many_attempts'));
          expect(result.userMessage, equals('Too many attempts'));
          expect(
            result.technicalMessage,
            equals('Biometric attempts exceeded'),
          );
          expect(result.severity, equals(ErrorSeverity.warning));
          expect(result.category, equals(ErrorCategory.biometric));
          expect(result.isRetryable, isFalse);
        },
      );

      test('should handle PlatformException with unknown code', () {
        // Arrange
        final error = PlatformException(
          code: 'UnknownError',
          message: 'Some unknown platform error',
        );

        // Act
        final result = BiometricErrorService.handleBiometricError(
          error,
          mockL10n,
        );

        // Assert
        expect(result.code, equals('biometric_platform_error'));
        expect(result.userMessage, equals('Platform error'));
        expect(result.technicalMessage, equals('Some unknown platform error'));
        expect(result.severity, equals(ErrorSeverity.error));
        expect(result.category, equals(ErrorCategory.biometric));
        expect(result.isRetryable, isTrue);
      });

      test('should handle PlatformException with no message', () {
        // Arrange
        final error = PlatformException(code: 'UnknownError');

        // Act
        final result = BiometricErrorService.handleBiometricError(
          error,
          mockL10n,
        );

        // Assert
        expect(result.code, equals('biometric_platform_error'));
        expect(result.userMessage, equals('Platform error'));
        expect(result.technicalMessage, equals('UnknownError'));
        expect(result.severity, equals(ErrorSeverity.error));
        expect(result.category, equals(ErrorCategory.biometric));
        expect(result.isRetryable, isTrue);
      });

      test('should handle non-PlatformException errors', () {
        // Arrange
        final error = Exception('Some generic error');

        // Act
        final result = BiometricErrorService.handleBiometricError(
          error,
          mockL10n,
        );

        // Assert
        expect(result.code, equals('biometric_unknown_error'));
        expect(result.userMessage, equals('Unknown biometric error'));
        expect(
          result.technicalMessage,
          equals('Exception: Some generic error'),
        );
        expect(result.severity, equals(ErrorSeverity.error));
        expect(result.category, equals(ErrorCategory.biometric));
        expect(result.isRetryable, isTrue);
      });
    });

    group('getErrorIcon', () {
      test('should return correct icon for biometric_passcode_not_set', () {
        // Act
        final icon = BiometricErrorService.getErrorIcon(
          'biometric_passcode_not_set',
        );

        // Assert
        expect(icon, equals('🔒'));
      });

      test('should return correct icon for biometric_not_enrolled', () {
        // Act
        final icon = BiometricErrorService.getErrorIcon(
          'biometric_not_enrolled',
        );

        // Assert
        expect(icon, equals('👆'));
      });

      test('should return correct icon for biometric_not_available', () {
        // Act
        final icon = BiometricErrorService.getErrorIcon(
          'biometric_not_available',
        );

        // Assert
        expect(icon, equals('❌'));
      });

      test('should return correct icon for biometric_user_cancel', () {
        // Act
        final icon = BiometricErrorService.getErrorIcon(
          'biometric_user_cancel',
        );

        // Assert
        expect(icon, equals('🚫'));
      });

      test('should return correct icon for biometric_locked_out', () {
        // Act
        final icon = BiometricErrorService.getErrorIcon('biometric_locked_out');

        // Assert
        expect(icon, equals('🔐'));
      });

      test(
        'should return correct icon for biometric_permanently_locked_out',
        () {
          // Act
          final icon = BiometricErrorService.getErrorIcon(
            'biometric_permanently_locked_out',
          );

          // Assert
          expect(icon, equals('🔐'));
        },
      );

      test('should return correct icon for biometric_too_many_attempts', () {
        // Act
        final icon = BiometricErrorService.getErrorIcon(
          'biometric_too_many_attempts',
        );

        // Assert
        expect(icon, equals('🔐'));
      });

      test('should return default icon for unknown error code', () {
        // Act
        final icon = BiometricErrorService.getErrorIcon('unknown_error');

        // Assert
        expect(icon, equals('⚠️'));
      });
    });

    group('BiometricAuthError extension', () {
      test(
        'should return setupPasscode action for biometric_passcode_not_set',
        () {
          // Arrange
          const authError = AuthError(
            code: 'biometric_passcode_not_set',
            userMessage: 'Passcode not set',
            technicalMessage: 'Technical message',
            severity: ErrorSeverity.warning,
            category: ErrorCategory.biometric,
          );

          // Act
          final action = authError.actionRequired;

          // Assert
          expect(action, equals(BiometricErrorAction.setupPasscode));
        },
      );

      test(
        'should return enrollBiometric action for biometric_not_enrolled',
        () {
          // Arrange
          const authError = AuthError(
            code: 'biometric_not_enrolled',
            userMessage: 'Not enrolled',
            technicalMessage: 'Technical message',
            severity: ErrorSeverity.warning,
            category: ErrorCategory.biometric,
          );

          // Act
          final action = authError.actionRequired;

          // Assert
          expect(action, equals(BiometricErrorAction.enrollBiometric));
        },
      );

      test('should return waitAndRetry action for biometric_locked_out', () {
        // Arrange
        const authError = AuthError(
          code: 'biometric_locked_out',
          userMessage: 'Locked out',
          technicalMessage: 'Technical message',
          severity: ErrorSeverity.error,
          category: ErrorCategory.biometric,
        );

        // Act
        final action = authError.actionRequired;

        // Assert
        expect(action, equals(BiometricErrorAction.waitAndRetry));
      });

      test(
        'should return waitAndRetry action for biometric_too_many_attempts',
        () {
          // Arrange
          const authError = AuthError(
            code: 'biometric_too_many_attempts',
            userMessage: 'Too many attempts',
            technicalMessage: 'Technical message',
            severity: ErrorSeverity.warning,
            category: ErrorCategory.biometric,
          );

          // Act
          final action = authError.actionRequired;

          // Assert
          expect(action, equals(BiometricErrorAction.waitAndRetry));
        },
      );

      test(
        'should return useAlternativeAuth action for biometric_permanently_locked_out',
        () {
          // Arrange
          const authError = AuthError(
            code: 'biometric_permanently_locked_out',
            userMessage: 'Permanently locked out',
            technicalMessage: 'Technical message',
            severity: ErrorSeverity.error,
            category: ErrorCategory.biometric,
          );

          // Act
          final action = authError.actionRequired;

          // Assert
          expect(action, equals(BiometricErrorAction.useAlternativeAuth));
        },
      );

      test('should return null action for unknown error code', () {
        // Arrange
        const authError = AuthError(
          code: 'unknown_error',
          userMessage: 'Unknown error',
          technicalMessage: 'Technical message',
          severity: ErrorSeverity.error,
          category: ErrorCategory.biometric,
        );

        // Act
        final action = authError.actionRequired;

        // Assert
        expect(action, isNull);
      });
    });

    group('BiometricErrorAction enum', () {
      test('should have all expected values', () {
        // Assert
        expect(BiometricErrorAction.values, hasLength(4));
        expect(
          BiometricErrorAction.values,
          contains(BiometricErrorAction.setupPasscode),
        );
        expect(
          BiometricErrorAction.values,
          contains(BiometricErrorAction.enrollBiometric),
        );
        expect(
          BiometricErrorAction.values,
          contains(BiometricErrorAction.waitAndRetry),
        );
        expect(
          BiometricErrorAction.values,
          contains(BiometricErrorAction.useAlternativeAuth),
        );
      });
    });
  });
}
