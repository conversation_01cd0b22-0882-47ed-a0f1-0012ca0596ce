import 'package:budapp/data/models/remote_config_data.dart';
import 'package:budapp/services/remote_config_service.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockFirebaseRemoteConfig extends Mock implements FirebaseRemoteConfig {}

class FakeRemoteConfigSettings extends Fake implements RemoteConfigSettings {}

void main() {
  setUpAll(() {
    registerFallbackValue(FakeRemoteConfigSettings());
  });

  group('RemoteConfigService', () {
    late MockFirebaseRemoteConfig mockRemoteConfig;
    late RemoteConfigService service;

    setUp(() {
      mockRemoteConfig = MockFirebaseRemoteConfig();
      service = RemoteConfigService(mockRemoteConfig);
    });

    group('initialize', () {
      test('should configure settings and set defaults successfully', () async {
        // Arrange
        when(
          () => mockRemoteConfig.setConfigSettings(any()),
        ).thenAnswer((_) async {});
        when(
          () => mockRemoteConfig.setDefaults(any()),
        ).thenAnswer((_) async {});

        // Act
        await service.initialize();

        // Assert
        verify(() => mockRemoteConfig.setConfigSettings(any())).called(1);
        verify(() => mockRemoteConfig.setDefaults(any())).called(1);
      });

      test('should throw exception when initialization fails', () async {
        // Arrange
        when(
          () => mockRemoteConfig.setConfigSettings(any()),
        ).thenThrow(Exception('Config failed'));

        // Act & Assert
        expect(() => service.initialize(), throwsException);
      });
    });

    group('fetchAndActivate', () {
      test('should return true when fetch and activate succeeds', () async {
        // Arrange
        when(
          () => mockRemoteConfig.fetchAndActivate(),
        ).thenAnswer((_) async => true);

        // Act
        final result = await service.fetchAndActivate();

        // Assert
        expect(result, isTrue);
        verify(() => mockRemoteConfig.fetchAndActivate()).called(1);
      });

      test('should return false and not throw when fetch fails', () async {
        // Arrange
        when(
          () => mockRemoteConfig.fetchAndActivate(),
        ).thenThrow(Exception('Network error'));

        // Act
        final result = await service.fetchAndActivate();

        // Assert
        expect(result, isFalse);
      });
    });

    group('getCurrentConfig', () {
      test('should return config with parsed values', () {
        // Arrange
        when(
          () => mockRemoteConfig.getString('predefined_income_categories'),
        ).thenReturn('["Salary", "Freelance"]');
        when(
          () => mockRemoteConfig.getString('predefined_expense_categories'),
        ).thenReturn('["Food", "Transport"]');
        when(() => mockRemoteConfig.getInt('max_accounts_free')).thenReturn(3);
        when(
          () => mockRemoteConfig.getInt('max_accounts_premium'),
        ).thenReturn(50);
        when(
          () => mockRemoteConfig.getInt('max_custom_categories_free'),
        ).thenReturn(5);
        when(
          () => mockRemoteConfig.getInt('max_custom_categories_premium'),
        ).thenReturn(100);
        when(() => mockRemoteConfig.getInt('max_budgets_free')).thenReturn(10);
        when(
          () => mockRemoteConfig.getInt('max_budgets_premium'),
        ).thenReturn(100);
        when(() => mockRemoteConfig.getInt('max_goals_free')).thenReturn(5);
        when(() => mockRemoteConfig.getInt('max_goals_premium')).thenReturn(50);
        when(
          () => mockRemoteConfig.getBool('enable_feature_x'),
        ).thenReturn(true);
        when(
          () => mockRemoteConfig.getBool('enable_beta_features'),
        ).thenReturn(false);

        // Act
        final config = service.getCurrentConfig();

        // Assert
        expect(
          config.categories.incomeCategories,
          equals(['Salary', 'Freelance']),
        );
        expect(
          config.categories.expenseCategories,
          equals(['Food', 'Transport']),
        );
        expect(config.premiumLimits.maxAccountsFree, equals(3));
        expect(config.premiumLimits.maxAccountsPremium, equals(50));
        expect(config.enableFeatureX, isTrue);
        expect(config.enableBetaFeatures, isFalse);
      });

      test('should return defaults when parsing fails', () {
        // Arrange
        when(
          () => mockRemoteConfig.getString(any()),
        ).thenThrow(Exception('Parse error'));
        when(
          () => mockRemoteConfig.getInt(any()),
        ).thenThrow(Exception('Parse error'));
        when(
          () => mockRemoteConfig.getBool(any()),
        ).thenThrow(Exception('Parse error'));

        // Act
        final config = service.getCurrentConfig();

        // Assert
        expect(config, equals(RemoteConfigData.defaults()));
      });
    });

    group('getValue', () {
      test('should return string value correctly', () {
        // Arrange
        when(
          () => mockRemoteConfig.getString('test_key'),
        ).thenReturn('test_value');

        // Act
        final result = service.getValue<String>(
          'test_key',
          defaultValue: 'default',
        );

        // Assert
        expect(result, equals('test_value'));
      });

      test('should return int value correctly', () {
        // Arrange
        when(() => mockRemoteConfig.getInt('test_key')).thenReturn(42);

        // Act
        final result = service.getValue<int>('test_key', defaultValue: 0);

        // Assert
        expect(result, equals(42));
      });

      test('should return bool value correctly', () {
        // Arrange
        when(() => mockRemoteConfig.getBool('test_key')).thenReturn(true);

        // Act
        final result = service.getValue<bool>('test_key', defaultValue: false);

        // Assert
        expect(result, isTrue);
      });

      test('should return double value correctly', () {
        // Arrange
        when(() => mockRemoteConfig.getDouble('test_key')).thenReturn(3.14);

        // Act
        final result = service.getValue<double>('test_key', defaultValue: 0);

        // Assert
        expect(result, equals(3.14));
      });

      test('should return default value when fetch fails', () {
        // Arrange
        when(
          () => mockRemoteConfig.getString('test_key'),
        ).thenThrow(Exception('Fetch error'));

        // Act
        final result = service.getValue<String>(
          'test_key',
          defaultValue: 'default',
        );

        // Assert
        expect(result, equals('default'));
      });

      test('should return default value for unsupported type', () {
        // Act
        final result = service.getValue<List<String>>(
          'test_key',
          defaultValue: ['default'],
        );

        // Assert
        expect(result, equals(['default']));
      });
    });

    group('isStale', () {
      test('should return true when last fetch is older than threshold', () {
        // Arrange - Use 10 minutes for debug mode threshold (5 minutes)
        final oldTime = DateTime.now().subtract(const Duration(minutes: 10));
        when(() => mockRemoteConfig.lastFetchTime).thenReturn(oldTime);

        // Act
        final result = service.isStale;

        // Assert
        expect(result, isTrue);
      });

      test('should return false when last fetch is recent', () {
        // Arrange - Use 1 minute for debug mode threshold (5 minutes)
        final recentTime = DateTime.now().subtract(const Duration(minutes: 1));
        when(() => mockRemoteConfig.lastFetchTime).thenReturn(recentTime);

        // Act
        final result = service.isStale;

        // Assert
        expect(result, isFalse);
      });
    });
  });
}
