import 'package:budapp/services/secure_storage_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock FlutterSecureStorage for testing
class MockFlutterSecureStorage extends Mock implements FlutterSecureStorage {}

void main() {
  group('SecureStorageService Tests', () {
    late SecureStorageService secureStorageService;
    late MockFlutterSecureStorage mockStorage;

    setUp(() {
      mockStorage = MockFlutterSecureStorage();
      secureStorageService = SecureStorageService(storage: mockStorage);
    });

    group('Basic Operations', () {
      test('write() stores data with prefixed key', () async {
        // Arrange
        const key = 'test_key';
        const value = 'test_value';
        when(
          () => mockStorage.write(
            key: any(named: 'key'),
            value: any(named: 'value'),
          ),
        ).thenAnswer((_) async {});

        // Act
        await secureStorageService.write(key, value);

        // Assert
        verify(
          () => mockStorage.write(key: 'budapp_$key', value: value),
        ).called(1);
      });

      test('read() retrieves data with prefixed key', () async {
        // Arrange
        const key = 'test_key';
        const value = 'test_value';
        when(
          () => mockStorage.read(key: any(named: 'key')),
        ).thenAnswer((_) async => value);

        // Act
        final result = await secureStorageService.read(key);

        // Assert
        expect(result, equals(value));
        verify(() => mockStorage.read(key: 'budapp_$key')).called(1);
      });

      test('read() returns null when key does not exist', () async {
        // Arrange
        const key = 'non_existent_key';
        when(
          () => mockStorage.read(key: any(named: 'key')),
        ).thenAnswer((_) async => null);

        // Act
        final result = await secureStorageService.read(key);

        // Assert
        expect(result, isNull);
        verify(() => mockStorage.read(key: 'budapp_$key')).called(1);
      });

      test('delete() removes data with prefixed key', () async {
        // Arrange
        const key = 'test_key';
        when(
          () => mockStorage.delete(key: any(named: 'key')),
        ).thenAnswer((_) async {});

        // Act
        await secureStorageService.delete(key);

        // Assert
        verify(() => mockStorage.delete(key: 'budapp_$key')).called(1);
      });

      test('deleteAll() clears all data', () async {
        // Arrange
        when(() => mockStorage.deleteAll()).thenAnswer((_) async {});

        // Act
        await secureStorageService.deleteAll();

        // Assert
        verify(() => mockStorage.deleteAll()).called(1);
      });
    });

    group('Key Management', () {
      test('containsKey() returns true when key exists', () async {
        // Arrange
        const key = 'existing_key';
        when(() => mockStorage.readAll()).thenAnswer(
          (_) async => {
            'budapp_$key': 'some_value',
            'budapp_other_key': 'other_value',
          },
        );

        // Act
        final result = await secureStorageService.containsKey(key);

        // Assert
        expect(result, isTrue);
        verify(() => mockStorage.readAll()).called(1);
      });

      test('containsKey() returns false when key does not exist', () async {
        // Arrange
        const key = 'non_existent_key';
        when(
          () => mockStorage.readAll(),
        ).thenAnswer((_) async => {'budapp_other_key': 'other_value'});

        // Act
        final result = await secureStorageService.containsKey(key);

        // Assert
        expect(result, isFalse);
        verify(() => mockStorage.readAll()).called(1);
      });

      test('getAllKeys() returns keys without prefix', () async {
        // Arrange
        when(() => mockStorage.readAll()).thenAnswer(
          (_) async => {
            'budapp_key1': 'value1',
            'budapp_key2': 'value2',
            'other_prefix_key': 'other_value', // Should be filtered out
          },
        );

        // Act
        final result = await secureStorageService.getAllKeys();

        // Assert
        expect(result, equals(['key1', 'key2']));
        verify(() => mockStorage.readAll()).called(1);
      });

      test('getAllKeys() returns empty list when no keys exist', () async {
        // Arrange
        when(() => mockStorage.readAll()).thenAnswer((_) async => {});

        // Act
        final result = await secureStorageService.getAllKeys();

        // Assert
        expect(result, isEmpty);
        verify(() => mockStorage.readAll()).called(1);
      });
    });

    group('Error Handling', () {
      test('write() throws SecureStorageException on error', () async {
        // Arrange
        const key = 'test_key';
        const value = 'test_value';
        final exception = Exception('Storage error');
        when(
          () => mockStorage.write(
            key: any(named: 'key'),
            value: any(named: 'value'),
          ),
        ).thenThrow(exception);

        // Act & Assert
        expect(
          () => secureStorageService.write(key, value),
          throwsA(isA<SecureStorageException>()),
        );
      });

      test('read() throws SecureStorageException on error', () async {
        // Arrange
        const key = 'test_key';
        final exception = Exception('Storage error');
        when(
          () => mockStorage.read(key: any(named: 'key')),
        ).thenThrow(exception);

        // Act & Assert
        expect(
          () => secureStorageService.read(key),
          throwsA(isA<SecureStorageException>()),
        );
      });

      test('delete() throws SecureStorageException on error', () async {
        // Arrange
        const key = 'test_key';
        final exception = Exception('Storage error');
        when(
          () => mockStorage.delete(key: any(named: 'key')),
        ).thenThrow(exception);

        // Act & Assert
        expect(
          () => secureStorageService.delete(key),
          throwsA(isA<SecureStorageException>()),
        );
      });

      test('containsKey() throws SecureStorageException on error', () async {
        // Arrange
        const key = 'test_key';
        final exception = Exception('Storage error');
        when(() => mockStorage.readAll()).thenThrow(exception);

        // Act & Assert
        expect(
          () => secureStorageService.containsKey(key),
          throwsA(isA<SecureStorageException>()),
        );
      });

      test('getAllKeys() throws SecureStorageException on error', () async {
        // Arrange
        final exception = Exception('Storage error');
        when(() => mockStorage.readAll()).thenThrow(exception);

        // Act & Assert
        expect(
          () => secureStorageService.getAllKeys(),
          throwsA(isA<SecureStorageException>()),
        );
      });

      test('deleteAll() throws SecureStorageException on error', () async {
        // Arrange
        final exception = Exception('Storage error');
        when(() => mockStorage.deleteAll()).thenThrow(exception);

        // Act & Assert
        expect(
          () => secureStorageService.deleteAll(),
          throwsA(isA<SecureStorageException>()),
        );
      });
    });

    group('Future-Ready Methods', () {
      test('storeBiometricKey() stores biometric key', () async {
        // Arrange
        const key = 'test_biometric_key';
        when(
          () => mockStorage.write(
            key: any(named: 'key'),
            value: any(named: 'value'),
          ),
        ).thenAnswer((_) async {});

        // Act
        await secureStorageService.storeBiometricKey(key);

        // Assert
        verify(
          () => mockStorage.write(key: 'budapp_biometric_key', value: key),
        ).called(1);
      });

      test('getBiometricKey() retrieves biometric key', () async {
        // Arrange
        const key = 'test_biometric_key';
        when(
          () => mockStorage.read(key: any(named: 'key')),
        ).thenAnswer((_) async => key);

        // Act
        final result = await secureStorageService.getBiometricKey();

        // Assert
        expect(result, equals(key));
        verify(() => mockStorage.read(key: 'budapp_biometric_key')).called(1);
      });

      test('storeApiKey() stores API key with service name', () async {
        // Arrange
        const serviceName = 'plaid';
        const apiKey = 'test_api_key';
        when(
          () => mockStorage.write(
            key: any(named: 'key'),
            value: any(named: 'value'),
          ),
        ).thenAnswer((_) async {});

        // Act
        await secureStorageService.storeApiKey(serviceName, apiKey);

        // Assert
        verify(
          () => mockStorage.write(
            key: 'budapp_api_key_$serviceName',
            value: apiKey,
          ),
        ).called(1);
      });

      test('getApiKey() retrieves API key by service name', () async {
        // Arrange
        const serviceName = 'plaid';
        const apiKey = 'test_api_key';
        when(
          () => mockStorage.read(key: any(named: 'key')),
        ).thenAnswer((_) async => apiKey);

        // Act
        final result = await secureStorageService.getApiKey(serviceName);

        // Assert
        expect(result, equals(apiKey));
        verify(
          () => mockStorage.read(key: 'budapp_api_key_$serviceName'),
        ).called(1);
      });

      test('storeUserPin() stores user PIN hash', () async {
        // Arrange
        const pinHash = 'hashed_pin_value';
        when(
          () => mockStorage.write(
            key: any(named: 'key'),
            value: any(named: 'value'),
          ),
        ).thenAnswer((_) async {});

        // Act
        await secureStorageService.storeUserPin(pinHash);

        // Assert
        verify(
          () => mockStorage.write(key: 'budapp_user_pin_hash', value: pinHash),
        ).called(1);
      });

      test('getUserPinHash() retrieves user PIN hash', () async {
        // Arrange
        const pinHash = 'hashed_pin_value';
        when(
          () => mockStorage.read(key: any(named: 'key')),
        ).thenAnswer((_) async => pinHash);

        // Act
        final result = await secureStorageService.getUserPinHash();

        // Assert
        expect(result, equals(pinHash));
        verify(() => mockStorage.read(key: 'budapp_user_pin_hash')).called(1);
      });
    });
  });

  group('SecureStorageException Tests', () {
    test('SecureStorageException creates correctly', () {
      const exception = SecureStorageException(
        'Test message',
        key: 'test_key',
        originalError: 'Original error',
      );

      expect(exception.message, equals('Test message'));
      expect(exception.key, equals('test_key'));
      expect(exception.originalError, equals('Original error'));
    });

    test('SecureStorageException toString works correctly', () {
      const exception = SecureStorageException(
        'Test message',
        key: 'test_key',
        originalError: 'Original error',
      );

      final stringValue = exception.toString();
      expect(stringValue, contains('Test message'));
      expect(stringValue, contains('test_key'));
      expect(stringValue, contains('Original error'));
    });

    test('SecureStorageException toString works without optional fields', () {
      const exception = SecureStorageException('Test message');

      final stringValue = exception.toString();
      expect(stringValue, contains('Test message'));
      expect(stringValue, isNot(contains('key:')));
      expect(stringValue, isNot(contains(' - ')));
    });
  });

  group('Service Integration', () {
    test('SecureStorageService creates with default storage', () {
      // Act
      final service = SecureStorageService();

      // Assert
      expect(service, isA<SecureStorageService>());
    });

    test('SecureStorageService creates with custom storage', () {
      // Arrange
      final customStorage = MockFlutterSecureStorage();

      // Act
      final service = SecureStorageService(storage: customStorage);

      // Assert
      expect(service, isA<SecureStorageService>());
    });
  });
}
