import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/services/error_service.dart';
import 'package:budapp/services/global_error_handler.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockGlobalErrorHandler extends Mock implements GlobalErrorHandler {}

class MockFirebaseAuthException extends Mock implements FirebaseAuthException {
  MockFirebaseAuthException({required String code, String? message})
    : _code = code,
      _message = message;

  final String _code;
  final String? _message;

  @override
  String get code => _code;

  @override
  String? get message => _message;

  @override
  String toString() => 'FirebaseAuthException: $_code - $_message';
}

class MockFirebaseException extends Mock implements FirebaseException {
  MockFirebaseException({required String code, String? message})
    : _code = code,
      _message = message;

  final String _code;
  final String? _message;

  @override
  String get code => _code;

  @override
  String? get message => _message;

  @override
  String toString() => 'FirebaseException: $_code - $_message';
}

class MockFormatException implements FormatException {
  MockFormatException(this.message, [this.source, this.offset]);

  @override
  final String message;

  @override
  final dynamic source;

  @override
  final int? offset;

  @override
  String toString() => 'FormatException: $message';
}

class MockArgumentError implements ArgumentError {
  MockArgumentError(this.message, [this.name]);

  @override
  final dynamic message;

  @override
  final String? name;

  @override
  String? get invalidValue => null;

  @override
  StackTrace? get stackTrace => null;

  @override
  String toString() => 'ArgumentError: $message';
}

void main() {
  group('ErrorService', () {
    late MockGlobalErrorHandler mockGlobalErrorHandler;
    late AppLocalizations l10n;

    setUpAll(() {
      registerFallbackValue(Exception('test'));
      registerFallbackValue(StackTrace.empty);
    });

    setUp(() {
      mockGlobalErrorHandler = MockGlobalErrorHandler();

      // Mock GlobalErrorHandler singleton
      when(() => mockGlobalErrorHandler.isInitialized).thenReturn(true);
      when(
        () => mockGlobalErrorHandler.reportError(
          any(),
          any(),
          reason: any(named: 'reason'),
          customKeys: any(named: 'customKeys'),
          fatal: any(named: 'fatal'),
        ),
      ).thenAnswer((_) async {});
      when(
        () => mockGlobalErrorHandler.setUserIdentifier(any()),
      ).thenAnswer((_) async {});
      when(() => mockGlobalErrorHandler.logBreadcrumb(any())).thenReturn(null);
    });

    /// Helper widget to wrap tests with localization
    Widget createTestWidget({required Widget child}) {
      return MaterialApp(
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [Locale('en', '')],
        home: Scaffold(body: child),
      );
    }

    /// Helper to get localized strings for testing
    Future<AppLocalizations> getLocalizations(WidgetTester tester) async {
      late AppLocalizations result;
      await tester.pumpWidget(
        createTestWidget(
          child: Builder(
            builder: (context) {
              result = AppLocalizations.of(context)!;
              return const SizedBox();
            },
          ),
        ),
      );
      return result;
    }

    group('getUserFriendlyMessage', () {
      testWidgets('returns correct message for FirebaseAuthException codes', (
        tester,
      ) async {
        l10n = await getLocalizations(tester);

        final testCases = {
          'user-not-found': l10n.userNotFoundError,
          'wrong-password': l10n.wrongPasswordError,
          'email-already-in-use': l10n.emailAlreadyInUseError,
          'weak-password': l10n.weakPasswordError,
          'invalid-email': l10n.invalidEmailError,
          'user-disabled': l10n.userDisabledError,
          'too-many-requests': l10n.tooManyRequestsError,
          'operation-not-allowed': l10n.operationNotAllowedError,
          'requires-recent-login': l10n.requiresRecentLoginError,
          'unknown-code': l10n.authenticationError, // Default case
        };

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                for (final entry in testCases.entries) {
                  final error = MockFirebaseAuthException(code: entry.key);
                  final message = ErrorService.getUserFriendlyMessage(
                    context,
                    error,
                  );
                  expect(message, equals(entry.value));
                }
                return const SizedBox();
              },
            ),
          ),
        );
      });

      testWidgets('returns correct message for FirebaseException codes', (
        tester,
      ) async {
        l10n = await getLocalizations(tester);

        final testCases = {
          'permission-denied': l10n.permissionDeniedError,
          'unavailable': l10n.serviceUnavailableError,
          'deadline-exceeded': l10n.timeoutError,
          'not-found': l10n.documentNotFoundError,
          'already-exists': l10n.documentAlreadyExistsError,
          'resource-exhausted': l10n.quotaExceededError,
          'failed-precondition': l10n.preconditionFailedError,
          'aborted': l10n.operationAbortedError,
          'out-of-range': l10n.outOfRangeError,
          'unimplemented': l10n.featureNotImplementedError,
          'internal': l10n.internalError,
          'data-loss': l10n.dataLossError,
          'unauthenticated': l10n.unauthenticatedError,
          'unknown-code': l10n.databaseError, // Default case
        };

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                for (final entry in testCases.entries) {
                  final error = MockFirebaseException(code: entry.key);
                  final message = ErrorService.getUserFriendlyMessage(
                    context,
                    error,
                  );
                  expect(message, equals(entry.value));
                }
                return const SizedBox();
              },
            ),
          ),
        );
      });

      testWidgets('returns correct message for FormatException types', (
        tester,
      ) async {
        l10n = await getLocalizations(tester);

        final testCases = [
          (
            MockFormatException('Invalid date format'),
            l10n.invalidDateFormatError,
          ),
          (
            MockFormatException('Invalid number format'),
            l10n.invalidNumberFormatError,
          ),
          (MockFormatException('Other format error'), l10n.invalidFormatError),
        ];

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                for (final testCase in testCases) {
                  final message = ErrorService.getUserFriendlyMessage(
                    context,
                    testCase.$1,
                  );
                  expect(message, equals(testCase.$2));
                }
                return const SizedBox();
              },
            ),
          ),
        );
      });

      testWidgets('returns correct message for ArgumentError', (tester) async {
        l10n = await getLocalizations(tester);

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                final error = MockArgumentError('Invalid argument');
                final message = ErrorService.getUserFriendlyMessage(
                  context,
                  error,
                );
                expect(message, equals(l10n.invalidArgumentError));
                return const SizedBox();
              },
            ),
          ),
        );
      });

      testWidgets('returns correct message for common error patterns', (
        tester,
      ) async {
        l10n = await getLocalizations(tester);

        final testCases = [
          (Exception('Network connection failed'), l10n.networkError),
          (Exception('Connection timeout'), l10n.networkError),
          (Exception('Permission denied access'), l10n.permissionError),
          (Exception('Unauthorized user'), l10n.permissionError),
          (Exception('Item not found'), l10n.notFoundError),
          (Exception('Resource does not exist'), l10n.notFoundError),
        ];

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                for (final testCase in testCases) {
                  final message = ErrorService.getUserFriendlyMessage(
                    context,
                    testCase.$1,
                  );
                  expect(message, equals(testCase.$2));
                }
                return const SizedBox();
              },
            ),
          ),
        );
      });

      testWidgets('returns generic error for unknown errors', (tester) async {
        l10n = await getLocalizations(tester);

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                final error = Exception('Some unknown error');
                final message = ErrorService.getUserFriendlyMessage(
                  context,
                  error,
                );
                expect(message, equals(l10n.genericError));
                return const SizedBox();
              },
            ),
          ),
        );
      });

      testWidgets('returns fallback message when provided', (tester) async {
        const fallbackMessage = 'Custom fallback message';

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                final error = Exception('Some unknown error');
                final message = ErrorService.getUserFriendlyMessage(
                  context,
                  error,
                  fallbackMessage: fallbackMessage,
                );
                expect(message, equals(fallbackMessage));
                return const SizedBox();
              },
            ),
          ),
        );
      });
    });

    group('showErrorSnackBar', () {
      testWidgets('displays error snackbar with correct styling', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    ErrorService.showErrorSnackBar(
                      context,
                      Exception('Test error'),
                    );
                  },
                  child: const Text('Show Error'),
                );
              },
            ),
          ),
        );

        // Tap the button to show the snackbar
        await tester.tap(find.text('Show Error'));
        await tester.pump();

        // Verify snackbar is displayed
        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
        expect(find.text('Dismiss'), findsOneWidget);

        // Verify the error message is shown
        l10n = await getLocalizations(tester);
        expect(find.text(l10n.genericError), findsOneWidget);
      });

      testWidgets('uses custom duration when provided', (tester) async {
        const customDuration = Duration(seconds: 10);

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    ErrorService.showErrorSnackBar(
                      context,
                      Exception('Test error'),
                      duration: customDuration,
                    );
                  },
                  child: const Text('Show Error'),
                );
              },
            ),
          ),
        );

        await tester.tap(find.text('Show Error'));
        await tester.pump();

        // Find the SnackBar widget and verify its duration
        final snackBar = tester.widget<SnackBar>(find.byType(SnackBar));
        expect(snackBar.duration, equals(customDuration));
      });

      testWidgets('dismisses snackbar when dismiss button is pressed', (
        tester,
      ) async {
        late BuildContext testContext;

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                testContext = context;
                return ElevatedButton(
                  onPressed: () {
                    ErrorService.showErrorSnackBar(
                      context,
                      Exception('Test error'),
                    );
                  },
                  child: const Text('Show Error'),
                );
              },
            ),
          ),
        );

        await tester.tap(find.text('Show Error'));
        await tester.pump();

        // Verify snackbar is displayed
        expect(find.byType(SnackBar), findsOneWidget);

        // Manually dismiss the snackbar to test the dismiss functionality
        ScaffoldMessenger.of(testContext).hideCurrentSnackBar();
        await tester.pumpAndSettle();

        // Verify snackbar is dismissed
        expect(find.byType(SnackBar), findsNothing);
      });
    });

    group('showSuccessSnackBar', () {
      testWidgets('displays success snackbar with correct styling', (
        tester,
      ) async {
        const successMessage = 'Operation completed successfully';

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    ErrorService.showSuccessSnackBar(context, successMessage);
                  },
                  child: const Text('Show Success'),
                );
              },
            ),
          ),
        );

        await tester.tap(find.text('Show Success'));
        await tester.pump();

        // Verify snackbar is displayed
        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.byIcon(Icons.check_circle_outline), findsOneWidget);
        expect(find.text(successMessage), findsOneWidget);

        // Verify default duration
        final snackBar = tester.widget<SnackBar>(find.byType(SnackBar));
        expect(snackBar.duration, equals(const Duration(seconds: 3)));
      });

      testWidgets('uses custom duration when provided', (tester) async {
        const customDuration = Duration(seconds: 5);
        const successMessage = 'Custom duration success';

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    ErrorService.showSuccessSnackBar(
                      context,
                      successMessage,
                      duration: customDuration,
                    );
                  },
                  child: const Text('Show Success'),
                );
              },
            ),
          ),
        );

        await tester.tap(find.text('Show Success'));
        await tester.pump();

        final snackBar = tester.widget<SnackBar>(find.byType(SnackBar));
        expect(snackBar.duration, equals(customDuration));
      });
    });

    group('showInfoSnackBar', () {
      testWidgets('displays info snackbar with correct styling', (
        tester,
      ) async {
        const infoMessage = 'Information message';

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    ErrorService.showInfoSnackBar(context, infoMessage);
                  },
                  child: const Text('Show Info'),
                );
              },
            ),
          ),
        );

        await tester.tap(find.text('Show Info'));
        await tester.pump();

        // Verify snackbar is displayed
        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.byIcon(Icons.info_outline), findsOneWidget);
        expect(find.text(infoMessage), findsOneWidget);

        // Verify default duration
        final snackBar = tester.widget<SnackBar>(find.byType(SnackBar));
        expect(snackBar.duration, equals(const Duration(seconds: 3)));
      });
    });

    group('showErrorDialog', () {
      testWidgets('displays error dialog with default title', (tester) async {
        late bool dialogResult;

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () async {
                    dialogResult = await ErrorService.showErrorDialog(
                      context,
                      Exception('Test error'),
                    );
                  },
                  child: const Text('Show Dialog'),
                );
              },
            ),
          ),
        );

        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        // Verify dialog is displayed
        expect(find.byType(AlertDialog), findsOneWidget);
        l10n = await getLocalizations(tester);
        expect(find.text(l10n.errorDialogTitle), findsOneWidget);
        expect(find.text(l10n.genericError), findsOneWidget);
        expect(find.text(l10n.ok), findsOneWidget);

        // Tap OK and verify result
        await tester.tap(find.text(l10n.ok));
        await tester.pumpAndSettle();
        expect(dialogResult, false);
      });

      testWidgets('displays custom title when provided', (tester) async {
        const customTitle = 'Custom Error Title';

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () async {
                    await ErrorService.showErrorDialog(
                      context,
                      Exception('Test error'),
                      title: customTitle,
                    );
                  },
                  child: const Text('Show Dialog'),
                );
              },
            ),
          ),
        );

        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        expect(find.text(customTitle), findsOneWidget);
      });

      testWidgets('shows retry button when enabled', (tester) async {
        late bool dialogResult;

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () async {
                    dialogResult = await ErrorService.showErrorDialog(
                      context,
                      Exception('Test error'),
                      showRetryButton: true,
                    );
                  },
                  child: const Text('Show Dialog'),
                );
              },
            ),
          ),
        );

        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        l10n = await getLocalizations(tester);
        expect(find.text(l10n.retry), findsOneWidget);

        // Tap retry and verify result
        await tester.tap(find.text(l10n.retry));
        await tester.pumpAndSettle();
        expect(dialogResult, true);
      });

      testWidgets('returns false when dialog is dismissed', (tester) async {
        late bool dialogResult;

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () async {
                    dialogResult = await ErrorService.showErrorDialog(
                      context,
                      Exception('Test error'),
                    );
                  },
                  child: const Text('Show Dialog'),
                );
              },
            ),
          ),
        );

        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        // Dismiss dialog by tapping outside
        await tester.tapAt(const Offset(10, 10));
        await tester.pumpAndSettle();
        expect(dialogResult, false);
      });
    });

    group('logError', () {
      setUp(() {
        // Reset debug mode for each test
        debugDefaultTargetPlatformOverride = null;
      });

      test('logs error in debug mode with print statements', () async {
        // Mock debug mode
        expect(kDebugMode, true); // Verify we're in debug mode during tests

        const testContext = 'Test Context';
        final testError = Exception('Test error');
        final testStackTrace = StackTrace.current;
        final testData = {'key': 'value'};

        // This test verifies the print statements are called in debug mode
        // We can't easily capture print output, but we can verify the method runs
        await ErrorService.logError(
          testError,
          testStackTrace,
          context: testContext,
          additionalData: testData,
          fatal: false,
        );

        // Verify the method completed successfully (no exceptions thrown)
      });

      test('reports error to GlobalErrorHandler when initialized', () async {
        const testContext = 'Test Context';
        final testError = Exception('Test error');
        final testStackTrace = StackTrace.current;
        final testData = {'key': 'value'};

        // We need to use a more direct approach since we can't mock the singleton
        await ErrorService.logError(
          testError,
          testStackTrace,
          context: testContext,
          additionalData: testData,
          fatal: true,
        );

        // Since we can't easily mock the singleton, we verify no exceptions are thrown
        // The actual Crashlytics integration would be tested separately
      });

      test('handles reporting failure gracefully', () async {
        final testError = Exception('Test error');

        // This should not throw even if error reporting fails
        await expectLater(
          () => ErrorService.logError(testError, null),
          returnsNormally,
        );
      });
    });

    group('setUserContext', () {
      test(
        'sets user identifier when GlobalErrorHandler is initialized',
        () async {
          const testUserId = 'test-user-123';

          // This should not throw
          await expectLater(
            () => ErrorService.setUserContext(testUserId),
            returnsNormally,
          );
        },
      );

      test('handles null user ID', () async {
        await expectLater(
          () => ErrorService.setUserContext(null),
          returnsNormally,
        );
      });

      test('handles error setting user context gracefully', () async {
        const testUserId = 'test-user-123';

        // Should not throw even if setting user context fails
        await expectLater(
          () => ErrorService.setUserContext(testUserId),
          returnsNormally,
        );
      });
    });

    group('logUserAction', () {
      test('logs user action without data', () {
        const testAction = 'button_clicked';

        // This should not throw
        expect(() => ErrorService.logUserAction(testAction), returnsNormally);
      });

      test('logs user action with additional data', () {
        const testAction = 'transaction_created';
        final testData = {'amount': 100, 'category': 'food'};

        // This should not throw
        expect(
          () => ErrorService.logUserAction(testAction, testData),
          returnsNormally,
        );
      });

      test('handles error logging user action gracefully', () {
        const testAction = 'test_action';

        // Should not throw even if logging fails
        expect(() => ErrorService.logUserAction(testAction), returnsNormally);
      });
    });

    group('showErrorSnackBarWithLogging', () {
      testWidgets('shows snackbar and logs error', (tester) async {
        const testContext = 'UI Error Test';
        final testError = Exception('Test error');
        final testData = {'screen': 'test_screen'};

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    ErrorService.showErrorSnackBarWithLogging(
                      context,
                      testError,
                      errorContext: testContext,
                      additionalData: testData,
                    );
                  },
                  child: const Text('Show Error'),
                );
              },
            ),
          ),
        );

        await tester.tap(find.text('Show Error'));
        await tester.pump();

        // Verify snackbar is displayed
        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
      });

      testWidgets('uses default context when not provided', (tester) async {
        final testError = Exception('Test error');

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    ErrorService.showErrorSnackBarWithLogging(
                      context,
                      testError,
                    );
                  },
                  child: const Text('Show Error'),
                );
              },
            ),
          ),
        );

        await tester.tap(find.text('Show Error'));
        await tester.pump();

        // Verify snackbar is displayed
        expect(find.byType(SnackBar), findsOneWidget);
      });
    });

    group('showErrorDialogWithLogging', () {
      testWidgets('shows dialog and logs error', (tester) async {
        const testContext = 'Dialog Error Test';
        final testError = Exception('Test error');
        final testData = {'dialog_type': 'error'};
        late bool dialogResult;

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () async {
                    dialogResult =
                        await ErrorService.showErrorDialogWithLogging(
                          context,
                          testError,
                          errorContext: testContext,
                          additionalData: testData,
                        );
                  },
                  child: const Text('Show Dialog'),
                );
              },
            ),
          ),
        );

        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        // Verify dialog is displayed
        expect(find.byType(AlertDialog), findsOneWidget);

        l10n = await getLocalizations(tester);
        await tester.tap(find.text(l10n.ok));
        await tester.pumpAndSettle();
        expect(dialogResult, false);
      });

      testWidgets('uses default context when not provided', (tester) async {
        final testError = Exception('Test error');
        late bool dialogResult;

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () async {
                    dialogResult =
                        await ErrorService.showErrorDialogWithLogging(
                          context,
                          testError,
                        );
                  },
                  child: const Text('Show Dialog'),
                );
              },
            ),
          ),
        );

        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        // Verify dialog is displayed
        expect(find.byType(AlertDialog), findsOneWidget);

        l10n = await getLocalizations(tester);
        await tester.tap(find.text(l10n.ok));
        await tester.pumpAndSettle();
        expect(dialogResult, false);
      });
    });

    group('Edge Cases', () {
      testWidgets('handles very long error messages', (tester) async {
        final longMessage = 'Very long error message. ' * 100;
        final longError = Exception(longMessage);

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    ErrorService.showErrorSnackBar(context, longError);
                  },
                  child: const Text('Show Error'),
                );
              },
            ),
          ),
        );

        await tester.tap(find.text('Show Error'));
        await tester.pump();

        // Verify snackbar is displayed (should handle long messages gracefully)
        expect(find.byType(SnackBar), findsOneWidget);
      });

      testWidgets('handles errors with null messages', (tester) async {
        final nullMessageError = MockFirebaseException(code: 'test-code');

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                final message = ErrorService.getUserFriendlyMessage(
                  context,
                  nullMessageError,
                );
                expect(message, isNotNull);
                expect(message, isNotEmpty);
                return const SizedBox();
              },
            ),
          ),
        );
      });

      testWidgets('handles concurrent error dialogs', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    // Show multiple dialogs rapidly
                    ErrorService.showErrorDialog(context, Exception('Error 1'));
                    ErrorService.showErrorDialog(context, Exception('Error 2'));
                  },
                  child: const Text('Show Multiple Dialogs'),
                );
              },
            ),
          ),
        );

        await tester.tap(find.text('Show Multiple Dialogs'));
        await tester.pumpAndSettle();

        // Should handle multiple dialogs gracefully
        expect(find.byType(AlertDialog), findsWidgets);
      });
    });
  });
}
