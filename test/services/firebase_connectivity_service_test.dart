import 'package:budapp/services/implementations/firebase_connectivity_service_impl.dart';
import 'package:budapp/services/interfaces/firebase_connectivity_service.dart';
import 'package:flutter_test/flutter_test.dart';

import '../helpers/firebase_test_setup.dart';

void main() {
  group('FirebaseConnectivityServiceImpl Tests', () {
    late IFirebaseConnectivityService connectivityService;
    late FirebaseTestSetup testSetup;

    setUp(() async {
      testSetup = await FirebaseTestSetup.create();
      connectivityService = FirebaseConnectivityServiceImpl(
        firebaseAuth: testSetup.auth,
        firestore: testSetup.firestore,
      );
    });

    group('testAuthConnectivity', () {
      test(
        'should return connected status when user is authenticated',
        () async {
          // Arrange - Create setup with authenticated user
          final authSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
            uid: 'test-uid',
            email: '<EMAIL>',
          );
          final authService = FirebaseConnectivityServiceImpl(
            firebaseAuth: authSetup.auth,
            firestore: authSetup.firestore,
          );

          // Act
          final result = await authService.testAuthConnectivity();

          // Assert
          expect(result['status'], equals('connected'));
          expect(result['uid'], equals('test-uid'));
          expect(result['email'], equals('<EMAIL>'));
        },
      );

      test(
        'should return not_authenticated status when user is null',
        () async {
          // Arrange - Create setup without authenticated user
          final unauthSetup = await FirebaseTestSetup.createUnauthenticated();
          final unauthService = FirebaseConnectivityServiceImpl(
            firebaseAuth: unauthSetup.auth,
            firestore: unauthSetup.firestore,
          );

          // Act
          final result = await unauthService.testAuthConnectivity();

          // Assert
          expect(result['status'], equals('not_authenticated'));
          expect(result['uid'], isNull);
          expect(result['email'], isNull);
        },
      );

      // Note: Testing error scenarios is more complex with fake Firebase services
      // For now, we focus on the main happy path and not authenticated scenarios
      // Error handling can be tested through integration tests
    });

    group('testFirestoreConnectivity', () {
      test(
        'should return connected status when Firestore operations succeed',
        () async {
          // Act - No arrange needed, fake Firestore works out of the box
          final result = await connectivityService.testFirestoreConnectivity();

          // Assert
          expect(result['status'], equals('connected'));
          expect(result['hasData'], isTrue);
          expect(result['timestamp'], isA<String>());

          // Verify document was actually written and can be read
          final doc = await testSetup.firestore
              .collection('test')
              .doc('connectivity')
              .get();
          expect(doc.exists, isTrue);
        },
      );

      // Note: With fake Firestore, documents are automatically created/updated
      // Error scenarios are better tested through integration tests
    });

    group('testFirebaseConnectivity', () {
      test(
        'should return combined results from auth and firestore tests',
        () async {
          // Arrange - Create setup with authenticated user
          final authSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
            uid: 'test-uid',
            email: '<EMAIL>',
          );
          final authService = FirebaseConnectivityServiceImpl(
            firebaseAuth: authSetup.auth,
            firestore: authSetup.firestore,
          );

          // Act
          final result = await authService.testFirebaseConnectivity();

          // Assert
          expect(result, containsPair('auth', isA<Map<String, dynamic>>()));
          expect(
            result,
            containsPair('firestore', isA<Map<String, dynamic>>()),
          );
          expect(
            (result['auth'] as Map<String, dynamic>)['status'],
            equals('connected'),
          );
          expect(
            (result['firestore'] as Map<String, dynamic>)['status'],
            equals('connected'),
          );
        },
      );

      test('should handle mixed auth scenarios', () async {
        // Arrange - No auth user
        await testSetup.auth.signOut();

        // Act
        final result = await connectivityService.testFirebaseConnectivity();

        // Assert
        expect(
          (result['auth'] as Map<String, dynamic>)['status'],
          equals('not_authenticated'),
        );
        expect(
          (result['firestore'] as Map<String, dynamic>)['status'],
          equals('connected'),
        );
      });
    });

    group('areFirebaseServicesAvailable', () {
      test(
        'should return true when both auth and firestore are available',
        () async {
          // Arrange - Create setup with authenticated user
          final authSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
            uid: 'test-uid',
            email: '<EMAIL>',
          );
          final authService = FirebaseConnectivityServiceImpl(
            firebaseAuth: authSetup.auth,
            firestore: authSetup.firestore,
          );

          // Act
          final result = await authService.areFirebaseServicesAvailable();

          // Assert
          expect(result, isTrue);
        },
      );

      test(
        'should return true when auth is not authenticated but firestore works',
        () async {
          // Arrange - Ensure no user is signed in
          await testSetup.auth.signOut();

          // Act
          final result = await connectivityService
              .areFirebaseServicesAvailable();

          // Assert
          expect(
            result,
            isTrue,
          ); // Auth not authenticated is still considered "available"
        },
      );
    });

    group('Edge Cases', () {
      test('should handle Firestore timestamp operations correctly', () async {
        // Act
        final result = await connectivityService.testFirestoreConnectivity();

        // Assert
        expect(result['timestamp'], isA<String>());
        // Verify that the timestamp is a valid ISO string
        expect(
          () => DateTime.parse(result['timestamp'] as String),
          returnsNormally,
        );

        // Verify the document was created with proper timestamp
        final doc = await testSetup.firestore
            .collection('test')
            .doc('connectivity')
            .get();
        expect(doc.exists, isTrue);
        expect(doc.data(), containsPair('timestamp', isNotNull));
      });
    });
  });
}
