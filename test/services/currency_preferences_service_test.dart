import 'package:budapp/services/currency_preferences_service.dart';
import 'package:budapp/services/currency_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('CurrencyPreferencesService', () {
    late CurrencyPreferencesService service;
    late SharedPreferences prefs;

    setUp(() async {
      // Initialize SharedPreferences with in-memory storage for testing
      SharedPreferences.setMockInitialValues({});
      prefs = await SharedPreferences.getInstance();
      service = CurrencyPreferencesService(prefs);
    });

    tearDown(() async {
      await prefs.clear();
    });

    group('getCurrentCurrency', () {
      test('should return default currency when no preference is set', () {
        final result = service.getCurrentCurrency();
        expect(result, equals(CurrencyService.defaultCurrencyCode));
      });

      test('should return stored valid currency preference', () async {
        await prefs.setString('user_currency_preference', 'EUR');
        final result = service.getCurrentCurrency();
        expect(result, equals('EUR'));
      });

      test(
        'should return default currency when stored currency is invalid',
        () async {
          await prefs.setString('user_currency_preference', 'INVALID');
          final result = service.getCurrentCurrency();
          expect(result, equals(CurrencyService.defaultCurrencyCode));
        },
      );

      test('should handle lowercase currency codes from storage', () async {
        await prefs.setString('user_currency_preference', 'eur');
        final result = service.getCurrentCurrency();
        expect(result, equals('eur')); // Returns as stored if valid
      });
    });

    group('setCurrency', () {
      test('should successfully set valid currency code', () async {
        final result = await service.setCurrency('EUR');
        expect(result, isTrue);
        expect(service.getCurrentCurrency(), equals('EUR'));
      });

      test('should convert currency code to uppercase when setting', () async {
        await service.setCurrency('eur');
        final storedValue = prefs.getString('user_currency_preference');
        expect(storedValue, equals('EUR'));
      });

      test('should throw ArgumentError for invalid currency code', () async {
        expect(
          () => service.setCurrency('INVALID'),
          throwsA(
            isA<ArgumentError>().having(
              (e) => e.message,
              'message',
              contains('Invalid currency code: INVALID'),
            ),
          ),
        );
      });

      test('should not store invalid currency code', () async {
        expect(() => service.setCurrency('INVALID'), throwsArgumentError);
        expect(service.hasCurrencyPreference(), isFalse);
      });

      test('should handle common valid currency codes', () async {
        final validCodes = ['USD', 'EUR', 'GBP', 'JPY', 'CAD'];

        for (final code in validCodes) {
          await service.setCurrency(code);
          expect(service.getCurrentCurrency(), equals(code));
        }
      });
    });

    group('hasCurrencyPreference', () {
      test('should return false when no preference is set', () {
        expect(service.hasCurrencyPreference(), isFalse);
      });

      test('should return true when preference is set', () async {
        await service.setCurrency('EUR');
        expect(service.hasCurrencyPreference(), isTrue);
      });

      test('should return true even for invalid stored values', () async {
        await prefs.setString('user_currency_preference', 'INVALID');
        expect(service.hasCurrencyPreference(), isTrue);
      });
    });

    group('resetCurrencyToDefault', () {
      test('should remove currency preference', () async {
        await service.setCurrency('EUR');
        expect(service.hasCurrencyPreference(), isTrue);

        final result = await service.resetCurrencyToDefault();
        expect(result, isTrue);
        expect(service.hasCurrencyPreference(), isFalse);
      });

      test('should return to default currency after reset', () async {
        await service.setCurrency('EUR');
        await service.resetCurrencyToDefault();

        expect(
          service.getCurrentCurrency(),
          equals(CurrencyService.defaultCurrencyCode),
        );
      });

      test('should succeed even when no preference exists', () async {
        final result = await service.resetCurrencyToDefault();
        expect(result, isTrue);
      });
    });

    group('getCurrentCurrencySymbol', () {
      test(
        'should return symbol for default currency when no preference set',
        () {
          final symbol = service.getCurrentCurrencySymbol();
          final expectedSymbol = CurrencyService.getCurrencySymbol(
            CurrencyService.defaultCurrencyCode,
          );
          expect(symbol, equals(expectedSymbol));
        },
      );

      test('should return symbol for set currency preference', () async {
        await service.setCurrency('EUR');
        final symbol = service.getCurrentCurrencySymbol();
        expect(symbol, equals('€'));
      });

      test('should work with various currency codes', () async {
        final testCases = {'USD': r'$', 'GBP': '£', 'JPY': '¥', 'EUR': '€'};

        for (final entry in testCases.entries) {
          await service.setCurrency(entry.key);
          expect(service.getCurrentCurrencySymbol(), equals(entry.value));
        }
      });
    });

    group('getCurrentCurrencyDisplayName', () {
      test(
        'should return display name for default currency when no preference set',
        () {
          final displayName = service.getCurrentCurrencyDisplayName();
          final expectedName = CurrencyService.getCurrencyDisplayName(
            CurrencyService.defaultCurrencyCode,
          );
          expect(displayName, equals(expectedName));
        },
      );

      test('should return display name for set currency preference', () async {
        await service.setCurrency('EUR');
        final displayName = service.getCurrentCurrencyDisplayName();
        expect(displayName, equals('EUR (€)'));
      });
    });

    group('formatAmount', () {
      test(
        'should format amount using default currency when no preference set',
        () {
          final formatted = service.formatAmount(12345); // $123.45
          expect(formatted, contains('123.45'));
          expect(
            formatted,
            contains(
              CurrencyService.getCurrencySymbol(
                CurrencyService.defaultCurrencyCode,
              ),
            ),
          );
        },
      );

      test('should format amount using set currency preference', () async {
        await service.setCurrency('EUR');
        final formatted = service.formatAmount(12345); // €123.45
        expect(formatted, contains('123.45'));
        expect(formatted, contains('€'));
      });

      test('should handle zero amount', () {
        final formatted = service.formatAmount(0);
        expect(formatted, contains('0.00'));
      });

      test('should handle negative amounts', () {
        final formatted = service.formatAmount(-12345);
        expect(formatted, contains('123.45'));
      });
    });

    group('formatAmountWithSign', () {
      test('should format positive amount without sign by default', () {
        final formatted = service.formatAmountWithSign(12345);
        expect(formatted, isNot(startsWith('+')));
        expect(formatted, contains('123.45'));
      });

      test('should format positive amount with sign when requested', () {
        final formatted = service.formatAmountWithSign(
          12345,
          showPositiveSign: true,
        );
        expect(formatted, startsWith('+'));
      });

      test('should format negative amount with minus sign', () {
        final formatted = service.formatAmountWithSign(-12345);
        expect(formatted, startsWith('-'));
      });

      test('should format zero without sign', () {
        final formatted = service.formatAmountWithSign(0);
        expect(formatted, isNot(startsWith('+')));
        expect(formatted, isNot(startsWith('-')));
      });

      test('should use current currency preference for formatting', () async {
        await service.setCurrency('EUR');
        final formatted = service.formatAmountWithSign(12345);
        expect(formatted, contains('€'));
      });
    });

    group('currentCurrencyUsesDecimals', () {
      test('should return true for decimal currencies like USD', () async {
        await service.setCurrency('USD');
        expect(service.currentCurrencyUsesDecimals(), isTrue);
      });

      test('should return false for non-decimal currencies like JPY', () async {
        await service.setCurrency('JPY');
        expect(service.currentCurrencyUsesDecimals(), isFalse);
      });

      test(
        'should return result for default currency when no preference set',
        () {
          final usesDecimals = service.currentCurrencyUsesDecimals();
          final expectedResult = CurrencyService.currencyUsesDecimals(
            CurrencyService.defaultCurrencyCode,
          );
          expect(usesDecimals, equals(expectedResult));
        },
      );
    });

    group('getCurrentCurrencyDecimalPlaces', () {
      test('should return 2 for decimal currencies', () async {
        await service.setCurrency('USD');
        expect(service.getCurrentCurrencyDecimalPlaces(), equals(2));
      });

      test('should return 0 for non-decimal currencies', () async {
        await service.setCurrency('JPY');
        expect(service.getCurrentCurrencyDecimalPlaces(), equals(0));
      });

      test(
        'should return result for default currency when no preference set',
        () {
          final decimalPlaces = service.getCurrentCurrencyDecimalPlaces();
          final expectedPlaces = CurrencyService.getCurrencyDecimalPlaces(
            CurrencyService.defaultCurrencyCode,
          );
          expect(decimalPlaces, equals(expectedPlaces));
        },
      );
    });

    group('Integration Tests', () {
      test('should maintain consistency across service methods', () async {
        await service.setCurrency('EUR');

        expect(service.getCurrentCurrency(), equals('EUR'));
        expect(service.getCurrentCurrencySymbol(), equals('€'));
        expect(service.getCurrentCurrencyDisplayName(), equals('EUR (€)'));
        expect(service.hasCurrencyPreference(), isTrue);
        expect(service.currentCurrencyUsesDecimals(), isTrue);
        expect(service.getCurrentCurrencyDecimalPlaces(), equals(2));
      });

      test('should handle full workflow: set, use, reset', () async {
        // Initially no preference
        expect(service.hasCurrencyPreference(), isFalse);
        expect(
          service.getCurrentCurrency(),
          equals(CurrencyService.defaultCurrencyCode),
        );

        // Set preference
        await service.setCurrency('GBP');
        expect(service.hasCurrencyPreference(), isTrue);
        expect(service.getCurrentCurrency(), equals('GBP'));
        expect(service.getCurrentCurrencySymbol(), equals('£'));

        // Reset preference
        await service.resetCurrencyToDefault();
        expect(service.hasCurrencyPreference(), isFalse);
        expect(
          service.getCurrentCurrency(),
          equals(CurrencyService.defaultCurrencyCode),
        );
      });
    });
  });
}
