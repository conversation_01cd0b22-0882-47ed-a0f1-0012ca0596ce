import 'package:budapp/features/transactions/services/transaction_error_service.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAppLocalizations extends Mock implements AppLocalizations {}

void main() {
  group('TransactionErrorService', () {
    late MockAppLocalizations mockL10n;

    setUp(() {
      mockL10n = MockAppLocalizations();

      // Setup common mock returns
      when(() => mockL10n.networkError).thenReturn('Network error');
      when(
        () => mockL10n.transactionOperationError,
      ).thenReturn('Transaction operation error');
      when(
        () => mockL10n.permissionDeniedError,
      ).thenReturn('Permission denied');
      when(
        () => mockL10n.serviceUnavailableError,
      ).thenReturn('Service unavailable');
      when(
        () => mockL10n.operationTimeoutError,
      ).thenReturn('Operation timeout');
      when(() => mockL10n.quotaExceededError).thenReturn('Quota exceeded');
      when(
        () => mockL10n.transactionNotFoundError,
      ).thenReturn('Transaction not found');
      when(
        () => mockL10n.transactionAlreadyExistsError,
      ).thenReturn('Transaction already exists');
      when(() => mockL10n.operationFailedError).thenReturn('Operation failed');
      when(
        () => mockL10n.operationAbortedError,
      ).thenReturn('Operation aborted');
      when(() => mockL10n.invalidDataError).thenReturn('Invalid data');
      when(
        () => mockL10n.featureNotAvailableError,
      ).thenReturn('Feature not available');
      when(
        () => mockL10n.internalServerError,
      ).thenReturn('Internal server error');
      when(() => mockL10n.dataCorruptionError).thenReturn('Data corruption');
      when(
        () => mockL10n.authenticationRequiredError,
      ).thenReturn('Authentication required');
      when(() => mockL10n.firestoreError).thenReturn('Firestore error');
      when(() => mockL10n.accountNotFoundError).thenReturn('Account not found');
      when(() => mockL10n.invalidAmountError).thenReturn('Invalid amount');
      when(() => mockL10n.sameAccountError).thenReturn('Same account error');
      when(() => mockL10n.validationError).thenReturn('Validation error');
      when(
        () => mockL10n.transactionCreateError,
      ).thenReturn('Transaction create error');
      when(
        () => mockL10n.transactionUpdateError,
      ).thenReturn('Transaction update error');
      when(
        () => mockL10n.transactionDeleteError,
      ).thenReturn('Transaction delete error');
    });

    group('getErrorMessage', () {
      test('should handle FirebaseException with permission-denied code', () {
        // Arrange
        final error = FirebaseException(
          plugin: 'cloud_firestore',
          code: 'permission-denied',
          message: 'Permission denied',
        );

        // Act
        final result = TransactionErrorService.getErrorMessage(error, mockL10n);

        // Assert
        expect(result, equals('Permission denied'));
      });

      test('should handle FirebaseException with unavailable code', () {
        // Arrange
        final error = FirebaseException(
          plugin: 'cloud_firestore',
          code: 'unavailable',
          message: 'Service unavailable',
        );

        // Act
        final result = TransactionErrorService.getErrorMessage(error, mockL10n);

        // Assert
        expect(result, equals('Service unavailable'));
      });

      test('should handle FirebaseException with timeout codes', () {
        // Arrange
        final timeoutError = FirebaseException(
          plugin: 'cloud_firestore',
          code: 'deadline-exceeded',
          message: 'Deadline exceeded',
        );

        final timeoutError2 = FirebaseException(
          plugin: 'cloud_firestore',
          code: 'timeout',
          message: 'Timeout',
        );

        // Act
        final result1 = TransactionErrorService.getErrorMessage(
          timeoutError,
          mockL10n,
        );
        final result2 = TransactionErrorService.getErrorMessage(
          timeoutError2,
          mockL10n,
        );

        // Assert
        expect(result1, equals('Operation timeout'));
        expect(result2, equals('Operation timeout'));
      });

      test('should handle FirebaseException with unknown code', () {
        // Arrange
        final error = FirebaseException(
          plugin: 'cloud_firestore',
          code: 'unknown-error',
          message: 'Unknown error occurred',
        );

        // Act
        final result = TransactionErrorService.getErrorMessage(error, mockL10n);

        // Assert
        expect(result, equals('Firestore error: Unknown error occurred'));
      });

      test(
        'should handle FirebaseException with unknown code and no message',
        () {
          // Arrange
          final error = FirebaseException(
            plugin: 'cloud_firestore',
            code: 'unknown-error',
          );

          // Act
          final result = TransactionErrorService.getErrorMessage(
            error,
            mockL10n,
          );

          // Assert
          expect(result, equals('Firestore error: unknown-error'));
        },
      );

      test(
        'should handle ArgumentError with transaction not found message',
        () {
          // Arrange
          final error = ArgumentError('Transaction not found for user');

          // Act
          final result = TransactionErrorService.getErrorMessage(
            error,
            mockL10n,
          );

          // Assert
          expect(result, equals('Transaction not found'));
        },
      );

      test('should handle ArgumentError with authentication messages', () {
        // Arrange
        final error1 = ArgumentError('User not authenticated');
        final error2 = ArgumentError(
          'Transaction does not belong to the specified user',
        );

        // Act
        final result1 = TransactionErrorService.getErrorMessage(
          error1,
          mockL10n,
        );
        final result2 = TransactionErrorService.getErrorMessage(
          error2,
          mockL10n,
        );

        // Assert
        expect(result1, equals('Authentication required'));
        expect(result2, equals('Authentication required'));
      });

      test('should handle ArgumentError with account not found message', () {
        // Arrange
        final error = ArgumentError('Account not found');

        // Act
        final result = TransactionErrorService.getErrorMessage(error, mockL10n);

        // Assert
        expect(result, equals('Account not found'));
      });

      test('should handle ArgumentError with invalid amount message', () {
        // Arrange
        final error = ArgumentError('Invalid amount provided');

        // Act
        final result = TransactionErrorService.getErrorMessage(error, mockL10n);

        // Assert
        expect(result, equals('Invalid amount'));
      });

      test('should handle ArgumentError with same account message', () {
        // Arrange
        final error = ArgumentError('Same account transfer not allowed');

        // Act
        final result = TransactionErrorService.getErrorMessage(error, mockL10n);

        // Assert
        expect(result, equals('Same account error'));
      });

      test('should handle ArgumentError with generic message', () {
        // Arrange
        final error = ArgumentError('Some validation failed');

        // Act
        final result = TransactionErrorService.getErrorMessage(error, mockL10n);

        // Assert
        expect(result, equals('Validation error'));
      });

      test('should handle network-related errors', () {
        // Arrange
        final networkError = Exception('network connection failed');
        final connectionError = Exception('connection timeout');
        final timeoutError = Exception('request timeout');

        // Act
        final result1 = TransactionErrorService.getErrorMessage(
          networkError,
          mockL10n,
        );
        final result2 = TransactionErrorService.getErrorMessage(
          connectionError,
          mockL10n,
        );
        final result3 = TransactionErrorService.getErrorMessage(
          timeoutError,
          mockL10n,
        );

        // Assert
        expect(result1, equals('Network error'));
        expect(result2, equals('Network error'));
        expect(result3, equals('Network error'));
      });

      test('should handle generic errors', () {
        // Arrange
        final error = Exception('Some unknown error');

        // Act
        final result = TransactionErrorService.getErrorMessage(error, mockL10n);

        // Assert
        expect(result, equals('Transaction operation error'));
      });
    });

    group('getCreateErrorMessage', () {
      test('should return formatted create error message', () {
        // Arrange
        final error = Exception('Some error');

        // Act
        final result = TransactionErrorService.getCreateErrorMessage(
          error,
          mockL10n,
        );

        // Assert
        expect(
          result,
          equals('Transaction create error: Transaction operation error'),
        );
      });
    });

    group('getUpdateErrorMessage', () {
      test('should return formatted update error message', () {
        // Arrange
        final error = Exception('Some error');

        // Act
        final result = TransactionErrorService.getUpdateErrorMessage(
          error,
          mockL10n,
        );

        // Assert
        expect(
          result,
          equals('Transaction update error: Transaction operation error'),
        );
      });
    });

    group('getDeleteErrorMessage', () {
      test('should return formatted delete error message', () {
        // Arrange
        final error = Exception('Some error');

        // Act
        final result = TransactionErrorService.getDeleteErrorMessage(
          error,
          mockL10n,
        );

        // Assert
        expect(
          result,
          equals('Transaction delete error: Transaction operation error'),
        );
      });
    });

    group('isRetryable', () {
      test('should return true for retryable FirebaseException codes', () {
        // Arrange
        final retryableCodes = [
          'unavailable',
          'deadline-exceeded',
          'timeout',
          'resource-exhausted',
          'aborted',
          'internal',
        ];

        // Act & Assert
        for (final code in retryableCodes) {
          final error = FirebaseException(
            plugin: 'cloud_firestore',
            code: code,
          );
          expect(
            TransactionErrorService.isRetryable(error),
            isTrue,
            reason: 'Code $code should be retryable',
          );
        }
      });

      test('should return false for non-retryable FirebaseException codes', () {
        // Arrange
        final nonRetryableCodes = [
          'permission-denied',
          'not-found',
          'already-exists',
          'failed-precondition',
          'out-of-range',
          'unimplemented',
          'data-loss',
          'unauthenticated',
        ];

        // Act & Assert
        for (final code in nonRetryableCodes) {
          final error = FirebaseException(
            plugin: 'cloud_firestore',
            code: code,
          );
          expect(
            TransactionErrorService.isRetryable(error),
            isFalse,
            reason: 'Code $code should not be retryable',
          );
        }
      });

      test('should return true for network-related errors', () {
        // Arrange
        final networkErrors = [
          Exception('network connection failed'),
          Exception('connection timeout'),
          Exception('request timeout'),
        ];

        // Act & Assert
        for (final error in networkErrors) {
          expect(
            TransactionErrorService.isRetryable(error),
            isTrue,
            reason: 'Network error should be retryable: $error',
          );
        }
      });

      test('should return false for non-retryable errors', () {
        // Arrange
        final nonRetryableErrors = [
          ArgumentError('Invalid argument'),
          Exception('Some other error'),
          StateError('Invalid state'),
        ];

        // Act & Assert
        for (final error in nonRetryableErrors) {
          expect(
            TransactionErrorService.isRetryable(error),
            isFalse,
            reason: 'Error should not be retryable: $error',
          );
        }
      });
    });
  });
}
