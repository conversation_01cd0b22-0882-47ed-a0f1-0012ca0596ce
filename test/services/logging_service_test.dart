import 'package:budapp/services/logging_service.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('LoggingService', () {
    late LoggingService loggingService;

    setUp(() {
      loggingService = LoggingService();
    });

    group('Singleton Pattern', () {
      test('should return same instance', () {
        final instance1 = LoggingService();
        final instance2 = LoggingService();

        expect(instance1, same(instance2));
      });
    });

    group('PII Sanitization', () {
      test('should sanitize email addresses in log messages', () {
        // This test verifies that emails are sanitized but doesn't test actual output
        // since we can't easily capture logger output in tests
        expect(
          () => loggingService.info('User email: <EMAIL>'),
          returnsNormally,
        );
      });

      test('should sanitize phone numbers in log messages', () {
        expect(
          () => loggingService.warning('Contact: ************'),
          returnsNormally,
        );
      });

      test('should sanitize financial data in log messages', () {
        expect(
          () => loggingService.error('Card: 4532-1234-5678-9012'),
          returnsNormally,
        );
      });
    });

    group('Log Levels', () {
      test('should handle trace level logging', () {
        expect(() => loggingService.trace('Trace message'), returnsNormally);
      });

      test('should handle debug level logging', () {
        expect(() => loggingService.debug('Debug message'), returnsNormally);
      });

      test('should handle info level logging', () {
        expect(() => loggingService.info('Info message'), returnsNormally);
      });

      test('should handle warning level logging', () {
        expect(
          () => loggingService.warning('Warning message'),
          returnsNormally,
        );
      });

      test('should handle error level logging', () {
        expect(() => loggingService.error('Error message'), returnsNormally);
      });

      test('should handle fatal level logging', () {
        expect(() => loggingService.fatal('Fatal message'), returnsNormally);
      });
    });

    group('Error and StackTrace Logging', () {
      test('should handle logging with error objects', () {
        final error = Exception('Test exception');

        expect(
          () => loggingService.error('Error occurred', error: error),
          returnsNormally,
        );
      });

      test('should handle logging with stack traces', () {
        final stackTrace = StackTrace.current;

        expect(
          () => loggingService.error(
            'Error with stack trace',
            stackTrace: stackTrace,
          ),
          returnsNormally,
        );
      });

      test('should handle logging with both error and stack trace', () {
        final error = Exception('Test exception');
        final stackTrace = StackTrace.current;

        expect(
          () => loggingService.error(
            'Complete error info',
            error: error,
            stackTrace: stackTrace,
          ),
          returnsNormally,
        );
      });
    });

    group('Extension Methods', () {
      test('should provide convenient logging extension methods', () {
        final testObject = _TestClass();

        expect(
          () => testObject.logDebug('Debug from extension'),
          returnsNormally,
        );
        expect(
          () => testObject.logInfo('Info from extension'),
          returnsNormally,
        );
        expect(
          () => testObject.logWarning('Warning from extension'),
          returnsNormally,
        );
        expect(
          () => testObject.logError('Error from extension'),
          returnsNormally,
        );
      });

      test('should include class name in extension logging', () {
        final testObject = _TestClass();

        // These should include the class name in the log message
        expect(() => testObject.logInfo('Test message'), returnsNormally);
      });
    });

    group('Global Logger Instance', () {
      test('should provide global log instance', () {
        expect(() => log.info('Global logger test'), returnsNormally);
        expect(() => log.debug('Global debug test'), returnsNormally);
        expect(() => log.error('Global error test'), returnsNormally);
      });
    });
  });

  group('ProductionFilter', () {
    late ProductionFilter filter;

    setUp(() {
      filter = ProductionFilter();
    });

    test('should have proper log level filtering logic', () {
      // The filter should respect debug mode settings
      // This test verifies the filter can be instantiated and used
      expect(filter, isA<ProductionFilter>());
    });
  });
}

class _TestClass {
  void doSomething() {
    // Test class for extension method testing
  }
}
