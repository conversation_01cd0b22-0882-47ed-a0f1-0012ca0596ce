import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/models/budget_progress.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/interfaces/transaction_repository.dart';
import 'package:budapp/features/budgets/services/budget_progress_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockTransactionRepository extends Mock
    implements ITransactionRepository {}

void main() {
  group('BudgetProgressService', () {
    late MockTransactionRepository mockTransactionRepository;
    late BudgetProgressService service;

    setUp(() {
      mockTransactionRepository = MockTransactionRepository();
      service = BudgetProgressService(
        transactionRepository: mockTransactionRepository,
      );
    });

    group('calculateBudgetProgress', () {
      test('should calculate progress correctly for expense budget', () async {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000, // $1000
          currentAmountCents: 25000, // $250 spent
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final progress = await service.calculateBudgetProgress(
          budget,
          DateTime(2024, 1, 15),
        );

        // Assert
        expect(progress.budgetId, equals(budget.id));
        expect(progress.spent, equals(250.0));
        expect(progress.remaining, equals(750.0));
        expect(progress.percentage, equals(25.0));
        expect(progress.isOverBudget, isFalse);
        expect(progress.status, equals(BudgetProgressStatus.onTrack));
      });

      test(
        'should calculate progress correctly for over-budget scenario',
        () async {
          // Arrange
          final budget = Budget.create(
            userId: 'user1',
            type: BudgetType.expense,
            plannedAmountCents: 100000, // $1000
            currentAmountCents: 120000, // $1200 spent (over budget)
            periodStart: DateTime(2024, 1, 1),
          );

          // Act
          final progress = await service.calculateBudgetProgress(
            budget,
            DateTime(2024, 1, 15),
          );

          // Assert
          expect(progress.spent, equals(1200.0));
          expect(progress.remaining, equals(-200.0));
          expect(progress.percentage, equals(120.0));
          expect(progress.isOverBudget, isTrue);
          expect(progress.status, equals(BudgetProgressStatus.overBudget));
        },
      );

      test('should handle zero budget amount', () async {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 0, // $0 budget
          currentAmountCents: 5000, // $50 spent
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final progress = await service.calculateBudgetProgress(
          budget,
          DateTime(2024, 1, 15),
        );

        // Assert
        expect(progress.spent, equals(50.0));
        expect(progress.remaining, equals(-50.0));
        expect(
          progress.percentage,
          equals(0.0),
        ); // BudgetProgress returns 0 for zero budget
        expect(progress.isOverBudget, isTrue);
      });
    });

    group('calculateMultipleBudgetProgress', () {
      test('should calculate progress for multiple budgets', () async {
        // Arrange
        final budgets = [
          Budget.create(
            userId: 'user1',
            type: BudgetType.expense,
            plannedAmountCents: 100000,
            currentAmountCents: 25000,
            periodStart: DateTime(2024, 1, 1),
          ),
          Budget.create(
            userId: 'user1',
            type: BudgetType.expense,
            plannedAmountCents: 50000,
            currentAmountCents: 40000,
            periodStart: DateTime(2024, 1, 1),
          ),
        ];

        // Act
        final progressList = await service.calculateMultipleBudgetProgress(
          budgets,
          DateTime(2024, 1, 15),
        );

        // Assert
        expect(progressList, hasLength(2));
        expect(progressList[0].percentage, equals(25.0));
        expect(progressList[1].percentage, equals(80.0));
      });

      test('should handle empty budget list', () async {
        // Act
        final progressList = await service.calculateMultipleBudgetProgress(
          [],
          DateTime(2024, 1, 15),
        );

        // Assert
        expect(progressList, isEmpty);
      });
    });

    group('getSpendingBreakdown', () {
      test('should return spending breakdown by category', () async {
        // Arrange
        final transactions = [
          Transaction(
            id: 'tx1',
            userId: 'user1',
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 5000, // $50
            fromAccountId: 'acc1',
            categoryId: 'cat1',
            description: 'Test expense',
            transactionDate: DateTime(2024, 1, 15),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        when(
          () => mockTransactionRepository.watchTransactionsByMonthAndCategories(
            userId: 'user1',
            month: DateTime(2024, 1, 15),
            categoryIds: ['cat1'],
          ),
        ).thenAnswer((_) => Stream.value(transactions));

        // Act
        final breakdown = await service.getSpendingBreakdown('user1', [
          'cat1',
        ], DateTime(2024, 1, 15));

        // Assert
        expect(breakdown, containsPair('cat1', 50.0));
      });
    });

    group('getTotalSpending', () {
      test('should return total spending for a month', () async {
        // Arrange
        final transactions = [
          Transaction(
            id: 'tx1',
            userId: 'user1',
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 5000, // $50
            fromAccountId: 'acc1',
            categoryId: 'cat1',
            description: 'Test expense 1',
            transactionDate: DateTime(2024, 1, 15),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Transaction(
            id: 'tx2',
            userId: 'user1',
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 3000, // $30
            fromAccountId: 'acc1',
            categoryId: 'cat2',
            description: 'Test expense 2',
            transactionDate: DateTime(2024, 1, 20),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        when(
          () => mockTransactionRepository.watchTransactionsByMonthAndCategories(
            userId: 'user1',
            month: DateTime(2024, 1, 15),
            categoryIds: [],
          ),
        ).thenAnswer((_) => Stream.value(transactions));

        // Act
        final totalSpending = await service.getTotalSpending(
          'user1',
          DateTime(2024, 1, 15),
        );

        // Assert
        expect(totalSpending, equals(80.0));
      });
    });

    group('getSpendingTrend', () {
      test('should return spending trend over multiple months', () async {
        // Arrange
        final month1Transactions = [
          Transaction(
            id: 'tx1',
            userId: 'user1',
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 5000,
            fromAccountId: 'acc1',
            categoryId: 'cat1',
            description: 'Test expense',
            transactionDate: DateTime(2024, 1, 15),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        final month2Transactions = [
          Transaction(
            id: 'tx2',
            userId: 'user1',
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 7000,
            fromAccountId: 'acc1',
            categoryId: 'cat1',
            description: 'Test expense',
            transactionDate: DateTime(2024, 2, 15),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        when(
          () => mockTransactionRepository.watchTransactionsByMonthAndCategories(
            userId: 'user1',
            month: DateTime(2024, 1, 1),
            categoryIds: [],
          ),
        ).thenAnswer((_) => Stream.value(month1Transactions));

        when(
          () => mockTransactionRepository.watchTransactionsByMonthAndCategories(
            userId: 'user1',
            month: DateTime(2024, 2, 1),
            categoryIds: [],
          ),
        ).thenAnswer((_) => Stream.value(month2Transactions));

        // Act
        final trend = await service.getSpendingTrend('user1', null, [
          DateTime(2024, 1, 1),
          DateTime(2024, 2, 1),
        ]);

        // Assert
        expect(trend, hasLength(2));
        expect(trend[DateTime(2024, 1, 1)], equals(50.0));
        expect(trend[DateTime(2024, 2, 1)], equals(70.0));
      });
    });

    group('calculateUtilization', () {
      test('should calculate utilization percentage correctly', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000, // $1000
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final utilization = service.calculateUtilization(budget, 250);

        // Assert
        expect(utilization, equals(25.0));
      });

      test('should return 0 for zero budget amount', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 0,
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final utilization = service.calculateUtilization(budget, 100);

        // Assert
        expect(utilization, equals(0));
      });
    });

    group('isBudgetOverLimit', () {
      test('should return true when spending exceeds budget', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000, // $1000
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final isOverLimit = service.isBudgetOverLimit(budget, 1200);

        // Assert
        expect(isOverLimit, isTrue);
      });

      test('should return false when spending is within budget', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000, // $1000
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final isOverLimit = service.isBudgetOverLimit(budget, 800);

        // Assert
        expect(isOverLimit, isFalse);
      });
    });

    group('getRemainingBudget', () {
      test('should calculate remaining budget correctly', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000, // $1000
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final remaining = service.getRemainingBudget(budget, 300);

        // Assert
        expect(remaining, equals(700.0));
      });

      test('should return negative value when over budget', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000, // $1000
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final remaining = service.getRemainingBudget(budget, 1200);

        // Assert
        expect(remaining, equals(-200.0));
      });
    });

    group('getBudgetPeriodStart', () {
      test('should return correct start date for monthly budget', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final periodStart = service.getBudgetPeriodStart(
          budget,
          DateTime(2024, 1, 15),
        );

        // Assert
        expect(periodStart, equals(DateTime(2024, 1, 1)));
      });

      test('should return correct start date for yearly budget', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.yearly,
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final periodStart = service.getBudgetPeriodStart(
          budget,
          DateTime(2024, 6, 15),
        );

        // Assert
        expect(periodStart, equals(DateTime(2024, 1, 1)));
      });
    });

    group('getBudgetPeriodEnd', () {
      test('should return correct end date for monthly budget', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final periodEnd = service.getBudgetPeriodEnd(
          budget,
          DateTime(2024, 1, 15),
        );

        // Assert
        expect(periodEnd, equals(DateTime(2024, 1, 31)));
      });

      test('should return correct end date for yearly budget', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.yearly,
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final periodEnd = service.getBudgetPeriodEnd(
          budget,
          DateTime(2024, 6, 15),
        );

        // Assert
        expect(periodEnd, equals(DateTime(2024, 12, 31)));
      });

      test('should handle December correctly for monthly budget', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2024, 12, 1),
        );

        // Act
        final periodEnd = service.getBudgetPeriodEnd(
          budget,
          DateTime(2024, 12, 15),
        );

        // Assert
        expect(periodEnd, equals(DateTime(2024, 12, 31)));
      });
    });

    group('isDateInBudgetPeriod', () {
      test('should return true for date within monthly budget period', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final isInPeriod = service.isDateInBudgetPeriod(
          budget,
          DateTime(2024, 1, 15),
          DateTime(2024, 1, 15),
        );

        // Assert
        expect(isInPeriod, isTrue);
      });

      test('should return false for date outside monthly budget period', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final isInPeriod = service.isDateInBudgetPeriod(
          budget,
          DateTime(2024, 2, 15),
          DateTime(2024, 1, 15),
        );

        // Assert
        expect(isInPeriod, isFalse);
      });
    });

    group('getNextBudgetPeriod', () {
      test('should return next month for monthly budget', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final nextPeriod = service.getNextBudgetPeriod(
          budget,
          DateTime(2024, 1, 15),
        );

        // Assert
        expect(nextPeriod, equals(DateTime(2024, 2, 1)));
      });

      test('should handle December to January transition', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2024, 12, 1),
        );

        // Act
        final nextPeriod = service.getNextBudgetPeriod(
          budget,
          DateTime(2024, 12, 15),
        );

        // Assert
        expect(nextPeriod, equals(DateTime(2025, 1, 1)));
      });

      test('should return next year for yearly budget', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.yearly,
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final nextPeriod = service.getNextBudgetPeriod(
          budget,
          DateTime(2024, 6, 15),
        );

        // Assert
        expect(nextPeriod, equals(DateTime(2025, 1, 1)));
      });
    });

    group('getPreviousBudgetPeriod', () {
      test('should return previous month for monthly budget', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2024, 2, 1),
        );

        // Act
        final previousPeriod = service.getPreviousBudgetPeriod(
          budget,
          DateTime(2024, 2, 15),
        );

        // Assert
        expect(previousPeriod, equals(DateTime(2024, 1, 1)));
      });

      test('should handle January to December transition', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.monthly,
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final previousPeriod = service.getPreviousBudgetPeriod(
          budget,
          DateTime(2024, 1, 15),
        );

        // Assert
        expect(previousPeriod, equals(DateTime(2023, 12, 1)));
      });

      test('should return previous year for yearly budget', () {
        // Arrange
        final budget = Budget.create(
          userId: 'user1',
          type: BudgetType.expense,
          plannedAmountCents: 100000,
          period: BudgetPeriod.yearly,
          periodStart: DateTime(2024, 1, 1),
        );

        // Act
        final previousPeriod = service.getPreviousBudgetPeriod(
          budget,
          DateTime(2024, 6, 15),
        );

        // Assert
        expect(previousPeriod, equals(DateTime(2023, 1, 1)));
      });
    });
  });
}
