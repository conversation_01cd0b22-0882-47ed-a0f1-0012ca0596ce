import 'package:budapp/utils/log_sanitizer.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('LogSanitizer', () {
    group('Email Sanitization', () {
      test('should sanitize basic email addresses', () {
        const input = 'User <NAME_EMAIL> for authentication';
        const expected = 'User email is ***@***.*** for authentication';

        expect(sanitize(input), expected);
      });

      test('should sanitize multiple email addresses', () {
        const input = 'Emails: user@test.<NAME_EMAIL>';
        const expected = 'Emails: ***@***.*** and ***@***.***';

        expect(sanitize(input), expected);
      });

      test('should sanitize complex email formats', () {
        const input = 'Complex email: <EMAIL>';
        const expected = 'Complex email: ***@***.***.***';

        expect(sanitize(input), expected);
      });
    });

    group('Phone Number Sanitization', () {
      test('should sanitize US phone numbers', () {
        const input = 'Call us at ************ for support';
        const expected = 'Call us at ***-***-**** for support';

        expect(sanitize(input), expected);
      });

      test('should sanitize phone numbers with parentheses', () {
        const input = 'Phone: (*************';
        const expected = 'Phone: ***-***-****';

        expect(sanitize(input), expected);
      });

      test('should sanitize phone numbers with dots', () {
        const input = 'Contact: ************';
        const expected = 'Contact: ***-***-****';

        expect(sanitize(input), expected);
      });
    });

    group('Financial Data Sanitization', () {
      test('should sanitize credit card numbers', () {
        const input = 'Card: 4532-1234-5678-9012';
        const expected = 'Card: ****-****-****-****';

        expect(sanitize(input), expected);
      });

      test('should sanitize account numbers', () {
        const input = 'Account number ********* is invalid';
        const expected = 'Account number ****[ACCOUNT] is invalid';

        expect(sanitize(input), expected);
      });

      test('should sanitize currency amounts', () {
        const input = r'Transaction amount: $1,234.56 USD';
        const expected = 'Transaction amount: [AMOUNT]';

        expect(sanitize(input), expected);
      });

      test('should sanitize multiple currency formats', () {
        const input = r'Amounts: $500.00, 1,000.50 EUR, 750 GBP';
        const expected = 'Amounts: [AMOUNT], [AMOUNT], [AMOUNT]';

        expect(sanitize(input), expected);
      });
    });

    group('Security Token Sanitization', () {
      test('should sanitize API keys', () {
        const input = 'API Key: AIzaSyDxKL6F8G9H2I3J4K5L6M7N8O9P0Q1R2S3T';
        const expected = 'API Key: [API_KEY]';

        expect(sanitize(input), expected);
      });

      test('should sanitize JWT tokens', () {
        const input =
            'Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
        const expected = 'Token: [JWT_TOKEN]';

        expect(sanitize(input), expected);
      });

      test('should sanitize IP addresses', () {
        const input = 'Request from ************* failed';
        const expected = 'Request from ***.***.***.*** failed';

        expect(sanitize(input), expected);
      });
    });

    group('Specific Sanitization Methods', () {
      test('sanitizeEmail should partially reveal email', () {
        const email = '<EMAIL>';
        final result = sanitizeEmail(email);

        expect(result, 'j*******h@***.com');
      });

      test('sanitizeEmail should handle short usernames', () {
        const email = '<EMAIL>';
        final result = sanitizeEmail(email);

        expect(result, '**@***.com');
      });

      test('sanitizePhone should show last 4 digits', () {
        const phone = '**********';
        final result = sanitizePhone(phone);

        expect(result, '(***) ***-4567');
      });

      test('sanitizeCreditCard should show last 4 digits', () {
        const card = '****************';
        final result = sanitizeCreditCard(card);

        expect(result, '****-****-****-9012');
      });

      test('sanitizeAccountNumber should show last 4 digits', () {
        const account = '*********012';
        final result = sanitizeAccountNumber(account);

        expect(result, '****9012');
      });

      test('sanitizeAmount should redact financial amounts', () {
        final result = sanitizeAmount(1234.56, currency: 'USD');

        expect(result, '[AMOUNT_USD]');
      });
    });

    group('PII Detection', () {
      test('containsPII should detect email addresses', () {
        const input = 'Contact <NAME_EMAIL>';

        expect(containsPII(input), isTrue);
      });

      test('containsPII should detect phone numbers', () {
        const input = 'Call ************ for help';

        expect(containsPII(input), isTrue);
      });

      test('containsPII should return false for clean text', () {
        const input = 'This is a clean log message';

        expect(containsPII(input), isFalse);
      });

      test('containsAuthData should detect authentication data', () {
        const input = 'Authentication failed with token abc123';

        expect(containsAuthData(input), isTrue);
      });

      test('containsAuthData should detect password references', () {
        const input = 'Invalid password provided';

        expect(containsAuthData(input), isTrue);
      });
    });

    group('Error Message Sanitization', () {
      test('should sanitize password in error messages', () {
        const input = 'Authentication error: password: mySecretPass123';
        const expected = 'Authentication error: password: [REDACTED]';

        expect(sanitizeError(input), expected);
      });

      test('should sanitize token in error messages', () {
        const input = 'API error: token=abc123xyz789';
        const expected = 'API error: token: [REDACTED]';

        expect(sanitizeError(input), expected);
      });

      test('should sanitize secret in error messages', () {
        const input = 'Config error: secret: supersecret456';
        const expected = 'Config error: secret: [REDACTED]';

        expect(sanitizeError(input), expected);
      });
    });

    group('Complex Sanitization Scenarios', () {
      test('should handle multiple PII types in one message', () {
        const input =
            'User <EMAIL> called ************ about account ********* with card ending 9012';
        final result = sanitize(input);

        expect(result, contains('***@***.***'));
        expect(result, contains('***-***-****'));
        expect(result, contains('****[ACCOUNT]'));
        expect(result, isNot(contains('<EMAIL>')));
        expect(result, isNot(contains('************')));
        expect(result, isNot(contains('*********')));
      });

      test('should preserve non-PII content while sanitizing PII', () {
        const input =
            'Authentication <NAME_EMAIL> at 2023-12-01 10:30:00';
        final result = sanitize(input);

        expect(result, contains('Authentication successful for'));
        expect(result, contains('at 2023-12-01 10:30:00'));
        expect(result, contains('***@***.***'));
        expect(result, isNot(contains('<EMAIL>')));
      });
    });
  });
}
