import 'package:budapp/firebase_options_prod.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FirebaseOptionsProd', () {
    tearDown(() {
      // Reset platform override after each test
      debugDefaultTargetPlatformOverride = null;
    });

    group('currentPlatform', () {
      test('should return android options for Android platform', () {
        debugDefaultTargetPlatformOverride = TargetPlatform.android;

        final options = FirebaseOptionsProd.currentPlatform;

        expect(options, equals(FirebaseOptionsProd.android));
        expect(options.projectId, equals('budapp-prod'));
        expect(
          options.appId,
          equals('1:133822146578:android:1fcaca12de7e27ece090cb'),
        );
        expect(
          options.apiKey,
          equals('AIzaSyA7lck7lbkg2Lmc0H4CJdvkdDIg0FQQuXI'),
        );
        expect(options.messagingSenderId, equals('133822146578'));
        expect(
          options.storageBucket,
          equals('budapp-prod.firebasestorage.app'),
        );
      });

      test('should return ios options for iOS platform', () {
        debugDefaultTargetPlatformOverride = TargetPlatform.iOS;

        final options = FirebaseOptionsProd.currentPlatform;

        expect(options, equals(FirebaseOptionsProd.ios));
        expect(options.projectId, equals('budapp-prod'));
        expect(
          options.appId,
          equals('1:133822146578:ios:1fcaca12de7e27ece090cb'),
        );
        expect(
          options.apiKey,
          equals('AIzaSyA7lck7lbkg2Lmc0H4CJdvkdDIg0FQQuXI'),
        );
        expect(options.messagingSenderId, equals('133822146578'));
        expect(
          options.storageBucket,
          equals('budapp-prod.firebasestorage.app'),
        );
        expect(options.iosBundleId, equals('com.digitau.budapp'));
      });

      test('should throw UnsupportedError for macOS platform', () {
        debugDefaultTargetPlatformOverride = TargetPlatform.macOS;

        expect(
          () => FirebaseOptionsProd.currentPlatform,
          throwsA(
            isA<UnsupportedError>().having(
              (e) => e.message,
              'message',
              contains(
                'FirebaseOptionsProd have not been configured for macos',
              ),
            ),
          ),
        );
      });

      test('should throw UnsupportedError for Windows platform', () {
        debugDefaultTargetPlatformOverride = TargetPlatform.windows;

        expect(
          () => FirebaseOptionsProd.currentPlatform,
          throwsA(
            isA<UnsupportedError>().having(
              (e) => e.message,
              'message',
              contains(
                'FirebaseOptionsProd have not been configured for windows',
              ),
            ),
          ),
        );
      });

      test('should throw UnsupportedError for Linux platform', () {
        debugDefaultTargetPlatformOverride = TargetPlatform.linux;

        expect(
          () => FirebaseOptionsProd.currentPlatform,
          throwsA(
            isA<UnsupportedError>().having(
              (e) => e.message,
              'message',
              contains(
                'FirebaseOptionsProd have not been configured for linux',
              ),
            ),
          ),
        );
      });
    });

    group('static options', () {
      test('android options should have correct configuration', () {
        const options = FirebaseOptionsProd.android;

        expect(options.projectId, equals('budapp-prod'));
        expect(
          options.appId,
          equals('1:133822146578:android:1fcaca12de7e27ece090cb'),
        );
        expect(
          options.apiKey,
          equals('AIzaSyA7lck7lbkg2Lmc0H4CJdvkdDIg0FQQuXI'),
        );
        expect(options.messagingSenderId, equals('133822146578'));
        expect(
          options.storageBucket,
          equals('budapp-prod.firebasestorage.app'),
        );
        expect(options.iosBundleId, isNull);
      });

      test('ios options should have correct configuration', () {
        const options = FirebaseOptionsProd.ios;

        expect(options.projectId, equals('budapp-prod'));
        expect(
          options.appId,
          equals('1:133822146578:ios:1fcaca12de7e27ece090cb'),
        );
        expect(
          options.apiKey,
          equals('AIzaSyA7lck7lbkg2Lmc0H4CJdvkdDIg0FQQuXI'),
        );
        expect(options.messagingSenderId, equals('133822146578'));
        expect(
          options.storageBucket,
          equals('budapp-prod.firebasestorage.app'),
        );
        expect(options.iosBundleId, equals('com.digitau.budapp'));
      });
    });
  });
}
