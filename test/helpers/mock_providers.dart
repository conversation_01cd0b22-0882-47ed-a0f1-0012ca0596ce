import 'dart:async';

import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/repositories/interfaces/repositories.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/providers/biometric_providers.dart';
import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:budapp/features/auth/services/biometric_service.dart';
import 'package:budapp/features/budgets/data/models/category_budget_info.dart';
import 'package:budapp/features/budgets/providers/budget_providers.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/features/dashboard/providers/dashboard_providers.dart';
import 'package:budapp/features/transactions/providers/transaction_providers.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:mocktail/mocktail.dart';

import 'mock_data_factory.dart';
import 'test_auth_helper.dart';

// Mock classes
// Note: Migrating to firebase_auth_mocks for better Firebase Auth simulation

class MockFirestore extends Mock implements FirebaseFirestore {}

class MockGoogleSignIn extends Mock implements GoogleSignIn {}

class MockUser extends Mock implements User {
  // Mutable backing fields so tests can customize values without mocktail stubs
  final String _uid = 'test-uid';
  final String _email = '<EMAIL>';
  final String _displayName = 'Test User';
  final bool _emailVerified = true;
  final List<UserInfo> _providerData = [];
  String? _photoURL;

  @override
  String get uid => _uid;

  @override
  String? get email => _email;

  @override
  String? get displayName => _displayName;

  @override
  bool get emailVerified => _emailVerified;

  @override
  List<UserInfo> get providerData => _providerData;

  @override
  String? get photoURL => _photoURL;
}

class MockUserCredential extends Mock implements UserCredential {}

class MockAuthService extends Mock implements AuthService {}

class MockAuthErrorService extends Mock implements AuthErrorService {}

class MockFirestoreService extends Mock implements FirestoreService {}

class MockUserRepository extends Mock implements IUserRepository {}

class MockTransactionRepository extends Mock
    implements ITransactionRepository {}

class MockAccountRepository extends Mock implements IAccountRepository {}

class MockCategoryRepository extends Mock implements ICategoryRepository {}

class MockRemoteConfigRepository extends Mock
    implements IRemoteConfigRepository {}

/// Test implementation of BiometricAuthNotifier for testing
class TestBiometricAuthNotifier extends BiometricAuthNotifier {
  BiometricAuthState _state = const BiometricAuthState.initial();

  @override
  BiometricAuthState build() => _state;

  @override
  BiometricAuthState get state => _state;

  @override
  set state(BiometricAuthState newState) {
    _state = newState;
  }

  @override
  Future<void> authenticate() async {
    state = const BiometricAuthState.success();
  }

  @override
  Future<void> enableBiometric() async {
    state = const BiometricAuthState.success();
  }

  @override
  Future<void> disableBiometric() async {
    state = const BiometricAuthState.success();
  }

  @override
  void reset() {
    state = const BiometricAuthState.initial();
  }
}

/// Mock provider overrides for testing
// ignore: avoid_classes_with_only_static_members
class MockProviders {
  // Core Firebase mocks - using firebase_auth_mocks for better simulation
  static final FirebaseAuth mockFirebaseAuth =
      TestAuthHelper.createUnauthenticatedMockAuth();
  static final mockFirestore = MockFirestore();
  static final mockGoogleSignIn = MockGoogleSignIn();
  static final mockUser = MockUser();
  static final mockUserCredential = MockUserCredential();

  // Service mocks
  static final mockAuthService = MockAuthService();
  static final mockAuthErrorService = MockAuthErrorService();
  static final mockFirestoreService = MockFirestoreService();

  // Repository mocks
  static final mockUserRepository = MockUserRepository();
  static final mockTransactionRepository = MockTransactionRepository();
  static final mockAccountRepository = MockAccountRepository();
  static final mockCategoryRepository = MockCategoryRepository();

  /// Provider overrides for Firebase services
  static List<Override> get firebaseOverrides => [
    firebaseAuthProvider.overrideWithValue(mockFirebaseAuth),
    firestoreProvider.overrideWithValue(mockFirestore),
    googleSignInProvider.overrideWithValue(mockGoogleSignIn),
    firestoreServiceProvider.overrideWithValue(mockFirestoreService),
  ];

  /// Provider overrides for authentication services
  static List<Override> get authOverrides => [
    ...firebaseOverrides,
    authServiceProvider.overrideWithValue(mockAuthService),
    authErrorServiceProvider.overrideWithValue(mockAuthErrorService),
  ];

  /// Provider overrides for repositories
  static List<Override> get repositoryOverrides => [
    ...firebaseOverrides,
    userRepositoryProvider.overrideWithValue(mockUserRepository),
    transactionRepositoryProvider.overrideWithValue(mockTransactionRepository),
    accountRepositoryProvider.overrideWithValue(mockAccountRepository),
    categoryRepositoryProvider.overrideWithValue(mockCategoryRepository),
  ];

  /// Provider overrides for dashboard testing
  static List<Override> get dashboardOverrides => [
    ...authenticatedUserOverrides(), // Include authentication
    // Mock account list provider with empty list
    accountListProvider.overrideWith((ref) => Stream.value([])),
    // Mock dashboard providers with default data
    currentPeriodBalanceProvider.overrideWith((ref) async {
      final now = DateTime.now();
      return CurrentPeriodBalance(
        totalIncome: 500000, // $5000.00
        totalExpenses: 300000, // $3000.00
        netIncome: 200000, // $2000.00
        periodStart: DateTime(now.year, now.month, 1),
        periodEnd: DateTime(now.year, now.month + 1, 0),
      );
    }),
    recentTransactionsForDashboardProvider.overrideWith((ref) async => []),
    topExpenseCategoriesSelectedPeriodProvider.overrideWith((ref) async => []),
    topIncomeCategoriesSelectedPeriodProvider.overrideWith((ref) async => []),
    totalAssetsProvider.overrideWith((ref) async => 1000000), // $10,000.00
    netWorthProvider.overrideWith((ref) async => 800000), // $8,000.00
  ];

  /// Provider overrides for transactions testing
  static List<Override> get transactionsOverrides => [
    ...authenticatedUserOverrides(),
    // Mock transaction list provider with empty list
    transactionListProvider.overrideWith((ref) => Stream.value([])),
    // Mock category provider for filtered views
    categoryProvider('test-category-id').overrideWith(
      (ref) => Stream.value(
        Category(
          id: 'test-category-id',
          userId: 'test-user-id',
          name: 'Test Category',
          type: CategoryType.expense,
          color: '#2196F3',
          icon: 'shopping_cart',
          isActive: true,
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ),
    ),
    // Mock account provider for filtered views
    accountProvider('test-account-id').overrideWith(
      (ref) => Stream.value(
        Account(
          id: 'test-account-id',
          userId: 'test-user-id',
          name: 'Test Account',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 100000,
          currentBalanceCents: 150000,

          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ),
    ),
  ];

  /// Complete provider overrides for full testing
  static List<Override> get allOverrides => [
    ...authOverrides,
    ...repositoryOverrides,
  ];

  /// Provider overrides for authenticated user testing
  static List<Override> authenticatedUserOverrides({
    String uid = 'test-uid',
    String email = '<EMAIL>',
    String? displayName = 'Test User',
    bool emailVerified = true,
  }) {
    // Create authenticated mock auth with specified user
    final authenticatedAuth = TestAuthHelper.createAuthenticatedMockAuth(
      uid: uid,
      email: email,
      displayName: displayName,
      isEmailVerified: emailVerified,
    );

    return [
      ...allOverrides,
      // Override with authenticated auth instance
      firebaseAuthProvider.overrideWithValue(authenticatedAuth),
      // Override authStateProvider directly to ensure immediate resolution
      authStateProvider.overrideWith(
        (ref) => Stream.value(authenticatedAuth.currentUser),
      ),
      // CRITICAL: Also override currentUserProvider directly for synchronous router access
      currentUserProvider.overrideWith((ref) => authenticatedAuth.currentUser),
      // Biometric providers for security tab
      biometricAvailabilityProvider.overrideWith(
        (ref) => Future.value(BiometricAvailability.available),
      ),
      biometricPreferenceProvider.overrideWith((ref) => Future.value(false)),
      biometricAuthReadyProvider.overrideWith((ref) => Future.value(false)),
      biometricAuthNotifierProvider.overrideWith(TestBiometricAuthNotifier.new),
    ];
  }

  /// Provider overrides for unauthenticated user testing
  static List<Override> get unauthenticatedUserOverrides {
    // Use unauthenticated mock auth instance
    final unauthenticatedAuth = TestAuthHelper.createUnauthenticatedMockAuth();

    return [
      ...allOverrides,
      // Override with unauthenticated auth instance
      firebaseAuthProvider.overrideWithValue(unauthenticatedAuth),
      // Override authStateProvider directly to ensure immediate resolution
      authStateProvider.overrideWith((ref) => Stream.value(null)),
      // CRITICAL: Also override currentUserProvider directly for synchronous router access
      currentUserProvider.overrideWith((ref) => null),
    ];
  }

  /// Provider overrides for loading state testing
  static List<Override> get loadingStateOverrides {
    return [
      globalLoadingProvider.overrideWith((ref) => false),
      secureModeProvider.overrideWith((ref) => false),
      themeModeProvider.overrideWith((ref) => 'system'),
      ...allOverrides,
    ];
  }

  /// Provider overrides for error state testing
  static List<Override> errorStateOverrides(Exception error) {
    return [
      ...allOverrides,
      // Add error state overrides as needed
    ];
  }

  /// Reset all mocks to their default state and create fresh instances
  static void resetMocks() {
    // Note: firebase_auth_mocks instances cannot be reset with mocktail's reset()
    // They are recreated fresh for each test setup instead

    // Reset only mocktail Mock objects
    reset(mockFirestore);
    reset(mockGoogleSignIn);
    reset(mockUser);
    reset(mockUserCredential);
    reset(mockAuthService);
    reset(mockAuthErrorService);
    reset(mockFirestoreService);
    reset(mockUserRepository);
    reset(mockTransactionRepository);
    reset(mockAccountRepository);
    reset(mockCategoryRepository);
  }

  /// Create fresh mock instances for complete test isolation
  static Map<String, dynamic> createFreshMocks() {
    return {
      'firebaseAuth': TestAuthHelper.createUnauthenticatedMockAuth(),
      'firestore': MockFirestore(),
      'googleSignIn': MockGoogleSignIn(),
      'user': MockUser(),
      'userCredential': MockUserCredential(),
      'authService': MockAuthService(),
      'authErrorService': MockAuthErrorService(),
      'firestoreService': MockFirestoreService(),
      'userRepository': MockUserRepository(),
      'transactionRepository': MockTransactionRepository(),
      'accountRepository': MockAccountRepository(),
      'categoryRepository': MockCategoryRepository(),
    };
  }

  /// Setup default mock behavior for common scenarios
  static void setupDefaultMocks() {
    // Register fallback values for mocktail
    registerFallbackValue(MockDataFactory.createUserProfile());

    // Firebase Auth defaults - firebase_auth_mocks handles this automatically
    // No need to manually set current user to null

    // User defaults
    // MockUser already provides reasonable default getters; no additional stubbing required.

    // UserCredential defaults
    when(() => mockUserCredential.user).thenReturn(mockUser);

    // AuthService defaults
    when(
      () => mockAuthService.signInWithEmailAndPassword(
        email: any(named: 'email'),
        password: any(named: 'password'),
      ),
    ).thenAnswer((_) async => mockUserCredential);
    when(
      () => mockAuthService.createUserWithEmailAndPassword(
        email: any(named: 'email'),
        password: any(named: 'password'),
      ),
    ).thenAnswer((_) async => mockUserCredential);
    when(
      mockAuthService.signInWithGoogle,
    ).thenAnswer((_) async => mockUserCredential);
    when(mockAuthService.signOut).thenAnswer((_) async {});
    when(
      () => mockAuthService.sendPasswordResetEmail(email: any(named: 'email')),
    ).thenAnswer((_) async {});
    when(mockAuthService.sendEmailVerification).thenAnswer((_) async {});

    // Repository defaults
    when(
      () => mockUserRepository.createOrUpdateFromFirebaseUser(any()),
    ).thenAnswer((_) async {});
    when(
      () => mockUserRepository.getUserById(any()),
    ).thenAnswer((_) async => null);
    when(
      () => mockUserRepository.updatePreferences(any(), any()),
    ).thenAnswer((_) async {});
    when(
      mockUserRepository.getCurrentUserProfile,
    ).thenAnswer((_) async => null);
    when(
      () => mockUserRepository.updateDisplayName(any()),
    ).thenAnswer((_) async {});
    when(() => mockUserRepository.updateEmail(any())).thenAnswer((_) async {});
    when(
      () => mockUserRepository.updateUserProfile(any()),
    ).thenAnswer((_) async {});

    when(
      () => mockTransactionRepository.createTransaction(any()),
    ).thenAnswer((_) async => 'test-transaction-id');
    when(
      () => mockTransactionRepository.getTransactionsByUserId(any()),
    ).thenAnswer((_) async => []);

    when(
      () => mockAccountRepository.createAccount(any()),
    ).thenAnswer((_) async => 'test-account-id');
    when(
      () => mockAccountRepository.getAccountsByUserId(any()),
    ).thenAnswer((_) async => []);
    when(
      () => mockAccountRepository.updateAccount(any(), any()),
    ).thenAnswer((_) async {});
    when(
      () => mockAccountRepository.deleteAccount(any()),
    ).thenAnswer((_) async {});
    when(
      () => mockAccountRepository.deactivateAccount(any()),
    ).thenAnswer((_) async {});
    when(
      () => mockAccountRepository.setPrimaryAccount(any(), any()),
    ).thenAnswer((_) async {});

    when(
      () => mockCategoryRepository.createCategory(any()),
    ).thenAnswer((_) async => 'test-category-id');
    when(
      () => mockCategoryRepository.updateCategory(any(), any()),
    ).thenAnswer((_) async {});
    when(
      () => mockCategoryRepository.updateSubcategory(any(), any()),
    ).thenAnswer((_) async {});
    when(
      () => mockCategoryRepository.createSubcategory(any(), any()),
    ).thenAnswer((_) async => 'test-subcategory-id');
    when(
      () => mockCategoryRepository.deleteSubcategory(any(), any()),
    ).thenAnswer((_) async {});
    when(
      () => mockCategoryRepository.deactivateCategory(any()),
    ).thenAnswer((_) async {});
    when(mockCategoryRepository.getActiveCategories).thenAnswer(
      (_) async => [
        // Parent categories for hierarchy validation
        Category.create(
          userId: 'test-user-id',
          name: 'Test Parent Category',
          type: CategoryType.expense,
        ).copyWith(id: 'parent-123'),
        Category.create(
          userId: 'test-user-id',
          name: 'New Parent Category',
          type: CategoryType.expense,
        ).copyWith(id: 'new-parent'),
        Category.create(
          userId: 'test-user-id',
          name: 'Food Parent',
          type: CategoryType.expense,
        ).copyWith(id: 'food-parent'),
        Category.create(
          userId: 'test-user-id',
          name: 'Parent 2',
          type: CategoryType.expense,
        ).copyWith(id: 'parent-2'),
      ],
    );
  }

  /// Setup mocks for successful authentication flow
  static void setupSuccessfulAuthMocks() {
    setupDefaultMocks();

    // firebase_auth_mocks handles authenticated state automatically
    // Use authenticatedUserOverrides() for authenticated testing scenarios
  }

  /// Setup mocks for failed authentication flow
  static void setupFailedAuthMocks(Exception error) {
    setupDefaultMocks();

    when(
      () => mockAuthService.signInWithEmailAndPassword(
        email: any(named: 'email'),
        password: any(named: 'password'),
      ),
    ).thenThrow(error);
    when(
      () => mockAuthService.createUserWithEmailAndPassword(
        email: any(named: 'email'),
        password: any(named: 'password'),
      ),
    ).thenThrow(error);
    when(mockAuthService.signInWithGoogle).thenThrow(error);
  }

  /// Setup mocks for repository operations
  static void setupRepositoryMocks() {
    setupDefaultMocks();

    // Add specific repository mock setups as needed
  }

  /// Setup mocks for offline scenarios
  static void setupOfflineMocks() {
    setupDefaultMocks();

    // Setup mocks to simulate offline behavior
    when(mockFirestoreService.testConnectivity).thenAnswer(
      (_) async => {'success': false, 'error': 'Network unavailable'},
    );
  }

  /// Setup mocks for network error scenarios
  static void setupNetworkErrorMocks() {
    setupDefaultMocks();

    final networkError = Exception('Network error');
    when(
      () => mockAuthService.signInWithEmailAndPassword(
        email: any(named: 'email'),
        password: any(named: 'password'),
      ),
    ).thenThrow(networkError);
    when(
      () => mockUserRepository.createOrUpdateFromFirebaseUser(any()),
    ).thenThrow(networkError);
    when(
      () => mockTransactionRepository.createTransaction(any()),
    ).thenThrow(networkError);
    when(
      () => mockAccountRepository.createAccount(any()),
    ).thenThrow(networkError);
  }

  /// Provider overrides for budgets testing
  static List<Override> get budgetsOverrides => [
    ...authenticatedUserOverrides(),
    // Mock budget providers with empty data
    categoryBudgetInfoProvider(
      CategoryType.expense,
    ).overrideWith((ref) => Future.value(<CategoryBudgetInfo>[])),
    categoryBudgetInfoProvider(
      CategoryType.income,
    ).overrideWith((ref) => Future.value(<CategoryBudgetInfo>[])),
    categoryTypeBudgetSummaryProvider(CategoryType.expense).overrideWith(
      (ref) => Future.value(
        const CategoryTypeBudgetSummary(
          categoryType: CategoryType.expense,
          totalBudgetAmount: 0,
          totalActualAmount: 0,
          currency: 'USD',
          categoriesWithBudgets: 0,
          totalCategories: 0,
        ),
      ),
    ),
    categoryTypeBudgetSummaryProvider(CategoryType.income).overrideWith(
      (ref) => Future.value(
        const CategoryTypeBudgetSummary(
          categoryType: CategoryType.income,
          totalBudgetAmount: 0,
          totalActualAmount: 0,
          currency: 'USD',
          categoriesWithBudgets: 0,
          totalCategories: 0,
        ),
      ),
    ),
  ];
}

/// Provider overrides for specific test scenarios
// ignore: avoid_classes_with_only_static_members
class MockTestScenarios {
  /// New user registration scenario
  static List<Override> get newUserRegistration => [
    ...MockProviders.unauthenticatedUserOverrides,
    // Add specific overrides for new user registration
  ];

  /// Existing user login scenario
  static List<Override> get existingUserLogin => [
    ...MockProviders.authenticatedUserOverrides(),
    // Add specific overrides for existing user login
  ];

  /// User with unverified email scenario
  static List<Override> get unverifiedEmailUser => [
    ...MockProviders.authenticatedUserOverrides(emailVerified: false),
    // Add specific overrides for unverified email
  ];

  /// Premium user scenario
  static List<Override> get premiumUser => [
    ...MockProviders.authenticatedUserOverrides(),
    // Add specific overrides for premium user features
  ];

  /// Free tier user scenario
  static List<Override> get freeTierUser => [
    ...MockProviders.authenticatedUserOverrides(),
    // Add specific overrides for free tier limitations
  ];

  /// Offline mode scenario
  static List<Override> get offlineMode => [
    ...MockProviders.allOverrides,
    // Add specific overrides for offline functionality
  ];

  /// Network error scenario
  static List<Override> get networkError => [
    ...MockProviders.allOverrides,
    // Add specific overrides for network error handling
  ];
}
