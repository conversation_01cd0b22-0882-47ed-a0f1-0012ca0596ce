import 'dart:async';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

import 'firebase_test_setup.dart';
import 'test_container_helper.dart';

/// Comprehensive test isolation helper that manages all aspects of test setup and cleanup
///
/// This helper addresses the core issues with parallel test execution:
/// - Firebase test setup resource conflicts with shared static state
/// - ProviderContainer lifecycle issues
/// - Stream and subscription leaks
/// - Async operation timing issues
/// - Mock state pollution
class TestIsolationHelper {
  TestIsolationHelper._({
    required this.testId,
    required this.containerHelper,
    this.firebaseSetup,
    this.streamSubscriptions = const [],
    this.timers = const [],
  });

  final String testId;
  final TestContainerHelper containerHelper;
  final FirebaseTestSetup? firebaseSetup;
  final List<StreamSubscription<dynamic>> streamSubscriptions;
  final List<Timer> timers;

  // Track all active test instances for global cleanup
  static final Map<String, TestIsolationHelper> _activeTests =
      <String, TestIsolationHelper>{};
  static const Duration _defaultTimeout = Duration(seconds: 10);

  /// Create a new isolated test environment
  ///
  /// [testName] - Human-readable test name for identification
  /// [withFirebase] - Whether to set up Firebase test environment
  /// [withAuthentication] - Whether to create authenticated Firebase user
  /// [providerOverrides] - Provider overrides for the test
  /// [firebaseOptions] - Custom Firebase configuration options
  static Future<TestIsolationHelper> create({
    required String testName,
    bool withFirebase = false,
    bool withAuthentication = true,
    List<Override> providerOverrides = const [],
    Map<String, dynamic>? firebaseOptions,
  }) async {
    final testId = _generateUniqueTestId(testName);

    // Clean up any existing test with the same ID
    await _cleanupExistingTest(testId);

    // Create provider container with proper isolation
    final containerHelper = TestContainerHelper.create(
      overrides: providerOverrides,
      testId: testId,
    );

    // Create Firebase setup if requested
    FirebaseTestSetup? firebaseSetup;
    if (withFirebase) {
      firebaseSetup = withAuthentication
          ? await FirebaseTestSetup.createWithAuthenticatedUser(
              testId: testId,
              uid: 'test-user-$testId',
              email: 'test-$<EMAIL>',
            )
          : await FirebaseTestSetup.createUnauthenticated(testId: testId);
    }

    final helper = TestIsolationHelper._(
      testId: testId,
      containerHelper: containerHelper,
      firebaseSetup: firebaseSetup,
      streamSubscriptions: [],
      timers: [],
    );

    _activeTests[testId] = helper;
    debugPrint('Created isolated test environment: $testName (ID: $testId)');

    return helper;
  }

  /// Get the provider container for this test
  ProviderContainer get container => containerHelper.container;

  /// Get the Firebase test setup (if created)
  FirebaseTestSetup? get firebase => firebaseSetup;

  /// Read a provider from the container
  T read<T>(ProviderListenable<T> provider) {
    return containerHelper.read(provider);
  }

  /// Listen to a provider with automatic subscription tracking
  ProviderSubscription<T> listen<T>(
    ProviderListenable<T> provider,
    void Function(T? previous, T next) listener, {
    void Function(Object error, StackTrace stackTrace)? onError,
    bool fireImmediately = true,
  }) {
    final subscription = containerHelper.listen(
      provider,
      listener,
      onError: onError,
      fireImmediately: fireImmediately,
    );

    // Track subscription for cleanup
    // Note: ProviderSubscription doesn't extend StreamSubscription,
    // but we'll handle it in disposal

    return subscription;
  }

  /// Add a stream subscription to be cleaned up
  void addSubscription(StreamSubscription<dynamic> subscription) {
    streamSubscriptions.add(subscription);
  }

  /// Add a timer to be cleaned up
  void addTimer(Timer timer) {
    timers.add(timer);
  }

  /// Read an async provider with timeout protection
  Future<T> readAsync<T>(
    ProviderListenable<AsyncValue<T>> provider, {
    Duration timeout = _defaultTimeout,
  }) async {
    return containerHelper.readAsync(provider, timeout: timeout);
  }

  /// Pump and settle with timeout protection for widget tests
  Future<void> pumpAndSettleWithTimeout(
    WidgetTester tester, {
    Duration timeout = _defaultTimeout,
    Duration settleDuration = const Duration(milliseconds: 100),
  }) async {
    final completer = Completer<void>();

    // Start pump and settle
    Timer(Duration.zero, () async {
      try {
        await tester.pumpAndSettle(settleDuration);
        if (!completer.isCompleted) {
          completer.complete();
        }
      } catch (e) {
        if (!completer.isCompleted) {
          completer.completeError(e);
        }
      }
    });

    // Set up timeout
    final timeoutTimer = Timer(timeout, () {
      if (!completer.isCompleted) {
        completer.completeError(
          TimeoutException(
            'pumpAndSettle timed out after ${timeout.inSeconds}s',
            timeout,
          ),
        );
      }
    });

    addTimer(timeoutTimer);

    try {
      await completer.future;
    } finally {
      timeoutTimer.cancel();
    }
  }

  /// Dispose all resources with proper cleanup order
  Future<void> dispose() async {
    debugPrint('Starting disposal of test environment: $testId');

    try {
      // Remove from active tests immediately
      _activeTests.remove(testId);

      // 1. Cancel all timers first to prevent new operations
      for (final timer in timers) {
        timer.cancel();
      }
      timers.clear();

      // 2. Cancel all stream subscriptions
      for (final subscription in streamSubscriptions) {
        await subscription.cancel();
      }
      streamSubscriptions.clear();

      // 3. Dispose provider container
      await containerHelper.dispose();

      // 4. Dispose Firebase setup last
      if (firebaseSetup != null) {
        await firebaseSetup!.dispose();
      }

      debugPrint('Successfully disposed test environment: $testId');
    } on Object catch (e) {
      debugPrint('Warning: Error during test disposal for $testId: $e');

      // Force cleanup even if there were errors
      try {
        for (final timer in timers) {
          timer.cancel();
        }
        for (final subscription in streamSubscriptions) {
          await subscription.cancel().catchError((_) {});
        }
        container.dispose();
      } on Object catch (e2) {
        debugPrint('Warning: Error in forced cleanup for $testId: $e2');
      }
    }
  }

  /// Clean up existing test with the same ID
  static Future<void> _cleanupExistingTest(String testId) async {
    final existing = _activeTests[testId];
    if (existing != null) {
      debugPrint('Cleaning up existing test with ID: $testId');
      await existing.dispose();
    }
  }

  /// Generate a unique test ID
  static String _generateUniqueTestId(String testName) {
    final sanitizedName = testName
        .replaceAll(RegExp('[^a-zA-Z0-9]'), '-')
        .toLowerCase();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(9999);
    return '${sanitizedName}_${timestamp}_$random';
  }

  /// Dispose all active test environments (useful for test suite cleanup)
  static Future<void> disposeAll() async {
    final tests = List<TestIsolationHelper>.from(_activeTests.values);
    _activeTests.clear();

    debugPrint('Disposing ${tests.length} active test environments...');

    await Future.wait(
      tests.map((test) => test.dispose()),
      eagerError: false,
    );

    // Also clean up container helper resources
    await TestContainerHelper.disposeAll();

    debugPrint('Disposed all test environments');
  }

  /// Get diagnostic information about active tests
  static Map<String, dynamic> getDiagnostics() {
    return {
      'activeTests': _activeTests.length,
      'testIds': _activeTests.keys.toList(),
      'activeContainers': TestContainerHelper.activeContainerCount,
      'containerIds': TestContainerHelper.activeContainerIds,
    };
  }
}

/// Timeout exception for test operations
class TimeoutException implements Exception {
  TimeoutException(this.message, this.timeout);

  final String message;
  final Duration timeout;

  @override
  String toString() => 'TimeoutException: $message';
}

/// Convenience extension for easier test setup
extension TestIsolationExtensions on TestIsolationHelper {
  /// Quick setup for repository tests
  static Future<TestIsolationHelper> forRepositoryTest(
    String testName, {
    List<Override> overrides = const [],
  }) async {
    return TestIsolationHelper.create(
      testName: testName,
      withFirebase: true,
      withAuthentication: true,
      providerOverrides: overrides,
    );
  }

  /// Quick setup for provider tests
  static Future<TestIsolationHelper> forProviderTest(
    String testName, {
    List<Override> overrides = const [],
  }) async {
    return TestIsolationHelper.create(
      testName: testName,
      withFirebase: false,
      providerOverrides: overrides,
    );
  }

  /// Quick setup for widget tests
  static Future<TestIsolationHelper> forWidgetTest(
    String testName, {
    List<Override> overrides = const [],
    bool withFirebase = false,
  }) async {
    return TestIsolationHelper.create(
      testName: testName,
      withFirebase: withFirebase,
      withAuthentication: true,
      providerOverrides: overrides,
    );
  }
}

/// Global test setup and teardown helper
class GlobalTestSetup {
  /// Setup that should be called once per test file
  static void setupAll() {
    // Register global test cleanup
    addTearDown(() async {
      await TestIsolationHelper.disposeAll();
    });
  }

  /// Setup that should be called for each test
  static void setupEach() {
    // Any per-test setup can go here
  }

  /// Teardown that should be called for each test
  static Future<void> tearDownEach() async {
    // Per-test cleanup can go here if needed
    // Note: Individual tests should handle their own disposal
  }

  /// Teardown that should be called once per test file
  static Future<void> tearDownAll() async {
    await TestIsolationHelper.disposeAll();

    // Print diagnostics for debugging
    final diagnostics = TestIsolationHelper.getDiagnostics();
    final activeTests = diagnostics['activeTests'] as int;
    final activeContainers = diagnostics['activeContainers'] as int;
    if (activeTests > 0 || activeContainers > 0) {
      debugPrint('Warning: Test cleanup incomplete: $diagnostics');
    }
  }
}
