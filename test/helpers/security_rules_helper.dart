import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_auth_mocks/firebase_auth_mocks.dart';
import 'package:flutter/foundation.dart';

import 'firebase_test_setup.dart';

/// Security rules testing utilities for Firestore security rules validation
///
/// This helper provides utilities for testing Firestore security rules with
/// different authentication states and user scenarios.
// ignore: avoid_classes_with_only_static_members
class SecurityRulesHelper {
  /// Load security rules from the firestore.rules file
  static Future<String> loadSecurityRules() async {
    try {
      final rulesFile = File('firestore.rules');
      if (!rulesFile.existsSync()) {
        throw Exception('firestore.rules file not found in project root');
      }

      final rules = await rulesFile.readAsString();
      debugPrint(
        'Loaded security rules from firestore.rules (${rules.length} characters)',
      );
      return rules;
    } catch (e) {
      debugPrint('Error loading security rules: $e');
      rethrow;
    }
  }

  /// Create a test setup with security rules loaded from file
  static Future<FirebaseTestSetup> createSecurityRulesTestSetup({
    MockUser? initialUser,
  }) async {
    final rules = await loadSecurityRules();
    return FirebaseTestSetup.create(
      securityRules: rules,
      initialUser: initialUser,
    );
  }

  /// Test that a user can only access their own data
  static Future<void> testUserIsolation(
    FirebaseTestSetup setup,
    String collectionPath, {
    required String userIdField,
    required Map<String, dynamic> testData,
  }) async {
    // Ensure user is signed in
    if (!setup.isSignedIn) {
      throw Exception('User must be signed in for isolation testing');
    }

    final userId = setup.currentUser!.uid;
    const otherUserId = 'other-user-id';

    // Test: User can create their own data
    final userDocPath = '$collectionPath/user-doc';
    final userTestData = {...testData, userIdField: userId};

    try {
      await setup.setTestData(userDocPath, userTestData);
      debugPrint('✅ User can create their own data in $collectionPath');
    } catch (e) {
      throw Exception('❌ User should be able to create their own data: $e');
    }

    // Test: User can read their own data
    try {
      final doc = await setup.doc(userDocPath).get();
      if (!doc.exists) {
        throw Exception('Document should exist after creation');
      }
      debugPrint('✅ User can read their own data in $collectionPath');
    } catch (e) {
      throw Exception('❌ User should be able to read their own data: $e');
    }

    // Test: User cannot create data for another user
    final otherUserDocPath = '$collectionPath/other-user-doc';
    final otherUserTestData = {...testData, userIdField: otherUserId};

    try {
      await setup.setTestData(otherUserDocPath, otherUserTestData);
      throw Exception(
        '❌ User should NOT be able to create data for another user',
      );
    } catch (e) {
      if (e is FirebaseException) {
        debugPrint(
          "✅ User correctly blocked from creating other user's data: ${e.code}",
        );
      } else {
        rethrow;
      }
    }

    // Test: User cannot read another user's data
    // First, create the other user's data without security rules (if possible)
    // For this test, we'll assume the data exists and try to read it
    try {
      await setup.doc(otherUserDocPath).get();
      throw Exception("❌ User should NOT be able to read another user's data");
    } catch (e) {
      if (e is FirebaseException) {
        debugPrint(
          "✅ User correctly blocked from reading other user's data: ${e.code}",
        );
      } else {
        rethrow;
      }
    }
  }

  /// Test that unauthenticated users cannot access protected data
  static Future<void> testUnauthenticatedAccess(
    String collectionPath,
    Map<String, dynamic> testData,
  ) async {
    final setup = await FirebaseTestSetup.createUnauthenticated(
      loadSecurityRulesFromFile: true,
    );

    final docPath = '$collectionPath/test-doc';

    // Test: Unauthenticated user cannot create data
    try {
      await setup.setTestData(docPath, testData);
      throw Exception(
        '❌ Unauthenticated user should NOT be able to create data',
      );
    } catch (e) {
      if (e is FirebaseException) {
        debugPrint(
          '✅ Unauthenticated user correctly blocked from creating data: ${e.code}',
        );
      } else {
        rethrow;
      }
    }

    // Test: Unauthenticated user cannot read data
    try {
      await setup.doc(docPath).get();
      throw Exception('❌ Unauthenticated user should NOT be able to read data');
    } catch (e) {
      if (e is FirebaseException) {
        debugPrint(
          '✅ Unauthenticated user correctly blocked from reading data: ${e.code}',
        );
      } else {
        rethrow;
      }
    }

    await setup.dispose();
  }

  /// Test data validation rules
  static Future<void> testDataValidation(
    FirebaseTestSetup setup,
    String documentPath,
    Map<String, dynamic> validData,
    Map<String, dynamic> invalidData,
    String validationDescription,
  ) async {
    // Test: Valid data should be accepted
    try {
      await setup.setTestData(documentPath, validData);
      debugPrint('✅ Valid data accepted: $validationDescription');
    } catch (e) {
      throw Exception(
        '❌ Valid data should be accepted ($validationDescription): $e',
      );
    }

    // Test: Invalid data should be rejected
    try {
      await setup.setTestData('${documentPath}_invalid', invalidData);
      throw Exception(
        '❌ Invalid data should be rejected ($validationDescription)',
      );
    } catch (e) {
      if (e is FirebaseException) {
        debugPrint(
          '✅ Invalid data correctly rejected ($validationDescription): ${e.code}',
        );
      } else {
        rethrow;
      }
    }
  }

  /// Test referential integrity rules
  static Future<void> testReferentialIntegrity(
    FirebaseTestSetup setup,
    String documentPath,
    Map<String, dynamic> dataWithValidReference,
    Map<String, dynamic> dataWithInvalidReference,
    String referenceDescription,
  ) async {
    // Test: Data with valid reference should be accepted
    try {
      await setup.setTestData(documentPath, dataWithValidReference);
      debugPrint('✅ Data with valid reference accepted: $referenceDescription');
    } catch (e) {
      throw Exception(
        '❌ Data with valid reference should be accepted ($referenceDescription): $e',
      );
    }

    // Test: Data with invalid reference should be rejected
    try {
      await setup.setTestData(
        '${documentPath}_invalid_ref',
        dataWithInvalidReference,
      );
      throw Exception(
        '❌ Data with invalid reference should be rejected ($referenceDescription)',
      );
    } catch (e) {
      if (e is FirebaseException) {
        debugPrint(
          '✅ Data with invalid reference correctly rejected ($referenceDescription): ${e.code}',
        );
      } else {
        rethrow;
      }
    }
  }

  /// Test premium user access restrictions
  static Future<void> testPremiumUserAccess(
    String collectionPath,
    Map<String, dynamic> testData,
  ) async {
    // Test with free user
    final freeUserSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
      uid: 'free-user-id',
      email: '<EMAIL>',
      loadSecurityRulesFromFile: true,
    );

    // Test with premium user
    final premiumUser = MockUser(
      uid: 'premium-user-id',
      email: '<EMAIL>',
      customClaim: {'premium': true},
    );

    final premiumUserSetup = await FirebaseTestSetup.create(
      initialUser: premiumUser,
      loadSecurityRulesFromFile: true,
    );

    final docPath = '$collectionPath/premium-feature-doc';

    // Test: Free user should be blocked from premium features
    try {
      await freeUserSetup.setTestData(docPath, testData);
      throw Exception(
        '❌ Free user should NOT be able to access premium features',
      );
    } catch (e) {
      if (e is FirebaseException) {
        debugPrint(
          '✅ Free user correctly blocked from premium features: ${e.code}',
        );
      } else {
        rethrow;
      }
    }

    // Test: Premium user should have access
    try {
      await premiumUserSetup.setTestData(docPath, testData);
      debugPrint('✅ Premium user can access premium features');
    } catch (e) {
      throw Exception(
        '❌ Premium user should be able to access premium features: $e',
      );
    }

    await freeUserSetup.dispose();
    await premiumUserSetup.dispose();
  }
}

/// Common security rule test scenarios
// ignore: avoid_classes_with_only_static_members
class SecurityRuleTestScenarios {
  /// Test account collection security rules
  static Future<void> testAccountSecurity() async {
    final setup = await SecurityRulesHelper.createSecurityRulesTestSetup(
      initialUser: MockUser(uid: 'test-user-id', email: '<EMAIL>'),
    );

    await SecurityRulesHelper.testUserIsolation(
      setup,
      'users/test-user-id/accounts',
      userIdField: 'userId',
      testData: FirebaseTestDataFactory.createAccountData(),
    );

    await setup.dispose();
  }

  /// Test transaction collection security rules
  static Future<void> testTransactionSecurity() async {
    final setup = await SecurityRulesHelper.createSecurityRulesTestSetup(
      initialUser: MockUser(uid: 'test-user-id', email: '<EMAIL>'),
    );

    await SecurityRulesHelper.testUserIsolation(
      setup,
      'users/test-user-id/transactions',
      userIdField: 'userId',
      testData: FirebaseTestDataFactory.createTransactionData(),
    );

    await setup.dispose();
  }

  /// Test category collection security rules
  static Future<void> testCategorySecurity() async {
    final setup = await SecurityRulesHelper.createSecurityRulesTestSetup(
      initialUser: MockUser(uid: 'test-user-id', email: '<EMAIL>'),
    );

    await SecurityRulesHelper.testUserIsolation(
      setup,
      'users/test-user-id/categories',
      userIdField: 'userId',
      testData: FirebaseTestDataFactory.createCategoryData(),
    );

    await setup.dispose();
  }

  /// Test budget collection security rules
  static Future<void> testBudgetSecurity() async {
    final setup = await SecurityRulesHelper.createSecurityRulesTestSetup(
      initialUser: MockUser(uid: 'test-user-id', email: '<EMAIL>'),
    );

    final budgetData = {
      'id': 'test-budget-id',
      'userId': 'test-user-id',
      'categoryId': 'test-category-id',
      'plannedAmountCents': 100000,
      'currentAmountCents': 50000,
      'period': 'monthly',
      'periodStart': DateTime.now(),
      'isActive': true,
      'createdAt': DateTime.now(),
      'updatedAt': DateTime.now(),
      'schemaVersion': 1,
    };

    await SecurityRulesHelper.testUserIsolation(
      setup,
      'users/test-user-id/budgets',
      userIdField: 'userId',
      testData: budgetData,
    );

    await setup.dispose();
  }

  /// Test unauthenticated access across all collections
  static Future<void> testUnauthenticatedAccessAll() async {
    final collections = [
      'users/test-user-id/accounts',
      'users/test-user-id/transactions',
      'users/test-user-id/categories',
      'users/test-user-id/budgets',
    ];

    for (final collection in collections) {
      await SecurityRulesHelper.testUnauthenticatedAccess(collection, {
        'test': 'data',
      });
    }
  }

  /// Run comprehensive security rules test suite
  static Future<void> runComprehensiveSecurityTests() async {
    debugPrint('🔒 Starting comprehensive security rules tests...');

    try {
      await testAccountSecurity();
      debugPrint('✅ Account security tests passed');

      await testTransactionSecurity();
      debugPrint('✅ Transaction security tests passed');

      await testCategorySecurity();
      debugPrint('✅ Category security tests passed');

      await testBudgetSecurity();
      debugPrint('✅ Budget security tests passed');

      await testUnauthenticatedAccessAll();
      debugPrint('✅ Unauthenticated access tests passed');

      debugPrint('🎉 All security rules tests passed!');
    } catch (e) {
      debugPrint('❌ Security rules test failed: $e');
      rethrow;
    }
  }
}
