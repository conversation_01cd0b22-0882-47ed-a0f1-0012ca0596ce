import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_auth_mocks/firebase_auth_mocks.dart';

/// Authentication testing utilities using firebase_auth_mocks
///
/// This helper provides standardized patterns for testing authentication
/// scenarios with firebase_auth_mocks, including user creation, sign-in
/// flows, and error simulation.
// ignore: avoid_classes_with_only_static_members
class TestAuthHelper {
  /// Create a mock user with default test values
  static MockUser createMockUser({
    String uid = 'test-user-id',
    String? email = '<EMAIL>',
    String? displayName = 'Test User',
    bool isEmailVerified = true,
    bool isAnonymous = false,
    String? phoneNumber,
    String? photoURL,
    List<UserInfo>? providerData,
    Map<String, dynamic>? customClaim,
  }) {
    return MockUser(
      uid: uid,
      email: email,
      displayName: displayName,
      isEmailVerified: isEmailVerified,
      isAnonymous: isAnonymous,
      phoneNumber: phoneNumber,
      photoURL: photoURL,
      providerData: providerData ?? [],
      customClaim: customClaim ?? {},
    );
  }

  /// Create a mock Firebase Auth instance with optional initial user
  static MockFirebaseAuth createMockAuth({
    MockUser? initialUser,
    bool signedIn = false,
  }) {
    return MockFirebaseAuth(
      mockUser: signedIn ? (initialUser ?? createMockUser()) : null,
      signedIn: signedIn,
    );
  }

  /// Create an authenticated mock auth instance
  static MockFirebaseAuth createAuthenticatedMockAuth({
    String uid = 'test-user-id',
    String email = '<EMAIL>',
    String? displayName = 'Test User',
    bool isEmailVerified = true,
    Map<String, dynamic>? customClaim,
  }) {
    final user = createMockUser(
      uid: uid,
      email: email,
      displayName: displayName,
      isEmailVerified: isEmailVerified,
      customClaim: customClaim,
    );

    return MockFirebaseAuth(mockUser: user, signedIn: true);
  }

  /// Create an unauthenticated mock auth instance
  static MockFirebaseAuth createUnauthenticatedMockAuth() {
    return MockFirebaseAuth(signedIn: false);
  }

  /// Create a mock auth with unverified email user
  static MockFirebaseAuth createUnverifiedEmailMockAuth({
    String uid = 'test-user-id',
    String email = '<EMAIL>',
    String? displayName = 'Test User',
  }) {
    final user = createMockUser(
      uid: uid,
      email: email,
      displayName: displayName,
      isEmailVerified: false,
    );

    return MockFirebaseAuth(mockUser: user, signedIn: true);
  }

  /// Create a mock auth with anonymous user
  static MockFirebaseAuth createAnonymousMockAuth({
    String uid = 'anonymous-user-id',
  }) {
    final user = createMockUser(
      uid: uid,
      email: null,
      displayName: null,
      isAnonymous: true,
      isEmailVerified: false,
    );

    return MockFirebaseAuth(mockUser: user, signedIn: true);
  }

  /// Create a premium user with custom claims
  static MockFirebaseAuth createPremiumUserMockAuth({
    String uid = 'premium-user-id',
    String email = '<EMAIL>',
    String? displayName = 'Premium User',
  }) {
    final user = createMockUser(
      uid: uid,
      email: email,
      displayName: displayName,
      customClaim: {
        'premium': true,
        'subscription': 'premium',
        'subscriptionExpiry': DateTime.now()
            .add(const Duration(days: 365))
            .millisecondsSinceEpoch,
      },
    );

    return MockFirebaseAuth(mockUser: user, signedIn: true);
  }

  /// Create a free tier user
  static MockFirebaseAuth createFreeTierMockAuth({
    String uid = 'free-user-id',
    String email = '<EMAIL>',
    String? displayName = 'Free User',
  }) {
    final user = createMockUser(
      uid: uid,
      email: email,
      displayName: displayName,
      customClaim: {'premium': false, 'subscription': 'free'},
    );

    return MockFirebaseAuth(mockUser: user, signedIn: true);
  }
}

/// Authentication test scenarios for common testing patterns
// ignore: avoid_classes_with_only_static_members
class AuthTestScenarios {
  /// Simulate successful email/password sign in
  static Future<UserCredential> simulateEmailPasswordSignIn(
    MockFirebaseAuth auth, {
    String email = '<EMAIL>',
    String password = 'password123',
  }) async {
    return auth.signInWithEmailAndPassword(email: email, password: password);
  }

  /// Simulate successful user registration
  static Future<UserCredential> simulateUserRegistration(
    MockFirebaseAuth auth, {
    String email = '<EMAIL>',
    String password = 'password123',
  }) async {
    return auth.createUserWithEmailAndPassword(
      email: email,
      password: password,
    );
  }

  /// Simulate anonymous sign in
  static Future<UserCredential> simulateAnonymousSignIn(
    MockFirebaseAuth auth,
  ) async {
    return auth.signInAnonymously();
  }

  /// Simulate password reset email
  static Future<void> simulatePasswordReset(
    MockFirebaseAuth auth, {
    String email = '<EMAIL>',
  }) async {
    return auth.sendPasswordResetEmail(email: email);
  }

  /// Simulate email verification
  static Future<void> simulateEmailVerification(MockFirebaseAuth auth) async {
    return auth.currentUser?.sendEmailVerification();
  }

  /// Simulate sign out
  static Future<void> simulateSignOut(MockFirebaseAuth auth) async {
    return auth.signOut();
  }

  /// Simulate user profile update
  static Future<void> simulateProfileUpdate(
    MockFirebaseAuth auth, {
    String? displayName,
    String? photoURL,
  }) async {
    final user = auth.currentUser;
    if (user != null) {
      if (displayName != null) {
        await user.updateDisplayName(displayName);
      }
      if (photoURL != null) {
        await user.updatePhotoURL(photoURL);
      }
    }
  }

  /// Simulate password change
  static Future<void> simulatePasswordChange(
    MockFirebaseAuth auth, {
    required String newPassword,
  }) async {
    return auth.currentUser?.updatePassword(newPassword);
  }

  /// Simulate user deletion
  static Future<void> simulateUserDeletion(MockFirebaseAuth auth) async {
    return auth.currentUser?.delete();
  }
}

/// Authentication error simulation for testing error handling
// ignore: avoid_classes_with_only_static_members
class AuthErrorSimulation {
  /// Simulate invalid email error
  static FirebaseAuthException get invalidEmail => FirebaseAuthException(
    code: 'invalid-email',
    message: 'The email address is badly formatted.',
  );

  /// Simulate user not found error
  static FirebaseAuthException get userNotFound => FirebaseAuthException(
    code: 'user-not-found',
    message: 'There is no user record corresponding to this identifier.',
  );

  /// Simulate wrong password error
  static FirebaseAuthException get wrongPassword => FirebaseAuthException(
    code: 'wrong-password',
    message: 'The password is invalid or the user does not have a password.',
  );

  /// Simulate weak password error
  static FirebaseAuthException get weakPassword => FirebaseAuthException(
    code: 'weak-password',
    message: 'The password provided is too weak.',
  );

  /// Simulate email already in use error
  static FirebaseAuthException get emailAlreadyInUse => FirebaseAuthException(
    code: 'email-already-in-use',
    message: 'The account already exists for that email.',
  );

  /// Simulate network error
  static FirebaseAuthException get networkError => FirebaseAuthException(
    code: 'network-request-failed',
    message: 'A network error has occurred.',
  );

  /// Simulate too many requests error
  static FirebaseAuthException get tooManyRequests => FirebaseAuthException(
    code: 'too-many-requests',
    message: 'Too many unsuccessful login attempts.',
  );

  /// Simulate requires recent login error
  static FirebaseAuthException get requiresRecentLogin => FirebaseAuthException(
    code: 'requires-recent-login',
    message: 'This operation is sensitive and requires recent authentication.',
  );
}

/// Utility functions for auth testing
// ignore: avoid_classes_with_only_static_members
class AuthTestUtils {
  /// Wait for auth state changes
  static Future<User?> waitForAuthStateChange(
    MockFirebaseAuth auth, {
    Duration timeout = const Duration(seconds: 5),
  }) async {
    return auth.authStateChanges().first.timeout(timeout);
  }

  /// Verify user is signed in
  static bool isUserSignedIn(MockFirebaseAuth auth) {
    return auth.currentUser != null;
  }

  /// Verify user has specific email
  static bool userHasEmail(MockFirebaseAuth auth, String email) {
    return auth.currentUser?.email == email;
  }

  /// Verify user email is verified
  static bool isEmailVerified(MockFirebaseAuth auth) {
    return auth.currentUser?.emailVerified ?? false;
  }

  /// Verify user is anonymous
  static bool isAnonymousUser(MockFirebaseAuth auth) {
    return auth.currentUser?.isAnonymous ?? false;
  }

  /// Verify user has premium claims
  /// Note: This is a simplified check since MockUser doesn't expose customClaim directly
  static bool isPremiumUser(MockFirebaseAuth auth) {
    // This would need to be implemented differently with firebase_auth_mocks
    // For now, return false as a placeholder
    return false;
  }

  /// Get user ID safely
  static String? getUserId(MockFirebaseAuth auth) {
    return auth.currentUser?.uid;
  }

  /// Create test ID token for testing
  static Future<String> getTestIdToken(MockFirebaseAuth auth) async {
    if (auth.currentUser != null) {
      final token = await auth.currentUser!.getIdToken();
      return token ?? 'test-id-token';
    }
    return 'test-id-token';
  }
}
