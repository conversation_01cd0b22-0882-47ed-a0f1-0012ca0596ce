import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/models/goal.dart';
import 'package:budapp/data/models/goal_contribution.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/models/user_profile.dart';
import 'package:firebase_auth_mocks/firebase_auth_mocks.dart';

import 'firebase_test_setup.dart';

/// Factory class for creating consistent mock data for testing
// ignore: avoid_classes_with_only_static_members
class MockDataFactory {
  // Test constants
  static const String testUserId = 'test-user-id';
  static const String testAccountId = 'test-account-id';
  static const String testTransactionId = 'test-transaction-id';
  static const String testCategoryId = 'test-category-id';
  static const String testBudgetId = 'test-budget-id';
  static const String testGoalId = 'test-goal-id';
  static const String testGoalContributionId = 'test-goal-contribution-id';

  static const String testEmail = '<EMAIL>';
  static const String testDisplayName = 'Test User';
  static const String testPassword = 'TestPassword123!';

  /// Creates a mock UserProfile for testing
  static UserProfile createUserProfile({
    String? uid,
    String? email,
    String? displayName,
    String? photoURL,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    bool? isEmailVerified,
    List<String>? authProviders,
    Map<String, dynamic>? preferences,
  }) {
    final now = DateTime.now();
    return UserProfile(
      uid: uid ?? testUserId,
      email: email ?? testEmail,
      displayName: displayName ?? testDisplayName,
      photoURL: photoURL,
      createdAt: createdAt ?? now,
      lastLoginAt: lastLoginAt ?? now,
      isEmailVerified: isEmailVerified ?? true,
      authProviders: authProviders ?? ['password'],
      preferences: preferences ?? _defaultPreferences(),
    );
  }

  /// Creates a mock Account for testing
  static Account createAccount({
    String? id,
    String? userId,
    String? name,
    AccountType? type,
    AccountClassification? classification,
    int? initialBalanceCents,
    String? description,
    String? iconName,
    String? colorHex,
    bool? isActive,
    bool? isPrimary,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    final now = DateTime.now();
    return Account(
      id: id ?? testAccountId,
      userId: userId ?? testUserId,
      name: name ?? 'Test Checking Account',
      type: type ?? AccountType.checking,
      classification: classification ?? AccountClassification.asset,
      initialBalanceCents: initialBalanceCents ?? 100000, // $1000.00 in cents
      currentBalanceCents:
          initialBalanceCents ??
          100000, // Initialize current balance to initial balance
      description: description ?? 'Test account for unit testing',
      iconName: iconName,
      colorHex: colorHex,
      isActive: isActive ?? true,
      isPrimary: isPrimary ?? false,
      createdAt: createdAt ?? now,
      updatedAt: updatedAt ?? now,
      metadata: metadata ?? const {},
    );
  }

  /// Creates a mock Transaction for testing
  static Transaction createTransaction({
    String? id,
    String? userId,
    TransactionType? type,
    TransactionStatus? status,
    int? amountCents,
    String? fromAccountId,
    String? toAccountId,
    String? categoryId,
    String? description,
    String? notes,
    List<String>? tags,
    DateTime? transactionDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    final now = DateTime.now();
    return Transaction(
      id: id ?? testTransactionId,
      userId: userId ?? testUserId,
      type: type ?? TransactionType.expense,
      status: status ?? TransactionStatus.completed,
      amountCents: amountCents ?? 5000, // $50.00 in cents
      fromAccountId: fromAccountId ?? testAccountId,
      toAccountId: toAccountId,
      categoryId: categoryId ?? testCategoryId,
      description: description ?? 'Test transaction',
      notes: notes,
      tagIds: tags ?? ['test'],
      transactionDate: transactionDate ?? now,
      createdAt: createdAt ?? now,
      updatedAt: updatedAt ?? now,
      metadata: metadata ?? const {},
    );
  }

  /// Creates a mock Category for testing
  static Category createCategory({
    String? id,
    String? userId,
    String? name,
    CategoryType? type,
    String? parentId,
    String? description,
    String? color,
    String? icon,
    bool? isActive,
    int? sortOrder,
    int? schemaVersion,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    final now = DateTime.now();
    return Category(
      id: id ?? testCategoryId,
      userId: userId ?? testUserId,
      name: name ?? 'Test Category',
      type: type ?? CategoryType.expense,
      parentId: parentId,
      description: description,
      color: color ?? '#FF5722',
      icon: icon ?? 'category',
      isActive: isActive ?? true,
      sortOrder: sortOrder ?? 0,
      schemaVersion: schemaVersion ?? 1,
      createdAt: createdAt ?? now,
      updatedAt: updatedAt ?? now,
      metadata: metadata ?? const {},
    );
  }

  /// Creates a mock Budget for testing
  static Budget createBudget({
    String? id,
    String? userId,
    BudgetType? type,
    int? plannedAmountCents,
    int? currentAmountCents,
    BudgetPeriod? period,
    DateTime? periodStart,
    String? categoryId,
    String? parentBudgetId,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    final now = DateTime.now();
    final defaultPeriodStart = periodStart ?? DateTime(now.year, now.month, 1);

    return Budget(
      id: id ?? testBudgetId,
      userId: userId ?? testUserId,
      type: type ?? BudgetType.expense,
      plannedAmountCents: plannedAmountCents ?? 100000, // $1000.00 in cents
      currentAmountCents: currentAmountCents ?? 0,
      period: period ?? BudgetPeriod.monthly,
      periodStart: defaultPeriodStart,
      categoryId: categoryId,
      parentBudgetId: parentBudgetId,
      isActive: isActive ?? true,
      createdAt: createdAt ?? now,
      updatedAt: updatedAt ?? now,
      schemaVersion: 1,
    );
  }

  /// Creates a mock Goal for testing
  static Goal createGoal({
    String? id,
    String? userId,
    String? name,
    String? description,
    int? targetAmountCents,
    int? currentAmountCents,
    DateTime? targetDate,
    String? colorHex,
    String? iconName,
    GoalStatus? status,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    final now = DateTime.now();
    return Goal(
      id: id ?? testGoalId,
      userId: userId ?? testUserId,
      name: name ?? 'Test Goal',
      description: description ?? 'Test goal description',
      targetAmountCents: targetAmountCents ?? 100000, // $1000.00 in cents
      currentAmountCents: currentAmountCents ?? 0,
      targetDate: targetDate ?? DateTime(now.year + 1, now.month, now.day),
      colorHex: colorHex ?? '#2196F3',
      iconName: iconName ?? 'account_balance',
      status: status ?? GoalStatus.active,
      isActive: isActive ?? true,
      createdAt: createdAt ?? now,
      updatedAt: updatedAt ?? now,
    );
  }

  /// Creates a mock GoalContribution for testing
  static GoalContribution createGoalContribution({
    String? id,
    String? userId,
    String? goalId,
    int? amountCents,
    DateTime? contributionDate,
    String? description,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    final now = DateTime.now();
    return GoalContribution(
      id: id ?? testGoalContributionId,
      userId: userId ?? testUserId,
      goalId: goalId ?? testGoalId,
      amountCents: amountCents ?? 5000, // $50.00 in cents
      contributionDate: contributionDate ?? now,
      description: description ?? 'Test contribution',
      isActive: isActive ?? true,
      createdAt: createdAt ?? now,
      updatedAt: updatedAt ?? now,
    );
  }

  /// Creates multiple test accounts for a user
  static List<Account> createMultipleAccounts({String? userId, int count = 3}) {
    return List.generate(count, (index) {
      final types = [
        AccountType.checking,
        AccountType.savings,
        AccountType.creditCard,
      ];
      final names = ['Checking Account', 'Savings Account', 'Credit Card'];

      return createAccount(
        id: 'test-account-$index',
        userId: userId,
        name: names[index % names.length],
        type: types[index % types.length],
        isPrimary: index == 0,
      );
    });
  }

  /// Creates multiple test transactions for a user
  static List<Transaction> createMultipleTransactions({
    String? userId,
    String? fromAccountId,
    int count = 5,
  }) {
    return List.generate(count, (index) {
      final types = [TransactionType.expense, TransactionType.income];
      final descriptions = [
        'Grocery Shopping',
        'Salary',
        'Gas',
        'Coffee',
        'Rent',
      ];
      final amounts = [
        5000,
        300000,
        4000,
        500,
        120000,
      ]; // Various amounts in cents

      return createTransaction(
        id: 'test-transaction-$index',
        userId: userId,
        fromAccountId: fromAccountId,
        type: types[index % types.length],
        description: descriptions[index % descriptions.length],
        amountCents: amounts[index % amounts.length],
        transactionDate: DateTime.now().subtract(Duration(days: index)),
      );
    });
  }

  /// Creates multiple test categories for a user
  static List<Category> createMultipleCategories({
    String? userId,
    int count = 4,
  }) {
    return List.generate(count, (index) {
      final names = [
        'Food & Dining',
        'Transportation',
        'Shopping',
        'Entertainment',
      ];
      final colors = ['#FF5722', '#2196F3', '#4CAF50', '#FF9800'];
      final icons = ['restaurant', 'directions_car', 'shopping_cart', 'movie'];

      return createCategory(
        id: 'test-category-$index',
        userId: userId,
        name: names[index % names.length],
        color: colors[index % colors.length],
        icon: icons[index % icons.length],
      );
    });
  }

  /// Creates multiple test budgets for a user
  static List<Budget> createMultipleBudgets({String? userId, int count = 3}) {
    return List.generate(count, (index) {
      final types = [BudgetType.expense, BudgetType.income];
      final periods = [BudgetPeriod.monthly, BudgetPeriod.yearly];

      return createBudget(
        id: 'test-budget-$index',
        userId: userId,
        type: types[index % types.length],
        period: periods[index % periods.length],
        plannedAmountCents: 50000 + (index * 25000), // $500, $750, $1000, etc.
        currentAmountCents: (50000 + (index * 25000)) ~/ 2, // Half spent
      );
    });
  }

  /// Creates a complete user data set for testing
  static Map<String, dynamic> createCompleteUserDataSet({String? userId}) {
    final uid = userId ?? testUserId;

    return {
      'user': createUserProfile(uid: uid),
      'accounts': createMultipleAccounts(userId: uid),
      'transactions': createMultipleTransactions(userId: uid),
      'categories': createMultipleCategories(userId: uid),
      'budgets': [createBudget(userId: uid)],
      // Note: Goals not yet implemented
    };
  }

  /// Default user preferences for testing
  static Map<String, dynamic> _defaultPreferences() {
    return {
      'currency': 'USD',
      'dateFormat': 'MM/dd/yyyy',
      'theme': 'system',
      'notifications': {
        'budgetAlerts': true,
        'goalReminders': true,
        'weeklyReports': false,
      },
      'privacy': {'secureMode': false, 'biometricAuth': false},
    };
  }
}

/// Test scenarios for different user types and situations
// ignore: avoid_classes_with_only_static_members
class TestScenarios {
  /// Data for a new user with no financial data
  static Map<String, dynamic> get newUser => {
    'user': MockDataFactory.createUserProfile(),
    'accounts': <Account>[],
    'transactions': <Transaction>[],
    'categories': <Category>[],
    'budgets': <Budget>[],
  };

  /// Data for a user with basic financial setup
  static Map<String, dynamic> get basicUser => {
    'user': MockDataFactory.createUserProfile(),
    'accounts': [MockDataFactory.createAccount()],
    'transactions': MockDataFactory.createMultipleTransactions(count: 3),
    'categories': MockDataFactory.createMultipleCategories(count: 2),
    'budgets': <Budget>[],
  };

  /// Data for a premium user with full features
  static Map<String, dynamic> get premiumUser => {
    'user': MockDataFactory.createUserProfile(),
    'accounts': MockDataFactory.createMultipleAccounts(count: 5),
    'transactions': MockDataFactory.createMultipleTransactions(count: 20),
    'categories': MockDataFactory.createMultipleCategories(count: 10),
    'budgets': [MockDataFactory.createBudget()],
  };

  /// Data for testing free tier limitations
  static Map<String, dynamic> get freeTierLimitUser => {
    'user': MockDataFactory.createUserProfile(),
    'accounts': MockDataFactory.createMultipleAccounts(
      count: 2,
    ), // At free tier limit
    'transactions': MockDataFactory.createMultipleTransactions(count: 10),
    'categories': MockDataFactory.createMultipleCategories(
      count: 3,
    ), // At free tier limit
    'budgets': <Budget>[],
  };

  /// Data for testing offline scenarios
  static Map<String, dynamic> get offlineUser => {
    'user': MockDataFactory.createUserProfile(),
    'accounts': [MockDataFactory.createAccount()],
    'transactions': [MockDataFactory.createTransaction()],
    'categories': [MockDataFactory.createCategory()],
    'budgets': <Budget>[],
  };

  // Firebase-specific test data patterns

  /// Create a Firebase test setup with pre-populated data
  static Future<FirebaseTestSetup> createPopulatedFirebaseSetup({
    String userId = MockDataFactory.testUserId,
    bool includeAccounts = true,
    bool includeCategories = true,
    bool includeTransactions = true,
    bool includeBudgets = true,
    bool loadSecurityRules = false,
  }) async {
    final user = MockUser(
      uid: userId,
      email: MockDataFactory.testEmail,
      displayName: MockDataFactory.testDisplayName,
      isEmailVerified: true,
    );

    final setup = await FirebaseTestSetup.create(
      initialUser: user,
      loadSecurityRulesFromFile: loadSecurityRules,
    );

    // Populate with test data
    await populateFirebaseTestData(
      setup,
      userId: userId,
      includeAccounts: includeAccounts,
      includeCategories: includeCategories,
      includeTransactions: includeTransactions,
      includeBudgets: includeBudgets,
    );

    return setup;
  }

  /// Populate Firebase test setup with mock data
  static Future<void> populateFirebaseTestData(
    FirebaseTestSetup setup, {
    String userId = MockDataFactory.testUserId,
    bool includeAccounts = true,
    bool includeCategories = true,
    bool includeTransactions = true,
    bool includeBudgets = true,
  }) async {
    // Create user profile
    await setup.setTestData(
      'users/$userId',
      FirebaseTestDataFactory.createUserData(uid: userId),
    );

    if (includeAccounts) {
      // Create test accounts
      final accounts = MockDataFactory.createMultipleAccounts(
        userId: userId,
        count: 3,
      );
      for (var i = 0; i < accounts.length; i++) {
        final account = accounts[i];
        await setup.setTestData(
          'users/$userId/accounts/${account.id}',
          account.toJson(),
        );
      }
    }

    if (includeCategories) {
      // Create test categories
      final categories = MockDataFactory.createMultipleCategories(
        userId: userId,
        count: 5,
      );
      for (var i = 0; i < categories.length; i++) {
        final category = categories[i];
        await setup.setTestData(
          'users/$userId/categories/${category.id}',
          category.toJson(),
        );
      }
    }

    if (includeTransactions) {
      // Create test transactions
      final transactions = MockDataFactory.createMultipleTransactions(
        userId: userId,
        count: 10,
      );
      for (var i = 0; i < transactions.length; i++) {
        final transaction = transactions[i];
        await setup.setTestData(
          'users/$userId/transactions/${transaction.id}',
          transaction.toJson(),
        );
      }
    }

    if (includeBudgets) {
      // Create test budgets
      final budgets = MockDataFactory.createMultipleBudgets(
        userId: userId,
        count: 3,
      );
      for (var i = 0; i < budgets.length; i++) {
        final budget = budgets[i];
        await setup.setTestData(
          'users/$userId/budgets/${budget.id}',
          budget.toJson(),
        );
      }
    }
  }

  /// Create Firebase test data for security rules testing
  static Future<FirebaseTestSetup> createSecurityRulesTestData() async {
    return createPopulatedFirebaseSetup(
      loadSecurityRules: true,
      includeAccounts: true,
      includeCategories: true,
      includeTransactions: true,
      includeBudgets: true,
    );
  }

  /// Create minimal Firebase test setup for performance testing
  static Future<FirebaseTestSetup> createMinimalFirebaseSetup() async {
    return createPopulatedFirebaseSetup(
      includeAccounts: false,
      includeCategories: false,
      includeTransactions: false,
      includeBudgets: false,
    );
  }
}
