import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_auth_mocks/firebase_auth_mocks.dart';
import 'package:flutter/foundation.dart';

/// Standardized Firebase test setup for fake_cloud_firestore + firebase_auth_mocks integration
///
/// This class provides a unified approach to setting up Firebase services for testing,
/// combining fake_cloud_firestore with firebase_auth_mocks for comprehensive testing
/// including security rules validation with proper test isolation.
class FirebaseTestSetup {
  FirebaseTestSetup._({
    required this.auth,
    required this.firestore,
    this.securityRules,
    required this.testId,
    required this.databaseName,
  });
  final MockFirebaseAuth auth;
  final FakeFirebaseFirestore firestore;
  final String? securityRules;
  final String testId;
  final String databaseName;

  /// Create a new Firebase test setup with optional security rules and initial user
  ///
  /// [securityRules] - Optional security rules string for testing rule behavior
  /// [initialUser] - Optional initial user to sign in automatically
  /// [loadSecurityRulesFromFile] - Whether to load security rules from firestore.rules file
  /// [testId] - Optional unique test identifier for isolation (auto-generated if not provided)
  static Future<FirebaseTestSetup> create({
    String? securityRules,
    MockUser? initialUser,
    bool loadSecurityRulesFromFile = false,
    String? testId,
  }) async {
    // Generate unique test ID for isolation
    final actualTestId =
        testId ?? DateTime.now().millisecondsSinceEpoch.toString();

    // Load security rules from file if requested
    var rules = securityRules;
    if (loadSecurityRulesFromFile && rules == null) {
      try {
        final rulesFile = File('firestore.rules');
        if (rulesFile.existsSync()) {
          rules = await rulesFile.readAsString();
          debugPrint(
            'Loaded security rules from firestore.rules for test: $actualTestId',
          );
        }
      } on Exception catch (e) {
        debugPrint(
          'Warning: Could not load security rules from file for test $actualTestId: $e',
        );
      }
    }

    // Create mock auth with optional initial user and unique instance
    final auth = MockFirebaseAuth(
      mockUser: initialUser,
      signedIn: initialUser != null,
    );

    // Create fake firestore with security rules, auth integration, and unique database name
    final databaseName = 'test-db-$actualTestId';
    final firestore = FakeFirebaseFirestore(
      securityRules: rules,
      authObject: auth.authForFakeFirestore,
      // Use unique database name for isolation - this helps with parallel test execution
    );

    return FirebaseTestSetup._(
      auth: auth,
      firestore: firestore,
      securityRules: rules,
      testId: actualTestId,
      databaseName: databaseName,
    );
  }

  /// Create a test setup with an authenticated user
  static Future<FirebaseTestSetup> createWithAuthenticatedUser({
    String uid = 'test-user-id',
    String email = '<EMAIL>',
    String? displayName = 'Test User',
    bool emailVerified = true,
    String? securityRules,
    bool loadSecurityRulesFromFile = false,
    String? testId,
    bool useUniqueUid = false,
  }) async {
    // Generate unique test ID if not provided
    final actualTestId =
        testId ?? DateTime.now().millisecondsSinceEpoch.toString();

    // Create user with optional test-specific UID for better isolation
    // Only append test ID if explicitly requested to maintain backward compatibility
    final uniqueUid = useUniqueUid && uid.contains('test-user')
        ? '$uid-$actualTestId'
        : uid;
    final user = MockUser(
      uid: uniqueUid,
      email: email,
      displayName: displayName,
      isEmailVerified: emailVerified,
    );

    final setup = await create(
      securityRules: securityRules,
      initialUser: user,
      loadSecurityRulesFromFile: loadSecurityRulesFromFile,
      testId: actualTestId,
    );

    return setup;
  }

  /// Create a test setup for unauthenticated scenarios
  static Future<FirebaseTestSetup> createUnauthenticated({
    String? securityRules,
    bool loadSecurityRulesFromFile = false,
    String? testId,
  }) async {
    return create(
      securityRules: securityRules,
      loadSecurityRulesFromFile: loadSecurityRulesFromFile,
      testId: testId,
    );
  }

  /// Sign in a user with email and password
  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    return auth.signInWithEmailAndPassword(email: email, password: password);
  }

  /// Create a user with email and password
  Future<UserCredential> createUserWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    return auth.createUserWithEmailAndPassword(
      email: email,
      password: password,
    );
  }

  /// Sign in anonymously
  Future<UserCredential> signInAnonymously() async {
    return auth.signInAnonymously();
  }

  /// Sign out the current user
  Future<void> signOut() async {
    return auth.signOut();
  }

  /// Get the current user
  User? get currentUser => auth.currentUser;

  /// Check if a user is currently signed in
  bool get isSignedIn => currentUser != null;

  /// Get a document reference for testing
  DocumentReference doc(String path) => firestore.doc(path);

  /// Get a collection reference for testing
  CollectionReference collection(String path) => firestore.collection(path);

  /// Clear all data from the fake firestore
  Future<void> clearFirestore() async {
    await firestore.clearPersistence();
  }

  /// Dump the current state of the fake firestore for debugging
  String dumpFirestore() => firestore.dump();

  /// Add test data to a collection
  Future<DocumentReference> addTestData(
    String collectionPath,
    Map<String, dynamic> data,
  ) async {
    return firestore.collection(collectionPath).add(data);
  }

  /// Set test data for a specific document
  Future<void> setTestData(
    String documentPath,
    Map<String, dynamic> data,
  ) async {
    return firestore.doc(documentPath).set(data);
  }

  /// Cleanup resources with proper test isolation
  Future<void> dispose() async {
    try {
      await signOut();
      await clearFirestore();
      // Additional cleanup for test isolation
      debugPrint('Cleaned up FirebaseTestSetup for test: $testId');
    } on Exception catch (e) {
      debugPrint(
        'Warning: Error during FirebaseTestSetup cleanup for test $testId: $e',
      );
    }
  }

  /// Get the unique test identifier
  String get uniqueTestId => testId;

  /// Get the unique database name for this test
  String get uniqueDatabaseName => databaseName;
}

/// Test data factory for creating consistent test data
// ignore: avoid_classes_with_only_static_members
class FirebaseTestDataFactory {
  /// Create a test user document
  static Map<String, dynamic> createUserData({
    String? uid,
    String? email,
    String? displayName,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    final now = DateTime.now();
    return {
      'uid': uid ?? 'test-user-id',
      'email': email ?? '<EMAIL>',
      'displayName': displayName ?? 'Test User',
      'createdAt': createdAt ?? now,
      'updatedAt': updatedAt ?? now,
      'schemaVersion': 1,
    };
  }

  /// Create a test account document
  static Map<String, dynamic> createAccountData({
    String? id,
    String? userId,
    String? name,
    String? type,
    int? initialBalanceCents,
    int? currentBalanceCents,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    final now = DateTime.now();
    return {
      'id': id ?? 'test-account-id',
      'userId': userId ?? 'test-user-id',
      'name': name ?? 'Test Account',
      'type': type ?? 'checking',
      'classification': 'asset',
      'initialBalanceCents': initialBalanceCents ?? 100000,
      'currentBalanceCents': currentBalanceCents ?? 100000,
      'isActive': isActive ?? true,
      'isPrimary': false,
      'createdAt': createdAt ?? now,
      'updatedAt': updatedAt ?? now,
      'schemaVersion': 1,
    };
  }

  /// Create a test transaction document
  static Map<String, dynamic> createTransactionData({
    String? id,
    String? userId,
    String? accountId,
    String? categoryId,
    String? type,
    int? amountCents,
    String? description,
    DateTime? date,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    final now = DateTime.now();
    return {
      'id': id ?? 'test-transaction-id',
      'userId': userId ?? 'test-user-id',
      'accountId': accountId ?? 'test-account-id',
      'categoryId': categoryId ?? 'test-category-id',
      'type': type ?? 'expense',
      'amountCents': amountCents ?? 5000,
      'description': description ?? 'Test Transaction',
      'date': date ?? now,
      'createdAt': createdAt ?? now,
      'updatedAt': updatedAt ?? now,
      'schemaVersion': 1,
    };
  }

  /// Create a test category document
  static Map<String, dynamic> createCategoryData({
    String? id,
    String? userId,
    String? name,
    String? type,
    String? color,
    String? icon,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    final now = DateTime.now();
    return {
      'id': id ?? 'test-category-id',
      'userId': userId ?? 'test-user-id',
      'name': name ?? 'Test Category',
      'type': type ?? 'expense',
      'color': color ?? '#2196F3',
      'icon': icon ?? 'shopping_cart',
      'isActive': isActive ?? true,
      'createdAt': createdAt ?? now,
      'updatedAt': updatedAt ?? now,
      'schemaVersion': 1,
    };
  }
}

/// Common test scenarios for Firebase testing
// ignore: avoid_classes_with_only_static_members
class FirebaseTestScenarios {
  /// Setup a basic user with account and category
  static Future<void> setupBasicUserData(FirebaseTestSetup setup) async {
    final userId = setup.currentUser?.uid ?? 'test-user-id';

    // Create user document
    await setup.setTestData(
      'users/$userId',
      FirebaseTestDataFactory.createUserData(uid: userId),
    );

    // Create test account
    await setup.setTestData(
      'users/$userId/accounts/test-account-id',
      FirebaseTestDataFactory.createAccountData(userId: userId),
    );

    // Create test category
    await setup.setTestData(
      'users/$userId/categories/test-category-id',
      FirebaseTestDataFactory.createCategoryData(userId: userId),
    );
  }

  /// Setup multiple accounts for testing
  static Future<void> setupMultipleAccounts(
    FirebaseTestSetup setup, {
    int count = 3,
  }) async {
    final userId = setup.currentUser?.uid ?? 'test-user-id';

    for (var i = 0; i < count; i++) {
      await setup.setTestData(
        'users/$userId/accounts/test-account-$i',
        FirebaseTestDataFactory.createAccountData(
          id: 'test-account-$i',
          userId: userId,
          name: 'Test Account $i',
          currentBalanceCents: 100000 + (i * 50000),
        ),
      );
    }
  }

  /// Setup test transactions
  static Future<void> setupTestTransactions(
    FirebaseTestSetup setup, {
    int count = 5,
  }) async {
    final userId = setup.currentUser?.uid ?? 'test-user-id';

    for (var i = 0; i < count; i++) {
      await setup.setTestData(
        'users/$userId/transactions/test-transaction-$i',
        FirebaseTestDataFactory.createTransactionData(
          id: 'test-transaction-$i',
          userId: userId,
          amountCents: 1000 + (i * 500),
          description: 'Test Transaction $i',
        ),
      );
    }
  }
}
