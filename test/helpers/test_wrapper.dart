import 'package:budapp/data/repositories/interfaces/repositories.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/providers.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:mocktail/mocktail.dart';
// Note: DesignTokens import removed - using default themes for now

// Mock classes for testing
class MockFirebaseAuth extends Mock implements FirebaseAuth {}

class MockFirestore extends Mock implements FirebaseFirestore {}

class MockGoogleSignIn extends Mock implements GoogleSignIn {}

class MockUser extends Mock implements User {}

class MockUserRepository extends Mock implements IUserRepository {}

class MockTransactionRepository extends Mock
    implements ITransactionRepository {}

class MockAccountRepository extends Mock implements IAccountRepository {}

/// Comprehensive test wrapper utilities for widget testing with Riverpod
// ignore: avoid_classes_with_only_static_members
class TestWrapper {
  /// Creates a test widget wrapped with ProviderScope and MaterialApp
  ///
  /// [child] - The widget to test
  /// [overrides] - List of provider overrides for testing
  /// [theme] - Optional theme data (defaults to light theme)
  /// [navigatorObservers] - Optional navigator observers for testing navigation
  static Widget createTestWidget(
    Widget child, {
    List<Override>? overrides,
    ThemeData? theme,
    List<NavigatorObserver>? navigatorObservers,
    Size? surfaceSize,
  }) {
    final app = MaterialApp(
      theme: theme ?? ThemeData.light(),
      darkTheme: ThemeData.dark(),
      navigatorObservers: navigatorObservers ?? [],
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en'), // English
      ],
      home: Scaffold(body: child),
    );

    return ProviderScope(
      overrides: overrides ?? [],
      child: surfaceSize != null
          ? SizedBox(
              width: surfaceSize.width,
              height: surfaceSize.height,
              child: app,
            )
          : app,
    );
  }

  /// Creates a test widget with go_router for navigation testing
  ///
  /// [child] - The widget to test (will be wrapped in a route)
  /// [overrides] - List of provider overrides for testing
  /// [theme] - Optional theme data (defaults to light theme)
  /// [initialLocation] - Initial route location (defaults to '/login/forgot-password')
  /// [canPop] - Whether the route can be popped (defaults to true)
  static Widget createTestWidgetWithRouter(
    Widget child, {
    List<Override>? overrides,
    ThemeData? theme,
    String initialLocation = '/login/forgot-password',
    bool canPop = true,
  }) {
    final router = GoRouter(
      initialLocation: initialLocation,
      routes: [
        GoRoute(
          path: '/login',
          builder: (context, state) =>
              const Scaffold(body: Center(child: Text('Login Screen'))),
          routes: [
            GoRoute(
              path: 'forgot-password',
              builder: (context, state) => child,
            ),
          ],
        ),
        GoRoute(
          path: '/signup',
          builder: (context, state) =>
              const Scaffold(body: Center(child: Text('Signup Screen'))),
        ),
        GoRoute(
          path: '/home',
          builder: (context, state) =>
              const Scaffold(body: Center(child: Text('Home Screen'))),
        ),
        GoRoute(path: '/test', builder: (context, state) => child),
      ],
    );

    return ProviderScope(
      overrides: overrides ?? [],
      child: MaterialApp.router(
        theme: theme ?? ThemeData.light(),
        darkTheme: ThemeData.dark(),
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('en'), // English
        ],
        routerConfig: router,
      ),
    );
  }

  /// Creates a test widget with go_router for category and form testing
  ///
  /// [child] - The widget to test
  /// [overrides] - List of provider overrides for testing
  /// [theme] - Optional theme data (defaults to light theme)
  /// [initialLocation] - Initial route location (defaults to '/categories/create')
  static Widget createCategoryTestWidgetWithRouter(
    Widget child, {
    List<Override>? overrides,
    ThemeData? theme,
    String initialLocation = '/categories/create',
  }) {
    final router = GoRouter(
      initialLocation: initialLocation,
      routes: [
        GoRoute(
          path: '/categories',
          builder: (context, state) =>
              const Scaffold(body: Center(child: Text('Categories List'))),
          routes: [
            GoRoute(path: 'create', builder: (context, state) => child),
            GoRoute(path: 'edit/:id', builder: (context, state) => child),
          ],
        ),
        GoRoute(
          path: '/accounts',
          builder: (context, state) =>
              const Scaffold(body: Center(child: Text('Accounts List'))),
          routes: [
            GoRoute(path: 'edit/:id', builder: (context, state) => child),
          ],
        ),
        GoRoute(
          path: '/forms',
          builder: (context, state) =>
              const Scaffold(body: Center(child: Text('Forms List'))),
          routes: [GoRoute(path: 'test', builder: (context, state) => child)],
        ),
      ],
    );

    return ProviderScope(
      overrides: overrides ?? [],
      child: MaterialApp.router(
        theme: theme ?? ThemeData.light(),
        darkTheme: ThemeData.dark(),
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('en'), // English
        ],
        routerConfig: router,
      ),
    );
  }

  /// Creates a test widget with form testing support (no GoRouter navigation)
  ///
  /// [child] - The widget to test
  /// [overrides] - List of provider overrides for testing
  /// [theme] - Optional theme data (defaults to light theme)
  /// [onFormSubmit] - Optional callback for form submission (replaces navigation)
  static Widget createFormTestWidget(
    Widget child, {
    List<Override>? overrides,
    ThemeData? theme,
    VoidCallback? onFormSubmit,
  }) {
    return ProviderScope(
      overrides: overrides ?? [],
      child: MaterialApp(
        theme: theme ?? ThemeData.light(),
        darkTheme: ThemeData.dark(),
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('en'), // English
        ],
        home: Scaffold(body: child),
      ),
    );
  }

  /// Creates a test widget with authentication mocks
  ///
  /// [child] - The widget to test
  /// [isAuthenticated] - Whether the user should be authenticated
  /// [isEmailVerified] - Whether the user's email should be verified
  /// [additionalOverrides] - Additional provider overrides
  static Widget createAuthTestWidget(
    Widget child, {
    bool isAuthenticated = false,
    bool isEmailVerified = true,
    List<Override>? additionalOverrides,
  }) {
    final mockAuth = MockFirebaseAuth();
    final mockUser = isAuthenticated ? MockUser() : null;

    // Setup mock behavior
    when(() => mockAuth.currentUser).thenReturn(mockUser);
    when(mockAuth.authStateChanges).thenAnswer((_) => Stream.value(mockUser));

    if (mockUser != null) {
      when(() => mockUser.emailVerified).thenReturn(isEmailVerified);
      when(() => mockUser.email).thenReturn('<EMAIL>');
      when(() => mockUser.displayName).thenReturn('Test User');
      when(() => mockUser.uid).thenReturn('test-uid');
    }

    final overrides = [
      firebaseAuthProvider.overrideWithValue(mockAuth),
      ...?additionalOverrides,
    ];

    return createTestWidget(child, overrides: overrides);
  }

  /// Creates a test widget with repository mocks
  ///
  /// [child] - The widget to test
  /// [userRepository] - Mock user repository
  /// [transactionRepository] - Mock transaction repository
  /// [accountRepository] - Mock account repository
  /// [additionalOverrides] - Additional provider overrides
  static Widget createRepositoryTestWidget(
    Widget child, {
    IUserRepository? userRepository,
    ITransactionRepository? transactionRepository,
    IAccountRepository? accountRepository,
    List<Override>? additionalOverrides,
  }) {
    final overrides = <Override>[
      if (userRepository != null)
        userRepositoryProvider.overrideWithValue(userRepository),
      if (transactionRepository != null)
        transactionRepositoryProvider.overrideWithValue(transactionRepository),
      if (accountRepository != null)
        accountRepositoryProvider.overrideWithValue(accountRepository),
      ...?additionalOverrides,
    ];

    return createTestWidget(child, overrides: overrides);
  }

  /// Creates a ProviderContainer for testing providers in isolation
  ///
  /// [overrides] - List of provider overrides
  static ProviderContainer createTestContainer({List<Override>? overrides}) {
    return ProviderContainer(overrides: overrides ?? []);
  }

  /// Creates a ProviderContainer with common mocks for testing
  ///
  /// [mockAuth] - Mock Firebase Auth instance
  /// [mockFirestore] - Mock Firestore instance
  /// [mockGoogleSignIn] - Mock Google Sign In instance
  /// [additionalOverrides] - Additional provider overrides
  static ProviderContainer createMockedContainer({
    FirebaseAuth? mockAuth,
    FirebaseFirestore? mockFirestore,
    GoogleSignIn? mockGoogleSignIn,
    List<Override>? additionalOverrides,
  }) {
    final auth = mockAuth ?? MockFirebaseAuth();
    final firestore = mockFirestore ?? MockFirestore();
    final googleSignIn = mockGoogleSignIn ?? MockGoogleSignIn();

    // Setup default mock behavior
    when(() => auth.currentUser).thenReturn(null);
    when(auth.authStateChanges).thenAnswer((_) => Stream.value(null));

    final overrides = [
      firebaseAuthProvider.overrideWithValue(auth),
      firestoreProvider.overrideWithValue(firestore),
      googleSignInProvider.overrideWithValue(googleSignIn),
      ...?additionalOverrides,
    ];

    return ProviderContainer(overrides: overrides);
  }

  /// Pumps a widget and waits for all async operations to complete with timeout
  ///
  /// [tester] - The widget tester
  /// [widget] - The widget to pump
  /// [duration] - Optional duration to wait (default: 5 seconds for better test isolation)
  static Future<void> pumpAndSettle(
    WidgetTester tester,
    Widget widget, {
    Duration? duration,
  }) async {
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle(duration ?? const Duration(seconds: 5));
  }

  /// Pumps a widget and waits with explicit short timeout to prevent hanging
  ///
  /// [tester] - The widget tester
  /// [widget] - The widget to pump
  /// [timeout] - Timeout duration (default: 3 seconds)
  static Future<void> pumpAndSettleWithTimeout(
    WidgetTester tester,
    Widget widget, {
    Duration timeout = const Duration(seconds: 3),
  }) async {
    await tester.pumpWidget(widget);

    try {
      await tester.pumpAndSettle(timeout);
    } on Exception catch (e) {
      // If timeout occurs, pump once more and continue
      await tester.pump();
      // ignore: avoid_print - Debug information for test timeouts
      print('Warning: pumpAndSettle timed out after ${timeout.inSeconds}s: $e');
    }
  }

  /// Safely dispose a ProviderContainer with error handling
  ///
  /// [container] - The container to dispose
  static void disposeContainer(ProviderContainer? container) {
    try {
      container?.dispose();
    } on Exception catch (e) {
      // ignore: avoid_print - Debug information for container disposal
      print('Warning: Error disposing ProviderContainer: $e');
    }
  }

  /// Finds a widget by type and verifies it exists
  ///
  /// [finder] - The finder to use
  /// [shouldExist] - Whether the widget should exist (default: true)
  static void expectWidget<T extends Widget>(
    Finder finder, {
    bool shouldExist = true,
  }) {
    if (shouldExist) {
      expect(finder, findsOneWidget);
    } else {
      expect(finder, findsNothing);
    }
  }

  /// Finds text and verifies it exists
  ///
  /// [text] - The text to find
  /// [shouldExist] - Whether the text should exist (default: true)
  static void expectText(String text, {bool shouldExist = true}) {
    final finder = find.text(text);
    if (shouldExist) {
      expect(finder, findsOneWidget);
    } else {
      expect(finder, findsNothing);
    }
  }

  /// Taps a widget and pumps the widget tree
  ///
  /// [tester] - The widget tester
  /// [finder] - The finder for the widget to tap
  static Future<void> tapAndPump(WidgetTester tester, Finder finder) async {
    await tester.tap(finder);
    await tester.pump();
  }

  /// Enters text into a text field and pumps the widget tree
  ///
  /// [tester] - The widget tester
  /// [finder] - The finder for the text field
  /// [text] - The text to enter
  static Future<void> enterTextAndPump(
    WidgetTester tester,
    Finder finder,
    String text,
  ) async {
    await tester.enterText(finder, text);
    await tester.pump();
  }

  /// Waits for a specific condition to be met
  ///
  /// [tester] - The widget tester
  /// [condition] - The condition to wait for
  /// [timeout] - Maximum time to wait (default: 5 seconds)
  static Future<void> waitFor(
    WidgetTester tester,
    bool Function() condition, {
    Duration timeout = const Duration(seconds: 5),
  }) async {
    final stopwatch = Stopwatch()..start();

    while (!condition() && stopwatch.elapsed < timeout) {
      await tester.pump(const Duration(milliseconds: 100));
    }

    if (!condition()) {
      throw TimeoutException(
        'Condition not met within ${timeout.inSeconds} seconds',
        timeout,
      );
    }
  }

  /// Verifies that a provider has a specific value
  ///
  /// [container] - The provider container
  /// [provider] - The provider to check
  /// [expectedValue] - The expected value
  static void expectProviderValue<T>(
    ProviderContainer container,
    ProviderListenable<T> provider,
    T expectedValue,
  ) {
    final value = container.read(provider);
    expect(value, expectedValue);
  }

  /// Verifies that an AsyncValue provider is in loading state
  ///
  /// [container] - The provider container
  /// [provider] - The AsyncValue provider to check
  static void expectProviderLoading<T>(
    ProviderContainer container,
    ProviderListenable<AsyncValue<T>> provider,
  ) {
    final value = container.read(provider);
    expect(value.isLoading, true);
  }

  /// Verifies that an AsyncValue provider has an error
  ///
  /// [container] - The provider container
  /// [provider] - The AsyncValue provider to check
  /// [expectedError] - Optional expected error type
  static void expectProviderError<T>(
    ProviderContainer container,
    ProviderListenable<AsyncValue<T>> provider, {
    Type? expectedError,
  }) {
    final value = container.read(provider);
    expect(value.hasError, true);
    if (expectedError != null) {
      expect(value.error.runtimeType, expectedError);
    }
  }

  /// Verifies that an AsyncValue provider has data
  ///
  /// [container] - The provider container
  /// [provider] - The AsyncValue provider to check
  /// [expectedData] - Optional expected data value
  static void expectProviderData<T>(
    ProviderContainer container,
    ProviderListenable<AsyncValue<T>> provider, {
    T? expectedData,
  }) {
    final value = container.read(provider);
    expect(value.hasValue, true);
    if (expectedData != null) {
      expect(value.value, expectedData);
    }
  }
}

/// Exception thrown when a condition is not met within the specified timeout
class TimeoutException implements Exception {
  const TimeoutException(this.message, this.timeout);
  final String message;
  final Duration timeout;

  @override
  String toString() => 'TimeoutException: $message';
}
