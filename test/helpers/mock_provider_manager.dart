import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mocktail/mocktail.dart';

/// Manages mock providers with proper isolation and state cleanup
///
/// This class addresses mock state pollution by:
/// - Creating isolated mock instances per test
/// - Resetting mock state between tests
/// - Providing consistent mock configurations
/// - Tracking mock lifecycles
class MockProviderManager {
  MockProviderManager._({
    required this.testId,
    required this.mocks,
  });

  final String testId;
  final Map<Type, Mock> mocks;

  // Track active mock managers
  static final Map<String, MockProviderManager> _activeManagers =
      <String, MockProviderManager>{};

  /// Create a new mock provider manager for a test
  // ignore: prefer_constructors_over_static_methods
  static MockProviderManager create(String testId) {
    // Clean up any existing manager for this test ID
    final existing = _activeManagers[testId];
    if (existing != null) {
      existing.dispose();
    }

    final manager = MockProviderManager._(
      testId: testId,
      mocks: <Type, Mock>{},
    );

    _activeManagers[testId] = manager;
    debugPrint('Created mock provider manager for test: $testId');

    return manager;
  }

  /// Get or create a mock of the specified type
  T getMock<T extends Mock>(T Function() factory) {
    final type = T;

    if (mocks.containsKey(type)) {
      return mocks[type]! as T;
    }

    final mock = factory();
    mocks[type] = mock;

    debugPrint('Created mock of type $type for test: $testId');
    return mock;
  }

  /// Reset all mocks to their initial state
  void resetAllMocks() {
    for (final mock in mocks.values) {
      reset(mock);
    }
    debugPrint('Reset all mocks for test: $testId');
  }

  /// Reset a specific mock type
  void resetMock<T extends Mock>() {
    final type = T;
    final mock = mocks[type];
    if (mock != null) {
      reset(mock);
      debugPrint('Reset mock of type $type for test: $testId');
    }
  }

  /// Get mock verification for a specific type
  T getMockForVerification<T extends Mock>() {
    final type = T;
    final mock = mocks[type];
    if (mock == null) {
      throw StateError('Mock of type $type not found for test: $testId');
    }
    return mock as T;
  }

  /// Dispose the manager and clean up all mocks
  void dispose() {
    // Reset all mocks before disposal
    resetAllMocks();

    // Clear the mocks map
    mocks.clear();

    // Remove from active managers
    _activeManagers.remove(testId);

    debugPrint('Disposed mock provider manager for test: $testId');
  }

  /// Get diagnostic information
  Map<String, dynamic> getDiagnostics() {
    return {
      'testId': testId,
      'mockCount': mocks.length,
      'mockTypes': mocks.keys.map((type) => type.toString()).toList(),
    };
  }

  /// Dispose all active managers
  static void disposeAll() {
    final managers = List<MockProviderManager>.from(_activeManagers.values);
    _activeManagers.clear();

    for (final manager in managers) {
      manager.dispose();
    }

    debugPrint('Disposed all mock provider managers: ${managers.length}');
  }

  /// Get active manager count
  static int get activeManagerCount => _activeManagers.length;

  /// Get active manager test IDs
  static List<String> get activeManagerIds => _activeManagers.keys.toList();
}

/// Builder for creating provider overrides with proper mock management
class MockProviderBuilder {
  MockProviderBuilder._(this.manager);

  final MockProviderManager manager;
  final List<Override> _overrides = [];

  /// Create a new builder for a test
  // ignore: prefer_constructors_over_static_methods
  static MockProviderBuilder forTest(String testId) {
    final manager = MockProviderManager.create(testId);
    return MockProviderBuilder._(manager);
  }

  /// Add a mock provider override
  MockProviderBuilder addMockProvider<T extends Mock, R>(
    Provider<R> provider,
    T Function() mockFactory,
    R mockValue,
  ) {
    // Create the mock to ensure it's tracked
    manager.getMock(mockFactory);
    _overrides.add(provider.overrideWithValue(mockValue));
    // ignore: avoid_returning_this - Builder pattern for method chaining
    return this;
  }

  /// Add a mock provider override with dynamic value
  MockProviderBuilder addMockProviderWithValue<T>(
    Provider<T> provider,
    T value,
  ) {
    _overrides.add(provider.overrideWithValue(value));
    // ignore: avoid_returning_this - Builder pattern for method chaining
    return this;
  }

  /// Add a mock provider override with factory
  MockProviderBuilder addMockProviderWithFactory<T>(
    Provider<T> provider,
    T Function(Ref<T> ref) factory,
  ) {
    _overrides.add(provider.overrideWith(factory));
    // ignore: avoid_returning_this - Builder pattern for method chaining
    return this;
  }

  /// Build the provider overrides list
  List<Override> build() {
    return List<Override>.from(_overrides);
  }

  /// Get the mock manager
  MockProviderManager get mockManager => manager;
}

/// Extension for easier mock setup
extension MockProviderExtensions on MockProviderManager {
  /// Setup common Firebase mocks
  void setupFirebaseMocks() {
    // Implementation would depend on specific Firebase mock interfaces
    debugPrint('Setup Firebase mocks for test: $testId');
  }

  /// Setup common service mocks
  void setupServiceMocks() {
    // Implementation would depend on specific service mock interfaces
    debugPrint('Setup service mocks for test: $testId');
  }

  /// Setup common repository mocks
  void setupRepositoryMocks() {
    // Implementation would depend on specific repository mock interfaces
    debugPrint('Setup repository mocks for test: $testId');
  }
}

/// Utility class for common mock configurations
class CommonMockConfigurations {
  /// Configure a mock to return success responses
  static void configureSuccessResponse<T extends Mock>(T mock) {
    // Common success configuration patterns
    debugPrint('Configured mock for success responses: ${mock.runtimeType}');
  }

  /// Configure a mock to return error responses
  static void configureErrorResponse<T extends Mock>(T mock, Object error) {
    // Common error configuration patterns
    debugPrint('Configured mock for error responses: ${mock.runtimeType}');
  }

  /// Configure a mock to return loading states
  static void configureLoadingResponse<T extends Mock>(T mock) {
    // Common loading configuration patterns
    debugPrint('Configured mock for loading responses: ${mock.runtimeType}');
  }
}

/// Mixin for test classes to easily manage mocks
mixin MockManagementMixin {
  MockProviderManager? _mockManager;

  /// Get the mock manager for the current test
  MockProviderManager get mockManager {
    if (_mockManager == null) {
      throw StateError(
        'Mock manager not initialized. Call setupMocks() first.',
      );
    }
    return _mockManager!;
  }

  /// Setup mocks for a test
  void setupMocks(String testId) {
    _mockManager = MockProviderManager.create(testId);
  }

  /// Cleanup mocks after a test
  void cleanupMocks() {
    _mockManager?.dispose();
    _mockManager = null;
  }

  /// Reset all mocks during a test
  void resetMocks() {
    _mockManager?.resetAllMocks();
  }

  /// Get a mock of the specified type
  T getMock<T extends Mock>(T Function() factory) {
    return mockManager.getMock(factory);
  }

  /// Verify interactions with a mock
  T verifyMock<T extends Mock>() {
    return mockManager.getMockForVerification<T>();
  }
}

/// Test helper that combines mock management with other test utilities
class MockAwareTestHelper {
  MockAwareTestHelper._(
    this.testId,
    this.mockManager,
  );

  final String testId;
  final MockProviderManager mockManager;

  /// Create a new mock-aware test helper
  // ignore: prefer_constructors_over_static_methods
  static MockAwareTestHelper create(String testId) {
    final mockManager = MockProviderManager.create(testId);
    return MockAwareTestHelper._(testId, mockManager);
  }

  /// Create provider overrides with mock management
  MockProviderBuilder createProviderOverrides() {
    return MockProviderBuilder._(mockManager);
  }

  /// Reset all mocks
  void resetMocks() {
    mockManager.resetAllMocks();
  }

  /// Dispose the helper
  void dispose() {
    mockManager.dispose();
  }
}
