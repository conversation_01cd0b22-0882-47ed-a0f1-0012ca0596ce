import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

import 'emulator_port_manager.dart';

/// Helper class for Firebase testing setup and teardown
// ignore: avoid_classes_with_only_static_members
class FirebaseTestHelper {
  // Track initialized apps per test to avoid shared static state
  static final Map<String, bool> _initializedApps = <String, bool>{};
  static final Map<String, String> _testDatabaseNames = <String, String>{};
  static final Map<String, EmulatorPorts> _testEmulatorPorts =
      <String, EmulatorPorts>{};

  /// Initialize Firebase for testing with unique test isolation
  ///
  /// This sets up Firebase with test configuration and connects to the
  /// Firebase Emulator Suite if available. Each test gets a unique app instance.
  ///
  /// [testId] - Optional unique test identifier for isolation
  static Future<String> initializeFirebase({String? testId}) async {
    // Generate unique test ID if not provided
    final actualTestId = testId ?? _generateUniqueTestId();
    final appName = 'test-app-$actualTestId';

    if (_initializedApps[appName] ?? false) {
      return _testDatabaseNames[appName]!;
    }

    try {
      // Create unique database name for this test
      final uniqueDbName = 'test-db-$actualTestId';
      _testDatabaseNames[appName] = uniqueDbName;

      // Use unique project ID for emulator testing with test isolation
      await Firebase.initializeApp(
        name: appName,
        options: FirebaseOptions(
          apiKey: 'demo-api-key-$actualTestId',
          appId: 'demo-app-id-$actualTestId',
          messagingSenderId: 'demo-sender-id',
          projectId: 'demo-budapp-test-$actualTestId',
        ),
      );

      // Allocate and connect to dedicated emulator ports
      final emulatorPorts = await EmulatorPortManager.allocatePortsForTest(
        actualTestId,
      );
      _testEmulatorPorts[appName] = emulatorPorts;
      await _connectToEmulators(appName, emulatorPorts);

      _initializedApps[appName] = true;
      debugPrint(
        'Firebase initialized for testing with unique app: $appName, db: $uniqueDbName, ports: $emulatorPorts',
      );

      return uniqueDbName;
    } catch (e) {
      // Firebase might already be initialized
      if (e.toString().contains('already exists') ||
          e.toString().contains('duplicate-app')) {
        _initializedApps[appName] = true;
        // Try to use existing emulator ports or allocate new ones
        final emulatorPorts =
            _testEmulatorPorts[appName] ??
            await EmulatorPortManager.allocatePortsForTest(actualTestId);
        _testEmulatorPorts[appName] = emulatorPorts;
        await _connectToEmulators(appName, emulatorPorts);
        debugPrint(
          'Firebase already initialized for app: $appName, using ports: $emulatorPorts',
        );
        return _testDatabaseNames[appName] ?? 'test-db-$actualTestId';
      } else {
        debugPrint('Firebase initialization error for app $appName: $e');
        rethrow;
      }
    }
  }

  /// Connect to Firebase Emulator Suite with app-specific configuration and dedicated ports
  static Future<void> _connectToEmulators(
    String appName,
    EmulatorPorts ports,
  ) async {
    try {
      final app = Firebase.app(appName);

      // Connect to Firestore Emulator with dedicated port
      FirebaseFirestore.instanceFor(
        app: app,
      ).useFirestoreEmulator('localhost', ports.firestorePort);
      debugPrint(
        'Connected to Firestore emulator for app $appName on localhost:${ports.firestorePort}',
      );

      // Connect to Auth Emulator with dedicated port
      await FirebaseAuth.instanceFor(
        app: app,
      ).useAuthEmulator('localhost', ports.authPort);
      debugPrint(
        'Connected to Auth emulator for app $appName on localhost:${ports.authPort}',
      );
    } on Exception catch (e) {
      // Emulators might already be connected or not running
      // This is acceptable for testing, but log the issue
      debugPrint('Emulator connection warning for app $appName: $e');
    }
  }

  /// Clean up Firebase resources after testing with proper isolation
  ///
  /// [testId] - Optional test identifier for cleanup of specific app instance
  static Future<void> cleanup({String? testId}) async {
    try {
      if (testId != null) {
        // Clean up specific test app
        final appName = 'test-app-$testId';
        await _cleanupAppInstance(appName);
      } else {
        // Clean up all test apps
        final appNames = List<String>.from(_initializedApps.keys);
        for (final appName in appNames) {
          await _cleanupAppInstance(appName);
        }
      }
    } on Exception catch (e) {
      // Cleanup errors are not critical for tests
      debugPrint('Cleanup warning: $e');
    }
  }

  /// Clean up a specific Firebase app instance
  static Future<void> _cleanupAppInstance(String appName) async {
    try {
      if (_initializedApps[appName] != true) return;

      final app = Firebase.app(appName);

      // Clear Firestore data if using emulator
      if (_isUsingEmulator()) {
        await _clearFirestoreData(appName);
      }

      // Sign out any authenticated users for this app
      final auth = FirebaseAuth.instanceFor(app: app);
      if (auth.currentUser != null) {
        await auth.signOut();
      }

      // Delete the Firebase app instance to free resources
      await app.delete();

      // Clean up emulator port allocation
      final emulatorPorts = _testEmulatorPorts.remove(appName);
      if (emulatorPorts != null) {
        // Extract test ID from app name to deallocate ports
        final testId = appName.replaceFirst('test-app-', '');
        await EmulatorPortManager.deallocatePortsForTest(testId);
      }

      // Remove from tracking
      _initializedApps.remove(appName);
      _testDatabaseNames.remove(appName);

      debugPrint('Cleaned up Firebase app: $appName');
    } on Exception catch (e) {
      debugPrint('Error cleaning up app $appName: $e');
      // Still remove from tracking even if cleanup failed
      _initializedApps.remove(appName);
      _testDatabaseNames.remove(appName);
    }
  }

  /// Check if we're using the Firebase Emulator
  static bool _isUsingEmulator() {
    try {
      // This is a simple check - in a real implementation you might
      // want to check environment variables or configuration
      return true; // Assume we're always using emulator in tests
    } on Exception {
      return false;
    }
  }

  /// Clear all Firestore data for a specific app (emulator only)
  static Future<void> _clearFirestoreData(String appName) async {
    try {
      // In a real implementation, you would call the emulator's clear endpoint
      // For now, we'll just ensure we're not accidentally clearing production data
      if (!_isUsingEmulator()) {
        throw Exception('Cannot clear data when not using emulator');
      }

      final app = Firebase.app(appName);
      final firestore = FirebaseFirestore.instanceFor(app: app);

      // Clear all data by terminating the instance
      // This ensures test isolation by resetting the Firestore state
      await firestore.terminate();
      await firestore.clearPersistence();

      debugPrint('Cleared Firestore data for app: $appName');
    } on Exception catch (e) {
      // Clearing data is not critical for individual tests
      debugPrint('Warning: Could not clear Firestore data for $appName: $e');
    }
  }

  /// Create a test user with the given email and password for a specific app
  static Future<User> createTestUser({
    required String email,
    required String password,
    String? testId,
  }) async {
    final appName = testId != null ? 'test-app-$testId' : '[DEFAULT]';
    final auth = testId != null
        ? FirebaseAuth.instanceFor(app: Firebase.app(appName))
        : FirebaseAuth.instance;

    final userCredential = await auth.createUserWithEmailAndPassword(
      email: email,
      password: password,
    );
    return userCredential.user!;
  }

  /// Delete a test user for a specific app
  static Future<void> deleteTestUser(User user, {String? testId}) async {
    try {
      await user.delete();
    } on Exception {
      // User might already be deleted or not exist
    }
  }

  /// Generate a unique test email
  static String generateTestEmail() {
    return 'test${DateTime.now().millisecondsSinceEpoch}@example.com';
  }

  /// Generate a unique test ID
  static String generateTestId([String? prefix]) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return prefix != null ? '${prefix}_$timestamp' : 'test_$timestamp';
  }

  /// Generate a unique test ID with random component for better isolation
  static String _generateUniqueTestId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(99999);
    return '${timestamp}_$random';
  }

  /// Get Firebase app instance for a specific test
  static FirebaseApp getTestApp(String testId) {
    final appName = 'test-app-$testId';
    return Firebase.app(appName);
  }

  /// Get Firestore instance for a specific test
  static FirebaseFirestore getTestFirestore(String testId) {
    final app = getTestApp(testId);
    return FirebaseFirestore.instanceFor(app: app);
  }

  /// Get Firebase Auth instance for a specific test
  static FirebaseAuth getTestAuth(String testId) {
    final app = getTestApp(testId);
    return FirebaseAuth.instanceFor(app: app);
  }

  /// Get the unique database name for a test
  static String? getTestDatabaseName(String testId) {
    final appName = 'test-app-$testId';
    return _testDatabaseNames[appName];
  }
}
