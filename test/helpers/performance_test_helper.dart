import 'package:budapp/data/repositories/interfaces/performance_optimized_repository.dart';

/// Test helper for mocking performance services to avoid noise in test output
class PerformanceTestHelper {
  /// Set up mock performance services to prevent debug noise during tests
  static void setupMockPerformanceServices() {
    // Performance services are static classes, so we can't directly inject them
    // Instead, we silence the debug output by ensuring tests run in non-debug mode
    // The actual performance service implementation already checks for Firebase
    // initialization and handles missing Firebase gracefully
  }

  /// Create a mock performance monitoring service for repository tests
  static MockPerformanceMonitoringService
  createMockPerformanceMonitoringService() {
    return MockPerformanceMonitoringService();
  }

  /// Suppress debug output during tests by ensuring clean test environment
  static void suppressPerformanceDebugOutput() {
    // The performance services already handle missing Firebase gracefully
    // and only log debug messages when Firebase is not available
    // This is the expected behavior for unit tests
  }
}
