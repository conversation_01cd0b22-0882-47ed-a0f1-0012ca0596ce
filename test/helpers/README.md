# Test Isolation Framework

This directory contains a comprehensive test isolation framework designed to resolve parallel test execution issues in the BudApp Flutter project.

## Overview

The framework addresses the following core issues that cause test failures in parallel execution:

1. **Firebase test setup resource conflicts** with shared static state
2. **ProviderContainer lifecycle issues** and resource leaks
3. **Stream and subscription leaks** 
4. **Async operation timing issues**
5. **Mock state pollution** between tests

## Components

### Core Helpers

#### `test_isolation_helper.dart`
The main orchestrator that provides complete test isolation:

```dart
// Create isolated test environment
final testEnv = await TestIsolationHelper.create(
  testName: 'my-test',
  withFirebase: true,
  withAuthentication: true,
  providerOverrides: [
    // Your provider overrides
  ],
);

// Use the environment
final data = await testEnv.readAsync(myProvider);
expect(data, expectedValue);

// Always dispose when done
await testEnv.dispose();
```

**Features:**
- Unique test IDs for complete isolation
- Firebase setup with dedicated resources
- Provider container lifecycle management
- Stream and timer cleanup
- Timeout protection for all operations

#### `firebase_test_helper.dart`
Enhanced Firebase testing with proper isolation:

```dart
// Initialize with unique app instance and database
final testId = await FirebaseTestHelper.initializeFirebase();

// Each test gets its own Firebase app and database
final firestore = FirebaseTestHelper.getTestFirestore(testId);
final auth = FirebaseTestHelper.getTestAuth(testId);

// Cleanup when done
await FirebaseTestHelper.cleanup(testId: testId);
```

**Key Improvements:**
- Unique Firebase app instances per test
- Dedicated emulator ports to prevent conflicts
- Proper resource cleanup and app deletion
- Test database isolation

#### `firebase_test_setup.dart`
Mock Firebase environment with test isolation:

```dart
// Create isolated Firebase setup
final setup = await FirebaseTestSetup.createWithAuthenticatedUser(
  testId: 'unique-test-id',
  uid: 'test-user-123',
  email: '<EMAIL>',
);

// Use for testing
await setup.setTestData('path/to/doc', data);
final doc = await setup.doc('path/to/doc').get();

// Cleanup
await setup.dispose();
```

#### `test_container_helper.dart`
Manages ProviderContainer lifecycle with proper isolation:

```dart
// Create isolated container
final helper = TestContainerHelper.create(
  overrides: [provider.overrideWithValue(mockValue)],
  testId: 'my-test',
);

// Use container
final result = helper.read(myProvider);

// Dispose with timeout protection
await helper.dispose();
```

**Features:**
- Timeout protection for disposal operations
- Automatic tracking of active containers
- Isolated provider state per test
- Global cleanup capabilities

#### `mock_provider_manager.dart`
Manages mock state with proper isolation:

```dart
// Create mock manager
final mockManager = MockProviderManager.create('test-id');

// Get isolated mock instances
final mockService = mockManager.getMock(() => MockMyService());

// Reset state between test phases
mockManager.resetAllMocks();

// Cleanup
mockManager.dispose();
```

**Features:**
- Isolated mock instances per test
- State reset capabilities
- Automatic mock tracking and cleanup
- Provider override builder integration

#### `emulator_port_manager.dart`
Manages Firebase emulator port allocation:

```dart
// Allocate unique ports for test
final ports = await EmulatorPortManager.allocatePortsForTest('test-id');

// Use ports for Firebase emulator connections
// Firestore: ports.firestorePort
// Auth: ports.authPort

// Cleanup when done
await EmulatorPortManager.deallocatePortsForTest('test-id');
```

**Features:**
- Dynamic port allocation to prevent conflicts
- Port availability checking
- Automatic cleanup and tracking
- Support for multiple emulator services

## Usage Patterns

### Basic Test Setup

```dart
void main() {
  group('My Feature Tests', () {
    test('should work in isolation', () async {
      final testEnv = await TestIsolationHelper.create(
        testName: 'basic-test',
        withFirebase: false,
      );
      
      // Your test logic here
      
      await testEnv.dispose();
    });
  });
  
  tearDownAll(() async {
    await TestIsolationHelper.disposeAll();
  });
}
```

### Firebase Integration Test

```dart
test('should handle Firebase operations', () async {
  final testEnv = await TestIsolationHelper.create(
    testName: 'firebase-test',
    withFirebase: true,
    withAuthentication: true,
  );
  
  // Test Firebase operations
  await testEnv.firebase!.setTestData('test/doc', {'data': 'value'});
  final doc = await testEnv.firebase!.doc('test/doc').get();
  expect(doc.exists, isTrue);
  
  await testEnv.dispose();
});
```

### Provider Testing

```dart
test('should test providers in isolation', () async {
  final mockService = MockMyService();
  when(() => mockService.getData()).thenAnswer((_) async => 'test-data');
  
  final testEnv = await TestIsolationHelper.create(
    testName: 'provider-test',
    providerOverrides: [
      myServiceProvider.overrideWithValue(mockService),
    ],
  );
  
  final result = await testEnv.readAsync(myDataProvider);
  expect(result, equals('test-data'));
  
  await testEnv.dispose();
});
```

### Convenience Extensions

```dart
// Quick setup for different test types
final repoTest = await TestIsolationExtensions.forRepositoryTest('repo-test');
final providerTest = await TestIsolationExtensions.forProviderTest('provider-test');
final widgetTest = await TestIsolationExtensions.forWidgetTest('widget-test');
```

## Best Practices

### 1. Always Use Unique Test Names
```dart
// Good: Descriptive and unique
await TestIsolationHelper.create(testName: 'user-login-success-flow');

// Bad: Generic names that might conflict
await TestIsolationHelper.create(testName: 'test');
```

### 2. Proper Cleanup
```dart
// Always dispose individual tests
await testEnv.dispose();

// Always include global cleanup
tearDownAll(() async {
  await TestIsolationHelper.disposeAll();
  MockProviderManager.disposeAll();
});
```

### 3. Timeout Protection
```dart
// Use timeout protection for async operations
final data = await testEnv.readAsync(
  asyncProvider,
  timeout: Duration(seconds: 5),
);
```

### 4. Mock State Management
```dart
// Reset mocks between test phases
mockManager.resetAllMocks();

// Or reset specific mock types
mockManager.resetMock<MockMyService>();
```

## Benefits

### Before (Issues)
- Tests fail in parallel due to shared Firebase state
- Provider containers leak between tests
- Stream subscriptions cause memory leaks
- Mock state pollution causes false positives/negatives
- Emulator port conflicts prevent parallel execution

### After (Solutions)
- ✅ Complete test isolation with unique resources
- ✅ Proper cleanup of all test resources
- ✅ Timeout protection prevents hanging tests
- ✅ Parallel test execution without conflicts
- ✅ Deterministic test results

## Migration Guide

### From Old Test Pattern
```dart
// OLD: Shared state and manual cleanup
group('Old Tests', () {
  late ProviderContainer container;
  
  setUp(() {
    container = ProviderContainer(overrides: [...]);
  });
  
  tearDown(() {
    container.dispose(); // May fail or hang
  });
  
  test('my test', () {
    // Test logic with potential conflicts
  });
});
```

### To New Test Pattern
```dart
// NEW: Isolated and automatically managed
group('New Tests', () {
  test('my test', () async {
    final testEnv = await TestIsolationHelper.create(
      testName: 'my-isolated-test',
      providerOverrides: [...],
    );
    
    // Test logic with complete isolation
    
    await testEnv.dispose(); // Guaranteed cleanup
  });
});
```

## Performance Impact

- **Setup Time**: ~50-100ms per test (acceptable for isolation benefits)
- **Memory Usage**: Isolated per test (prevents leaks)
- **Parallel Execution**: Now possible without conflicts
- **Test Reliability**: Dramatically improved

## Validation

The framework includes comprehensive tests demonstrating:
- Firebase test isolation
- Provider container lifecycle management
- Stream and subscription cleanup
- Mock state isolation
- Parallel test execution

Run the validation tests:
```bash
flutter test test/examples/simple_test_isolation_example_test.dart
```

## Future Enhancements

1. **Integration with existing test helpers** for backward compatibility
2. **Performance optimizations** for test setup/teardown
3. **Additional emulator services** (Functions, Storage, etc.)
4. **Test result caching** for faster re-runs
5. **Visual test isolation dashboard** for debugging

## Support

For issues or questions about the test isolation framework:

1. Check the validation tests for usage examples
2. Review the documentation in each helper file
3. Examine existing test patterns in the codebase
4. Ensure proper cleanup in all test implementations

---

*This framework provides the foundation for reliable, parallel test execution in the BudApp project.*