import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Helper class for managing ProviderContainer lifecycle in tests with proper isolation
///
/// This class addresses issues with:
/// - Container disposal timing and resource leaks
/// - Proper cleanup order and timeout protection
/// - Shared state pollution between tests
class TestContainerHelper {
  TestContainerHelper._({
    required this.container,
    required this.testId,
    this.disposalTimeout = const Duration(seconds: 5),
  });

  final ProviderContainer container;
  final String testId;
  final Duration disposalTimeout;

  // Track active containers to prevent leaks
  static final Map<String, TestContainerHelper> _activeContainers =
      <String, TestContainerHelper>{};

  /// Create a new test container with proper isolation
  ///
  /// [overrides] - Provider overrides for the test
  /// [testId] - Optional unique test identifier (auto-generated if not provided)
  /// [disposalTimeout] - Timeout for container disposal operations
  // ignore: prefer_constructors_over_static_methods
  static TestContainerHelper create({
    List<Override> overrides = const [],
    String? testId,
    Duration disposalTimeout = const Duration(seconds: 5),
  }) {
    final actualTestId = testId ?? _generateUniqueTestId();

    // Clean up any existing container with the same test ID
    if (_activeContainers.containsKey(actualTestId)) {
      _activeContainers[actualTestId]!.dispose();
    }

    final container = ProviderContainer(overrides: overrides);
    final helper = TestContainerHelper._(
      container: container,
      testId: actualTestId,
      disposalTimeout: disposalTimeout,
    );

    _activeContainers[actualTestId] = helper;
    debugPrint('Created test container: $actualTestId');

    return helper;
  }

  /// Create a test container with Firebase test setup integration
  ///
  /// This creates a container with common Firebase-related overrides
  /// and ensures proper integration with FirebaseTestSetup.
  static TestContainerHelper createWithFirebase({
    List<Override> additionalOverrides = const [],
    String? testId,
    Duration disposalTimeout = const Duration(seconds: 5),
  }) {
    // Common Firebase-related overrides can be added here
    final overrides = <Override>[
      ...additionalOverrides,
    ];

    return create(
      overrides: overrides,
      testId: testId,
      disposalTimeout: disposalTimeout,
    );
  }

  /// Read a provider from the container
  T read<T>(ProviderListenable<T> provider) {
    return container.read(provider);
  }

  /// Listen to a provider from the container
  ProviderSubscription<T> listen<T>(
    ProviderListenable<T> provider,
    void Function(T? previous, T next) listener, {
    void Function(Object error, StackTrace stackTrace)? onError,
    bool fireImmediately = true,
  }) {
    return container.listen(
      provider,
      listener,
      onError: onError,
      fireImmediately: fireImmediately,
    );
  }

  /// Dispose the container with proper timeout protection
  Future<void> dispose() async {
    try {
      // Remove from active containers immediately to prevent reuse
      _activeContainers.remove(testId);

      await _disposeWithTimeout();
      debugPrint('Disposed test container: $testId');
    } on Object catch (e) {
      debugPrint('Warning: Error disposing test container $testId: $e');
      // Force disposal even if there was an error
      try {
        container.dispose();
      } on Object catch (e2) {
        debugPrint('Warning: Error in force disposal for $testId: $e2');
      }
    }
  }

  /// Dispose with timeout protection to prevent hanging tests
  Future<void> _disposeWithTimeout() async {
    final completer = Completer<void>();

    // Start disposal
    Timer(Duration.zero, () {
      try {
        container.dispose();
        if (!completer.isCompleted) {
          completer.complete();
        }
      } catch (e) {
        if (!completer.isCompleted) {
          completer.completeError(e);
        }
      }
    });

    // Set up timeout
    Timer(disposalTimeout, () {
      if (!completer.isCompleted) {
        completer.completeError(
          TimeoutException(
            'Container disposal timed out after ${disposalTimeout.inSeconds}s',
            disposalTimeout,
          ),
        );
      }
    });

    return completer.future;
  }

  /// Generate a unique test ID
  static String _generateUniqueTestId() {
    return 'test-container-${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Clean up all active containers (useful for test suite cleanup)
  static Future<void> disposeAll() async {
    final containers = List<TestContainerHelper>.from(_activeContainers.values);
    _activeContainers.clear();

    await Future.wait(
      containers.map((helper) => helper.dispose()),
      eagerError: false,
    );

    debugPrint('Disposed all test containers: ${containers.length}');
  }

  /// Get count of active containers (useful for debugging)
  static int get activeContainerCount => _activeContainers.length;

  /// Get active container test IDs (useful for debugging)
  static List<String> get activeContainerIds => _activeContainers.keys.toList();
}

/// Test setup helper that combines Firebase and Provider container setup
class IntegratedTestSetup {
  IntegratedTestSetup._({
    required this.containerHelper,
  }) : firebaseTestSetup = null;

  final TestContainerHelper containerHelper;
  final dynamic firebaseTestSetup; // Using dynamic to avoid import issues

  /// Create integrated test setup with both Firebase and Provider container
  static Future<IntegratedTestSetup> create({
    List<Override> overrides = const [],
    String? testId,
    bool withAuthentication = true,
  }) async {
    final actualTestId =
        testId ?? DateTime.now().millisecondsSinceEpoch.toString();

    // Create container helper
    final containerHelper = TestContainerHelper.create(
      overrides: overrides,
      testId: actualTestId,
    );

    // Note: Firebase test setup creation would be added here if needed
    // For now, keeping it separate to avoid circular dependencies

    return IntegratedTestSetup._(
      containerHelper: containerHelper,
    );
  }

  /// Read a provider from the container
  T read<T>(ProviderListenable<T> provider) {
    return containerHelper.read(provider);
  }

  /// Listen to a provider from the container
  ProviderSubscription<T> listen<T>(
    ProviderListenable<T> provider,
    void Function(T? previous, T next) listener, {
    void Function(Object error, StackTrace stackTrace)? onError,
    bool fireImmediately = true,
  }) {
    return containerHelper.listen(
      provider,
      listener,
      onError: onError,
      fireImmediately: fireImmediately,
    );
  }

  /// Dispose all resources
  Future<void> dispose() async {
    await containerHelper.dispose();

    // Firebase cleanup would be added here if firebaseTestSetup is not null
    if (firebaseTestSetup != null) {
      try {
        final fb = firebaseTestSetup;
        // Use reflection-like approach to safely check for dispose method
        if (fb != null) {
          try {
            await (fb as dynamic).dispose();
          } on Exception {
            // dispose method doesn't exist or failed, skip cleanup
          }
        }
      } on Object catch (e) {
        debugPrint('Warning: Error disposing Firebase test setup: $e');
      }
    }
  }
}

/// Extensions for easier testing with timeout protection
extension TestContainerExtensions on TestContainerHelper {
  /// Read a provider with timeout protection
  Future<T> readAsync<T>(
    ProviderListenable<AsyncValue<T>> provider, {
    Duration timeout = const Duration(seconds: 10),
  }) async {
    final completer = Completer<T>();
    late ProviderSubscription subscription;

    subscription = listen(
      provider,
      (previous, next) {
        // ignore: avoid_dynamic_calls - Dynamic callback from Riverpod listen method
        next.when(
          data: (T data) {
            if (!completer.isCompleted) {
              completer.complete(data);
              subscription.close();
            }
          },
          error: (Object error, StackTrace stackTrace) {
            if (!completer.isCompleted) {
              completer.completeError(error, stackTrace);
              subscription.close();
            }
          },
          loading: () {
            // Continue waiting
          },
        );
      },
      onError: (Object error, StackTrace stackTrace) {
        if (!completer.isCompleted) {
          completer.completeError(error, stackTrace);
          subscription.close();
        }
      },
    );

    // Set up timeout
    Timer(timeout, () {
      if (!completer.isCompleted) {
        subscription.close();
        completer.completeError(
          TimeoutException(
            'Provider read timed out after ${timeout.inSeconds}s',
            timeout,
          ),
        );
      }
    });

    return completer.future;
  }
}

/// Timeout exception for test operations
class TimeoutException implements Exception {
  TimeoutException(this.message, this.timeout);

  final String message;
  final Duration timeout;

  @override
  String toString() => 'TimeoutException: $message';
}
