import 'dart:io';
import 'dart:math';

import 'package:flutter/foundation.dart';

/// Manages Firebase emulator port allocation for parallel test execution
///
/// This class addresses emulator conflicts by:
/// - Allocating unique ports per test run
/// - Checking port availability before allocation
/// - Providing fallback port ranges
/// - Cleaning up port allocations
class EmulatorPortManager {
  EmulatorPortManager._();

  static const int _baseFirestorePort = 8080;
  static const int _baseAuthPort = 9099;
  static const int _baseFunctionsPort = 5001;
  static const int _baseStoragePort = 9199;
  static const int _portRangeSize = 1000;

  // Track allocated ports to avoid conflicts
  static final Set<int> _allocatedPorts = <int>{};
  static final Map<String, EmulatorPorts> _testPorts =
      <String, EmulatorPorts>{};

  /// Allocate unique emulator ports for a test
  static Future<EmulatorPorts> allocatePortsForTest(String testId) async {
    // Clean up any existing allocation for this test ID
    await deallocatePortsForTest(testId);

    final ports = await _findAvailablePorts();
    _testPorts[testId] = ports;

    debugPrint('Allocated emulator ports for test $testId: $ports');
    return ports;
  }

  /// Deallocate ports for a specific test
  static Future<void> deallocatePortsForTest(String testId) async {
    final ports = _testPorts.remove(testId);
    if (ports != null) {
      _allocatedPorts.remove(ports.firestorePort);
      _allocatedPorts.remove(ports.authPort);
      _allocatedPorts.remove(ports.functionsPort);
      _allocatedPorts.remove(ports.storagePort);

      debugPrint('Deallocated emulator ports for test $testId');
    }
  }

  /// Get allocated ports for a test (if any)
  static EmulatorPorts? getPortsForTest(String testId) {
    return _testPorts[testId];
  }

  /// Find available ports for all emulators
  static Future<EmulatorPorts> _findAvailablePorts() async {
    final firestorePort = await _findAvailablePort(_baseFirestorePort);
    final authPort = await _findAvailablePort(_baseAuthPort);
    final functionsPort = await _findAvailablePort(_baseFunctionsPort);
    final storagePort = await _findAvailablePort(_baseStoragePort);

    // Mark ports as allocated
    _allocatedPorts.addAll([
      firestorePort,
      authPort,
      functionsPort,
      storagePort,
    ]);

    return EmulatorPorts(
      firestorePort: firestorePort,
      authPort: authPort,
      functionsPort: functionsPort,
      storagePort: storagePort,
    );
  }

  /// Find an available port starting from the base port
  static Future<int> _findAvailablePort(int basePort) async {
    for (var i = 0; i < _portRangeSize; i++) {
      final port = basePort + i;

      // Skip if already allocated by us
      if (_allocatedPorts.contains(port)) {
        continue;
      }

      // Check if port is actually available
      if (await _isPortAvailable(port)) {
        return port;
      }
    }

    // If no port found in range, use a random high port
    final random = Random();
    for (var i = 0; i < 100; i++) {
      final port = 10000 + random.nextInt(50000);
      if (!_allocatedPorts.contains(port) && await _isPortAvailable(port)) {
        return port;
      }
    }

    throw Exception('Unable to find available port starting from $basePort');
  }

  /// Check if a port is available
  static Future<bool> _isPortAvailable(int port) async {
    try {
      final socket = await ServerSocket.bind(
        InternetAddress.loopbackIPv4,
        port,
      );
      await socket.close();
      return true;
    } on SocketException {
      return false;
    }
  }

  /// Clean up all allocated ports
  static Future<void> cleanup() async {
    final testIds = List<String>.from(_testPorts.keys);
    for (final testId in testIds) {
      await deallocatePortsForTest(testId);
    }
    _allocatedPorts.clear();
    debugPrint('Cleaned up all emulator port allocations');
  }

  /// Get diagnostic information
  static Map<String, dynamic> getDiagnostics() {
    return {
      'allocatedPorts': _allocatedPorts.toList()..sort(),
      'testPortAllocations': _testPorts.length,
      'testIds': _testPorts.keys.toList(),
    };
  }
}

/// Container for emulator port configuration
class EmulatorPorts {
  const EmulatorPorts({
    required this.firestorePort,
    required this.authPort,
    required this.functionsPort,
    required this.storagePort,
  });

  final int firestorePort;
  final int authPort;
  final int functionsPort;
  final int storagePort;

  @override
  String toString() {
    return 'EmulatorPorts(firestore: $firestorePort, auth: $authPort, '
        'functions: $functionsPort, storage: $storagePort)';
  }

  /// Convert to environment variables map
  Map<String, String> toEnvironmentVariables() {
    return {
      'FIRESTORE_EMULATOR_HOST': 'localhost:$firestorePort',
      'FIREBASE_AUTH_EMULATOR_HOST': 'localhost:$authPort',
      'FIREBASE_FUNCTIONS_EMULATOR_HOST': 'localhost:$functionsPort',
      'FIREBASE_STORAGE_EMULATOR_HOST': 'localhost:$storagePort',
    };
  }
}

/// Enhanced Firebase test helper with emulator port management
class EmulatorAwareFirebaseHelper {
  /// Initialize Firebase for testing with dedicated emulator ports
  static Future<EmulatorPorts> initializeWithDedicatedPorts(
    String testId, {
    bool useExistingPorts = false,
  }) async {
    EmulatorPorts ports;

    if (useExistingPorts) {
      // Try to use existing allocation
      ports =
          EmulatorPortManager.getPortsForTest(testId) ??
          await EmulatorPortManager.allocatePortsForTest(testId);
    } else {
      // Always allocate new ports
      ports = await EmulatorPortManager.allocatePortsForTest(testId);
    }

    debugPrint('Using emulator ports for test $testId: $ports');
    return ports;
  }

  /// Clean up emulator allocation for a test
  static Future<void> cleanupEmulatorPorts(String testId) async {
    await EmulatorPortManager.deallocatePortsForTest(testId);
  }

  /// Check if emulators are running on the allocated ports
  static Future<bool> areEmulatorsRunning(EmulatorPorts ports) async {
    final checks = await Future.wait([
      _checkEmulatorHealth('localhost', ports.firestorePort, 'Firestore'),
      _checkEmulatorHealth('localhost', ports.authPort, 'Auth'),
    ]);

    return checks.every((isRunning) => isRunning);
  }

  /// Check if a specific emulator is running
  static Future<bool> _checkEmulatorHealth(
    String host,
    int port,
    String emulatorName,
  ) async {
    try {
      final socket = await Socket.connect(host, port);
      await socket.close();
      debugPrint('$emulatorName emulator is running on $host:$port');
      return true;
    } on SocketException catch (e) {
      debugPrint('$emulatorName emulator not available on $host:$port: $e');
      return false;
    }
  }

  /// Wait for emulators to be ready
  static Future<void> waitForEmulatorsReady(
    EmulatorPorts ports, {
    Duration timeout = const Duration(seconds: 30),
    Duration checkInterval = const Duration(seconds: 1),
  }) async {
    final startTime = DateTime.now();

    while (DateTime.now().difference(startTime) < timeout) {
      if (await areEmulatorsRunning(ports)) {
        debugPrint('Emulators are ready on ports: $ports');
        return;
      }

      await Future<void>.delayed(checkInterval);
    }

    throw TimeoutException(
      'Emulators not ready after ${timeout.inSeconds}s',
      timeout,
    );
  }
}

/// Timeout exception for emulator operations
class TimeoutException implements Exception {
  TimeoutException(this.message, this.timeout);

  final String message;
  final Duration timeout;

  @override
  String toString() => 'TimeoutException: $message';
}
