import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/remote_config_data.dart';
import 'package:budapp/data/repositories/interfaces/repositories.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../helpers/mock_data_factory.dart';
import '../helpers/mock_providers.dart';

void main() {
  // Register fallback values for mocktail
  setUpAll(() {
    registerFallbackValue(MockDataFactory.createUserProfile());
    registerFallbackValue(MockDataFactory.createAccount());
    registerFallbackValue(<String, dynamic>{});
  });
  group('Repository Providers Tests', () {
    late ProviderContainer container;
    late MockUserRepository mockUserRepository;
    late MockAccountRepository mockAccountRepository;
    late MockRemoteConfigRepository mockRemoteConfigRepository;

    setUp(() {
      mockUserRepository = MockUserRepository();
      mockAccountRepository = MockAccountRepository();
      mockRemoteConfigRepository = MockRemoteConfigRepository();

      container = ProviderContainer(
        overrides: [
          // Mock Firebase providers to avoid initialization errors
          ...MockProviders.firebaseOverrides,
          // Mock repository providers
          userRepositoryProvider.overrideWithValue(mockUserRepository),
          accountRepositoryProvider.overrideWithValue(mockAccountRepository),
          remoteConfigRepositoryProvider.overrideWithValue(
            mockRemoteConfigRepository,
          ),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('Repository Provider Definitions', () {
      test('should provide correct repository implementations', () {
        final userRepo = container.read(userRepositoryProvider);
        final accountRepo = container.read(accountRepositoryProvider);
        final remoteConfigRepo = container.read(remoteConfigRepositoryProvider);

        expect(userRepo, isA<IUserRepository>());
        expect(accountRepo, isA<IAccountRepository>());
        expect(remoteConfigRepo, isA<IRemoteConfigRepository>());
      });

      test('should provide singleton instances', () {
        final userRepo1 = container.read(userRepositoryProvider);
        final userRepo2 = container.read(userRepositoryProvider);

        expect(identical(userRepo1, userRepo2), isTrue);
      });

      test('should provide all repository providers', () {
        // Test that all repository providers are accessible
        expect(userRepositoryProvider, isA<Provider<IUserRepository>>());
        expect(accountRepositoryProvider, isA<Provider<IAccountRepository>>());
        expect(
          transactionRepositoryProvider,
          isA<Provider<ITransactionRepository>>(),
        );
        expect(
          categoryRepositoryProvider,
          isA<Provider<ICategoryRepository>>(),
        );
        expect(budgetRepositoryProvider, isA<Provider<BudgetRepository>>());
        expect(
          remoteConfigRepositoryProvider,
          isA<Provider<IRemoteConfigRepository>>(),
        );
        expect(tagRepositoryProvider, isA<Provider<TagRepository>>());
        expect(goalRepositoryProvider, isA<Provider<IGoalRepository>>());
        expect(
          goalContributionRepositoryProvider,
          isA<Provider<IGoalContributionRepository>>(),
        );
      });
    });

    group('AsyncNotifier Provider Tests', () {
      test('should provide UserProfileNotifier family', () {
        expect(userProfileNotifierProvider, isA<UserProfileNotifierFamily>());
      });

      test('should provide UserAccountsNotifier family', () {
        expect(userAccountsNotifierProvider, isA<UserAccountsNotifierFamily>());
      });

      test('should provide RemoteConfigNotifier', () {
        expect(
          remoteConfigNotifierProvider,
          isA<
            AutoDisposeAsyncNotifierProvider<
              RemoteConfigNotifier,
              RemoteConfigData
            >
          >(),
        );
      });

      test('should create different instances for different user IDs', () {
        final provider1 = userProfileNotifierProvider('user1');
        final provider2 = userProfileNotifierProvider('user2');

        expect(provider1, isNot(equals(provider2)));
      });

      test('should create same instance for same user ID', () {
        final provider1 = userProfileNotifierProvider('user1');
        final provider2 = userProfileNotifierProvider('user1');

        expect(provider1, equals(provider2));
      });
    });

    group('Provider Integration Tests', () {
      test('should access repository providers without errors', () {
        // Test that we can access all repository providers
        expect(() => container.read(userRepositoryProvider), returnsNormally);
        expect(
          () => container.read(accountRepositoryProvider),
          returnsNormally,
        );
        expect(
          () => container.read(transactionRepositoryProvider),
          returnsNormally,
        );
        expect(
          () => container.read(categoryRepositoryProvider),
          returnsNormally,
        );
        expect(() => container.read(budgetRepositoryProvider), returnsNormally);
        expect(
          () => container.read(remoteConfigRepositoryProvider),
          returnsNormally,
        );
        expect(() => container.read(tagRepositoryProvider), returnsNormally);
        expect(() => container.read(goalRepositoryProvider), returnsNormally);
        expect(
          () => container.read(goalContributionRepositoryProvider),
          returnsNormally,
        );
      });

      test('should create AsyncNotifier providers without errors', () {
        // Test that we can create AsyncNotifier providers
        expect(() => userProfileNotifierProvider('test-id'), returnsNormally);
        expect(() => userAccountsNotifierProvider('test-id'), returnsNormally);
        expect(() => remoteConfigNotifierProvider, returnsNormally);
      });

      test('should handle provider dependencies correctly', () {
        // Test that providers can be read without throwing errors
        expect(() => container.read(userRepositoryProvider), returnsNormally);
        expect(
          () => container.read(accountRepositoryProvider),
          returnsNormally,
        );
        expect(
          () => container.read(remoteConfigRepositoryProvider),
          returnsNormally,
        );
      });

      test('should provide correct repository types', () {
        final userRepo = container.read(userRepositoryProvider);
        final accountRepo = container.read(accountRepositoryProvider);
        final remoteConfigRepo = container.read(remoteConfigRepositoryProvider);

        // Verify the repositories are the mocked instances
        expect(userRepo, equals(mockUserRepository));
        expect(accountRepo, equals(mockAccountRepository));
        expect(remoteConfigRepo, equals(mockRemoteConfigRepository));
      });

      test('should handle AsyncNotifier provider creation', () {
        // Test that AsyncNotifier providers can be created
        final userProfileProvider = userProfileNotifierProvider('test-user');
        final userAccountsProvider = userAccountsNotifierProvider('test-user');
        final remoteConfigProvider = remoteConfigNotifierProvider;

        expect(userProfileProvider, isA<UserProfileNotifierProvider>());
        expect(userAccountsProvider, isA<UserAccountsNotifierProvider>());
        expect(
          remoteConfigProvider,
          isA<
            AutoDisposeAsyncNotifierProvider<
              RemoteConfigNotifier,
              RemoteConfigData
            >
          >(),
        );
      });

      test('should maintain provider consistency', () {
        // Test that the same provider instance is returned for the same parameters
        final provider1 = userProfileNotifierProvider('test-user');
        final provider2 = userProfileNotifierProvider('test-user');

        expect(provider1, equals(provider2));
        expect(provider1.hashCode, equals(provider2.hashCode));
      });

      test('should differentiate providers with different parameters', () {
        // Test that different parameters create different providers
        final provider1 = userProfileNotifierProvider('user1');
        final provider2 = userProfileNotifierProvider('user2');

        expect(provider1, isNot(equals(provider2)));
        expect(provider1.hashCode, isNot(equals(provider2.hashCode)));
      });

      test('should handle provider overrides correctly', () {
        // Test that provider overrides work as expected
        final userRepo = container.read(userRepositoryProvider);
        final accountRepo = container.read(accountRepositoryProvider);

        // Should return the mocked instances
        expect(userRepo, same(mockUserRepository));
        expect(accountRepo, same(mockAccountRepository));
      });

      test('should support provider families', () {
        // Test that provider families work correctly
        final provider1 = userProfileNotifierProvider('user1');
        final provider2 = userProfileNotifierProvider('user2');
        final provider3 = userProfileNotifierProvider(
          'user1',
        ); // Same as provider1

        expect(provider1, isNot(same(provider2)));
        expect(provider1, equals(provider3));
        expect(provider1.userId, equals('user1'));
        expect(provider2.userId, equals('user2'));
      });

      test('should handle repository provider dependencies', () {
        // Test that repository providers have correct dependencies
        expect(() => container.read(userRepositoryProvider), returnsNormally);
        expect(
          () => container.read(transactionRepositoryProvider),
          returnsNormally,
        );
        expect(
          () => container.read(categoryRepositoryProvider),
          returnsNormally,
        );
        expect(() => container.read(budgetRepositoryProvider), returnsNormally);
        expect(() => container.read(goalRepositoryProvider), returnsNormally);
        expect(
          () => container.read(goalContributionRepositoryProvider),
          returnsNormally,
        );
        expect(() => container.read(tagRepositoryProvider), returnsNormally);
      });

      test('should provide correct provider types', () {
        // Test that all providers have correct types
        expect(userRepositoryProvider, isA<Provider<IUserRepository>>());
        expect(accountRepositoryProvider, isA<Provider<IAccountRepository>>());
        expect(
          transactionRepositoryProvider,
          isA<Provider<ITransactionRepository>>(),
        );
        expect(
          categoryRepositoryProvider,
          isA<Provider<ICategoryRepository>>(),
        );
        expect(
          remoteConfigRepositoryProvider,
          isA<Provider<IRemoteConfigRepository>>(),
        );
        expect(goalRepositoryProvider, isA<Provider<IGoalRepository>>());
        expect(
          goalContributionRepositoryProvider,
          isA<Provider<IGoalContributionRepository>>(),
        );

        // AsyncNotifier providers
        expect(userProfileNotifierProvider, isA<UserProfileNotifierFamily>());
        expect(userAccountsNotifierProvider, isA<UserAccountsNotifierFamily>());
        expect(
          remoteConfigNotifierProvider,
          isA<
            AutoDisposeAsyncNotifierProvider<
              RemoteConfigNotifier,
              RemoteConfigData
            >
          >(),
        );
      });
    });

    group('Generated Code Tests', () {
      group('UserProfileNotifierProvider Tests', () {
        test('should create provider with correct userId', () {
          const userId = 'test-user-123';
          final provider = UserProfileNotifierProvider(userId);

          expect(provider.userId, equals(userId));
          expect(provider, isA<UserProfileNotifierProvider>());
        });

        test('should implement equality correctly', () {
          const userId1 = 'user1';
          const userId2 = 'user2';

          final provider1a = UserProfileNotifierProvider(userId1);
          final provider1b = UserProfileNotifierProvider(userId1);
          final provider2 = UserProfileNotifierProvider(userId2);

          // Same userId should be equal
          expect(provider1a, equals(provider1b));
          expect(provider1a.hashCode, equals(provider1b.hashCode));

          // Different userId should not be equal
          expect(provider1a, isNot(equals(provider2)));
          expect(provider1a.hashCode, isNot(equals(provider2.hashCode)));
        });

        test('should create element correctly', () {
          const userId = 'test-user';
          final provider = UserProfileNotifierProvider(userId);

          final element = provider.createElement();
          expect(element, isNotNull);
        });

        test('should handle overrideWith correctly', () {
          const userId = 'test-user';
          final provider = UserProfileNotifierProvider(userId);

          final mockNotifier = MockUserProfileNotifier();
          final override = provider.overrideWith(() => mockNotifier);

          expect(override, isNotNull);
        });
      });

      group('UserAccountsNotifierProvider Tests', () {
        test('should create provider with correct userId', () {
          const userId = 'test-user-456';
          final provider = UserAccountsNotifierProvider(userId);

          expect(provider.userId, equals(userId));
          expect(provider, isA<UserAccountsNotifierProvider>());
        });

        test('should implement equality correctly', () {
          const userId1 = 'user1';
          const userId2 = 'user2';

          final provider1a = UserAccountsNotifierProvider(userId1);
          final provider1b = UserAccountsNotifierProvider(userId1);
          final provider2 = UserAccountsNotifierProvider(userId2);

          // Same userId should be equal
          expect(provider1a, equals(provider1b));
          expect(provider1a.hashCode, equals(provider1b.hashCode));

          // Different userId should not be equal
          expect(provider1a, isNot(equals(provider2)));
          expect(provider1a.hashCode, isNot(equals(provider2.hashCode)));
        });

        test('should create element correctly', () {
          const userId = 'test-user';
          final provider = UserAccountsNotifierProvider(userId);

          final element = provider.createElement();
          expect(element, isNotNull);
        });

        test('should handle overrideWith correctly', () {
          const userId = 'test-user';
          final provider = UserAccountsNotifierProvider(userId);

          final mockNotifier = MockUserAccountsNotifier();
          final override = provider.overrideWith(() => mockNotifier);

          expect(override, isNotNull);
        });
      });

      group('Provider Family Tests', () {
        test('should handle UserProfileNotifierFamily methods', () {
          const family = UserProfileNotifierFamily();

          // Test call method
          const userId = 'test-user';
          final provider = family.call(userId);
          expect(provider, isA<UserProfileNotifierProvider>());
          expect(provider.userId, equals(userId));

          // Test getProviderOverride
          final override = family.getProviderOverride(provider);
          expect(override, isA<UserProfileNotifierProvider>());
          expect(override.userId, equals(userId));

          // Test dependencies
          expect(family.dependencies, isNull);
          expect(family.allTransitiveDependencies, isNull);

          // Test name
          expect(family.name, equals('userProfileNotifierProvider'));
        });

        test('should handle UserAccountsNotifierFamily methods', () {
          const family = UserAccountsNotifierFamily();

          // Test call method
          const userId = 'test-user';
          final provider = family.call(userId);
          expect(provider, isA<UserAccountsNotifierProvider>());
          expect(provider.userId, equals(userId));

          // Test getProviderOverride
          final override = family.getProviderOverride(provider);
          expect(override, isA<UserAccountsNotifierProvider>());
          expect(override.userId, equals(userId));

          // Test dependencies
          expect(family.dependencies, isNull);
          expect(family.allTransitiveDependencies, isNull);

          // Test name
          expect(family.name, equals('userAccountsNotifierProvider'));
        });
      });

      group('AsyncNotifier Functionality Tests', () {
        test('should handle UserProfileNotifier operations', () async {
          final testContainer = ProviderContainer(
            overrides: [
              ...MockProviders.firebaseOverrides,
              userRepositoryProvider.overrideWithValue(MockUserRepository()),
            ],
          );

          // Test provider creation and access
          final provider = userProfileNotifierProvider('test-user');
          expect(provider, isA<UserProfileNotifierProvider>());
          expect(provider.userId, equals('test-user'));

          testContainer.dispose();
        });

        test('should handle UserAccountsNotifier operations', () async {
          final testContainer = ProviderContainer(
            overrides: [
              ...MockProviders.firebaseOverrides,
              accountRepositoryProvider.overrideWithValue(
                MockAccountRepository(),
              ),
            ],
          );

          // Test provider creation and access
          final provider = userAccountsNotifierProvider('test-user');
          expect(provider, isA<UserAccountsNotifierProvider>());
          expect(provider.userId, equals('test-user'));

          testContainer.dispose();
        });

        test('should handle RemoteConfigNotifier operations', () async {
          final testContainer = ProviderContainer(
            overrides: [
              ...MockProviders.firebaseOverrides,
              remoteConfigRepositoryProvider.overrideWithValue(
                MockRemoteConfigRepository(),
              ),
            ],
          );

          // Test provider access
          expect(remoteConfigNotifierProvider, isNotNull);

          testContainer.dispose();
        });

        test('should handle provider element access', () {
          const userId = 'test-user';

          // Test UserProfileNotifierProvider element
          final userProfileProvider = UserProfileNotifierProvider(userId);
          final userProfileElement = userProfileProvider.createElement();
          expect(userProfileElement, isNotNull);

          // Test UserAccountsNotifierProvider element
          final userAccountsProvider = UserAccountsNotifierProvider(userId);
          final userAccountsElement = userAccountsProvider.createElement();
          expect(userAccountsElement, isNotNull);
        });

        test('should handle provider hash codes correctly', () {
          const userId1 = 'user1';
          const userId2 = 'user2';

          // Test UserProfileNotifierProvider hash codes
          final userProfile1a = UserProfileNotifierProvider(userId1);
          final userProfile1b = UserProfileNotifierProvider(userId1);
          final userProfile2 = UserProfileNotifierProvider(userId2);

          expect(userProfile1a.hashCode, equals(userProfile1b.hashCode));
          expect(userProfile1a.hashCode, isNot(equals(userProfile2.hashCode)));

          // Test UserAccountsNotifierProvider hash codes
          final userAccounts1a = UserAccountsNotifierProvider(userId1);
          final userAccounts1b = UserAccountsNotifierProvider(userId1);
          final userAccounts2 = UserAccountsNotifierProvider(userId2);

          expect(userAccounts1a.hashCode, equals(userAccounts1b.hashCode));
          expect(
            userAccounts1a.hashCode,
            isNot(equals(userAccounts2.hashCode)),
          );
        });

        test('should handle provider runtime type correctly', () {
          const userId = 'test-user';

          final userProfileProvider = UserProfileNotifierProvider(userId);
          final userAccountsProvider = UserAccountsNotifierProvider(userId);

          expect(
            userProfileProvider.runtimeType,
            equals(UserProfileNotifierProvider),
          );
          expect(
            userAccountsProvider.runtimeType,
            equals(UserAccountsNotifierProvider),
          );

          // Different provider types should have different runtime types
          expect(
            userProfileProvider.runtimeType,
            isNot(equals(userAccountsProvider.runtimeType)),
          );
        });
      });
    });

    group('AsyncNotifier Business Logic Tests', () {
      group('UserProfileNotifier Business Logic', () {
        late ProviderContainer testContainer;
        late MockUserRepository mockUserRepository;

        setUp(() {
          mockUserRepository = MockUserRepository();
          testContainer = ProviderContainer(
            overrides: [
              ...MockProviders.firebaseOverrides,
              userRepositoryProvider.overrideWithValue(mockUserRepository),
            ],
          );
        });

        tearDown(() {
          testContainer.dispose();
        });

        test('build should load user profile successfully', () async {
          // Arrange
          const userId = 'test-user';
          final testProfile = MockDataFactory.createUserProfile(uid: userId);
          when(
            () => mockUserRepository.getUserById(any()),
          ).thenAnswer((_) async => testProfile);

          // Act
          final result = await testContainer.read(
            userProfileNotifierProvider(userId).future,
          );

          // Assert
          expect(result, equals(testProfile));
          verify(() => mockUserRepository.getUserById(userId)).called(1);
        });

        test('build should handle null user profile', () async {
          // Arrange
          const userId = 'test-user';
          when(
            () => mockUserRepository.getUserById(any()),
          ).thenAnswer((_) async => null);

          // Act
          final result = await testContainer.read(
            userProfileNotifierProvider(userId).future,
          );

          // Assert
          expect(result, isNull);
          verify(() => mockUserRepository.getUserById(userId)).called(1);
        });

        test('build should handle repository errors', () async {
          // Arrange
          const userId = 'test-user';
          final testError = Exception('Repository error');
          when(
            () => mockUserRepository.getUserById(any()),
          ).thenThrow(testError);

          // Act & Assert
          expect(
            () =>
                testContainer.read(userProfileNotifierProvider(userId).future),
            throwsA(testError),
          );
        });

        test(
          'createOrUpdateProfile should update profile successfully',
          () async {
            // Arrange
            final testProfile = MockDataFactory.createUserProfile();
            when(
              () => mockUserRepository.update(any(), any()),
            ).thenAnswer((_) async {});

            // Act
            final notifier = testContainer.read(
              userProfileNotifierProvider(testProfile.uid).notifier,
            );
            await notifier.createOrUpdateProfile(testProfile);

            // Assert
            verify(
              () => mockUserRepository.update(testProfile.uid, testProfile),
            ).called(1);
            expect(
              testContainer
                  .read(userProfileNotifierProvider(testProfile.uid))
                  .value,
              equals(testProfile),
            );
          },
        );

        test('createOrUpdateProfile should handle errors gracefully', () async {
          // Arrange
          final testProfile = MockDataFactory.createUserProfile();
          final testError = Exception('Update failed');
          when(
            () => mockUserRepository.update(any(), any()),
          ).thenThrow(testError);

          // Act
          final notifier = testContainer.read(
            userProfileNotifierProvider(testProfile.uid).notifier,
          );
          await notifier.createOrUpdateProfile(testProfile);

          // Assert
          final state = testContainer.read(
            userProfileNotifierProvider(testProfile.uid),
          );
          expect(state.hasError, isTrue);
          expect(state.error, equals(testError));
        });

        test(
          'updatePreferences should update preferences and refresh profile',
          () async {
            // Arrange
            const userId = 'test-user';
            final preferences = {'theme': 'dark', 'currency': 'USD'};
            final updatedProfile = MockDataFactory.createUserProfile(
              uid: userId,
              preferences: preferences,
            );

            when(
              () => mockUserRepository.updatePreferences(any(), any()),
            ).thenAnswer((_) async {});
            when(
              () => mockUserRepository.getUserById(any()),
            ).thenAnswer((_) async => updatedProfile);

            // Act
            final notifier = testContainer.read(
              userProfileNotifierProvider(userId).notifier,
            );
            await notifier.updatePreferences(userId, preferences);

            // Assert
            verify(
              () => mockUserRepository.updatePreferences(userId, preferences),
            ).called(1);
            verify(() => mockUserRepository.getUserById(userId)).called(2);
            expect(
              testContainer.read(userProfileNotifierProvider(userId)).value,
              equals(updatedProfile),
            );
          },
        );

        test('updatePreferences should handle errors gracefully', () async {
          // Arrange
          const userId = 'test-user';
          final preferences = {'theme': 'dark'};
          final testError = Exception('Preferences update failed');
          when(
            () => mockUserRepository.updatePreferences(any(), any()),
          ).thenThrow(testError);

          // Act
          final notifier = testContainer.read(
            userProfileNotifierProvider(userId).notifier,
          );
          await notifier.updatePreferences(userId, preferences);

          // Assert
          final state = testContainer.read(userProfileNotifierProvider(userId));
          expect(state.hasError, isTrue);
          expect(state.error, equals(testError));
        });

        test('updatePreferences should handle null returned profile', () async {
          // Arrange
          const userId = 'test-user';
          final preferences = {'theme': 'dark'};
          when(
            () => mockUserRepository.updatePreferences(any(), any()),
          ).thenAnswer((_) async {});
          when(
            () => mockUserRepository.getUserById(any()),
          ).thenAnswer((_) async => null);

          // Act
          final notifier = testContainer.read(
            userProfileNotifierProvider(userId).notifier,
          );
          await notifier.updatePreferences(userId, preferences);

          // Assert
          final state = testContainer.read(userProfileNotifierProvider(userId));
          expect(state.value, isNull);
          verify(
            () => mockUserRepository.updatePreferences(userId, preferences),
          ).called(1);
          verify(() => mockUserRepository.getUserById(userId)).called(2);
        });
      });

      group('UserAccountsNotifier Business Logic', () {
        late ProviderContainer testContainer;
        late MockAccountRepository mockAccountRepository;

        setUp(() {
          mockAccountRepository = MockAccountRepository();
          testContainer = ProviderContainer(
            overrides: [
              ...MockProviders.firebaseOverrides,
              accountRepositoryProvider.overrideWithValue(
                mockAccountRepository,
              ),
            ],
          );
        });

        tearDown(() {
          testContainer.dispose();
        });

        test('build should load user accounts successfully', () async {
          // Arrange
          const userId = 'test-user';
          final testAccounts = [
            MockDataFactory.createAccount(userId: userId),
            MockDataFactory.createAccount(userId: userId),
          ];
          when(
            () => mockAccountRepository.getAccountsByUserId(any()),
          ).thenAnswer((_) async => testAccounts);

          // Act
          final result = await testContainer.read(
            userAccountsNotifierProvider(userId).future,
          );

          // Assert
          expect(result, equals(testAccounts));
          verify(
            () => mockAccountRepository.getAccountsByUserId(userId),
          ).called(1);
        });

        test('build should handle empty accounts list', () async {
          // Arrange
          const userId = 'test-user';
          when(
            () => mockAccountRepository.getAccountsByUserId(any()),
          ).thenAnswer((_) async => <Account>[]);

          // Act
          final result = await testContainer.read(
            userAccountsNotifierProvider(userId).future,
          );

          // Assert
          expect(result, isEmpty);
          verify(
            () => mockAccountRepository.getAccountsByUserId(userId),
          ).called(1);
        });

        test('build should handle repository errors', () async {
          // Arrange
          const userId = 'test-user';
          final testError = Exception('Repository error');
          when(
            () => mockAccountRepository.getAccountsByUserId(any()),
          ).thenThrow(testError);

          // Act & Assert
          expect(
            () =>
                testContainer.read(userAccountsNotifierProvider(userId).future),
            throwsA(testError),
          );
        });

        test('createAccount should create account successfully', () async {
          // Arrange
          final testAccount = MockDataFactory.createAccount();
          final updatedAccounts = [testAccount];
          when(
            () => mockAccountRepository.create(any()),
          ).thenAnswer((_) async => testAccount.id);
          when(
            () => mockAccountRepository.getAccountsByUserId(any()),
          ).thenAnswer((_) async => updatedAccounts);

          // Act
          final notifier = testContainer.read(
            userAccountsNotifierProvider(testAccount.userId).notifier,
          );
          await notifier.createAccount(testAccount);

          // Assert
          verify(() => mockAccountRepository.create(testAccount)).called(1);
          verify(
            () => mockAccountRepository.getAccountsByUserId(testAccount.userId),
          ).called(2); // Once in build, once in createAccount
          expect(
            testContainer
                .read(userAccountsNotifierProvider(testAccount.userId))
                .value,
            equals(updatedAccounts),
          );
        });

        test('createAccount should handle errors gracefully', () async {
          // Arrange
          final testAccount = MockDataFactory.createAccount();
          final testError = Exception('Create failed');
          when(() => mockAccountRepository.create(any())).thenThrow(testError);

          // Act
          final notifier = testContainer.read(
            userAccountsNotifierProvider(testAccount.userId).notifier,
          );
          await notifier.createAccount(testAccount);

          // Assert
          final state = testContainer.read(
            userAccountsNotifierProvider(testAccount.userId),
          );
          expect(state.hasError, isTrue);
          expect(state.error, equals(testError));
        });

        test('updateAccount should update account successfully', () async {
          // Arrange
          final testAccount = MockDataFactory.createAccount();
          final updatedAccounts = [testAccount];
          when(
            () => mockAccountRepository.update(any(), any()),
          ).thenAnswer((_) async {});
          when(
            () => mockAccountRepository.getAccountsByUserId(any()),
          ).thenAnswer((_) async => updatedAccounts);

          // Act
          final notifier = testContainer.read(
            userAccountsNotifierProvider(testAccount.userId).notifier,
          );
          await notifier.updateAccount(testAccount.id, testAccount);

          // Assert
          verify(
            () => mockAccountRepository.update(testAccount.id, testAccount),
          ).called(1);
          verify(
            () => mockAccountRepository.getAccountsByUserId(testAccount.userId),
          ).called(2);
          expect(
            testContainer
                .read(userAccountsNotifierProvider(testAccount.userId))
                .value,
            equals(updatedAccounts),
          );
        });

        test('updateAccount should handle errors gracefully', () async {
          // Arrange
          final testAccount = MockDataFactory.createAccount();
          final testError = Exception('Update failed');
          when(
            () => mockAccountRepository.update(any(), any()),
          ).thenThrow(testError);

          // Act
          final notifier = testContainer.read(
            userAccountsNotifierProvider(testAccount.userId).notifier,
          );
          await notifier.updateAccount(testAccount.id, testAccount);

          // Assert
          final state = testContainer.read(
            userAccountsNotifierProvider(testAccount.userId),
          );
          expect(state.hasError, isTrue);
          expect(state.error, equals(testError));
        });

        test('deleteAccount should delete account successfully', () async {
          // Arrange
          const userId = 'test-user';
          const accountId = 'test-account';
          final updatedAccounts = <Account>[];

          // Mock the dynamic cast and method call
          final mockAccountRepositoryDynamic = mockAccountRepository;
          when(
            () => (mockAccountRepositoryDynamic as dynamic)
                .deleteAccountForUser(any<String>(), any<String>()),
          ).thenAnswer((_) async {});
          when(
            () => mockAccountRepository.getAccountsByUserId(any()),
          ).thenAnswer((_) async => updatedAccounts);

          // Act
          final notifier = testContainer.read(
            userAccountsNotifierProvider(userId).notifier,
          );
          await notifier.deleteAccount(userId, accountId);

          // Assert
          verify(
            () => (mockAccountRepositoryDynamic as dynamic)
                .deleteAccountForUser(userId, accountId),
          ).called(1);
          verify(
            () => mockAccountRepository.getAccountsByUserId(userId),
          ).called(2);
          expect(
            testContainer.read(userAccountsNotifierProvider(userId)).value,
            equals(updatedAccounts),
          );
        });

        test('deleteAccount should handle errors gracefully', () async {
          // Arrange
          const userId = 'test-user';
          const accountId = 'test-account';
          final testError = Exception('Delete failed');

          final mockAccountRepositoryDynamic = mockAccountRepository;
          when(
            () => (mockAccountRepositoryDynamic as dynamic)
                .deleteAccountForUser(any<String>(), any<String>()),
          ).thenThrow(testError);

          // Act
          final notifier = testContainer.read(
            userAccountsNotifierProvider(userId).notifier,
          );
          await notifier.deleteAccount(userId, accountId);

          // Assert
          final state = testContainer.read(
            userAccountsNotifierProvider(userId),
          );
          expect(state.hasError, isTrue);
          expect(state.error, equals(testError));
        });
      });

      group('RemoteConfigNotifier Business Logic', () {
        late ProviderContainer testContainer;
        late MockRemoteConfigRepository mockRemoteConfigRepository;

        setUp(() {
          mockRemoteConfigRepository = MockRemoteConfigRepository();
          testContainer = ProviderContainer(
            overrides: [
              ...MockProviders.firebaseOverrides,
              remoteConfigRepositoryProvider.overrideWithValue(
                mockRemoteConfigRepository,
              ),
            ],
          );
        });

        tearDown(() {
          testContainer.dispose();
        });

        test('build should initialize and load config successfully', () async {
          // Arrange
          final testConfig = RemoteConfigData.defaults();
          when(
            () => mockRemoteConfigRepository.initialize(),
          ).thenAnswer((_) async {});
          when(
            () => mockRemoteConfigRepository.fetchAndActivate(),
          ).thenAnswer((_) async => true);
          when(
            () => mockRemoteConfigRepository.getCurrentConfig(),
          ).thenReturn(testConfig);

          // Act
          final result = await testContainer.read(
            remoteConfigNotifierProvider.future,
          );

          // Assert
          expect(result, equals(testConfig));
          verify(() => mockRemoteConfigRepository.initialize()).called(1);
          verify(() => mockRemoteConfigRepository.fetchAndActivate()).called(1);
          verify(() => mockRemoteConfigRepository.getCurrentConfig()).called(1);
        });

        test(
          'build should handle fetchAndActivate failure gracefully',
          () async {
            // Arrange
            final testConfig = RemoteConfigData.defaults();
            when(
              () => mockRemoteConfigRepository.initialize(),
            ).thenAnswer((_) async {});
            when(
              () => mockRemoteConfigRepository.fetchAndActivate(),
            ).thenThrow(Exception('Fetch failed'));
            when(
              () => mockRemoteConfigRepository.getCurrentConfig(),
            ).thenReturn(testConfig);

            // Act
            final result = await testContainer.read(
              remoteConfigNotifierProvider.future,
            );

            // Assert
            expect(result, equals(testConfig));
            verify(() => mockRemoteConfigRepository.initialize()).called(1);
            verify(
              () => mockRemoteConfigRepository.fetchAndActivate(),
            ).called(1);
            verify(
              () => mockRemoteConfigRepository.getCurrentConfig(),
            ).called(1);
          },
        );

        test('build should handle initialization failure', () async {
          // Arrange
          final testError = Exception('Initialization failed');
          when(
            () => mockRemoteConfigRepository.initialize(),
          ).thenThrow(testError);

          // Act & Assert
          expect(
            () => testContainer.read(remoteConfigNotifierProvider.future),
            throwsA(testError),
          );
        });

        test('refresh should force refresh config successfully', () async {
          // Arrange
          final testConfig = RemoteConfigData.defaults();
          when(
            () => mockRemoteConfigRepository.forceRefresh(),
          ).thenAnswer((_) async {});
          when(
            () => mockRemoteConfigRepository.getCurrentConfig(),
          ).thenReturn(testConfig);

          // Act
          final notifier = testContainer.read(
            remoteConfigNotifierProvider.notifier,
          );
          await notifier.refresh();

          // Assert
          verify(() => mockRemoteConfigRepository.forceRefresh()).called(1);
          verify(() => mockRemoteConfigRepository.getCurrentConfig()).called(1);
          expect(
            testContainer.read(remoteConfigNotifierProvider).value,
            equals(testConfig),
          );
        });

        test('refresh should handle errors gracefully', () async {
          // Arrange
          final testError = Exception('Refresh failed');
          when(
            () => mockRemoteConfigRepository.forceRefresh(),
          ).thenThrow(testError);

          // Act
          final notifier = testContainer.read(
            remoteConfigNotifierProvider.notifier,
          );
          await notifier.refresh();

          // Assert
          final state = testContainer.read(remoteConfigNotifierProvider);
          expect(state.hasError, isTrue);
          expect(state.error, equals(testError));
        });

        test(
          'getPredefinedCategories should return categories from config',
          () async {
            // Arrange
            final testCategories = PredefinedCategories.defaults();
            final testConfig = RemoteConfigData.defaults().copyWith(
              categories: testCategories,
            );

            // Set up the notifier with test config
            when(
              () => mockRemoteConfigRepository.initialize(),
            ).thenAnswer((_) async {});
            when(
              () => mockRemoteConfigRepository.fetchAndActivate(),
            ).thenAnswer((_) async => true);
            when(
              () => mockRemoteConfigRepository.getCurrentConfig(),
            ).thenReturn(testConfig);

            // Load the config first
            await testContainer.read(remoteConfigNotifierProvider.future);

            // Act
            final notifier = testContainer.read(
              remoteConfigNotifierProvider.notifier,
            );
            final result = notifier.getPredefinedCategories();

            // Assert
            expect(result, equals(testCategories));
          },
        );

        test(
          'getPredefinedCategories should return defaults when config is null',
          () {
            // Arrange - no config loaded
            final notifier = testContainer.read(
              remoteConfigNotifierProvider.notifier,
            );

            // Act
            final result = notifier.getPredefinedCategories();

            // Assert
            expect(result, equals(PredefinedCategories.defaults()));
          },
        );

        test('getPremiumLimits should return limits from config', () async {
          // Arrange
          final testLimits = PremiumLimits.defaults();
          final testConfig = RemoteConfigData.defaults().copyWith(
            premiumLimits: testLimits,
          );

          // Set up the notifier with test config
          when(
            () => mockRemoteConfigRepository.initialize(),
          ).thenAnswer((_) async {});
          when(
            () => mockRemoteConfigRepository.fetchAndActivate(),
          ).thenAnswer((_) async => true);
          when(
            () => mockRemoteConfigRepository.getCurrentConfig(),
          ).thenReturn(testConfig);

          // Load the config first
          await testContainer.read(remoteConfigNotifierProvider.future);

          // Act
          final notifier = testContainer.read(
            remoteConfigNotifierProvider.notifier,
          );
          final result = notifier.getPremiumLimits();

          // Assert
          expect(result, equals(testLimits));
        });

        test('getPremiumLimits should return defaults when config is null', () {
          // Arrange - no config loaded
          final notifier = testContainer.read(
            remoteConfigNotifierProvider.notifier,
          );

          // Act
          final result = notifier.getPremiumLimits();

          // Assert
          expect(result, equals(PremiumLimits.defaults()));
        });

        test(
          'isFeatureEnabled should return feature status from config',
          () async {
            // Arrange
            const featureKey = 'test_feature';
            final testConfig = RemoteConfigData.defaults().copyWith(
              enableFeatureX: true,
            );

            // Set up the notifier with test config
            when(
              () => mockRemoteConfigRepository.initialize(),
            ).thenAnswer((_) async {});
            when(
              () => mockRemoteConfigRepository.fetchAndActivate(),
            ).thenAnswer((_) async => true);
            when(
              () => mockRemoteConfigRepository.getCurrentConfig(),
            ).thenReturn(testConfig);
            when(
              () => mockRemoteConfigRepository.isFeatureEnabled(any()),
            ).thenReturn(true);

            // Load the config first
            await testContainer.read(remoteConfigNotifierProvider.future);

            // Act
            final notifier = testContainer.read(
              remoteConfigNotifierProvider.notifier,
            );
            final result = notifier.isFeatureEnabled(featureKey);

            // Assert
            expect(result, isTrue);
            verify(
              () => mockRemoteConfigRepository.isFeatureEnabled(featureKey),
            ).called(1);
          },
        );

        test('isFeatureEnabled should return false when config is null', () {
          // Arrange - no config loaded
          const featureKey = 'test_feature';
          when(
            () => mockRemoteConfigRepository.isFeatureEnabled(any()),
          ).thenReturn(false);

          final notifier = testContainer.read(
            remoteConfigNotifierProvider.notifier,
          );

          // Act
          final result = notifier.isFeatureEnabled(featureKey);

          // Assert
          expect(result, isFalse);
          verify(
            () => mockRemoteConfigRepository.isFeatureEnabled(featureKey),
          ).called(1);
        });
      });
    });
  });
}

// Mock classes for testing
class MockUserProfileNotifier extends Mock implements UserProfileNotifier {}

class MockUserAccountsNotifier extends Mock implements UserAccountsNotifier {}
