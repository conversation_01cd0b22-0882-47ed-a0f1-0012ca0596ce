import 'package:budapp/providers/logging_providers.dart';
import 'package:budapp/services/logging_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockLoggingService extends Mock implements LoggingService {}

void main() {
  group('Logging Providers Tests', () {
    late ProviderContainer container;
    late MockLoggingService mockLoggingService;

    setUp(() {
      mockLoggingService = MockLoggingService();
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    group('Provider Definitions', () {
      group('loggingServiceProvider', () {
        test('should provide LoggingService instance', () {
          final loggingService = container.read(loggingServiceProvider);

          expect(loggingService, isA<LoggingService>());
          expect(loggingService, isNotNull);
        });

        test(
          'should return same instance on multiple reads (singleton behavior)',
          () {
            final service1 = container.read(loggingServiceProvider);
            final service2 = container.read(loggingServiceProvider);

            expect(identical(service1, service2), isTrue);
          },
        );

        test('should be accessible without throwing errors', () {
          expect(() => container.read(loggingServiceProvider), returnsNormally);
        });

        test('should have correct provider type', () {
          expect(loggingServiceProvider, isA<Provider<LoggingService>>());
        });
      });

      group('loggerProvider', () {
        test('should provide Logger instance', () {
          final logger = container.read(loggerProvider);

          expect(logger, isA<Logger>());
          expect(logger, isNotNull);
        });

        test(
          'should return same instance on multiple reads (singleton behavior)',
          () {
            final logger1 = container.read(loggerProvider);
            final logger2 = container.read(loggerProvider);

            expect(identical(logger1, logger2), isTrue);
          },
        );

        test('should depend on loggingServiceProvider', () {
          // This test verifies the dependency injection chain
          final logger = container.read(loggerProvider);
          final loggingService = container.read(loggingServiceProvider);

          expect(logger, isNotNull);
          expect(loggingService, isNotNull);
        });

        test('should be accessible without throwing errors', () {
          expect(() => container.read(loggerProvider), returnsNormally);
        });

        test('should have correct provider type', () {
          expect(loggerProvider, isA<Provider<Logger>>());
        });
      });
    });

    group('Provider Integration', () {
      test('should work with ProviderContainer', () {
        final testContainer = ProviderContainer();

        expect(
          () => testContainer.read(loggingServiceProvider),
          returnsNormally,
        );
        expect(() => testContainer.read(loggerProvider), returnsNormally);

        testContainer.dispose();
      });

      test('should support provider overrides', () {
        final overrideContainer = ProviderContainer(
          overrides: [
            loggingServiceProvider.overrideWithValue(mockLoggingService),
          ],
        );

        final loggingService = overrideContainer.read(loggingServiceProvider);
        expect(loggingService, equals(mockLoggingService));

        overrideContainer.dispose();
      });

      test('should support nested provider overrides', () {
        final overrideContainer = ProviderContainer(
          overrides: [
            loggingServiceProvider.overrideWithValue(mockLoggingService),
          ],
        );

        final logger = overrideContainer.read(loggerProvider);
        expect(logger, isA<Logger>());

        overrideContainer.dispose();
      });

      test('should handle container disposal correctly', () {
        final testContainer = ProviderContainer();

        // Access providers
        testContainer.read(loggingServiceProvider);
        testContainer.read(loggerProvider);

        // Should dispose without errors
        expect(testContainer.dispose, returnsNormally);
      });
    });

    group('Logger Class', () {
      late Logger logger;
      late ProviderContainer testContainer;

      setUp(() {
        testContainer = ProviderContainer(
          overrides: [
            loggingServiceProvider.overrideWithValue(mockLoggingService),
          ],
        );
        logger = testContainer.read(loggerProvider);
      });

      tearDown(() {
        testContainer.dispose();
      });

      group('Constructor', () {
        test('should create Logger with LoggingService parameter', () {
          final customLogger = Logger(mockLoggingService);
          expect(customLogger, isA<Logger>());
        });

        test('should store LoggingService reference correctly', () {
          // Verify constructor accepts and stores the service
          final customLogger = Logger(mockLoggingService);
          expect(customLogger, isNotNull);
        });
      });

      group('Logging Methods - Basic Functionality', () {
        test('trace method should exist and be callable', () {
          expect(() => logger.trace('test message'), returnsNormally);
        });

        test('debug method should exist and be callable', () {
          expect(() => logger.debug('test message'), returnsNormally);
        });

        test('info method should exist and be callable', () {
          expect(() => logger.info('test message'), returnsNormally);
        });

        test('warning method should exist and be callable', () {
          expect(() => logger.warning('test message'), returnsNormally);
        });

        test('error method should exist and be callable', () {
          expect(() => logger.error('test message'), returnsNormally);
        });

        test('fatal method should exist and be callable', () {
          expect(() => logger.fatal('test message'), returnsNormally);
        });
      });

      group('Method Delegation - Message Only', () {
        test(
          'trace should delegate to loggingService.trace with message only',
          () {
            logger.trace('trace message');

            verify(() => mockLoggingService.trace('trace message')).called(1);
          },
        );

        test(
          'debug should delegate to loggingService.debug with message only',
          () {
            logger.debug('debug message');

            verify(() => mockLoggingService.debug('debug message')).called(1);
          },
        );

        test(
          'info should delegate to loggingService.info with message only',
          () {
            logger.info('info message');

            verify(() => mockLoggingService.info('info message')).called(1);
          },
        );

        test(
          'warning should delegate to loggingService.warning with message only',
          () {
            logger.warning('warning message');

            verify(
              () => mockLoggingService.warning('warning message'),
            ).called(1);
          },
        );

        test(
          'error should delegate to loggingService.error with message only',
          () {
            logger.error('error message');

            verify(() => mockLoggingService.error('error message')).called(1);
          },
        );

        test(
          'fatal should delegate to loggingService.fatal with message only',
          () {
            logger.fatal('fatal message');

            verify(() => mockLoggingService.fatal('fatal message')).called(1);
          },
        );
      });

      group('Method Delegation - With Error Parameter', () {
        test('trace should delegate with error parameter', () {
          final error = Exception('test error');
          logger.trace('trace message', error: error);

          verify(
            () => mockLoggingService.trace('trace message', error: error),
          ).called(1);
        });

        test('debug should delegate with error parameter', () {
          final error = Exception('test error');
          logger.debug('debug message', error: error);

          verify(
            () => mockLoggingService.debug('debug message', error: error),
          ).called(1);
        });

        test('info should delegate with error parameter', () {
          final error = Exception('test error');
          logger.info('info message', error: error);

          verify(
            () => mockLoggingService.info('info message', error: error),
          ).called(1);
        });

        test('warning should delegate with error parameter', () {
          final error = Exception('test error');
          logger.warning('warning message', error: error);

          verify(
            () => mockLoggingService.warning('warning message', error: error),
          ).called(1);
        });

        test('error should delegate with error parameter', () {
          final error = Exception('test error');
          logger.error('error message', error: error);

          verify(
            () => mockLoggingService.error('error message', error: error),
          ).called(1);
        });

        test('fatal should delegate with error parameter', () {
          final error = Exception('test error');
          logger.fatal('fatal message', error: error);

          verify(
            () => mockLoggingService.fatal('fatal message', error: error),
          ).called(1);
        });
      });

      group('Method Delegation - With StackTrace Parameter', () {
        test('trace should delegate with stackTrace parameter', () {
          final stackTrace = StackTrace.current;
          logger.trace('trace message', stackTrace: stackTrace);

          verify(
            () => mockLoggingService.trace(
              'trace message',
              stackTrace: stackTrace,
            ),
          ).called(1);
        });

        test('debug should delegate with stackTrace parameter', () {
          final stackTrace = StackTrace.current;
          logger.debug('debug message', stackTrace: stackTrace);

          verify(
            () => mockLoggingService.debug(
              'debug message',
              stackTrace: stackTrace,
            ),
          ).called(1);
        });

        test('info should delegate with stackTrace parameter', () {
          final stackTrace = StackTrace.current;
          logger.info('info message', stackTrace: stackTrace);

          verify(
            () =>
                mockLoggingService.info('info message', stackTrace: stackTrace),
          ).called(1);
        });

        test('warning should delegate with stackTrace parameter', () {
          final stackTrace = StackTrace.current;
          logger.warning('warning message', stackTrace: stackTrace);

          verify(
            () => mockLoggingService.warning(
              'warning message',
              stackTrace: stackTrace,
            ),
          ).called(1);
        });

        test('error should delegate with stackTrace parameter', () {
          final stackTrace = StackTrace.current;
          logger.error('error message', stackTrace: stackTrace);

          verify(
            () => mockLoggingService.error(
              'error message',
              stackTrace: stackTrace,
            ),
          ).called(1);
        });

        test('fatal should delegate with stackTrace parameter', () {
          final stackTrace = StackTrace.current;
          logger.fatal('fatal message', stackTrace: stackTrace);

          verify(
            () => mockLoggingService.fatal(
              'fatal message',
              stackTrace: stackTrace,
            ),
          ).called(1);
        });
      });

      group('Method Delegation - With Both Parameters', () {
        test(
          'trace should delegate with both error and stackTrace parameters',
          () {
            final error = Exception('test error');
            final stackTrace = StackTrace.current;
            logger.trace('trace message', error: error, stackTrace: stackTrace);

            verify(
              () => mockLoggingService.trace(
                'trace message',
                error: error,
                stackTrace: stackTrace,
              ),
            ).called(1);
          },
        );

        test(
          'debug should delegate with both error and stackTrace parameters',
          () {
            final error = Exception('test error');
            final stackTrace = StackTrace.current;
            logger.debug('debug message', error: error, stackTrace: stackTrace);

            verify(
              () => mockLoggingService.debug(
                'debug message',
                error: error,
                stackTrace: stackTrace,
              ),
            ).called(1);
          },
        );

        test(
          'info should delegate with both error and stackTrace parameters',
          () {
            final error = Exception('test error');
            final stackTrace = StackTrace.current;
            logger.info('info message', error: error, stackTrace: stackTrace);

            verify(
              () => mockLoggingService.info(
                'info message',
                error: error,
                stackTrace: stackTrace,
              ),
            ).called(1);
          },
        );

        test(
          'warning should delegate with both error and stackTrace parameters',
          () {
            final error = Exception('test error');
            final stackTrace = StackTrace.current;
            logger.warning(
              'warning message',
              error: error,
              stackTrace: stackTrace,
            );

            verify(
              () => mockLoggingService.warning(
                'warning message',
                error: error,
                stackTrace: stackTrace,
              ),
            ).called(1);
          },
        );

        test(
          'error should delegate with both error and stackTrace parameters',
          () {
            final error = Exception('test error');
            final stackTrace = StackTrace.current;
            logger.error('error message', error: error, stackTrace: stackTrace);

            verify(
              () => mockLoggingService.error(
                'error message',
                error: error,
                stackTrace: stackTrace,
              ),
            ).called(1);
          },
        );

        test(
          'fatal should delegate with both error and stackTrace parameters',
          () {
            final error = Exception('test error');
            final stackTrace = StackTrace.current;
            logger.fatal('fatal message', error: error, stackTrace: stackTrace);

            verify(
              () => mockLoggingService.fatal(
                'fatal message',
                error: error,
                stackTrace: stackTrace,
              ),
            ).called(1);
          },
        );
      });

      group('Parameter Edge Cases', () {
        test('should handle empty string messages', () {
          logger.info('');

          verify(() => mockLoggingService.info('')).called(1);
        });

        test('should handle null error parameter', () {
          logger.error('test message', error: null);

          verify(
            () => mockLoggingService.error('test message', error: null),
          ).called(1);
        });

        test('should handle null stackTrace parameter', () {
          logger.error('test message', stackTrace: null);

          verify(
            () => mockLoggingService.error('test message', stackTrace: null),
          ).called(1);
        });

        test('should handle both parameters as null', () {
          logger.error('test message', error: null, stackTrace: null);

          verify(
            () => mockLoggingService.error(
              'test message',
              error: null,
              stackTrace: null,
            ),
          ).called(1);
        });

        test('should handle long messages', () {
          final longMessage = 'A' * 1000;
          logger.info(longMessage);

          verify(() => mockLoggingService.info(longMessage)).called(1);
        });

        test('should handle special characters in messages', () {
          const specialMessage = 'Test with special chars: 你好 🌟 \n\t\r';
          logger.info(specialMessage);

          verify(() => mockLoggingService.info(specialMessage)).called(1);
        });

        test('should handle complex error objects', () {
          final complexError = {
            'code': 500,
            'message': 'Server error',
            'details': ['detail1', 'detail2'],
          };
          logger.error('Complex error', error: complexError);

          verify(
            () =>
                mockLoggingService.error('Complex error', error: complexError),
          ).called(1);
        });
      });
    });

    group('Service Integration', () {
      test('should work with real LoggingService', () {
        final realContainer = ProviderContainer();

        final logger = realContainer.read(loggerProvider);

        // Should not throw when calling real methods
        expect(() => logger.info('Real service test'), returnsNormally);
        expect(() => logger.debug('Real service test'), returnsNormally);
        expect(() => logger.warning('Real service test'), returnsNormally);
        expect(() => logger.error('Real service test'), returnsNormally);

        realContainer.dispose();
      });

      test('should maintain provider dependency chain with real services', () {
        final realContainer = ProviderContainer();

        final loggingService = realContainer.read(loggingServiceProvider);
        final logger = realContainer.read(loggerProvider);

        expect(loggingService, isA<LoggingService>());
        expect(logger, isA<Logger>());

        realContainer.dispose();
      });
    });

    group('Mock Scenarios', () {
      test('should work with different mock implementations', () {
        final customMock = MockLoggingService();
        final testContainer = ProviderContainer(
          overrides: [loggingServiceProvider.overrideWithValue(customMock)],
        );

        final logger = testContainer.read(loggerProvider);
        logger.info('Custom mock test');

        verify(() => customMock.info('Custom mock test')).called(1);

        testContainer.dispose();
      });

      test('should handle mock service replacement', () {
        final mock1 = MockLoggingService();
        final mock2 = MockLoggingService();

        var testContainer = ProviderContainer(
          overrides: [loggingServiceProvider.overrideWithValue(mock1)],
        );

        var logger = testContainer.read(loggerProvider);
        logger.info('Mock 1 test');

        verify(() => mock1.info('Mock 1 test')).called(1);

        testContainer.dispose();

        // Replace with different mock
        testContainer = ProviderContainer(
          overrides: [loggingServiceProvider.overrideWithValue(mock2)],
        );

        logger = testContainer.read(loggerProvider);
        logger.info('Mock 2 test');

        verify(() => mock2.info('Mock 2 test')).called(1);
        verifyNever(() => mock1.info('Mock 2 test'));

        testContainer.dispose();
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle provider access after container disposal', () {
        final testContainer = ProviderContainer();
        testContainer.dispose();

        // Should throw when accessing disposed container
        expect(
          () => testContainer.read(loggingServiceProvider),
          throwsA(isA<StateError>()),
        );
      });

      test('should handle multiple container instances', () {
        final container1 = ProviderContainer();
        final container2 = ProviderContainer();

        final service1 = container1.read(loggingServiceProvider);
        final service2 = container2.read(loggingServiceProvider);

        // LoggingService is a singleton - same instance across containers
        expect(identical(service1, service2), isTrue);

        // But containers themselves should be independent
        expect(identical(container1, container2), isFalse);

        container1.dispose();
        container2.dispose();
      });

      test('should handle rapid provider access', () {
        final testContainer = ProviderContainer();

        // Rapid access should not cause issues
        for (var i = 0; i < 100; i++) {
          testContainer.read(loggerProvider).info('Rapid access test $i');
        }

        testContainer.dispose();
      });
    });

    group('Performance and Memory', () {
      test('should not leak memory with repeated provider access', () {
        final testContainer = ProviderContainer();

        // Access providers multiple times
        for (var i = 0; i < 50; i++) {
          testContainer.read(loggingServiceProvider);
          testContainer.read(loggerProvider);
        }

        // Should dispose cleanly
        expect(testContainer.dispose, returnsNormally);
      });

      test('should handle concurrent provider access', () {
        final testContainer = ProviderContainer();

        // Simulate rapid sequential access to test provider stability
        for (var i = 0; i < 10; i++) {
          testContainer.read(loggerProvider).info('Concurrent test $i');
        }

        // Verify container can still be disposed after rapid access
        expect(testContainer.dispose, returnsNormally);
      });
    });
  });
}
