import 'package:budapp/data/repositories/interfaces/repositories.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:budapp/services/secure_storage_service.dart';
import 'package:budapp/services/session_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockFirebaseAuth extends Mock implements FirebaseAuth {}

class MockFirestore extends Mock implements FirebaseFirestore {}

class MockGoogleSignIn extends Mock implements GoogleSignIn {}

class MockUserRepository extends Mock implements IUserRepository {}

void main() {
  group('Core Providers Tests', () {
    late ProviderContainer container;
    late MockFirebaseAuth mockAuth;
    late MockFirestore mockFirestore;
    late MockGoogleSignIn mockGoogleSignIn;
    late MockUserRepository mockUserRepository;

    setUp(() {
      mockAuth = MockFirebaseAuth();
      mockFirestore = MockFirestore();
      mockGoogleSignIn = MockGoogleSignIn();
      mockUserRepository = MockUserRepository();

      // Set up mock behavior
      when(() => mockAuth.currentUser).thenReturn(null);
      when(
        () => mockAuth.authStateChanges(),
      ).thenAnswer((_) => Stream.value(null));

      container = ProviderContainer(
        overrides: [
          firebaseAuthProvider.overrideWithValue(mockAuth),
          firestoreProvider.overrideWithValue(mockFirestore),
          googleSignInProvider.overrideWithValue(mockGoogleSignIn),
          userRepositoryProvider.overrideWithValue(mockUserRepository),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    test('providers are defined and accessible', () {
      // Test that providers are defined without actually reading them
      // since Firebase isn't initialized in test environment
      expect(firebaseAuthProvider, isA<Provider<FirebaseAuth>>());
      expect(firestoreProvider, isA<Provider<FirebaseFirestore>>());
      expect(firestoreServiceProvider, isA<Provider<FirestoreService>>());
      expect(
        secureStorageServiceProvider,
        isA<Provider<SecureStorageService>>(),
      );
      expect(isAuthenticatedProvider, isA<Provider<bool>>());
      expect(isEmailVerifiedProvider, isA<Provider<bool>>());
      expect(currentUserProvider, isA<Provider<User?>>());
    });

    test('firestoreServiceProvider provides FirestoreService instance', () {
      final firestoreService = container.read(firestoreServiceProvider);
      expect(firestoreService, isA<FirestoreService>());
    });

    test(
      'secureStorageServiceProvider provides SecureStorageService instance',
      () {
        final secureStorageService = container.read(
          secureStorageServiceProvider,
        );
        expect(secureStorageService, isA<SecureStorageService>());
      },
    );

    test('repository providers are defined and accessible', () {
      // Test that repository providers are defined
      expect(userRepositoryProvider, isA<Provider<IUserRepository>>());
      expect(
        transactionRepositoryProvider,
        isA<Provider<ITransactionRepository>>(),
      );
      expect(accountRepositoryProvider, isA<Provider<IAccountRepository>>());
    });

    test('repository providers provide correct implementations', () {
      final userRepo = container.read(userRepositoryProvider);
      final transactionRepo = container.read(transactionRepositoryProvider);
      final accountRepo = container.read(accountRepositoryProvider);

      expect(userRepo, isA<IUserRepository>());
      expect(transactionRepo, isA<ITransactionRepository>());
      expect(accountRepo, isA<IAccountRepository>());
    });
  });

  group('UI Providers Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('globalLoadingProvider initial state is false', () {
      final loading = container.read(globalLoadingProvider);
      expect(loading, false);
    });

    test('globalLoadingProvider can be updated', () {
      expect(container.read(globalLoadingProvider), false);

      container.read(globalLoadingProvider.notifier).state = true;
      expect(container.read(globalLoadingProvider), true);
    });

    test('secureModeProvider initial state is false', () {
      final secureMode = container.read(secureModeProvider);
      expect(secureMode, false);
    });

    test('themeModeProvider initial state is system', () {
      final themeMode = container.read(themeModeProvider);
      expect(themeMode, 'system');
    });

    test('can update loading state providers', () {
      // Update global loading state
      container.read(globalLoadingProvider.notifier).state = true;
      expect(container.read(globalLoadingProvider), true);

      // Update secure mode
      container.read(secureModeProvider.notifier).state = true;
      expect(container.read(secureModeProvider), true);

      // Update theme mode
      container.read(themeModeProvider.notifier).state = 'dark';
      expect(container.read(themeModeProvider), 'dark');
    });
  });

  group('Auth Providers Tests', () {
    late ProviderContainer container;
    late MockFirebaseAuth mockAuth;
    late MockFirestore mockFirestore;
    late MockGoogleSignIn mockGoogleSignIn;
    late MockUserRepository mockUserRepository;

    setUp(() {
      mockAuth = MockFirebaseAuth();
      mockFirestore = MockFirestore();
      mockGoogleSignIn = MockGoogleSignIn();
      mockUserRepository = MockUserRepository();

      // Set up mock behavior
      when(() => mockAuth.currentUser).thenReturn(null);
      when(
        () => mockAuth.authStateChanges(),
      ).thenAnswer((_) => Stream.value(null));

      container = ProviderContainer(
        overrides: [
          firebaseAuthProvider.overrideWithValue(mockAuth),
          firestoreProvider.overrideWithValue(mockFirestore),
          googleSignInProvider.overrideWithValue(mockGoogleSignIn),
          userRepositoryProvider.overrideWithValue(mockUserRepository),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    test('authServiceProvider provides AuthService instance', () {
      final authService = container.read(authServiceProvider);
      expect(authService, isA<AuthService>());
    });

    test('authErrorServiceProvider provides AuthErrorService instance', () {
      final authErrorService = container.read(authErrorServiceProvider);
      expect(authErrorService, isA<AuthErrorService>());
    });

    test('sessionServiceProvider provides SessionService instance', () {
      final sessionService = container.read(sessionServiceProvider);
      expect(sessionService, isA<SessionService>());
    });

    test('isFullyAuthenticatedProvider returns false when no user', () {
      final isFullyAuthenticated = container.read(isFullyAuthenticatedProvider);
      expect(isFullyAuthenticated, false);
    });

    test('userProvidersProvider returns empty list when no user', () {
      final providers = container.read(userProvidersProvider);
      expect(providers, isEmpty);
    });

    test('isSignedInWithGoogleProvider returns false when no user', () {
      final isSignedInWithGoogle = container.read(isSignedInWithGoogleProvider);
      expect(isSignedInWithGoogle, false);
    });

    test('userDisplayNameProvider returns null when no user', () {
      final displayName = container.read(userDisplayNameProvider);
      expect(displayName, isNull);
    });

    test('userEmailProvider returns null when no user', () {
      final email = container.read(userEmailProvider);
      expect(email, isNull);
    });
  });

  group('Firebase Providers Tests', () {
    late ProviderContainer container;
    late MockFirebaseAuth mockAuth;
    late MockFirestore mockFirestore;

    setUp(() {
      mockAuth = MockFirebaseAuth();
      mockFirestore = MockFirestore();
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test(
      'firebaseServiceProvider provides FirebaseInitializationService instance',
      () {
        // Use a container with mocked Firebase providers to avoid Firebase initialization
        final testContainer = ProviderContainer(
          overrides: [
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
          ],
        );

        final firebaseService = testContainer.read(firebaseServiceProvider);
        expect(firebaseService, isA<FirebaseInitializationService>());

        testContainer.dispose();
      },
    );

    test('firebaseInitializedProvider returns initialization status', () {
      // Use a container with mocked Firebase providers to avoid Firebase initialization
      final testContainer = ProviderContainer(
        overrides: [
          firebaseAuthProvider.overrideWithValue(mockAuth),
          firestoreProvider.overrideWithValue(mockFirestore),
        ],
      );

      final isInitialized = testContainer.read(firebaseInitializedProvider);
      expect(isInitialized, isA<bool>());

      testContainer.dispose();
    });

    test('firebase providers are defined', () {
      // Test that providers are defined without calling Firebase methods
      expect(
        firebaseServiceProvider,
        isA<Provider<FirebaseInitializationService>>(),
      );
      expect(firebaseInitializedProvider, isA<Provider<bool>>());
      expect(firebaseProjectInfoProvider, isA<Provider<Map<String, String>>>());
    });
  });
}
