import 'package:budapp/providers/ui_providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('UI Providers Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    group('globalLoadingProvider', () {
      testWidgets('should initialize with false', (tester) async {
        final loading = container.read(globalLoadingProvider);
        expect(loading, isFalse);
      });

      testWidgets('should update loading state', (tester) async {
        // Initially false
        expect(container.read(globalLoadingProvider), isFalse);

        // Set to true
        container.read(globalLoadingProvider.notifier).state = true;
        expect(container.read(globalLoadingProvider), isTrue);

        // Set back to false
        container.read(globalLoadingProvider.notifier).state = false;
        expect(container.read(globalLoadingProvider), isFalse);
      });

      testWidgets('should notify listeners when state changes', (tester) async {
        var notificationCount = 0;

        container.listen<bool>(globalLoadingProvider, (previous, next) {
          notificationCount++;
        });

        // Change state multiple times
        container.read(globalLoadingProvider.notifier).state = true;
        container.read(globalLoadingProvider.notifier).state = false;
        container.read(globalLoadingProvider.notifier).state = true;

        expect(notificationCount, equals(3));
      });

      testWidgets('should not notify if state is the same', (tester) async {
        var notificationCount = 0;

        container.listen<bool>(globalLoadingProvider, (previous, next) {
          notificationCount++;
        });

        // Set to same value multiple times
        container.read(globalLoadingProvider.notifier).state = false;
        container.read(globalLoadingProvider.notifier).state = false;

        expect(notificationCount, equals(0));
      });

      testWidgets('should work with custom overrides', (tester) async {
        final customContainer = ProviderContainer(
          overrides: [globalLoadingProvider.overrideWith((ref) => true)],
        );

        expect(customContainer.read(globalLoadingProvider), isTrue);

        customContainer.dispose();
      });
    });

    group('secureModeProvider', () {
      testWidgets('should initialize with false', (tester) async {
        final secureMode = container.read(secureModeProvider);
        expect(secureMode, isFalse);
      });

      testWidgets('should update secure mode state', (tester) async {
        // Initially false
        expect(container.read(secureModeProvider), isFalse);

        // Enable secure mode
        container.read(secureModeProvider.notifier).state = true;
        expect(container.read(secureModeProvider), isTrue);

        // Disable secure mode
        container.read(secureModeProvider.notifier).state = false;
        expect(container.read(secureModeProvider), isFalse);
      });

      testWidgets('should notify listeners when state changes', (tester) async {
        var lastValue = false;
        var notificationCount = 0;

        container.listen<bool>(secureModeProvider, (previous, next) {
          lastValue = next;
          notificationCount++;
        });

        // Toggle secure mode
        container.read(secureModeProvider.notifier).state = true;
        expect(lastValue, isTrue);
        expect(notificationCount, equals(1));

        container.read(secureModeProvider.notifier).state = false;
        expect(lastValue, isFalse);
        expect(notificationCount, equals(2));
      });

      testWidgets('should handle toggle operations', (tester) async {
        // Start with false
        expect(container.read(secureModeProvider), isFalse);

        // Toggle to true
        final notifier = container.read(secureModeProvider.notifier);
        notifier.state = !notifier.state;
        expect(container.read(secureModeProvider), isTrue);

        // Toggle back to false
        notifier.state = !notifier.state;
        expect(container.read(secureModeProvider), isFalse);
      });

      testWidgets('should work with custom initial value', (tester) async {
        final customContainer = ProviderContainer(
          overrides: [secureModeProvider.overrideWith((ref) => true)],
        );

        expect(customContainer.read(secureModeProvider), isTrue);

        customContainer.dispose();
      });
    });

    group('themeModeProvider', () {
      testWidgets('should initialize with system theme', (tester) async {
        final themeMode = container.read(themeModeProvider);
        expect(themeMode, equals('system'));
      });

      testWidgets('should update theme mode', (tester) async {
        // Initially system
        expect(container.read(themeModeProvider), equals('system'));

        // Set to light theme
        container.read(themeModeProvider.notifier).state = 'light';
        expect(container.read(themeModeProvider), equals('light'));

        // Set to dark theme
        container.read(themeModeProvider.notifier).state = 'dark';
        expect(container.read(themeModeProvider), equals('dark'));

        // Set back to system
        container.read(themeModeProvider.notifier).state = 'system';
        expect(container.read(themeModeProvider), equals('system'));
      });

      testWidgets('should handle valid theme mode values', (tester) async {
        final validThemes = ['system', 'light', 'dark'];

        for (final theme in validThemes) {
          container.read(themeModeProvider.notifier).state = theme;
          expect(container.read(themeModeProvider), equals(theme));
        }
      });

      testWidgets('should handle custom theme mode values', (tester) async {
        // Provider doesn't enforce specific values, so any string should work
        container.read(themeModeProvider.notifier).state = 'custom';
        expect(container.read(themeModeProvider), equals('custom'));

        container.read(themeModeProvider.notifier).state = 'highContrast';
        expect(container.read(themeModeProvider), equals('highContrast'));
      });

      testWidgets('should notify listeners when theme changes', (tester) async {
        var lastTheme = '';
        var notificationCount = 0;

        container.listen<String>(themeModeProvider, (previous, next) {
          lastTheme = next;
          notificationCount++;
        });

        // Change theme modes
        container.read(themeModeProvider.notifier).state = 'light';
        expect(lastTheme, equals('light'));
        expect(notificationCount, equals(1));

        container.read(themeModeProvider.notifier).state = 'dark';
        expect(lastTheme, equals('dark'));
        expect(notificationCount, equals(2));
      });

      testWidgets('should work with custom initial theme', (tester) async {
        final customContainer = ProviderContainer(
          overrides: [themeModeProvider.overrideWith((ref) => 'dark')],
        );

        expect(customContainer.read(themeModeProvider), equals('dark'));

        customContainer.dispose();
      });
    });

    group('Provider Integration Tests', () {
      testWidgets('should work independently', (tester) async {
        // All providers should work independently without affecting each other
        container.read(globalLoadingProvider.notifier).state = true;
        container.read(secureModeProvider.notifier).state = true;
        container.read(themeModeProvider.notifier).state = 'dark';

        expect(container.read(globalLoadingProvider), isTrue);
        expect(container.read(secureModeProvider), isTrue);
        expect(container.read(themeModeProvider), equals('dark'));

        // Changing one shouldn't affect others
        container.read(globalLoadingProvider.notifier).state = false;

        expect(container.read(globalLoadingProvider), isFalse);
        expect(container.read(secureModeProvider), isTrue); // unchanged
        expect(container.read(themeModeProvider), equals('dark')); // unchanged
      });

      testWidgets('should handle multiple state changes efficiently', (
        tester,
      ) async {
        var globalLoadingNotifications = 0;
        var secureModeNotifications = 0;
        var themeModeNotifications = 0;

        container.listen<bool>(
          globalLoadingProvider,
          (previous, next) => globalLoadingNotifications++,
        );

        container.listen<bool>(
          secureModeProvider,
          (previous, next) => secureModeNotifications++,
        );

        container.listen<String>(
          themeModeProvider,
          (previous, next) => themeModeNotifications++,
        );

        // Rapid state changes - ensure each provider gets actual state changes
        for (var i = 0; i < 5; i++) {
          container.read(globalLoadingProvider.notifier).state = i.isEven;
          // Start with true for first iteration to ensure state change from initial false
          container.read(secureModeProvider.notifier).state = i.isEven;
          container.read(themeModeProvider.notifier).state = i.isEven
              ? 'light'
              : 'dark';
        }

        expect(globalLoadingNotifications, equals(5));
        expect(secureModeNotifications, equals(5));
        expect(themeModeNotifications, equals(5));
      });

      testWidgets('should work with provider overrides', (tester) async {
        final customContainer = ProviderContainer(
          overrides: [
            globalLoadingProvider.overrideWith((ref) => true),
            secureModeProvider.overrideWith((ref) => true),
            themeModeProvider.overrideWith((ref) => 'dark'),
          ],
        );

        expect(customContainer.read(globalLoadingProvider), isTrue);
        expect(customContainer.read(secureModeProvider), isTrue);
        expect(customContainer.read(themeModeProvider), equals('dark'));

        customContainer.dispose();
      });

      testWidgets('should dispose cleanly', (tester) async {
        // Read all providers to initialize them
        container.read(globalLoadingProvider);
        container.read(secureModeProvider);
        container.read(themeModeProvider);

        // Make some state changes
        container.read(globalLoadingProvider.notifier).state = true;
        container.read(secureModeProvider.notifier).state = true;
        container.read(themeModeProvider.notifier).state = 'dark';

        // Container disposal should not throw
        expect(() => container.dispose(), returnsNormally);
      });
    });

    group('Edge Cases and Error Handling', () {
      testWidgets('should handle null-like values for theme mode', (
        tester,
      ) async {
        // Empty string
        container.read(themeModeProvider.notifier).state = '';
        expect(container.read(themeModeProvider), equals(''));

        // Reset to valid value
        container.read(themeModeProvider.notifier).state = 'system';
        expect(container.read(themeModeProvider), equals('system'));
      });

      testWidgets('should handle rapid toggle operations', (tester) async {
        // Rapid loading state toggles
        for (var i = 0; i < 100; i++) {
          container.read(globalLoadingProvider.notifier).state = i.isEven;
        }

        expect(container.read(globalLoadingProvider), isFalse); // 100 is even
      });

      testWidgets('should maintain state after provider refresh', (
        tester,
      ) async {
        // Set initial states
        container.read(globalLoadingProvider.notifier).state = true;
        container.read(secureModeProvider.notifier).state = true;
        container.read(themeModeProvider.notifier).state = 'dark';

        // Refresh container (simulates hot restart)
        container.refresh(globalLoadingProvider);
        container.refresh(secureModeProvider);
        container.refresh(themeModeProvider);

        // Wait for any pending tasks to complete
        await tester.pumpAndSettle();

        // Values should reset to defaults after refresh
        expect(container.read(globalLoadingProvider), isFalse);
        expect(container.read(secureModeProvider), isFalse);
        expect(container.read(themeModeProvider), equals('system'));
      });

      testWidgets('should handle concurrent access', (tester) async {
        // Simulate concurrent access to the same provider
        final futures = <Future<void>>[];

        for (var i = 0; i < 10; i++) {
          futures.add(
            Future.microtask(() {
              container.read(globalLoadingProvider.notifier).state = i.isEven;
            }),
          );
        }

        await Future.wait(futures);

        // Should complete without errors
        expect(container.read(globalLoadingProvider), isA<bool>());
      });
    });

    group('Performance Tests', () {
      testWidgets('should handle many listeners efficiently', (tester) async {
        final subscriptions = <ProviderSubscription<bool>>[];

        // Add many listeners
        for (var i = 0; i < 100; i++) {
          subscriptions.add(
            container.listen<bool>(globalLoadingProvider, (previous, next) {
              // Minimal listener logic
            }),
          );
        }

        // Change state - should notify all listeners efficiently
        container.read(globalLoadingProvider.notifier).state = true;

        // Cleanup
        for (final subscription in subscriptions) {
          subscription.close();
        }
      });

      testWidgets('should handle frequent state changes efficiently', (
        tester,
      ) async {
        final stopwatch = Stopwatch()..start();

        // Perform many state changes
        for (var i = 0; i < 1000; i++) {
          container.read(globalLoadingProvider.notifier).state = i.isEven;
        }

        stopwatch.stop();

        // Should complete reasonably quickly (less than 100ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });
    });
  });
}
