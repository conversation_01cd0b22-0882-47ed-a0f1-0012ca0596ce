import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';

/// Test coverage analysis and reporting utilities
// ignore: avoid_classes_with_only_static_members
class TestCoverageAnalysis {
  /// Runs test coverage analysis and generates report
  static Future<void> runCoverageAnalysis() async {
    debugPrint('🧪 Running comprehensive test coverage analysis...\n');

    // Run tests with coverage
    final testResult = await _runTestsWithCoverage();

    if (testResult.exitCode != 0) {
      debugPrint('❌ Tests failed. Coverage analysis aborted.');
      debugPrint('Test output:\n${testResult.stderr}');
      return;
    }

    debugPrint('✅ All tests passed!\n');

    // Generate coverage report
    await _generateCoverageReport();

    // Analyze coverage results
    await _analyzeCoverageResults();

    debugPrint('\n📊 Test coverage analysis complete!');
    debugPrint('📁 Coverage report generated in coverage/html/index.html');
  }

  /// Runs tests with coverage collection
  static Future<ProcessResult> _runTestsWithCoverage() async {
    debugPrint('🏃 Running tests with coverage collection...');

    final result = await Process.run('flutter', [
      'test',
      '--coverage',
    ], workingDirectory: Directory.current.path);

    if (result.exitCode == 0) {
      debugPrint('✅ Tests completed successfully');
    } else {
      debugPrint('❌ Tests failed with exit code: ${result.exitCode}');
    }

    return result;
  }

  /// Generates HTML coverage report
  static Future<void> _generateCoverageReport() async {
    debugPrint('📊 Generating HTML coverage report...');

    // Check if genhtml is available (part of lcov)
    final genhtmlCheck = await Process.run('which', ['genhtml']);

    if (genhtmlCheck.exitCode != 0) {
      debugPrint(
        '⚠️  genhtml not found. Install lcov to generate HTML reports:',
      );
      debugPrint('   macOS: brew install lcov');
      debugPrint('   Ubuntu: sudo apt-get install lcov');
      return;
    }

    // Generate HTML report
    final result = await Process.run('genhtml', [
      'coverage/lcov.info',
      '-o',
      'coverage/html',
      '--show-details',
      '--highlight',
      '--num-spaces',
      '4',
      '--legend',
    ], workingDirectory: Directory.current.path);

    if (result.exitCode == 0) {
      debugPrint('✅ HTML coverage report generated');
    } else {
      debugPrint('❌ Failed to generate HTML report: ${result.stderr}');
    }
  }

  /// Analyzes coverage results and provides recommendations
  static Future<void> _analyzeCoverageResults() async {
    debugPrint('🔍 Analyzing coverage results...\n');

    final lcovFile = File('coverage/lcov.info');
    if (!lcovFile.existsSync()) {
      debugPrint('❌ Coverage file not found: coverage/lcov.info');
      return;
    }

    final lcovContent = await lcovFile.readAsString();
    final coverageData = _parseLcovData(lcovContent);

    _printCoverageSummary(coverageData);
    _printDetailedAnalysis(coverageData);
    _printRecommendations(coverageData);
  }

  /// Parses LCOV data into structured format
  static Map<String, dynamic> _parseLcovData(String lcovContent) {
    final lines = lcovContent.split('\n');
    final files = <String, Map<String, dynamic>>{};
    String? currentFile;

    for (final line in lines) {
      if (line.startsWith('SF:')) {
        currentFile = line.substring(3);
        files[currentFile] = {
          'lines_found': 0,
          'lines_hit': 0,
          'functions_found': 0,
          'functions_hit': 0,
          'branches_found': 0,
          'branches_hit': 0,
        };
      } else if (currentFile != null) {
        if (line.startsWith('LF:')) {
          files[currentFile]!['lines_found'] = int.parse(line.substring(3));
        } else if (line.startsWith('LH:')) {
          files[currentFile]!['lines_hit'] = int.parse(line.substring(3));
        } else if (line.startsWith('FNF:')) {
          files[currentFile]!['functions_found'] = int.parse(line.substring(4));
        } else if (line.startsWith('FNH:')) {
          files[currentFile]!['functions_hit'] = int.parse(line.substring(4));
        } else if (line.startsWith('BRF:')) {
          files[currentFile]!['branches_found'] = int.parse(line.substring(4));
        } else if (line.startsWith('BRH:')) {
          files[currentFile]!['branches_hit'] = int.parse(line.substring(4));
        }
      }
    }

    return files;
  }

  /// Prints coverage summary
  static void _printCoverageSummary(Map<String, dynamic> coverageData) {
    var totalLinesFound = 0;
    var totalLinesHit = 0;
    var totalFunctionsFound = 0;
    var totalFunctionsHit = 0;
    var totalBranchesFound = 0;
    var totalBranchesHit = 0;

    for (final fileData in coverageData.values) {
      final data = fileData as Map<String, dynamic>;
      totalLinesFound += data['lines_found'] as int;
      totalLinesHit += data['lines_hit'] as int;
      totalFunctionsFound += data['functions_found'] as int;
      totalFunctionsHit += data['functions_hit'] as int;
      totalBranchesFound += data['branches_found'] as int;
      totalBranchesHit += data['branches_hit'] as int;
    }

    final linesCoverage = totalLinesFound > 0
        ? (totalLinesHit / totalLinesFound * 100).toStringAsFixed(1)
        : '0.0';
    final functionsCoverage = totalFunctionsFound > 0
        ? (totalFunctionsHit / totalFunctionsFound * 100).toStringAsFixed(1)
        : '0.0';
    final branchesCoverage = totalBranchesFound > 0
        ? (totalBranchesHit / totalBranchesFound * 100).toStringAsFixed(1)
        : '0.0';

    debugPrint('📊 COVERAGE SUMMARY');
    debugPrint('═══════════════════');
    debugPrint('Lines:     $totalLinesHit/$totalLinesFound ($linesCoverage%)');
    debugPrint(
      'Functions: $totalFunctionsHit/$totalFunctionsFound ($functionsCoverage%)',
    );
    debugPrint(
      'Branches:  $totalBranchesHit/$totalBranchesFound ($branchesCoverage%)',
    );
    debugPrint('');
  }

  /// Prints detailed coverage analysis by category
  static void _printDetailedAnalysis(Map<String, dynamic> coverageData) {
    debugPrint('📋 DETAILED ANALYSIS BY CATEGORY');
    debugPrint('═══════════════════════════════════');

    final categories = {
      'Repositories': 'lib/data/repositories/',
      'Services': 'lib/services/',
      'Providers': 'lib/providers/',
      'Models': 'lib/data/models/',
      'Screens': 'lib/screens/',
      'Widgets': 'lib/widgets/',
      'Utils': 'lib/utils/',
    };

    for (final entry in categories.entries) {
      final categoryName = entry.key;
      final pathPrefix = entry.value;

      final categoryFiles = coverageData.entries
          .where((e) => e.key.contains(pathPrefix))
          .toList();

      if (categoryFiles.isEmpty) {
        debugPrint('$categoryName: No files found');
        continue;
      }

      var categoryLinesFound = 0;
      var categoryLinesHit = 0;

      for (final file in categoryFiles) {
        final data = file.value as Map<String, dynamic>;
        categoryLinesFound += data['lines_found'] as int;
        categoryLinesHit += data['lines_hit'] as int;
      }

      final coverage = categoryLinesFound > 0
          ? (categoryLinesHit / categoryLinesFound * 100).toStringAsFixed(1)
          : '0.0';

      final status = double.parse(coverage) >= 80 ? '✅' : '⚠️';
      debugPrint(
        '$status $categoryName: $categoryLinesHit/$categoryLinesFound ($coverage%)',
      );
    }
    debugPrint('');
  }

  /// Prints recommendations for improving coverage
  static void _printRecommendations(Map<String, dynamic> coverageData) {
    debugPrint('💡 RECOMMENDATIONS');
    debugPrint('═══════════════════');

    final lowCoverageFiles = <String>[];
    final uncoveredFiles = <String>[];

    for (final entry in coverageData.entries) {
      final fileName = entry.key;
      final fileData = entry.value as Map<String, dynamic>;

      final linesFound = fileData['lines_found'] as int;
      final linesHit = fileData['lines_hit'] as int;

      if (linesFound == 0) continue;

      final coverage = linesHit / linesFound * 100;

      if (coverage == 0) {
        uncoveredFiles.add(fileName);
      } else if (coverage < 80) {
        lowCoverageFiles.add(fileName);
      }
    }

    if (uncoveredFiles.isNotEmpty) {
      debugPrint('🔴 Files with NO coverage (${uncoveredFiles.length}):');
      for (final file in uncoveredFiles.take(5)) {
        debugPrint('   • ${_getRelativePath(file)}');
      }
      if (uncoveredFiles.length > 5) {
        debugPrint('   • ... and ${uncoveredFiles.length - 5} more');
      }
      debugPrint('');
    }

    if (lowCoverageFiles.isNotEmpty) {
      debugPrint(
        '🟡 Files with LOW coverage (<80%) (${lowCoverageFiles.length}):',
      );
      for (final file in lowCoverageFiles.take(5)) {
        final fileData = coverageData[file] as Map<String, dynamic>;
        final coverage =
            ((fileData['lines_hit'] as int) /
                    (fileData['lines_found'] as int) *
                    100)
                .toStringAsFixed(1);
        debugPrint('   • ${_getRelativePath(file)} ($coverage%)');
      }
      if (lowCoverageFiles.length > 5) {
        debugPrint('   • ... and ${lowCoverageFiles.length - 5} more');
      }
      debugPrint('');
    }

    debugPrint('📝 Action Items:');
    debugPrint(
      '   1. Focus on testing repositories and services (business logic)',
    );
    debugPrint('   2. Add widget tests for critical UI components');
    debugPrint('   3. Create integration tests for complete user flows');
    debugPrint('   4. Test error handling and edge cases');
    debugPrint('   5. Aim for 80%+ coverage on business logic');
  }

  /// Gets relative path for display
  static String _getRelativePath(String fullPath) {
    if (fullPath.contains('/lib/')) {
      return fullPath.substring(fullPath.indexOf('/lib/') + 1);
    }
    return fullPath;
  }
}

/// Main function to run coverage analysis
void main() async {
  await TestCoverageAnalysis.runCoverageAnalysis();
}
