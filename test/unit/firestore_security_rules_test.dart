import 'dart:io';

import 'package:flutter_test/flutter_test.dart';

/// Unit tests for Firestore Security Rules validation
///
/// These tests verify that:
/// 1. The firestore.rules file exists and is properly formatted
/// 2. Referential integrity functions are defined
/// 3. Security rules include proper validation logic
/// 4. All required helper functions are present
void main() {
  group('Firestore Security Rules Validation', () {
    late String rulesContent;

    setUpAll(() async {
      // Read the firestore.rules file
      final rulesFile = File('firestore.rules');
      expect(
        rulesFile.existsSync(),
        isTrue,
        reason: 'firestore.rules file should exist in project root',
      );

      rulesContent = await rulesFile.readAsString();
    });

    group('Basic Structure', () {
      test('should have proper rules version', () {
        expect(rulesContent, contains("rules_version = '2'"));
      });

      test('should define cloud.firestore service', () {
        expect(rulesContent, contains('service cloud.firestore'));
      });

      test('should have comprehensive documentation', () {
        expect(rulesContent, contains('BudApp Firestore Security Rules'));
        expect(rulesContent, contains('User data isolation'));
        expect(rulesContent, contains('Authentication required'));
        expect(rulesContent, contains('Data validation'));
        expect(rulesContent, contains('Referential integrity'));
      });
    });

    group('Helper Functions', () {
      test('should define authentication helper functions', () {
        expect(rulesContent, contains('function isAuthenticated()'));
        expect(rulesContent, contains('function isOwner(userId)'));
      });

      test('should define validation helper functions', () {
        expect(rulesContent, contains('function isValidTimestamp(value)'));
        expect(
          rulesContent,
          contains('function isValidEnum(value, allowedValues)'),
        );
        expect(
          rulesContent,
          contains('function isValidStringLength(value, minLength, maxLength)'),
        );
        expect(
          rulesContent,
          contains(
            'function isValidOptionalString(data, field, minLength, maxLength)',
          ),
        );
        expect(
          rulesContent,
          contains('function isValidOptionalMap(data, field)'),
        );
        expect(
          rulesContent,
          contains('function isValidOptionalBoolean(data, field)'),
        );
        // Currency validation removed - using global currency preference
      });

      test('should define referential integrity helper functions', () {
        expect(
          rulesContent,
          contains('function canDeleteAccount(accountData)'),
        );
        expect(
          rulesContent,
          contains('function canDeleteCategory(categoryData)'),
        );
        expect(rulesContent, contains('function canDeleteTag(tagData)'));
      });
    });

    group('Collection Security Rules', () {
      test('should define user profile rules', () {
        expect(rulesContent, contains('match /users/{userId}'));
        expect(rulesContent, contains('allow read: if isOwner(userId)'));
        expect(
          rulesContent,
          contains('allow create: if isOwner(userId) && isValidUserProfile'),
        );
        expect(
          rulesContent,
          contains(
            'allow update: if isOwner(userId) && resource.data != null && isValidUserProfileUpdate',
          ),
        );
        expect(
          rulesContent,
          contains('allow delete: if false'),
        ); // Users cannot delete their profile
      });

      test('should define account rules with referential integrity', () {
        expect(
          rulesContent,
          contains('match /users/{userId}/accounts/{accountId}'),
        );
        expect(
          rulesContent,
          contains(
            'allow delete: if isOwner(userId) && resource.data != null && canDeleteAccount(resource.data)',
          ),
        );
      });

      test('should define transaction rules with reference validation', () {
        expect(
          rulesContent,
          contains('match /users/{userId}/transactions/{transactionId}'),
        );
      });

      test('should define category rules with referential integrity', () {
        expect(
          rulesContent,
          contains('match /users/{userId}/categories/{categoryId}'),
        );
        expect(
          rulesContent,
          contains(
            'allow delete: if isOwner(userId) && canDeleteCategory(resource.data)',
          ),
        );
      });

      test('should define budget rules with reference validation', () {
        expect(
          rulesContent,
          contains('match /users/{userId}/budgets/{budgetId}'),
        );
        expect(
          rulesContent,
          contains('referencedCategoryExists(data, userId)'),
        );
      });

      test('should define tag rules with referential integrity', () {
        expect(rulesContent, contains('match /users/{userId}/tags/{tagId}'));
        expect(
          rulesContent,
          contains(
            'allow delete: if isOwner(userId) && canDeleteTag(resource.data)',
          ),
        );
      });

      test('should define goal rules', () {
        expect(rulesContent, contains('match /users/{userId}/goals/{goalId}'));
      });
    });

    group('Validation Functions', () {
      test('should define user profile validation', () {
        expect(
          rulesContent,
          contains('function isValidUserProfile(data, userId)'),
        );
        expect(
          rulesContent,
          contains('function isValidUserProfileUpdate(newData, oldData)'),
        );
      });

      test('should define account validation', () {
        expect(
          rulesContent,
          contains('function isValidAccountCreation(data, userId, accountId)'),
        );
        expect(
          rulesContent,
          contains('function isValidAccountUpdate(newData, oldData, userId)'),
        );
      });

      test('should define transaction validation', () {
        expect(
          rulesContent,
          contains(
            'function isValidTransactionCreation(data, userId, transactionId)',
          ),
        );
        expect(
          rulesContent,
          contains(
            'function isValidTransactionUpdate(newData, oldData, userId)',
          ),
        );
        expect(
          rulesContent,
          contains('function isValidTransactionAccountLogic(data)'),
        );
      });

      test('should define category validation', () {
        expect(
          rulesContent,
          contains('function isValidCategory(data, userId, categoryId)'),
        );
        expect(
          rulesContent,
          contains('function isValidCategoryUpdate(newData, oldData, userId)'),
        );
      });

      test('should define budget validation', () {
        expect(
          rulesContent,
          contains('function isValidBudget(data, userId, budgetId)'),
        );
        expect(
          rulesContent,
          contains('function isValidBudgetUpdate(newData, oldData, userId)'),
        );
      });

      test('should define goal validation', () {
        expect(
          rulesContent,
          contains('function isValidGoalCreation(data, userId, goalId)'),
        );
        expect(
          rulesContent,
          contains('function isValidGoalUpdate(newData, oldData, userId)'),
        );
      });

      test('should define tag validation', () {
        expect(
          rulesContent,
          contains('function isValidTag(data, userId, tagId)'),
        );
        expect(
          rulesContent,
          contains('function isValidTagUpdate(newData, oldData, userId)'),
        );
      });
    });

    group('Referential Integrity Implementation', () {
      test('should implement safe deletion logic for accounts', () {
        // Account deletion should have canDeleteAccount function
        expect(
          rulesContent,
          contains('function canDeleteAccount(accountData)'),
        );
      });

      test('should implement safe deletion logic for categories', () {
        // Category deletion should check if category is inactive
        expect(rulesContent, contains('categoryData.isActive == false'));
      });

      test('should implement safe deletion logic for tags', () {
        // Tag deletion should check if tag is inactive
        expect(rulesContent, contains('tagData.isActive == false'));
      });

      test('should validate account references in transactions', () {
        // Transaction validation should have basic account logic validation
        // Note: Complex exists() checks removed to prevent timeout issues
        expect(rulesContent, contains('isValidTransactionAccountLogic'));
      });

      test(
        'should validate category references in transactions and budgets',
        () {
          // Transaction and budget validation should have basic field validation
          // Note: Complex exists() checks removed to prevent timeout issues
          expect(rulesContent, contains('isValidTransactionCreation'));
          expect(rulesContent, contains('isValidBudget'));
        },
      );
    });

    group('Security Enforcement', () {
      test('should deny all other access', () {
        expect(rulesContent, contains('match /{document=**}'));
        expect(rulesContent, contains('allow read, write: if false'));
      });

      test('should enforce user data isolation', () {
        // All subcollection rules should check ownership
        final userSubcollections = [
          'accounts',
          'transactions',
          'categories',
          'budgets',
          'goals',
          'tags',
        ];

        for (final collection in userSubcollections) {
          expect(rulesContent, contains('match /users/{userId}/$collection/{'));
          expect(rulesContent, contains('isOwner(userId)'));
        }
      });

      test('should require authentication for all operations', () {
        // All allow rules should include authentication checks
        final allowRules = RegExp(r'allow \w+: if').allMatches(rulesContent);
        expect(allowRules.length, greaterThan(0));

        // Most rules should include isOwner or isAuthenticated
        expect(rulesContent, contains('isOwner(userId)'));
        expect(rulesContent, contains('isAuthenticated()'));
      });
    });

    group('Data Validation', () {
      test('should validate required fields for all entities', () {
        // Check that validation functions verify required fields exist
        expect(rulesContent, contains("('userId' in data)"));
        expect(rulesContent, contains("('name' in data)"));
        expect(rulesContent, contains("('type' in data)"));
      });

      test('should validate enum values', () {
        expect(
          rulesContent,
          contains("data.classification in ['asset', 'liability']"),
        );
        expect(
          rulesContent,
          contains("data.type in ['income', 'expense', 'transfer']"),
        );
        expect(
          rulesContent,
          contains(
            "data.status in ['pending', 'completed', 'cancelled', 'failed']",
          ),
        );
      });

      // Currency validation test removed - using global currency preference

      test('should validate timestamps', () {
        expect(rulesContent, contains('isValidTimestamp(data.createdAt)'));
        expect(rulesContent, contains('isValidTimestamp(data.updatedAt)'));
      });
    });
  });
}
