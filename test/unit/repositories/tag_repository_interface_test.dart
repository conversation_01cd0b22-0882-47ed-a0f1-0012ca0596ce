import 'package:budapp/data/repositories/interfaces/tag_repository.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('TagRepository Interface Tests', () {
    group('TagValidationException', () {
      test('should create exception with message and errors', () {
        // Arrange
        const message = 'Validation failed';
        const errors = ['Name is required', 'Color is invalid'];

        // Act
        const exception = TagValidationException(message, errors);

        // Assert
        expect(exception.message, equals(message));
        expect(exception.errors, equals(errors));
        expect(
          exception.toString(),
          equals('TagValidationException: Validation failed'),
        );
      });

      test('should create exception with empty errors list', () {
        // Arrange
        const message = 'Validation failed';
        const errors = <String>[];

        // Act
        const exception = TagValidationException(message, errors);

        // Assert
        expect(exception.message, equals(message));
        expect(exception.errors, isEmpty);
        expect(
          exception.toString(),
          equals('TagValidationException: Validation failed'),
        );
      });

      test('should create exception with single error', () {
        // Arrange
        const message = 'Single validation error';
        const errors = ['Name cannot be empty'];

        // Act
        const exception = TagValidationException(message, errors);

        // Assert
        expect(exception.message, equals(message));
        expect(exception.errors, hasLength(1));
        expect(exception.errors.first, equals('Name cannot be empty'));
        expect(
          exception.toString(),
          equals('TagValidationException: Single validation error'),
        );
      });

      test('should create exception with multiple errors', () {
        // Arrange
        const message = 'Multiple validation errors';
        const errors = [
          'Name is required',
          'Color must be a valid hex code',
          'Name must be unique',
          'Name cannot exceed 50 characters',
        ];

        // Act
        const exception = TagValidationException(message, errors);

        // Assert
        expect(exception.message, equals(message));
        expect(exception.errors, hasLength(4));
        expect(
          exception.errors,
          containsAll([
            'Name is required',
            'Color must be a valid hex code',
            'Name must be unique',
            'Name cannot exceed 50 characters',
          ]),
        );
        expect(
          exception.toString(),
          equals('TagValidationException: Multiple validation errors'),
        );
      });

      test('should handle special characters in message and errors', () {
        // Arrange
        const message = r'Validation failed: @#$%^&*()';
        const errors = [
          r'Error with special chars: !@#$%',
          'Unicode error: 🏷️📝',
        ];

        // Act
        const exception = TagValidationException(message, errors);

        // Assert
        expect(exception.message, equals(message));
        expect(exception.errors, equals(errors));
        expect(
          exception.toString(),
          equals(r'TagValidationException: Validation failed: @#$%^&*()'),
        );
      });

      test('should handle very long message and errors', () {
        // Arrange
        final longMessage = 'A' * 1000;
        final longErrors = ['B' * 500, 'C' * 500];

        // Act
        final exception = TagValidationException(longMessage, longErrors);

        // Assert
        expect(exception.message, equals(longMessage));
        expect(exception.errors, equals(longErrors));
        expect(
          exception.toString(),
          equals('TagValidationException: $longMessage'),
        );
      });

      test('should be an Exception type', () {
        // Arrange & Act
        const exception = TagValidationException('Test message', [
          'Test error',
        ]);

        // Assert
        expect(exception, isA<Exception>());
      });

      test('should maintain errors list content', () {
        // Arrange
        const originalErrors = ['Error 1', 'Error 2'];
        const exception = TagValidationException('Test', originalErrors);

        // Act & Assert
        expect(exception.errors, equals(originalErrors));
        expect(exception.errors, hasLength(2));
        expect(exception.errors.first, equals('Error 1'));
        expect(exception.errors.last, equals('Error 2'));
      });
    });

    group('TagAlreadyExistsException', () {
      test('should create exception with tag name', () {
        // Arrange
        const tagName = 'Duplicate Tag';

        // Act
        const exception = TagAlreadyExistsException(tagName);

        // Assert
        expect(exception.tagName, equals(tagName));
        expect(
          exception.toString(),
          equals(
            'TagAlreadyExistsException: Tag "Duplicate Tag" already exists',
          ),
        );
      });

      test('should handle empty tag name', () {
        // Arrange
        const tagName = '';

        // Act
        const exception = TagAlreadyExistsException(tagName);

        // Assert
        expect(exception.tagName, equals(tagName));
        expect(
          exception.toString(),
          equals('TagAlreadyExistsException: Tag "" already exists'),
        );
      });

      test('should handle tag name with special characters', () {
        // Arrange
        const tagName = r'Special @#$ Tag';

        // Act
        const exception = TagAlreadyExistsException(tagName);

        // Assert
        expect(exception.tagName, equals(tagName));
        expect(
          exception.toString(),
          equals(
            r'TagAlreadyExistsException: Tag "Special @#$ Tag" already exists',
          ),
        );
      });

      test('should handle tag name with quotes', () {
        // Arrange
        const tagName = 'Tag with "quotes"';

        // Act
        const exception = TagAlreadyExistsException(tagName);

        // Assert
        expect(exception.tagName, equals(tagName));
        expect(
          exception.toString(),
          equals(
            'TagAlreadyExistsException: Tag "Tag with "quotes"" already exists',
          ),
        );
      });

      test('should handle very long tag name', () {
        // Arrange
        final longTagName = 'A' * 1000;

        // Act
        final exception = TagAlreadyExistsException(longTagName);

        // Assert
        expect(exception.tagName, equals(longTagName));
        expect(
          exception.toString(),
          equals(
            'TagAlreadyExistsException: Tag "$longTagName" already exists',
          ),
        );
      });

      test('should handle unicode characters in tag name', () {
        // Arrange
        const tagName = '🏷️ Unicode Tag 📝';

        // Act
        const exception = TagAlreadyExistsException(tagName);

        // Assert
        expect(exception.tagName, equals(tagName));
        expect(
          exception.toString(),
          equals(
            'TagAlreadyExistsException: Tag "🏷️ Unicode Tag 📝" already exists',
          ),
        );
      });

      test('should be an Exception type', () {
        // Arrange & Act
        const exception = TagAlreadyExistsException('Test Tag');

        // Assert
        expect(exception, isA<Exception>());
      });

      test('should handle null-like string values', () {
        // Arrange
        const tagName = 'null';

        // Act
        const exception = TagAlreadyExistsException(tagName);

        // Assert
        expect(exception.tagName, equals(tagName));
        expect(
          exception.toString(),
          equals('TagAlreadyExistsException: Tag "null" already exists'),
        );
      });
    });

    group('TagNotFoundException', () {
      test('should create exception with tag ID', () {
        // Arrange
        const tagId = 'tag-123';

        // Act
        const exception = TagNotFoundException(tagId);

        // Assert
        expect(exception.tagId, equals(tagId));
        expect(
          exception.toString(),
          equals('TagNotFoundException: Tag with ID "tag-123" not found'),
        );
      });

      test('should handle empty tag ID', () {
        // Arrange
        const tagId = '';

        // Act
        const exception = TagNotFoundException(tagId);

        // Assert
        expect(exception.tagId, equals(tagId));
        expect(
          exception.toString(),
          equals('TagNotFoundException: Tag with ID "" not found'),
        );
      });

      test('should handle tag ID with special characters', () {
        // Arrange
        const tagId = r'tag-@#$-123';

        // Act
        const exception = TagNotFoundException(tagId);

        // Assert
        expect(exception.tagId, equals(tagId));
        expect(
          exception.toString(),
          equals(r'TagNotFoundException: Tag with ID "tag-@#$-123" not found'),
        );
      });

      test('should handle very long tag ID', () {
        // Arrange
        final longTagId = 'tag-${'A' * 1000}';

        // Act
        final exception = TagNotFoundException(longTagId);

        // Assert
        expect(exception.tagId, equals(longTagId));
        expect(
          exception.toString(),
          equals('TagNotFoundException: Tag with ID "$longTagId" not found'),
        );
      });

      test('should handle UUID-like tag ID', () {
        // Arrange
        const tagId = '550e8400-e29b-41d4-a716-************';

        // Act
        const exception = TagNotFoundException(tagId);

        // Assert
        expect(exception.tagId, equals(tagId));
        expect(
          exception.toString(),
          equals(
            'TagNotFoundException: Tag with ID "550e8400-e29b-41d4-a716-************" not found',
          ),
        );
      });

      test('should handle numeric tag ID', () {
        // Arrange
        const tagId = '12345';

        // Act
        const exception = TagNotFoundException(tagId);

        // Assert
        expect(exception.tagId, equals(tagId));
        expect(
          exception.toString(),
          equals('TagNotFoundException: Tag with ID "12345" not found'),
        );
      });

      test('should be an Exception type', () {
        // Arrange & Act
        const exception = TagNotFoundException('test-id');

        // Assert
        expect(exception, isA<Exception>());
      });

      test('should handle unicode characters in tag ID', () {
        // Arrange
        const tagId = 'tag-🏷️-123';

        // Act
        const exception = TagNotFoundException(tagId);

        // Assert
        expect(exception.tagId, equals(tagId));
        expect(
          exception.toString(),
          equals('TagNotFoundException: Tag with ID "tag-🏷️-123" not found'),
        );
      });

      test('should handle null-like string values', () {
        // Arrange
        const tagId = 'null';

        // Act
        const exception = TagNotFoundException(tagId);

        // Assert
        expect(exception.tagId, equals(tagId));
        expect(
          exception.toString(),
          equals('TagNotFoundException: Tag with ID "null" not found'),
        );
      });
    });

    group('Exception Inheritance and Type Checking', () {
      test('all exceptions should be Exception types', () {
        // Arrange & Act
        const validationException = TagValidationException('Test', ['Error']);
        const alreadyExistsException = TagAlreadyExistsException('Test Tag');
        const notFoundException = TagNotFoundException('test-id');

        // Assert
        expect(validationException, isA<Exception>());
        expect(alreadyExistsException, isA<Exception>());
        expect(notFoundException, isA<Exception>());
      });

      test('exceptions should have different types', () {
        // Arrange & Act
        const validationException = TagValidationException('Test', ['Error']);
        const alreadyExistsException = TagAlreadyExistsException('Test Tag');
        const notFoundException = TagNotFoundException('test-id');

        // Assert
        expect(
          validationException.runtimeType,
          isNot(equals(alreadyExistsException.runtimeType)),
        );
        expect(
          validationException.runtimeType,
          isNot(equals(notFoundException.runtimeType)),
        );
        expect(
          alreadyExistsException.runtimeType,
          isNot(equals(notFoundException.runtimeType)),
        );
      });

      test('exceptions should be distinguishable in catch blocks', () {
        // Arrange
        final exceptions = [
          const TagValidationException('Test', ['Error']),
          const TagAlreadyExistsException('Test Tag'),
          const TagNotFoundException('test-id'),
        ];

        // Act & Assert
        for (final exception in exceptions) {
          expect(() => throw exception, throwsA(isA<Exception>()));

          if (exception is TagValidationException) {
            expect(exception.message, isNotEmpty);
            expect(exception.errors, isNotEmpty);
          } else if (exception is TagAlreadyExistsException) {
            expect(exception.tagName, isNotEmpty);
          } else if (exception is TagNotFoundException) {
            expect(exception.tagId, isNotEmpty);
          }
        }
      });
    });

    group('Exception Equality and Comparison', () {
      test(
        'TagValidationException instances with same data should be equal',
        () {
          // Arrange
          const exception1 = TagValidationException('Test', [
            'Error1',
            'Error2',
          ]);
          const exception2 = TagValidationException('Test', [
            'Error1',
            'Error2',
          ]);

          // Act & Assert
          expect(exception1.message, equals(exception2.message));
          expect(exception1.errors, equals(exception2.errors));
          expect(exception1.toString(), equals(exception2.toString()));
        },
      );

      test(
        'TagAlreadyExistsException instances with same data should be equal',
        () {
          // Arrange
          const exception1 = TagAlreadyExistsException('Test Tag');
          const exception2 = TagAlreadyExistsException('Test Tag');

          // Act & Assert
          expect(exception1.tagName, equals(exception2.tagName));
          expect(exception1.toString(), equals(exception2.toString()));
        },
      );

      test('TagNotFoundException instances with same data should be equal', () {
        // Arrange
        const exception1 = TagNotFoundException('test-id');
        const exception2 = TagNotFoundException('test-id');

        // Act & Assert
        expect(exception1.tagId, equals(exception2.tagId));
        expect(exception1.toString(), equals(exception2.toString()));
      });
    });
  });
}
