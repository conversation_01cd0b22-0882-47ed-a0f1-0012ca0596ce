import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/implementations/transaction_repository_impl.dart';
import 'package:budapp/features/budgets/services/budget_transaction_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes for testing
class MockBudgetTransactionService extends Mock
    implements BudgetTransactionService {}

void main() {
  group('TransactionRepositoryImpl - Basic Tests', () {
    late TransactionRepositoryImpl repository;
    late FirestoreService firestoreService;
    late MockBudgetTransactionService mockBudgetTransactionService;
    late FakeFirebaseFirestore fakeFirestore;

    // Test data constants
    const testUserId = 'test-user-123';
    const testTransactionId = 'test-transaction-456';
    const testAccountId = 'test-account-789';
    const testCategoryId = 'test-category-112';

    final testDateTime = DateTime(2024, 1, 15, 10, 30);

    setUp(() async {
      // Initialize fake Firestore
      fakeFirestore = FakeFirebaseFirestore();
      firestoreService = FirestoreService(fakeFirestore);
      mockBudgetTransactionService = MockBudgetTransactionService();

      // Create repository instance
      repository = TransactionRepositoryImpl(
        firestoreService,
        mockBudgetTransactionService,
      );

      // Create a test account in fake Firestore for validation tests
      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId)
          .set({
            'id': testAccountId,
            'userId': testUserId,
            'name': 'Test Account',
            'type': 'checking',
            'classification': 'asset',
            'initialBalanceCents': 100000,
            'currentBalanceCents': 100000,
            'createdAt': testDateTime.toIso8601String(),
            'updatedAt': testDateTime.toIso8601String(),
          });
    });

    group('Basic Validation Tests', () {
      test(
        'validateTransaction - should return false for zero amount',
        () async {
          // Arrange
          final invalidTransaction = Transaction(
            id: testTransactionId,
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 0, // Invalid: zero amount
            fromAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'Test expense',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: testDateTime,
          );

          // Act
          final result = await repository.validateTransaction(
            invalidTransaction,
          );

          // Assert
          expect(result, isFalse);
        },
      );

      test(
        'validateTransaction - should return false for negative amount',
        () async {
          // Arrange
          final invalidTransaction = Transaction(
            id: testTransactionId,
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: -1000, // Invalid: negative amount
            fromAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'Test expense',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: testDateTime,
          );

          // Act
          final result = await repository.validateTransaction(
            invalidTransaction,
          );

          // Assert
          expect(result, isFalse);
        },
      );

      test(
        'validateTransaction - should return false for empty userId',
        () async {
          // Arrange
          final invalidTransaction = Transaction(
            id: testTransactionId,
            userId: '', // Invalid: empty userId
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 5000,
            fromAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'Test expense',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: testDateTime,
          );

          // Act
          final result = await repository.validateTransaction(
            invalidTransaction,
          );

          // Assert
          expect(result, isFalse);
        },
      );

      test(
        'validateTransaction - should return true for valid expense transaction',
        () async {
          // Arrange
          final validTransaction = Transaction(
            id: testTransactionId,
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 5000, // Valid: positive amount
            fromAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'Test expense',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: testDateTime,
          );

          // Act
          final result = await repository.validateTransaction(validTransaction);

          // Assert
          expect(result, isTrue);
        },
      );

      test(
        'validateTransaction - should return false for non-existent account',
        () async {
          // Arrange
          final invalidTransaction = Transaction(
            id: testTransactionId,
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 5000,
            fromAccountId:
                'non-existent-account', // Invalid: account doesn't exist
            categoryId: testCategoryId,
            description: 'Test expense',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: testDateTime,
          );

          // Act
          final result = await repository.validateTransaction(
            invalidTransaction,
          );

          // Assert
          expect(result, isFalse);
        },
      );
    });

    group('CRUD Operations', () {
      test('create - should create transaction successfully', () async {
        // Arrange
        final transaction = Transaction(
          id: testTransactionId,
          userId: testUserId,
          type: TransactionType.expense,
          status: TransactionStatus.completed,
          amountCents: 5000,
          fromAccountId: testAccountId,
          categoryId: testCategoryId,
          description: 'Test expense',
          transactionDate: testDateTime,
          createdAt: testDateTime,
          updatedAt: testDateTime,
        );

        // Act
        final result = await repository.create(transaction);

        // Assert
        expect(result, equals(testTransactionId));

        // Verify transaction was created in Firestore
        final doc = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('transactions')
            .doc(testTransactionId)
            .get();
        expect(doc.exists, isTrue);
        expect(doc.data()!['amountCents'], equals(5000));
      });

      test(
        'getTransactionById - should return transaction when exists',
        () async {
          // Arrange - Create transaction in fake Firestore
          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(testTransactionId)
              .set({
                'id': testTransactionId,
                'userId': testUserId,
                'type': 'expense',
                'status': 'completed',
                'amountCents': 5000,
                'fromAccountId': testAccountId,
                'categoryId': testCategoryId,
                'description': 'Test expense',
                'transactionDate': testDateTime.toIso8601String(),
                'createdAt': testDateTime.toIso8601String(),
                'updatedAt': testDateTime.toIso8601String(),
                'tagIds': <String>[],
                'metadata': <String, dynamic>{},
                'schemaVersion': 1,
              });

          // Act
          final result = await repository.getTransactionById(
            testUserId,
            testTransactionId,
          );

          // Assert
          expect(result, isNotNull);
          expect(result!.id, equals(testTransactionId));
          expect(result.userId, equals(testUserId));
          expect(result.amountCents, equals(5000));
          expect(result.type, equals(TransactionType.expense));
        },
      );

      test(
        'getTransactionById - should return null when transaction does not exist',
        () async {
          // Act
          final result = await repository.getTransactionById(
            testUserId,
            'non-existent-id',
          );

          // Assert
          expect(result, isNull);
        },
      );

      test(
        'transactionExists - should return true when transaction exists',
        () async {
          // Arrange - Create transaction in fake Firestore
          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(testTransactionId)
              .set({'test': 'data'});

          // Act
          final result = await repository.transactionExists(
            testUserId,
            testTransactionId,
          );

          // Assert
          expect(result, isTrue);
        },
      );

      test(
        'transactionExists - should return false when transaction does not exist',
        () async {
          // Act
          final result = await repository.transactionExists(
            testUserId,
            'non-existent-id',
          );

          // Assert
          expect(result, isFalse);
        },
      );

      test(
        'getUserTransactionCount - should return transaction count',
        () async {
          // Arrange - Create multiple transactions in fake Firestore
          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc('tx1')
              .set({'test': 'data1'});
          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc('tx2')
              .set({'test': 'data2'});
          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc('tx3')
              .set({'test': 'data3'});

          // Act
          final result = await repository.getUserTransactionCount(testUserId);

          // Assert
          expect(result, equals(3));
        },
      );

      test('update - should update transaction successfully', () async {
        // Arrange - Create test transaction
        final testTransaction = Transaction(
          id: testTransactionId,
          userId: testUserId,
          type: TransactionType.expense,
          status: TransactionStatus.completed,
          amountCents: 5000,
          fromAccountId: testAccountId,
          categoryId: testCategoryId,
          description: 'Test expense',
          transactionDate: testDateTime,
          createdAt: testDateTime,
          updatedAt: testDateTime,
          tagIds: [],
          metadata: {},
        );

        // Create initial transaction
        await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('transactions')
            .doc(testTransactionId)
            .set(testTransaction.toJson());

        final updatedTransaction = testTransaction.copyWith(
          description: 'Updated description',
          amountCents: 7500,
        );

        // Act
        await repository.update(testTransactionId, updatedTransaction);

        // Assert - Verify transaction was updated
        final doc = await fakeFirestore
            .collection('users')
            .doc(testUserId)
            .collection('transactions')
            .doc(testTransactionId)
            .get();
        expect(doc.exists, isTrue);
        expect(doc.data()!['description'], equals('Updated description'));
        expect(doc.data()!['amountCents'], equals(7500));
      });

      test(
        'deleteTransactionForUser - should delete transaction successfully',
        () async {
          // Arrange - Create test transaction
          final testTransaction = Transaction(
            id: testTransactionId,
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 5000,
            fromAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'Test expense',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: testDateTime,
            tagIds: [],
            metadata: {},
          );

          // Create initial transaction
          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(testTransactionId)
              .set(testTransaction.toJson());

          // Verify it exists first
          var doc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(testTransactionId)
              .get();
          expect(doc.exists, isTrue);

          // Act - Use the simple delete method for testing
          await repository.deleteTransactionForUser(
            testUserId,
            testTransactionId,
          );

          // Assert - Verify transaction was deleted
          doc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(testTransactionId)
              .get();
          expect(doc.exists, isFalse);
        },
      );
    });
  });
}
