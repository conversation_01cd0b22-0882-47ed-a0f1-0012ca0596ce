import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/implementations/transaction_repository_impl.dart';
import 'package:budapp/features/budgets/services/budget_transaction_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart' as firestore;
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/mock_data_factory.dart';

// Mock classes for testing
class MockBudgetTransactionService extends Mock
    implements BudgetTransactionService {}

// Fake classes for fallback values
class FakeFirestoreTransaction extends Fake implements firestore.Transaction {}

void main() {
  setUpAll(() {
    // Use MockDataFactory to create fallback values instead of Fake classes
    registerFallbackValue(MockDataFactory.createTransaction());
    registerFallbackValue(FakeFirestoreTransaction());
  });

  group('TransactionRepositoryImpl - Advanced Coverage Tests', () {
    late TransactionRepositoryImpl repository;
    late FirestoreService firestoreService;
    late MockBudgetTransactionService mockBudgetTransactionService;
    late FakeFirebaseFirestore fakeFirestore;

    // Test data constants
    const testUserId = 'test-user-123';
    const testAccountId = 'test-account-789';
    const testToAccountId = 'test-to-account-890';
    const testCategoryId = 'test-category-112';

    final testDateTime = DateTime(2024, 1, 15, 10, 30);

    setUp(() async {
      // Initialize fake Firestore
      fakeFirestore = FakeFirebaseFirestore();
      firestoreService = FirestoreService(fakeFirestore);
      mockBudgetTransactionService = MockBudgetTransactionService();

      // Mock budget service methods to prevent errors
      when(
        () => mockBudgetTransactionService.updateBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async => []);

      when(
        () => mockBudgetTransactionService.updateBudgetsForTransactionUpdate(
          any(),
          any(),
          any(),
        ),
      ).thenAnswer((_) async => []);

      when(
        () => mockBudgetTransactionService.revertBudgetsForTransaction(
          any(),
          any(),
        ),
      ).thenAnswer((_) async => []);

      // Create repository instance
      repository = TransactionRepositoryImpl(
        firestoreService,
        mockBudgetTransactionService,
      );

      // Create test accounts in fake Firestore
      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testAccountId)
          .set({
            'id': testAccountId,
            'userId': testUserId,
            'name': 'Test Source Account',
            'type': 'checking',
            'classification': 'asset',
            'initialBalanceCents': 100000,
            'currentBalanceCents': 100000,
            'isPrimary': false,
            'isActive': true,
            'schemaVersion': 1,
            'createdAt': testDateTime.toIso8601String(),
            'updatedAt': testDateTime.toIso8601String(),
            'metadata': <String, dynamic>{},
          });

      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(testToAccountId)
          .set({
            'id': testToAccountId,
            'userId': testUserId,
            'name': 'Test Destination Account',
            'type': 'savings',
            'classification': 'asset',
            'initialBalanceCents': 50000,
            'currentBalanceCents': 50000,
            'isPrimary': false,
            'isActive': true,
            'schemaVersion': 1,
            'createdAt': testDateTime.toIso8601String(),
            'updatedAt': testDateTime.toIso8601String(),
            'metadata': <String, dynamic>{},
          });
    });

    group('Atomic Transaction Creation Tests', () {
      test(
        'createIncomeTransaction - should create transaction and update account balance atomically',
        () async {
          // Act - Create income transaction
          final transactionId = await repository.createIncomeTransaction(
            userId: testUserId,
            toAccountId: testToAccountId,
            amountCents: 25000,
            transactionDate: testDateTime,
            categoryId: testCategoryId,
            description: 'Test income',
            notes: 'Test notes',
            tags: ['tag1', 'tag2'],
            metadata: {'key': 'value'},
          );

          // Assert - Verify transaction was created
          expect(transactionId, isNotEmpty);

          final transactionDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(transactionId)
              .get();

          expect(transactionDoc.exists, isTrue);
          final transactionData = transactionDoc.data()!;
          expect(transactionData['type'], equals('income'));
          expect(transactionData['amountCents'], equals(25000));
          expect(transactionData['toAccountId'], equals(testToAccountId));
          expect(transactionData['categoryId'], equals(testCategoryId));
          expect(transactionData['description'], equals('Test income'));
          expect(transactionData['notes'], equals('Test notes'));
          expect(transactionData['tagIds'], equals(['tag1', 'tag2']));

          // Assert - Verify account balance was updated
          final accountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testToAccountId)
              .get();

          expect(accountDoc.exists, isTrue);
          final accountData = accountDoc.data()!;
          expect(
            accountData['currentBalanceCents'],
            equals(75000),
          ); // 50000 + 25000

          // Verify budget service was called
          verify(
            () => mockBudgetTransactionService.updateBudgetsForTransaction(
              any(),
              any(),
            ),
          ).called(1);
        },
      );

      test(
        'createIncomeTransaction - should throw error for invalid account',
        () async {
          // Act & Assert - Should throw error for non-existent account
          expect(
            () => repository.createIncomeTransaction(
              userId: testUserId,
              toAccountId: 'invalid-account',
              amountCents: 25000,
              transactionDate: testDateTime,
            ),
            throwsA(isA<ArgumentError>()),
          );
        },
      );

      test(
        'createExpenseTransaction - should create transaction and update account balance atomically',
        () async {
          // Act - Create expense transaction
          final transactionId = await repository.createExpenseTransaction(
            userId: testUserId,
            fromAccountId: testAccountId,
            amountCents: 30000,
            transactionDate: testDateTime,
            categoryId: testCategoryId,
            description: 'Test expense',
            notes: 'Test expense notes',
            tags: ['expense-tag'],
            metadata: {'expense': true},
          );

          // Assert - Verify transaction was created
          expect(transactionId, isNotEmpty);

          final transactionDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(transactionId)
              .get();

          expect(transactionDoc.exists, isTrue);
          final transactionData = transactionDoc.data()!;
          expect(transactionData['type'], equals('expense'));
          expect(transactionData['amountCents'], equals(30000));
          expect(transactionData['fromAccountId'], equals(testAccountId));

          // Assert - Verify account balance was updated (decreased)
          final accountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();

          expect(accountDoc.exists, isTrue);
          final accountData = accountDoc.data()!;
          expect(
            accountData['currentBalanceCents'],
            equals(70000),
          ); // 100000 - 30000
        },
      );

      test(
        'createExpenseTransaction - should throw error for invalid source account',
        () async {
          // Act & Assert - Should throw error for non-existent account
          expect(
            () => repository.createExpenseTransaction(
              userId: testUserId,
              fromAccountId: 'invalid-account',
              amountCents: 30000,
              transactionDate: testDateTime,
            ),
            throwsA(isA<ArgumentError>()),
          );
        },
      );

      test(
        'createTransferTransaction - should create transaction and update both account balances atomically',
        () async {
          // Act - Create transfer transaction
          final transactionId = await repository.createTransferTransaction(
            userId: testUserId,
            fromAccountId: testAccountId,
            toAccountId: testToAccountId,
            amountCents: 20000,
            transactionDate: testDateTime,
            description: 'Test transfer',
            notes: 'Transfer notes',
            tags: ['transfer-tag'],
            metadata: {'transfer': true},
          );

          // Assert - Verify transaction was created
          expect(transactionId, isNotEmpty);

          final transactionDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(transactionId)
              .get();

          expect(transactionDoc.exists, isTrue);
          final transactionData = transactionDoc.data()!;
          expect(transactionData['type'], equals('transfer'));
          expect(transactionData['amountCents'], equals(20000));
          expect(transactionData['fromAccountId'], equals(testAccountId));
          expect(transactionData['toAccountId'], equals(testToAccountId));

          // Assert - Verify source account balance was decreased
          final fromAccountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();

          expect(fromAccountDoc.exists, isTrue);
          final fromAccountData = fromAccountDoc.data()!;
          expect(
            fromAccountData['currentBalanceCents'],
            equals(80000),
          ); // 100000 - 20000

          // Assert - Verify destination account balance was increased
          final toAccountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testToAccountId)
              .get();

          expect(toAccountDoc.exists, isTrue);
          final toAccountData = toAccountDoc.data()!;
          expect(
            toAccountData['currentBalanceCents'],
            equals(70000),
          ); // 50000 + 20000
        },
      );

      test(
        'createTransferTransaction - should throw error for same source and destination accounts',
        () async {
          // Act & Assert - Should throw error for same accounts
          expect(
            () => repository.createTransferTransaction(
              userId: testUserId,
              fromAccountId: testAccountId,
              toAccountId: testAccountId, // Same as source
              amountCents: 20000,
              transactionDate: testDateTime,
            ),
            throwsA(isA<ArgumentError>()),
          );
        },
      );

      test(
        'createTransferTransaction - should throw error for invalid accounts',
        () async {
          // Test invalid source account
          expect(
            () => repository.createTransferTransaction(
              userId: testUserId,
              fromAccountId: 'invalid-account',
              toAccountId: testToAccountId,
              amountCents: 20000,
              transactionDate: testDateTime,
            ),
            throwsA(isA<ArgumentError>()),
          );

          // Test invalid destination account
          expect(
            () => repository.createTransferTransaction(
              userId: testUserId,
              fromAccountId: testAccountId,
              toAccountId: 'invalid-account',
              amountCents: 20000,
              transactionDate: testDateTime,
            ),
            throwsA(isA<ArgumentError>()),
          );
        },
      );
    });

    group('Transaction Update with Balance Adjustment Tests', () {
      late String existingTransactionId;

      setUp(() async {
        // Create an existing transaction for update tests
        existingTransactionId = await repository.createExpenseTransaction(
          userId: testUserId,
          fromAccountId: testAccountId,
          amountCents: 15000,
          transactionDate: testDateTime,
          categoryId: testCategoryId,
          description: 'Original expense',
        );
      });

      test(
        'updateTransactionWithBalanceAdjustment - should update transaction and adjust account balances',
        () async {
          // Arrange - Create updated transaction with different amount
          final updatedTransaction = Transaction(
            id: existingTransactionId,
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 25000, // Changed from 15000 to 25000
            fromAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'Updated expense',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: DateTime.now(),
          );

          // Act - Update transaction
          await repository.updateTransactionWithBalanceAdjustment(
            testUserId,
            existingTransactionId,
            updatedTransaction,
          );

          // Assert - Verify transaction was updated
          final transactionDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(existingTransactionId)
              .get();

          expect(transactionDoc.exists, isTrue);
          final transactionData = transactionDoc.data()!;
          expect(transactionData['amountCents'], equals(25000));
          expect(transactionData['description'], equals('Updated expense'));

          // Assert - Verify account balance reflects the change
          // Original: 100000 - 15000 = 85000
          // Update: 85000 - (25000 - 15000) = 75000
          final accountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();

          expect(accountDoc.exists, isTrue);
          final accountData = accountDoc.data()!;
          expect(accountData['currentBalanceCents'], equals(75000));

          // Verify budget service was called for update
          verify(
            () => mockBudgetTransactionService
                .updateBudgetsForTransactionUpdate(any(), any(), any()),
          ).called(1);
        },
      );

      test(
        'updateTransactionWithBalanceAdjustment - should throw error for non-existent transaction',
        () async {
          // Arrange - Create transaction with non-existent ID
          final invalidTransaction = Transaction(
            id: 'non-existent-id',
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 25000,
            fromAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'Invalid transaction',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: DateTime.now(),
          );

          // Act & Assert - Should throw error
          expect(
            () => repository.updateTransactionWithBalanceAdjustment(
              testUserId,
              'non-existent-id',
              invalidTransaction,
            ),
            throwsA(isA<ArgumentError>()),
          );
        },
      );

      test(
        'updateTransactionWithBalanceAdjustment - should throw error for wrong user',
        () async {
          // Arrange - Create transaction with different user ID
          final invalidTransaction = Transaction(
            id: existingTransactionId,
            userId: 'different-user',
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 25000,
            fromAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'Invalid transaction',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: DateTime.now(),
          );

          // Act & Assert - Should throw error
          expect(
            () => repository.updateTransactionWithBalanceAdjustment(
              testUserId,
              existingTransactionId,
              invalidTransaction,
            ),
            throwsA(isA<ArgumentError>()),
          );
        },
      );
    });

    group('Transaction Deletion with Balance Reversal Tests', () {
      test(
        'deleteTransaction - should delete transaction and reverse account balance changes',
        () async {
          // Arrange - Create an expense transaction first
          final transactionId = await repository.createExpenseTransaction(
            userId: testUserId,
            fromAccountId: testAccountId,
            amountCents: 40000,
            transactionDate: testDateTime,
            categoryId: testCategoryId,
            description: 'Transaction to delete',
          );

          // Verify initial state - balance should be 60000 (100000 - 40000)
          var accountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();
          expect(accountDoc.data()!['currentBalanceCents'], equals(60000));

          // Act - Delete the transaction
          await repository.deleteTransaction(testUserId, transactionId);

          // Assert - Verify transaction was deleted
          final transactionDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(transactionId)
              .get();

          expect(transactionDoc.exists, isFalse);

          // Assert - Verify account balance was restored
          accountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();

          expect(accountDoc.exists, isTrue);
          final accountData = accountDoc.data()!;
          expect(
            accountData['currentBalanceCents'],
            equals(100000),
          ); // Back to original

          // Verify budget service was called for reversal
          verify(
            () => mockBudgetTransactionService.revertBudgetsForTransaction(
              any(),
              any(),
            ),
          ).called(1);
        },
      );

      test(
        'deleteTransaction - should handle transfer transaction deletion correctly',
        () async {
          // Arrange - Create a transfer transaction
          final transactionId = await repository.createTransferTransaction(
            userId: testUserId,
            fromAccountId: testAccountId,
            toAccountId: testToAccountId,
            amountCents: 30000,
            transactionDate: testDateTime,
            description: 'Transfer to delete',
          );

          // Act - Delete the transfer transaction
          await repository.deleteTransaction(testUserId, transactionId);

          // Assert - Verify both account balances were restored
          final fromAccountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testAccountId)
              .get();
          expect(
            fromAccountDoc.data()!['currentBalanceCents'],
            equals(100000),
          ); // Restored

          final toAccountDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('accounts')
              .doc(testToAccountId)
              .get();
          expect(
            toAccountDoc.data()!['currentBalanceCents'],
            equals(50000),
          ); // Restored
        },
      );

      test(
        'deleteTransaction - should throw error for non-existent transaction',
        () async {
          // Act & Assert - Should throw error for non-existent transaction
          expect(
            () => repository.deleteTransaction(testUserId, 'non-existent-id'),
            throwsA(isA<ArgumentError>()),
          );
        },
      );

      test('deleteTransaction - should throw error for wrong user', () async {
        // Arrange - Create a transaction with one user
        final transactionId = await repository.createExpenseTransaction(
          userId: testUserId,
          fromAccountId: testAccountId,
          amountCents: 10000,
          transactionDate: testDateTime,
        );

        // Act & Assert - Try to delete with different user
        expect(
          () => repository.deleteTransaction('different-user', transactionId),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('Advanced Query and Analysis Tests', () {
      setUp(() async {
        // Create diverse test transactions for analysis
        final transactions = [
          Transaction(
            id: 'income-1',
            userId: testUserId,
            type: TransactionType.income,
            status: TransactionStatus.completed,
            amountCents: 50000,
            toAccountId: testAccountId,
            categoryId: 'income-cat',
            description: 'Salary',
            transactionDate: DateTime(2024, 1, 1),
            createdAt: testDateTime,
            updatedAt: testDateTime,
          ),
          Transaction(
            id: 'expense-1',
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 20000,
            fromAccountId: testAccountId,
            categoryId: 'food-cat',
            description: 'Groceries',
            transactionDate: DateTime(2024, 1, 15),
            createdAt: testDateTime,
            updatedAt: testDateTime,
          ),
          Transaction(
            id: 'expense-2',
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 30000,
            fromAccountId: testAccountId,
            categoryId: 'food-cat',
            description: 'Restaurant',
            transactionDate: DateTime(2024, 1, 20),
            createdAt: testDateTime,
            updatedAt: testDateTime,
          ),
          Transaction(
            id: 'transfer-1',
            userId: testUserId,
            type: TransactionType.transfer,
            status: TransactionStatus.completed,
            amountCents: 15000,
            fromAccountId: testAccountId,
            toAccountId: testToAccountId,
            description: 'Savings transfer',
            transactionDate: DateTime(2024, 1, 25),
            createdAt: testDateTime,
            updatedAt: testDateTime,
          ),
        ];

        for (final tx in transactions) {
          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(tx.id)
              .set(tx.toJson());
        }
      });

      test(
        'getTransactionStats - should calculate comprehensive transaction statistics',
        () async {
          // Act
          final stats = await repository.getTransactionStats(testUserId);

          // Assert - Basic counts and totals
          expect(stats['totalTransactions'], equals(4));
          expect(stats['totalIncome'], equals(50000));
          expect(stats['totalExpenses'], equals(50000)); // 20000 + 30000
          expect(stats['totalTransfers'], equals(15000));

          // Assert - Min/max/average calculations
          expect(stats['largestTransaction'], equals(50000));
          expect(stats['smallestTransaction'], equals(15000));
          expect(
            stats['averageTransactionAmount'],
            equals(28750),
          ); // (50000+20000+30000+15000)/4

          // Assert - Transaction type breakdown
          final typeBreakdown = stats['transactionsByType'] as Map<String, int>;
          expect(typeBreakdown['income'], equals(1));
          expect(typeBreakdown['expense'], equals(2));
          expect(typeBreakdown['transfer'], equals(1));

          // Assert - Status breakdown
          final statusBreakdown =
              stats['transactionsByStatus'] as Map<String, int>;
          expect(statusBreakdown['completed'], equals(4));
        },
      );

      test(
        'getSpendingByCategory - should calculate spending by category for date range',
        () async {
          // Act - Get spending for January 2024
          final spending = await repository.getSpendingByCategory(
            testUserId,
            DateTime(2024, 1, 1),
            DateTime(2024, 1, 31),
          );

          // Assert - Only expense transactions should be included
          expect(spending.length, equals(1));
          expect(spending['food-cat'], equals(50000)); // 20000 + 30000
        },
      );

      test(
        'getIncomeExpenseSummary - should calculate income/expense summary for date range',
        () async {
          // Act - Get summary for January 2024
          final summary = await repository.getIncomeExpenseSummary(
            testUserId,
            DateTime(2024, 1, 1),
            DateTime(2024, 1, 31),
          );

          // Assert
          expect(summary['income'], equals(50000));
          expect(summary['expenses'], equals(50000));
          expect(summary['netIncome'], equals(0)); // 50000 - 50000
        },
      );

      test(
        'calculateAccountBalanceForUser - should calculate balance from transaction history',
        () async {
          // Act - Calculate balance for test account
          final balance = await repository.calculateAccountBalanceForUser(
            testUserId,
            testAccountId,
          );

          // Assert - Balance calculation:
          // +50000 (income) -20000 (expense-1) -30000 (expense-2) -15000 (transfer out) = -15000
          expect(balance, equals(-15000));
        },
      );

      test(
        'searchTransactions - should find transactions by description, notes, and tags',
        () async {
          // Create a transaction with searchable content
          final searchableTransaction = Transaction(
            id: 'searchable-1',
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 10000,
            fromAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'Coffee Shop Purchase',
            notes: 'Morning coffee with friends',
            tagIds: ['social', 'caffeine'],
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: testDateTime,
          );

          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(searchableTransaction.id)
              .set(searchableTransaction.toJson());

          // Test search by description
          var results = await repository.searchTransactions(
            testUserId,
            'coffee',
          );
          expect(results.length, equals(1));
          expect(results.first.id, equals('searchable-1'));

          // Test search by notes
          results = await repository.searchTransactions(testUserId, 'morning');
          expect(results.length, equals(1));
          expect(results.first.id, equals('searchable-1'));

          // Test search by tags
          results = await repository.searchTransactions(testUserId, 'social');
          expect(results.length, equals(1));
          expect(results.first.id, equals('searchable-1'));

          // Test case insensitive search
          results = await repository.searchTransactions(testUserId, 'COFFEE');
          expect(results.length, equals(1));
        },
      );
    });

    group('Stream and Real-time Update Tests', () {
      test(
        'watchUserTransactions - should stream transactions for user',
        () async {
          // Create a test transaction
          final transaction = Transaction(
            id: 'stream-test-1',
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 10000,
            fromAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'Stream test',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: testDateTime,
          );

          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(transaction.id)
              .set(transaction.toJson());

          // Act - Create stream
          final stream = repository.watchUserTransactions(testUserId);

          // Assert - Stream should emit the transaction
          await expectLater(
            stream,
            emits(
              predicate<List<Transaction>>(
                (transactions) =>
                    transactions.isNotEmpty &&
                    transactions.any((tx) => tx.id == 'stream-test-1'),
              ),
            ),
          );
        },
      );

      test(
        'watchAccountTransactionsForUser - should stream transactions for specific account',
        () async {
          // Create transactions for different accounts
          final tx1 = Transaction(
            id: 'account-stream-1',
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 10000,
            fromAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'Account stream test 1',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: testDateTime,
          );

          final tx2 = Transaction(
            id: 'account-stream-2',
            userId: testUserId,
            type: TransactionType.income,
            status: TransactionStatus.completed,
            amountCents: 15000,
            toAccountId: testToAccountId,
            categoryId: testCategoryId,
            description: 'Account stream test 2',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: testDateTime,
          );

          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(tx1.id)
              .set(tx1.toJson());

          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(tx2.id)
              .set(tx2.toJson());

          // Act - Stream transactions for testAccountId only
          final stream = repository.watchAccountTransactionsForUser(
            testUserId,
            testAccountId,
          );

          // Assert - Stream should only emit transactions for testAccountId
          await expectLater(
            stream,
            emits(
              predicate<List<Transaction>>(
                (transactions) =>
                    transactions.length == 1 &&
                    transactions.first.id == 'account-stream-1',
              ),
            ),
          );
        },
      );

      test(
        'watchTransactionForUser - should stream individual transaction updates',
        () async {
          // Create a test transaction
          final transaction = Transaction(
            id: 'individual-stream-1',
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 10000,
            fromAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'Individual stream test',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: testDateTime,
          );

          await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(transaction.id)
              .set(transaction.toJson());

          // Act - Create stream for specific transaction
          final stream = repository.watchTransactionForUser(
            testUserId,
            transaction.id,
          );

          // Assert - Stream should emit the transaction
          await expectLater(
            stream,
            emits(
              predicate<Transaction?>(
                (tx) => tx != null && tx.id == 'individual-stream-1',
              ),
            ),
          );
        },
      );
    });

    group('Batch Operations Tests', () {
      test(
        'batchCreate - should create multiple transactions atomically',
        () async {
          // Arrange - Create multiple transactions
          final transactions = [
            Transaction(
              id: 'batch-1',
              userId: testUserId,
              type: TransactionType.expense,
              status: TransactionStatus.completed,
              amountCents: 10000,
              fromAccountId: testAccountId,
              categoryId: testCategoryId,
              description: 'Batch transaction 1',
              transactionDate: testDateTime,
              createdAt: testDateTime,
              updatedAt: testDateTime,
            ),
            Transaction(
              id: 'batch-2',
              userId: testUserId,
              type: TransactionType.expense,
              status: TransactionStatus.completed,
              amountCents: 20000,
              fromAccountId: testAccountId,
              categoryId: testCategoryId,
              description: 'Batch transaction 2',
              transactionDate: testDateTime,
              createdAt: testDateTime,
              updatedAt: testDateTime,
            ),
          ];

          // Act - Batch create transactions
          await repository.batchCreate(transactions);

          // Assert - Verify all transactions were created
          for (final tx in transactions) {
            final doc = await fakeFirestore
                .collection('users')
                .doc(testUserId)
                .collection('transactions')
                .doc(tx.id)
                .get();

            expect(doc.exists, isTrue);
            expect(doc.data()!['description'], equals(tx.description));
          }
        },
      );

      test(
        'batchCreate - should throw error for transactions with different user IDs',
        () async {
          // Arrange - Create transactions with different user IDs
          final transactions = [
            Transaction(
              id: 'batch-1',
              userId: testUserId,
              type: TransactionType.expense,
              status: TransactionStatus.completed,
              amountCents: 10000,
              fromAccountId: testAccountId,
              categoryId: testCategoryId,
              description: 'Batch transaction 1',
              transactionDate: testDateTime,
              createdAt: testDateTime,
              updatedAt: testDateTime,
            ),
            Transaction(
              id: 'batch-2',
              userId: 'different-user', // Different user ID
              type: TransactionType.expense,
              status: TransactionStatus.completed,
              amountCents: 20000,
              fromAccountId: testAccountId,
              categoryId: testCategoryId,
              description: 'Batch transaction 2',
              transactionDate: testDateTime,
              createdAt: testDateTime,
              updatedAt: testDateTime,
            ),
          ];

          // Act & Assert - Should throw error
          expect(
            () => repository.batchCreate(transactions),
            throwsA(isA<ArgumentError>()),
          );
        },
      );

      test(
        'batchUpdate - should update multiple transactions atomically',
        () async {
          // Arrange - First create some transactions
          await repository.batchCreate([
            Transaction(
              id: 'update-1',
              userId: testUserId,
              type: TransactionType.expense,
              status: TransactionStatus.completed,
              amountCents: 10000,
              fromAccountId: testAccountId,
              categoryId: testCategoryId,
              description: 'Original description 1',
              transactionDate: testDateTime,
              createdAt: testDateTime,
              updatedAt: testDateTime,
            ),
            Transaction(
              id: 'update-2',
              userId: testUserId,
              type: TransactionType.expense,
              status: TransactionStatus.completed,
              amountCents: 20000,
              fromAccountId: testAccountId,
              categoryId: testCategoryId,
              description: 'Original description 2',
              transactionDate: testDateTime,
              createdAt: testDateTime,
              updatedAt: testDateTime,
            ),
          ]);

          // Arrange - Create updated transactions
          final updatedTransactions = {
            'update-1': Transaction(
              id: 'update-1',
              userId: testUserId,
              type: TransactionType.expense,
              status: TransactionStatus.completed,
              amountCents: 15000, // Changed amount
              fromAccountId: testAccountId,
              categoryId: testCategoryId,
              description: 'Updated description 1', // Changed description
              transactionDate: testDateTime,
              createdAt: testDateTime,
              updatedAt: DateTime.now(),
            ),
            'update-2': Transaction(
              id: 'update-2',
              userId: testUserId,
              type: TransactionType.expense,
              status: TransactionStatus.completed,
              amountCents: 25000, // Changed amount
              fromAccountId: testAccountId,
              categoryId: testCategoryId,
              description: 'Updated description 2', // Changed description
              transactionDate: testDateTime,
              createdAt: testDateTime,
              updatedAt: DateTime.now(),
            ),
          };

          // Act - Batch update transactions
          await repository.batchUpdate(updatedTransactions);

          // Assert - Verify all transactions were updated
          for (final entry in updatedTransactions.entries) {
            final doc = await fakeFirestore
                .collection('users')
                .doc(testUserId)
                .collection('transactions')
                .doc(entry.key)
                .get();

            expect(doc.exists, isTrue);
            expect(doc.data()!['amountCents'], equals(entry.value.amountCents));
            expect(doc.data()!['description'], equals(entry.value.description));
          }
        },
      );

      test(
        'batchDeleteUserTransactions - should delete multiple transactions atomically',
        () async {
          // Arrange - First create some transactions
          await repository.batchCreate([
            Transaction(
              id: 'delete-1',
              userId: testUserId,
              type: TransactionType.expense,
              status: TransactionStatus.completed,
              amountCents: 10000,
              fromAccountId: testAccountId,
              categoryId: testCategoryId,
              description: 'To be deleted 1',
              transactionDate: testDateTime,
              createdAt: testDateTime,
              updatedAt: testDateTime,
            ),
            Transaction(
              id: 'delete-2',
              userId: testUserId,
              type: TransactionType.expense,
              status: TransactionStatus.completed,
              amountCents: 20000,
              fromAccountId: testAccountId,
              categoryId: testCategoryId,
              description: 'To be deleted 2',
              transactionDate: testDateTime,
              createdAt: testDateTime,
              updatedAt: testDateTime,
            ),
          ]);

          // Act - Batch delete transactions
          await repository.batchDeleteUserTransactions(testUserId, [
            'delete-1',
            'delete-2',
          ]);

          // Assert - Verify all transactions were deleted
          final doc1 = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc('delete-1')
              .get();

          final doc2 = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc('delete-2')
              .get();

          expect(doc1.exists, isFalse);
          expect(doc2.exists, isFalse);
        },
      );
    });

    group('Transaction Reassignment Tests', () {
      setUp(() async {
        // Create transactions for reassignment tests
        await repository.batchCreate([
          Transaction(
            id: 'reassign-1',
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 10000,
            fromAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'For reassignment test',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: testDateTime,
          ),
          Transaction(
            id: 'reassign-2',
            userId: testUserId,
            type: TransactionType.income,
            status: TransactionStatus.completed,
            amountCents: 20000,
            toAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'For reassignment test 2',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: testDateTime,
          ),
        ]);
      });

      test(
        'reassignTransactionsToAccountForUser - should reassign transactions to new account',
        () async {
          // Act - Reassign transactions from testAccountId to testToAccountId
          await repository.reassignTransactionsToAccountForUser(
            testUserId,
            testAccountId,
            testToAccountId,
          );

          // Assert - Verify transactions were reassigned
          final doc1 = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc('reassign-1')
              .get();

          final doc2 = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc('reassign-2')
              .get();

          expect(doc1.exists, isTrue);
          expect(doc1.data()!['fromAccountId'], equals(testToAccountId));

          expect(doc2.exists, isTrue);
          expect(doc2.data()!['toAccountId'], equals(testToAccountId));
        },
      );

      test(
        'reassignTransactionsToCategoryForUser - should reassign transactions to new category',
        () async {
          const newCategoryId = 'new-category-123';

          // Act - Reassign transactions to new category
          await repository.reassignTransactionsToCategoryForUser(
            testUserId,
            testCategoryId,
            newCategoryId,
          );

          // Assert - Verify transactions were reassigned
          final doc1 = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc('reassign-1')
              .get();

          final doc2 = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc('reassign-2')
              .get();

          expect(doc1.exists, isTrue);
          expect(doc1.data()!['categoryId'], equals(newCategoryId));

          expect(doc2.exists, isTrue);
          expect(doc2.data()!['categoryId'], equals(newCategoryId));
        },
      );
    });

    group('UnimplementedError Method Tests', () {
      test(
        'methods without user context should throw UnimplementedError',
        () async {
          // Test all methods that should throw UnimplementedError
          expect(
            () => repository.getById('test-id'),
            throwsA(isA<UnimplementedError>()),
          );

          expect(
            () => repository.delete('test-id'),
            throwsA(isA<UnimplementedError>()),
          );

          expect(
            () => repository.exists('test-id'),
            throwsA(isA<UnimplementedError>()),
          );

          expect(() => repository.getAll(), throwsA(isA<UnimplementedError>()));

          expect(
            () => repository.getPaginated(),
            throwsA(isA<UnimplementedError>()),
          );

          expect(
            () => repository.watchById('test-id'),
            throwsA(isA<UnimplementedError>()),
          );

          expect(
            () => repository.watchAll(),
            throwsA(isA<UnimplementedError>()),
          );

          expect(
            () => repository.batchDelete(['test-id']),
            throwsA(isA<UnimplementedError>()),
          );

          expect(() => repository.query(), throwsA(isA<UnimplementedError>()));

          expect(() => repository.count(), throwsA(isA<UnimplementedError>()));

          expect(
            () => repository.getTransactionsByAccountId('test-account'),
            throwsA(isA<UnimplementedError>()),
          );

          expect(
            () => repository.updateTransactionStatus(
              'test-id',
              TransactionStatus.completed,
            ),
            throwsA(isA<UnimplementedError>()),
          );

          expect(
            () => repository.calculateAccountBalance('test-account'),
            throwsA(isA<UnimplementedError>()),
          );

          expect(
            () => repository.watchAccountTransactions('test-account'),
            throwsA(isA<UnimplementedError>()),
          );

          expect(
            () => repository.watchTransaction('test-id'),
            throwsA(isA<UnimplementedError>()),
          );

          expect(
            () => repository.getTransactionsReferencingAccount('test-account'),
            throwsA(isA<UnimplementedError>()),
          );

          expect(
            () =>
                repository.getTransactionsReferencingCategory('test-category'),
            throwsA(isA<UnimplementedError>()),
          );

          expect(
            () => repository.reassignTransactionsToAccount(
              'old-account',
              'new-account',
            ),
            throwsA(isA<UnimplementedError>()),
          );

          expect(
            () => repository.reassignTransactionsToCategory(
              'old-category',
              'new-category',
            ),
            throwsA(isA<UnimplementedError>()),
          );
        },
      );
    });

    group('Additional Edge Cases and Error Handling', () {
      test(
        'createTransaction - should validate transaction before creation',
        () async {
          // Arrange - Create invalid transaction (zero amount)
          final invalidTransaction = Transaction(
            id: 'invalid-tx',
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 0, // Invalid amount
            fromAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'Invalid transaction',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: testDateTime,
          );

          // Act & Assert - Should throw error
          expect(
            () => repository.createTransaction(invalidTransaction),
            throwsA(isA<ArgumentError>()),
          );
        },
      );

      test(
        'bulkCreateTransactions - should validate all transactions before creation',
        () async {
          // Arrange - Mix of valid and invalid transactions
          final transactions = [
            Transaction(
              id: 'bulk-valid',
              userId: testUserId,
              type: TransactionType.expense,
              status: TransactionStatus.completed,
              amountCents: 10000,
              fromAccountId: testAccountId,
              categoryId: testCategoryId,
              description: 'Valid transaction',
              transactionDate: testDateTime,
              createdAt: testDateTime,
              updatedAt: testDateTime,
            ),
            Transaction(
              id: 'bulk-invalid',
              userId: testUserId,
              type: TransactionType.expense,
              status: TransactionStatus.completed,
              amountCents: 0, // Invalid amount
              fromAccountId: testAccountId,
              categoryId: testCategoryId,
              description: 'Invalid transaction',
              transactionDate: testDateTime,
              createdAt: testDateTime,
              updatedAt: testDateTime,
            ),
          ];

          // Act & Assert - Should throw error due to invalid transaction
          expect(
            () => repository.bulkCreateTransactions(transactions),
            throwsA(isA<ArgumentError>()),
          );
        },
      );

      test('getUserTransactionCount - should return correct count', () async {
        // Arrange - Create some transactions
        await repository.batchCreate([
          Transaction(
            id: 'count-1',
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 10000,
            fromAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'Count test 1',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: testDateTime,
          ),
          Transaction(
            id: 'count-2',
            userId: testUserId,
            type: TransactionType.expense,
            status: TransactionStatus.completed,
            amountCents: 20000,
            fromAccountId: testAccountId,
            categoryId: testCategoryId,
            description: 'Count test 2',
            transactionDate: testDateTime,
            createdAt: testDateTime,
            updatedAt: testDateTime,
          ),
        ]);

        // Act
        final count = await repository.getUserTransactionCount(testUserId);

        // Assert
        expect(count, equals(2));
      });

      test(
        'updateTransactionStatusForUser - should update transaction status',
        () async {
          // Arrange - Create a transaction
          final transactionId = await repository.createExpenseTransaction(
            userId: testUserId,
            fromAccountId: testAccountId,
            amountCents: 10000,
            transactionDate: testDateTime,
          );

          // Act - Update status
          await repository.updateTransactionStatusForUser(
            testUserId,
            transactionId,
            TransactionStatus.pending,
          );

          // Assert - Verify status was updated
          final transactionDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(transactionId)
              .get();

          expect(transactionDoc.exists, isTrue);
          expect(transactionDoc.data()!['status'], equals('pending'));
        },
      );

      test(
        'deleteTransactionForUser - should delete transaction without balance adjustment',
        () async {
          // Arrange - Create a transaction
          final transactionId = await repository.createExpenseTransaction(
            userId: testUserId,
            fromAccountId: testAccountId,
            amountCents: 10000,
            transactionDate: testDateTime,
          );

          // Act - Delete using simple method
          await repository.deleteTransactionForUser(testUserId, transactionId);

          // Assert - Verify transaction was deleted
          final transactionDoc = await fakeFirestore
              .collection('users')
              .doc(testUserId)
              .collection('transactions')
              .doc(transactionId)
              .get();

          expect(transactionDoc.exists, isFalse);
        },
      );
    });
  });
}
