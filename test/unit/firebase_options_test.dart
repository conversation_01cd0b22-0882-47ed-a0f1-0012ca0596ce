import 'package:budapp/firebase_options_dev.dart';
import 'package:budapp/firebase_options_staging.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Firebase Options Tests', () {
    group('FirebaseOptionsDev', () {
      test('should return android options for Android platform', () {
        // Arrange & Act
        const androidOptions = FirebaseOptionsDev.android;

        // Assert
        expect(androidOptions, isA<FirebaseOptions>());
        expect(
          androidOptions.apiKey,
          equals('AIzaSyBrk9FVoUnvo43FOpU-DPjWj0VTpigzy78'),
        );
        expect(
          androidOptions.appId,
          equals('1:710002723223:android:82612e2c81d043p3d51cd2'),
        );
        expect(androidOptions.messagingSenderId, equals('710002723223'));
        expect(androidOptions.projectId, equals('budapp-dev'));
        expect(
          androidOptions.storageBucket,
          equals('budapp-dev.firebasestorage.app'),
        );
      });

      test('should return iOS options for iOS platform', () {
        // Arrange & Act
        const iosOptions = FirebaseOptionsDev.ios;

        // Assert
        expect(iosOptions, isA<FirebaseOptions>());
        expect(
          iosOptions.apiKey,
          equals('AIzaSyBrk9FVoUnvo43FOpU-DPjWj0VTpigzy78'),
        );
        expect(
          iosOptions.appId,
          equals('1:710002723223:ios:82612e2c81d043p3d51cd2'),
        );
        expect(iosOptions.messagingSenderId, equals('710002723223'));
        expect(iosOptions.projectId, equals('budapp-dev'));
        expect(
          iosOptions.storageBucket,
          equals('budapp-dev.firebasestorage.app'),
        );
        expect(iosOptions.iosBundleId, equals('com.digitau.budapp.dev'));
      });

      test('should have different app IDs for Android and iOS', () {
        // Arrange & Act
        const androidOptions = FirebaseOptionsDev.android;
        const iosOptions = FirebaseOptionsDev.ios;

        // Assert
        expect(androidOptions.appId, isNot(equals(iosOptions.appId)));
        expect(androidOptions.appId, contains('android'));
        expect(iosOptions.appId, contains('ios'));
      });

      test('should have same project configuration for both platforms', () {
        // Arrange & Act
        const androidOptions = FirebaseOptionsDev.android;
        const iosOptions = FirebaseOptionsDev.ios;

        // Assert
        expect(androidOptions.apiKey, equals(iosOptions.apiKey));
        expect(
          androidOptions.messagingSenderId,
          equals(iosOptions.messagingSenderId),
        );
        expect(androidOptions.projectId, equals(iosOptions.projectId));
        expect(androidOptions.storageBucket, equals(iosOptions.storageBucket));
      });

      test('should have iOS-specific bundle ID', () {
        // Arrange & Act
        const iosOptions = FirebaseOptionsDev.ios;

        // Assert
        expect(iosOptions.iosBundleId, equals('com.digitau.budapp.dev'));
      });

      test('should not have bundle ID for Android', () {
        // Arrange & Act
        const androidOptions = FirebaseOptionsDev.android;

        // Assert
        expect(androidOptions.iosBundleId, isNull);
      });
    });

    group('FirebaseOptionsStaging', () {
      test('should return android options for Android platform', () {
        // Arrange & Act
        const androidOptions = FirebaseOptionsStaging.android;

        // Assert
        expect(androidOptions, isA<FirebaseOptions>());
        expect(
          androidOptions.apiKey,
          equals('AIzaSyDV9MFofuM8uzOH-_RKxhB2T4621XsE1M0'),
        );
        expect(
          androidOptions.appId,
          equals('1:552643559728:android:95892848efbb65967c48f5'),
        );
        expect(androidOptions.messagingSenderId, equals('552643559728'));
        expect(androidOptions.projectId, equals('budapp-staging-1'));
        expect(
          androidOptions.storageBucket,
          equals('budapp-staging-1.firebasestorage.app'),
        );
      });

      test('should return iOS options for iOS platform', () {
        // Arrange & Act
        const iosOptions = FirebaseOptionsStaging.ios;

        // Assert
        expect(iosOptions, isA<FirebaseOptions>());
        expect(
          iosOptions.apiKey,
          equals('AIzaSyDV9MFofuM8uzOH-_RKxhB2T4621XsE1M0'),
        );
        expect(
          iosOptions.appId,
          equals('1:552643559728:ios:95892848efbb65967c48f5'),
        );
        expect(iosOptions.messagingSenderId, equals('552643559728'));
        expect(iosOptions.projectId, equals('budapp-staging-1'));
        expect(
          iosOptions.storageBucket,
          equals('budapp-staging-1.firebasestorage.app'),
        );
        expect(iosOptions.iosBundleId, equals('com.digitau.budapp.staging'));
      });

      test('should have different app IDs for Android and iOS', () {
        // Arrange & Act
        const androidOptions = FirebaseOptionsStaging.android;
        const iosOptions = FirebaseOptionsStaging.ios;

        // Assert
        expect(androidOptions.appId, isNot(equals(iosOptions.appId)));
        expect(androidOptions.appId, contains('android'));
        expect(iosOptions.appId, contains('ios'));
      });

      test('should have same project configuration for both platforms', () {
        // Arrange & Act
        const androidOptions = FirebaseOptionsStaging.android;
        const iosOptions = FirebaseOptionsStaging.ios;

        // Assert
        expect(androidOptions.apiKey, equals(iosOptions.apiKey));
        expect(
          androidOptions.messagingSenderId,
          equals(iosOptions.messagingSenderId),
        );
        expect(androidOptions.projectId, equals(iosOptions.projectId));
        expect(androidOptions.storageBucket, equals(iosOptions.storageBucket));
      });

      test('should have iOS-specific bundle ID', () {
        // Arrange & Act
        const iosOptions = FirebaseOptionsStaging.ios;

        // Assert
        expect(iosOptions.iosBundleId, equals('com.digitau.budapp.staging'));
      });

      test('should not have bundle ID for Android', () {
        // Arrange & Act
        const androidOptions = FirebaseOptionsStaging.android;

        // Assert
        expect(androidOptions.iosBundleId, isNull);
      });
    });

    group('Environment Differences', () {
      test('should have different project IDs between dev and staging', () {
        // Arrange & Act
        const devAndroid = FirebaseOptionsDev.android;
        const stagingAndroid = FirebaseOptionsStaging.android;

        // Assert
        expect(devAndroid.projectId, equals('budapp-dev'));
        expect(stagingAndroid.projectId, equals('budapp-staging-1'));
        expect(devAndroid.projectId, isNot(equals(stagingAndroid.projectId)));
      });

      test('should have different API keys between dev and staging', () {
        // Arrange & Act
        const devAndroid = FirebaseOptionsDev.android;
        const stagingAndroid = FirebaseOptionsStaging.android;

        // Assert
        expect(devAndroid.apiKey, isNot(equals(stagingAndroid.apiKey)));
      });

      test('should have different app IDs between dev and staging', () {
        // Arrange & Act
        const devAndroid = FirebaseOptionsDev.android;
        const stagingAndroid = FirebaseOptionsStaging.android;

        // Assert
        expect(devAndroid.appId, isNot(equals(stagingAndroid.appId)));
      });

      test(
        'should have different messaging sender IDs between dev and staging',
        () {
          // Arrange & Act
          const devAndroid = FirebaseOptionsDev.android;
          const stagingAndroid = FirebaseOptionsStaging.android;

          // Assert
          expect(
            devAndroid.messagingSenderId,
            isNot(equals(stagingAndroid.messagingSenderId)),
          );
        },
      );

      test('should have different storage buckets between dev and staging', () {
        // Arrange & Act
        const devAndroid = FirebaseOptionsDev.android;
        const stagingAndroid = FirebaseOptionsStaging.android;

        // Assert
        expect(devAndroid.storageBucket, contains('budapp-dev'));
        expect(stagingAndroid.storageBucket, contains('budapp-staging-1'));
        expect(
          devAndroid.storageBucket,
          isNot(equals(stagingAndroid.storageBucket)),
        );
      });

      test('should have different bundle IDs between dev and staging iOS', () {
        // Arrange & Act
        const devIos = FirebaseOptionsDev.ios;
        const stagingIos = FirebaseOptionsStaging.ios;

        // Assert
        expect(devIos.iosBundleId, equals('com.digitau.budapp.dev'));
        expect(stagingIos.iosBundleId, equals('com.digitau.budapp.staging'));
        expect(devIos.iosBundleId, isNot(equals(stagingIos.iosBundleId)));
      });
    });

    group('Configuration Validation', () {
      test('should have valid project ID format for dev', () {
        // Arrange & Act
        const devOptions = FirebaseOptionsDev.android;

        // Assert
        expect(devOptions.projectId, matches(RegExp(r'^[a-z0-9-]+$')));
        expect(devOptions.projectId.length, greaterThan(0));
        expect(devOptions.projectId, contains('budapp'));
      });

      test('should have valid project ID format for staging', () {
        // Arrange & Act
        const stagingOptions = FirebaseOptionsStaging.android;

        // Assert
        expect(stagingOptions.projectId, matches(RegExp(r'^[a-z0-9-]+$')));
        expect(stagingOptions.projectId.length, greaterThan(0));
        expect(stagingOptions.projectId, contains('budapp'));
      });

      test('should have valid API key format', () {
        // Arrange & Act
        const devOptions = FirebaseOptionsDev.android;
        const stagingOptions = FirebaseOptionsStaging.android;

        // Assert
        expect(devOptions.apiKey, matches(RegExp(r'^AIza[A-Za-z0-9_-]+$')));
        expect(stagingOptions.apiKey, matches(RegExp(r'^AIza[A-Za-z0-9_-]+$')));
        expect(devOptions.apiKey.length, greaterThan(30));
        expect(stagingOptions.apiKey.length, greaterThan(30));
      });

      test('should have valid app ID format', () {
        // Arrange & Act
        const devAndroid = FirebaseOptionsDev.android;
        const devIos = FirebaseOptionsDev.ios;

        // Assert
        expect(
          devAndroid.appId,
          matches(RegExp(r'^\d+:\d+:android:[a-zA-Z0-9]+$')),
        );
        expect(devIos.appId, matches(RegExp(r'^\d+:\d+:ios:[a-zA-Z0-9]+$')));
      });

      test('should have valid messaging sender ID format', () {
        // Arrange & Act
        const devOptions = FirebaseOptionsDev.android;
        const stagingOptions = FirebaseOptionsStaging.android;

        // Assert
        expect(devOptions.messagingSenderId, matches(RegExp(r'^\d+$')));
        expect(stagingOptions.messagingSenderId, matches(RegExp(r'^\d+$')));
        expect(devOptions.messagingSenderId.length, greaterThan(10));
        expect(stagingOptions.messagingSenderId.length, greaterThan(10));
      });

      test('should have valid storage bucket format', () {
        // Arrange & Act
        const devOptions = FirebaseOptionsDev.android;
        const stagingOptions = FirebaseOptionsStaging.android;

        // Assert
        expect(devOptions.storageBucket, endsWith('.firebasestorage.app'));
        expect(stagingOptions.storageBucket, endsWith('.firebasestorage.app'));
        expect(devOptions.storageBucket, startsWith('budapp-'));
        expect(stagingOptions.storageBucket, startsWith('budapp-'));
      });

      test('should have valid bundle ID format for iOS', () {
        // Arrange & Act
        const devIos = FirebaseOptionsDev.ios;
        const stagingIos = FirebaseOptionsStaging.ios;

        // Assert
        expect(devIos.iosBundleId, matches(RegExp(r'^[a-z0-9.]+$')));
        expect(stagingIos.iosBundleId, matches(RegExp(r'^[a-z0-9.]+$')));
        expect(devIos.iosBundleId, startsWith('com.digitau.budapp.'));
        expect(stagingIos.iosBundleId, startsWith('com.digitau.budapp.'));
      });
    });

    group('Platform Detection Edge Cases', () {
      test('should have consistent configuration structure', () {
        // Arrange & Act
        const devAndroid = FirebaseOptionsDev.android;
        const devIos = FirebaseOptionsDev.ios;
        const stagingAndroid = FirebaseOptionsStaging.android;
        const stagingIos = FirebaseOptionsStaging.ios;

        // Assert - All configurations should have required fields
        final allConfigs = [devAndroid, devIos, stagingAndroid, stagingIos];
        for (final config in allConfigs) {
          expect(config.apiKey, isNotEmpty);
          expect(config.appId, isNotEmpty);
          expect(config.messagingSenderId, isNotEmpty);
          expect(config.projectId, isNotEmpty);
          expect(config.storageBucket, isNotEmpty);
        }

        // iOS-specific checks
        expect(devIos.iosBundleId, isNotNull);
        expect(stagingIos.iosBundleId, isNotNull);
      });

      test('should have environment-specific naming conventions', () {
        // Arrange & Act
        const devAndroid = FirebaseOptionsDev.android;
        const stagingAndroid = FirebaseOptionsStaging.android;

        // Assert
        expect(devAndroid.projectId, contains('dev'));
        expect(stagingAndroid.projectId, contains('staging'));
        expect(devAndroid.storageBucket, contains('dev'));
        expect(stagingAndroid.storageBucket, contains('staging'));
      });

      test('should maintain consistent bundle ID patterns', () {
        // Arrange & Act
        const devIos = FirebaseOptionsDev.ios;
        const stagingIos = FirebaseOptionsStaging.ios;

        // Assert
        expect(devIos.iosBundleId, endsWith('.dev'));
        expect(stagingIos.iosBundleId, endsWith('.staging'));

        // Both should follow the same base pattern
        final devBase = devIos.iosBundleId!.replaceAll('.dev', '');
        final stagingBase = stagingIos.iosBundleId!.replaceAll('.staging', '');
        expect(devBase, equals(stagingBase));
      });

      test('should have unique identifiers across environments', () {
        // Arrange & Act
        final devConfigs = [FirebaseOptionsDev.android, FirebaseOptionsDev.ios];
        final stagingConfigs = [
          FirebaseOptionsStaging.android,
          FirebaseOptionsStaging.ios,
        ];

        // Assert - No configuration should be identical
        for (final devConfig in devConfigs) {
          for (final stagingConfig in stagingConfigs) {
            expect(devConfig.apiKey, isNot(equals(stagingConfig.apiKey)));
            expect(devConfig.appId, isNot(equals(stagingConfig.appId)));
            expect(
              devConfig.messagingSenderId,
              isNot(equals(stagingConfig.messagingSenderId)),
            );
            expect(devConfig.projectId, isNot(equals(stagingConfig.projectId)));
            expect(
              devConfig.storageBucket,
              isNot(equals(stagingConfig.storageBucket)),
            );
          }
        }
      });

      test('should have proper app ID correlation with messaging sender ID', () {
        // Arrange & Act
        const devAndroid = FirebaseOptionsDev.android;
        const devIos = FirebaseOptionsDev.ios;

        // Assert - App IDs should contain the messaging sender ID
        expect(devAndroid.appId, contains(devAndroid.messagingSenderId));
        expect(devIos.appId, contains(devIos.messagingSenderId));

        // Both platforms should have the same messaging sender ID within environment
        expect(devAndroid.messagingSenderId, equals(devIos.messagingSenderId));
      });

      test('should have proper storage bucket correlation with project ID', () {
        // Arrange & Act
        const devAndroid = FirebaseOptionsDev.android;
        const stagingAndroid = FirebaseOptionsStaging.android;

        // Assert - Storage bucket should be based on project ID
        expect(devAndroid.storageBucket, startsWith(devAndroid.projectId));
        expect(
          stagingAndroid.storageBucket,
          startsWith(stagingAndroid.projectId),
        );
      });
    });

    group('Configuration Completeness', () {
      test('should have all required Firebase configuration fields', () {
        // Arrange
        final configs = [
          FirebaseOptionsDev.android,
          FirebaseOptionsDev.ios,
          FirebaseOptionsStaging.android,
          FirebaseOptionsStaging.ios,
        ];

        // Act & Assert
        for (final config in configs) {
          expect(
            config.apiKey,
            isNotEmpty,
            reason: 'API key should not be empty',
          );
          expect(
            config.appId,
            isNotEmpty,
            reason: 'App ID should not be empty',
          );
          expect(
            config.messagingSenderId,
            isNotEmpty,
            reason: 'Messaging sender ID should not be empty',
          );
          expect(
            config.projectId,
            isNotEmpty,
            reason: 'Project ID should not be empty',
          );
          expect(
            config.storageBucket,
            isNotEmpty,
            reason: 'Storage bucket should not be empty',
          );
        }
      });

      test('should have iOS bundle IDs only for iOS configurations', () {
        // Arrange & Act
        final androidConfigs = [
          FirebaseOptionsDev.android,
          FirebaseOptionsStaging.android,
        ];
        final iosConfigs = [FirebaseOptionsDev.ios, FirebaseOptionsStaging.ios];

        // Assert
        for (final config in androidConfigs) {
          expect(
            config.iosBundleId,
            isNull,
            reason: 'Android configs should not have iOS bundle ID',
          );
        }

        for (final config in iosConfigs) {
          expect(
            config.iosBundleId,
            isNotNull,
            reason: 'iOS configs should have iOS bundle ID',
          );
          expect(
            config.iosBundleId,
            isNotEmpty,
            reason: 'iOS bundle ID should not be empty',
          );
        }
      });

      test(
        'should have consistent configuration structure across environments',
        () {
          // Arrange
          const devAndroid = FirebaseOptionsDev.android;
          const devIos = FirebaseOptionsDev.ios;
          const stagingAndroid = FirebaseOptionsStaging.android;
          const stagingIos = FirebaseOptionsStaging.ios;

          // Act & Assert - Same structure across environments
          expect(devAndroid.runtimeType, equals(stagingAndroid.runtimeType));
          expect(devIos.runtimeType, equals(stagingIos.runtimeType));

          // Both environments should have the same field availability
          expect(devAndroid.iosBundleId, isNull);
          expect(stagingAndroid.iosBundleId, isNull);
          expect(devIos.iosBundleId, isNotNull);
          expect(stagingIos.iosBundleId, isNotNull);
        },
      );
    });
  });
}
