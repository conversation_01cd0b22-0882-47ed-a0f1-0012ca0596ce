import 'package:budapp/data/models/goal.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Goal Model Tests', () {
    group('Goal Creation', () {
      test('should create goal with all required fields', () {
        final goal = Goal(
          id: 'goal-1',
          userId: 'user-1',
          name: 'Test Goal',
          targetAmountCents: 100000, // $1000.00
          currentAmountCents: 25000, // $250.00
          targetDate: DateTime(2024, 12, 31),
          isCompleted: false,
          colorHex: '#FF5722',
          iconName: 'savings',
          status: GoalStatus.active,
          isActive: true,
          schemaVersion: 1,
          createdAt: DateTime(2024, 1, 1),
          updatedAt: DateTime(2024, 1, 1),
          metadata: {},
        );

        expect(goal.id, equals('goal-1'));
        expect(goal.userId, equals('user-1'));
        expect(goal.name, equals('Test Goal'));
        expect(goal.targetAmountCents, equals(100000));
        expect(goal.currentAmountCents, equals(25000));
        expect(goal.targetDate, equals(DateTime(2024, 12, 31)));
        expect(goal.status, equals(GoalStatus.active));
        expect(goal.isActive, isTrue);
        expect(goal.schemaVersion, equals(1));
        expect(goal.createdAt, equals(DateTime(2024, 1, 1)));
        expect(goal.updatedAt, equals(DateTime(2024, 1, 1)));
        expect(goal.metadata, isEmpty);
      });

      test('should create goal with factory method', () {
        final goal = Goal.create(
          userId: 'user-1',
          name: 'Factory Goal',
          targetAmountCents: 50000,
          targetDate: DateTime(2024, 6, 30),
          status: GoalStatus.active,
        );

        expect(goal.id, isEmpty); // Will be set by repository
        expect(goal.userId, equals('user-1'));
        expect(goal.name, equals('Factory Goal'));
        expect(goal.targetAmountCents, equals(50000));
        expect(goal.currentAmountCents, equals(0)); // Default value
        expect(goal.targetDate, equals(DateTime(2024, 6, 30)));
        expect(goal.status, equals(GoalStatus.active));
        expect(goal.isActive, isTrue);
        expect(goal.schemaVersion, equals(1));
        expect(goal.createdAt, isNotNull);
        expect(goal.updatedAt, isNotNull);
        expect(goal.metadata, isEmpty);
      });

      test('should create goal with optional description', () {
        final goal = Goal.create(
          userId: 'user-1',
          name: 'Goal with Description',
          targetAmountCents: 75000,
          targetDate: DateTime(2024, 9, 15),
          description: 'This is a test goal',
        );

        expect(goal.description, equals('This is a test goal'));
      });

      test('should create goal with custom current amount', () {
        final goal = Goal.create(
          userId: 'user-1',
          name: 'Goal with Progress',
          targetAmountCents: 100000,
          targetDate: DateTime(2024, 12, 31),
          currentAmountCents: 30000,
        );

        expect(goal.currentAmountCents, equals(30000));
      });

      test('should create different goal statuses', () {
        final activeGoal = Goal.create(
          userId: 'user-1',
          name: 'Active Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2024, 12, 31),
          status: GoalStatus.active,
        );

        final pausedGoal = Goal.create(
          userId: 'user-1',
          name: 'Paused Goal',
          targetAmountCents: 50000,
          targetDate: DateTime(2024, 6, 30),
          status: GoalStatus.paused,
        );

        final completedGoal = Goal.create(
          userId: 'user-1',
          name: 'Completed Goal',
          targetAmountCents: 200000,
          targetDate: DateTime(2025, 12, 31),
          status: GoalStatus.completed,
        );

        expect(activeGoal.status, equals(GoalStatus.active));
        expect(pausedGoal.status, equals(GoalStatus.paused));
        expect(completedGoal.status, equals(GoalStatus.completed));
        expect(
          completedGoal.isCompleted,
          isTrue,
        ); // Should be set automatically
      });
    });

    group('Goal JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final goal = Goal(
          id: 'goal-1',
          userId: 'user-1',
          name: 'JSON Goal',
          targetAmountCents: 100000,
          currentAmountCents: 25000,
          targetDate: DateTime(2024, 12, 31),
          status: GoalStatus.active,
          description: 'Test description',
          isCompleted: false,
          isActive: true,
          schemaVersion: 1,
          createdAt: DateTime(2024, 1, 1),
          updatedAt: DateTime(2024, 1, 2),
          metadata: {'priority': 'high'},
        );

        final json = goal.toJson();

        expect(json['id'], equals('goal-1'));
        expect(json['userId'], equals('user-1'));
        expect(json['name'], equals('JSON Goal'));
        expect(json['targetAmountCents'], equals(100000));
        expect(json['currentAmountCents'], equals(25000));
        expect(json['status'], equals('active'));
        expect(json['description'], equals('Test description'));
        expect(json['isActive'], isTrue);
        expect(json['schemaVersion'], equals(1));
        expect(json['metadata'], equals({'priority': 'high'}));
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'id': 'goal-1',
          'userId': 'user-1',
          'name': 'JSON Goal',
          'targetAmountCents': 100000,
          'currentAmountCents': 25000,
          'targetDate': '2024-12-31T00:00:00.000',
          'status': 'active',
          'description': 'Test description',
          'isCompleted': false,
          'isActive': true,
          'schemaVersion': 1,
          'createdAt': '2024-01-01T00:00:00.000',
          'updatedAt': '2024-01-02T00:00:00.000',
          'metadata': {'priority': 'high'},
        };

        final goal = Goal.fromJson(json);

        expect(goal.id, equals('goal-1'));
        expect(goal.userId, equals('user-1'));
        expect(goal.name, equals('JSON Goal'));
        expect(goal.targetAmountCents, equals(100000));
        expect(goal.currentAmountCents, equals(25000));
        expect(goal.targetDate, equals(DateTime(2024, 12, 31)));
        expect(goal.status, equals(GoalStatus.active));
        expect(goal.description, equals('Test description'));
        expect(goal.isActive, isTrue);
        expect(goal.schemaVersion, equals(1));
        expect(goal.createdAt, equals(DateTime(2024, 1, 1)));
        expect(goal.updatedAt, equals(DateTime(2024, 1, 2)));
        expect(goal.metadata, equals({'priority': 'high'}));
      });

      test('should handle missing optional fields in JSON', () {
        final json = {
          'id': 'goal-1',
          'userId': 'user-1',
          'name': 'Minimal Goal',
          'targetAmountCents': 100000,
          'currentAmountCents': 0, // Required field
          'isCompleted': false, // Required field
          'isActive': true, // Required field
          'schemaVersion': 1, // Required field
          'createdAt': '2024-01-01T00:00:00.000',
          'updatedAt': '2024-01-01T00:00:00.000',
          'metadata': <String, dynamic>{}, // Required field
        };

        final goal = Goal.fromJson(json);

        expect(goal.description, isNull);
        expect(goal.currentAmountCents, equals(0)); // Default value
        expect(goal.status, equals(GoalStatus.active)); // Default value
        expect(goal.isActive, isTrue); // Default value
        expect(goal.metadata, isEmpty);
      });

      test('should handle different goal statuses in JSON', () {
        final statuses = ['active', 'paused', 'completed', 'cancelled'];
        final expectedStatuses = [
          GoalStatus.active,
          GoalStatus.paused,
          GoalStatus.completed,
          GoalStatus.cancelled,
        ];

        for (var i = 0; i < statuses.length; i++) {
          final json = {
            'id': 'goal-$i',
            'userId': 'user-1',
            'name': 'Status Goal $i',
            'targetAmountCents': 100000,
            'currentAmountCents': 0, // Required field
            'isCompleted': false, // Required field
            'isActive': true, // Required field
            'schemaVersion': 1, // Required field
            'status': statuses[i],
            'createdAt': '2024-01-01T00:00:00.000',
            'updatedAt': '2024-01-01T00:00:00.000',
            'metadata': <String, dynamic>{}, // Required field
          };

          final goal = Goal.fromJson(json);
          expect(goal.status, equals(expectedStatuses[i]));
        }
      });
    });

    group('Goal CopyWith', () {
      test('should copy with updated current amount', () {
        final originalGoal = Goal.create(
          userId: 'user-1',
          name: 'Progress Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2024, 12, 31),
        );

        final updatedGoal = originalGoal.copyWith(currentAmountCents: 50000);

        expect(updatedGoal.currentAmountCents, equals(50000));
        expect(
          updatedGoal.targetAmountCents,
          equals(originalGoal.targetAmountCents),
        );
        expect(updatedGoal.name, equals(originalGoal.name));
      });

      test('should copy with updated target amount', () {
        final originalGoal = Goal.create(
          userId: 'user-1',
          name: 'Target Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2024, 12, 31),
        );

        final updatedGoal = originalGoal.copyWith(targetAmountCents: 150000);

        expect(updatedGoal.targetAmountCents, equals(150000));
        expect(
          updatedGoal.currentAmountCents,
          equals(originalGoal.currentAmountCents),
        );
        expect(updatedGoal.name, equals(originalGoal.name));
      });

      test('should copy with updated target date', () {
        final originalGoal = Goal.create(
          userId: 'user-1',
          name: 'Date Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2024, 12, 31),
        );

        final newDate = DateTime(2025, 6, 30);
        final updatedGoal = originalGoal.copyWith(targetDate: newDate);

        expect(updatedGoal.targetDate, equals(newDate));
        expect(
          updatedGoal.targetAmountCents,
          equals(originalGoal.targetAmountCents),
        );
        expect(updatedGoal.name, equals(originalGoal.name));
      });

      test('should copy with updated name and description', () {
        final originalGoal = Goal.create(
          userId: 'user-1',
          name: 'Original Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2024, 12, 31),
        );

        final updatedGoal = originalGoal.copyWith(
          name: 'Updated Goal',
          description: 'Updated description',
        );

        expect(updatedGoal.name, equals('Updated Goal'));
        expect(updatedGoal.description, equals('Updated description'));
        expect(
          updatedGoal.targetAmountCents,
          equals(originalGoal.targetAmountCents),
        );
      });

      test('should copy with updated status', () {
        final originalGoal = Goal.create(
          userId: 'user-1',
          name: 'Status Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2024, 12, 31),
        );

        final updatedGoal = originalGoal.copyWith(status: GoalStatus.paused);

        expect(updatedGoal.status, equals(GoalStatus.paused));
        expect(updatedGoal.name, equals(originalGoal.name));
      });
    });

    group('Goal Equality', () {
      test('should be equal when all fields match', () {
        final goal1 = Goal(
          id: 'goal-1',
          userId: 'user-1',
          name: 'Equal Goal',
          targetAmountCents: 100000,
          currentAmountCents: 25000,
          targetDate: DateTime(2024, 12, 31),
          status: GoalStatus.active,
          isActive: true,
          schemaVersion: 1,
          createdAt: DateTime(2024, 1, 1),
          updatedAt: DateTime(2024, 1, 1),
          metadata: {},
        );

        final goal2 = Goal(
          id: 'goal-1',
          userId: 'user-1',
          name: 'Equal Goal',
          targetAmountCents: 100000,
          currentAmountCents: 25000,
          targetDate: DateTime(2024, 12, 31),
          status: GoalStatus.active,
          isActive: true,
          schemaVersion: 1,
          createdAt: DateTime(2024, 1, 1),
          updatedAt: DateTime(2024, 1, 1),
          metadata: {},
        );

        expect(goal1, equals(goal2));
        expect(goal1.hashCode, equals(goal2.hashCode));
      });

      test('should not be equal when current amounts differ', () {
        final goal1 = Goal.create(
          userId: 'user-1',
          name: 'Amount Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2024, 12, 31),
          currentAmountCents: 25000,
        );

        final goal2 = goal1.copyWith(currentAmountCents: 50000);

        expect(goal1, isNot(equals(goal2)));
      });

      test('should not be equal when target amounts differ', () {
        final goal1 = Goal.create(
          userId: 'user-1',
          name: 'Target Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2024, 12, 31),
        );

        final goal2 = goal1.copyWith(targetAmountCents: 150000);

        expect(goal1, isNot(equals(goal2)));
      });

      test('should not be equal when statuses differ', () {
        final goal1 = Goal.create(
          userId: 'user-1',
          name: 'Status Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2024, 12, 31),
          status: GoalStatus.active,
        );

        final goal2 = goal1.copyWith(status: GoalStatus.paused);

        expect(goal1, isNot(equals(goal2)));
      });
    });

    group('Goal Validation', () {
      test('should validate successfully with valid data', () {
        final goal = Goal.create(
          userId: 'user-1',
          name: 'Valid Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2025, 12, 31), // Future date
        );

        final errors = goal.validate();

        expect(errors, isEmpty);
        expect(goal.isValid, isTrue);
      });

      test('should fail validation with empty name', () {
        final goal = Goal.create(
          userId: 'user-1',
          name: '',
          targetAmountCents: 100000,
          targetDate: DateTime(2025, 12, 31),
        );

        final errors = goal.validate();

        expect(errors, isNotEmpty);
        expect(errors, contains('Goal name is required'));
        expect(goal.isValid, isFalse);
      });

      test('should fail validation with too short name', () {
        final goal = Goal.create(
          userId: 'user-1',
          name: 'A',
          targetAmountCents: 100000,
          targetDate: DateTime(2025, 12, 31),
        );

        final errors = goal.validate();

        expect(errors, isNotEmpty);
        expect(errors, contains('Goal name must be at least 2 characters'));
        expect(goal.isValid, isFalse);
      });

      test('should fail validation with too long name', () {
        final goal = Goal.create(
          userId: 'user-1',
          name: 'A' * 101, // 101 characters
          targetAmountCents: 100000,
          targetDate: DateTime(2025, 12, 31),
        );

        final errors = goal.validate();

        expect(errors, isNotEmpty);
        expect(errors, contains('Goal name must be 100 characters or less'));
        expect(goal.isValid, isFalse);
      });

      test('should fail validation with zero target amount', () {
        final goal = Goal.create(
          userId: 'user-1',
          name: 'Zero Target Goal',
          targetAmountCents: 0,
          targetDate: DateTime(2025, 12, 31),
        );

        final errors = goal.validate();

        expect(errors, isNotEmpty);
        expect(errors, contains('Goal target amount must be greater than 0'));
        expect(goal.isValid, isFalse);
      });

      test('should fail validation with negative current amount', () {
        final goal = Goal.create(
          userId: 'user-1',
          name: 'Negative Current Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2025, 12, 31),
          currentAmountCents: -1000,
        );

        final errors = goal.validate();

        expect(errors, isNotEmpty);
        expect(errors, contains('Goal current amount cannot be negative'));
        expect(goal.isValid, isFalse);
      });

      test('should fail validation with past target date', () {
        final goal = Goal.create(
          userId: 'user-1',
          name: 'Past Date Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2020, 1, 1), // Past date
        );

        final errors = goal.validate();

        expect(errors, isNotEmpty);
        expect(errors, contains('Goal target date cannot be in the past'));
        expect(goal.isValid, isFalse);
      });

      test('should fail validation with invalid color', () {
        final goal = Goal.create(
          userId: 'user-1',
          name: 'Invalid Color Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2025, 12, 31),
          colorHex: 'invalid-color',
        );

        final errors = goal.validate();

        expect(errors, isNotEmpty);
        expect(
          errors,
          contains('Goal color must be a valid hex color (e.g., #FF0000)'),
        );
        expect(goal.isValid, isFalse);
      });

      test('should pass validation with valid color', () {
        final goal = Goal.create(
          userId: 'user-1',
          name: 'Valid Color Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2025, 12, 31),
          colorHex: '#FF0000',
        );

        final errors = goal.validate();

        expect(errors, isEmpty);
        expect(goal.isValid, isTrue);
      });
    });

    group('Goal Utility Methods', () {
      test('should calculate progress percentage correctly', () {
        final goal = Goal.create(
          userId: 'user-1',
          name: 'Progress Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2025, 12, 31),
          currentAmountCents: 25000,
        );

        expect(goal.progressPercentage, equals(0.25));
      });

      test('should calculate remaining amount correctly', () {
        final goal = Goal.create(
          userId: 'user-1',
          name: 'Remaining Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2025, 12, 31),
          currentAmountCents: 30000,
        );

        expect(goal.remainingAmountCents, equals(70000));
      });

      test('should detect achieved goal', () {
        final goal = Goal.create(
          userId: 'user-1',
          name: 'Achieved Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2025, 12, 31),
          currentAmountCents: 100000,
        );

        expect(goal.isAchieved, isTrue);
      });

      test('should detect goal with deadline', () {
        final goalWithDeadline = Goal.create(
          userId: 'user-1',
          name: 'Deadline Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2025, 12, 31),
        );

        final goalWithoutDeadline = Goal.create(
          userId: 'user-1',
          name: 'No Deadline Goal',
          targetAmountCents: 100000,
        );

        expect(goalWithDeadline.hasDeadline, isTrue);
        expect(goalWithoutDeadline.hasDeadline, isFalse);
      });

      test('should get status string correctly', () {
        final activeGoal = Goal.create(
          userId: 'user-1',
          name: 'Active Goal',
          targetAmountCents: 100000,
          status: GoalStatus.active,
        );

        final pausedGoal = Goal.create(
          userId: 'user-1',
          name: 'Paused Goal',
          targetAmountCents: 100000,
          status: GoalStatus.paused,
        );

        final completedGoal = Goal.create(
          userId: 'user-1',
          name: 'Completed Goal',
          targetAmountCents: 100000,
          status: GoalStatus.completed,
        );

        final cancelledGoal = Goal.create(
          userId: 'user-1',
          name: 'Cancelled Goal',
          targetAmountCents: 100000,
          status: GoalStatus.cancelled,
        );

        expect(activeGoal.statusString, equals('Active'));
        expect(pausedGoal.statusString, equals('Paused'));
        expect(completedGoal.statusString, equals('Completed'));
        expect(cancelledGoal.statusString, equals('Cancelled'));
      });

      test('should get progress message correctly', () {
        final achievedGoal = Goal.create(
          userId: 'user-1',
          name: 'Achieved Goal',
          targetAmountCents: 100000,
          currentAmountCents: 100000,
        );

        final partialGoal = Goal.create(
          userId: 'user-1',
          name: 'Partial Goal',
          targetAmountCents: 100000,
          currentAmountCents: 25000,
        );

        expect(achievedGoal.progressMessage, equals('Goal achieved!'));
        expect(partialGoal.progressMessage, equals('25.0% complete'));
      });

      test('should update timestamp with updated method', () {
        final originalGoal = Goal.create(
          userId: 'user-1',
          name: 'Update Goal',
          targetAmountCents: 100000,
          targetDate: DateTime(2025, 12, 31),
        );

        final updatedGoal = originalGoal.updated();

        expect(updatedGoal.name, equals(originalGoal.name));
        expect(updatedGoal.userId, equals(originalGoal.userId));
        expect(
          updatedGoal.targetAmountCents,
          equals(originalGoal.targetAmountCents),
        );
        // updatedAt should be different (newer)
        expect(
          updatedGoal.updatedAt?.millisecondsSinceEpoch ?? 0,
          greaterThanOrEqualTo(
            originalGoal.updatedAt?.millisecondsSinceEpoch ?? 0,
          ),
        );
      });
    });
  });
}
