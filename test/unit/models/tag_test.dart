import 'package:budapp/data/models/tag.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Tag Model Tests', () {
    group('Tag Creation', () {
      test('should create tag with all required fields', () {
        final now = Timestamp.now();
        final tag = Tag(
          id: 'tag-1',
          userId: 'user-1',
          name: 'Test Tag',
          color: '#FF5722',
          usageCount: 5,
          schemaVersion: 1,
          createdAt: now,
          updatedAt: now,
        );

        expect(tag.id, equals('tag-1'));
        expect(tag.userId, equals('user-1'));
        expect(tag.name, equals('Test Tag'));
        expect(tag.color, equals('#FF5722'));
        expect(tag.usageCount, equals(5));
        expect(tag.schemaVersion, equals(1));
        expect(tag.createdAt, equals(now));
        expect(tag.updatedAt, equals(now));
      });

      test('should create tag with factory method', () {
        final tag = Tag.create(
          userId: 'user-1',
          name: 'Factory Tag',
          color: '#2196F3',
        );

        expect(tag.id, isEmpty); // Will be set by repository
        expect(tag.userId, equals('user-1'));
        expect(tag.name, equals('Factory Tag'));
        expect(tag.color, equals('#2196F3'));
        expect(tag.usageCount, equals(0)); // Default value
        expect(tag.schemaVersion, equals(1)); // Default value
        expect(tag.createdAt, isNotNull);
        expect(tag.updatedAt, isNotNull);
      });

      test('should create tag with default values', () {
        final tag = Tag.create(
          userId: 'user-1',
          name: 'Default Tag',
          color: '#9C27B0',
        );

        expect(tag.usageCount, equals(0));
        expect(tag.schemaVersion, equals(1));
      });
    });

    group('Tag JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final now = Timestamp.now();
        final tag = Tag(
          id: 'tag-1',
          userId: 'user-1',
          name: 'JSON Tag',
          color: '#FF9800',
          usageCount: 3,
          schemaVersion: 1,
          createdAt: now,
          updatedAt: now,
        );

        final json = tag.toJson();

        expect(json['id'], equals('tag-1'));
        expect(json['userId'], equals('user-1'));
        expect(json['name'], equals('JSON Tag'));
        expect(json['color'], equals('#FF9800'));
        expect(json['usageCount'], equals(3));
        expect(json['schemaVersion'], equals(1));
        expect(json['createdAt'], isA<Timestamp>());
        expect(json['updatedAt'], isA<Timestamp>());
      });

      test('should deserialize from JSON correctly', () {
        final now = Timestamp.now();
        final json = {
          'id': 'tag-1',
          'userId': 'user-1',
          'name': 'JSON Tag',
          'color': '#FF9800',
          'usageCount': 3,
          'schemaVersion': 1,
          'createdAt': now,
          'updatedAt': now,
        };

        final tag = Tag.fromJson(json);

        expect(tag.id, equals('tag-1'));
        expect(tag.userId, equals('user-1'));
        expect(tag.name, equals('JSON Tag'));
        expect(tag.color, equals('#FF9800'));
        expect(tag.usageCount, equals(3));
        expect(tag.schemaVersion, equals(1));
        expect(tag.createdAt, equals(now));
        expect(tag.updatedAt, equals(now));
      });

      test('should handle default values in JSON', () {
        final now = Timestamp.now();
        final json = {
          'id': 'tag-1',
          'userId': 'user-1',
          'name': 'Minimal Tag',
          'color': '#607D8B',
          'createdAt': now,
          'updatedAt': now,
        };

        final tag = Tag.fromJson(json);

        expect(tag.usageCount, equals(0)); // Default value
        expect(tag.schemaVersion, equals(1)); // Default value
      });
    });

    group('Tag CopyWith', () {
      test('should copy with updated name', () {
        final originalTag = Tag.create(
          userId: 'user-1',
          name: 'Original Tag',
          color: '#E91E63',
        );

        final updatedTag = originalTag.copyWith(name: 'Updated Tag');

        expect(updatedTag.name, equals('Updated Tag'));
        expect(updatedTag.userId, equals(originalTag.userId));
        expect(updatedTag.color, equals(originalTag.color));
        expect(updatedTag.id, equals(originalTag.id));
      });

      test('should copy with updated color', () {
        final originalTag = Tag.create(
          userId: 'user-1',
          name: 'Color Tag',
          color: '#3F51B5',
        );

        final updatedTag = originalTag.copyWith(color: '#009688');

        expect(updatedTag.color, equals('#009688'));
        expect(updatedTag.name, equals(originalTag.name));
        expect(updatedTag.userId, equals(originalTag.userId));
      });

      test('should copy with updated usage count', () {
        final originalTag = Tag.create(
          userId: 'user-1',
          name: 'Usage Tag',
          color: '#FF5722',
        );

        final updatedTag = originalTag.copyWith(usageCount: 10);

        expect(updatedTag.usageCount, equals(10));
        expect(updatedTag.name, equals(originalTag.name));
      });
    });

    group('Tag Equality', () {
      test('should be equal when all fields match', () {
        final now = Timestamp.now();
        final tag1 = Tag(
          id: 'tag-1',
          userId: 'user-1',
          name: 'Equal Tag',
          color: '#2196F3',
          usageCount: 5,
          schemaVersion: 1,
          createdAt: now,
          updatedAt: now,
        );

        final tag2 = Tag(
          id: 'tag-1',
          userId: 'user-1',
          name: 'Equal Tag',
          color: '#2196F3',
          usageCount: 5,
          schemaVersion: 1,
          createdAt: now,
          updatedAt: now,
        );

        expect(tag1, equals(tag2));
        expect(tag1.hashCode, equals(tag2.hashCode));
      });

      test('should not be equal when names differ', () {
        final tag1 = Tag.create(
          userId: 'user-1',
          name: 'Tag One',
          color: '#2196F3',
        );

        final tag2 = tag1.copyWith(name: 'Tag Two');

        expect(tag1, isNot(equals(tag2)));
      });

      test('should not be equal when colors differ', () {
        final tag1 = Tag.create(
          userId: 'user-1',
          name: 'Color Tag',
          color: '#2196F3',
        );

        final tag2 = tag1.copyWith(color: '#4CAF50');

        expect(tag1, isNot(equals(tag2)));
      });

      test('should not be equal when usage counts differ', () {
        final tag1 = Tag.create(
          userId: 'user-1',
          name: 'Usage Tag',
          color: '#2196F3',
        );

        final tag2 = tag1.copyWith(usageCount: 5);

        expect(tag1, isNot(equals(tag2)));
      });
    });

    group('Tag Validation', () {
      test('should validate successfully with valid data', () {
        final tag = Tag.create(
          userId: 'user-1',
          name: 'Valid Tag',
          color: '#2196F3',
        );

        final errors = tag.validate();

        expect(errors, isEmpty);
        expect(tag.isValid, isTrue);
      });

      test('should fail validation with empty name', () {
        final tag = Tag.create(userId: 'user-1', name: '', color: '#2196F3');

        final errors = tag.validate();

        expect(errors, isNotEmpty);
        expect(errors, contains('Tag name is required'));
        expect(tag.isValid, isFalse);
      });

      test('should fail validation with whitespace-only name', () {
        final tag = Tag.create(userId: 'user-1', name: '   ', color: '#2196F3');

        final errors = tag.validate();

        expect(errors, isNotEmpty);
        expect(errors, contains('Tag name is required'));
        expect(tag.isValid, isFalse);
      });

      test('should fail validation with too long name', () {
        final tag = Tag.create(
          userId: 'user-1',
          name: 'A' * 51, // 51 characters
          color: '#2196F3',
        );

        final errors = tag.validate();

        expect(errors, isNotEmpty);
        expect(errors, contains('Tag name must be 50 characters or less'));
        expect(tag.isValid, isFalse);
      });

      test('should fail validation with invalid color format', () {
        final tag = Tag.create(
          userId: 'user-1',
          name: 'Invalid Color Tag',
          color: 'invalid-color',
        );

        final errors = tag.validate();

        expect(errors, isNotEmpty);
        expect(errors, contains('Tag color must be a valid hex color'));
        expect(tag.isValid, isFalse);
      });

      test('should fail validation with empty color', () {
        final tag = Tag.create(
          userId: 'user-1',
          name: 'No Color Tag',
          color: '',
        );

        final errors = tag.validate();

        expect(errors, isNotEmpty);
        expect(errors, contains('Tag color is required'));
        expect(tag.isValid, isFalse);
      });

      test('should pass validation with valid hex colors', () {
        final validColors = [
          '#FF0000',
          '#00FF00',
          '#0000FF',
          '#FFFFFF',
          '#000000',
          '#123456',
          '#ABCDEF',
          '#abc', // 3-character hex
          '#ABC', // 3-character hex uppercase
        ];

        for (final color in validColors) {
          final tag = Tag.create(
            userId: 'user-1',
            name: 'Color Test',
            color: color,
          );

          expect(tag.isValid, isTrue, reason: 'Color $color should be valid');
        }
      });

      test('should fail validation with invalid hex colors', () {
        final invalidColors = [
          'FF0000', // Missing #
          '#FF00', // Too short (not 3 or 6)
          '#FF00000', // Too long
          '#GGGGGG', // Invalid hex characters
          'red', // Color name
          'rgb(255,0,0)', // RGB format
        ];

        for (final color in invalidColors) {
          final tag = Tag.create(
            userId: 'user-1',
            name: 'Color Test',
            color: color,
          );

          expect(
            tag.isValid,
            isFalse,
            reason: 'Color $color should be invalid',
          );
        }
      });

      test('should fail validation with negative usage count', () {
        final tag = Tag.create(
          userId: 'user-1',
          name: 'Usage Tag',
          color: '#2196F3',
        ).copyWith(usageCount: -1);

        final errors = tag.validate();

        expect(errors, isNotEmpty);
        expect(errors, contains('Usage count cannot be negative'));
        expect(tag.isValid, isFalse);
      });
    });

    group('Tag Utility Methods', () {
      test('should update timestamp with withUpdatedTimestamp', () {
        final originalTag = Tag.create(
          userId: 'user-1',
          name: 'Timestamp Tag',
          color: '#2196F3',
        );

        // Wait a bit to ensure different timestamp
        final updatedTag = originalTag.withUpdatedTimestamp();

        expect(updatedTag.name, equals(originalTag.name));
        expect(updatedTag.userId, equals(originalTag.userId));
        expect(updatedTag.color, equals(originalTag.color));
        // updatedAt should be different (newer)
        expect(
          updatedTag.updatedAt.millisecondsSinceEpoch,
          greaterThanOrEqualTo(originalTag.updatedAt.millisecondsSinceEpoch),
        );
      });

      test('should convert to Firestore format', () {
        final tag = Tag.create(
          userId: 'user-1',
          name: 'Firestore Tag',
          color: '#2196F3',
        );

        final firestoreData = tag.toFirestore();

        expect(firestoreData['userId'], equals('user-1'));
        expect(firestoreData['name'], equals('Firestore Tag'));
        expect(firestoreData['color'], equals('#2196F3'));
        expect(firestoreData['usageCount'], equals(0));
        expect(firestoreData['schemaVersion'], equals(1));
        expect(firestoreData['createdAt'], isA<Timestamp>());
        expect(firestoreData['updatedAt'], isA<Timestamp>());
        // Should not include 'id' field
        expect(firestoreData.containsKey('id'), isFalse);
      });
    });
  });
}
