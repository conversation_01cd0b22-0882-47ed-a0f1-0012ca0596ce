import 'package:budapp/data/models/category.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Category Firestore Integration Tests', () {
    late Category testCategory;

    setUp(() {
      testCategory = Category(
        id: 'test_category_id',
        userId: 'test_user_id',
        name: 'Test Category',
        type: CategoryType.expense,
        parentId: 'parent_category_id',
        description: 'Test category description',
        color: '#FF5722',
        icon: 'shopping_cart',
        isActive: true,
        sortOrder: 5,
        schemaVersion: 1,
        createdAt: DateTime(2025, 1, 1, 12, 0, 0),
        updatedAt: DateTime(2025, 1, 1, 12, 30, 0),
        metadata: {'customField': 'customValue'},
      );
    });

    group('toFirestore()', () {
      test('should convert Category to Firestore document data', () {
        final result = testCategory.toFirestore();

        expect(result['id'], equals('test_category_id'));
        expect(result['userId'], equals('test_user_id'));
        expect(result['name'], equals('Test Category'));
        expect(result['type'], equals('expense'));
        expect(result['parentId'], equals('parent_category_id'));
        expect(result['description'], equals('Test category description'));
        expect(result['color'], equals('#FF5722'));
        expect(result['icon'], equals('shopping_cart'));
        expect(result['isActive'], equals(true));
        expect(result['sortOrder'], equals(5));
        expect(result['schemaVersion'], equals(1));
        expect(result['metadata'], equals({'customField': 'customValue'}));

        // Check that DateTime fields are converted to Firestore Timestamps
        expect(result['createdAt'], isA<Timestamp>());
        expect(result['updatedAt'], isA<Timestamp>());
        expect(
          (result['createdAt'] as Timestamp).toDate(),
          equals(DateTime(2025, 1, 1, 12, 0, 0)),
        );
        expect(
          (result['updatedAt'] as Timestamp).toDate(),
          equals(DateTime(2025, 1, 1, 12, 30, 0)),
        );
      });

      test('should handle null optional fields correctly', () {
        final categoryWithNulls = Category(
          id: 'test_id',
          userId: 'test_user',
          name: 'Test',
          type: CategoryType.income,
          schemaVersion: 1,
          createdAt: DateTime(2025, 1, 1),
          updatedAt: DateTime(2025, 1, 1),
        );

        final result = categoryWithNulls.toFirestore();

        expect(result['parentId'], isNull);
        expect(result['description'], isNull);
        expect(result['color'], isNull);
        expect(result['icon'], isNull);
        expect(result['isActive'], equals(true)); // Default value
        expect(result['sortOrder'], equals(0)); // Default value
        expect(result['metadata'], equals({})); // Default value
      });
    });

    group('Timestamp Conversion', () {
      test('should convert DateTime to Firestore Timestamp correctly', () {
        final testDate = DateTime(2025, 1, 1, 12, 0, 0);
        final category = Category(
          id: 'test_id',
          userId: 'test_user',
          name: 'Test',
          type: CategoryType.income,
          schemaVersion: 1,
          createdAt: testDate,
          updatedAt: testDate,
        );

        final result = category.toFirestore();

        expect(result['createdAt'], isA<Timestamp>());
        expect(result['updatedAt'], isA<Timestamp>());

        final createdTimestamp = result['createdAt'] as Timestamp;
        final updatedTimestamp = result['updatedAt'] as Timestamp;

        expect(createdTimestamp.toDate(), equals(testDate));
        expect(updatedTimestamp.toDate(), equals(testDate));
      });

      test('should handle round-trip conversion through JSON', () {
        // Test the conversion logic that fromFirestore would use
        final firestoreData = testCategory.toFirestore();

        // Simulate what fromFirestore does - convert Timestamps to ISO strings
        final jsonData = Map<String, dynamic>.from(firestoreData);
        jsonData['createdAt'] = (firestoreData['createdAt'] as Timestamp)
            .toDate()
            .toIso8601String();
        jsonData['updatedAt'] = (firestoreData['updatedAt'] as Timestamp)
            .toDate()
            .toIso8601String();

        final roundTripCategory = Category.fromJson(jsonData);

        expect(roundTripCategory.id, equals(testCategory.id));
        expect(roundTripCategory.name, equals(testCategory.name));
        expect(roundTripCategory.type, equals(testCategory.type));
        expect(roundTripCategory.createdAt, equals(testCategory.createdAt));
        expect(roundTripCategory.updatedAt, equals(testCategory.updatedAt));
      });
    });

    group('Data Type Validation', () {
      test('should ensure proper enum serialization', () {
        final result = testCategory.toFirestore();

        expect(result['type'], equals('expense'));

        // Verify these are strings, not enum objects
        expect(result['type'], isA<String>());
      });

      test('should handle metadata as Map<String, dynamic>', () {
        final result = testCategory.toFirestore();

        expect(result['metadata'], isA<Map<String, dynamic>>());
        expect(result['metadata'], equals({'customField': 'customValue'}));
      });
    });

    group('Firestore Timestamp Conversion Logic', () {
      test('should handle Firestore Timestamp conversion logic', () {
        // Test the timestamp conversion logic that fromFirestore uses
        final testDate = DateTime(2025, 1, 1, 12, 0, 0);
        final timestamp = Timestamp.fromDate(testDate);

        final mockData = {
          'id': 'test-id',
          'userId': 'user-123',
          'name': 'Test Category',
          'type': 'expense',
          'schemaVersion': 1,
          'createdAt': timestamp,
          'updatedAt': timestamp,
          'isActive': true,
          'sortOrder': 0,
          'metadata': <String, dynamic>{},
        };

        // Simulate the conversion logic from fromFirestore
        final convertedData = Map<String, dynamic>.from(mockData);

        // Convert Firestore Timestamps to DateTime (as fromFirestore does)
        if (convertedData['createdAt'] is Timestamp) {
          convertedData['createdAt'] = (convertedData['createdAt'] as Timestamp)
              .toDate()
              .toIso8601String();
        }
        if (convertedData['updatedAt'] is Timestamp) {
          convertedData['updatedAt'] = (convertedData['updatedAt'] as Timestamp)
              .toDate()
              .toIso8601String();
        }

        final category = Category.fromJson(convertedData);

        expect(category.id, equals('test-id'));
        expect(category.name, equals('Test Category'));
        expect(category.createdAt, equals(testDate));
        expect(category.updatedAt, equals(testDate));
      });

      test('should handle mixed Timestamp and string dates', () {
        final testDate = DateTime(2025, 1, 1, 12, 0, 0);
        final timestamp = Timestamp.fromDate(testDate);

        final mockData = {
          'id': 'test-id',
          'userId': 'user-123',
          'name': 'Test Category',
          'type': 'expense',
          'schemaVersion': 1,
          'createdAt': timestamp, // Firestore Timestamp
          'updatedAt': testDate.toIso8601String(), // Already converted string
          'isActive': true,
          'sortOrder': 0,
          'metadata': <String, dynamic>{},
        };

        // Simulate the conversion logic from fromFirestore
        final convertedData = Map<String, dynamic>.from(mockData);

        if (convertedData['createdAt'] is Timestamp) {
          convertedData['createdAt'] = (convertedData['createdAt'] as Timestamp)
              .toDate()
              .toIso8601String();
        }
        if (convertedData['updatedAt'] is Timestamp) {
          convertedData['updatedAt'] = (convertedData['updatedAt'] as Timestamp)
              .toDate()
              .toIso8601String();
        }

        final category = Category.fromJson(convertedData);

        expect(category.createdAt, equals(testDate));
        expect(category.updatedAt, equals(testDate));
      });

      test('should handle document ID override logic', () {
        final mockData = {
          'id': 'wrong-id', // This should be overridden
          'userId': 'user-123',
          'name': 'Test Category',
          'type': 'expense',
          'schemaVersion': 1,
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
          'isActive': true,
          'sortOrder': 0,
          'metadata': <String, dynamic>{},
        };

        // Simulate the ID override logic from fromFirestore
        final convertedData = Map<String, dynamic>.from(mockData);
        convertedData['id'] = 'correct-id'; // Document ID override

        final category = Category.fromJson(convertedData);

        expect(category.id, equals('correct-id')); // Should use document ID
      });

      test('should handle complex metadata conversion', () {
        final complexMetadata = {
          'nested': {'key': 'value'},
          'array': [1, 2, 3],
          'string': 'test',
          'number': 42,
          'boolean': true,
        };

        final mockData = {
          'id': 'test-id',
          'userId': 'user-123',
          'name': 'Test Category',
          'type': 'expense',
          'schemaVersion': 1,
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
          'isActive': true,
          'sortOrder': 0,
          'metadata': complexMetadata,
        };

        final category = Category.fromJson(mockData);

        expect(category.metadata, equals(complexMetadata));
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle toFirestore with already converted Timestamps', () {
        // Test case where DateTime fields are already Timestamps (shouldn't happen but defensive)
        final category = testCategory;
        final firestoreData = category.toFirestore();

        // Verify conversion happened correctly
        expect(firestoreData['createdAt'], isA<Timestamp>());
        expect(firestoreData['updatedAt'], isA<Timestamp>());
      });

      test('should handle fromJson with invalid metadata types', () {
        final invalidMetadataData = {
          'id': 'test-id',
          'userId': 'user-123',
          'name': 'Test Category',
          'type': 'expense',
          'schemaVersion': 1,
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
          'isActive': true,
          'sortOrder': 0,
          'metadata': 'invalid-string', // Should be converted to empty map
        };

        final category = Category.fromJson(invalidMetadataData);
        expect(category.metadata, equals(<String, dynamic>{}));
      });

      test('should handle fromJson with nested Map metadata', () {
        final nestedMetadata = {
          'level1': {
            'level2': {'value': 'deep'},
          },
        };

        final dataWithNestedMetadata = {
          'id': 'test-id',
          'userId': 'user-123',
          'name': 'Test Category',
          'type': 'expense',
          'schemaVersion': 1,
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
          'isActive': true,
          'sortOrder': 0,
          'metadata': nestedMetadata,
        };

        final category = Category.fromJson(dataWithNestedMetadata);
        expect(category.metadata, equals(nestedMetadata));
      });

      test('should handle toFirestore with empty metadata', () {
        final categoryWithEmptyMetadata = Category(
          id: 'test-id',
          userId: 'user-123',
          name: 'Test Category',
          type: CategoryType.expense,
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          metadata: <String, dynamic>{},
        );

        final result = categoryWithEmptyMetadata.toFirestore();
        expect(result['metadata'], equals(<String, dynamic>{}));
      });

      test('should handle toFirestore with large metadata', () {
        final largeMetadata = <String, dynamic>{};
        for (var i = 0; i < 100; i++) {
          largeMetadata['key$i'] = 'value$i';
        }

        final categoryWithLargeMetadata = Category(
          id: 'test-id',
          userId: 'user-123',
          name: 'Test Category',
          type: CategoryType.expense,
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          metadata: largeMetadata,
        );

        final result = categoryWithLargeMetadata.toFirestore();
        expect(result['metadata'], equals(largeMetadata));
        expect((result['metadata'] as Map).length, equals(100));
      });
    });
  });
}

// Note: fromFirestore() testing with DocumentSnapshot requires
// integration tests with actual Firestore instances, as DocumentSnapshot
// is sealed and cannot be mocked in unit tests. The tests above focus on
// the conversion logic that fromFirestore uses internally.
