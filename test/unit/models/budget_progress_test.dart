import 'package:budapp/data/models/budget_progress.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('BudgetProgress Model Tests', () {
    group('Factory Constructors', () {
      test('should create budget progress with main constructor', () {
        const progress = BudgetProgress(
          budgetId: 'test-budget-1',
          spent: 100,
          remaining: 400,
          percentage: 20,
          statusColor: Colors.green,
          isOverBudget: false,
          status: BudgetProgressStatus.onTrack,
        );

        expect(progress.budgetId, equals('test-budget-1'));
        expect(progress.spent, equals(100));
        expect(progress.remaining, equals(400));
        expect(progress.percentage, equals(20));
        expect(progress.statusColor, equals(Colors.green));
        expect(progress.isOverBudget, isFalse);
        expect(progress.status, equals(BudgetProgressStatus.onTrack));
      });

      test(
        'should create budget progress from budget and spent (on track)',
        () {
          final progress = BudgetProgress.fromBudgetAndSpent(
            budgetId: 'test-budget-1',
            budgetAmount: 1000,
            spentAmount: 250, // 25% - on track
          );

          expect(progress.budgetId, equals('test-budget-1'));
          expect(progress.spent, equals(250));
          expect(progress.remaining, equals(750));
          expect(progress.percentage, equals(25));
          expect(progress.statusColor, equals(Colors.green));
          expect(progress.isOverBudget, isFalse);
          expect(progress.status, equals(BudgetProgressStatus.onTrack));
        },
      );

      test('should create budget progress from budget and spent (warning)', () {
        final progress = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test-budget-2',
          budgetAmount: 500,
          spentAmount: 450, // 90% - warning
        );

        expect(progress.budgetId, equals('test-budget-2'));
        expect(progress.spent, equals(450));
        expect(progress.remaining, equals(50));
        expect(progress.percentage, equals(90));
        expect(progress.statusColor, equals(Colors.orange));
        expect(progress.isOverBudget, isFalse);
        expect(progress.status, equals(BudgetProgressStatus.warning));
      });

      test(
        'should create budget progress from budget and spent (over budget)',
        () {
          final progress = BudgetProgress.fromBudgetAndSpent(
            budgetId: 'test-budget-3',
            budgetAmount: 200,
            spentAmount: 300, // 150% - over budget
          );

          expect(progress.budgetId, equals('test-budget-3'));
          expect(progress.spent, equals(300));
          expect(progress.remaining, equals(-100));
          expect(progress.percentage, equals(150));
          expect(progress.statusColor, equals(Colors.red));
          expect(progress.isOverBudget, isTrue);
          expect(progress.status, equals(BudgetProgressStatus.overBudget));
        },
      );

      test(
        'should handle negative spent amounts by converting to absolute value',
        () {
          final progress = BudgetProgress.fromBudgetAndSpent(
            budgetId: 'test-budget-4',
            budgetAmount: 1000,
            spentAmount: -300, // Should become 300.0
          );

          expect(progress.spent, equals(300));
          expect(progress.remaining, equals(700));
          expect(progress.percentage, equals(30));
          expect(progress.status, equals(BudgetProgressStatus.onTrack));
        },
      );

      test('should handle zero budget amount', () {
        final progress = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test-budget-5',
          budgetAmount: 0,
          spentAmount: 100,
        );

        expect(progress.spent, equals(100));
        expect(progress.remaining, equals(-100));
        expect(progress.percentage, equals(0));
        expect(progress.isOverBudget, isTrue);
      });

      test('should create empty budget progress', () {
        final progress = BudgetProgress.empty('test-budget-6', 500);

        expect(progress.budgetId, equals('test-budget-6'));
        expect(progress.spent, equals(0));
        expect(progress.remaining, equals(500));
        expect(progress.percentage, equals(0));
        expect(progress.statusColor, equals(Colors.green));
        expect(progress.isOverBudget, isFalse);
        expect(progress.status, equals(BudgetProgressStatus.onTrack));
      });
    });

    group('Status Logic', () {
      test('should assign on track status for percentages <= 75%', () {
        final progress75 = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 75, // Exactly 75%
        );

        final progress50 = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 50, // 50%
        );

        expect(progress75.status, equals(BudgetProgressStatus.onTrack));
        expect(progress75.statusColor, equals(Colors.green));
        expect(progress50.status, equals(BudgetProgressStatus.onTrack));
        expect(progress50.statusColor, equals(Colors.green));
      });

      test('should assign warning status for percentages 75-100%', () {
        final progress76 = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 76, // 76%
        );

        final progress100 = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 100, // Exactly 100%
        );

        expect(progress76.status, equals(BudgetProgressStatus.warning));
        expect(progress76.statusColor, equals(Colors.orange));
        expect(progress100.status, equals(BudgetProgressStatus.warning));
        expect(progress100.statusColor, equals(Colors.orange));
      });

      test('should assign over budget status for percentages > 100%', () {
        final progress120 = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 120, // 120%
        );

        final progress200 = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 200, // 200%
        );

        expect(progress120.status, equals(BudgetProgressStatus.overBudget));
        expect(progress120.statusColor, equals(Colors.red));
        expect(progress200.status, equals(BudgetProgressStatus.overBudget));
        expect(progress200.statusColor, equals(Colors.red));
      });
    });

    group('Extension Methods', () {
      test('progressValue should clamp percentage to 0-1 range', () {
        final progress50 = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 50, // 50%
        );

        final progress150 = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 150, // 150%
        );

        final progressZero = BudgetProgress.empty('test', 100); // 0%

        expect(progress50.progressValue, equals(0.5));
        expect(progress150.progressValue, equals(1)); // Clamped to 1
        expect(progressZero.progressValue, equals(0));
      });

      test('overflowPercentage should calculate overflow correctly', () {
        final progress80 = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 80, // 80% - no overflow
        );

        final progress150 = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 150, // 150% - 50% overflow
        );

        final progress200 = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 200, // 200% - 100% overflow
        );

        expect(progress80.overflowPercentage, equals(0));
        expect(progress150.overflowPercentage, equals(50));
        expect(progress200.overflowPercentage, equals(100));
      });

      test('statusMessage should return correct message for each status', () {
        final onTrack = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 50,
        );

        final warning = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 90,
        );

        final overBudget = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 150,
        );

        expect(onTrack.statusMessage, equals('On track'));
        expect(warning.statusMessage, equals('Approaching limit'));
        expect(overBudget.statusMessage, equals('Over budget'));
      });

      test('percentageString should format percentage correctly', () {
        final progress = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 33.33,
        );

        final progressWhole = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 75,
        );

        expect(progress.percentageString, equals('33.3%'));
        expect(progressWhole.percentageString, equals('75.0%'));
      });

      test(
        'remainingString should format remaining amount with proper signs',
        () {
          final positive = BudgetProgress.fromBudgetAndSpent(
            budgetId: 'test',
            budgetAmount: 100,
            spentAmount: 30, // +70 remaining
          );

          final negative = BudgetProgress.fromBudgetAndSpent(
            budgetId: 'test',
            budgetAmount: 100,
            spentAmount: 150, // -50 remaining
          );

          final zero = BudgetProgress.fromBudgetAndSpent(
            budgetId: 'test',
            budgetAmount: 100,
            spentAmount: 100, // 0 remaining
          );

          expect(positive.remainingString, equals('+70.00'));
          expect(negative.remainingString, equals('-50.00'));
          expect(zero.remainingString, equals('+0.00'));
        },
      );

      test('spentString should format spent amount correctly', () {
        final progress = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 33.456, // Should round to 2 decimal places
        );

        final progressWhole = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 50,
        );

        expect(progress.spentString, equals('33.46'));
        expect(progressWhole.spentString, equals('50.00'));
      });

      test('isCritical should detect critical states correctly', () {
        final safe = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 80, // 80% - not critical
        );

        final almostCritical = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 94, // 94% - not quite critical
        );

        final critical95 = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 95, // 95% - critical
        );

        final critical98 = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 98, // 98% - critical
        );

        final overBudget = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 120, // 120% - critical (over budget)
        );

        expect(safe.isCritical, isFalse);
        expect(almostCritical.isCritical, isFalse);
        expect(critical95.isCritical, isTrue);
        expect(critical98.isCritical, isTrue);
        expect(overBudget.isCritical, isTrue);
      });

      test('statusIcon should return correct icons for each status', () {
        final onTrack = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 50,
        );

        final warning = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 90,
        );

        final overBudget = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 100,
          spentAmount: 150,
        );

        expect(onTrack.statusIcon, equals(Icons.check_circle));
        expect(warning.statusIcon, equals(Icons.warning));
        expect(overBudget.statusIcon, equals(Icons.error));
      });
    });

    group('Edge Cases', () {
      test('should handle very large percentages', () {
        final progress = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 1,
          spentAmount: 10000, // 1,000,000%
        );

        expect(progress.percentage, equals(1000000));
        expect(progress.progressValue, equals(1)); // Clamped
        expect(progress.overflowPercentage, equals(999900));
        expect(progress.status, equals(BudgetProgressStatus.overBudget));
        expect(progress.isCritical, isTrue);
      });

      test('should handle zero spent amount', () {
        final progress = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 500,
          spentAmount: 0,
        );

        expect(progress.spent, equals(0));
        expect(progress.remaining, equals(500));
        expect(progress.percentage, equals(0));
        expect(progress.progressValue, equals(0));
        expect(progress.overflowPercentage, equals(0));
        expect(progress.status, equals(BudgetProgressStatus.onTrack));
        expect(progress.isCritical, isFalse);
      });

      test('should handle very small amounts', () {
        final progress = BudgetProgress.fromBudgetAndSpent(
          budgetId: 'test',
          budgetAmount: 0.01,
          spentAmount: 0.005, // 50%
        );

        expect(progress.spent, equals(0.005));
        expect(progress.remaining, equals(0.005));
        expect(progress.percentage, equals(50));
        expect(progress.status, equals(BudgetProgressStatus.onTrack));
      });
    });
  });
}
