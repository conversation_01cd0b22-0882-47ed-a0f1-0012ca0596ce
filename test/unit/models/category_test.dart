import 'package:budapp/data/models/category.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Category Model Tests', () {
    late DateTime testDate;
    late Map<String, dynamic> mockFirestoreData;

    setUp(() {
      testDate = DateTime(2024, 1, 15, 10, 30);
      mockFirestoreData = {
        'id': 'test-category-id',
        'userId': 'user-123',
        'name': 'Test Category',
        'type': 'expense',
        'parentId': null,
        'description': 'Test description',
        'color': '#FF5722',
        'icon': 'food',
        'isActive': true,
        'sortOrder': 0,
        'schemaVersion': 1,
        'createdAt': testDate.toIso8601String(),
        'updatedAt': testDate.toIso8601String(),
        'metadata': {'key': 'value'},
      };
    });

    group('Category Creation', () {
      test('should create category with all required fields', () {
        final category = Category(
          id: 'test-id',
          userId: 'user-123',
          name: 'Food',
          type: CategoryType.expense,
          schemaVersion: 1,
          createdAt: testDate,
          updatedAt: testDate,
        );

        expect(category.id, equals('test-id'));
        expect(category.userId, equals('user-123'));
        expect(category.name, equals('Food'));
        expect(category.type, equals(CategoryType.expense));
        expect(category.schemaVersion, equals(1));
        expect(category.createdAt, equals(testDate));
        expect(category.updatedAt, equals(testDate));
        expect(category.isActive, isTrue);
        expect(category.sortOrder, equals(0));
        expect(category.metadata, isEmpty);
      });

      test('should create category with factory method', () {
        final category = Category.create(
          userId: 'user-123',
          name: 'Transportation',
          type: CategoryType.expense,
          description: 'Transport costs',
          color: '#2196F3',
          icon: 'car',
          sortOrder: 5,
        );

        expect(category.id, isEmpty);
        expect(category.userId, equals('user-123'));
        expect(category.name, equals('Transportation'));
        expect(category.type, equals(CategoryType.expense));
        expect(category.description, equals('Transport costs'));
        expect(category.color, equals('#2196F3'));
        expect(category.icon, equals('car'));
        expect(category.sortOrder, equals(5));
        expect(category.schemaVersion, equals(1));
        expect(category.isActive, isTrue);
      });

      test('should create category with optional fields', () {
        final category = Category.create(
          userId: 'user-123',
          name: 'Salary',
          type: CategoryType.income,
          parentId: 'parent-id',
        );

        expect(category.parentId, equals('parent-id'));
        expect(category.description, isNull);
        expect(category.color, isNull);
        expect(category.icon, isNull);
        expect(category.sortOrder, equals(0));
      });

      test('should create income category', () {
        final category = Category.create(
          userId: 'user-123',
          name: 'Salary',
          type: CategoryType.income,
        );

        expect(category.type, equals(CategoryType.income));
      });
    });

    group('Category JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final category = Category(
          id: 'test-id',
          userId: 'user-123',
          name: 'Food',
          type: CategoryType.expense,
          description: 'Food expenses',
          color: '#FF5722',
          icon: 'restaurant',
          parentId: 'parent-id',
          sortOrder: 1,
          schemaVersion: 1,
          createdAt: testDate,
          updatedAt: testDate,
          metadata: {'custom': 'data'},
        );

        final json = category.toJson();

        expect(json['id'], equals('test-id'));
        expect(json['userId'], equals('user-123'));
        expect(json['name'], equals('Food'));
        expect(json['type'], equals('expense'));
        expect(json['description'], equals('Food expenses'));
        expect(json['color'], equals('#FF5722'));
        expect(json['icon'], equals('restaurant'));
        expect(json['parentId'], equals('parent-id'));
        expect(json['sortOrder'], equals(1));
        expect(json['schemaVersion'], equals(1));
        expect(json['createdAt'], equals(testDate.toIso8601String()));
        expect(json['updatedAt'], equals(testDate.toIso8601String()));
        expect(json['metadata'], equals({'custom': 'data'}));
        expect(json['isActive'], isTrue);
      });

      test('should deserialize from JSON correctly', () {
        final category = Category.fromJson(mockFirestoreData);

        expect(category.id, equals('test-category-id'));
        expect(category.userId, equals('user-123'));
        expect(category.name, equals('Test Category'));
        expect(category.type, equals(CategoryType.expense));
        expect(category.description, equals('Test description'));
        expect(category.color, equals('#FF5722'));
        expect(category.icon, equals('food'));
        expect(category.isActive, isTrue);
        expect(category.sortOrder, equals(0));
        expect(category.schemaVersion, equals(1));
        expect(category.createdAt, equals(testDate));
        expect(category.updatedAt, equals(testDate));
        expect(category.metadata, equals({'key': 'value'}));
      });

      test('should handle missing optional fields in JSON', () {
        final minimalData = {
          'id': 'test-id',
          'userId': 'user-123',
          'name': 'Simple Category',
          'type': 'income',
          'schemaVersion': 1,
          'createdAt': testDate.toIso8601String(),
          'updatedAt': testDate.toIso8601String(),
          'isActive': true,
          'sortOrder': 0,
        };

        final category = Category.fromJson(minimalData);

        expect(category.parentId, isNull);
        expect(category.description, isNull);
        expect(category.color, isNull);
        expect(category.icon, isNull);
        expect(category.isActive, isTrue);
        expect(category.sortOrder, equals(0));
        expect(category.metadata, isEmpty);
      });

      test('should handle null metadata in JSON', () {
        final dataWithNullMetadata = Map<String, dynamic>.from(
          mockFirestoreData,
        );
        dataWithNullMetadata['metadata'] = null;

        final category = Category.fromJson(dataWithNullMetadata);

        expect(category.metadata, isEmpty);
      });

      test('should handle non-Map metadata in JSON', () {
        final dataWithStringMetadata = Map<String, dynamic>.from(
          mockFirestoreData,
        );
        dataWithStringMetadata['metadata'] = 'invalid';

        final category = Category.fromJson(dataWithStringMetadata);

        expect(category.metadata, isEmpty);
      });
    });

    group('Category Firestore Integration', () {
      test('should convert to Firestore format correctly', () {
        final category = Category(
          id: 'test-id',
          userId: 'user-123',
          name: 'Food',
          type: CategoryType.expense,
          schemaVersion: 1,
          createdAt: testDate,
          updatedAt: testDate,
          metadata: {'custom': 'data'},
        );

        final firestoreData = category.toFirestore();

        expect(firestoreData['id'], equals('test-id'));
        expect(firestoreData['userId'], equals('user-123'));
        expect(firestoreData['name'], equals('Food'));
        expect(firestoreData['type'], equals('expense'));
        expect(firestoreData['schemaVersion'], equals(1));
        expect(firestoreData['createdAt'], isA<Timestamp>());
        expect(firestoreData['updatedAt'], isA<Timestamp>());
        expect(firestoreData['metadata'], equals({'custom': 'data'}));
        expect(firestoreData['isActive'], isTrue);
        expect(firestoreData['sortOrder'], equals(0));

        // Verify timestamp conversion
        final createdTimestamp = firestoreData['createdAt'] as Timestamp;
        final updatedTimestamp = firestoreData['updatedAt'] as Timestamp;
        expect(createdTimestamp.toDate(), equals(testDate));
        expect(updatedTimestamp.toDate(), equals(testDate));
      });

      test('should handle DateTime objects in toFirestore', () {
        final category = Category(
          id: 'test-id',
          userId: 'user-123',
          name: 'Food',
          type: CategoryType.expense,
          schemaVersion: 1,
          createdAt: testDate,
          updatedAt: testDate,
        );

        final firestoreData = category.toFirestore();

        expect(firestoreData['createdAt'], isA<Timestamp>());
        expect(firestoreData['updatedAt'], isA<Timestamp>());
      });
    });

    group('Category Utility Methods', () {
      test('should identify root category correctly', () {
        final rootCategory = Category.create(
          userId: 'user-123',
          name: 'Root Category',
          type: CategoryType.expense,
        );

        expect(rootCategory.isRoot, isTrue);
        expect(rootCategory.isSubcategory, isFalse);
      });

      test('should identify subcategory correctly', () {
        final subcategory = Category.create(
          userId: 'user-123',
          name: 'Subcategory',
          type: CategoryType.expense,
          parentId: 'parent-id',
        );

        expect(subcategory.isSubcategory, isTrue);
        expect(subcategory.isRoot, isFalse);
      });

      test('should update timestamp with updated method', () {
        final category = Category.create(
          userId: 'user-123',
          name: 'Test Category',
          type: CategoryType.expense,
        );

        final originalUpdatedAt = category.updatedAt;

        // Wait a small amount to ensure timestamp difference
        Future<void>.delayed(const Duration(milliseconds: 1));

        final updatedCategory = category.updated();

        expect(
          updatedCategory.updatedAt?.isAfter(
                originalUpdatedAt ?? DateTime.now(),
              ) ??
              false,
          isTrue,
        );
        expect(updatedCategory.name, equals(category.name));
        expect(updatedCategory.id, equals(category.id));
      });
    });

    group('Category CopyWith', () {
      late Category baseCategory;

      setUp(() {
        baseCategory = Category.create(
          userId: 'user-123',
          name: 'Original Name',
          type: CategoryType.expense,
          description: 'Original description',
          color: '#FF0000',
        );
      });

      test('should copy with updated name', () {
        final updated = baseCategory.copyWith(name: 'New Name');

        expect(updated.name, equals('New Name'));
        expect(updated.userId, equals(baseCategory.userId));
        expect(updated.type, equals(baseCategory.type));
        expect(updated.description, equals(baseCategory.description));
      });

      test('should copy with updated type', () {
        final updated = baseCategory.copyWith(type: CategoryType.income);

        expect(updated.type, equals(CategoryType.income));
        expect(updated.name, equals(baseCategory.name));
      });

      test('should copy with updated parent', () {
        final updated = baseCategory.copyWith(parentId: 'new-parent');

        expect(updated.parentId, equals('new-parent'));
        expect(updated.name, equals(baseCategory.name));
      });

      test('should copy with updated metadata', () {
        final newMetadata = {'new': 'data'};
        final updated = baseCategory.copyWith(metadata: newMetadata);

        expect(updated.metadata, equals(newMetadata));
        expect(updated.name, equals(baseCategory.name));
      });
    });

    group('Category Equality', () {
      test('should be equal when all fields match', () {
        final category1 = Category(
          id: 'test-id',
          userId: 'user-123',
          name: 'Food',
          type: CategoryType.expense,
          schemaVersion: 1,
          createdAt: testDate,
          updatedAt: testDate,
        );

        final category2 = Category(
          id: 'test-id',
          userId: 'user-123',
          name: 'Food',
          type: CategoryType.expense,
          schemaVersion: 1,
          createdAt: testDate,
          updatedAt: testDate,
        );

        expect(category1, equals(category2));
        expect(category1.hashCode, equals(category2.hashCode));
      });

      test('should not be equal when names differ', () {
        final category1 = Category.create(
          userId: 'user-123',
          name: 'Food',
          type: CategoryType.expense,
        );

        final category2 = category1.copyWith(name: 'Transportation');

        expect(category1, isNot(equals(category2)));
      });

      test('should not be equal when types differ', () {
        final category1 = Category.create(
          userId: 'user-123',
          name: 'Food',
          type: CategoryType.expense,
        );

        final category2 = category1.copyWith(type: CategoryType.income);

        expect(category1, isNot(equals(category2)));
      });

      test('should not be equal when user IDs differ', () {
        final category1 = Category.create(
          userId: 'user-123',
          name: 'Food',
          type: CategoryType.expense,
        );

        final category2 = category1.copyWith(userId: 'user-456');

        expect(category1, isNot(equals(category2)));
      });
    });
  });

  group('CategoryTree Model Tests', () {
    late Category rootCategory;
    late Category childCategory1;
    late Category childCategory2;
    late Category grandchildCategory;

    setUp(() {
      rootCategory = Category.create(
        userId: 'user-123',
        name: 'Root',
        type: CategoryType.expense,
      );

      childCategory1 = Category.create(
        userId: 'user-123',
        name: 'Child 1',
        type: CategoryType.expense,
        parentId: 'root-id',
      );

      childCategory2 = Category.create(
        userId: 'user-123',
        name: 'Child 2',
        type: CategoryType.expense,
        parentId: 'root-id',
      );

      grandchildCategory = Category.create(
        userId: 'user-123',
        name: 'Grandchild',
        type: CategoryType.expense,
        parentId: 'child1-id',
      );
    });

    group('CategoryTree Creation', () {
      test('should create empty tree node', () {
        final tree = CategoryTree(category: rootCategory);

        expect(tree.category, equals(rootCategory));
        expect(tree.children, isEmpty);
        expect(tree.depth, equals(0));
        expect(tree.hasChildren, isFalse);
      });

      test('should create tree node with children', () {
        final childTrees = [
          CategoryTree(category: childCategory1, depth: 1),
          CategoryTree(category: childCategory2, depth: 1),
        ];

        final tree = CategoryTree(category: rootCategory, children: childTrees);

        expect(tree.hasChildren, isTrue);
        expect(tree.children.length, equals(2));
        expect(tree.children[0].category, equals(childCategory1));
        expect(tree.children[1].category, equals(childCategory2));
      });

      test('should create tree node with custom depth', () {
        final tree = CategoryTree(category: childCategory1, depth: 2);

        expect(tree.depth, equals(2));
      });
    });

    group('CategoryTree Hierarchy Operations', () {
      test('should get all descendants correctly', () {
        final grandchildTree = CategoryTree(
          category: grandchildCategory,
          depth: 2,
        );

        final child1Tree = CategoryTree(
          category: childCategory1,
          children: [grandchildTree],
          depth: 1,
        );

        final child2Tree = CategoryTree(category: childCategory2, depth: 1);

        final rootTree = CategoryTree(
          category: rootCategory,
          children: [child1Tree, child2Tree],
        );

        final descendants = rootTree.getAllDescendants();

        expect(descendants.length, equals(3));
        expect(descendants[0], equals(childCategory1));
        expect(descendants[1], equals(grandchildCategory));
        expect(descendants[2], equals(childCategory2));
      });

      test('should calculate total count correctly', () {
        final grandchildTree = CategoryTree(
          category: grandchildCategory,
          depth: 2,
        );

        final child1Tree = CategoryTree(
          category: childCategory1,
          children: [grandchildTree],
          depth: 1,
        );

        final child2Tree = CategoryTree(category: childCategory2, depth: 1);

        final rootTree = CategoryTree(
          category: rootCategory,
          children: [child1Tree, child2Tree],
        );

        expect(
          rootTree.totalCount,
          equals(4),
        ); // root + 2 children + 1 grandchild
        expect(child1Tree.totalCount, equals(2)); // child1 + grandchild
        expect(child2Tree.totalCount, equals(1)); // child2 only
        expect(grandchildTree.totalCount, equals(1)); // grandchild only
      });

      test('should handle empty descendants', () {
        final leafTree = CategoryTree(category: childCategory1);

        final descendants = leafTree.getAllDescendants();
        expect(descendants, isEmpty);
        expect(leafTree.totalCount, equals(1));
      });
    });

    group('CategoryTree Structure', () {
      test('should create tree structure correctly', () {
        final childTree = CategoryTree(
          category: childCategory1,
          depth: 1,
        );

        final tree = CategoryTree(
          category: rootCategory,
          children: [childTree],
          depth: 0,
        );

        expect(tree.category.name, equals(rootCategory.name));
        expect(tree.depth, equals(0));
        expect(tree.children.length, equals(1));
        expect(tree.children[0].category.name, equals(childCategory1.name));
        expect(tree.children[0].depth, equals(1));
      });

      test('should handle empty children correctly', () {
        final tree = CategoryTree(
          category: rootCategory,
          children: [],
          depth: 0,
        );

        expect(tree.category.name, equals(rootCategory.name));
        expect(tree.children, isEmpty);
        expect(tree.hasChildren, isFalse);
      });
    });

    group('CategoryTree Equality', () {
      test('should be equal when all fields match', () {
        final tree1 = CategoryTree(category: rootCategory, depth: 0);

        final tree2 = CategoryTree(category: rootCategory, depth: 0);

        expect(tree1, equals(tree2));
      });

      test('should not be equal when categories differ', () {
        final tree1 = CategoryTree(category: rootCategory);
        final tree2 = CategoryTree(category: childCategory1);

        expect(tree1, isNot(equals(tree2)));
      });

      test('should not be equal when depths differ', () {
        final tree1 = CategoryTree(category: rootCategory, depth: 0);
        final tree2 = CategoryTree(category: rootCategory, depth: 1);

        expect(tree1, isNot(equals(tree2)));
      });

      test('should not be equal when children differ', () {
        final childTree = CategoryTree(category: childCategory1);

        final tree1 = CategoryTree(category: rootCategory);
        final tree2 = CategoryTree(
          category: rootCategory,
          children: [childTree],
        );

        expect(tree1, isNot(equals(tree2)));
      });
    });
  });

  group('CategoryType Enum Tests', () {
    test('should serialize to correct JSON values', () {
      expect(CategoryType.income.name, equals('income'));
      expect(CategoryType.expense.name, equals('expense'));
    });

    test('should handle all enum values', () {
      expect(CategoryType.values.length, equals(2));
      expect(CategoryType.values.contains(CategoryType.income), isTrue);
      expect(CategoryType.values.contains(CategoryType.expense), isTrue);
    });
  });
}
