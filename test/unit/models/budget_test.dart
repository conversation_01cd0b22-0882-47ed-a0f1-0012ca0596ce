import 'package:budapp/data/models/budget.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Budget Model Tests', () {
    group('Budget Creation', () {
      test('should create budget with all required fields', () {
        final now = DateTime.now();
        final budget = Budget(
          id: 'test-budget-1',
          userId: 'test-user-1',
          type: BudgetType.expense,
          plannedAmountCents: 50000, // $500.00 in cents
          currentAmountCents: 0,
          period: BudgetPeriod.monthly,
          categoryId: 'category-1',
          parentBudgetId: null,
          isActive: true,
          periodStart: DateTime(now.year, now.month, 1),
          schemaVersion: 1,
          createdAt: now,
          updatedAt: now,
        );

        expect(budget.id, equals('test-budget-1'));
        expect(budget.userId, equals('test-user-1'));

        expect(budget.type, equals(BudgetType.expense));
        expect(budget.plannedAmountCents, equals(50000));
        expect(budget.currentAmountCents, equals(0));
        expect(budget.period, equals(BudgetPeriod.monthly));
        expect(budget.categoryId, equals('category-1'));
        expect(budget.parentBudgetId, isNull);
        expect(budget.isActive, isTrue);
        expect(budget.schemaVersion, equals(1));
        expect(budget.createdAt, equals(now));
        expect(budget.updatedAt, equals(now));
      });

      test('should create budget with factory method', () {
        final budget = Budget.create(
          userId: 'test-user-1',
          type: BudgetType.expense,
          plannedAmountCents: 20000, // $200.00 in cents
          categoryId: 'category-2',
          periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
        );

        expect(budget.userId, equals('test-user-1'));

        expect(budget.type, equals(BudgetType.expense));
        expect(budget.plannedAmountCents, equals(20000));
        expect(budget.period, equals(BudgetPeriod.monthly)); // Default
        expect(budget.categoryId, equals('category-2'));
        expect(budget.isActive, isTrue); // Default
        expect(budget.schemaVersion, equals(1)); // Updated default
        expect(budget.currentAmountCents, equals(0)); // Default
      });

      test('should create income budget with default values', () {
        final budget = Budget.create(
          userId: 'test-user-1',
          type: BudgetType.income,
          plannedAmountCents: 500000, // $5000.00 in cents
          periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
        );

        expect(budget.type, equals(BudgetType.income));
        expect(budget.currentAmountCents, equals(0));
      });
    });

    group('Budget JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final now = DateTime.parse('2025-01-01T00:00:00.000Z');
        final budget = Budget(
          id: 'test-budget-1',
          userId: 'test-user-1',
          type: BudgetType.expense,
          plannedAmountCents: 50000, // $500.00 in cents
          currentAmountCents: 15000, // $150.00 in cents
          period: BudgetPeriod.monthly,
          categoryId: 'category-1',
          parentBudgetId: null,
          isActive: true,
          periodStart: DateTime(now.year, now.month, 1),
          schemaVersion: 1,
          createdAt: now,
          updatedAt: now,
        );

        final json = budget.toJson();

        expect(json['id'], equals('test-budget-1'));
        expect(json['userId'], equals('test-user-1'));
        expect(json['type'], equals('expense'));
        expect(json['plannedAmountCents'], equals(50000));
        expect(json['currentAmountCents'], equals(15000));
        // Currency code removed - using global currency preference
        expect(json['period'], equals('monthly'));
        expect(json['categoryId'], equals('category-1'));
        expect(json['parentBudgetId'], isNull);
        expect(json['isActive'], isTrue);
        expect(json['schemaVersion'], equals(1));
        expect(json['createdAt'], equals(now.toIso8601String()));
        expect(json['updatedAt'], equals(now.toIso8601String()));
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'id': 'test-budget-1',
          'userId': 'test-user-1',
          'name': 'Groceries',
          'type': 'expense',
          'plannedAmountCents': 50000,
          'currentAmountCents': 15000,
          'period': 'monthly',
          'categoryId': 'category-1',
          'parentBudgetId': null,
          'isActive': true,
          'periodStart': '2025-01-01T00:00:00.000Z',
          'schemaVersion': 1,
          'createdAt': '2025-01-01T00:00:00.000Z',
          'updatedAt': '2025-01-01T00:00:00.000Z',
        };

        final budget = Budget.fromJson(json);

        expect(budget.id, equals('test-budget-1'));
        expect(budget.userId, equals('test-user-1'));

        expect(budget.type, equals(BudgetType.expense));
        expect(budget.plannedAmountCents, equals(50000));
        expect(budget.currentAmountCents, equals(15000));
        expect(budget.period, equals(BudgetPeriod.monthly));
        expect(budget.categoryId, equals('category-1'));
        expect(budget.parentBudgetId, isNull);
        expect(budget.isActive, isTrue);
        expect(budget.schemaVersion, equals(1));
      });

      test(
        'should handle missing currentAmountCents field (backward compatibility)',
        () {
          final json = {
            'id': 'test-budget-1',
            'userId': 'test-user-1',
            'name': 'Groceries',
            'type': 'expense',
            'plannedAmountCents': 50000,
            'period': 'monthly',
            'isActive': true,
            'periodStart': '2025-01-01T00:00:00.000Z',
            'schemaVersion': 1, // Current schema version
            'createdAt': '2025-01-01T00:00:00.000Z',
            'updatedAt': '2025-01-01T00:00:00.000Z',
            // Missing currentAmountCents field
          };

          final budget = Budget.fromJson(json);

          expect(budget.currentAmountCents, equals(0)); // Should default to 0
          expect(
            budget.schemaVersion,
            equals(1),
          ); // Should preserve old version
        },
      );
    });

    group('Budget CopyWith', () {
      test('should copy with updated current amount', () {
        final original = Budget.create(
          userId: 'test-user-1',
          plannedAmountCents: 50000, // $500.00 in cents
          periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
        );

        final updated = original.copyWith(
          currentAmountCents: 15000,
        ); // $150.00 in cents

        expect(updated.currentAmountCents, equals(15000));
        expect(updated.plannedAmountCents, equals(50000)); // Unchanged
      });

      test('should copy with updated current amount for income budget', () {
        final original = Budget.create(
          userId: 'test-user-1',
          type: BudgetType.income,
          plannedAmountCents: 500000, // $5000.00 in cents
          periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
        );

        final updated = original.copyWith(
          currentAmountCents: 250000,
        ); // $2500.00 in cents

        expect(updated.currentAmountCents, equals(250000));
        expect(updated.plannedAmountCents, equals(500000)); // Unchanged
        expect(updated.type, equals(BudgetType.income)); // Unchanged
      });
    });

    group('Budget Equality', () {
      test('should be equal when all fields match', () {
        final now = DateTime.now();
        final budget1 = Budget(
          id: 'test-budget-1',
          userId: 'test-user-1',
          type: BudgetType.expense,
          plannedAmountCents: 50000, // $500.00 in cents
          currentAmountCents: 15000, // $150.00 in cents
          period: BudgetPeriod.monthly,
          isActive: true,
          periodStart: DateTime(now.year, now.month, 1),
          schemaVersion: 1,
          createdAt: now,
          updatedAt: now,
        );

        final budget2 = Budget(
          id: 'test-budget-1',
          userId: 'test-user-1',
          type: BudgetType.expense,
          plannedAmountCents: 50000, // $500.00 in cents
          currentAmountCents: 15000, // $150.00 in cents
          period: BudgetPeriod.monthly,
          isActive: true,
          periodStart: DateTime(now.year, now.month, 1),
          schemaVersion: 1,
          createdAt: now,
          updatedAt: now,
        );

        expect(budget1, equals(budget2));
        expect(budget1.hashCode, equals(budget2.hashCode));
      });

      test('should not be equal when current amounts differ', () {
        final budget1 = Budget.create(
          userId: 'test-user-1',
          plannedAmountCents: 50000, // $500.00 in cents
          periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
        ).copyWith(currentAmountCents: 15000); // $150.00 in cents

        final budget2 = Budget.create(
          userId: 'test-user-1',
          plannedAmountCents: 50000, // $500.00 in cents
          periodStart: DateTime(DateTime.now().year, DateTime.now().month, 1),
        ).copyWith(currentAmountCents: 20000); // $200.00 in cents

        expect(budget1, isNot(equals(budget2)));
      });
    });
  });
}
