import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/services/currency_preferences_service.dart';
import 'package:budapp/services/currency_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('Currency Providers Tests', () {
    late ProviderContainer container;

    setUp(() {
      SharedPreferences.setMockInitialValues({});
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    group('SharedPreferences Provider', () {
      test(
        'sharedPreferencesProvider should return SharedPreferences instance',
        () async {
          final sharedPrefs = await container.read(
            sharedPreferencesProvider.future,
          );
          expect(sharedPrefs, isA<SharedPreferences>());
        },
      );

      test('sharedPreferencesProvider should handle multiple reads', () async {
        final sharedPrefs1 = await container.read(
          sharedPreferencesProvider.future,
        );
        final sharedPrefs2 = await container.read(
          sharedPreferencesProvider.future,
        );
        expect(sharedPrefs1, equals(sharedPrefs2));
      });
    });

    group('CurrencyPreferencesService Provider', () {
      test(
        'currencyPreferencesServiceProvider should create service instance',
        () async {
          // Wait for SharedPreferences to load
          await container.read(sharedPreferencesProvider.future);

          final service = container.read(currencyPreferencesServiceProvider);
          expect(service, isA<CurrencyPreferencesService>());
        },
      );

      test(
        'currencyPreferencesServiceProvider should throw when SharedPreferences loading',
        () {
          // Create a new container that hasn't loaded SharedPreferences yet
          final newContainer = ProviderContainer();

          expect(
            () => newContainer.read(currencyPreferencesServiceProvider),
            throwsA(isA<Exception>()),
          );

          newContainer.dispose();
        },
      );

      test(
        'currencyPreferencesServiceProvider should handle SharedPreferences error',
        () {
          // Create container with overridden provider that throws
          final errorContainer = ProviderContainer(
            overrides: [
              sharedPreferencesProvider.overrideWith(
                (ref) => throw Exception('Test error'),
              ),
            ],
          );

          expect(
            () => errorContainer.read(currencyPreferencesServiceProvider),
            throwsA(isA<Exception>()),
          );

          errorContainer.dispose();
        },
      );
    });

    group('Supported Currencies Provider', () {
      test('supportedCurrenciesProvider should return default currencies', () {
        final currencies = container.read(supportedCurrenciesProvider);
        expect(currencies, isA<List<String>>());
        expect(currencies, equals(CurrencyService.defaultSupportedCurrencies));
        expect(currencies, isNotEmpty);
      });

      test('supportedCurrenciesProvider should contain common currencies', () {
        final currencies = container.read(supportedCurrenciesProvider);
        expect(currencies, contains('USD'));
        expect(currencies, contains('EUR'));
      });
    });

    group('CurrencyPreferenceNotifier', () {
      test('should initialize with default currency', () async {
        // Wait for initialization
        await container.read(sharedPreferencesProvider.future);

        final notifier = container.read(currencyPreferenceProvider.notifier);
        final state = container.read(currencyPreferenceProvider);

        expect(state, equals(CurrencyService.defaultCurrencyCode));
        expect(notifier, isA<CurrencyPreferenceNotifier>());
      });

      test(
        'should load stored currency preference on initialization',
        () async {
          // Setup stored preference
          SharedPreferences.setMockInitialValues({
            'user_currency_preference': 'EUR',
          });

          final newContainer = ProviderContainer();

          // Wait for initialization
          await newContainer.read(sharedPreferencesProvider.future);

          // Read the notifier to trigger initialization
          newContainer.read(currencyPreferenceProvider.notifier);

          // Give time for initialization to complete
          await Future<void>.delayed(const Duration(milliseconds: 200));

          final state = newContainer.read(currencyPreferenceProvider);
          expect(state, equals('EUR'));

          newContainer.dispose();
        },
      );

      test('should handle initialization failure gracefully', () async {
        // Create container with failing SharedPreferences
        final errorContainer = ProviderContainer(
          overrides: [
            sharedPreferencesProvider.overrideWith(
              (ref) => throw Exception('Test error'),
            ),
          ],
        );

        final state = errorContainer.read(currencyPreferenceProvider);
        expect(state, equals(CurrencyService.defaultCurrencyCode));

        // Give time for any async operations to complete before disposing
        await Future<void>.delayed(const Duration(milliseconds: 50));

        errorContainer.dispose();
      });

      test('setCurrency should update preference and state', () async {
        await container.read(sharedPreferencesProvider.future);

        final notifier = container.read(currencyPreferenceProvider.notifier);

        // Give time for initialization
        await Future<void>.delayed(const Duration(milliseconds: 100));

        final success = await notifier.setCurrency('EUR');
        expect(success, isTrue);

        final state = container.read(currencyPreferenceProvider);
        expect(state, equals('EUR'));
      });

      test('setCurrency should handle lowercase input', () async {
        await container.read(sharedPreferencesProvider.future);

        final notifier = container.read(currencyPreferenceProvider.notifier);

        // Give time for initialization
        await Future<void>.delayed(const Duration(milliseconds: 100));

        final success = await notifier.setCurrency('eur');
        expect(success, isTrue);

        final state = container.read(currencyPreferenceProvider);
        expect(state, equals('EUR'));
      });

      test('setCurrency should initialize service if not ready', () async {
        final notifier = container.read(currencyPreferenceProvider.notifier);

        // Call setCurrency before initialization completes
        final success = await notifier.setCurrency('GBP');
        expect(success, isTrue);

        final state = container.read(currencyPreferenceProvider);
        expect(state, equals('GBP'));
      });

      test('setCurrency should handle service errors', () async {
        // Create a mock that will fail on setCurrency
        SharedPreferences.setMockInitialValues({});

        final notifier = container.read(currencyPreferenceProvider.notifier);

        // The service throws ArgumentError for invalid currency codes
        expect(
          () => notifier.setCurrency('INVALID'),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('resetToDefault should reset currency preference', () async {
        await container.read(sharedPreferencesProvider.future);

        final notifier = container.read(currencyPreferenceProvider.notifier);

        // Give time for initialization
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // First set a different currency
        await notifier.setCurrency('EUR');
        expect(container.read(currencyPreferenceProvider), equals('EUR'));

        // Then reset to default
        final success = await notifier.resetToDefault();
        expect(success, isTrue);

        final state = container.read(currencyPreferenceProvider);
        expect(state, equals(CurrencyService.defaultCurrencyCode));
      });

      test('resetToDefault should initialize service if not ready', () async {
        final notifier = container.read(currencyPreferenceProvider.notifier);

        // Call resetToDefault before initialization completes
        final success = await notifier.resetToDefault();
        expect(success, isTrue);

        final state = container.read(currencyPreferenceProvider);
        expect(state, equals(CurrencyService.defaultCurrencyCode));
      });

      test('resetToDefault should handle service errors', () async {
        final notifier = container.read(currencyPreferenceProvider.notifier);

        // This should handle errors gracefully
        final success = await notifier.resetToDefault();
        expect(success, isA<bool>());
      });

      test('getCurrentCurrencySymbol should return correct symbol', () async {
        await container.read(sharedPreferencesProvider.future);

        final notifier = container.read(currencyPreferenceProvider.notifier);

        // Give time for initialization
        await Future<void>.delayed(const Duration(milliseconds: 100));

        await notifier.setCurrency('USD');

        final symbol = notifier.getCurrentCurrencySymbol();
        expect(symbol, equals(CurrencyService.getCurrencySymbol('USD')));
      });

      test(
        'getCurrentCurrencyDisplayName should return correct name',
        () async {
          await container.read(sharedPreferencesProvider.future);

          final notifier = container.read(currencyPreferenceProvider.notifier);

          // Give time for initialization
          await Future<void>.delayed(const Duration(milliseconds: 100));

          await notifier.setCurrency('EUR');

          final displayName = notifier.getCurrentCurrencyDisplayName();
          expect(
            displayName,
            equals(CurrencyService.getCurrencyDisplayName('EUR')),
          );
        },
      );
    });

    group('CurrencyFormatter Provider', () {
      test(
        'currencyFormatterProvider should create formatter with current currency',
        () async {
          await container.read(sharedPreferencesProvider.future);

          final notifier = container.read(currencyPreferenceProvider.notifier);

          // Give time for initialization
          await Future<void>.delayed(const Duration(milliseconds: 100));

          await notifier.setCurrency('EUR');

          final formatter = container.read(currencyFormatterProvider);
          expect(formatter, isA<CurrencyFormatter>());
          expect(formatter.currencyCode, equals('EUR'));
        },
      );

      test(
        'currencyFormatterProvider should update when currency changes',
        () async {
          await container.read(sharedPreferencesProvider.future);

          final notifier = container.read(currencyPreferenceProvider.notifier);

          // Give time for initialization
          await Future<void>.delayed(const Duration(milliseconds: 100));

          await notifier.setCurrency('USD');
          final formatter1 = container.read(currencyFormatterProvider);
          expect(formatter1.currencyCode, equals('USD'));

          await notifier.setCurrency('GBP');
          final formatter2 = container.read(currencyFormatterProvider);
          expect(formatter2.currencyCode, equals('GBP'));
        },
      );
    });
  });

  group('CurrencyFormatter Tests', () {
    late CurrencyFormatter formatter;

    setUp(() {
      formatter = CurrencyFormatter('USD');
    });

    test('should initialize with currency code', () {
      expect(formatter.currencyCode, equals('USD'));
    });

    test('formatAmount should format amount correctly', () {
      final formatted = formatter.formatAmount(12345); // $123.45
      expect(formatted, isA<String>());
      expect(formatted, isNotEmpty);
    });

    test('formatAmountWithSign should format with sign', () {
      final positiveFormatted = formatter.formatAmountWithSign(12345);
      final negativeFormatted = formatter.formatAmountWithSign(-12345);

      expect(positiveFormatted, isA<String>());
      expect(negativeFormatted, isA<String>());
      expect(positiveFormatted, isNot(equals(negativeFormatted)));
    });

    test('formatAmountWithSign should show positive sign when requested', () {
      final formatted = formatter.formatAmountWithSign(
        12345,
        showPositiveSign: true,
      );
      expect(formatted, isA<String>());
    });

    test('symbol getter should return currency symbol', () {
      final symbol = formatter.symbol;
      expect(symbol, equals(CurrencyService.getCurrencySymbol('USD')));
    });

    test('displayName getter should return currency display name', () {
      final displayName = formatter.displayName;
      expect(
        displayName,
        equals(CurrencyService.getCurrencyDisplayName('USD')),
      );
    });

    test('usesDecimals getter should return decimal usage', () {
      final usesDecimals = formatter.usesDecimals;
      expect(usesDecimals, isA<bool>());
    });

    test('decimalPlaces getter should return decimal places', () {
      final decimalPlaces = formatter.decimalPlaces;
      expect(decimalPlaces, isA<int>());
      expect(decimalPlaces, greaterThanOrEqualTo(0));
    });

    test('should work with different currencies', () {
      final eurFormatter = CurrencyFormatter('EUR');
      expect(eurFormatter.currencyCode, equals('EUR'));
      expect(
        eurFormatter.symbol,
        equals(CurrencyService.getCurrencySymbol('EUR')),
      );

      final gbpFormatter = CurrencyFormatter('GBP');
      expect(gbpFormatter.currencyCode, equals('GBP'));
      expect(
        gbpFormatter.symbol,
        equals(CurrencyService.getCurrencySymbol('GBP')),
      );
    });

    test('should handle edge cases in formatting', () {
      expect(formatter.formatAmount(0), isA<String>());
      expect(formatter.formatAmount(-1), isA<String>());
      expect(formatter.formatAmount(1), isA<String>());
      expect(formatter.formatAmount(999999999), isA<String>());
    });
  });
}
