import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/widgets/forms/helpers/common_form_fields.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CommonFormFields Tests', () {
    group('Name Field Tests', () {
      test('should create name field with default configuration', () {
        final config = CommonFormFields.name();

        expect(config.key, equals('name'));
        expect(config.label, equals('Name'));
        expect(config.hintText, equals('Enter name'));
        expect(config.isRequired, isTrue);
        expect(config.initialValue, isNull);
        expect(config.keyboardType, equals(TextInputType.text));
        expect(config.textCapitalization, equals(TextCapitalization.words));
        expect(config.maxLength, equals(100));
      });

      test('should create name field with custom parameters', () {
        final config = CommonFormFields.name(
          initialValue: 'Test Name',
          label: 'Custom Label',
          hintText: 'Custom hint',
          isRequired: false,
        );

        expect(config.initialValue, equals('Test Name'));
        expect(config.label, equals('Custom Label'));
        expect(config.hintText, equals('Custom hint'));
        expect(config.isRequired, isFalse);
      });

      test('should validate name field correctly', () {
        final config = CommonFormFields.name();
        final validator = config.validator!;

        // Test required validation
        expect(validator(null), equals('Name is required'));
        expect(validator(''), equals('Name is required'));
        expect(validator('Valid Name'), isNull);

        // Test length validation
        final longName = 'a' * 101;
        expect(
          validator(longName),
          equals('Name must be 100 characters or less'),
        );

        final validLongName = 'a' * 100;
        expect(validator(validLongName), isNull);
      });

      test('should validate optional name field correctly', () {
        final config = CommonFormFields.name(isRequired: false);
        final validator = config.validator!;

        expect(validator(null), isNull);
        expect(validator(''), isNull);
        expect(validator('Valid Name'), isNull);
      });

      test('should use custom validator when provided', () {
        String? customValidator(String? value) {
          if (value == 'forbidden') return 'Forbidden name';
          return null;
        }

        final config = CommonFormFields.name(customValidator: customValidator);
        final validator = config.validator!;

        expect(validator('forbidden'), equals('Forbidden name'));
        expect(validator('allowed'), isNull);
      });
    });

    group('Description Field Tests', () {
      test('should create description field with default configuration', () {
        final config = CommonFormFields.description();

        expect(config.key, equals('description'));
        expect(config.label, equals('Description'));
        expect(config.hintText, equals('Enter description (optional)'));
        expect(config.isRequired, isFalse);
        expect(config.keyboardType, equals(TextInputType.multiline));
        expect(config.textCapitalization, equals(TextCapitalization.sentences));
        expect(config.maxLines, equals(3));
        expect(config.maxLength, equals(500));
      });

      test('should create description field with custom parameters', () {
        final config = CommonFormFields.description(
          initialValue: 'Test description',
          label: 'Custom Description',
          hintText: 'Custom hint',
          isRequired: true,
          maxLines: 5,
          maxLength: 200,
        );

        expect(config.initialValue, equals('Test description'));
        expect(config.label, equals('Custom Description'));
        expect(config.hintText, equals('Custom hint'));
        expect(config.isRequired, isTrue);
        expect(config.maxLines, equals(5));
        expect(config.maxLength, equals(200));
      });

      test('should validate description field correctly', () {
        final config = CommonFormFields.description();
        final validator = config.validator!;

        // Optional field should allow null/empty
        expect(validator(null), isNull);
        expect(validator(''), isNull);
        expect(validator('Valid description'), isNull);

        // Test length validation
        final longDescription = 'a' * 501;
        expect(
          validator(longDescription),
          equals('Description must be 500 characters or less'),
        );

        final validLongDescription = 'a' * 500;
        expect(validator(validLongDescription), isNull);
      });

      test('should validate description field with custom max length', () {
        final config = CommonFormFields.description(maxLength: 100);
        final validator = config.validator!;

        final longDescription = 'a' * 101;
        expect(
          validator(longDescription),
          equals('Description must be 100 characters or less'),
        );

        final validDescription = 'a' * 100;
        expect(validator(validDescription), isNull);
      });

      test('should use custom validator when provided', () {
        String? customValidator(String? value) {
          if (value?.contains('bad') ?? false) return 'Contains bad word';
          return null;
        }

        final config = CommonFormFields.description(
          customValidator: customValidator,
        );
        final validator = config.validator!;

        expect(validator('This is bad'), equals('Contains bad word'));
        expect(validator('This is good'), isNull);
      });
    });

    group('Amount Field Tests', () {
      test('should create amount field with default configuration', () {
        final config = CommonFormFields.amount();

        expect(config.key, equals('amount'));
        expect(config.label, equals('Amount'));
        expect(config.hintText, equals('0.00'));
        expect(config.isRequired, isTrue);
        expect(
          config.keyboardType,
          equals(const TextInputType.numberWithOptions(decimal: true)),
        );
        expect(config.prefixIcon, isA<Icon>());
      });

      test('should create amount field with custom parameters', () {
        final config = CommonFormFields.amount(
          initialValue: '100.50',
          label: 'Custom Amount',
          hintText: 'Enter amount',
          isRequired: false,
        );

        expect(config.initialValue, equals('100.50'));
        expect(config.label, equals('Custom Amount'));
        expect(config.hintText, equals('Enter amount'));
        expect(config.isRequired, isFalse);
      });

      test('should validate amount field correctly', () {
        final config = CommonFormFields.amount();
        final validator = config.validator!;

        // Test required validation
        expect(validator(null), equals('Amount is required'));
        expect(validator(''), equals('Amount is required'));

        // Test valid amounts
        expect(validator('100'), isNull);
        expect(validator('100.50'), isNull);
        expect(validator('0'), isNull);

        // Test invalid amounts
        expect(validator('abc'), equals('Please enter a valid amount'));
        expect(validator('100.50.25'), equals('Please enter a valid amount'));
        expect(validator('-50'), equals('Amount cannot be negative'));
      });

      test('should validate optional amount field correctly', () {
        final config = CommonFormFields.amount(isRequired: false);
        final validator = config.validator!;

        expect(validator(null), isNull);
        expect(validator(''), isNull);
        expect(validator('100'), isNull);
        expect(validator('abc'), equals('Please enter a valid amount'));
      });

      test('should use custom validator when provided', () {
        String? customValidator(String? value) {
          final amount = double.tryParse(value ?? '');
          if (amount != null && amount > 1000) return 'Amount too large';
          return null;
        }

        final config = CommonFormFields.amount(
          customValidator: customValidator,
        );
        final validator = config.validator!;

        expect(validator('1500'), equals('Amount too large'));
        expect(validator('500'), isNull);
      });
    });

    group('Email Field Tests', () {
      test('should create email field with default configuration', () {
        final config = CommonFormFields.email();

        expect(config.key, equals('email'));
        expect(config.label, equals('Email'));
        expect(config.hintText, equals('<EMAIL>'));
        expect(config.isRequired, isTrue);
        expect(config.keyboardType, equals(TextInputType.emailAddress));
        expect(config.textCapitalization, equals(TextCapitalization.none));
        expect(config.prefixIcon, isA<Icon>());
      });

      test('should create email field with custom parameters', () {
        final config = CommonFormFields.email(
          initialValue: '<EMAIL>',
          label: 'Email Address',
          hintText: 'Enter your email',
          isRequired: false,
        );

        expect(config.initialValue, equals('<EMAIL>'));
        expect(config.label, equals('Email Address'));
        expect(config.hintText, equals('Enter your email'));
        expect(config.isRequired, isFalse);
      });

      test('should validate email field correctly', () {
        final config = CommonFormFields.email();
        final validator = config.validator!;

        // Test required validation
        expect(validator(null), equals('Email is required'));
        expect(validator(''), equals('Email is required'));

        // Test valid emails
        expect(validator('<EMAIL>'), isNull);
        expect(validator('<EMAIL>'), isNull);
        expect(validator('<EMAIL>'), isNull);

        // Test invalid emails
        expect(
          validator('invalid-email'),
          equals('Please enter a valid email address'),
        );
        expect(
          validator('@domain.com'),
          equals('Please enter a valid email address'),
        );
        expect(
          validator('user@'),
          equals('Please enter a valid email address'),
        );
        expect(
          validator('user@domain'),
          equals('Please enter a valid email address'),
        );
      });

      test('should validate optional email field correctly', () {
        final config = CommonFormFields.email(isRequired: false);
        final validator = config.validator!;

        expect(validator(null), isNull);
        expect(validator(''), isNull);
        expect(validator('<EMAIL>'), isNull);
        expect(
          validator('invalid-email'),
          equals('Please enter a valid email address'),
        );
      });

      test('should use custom validator when provided', () {
        String? customValidator(String? value) {
          if (value?.endsWith('@banned.com') ?? false) {
            return 'Domain not allowed';
          }
          return null;
        }

        final config = CommonFormFields.email(customValidator: customValidator);
        final validator = config.validator!;

        expect(validator('<EMAIL>'), equals('Domain not allowed'));
        expect(validator('<EMAIL>'), isNull);
      });
    });

    group('Password Field Tests', () {
      test('should create password field with default configuration', () {
        final config = CommonFormFields.password();

        expect(config.key, equals('password'));
        expect(config.label, equals('Password'));
        expect(config.hintText, equals('Enter password'));
        expect(config.isRequired, isTrue);
        expect(config.keyboardType, equals(TextInputType.visiblePassword));
        expect(config.prefixIcon, isA<Icon>());
      });

      test('should create password field with custom parameters', () {
        final config = CommonFormFields.password(
          initialValue: 'testpass',
          label: 'Password',
          hintText: 'Enter strong password',
          isRequired: false,
          minLength: 12,
        );

        expect(config.initialValue, equals('testpass'));
        expect(config.label, equals('Password'));
        expect(config.hintText, equals('Enter strong password'));
        expect(config.isRequired, isFalse);
      });

      test('should validate password field correctly', () {
        final config = CommonFormFields.password();
        final validator = config.validator!;

        // Test required validation
        expect(validator(null), equals('Password is required'));
        expect(validator(''), equals('Password is required'));

        // Test length validation
        expect(
          validator('short'),
          equals('Password must be at least 8 characters'),
        );
        expect(validator('validpassword'), isNull);
      });

      test('should validate password field with custom min length', () {
        final config = CommonFormFields.password(minLength: 12);
        final validator = config.validator!;

        expect(
          validator('shortpass'),
          equals('Password must be at least 12 characters'),
        );
        expect(validator('verylongpassword'), isNull);
      });

      test('should use custom validator when provided', () {
        String? customValidator(String? value) {
          if (value != null && !value.contains(RegExp('[0-9]'))) {
            return 'Password must contain numbers';
          }
          return null;
        }

        final config = CommonFormFields.password(
          customValidator: customValidator,
        );
        final validator = config.validator!;

        expect(
          validator('onlyletters'),
          equals('Password must contain numbers'),
        );
        expect(validator('password123'), isNull);
      });
    });

    group('Color Selector Field Tests', () {
      test('should create color selector field with default configuration', () {
        final config = CommonFormFields.colorSelector();

        expect(config.key, equals('color'));
        expect(config.label, equals('Color'));
        expect(config.isRequired, isFalse);
        expect(config.availableColors.length, equals(16));
        expect(config.availableColors.contains('#F44336'), isTrue);
        expect(config.availableColors.contains('#FF5722'), isTrue);
      });

      test('should create color selector field with custom parameters', () {
        final customColors = ['#FF0000', '#00FF00', '#0000FF'];
        final config = CommonFormFields.colorSelector(
          initialValue: '#FF0000',
          label: 'Choose Color',
          availableColors: customColors,
          isRequired: true,
        );

        expect(config.initialValue, equals('#FF0000'));
        expect(config.label, equals('Choose Color'));
        expect(config.availableColors, equals(customColors));
        expect(config.isRequired, isTrue);
      });

      test('should use custom validator when provided', () {
        String? customValidator(String? value) {
          if (value == '#FF0000') return 'Red not allowed';
          return null;
        }

        final config = CommonFormFields.colorSelector(
          customValidator: customValidator,
        );
        final validator = config.validator;

        expect(validator!('#FF0000'), equals('Red not allowed'));
        expect(validator('#00FF00'), isNull);
      });
    });

    group('Icon Selector Field Tests', () {
      test('should create icon selector field with default configuration', () {
        final config = CommonFormFields.iconSelector();

        expect(config.key, equals('icon'));
        expect(config.label, equals('Icon'));
        expect(config.isRequired, isFalse);
        expect(config.iconSize, equals(24.0));
        expect(config.availableIcons.length, equals(16));
        expect(
          config.availableIcons.contains('account_balance_wallet'),
          isTrue,
        );
        expect(config.availableIcons.contains('music_note'), isTrue);
      });

      test('should create icon selector field with custom parameters', () {
        final customIcons = ['home', 'work', 'favorite'];
        final config = CommonFormFields.iconSelector(
          initialValue: 'home',
          label: 'Choose Icon',
          availableIcons: customIcons,
          isRequired: true,
          iconSize: 32,
        );

        expect(config.initialValue, equals('home'));
        expect(config.label, equals('Choose Icon'));
        expect(config.availableIcons, equals(customIcons));
        expect(config.isRequired, isTrue);
        expect(config.iconSize, equals(32.0));
      });

      test('should use custom validator when provided', () {
        String? customValidator(String? value) {
          if (value == 'banned_icon') return 'Icon not allowed';
          return null;
        }

        final config = CommonFormFields.iconSelector(
          customValidator: customValidator,
        );
        final validator = config.validator;

        expect(validator!('banned_icon'), equals('Icon not allowed'));
        expect(validator('home'), isNull);
      });
    });

    group('Date Picker Field Tests', () {
      test('should create date picker field with default configuration', () {
        final config = CommonFormFields.datePicker();

        expect(config.key, equals('date'));
        expect(config.label, equals('Date'));
        expect(config.hintText, equals('Select date'));
        expect(config.isRequired, isTrue);
        expect(config.firstDate, equals(DateTime(1900)));
        expect(config.lastDate, equals(DateTime(2100)));
      });

      test('should create date picker field with custom parameters', () {
        final initialDate = DateTime(2023, 1, 15);
        final firstDate = DateTime(2020);
        final lastDate = DateTime(2030);

        final config = CommonFormFields.datePicker(
          initialValue: initialDate,
          label: 'Select Date',
          hintText: 'Pick a date',
          isRequired: false,
          firstDate: firstDate,
          lastDate: lastDate,
          dateFormat: 'dd/MM/yyyy',
        );

        expect(config.initialValue, equals(initialDate));
        expect(config.label, equals('Select Date'));
        expect(config.hintText, equals('Pick a date'));
        expect(config.isRequired, isFalse);
        expect(config.firstDate, equals(firstDate));
        expect(config.lastDate, equals(lastDate));
        expect(config.dateFormat, equals('dd/MM/yyyy'));
      });

      test('should validate date picker field correctly', () {
        final config = CommonFormFields.datePicker();
        final validator = config.validator!;

        // Test required validation
        expect(validator(null), equals('Date is required'));
        expect(validator(DateTime(2023, 1, 15)), isNull);
      });

      test('should validate optional date picker field correctly', () {
        final config = CommonFormFields.datePicker(isRequired: false);
        final validator = config.validator!;

        expect(validator(null), isNull);
        expect(validator(DateTime(2023, 1, 15)), isNull);
      });

      test('should use custom validator when provided', () {
        String? customValidator(DateTime? value) {
          if (value != null && value.isAfter(DateTime(2025))) {
            return 'Date too far in future';
          }
          return null;
        }

        final config = CommonFormFields.datePicker(
          customValidator: customValidator,
        );
        final validator = config.validator!;

        expect(validator(DateTime(2026)), equals('Date too far in future'));
        expect(validator(DateTime(2024)), isNull);
      });
    });

    group('Account Type Dropdown Tests', () {
      test(
        'should create account type dropdown with default configuration',
        () {
          final config = CommonFormFields.accountTypeDropdown();

          expect(config.key, equals('accountType'));
          expect(config.label, equals('Account Type'));
          expect(config.isRequired, isTrue);
          expect(config.items, equals(AccountType.values));
        },
      );

      test('should create account type dropdown with custom parameters', () {
        final config = CommonFormFields.accountTypeDropdown(
          initialValue: AccountType.checking,
          label: 'Select Account Type',
          isRequired: false,
        );

        expect(config.initialValue, equals(AccountType.checking));
        expect(config.label, equals('Select Account Type'));
        expect(config.isRequired, isFalse);
      });

      test('should validate account type dropdown correctly', () {
        final config = CommonFormFields.accountTypeDropdown();
        final validator = config.validator!;

        expect(validator(null), equals('Account type is required'));
        expect(validator(AccountType.checking), isNull);
      });

      test('should validate optional account type dropdown correctly', () {
        final config = CommonFormFields.accountTypeDropdown(isRequired: false);
        final validator = config.validator!;

        expect(validator(null), isNull);
        expect(validator(AccountType.savings), isNull);
      });

      test('should use custom validator when provided', () {
        String? customValidator(AccountType? value) {
          if (value == AccountType.creditCard) {
            return 'Credit cards not allowed';
          }
          return null;
        }

        final config = CommonFormFields.accountTypeDropdown(
          customValidator: customValidator,
        );
        final validator = config.validator!;

        expect(
          validator(AccountType.creditCard),
          equals('Credit cards not allowed'),
        );
        expect(validator(AccountType.checking), isNull);
      });

      test('should format display string correctly', () {
        final config = CommonFormFields.accountTypeDropdown();
        final displayString = config.displayStringForItem!(
          AccountType.checking,
        );

        expect(displayString, equals('checking'));
      });
    });

    group('Category Type Dropdown Tests', () {
      test(
        'should create category type dropdown with default configuration',
        () {
          final config = CommonFormFields.categoryTypeDropdown();

          expect(config.key, equals('categoryType'));
          expect(config.label, equals('Category Type'));
          expect(config.isRequired, isTrue);
          expect(config.items, equals(CategoryType.values));
        },
      );

      test('should create category type dropdown with custom parameters', () {
        final config = CommonFormFields.categoryTypeDropdown(
          initialValue: CategoryType.expense,
          label: 'Select Category Type',
          isRequired: false,
        );

        expect(config.initialValue, equals(CategoryType.expense));
        expect(config.label, equals('Select Category Type'));
        expect(config.isRequired, isFalse);
      });

      test('should validate category type dropdown correctly', () {
        final config = CommonFormFields.categoryTypeDropdown();
        final validator = config.validator!;

        expect(validator(null), equals('Category type is required'));
        expect(validator(CategoryType.expense), isNull);
      });

      test('should validate optional category type dropdown correctly', () {
        final config = CommonFormFields.categoryTypeDropdown(isRequired: false);
        final validator = config.validator!;

        expect(validator(null), isNull);
        expect(validator(CategoryType.income), isNull);
      });

      test('should use custom validator when provided', () {
        String? customValidator(CategoryType? value) {
          if (value == CategoryType.income) {
            return 'Income categories not allowed';
          }
          return null;
        }

        final config = CommonFormFields.categoryTypeDropdown(
          customValidator: customValidator,
        );
        final validator = config.validator!;

        expect(
          validator(CategoryType.income),
          equals('Income categories not allowed'),
        );
        expect(validator(CategoryType.expense), isNull);
      });

      test('should format display string correctly', () {
        final config = CommonFormFields.categoryTypeDropdown();
        final displayString = config.displayStringForItem!(
          CategoryType.expense,
        );

        expect(displayString, equals('expense'));
      });
    });

    group('Transaction Type Dropdown Tests', () {
      test(
        'should create transaction type dropdown with default configuration',
        () {
          final config = CommonFormFields.transactionTypeDropdown();

          expect(config.key, equals('transactionType'));
          expect(config.label, equals('Transaction Type'));
          expect(config.isRequired, isTrue);
          expect(config.items, equals(TransactionType.values));
        },
      );

      test(
        'should create transaction type dropdown with custom parameters',
        () {
          final config = CommonFormFields.transactionTypeDropdown(
            initialValue: TransactionType.expense,
            label: 'Select Transaction Type',
            isRequired: false,
          );

          expect(config.initialValue, equals(TransactionType.expense));
          expect(config.label, equals('Select Transaction Type'));
          expect(config.isRequired, isFalse);
        },
      );

      test('should validate transaction type dropdown correctly', () {
        final config = CommonFormFields.transactionTypeDropdown();
        final validator = config.validator!;

        expect(validator(null), equals('Transaction type is required'));
        expect(validator(TransactionType.expense), isNull);
      });

      test('should validate optional transaction type dropdown correctly', () {
        final config = CommonFormFields.transactionTypeDropdown(
          isRequired: false,
        );
        final validator = config.validator!;

        expect(validator(null), isNull);
        expect(validator(TransactionType.income), isNull);
      });

      test('should use custom validator when provided', () {
        String? customValidator(TransactionType? value) {
          if (value == TransactionType.transfer) return 'Transfers not allowed';
          return null;
        }

        final config = CommonFormFields.transactionTypeDropdown(
          customValidator: customValidator,
        );
        final validator = config.validator!;

        expect(
          validator(TransactionType.transfer),
          equals('Transfers not allowed'),
        );
        expect(validator(TransactionType.expense), isNull);
      });

      test('should format display string correctly', () {
        final config = CommonFormFields.transactionTypeDropdown();
        final displayString = config.displayStringForItem!(
          TransactionType.income,
        );

        expect(displayString, equals('income'));
      });
    });

    group('Static Class Tests', () {
      test('should be a static utility class with all static methods', () {
        // Test that all methods are static by verifying they can be called without instantiation
        expect(CommonFormFields.name().key, equals('name'));
        expect(CommonFormFields.description().key, equals('description'));
        expect(CommonFormFields.amount().key, equals('amount'));
        expect(CommonFormFields.email().key, equals('email'));
        expect(CommonFormFields.password().key, equals('password'));
        expect(CommonFormFields.colorSelector().key, equals('color'));
        expect(CommonFormFields.iconSelector().key, equals('icon'));
        expect(CommonFormFields.datePicker().key, equals('date'));
        expect(
          CommonFormFields.accountTypeDropdown().key,
          equals('accountType'),
        );
        expect(
          CommonFormFields.categoryTypeDropdown().key,
          equals('categoryType'),
        );
        expect(
          CommonFormFields.transactionTypeDropdown().key,
          equals('transactionType'),
        );
      });
    });
  });
}
