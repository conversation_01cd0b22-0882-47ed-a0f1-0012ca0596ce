import 'dart:async';

import 'package:budapp/config/app_theme.dart';
import 'package:budapp/config/environment_config.dart';
import 'package:budapp/main.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';

// Helper function to create a minimal GoRouter for testing
GoRouter createTestRouter() {
  return GoRouter(
    initialLocation: '/',
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) =>
            const Scaffold(body: Center(child: Text('Test Home'))),
      ),
    ],
  );
}

void main() {
  group('Main App Tests', () {
    group('MyApp Widget Tests', () {
      testWidgets(
        'should render MaterialApp.router immediately during background initialization',
        (tester) async {
          // Arrange - Create a provider that never completes (stays loading)
          final loadingCompleter = Completer<void>();
          final testRouter = createTestRouter();

          await tester.pumpWidget(
            ProviderScope(
              overrides: [
                backgroundInitializationProvider.overrideWith(
                  (ref) => loadingCompleter.future.then((_) => true),
                ),
                goRouterProvider.overrideWithValue(testRouter),
              ],
              child: const MyApp(),
            ),
          );

          // Act - Pump once to build the widget tree
          await tester.pump();

          // Assert - App renders immediately with MaterialApp.router (non-blocking pattern)
          expect(find.byType(CircularProgressIndicator), findsNothing);
          expect(find.text('Error initializing app:'), findsNothing);
          expect(find.byType(MaterialApp), findsOneWidget);

          // Verify app configuration during background initialization
          final materialApp = tester.widget<MaterialApp>(
            find.byType(MaterialApp),
          );
          expect(materialApp.title, equals(EnvironmentConfig.appTitle));
          expect(materialApp.theme, equals(AppTheme.lightTheme));
          expect(materialApp.darkTheme, equals(AppTheme.darkTheme));
          expect(materialApp.routerConfig, equals(testRouter));
        },
      );

      testWidgets(
        'should render MaterialApp.router even when initialization fails',
        (
          tester,
        ) async {
          // Arrange - Create a provider that throws an error
          const errorMessage = 'Test initialization error';
          final testRouter = createTestRouter();

          await tester.pumpWidget(
            ProviderScope(
              overrides: [
                backgroundInitializationProvider.overrideWith(
                  (ref) => Future<bool>.error(errorMessage),
                ),
                goRouterProvider.overrideWithValue(testRouter),
              ],
              child: const MyApp(),
            ),
          );

          // Act - Pump to build and let the error propagate
          await tester.pump();
          await tester.pump(); // Second pump to handle async error

          // Assert - App renders MaterialApp.router immediately (non-blocking pattern)
          expect(
            find.text('Error initializing app: $errorMessage'),
            findsNothing,
          );
          expect(find.byType(CircularProgressIndicator), findsNothing);
          expect(find.byType(MaterialApp), findsOneWidget);

          // Verify app configuration - router works despite background init failure
          final materialApp = tester.widget<MaterialApp>(
            find.byType(MaterialApp),
          );
          expect(materialApp.title, equals(EnvironmentConfig.appTitle));
          expect(materialApp.theme, equals(AppTheme.lightTheme));
          expect(materialApp.darkTheme, equals(AppTheme.darkTheme));
          expect(materialApp.routerConfig, equals(testRouter));
        },
      );

      testWidgets(
        'should render successful initialization with MaterialApp.router',
        (tester) async {
          // Arrange - Create successful initialization and test router
          final testRouter = createTestRouter();

          await tester.pumpWidget(
            ProviderScope(
              overrides: [
                backgroundInitializationProvider.overrideWith(
                  (ref) => Future<bool>.value(true),
                ),
                goRouterProvider.overrideWithValue(testRouter),
                themeModeProvider.overrideWith((ref) => 'system'),
              ],
              child: const MyApp(),
            ),
          );

          // Act - Pump to complete initialization
          await tester.pump();
          await tester.pump(); // Second pump for async completion

          // Assert
          expect(find.byType(CircularProgressIndicator), findsNothing);
          expect(find.text('Error initializing app:'), findsNothing);
          expect(find.byType(MaterialApp), findsOneWidget);

          // Verify MaterialApp.router is used for successful initialization
          final materialApp = tester.widget<MaterialApp>(
            find.byType(MaterialApp),
          );
          expect(materialApp.routerConfig, equals(testRouter));
          expect(materialApp.title, equals(EnvironmentConfig.appTitle));
          expect(materialApp.theme, equals(AppTheme.lightTheme));
          expect(materialApp.darkTheme, equals(AppTheme.darkTheme));
          expect(materialApp.themeMode, equals(ThemeMode.system));
        },
      );

      testWidgets('should use system theme mode (hardcoded)', (tester) async {
        // Arrange
        final testRouter = createTestRouter();

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              backgroundInitializationProvider.overrideWith(
                (ref) => Future<bool>.value(true),
              ),
              goRouterProvider.overrideWithValue(testRouter),
              // Note: themeModeProvider is not used in current implementation
            ],
            child: const MyApp(),
          ),
        );

        // Act
        await tester.pump();
        await tester.pump();

        // Assert - Current implementation hardcodes ThemeMode.system
        final materialApp = tester.widget<MaterialApp>(
          find.byType(MaterialApp),
        );
        expect(materialApp.themeMode, equals(ThemeMode.system));
      });

      testWidgets(
        'should always use system theme mode regardless of provider',
        (tester) async {
          // Arrange
          final testRouter = createTestRouter();

          await tester.pumpWidget(
            ProviderScope(
              overrides: [
                backgroundInitializationProvider.overrideWith(
                  (ref) => Future<bool>.value(true),
                ),
                goRouterProvider.overrideWithValue(testRouter),
                // themeModeProvider would be ignored in current implementation
              ],
              child: const MyApp(),
            ),
          );

          // Act
          await tester.pump();
          await tester.pump();

          // Assert - Always system theme in current implementation
          final materialApp = tester.widget<MaterialApp>(
            find.byType(MaterialApp),
          );
          expect(materialApp.themeMode, equals(ThemeMode.system));
        },
      );

      testWidgets('should use hardcoded system theme mode', (
        tester,
      ) async {
        // Arrange
        final testRouter = createTestRouter();

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              backgroundInitializationProvider.overrideWith(
                (ref) => Future<bool>.value(true),
              ),
              goRouterProvider.overrideWithValue(testRouter),
              // Current implementation ignores theme mode provider
            ],
            child: const MyApp(),
          ),
        );

        // Act
        await tester.pump();
        await tester.pump();

        // Assert - Hardcoded system theme
        final materialApp = tester.widget<MaterialApp>(
          find.byType(MaterialApp),
        );
        expect(materialApp.themeMode, equals(ThemeMode.system));
      });

      testWidgets('should configure localization delegates correctly', (
        tester,
      ) async {
        // Arrange
        final testRouter = createTestRouter();

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              backgroundInitializationProvider.overrideWith(
                (ref) => Future<bool>.value(true),
              ),
              goRouterProvider.overrideWithValue(testRouter),
              themeModeProvider.overrideWith((ref) => 'system'),
            ],
            child: const MyApp(),
          ),
        );

        // Act
        await tester.pump();
        await tester.pump();

        // Assert
        final materialApp = tester.widget<MaterialApp>(
          find.byType(MaterialApp),
        );
        expect(materialApp.localizationsDelegates, isNotNull);
        expect(materialApp.localizationsDelegates!.length, equals(4));
        expect(materialApp.supportedLocales, contains(const Locale('en')));
      });
    });

    group('App Configuration Tests', () {
      test('should have correct environment configuration access', () {
        // Test that EnvironmentConfig properties are accessible
        expect(EnvironmentConfig.appTitle, isNotNull);
        expect(EnvironmentConfig.appTitle, isNotEmpty);
        expect(EnvironmentConfig.firebaseOptions, isNotNull);
        expect(EnvironmentConfig.firebaseInitMessage, isNotNull);
      });

      test('should have correct theme configuration', () {
        // Test that AppTheme properties are accessible
        expect(AppTheme.lightTheme, isNotNull);
        expect(AppTheme.darkTheme, isNotNull);
        expect(AppTheme.lightTheme, isA<ThemeData>());
        expect(AppTheme.darkTheme, isA<ThemeData>());
      });
    });

    group('Theme Mode Switch Logic Tests', () {
      test('should correctly map theme mode strings to ThemeMode enum', () {
        // Test the theme mode mapping logic that's used in the widget
        const testCases = {
          'light': ThemeMode.light,
          'dark': ThemeMode.dark,
          'system': ThemeMode.system,
          'unknown': ThemeMode.system, // default case
          '': ThemeMode.system, // empty string case
        };

        for (final entry in testCases.entries) {
          final input = entry.key;
          final expected = entry.value;

          // Simulate the switch logic from MyApp.build
          ThemeMode result;
          switch (input) {
            case 'light':
              result = ThemeMode.light;
            case 'dark':
              result = ThemeMode.dark;
            case 'system':
            default:
              result = ThemeMode.system;
          }

          expect(result, equals(expected), reason: 'Failed for input: $input');
        }
      });
    });

    group('Widget Structure Tests', () {
      testWidgets(
        'should have correct widget hierarchy during background initialization',
        (
          tester,
        ) async {
          // Arrange
          final loadingCompleter = Completer<void>();
          final testRouter = createTestRouter();

          await tester.pumpWidget(
            ProviderScope(
              overrides: [
                backgroundInitializationProvider.overrideWith(
                  (ref) => loadingCompleter.future.then((_) => true),
                ),
                goRouterProvider.overrideWithValue(testRouter),
              ],
              child: const MyApp(),
            ),
          );

          // Act
          await tester.pump();

          // Assert widget hierarchy - non-blocking pattern shows MaterialApp.router immediately
          expect(find.byType(ProviderScope), findsOneWidget);
          expect(find.byType(MyApp), findsOneWidget);
          expect(find.byType(MaterialApp), findsOneWidget);
          // No CircularProgressIndicator in non-blocking pattern
          expect(find.byType(CircularProgressIndicator), findsNothing);

          // Verify MaterialApp.router is configured
          final materialApp = tester.widget<MaterialApp>(
            find.byType(MaterialApp),
          );
          expect(materialApp.routerConfig, isNotNull);
        },
      );

      testWidgets(
        'should have correct widget hierarchy when initialization fails',
        (
          tester,
        ) async {
          // Arrange
          final testRouter = createTestRouter();

          await tester.pumpWidget(
            ProviderScope(
              overrides: [
                backgroundInitializationProvider.overrideWith(
                  (ref) => Future<bool>.error('Test error'),
                ),
                goRouterProvider.overrideWithValue(testRouter),
              ],
              child: const MyApp(),
            ),
          );

          // Act
          await tester.pump();
          await tester.pump();

          // Assert widget hierarchy - non-blocking pattern shows MaterialApp.router immediately
          expect(find.byType(ProviderScope), findsOneWidget);
          expect(find.byType(MyApp), findsOneWidget);
          expect(find.byType(MaterialApp), findsOneWidget);

          // No error UI in non-blocking pattern - router handles navigation
          expect(find.textContaining('Error initializing app:'), findsNothing);

          // Verify MaterialApp.router is configured
          final materialApp = tester.widget<MaterialApp>(
            find.byType(MaterialApp),
          );
          expect(materialApp.routerConfig, isNotNull);
        },
      );

      testWidgets('should have correct widget hierarchy for success state', (
        tester,
      ) async {
        // Arrange
        final testRouter = createTestRouter();

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              backgroundInitializationProvider.overrideWith(
                (ref) => Future<bool>.value(true),
              ),
              goRouterProvider.overrideWithValue(testRouter),
              themeModeProvider.overrideWith((ref) => 'system'),
            ],
            child: const MyApp(),
          ),
        );

        // Act
        await tester.pump();
        await tester.pump();

        // Assert widget hierarchy
        expect(find.byType(ProviderScope), findsOneWidget);
        expect(find.byType(MyApp), findsOneWidget);
        expect(find.byType(MaterialApp), findsOneWidget);

        // Verify MaterialApp.router is used (no Scaffold in this case)
        final materialApp = tester.widget<MaterialApp>(
          find.byType(MaterialApp),
        );
        expect(materialApp.routerConfig, isNotNull);
      });
    });
  });
}
