import 'package:budapp/l10n/app_localizations_en.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late AppLocalizationsEn localizations;

  setUp(() {
    localizations = AppLocalizationsEn();
  });

  group('AppLocalizationsEn', () {
    group('Constructor', () {
      test('should create instance with default locale', () {
        final localization = AppLocalizationsEn();
        expect(localization, isA<AppLocalizationsEn>());
      });

      test('should create instance with custom locale', () {
        final localization = AppLocalizationsEn('en_US');
        expect(localization, isA<AppLocalizationsEn>());
      });
    });

    group('String Getters - Basic Functionality', () {
      test('should return non-empty strings for authentication', () {
        expect(localizations.welcomeBack, isNotEmpty);
        expect(localizations.signInToAccount, isNotEmpty);
        expect(localizations.email, isNotEmpty);
        expect(localizations.enterEmailAddress, isNotEmpty);
        expect(localizations.enterYourEmail, isNotEmpty);
        expect(localizations.password, isNotEmpty);
        expect(localizations.enterPassword, isNotEmpty);
        expect(localizations.createPassword, isNotEmpty);
        expect(localizations.forgotPassword, isNotEmpty);
        expect(localizations.signIn, isNotEmpty);
        expect(localizations.orContinueWith, isNotEmpty);
        expect(localizations.continueWithGoogle, isNotEmpty);
        expect(localizations.continueWithApple, isNotEmpty);
        expect(localizations.dontHaveAccount, isNotEmpty);
        expect(localizations.signUp, isNotEmpty);
        expect(localizations.signUpToGetStarted, isNotEmpty);
        expect(localizations.createAccount, isNotEmpty);
        expect(localizations.confirmPassword, isNotEmpty);
        expect(localizations.confirmPasswordHint, isNotEmpty);
        expect(localizations.confirmYourPassword, isNotEmpty);
        expect(localizations.iAgreeToThe, isNotEmpty);
        expect(localizations.termsOfService, isNotEmpty);
        expect(localizations.and, isNotEmpty);
        expect(localizations.privacyPolicy, isNotEmpty);
        expect(localizations.pleaseAcceptTermsAndPrivacy, isNotEmpty);
        expect(localizations.appleSignupSuccessful, isNotEmpty);
        expect(localizations.alreadyHaveAccount, isNotEmpty);
      });

      test('should return non-empty strings for account management', () {
        expect(localizations.accounts, isNotEmpty);
        expect(localizations.accountName, isNotEmpty);
        expect(localizations.accountType, isNotEmpty);
        expect(localizations.accountColor, isNotEmpty);
        expect(localizations.accountIcon, isNotEmpty);
        expect(localizations.addAccount, isNotEmpty);
        expect(localizations.accountDetails, isNotEmpty);
        expect(localizations.accountSettings, isNotEmpty);
        expect(localizations.aboutAccounts, isNotEmpty);
        expect(localizations.accountNameRequired, isNotEmpty);
        expect(localizations.accountNameMinLength, isNotEmpty);
        expect(localizations.accountNameMaxLength, isNotEmpty);
        expect(localizations.accountNameInvalidCharacters, isNotEmpty);
        expect(localizations.accountTypeRequired, isNotEmpty);
        expect(localizations.accountRequired, isNotEmpty);
        expect(localizations.accountNotFound, isNotEmpty);
        expect(localizations.accountNotFoundError, isNotEmpty);
        expect(localizations.accountCreatedSuccessfully, isNotEmpty);
        expect(localizations.accountUpdatedSuccessfully, isNotEmpty);
        expect(localizations.activate, isNotEmpty);
        expect(localizations.active, isNotEmpty);
      });

      test('should return non-empty strings for transactions', () {
        expect(localizations.addTransaction, isNotEmpty);
        expect(localizations.addFirstTransaction, isNotEmpty);
        expect(localizations.addAndViewTransactions, isNotEmpty);
        expect(localizations.amount, isNotEmpty);
        expect(localizations.amountRequired, isNotEmpty);
        expect(localizations.amountInvalid, isNotEmpty);
        expect(localizations.amountTooLarge, isNotEmpty);
      });

      test('should return non-empty strings for categories', () {
        expect(localizations.addCategory, isNotEmpty);
        expect(localizations.addSubcategory, isNotEmpty);
        expect(localizations.aboutCategories, isNotEmpty);
        expect(localizations.allCategories, isNotEmpty);
      });

      test('should return non-empty strings for budgets', () {
        expect(localizations.activateBudgets, isNotEmpty);
        expect(localizations.activateBudgetsDescription, isNotEmpty);
        expect(localizations.adjustBudgets, isNotEmpty);
        expect(localizations.adjustByPercentage, isNotEmpty);
        expect(localizations.adjustByPercentageDescription, isNotEmpty);
      });

      test('should return non-empty strings for common UI', () {
        expect(localizations.all, isNotEmpty);
        expect(localizations.allAccounts, isNotEmpty);
        expect(localizations.and, isNotEmpty);
      });
    });

    group('Parameterized Methods', () {
      test('welcomeToApp should format name correctly', () {
        expect(localizations.welcomeToApp('John'), contains('John'));
        expect(localizations.welcomeToApp(''), isNotEmpty);
        expect(localizations.welcomeToApp('Test User'), contains('Test User'));
      });

      test('appleSignupFailed should format error correctly', () {
        expect(
          localizations.appleSignupFailed('Network error'),
          contains('Network error'),
        );
        expect(localizations.appleSignupFailed(''), isNotEmpty);
      });

      test('signOutFailed should format error correctly', () {
        expect(
          localizations.signOutFailed('Connection timeout'),
          contains('Connection timeout'),
        );
      });

      test('checkEmailForResetLink should format email correctly', () {
        expect(
          localizations.checkEmailForResetLink('<EMAIL>'),
          contains('<EMAIL>'),
        );
      });

      test('subcategoryCount should format count correctly', () {
        expect(localizations.subcategoryCount(0), contains('0'));
        expect(localizations.subcategoryCount(1), contains('1'));
        expect(localizations.subcategoryCount(5), contains('5'));
      });

      test('category deletion constraint messages should format correctly', () {
        expect(
          localizations.cannotDeleteCategoryWithTransactions('Food'),
          contains('Food'),
        );
        expect(
          localizations.cannotDeleteCategoryWithSubcategories('Food'),
          contains('Food'),
        );
        expect(
          localizations.cannotDeleteSubcategoryWithTransactions('Restaurant'),
          contains('Restaurant'),
        );
        expect(
          localizations.cannotDeleteSubcategoryWithChildren('Entertainment'),
          contains('Entertainment'),
        );
      });

      test('error messages should format correctly', () {
        expect(
          localizations.errorCheckingDeletionConstraints('Database error'),
          contains('Database error'),
        );
        expect(
          localizations.failedToDeleteCategory('Permission denied'),
          contains('Permission denied'),
        );
        expect(
          localizations.failedToDeleteSubcategory('Network error'),
          contains('Network error'),
        );
      });

      test('count-based messages should format correctly', () {
        expect(localizations.categoryHasTransactions(1), contains('1'));
        expect(localizations.categoryHasTransactions(5), contains('5'));
        expect(localizations.categoryHasSubcategories(1), contains('1'));
        expect(localizations.categoryHasSubcategories(3), contains('3'));
      });

      test('transactionsInCategory should format correctly', () {
        expect(localizations.transactionsInCategory('Food'), contains('Food'));
      });

      test('selectedBudgets should format correctly', () {
        expect(localizations.selectedBudgets(0), contains('0'));
        expect(localizations.selectedBudgets(1), contains('1'));
        expect(localizations.selectedBudgets(5), contains('5'));
      });
    });

    group('Large Scale Coverage Testing', () {
      test('should test 50+ auth-related getters for coverage', () {
        // Large list of auth-related getters to maximize coverage
        final authGetters = [
          localizations.welcomeBack,
          localizations.signInToAccount,
          localizations.email,
          localizations.enterEmailAddress,
          localizations.enterYourEmail,
          localizations.password,
          localizations.enterPassword,
          localizations.createPassword,
          localizations.forgotPassword,
          localizations.signIn,
          localizations.orContinueWith,
          localizations.continueWithGoogle,
          localizations.continueWithApple,
          localizations.dontHaveAccount,
          localizations.signUp,
          localizations.signUpToGetStarted,
          localizations.createAccount,
          localizations.confirmPassword,
          localizations.confirmPasswordHint,
          localizations.confirmYourPassword,
          localizations.iAgreeToThe,
          localizations.termsOfService,
          localizations.and,
          localizations.privacyPolicy,
          localizations.pleaseAcceptTermsAndPrivacy,
          localizations.appleSignupSuccessful,
          localizations.alreadyHaveAccount,
        ];

        // Verify all getters return non-empty strings
        for (var i = 0; i < authGetters.length; i++) {
          expect(
            authGetters[i],
            isNotEmpty,
            reason: 'Auth getter at index $i should not be empty',
          );
          expect(
            authGetters[i],
            isA<String>(),
            reason: 'Auth getter at index $i should be a String',
          );
        }
        expect(
          authGetters.length,
          greaterThan(25),
        ); // Ensure we're testing a good number
      });

      test('should test 30+ account-related getters for coverage', () {
        final accountGetters = [
          localizations.accounts,
          localizations.accountName,
          localizations.accountType,
          localizations.accountColor,
          localizations.accountIcon,
          localizations.addAccount,
          localizations.accountDetails,
          localizations.accountSettings,
          localizations.aboutAccounts,
          localizations.accountNameRequired,
          localizations.accountNameMinLength,
          localizations.accountNameMaxLength,
          localizations.accountNameInvalidCharacters,
          localizations.accountTypeRequired,
          localizations.accountRequired,
          localizations.accountNotFound,
          localizations.accountNotFoundError,
          localizations.accountCreatedSuccessfully,
          localizations.accountUpdatedSuccessfully,
          localizations.activate,
          localizations.active,
          localizations.accountsSection,
          localizations.accountsHelpDescription,
          localizations.accountClassificationRequired,
          localizations.accountDescriptionHint,
          localizations.accountDescriptionMaxLength,
          localizations.accountMayHaveBeenDeleted,
          localizations.accountNameHint,
          localizations.accountTypeClassificationMismatch,
          localizations.accountTypesTitle,
        ];

        for (var i = 0; i < accountGetters.length; i++) {
          expect(
            accountGetters[i],
            isNotEmpty,
            reason: 'Account getter at index $i should not be empty',
          );
          expect(
            accountGetters[i],
            isA<String>(),
            reason: 'Account getter at index $i should be a String',
          );
        }
        expect(
          accountGetters.length,
          greaterThan(25),
        ); // Ensure we're testing a good number
      });

      test('should test transaction-related getters for coverage', () {
        final transactionGetters = [
          localizations.addTransaction,
          localizations.addFirstTransaction,
          localizations.addAndViewTransactions,
          localizations.amount,
          localizations.amountRequired,
          localizations.amountInvalid,
          localizations.amountTooLarge,
        ];

        for (var i = 0; i < transactionGetters.length; i++) {
          expect(
            transactionGetters[i],
            isNotEmpty,
            reason: 'Transaction getter at index $i should not be empty',
          );
          expect(
            transactionGetters[i],
            isA<String>(),
            reason: 'Transaction getter at index $i should be a String',
          );
        }
      });

      test('should test category-related getters for coverage', () {
        final categoryGetters = [
          localizations.addCategory,
          localizations.addSubcategory,
          localizations.aboutCategories,
          localizations.allCategories,
        ];

        for (var i = 0; i < categoryGetters.length; i++) {
          expect(
            categoryGetters[i],
            isNotEmpty,
            reason: 'Category getter at index $i should not be empty',
          );
          expect(
            categoryGetters[i],
            isA<String>(),
            reason: 'Category getter at index $i should be a String',
          );
        }
      });

      test('should test budget-related getters for coverage', () {
        final budgetGetters = [
          localizations.activateBudgets,
          localizations.activateBudgetsDescription,
          localizations.adjustBudgets,
          localizations.adjustByPercentage,
          localizations.adjustByPercentageDescription,
        ];

        for (var i = 0; i < budgetGetters.length; i++) {
          expect(
            budgetGetters[i],
            isNotEmpty,
            reason: 'Budget getter at index $i should not be empty',
          );
          expect(
            budgetGetters[i],
            isA<String>(),
            reason: 'Budget getter at index $i should be a String',
          );
        }
      });

      test('should test common UI getters for coverage', () {
        final commonGetters = [
          localizations.all,
          localizations.allAccounts,
          localizations.and,
          localizations.activate,
          localizations.active,
        ];

        for (var i = 0; i < commonGetters.length; i++) {
          expect(
            commonGetters[i],
            isNotEmpty,
            reason: 'Common UI getter at index $i should not be empty',
          );
          expect(
            commonGetters[i],
            isA<String>(),
            reason: 'Common UI getter at index $i should be a String',
          );
        }
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle parameterized methods with special characters', () {
        expect(localizations.welcomeToApp("John O'Connor"), isNotEmpty);
        expect(localizations.welcomeToApp('测试用户'), isNotEmpty);
        expect(localizations.welcomeToApp('User@123'), isNotEmpty);
      });

      test('should handle error messages with special characters', () {
        expect(
          localizations.appleSignupFailed('Error: "Network timeout"'),
          isNotEmpty,
        );
        expect(
          localizations.signOutFailed('Connection failed & retry'),
          isNotEmpty,
        );
      });

      test('should handle category names with special characters', () {
        expect(
          localizations.cannotDeleteCategoryWithTransactions('Food & Dining'),
          isNotEmpty,
        );
        expect(
          localizations.transactionsInCategory('Health & Fitness'),
          isNotEmpty,
        );
      });

      test('should handle count edge cases', () {
        expect(localizations.subcategoryCount(-1), isNotEmpty);
        expect(localizations.categoryHasTransactions(0), isNotEmpty);
        expect(localizations.selectedBudgets(1000), isNotEmpty);
      });
    });

    group('String Content Validation', () {
      test('should not contain null or empty required strings', () {
        final criticalStrings = [
          localizations.welcomeBack,
          localizations.email,
          localizations.password,
          localizations.signIn,
          localizations.accounts,
          localizations.amount,
        ];

        for (var i = 0; i < criticalStrings.length; i++) {
          expect(
            criticalStrings[i],
            isNotNull,
            reason: 'Critical string at index $i should not be null',
          );
          expect(
            criticalStrings[i],
            isNotEmpty,
            reason: 'Critical string at index $i should not be empty',
          );
          expect(
            criticalStrings[i].trim(),
            isNotEmpty,
            reason: 'Critical string at index $i should not be just whitespace',
          );
        }
      });

      test('should contain expected string patterns', () {
        expect(localizations.signInToAccount.toLowerCase(), contains('sign'));
        expect(localizations.createAccount.toLowerCase(), contains('account'));
        expect(
          localizations.enterEmailAddress.toLowerCase(),
          contains('email'),
        );
        expect(localizations.enterPassword.toLowerCase(), contains('password'));
        expect(
          localizations.accountNameRequired.toLowerCase(),
          contains('required'),
        );
      });

      test('should have consistent capitalization patterns', () {
        expect(localizations.signIn, matches(RegExp('^[A-Z]')));
        expect(localizations.signUp, matches(RegExp('^[A-Z]')));
        expect(localizations.createAccount, matches(RegExp('^[A-Z]')));
        expect(localizations.addAccount, matches(RegExp('^[A-Z]')));
        expect(localizations.addTransaction, matches(RegExp('^[A-Z]')));
      });
    });

    group('Comprehensive Getter Validation', () {
      test('should test all parameterized methods exist and work', () {
        // Test all known parameterized methods
        expect(() => localizations.welcomeToApp('test'), returnsNormally);
        expect(() => localizations.appleSignupFailed('test'), returnsNormally);
        expect(() => localizations.signOutFailed('test'), returnsNormally);
        expect(
          () => localizations.checkEmailForResetLink('<EMAIL>'),
          returnsNormally,
        );
        expect(() => localizations.subcategoryCount(1), returnsNormally);
        expect(
          () => localizations.cannotDeleteCategoryWithTransactions('test'),
          returnsNormally,
        );
        expect(
          () => localizations.cannotDeleteCategoryWithSubcategories('test'),
          returnsNormally,
        );
        expect(
          () => localizations.cannotDeleteSubcategoryWithTransactions('test'),
          returnsNormally,
        );
        expect(
          () => localizations.cannotDeleteSubcategoryWithChildren('test'),
          returnsNormally,
        );
        expect(
          () => localizations.errorCheckingDeletionConstraints('test'),
          returnsNormally,
        );
        expect(
          () => localizations.failedToDeleteCategory('test'),
          returnsNormally,
        );
        expect(
          () => localizations.failedToDeleteSubcategory('test'),
          returnsNormally,
        );
        expect(() => localizations.categoryHasTransactions(1), returnsNormally);
        expect(
          () => localizations.categoryHasSubcategories(1),
          returnsNormally,
        );
        expect(
          () => localizations.transactionsInCategory('test'),
          returnsNormally,
        );
        expect(() => localizations.selectedBudgets(1), returnsNormally);
      });

      test('should test parameterized methods return expected formats', () {
        // Verify that parameterized methods return properly formatted strings
        final welcome = localizations.welcomeToApp('TestUser');
        expect(welcome, contains('TestUser'));
        expect(welcome, isA<String>());

        final error = localizations.appleSignupFailed('TestError');
        expect(error, contains('TestError'));
        expect(error, isA<String>());

        final count = localizations.subcategoryCount(42);
        expect(count, contains('42'));
        expect(count, isA<String>());
      });
    });

    group('Development and Firebase Getters', () {
      test('should test development and Firebase-related getters', () {
        final developmentGetters = [
          localizations.environment,
          localizations.firebaseProject,
          localizations.testFirebaseServices,
          localizations.firebaseServicesStatus,
          localizations.firebaseAuth,
          localizations.firestore,
          localizations.signOut,
          localizations.tryAgain,
          localizations.suggestions,
          localizations.or,
        ];

        for (var i = 0; i < developmentGetters.length; i++) {
          expect(
            developmentGetters[i],
            isNotEmpty,
            reason: 'Development getter at index $i should not be empty',
          );
          expect(
            developmentGetters[i],
            isA<String>(),
            reason: 'Development getter at index $i should be a String',
          );
        }
      });
    });

    group('Password Reset Getters', () {
      test('should test password reset related getters', () {
        final passwordResetGetters = [
          localizations.resetPassword,
          localizations.checkYourEmail,
          localizations.enterEmailForReset,
          localizations.passwordResetEmailSent,
          localizations.sendResetLink,
          localizations.passwordResetEmailSentSuccess,
          localizations.unexpectedError,
          localizations.userNotFound,
          localizations.invalidEmail,
          localizations.tooManyRequests,
          localizations.emailSentSuccessfully,
          localizations.checkSpamFolder,
          localizations.resendEmail,
          localizations.backToSignIn,
        ];

        for (var i = 0; i < passwordResetGetters.length; i++) {
          expect(
            passwordResetGetters[i],
            isNotEmpty,
            reason: 'Password reset getter at index $i should not be empty',
          );
          expect(
            passwordResetGetters[i],
            isA<String>(),
            reason: 'Password reset getter at index $i should be a String',
          );
        }
      });
    });

    group('Email Verification Getters', () {
      test('should test email verification related getters', () {
        final emailVerificationGetters = [
          localizations.verifyYourEmail,
          localizations.verificationEmailSentTo,
        ];

        for (var i = 0; i < emailVerificationGetters.length; i++) {
          expect(
            emailVerificationGetters[i],
            isNotEmpty,
            reason: 'Email verification getter at index $i should not be empty',
          );
          expect(
            emailVerificationGetters[i],
            isA<String>(),
            reason: 'Email verification getter at index $i should be a String',
          );
        }
      });
    });

    group('Authentication Success Messages Getters', () {
      test('should test authentication success message getters', () {
        final authSuccessGetters = [
          localizations.googleSignInSuccess,
          localizations.appleLoginSuccessful,
          localizations.retry,
        ];

        for (var i = 0; i < authSuccessGetters.length; i++) {
          expect(
            authSuccessGetters[i],
            isNotEmpty,
            reason: 'Auth success getter at index $i should not be empty',
          );
          expect(
            authSuccessGetters[i],
            isA<String>(),
            reason: 'Auth success getter at index $i should be a String',
          );
        }
      });
    });

    group('General UI Messages Getters', () {
      test('should test general UI message getters', () {
        final generalUIGetters = [
          localizations.information,
          localizations.warning,
          localizations.error,
          localizations.criticalError,
          localizations.sort,
          localizations.settings,
          localizations.sortBy,
          localizations.name,
          localizations.balance,
          localizations.dateCreated,
          localizations.primary,
          localizations.edit,
          localizations.delete,
          localizations.clearAll,
        ];

        for (var i = 0; i < generalUIGetters.length; i++) {
          expect(
            generalUIGetters[i],
            isNotEmpty,
            reason: 'General UI getter at index $i should not be empty',
          );
          expect(
            generalUIGetters[i],
            isA<String>(),
            reason: 'General UI getter at index $i should be a String',
          );
        }
      });
    });

    group('Account Types and Status Getters', () {
      test('should test account types and status getters', () {
        final accountTypesGetters = [
          localizations.filterAccounts,
          localizations.assets,
          localizations.liabilities,
          localizations.totalAssets,
          localizations.netWorth,
          localizations.errorLoadingAccounts,
          localizations.inactive,
          localizations.checkingAccount,
          localizations.savingsAccount,
          localizations.creditCard,
          localizations.cashAccount,
          localizations.investmentAccount,
          localizations.loanAccount,
        ];

        for (var i = 0; i < accountTypesGetters.length; i++) {
          expect(
            accountTypesGetters[i],
            isNotEmpty,
            reason: 'Account types getter at index $i should not be empty',
          );
          expect(
            accountTypesGetters[i],
            isA<String>(),
            reason: 'Account types getter at index $i should be a String',
          );
        }
      });
    });

    group('Coverage Expansion Testing - Part 1', () {
      test('should test additional existing getters batch 1', () {
        // Test additional existing getters to improve coverage
        final additionalGetters = [
          localizations.transactions,
          localizations.categories,
          localizations.budgets,
          localizations.goals,
          localizations.profile,
          localizations.help,
          localizations.feedback,
          localizations.close,
          localizations.cancel,
          localizations.save,
          localizations.saveChanges,
          localizations.income,
          localizations.expense,
          localizations.transfer,
          localizations.currency,
          localizations.description,
          localizations.loading,
          localizations.apply,
        ];

        for (var i = 0; i < additionalGetters.length; i++) {
          expect(
            additionalGetters[i],
            isNotEmpty,
            reason: 'Additional getter at index $i should not be empty',
          );
          expect(
            additionalGetters[i],
            isA<String>(),
            reason: 'Additional getter at index $i should be a String',
          );
        }

        // Verify we tested a reasonable number
        expect(additionalGetters.length, greaterThan(15));
      });
    });

    group('Coverage Expansion Testing - Part 2', () {
      test('should test additional existing getters batch 2', () {
        final additionalGetters2 = [
          localizations.date,
          localizations.selectDate,
          localizations.notes,
          localizations.required,
          localizations.optional,
          localizations.creating,
          localizations.updating,
          localizations.saving,
          localizations.confirm,
          localizations.dismiss,
          localizations.ok,
          localizations.gotIt,
          localizations.goBack,
          localizations.viewAll,
          localizations.monthly,
          localizations.yearly,
          localizations.custom,
          localizations.predefined,
        ];

        for (var i = 0; i < additionalGetters2.length; i++) {
          expect(
            additionalGetters2[i],
            isNotEmpty,
            reason: 'Additional getter 2 at index $i should not be empty',
          );
          expect(
            additionalGetters2[i],
            isA<String>(),
            reason: 'Additional getter 2 at index $i should be a String',
          );
        }

        expect(additionalGetters2.length, greaterThan(15));
      });
    });

    group('Coverage Expansion Testing - Part 3', () {
      test('should test additional existing getters batch 3', () {
        final additionalGetters3 = [
          localizations.spent,
          localizations.remaining,
          localizations.overBudget,
          localizations.onTrack,
          localizations.nearLimit,
          localizations.comingSoon,
          localizations.welcome,
          localizations.noEmail,
          localizations.increment,
          localizations.budApp,
          localizations.welcomeBackSuccess,
          localizations.asset,
          localizations.liability,
          localizations.classification,
          localizations.lastUpdated,
          localizations.currentBalance,
          localizations.quickActions,
          localizations.manageAccounts,
        ];

        for (var i = 0; i < additionalGetters3.length; i++) {
          expect(
            additionalGetters3[i],
            isNotEmpty,
            reason: 'Additional getter 3 at index $i should not be empty',
          );
          expect(
            additionalGetters3[i],
            isA<String>(),
            reason: 'Additional getter 3 at index $i should be a String',
          );
        }

        expect(additionalGetters3.length, greaterThan(15));
      });
    });

    group('Comprehensive Coverage Testing - Batch 4', () {
      test('should test biometric error getters for coverage', () {
        final biometricErrorGetters = [
          localizations.biometricUnknownError,
          localizations.biometricPasscodeNotSet,
          localizations.biometricNotEnrolled,
          localizations.biometricNotAvailable,
          localizations.biometricUserCancelled,
          localizations.biometricUserFallback,
          localizations.biometricSystemCancelled,
          localizations.biometricInvalidContext,
          localizations.biometricLockedOut,
          localizations.biometricPermanentlyLockedOut,
          localizations.biometricTooManyAttempts,
          localizations.biometricPlatformError,
          localizations.biometricSetupRequired,
          localizations.biometricSetupMessage,
          localizations.biometricSettingsDescription,
          localizations.biometricAuthRequired,
          localizations.biometricAuthGateDescription,
          localizations.authenticating,
        ];

        for (var i = 0; i < biometricErrorGetters.length; i++) {
          expect(
            biometricErrorGetters[i],
            isNotEmpty,
            reason: 'Biometric error getter at index $i should not be empty',
          );
          expect(
            biometricErrorGetters[i],
            isA<String>(),
            reason: 'Biometric error getter at index $i should be a String',
          );
        }
      });

      test('should test time period getters for coverage', () {
        final timePeriodGetters = [
          localizations.selectTimePeriod,
          localizations.currentMonth,
          localizations.thisMonth,
          localizations.selectMonth,
          localizations.selectYear,
          localizations.periodSelector,
          localizations.previousPeriod,
          localizations.nextPeriod,
          localizations.cannotSelectFuturePeriod,
          localizations.invalidPeriodSelection,
          localizations.periodSelectionError,
          localizations.weekly,
          localizations.quarterly,
          localizations.select,
        ];

        for (var i = 0; i < timePeriodGetters.length; i++) {
          expect(
            timePeriodGetters[i],
            isNotEmpty,
            reason: 'Time period getter at index $i should not be empty',
          );
          expect(
            timePeriodGetters[i],
            isA<String>(),
            reason: 'Time period getter at index $i should be a String',
          );
        }
      });

      test('should test bulk budget operation getters for coverage', () {
        final bulkBudgetGetters = [
          localizations.bulkBudgetOperations,
          localizations.selectOperation,
          localizations.adjustByPercentage,
          localizations.adjustByPercentageDescription,
          localizations.percentageChange,
          localizations.percentageChangeHint,
          localizations.percentageRequired,
          localizations.invalidPercentage,
          localizations.percentageTooLow,
          localizations.percentageTooHigh,
          localizations.deactivateBudgets,
          localizations.deactivateBudgetsDescription,
          localizations.activateBudgets,
          localizations.activateBudgetsDescription,
          localizations.deleteBudgets,
          localizations.deleteBudgetsDescription,
          localizations.adjustBudgets,
          localizations.budgetsAdjusted,
          localizations.budgetsActivated,
          localizations.budgetsDeactivated,
          localizations.budgetsDeleted,
          localizations.bulkOperationFailed,
        ];

        for (var i = 0; i < bulkBudgetGetters.length; i++) {
          expect(
            bulkBudgetGetters[i],
            isNotEmpty,
            reason: 'Bulk budget getter at index $i should not be empty',
          );
          expect(
            bulkBudgetGetters[i],
            isA<String>(),
            reason: 'Bulk budget getter at index $i should be a String',
          );
        }
      });

      test('should test currency settings getters for coverage', () {
        final currencySettingsGetters = [
          localizations.currencySettings,
          localizations.selectCurrency,
          localizations.currencySettingsDescription,
          localizations.availableCurrencies,
          localizations.preview,
          localizations.currencyUpdatedSuccessfully,
          localizations.failedToUpdateCurrency,
        ];

        for (var i = 0; i < currencySettingsGetters.length; i++) {
          expect(
            currencySettingsGetters[i],
            isNotEmpty,
            reason: 'Currency settings getter at index $i should not be empty',
          );
          expect(
            currencySettingsGetters[i],
            isA<String>(),
            reason: 'Currency settings getter at index $i should be a String',
          );
        }
      });
    });

    group('Comprehensive Coverage Testing - Batch 5', () {
      test('should test comprehensive error message getters for coverage', () {
        final errorMessageGetters = [
          localizations.networkError,
          localizations.permissionError,
          localizations.notFoundError,
          localizations.genericError,
          localizations.userNotFoundError,
          localizations.wrongPasswordError,
          localizations.emailAlreadyInUseError,
          localizations.weakPasswordError,
          localizations.invalidEmailError,
          localizations.userDisabledError,
          localizations.tooManyRequestsError,
          localizations.operationNotAllowedError,
          localizations.requiresRecentLoginError,
          localizations.authenticationError,
          localizations.permissionDeniedError,
          localizations.serviceUnavailableError,
          localizations.timeoutError,
          localizations.documentNotFoundError,
          localizations.documentAlreadyExistsError,
          localizations.quotaExceededError,
          localizations.preconditionFailedError,
          localizations.operationAbortedError,
          localizations.outOfRangeError,
          localizations.featureNotImplementedError,
          localizations.internalError,
          localizations.dataLossError,
          localizations.unauthenticatedError,
          localizations.databaseError,
          localizations.invalidDateFormatError,
          localizations.invalidNumberFormatError,
          localizations.invalidFormatError,
          localizations.invalidArgumentError,
        ];

        for (var i = 0; i < errorMessageGetters.length; i++) {
          expect(
            errorMessageGetters[i],
            isNotEmpty,
            reason: 'Error message getter at index $i should not be empty',
          );
          expect(
            errorMessageGetters[i],
            isA<String>(),
            reason: 'Error message getter at index $i should be a String',
          );
        }
      });

      test('should test transaction operation getters for coverage', () {
        final transactionOperationGetters = [
          localizations.dismiss,
          localizations.errorDialogTitle,
          localizations.ok,
          localizations.amountTooLarge,
          localizations.accountRequired,
          localizations.dateRequired,
          localizations.descriptionTooLong,
          localizations.notesTooLong,
          localizations.transactionSaved,
          localizations.errorSavingTransaction,
          localizations.selectAccount,
          localizations.selectCategory,
          localizations.noCategory,
          localizations.enterAmount,
          localizations.enterDescription,
          localizations.enterNotes,
          localizations.saving,
          localizations.createTransaction,
          localizations.updateTransaction,
          localizations.transactionCreated,
          localizations.transactionUpdated,
          localizations.transactionDeleted,
          localizations.deletingTransaction,
          localizations.transactionDeleteError,
          localizations.transactionOperationError,
          localizations.operationTimeoutError,
          localizations.transactionNotFoundError,
          localizations.transactionAlreadyExistsError,
          localizations.operationFailedError,
          localizations.invalidDataError,
          localizations.featureNotAvailableError,
          localizations.internalServerError,
          localizations.dataCorruptionError,
          localizations.authenticationRequiredError,
          localizations.firestoreError,
          localizations.accountNotFoundError,
          localizations.invalidAmountError,
          localizations.validationError,
          localizations.transactionCreateError,
          localizations.sameAccountError,
          localizations.destinationAccountRequired,
        ];

        for (var i = 0; i < transactionOperationGetters.length; i++) {
          expect(
            transactionOperationGetters[i],
            isNotEmpty,
            reason:
                'Transaction operation getter at index $i should not be empty',
          );
          expect(
            transactionOperationGetters[i],
            isA<String>(),
            reason:
                'Transaction operation getter at index $i should be a String',
          );
        }
      });

      test(
        'should test transaction search and filter getters for coverage',
        () {
          final transactionSearchGetters = [
            localizations.filterTransactions,
            localizations.searchTransactions,
            localizations.searchTransactionsHint,
            localizations.noTransactionsFound,
            localizations.tryAdjustingFilters,
            localizations.clearFilters,
            localizations.errorLoadingTransactions,
            localizations.noTransactionsDescription,
            localizations.addFirstTransaction,
            localizations.quickTips,
            localizations.tipCreateAccounts,
            localizations.tipCreateCategories,
            localizations.tipRecordTransactions,
            localizations.transactionNotFound,
            localizations.transactionMayHaveBeenDeleted,
            localizations.deleteTransaction,
            localizations.deleteTransactionConfirmation,
            localizations.editTransaction,
            localizations.errorLoadingTransaction,
            localizations.transactionUpdateError,
            localizations.errorUpdatingTransaction,
            localizations.transactionDetails,
            localizations.failed,
            localizations.accountsSection,
            localizations.categorySection,
            localizations.notesSection,
            localizations.metadataSection,
            localizations.createdAt,
            localizations.updatedAt,
            localizations.noNotes,
            localizations.errorLoadingTransactionDetails,
            localizations.pending,
            localizations.completed,
            localizations.cancelled,
            localizations.all,
            localizations.search,
            localizations.today,
            localizations.yesterday,
            localizations.metadata,
            localizations.transactionId,
            localizations.status,
            localizations.tags,
            localizations.from,
            localizations.to,
          ];

          for (var i = 0; i < transactionSearchGetters.length; i++) {
            expect(
              transactionSearchGetters[i],
              isNotEmpty,
              reason:
                  'Transaction search getter at index $i should not be empty',
            );
            expect(
              transactionSearchGetters[i],
              isA<String>(),
              reason:
                  'Transaction search getter at index $i should be a String',
            );
          }
        },
      );
    });

    // AGGRESSIVE COVERAGE EXPANSION - Testing 200+ additional methods
    group('Aggressive Coverage Expansion - Biometric & Security', () {
      test('should test all biometric authentication getters for coverage', () {
        final biometricGetters = [
          localizations.biometricAuthentication,
          localizations.signInWithBiometrics,
          localizations.useBiometrics,
          localizations.biometricUnknownError,
          localizations.biometricPasscodeNotSet,
          localizations.biometricNotEnrolled,
          localizations.biometricNotAvailable,
          localizations.biometricUserCancelled,
          localizations.biometricUserFallback,
          localizations.biometricSystemCancelled,
          localizations.biometricInvalidContext,
          localizations.biometricLockedOut,
          localizations.biometricPermanentlyLockedOut,
          localizations.biometricTooManyAttempts,
          localizations.biometricPlatformError,
          localizations.enableBiometricAuth,
          localizations.biometricAuthEnabled,
          localizations.biometricAuthDisabled,
          localizations.setupBiometrics,
          localizations.biometricSetupRequired,
          localizations.biometricSetupMessage,
          localizations.goToSettings,
          localizations.authenticationSettings,
          localizations.securitySettings,
          localizations.biometricSettingsDescription,
          localizations.biometricAuthRequired,
          localizations.biometricAuthGateDescription,
          localizations.authenticating,
        ];

        for (var i = 0; i < biometricGetters.length; i++) {
          expect(
            biometricGetters[i],
            isNotEmpty,
            reason: 'Biometric getter at index $i should not be empty',
          );
          expect(
            biometricGetters[i],
            isA<String>(),
            reason: 'Biometric getter at index $i should be a String',
          );
        }
      });

      test(
        'should test all time period and navigation getters for coverage',
        () {
          final timePeriodGetters = [
            localizations.selectTimePeriod,
            localizations.currentMonth,
            localizations.thisMonth,
            localizations.selectMonth,
            localizations.selectYear,
            localizations.periodSelector,
            localizations.previousPeriod,
            localizations.nextPeriod,
            localizations.cannotSelectFuturePeriod,
            localizations.invalidPeriodSelection,
            localizations.periodSelectionError,
            localizations.weekly,
            localizations.quarterly,
            localizations.select,
          ];

          for (var i = 0; i < timePeriodGetters.length; i++) {
            expect(
              timePeriodGetters[i],
              isNotEmpty,
              reason: 'Time period getter at index $i should not be empty',
            );
            expect(
              timePeriodGetters[i],
              isA<String>(),
              reason: 'Time period getter at index $i should be a String',
            );
          }
        },
      );

      test('should test all bulk budget operations getters for coverage', () {
        final bulkBudgetGetters = [
          localizations.bulkBudgetOperations,
          localizations.selectOperation,
          localizations.adjustByPercentage,
          localizations.adjustByPercentageDescription,
          localizations.percentageChange,
          localizations.percentageChangeHint,
          localizations.percentageRequired,
          localizations.invalidPercentage,
          localizations.percentageTooLow,
          localizations.percentageTooHigh,
          localizations.deactivateBudgets,
          localizations.deactivateBudgetsDescription,
          localizations.activateBudgets,
          localizations.activateBudgetsDescription,
          localizations.deleteBudgets,
          localizations.deleteBudgetsDescription,
          localizations.adjustBudgets,
          localizations.budgetsAdjusted,
          localizations.budgetsActivated,
          localizations.budgetsDeactivated,
          localizations.budgetsDeleted,
          localizations.bulkOperationFailed,
        ];

        for (var i = 0; i < bulkBudgetGetters.length; i++) {
          expect(
            bulkBudgetGetters[i],
            isNotEmpty,
            reason: 'Bulk budget getter at index $i should not be empty',
          );
          expect(
            bulkBudgetGetters[i],
            isA<String>(),
            reason: 'Bulk budget getter at index $i should be a String',
          );
        }
      });

      test('should test all currency settings getters for coverage', () {
        final currencyGetters = [
          localizations.currencySettings,
          localizations.selectCurrency,
          localizations.currencySettingsDescription,
          localizations.availableCurrencies,
          localizations.preview,
          localizations.currencyUpdatedSuccessfully,
          localizations.failedToUpdateCurrency,
        ];

        for (var i = 0; i < currencyGetters.length; i++) {
          expect(
            currencyGetters[i],
            isNotEmpty,
            reason: 'Currency getter at index $i should not be empty',
          );
          expect(
            currencyGetters[i],
            isA<String>(),
            reason: 'Currency getter at index $i should be a String',
          );
        }
      });
    });

    group('Aggressive Coverage Expansion - Category Management', () {
      test('should test all category management getters for coverage', () {
        final categoryManagementGetters = [
          localizations.manageCategories,
          localizations.createCategory,
          localizations.createSubcategory,
          localizations.editCategoryTitle,
          localizations.editSubcategoryTitle,
          localizations.categoryNameTooShort,
          localizations.categoryNameTooLong,
          localizations.categoryNameInvalidCharacters,
          localizations.categoryDescriptionTooLong,
          localizations.categoryTypeRequired,
          localizations.categoryColorInvalidFormat,
          localizations.categoryIconInvalidFormat,
          localizations.parentCategoryRequired,
          localizations.categoryCreatedSuccessfully,
          localizations.categoryUpdatedSuccessfully,
          localizations.errorCreatingCategory,
          localizations.errorUpdatingCategory,
          localizations.selectCategoryType,
          localizations.selectParentCategory,
          localizations.noParentCategory,
          localizations.selectColor,
          localizations.selectIcon,
          localizations.organizeTransactions,
          localizations.close,
          localizations.activate,
        ];

        for (var i = 0; i < categoryManagementGetters.length; i++) {
          expect(
            categoryManagementGetters[i],
            isNotEmpty,
            reason:
                'Category management getter at index $i should not be empty',
          );
          expect(
            categoryManagementGetters[i],
            isA<String>(),
            reason: 'Category management getter at index $i should be a String',
          );
        }
      });

      test(
        'should test all category deletion and constraints getters for coverage',
        () {
          final categoryDeletionGetters = [
            localizations.categoryDeletionConstraints,
            localizations.reassignTransactions,
            localizations.deleteAnyway,
            localizations.selectNewCategory,
            localizations.selectCategoryForReassignment,
            localizations.noAvailableCategories,
            localizations.reassignmentConfirmation,
            localizations.reassigningTransactions,
            localizations.transactionsReassignedSuccessfully,
            localizations.errorReassigningTransactions,
            localizations.categoryDeletedSuccessfully,
            localizations.viewTransactions,
            localizations.transactionList,
          ];

          for (var i = 0; i < categoryDeletionGetters.length; i++) {
            expect(
              categoryDeletionGetters[i],
              isNotEmpty,
              reason:
                  'Category deletion getter at index $i should not be empty',
            );
            expect(
              categoryDeletionGetters[i],
              isA<String>(),
              reason: 'Category deletion getter at index $i should be a String',
            );
          }
        },
      );
    });

    group('Aggressive Coverage Expansion - Additional UI Elements', () {
      test('should test all additional UI and form getters for coverage', () {
        final additionalUIGetters = [
          localizations.dismiss,
          localizations.errorDialogTitle,
          localizations.ok,
          localizations.amountTooLarge,
          localizations.accountRequired,
          localizations.dateRequired,
          localizations.descriptionTooLong,
          localizations.notesTooLong,
          localizations.selectAccount,
          localizations.selectCategory,
          localizations.noCategory,
          localizations.enterAmount,
          localizations.enterDescription,
          localizations.enterNotes,
          localizations.saving,
          localizations.createTransaction,
          localizations.updateTransaction,
          localizations.quickActions,
          localizations.manageAccounts,
          localizations.viewAndEditAccounts,
          localizations.manageTransactions,
          localizations.addAndViewTransactions,
          localizations.manageBudgets,
          localizations.createAndTrackBudgets,
          localizations.manageGoals,
          localizations.setAndTrackGoals,
          localizations.comingSoon,
          localizations.importExport,
          localizations.reports,
          localizations.help,
          localizations.feedback,
        ];

        for (var i = 0; i < additionalUIGetters.length; i++) {
          expect(
            additionalUIGetters[i],
            isNotEmpty,
            reason: 'Additional UI getter at index $i should not be empty',
          );
          expect(
            additionalUIGetters[i],
            isA<String>(),
            reason: 'Additional UI getter at index $i should be a String',
          );
        }
      });

      test(
        'should test all additional transaction form getters for coverage',
        () {
          final transactionFormGetters = [
            localizations.addTransaction,
            localizations.transactionType,
            localizations.transfer,
            localizations.amount,
            localizations.toAccount,
            localizations.fromAccount,
            localizations.category,
            localizations.date,
            localizations.selectDate,
            localizations.notes,
            localizations.required,
            localizations.amountRequired,
            localizations.amountInvalid,
            localizations.invalidNumber,
            localizations.pleaseSelect,
            localizations.noItemsAvailable,
            localizations.errorLoadingData,
          ];

          for (var i = 0; i < transactionFormGetters.length; i++) {
            expect(
              transactionFormGetters[i],
              isNotEmpty,
              reason: 'Transaction form getter at index $i should not be empty',
            );
            expect(
              transactionFormGetters[i],
              isA<String>(),
              reason: 'Transaction form getter at index $i should be a String',
            );
          }
        },
      );

      test(
        'should test all additional account management getters for coverage',
        () {
          final accountManagementGetters = [
            localizations.accountNameRequired,
            localizations.accountNameMinLength,
            localizations.accountNameMaxLength,
            localizations.accountNameInvalidCharacters,
            localizations.accountDescriptionMaxLength,
            localizations.initialBalanceRequired,
            localizations.initialBalanceInvalid,
            localizations.initialBalanceOutOfRange,
            localizations.initialBalanceMaxDecimals,
            localizations.currencyCodeRequired,
            localizations.currencyCodeInvalid,
            localizations.currencyCodeUnsupported,
            localizations.accountTypeRequired,
            localizations.accountClassificationRequired,
            localizations.accountTypeClassificationMismatch,
            localizations.colorHexInvalid,
            localizations.iconNameInvalid,
            localizations.accountName,
            localizations.accountNameHint,
            localizations.initialBalance,
            localizations.currency,
            localizations.accountDescriptionHint,
            localizations.customize,
            localizations.optional,
            localizations.accountColor,
            localizations.defaultColor,
            localizations.accountIcon,
            localizations.defaultIcon,
            localizations.creating,
            localizations.invalidBalance,
            localizations.errorCreatingAccount,
            localizations.creditCardAccount,
            localizations.description,
            localizations.accountDetails,
            localizations.editAccount,
            localizations.setPrimary,
            localizations.deactivateAccount,
            localizations.deleteAccount,
            localizations.currentBalance,
            localizations.classification,
            localizations.lastUpdated,
            localizations.accountSettings,
            localizations.primaryAccount,
            localizations.recentTransactions,
            localizations.viewAll,
            localizations.noTransactionsYet,
            localizations.transactionsWillAppearHere,
            localizations.accountNotFound,
            localizations.accountMayHaveBeenDeleted,
            localizations.goBack,
            localizations.errorLoadingAccount,
            localizations.setPrimaryAccount,
            localizations.setPrimaryAccountConfirmation,
            localizations.deactivateAccountConfirmation,
            localizations.deleteAccountConfirmation,
            localizations.cancel,
            localizations.confirm,
            localizations.asset,
            localizations.liability,
            localizations.updating,
            localizations.updateAccount,
            localizations.accountUpdatedSuccessfully,
            localizations.errorUpdatingAccount,
          ];

          for (var i = 0; i < accountManagementGetters.length; i++) {
            expect(
              accountManagementGetters[i],
              isNotEmpty,
              reason:
                  'Account management getter at index $i should not be empty',
            );
            expect(
              accountManagementGetters[i],
              isA<String>(),
              reason:
                  'Account management getter at index $i should be a String',
            );
          }
        },
      );
    });

    group('Aggressive Coverage Expansion - Budget & Goal Management', () {
      test('should test all budget management getters for coverage', () {
        final budgetManagementGetters = [
          localizations.createBudget,
          localizations.editBudget,
          localizations.budgetName,
          localizations.budgetAmount,
          localizations.budgetAmountHint,
          localizations.budgetAmountRequired,
          localizations.budgetPeriod,
          localizations.monthly,
          localizations.yearly,
          localizations.budgetCategory,
          localizations.allCategories,
          localizations.budgetDescription,
          localizations.budgetDescriptionHint,
          localizations.createFirstBudget,
          localizations.noBudgetsYet,
          localizations.noBudgetsDescription,
          localizations.noBudgetsForMonth,
          localizations.viewAllBudgets,
          localizations.budgetCreated,
          localizations.budgetUpdated,
          localizations.budgetDeleted,
          localizations.errorCreatingBudget,
          localizations.errorUpdatingBudget,
          localizations.errorDeletingBudget,
          localizations.errorLoadingBudget,
          localizations.deleteBudget,
          localizations.deleteBudgetConfirmation,
          localizations.discardChanges,
          localizations.discardChangesConfirmation,
          localizations.keepEditing,
          localizations.discard,
          localizations.saveChanges,
          localizations.spent,
          localizations.remaining,
          localizations.overBudget,
          localizations.onTrack,
          localizations.nearLimit,
          localizations.goals,
        ];

        for (var i = 0; i < budgetManagementGetters.length; i++) {
          expect(
            budgetManagementGetters[i],
            isNotEmpty,
            reason: 'Budget management getter at index $i should not be empty',
          );
          expect(
            budgetManagementGetters[i],
            isA<String>(),
            reason: 'Budget management getter at index $i should be a String',
          );
        }
      });

      test('should test all parameterized methods for coverage', () {
        // Test methods with parameters
        expect(localizations.welcomeToApp('TestUser'), isNotEmpty);
        expect(localizations.welcomeToApp('TestUser'), contains('TestUser'));

        expect(localizations.appleSignupFailed('Test error'), isNotEmpty);
        expect(
          localizations.appleSignupFailed('Test error'),
          contains('Test error'),
        );

        expect(localizations.signOutFailed('Test error'), isNotEmpty);
        expect(
          localizations.signOutFailed('Test error'),
          contains('Test error'),
        );

        expect(
          localizations.checkEmailForResetLink('<EMAIL>'),
          isNotEmpty,
        );
        expect(
          localizations.checkEmailForResetLink('<EMAIL>'),
          contains('<EMAIL>'),
        );

        expect(localizations.subcategoryCount(5), isNotEmpty);
        expect(localizations.subcategoryCount(5), contains('5'));
        expect(localizations.subcategoryCount(1), isNotEmpty);
        expect(localizations.subcategoryCount(0), isNotEmpty);

        expect(
          localizations.cannotDeleteCategoryWithTransactions('TestCategory'),
          isNotEmpty,
        );
        expect(
          localizations.cannotDeleteCategoryWithTransactions('TestCategory'),
          contains('TestCategory'),
        );

        expect(
          localizations.cannotDeleteCategoryWithSubcategories('TestCategory'),
          isNotEmpty,
        );
        expect(
          localizations.cannotDeleteCategoryWithSubcategories('TestCategory'),
          contains('TestCategory'),
        );

        expect(
          localizations.cannotDeleteSubcategoryWithTransactions(
            'TestSubcategory',
          ),
          isNotEmpty,
        );
        expect(
          localizations.cannotDeleteSubcategoryWithTransactions(
            'TestSubcategory',
          ),
          contains('TestSubcategory'),
        );

        expect(
          localizations.cannotDeleteSubcategoryWithChildren('TestSubcategory'),
          isNotEmpty,
        );
        expect(
          localizations.cannotDeleteSubcategoryWithChildren('TestSubcategory'),
          contains('TestSubcategory'),
        );

        expect(
          localizations.errorCheckingDeletionConstraints('Test error'),
          isNotEmpty,
        );
        expect(
          localizations.errorCheckingDeletionConstraints('Test error'),
          contains('Test error'),
        );

        expect(localizations.failedToDeleteCategory('Test error'), isNotEmpty);
        expect(
          localizations.failedToDeleteCategory('Test error'),
          contains('Test error'),
        );

        expect(
          localizations.failedToDeleteSubcategory('Test error'),
          isNotEmpty,
        );
        expect(
          localizations.failedToDeleteSubcategory('Test error'),
          contains('Test error'),
        );

        expect(localizations.categoryHasTransactions(10), isNotEmpty);
        expect(localizations.categoryHasTransactions(10), contains('10'));
        expect(localizations.categoryHasTransactions(1), isNotEmpty);
        expect(localizations.categoryHasTransactions(0), isNotEmpty);

        expect(localizations.categoryHasSubcategories(5), isNotEmpty);
        expect(localizations.categoryHasSubcategories(5), contains('5'));
        expect(localizations.categoryHasSubcategories(1), isNotEmpty);
        expect(localizations.categoryHasSubcategories(0), isNotEmpty);

        expect(
          localizations.transactionsInCategory('TestCategory'),
          isNotEmpty,
        );
        expect(
          localizations.transactionsInCategory('TestCategory'),
          contains('TestCategory'),
        );

        expect(localizations.selectedBudgets(3), isNotEmpty);
        expect(localizations.selectedBudgets(3), contains('3'));
        expect(localizations.selectedBudgets(1), isNotEmpty);
        expect(localizations.selectedBudgets(0), isNotEmpty);
      });
    });

    group('Aggressive Coverage Expansion - Final Batch', () {
      test('should test remaining uncovered getters for maximum coverage', () {
        // Test any remaining getters that might not be covered
        final remainingGetters = [
          localizations.loading,
          localizations.profile,
          localizations.categories,
          localizations.addCategory,
          localizations.filterCategories,
          localizations.categoryType,
          localizations.incomeCategories,
          localizations.expenseCategories,
          localizations.customCategories,
          localizations.predefinedCategories,
          localizations.showInactiveCategories,
          localizations.includeInactiveCategoriesInList,
          localizations.noCategoriesYet,
          localizations.noCategoriesDescription,
          localizations.createFirstCategory,
          localizations.learnAboutCategories,
          localizations.aboutCategories,
          localizations.categoriesHelpDescription,
          localizations.categoryTypesTitle,
          localizations.incomeDescription,
          localizations.expenseDescription,
          localizations.subcategoriesDescription,
          localizations.errorLoadingCategories,
          localizations.categoryDetails,
          localizations.editCategory,
          localizations.addSubcategory,
          localizations.deactivateCategory,
          localizations.deleteCategory,
          localizations.categoryName,
          localizations.categoryNameHint,
          localizations.categoryDescriptionHint,
          localizations.categoryColor,
          localizations.categoryIcon,
          localizations.parentCategory,
          localizations.rootCategory,
          localizations.subcategories,
          localizations.noSubcategories,
          localizations.expandCategory,
          localizations.collapseCategory,
          localizations.categoryNotFound,
          localizations.categoryMayHaveBeenDeleted,
          localizations.errorLoadingCategory,
          localizations.deactivateCategoryConfirmation,
          localizations.deleteCategoryConfirmation,
          localizations.subcategoryNotBelongToParent,
          localizations.categorySource,
          localizations.custom,
          localizations.predefined,
          localizations.income,
          localizations.expense,
        ];

        for (var i = 0; i < remainingGetters.length; i++) {
          expect(
            remainingGetters[i],
            isNotEmpty,
            reason: 'Remaining getter at index $i should not be empty',
          );
          expect(
            remainingGetters[i],
            isA<String>(),
            reason: 'Remaining getter at index $i should be a String',
          );
        }
      });
    });
  });
}
