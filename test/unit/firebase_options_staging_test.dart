import 'package:budapp/firebase_options_staging.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FirebaseOptionsStaging Platform Detection Tests', () {
    group('currentPlatform getter', () {
      test('should throw UnsupportedError for web platform', () {
        // Arrange - Test web platform error message format
        const expectedMessage =
            'FirebaseOptionsStaging have not been configured for web - '
            'you can reconfigure this by running the FlutterFire CLI again.';

        // Act & Assert - Test that the error message format is correct
        expect(
          () => throw UnsupportedError(expectedMessage),
          throwsA(
            isA<UnsupportedError>().having(
              (e) => e.message,
              'message',
              contains(
                'FirebaseOptionsStaging have not been configured for web',
              ),
            ),
          ),
        );
      });

      test('should return android options for Android platform', () {
        // Arrange
        debugDefaultTargetPlatformOverride = TargetPlatform.android;

        try {
          // Act
          final options = FirebaseOptionsStaging.currentPlatform;

          // Assert
          expect(options, isA<FirebaseOptions>());
          expect(options.projectId, equals('budapp-staging-1'));
          expect(options.appId, contains('android'));
          expect(options.iosBundleId, isNull);
        } finally {
          // Cleanup
          debugDefaultTargetPlatformOverride = null;
        }
      });

      test('should return iOS options for iOS platform', () {
        // Arrange
        debugDefaultTargetPlatformOverride = TargetPlatform.iOS;

        try {
          // Act
          final options = FirebaseOptionsStaging.currentPlatform;

          // Assert
          expect(options, isA<FirebaseOptions>());
          expect(options.projectId, equals('budapp-staging-1'));
          expect(options.appId, contains('ios'));
          expect(options.iosBundleId, equals('com.digitau.budapp.staging'));
        } finally {
          // Cleanup
          debugDefaultTargetPlatformOverride = null;
        }
      });

      test('should throw UnsupportedError for macOS platform', () {
        // Arrange
        debugDefaultTargetPlatformOverride = TargetPlatform.macOS;

        try {
          // Act & Assert
          expect(
            () => FirebaseOptionsStaging.currentPlatform,
            throwsA(
              isA<UnsupportedError>().having(
                (e) => e.message,
                'message',
                contains(
                  'FirebaseOptionsStaging have not been configured for macos',
                ),
              ),
            ),
          );
        } finally {
          // Cleanup
          debugDefaultTargetPlatformOverride = null;
        }
      });

      test('should throw UnsupportedError for Windows platform', () {
        // Arrange
        debugDefaultTargetPlatformOverride = TargetPlatform.windows;

        try {
          // Act & Assert
          expect(
            () => FirebaseOptionsStaging.currentPlatform,
            throwsA(
              isA<UnsupportedError>().having(
                (e) => e.message,
                'message',
                contains(
                  'FirebaseOptionsStaging have not been configured for windows',
                ),
              ),
            ),
          );
        } finally {
          // Cleanup
          debugDefaultTargetPlatformOverride = null;
        }
      });

      test('should throw UnsupportedError for Linux platform', () {
        // Arrange
        debugDefaultTargetPlatformOverride = TargetPlatform.linux;

        try {
          // Act & Assert
          expect(
            () => FirebaseOptionsStaging.currentPlatform,
            throwsA(
              isA<UnsupportedError>().having(
                (e) => e.message,
                'message',
                contains(
                  'FirebaseOptionsStaging have not been configured for linux',
                ),
              ),
            ),
          );
        } finally {
          // Cleanup
          debugDefaultTargetPlatformOverride = null;
        }
      });

      test('should throw UnsupportedError for fuchsia platform', () {
        // Arrange
        debugDefaultTargetPlatformOverride = TargetPlatform.fuchsia;

        try {
          // Act & Assert
          expect(
            () => FirebaseOptionsStaging.currentPlatform,
            throwsA(
              isA<UnsupportedError>().having(
                (e) => e.message,
                'message',
                contains(
                  'FirebaseOptionsStaging are not supported for this platform',
                ),
              ),
            ),
          );
        } finally {
          // Cleanup
          debugDefaultTargetPlatformOverride = null;
        }
      });
    });

    group('Static Configuration Validation', () {
      test('should have valid android configuration', () {
        // Arrange & Act
        const androidOptions = FirebaseOptionsStaging.android;

        // Assert
        expect(androidOptions, isA<FirebaseOptions>());
        expect(androidOptions.apiKey, isNotEmpty);
        expect(androidOptions.appId, isNotEmpty);
        expect(androidOptions.messagingSenderId, isNotEmpty);
        expect(androidOptions.projectId, equals('budapp-staging-1'));
        expect(
          androidOptions.storageBucket,
          equals('budapp-staging-1.firebasestorage.app'),
        );
        expect(androidOptions.iosBundleId, isNull);
      });

      test('should have valid iOS configuration', () {
        // Arrange & Act
        const iosOptions = FirebaseOptionsStaging.ios;

        // Assert
        expect(iosOptions, isA<FirebaseOptions>());
        expect(iosOptions.apiKey, isNotEmpty);
        expect(iosOptions.appId, isNotEmpty);
        expect(iosOptions.messagingSenderId, isNotEmpty);
        expect(iosOptions.projectId, equals('budapp-staging-1'));
        expect(
          iosOptions.storageBucket,
          equals('budapp-staging-1.firebasestorage.app'),
        );
        expect(iosOptions.iosBundleId, equals('com.digitau.budapp.staging'));
      });

      test(
        'should have consistent project configuration between platforms',
        () {
          // Arrange & Act
          const androidOptions = FirebaseOptionsStaging.android;
          const iosOptions = FirebaseOptionsStaging.ios;

          // Assert
          expect(androidOptions.apiKey, equals(iosOptions.apiKey));
          expect(
            androidOptions.messagingSenderId,
            equals(iosOptions.messagingSenderId),
          );
          expect(androidOptions.projectId, equals(iosOptions.projectId));
          expect(
            androidOptions.storageBucket,
            equals(iosOptions.storageBucket),
          );
        },
      );

      test('should have platform-specific app IDs', () {
        // Arrange & Act
        const androidOptions = FirebaseOptionsStaging.android;
        const iosOptions = FirebaseOptionsStaging.ios;

        // Assert
        expect(androidOptions.appId, contains('android'));
        expect(iosOptions.appId, contains('ios'));
        expect(androidOptions.appId, isNot(equals(iosOptions.appId)));
      });
    });

    group('Error Message Validation', () {
      test(
        'should have descriptive error messages for unsupported platforms',
        () {
          // Test that error messages are helpful and contain FlutterFire CLI guidance
          const expectedMessages = [
            'FirebaseOptionsStaging have not been configured for macos',
            'FirebaseOptionsStaging have not been configured for windows',
            'FirebaseOptionsStaging have not been configured for linux',
            'FirebaseOptionsStaging are not supported for this platform',
            'you can reconfigure this by running the FlutterFire CLI again',
          ];

          for (final message in expectedMessages) {
            expect(message, isNotEmpty);
            expect(message.length, greaterThan(10));
          }
        },
      );
    });
  });
}
