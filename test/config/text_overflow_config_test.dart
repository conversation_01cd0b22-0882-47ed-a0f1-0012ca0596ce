import 'package:budapp/config/text_overflow_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('TextOverflowConfig', () {
    test('should create config with default values', () {
      const config = TextOverflowConfig();

      expect(config.maxLines, isNull);
      expect(config.overflow, TextOverflow.ellipsis);
      expect(config.softWrap, true);
      expect(config.adaptive, false);
    });

    test('should create config with custom values', () {
      const config = TextOverflowConfig(
        maxLines: 2,
        overflow: TextOverflow.clip,
        softWrap: false,
        adaptive: true,
        minScaleFactor: 0.8,
        maxScaleFactor: 1.2,
      );

      expect(config.maxLines, 2);
      expect(config.overflow, TextOverflow.clip);
      expect(config.softWrap, false);
      expect(config.adaptive, true);
      expect(config.minScaleFactor, 0.8);
      expect(config.maxScaleFactor, 1.2);
    });

    test('should copy with modified properties', () {
      const original = TextOverflowConfig(
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      );

      final modified = original.copyWith(maxLines: 3, softWrap: false);

      expect(modified.maxLines, 3);
      expect(modified.overflow, TextOverflow.ellipsis); // unchanged
      expect(modified.softWrap, false);
    });
  });

  group('AppTextOverflow', () {
    test('should provide correct title configuration', () {
      const config = AppTextOverflow.title;

      expect(config.maxLines, 1);
      expect(config.overflow, TextOverflow.ellipsis);
      expect(config.softWrap, false);
    });

    test('should provide correct body configuration', () {
      const config = AppTextOverflow.body;

      expect(config.maxLines, 3);
      expect(config.overflow, TextOverflow.ellipsis);
      expect(config.softWrap, true);
    });

    test('should provide correct critical configuration', () {
      const config = AppTextOverflow.critical;

      expect(config.overflow, TextOverflow.visible);
      expect(config.softWrap, true);
      expect(config.adaptive, true);
      expect(config.minScaleFactor, 0.8);
      expect(config.maxScaleFactor, 1.2);
    });

    test('should provide correct button configuration', () {
      const config = AppTextOverflow.button;

      expect(config.maxLines, 1);
      expect(config.overflow, TextOverflow.ellipsis);
      expect(config.softWrap, false);
      expect(config.adaptive, true);
      expect(config.minScaleFactor, 0.85);
    });

    test('should return correct config for context', () {
      expect(
        AppTextOverflow.forContext(TextContext.title),
        AppTextOverflow.title,
      );
      expect(
        AppTextOverflow.forContext(TextContext.body),
        AppTextOverflow.body,
      );
      expect(
        AppTextOverflow.forContext(TextContext.label),
        AppTextOverflow.label,
      );
      expect(
        AppTextOverflow.forContext(TextContext.critical),
        AppTextOverflow.critical,
      );
      expect(
        AppTextOverflow.forContext(TextContext.note),
        AppTextOverflow.note,
      );
      expect(
        AppTextOverflow.forContext(TextContext.button),
        AppTextOverflow.button,
      );
      expect(
        AppTextOverflow.forContext(TextContext.listItem),
        AppTextOverflow.listItem,
      );
    });

    group('detectFromStyle', () {
      test('should detect title from large font size', () {
        const style = TextStyle(fontSize: 24);
        final config = AppTextOverflow.detectFromStyle(style);

        expect(config, AppTextOverflow.title);
      });

      test('should detect title from bold weight', () {
        const style = TextStyle(fontSize: 18, fontWeight: FontWeight.w600);
        final config = AppTextOverflow.detectFromStyle(style);

        expect(config, AppTextOverflow.title);
      });

      test('should detect label from small bold text', () {
        const style = TextStyle(fontSize: 14, fontWeight: FontWeight.w600);
        final config = AppTextOverflow.detectFromStyle(style);

        expect(config, AppTextOverflow.label);
      });

      test('should detect note from small font size', () {
        const style = TextStyle(fontSize: 12);
        final config = AppTextOverflow.detectFromStyle(style);

        expect(config, AppTextOverflow.note);
      });

      test('should default to body for normal text', () {
        const style = TextStyle(fontSize: 16);
        final config = AppTextOverflow.detectFromStyle(style);

        expect(config, AppTextOverflow.body);
      });

      test('should default to body for null style', () {
        final config = AppTextOverflow.detectFromStyle(null);

        expect(config, AppTextOverflow.body);
      });
    });
  });

  group('TextContext', () {
    test('should have all expected values', () {
      expect(TextContext.values, [
        TextContext.title,
        TextContext.body,
        TextContext.label,
        TextContext.critical,
        TextContext.note,
        TextContext.button,
        TextContext.listItem,
      ]);
    });
  });

  group('OverflowStrategy', () {
    test('should have all expected values', () {
      expect(OverflowStrategy.values, [
        OverflowStrategy.ellipsis,
        OverflowStrategy.fade,
        OverflowStrategy.clip,
        OverflowStrategy.visible,
        OverflowStrategy.scale,
        OverflowStrategy.wrap,
      ]);
    });
  });
}
