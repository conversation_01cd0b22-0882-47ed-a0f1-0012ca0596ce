import 'package:budapp/config/environment_config.dart';
import 'package:budapp/firebase_options_dev.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EnvironmentConfig', () {
    group('flavor detection', () {
      test('should return default flavor as dev', () {
        // Act & Assert
        expect(EnvironmentConfig.flavor, equals('dev'));
      });
    });

    group('environment name', () {
      test('should return Development for dev flavor', () {
        // Act & Assert
        expect(EnvironmentConfig.environmentName, equals('Development'));
      });

      // Note: Testing other flavors would require setting environment variables
      // which is not easily testable in unit tests. These would be covered
      // in integration tests or by running with different FLAVOR values.
    });

    group('app title', () {
      test('should return BudApp Dev for dev flavor', () {
        // Act & Assert
        expect(EnvironmentConfig.appTitle, equals('BudApp Dev'));
      });
    });

    group('theme color', () {
      test('should return orange for dev flavor', () {
        // Act & Assert
        expect(EnvironmentConfig.themeColor, equals(Colors.orange));
      });
    });

    group('firebase project name', () {
      test('should return budapp-dev for dev flavor', () {
        // Act & Assert
        expect(EnvironmentConfig.firebaseProjectName, equals('budapp-dev'));
      });
    });

    group('firebase options', () {
      test('should return dev firebase options for dev flavor', () {
        // Act & Assert
        expect(
          EnvironmentConfig.firebaseOptions,
          equals(FirebaseOptionsDev.currentPlatform),
        );
      });
    });

    group('environment flags', () {
      test('should return true for isDevelopment in dev flavor', () {
        // Act & Assert
        expect(EnvironmentConfig.isDevelopment, isTrue);
      });

      test('should return false for isStaging in dev flavor', () {
        // Act & Assert
        expect(EnvironmentConfig.isStaging, isFalse);
      });

      test('should return false for isProduction in dev flavor', () {
        // Act & Assert
        expect(EnvironmentConfig.isProduction, isFalse);
      });
    });

    group('firebase testing', () {
      test('should show firebase testing features', () {
        // Act & Assert
        expect(EnvironmentConfig.showFirebaseTesting, isTrue);
      });
    });

    group('firebase init message', () {
      test('should return correct init message for dev environment', () {
        // Act & Assert
        expect(
          EnvironmentConfig.firebaseInitMessage,
          equals(
            'Firebase initialized successfully for Development environment',
          ),
        );
      });
    });

    group('home page title', () {
      test('should return correct home page title for dev environment', () {
        // Act & Assert
        expect(
          EnvironmentConfig.homePageTitle,
          equals('BudApp Dev - Development'),
        );
      });
    });

    group('app check configuration', () {
      test('should use debug app check provider in development', () {
        // Act & Assert
        expect(EnvironmentConfig.useDebugAppCheckProvider, isTrue);
      });

      test('should return debug site key for web recaptcha', () {
        // Act & Assert
        expect(
          EnvironmentConfig.appCheckWebRecaptchaSiteKey,
          equals('debug-site-key'),
        );
      });

      test('should return debug android provider in development', () {
        // Act & Assert
        expect(
          EnvironmentConfig.androidAppCheckProvider,
          equals(AndroidProvider.debug),
        );
      });

      test('should return debug apple provider in development', () {
        // Act & Assert
        expect(
          EnvironmentConfig.appleAppCheckProvider,
          equals(AppleProvider.debug),
        );
      });
    });

    group('environment summary', () {
      test('should return complete environment summary for dev', () {
        // Act
        final summary = EnvironmentConfig.environmentSummary;

        // Assert
        expect(summary['flavor'], equals('dev'));
        expect(summary['environment'], equals('Development'));
        expect(summary['appTitle'], equals('BudApp Dev'));
        expect(summary['firebaseProject'], equals('budapp-dev'));
        expect(summary['themeColor'], equals(Colors.orange.toString()));
        expect(summary['isDevelopment'], isTrue);
        expect(summary['isStaging'], isFalse);
        expect(summary['isProduction'], isFalse);
        expect(summary['appCheckDebugMode'], isTrue);
      });

      test('should contain all expected keys in environment summary', () {
        // Act
        final summary = EnvironmentConfig.environmentSummary;

        // Assert
        expect(
          summary.keys,
          containsAll([
            'flavor',
            'environment',
            'appTitle',
            'firebaseProject',
            'themeColor',
            'isDevelopment',
            'isStaging',
            'isProduction',
            'appCheckDebugMode',
          ]),
        );
      });
    });

    group('initializeAppCheck', () {
      test('should handle Firebase not being initialized gracefully', () async {
        // Act & Assert - This tests that the method handles Firebase not being initialized
        // The method should catch exceptions and not crash the app
        // Since Firebase is not initialized in this test, we expect it to complete
        // without throwing unhandled exceptions
        await expectLater(EnvironmentConfig.initializeAppCheck(), completes);
      });
    });

    group('static configuration consistency', () {
      test('should have consistent environment detection', () {
        // Act & Assert - Verify that all environment detection methods agree
        final isDev = EnvironmentConfig.isDevelopment;
        final isStaging = EnvironmentConfig.isStaging;
        final isProd = EnvironmentConfig.isProduction;

        // Exactly one should be true
        final trueCount = [isDev, isStaging, isProd].where((x) => x).length;
        expect(trueCount, equals(1));
      });

      test('should have matching firebase options and project name', () {
        // Act
        final projectName = EnvironmentConfig.firebaseProjectName;
        final options = EnvironmentConfig.firebaseOptions;

        // Assert - For dev environment
        expect(projectName, equals('budapp-dev'));
        expect(options, equals(FirebaseOptionsDev.currentPlatform));
      });

      test('should have consistent app check provider configuration', () {
        // Act
        final useDebug = EnvironmentConfig.useDebugAppCheckProvider;
        final androidProvider = EnvironmentConfig.androidAppCheckProvider;
        final appleProvider = EnvironmentConfig.appleAppCheckProvider;

        // Assert - In dev environment, should use debug providers
        expect(useDebug, isTrue);
        expect(androidProvider, equals(AndroidProvider.debug));
        expect(appleProvider, equals(AppleProvider.debug));
      });
    });

    group('edge cases', () {
      test('should handle environment summary serialization', () {
        // Act
        final summary = EnvironmentConfig.environmentSummary;

        // Assert - Should be able to convert to string without errors
        expect(summary.toString, returnsNormally);

        // All values should be non-null
        for (final value in summary.values) {
          expect(value, isNotNull);
        }
      });

      test('should provide valid color objects', () {
        // Act
        final themeColor = EnvironmentConfig.themeColor;

        // Assert
        expect(themeColor, isA<Color>());
        expect(themeColor.r, isA<double>());
        expect(themeColor.a, greaterThan(0));
      });

      test('should provide non-empty string values', () {
        // Act & Assert
        expect(EnvironmentConfig.flavor, isNotEmpty);
        expect(EnvironmentConfig.environmentName, isNotEmpty);
        expect(EnvironmentConfig.appTitle, isNotEmpty);
        expect(EnvironmentConfig.firebaseProjectName, isNotEmpty);
        expect(EnvironmentConfig.firebaseInitMessage, isNotEmpty);
        expect(EnvironmentConfig.homePageTitle, isNotEmpty);
        expect(EnvironmentConfig.appCheckWebRecaptchaSiteKey, isNotEmpty);
      });
    });
  });
}
