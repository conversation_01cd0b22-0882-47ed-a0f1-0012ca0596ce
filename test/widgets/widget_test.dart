// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:budapp/config/app_theme.dart';
import 'package:budapp/features/auth/presentation/screens/login_screen.dart';
import 'package:budapp/providers/providers.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';

import '../helpers/test_wrapper.dart';

void main() {
  testWidgets('Login screen displays correctly', (WidgetTester tester) async {
    // Create mock Firestore instance
    final fakeFirestore = FakeFirebaseFirestore();

    // Build the login screen with TestWrapper and trigger a frame.
    await tester.pumpWidget(
      TestWrapper.createTestWidget(
        const LoginScreen(),
        theme: AppTheme.lightTheme,
        overrides: [
          firestoreProvider.overrideWithValue(fakeFirestore),
          globalLoadingProvider.overrideWith((ref) => false),
        ],
      ),
    );

    // Verify that login screen elements are present.
    expect(find.text('Welcome Back'), findsOneWidget);
    expect(find.text('Sign in to your account'), findsOneWidget);
    expect(find.text('Email'), findsOneWidget);
    expect(find.text('Password'), findsOneWidget);
    expect(find.text('Sign In'), findsOneWidget);
    expect(find.text('Continue with Google'), findsOneWidget);
    // Note: Apple sign-in text not included as it's not in localization yet
  });
}
