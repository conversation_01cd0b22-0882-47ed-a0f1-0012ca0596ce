import 'package:budapp/config/text_overflow_config.dart';
import 'package:budapp/widgets/common/app_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AppText', () {
    testWidgets('should render basic text', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(home: Scaffold(body: AppText('Hello World'))),
      );

      expect(find.text('Hello World'), findsOneWidget);
    });

    testWidgets('should apply title context configuration', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(home: Scaffold(body: AppText.title('Title Text'))),
      );

      final textWidget = tester.widget<Text>(find.byType(Text));
      expect(textWidget.maxLines, 1);
      expect(textWidget.overflow, TextOverflow.ellipsis);
      expect(textWidget.softWrap, false);
    });

    testWidgets('should apply body context configuration', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(body: AppText.body('Body text content')),
        ),
      );

      final textWidget = tester.widget<Text>(find.byType(Text));
      expect(textWidget.maxLines, 3);
      expect(textWidget.overflow, TextOverflow.ellipsis);
      expect(textWidget.softWrap, true);
    });

    testWidgets('should apply label context configuration', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(home: Scaffold(body: AppText.label('Label'))),
      );

      final textWidget = tester.widget<Text>(find.byType(Text));
      expect(textWidget.maxLines, 1);
      expect(textWidget.overflow, TextOverflow.ellipsis);
      expect(textWidget.softWrap, false);
    });

    testWidgets('should apply note context configuration', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(home: Scaffold(body: AppText.note('Note text'))),
      );

      final textWidget = tester.widget<Text>(find.byType(Text));
      expect(textWidget.maxLines, 2);
      expect(textWidget.overflow, TextOverflow.ellipsis);
      expect(textWidget.softWrap, true);
    });

    testWidgets('should apply button context configuration', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(home: Scaffold(body: AppText.button('Button Text'))),
      );

      // Button context uses adaptive text, so it should be wrapped in FittedBox
      expect(find.byType(FittedBox), findsOneWidget);
    });

    testWidgets('should apply critical context configuration', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(home: Scaffold(body: AppText.critical(r'$1,234.56'))),
      );

      // Critical context uses adaptive text, so it should be wrapped in FittedBox
      expect(find.byType(FittedBox), findsOneWidget);
    });

    testWidgets('should apply manual overflow configuration', (tester) async {
      const customConfig = TextOverflowConfig(
        maxLines: 5,
        overflow: TextOverflow.clip,
        softWrap: false,
      );

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AppText('Custom text', overflowConfig: customConfig),
          ),
        ),
      );

      final textWidget = tester.widget<Text>(find.byType(Text));
      expect(textWidget.maxLines, 5);
      expect(textWidget.overflow, TextOverflow.clip);
      expect(textWidget.softWrap, false);
    });

    testWidgets('should override context with manual parameters', (
      tester,
    ) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AppText.title(
              'Title with override',
              maxLines: 3,
              overflow: TextOverflow.visible,
            ),
          ),
        ),
      );

      final textWidget = tester.widget<Text>(find.byType(Text));
      expect(textWidget.maxLines, 3); // overridden
      expect(textWidget.overflow, TextOverflow.visible); // overridden
    });

    testWidgets('should auto-detect context from style', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AppText(
              'Large title text',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
          ),
        ),
      );

      final textWidget = tester.widget<Text>(find.byType(Text));
      // Should detect as title context
      expect(textWidget.maxLines, 1);
      expect(textWidget.overflow, TextOverflow.ellipsis);
      expect(textWidget.softWrap, false);
    });

    testWidgets('should pass through all Text widget properties', (
      tester,
    ) async {
      const style = TextStyle(color: Colors.red);

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AppText(
              'Styled text',
              style: style,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
              semanticsLabel: 'Semantic label',
            ),
          ),
        ),
      );

      final textWidget = tester.widget<Text>(find.byType(Text));
      expect(textWidget.style, style);
      expect(textWidget.textAlign, TextAlign.center);
      expect(textWidget.textDirection, TextDirection.rtl);
      expect(textWidget.semanticsLabel, 'Semantic label');
    });

    testWidgets('should handle adaptive text with FittedBox', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 100,
              child: AppText.critical(
                'Very long critical text that should scale',
              ),
            ),
          ),
        ),
      );

      expect(find.byType(FittedBox), findsOneWidget);
      expect(find.byType(LayoutBuilder), findsOneWidget);

      final fittedBox = tester.widget<FittedBox>(find.byType(FittedBox));
      expect(fittedBox.fit, BoxFit.scaleDown);
    });

    testWidgets('should use correct alignment for adaptive text', (
      tester,
    ) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AppText.critical(
              'Critical text',
              textAlign: TextAlign.center,
            ),
          ),
        ),
      );

      final fittedBox = tester.widget<FittedBox>(find.byType(FittedBox));
      expect(fittedBox.alignment, Alignment.center);
    });
  });
}
