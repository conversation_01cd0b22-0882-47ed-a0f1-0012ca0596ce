import 'package:budapp/widgets/common/app_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../helpers/test_wrapper.dart';

void main() {
  group('AppTextFormField Tests', () {
    group('Basic Functionality', () {
      testWidgets('should render with basic configuration', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AppTextFormField(label: 'Test Label'),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Test Label'), findsOneWidget);
        expect(find.byType(TextFormField), findsOneWidget);
      });

      testWidgets('should render with hint text', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AppTextFormField(
              label: 'Test Label',
              hintText: 'Enter some text',
            ),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Enter some text'), findsOneWidget);
      });

      testWidgets('should render with initial value', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AppTextFormField(
              label: 'Test Label',
              initialValue: 'Initial text',
            ),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Initial text'), findsOneWidget);
      });

      testWidgets('should render with custom controller', (tester) async {
        final controller = TextEditingController(text: 'Controller text');

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AppTextFormField(label: 'Test Label', controller: controller),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Controller text'), findsOneWidget);
        controller.dispose();
      });
    });

    group('Floating Label Behavior', () {
      testWidgets('should show floating label when useFloatingLabel is true', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AppTextFormField(
              label: 'Floating Label',
              useFloatingLabel: true,
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Check that the floating label is present
        expect(find.text('Floating Label'), findsOneWidget);

        // Verify the TextFormField was created
        expect(find.byType(TextFormField), findsOneWidget);
      });

      testWidgets('should show static label when useFloatingLabel is false', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AppTextFormField(
              label: 'Static Label',
              useFloatingLabel: false,
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Should find label in Column structure (legacy mode)
        expect(find.text('Static Label'), findsOneWidget);
        expect(find.byType(Column), findsOneWidget);
      });

      testWidgets('should show required indicator with floating label', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AppTextFormField(
              label: 'Required Field',
              isRequired: true,
              useFloatingLabel: true,
            ),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Required Field *'), findsOneWidget);
      });

      testWidgets('should show required indicator with static label', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AppTextFormField(
              label: 'Required Field',
              isRequired: true,
              useFloatingLabel: false,
            ),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Required Field'), findsOneWidget);
        expect(find.text('*'), findsOneWidget);
      });
    });

    group('Password Field Functionality', () {
      testWidgets('should show password toggle when isPassword is true', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AppTextFormField(label: 'Password', isPassword: true),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.byIcon(Icons.visibility), findsOneWidget);
      });

      testWidgets('should toggle password visibility when icon is tapped', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AppTextFormField(label: 'Password', isPassword: true),
          ),
        );
        await tester.pumpAndSettle();

        // Initially should show visibility icon
        expect(find.byIcon(Icons.visibility), findsOneWidget);

        // Tap the visibility toggle
        await tester.tap(find.byIcon(Icons.visibility));
        await tester.pumpAndSettle();

        // Should now show visibility_off icon
        expect(find.byIcon(Icons.visibility_off), findsOneWidget);
      });
    });

    group('Validation', () {
      testWidgets('should call validator and show error', (tester) async {
        final formKey = GlobalKey<FormState>();

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Form(
              key: formKey,
              child: AppTextFormField(
                label: 'Validated Field',
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'Field is required';
                  }
                  return null;
                },
              ),
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Find the form and trigger validation
        final formFinder = find.byType(Form);
        expect(formFinder, findsOneWidget);

        // Manually trigger validation
        expect(formKey.currentState!.validate(), isFalse);
        await tester.pumpAndSettle();

        expect(find.text('Field is required'), findsOneWidget);
      });

      testWidgets('should not show error when validation passes', (
        tester,
      ) async {
        final controller = TextEditingController(text: 'Valid text');
        final formKey = GlobalKey<FormState>();

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Form(
              key: formKey,
              child: AppTextFormField(
                label: 'Validated Field',
                controller: controller,
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'Field is required';
                  }
                  return null;
                },
              ),
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Find the form and trigger validation
        final formFinder = find.byType(Form);
        expect(formFinder, findsOneWidget);

        // Manually trigger validation
        expect(formKey.currentState!.validate(), isTrue);
        await tester.pumpAndSettle();

        expect(find.text('Field is required'), findsNothing);

        controller.dispose();
      });

      testWidgets('should show custom error text', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AppTextFormField(
              label: 'Field with Error',
              errorText: 'Custom error message',
            ),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Custom error message'), findsOneWidget);
      });
    });

    group('State Management', () {
      testWidgets('should call onChanged when text changes', (tester) async {
        String? changedValue;

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AppTextFormField(
              label: 'Changing Field',
              onChanged: (value) {
                changedValue = value;
              },
            ),
          ),
        );
        await tester.pumpAndSettle();

        await tester.enterText(find.byType(TextFormField), 'New text');
        await tester.pumpAndSettle();

        expect(changedValue, equals('New text'));
      });

      testWidgets('should call onFieldSubmitted when submitted', (
        tester,
      ) async {
        String? submittedValue;

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AppTextFormField(
              label: 'Submittable Field',
              onFieldSubmitted: (value) {
                submittedValue = value;
              },
            ),
          ),
        );
        await tester.pumpAndSettle();

        await tester.enterText(find.byType(TextFormField), 'Submit text');
        await tester.testTextInput.receiveAction(TextInputAction.done);
        await tester.pumpAndSettle();

        expect(submittedValue, equals('Submit text'));
      });

      testWidgets('should handle focus changes', (tester) async {
        final focusNode = FocusNode();

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            AppTextFormField(label: 'Focus Field', focusNode: focusNode),
          ),
        );
        await tester.pumpAndSettle();

        expect(focusNode.hasFocus, isFalse);

        await tester.tap(find.byType(TextFormField));
        await tester.pumpAndSettle();

        expect(focusNode.hasFocus, isTrue);

        focusNode.dispose();
      });
    });

    group('Prefix and Suffix Icons', () {
      testWidgets('should show prefix icon', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AppTextFormField(
              label: 'Icon Field',
              prefixIcon: Icon(Icons.person),
            ),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.byIcon(Icons.person), findsOneWidget);
      });

      testWidgets('should show suffix icon', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AppTextFormField(
              label: 'Icon Field',
              suffixIcon: Icon(Icons.clear),
            ),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.byIcon(Icons.clear), findsOneWidget);
      });

      testWidgets('should prioritize password toggle over suffix icon', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AppTextFormField(
              label: 'Password Field',
              isPassword: true,
              suffixIcon: Icon(Icons.clear), // Should be ignored
            ),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.byIcon(Icons.visibility), findsOneWidget);
        expect(find.byIcon(Icons.clear), findsNothing);
      });
    });

    group('Helper and Error Text', () {
      testWidgets('should show helper text', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AppTextFormField(
              label: 'Helper Field',
              helperText: 'This is helpful information',
            ),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('This is helpful information'), findsOneWidget);
      });

      testWidgets('should show error text', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const AppTextFormField(
              label: 'Error Field',
              errorText: 'This is an error',
            ),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('This is an error'), findsOneWidget);
      });
    });
  });

  group('AppCurrencyFormField Tests', () {
    testWidgets('should render with currency-specific configuration', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const AppCurrencyFormField(label: 'Amount'),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text('Amount'), findsOneWidget);
      expect(find.byIcon(Icons.attach_money), findsOneWidget);
    });

    testWidgets('should have default hint text for currency', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const AppCurrencyFormField(label: 'Amount'),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text('0.00'), findsOneWidget);
    });

    testWidgets('should handle currency input correctly', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const AppCurrencyFormField(label: 'Amount'),
        ),
      );
      await tester.pumpAndSettle();

      await tester.enterText(find.byType(TextFormField), '123.45');
      await tester.pumpAndSettle();

      expect(find.text('123.45'), findsOneWidget);
    });
  });

  group('AppEmailFormField Tests', () {
    testWidgets('should render with email-specific configuration', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const AppEmailFormField()),
      );
      await tester.pumpAndSettle();

      // Check for the TextFormField with email configuration
      expect(find.byType(TextFormField), findsOneWidget);
      expect(find.byIcon(Icons.email_outlined), findsOneWidget);

      // Check that the label is present somewhere in the widget tree
      expect(
        find.byWidgetPredicate(
          (widget) => widget is Text && widget.data == 'Email *',
        ),
        findsWidgets,
      );
    });

    testWidgets('should have email hint text by default', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const AppEmailFormField()),
      );
      await tester.pumpAndSettle();

      expect(find.text('<EMAIL>'), findsOneWidget);
    });

    testWidgets('should be required by default', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const AppEmailFormField()),
      );
      await tester.pumpAndSettle();

      expect(find.text('Email *'), findsOneWidget);
    });

    testWidgets('should handle email input correctly', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const AppEmailFormField()),
      );
      await tester.pumpAndSettle();

      await tester.enterText(find.byType(TextFormField), '<EMAIL>');
      await tester.pumpAndSettle();

      expect(find.text('<EMAIL>'), findsOneWidget);
    });
  });

  group('AppPasswordFormField Tests', () {
    testWidgets('should render with password-specific configuration', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const AppPasswordFormField()),
      );
      await tester.pumpAndSettle();

      // Check for the TextFormField with password configuration
      expect(find.byType(TextFormField), findsOneWidget);
      expect(find.byIcon(Icons.lock_outlined), findsOneWidget);
      expect(find.byIcon(Icons.visibility), findsOneWidget);

      // Check that the label is present somewhere in the widget tree
      expect(
        find.byWidgetPredicate(
          (widget) => widget is Text && widget.data == 'Password *',
        ),
        findsWidgets,
      );
    });

    testWidgets('should be required by default', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const AppPasswordFormField()),
      );
      await tester.pumpAndSettle();

      expect(find.text('Password *'), findsOneWidget);
    });

    testWidgets('should toggle visibility when icon is tapped', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const AppPasswordFormField()),
      );
      await tester.pumpAndSettle();

      // Initially should show visibility icon
      expect(find.byIcon(Icons.visibility), findsOneWidget);

      // Tap the visibility toggle
      await tester.tap(find.byIcon(Icons.visibility));
      await tester.pumpAndSettle();

      // Should now show visibility_off icon
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });

    testWidgets('should handle password input correctly', (tester) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          AppPasswordFormField(controller: controller),
        ),
      );
      await tester.pumpAndSettle();

      await tester.enterText(find.byType(TextFormField), 'password123');
      await tester.pumpAndSettle();

      expect(controller.text, equals('password123'));
      controller.dispose();
    });
  });

  group('Edge Cases and Error Handling', () {
    testWidgets('should handle null validator gracefully', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const AppTextFormField(label: 'No Validator', validator: null),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text('No Validator'), findsOneWidget);
    });

    testWidgets('should handle empty label gracefully', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const AppTextFormField(label: '')),
      );
      await tester.pumpAndSettle();

      expect(find.byType(TextFormField), findsOneWidget);
    });

    testWidgets('should handle disabled state correctly', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const AppTextFormField(label: 'Disabled Field', enabled: false),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text('Disabled Field'), findsOneWidget);

      // Try to interact with disabled field
      await tester.tap(find.byType(TextFormField));
      await tester.pumpAndSettle();

      // Should still be present but not interactive
      expect(find.text('Disabled Field'), findsOneWidget);
    });

    testWidgets('should handle readOnly state correctly', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const AppTextFormField(
            label: 'ReadOnly Field',
            readOnly: true,
            initialValue: 'Cannot edit this',
          ),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text('ReadOnly Field'), findsOneWidget);
      expect(find.text('Cannot edit this'), findsOneWidget);
    });

    testWidgets('should handle very long text gracefully', (tester) async {
      const longText =
          'This is a very long text that should be handled gracefully by the text field without causing overflow or rendering issues in the UI';

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const AppTextFormField(
            label: 'Long Text Field',
            initialValue: longText,
            useFloatingLabel: true, // Ensure consistent label behavior
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Check that the TextFormField is rendered
      final textField = find.byType(TextFormField);
      expect(textField, findsOneWidget);

      // Verify the TextFormField has the correct initial value
      final textFormField = tester.widget<TextFormField>(textField);
      expect(textFormField.initialValue, equals(longText));

      // Check that the label is present somewhere in the widget tree
      // (it could be as labelText in InputDecoration or as a separate Text widget)
      expect(
        find.byWidgetPredicate(
          (widget) => widget is Text && widget.data == 'Long Text Field',
        ),
        findsWidgets,
      );
    });
  });
}
