import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/widgets/common/error_display.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../helpers/test_wrapper.dart';

void main() {
  group('ErrorDisplay', () {
    group('Basic Rendering', () {
      testWidgets('should render with default properties', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(ErrorDisplay(error: error)),
        );

        // Assert - Should render basic error display components
        expect(find.byType(ErrorDisplay), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
        expect(find.text('Something went wrong'), findsOneWidget);
        expect(
          find.text('An unexpected error occurred. Please try again.'),
          findsOneWidget,
        );
      });

      testWidgets('should render with custom title and message', (
        tester,
      ) async {
        // Arrange
        final error = Exception('Test error');
        const customTitle = 'Custom Error Title';
        const customMessage = 'Custom error message';

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ErrorDisplay(
              error: error,
              title: customTitle,
              message: customMessage,
            ),
          ),
        );

        // Assert - Should display custom title and message
        expect(find.text(customTitle), findsOneWidget);
        expect(find.text(customMessage), findsOneWidget);
        expect(find.text('Something went wrong'), findsNothing);
        expect(
          find.text('An unexpected error occurred. Please try again.'),
          findsNothing,
        );
      });

      testWidgets('should render with custom icon', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ErrorDisplay(error: error, icon: Icons.warning),
          ),
        );

        // Assert - Should display custom icon
        expect(find.byIcon(Icons.warning), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsNothing);
      });
    });

    group('Error Details Display', () {
      testWidgets('should not show error details by default', (tester) async {
        // Arrange
        final error = Exception('Detailed error message');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(ErrorDisplay(error: error)),
        );

        // Assert - Error details should not be visible
        expect(find.text('Exception: Detailed error message'), findsNothing);
        // No details container should be present when showDetails is false
        expect(find.byType(ErrorDisplay), findsOneWidget);
      });

      testWidgets('should show error details when showDetails is true', (
        tester,
      ) async {
        // Arrange
        final error = Exception('Detailed error message');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ErrorDisplay(error: error, showDetails: true),
          ),
        );

        // Assert - Error details should be visible
        expect(find.text('Exception: Detailed error message'), findsOneWidget);

        // Find the details container - there should be one for error details
        final containers = find.byType(Container);
        expect(containers, findsOneWidget); // Only the details container
      });

      testWidgets('should display different error types correctly', (
        tester,
      ) async {
        // Arrange
        const stringError = 'String error';

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ErrorDisplay(error: stringError, showDetails: true),
          ),
        );

        // Assert - String error should be displayed
        expect(find.text(stringError), findsOneWidget);
      });

      testWidgets('should style error details container correctly', (
        tester,
      ) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ErrorDisplay(error: error, showDetails: true),
          ),
        );

        // Assert - Container should have proper styling
        final containers = tester.widgetList<Container>(find.byType(Container));
        final detailsContainer =
            containers.first; // There should be only one details container

        expect(detailsContainer.decoration, isA<BoxDecoration>());
        final decoration = detailsContainer.decoration! as BoxDecoration;
        expect(decoration.borderRadius, isA<BorderRadius>());
        expect(decoration.border, isA<Border>());
      });
    });

    group('Retry Button', () {
      testWidgets('should not show retry button when onRetry is null', (
        tester,
      ) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(ErrorDisplay(error: error)),
        );

        // Assert - Retry button should not be visible
        expect(find.text('Try Again'), findsNothing);
        expect(find.byIcon(Icons.refresh), findsNothing);
      });

      testWidgets('should show retry button when onRetry is provided', (
        tester,
      ) async {
        // Arrange
        final error = Exception('Test error');
        void onRetry() {
          // Test callback
        }

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ErrorDisplay(error: error, onRetry: onRetry),
          ),
        );

        // Assert - Retry button should be visible
        expect(find.text('Try Again'), findsOneWidget);
        expect(find.byIcon(Icons.refresh), findsOneWidget);
      });

      testWidgets('should call onRetry when retry button is pressed', (
        tester,
      ) async {
        // Arrange
        final error = Exception('Test error');
        var retryPressed = false;
        void onRetry() {
          retryPressed = true;
        }

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ErrorDisplay(error: error, onRetry: onRetry),
          ),
        );

        await tester.tap(find.text('Try Again'));
        await tester.pumpAndSettle();

        // Assert - onRetry should have been called
        expect(retryPressed, isTrue);
      });

      testWidgets('should style retry button correctly', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ErrorDisplay(error: error, onRetry: () {}),
          ),
        );

        // Assert - Button should be present with correct styling
        expect(find.text('Try Again'), findsOneWidget);
        expect(find.byIcon(Icons.refresh), findsOneWidget);
      });
    });

    group('Layout and Styling', () {
      testWidgets('should use correct spacing throughout', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ErrorDisplay(error: error, onRetry: () {}, showDetails: true),
          ),
        );

        // Assert - Should have proper spacing elements
        final sizedBoxes = find.byType(SizedBox);
        expect(
          sizedBoxes,
          findsAtLeastNWidgets(3),
        ); // Multiple spacing elements
      });

      testWidgets('should center content properly', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(ErrorDisplay(error: error)),
        );

        // Assert - Should be wrapped in Center widget and have Column for layout
        expect(find.byType(Center), findsAtLeastNWidgets(1));
        expect(find.byType(Column), findsOneWidget);
      });

      testWidgets('should apply proper padding', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(ErrorDisplay(error: error)),
        );

        // Assert - Should have Padding widget with correct padding
        final padding = tester.widget<Padding>(find.byType(Padding).first);
        expect(padding.padding, const EdgeInsets.all(DesignTokens.spacing24));
      });

      testWidgets('should use correct text styles', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(ErrorDisplay(error: error)),
        );

        // Assert - Text widgets should exist with proper styling
        final titleText = find.text('Something went wrong');
        final messageText = find.text(
          'An unexpected error occurred. Please try again.',
        );

        expect(titleText, findsOneWidget);
        expect(messageText, findsOneWidget);
      });

      testWidgets('should size icon correctly', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(ErrorDisplay(error: error)),
        );

        // Assert - Icon should have correct size
        final icon = tester.widget<Icon>(find.byIcon(Icons.error_outline));
        expect(icon.size, 64);
      });
    });

    group('Theme Integration', () {
      testWidgets('should use theme colors correctly', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(ErrorDisplay(error: error)),
        );

        // Assert - Should use theme colors for icon and text
        final icon = tester.widget<Icon>(find.byIcon(Icons.error_outline));
        expect(icon.color, isNotNull);
      });

      testWidgets('should adapt to different themes', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act - Test with dark theme
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ErrorDisplay(error: error, showDetails: true),
            theme: ThemeData.dark(),
          ),
        );

        // Assert - Should render without errors in dark theme
        expect(find.byType(ErrorDisplay), findsOneWidget);
        expect(find.text('Something went wrong'), findsOneWidget);
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle null error gracefully', (tester) async {
        // Arrange - This tests edge case handling
        const error = 'null error';

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ErrorDisplay(error: error, showDetails: true),
          ),
        );

        // Assert - Should display string error
        expect(find.text('null error'), findsOneWidget);
      });

      testWidgets('should handle empty title and message', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ErrorDisplay(error: error, title: '', message: ''),
          ),
        );

        // Assert - Should handle empty strings
        expect(find.text(''), findsNWidgets(2)); // Empty title and message
      });

      testWidgets('should handle long error messages', (tester) async {
        // Arrange
        final longError = Exception(
          'This is a very long error message that should wrap properly and not overflow the screen boundaries when displayed',
        );

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ErrorDisplay(error: longError, showDetails: true),
          ),
        );

        // Assert - Should render without overflow
        expect(find.byType(ErrorDisplay), findsOneWidget);
        expect(tester.takeException(), isNull); // No overflow exceptions
      });

      testWidgets('should handle multiple retry button presses', (
        tester,
      ) async {
        // Arrange
        final error = Exception('Test error');
        var retryCount = 0;
        void onRetry() {
          retryCount++;
        }

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            ErrorDisplay(error: error, onRetry: onRetry),
          ),
        );

        // Press retry button multiple times
        await tester.tap(find.text('Try Again'));
        await tester.pump();
        await tester.tap(find.text('Try Again'));
        await tester.pump();
        await tester.tap(find.text('Try Again'));
        await tester.pump();

        // Assert - Should handle multiple presses
        expect(retryCount, 3);
      });
    });
  });

  group('CompactErrorDisplay', () {
    group('Basic Rendering', () {
      testWidgets('should render with default properties', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(CompactErrorDisplay(error: error)),
        );

        // Assert - Should render compact error display
        expect(find.byType(CompactErrorDisplay), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
        expect(find.text('Error occurred'), findsOneWidget);
      });

      testWidgets('should render with custom message', (tester) async {
        // Arrange
        final error = Exception('Test error');
        const customMessage = 'Custom error message';

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CompactErrorDisplay(error: error, message: customMessage),
          ),
        );

        // Assert - Should display custom message
        expect(find.text(customMessage), findsOneWidget);
        expect(find.text('Error occurred'), findsNothing);
      });

      testWidgets('should use Row layout', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(CompactErrorDisplay(error: error)),
        );

        // Assert - Should use Row for horizontal layout
        expect(find.byType(Row), findsOneWidget);
        expect(find.byType(Expanded), findsOneWidget);
      });
    });

    group('Retry Button', () {
      testWidgets('should not show retry button when onRetry is null', (
        tester,
      ) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(CompactErrorDisplay(error: error)),
        );

        // Assert - Retry button should not be visible
        expect(find.byType(IconButton), findsNothing);
        expect(find.byIcon(Icons.refresh), findsNothing);
      });

      testWidgets('should show retry button when onRetry is provided', (
        tester,
      ) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CompactErrorDisplay(error: error, onRetry: () {}),
          ),
        );

        // Assert - Retry button should be visible
        expect(find.byType(IconButton), findsOneWidget);
        expect(find.byIcon(Icons.refresh), findsOneWidget);
      });

      testWidgets('should call onRetry when retry button is pressed', (
        tester,
      ) async {
        // Arrange
        final error = Exception('Test error');
        var retryPressed = false;
        void onRetry() {
          retryPressed = true;
        }

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CompactErrorDisplay(error: error, onRetry: onRetry),
          ),
        );

        await tester.tap(find.byType(IconButton));
        await tester.pumpAndSettle();

        // Assert - onRetry should have been called
        expect(retryPressed, isTrue);
      });

      testWidgets('should style retry icon button correctly', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CompactErrorDisplay(error: error, onRetry: () {}),
          ),
        );

        // Assert - Icon button should have correct styling
        final iconButton = tester.widget<IconButton>(find.byType(IconButton));
        expect(iconButton.iconSize, 16);
        expect(iconButton.padding, EdgeInsets.zero);
        expect(
          iconButton.constraints,
          const BoxConstraints(minWidth: 24, minHeight: 24),
        );
      });
    });

    group('Layout and Styling', () {
      testWidgets('should apply container styling correctly', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(CompactErrorDisplay(error: error)),
        );

        // Assert - Container should have proper styling
        final container = tester.widget<Container>(find.byType(Container));
        expect(container.padding, const EdgeInsets.all(DesignTokens.spacing12));
        expect(container.decoration, isA<BoxDecoration>());

        final decoration = container.decoration! as BoxDecoration;
        expect(decoration.borderRadius, isA<BorderRadius>());
        expect(decoration.border, isA<Border>());
      });

      testWidgets('should use correct spacing', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CompactErrorDisplay(error: error, onRetry: () {}),
          ),
        );

        // Assert - Should have proper spacing elements
        final sizedBoxes = find.byType(SizedBox);
        expect(
          sizedBoxes,
          findsAtLeastNWidgets(1),
        ); // At least one spacing element
      });

      testWidgets('should size icon correctly', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(CompactErrorDisplay(error: error)),
        );

        // Assert - Icon should have correct size
        final icons = tester.widgetList<Icon>(find.byIcon(Icons.error_outline));
        final errorIcon = icons.first;
        expect(errorIcon.size, 20);
      });
    });

    group('Theme Integration', () {
      testWidgets('should use theme colors correctly', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(CompactErrorDisplay(error: error)),
        );

        // Assert - Should use theme colors
        final icon = tester.widget<Icon>(find.byIcon(Icons.error_outline));
        expect(icon.color, isNotNull);
      });

      testWidgets('should adapt to different themes', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act - Test with dark theme
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CompactErrorDisplay(error: error, onRetry: () {}),
            theme: ThemeData.dark(),
          ),
        );

        // Assert - Should render without errors in dark theme
        expect(find.byType(CompactErrorDisplay), findsOneWidget);
        expect(find.text('Error occurred'), findsOneWidget);
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle empty message', (tester) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CompactErrorDisplay(error: error, message: ''),
          ),
        );

        // Assert - Should handle empty message
        expect(find.text(''), findsOneWidget);
      });

      testWidgets('should handle long messages with text overflow', (
        tester,
      ) async {
        // Arrange
        final error = Exception('Test error');
        const longMessage =
            'This is a very long error message that should wrap properly within the Expanded widget and not overflow the container boundaries';

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CompactErrorDisplay(error: error, message: longMessage),
          ),
        );

        // Assert - Should render without overflow
        expect(find.byType(CompactErrorDisplay), findsOneWidget);
        expect(find.text(longMessage), findsOneWidget);
        expect(tester.takeException(), isNull); // No overflow exceptions
      });

      testWidgets('should handle retry button with spacing correctly', (
        tester,
      ) async {
        // Arrange
        final error = Exception('Test error');

        // Act
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            CompactErrorDisplay(
              error: error,
              message: 'Short message',
              onRetry: () {},
            ),
          ),
        );

        // Assert - Should have proper spacing when retry button is present
        final sizedBoxes = find.byType(SizedBox);
        expect(sizedBoxes, findsAtLeastNWidgets(2)); // Spacing between elements
      });

      testWidgets('should handle multiple error types in compact display', (
        tester,
      ) async {
        // Test with different error types
        final errors = [
          Exception('Exception error'),
          'String error',
          42, // Number error
          null, // Null error (handled as string)
        ];

        for (final error in errors) {
          // Act
          await tester.pumpWidget(
            TestWrapper.createTestWidget(
              CompactErrorDisplay(error: error ?? 'null'),
            ),
          );

          // Assert - Should render without errors
          expect(find.byType(CompactErrorDisplay), findsOneWidget);
        }
      });
    });
  });

  group('ErrorDisplay vs CompactErrorDisplay Integration', () {
    testWidgets('should display both components together', (tester) async {
      // Arrange
      final error1 = Exception('Error 1');
      final error2 = Exception('Error 2');

      // Act
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Column(
            children: [
              ErrorDisplay(error: error1),
              const SizedBox(height: 16),
              CompactErrorDisplay(error: error2, onRetry: () {}),
            ],
          ),
        ),
      );

      // Assert - Both components should render together
      expect(find.byType(ErrorDisplay), findsOneWidget);
      expect(find.byType(CompactErrorDisplay), findsOneWidget);
    });

    testWidgets('should handle different error objects consistently', (
      tester,
    ) async {
      // Similar error handling between both components
      final sameError = Exception('Same error');

      // Act
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          Column(
            children: [
              ErrorDisplay(error: sameError, showDetails: true),
              const SizedBox(height: 16),
              CompactErrorDisplay(error: sameError),
            ],
          ),
        ),
      );

      // Assert - Both should handle the same error
      expect(
        find.text('Exception: Same error'),
        findsOneWidget,
      ); // In full display
      expect(find.text('Error occurred'), findsOneWidget); // In compact display
    });
  });
}
