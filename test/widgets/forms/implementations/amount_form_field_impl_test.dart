import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/services/currency_preferences_service.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/implementations/amount_form_field_impl.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../helpers/test_wrapper.dart';

class MockCurrencyPreferencesService extends Mock
    implements CurrencyPreferencesService {}

void main() {
  group('AmountFormFieldImpl', () {
    late MockCurrencyPreferencesService mockCurrencyPreferencesService;
    late List<Override> overrides;

    setUp(() {
      mockCurrencyPreferencesService = MockCurrencyPreferencesService();

      // Mock currency preferences service
      when(
        () => mockCurrencyPreferencesService.getCurrentCurrency(),
      ).thenReturn('USD');

      // Set up SharedPreferences mock to return a valid instance
      SharedPreferences.setMockInitialValues({});

      overrides = [
        currencyPreferencesServiceProvider.overrideWithValue(
          mockCurrencyPreferencesService,
        ),
      ];
    });

    AmountFormFieldImpl createAmountField({
      String? label,
      String? hintText,
      double? initialValue,
      bool isRequired = false,
      String? Function(double?)? validator,
      void Function(double?)? onChanged,
    }) {
      final config = FormFieldConfig<double>(
        key: 'amount',
        type: FormFieldType.currency,
        label: label ?? 'Amount',
        hintText: hintText,
        initialValue: initialValue,
        isRequired: isRequired,
        validator: validator,
        onChanged: onChanged,
      );

      return AmountFormFieldImpl(config);
    }

    group('Field Properties', () {
      test('should set correct key from config', () {
        final field = createAmountField();
        expect(field.key, equals('amount'));
      });

      test('should set initial value', () {
        final field = createAmountField(initialValue: 50.75);
        expect(field.value, equals(50.75));
      });

      test('should set required property', () {
        final field = createAmountField(isRequired: true);
        expect(field.isRequired, isTrue);
      });

      test('should not be required by default', () {
        final field = createAmountField();
        expect(field.isRequired, isFalse);
      });

      test('should start without errors', () {
        final field = createAmountField();
        expect(field.errorText, isNull);
      });

      test('should not be dirty initially', () {
        final field = createAmountField();
        expect(field.isDirty, isFalse);
      });
    });

    group('Value Management', () {
      test('should update value and mark as dirty', () {
        final field = createAmountField();

        field.value = 100;

        expect(field.value, equals(100));
        expect(field.isDirty, isTrue);
      });

      test('should call onChanged callback when value changes', () {
        double? capturedValue;
        final field = createAmountField(
          onChanged: (value) => capturedValue = value,
        );

        field.value = 75;

        expect(capturedValue, equals(75));
      });

      test('should handle null values', () {
        final field = createAmountField(initialValue: 100);

        field.value = null;

        expect(field.value, isNull);
        expect(field.isDirty, isTrue);
      });

      test('should reset to initial state', () {
        final field = createAmountField(initialValue: 50);

        field
          ..value = 100
          ..errorText = 'Test error';
        expect(field.isDirty, isTrue);

        field.reset();

        expect(field.value, equals(50.0));
        expect(field.errorText, isNull);
        expect(field.isDirty, isFalse);
      });
    });

    group('Validation', () {
      test('should validate required field when empty', () {
        final field = createAmountField(isRequired: true);

        final error = field.validate();

        expect(error, equals('This field is required'));
        expect(field.errorText, equals('This field is required'));
      });

      test('should pass validation for valid amounts when required', () {
        final field = createAmountField(isRequired: true);
        field.value = 100;

        final error = field.validate();

        expect(error, isNull);
        expect(field.errorText, isNull);
      });

      test('should use custom validator when provided', () {
        String? customValidator(double? value) {
          if (value != null && value < 10) {
            return r'Amount must be at least $10';
          }
          return null;
        }

        final field = createAmountField(validator: customValidator);
        field.value = 5;

        final error = field.validate();

        expect(error, equals(r'Amount must be at least $10'));
        expect(field.errorText, equals(r'Amount must be at least $10'));
      });

      test('should pass custom validation for valid amounts', () {
        String? customValidator(double? value) {
          if (value != null && value < 10) {
            return r'Amount must be at least $10';
          }
          return null;
        }

        final field = createAmountField(validator: customValidator);
        field.value = 15;

        final error = field.validate();

        expect(error, isNull);
        expect(field.errorText, isNull);
      });

      test('should clear error when clearError is called', () {
        final field = createAmountField(isRequired: true);
        field.validate(); // This will set an error

        field.clearError();

        expect(field.errorText, isNull);
      });

      test('should validate zero values as valid', () {
        final field = createAmountField(isRequired: true);
        field.value = 0.0;

        final error = field.validate();

        expect(error, isNull);
      });
    });

    group('Error Handling', () {
      test('should set and get error text', () {
        final field = createAmountField();

        field.errorText = 'Custom error';

        expect(field.errorText, equals('Custom error'));
      });

      test('should clear error text when set to null', () {
        final field = createAmountField();
        field.errorText = 'Error';

        field.errorText = null;

        expect(field.errorText, isNull);
      });
    });

    group('Field Lifecycle', () {
      test('should handle onFieldSubmitted without errors', () {
        final field = createAmountField();

        expect(field.onFieldSubmitted, returnsNormally);
      });

      test('should handle dispose without errors', () {
        final field = createAmountField();

        expect(field.dispose, returnsNormally);
      });
    });

    group('Edge Cases', () {
      test('should handle null initial value', () {
        final field = createAmountField(initialValue: null);

        expect(field.value, isNull);
        expect(field.isDirty, isFalse);
      });

      test('should handle zero initial value', () {
        final field = createAmountField(initialValue: 0);

        expect(field.value, equals(0));
      });

      test('should handle very large numbers', () {
        final field = createAmountField();

        field.value = 999999.99;

        expect(field.value, equals(999999.99));
      });

      test('should handle very small positive numbers', () {
        final field = createAmountField();

        field.value = 0.01;

        expect(field.value, equals(0.01));
      });

      test('should handle negative numbers', () {
        final field = createAmountField();

        field.value = -50.25;

        expect(field.value, equals(-50.25));
      });
    });

    group('Widget Rendering', () {
      testWidgets('should build widget without errors', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (context) {
                final field = createAmountField(label: 'Test Amount');
                return Scaffold(body: field.build(context));
              },
            ),
            overrides: overrides,
          ),
        );

        expect(find.text('Test Amount'), findsOneWidget);
        expect(find.byType(TextFormField), findsOneWidget);
      });

      testWidgets('should display currency symbol', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (context) {
                final field = createAmountField();
                return Scaffold(body: field.build(context));
              },
            ),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text(r'$'), findsOneWidget);
      });

      testWidgets('should handle text input', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (context) {
                final field = createAmountField();
                return Scaffold(body: field.build(context));
              },
            ),
            overrides: overrides,
          ),
        );

        final textField = find.byType(TextFormField);
        await tester.enterText(textField, '123.45');
        await tester.pump();

        expect(find.text('123.45'), findsOneWidget);
      });

      testWidgets('should show validation icons for valid input', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (context) {
                final field = createAmountField();
                return Scaffold(body: field.build(context));
              },
            ),
            overrides: overrides,
          ),
        );

        final textField = find.byType(TextFormField);
        await tester.enterText(textField, '100.00');
        await tester.pump();

        expect(find.byIcon(Icons.check_circle), findsOneWidget);
      });

      testWidgets('should hide icons for empty input', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (context) {
                final field = createAmountField();
                return Scaffold(body: field.build(context));
              },
            ),
            overrides: overrides,
          ),
        );

        expect(find.byIcon(Icons.check_circle), findsNothing);
        expect(find.byIcon(Icons.error), findsNothing);
      });

      testWidgets('should display help text when hint is provided', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Builder(
              builder: (context) {
                final field = createAmountField(hintText: 'Enter amount');
                return Scaffold(body: field.build(context));
              },
            ),
            overrides: overrides,
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text(r'Enter the transaction amount in $'), findsOneWidget);
      });
    });
  });
}
