import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/interfaces/repositories.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/implementations/account_selector_form_field_impl.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/mock_data_factory.dart';

void main() {
  group('AccountSelectorFormFieldImpl Tests', () {
    late MockAccountRepository mockAccountRepository;
    late MockAuthService mockAuthService;
    late MockUser mockUser;

    setUp(() {
      mockAccountRepository = MockAccountRepository();
      mockAuthService = MockAuthService();
      mockUser = MockUser();

      // Setup mock user
      when(() => mockUser.uid).thenReturn('test-uid');
      when(() => mockAuthService.currentUser).thenReturn(mockUser);

      // Register fallback values
      registerFallbackValue(MockDataFactory.createAccount());
    });

    Widget createTestWidget(AccountSelectorFormFieldImpl field) {
      return ProviderScope(
        overrides: [
          accountRepositoryProvider.overrideWithValue(mockAccountRepository),
          authServiceProvider.overrideWithValue(mockAuthService),
          currencyFormatterProvider.overrideWithValue(CurrencyFormatter('USD')),
        ],
        child: MaterialApp(
          localizationsDelegates: AppLocalizations.localizationsDelegates,
          supportedLocales: AppLocalizations.supportedLocales,
          home: Scaffold(
            body: SingleChildScrollView(
              child: SizedBox(
                width: double.infinity,
                child: Builder(builder: field.build),
              ),
            ),
          ),
        ),
      );
    }

    group('Basic Widget Rendering', () {
      testWidgets('should display label and dropdown field', (tester) async {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
        );

        final field = AccountSelectorFormFieldImpl(config);
        final mockAccounts = [
          MockDataFactory.createAccount(id: 'account-1', name: 'Checking'),
          MockDataFactory.createAccount(id: 'account-2', name: 'Savings'),
        ];

        when(
          () => mockAccountRepository.watchUserAccounts(any()),
        ).thenAnswer((_) => Stream.value(mockAccounts));

        // Act
        await tester.pumpWidget(createTestWidget(field));
        await tester.pump(); // Wait for async operations

        // Assert
        expect(find.text('Select Account'), findsOneWidget);
        expect(find.byType(DropdownButtonFormField<String?>), findsOneWidget);
        expect(find.byIcon(Icons.account_balance_wallet), findsOneWidget);
      });

      testWidgets('should display loading state', (tester) async {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
        );

        final field = AccountSelectorFormFieldImpl(config);

        // Create a stream that never emits to simulate loading state
        when(
          () => mockAccountRepository.watchUserAccounts(any()),
        ).thenAnswer((_) => const Stream.empty());

        // Act
        await tester.pumpWidget(createTestWidget(field));
        // Don't pump additional frames - should show loading state immediately

        // Assert
        expect(find.text('Loading accounts...'), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });

      testWidgets('should display error state', (tester) async {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
        );

        final field = AccountSelectorFormFieldImpl(config);

        when(
          () => mockAccountRepository.watchUserAccounts(any()),
        ).thenAnswer((_) => Stream.error(Exception('Database error')));

        // Act
        await tester.pumpWidget(createTestWidget(field));
        await tester.pump(); // Wait for async operations

        // Assert
        expect(find.text('Exception: Database error'), findsOneWidget);
        expect(find.byIcon(Icons.error), findsOneWidget);
      });
    });

    group('Account Display and Selection', () {
      testWidgets('should display account options with names and balances', (
        tester,
      ) async {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
        );

        final field = AccountSelectorFormFieldImpl(config);
        final mockAccounts = [
          MockDataFactory.createAccount(
            id: 'account-1',
            name: 'Main Checking',
            type: AccountType.checking,
            initialBalanceCents: 150000, // $1,500.00
          ),
          MockDataFactory.createAccount(
            id: 'account-2',
            name: 'Savings Account',
            type: AccountType.savings,
            initialBalanceCents: 500000, // $5,000.00
          ),
        ];

        when(
          () => mockAccountRepository.watchUserAccounts(any()),
        ).thenAnswer((_) => Stream.value(mockAccounts));

        // Act
        await tester.pumpWidget(createTestWidget(field));
        await tester.pump(); // Wait for async operations

        // Open dropdown
        await tester.tap(find.byType(DropdownButtonFormField<String?>));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Main Checking'), findsOneWidget);
        expect(find.text('Savings Account'), findsOneWidget);
        expect(find.text(r'$1500.00'), findsOneWidget);
        expect(find.text(r'$5000.00'), findsOneWidget);
        expect(
          find.byIcon(Icons.account_balance),
          findsAtLeastNWidgets(1),
        ); // Checking icon
        expect(
          find.byIcon(Icons.savings),
          findsAtLeastNWidgets(1),
        ); // Savings icon
      });

      testWidgets('should handle account selection', (tester) async {
        // Arrange
        String? selectedValue;
        final config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
          onChanged: (value) => selectedValue = value,
        );

        final field = AccountSelectorFormFieldImpl(config);
        final mockAccounts = [
          MockDataFactory.createAccount(id: 'account-1', name: 'Checking'),
          MockDataFactory.createAccount(id: 'account-2', name: 'Savings'),
        ];

        when(
          () => mockAccountRepository.watchUserAccounts(any()),
        ).thenAnswer((_) => Stream.value(mockAccounts));

        // Act
        await tester.pumpWidget(createTestWidget(field));
        await tester.pump(); // Wait for async operations

        // Open dropdown and select first account
        await tester.tap(find.byType(DropdownButtonFormField<String?>));
        await tester.pumpAndSettle();
        await tester.tap(find.text('Checking').last);
        await tester.pumpAndSettle();

        // Assert
        expect(selectedValue, equals('account-1'));
        expect(field.value, equals('account-1'));
      });

      testWidgets('should show None option when not required', (tester) async {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
          isRequired: false,
        );

        final field = AccountSelectorFormFieldImpl(config);
        final mockAccounts = [
          MockDataFactory.createAccount(id: 'account-1', name: 'Checking'),
        ];

        when(
          () => mockAccountRepository.watchUserAccounts(any()),
        ).thenAnswer((_) => Stream.value(mockAccounts));

        // Act
        await tester.pumpWidget(createTestWidget(field));
        await tester.pump(); // Wait for async operations

        // Open dropdown
        await tester.tap(find.byType(DropdownButtonFormField<String?>));
        await tester.pumpAndSettle();

        // Assert - should find None option in dropdown (may be 1 or 2 due to selected value + dropdown item)
        expect(find.text('None'), findsAtLeastNWidgets(1));
      });

      testWidgets('should not show None option when required', (tester) async {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
          isRequired: true,
        );

        final field = AccountSelectorFormFieldImpl(config);
        final mockAccounts = [
          MockDataFactory.createAccount(id: 'account-1', name: 'Checking'),
        ];

        when(
          () => mockAccountRepository.watchUserAccounts(any()),
        ).thenAnswer((_) => Stream.value(mockAccounts));

        // Act
        await tester.pumpWidget(createTestWidget(field));
        await tester.pump(); // Wait for async operations

        // Open dropdown
        await tester.tap(find.byType(DropdownButtonFormField<String?>));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('None'), findsNothing);
      });
    });

    group('Account Filtering', () {
      testWidgets('should filter out inactive accounts', (tester) async {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
        );

        final field = AccountSelectorFormFieldImpl(config);
        final mockAccounts = [
          MockDataFactory.createAccount(
            id: 'account-1',
            name: 'Active Account',
            isActive: true,
          ),
          MockDataFactory.createAccount(
            id: 'account-2',
            name: 'Inactive Account',
            isActive: false,
          ),
        ];

        when(
          () => mockAccountRepository.watchUserAccounts(any()),
        ).thenAnswer((_) => Stream.value(mockAccounts));

        // Act
        await tester.pumpWidget(createTestWidget(field));
        await tester.pump(); // Wait for async operations

        // Open dropdown
        await tester.tap(find.byType(DropdownButtonFormField<String?>));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Active Account'), findsOneWidget);
        expect(find.text('Inactive Account'), findsNothing);
      });

      testWidgets(
        'should exclude specific account when excludeAccountId is provided',
        (tester) async {
          // Arrange
          const config = FormFieldConfig<String?>(
            key: 'toAccountId',
            type: FormFieldType.accountSelector,
            label: 'Select Destination Account',
            customProperties: {'excludeAccountId': 'account-1'},
          );

          final field = AccountSelectorFormFieldImpl(config);
          final mockAccounts = [
            MockDataFactory.createAccount(
              id: 'account-1',
              name: 'Source Account',
            ),
            MockDataFactory.createAccount(
              id: 'account-2',
              name: 'Destination Account',
            ),
          ];

          when(
            () => mockAccountRepository.watchUserAccounts(any()),
          ).thenAnswer((_) => Stream.value(mockAccounts));

          // Act
          await tester.pumpWidget(createTestWidget(field));
          await tester.pump(); // Wait for async operations

          // Open dropdown
          await tester.tap(find.byType(DropdownButtonFormField<String?>));
          await tester.pumpAndSettle();

          // Assert
          expect(find.text('Source Account'), findsNothing);
          expect(find.text('Destination Account'), findsOneWidget);
        },
      );
    });

    group('Account Icons', () {
      testWidgets('should display correct icons for different account types', (
        tester,
      ) async {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
        );

        final field = AccountSelectorFormFieldImpl(config);
        final mockAccounts = [
          MockDataFactory.createAccount(
            id: 'checking',
            name: 'Checking',
            type: AccountType.checking,
          ),
          MockDataFactory.createAccount(
            id: 'savings',
            name: 'Savings',
            type: AccountType.savings,
          ),
          MockDataFactory.createAccount(
            id: 'credit',
            name: 'Credit Card',
            type: AccountType.creditCard,
          ),
          MockDataFactory.createAccount(
            id: 'investment',
            name: 'Investment',
            type: AccountType.investment,
          ),
          MockDataFactory.createAccount(
            id: 'cash',
            name: 'Cash',
            type: AccountType.cash,
          ),
          MockDataFactory.createAccount(
            id: 'loan',
            name: 'Loan',
            type: AccountType.loan,
          ),
        ];

        when(
          () => mockAccountRepository.watchUserAccounts(any()),
        ).thenAnswer((_) => Stream.value(mockAccounts));

        // Act
        await tester.pumpWidget(createTestWidget(field));
        await tester.pump(); // Wait for async operations

        // Open dropdown
        await tester.tap(find.byType(DropdownButtonFormField<String?>));
        await tester.pumpAndSettle();

        // Assert different icons are present
        expect(
          find.byIcon(Icons.account_balance),
          findsAtLeastNWidgets(1),
        ); // Checking
        expect(find.byIcon(Icons.savings), findsAtLeastNWidgets(1)); // Savings
        expect(
          find.byIcon(Icons.credit_card),
          findsAtLeastNWidgets(1),
        ); // Credit Card
        expect(
          find.byIcon(Icons.trending_up),
          findsAtLeastNWidgets(1),
        ); // Investment
        expect(find.byIcon(Icons.payments), findsAtLeastNWidgets(1)); // Cash
        expect(
          find.byIcon(Icons.account_balance_wallet),
          findsAtLeastNWidgets(1),
        ); // Loan or field icon
      });
    });

    group('Help Text and Context', () {
      testWidgets('should show income help text for income transaction type', (
        tester,
      ) async {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
          hintText: 'Choose an account',
          customProperties: {'transactionType': TransactionType.income},
        );

        final field = AccountSelectorFormFieldImpl(config);
        final mockAccounts = [
          MockDataFactory.createAccount(id: 'account-1', name: 'Checking'),
        ];

        when(
          () => mockAccountRepository.watchUserAccounts(any()),
        ).thenAnswer((_) => Stream.value(mockAccounts));

        // Act
        await tester.pumpWidget(createTestWidget(field));
        await tester.pump(); // Wait for async operations

        // Assert
        expect(
          find.text('Select the account where money will be deposited'),
          findsOneWidget,
        );
      });

      testWidgets(
        'should show expense help text for expense transaction type',
        (tester) async {
          // Arrange
          const config = FormFieldConfig<String?>(
            key: 'accountId',
            type: FormFieldType.accountSelector,
            label: 'Select Account',
            hintText: 'Choose an account',
            customProperties: {'transactionType': TransactionType.expense},
          );

          final field = AccountSelectorFormFieldImpl(config);
          final mockAccounts = [
            MockDataFactory.createAccount(id: 'account-1', name: 'Checking'),
          ];

          when(
            () => mockAccountRepository.watchUserAccounts(any()),
          ).thenAnswer((_) => Stream.value(mockAccounts));

          // Act
          await tester.pumpWidget(createTestWidget(field));
          await tester.pump(); // Wait for async operations

          // Assert
          expect(
            find.text('Select the account money will be withdrawn from'),
            findsOneWidget,
          );
        },
      );

      testWidgets(
        'should show transfer help text for transfer transaction type',
        (tester) async {
          // Arrange
          const config = FormFieldConfig<String?>(
            key: 'toAccountId',
            type: FormFieldType.accountSelector,
            label: 'Select Account',
            hintText: 'Choose an account',
            customProperties: {'transactionType': TransactionType.transfer},
          );

          final field = AccountSelectorFormFieldImpl(config);
          final mockAccounts = [
            MockDataFactory.createAccount(id: 'account-1', name: 'Checking'),
          ];

          when(
            () => mockAccountRepository.watchUserAccounts(any()),
          ).thenAnswer((_) => Stream.value(mockAccounts));

          // Act
          await tester.pumpWidget(createTestWidget(field));
          await tester.pump(); // Wait for async operations

          // Assert
          expect(
            find.text('Select the destination account for the transfer'),
            findsOneWidget,
          );
        },
      );

      testWidgets('should show source transfer help text for fromAccountId', (
        tester,
      ) async {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'fromAccountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
          hintText: 'Choose an account',
          customProperties: {'transactionType': TransactionType.transfer},
        );

        final field = AccountSelectorFormFieldImpl(config);
        final mockAccounts = [
          MockDataFactory.createAccount(id: 'account-1', name: 'Checking'),
        ];

        when(
          () => mockAccountRepository.watchUserAccounts(any()),
        ).thenAnswer((_) => Stream.value(mockAccounts));

        // Act
        await tester.pumpWidget(createTestWidget(field));
        await tester.pump(); // Wait for async operations

        // Assert
        expect(
          find.text('Select the source account for the transfer'),
          findsOneWidget,
        );
      });
    });

    group('Validation', () {
      testWidgets('should validate required field', (tester) async {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
          isRequired: true,
        );

        final field = AccountSelectorFormFieldImpl(config);
        final mockAccounts = [
          MockDataFactory.createAccount(id: 'account-1', name: 'Checking'),
        ];

        when(
          () => mockAccountRepository.watchUserAccounts(any()),
        ).thenAnswer((_) => Stream.value(mockAccounts));

        // Act
        await tester.pumpWidget(createTestWidget(field));
        await tester.pump(); // Wait for async operations

        // Validate with no selection
        final errorMessage = field.validate();

        // Assert
        expect(errorMessage, equals('This field is required'));
      });

      testWidgets('should use custom validator', (tester) async {
        // Arrange
        final config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
          validator: (value) {
            if (value == 'invalid-account') {
              return 'Invalid account selected';
            }
            return null;
          },
        );

        final field = AccountSelectorFormFieldImpl(config);
        final mockAccounts = [
          MockDataFactory.createAccount(id: 'account-1', name: 'Checking'),
        ];

        when(
          () => mockAccountRepository.watchUserAccounts(any()),
        ).thenAnswer((_) => Stream.value(mockAccounts));

        // Act
        await tester.pumpWidget(createTestWidget(field));
        await tester.pump(); // Wait for async operations

        // Set invalid value and validate
        field.value = 'invalid-account';
        final errorMessage = field.validate();

        // Assert
        expect(errorMessage, equals('Invalid account selected'));
      });

      testWidgets('should display validation error in UI', (tester) async {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
          isRequired: true,
        );

        final field = AccountSelectorFormFieldImpl(config);
        final mockAccounts = [
          MockDataFactory.createAccount(id: 'account-1', name: 'Checking'),
        ];

        when(
          () => mockAccountRepository.watchUserAccounts(any()),
        ).thenAnswer((_) => Stream.value(mockAccounts));

        // Act
        await tester.pumpWidget(createTestWidget(field));
        await tester.pump(); // Wait for async operations

        // Set error and rebuild
        field.errorText = 'This field is required';
        await tester.pumpWidget(createTestWidget(field));

        // Assert
        expect(find.text('This field is required'), findsOneWidget);
      });
    });

    group('Field State Management', () {
      testWidgets('should track dirty state', (tester) async {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
        );

        final field = AccountSelectorFormFieldImpl(config);

        // Act & Assert
        expect(field.isDirty, isFalse);

        field.value = 'account-1';
        expect(field.isDirty, isTrue);
      });

      testWidgets('should reset to initial state', (tester) async {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
          initialValue: 'initial-account',
        );

        final field = AccountSelectorFormFieldImpl(config);

        // Act
        field
          ..value = 'changed-account'
          ..errorText = 'Some error';
        expect(field.isDirty, isTrue);

        field.reset();

        // Assert
        expect(field.value, equals('initial-account'));
        expect(field.errorText, isNull);
        expect(field.isDirty, isFalse);
      });

      testWidgets('should clear error', (tester) async {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
        );

        final field = AccountSelectorFormFieldImpl(config);

        // Act
        field.errorText = 'Some error';
        expect(field.errorText, equals('Some error'));

        field.clearError();

        // Assert
        expect(field.errorText, isNull);
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle null initial value', (tester) async {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
          initialValue: null,
        );

        final field = AccountSelectorFormFieldImpl(config);

        // Act & Assert
        expect(field.value, isNull);
        expect(field.isDirty, isFalse);
      });
    });

    group('Method Coverage', () {
      test('onFieldSubmitted should not throw', () {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
        );

        final field = AccountSelectorFormFieldImpl(config);

        // Act & Assert
        expect(field.onFieldSubmitted, returnsNormally);
      });

      test('dispose should not throw', () {
        // Arrange
        const config = FormFieldConfig<String?>(
          key: 'accountId',
          type: FormFieldType.accountSelector,
          label: 'Select Account',
        );

        final field = AccountSelectorFormFieldImpl(config);

        // Act & Assert
        expect(field.dispose, returnsNormally);
      });
    });
  });
}

// Mock classes for testing
class MockAccountRepository extends Mock implements IAccountRepository {}

class MockAuthService extends Mock implements AuthService {}

class MockUser extends Mock implements User {}
