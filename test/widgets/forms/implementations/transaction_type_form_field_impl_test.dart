import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/implementations/transaction_type_form_field_impl.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('TransactionTypeFormFieldImpl Tests', () {
    testWidgets('should display label when provided', (tester) async {
      const config = FormFieldConfig<TransactionType>(
        key: 'transactionType',
        type: FormFieldType.custom,
        label: 'Transaction Type',
      );

      final transactionTypeField = TransactionTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: Si<PERSON><PERSON><PERSON>(
                  width: double.infinity,
                  child: Builder(builder: transactionTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should display the label
      expect(find.text('Transaction Type'), findsOneWidget);
    });

    testWidgets('should not display label when empty', (tester) async {
      const config = FormFieldConfig<TransactionType>(
        key: 'transactionType',
        type: FormFieldType.custom,
        label: '',
      );

      final transactionTypeField = TransactionTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: transactionTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should not display any label
      expect(find.text('Transaction Type'), findsNothing);
    });

    testWidgets(
      'should display segmented button with income, expense, and transfer options',
      (tester) async {
        const config = FormFieldConfig<TransactionType>(
          key: 'transactionType',
          type: FormFieldType.custom,
          label: 'Transaction Type',
        );

        final transactionTypeField = TransactionTypeFormFieldImpl(config);

        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              localizationsDelegates: AppLocalizations.localizationsDelegates,
              supportedLocales: AppLocalizations.supportedLocales,
              home: Scaffold(
                body: SingleChildScrollView(
                  child: SizedBox(
                    width: double.infinity,
                    child: Builder(builder: transactionTypeField.build),
                  ),
                ),
              ),
            ),
          ),
        );

        // Should display segmented button
        expect(find.byType(SegmentedButton<TransactionType>), findsOneWidget);

        // Should display income, expense, and transfer options
        expect(find.text('Income'), findsOneWidget);
        expect(find.text('Expense'), findsOneWidget);
        expect(find.text('Transfer'), findsOneWidget);

        // Should display appropriate icons
        expect(find.byIcon(Icons.trending_up), findsOneWidget);
        expect(find.byIcon(Icons.trending_down), findsOneWidget);
        expect(find.byIcon(Icons.swap_horiz), findsOneWidget);
      },
    );

    testWidgets('should handle income selection', (tester) async {
      TransactionType? selectedValue;

      final config = FormFieldConfig<TransactionType>(
        key: 'transactionType',
        type: FormFieldType.custom,
        label: 'Transaction Type',
        onChanged: (value) => selectedValue = value,
      );

      final transactionTypeField = TransactionTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: transactionTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Tap on income option
      await tester.tap(find.text('Income'));
      await tester.pump();

      // Should have called the onChanged callback
      expect(selectedValue, equals(TransactionType.income));
      expect(transactionTypeField.value, equals(TransactionType.income));
    });

    testWidgets('should handle expense selection', (tester) async {
      TransactionType? selectedValue;

      final config = FormFieldConfig<TransactionType>(
        key: 'transactionType',
        type: FormFieldType.custom,
        label: 'Transaction Type',
        onChanged: (value) => selectedValue = value,
      );

      final transactionTypeField = TransactionTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: transactionTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Tap on expense option
      await tester.tap(find.text('Expense'));
      await tester.pump();

      // Should have called the onChanged callback
      expect(selectedValue, equals(TransactionType.expense));
      expect(transactionTypeField.value, equals(TransactionType.expense));
    });

    testWidgets('should handle transfer selection', (tester) async {
      TransactionType? selectedValue;

      final config = FormFieldConfig<TransactionType>(
        key: 'transactionType',
        type: FormFieldType.custom,
        label: 'Transaction Type',
        onChanged: (value) => selectedValue = value,
      );

      final transactionTypeField = TransactionTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: transactionTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Tap on transfer option
      await tester.tap(find.text('Transfer'));
      await tester.pump();

      // Should have called the onChanged callback
      expect(selectedValue, equals(TransactionType.transfer));
      expect(transactionTypeField.value, equals(TransactionType.transfer));
    });

    testWidgets('should show initial value when provided', (tester) async {
      const config = FormFieldConfig<TransactionType>(
        key: 'transactionType',
        type: FormFieldType.custom,
        label: 'Transaction Type',
        initialValue: TransactionType.transfer,
      );

      final transactionTypeField = TransactionTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: transactionTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should reflect the initial value
      expect(transactionTypeField.value, equals(TransactionType.transfer));
    });

    testWidgets('should display error text when provided', (tester) async {
      const config = FormFieldConfig<TransactionType>(
        key: 'transactionType',
        type: FormFieldType.custom,
        label: 'Transaction Type',
      );

      final transactionTypeField = TransactionTypeFormFieldImpl(config)
        ..errorText = 'Transaction type is required';

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: transactionTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should display error text
      expect(find.text('Transaction type is required'), findsOneWidget);
    });

    testWidgets('should display transfer help text when transfer is selected', (
      tester,
    ) async {
      const config = FormFieldConfig<TransactionType>(
        key: 'transactionType',
        type: FormFieldType.custom,
        label: 'Transaction Type',
        initialValue: TransactionType.transfer,
      );

      final transactionTypeField = TransactionTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: transactionTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should display transfer help text
      expect(
        find.text(
          'Transfers move money between your accounts. Categories are not used for transfers.',
        ),
        findsOneWidget,
      );

      // Should display info icon
      expect(find.byIcon(Icons.info_outline), findsOneWidget);
    });

    testWidgets(
      'should not display transfer help text when transfer is not selected',
      (tester) async {
        const config = FormFieldConfig<TransactionType>(
          key: 'transactionType',
          type: FormFieldType.custom,
          label: 'Transaction Type',
          initialValue: TransactionType.income,
        );

        final transactionTypeField = TransactionTypeFormFieldImpl(config);

        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              localizationsDelegates: AppLocalizations.localizationsDelegates,
              supportedLocales: AppLocalizations.supportedLocales,
              home: Scaffold(
                body: SingleChildScrollView(
                  child: SizedBox(
                    width: double.infinity,
                    child: Builder(builder: transactionTypeField.build),
                  ),
                ),
              ),
            ),
          ),
        );

        // Should not display transfer help text
        expect(
          find.text(
            'Transfers move money between your accounts. Categories are not used for transfers.',
          ),
          findsNothing,
        );
      },
    );

    testWidgets('should show transfer help text when switching to transfer', (
      tester,
    ) async {
      TransactionType? selectedValue;

      final config = FormFieldConfig<TransactionType>(
        key: 'transactionType',
        type: FormFieldType.custom,
        label: 'Transaction Type',
        onChanged: (value) => selectedValue = value,
      );

      final transactionTypeField = TransactionTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: transactionTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Initially should not show transfer help text
      expect(
        find.text(
          'Transfers move money between your accounts. Categories are not used for transfers.',
        ),
        findsNothing,
      );

      // Tap on transfer option
      await tester.tap(find.text('Transfer'));
      await tester.pump();

      // Should have called the onChanged callback
      expect(selectedValue, equals(TransactionType.transfer));
      expect(transactionTypeField.value, equals(TransactionType.transfer));

      // Note: The help text display is tested separately with initial value
    });

    testWidgets('should update value property when selection changes', (
      tester,
    ) async {
      const config = FormFieldConfig<TransactionType>(
        key: 'transactionType',
        type: FormFieldType.custom,
        label: 'Transaction Type',
      );

      final transactionTypeField = TransactionTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: transactionTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Simulate value change
      transactionTypeField.value = TransactionType.expense;

      // Should update the internal value
      expect(transactionTypeField.value, equals(TransactionType.expense));
    });

    testWidgets('should handle empty selection set gracefully', (tester) async {
      const config = FormFieldConfig<TransactionType>(
        key: 'transactionType',
        type: FormFieldType.custom,
        label: 'Transaction Type',
      );

      final transactionTypeField = TransactionTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: transactionTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should handle null value gracefully
      expect(transactionTypeField.value, isNull);
    });

    testWidgets('should call onFieldSubmitted without error', (tester) async {
      const config = FormFieldConfig<TransactionType>(
        key: 'transactionType',
        type: FormFieldType.custom,
        label: 'Transaction Type',
      );

      final transactionTypeField = TransactionTypeFormFieldImpl(config);

      // Should not throw when calling onFieldSubmitted
      expect(transactionTypeField.onFieldSubmitted, returnsNormally);
    });

    testWidgets('should call dispose without error', (tester) async {
      const config = FormFieldConfig<TransactionType>(
        key: 'transactionType',
        type: FormFieldType.custom,
        label: 'Transaction Type',
      );

      final transactionTypeField = TransactionTypeFormFieldImpl(config);

      // Should not throw when calling dispose
      expect(transactionTypeField.dispose, returnsNormally);
    });

    testWidgets('should apply correct styling to segmented button', (
      tester,
    ) async {
      const config = FormFieldConfig<TransactionType>(
        key: 'transactionType',
        type: FormFieldType.custom,
        label: 'Transaction Type',
      );

      final transactionTypeField = TransactionTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: transactionTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Find the segmented button
      final segmentedButton = tester.widget<SegmentedButton<TransactionType>>(
        find.byType(SegmentedButton<TransactionType>),
      );

      // Should not show selected icon
      expect(segmentedButton.showSelectedIcon, isFalse);

      // Should allow empty selection
      expect(segmentedButton.emptySelectionAllowed, isTrue);

      // Should have proper styling
      expect(segmentedButton.style, isNotNull);
    });

    testWidgets('should handle selection change to null when empty selection', (
      tester,
    ) async {
      TransactionType? selectedValue = TransactionType.income;

      final config = FormFieldConfig<TransactionType>(
        key: 'transactionType',
        type: FormFieldType.custom,
        label: 'Transaction Type',
        initialValue: TransactionType.income,
        onChanged: (value) => selectedValue = value,
      );

      final transactionTypeField = TransactionTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: transactionTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Initially should have income selected
      expect(transactionTypeField.value, equals(TransactionType.income));

      // Tap on the already selected income option to deselect it
      await tester.tap(find.text('Income'));
      await tester.pump();

      // Should now be null (deselected)
      expect(selectedValue, isNull);
      expect(transactionTypeField.value, isNull);
    });

    testWidgets(
      'should hide transfer help text when switching away from transfer',
      (tester) async {
        TransactionType? selectedValue;

        final config = FormFieldConfig<TransactionType>(
          key: 'transactionType',
          type: FormFieldType.custom,
          label: 'Transaction Type',
          initialValue: TransactionType.transfer,
          onChanged: (value) => selectedValue = value,
        );

        final transactionTypeField = TransactionTypeFormFieldImpl(config);

        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              localizationsDelegates: AppLocalizations.localizationsDelegates,
              supportedLocales: AppLocalizations.supportedLocales,
              home: Scaffold(
                body: SingleChildScrollView(
                  child: SizedBox(
                    width: double.infinity,
                    child: Builder(builder: transactionTypeField.build),
                  ),
                ),
              ),
            ),
          ),
        );

        // Initially should show transfer help text
        expect(
          find.text(
            'Transfers move money between your accounts. Categories are not used for transfers.',
          ),
          findsOneWidget,
        );

        // Tap on income option
        await tester.tap(find.text('Income'));
        await tester.pump();

        // Should have called the onChanged callback
        expect(selectedValue, equals(TransactionType.income));
        expect(transactionTypeField.value, equals(TransactionType.income));

        // Note: The help text hiding is tested separately with initial value
      },
    );
  });
}
