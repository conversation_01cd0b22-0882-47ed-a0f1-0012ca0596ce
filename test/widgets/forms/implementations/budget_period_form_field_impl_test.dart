import 'package:budapp/data/models/budget.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/implementations/budget_period_form_field_impl.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('BudgetPeriodFormFieldImpl Tests', () {
    testWidgets('should display label when provided', (tester) async {
      const config = FormFieldConfig<BudgetPeriod>(
        key: 'budgetPeriod',
        type: FormFieldType.custom,
        label: 'Budget Period',
      );

      final budgetPeriodField = BudgetPeriodFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedB<PERSON>(
                  width: double.infinity,
                  child: Builder(builder: budgetPeriodField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should display the label
      expect(find.text('Budget Period'), findsOneWidget);
    });

    testWidgets('should not display label when empty', (tester) async {
      const config = FormFieldConfig<BudgetPeriod>(
        key: 'budgetPeriod',
        type: FormFieldType.custom,
        label: '',
      );

      final budgetPeriodField = BudgetPeriodFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetPeriodField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should not display any label
      expect(find.text('Budget Period'), findsNothing);
    });

    testWidgets(
      'should display segmented button with monthly and yearly options',
      (tester) async {
        const config = FormFieldConfig<BudgetPeriod>(
          key: 'budgetPeriod',
          type: FormFieldType.custom,
          label: 'Budget Period',
        );

        final budgetPeriodField = BudgetPeriodFormFieldImpl(config);

        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              localizationsDelegates: AppLocalizations.localizationsDelegates,
              supportedLocales: AppLocalizations.supportedLocales,
              home: Scaffold(
                body: SingleChildScrollView(
                  child: SizedBox(
                    width: double.infinity,
                    child: Builder(builder: budgetPeriodField.build),
                  ),
                ),
              ),
            ),
          ),
        );

        // Should display segmented button
        expect(find.byType(SegmentedButton<BudgetPeriod>), findsOneWidget);

        // Should display monthly and yearly options
        expect(find.text('Monthly'), findsOneWidget);
        expect(find.text('Yearly'), findsOneWidget);

        // Should display appropriate icons
        expect(find.byIcon(Icons.calendar_month), findsOneWidget);
        expect(find.byIcon(Icons.calendar_today), findsOneWidget);
      },
    );

    testWidgets('should handle monthly selection', (tester) async {
      BudgetPeriod? selectedValue;

      final config = FormFieldConfig<BudgetPeriod>(
        key: 'budgetPeriod',
        type: FormFieldType.custom,
        label: 'Budget Period',
        onChanged: (value) => selectedValue = value,
      );

      final budgetPeriodField = BudgetPeriodFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetPeriodField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Tap on monthly option
      await tester.tap(find.text('Monthly'));
      await tester.pump();

      // Should have called the onChanged callback
      expect(selectedValue, equals(BudgetPeriod.monthly));
      expect(budgetPeriodField.value, equals(BudgetPeriod.monthly));
    });

    testWidgets('should handle yearly selection', (tester) async {
      BudgetPeriod? selectedValue;

      final config = FormFieldConfig<BudgetPeriod>(
        key: 'budgetPeriod',
        type: FormFieldType.custom,
        label: 'Budget Period',
        onChanged: (value) => selectedValue = value,
      );

      final budgetPeriodField = BudgetPeriodFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetPeriodField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Tap on yearly option
      await tester.tap(find.text('Yearly'));
      await tester.pump();

      // Should have called the onChanged callback
      expect(selectedValue, equals(BudgetPeriod.yearly));
      expect(budgetPeriodField.value, equals(BudgetPeriod.yearly));
    });

    testWidgets('should show initial value when provided', (tester) async {
      const config = FormFieldConfig<BudgetPeriod>(
        key: 'budgetPeriod',
        type: FormFieldType.custom,
        label: 'Budget Period',
        initialValue: BudgetPeriod.yearly,
      );

      final budgetPeriodField = BudgetPeriodFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetPeriodField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should reflect the initial value
      expect(budgetPeriodField.value, equals(BudgetPeriod.yearly));
    });

    testWidgets('should display error text when provided', (tester) async {
      const config = FormFieldConfig<BudgetPeriod>(
        key: 'budgetPeriod',
        type: FormFieldType.custom,
        label: 'Budget Period',
      );

      final budgetPeriodField = BudgetPeriodFormFieldImpl(config)
        ..errorText = 'Budget period is required';

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetPeriodField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should display error text
      expect(find.text('Budget period is required'), findsOneWidget);
    });

    testWidgets('should display help text when provided and no error', (
      tester,
    ) async {
      const config = FormFieldConfig<BudgetPeriod>(
        key: 'budgetPeriod',
        type: FormFieldType.custom,
        label: 'Budget Period',
        hintText: 'Choose between monthly or yearly budget periods',
      );

      final budgetPeriodField = BudgetPeriodFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetPeriodField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should display help text
      expect(
        find.text('Choose between monthly or yearly budget periods'),
        findsOneWidget,
      );

      // Should display info icon
      expect(find.byIcon(Icons.info_outline), findsOneWidget);
    });

    testWidgets('should not display help text when error is present', (
      tester,
    ) async {
      const config = FormFieldConfig<BudgetPeriod>(
        key: 'budgetPeriod',
        type: FormFieldType.custom,
        label: 'Budget Period',
        hintText: 'Choose between monthly or yearly budget periods',
      );

      final budgetPeriodField = BudgetPeriodFormFieldImpl(config)
        ..errorText = 'Budget period is required';

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetPeriodField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should display error text
      expect(find.text('Budget period is required'), findsOneWidget);

      // Should not display help text when error is present
      expect(
        find.text('Choose between monthly or yearly budget periods'),
        findsNothing,
      );
    });

    testWidgets('should not display help text when hint text is empty', (
      tester,
    ) async {
      const config = FormFieldConfig<BudgetPeriod>(
        key: 'budgetPeriod',
        type: FormFieldType.custom,
        label: 'Budget Period',
        hintText: '',
      );

      final budgetPeriodField = BudgetPeriodFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetPeriodField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should not display help text
      expect(find.byIcon(Icons.info_outline), findsNothing);
    });

    testWidgets('should not display help text when hint text is null', (
      tester,
    ) async {
      const config = FormFieldConfig<BudgetPeriod>(
        key: 'budgetPeriod',
        type: FormFieldType.custom,
        label: 'Budget Period',
        hintText: null,
      );

      final budgetPeriodField = BudgetPeriodFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetPeriodField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should not display help text
      expect(find.byIcon(Icons.info_outline), findsNothing);
    });

    testWidgets('should update value property when selection changes', (
      tester,
    ) async {
      const config = FormFieldConfig<BudgetPeriod>(
        key: 'budgetPeriod',
        type: FormFieldType.custom,
        label: 'Budget Period',
      );

      final budgetPeriodField = BudgetPeriodFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetPeriodField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Simulate value change
      budgetPeriodField.value = BudgetPeriod.monthly;

      // Should update the internal value
      expect(budgetPeriodField.value, equals(BudgetPeriod.monthly));
    });

    testWidgets('should handle empty selection set gracefully', (tester) async {
      const config = FormFieldConfig<BudgetPeriod>(
        key: 'budgetPeriod',
        type: FormFieldType.custom,
        label: 'Budget Period',
      );

      final budgetPeriodField = BudgetPeriodFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetPeriodField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should handle null value gracefully
      expect(budgetPeriodField.value, isNull);
    });

    testWidgets('should call onFieldSubmitted without error', (tester) async {
      const config = FormFieldConfig<BudgetPeriod>(
        key: 'budgetPeriod',
        type: FormFieldType.custom,
        label: 'Budget Period',
      );

      final budgetPeriodField = BudgetPeriodFormFieldImpl(config);

      // Should not throw when calling onFieldSubmitted
      expect(budgetPeriodField.onFieldSubmitted, returnsNormally);
    });

    testWidgets('should call dispose without error', (tester) async {
      const config = FormFieldConfig<BudgetPeriod>(
        key: 'budgetPeriod',
        type: FormFieldType.custom,
        label: 'Budget Period',
      );

      final budgetPeriodField = BudgetPeriodFormFieldImpl(config);

      // Should not throw when calling dispose
      expect(budgetPeriodField.dispose, returnsNormally);
    });

    testWidgets('should apply correct styling to segmented button', (
      tester,
    ) async {
      const config = FormFieldConfig<BudgetPeriod>(
        key: 'budgetPeriod',
        type: FormFieldType.custom,
        label: 'Budget Period',
      );

      final budgetPeriodField = BudgetPeriodFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetPeriodField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Find the segmented button
      final segmentedButton = tester.widget<SegmentedButton<BudgetPeriod>>(
        find.byType(SegmentedButton<BudgetPeriod>),
      );

      // Should not show selected icon
      expect(segmentedButton.showSelectedIcon, isFalse);

      // Should have proper styling
      expect(segmentedButton.style, isNotNull);
    });
  });
}
