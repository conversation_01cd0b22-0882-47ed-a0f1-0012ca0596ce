import 'dart:async';

import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/repositories/interfaces/category_repository.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/implementations/parent_category_form_field_impl.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockCategoryRepository extends Mock implements ICategoryRepository {}

void main() {
  group('ParentCategoryFormFieldImpl Tests', () {
    late MockCategoryRepository mockCategoryRepository;

    setUp(() {
      mockCategoryRepository = MockCategoryRepository();
    });

    testWidgets('should display loading indicator while fetching categories', (
      tester,
    ) async {
      // Setup mock to never complete (to test loading state)
      final completer = Completer<List<Category>>();
      when(
        () => mockCategoryRepository.getActiveCategories(),
      ).thenAnswer((_) => completer.future);

      const config = FormFieldConfig<String?>(
        key: 'parentCategory',
        type: FormFieldType.custom,
        label: 'Parent Category',
      );

      final parentCategoryField = ParentCategoryFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            categoryRepositoryProvider.overrideWithValue(
              mockCategoryRepository,
            ),
          ],
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: parentCategoryField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should show loading indicator immediately
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Parent Category'), findsOneWidget);

      // Complete the future to avoid pending timer
      completer.complete(<Category>[]);
      await tester.pumpAndSettle();
    });

    testWidgets('should display dropdown with root categories when loaded', (
      tester,
    ) async {
      // Create test categories - mix of root and child categories
      final testCategories = [
        Category(
          id: 'cat1',
          userId: 'test-user',
          name: 'Food',
          type: CategoryType.expense,
          parentId: null, // Root category
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Category(
          id: 'cat2',
          userId: 'test-user',
          name: 'Groceries',
          type: CategoryType.expense,
          parentId: 'cat1', // Child category - should be filtered out
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Category(
          id: 'cat3',
          userId: 'test-user',
          name: 'Salary',
          type: CategoryType.income,
          parentId: null, // Root category
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      when(
        () => mockCategoryRepository.getActiveCategories(),
      ).thenAnswer((_) async => testCategories);

      const config = FormFieldConfig<String?>(
        key: 'parentCategory',
        type: FormFieldType.custom,
        label: 'Parent Category',
      );

      final parentCategoryField = ParentCategoryFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            categoryRepositoryProvider.overrideWithValue(
              mockCategoryRepository,
            ),
          ],
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: parentCategoryField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Wait for the future to complete
      await tester.pumpAndSettle();

      // Should display dropdown
      expect(find.byType(DropdownButtonFormField<String?>), findsOneWidget);

      // Should not show loading indicator anymore
      expect(find.byType(CircularProgressIndicator), findsNothing);

      // Tap dropdown to open it
      await tester.tap(find.byType(DropdownButtonFormField<String?>));
      await tester.pumpAndSettle();

      // Should show "None (Root Category)" option
      expect(find.text('None (Root Category)'), findsAtLeastNWidgets(1));

      // Should show only root categories (Food and Salary, not Groceries)
      expect(find.text('Food (Expense)'), findsOneWidget);
      expect(find.text('Salary (Income)'), findsOneWidget);
      expect(
        find.text('Groceries'),
        findsNothing,
      ); // Child category should not appear
    });

    testWidgets('should handle selection and call onChanged callback', (
      tester,
    ) async {
      String? selectedValue;

      final testCategories = [
        Category(
          id: 'cat1',
          userId: 'test-user',
          name: 'Food',
          type: CategoryType.expense,
          parentId: null,
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      when(
        () => mockCategoryRepository.getActiveCategories(),
      ).thenAnswer((_) async => testCategories);

      final config = FormFieldConfig<String?>(
        key: 'parentCategory',
        type: FormFieldType.custom,
        label: 'Parent Category',
        onChanged: (value) => selectedValue = value,
      );

      final parentCategoryField = ParentCategoryFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            categoryRepositoryProvider.overrideWithValue(
              mockCategoryRepository,
            ),
          ],
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: parentCategoryField.build),
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap dropdown to open it
      await tester.tap(find.byType(DropdownButtonFormField<String?>));
      await tester.pumpAndSettle();

      // Select the Food category
      await tester.tap(find.text('Food (Expense)'));
      await tester.pumpAndSettle();

      // Should have called onChanged callback with the category ID
      expect(selectedValue, equals('cat1'));
      expect(parentCategoryField.value, equals('cat1'));
    });

    testWidgets('should display error when category loading fails', (
      tester,
    ) async {
      when(
        () => mockCategoryRepository.getActiveCategories(),
      ).thenThrow(Exception('Failed to load categories'));

      const config = FormFieldConfig<String?>(
        key: 'parentCategory',
        type: FormFieldType.custom,
        label: 'Parent Category',
      );

      final parentCategoryField = ParentCategoryFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            categoryRepositoryProvider.overrideWithValue(
              mockCategoryRepository,
            ),
          ],
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: parentCategoryField.build),
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should display error message
      expect(find.textContaining('Error loading categories:'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsNothing);
      expect(find.byType(DropdownButtonFormField<String?>), findsNothing);
    });

    testWidgets('should show initial value when provided', (tester) async {
      final testCategories = [
        Category(
          id: 'cat1',
          userId: 'test-user',
          name: 'Food',
          type: CategoryType.expense,
          parentId: null,
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      when(
        () => mockCategoryRepository.getActiveCategories(),
      ).thenAnswer((_) async => testCategories);

      const config = FormFieldConfig<String?>(
        key: 'parentCategory',
        type: FormFieldType.custom,
        label: 'Parent Category',
        initialValue: 'cat1',
      );

      final parentCategoryField = ParentCategoryFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            categoryRepositoryProvider.overrideWithValue(
              mockCategoryRepository,
            ),
          ],
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: parentCategoryField.build),
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should show the initial value in the dropdown
      expect(parentCategoryField.value, equals('cat1'));
    });

    testWidgets('should display help text', (tester) async {
      when(
        () => mockCategoryRepository.getActiveCategories(),
      ).thenAnswer((_) async => <Category>[]);

      const config = FormFieldConfig<String?>(
        key: 'parentCategory',
        type: FormFieldType.custom,
        label: 'Parent Category',
        hintText: 'Select a parent category',
      );

      final parentCategoryField = ParentCategoryFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            categoryRepositoryProvider.overrideWithValue(
              mockCategoryRepository,
            ),
          ],
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: parentCategoryField.build),
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should display help text
      expect(
        find.text(
          'Select a parent category to create a subcategory, or leave empty for a root category.',
        ),
        findsOneWidget,
      );
    });
  });
}
