import 'package:budapp/config/form_constants.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/implementations/color_picker_form_field_impl.dart';
import 'package:budapp/widgets/forms/implementations/icon_picker_form_field_impl.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Color and Icon Picker Tests', () {
    testWidgets('ColorPickerFormFieldImpl should display available colors', (
      tester,
    ) async {
      const config = ColorPickerFieldConfig(
        key: 'color',
        label: 'Test Color',
        availableColors: ['#F44336', '#4CAF50', '#2196F3'],
      );

      final colorPicker = ColorPickerFormFieldImpl(config);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: Builder(builder: colorPicker.build)),
        ),
      );

      // Should display the label
      expect(find.text('Test Color'), findsOneWidget);

      // Should display color options (at least some of them)
      expect(find.byType(GestureDetector), findsAtLeastNWidgets(3));
    });

    testWidgets('IconPickerFormFieldImpl should display available icons', (
      tester,
    ) async {
      const config = IconPickerFieldConfig(
        key: 'icon',
        label: 'Test Icon',
        availableIcons: ['shopping_cart', 'home', 'work'],
      );

      final iconPicker = IconPickerFormFieldImpl(config);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: Builder(builder: iconPicker.build)),
        ),
      );

      // Should display the label
      expect(find.text('Test Icon'), findsOneWidget);

      // Should display icon options (at least some of them)
      expect(find.byType(GestureDetector), findsAtLeastNWidgets(3));
      expect(find.byType(Icon), findsAtLeastNWidgets(3));
    });

    testWidgets('ColorPickerFormFieldImpl should handle selection', (
      tester,
    ) async {
      String? selectedColor;

      final config = ColorPickerFieldConfig(
        key: 'color',
        label: 'Test Color',
        availableColors: ['#F44336', '#4CAF50', '#2196F3'],
        onChanged: (value) => selectedColor = value,
      );

      final colorPicker = ColorPickerFormFieldImpl(config);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: Builder(builder: colorPicker.build)),
        ),
      );

      // Tap on the first color option
      await tester.tap(find.byType(GestureDetector).first);
      await tester.pump();

      // Should have called the onChanged callback
      expect(selectedColor, equals('#F44336'));
    });

    testWidgets('IconPickerFormFieldImpl should handle selection', (
      tester,
    ) async {
      String? selectedIcon;

      final config = IconPickerFieldConfig(
        key: 'icon',
        label: 'Test Icon',
        availableIcons: ['shopping_cart', 'home', 'work'],
        onChanged: (value) => selectedIcon = value,
      );

      final iconPicker = IconPickerFormFieldImpl(config);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: Builder(builder: iconPicker.build)),
        ),
      );

      // Tap on the first icon option
      await tester.tap(find.byType(GestureDetector).first);
      await tester.pump();

      // Should have called the onChanged callback
      expect(selectedIcon, equals('shopping_cart'));
    });

    test('FormConstants should provide consistent color and icon sets', () {
      // Test that all constant sets are non-empty
      expect(FormConstants.standardColors, isNotEmpty);
      expect(FormConstants.standardIcons, isNotEmpty);
      expect(FormConstants.categoryColors, isNotEmpty);
      expect(FormConstants.categoryIcons, isNotEmpty);
      expect(FormConstants.accountColors, isNotEmpty);
      expect(FormConstants.accountIcons, isNotEmpty);
      expect(FormConstants.tagColors, isNotEmpty);
      expect(FormConstants.tagIcons, isNotEmpty);

      // Test that colors are valid hex strings
      for (final color in FormConstants.standardColors) {
        expect(color, matches(r'^#[0-9A-Fa-f]{6}$'));
      }

      // Test that icon names are non-empty strings
      for (final icon in FormConstants.standardIcons) {
        expect(icon, isNotEmpty);
        expect(icon, isA<String>());
      }
    });
  });
}
