import 'package:budapp/data/models/account.dart';
import 'package:budapp/features/accounts/presentation/widgets/account_type_selector.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/implementations/account_type_form_field_impl.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AccountTypeFormFieldImpl Tests', () {
    testWidgets('should display account type selector', (tester) async {
      const config = FormFieldConfig<Map<String, dynamic>>(
        key: 'accountType',
        type: FormFieldType.custom,
        label: 'Account Type',
      );

      final accountTypeField = AccountTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: accountTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should display the label
      expect(find.text('Account Type'), findsOneWidget);

      // Should display AccountTypeSelector widget
      expect(find.byType(AccountTypeSelector), findsOneWidget);
    });

    testWidgets('should handle account type selection and update value', (
      tester,
    ) async {
      Map<String, dynamic>? selectedValue;

      final config = FormFieldConfig<Map<String, dynamic>>(
        key: 'accountType',
        type: FormFieldType.custom,
        label: 'Account Type',
        onChanged: (value) => selectedValue = value,
      );

      final accountTypeField = AccountTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: accountTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Find and tap on a checking account option
      // Note: This test will need to be adapted based on the actual AccountTypeSelector implementation
      final checkingOptions = find.text('Checking');
      if (checkingOptions.evaluate().isNotEmpty) {
        await tester.tap(checkingOptions.first);
        await tester.pump();

        // Should have called the onChanged callback
        expect(selectedValue, isNotNull);
        expect(selectedValue!['type'], equals(AccountType.checking));
        expect(
          selectedValue!['classification'],
          equals(AccountClassification.asset),
        );
      }
    });

    testWidgets('should show initial value when provided', (tester) async {
      final initialValue = {
        'type': AccountType.savings,
        'classification': AccountClassification.asset,
      };

      final config = FormFieldConfig<Map<String, dynamic>>(
        key: 'accountType',
        type: FormFieldType.custom,
        label: 'Account Type',
        initialValue: initialValue,
      );

      final accountTypeField = AccountTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: accountTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should reflect the initial value
      expect(accountTypeField.selectedType, equals(AccountType.savings));
      expect(
        accountTypeField.selectedClassification,
        equals(AccountClassification.asset),
      );
    });

    testWidgets('should display error text when provided', (tester) async {
      const config = FormFieldConfig<Map<String, dynamic>>(
        key: 'accountType',
        type: FormFieldType.custom,
        label: 'Account Type',
      );

      final accountTypeField = AccountTypeFormFieldImpl(config)
        ..errorText = 'Account type is required';

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: accountTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should display error text
      expect(find.text('Account type is required'), findsOneWidget);
    });

    testWidgets('should update value property when selection changes', (
      tester,
    ) async {
      const config = FormFieldConfig<Map<String, dynamic>>(
        key: 'accountType',
        type: FormFieldType.custom,
        label: 'Account Type',
      );

      final accountTypeField = AccountTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: accountTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Simulate value change (this would normally come from AccountTypeSelector)
      final newValue = {
        'type': AccountType.creditCard,
        'classification': AccountClassification.liability,
      };

      accountTypeField.value = newValue;

      // Should update the internal value
      expect(accountTypeField.value, equals(newValue));
      expect(accountTypeField.selectedType, equals(AccountType.creditCard));
      expect(
        accountTypeField.selectedClassification,
        equals(AccountClassification.liability),
      );
    });
  });
}
