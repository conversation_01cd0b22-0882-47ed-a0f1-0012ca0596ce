import 'package:budapp/data/models/budget.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/implementations/budget_type_form_field_impl.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('BudgetTypeFormFieldImpl Tests', () {
    testWidgets('should display label when provided', (tester) async {
      const config = FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: 'Budget Type',
      );

      final budgetTypeField = BudgetTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: Si<PERSON><PERSON><PERSON>(
                  width: double.infinity,
                  child: Builder(builder: budgetTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should display the label
      expect(find.text('Budget Type'), findsOneWidget);
    });

    testWidgets('should not display label when empty', (tester) async {
      const config = FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: '',
      );

      final budgetTypeField = BudgetTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should not display any label
      expect(find.text('Budget Type'), findsNothing);
    });

    testWidgets(
      'should display segmented button with expense and income options',
      (tester) async {
        const config = FormFieldConfig<BudgetType>(
          key: 'budgetType',
          type: FormFieldType.custom,
          label: 'Budget Type',
        );

        final budgetTypeField = BudgetTypeFormFieldImpl(config);

        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              localizationsDelegates: AppLocalizations.localizationsDelegates,
              supportedLocales: AppLocalizations.supportedLocales,
              home: Scaffold(
                body: SingleChildScrollView(
                  child: SizedBox(
                    width: double.infinity,
                    child: Builder(builder: budgetTypeField.build),
                  ),
                ),
              ),
            ),
          ),
        );

        // Should display segmented button
        expect(find.byType(SegmentedButton<BudgetType>), findsOneWidget);

        // Should display expense and income options
        expect(find.text('Expense'), findsOneWidget);
        expect(find.text('Income'), findsOneWidget);

        // Should display appropriate icons
        expect(find.byIcon(Icons.trending_down), findsOneWidget);
        expect(find.byIcon(Icons.trending_up), findsOneWidget);
      },
    );

    testWidgets('should handle expense selection', (tester) async {
      BudgetType? selectedValue;

      final config = FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: 'Budget Type',
        onChanged: (value) => selectedValue = value,
      );

      final budgetTypeField = BudgetTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Tap on expense option
      await tester.tap(find.text('Expense'));
      await tester.pump();

      // Should have called the onChanged callback
      expect(selectedValue, equals(BudgetType.expense));
      expect(budgetTypeField.value, equals(BudgetType.expense));
    });

    testWidgets('should handle income selection', (tester) async {
      BudgetType? selectedValue;

      final config = FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: 'Budget Type',
        onChanged: (value) => selectedValue = value,
      );

      final budgetTypeField = BudgetTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Tap on income option
      await tester.tap(find.text('Income'));
      await tester.pump();

      // Should have called the onChanged callback
      expect(selectedValue, equals(BudgetType.income));
      expect(budgetTypeField.value, equals(BudgetType.income));
    });

    testWidgets('should show initial value when provided', (tester) async {
      const config = FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: 'Budget Type',
        initialValue: BudgetType.income,
      );

      final budgetTypeField = BudgetTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should reflect the initial value
      expect(budgetTypeField.value, equals(BudgetType.income));
    });

    testWidgets('should display error text when provided', (tester) async {
      const config = FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: 'Budget Type',
      );

      final budgetTypeField = BudgetTypeFormFieldImpl(config)
        ..errorText = 'Budget type is required';

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should display error text
      expect(find.text('Budget type is required'), findsOneWidget);
    });

    testWidgets('should display help text when provided and no error', (
      tester,
    ) async {
      const config = FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: 'Budget Type',
        hintText: 'Choose between expense or income budget types',
      );

      final budgetTypeField = BudgetTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should display help text
      expect(
        find.text('Choose between expense or income budget types'),
        findsOneWidget,
      );

      // Should display info icon
      expect(find.byIcon(Icons.info_outline), findsOneWidget);
    });

    testWidgets('should not display help text when error is present', (
      tester,
    ) async {
      const config = FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: 'Budget Type',
        hintText: 'Choose between expense or income budget types',
      );

      final budgetTypeField = BudgetTypeFormFieldImpl(config)
        ..errorText = 'Budget type is required';

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should display error text
      expect(find.text('Budget type is required'), findsOneWidget);

      // Should not display help text when error is present
      expect(
        find.text('Choose between expense or income budget types'),
        findsNothing,
      );
    });

    testWidgets('should not display help text when hint text is empty', (
      tester,
    ) async {
      const config = FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: 'Budget Type',
        hintText: '',
      );

      final budgetTypeField = BudgetTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should not display help text
      expect(find.byIcon(Icons.info_outline), findsNothing);
    });

    testWidgets('should not display help text when hint text is null', (
      tester,
    ) async {
      const config = FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: 'Budget Type',
        hintText: null,
      );

      final budgetTypeField = BudgetTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should not display help text
      expect(find.byIcon(Icons.info_outline), findsNothing);
    });

    testWidgets('should update value property when selection changes', (
      tester,
    ) async {
      const config = FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: 'Budget Type',
      );

      final budgetTypeField = BudgetTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Simulate value change
      budgetTypeField.value = BudgetType.expense;

      // Should update the internal value
      expect(budgetTypeField.value, equals(BudgetType.expense));
    });

    testWidgets('should handle empty selection set gracefully', (tester) async {
      const config = FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: 'Budget Type',
      );

      final budgetTypeField = BudgetTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Should handle null value gracefully
      expect(budgetTypeField.value, isNull);
    });

    testWidgets('should call onFieldSubmitted without error', (tester) async {
      const config = FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: 'Budget Type',
      );

      final budgetTypeField = BudgetTypeFormFieldImpl(config);

      // Should not throw when calling onFieldSubmitted
      expect(budgetTypeField.onFieldSubmitted, returnsNormally);
    });

    testWidgets('should call dispose without error', (tester) async {
      const config = FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: 'Budget Type',
      );

      final budgetTypeField = BudgetTypeFormFieldImpl(config);

      // Should not throw when calling dispose
      expect(budgetTypeField.dispose, returnsNormally);
    });

    testWidgets('should apply correct styling to segmented button', (
      tester,
    ) async {
      const config = FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: 'Budget Type',
      );

      final budgetTypeField = BudgetTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Find the segmented button
      final segmentedButton = tester.widget<SegmentedButton<BudgetType>>(
        find.byType(SegmentedButton<BudgetType>),
      );

      // Should not show selected icon
      expect(segmentedButton.showSelectedIcon, isFalse);

      // Should allow empty selection
      expect(segmentedButton.emptySelectionAllowed, isTrue);

      // Should have proper styling
      expect(segmentedButton.style, isNotNull);
    });

    testWidgets('should handle selection change to null when empty selection', (
      tester,
    ) async {
      BudgetType? selectedValue = BudgetType.expense;

      final config = FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: 'Budget Type',
        initialValue: BudgetType.expense,
        onChanged: (value) => selectedValue = value,
      );

      final budgetTypeField = BudgetTypeFormFieldImpl(config);

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Builder(builder: budgetTypeField.build),
                ),
              ),
            ),
          ),
        ),
      );

      // Initially should have expense selected
      expect(budgetTypeField.value, equals(BudgetType.expense));

      // Tap on the already selected expense option to deselect it
      await tester.tap(find.text('Expense'));
      await tester.pump();

      // Should now be null (deselected)
      expect(selectedValue, isNull);
      expect(budgetTypeField.value, isNull);
    });
  });
}
