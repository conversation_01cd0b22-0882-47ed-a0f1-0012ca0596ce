import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/config/generic_form_config.dart';
import 'package:budapp/widgets/forms/screens/base_editable_form_screen.dart';
import 'package:budapp/widgets/forms/screens/generic_form_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../helpers/test_wrapper.dart';

// Test entity class for demonstration
class TestEntity {
  const TestEntity({
    required this.id,
    required this.name,
    required this.description,
    this.isActive = true,
  });

  factory TestEntity.fromJson(Map<String, dynamic> json) => TestEntity(
    id: json['id'] as String,
    name: json['name'] as String,
    description: json['description'] as String,
    isActive: json['isActive'] as bool? ?? true,
  );

  final String id;
  final String name;
  final String description;
  final bool isActive;

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'description': description,
    'isActive': isActive,
  };

  TestEntity copyWith({
    String? id,
    String? name,
    String? description,
    bool? isActive,
  }) {
    return TestEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
    );
  }
}

// Test data mapper
class TestEntityDataMapper extends GenericFormDataMapper<TestEntity> {
  @override
  Map<String, dynamic> entityToFormData(TestEntity entity) {
    return {
      'id': entity.id,
      'name': entity.name,
      'description': entity.description,
      'isActive': entity.isActive,
    };
  }

  @override
  TestEntity formDataToEntity(Map<String, dynamic> formData) {
    return TestEntity(
      id: formData['id'] as String? ?? '',
      name: formData['name'] as String? ?? '',
      description: formData['description'] as String? ?? '',
      isActive: formData['isActive'] as bool? ?? true,
    );
  }
}

void main() {
  group('GenericFormScreen Tests', () {
    late List<FormFieldConfig<dynamic>> testFields;
    late Map<String, dynamic> submittedData;
    late String fieldChangedKey;
    late dynamic fieldChangedValue;

    setUp(() {
      testFields = [
        TextFieldConfig(
          key: 'name',
          label: 'Name',
          isRequired: true,
          validator: (value) =>
              value?.isEmpty ?? true ? 'Name is required' : null,
        ),
        const TextFieldConfig(
          key: 'description',
          label: 'Description',
          maxLines: 3,
        ),
        DropdownFieldConfig<bool>(
          key: 'isActive',
          label: 'Status',
          items: [true, false],
          displayStringForItem: (item) => item ? 'Active' : 'Inactive',
        ),
      ];

      submittedData = {};
      fieldChangedKey = '';
      fieldChangedValue = null;
    });

    group('Create Mode Tests', () {
      testWidgets('should render form screen in create mode', (tester) async {
        final config = GenericFormConfig<TestEntity>(
          title: 'Create Entity',
          fields: testFields,
          onSubmit: (data) async {
            submittedData = data;
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Create Entity'), findsOneWidget);
        expect(
          find.text('Name *'),
          findsOneWidget,
        ); // Required fields have asterisk
        expect(find.text('Description'), findsOneWidget);
        expect(find.text('Status'), findsOneWidget);
        expect(find.text('Create'), findsOneWidget);
        expect(find.text('Delete'), findsNothing);
      });

      testWidgets('should show create button by default', (tester) async {
        final config = GenericFormConfig<TestEntity>(
          title: 'Create Entity',
          fields: testFields,
          onSubmit: (data) async {
            submittedData = data;
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Create'), findsOneWidget);
        expect(config.isCreateMode, isTrue);
        expect(config.isEditMode, isFalse);
      });

      testWidgets('should use custom save button text', (tester) async {
        final config = GenericFormConfig<TestEntity>(
          title: 'Create Entity',
          fields: testFields,
          onSubmit: (data) async {
            submittedData = data;
          },
          saveButtonText: 'Add New',
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Add New'), findsOneWidget);
        expect(find.text('Create'), findsNothing);
      });

      testWidgets('should not show delete button in create mode', (
        tester,
      ) async {
        final config = GenericFormConfig<TestEntity>(
          title: 'Create Entity',
          fields: testFields,
          onSubmit: (data) async {
            submittedData = data;
          },
          showDeleteButton: true,
          onDelete: () async {
            // Delete callback for testing
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Delete'), findsNothing);
      });
    });

    group('Edit Mode Tests', () {
      testWidgets('should render form screen in edit mode', (tester) async {
        const testEntity = TestEntity(
          id: '1',
          name: 'Test Entity',
          description: 'Test description',
          isActive: true,
        );

        final config = GenericFormConfig<TestEntity>(
          title: 'Edit Entity',
          fields: testFields,
          initialData: testEntity,
          dataMapper: TestEntityDataMapper(),
          onSubmit: (data) async {
            submittedData = data;
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Edit Entity'), findsOneWidget);

        // Check that form fields have the correct initial values
        final nameField = find.byType(TextFormField).first;
        final nameController = tester
            .widget<TextFormField>(nameField)
            .controller;
        expect(nameController?.text, equals('Test Entity'));

        final descriptionField = find.byType(TextFormField).at(1);
        final descriptionController = tester
            .widget<TextFormField>(descriptionField)
            .controller;
        expect(descriptionController?.text, equals('Test description'));

        expect(find.text('Save'), findsOneWidget);
        expect(config.isEditMode, isTrue);
        expect(config.isCreateMode, isFalse);
      });

      testWidgets('should populate fields with initial data', (tester) async {
        const testEntity = TestEntity(
          id: '1',
          name: 'Initial Name',
          description: 'Initial description',
          isActive: false,
        );

        final config = GenericFormConfig<TestEntity>(
          title: 'Edit Entity',
          fields: testFields,
          initialData: testEntity,
          dataMapper: TestEntityDataMapper(),
          onSubmit: (data) async {
            submittedData = data;
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        // Check that form fields have the correct initial values
        final nameField = find.byType(TextFormField).first;
        final nameController = tester
            .widget<TextFormField>(nameField)
            .controller;
        expect(nameController?.text, equals('Initial Name'));

        final descriptionField = find.byType(TextFormField).at(1);
        final descriptionController = tester
            .widget<TextFormField>(descriptionField)
            .controller;
        expect(descriptionController?.text, equals('Initial description'));
      });

      testWidgets('should show delete button when configured', (tester) async {
        const testEntity = TestEntity(
          id: '1',
          name: 'Test Entity',
          description: 'Test description',
        );

        final config = GenericFormConfig<TestEntity>(
          title: 'Edit Entity',
          fields: testFields,
          initialData: testEntity,
          onSubmit: (data) async {
            submittedData = data;
          },
          showDeleteButton: true,
          onDelete: () async {
            // Delete callback for testing
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        // Delete button is an IconButton with delete_outline icon
        expect(find.byIcon(Icons.delete_outline), findsOneWidget);
      });

      testWidgets('should use custom delete button text', (tester) async {
        const testEntity = TestEntity(
          id: '1',
          name: 'Test Entity',
          description: 'Test description',
        );

        final config = GenericFormConfig<TestEntity>(
          title: 'Edit Entity',
          fields: testFields,
          initialData: testEntity,
          onSubmit: (data) async {
            submittedData = data;
          },
          showDeleteButton: true,
          onDelete: () async {
            // Delete callback for testing
          },
          deleteButtonText: 'Remove',
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        // Custom delete button text should be used as tooltip
        expect(find.byIcon(Icons.delete_outline), findsOneWidget);

        // Find the IconButton with delete icon and check its tooltip
        final iconButtons = find.byType(IconButton);
        var foundDeleteButton = false;
        for (var i = 0; i < tester.widgetList(iconButtons).length; i++) {
          final iconButton = tester.widget<IconButton>(iconButtons.at(i));
          if (iconButton.icon is Icon &&
              (iconButton.icon as Icon).icon == Icons.delete_outline) {
            expect(iconButton.tooltip, equals('Remove'));
            foundDeleteButton = true;
            break;
          }
        }
        expect(
          foundDeleteButton,
          isTrue,
          reason: 'Delete button with custom tooltip not found',
        );
      });
    });

    group('Data Mapper Tests', () {
      testWidgets('should use custom data mapper for initial data', (
        tester,
      ) async {
        const testEntity = TestEntity(
          id: '1',
          name: 'Mapped Entity',
          description: 'Mapped description',
        );

        final config = GenericFormConfig<TestEntity>(
          title: 'Edit Entity',
          fields: testFields,
          initialData: testEntity,
          dataMapper: TestEntityDataMapper(),
          onSubmit: (data) async {
            submittedData = data;
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Mapped Entity'), findsOneWidget);
        expect(find.text('Mapped description'), findsOneWidget);
      });

      testWidgets('should handle null initial data gracefully', (tester) async {
        final config = GenericFormConfig<TestEntity>(
          title: 'Create Entity',
          fields: testFields,
          initialData: null,
          onSubmit: (data) async {
            submittedData = data;
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Create Entity'), findsOneWidget);
        expect(find.text('Create'), findsOneWidget);
      });

      testWidgets('should handle entity without toJson method gracefully', (
        tester,
      ) async {
        // Create an entity without toJson method
        final simpleEntity = {'id': '1', 'name': 'Simple Entity'};

        final config = GenericFormConfig<Map<String, dynamic>>(
          title: 'Edit Simple Entity',
          fields: testFields,
          initialData: simpleEntity,
          onSubmit: (data) async {
            submittedData = data;
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<Map<String, dynamic>>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Edit Simple Entity'), findsOneWidget);
        expect(find.text('Simple Entity'), findsOneWidget);
      });
    });

    group('Form Validation Tests', () {
      testWidgets('should validate required fields', (tester) async {
        final config = GenericFormConfig<TestEntity>(
          title: 'Create Entity',
          fields: testFields,
          onSubmit: (data) async {
            submittedData = data;
          },
        );

        await tester.pumpWidget(
          TestWrapper.createFormTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        // Try to submit without filling required field
        await tester.tap(find.text('Create'));
        await tester.pump(); // Trigger validation
        await tester.pump(); // Allow error text to appear

        // The form field uses the custom validation message from the field config
        expect(find.text('Name is required'), findsOneWidget);
        expect(submittedData.isEmpty, isTrue);
      });

      testWidgets('should use custom form validator', (tester) async {
        final config = GenericFormConfig<TestEntity>(
          title: 'Create Entity',
          fields: testFields,
          onSubmit: (data) async {
            submittedData = data;
          },
          validator: (data) {
            if (data['name'] == 'invalid') {
              return 'Custom validation error';
            }
            return null;
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        // Enter invalid data
        await tester.enterText(find.byKey(const ValueKey('name')), 'invalid');

        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        expect(find.text('Custom validation error'), findsOneWidget);
      });
    });

    group('Field Change Callback Tests', () {
      testWidgets('should call onFieldChanged when field values change', (
        tester,
      ) async {
        final config = GenericFormConfig<TestEntity>(
          title: 'Create Entity',
          fields: testFields,
          onSubmit: (data) async {
            submittedData = data;
          },
          onFieldChanged: (key, value) {
            fieldChangedKey = key;
            fieldChangedValue = value;
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        await tester.enterText(find.byKey(const ValueKey('name')), 'Test Name');
        await tester.pumpAndSettle();

        expect(fieldChangedKey, equals('name'));
        expect(fieldChangedValue, equals('Test Name'));
      });
    });

    group('Custom Actions Tests', () {
      testWidgets('should show custom actions when provided', (tester) async {
        final customActions = [
          IconButton(icon: const Icon(Icons.help), onPressed: () {}),
          IconButton(icon: const Icon(Icons.info), onPressed: () {}),
        ];

        final config = GenericFormConfig<TestEntity>(
          title: 'Create Entity',
          fields: testFields,
          onSubmit: (data) async {
            submittedData = data;
          },
          customActions: customActions,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.byIcon(Icons.help), findsOneWidget);
        expect(find.byIcon(Icons.info), findsOneWidget);
      });
    });

    group('BaseEditableFormScreen Integration Tests', () {
      testWidgets('should pass configuration to BaseEditableFormScreen', (
        tester,
      ) async {
        final config = GenericFormConfig<TestEntity>(
          title: 'Integration Test',
          fields: testFields,
          onSubmit: (data) async {
            submittedData = data;
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        // Check that BaseEditableFormScreen is rendered
        expect(find.byType(BaseEditableFormScreen<TestEntity>), findsOneWidget);
        expect(find.text('Integration Test'), findsOneWidget);
      });

      testWidgets('should handle loading state correctly', (tester) async {
        final config = GenericFormConfig<TestEntity>(
          title: 'Loading Test',
          fields: testFields,
          onSubmit: (data) async {
            submittedData = data;
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        // The form should not be in loading state by default
        expect(find.byType(CircularProgressIndicator), findsNothing);
      });
    });

    group('Configuration Validation Tests', () {
      testWidgets('should handle empty field list gracefully', (tester) async {
        final config = GenericFormConfig<TestEntity>(
          title: 'Empty Fields Test',
          fields: [],
          onSubmit: (data) async {
            submittedData = data;
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Empty Fields Test'), findsOneWidget);
        expect(find.text('Create'), findsOneWidget);
      });

      testWidgets('should handle complex field configurations', (tester) async {
        final complexFields = <FormFieldConfig<dynamic>>[
          TextFieldConfig(
            key: 'email',
            label: 'Email',
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value?.isEmpty ?? true) return 'Email is required';
              if (!value!.contains('@')) return 'Invalid email';
              return null;
            },
          ),
          TextFieldConfig(
            key: 'password',
            label: 'Password',
            keyboardType: TextInputType.visiblePassword,
            validator: (value) {
              if (value?.isEmpty ?? true) return 'Password is required';
              if (value!.length < 6) return 'Password too short';
              return null;
            },
          ),
          DatePickerFieldConfig(
            key: 'birthdate',
            label: 'Birth Date',
            firstDate: DateTime(1900),
            lastDate: DateTime.now(),
          ),
        ];

        final config = GenericFormConfig<TestEntity>(
          title: 'Complex Form',
          fields: complexFields,
          onSubmit: (data) async {
            submittedData = data;
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Complex Form'), findsOneWidget);
        expect(find.text('Email'), findsOneWidget);
        expect(find.text('Password'), findsOneWidget);
        expect(find.text('Birth Date'), findsOneWidget);
      });
    });

    group('Error Handling Tests', () {
      testWidgets('should handle form submission errors gracefully', (
        tester,
      ) async {
        final config = GenericFormConfig<TestEntity>(
          title: 'Error Test',
          fields: testFields,
          onSubmit: (data) async {
            throw Exception('Submission error');
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        await tester.enterText(find.byKey(const Key('name')), 'Test Name');
        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // The form should handle the error gracefully
        expect(find.text('Error Test'), findsOneWidget);
      });

      testWidgets('should handle delete operation errors gracefully', (
        tester,
      ) async {
        const testEntity = TestEntity(
          id: '1',
          name: 'Test Entity',
          description: 'Test description',
        );

        final config = GenericFormConfig<TestEntity>(
          title: 'Delete Error Test',
          fields: testFields,
          initialData: testEntity,
          onSubmit: (data) async {
            submittedData = data;
          },
          showDeleteButton: true,
          onDelete: () async {
            throw Exception('Delete error');
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            GenericFormScreen<TestEntity>(config: config),
          ),
        );
        await tester.pumpAndSettle();

        // Tap the delete icon button first
        await tester.tap(find.byIcon(Icons.delete_outline));
        await tester.pumpAndSettle();

        // Then confirm delete in the dialog
        await tester.tap(find.text('Delete'));
        await tester.pumpAndSettle();

        // The form should handle the error gracefully and show error message
        expect(find.text('Delete Error Test'), findsOneWidget);
        expect(find.text('Error: Exception: Delete error'), findsOneWidget);
      });
    });

    group('GenericFormConfig Tests', () {
      test('should create config with required parameters', () {
        final config = GenericFormConfig<TestEntity>(
          title: 'Test Form',
          fields: testFields,
          onSubmit: (data) async {},
        );

        expect(config.title, equals('Test Form'));
        expect(config.fields, equals(testFields));
        expect(config.isCreateMode, isTrue);
        expect(config.isEditMode, isFalse);
      });

      test('should detect create mode correctly', () {
        final config = GenericFormConfig<TestEntity>(
          title: 'Create Test',
          fields: testFields,
          onSubmit: (data) async {},
        );

        expect(config.isCreateMode, isTrue);
        expect(config.isEditMode, isFalse);
        expect(config.getEffectiveSaveButtonText(), equals('Create'));
      });

      test('should detect edit mode correctly', () {
        const testEntity = TestEntity(
          id: '1',
          name: 'Test',
          description: 'Test',
        );

        final config = GenericFormConfig<TestEntity>(
          title: 'Edit Test',
          fields: testFields,
          initialData: testEntity,
          onSubmit: (data) async {},
        );

        expect(config.isCreateMode, isFalse);
        expect(config.isEditMode, isTrue);
        expect(config.getEffectiveSaveButtonText(), equals('Save'));
      });

      test('should return custom button text when provided', () {
        final config = GenericFormConfig<TestEntity>(
          title: 'Custom Button Test',
          fields: testFields,
          onSubmit: (data) async {},
          saveButtonText: 'Custom Save',
          deleteButtonText: 'Custom Delete',
        );

        expect(config.getEffectiveSaveButtonText(), equals('Custom Save'));
        expect(config.getEffectiveDeleteButtonText(), equals('Custom Delete'));
      });

      test('should handle copyWith correctly', () {
        final originalConfig = GenericFormConfig<TestEntity>(
          title: 'Original',
          fields: testFields,
          onSubmit: (data) async {},
        );

        final copiedConfig = originalConfig.copyWith(
          title: 'Updated',
          saveButtonText: 'Updated Save',
        );

        expect(copiedConfig.title, equals('Updated'));
        expect(copiedConfig.saveButtonText, equals('Updated Save'));
        expect(copiedConfig.fields, equals(testFields));
      });
    });

    group('DefaultFormDataMapper Tests', () {
      test('should map entity to form data using toJson', () {
        final mapper = DefaultFormDataMapper<TestEntity>(
          fromJsonFactory: TestEntity.fromJson,
        );

        const entity = TestEntity(
          id: '1',
          name: 'Test Entity',
          description: 'Test description',
        );

        final formData = mapper.entityToFormData(entity);

        expect(formData['id'], equals('1'));
        expect(formData['name'], equals('Test Entity'));
        expect(formData['description'], equals('Test description'));
      });

      test('should map form data to entity using fromJson', () {
        final mapper = DefaultFormDataMapper<TestEntity>(
          fromJsonFactory: TestEntity.fromJson,
        );

        final formData = {
          'id': '1',
          'name': 'Test Entity',
          'description': 'Test description',
        };

        final entity = mapper.formDataToEntity(formData);

        expect(entity.id, equals('1'));
        expect(entity.name, equals('Test Entity'));
        expect(entity.description, equals('Test description'));
      });

      test('should use custom toJson method when provided', () {
        Map<String, String> customToJson(TestEntity entity) => {
          'custom_id': entity.id,
          'custom_name': entity.name,
        };

        final mapper = DefaultFormDataMapper<TestEntity>(
          fromJsonFactory: TestEntity.fromJson,
          toJsonMethod: customToJson,
        );

        const entity = TestEntity(
          id: '1',
          name: 'Test Entity',
          description: 'Test description',
        );

        final formData = mapper.entityToFormData(entity);

        expect(formData['custom_id'], equals('1'));
        expect(formData['custom_name'], equals('Test Entity'));
        expect(formData.containsKey('description'), isFalse);
      });
    });

    group('FormValidationResult Tests', () {
      test('should create valid result', () {
        const result = FormValidationResult.valid;

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
        expect(result.globalError, isNull);
      });

      test('should create invalid result with errors', () {
        const result = FormValidationResult.invalid(
          errors: {'name': 'Name is required'},
          globalError: 'Form has errors',
        );

        expect(result.isValid, isFalse);
        expect(result.errors['name'], equals('Name is required'));
        expect(result.globalError, equals('Form has errors'));
      });

      test('should provide valid constant', () {
        const result = FormValidationResult.valid;

        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
        expect(result.globalError, isNull);
      });
    });
  });
}
