import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/factory/form_field_factory.dart';
import 'package:budapp/widgets/forms/interfaces/form_field_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../helpers/test_wrapper.dart';

void main() {
  group('FormFieldFactory Tests', () {
    group('Field Creation', () {
      test('should create text field from TextFieldConfig', () {
        final config = TextFieldConfig(
          key: 'test_field',
          label: 'Test Field',
          hintText: 'Enter text',
          isRequired: true,
          validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
        );

        final field = FormFieldFactory.createField(config);

        expect(field, isA<IFormField<String>>());
        expect(field.key, equals('test_field'));
        expect(field.isRequired, isTrue);
      });

      test('should create dropdown field from DropdownFieldConfig', () {
        const config = DropdownFieldConfig<String>(
          key: 'dropdown_field',
          label: 'Select Option',
          items: ['Option 1', 'Option 2', 'Option 3'],
          isRequired: true,
        );

        final field = FormFieldFactory.createField(config);

        expect(field, isA<IFormField<dynamic>>());
        expect(field.key, equals('dropdown_field'));
        expect(field.isRequired, isTrue);
      });

      test('should create color picker field from ColorPickerFieldConfig', () {
        const config = ColorPickerFieldConfig(
          key: 'color_field',
          label: 'Choose Color',
          availableColors: ['#FF0000', '#00FF00', '#0000FF'],
          isRequired: false,
        );

        final field = FormFieldFactory.createField(config);

        expect(field, isA<IFormField<dynamic>>());
        expect(field.key, equals('color_field'));
        expect(field.isRequired, isFalse);
      });

      test('should create icon picker field from IconPickerFieldConfig', () {
        const config = IconPickerFieldConfig(
          key: 'icon_field',
          label: 'Select Icon',
          availableIcons: ['home', 'work', 'favorite'],
          isRequired: false,
        );

        final field = FormFieldFactory.createField(config);

        expect(field, isA<IFormField<dynamic>>());
        expect(field.key, equals('icon_field'));
        expect(field.isRequired, isFalse);
      });

      test('should create date picker field from DatePickerFieldConfig', () {
        final config = DatePickerFieldConfig(
          key: 'date_field',
          label: 'Select Date',
          firstDate: DateTime(2020, 1, 1),
          lastDate: DateTime(2030, 12, 31),
          isRequired: true,
        );

        final field = FormFieldFactory.createField(config);

        expect(field, isA<IFormField<dynamic>>());
        expect(field.key, equals('date_field'));
        expect(field.isRequired, isTrue);
      });

      test('should handle unsupported field type gracefully', () {
        const config = FormFieldConfig<String>(
          key: 'unsupported_field',
          type: FormFieldType.custom,
          label: 'Custom Field',
        );

        expect(
          () => FormFieldFactory.createField(config),
          throwsA(isA<UnsupportedError>()),
        );
      });
    });

    group('Widget Creation', () {
      testWidgets('should create text widget from TextFieldConfig', (
        tester,
      ) async {
        const config = TextFieldConfig(
          key: 'text_widget',
          label: 'Text Widget',
          hintText: 'Enter text',
        );

        // Test the widget creation inside MaterialApp context
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(body: FormFieldFactory.createWidget(config)),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Text Widget'), findsOneWidget);
        expect(find.text('Enter text'), findsOneWidget);
      });

      testWidgets('should create dropdown widget from DropdownFieldConfig', (
        tester,
      ) async {
        const config = DropdownFieldConfig<String>(
          key: 'dropdown_widget',
          label: 'Dropdown Widget',
          items: ['Item 1', 'Item 2', 'Item 3'],
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(body: FormFieldFactory.createWidget(config)),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Dropdown Widget'), findsOneWidget);
        expect(find.byType(DropdownButtonFormField<String>), findsOneWidget);
      });

      testWidgets(
        'should create color picker widget from ColorPickerFieldConfig',
        (tester) async {
          const config = ColorPickerFieldConfig(
            key: 'color_widget',
            label: 'Color Widget',
            availableColors: ['#FF0000', '#00FF00', '#0000FF'],
          );

          await tester.pumpWidget(
            TestWrapper.createTestWidget(
              Scaffold(body: FormFieldFactory.createWidget(config)),
            ),
          );
          await tester.pumpAndSettle();

          expect(find.text('Color Widget'), findsOneWidget);
          // Color picker should render multiple color options (3 available colors)
          expect(find.byType(GestureDetector), findsAtLeastNWidgets(3));
        },
      );

      testWidgets(
        'should create icon picker widget from IconPickerFieldConfig',
        (tester) async {
          const config = IconPickerFieldConfig(
            key: 'icon_widget',
            label: 'Icon Widget',
            availableIcons: ['home', 'work', 'favorite'],
          );

          await tester.pumpWidget(
            TestWrapper.createTestWidget(
              Scaffold(body: FormFieldFactory.createWidget(config)),
            ),
          );
          await tester.pumpAndSettle();

          expect(find.text('Icon Widget'), findsOneWidget);
          // Icon picker should render multiple icon options (3 available icons)
          expect(find.byType(GestureDetector), findsAtLeastNWidgets(3));
          expect(find.byType(Icon), findsAtLeastNWidgets(3));
        },
      );

      testWidgets(
        'should create date picker widget from DatePickerFieldConfig',
        (tester) async {
          final config = DatePickerFieldConfig(
            key: 'date_widget',
            label: 'Date Widget',
            firstDate: DateTime(2020, 1, 1),
            lastDate: DateTime(2030, 12, 31),
          );

          await tester.pumpWidget(
            TestWrapper.createTestWidget(
              Scaffold(body: FormFieldFactory.createWidget(config)),
            ),
          );
          await tester.pumpAndSettle();

          expect(find.text('Date Widget'), findsOneWidget);
          // Date picker should render as an InkWell with calendar icon
          expect(find.byType(InkWell), findsOneWidget);
          expect(find.byIcon(Icons.calendar_today), findsOneWidget);
        },
      );

      testWidgets('should handle widget creation with existing field', (
        tester,
      ) async {
        const config = TextFieldConfig(
          key: 'existing_field',
          label: 'Existing Field',
          initialValue: 'Initial value',
        );

        // Create field first
        final field = FormFieldFactory.createField(config)
          ..value = 'Modified value';

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(
              body: FormFieldFactory.createWidget(config, existingField: field),
            ),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Existing Field'), findsOneWidget);
        expect(find.text('Modified value'), findsOneWidget);
      });

      testWidgets('should handle unsupported widget type gracefully', (
        tester,
      ) async {
        const config = FormFieldConfig<String>(
          key: 'unsupported_widget',
          type: FormFieldType.custom,
          label: 'Custom Widget',
        );

        expect(
          () => FormFieldFactory.createWidget(config),
          throwsA(isA<UnsupportedError>()),
        );
      });
    });

    group('Field Type Validation', () {
      test('should validate text field type correctly', () {
        const config = TextFieldConfig(key: 'text_field', label: 'Text Field');

        expect(config.type, equals(FormFieldType.text));
      });

      test('should validate email field type correctly', () {
        const config = TextFieldConfig(
          key: 'email_field',
          label: 'Email Field',
          keyboardType: TextInputType.emailAddress,
        );

        expect(config.type, equals(FormFieldType.text));
        expect(config.keyboardType, equals(TextInputType.emailAddress));
      });

      test('should validate password field type correctly', () {
        const config = TextFieldConfig(
          key: 'password_field',
          label: 'Password Field',
          keyboardType: TextInputType.visiblePassword,
        );

        expect(config.type, equals(FormFieldType.text));
        expect(config.keyboardType, equals(TextInputType.visiblePassword));
      });

      test('should validate number field type correctly', () {
        const config = TextFieldConfig(
          key: 'number_field',
          label: 'Number Field',
          keyboardType: TextInputType.number,
        );

        expect(config.type, equals(FormFieldType.text));
        expect(config.keyboardType, equals(TextInputType.number));
      });

      test('should validate currency field type correctly', () {
        const config = TextFieldConfig(
          key: 'currency_field',
          label: 'Currency Field',
          keyboardType: TextInputType.numberWithOptions(decimal: true),
        );

        expect(config.type, equals(FormFieldType.text));
        expect(
          config.keyboardType,
          equals(const TextInputType.numberWithOptions(decimal: true)),
        );
      });

      test('should validate multiline text field type correctly', () {
        const config = TextFieldConfig(
          key: 'multiline_field',
          label: 'Multiline Field',
          maxLines: 5,
          minLines: 2,
        );

        expect(config.type, equals(FormFieldType.text));
        expect(config.maxLines, equals(5));
        expect(config.minLines, equals(2));
      });

      test('should validate dropdown field type correctly', () {
        const config = DropdownFieldConfig<String>(
          key: 'dropdown_field',
          label: 'Dropdown Field',
          items: ['Option 1', 'Option 2'],
        );

        expect(config.type, equals(FormFieldType.dropdown));
        expect(config.items, equals(['Option 1', 'Option 2']));
      });

      test('should validate color picker field type correctly', () {
        const config = ColorPickerFieldConfig(
          key: 'color_field',
          label: 'Color Field',
          availableColors: ['#FF0000', '#00FF00'],
        );

        expect(config.type, equals(FormFieldType.colorPicker));
        expect(config.availableColors, equals(['#FF0000', '#00FF00']));
      });

      test('should validate icon picker field type correctly', () {
        const config = IconPickerFieldConfig(
          key: 'icon_field',
          label: 'Icon Field',
          availableIcons: ['home', 'work'],
        );

        expect(config.type, equals(FormFieldType.iconPicker));
        expect(config.availableIcons, equals(['home', 'work']));
      });

      test('should validate date picker field type correctly', () {
        final config = DatePickerFieldConfig(
          key: 'date_field',
          label: 'Date Field',
          firstDate: DateTime(2020, 1, 1),
          lastDate: DateTime(2030, 12, 31),
        );

        expect(config.type, equals(FormFieldType.datePicker));
        expect(config.firstDate, equals(DateTime(2020, 1, 1)));
        expect(config.lastDate, equals(DateTime(2030, 12, 31)));
      });
    });

    group('Field Configuration Validation', () {
      test('should validate required field configuration', () {
        const config = TextFieldConfig(
          key: 'required_field',
          label: 'Required Field',
          isRequired: true,
        );

        expect(config.isRequired, isTrue);
      });

      test('should validate optional field configuration', () {
        const config = TextFieldConfig(
          key: 'optional_field',
          label: 'Optional Field',
          isRequired: false,
        );

        expect(config.isRequired, isFalse);
      });

      test('should validate field with validator', () {
        String? validator(String? value) =>
            value?.isEmpty ?? true ? 'Required' : null;
        final config = TextFieldConfig(
          key: 'validated_field',
          label: 'Validated Field',
          validator: validator,
        );

        expect(config.validator, equals(validator));
      });

      test('should validate field with initial value', () {
        const config = TextFieldConfig(
          key: 'initial_field',
          label: 'Initial Field',
          initialValue: 'Initial value',
        );

        expect(config.initialValue, equals('Initial value'));
      });

      test('should validate field with hint text', () {
        const config = TextFieldConfig(
          key: 'hint_field',
          label: 'Hint Field',
          hintText: 'Enter value here',
        );

        expect(config.hintText, equals('Enter value here'));
      });

      test('should validate field with custom properties', () {
        const config = TextFieldConfig(
          key: 'custom_field',
          label: 'Custom Field',
        );

        expect(config.customProperties, equals({}));
      });

      test('should validate enabled field configuration', () {
        const config = TextFieldConfig(
          key: 'enabled_field',
          label: 'Enabled Field',
          enabled: true,
        );

        expect(config.enabled, isTrue);
      });

      test('should validate disabled field configuration', () {
        const config = TextFieldConfig(
          key: 'disabled_field',
          label: 'Disabled Field',
          enabled: false,
        );

        expect(config.enabled, isFalse);
      });

      test('should validate autofocus field configuration', () {
        const config = TextFieldConfig(
          key: 'autofocus_field',
          label: 'Autofocus Field',
          autofocus: true,
        );

        expect(config.autofocus, isTrue);
      });

      test('should validate field with onChange callback', () {
        void onChanged(String? value) {
          // Callback function for testing
        }

        final config = TextFieldConfig(
          key: 'onchange_field',
          label: 'OnChange Field',
          onChanged: onChanged,
        );

        expect(config.onChanged, equals(onChanged));
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle empty key gracefully', () {
        const config = TextFieldConfig(key: '', label: 'Empty Key Field');
        expect(config.key, equals(''));
        expect(config.label, equals('Empty Key Field'));
      });

      test('should handle null key gracefully', () {
        // Since key is non-nullable, this test verifies that the type system
        // prevents null keys at compile time. We test with empty string instead.
        const config = TextFieldConfig(key: '', label: 'Empty Key Field');
        expect(config.key, equals(''));
        expect(config.label, equals('Empty Key Field'));
      });

      test('should handle empty label gracefully', () {
        const config = TextFieldConfig(key: 'empty_label_field', label: '');

        expect(config.label, equals(''));
      });

      test('should handle null items in dropdown gracefully', () {
        // Test with empty list instead of null since items is non-nullable
        const config = DropdownFieldConfig<String>(
          key: 'empty_items_field',
          label: 'Empty Items Field',
          items: [],
        );
        expect(config.items, equals([]));
        expect(config.key, equals('empty_items_field'));
      });

      test('should handle empty items in dropdown gracefully', () {
        const config = DropdownFieldConfig<String>(
          key: 'empty_items_field',
          label: 'Empty Items Field',
          items: [],
        );

        expect(config.items, equals([]));
      });

      test('should handle null colors in color picker gracefully', () {
        const config = ColorPickerFieldConfig(
          key: 'null_colors_field',
          label: 'Null Colors Field',
          availableColors: [],
        );

        expect(config.availableColors, equals([]));
      });

      test('should handle empty colors in color picker gracefully', () {
        const config = ColorPickerFieldConfig(
          key: 'empty_colors_field',
          label: 'Empty Colors Field',
          availableColors: [],
        );

        expect(config.availableColors, equals([]));
      });

      test('should handle null icons in icon picker gracefully', () {
        const config = IconPickerFieldConfig(
          key: 'null_icons_field',
          label: 'Null Icons Field',
          availableIcons: [],
        );

        expect(config.availableIcons, equals([]));
      });

      test('should handle empty icons in icon picker gracefully', () {
        const config = IconPickerFieldConfig(
          key: 'empty_icons_field',
          label: 'Empty Icons Field',
          availableIcons: [],
        );

        expect(config.availableIcons, equals([]));
      });

      test('should handle invalid date range gracefully', () {
        final config = DatePickerFieldConfig(
          key: 'invalid_date_field',
          label: 'Invalid Date Field',
          firstDate: DateTime(2030, 1, 1),
          lastDate: DateTime(
            2020,
            1,
            1,
          ), // Invalid: last date before first date
        );

        expect(config.firstDate, equals(DateTime(2030, 1, 1)));
        expect(config.lastDate, equals(DateTime(2020, 1, 1)));
      });
    });

    group('Form Field Interface Compliance', () {
      test('should implement IFormField interface correctly', () {
        const config = TextFieldConfig(
          key: 'interface_field',
          label: 'Interface Field',
          isRequired: true,
        );

        final field = FormFieldFactory.createField(config);

        expect(field, isA<IFormField<dynamic>>());
        expect(field.key, equals('interface_field'));
        expect(field.isRequired, isTrue);
        expect(field.value, isNull);
        expect(field.errorText, isNull);
        expect(
          field.validate(),
          isNotNull,
        ); // Required field with no value should fail
      });

      test('should handle field value assignment correctly', () {
        const config = TextFieldConfig(
          key: 'set_value_field',
          label: 'Set Value Field',
        );

        final field = FormFieldFactory.createField(config)..value = 'New value';

        expect(field.value, equals('New value'));
      });

      test('should handle field validation correctly', () {
        final config = TextFieldConfig(
          key: 'validation_field',
          label: 'Validation Field',
          isRequired: true,
          validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
        );

        final field = FormFieldFactory.createField(config);

        // Test validation with empty value
        final errorEmpty = field.validate();
        expect(errorEmpty, equals('Required'));
        expect(field.errorText, equals('Required'));

        // Test validation with valid value
        field.value = 'Valid value';
        final errorValid = field.validate();
        expect(errorValid, isNull);
        expect(field.errorText, isNull);
      });

      test('should handle field disposal correctly', () {
        const config = TextFieldConfig(
          key: 'disposal_field',
          label: 'Disposal Field',
        );

        final field = FormFieldFactory.createField(config)
          ..value = 'Test value';

        // Should not throw when disposing
        expect(field.dispose, returnsNormally);
      });

      test('should handle field reset correctly', () {
        const config = TextFieldConfig(
          key: 'reset_field',
          label: 'Reset Field',
          initialValue: 'Initial value',
        );

        final field = FormFieldFactory.createField(config)
          ..value = 'Modified value';
        expect(field.value, equals('Modified value'));

        field.reset();
        expect(field.value, equals('Initial value'));
      });
    });
  });
}
