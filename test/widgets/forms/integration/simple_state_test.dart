import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/implementations/color_picker_form_field_impl.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Simple State Management Tests', () {
    testWidgets('ColorPicker should update visual state when value changes', (
      tester,
    ) async {
      String? callbackValue;
      late ColorPickerFormFieldImpl colorPicker;

      // Create the color picker once, like BaseEditableFormScreen does
      final config = ColorPickerFieldConfig(
        key: 'color',
        label: 'Test Color',
        availableColors: ['#F44336', '#4CAF50', '#2196F3'],
        initialValue: '#F44336',
        onChanged: (value) {
          callbackValue = value;
        },
      );

      colorPicker = ColorPickerFormFieldImpl(config);

      // Test wrapper that rebuilds when value changes
      Widget buildColorPicker() {
        return MaterialApp(
          home: Scaffold(
            body: SingleChildScrollView(
              child: SizedBox(
                width: double.infinity,
                child: Builder(
                  builder: (context) => colorPicker.build(context),
                ),
              ),
            ),
          ),
        );
      }

      await tester.pumpWidget(buildColorPicker());

      // Initially should show red as selected
      expect(colorPicker.value, equals('#F44336'));

      // Tap the second color (green) to change selection
      final gestureDetectors = find.byType(GestureDetector);
      expect(gestureDetectors, findsAtLeastNWidgets(3));

      await tester.tap(gestureDetectors.at(1));
      await tester.pump();

      // Should have called the callback
      expect(callbackValue, equals('#4CAF50'));

      // Rebuild the widget to see if visual state updates
      await tester.pumpWidget(buildColorPicker());

      // The visual state should now reflect the new selection
      // We can test this by checking the value property
      expect(colorPicker.value, equals('#4CAF50'));
    });

    testWidgets(
      'Form field value changes should be persistent after rebuilds',
      (tester) async {
        late ColorPickerFormFieldImpl colorPicker;
        var buildCount = 0;

        late StateSetter setStateCallback;

        // Create the colorPicker with onChanged that triggers setState
        final config = ColorPickerFieldConfig(
          key: 'color',
          label: 'Test Color',
          availableColors: ['#F44336', '#4CAF50', '#2196F3'],
          initialValue: '#F44336',
          onChanged: (value) {
            // This simulates how BaseEditableFormScreen wraps onChanged
            setStateCallback(() {
              // Trigger rebuild when value changes
            });
          },
        );
        colorPicker = ColorPickerFormFieldImpl(config);

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: StatefulBuilder(
                    builder: (context, setState) {
                      setStateCallback =
                          setState; // Capture setState for use in onChanged
                      buildCount++;
                      return colorPicker.build(context);
                    },
                  ),
                ),
              ),
            ),
          ),
        );

        expect(buildCount, equals(1));
        expect(colorPicker.value, equals('#F44336'));

        // Simulate user interaction instead of direct property assignment
        final gestureDetectors = find.byType(GestureDetector);
        expect(gestureDetectors, findsAtLeastNWidgets(3));

        // Tap the second color (green) to simulate user interaction
        await tester.tap(gestureDetectors.at(1));
        await tester.pump();

        // Should have triggered a rebuild through onChanged callback
        expect(buildCount, equals(2));

        // Value should persist after rebuild
        expect(colorPicker.value, equals('#4CAF50'));
      },
    );
  });
}
