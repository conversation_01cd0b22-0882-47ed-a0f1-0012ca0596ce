import 'package:budapp/data/models/goal_contribution.dart';
import 'package:budapp/data/repositories/interfaces/i_goal_contribution_repository.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/config/generic_form_config.dart';
import 'package:budapp/widgets/forms/configs/goal_contribution_form_config.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/mock_data_factory.dart';

// Mock repository for testing
class MockGoalContributionRepository extends Mock
    implements IGoalContributionRepository {}

void main() {
  group('GoalContributionFormConfig Tests', () {
    late MockGoalContributionRepository mockRepository;

    setUpAll(() {
      // Use MockDataFactory to create fallback values instead of Fake classes
      registerFallbackValue(MockDataFactory.createGoalContribution());
    });

    setUp(() {
      mockRepository = MockGoalContributionRepository();
    });

    group('Create Configuration', () {
      test('should create configuration for new contribution', () {
        // Act
        final config = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        );

        // Assert
        expect(config, isA<GenericFormConfig<GoalContribution>>());
        expect(config.title, equals('Add Contribution'));
        expect(config.fields, hasLength(3));
        expect(config.initialData, isNull);
        expect(config.showDeleteButton, isFalse);
        expect(config.onSubmit, isNotNull);
        expect(config.dataMapper, isNotNull);
      });

      test('should create configuration with field change callback', () {
        // Arrange
        var fieldChangedCalled = false;
        void onFieldChanged(String fieldKey, dynamic value) {
          fieldChangedCalled = true;
        }

        // Act
        final config = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
          onFieldChanged: onFieldChanged,
        );

        // Assert
        expect(config.onFieldChanged, isNotNull);

        // Test callback
        config.onFieldChanged!('test', 'value');
        expect(fieldChangedCalled, isTrue);
      });

      test('should have correct field configurations for create', () {
        // Act
        final config = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        );

        // Assert
        expect(config.fields, hasLength(3));

        // Amount field
        final amountField = config.fields[0];
        expect(amountField.key, equals('amount'));
        expect(amountField.isRequired, isTrue);

        // Contribution date field
        final dateField = config.fields[1];
        expect(dateField.key, equals('contributionDate'));
        expect(dateField.isRequired, isTrue);

        // Description field
        final descriptionField = config.fields[2];
        expect(descriptionField.key, equals('description'));
        expect(descriptionField.isRequired, isFalse);
      });
    });

    group('Edit Configuration', () {
      late GoalContribution testContribution;

      setUp(() {
        testContribution = GoalContribution.create(
          userId: 'test-user-id',
          goalId: 'test-goal-id',
          amountCents: 10000, // $100.00
          contributionDate: DateTime(2024, 1, 15),
          description: 'Test contribution',
        );
      });

      test('should create configuration for editing existing contribution', () {
        // Act
        final config = GoalContributionFormConfig.edit(
          contribution: testContribution,
          repository: mockRepository,
        );

        // Assert
        expect(config, isA<GenericFormConfig<GoalContribution>>());
        expect(config.title, equals('Edit Contribution'));
        expect(config.fields, hasLength(3));
        expect(config.initialData, equals(testContribution));
        expect(config.showDeleteButton, isTrue);
        expect(config.onSubmit, isNotNull);
        expect(config.onDelete, isNotNull);
        expect(config.dataMapper, isNotNull);
      });
    });

    group('Field Configurations', () {
      test('should have amount field with correct configuration', () {
        // Act
        final config = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        );
        final amountField = config.fields.firstWhere((f) => f.key == 'amount');

        // Assert
        expect(amountField.key, equals('amount'));
        expect(amountField.isRequired, isTrue);
      });

      test(
        'should have contribution date field with correct configuration',
        () {
          // Act
          final config = GoalContributionFormConfig.create(
            goalId: 'test-goal-id',
            repository: mockRepository,
          );
          final dateField = config.fields.firstWhere(
            (f) => f.key == 'contributionDate',
          );

          // Assert
          expect(dateField.key, equals('contributionDate'));
          expect(dateField.isRequired, isTrue);
        },
      );

      test('should have description field with correct configuration', () {
        // Act
        final config = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        );
        final descriptionField = config.fields.firstWhere(
          (f) => f.key == 'description',
        );

        // Assert
        expect(descriptionField.key, equals('description'));
        expect(descriptionField.isRequired, isFalse);
      });
    });

    group('Form Data to Entity Conversion', () {
      test(
        'should convert form data with string amount to GoalContribution entity',
        () {
          // Simulate form data as it comes from the form
          final formData = {
            'amount': '100.50', // String as expected from TextFieldConfig
            'contributionDate': DateTime(2024, 1, 15),
            'description': 'Monthly contribution',
          };

          // Convert using the data mapper
          final dataMapper = GoalContributionFormConfig.create(
            goalId: 'test-goal-id',
            repository: mockRepository,
          ).dataMapper!;
          final contribution = dataMapper.formDataToEntity(formData);

          // Verify conversion
          expect(
            contribution.amountCents,
            equals(10050),
          ); // $100.50 = 10050 cents
          expect(contribution.contributionDate, equals(DateTime(2024, 1, 15)));
          expect(contribution.description, equals('Monthly contribution'));
        },
      );

      test(
        'should convert form data with numeric amount to GoalContribution entity',
        () {
          // Simulate form data with numeric amount (as seen in some cases)
          final formData = {
            'amount': '50', // String value from form
            'contributionDate': DateTime(2024, 2, 1),
            'description': null,
          };

          // Convert using the data mapper
          final dataMapper = GoalContributionFormConfig.create(
            goalId: 'test-goal-id',
            repository: mockRepository,
          ).dataMapper!;
          final contribution = dataMapper.formDataToEntity(formData);

          // Verify conversion
          expect(contribution.amountCents, equals(5000)); // $50 = 5000 cents
          expect(contribution.contributionDate, equals(DateTime(2024, 2, 1)));
          expect(contribution.description, isNull);
        },
      );

      test('should handle edge cases in amount conversion', () {
        final testCases = [
          {'input': '0', 'expected': 0, 'description': 'zero string'},
          {'input': '0.01', 'expected': 1, 'description': 'minimum cents'},
          {
            'input': '999999.99',
            'expected': 99999999,
            'description': 'large amount',
          },
          {'input': '', 'expected': 0, 'description': 'empty string'},
          {'input': null, 'expected': 0, 'description': 'null value'},
          {'input': 'invalid', 'expected': 0, 'description': 'invalid string'},
        ];

        for (final testCase in testCases) {
          final formData = {
            'amount': testCase['input']?.toString() ?? '',
            'contributionDate': DateTime.now(),
          };

          final dataMapper = GoalContributionFormConfig.create(
            goalId: 'test-goal-id',
            repository: mockRepository,
          ).dataMapper!;
          final contribution = dataMapper.formDataToEntity(formData);

          expect(
            contribution.amountCents,
            equals(testCase['expected']),
            reason: 'Failed for ${testCase['description']}',
          );
        }
      });

      test('should handle null and empty description correctly', () {
        final testCases = [
          {'description': null, 'expected': null},
          {'description': '', 'expected': null},
          {'description': '   ', 'expected': '   '}, // Whitespace preserved
          {'description': 'Valid description', 'expected': 'Valid description'},
        ];

        for (final testCase in testCases) {
          final formData = {
            'amount': '100',
            'contributionDate': DateTime.now(),
            'description': testCase['description'],
          };

          final dataMapper = GoalContributionFormConfig.create(
            goalId: 'test-goal-id',
            repository: mockRepository,
          ).dataMapper!;
          final contribution = dataMapper.formDataToEntity(formData);

          expect(contribution.description, equals(testCase['expected']));
        }
      });

      test('should handle default date when not provided', () {
        final formData = {
          'amount': '100',
          // contributionDate not provided
        };

        final dataMapper = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        ).dataMapper!;
        final contribution = dataMapper.formDataToEntity(formData);

        // Should default to current date
        expect(contribution.contributionDate, isA<DateTime>());
      });
    });

    group('Entity to Form Data Conversion', () {
      test('should convert GoalContribution entity to form data', () {
        // Create a test contribution
        final contribution = GoalContribution.create(
          userId: 'test-user',
          goalId: 'test-goal',
          amountCents: 15075, // $150.75
          contributionDate: DateTime(2024, 3, 15),
          description: 'Quarterly contribution',
        );

        // Convert using the data mapper
        final dataMapper = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        ).dataMapper!;
        final formData = dataMapper.entityToFormData(contribution);

        // Verify conversion
        expect(formData['amount'], equals('150.75'));
        expect(formData['contributionDate'], equals(DateTime(2024, 3, 15)));
        expect(formData['description'], equals('Quarterly contribution'));
      });

      test(
        'should handle null description in entity to form data conversion',
        () {
          // Create a test contribution with null description
          final contribution = GoalContribution.create(
            userId: 'test-user',
            goalId: 'test-goal',
            amountCents: 10000, // $100.00
            contributionDate: DateTime(2024, 3, 15),
            description: null, // Null description
          );

          // Convert using the data mapper
          final dataMapper = GoalContributionFormConfig.create(
            goalId: 'test-goal-id',
            repository: mockRepository,
          ).dataMapper!;
          final formData = dataMapper.entityToFormData(contribution);

          // Verify conversion
          expect(formData['amount'], equals('100.00'));
          expect(formData['contributionDate'], equals(DateTime(2024, 3, 15)));
          expect(formData['description'], equals(''));
        },
      );
    });

    group('Entity Update with Form Data', () {
      test('should update existing entity with form data', () {
        // Create original contribution
        final originalContribution = GoalContribution.create(
          userId: 'test-user',
          goalId: 'test-goal',
          amountCents: 10000, // $100.00
          contributionDate: DateTime(2024, 1, 15),
          description: 'Original description',
        );

        // Form data with updates
        final formData = {
          'amount': '150.50',
          'contributionDate': DateTime(2024, 2, 15),
          'description': 'Updated description',
        };

        // Update using the data mapper
        final dataMapper = GoalContributionFormConfig.edit(
          contribution: originalContribution,
          repository: mockRepository,
        ).dataMapper!;
        final updatedContribution = dataMapper.updateEntityWithFormData(
          originalContribution,
          formData,
        );

        // Verify updates
        expect(updatedContribution.amountCents, equals(15050)); // $150.50
        expect(
          updatedContribution.contributionDate,
          equals(DateTime(2024, 2, 15)),
        );
        expect(updatedContribution.description, equals('Updated description'));
        expect(updatedContribution.updatedAt, isA<DateTime>());
      });

      test('should preserve unchanged fields when updating entity', () {
        // Create original contribution
        final originalContribution = GoalContribution.create(
          userId: 'test-user',
          goalId: 'test-goal',
          amountCents: 10000, // $100.00
          contributionDate: DateTime(2024, 1, 15),
          description: 'Original description',
        );

        // Form data with partial updates (only amount)
        final formData = {
          'amount': '150.50',
          // contributionDate and description not provided, should be preserved
        };

        // Update using the data mapper
        final dataMapper = GoalContributionFormConfig.edit(
          contribution: originalContribution,
          repository: mockRepository,
        ).dataMapper!;
        final updatedContribution = dataMapper.updateEntityWithFormData(
          originalContribution,
          formData,
        );

        // Verify only amount was updated, others preserved
        expect(updatedContribution.amountCents, equals(15050)); // $150.50
        expect(
          updatedContribution.contributionDate,
          equals(DateTime(2024, 1, 15)),
        ); // Preserved
        // Note: When description is not provided in form data, it becomes null in the updated entity
        expect(updatedContribution.description, isNull);
        expect(updatedContribution.updatedAt, isA<DateTime>());
      });
    });

    group('Amount Validation', () {
      test('should validate required amount field', () {
        // Act
        final config = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        );
        final amountField =
            config.fields.firstWhere((f) => f.key == 'amount')
                as TextFieldConfig;

        // Assert
        expect(amountField.isRequired, isTrue);
        expect(amountField.validator, isNotNull);

        // Test validation - cast to the correct function type
        final validator = amountField.validator;
        expect(validator!(null), equals('Contribution amount is required'));
        expect(validator(''), equals('Contribution amount is required'));
      });

      test('should validate amount format', () {
        // Act
        final config = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        );
        final amountField =
            config.fields.firstWhere((f) => f.key == 'amount')
                as TextFieldConfig;

        // Assert
        expect(amountField.validator, isNotNull);

        // Test validation - cast to the correct function type
        final validator = amountField.validator;
        expect(validator!('invalid'), equals('Please enter a valid amount'));
        expect(validator('100.50'), isNull); // Valid
        expect(
          validator('0'),
          equals('Contribution amount must be greater than zero'),
        );
        expect(
          validator('-10'),
          equals('Contribution amount must be greater than zero'),
        );
        expect(
          validator('1000001'),
          equals(r'Contribution amount cannot exceed $1,000,000'),
        );
      });
    });

    group('Date Validation', () {
      test('should validate required date field', () {
        // Act
        final config = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        );
        final dateField =
            config.fields.firstWhere((f) => f.key == 'contributionDate')
                as DatePickerFieldConfig;

        // Assert
        expect(dateField.isRequired, isTrue);
        expect(dateField.validator, isNotNull);

        // Test validation - cast to the correct function type
        final validator = dateField.validator;
        expect(validator!(null), equals('Contribution date is required'));
      });

      test('should validate date constraints', () {
        // Act
        final config = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        );
        final dateField =
            config.fields.firstWhere((f) => f.key == 'contributionDate')
                as DatePickerFieldConfig;

        // Assert
        expect(dateField.validator, isNotNull);

        // Test validation - cast to the correct function type
        final validator = dateField.validator;
        final futureDate = DateTime.now().add(const Duration(days: 1));
        expect(
          validator!(futureDate),
          equals('Contribution date cannot be in the future'),
        );

        final tenYearsAgo = DateTime.now().subtract(const Duration(days: 3651));
        expect(
          validator(tenYearsAgo),
          equals('Contribution date cannot be more than 10 years ago'),
        );

        final validDate = DateTime.now().subtract(const Duration(days: 30));
        expect(validator(validDate), isNull); // Valid
      });
    });

    group('Description Validation', () {
      test('should validate description length', () {
        // Act
        final config = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        );
        final descriptionField = config.fields.firstWhere(
          (f) => f.key == 'description',
        );

        // Assert
        expect(descriptionField.isRequired, isFalse);
        // Note: maxLength is not directly accessible from FormFieldConfig, but we can test it indirectly
      });
    });

    group('Form Submission Handling', () {
      test('should handle contribution creation', () async {
        // Arrange
        final formData = {
          'amount': '100.50',
          'contributionDate': DateTime(2024, 1, 15),
          'description': 'Test contribution',
        };

        // Mock repository behavior
        when(
          () => mockRepository.createContribution(any(), any()),
        ).thenAnswer((_) async => 'test-contribution-id');

        // Act
        final config = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        );
        await config.onSubmit(formData);

        // Assert
        verify(
          () => mockRepository.createContribution(
            'test-goal-id',
            any(that: isA<GoalContribution>()),
          ),
        ).called(1);
      });

      test('should handle contribution update', () async {
        // Arrange
        final originalContribution = GoalContribution.create(
          userId: 'test-user',
          goalId: 'test-goal',
          amountCents: 10000,
          contributionDate: DateTime(2024, 1, 15),
          description: 'Original description',
        );

        final formData = {
          'amount': '150.50',
          'contributionDate': DateTime(2024, 2, 15),
          'description': 'Updated description',
        };

        // Mock repository behavior
        when(
          () => mockRepository.updateContribution(any(), any(), any()),
        ).thenAnswer((_) async {});

        // Act
        final config = GoalContributionFormConfig.edit(
          contribution: originalContribution,
          repository: mockRepository,
        );
        await config.onSubmit(formData);

        // Assert
        verify(
          () => mockRepository.updateContribution(
            'test-goal',
            originalContribution.id,
            any(that: isA<GoalContribution>()),
          ),
        ).called(1);
      });

      test('should handle contribution deletion', () async {
        // Arrange
        final contribution = GoalContribution.create(
          userId: 'test-user',
          goalId: 'test-goal',
          amountCents: 10000,
          contributionDate: DateTime(2024, 1, 15),
          description: 'Test description',
        );

        // Mock repository behavior
        when(
          () => mockRepository.deleteContribution(any(), any()),
        ).thenAnswer((_) async {});

        // Act
        final config = GoalContributionFormConfig.edit(
          contribution: contribution,
          repository: mockRepository,
        );
        await config.onDelete!();

        // Assert
        verify(
          () => mockRepository.deleteContribution(
            contribution.goalId,
            contribution.id,
          ),
        ).called(1);
      });
    });

    group('Data Mapper Edge Cases', () {
      test('should handle data mapper with different goalId', () {
        // Test that data mapper uses the correct goalId
        final config = GoalContributionFormConfig.create(
          goalId: 'specific-goal-id',
          repository: mockRepository,
        );

        final formData = {
          'amount': '100.00',
          'contributionDate': DateTime(2024, 1, 15),
          'description': 'Test contribution',
        };

        final contribution = config.dataMapper!.formDataToEntity(formData);
        expect(contribution.goalId, equals('specific-goal-id'));
      });

      test('should handle data mapper with empty form data', () {
        final config = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        );

        final formData = <String, dynamic>{};

        final contribution = config.dataMapper!.formDataToEntity(formData);
        expect(contribution.amountCents, equals(0));
        expect(contribution.contributionDate, isA<DateTime>());
        expect(contribution.description, isNull);
      });

      test('should handle updateEntityWithFormData with missing date', () {
        final originalContribution = GoalContribution.create(
          userId: 'test-user',
          goalId: 'test-goal',
          amountCents: 10000,
          contributionDate: DateTime(2024, 1, 15),
          description: 'Original description',
        );

        final formData = {
          'amount': '150.50',
          // contributionDate missing
          'description': 'Updated description',
        };

        final config = GoalContributionFormConfig.edit(
          contribution: originalContribution,
          repository: mockRepository,
        );

        final updatedContribution = config.dataMapper!.updateEntityWithFormData(
          originalContribution,
          formData,
        );

        expect(updatedContribution.amountCents, equals(15050));
        expect(
          updatedContribution.contributionDate,
          equals(DateTime(2024, 1, 15)),
        ); // Preserved
        expect(updatedContribution.description, equals('Updated description'));
      });

      test('should handle updateEntityWithFormData with empty description', () {
        final originalContribution = GoalContribution.create(
          userId: 'test-user',
          goalId: 'test-goal',
          amountCents: 10000,
          contributionDate: DateTime(2024, 1, 15),
          description: 'Original description',
        );

        final formData = {
          'amount': '150.50',
          'contributionDate': DateTime(2024, 2, 15),
          'description': '', // Empty description
        };

        final config = GoalContributionFormConfig.edit(
          contribution: originalContribution,
          repository: mockRepository,
        );

        final updatedContribution = config.dataMapper!.updateEntityWithFormData(
          originalContribution,
          formData,
        );

        expect(
          updatedContribution.description,
          isNull,
        ); // Empty string becomes null
      });
    });

    group('Error Handling', () {
      test('should handle repository errors during creation', () async {
        final formData = {
          'amount': '100.50',
          'contributionDate': DateTime(2024, 1, 15),
          'description': 'Test contribution',
        };

        // Mock repository to throw error
        when(
          () => mockRepository.createContribution(any(), any()),
        ).thenThrow(Exception('Repository error'));

        final config = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        );

        // Should propagate the error
        expect(() => config.onSubmit(formData), throwsA(isA<Exception>()));
      });

      test('should handle repository errors during update', () async {
        final originalContribution = GoalContribution.create(
          userId: 'test-user',
          goalId: 'test-goal',
          amountCents: 10000,
          contributionDate: DateTime(2024, 1, 15),
          description: 'Original description',
        );

        final formData = {
          'amount': '150.50',
          'contributionDate': DateTime(2024, 2, 15),
          'description': 'Updated description',
        };

        // Mock repository to throw error
        when(
          () => mockRepository.updateContribution(any(), any(), any()),
        ).thenThrow(Exception('Update error'));

        final config = GoalContributionFormConfig.edit(
          contribution: originalContribution,
          repository: mockRepository,
        );

        // Should propagate the error
        expect(() => config.onSubmit(formData), throwsA(isA<Exception>()));
      });

      test('should handle repository errors during deletion', () async {
        final contribution = GoalContribution.create(
          userId: 'test-user',
          goalId: 'test-goal',
          amountCents: 10000,
          contributionDate: DateTime(2024, 1, 15),
          description: 'Test description',
        );

        // Mock repository to throw error
        when(
          () => mockRepository.deleteContribution(any(), any()),
        ).thenThrow(Exception('Delete error'));

        final config = GoalContributionFormConfig.edit(
          contribution: contribution,
          repository: mockRepository,
        );

        // Should propagate the error
        expect(() => config.onDelete!(), throwsA(isA<Exception>()));
      });
    });

    group('Field Configuration Details', () {
      test('should have correct date field constraints', () {
        final config = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        );
        final dateField =
            config.fields.firstWhere((f) => f.key == 'contributionDate')
                as DatePickerFieldConfig;

        // Test date constraints
        expect(dateField.firstDate, isA<DateTime>());
        expect(dateField.lastDate, isA<DateTime>());
        expect(dateField.firstDate!.isBefore(DateTime.now()), isTrue);
        expect(
          dateField.lastDate!.isAtSameMomentAs(DateTime.now()) ||
              dateField.lastDate!.isBefore(DateTime.now()),
          isTrue,
        );
      });

      test('should have correct amount field properties', () {
        final config = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        );
        final amountField =
            config.fields.firstWhere((f) => f.key == 'amount')
                as TextFieldConfig;

        expect(amountField.label, equals('Contribution Amount'));
        expect(amountField.hintText, equals('0.00'));
        expect(amountField.isRequired, isTrue);
        expect(amountField.validator, isNotNull);
      });

      test('should have correct description field properties', () {
        final config = GoalContributionFormConfig.create(
          goalId: 'test-goal-id',
          repository: mockRepository,
        );
        final descriptionField =
            config.fields.firstWhere((f) => f.key == 'description')
                as TextFieldConfig;

        expect(descriptionField.label, equals('Description'));
        expect(
          descriptionField.hintText,
          equals('Optional description for this contribution'),
        );
        expect(descriptionField.isRequired, isFalse);
        expect(descriptionField.maxLength, equals(500));
      });
    });

    group('Integration Tests', () {
      test('should create complete configuration with all components', () {
        final config = GoalContributionFormConfig.create(
          goalId: 'integration-test-goal',
          repository: mockRepository,
        );

        // Verify all components are properly configured
        expect(config.title, isNotNull);
        expect(config.fields, isNotEmpty);
        expect(config.onSubmit, isNotNull);
        expect(config.dataMapper, isNotNull);
        expect(config.initialData, isNull);
        expect(config.showDeleteButton, isFalse);
        expect(config.onDelete, isNull);
      });

      test('should create complete edit configuration with all components', () {
        final testContribution = GoalContribution.create(
          userId: 'test-user',
          goalId: 'test-goal',
          amountCents: 10000,
          contributionDate: DateTime(2024, 1, 15),
          description: 'Test contribution',
        );

        final config = GoalContributionFormConfig.edit(
          contribution: testContribution,
          repository: mockRepository,
        );

        // Verify all components are properly configured
        expect(config.title, isNotNull);
        expect(config.fields, isNotEmpty);
        expect(config.onSubmit, isNotNull);
        expect(config.dataMapper, isNotNull);
        expect(config.initialData, isNotNull);
        expect(config.showDeleteButton, isTrue);
        expect(config.onDelete, isNotNull);
      });
    });
  });
}
