import 'package:budapp/data/models/category.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/config/generic_form_config.dart';
import 'package:budapp/widgets/forms/configs/category_form_config.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/mock_data_factory.dart';
import '../../../helpers/mock_providers.dart';

class FakeUser extends Fake implements User {}

void main() {
  setUpAll(() {
    registerFallbackValue(FakeUser());
    registerFallbackValue(MockDataFactory.createAccount());
    registerFallbackValue(MockDataFactory.createTransaction());
    registerFallbackValue(MockDataFactory.createCategory());
  });
  group('CategoryFormConfig Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer(
        overrides: MockProviders.repositoryOverrides,
      );
      MockProviders.setupDefaultMocks();
    });

    tearDown(() {
      container.dispose();
      MockProviders.resetMocks();
    });

    group('Create Configuration', () {
      test('should create form config for new root category', () {
        final config = CategoryFormConfig.create(
          repository: MockProviders.mockCategoryRepository,
        );

        expect(config.title, 'Create Category');
        expect(config.showDeleteButton, false);
        expect(config.fields.length, 6);
        expect(config.initialData, isNull);
      });

      test('should create form config for new subcategory', () {
        final config = CategoryFormConfig.create(
          repository: MockProviders.mockCategoryRepository,
          parentId: 'parent-123',
        );

        expect(config.title, 'Create Subcategory');
        expect(config.showDeleteButton, false);
        expect(config.fields.length, 6);
        expect(config.initialData, isNull);
      });

      test('should have correct field order', () {
        final config = CategoryFormConfig.create(
          repository: MockProviders.mockCategoryRepository,
        );
        final fieldKeys = config.fields.map((f) => f.key).toList();

        expect(fieldKeys, [
          'name',
          'parentId',
          'type',
          'description',
          'color',
          'icon',
        ]);
      });
    });

    group('Edit Configuration', () {
      test('should create form config for existing root category', () {
        final category =
            Category.create(
              userId: 'user-id',
              name: 'Test Category',
              type: CategoryType.expense,
            ).copyWith(
              id: 'test-id',
              description: 'Test Description',
              color: '#FF5722',
              icon: 'category',
            );

        final config = CategoryFormConfig.edit(
          category: category,
          repository: MockProviders.mockCategoryRepository,
        );

        expect(config.title, 'Edit Category');
        expect(config.showDeleteButton, true);
        expect(config.fields.length, 6);
        expect(config.initialData, isNotNull);
      });

      test('should create form config for existing subcategory', () {
        final subcategory =
            Category.create(
              userId: 'user-id',
              name: 'Test Subcategory',
              type: CategoryType.expense,
              parentId: 'parent-id',
            ).copyWith(
              id: 'sub-id',
              description: 'Test Description',
              color: '#4CAF50',
              icon: 'subcategory',
            );

        final config = CategoryFormConfig.edit(
          category: subcategory,
          repository: MockProviders.mockCategoryRepository,
        );

        expect(config.title, 'Edit Subcategory');
        expect(config.showDeleteButton, true);
        expect(config.fields.length, 6);
        expect(config.initialData, isNotNull);
      });

      test('should populate initial data correctly for root category', () {
        final category =
            Category.create(
              userId: 'user-id',
              name: 'Food & Dining',
              type: CategoryType.expense,
            ).copyWith(
              id: 'test-id',
              description: 'Restaurant and grocery expenses',
              color: '#FF9800',
              icon: 'restaurant',
            );

        final config = CategoryFormConfig.edit(
          category: category,
          repository: MockProviders.mockCategoryRepository,
        );
        final mapper = config.dataMapper!;
        final initialData = mapper.entityToFormData(category);

        expect(initialData['name'], 'Food & Dining');
        expect(initialData['parentId'], null);
        expect(initialData['type'], CategoryType.expense);
        expect(initialData['description'], 'Restaurant and grocery expenses');
        expect(initialData['color'], '#FF9800');
        expect(initialData['icon'], 'restaurant');
      });

      test('should populate initial data correctly for subcategory', () {
        final subcategory =
            Category.create(
              userId: 'user-id',
              name: 'Restaurants',
              type: CategoryType.expense,
              parentId: 'food-parent',
            ).copyWith(
              id: 'sub-id',
              description: 'Dining out expenses',
              color: '#E91E63',
              icon: 'restaurant_menu',
            );

        final config = CategoryFormConfig.edit(
          category: subcategory,
          repository: MockProviders.mockCategoryRepository,
        );
        final mapper = config.dataMapper!;
        final initialData = mapper.entityToFormData(subcategory);

        expect(initialData['name'], 'Restaurants');
        expect(initialData['parentId'], 'food-parent');
        expect(initialData['type'], CategoryType.expense);
        expect(initialData['description'], 'Dining out expenses');
        expect(initialData['color'], '#E91E63');
        expect(initialData['icon'], 'restaurant_menu');
      });

      test('should handle null optional fields', () {
        final category = Category.create(
          userId: 'user-id',
          name: 'Basic Category',
          type: CategoryType.income,
        ).copyWith(id: 'test-id');

        final config = CategoryFormConfig.edit(
          category: category,
          repository: MockProviders.mockCategoryRepository,
        );
        final mapper = config.dataMapper!;
        final initialData = mapper.entityToFormData(category);

        expect(initialData['description'], '');
        expect(initialData['color'], isNull);
        expect(initialData['icon'], isNull);
      });
    });

    group('Field Configurations', () {
      late GenericFormConfig<Category> config;

      setUp(() {
        config = CategoryFormConfig.create(
          repository: MockProviders.mockCategoryRepository,
        );
      });

      test('should configure name field correctly', () {
        final nameField = config.fields.firstWhere((f) => f.key == 'name');

        expect(nameField.type, FormFieldType.text);
        expect(nameField.label, 'Category Name');
        expect(nameField.isRequired, true);
        // Skip validator check due to type casting complexity
      });

      test('should validate name field', () {
        final nameField =
            config.fields.firstWhere((f) => f.key == 'name')
                as FormFieldConfig<String>;

        expect(nameField.validator!(''), 'Category name is required');
        expect(
          nameField.validator!('a' * 101),
          'Category name must be 100 characters or less',
        );
        expect(nameField.validator!('Valid Name'), null);
      });

      test('should configure parentId field correctly', () {
        final parentField = config.fields.firstWhere(
          (f) => f.key == 'parentId',
        );

        expect(parentField.type, FormFieldType.custom);
        expect(parentField.label, 'Parent Category');
        expect(parentField.isRequired, false);
        // Skip validator check due to type casting complexity
      });

      test('should validate parentId field', () {
        final parentField =
            config.fields.firstWhere((f) => f.key == 'parentId')
                as FormFieldConfig<String?>;

        expect(parentField.validator!(null), null);
        expect(parentField.validator!(''), null);
        expect(parentField.validator!('parent-id'), null);
      });

      test('should configure type field correctly', () {
        final typeField = config.fields.firstWhere((f) => f.key == 'type');

        expect(typeField.type, FormFieldType.custom);
        expect(typeField.label, 'Category Type');
        expect(typeField.isRequired, true);
        // Skip validator check due to type casting complexity
      });

      test('should validate type field', () {
        final typeField =
            config.fields.firstWhere((f) => f.key == 'type')
                as FormFieldConfig<CategoryType>;

        expect(typeField.validator!(null), 'Please select a category type');
        expect(typeField.validator!(CategoryType.expense), null);
        expect(typeField.validator!(CategoryType.income), null);
      });

      test('should configure description field correctly', () {
        final descField = config.fields.firstWhere(
          (f) => f.key == 'description',
        );

        expect(descField.type, FormFieldType.multilineText);
        expect(descField.label, 'Description');
        expect(descField.isRequired, false);
        // Skip validator check due to type casting complexity
      });

      test('should validate description field', () {
        final descField =
            config.fields.firstWhere((f) => f.key == 'description')
                as FormFieldConfig<String>;

        expect(descField.validator!(''), null);
        expect(
          descField.validator!('a' * 501),
          'Description must be 500 characters or less',
        );
        expect(descField.validator!('Valid description'), null);
      });

      test('should configure color field correctly', () {
        final colorField = config.fields.firstWhere((f) => f.key == 'color');

        expect(colorField.type, FormFieldType.colorPicker);
        expect(colorField.label, 'Category Color');
        expect(colorField.isRequired, false);
      });

      test('should configure icon field correctly', () {
        final iconField = config.fields.firstWhere((f) => f.key == 'icon');

        expect(iconField.type, FormFieldType.iconPicker);
        expect(iconField.label, 'Category Icon');
        expect(iconField.isRequired, false);
      });
    });

    group('Data Mapping', () {
      group('entityToFormData', () {
        test('should map complete root category to form data', () {
          final category =
              Category.create(
                userId: 'user-id',
                name: 'Test Category',
                type: CategoryType.expense,
              ).copyWith(
                id: 'test-id',
                description: 'Test Description',
                color: '#4CAF50',
                icon: 'category',
              );

          final config = CategoryFormConfig.edit(
            category: category,
            repository: MockProviders.mockCategoryRepository,
          );
          final mapper = config.dataMapper!;
          final formData = mapper.entityToFormData(category);

          expect(formData['name'], 'Test Category');
          expect(formData['parentId'], null);
          expect(formData['type'], CategoryType.expense);
          expect(formData['description'], 'Test Description');
          expect(formData['color'], '#4CAF50');
          expect(formData['icon'], 'category');
        });

        test('should map complete subcategory to form data', () {
          final subcategory =
              Category.create(
                userId: 'user-id',
                name: 'Test Subcategory',
                type: CategoryType.income,
                parentId: 'parent-123',
              ).copyWith(
                id: 'sub-id',
                description: 'Sub Description',
                color: '#2196F3',
                icon: 'subcategory',
              );

          final config = CategoryFormConfig.edit(
            category: subcategory,
            repository: MockProviders.mockCategoryRepository,
          );
          final mapper = config.dataMapper!;
          final formData = mapper.entityToFormData(subcategory);

          expect(formData['name'], 'Test Subcategory');
          expect(formData['parentId'], 'parent-123');
          expect(formData['type'], CategoryType.income);
          expect(formData['description'], 'Sub Description');
          expect(formData['color'], '#2196F3');
          expect(formData['icon'], 'subcategory');
        });

        test('should handle null optional fields', () {
          final category = Category.create(
            userId: 'user-id',
            name: 'Basic Category',
            type: CategoryType.expense,
          ).copyWith(id: 'test-id');

          final config = CategoryFormConfig.edit(
            category: category,
            repository: MockProviders.mockCategoryRepository,
          );
          final mapper = config.dataMapper!;
          final formData = mapper.entityToFormData(category);

          expect(formData['name'], 'Basic Category');
          expect(formData['parentId'], null);
          expect(formData['type'], CategoryType.expense);
          expect(formData['description'], '');
          expect(formData['color'], isNull);
          expect(formData['icon'], isNull);
        });
      });

      group('formDataToEntity', () {
        test('should create root category from form data', () {
          final formData = {
            'name': 'New Category',
            'parentId': null,
            'type': CategoryType.expense,
            'description': 'Category description',
            'color': '#FF5722',
            'icon': 'category',
          };

          final config = CategoryFormConfig.create(
            repository: MockProviders.mockCategoryRepository,
          );
          final mapper = config.dataMapper!;
          final category = mapper.formDataToEntity(formData);

          expect(category.name, 'New Category');
          expect(category.parentId, null);
          expect(category.type, CategoryType.expense);
          expect(category.description, 'Category description');
          expect(category.color, '#FF5722');
          expect(category.icon, 'category');
          expect(category.isActive, true);
        });

        test('should create subcategory from form data', () {
          final formData = {
            'name': 'New Subcategory',
            'parentId': 'parent-456',
            'type': CategoryType.income,
            'description': 'Sub description',
            'color': '#4CAF50',
            'icon': 'subcategory',
          };

          final config = CategoryFormConfig.create(
            repository: MockProviders.mockCategoryRepository,
          );
          final mapper = config.dataMapper!;
          final category = mapper.formDataToEntity(formData);

          expect(category.name, 'New Subcategory');
          expect(category.parentId, 'parent-456');
          expect(category.type, CategoryType.income);
          expect(category.description, 'Sub description');
          expect(category.color, '#4CAF50');
          expect(category.icon, 'subcategory');
          expect(category.isActive, true);
        });

        test('should handle empty optional fields', () {
          final formData = {
            'name': 'Basic Category',
            'parentId': null,
            'type': CategoryType.expense,
            'description': '',
            'color': null,
            'icon': null,
          };

          final config = CategoryFormConfig.create(
            repository: MockProviders.mockCategoryRepository,
          );
          final mapper = config.dataMapper!;
          final category = mapper.formDataToEntity(formData);

          expect(category.name, 'Basic Category');
          expect(category.parentId, null);
          expect(category.type, CategoryType.expense);
          expect(category.description, null);
          expect(category.color, null);
          expect(category.icon, null);
        });

        test('should handle initialParentId parameter', () {
          final formData = {
            'name': 'Subcategory',
            'type': CategoryType.expense,
          };

          final config = CategoryFormConfig.create(
            repository: MockProviders.mockCategoryRepository,
            parentId: 'preset-parent',
          );
          final mapper = config.dataMapper!;
          final category = mapper.formDataToEntity(formData);

          expect(category.parentId, 'preset-parent');
        });
      });

      group('updateEntityWithFormData', () {
        test('should update root category with new form data', () {
          final existingCategory =
              Category.create(
                userId: 'user-id',
                name: 'Old Name',
                type: CategoryType.expense,
              ).copyWith(
                id: 'existing-id',
                description: 'Old Description',
                color: '#FF0000',
                icon: 'old_icon',
              );

          final formData = {
            'name': 'Updated Name',
            'parentId': null,
            'type': CategoryType.income,
            'description': 'Updated Description',
            'color': '#00FF00',
            'icon': 'new_icon',
          };

          final config = CategoryFormConfig.edit(
            category: existingCategory,
            repository: MockProviders.mockCategoryRepository,
          );
          final mapper = config.dataMapper!;
          final updatedCategory = mapper.updateEntityWithFormData(
            existingCategory,
            formData,
          );

          expect(updatedCategory.id, 'existing-id');
          expect(updatedCategory.userId, 'user-id');
          expect(updatedCategory.name, 'Updated Name');
          expect(updatedCategory.parentId, null);
          expect(updatedCategory.type, CategoryType.income);
          expect(updatedCategory.description, 'Updated Description');
          expect(updatedCategory.color, '#00FF00');
          expect(updatedCategory.icon, 'new_icon');
          expect(updatedCategory.isActive, true);
        });

        test('should update subcategory to root category', () {
          final existingSubcategory = Category.create(
            userId: 'user-id',
            name: 'Subcategory',
            type: CategoryType.expense,
            parentId: 'old-parent',
          ).copyWith(id: 'sub-id');

          final formData = {
            'name': 'Now Root Category',
            'parentId': null,
            'type': CategoryType.income,
          };

          final config = CategoryFormConfig.edit(
            category: existingSubcategory,
            repository: MockProviders.mockCategoryRepository,
          );
          final mapper = config.dataMapper!;
          final updatedCategory = mapper.updateEntityWithFormData(
            existingSubcategory,
            formData,
          );

          expect(updatedCategory.name, 'Now Root Category');
          expect(updatedCategory.parentId, null);
          expect(updatedCategory.type, CategoryType.income);
        });

        test('should update root category to subcategory', () {
          final existingCategory = Category.create(
            userId: 'user-id',
            name: 'Root Category',
            type: CategoryType.expense,
          ).copyWith(id: 'root-id');

          final formData = {
            'name': 'Now Subcategory',
            'parentId': 'new-parent',
            'type': CategoryType.expense,
          };

          final config = CategoryFormConfig.edit(
            category: existingCategory,
            repository: MockProviders.mockCategoryRepository,
          );
          final mapper = config.dataMapper!;
          final updatedCategory = mapper.updateEntityWithFormData(
            existingCategory,
            formData,
          );

          expect(updatedCategory.name, 'Now Subcategory');
          expect(updatedCategory.parentId, 'new-parent');
          expect(updatedCategory.type, CategoryType.expense);
        });

        test('should change subcategory parent', () {
          final existingSubcategory = Category.create(
            userId: 'user-id',
            name: 'Subcategory',
            type: CategoryType.expense,
            parentId: 'old-parent',
          ).copyWith(id: 'sub-id');

          final formData = {
            'name': 'Subcategory',
            'parentId': 'new-parent',
            'type': CategoryType.expense,
          };

          final config = CategoryFormConfig.edit(
            category: existingSubcategory,
            repository: MockProviders.mockCategoryRepository,
          );
          final mapper = config.dataMapper!;
          final updatedCategory = mapper.updateEntityWithFormData(
            existingSubcategory,
            formData,
          );

          expect(updatedCategory.name, 'Subcategory');
          expect(updatedCategory.parentId, 'new-parent');
          expect(updatedCategory.type, CategoryType.expense);
        });
      });
    });

    group('CRUD Operations', () {
      test('should handle create operation for root category', () async {
        final config = CategoryFormConfig.create(
          repository: MockProviders.mockCategoryRepository,
        );
        final formData = {
          'name': 'New Category',
          'parentId': null,
          'type': CategoryType.expense,
          'description': 'Test category',
        };

        await config.onSubmit(formData);
        // Verify the category was created through the repository
        // This test passes if no exception is thrown
      });

      test('should handle create operation for subcategory', () async {
        final config = CategoryFormConfig.create(
          repository: MockProviders.mockCategoryRepository,
          parentId: 'parent-123',
        );
        final formData = {
          'name': 'New Subcategory',
          'type': CategoryType.expense,
          'description': 'Test subcategory',
        };

        await config.onSubmit(formData);
        // Verify the subcategory was created through the repository
        // This test passes if no exception is thrown
      });

      test('should handle update operation for root category', () async {
        final existingCategory = Category.create(
          userId: 'user-id',
          name: 'Existing Category',
          type: CategoryType.expense,
        ).copyWith(id: 'existing-id');

        final config = CategoryFormConfig.edit(
          category: existingCategory,
          repository: MockProviders.mockCategoryRepository,
        );
        final formData = {
          'name': 'Updated Category',
          'parentId': null,
          'type': CategoryType.income,
        };

        await config.onSubmit(formData);
        // Verify the category was updated through the repository
        // This test passes if no exception is thrown
      });

      test('should handle update operation for subcategory', () async {
        final existingSubcategory = Category.create(
          userId: 'user-id',
          name: 'Existing Subcategory',
          type: CategoryType.expense,
          parentId: 'parent-id',
        ).copyWith(id: 'sub-id');

        final config = CategoryFormConfig.edit(
          category: existingSubcategory,
          repository: MockProviders.mockCategoryRepository,
        );
        final formData = {
          'name': 'Updated Subcategory',
          'parentId': 'new-parent',
          'type': CategoryType.income,
        };

        await config.onSubmit(formData);
        // Verify the subcategory was updated through the repository
        // This test passes if no exception is thrown
      });

      test('should handle delete operation for root category', () async {
        final existingCategory = Category.create(
          userId: 'user-id',
          name: 'Category to Delete',
          type: CategoryType.expense,
        ).copyWith(id: 'existing-id');

        final config = CategoryFormConfig.edit(
          category: existingCategory,
          repository: MockProviders.mockCategoryRepository,
        );

        await config.onDelete!();
        // Verify the category was deactivated through the repository
        // This test passes if no exception is thrown
      });

      test('should handle delete operation for subcategory', () async {
        final existingSubcategory = Category.create(
          userId: 'user-id',
          name: 'Subcategory to Delete',
          type: CategoryType.expense,
          parentId: 'parent-id',
        ).copyWith(id: 'sub-id');

        final config = CategoryFormConfig.edit(
          category: existingSubcategory,
          repository: MockProviders.mockCategoryRepository,
        );

        await config.onDelete!();
        // Verify the subcategory was deleted through the repository
        // This test passes if no exception is thrown
      });
    });

    group('Validation Logic', () {
      test('should normalize optional strings correctly', () {
        final testCases = [
          {'input': '', 'expected': null},
          {'input': '   ', 'expected': '   '}, // Not trimmed by mapper
          {'input': 'valid string', 'expected': 'valid string'},
          {
            'input': '  trimmed  ',
            'expected': '  trimmed  ',
          }, // Not trimmed by mapper
        ];

        for (final testCase in testCases) {
          final formData = {
            'name': 'Test',
            'description': testCase['input'],
            'type': CategoryType.expense,
          };

          final config = CategoryFormConfig.create(
            repository: MockProviders.mockCategoryRepository,
          );
          final mapper = config.dataMapper!;
          final category = mapper.formDataToEntity(formData);

          final expected = testCase['input']!.isEmpty
              ? null
              : testCase['expected'];
          expect(category.description, expected);
        }
      });

      test('should handle CategoryType validation', () {
        final validTypes = [CategoryType.expense, CategoryType.income];

        for (final type in validTypes) {
          final formData = {'name': 'Test Category', 'type': type};

          final config = CategoryFormConfig.create(
            repository: MockProviders.mockCategoryRepository,
          );
          final mapper = config.dataMapper!;
          final category = mapper.formDataToEntity(formData);
          expect(category.type, type);
        }
      });
    });

    group('Error Handling', () {
      test('should handle missing required fields', () {
        expect(() {
          final formData = <String, dynamic>{'description': 'Test Description'};

          final config = CategoryFormConfig.create(
            repository: MockProviders.mockCategoryRepository,
          );
          final mapper = config.dataMapper!;
          mapper.formDataToEntity(formData);
        }, throwsA(isA<TypeError>()));
      });

      test('should handle invalid category type', () {
        expect(() {
          final formData = {'name': 'Test Category', 'type': 'invalid_type'};

          final config = CategoryFormConfig.create(
            repository: MockProviders.mockCategoryRepository,
          );
          final mapper = config.dataMapper!;
          mapper.formDataToEntity(formData);
        }, throwsA(isA<TypeError>()));
      });

      test('should handle malformed form data', () {
        expect(() {
          final formData = {
            'name': 123, // Invalid type for name
            'type': CategoryType.expense,
          };

          final config = CategoryFormConfig.create(
            repository: MockProviders.mockCategoryRepository,
          );
          final mapper = config.dataMapper!;
          mapper.formDataToEntity(formData);
        }, throwsA(isA<TypeError>()));
      });
    });

    group('Repository Integration', () {
      test(
        'should call appropriate repository method for root category creation',
        () async {
          final config = CategoryFormConfig.create(
            repository: MockProviders.mockCategoryRepository,
          );
          final formData = {
            'name': 'Repository Test Category',
            'parentId': null,
            'type': CategoryType.expense,
          };

          // This indirectly tests that createCategory is called for root categories
          await config.onSubmit(formData);
          // Test passes if no exception is thrown
        },
      );

      test(
        'should call appropriate repository method for subcategory creation',
        () async {
          final config = CategoryFormConfig.create(
            repository: MockProviders.mockCategoryRepository,
            parentId: 'parent-123',
          );
          final formData = {
            'name': 'Repository Test Subcategory',
            'type': CategoryType.expense,
          };

          // This indirectly tests that createSubcategory is called for subcategories
          await config.onSubmit(formData);
          // Test passes if no exception is thrown
        },
      );

      test('should handle different update scenarios', () async {
        // Test subcategory to subcategory update
        final subcategory = Category.create(
          userId: 'user-id',
          name: 'Subcategory',
          type: CategoryType.expense,
          parentId: 'parent-1',
        ).copyWith(id: 'sub-id');

        final config = CategoryFormConfig.edit(
          category: subcategory,
          repository: MockProviders.mockCategoryRepository,
        );
        final formData = {
          'name': 'Updated Subcategory',
          'parentId': 'parent-2',
          'type': CategoryType.expense,
        };

        await config.onSubmit(formData);
        // Test passes if appropriate repository method is called
      });

      test('should handle repository failures gracefully', () async {
        final config = CategoryFormConfig.create(
          repository: MockProviders.mockCategoryRepository,
        );
        final formData = {
          'name': 'Test Category',
          'type': CategoryType.expense,
        };

        await config.onSubmit(formData);
        // Test passes if handled gracefully
      });
    });

    group('Form Configuration Variations', () {
      test('should show different titles for create vs edit', () {
        final createConfig = CategoryFormConfig.create(
          repository: MockProviders.mockCategoryRepository,
        );
        expect(createConfig.title, 'Create Category');

        final createSubConfig = CategoryFormConfig.create(
          repository: MockProviders.mockCategoryRepository,
          parentId: 'parent',
        );
        expect(createSubConfig.title, 'Create Subcategory');

        final rootCategory = Category.create(
          userId: 'user-id',
          name: 'Root',
          type: CategoryType.expense,
        ).copyWith(id: 'root-id');
        final editRootConfig = CategoryFormConfig.edit(
          category: rootCategory,
          repository: MockProviders.mockCategoryRepository,
        );
        expect(editRootConfig.title, 'Edit Category');

        final subcategory = Category.create(
          userId: 'user-id',
          name: 'Sub',
          type: CategoryType.expense,
          parentId: 'parent-id',
        ).copyWith(id: 'sub-id');
        final editSubConfig = CategoryFormConfig.edit(
          category: subcategory,
          repository: MockProviders.mockCategoryRepository,
        );
        expect(editSubConfig.title, 'Edit Subcategory');
      });

      test('should have identical field configurations regardless of type', () {
        final createConfig = CategoryFormConfig.create(
          repository: MockProviders.mockCategoryRepository,
        );
        final createSubConfig = CategoryFormConfig.create(
          repository: MockProviders.mockCategoryRepository,
          parentId: 'parent',
        );

        expect(createConfig.fields.length, createSubConfig.fields.length);

        for (var i = 0; i < createConfig.fields.length; i++) {
          final field1 = createConfig.fields[i];
          final field2 = createSubConfig.fields[i];

          expect(field1.key, field2.key);
          expect(field1.type, field2.type);
          expect(field1.label, field2.label);
          expect(field1.isRequired, field2.isRequired);
        }
      });

      test('should show delete button in edit configurations', () {
        final category = Category.create(
          userId: 'user-id',
          name: 'Test',
          type: CategoryType.expense,
        ).copyWith(id: 'test-id');

        final editConfig = CategoryFormConfig.edit(
          category: category,
          repository: MockProviders.mockCategoryRepository,
        );

        expect(editConfig.showDeleteButton, true);
        expect(editConfig.onDelete, isNotNull);
      });
    });
  });
}
