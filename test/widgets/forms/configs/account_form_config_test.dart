import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/config/generic_form_config.dart';
import 'package:budapp/widgets/forms/configs/account_form_config.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/mock_providers.dart';

class FakeUser extends Fake implements User {}

// Factory functions for creating test instances
Account createTestAccount({
  String id = 'test-account-id',
  String userId = 'test-user-id',
  String name = 'Test Account',
}) {
  return Account(
    id: id,
    userId: userId,
    name: name,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
}

Transaction createTestTransaction({
  String id = 'test-transaction-id',
  String userId = 'test-user-id',
  int amountCents = 1000,
}) {
  return Transaction(
    id: id,
    userId: userId,
    amountCents: amountCents,
    transactionDate: DateTime.now(),
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
}

Category createTestCategory({
  String id = 'test-category-id',
  String userId = 'test-user-id',
  String name = 'Test Category',
}) {
  return Category(
    id: id,
    userId: userId,
    name: name,
    type: CategoryType.expense,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
}

void main() {
  setUpAll(() {
    registerFallbackValue(FakeUser());
    registerFallbackValue(createTestAccount());
    registerFallbackValue(createTestTransaction());
    registerFallbackValue(createTestCategory());
  });
  group('AccountFormConfig Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer(
        overrides: MockProviders.repositoryOverrides,
      );
      MockProviders.setupDefaultMocks();
    });

    tearDown(() {
      container.dispose();
      MockProviders.resetMocks();
    });

    group('Create Configuration', () {
      test('should create form config for new account', () {
        final config = AccountFormConfig.create(
          repository: MockProviders.mockAccountRepository,
        );

        expect(config.title, 'Create Account');
        expect(config.showDeleteButton, false);
        expect(config.fields.length, 6);
        expect(config.initialData, isNull);
      });

      test('should have correct field order', () {
        final config = AccountFormConfig.create(
          repository: MockProviders.mockAccountRepository,
        );
        final fieldKeys = config.fields.map((f) => f.key).toList();

        expect(fieldKeys, [
          'name',
          'description',
          'accountType',
          'initialBalance',
          'color',
          'icon',
        ]);
      });
    });

    group('Edit Configuration', () {
      test('should create form config for existing account', () {
        final account = Account(
          id: 'test-id',
          userId: 'user-id',
          name: 'Test Account',
          description: 'Test Description',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 10000,
          currentBalanceCents: 10000,
          colorHex: '#FF5722',
          iconName: 'account_balance',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final config = AccountFormConfig.edit(
          account: account,
          repository: MockProviders.mockAccountRepository,
        );

        expect(config.title, 'Edit Account');
        expect(config.showDeleteButton, true);
        expect(config.fields.length, 6);
        expect(config.initialData, isNotNull);
      });

      test('should populate initial data correctly', () {
        final account = Account(
          id: 'test-id',
          userId: 'user-id',
          name: 'Savings Account',
          description: 'Emergency fund',
          type: AccountType.savings,
          classification: AccountClassification.asset,
          initialBalanceCents: 50000,
          currentBalanceCents: 50000,
          colorHex: '#4CAF50',
          iconName: 'savings',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final config = AccountFormConfig.edit(
          account: account,
          repository: MockProviders.mockAccountRepository,
        );
        final mapper = config.dataMapper!;
        final initialData = mapper.entityToFormData(account);

        expect(initialData['name'], 'Savings Account');
        expect(initialData['description'], 'Emergency fund');
        expect(initialData['accountType'], {
          'type': AccountType.savings,
          'classification': AccountClassification.asset,
        });
        expect(initialData['initialBalance'], '500.00');
        expect(initialData['color'], '#4CAF50');
        expect(initialData['icon'], 'savings');
      });

      test('should handle null optional fields', () {
        final account = Account(
          id: 'test-id',
          userId: 'user-id',
          name: 'Basic Account',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 0,
          currentBalanceCents: 0,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final config = AccountFormConfig.edit(
          account: account,
          repository: MockProviders.mockAccountRepository,
        );
        final mapper = config.dataMapper!;
        final initialData = mapper.entityToFormData(account);

        expect(initialData['description'], '');
        expect(initialData['color'], isNull);
        expect(initialData['icon'], isNull);
      });
    });

    group('Field Configurations', () {
      late GenericFormConfig<Account> config;

      setUp(() {
        config = AccountFormConfig.create(
          repository: MockProviders.mockAccountRepository,
        );
      });

      test('should configure name field correctly', () {
        final nameField = config.fields.firstWhere((f) => f.key == 'name');

        expect(nameField.type, FormFieldType.text);
        expect(nameField.label, 'Account Name');
        expect(nameField.isRequired, true);
        // Skip validator check due to type casting complexity
      });

      test('should validate name field', () {
        final nameField =
            config.fields.firstWhere((f) => f.key == 'name')
                as FormFieldConfig<String>;

        expect(nameField.validator!(''), 'Account name is required');
        expect(
          nameField.validator!('a' * 101),
          'Account name must be 100 characters or less',
        );
        expect(nameField.validator!('Valid Name'), null);
      });

      test('should configure description field correctly', () {
        final descField = config.fields.firstWhere(
          (f) => f.key == 'description',
        );

        expect(descField.type, FormFieldType.multilineText);
        expect(descField.label, 'Description');
        expect(descField.isRequired, false);
        // Skip validator check due to type casting complexity
      });

      test('should validate description field', () {
        final descField =
            config.fields.firstWhere((f) => f.key == 'description')
                as FormFieldConfig<String>;

        expect(descField.validator!(''), null);
        expect(
          descField.validator!('a' * 501),
          'Description must be 500 characters or less',
        );
        expect(descField.validator!('Valid description'), null);
      });

      test('should configure accountType field correctly', () {
        final typeField = config.fields.firstWhere(
          (f) => f.key == 'accountType',
        );

        expect(typeField.type, FormFieldType.custom);
        expect(typeField.label, 'Account Type');
        expect(typeField.isRequired, true);
        // Skip validator check due to type casting complexity
      });

      test('should validate accountType field', () {
        final typeField =
            config.fields.firstWhere((f) => f.key == 'accountType')
                as FormFieldConfig<Map<String, dynamic>>;

        expect(typeField.validator!(null), 'Please select an account type');
        expect(
          typeField.validator!(<String, dynamic>{}),
          'Please select an account type',
        );
        expect(
          typeField.validator!({
            'type': AccountType.checking,
            'classification': AccountClassification.asset,
          }),
          null,
        );
      });

      test('should configure initialBalance field correctly', () {
        final balanceField = config.fields.firstWhere(
          (f) => f.key == 'initialBalance',
        );

        expect(balanceField.type, FormFieldType.text);
        expect(balanceField.label, 'Initial Balance');
        expect(balanceField.isRequired, true);
        // Skip validator check due to type casting complexity
      });

      test('should validate initialBalance field', () {
        final balanceField =
            config.fields.firstWhere((f) => f.key == 'initialBalance')
                as FormFieldConfig<String>;

        expect(balanceField.validator!(''), 'Initial balance is required');
        expect(
          balanceField.validator!('invalid'),
          'Please enter a valid balance amount',
        );
        expect(balanceField.validator!('100.00'), null);
        expect(balanceField.validator!('0'), null);
        expect(balanceField.validator!('-50.25'), null);
      });

      test('should configure color field correctly', () {
        final colorField = config.fields.firstWhere((f) => f.key == 'color');

        expect(colorField.type, FormFieldType.colorPicker);
        expect(colorField.label, 'Account Color');
        expect(colorField.isRequired, false);
      });

      test('should configure icon field correctly', () {
        final iconField = config.fields.firstWhere((f) => f.key == 'icon');

        expect(iconField.type, FormFieldType.iconPicker);
        expect(iconField.label, 'Account Icon');
        expect(iconField.isRequired, false);
      });
    });

    group('Data Mapping', () {
      group('entityToFormData', () {
        test('should map complete account to form data', () {
          final account = Account(
            id: 'test-id',
            userId: 'user-id',
            name: 'Test Account',
            description: 'Test Description',
            type: AccountType.savings,
            classification: AccountClassification.asset,
            initialBalanceCents: 25050,
            currentBalanceCents: 25050,
            colorHex: '#4CAF50',
            iconName: 'savings',
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          final config = AccountFormConfig.edit(
            account: account,
            repository: MockProviders.mockAccountRepository,
          );
          final mapper = config.dataMapper!;
          final formData = mapper.entityToFormData(account);

          expect(formData['name'], 'Test Account');
          expect(formData['description'], 'Test Description');
          expect(formData['accountType'], {
            'type': AccountType.savings,
            'classification': AccountClassification.asset,
          });
          expect(formData['initialBalance'], '250.50');
          expect(formData['color'], '#4CAF50');
          expect(formData['icon'], 'savings');
        });

        test('should handle null optional fields', () {
          final account = Account(
            id: 'test-id',
            userId: 'user-id',
            name: 'Basic Account',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 0,
            currentBalanceCents: 0,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          final config = AccountFormConfig.edit(
            account: account,
            repository: MockProviders.mockAccountRepository,
          );
          final mapper = config.dataMapper!;
          final formData = mapper.entityToFormData(account);

          expect(formData['name'], 'Basic Account');
          expect(formData['description'], '');
          expect(formData['initialBalance'], '0.00');
          expect(formData['color'], isNull);
          expect(formData['icon'], isNull);
        });
      });

      group('formDataToEntity', () {
        test('should create account from complete form data', () {
          final formData = {
            'name': 'New Account',
            'description': 'Account description',
            'accountType': {
              'type': AccountType.checking,
              'classification': AccountClassification.asset,
            },
            'initialBalance': '1000.00',
            'color': '#FF5722',
            'icon': 'account_balance',
          };

          final config = AccountFormConfig.create(
            repository: MockProviders.mockAccountRepository,
          );
          final mapper = config.dataMapper!;
          final account = mapper.formDataToEntity(formData);

          expect(account.name, 'New Account');
          expect(account.description, 'Account description');
          expect(account.type, AccountType.checking);
          expect(account.classification, AccountClassification.asset);
          expect(account.initialBalanceCents, 100000);
          expect(account.colorHex, '#FF5722');
          expect(account.iconName, 'account_balance');
          expect(account.isActive, true);
        });

        test('should handle empty optional fields', () {
          final formData = {
            'name': 'Basic Account',
            'accountType': {
              'type': AccountType.savings,
              'classification': AccountClassification.asset,
            },
            'initialBalance': '0',
            'description': '',
            'color': '',
            'icon': '',
          };

          final config = AccountFormConfig.create(
            repository: MockProviders.mockAccountRepository,
          );
          final mapper = config.dataMapper!;
          final account = mapper.formDataToEntity(formData);

          expect(account.name, 'Basic Account');
          expect(account.description, null);
          expect(account.colorHex, '');
          expect(account.iconName, '');
          expect(account.initialBalanceCents, 0);
        });

        test('should handle negative balance', () {
          final formData = {
            'name': 'Credit Account',
            'accountType': {
              'type': AccountType.creditCard,
              'classification': AccountClassification.liability,
            },
            'initialBalance': '-500.75',
          };

          final config = AccountFormConfig.create(
            repository: MockProviders.mockAccountRepository,
          );
          final mapper = config.dataMapper!;
          final account = mapper.formDataToEntity(formData);

          expect(account.initialBalanceCents, -50075);
        });
      });

      group('updateEntityWithFormData', () {
        test('should update account with new form data', () {
          final existingAccount = Account(
            id: 'existing-id',
            userId: 'user-id',
            name: 'Old Name',
            description: 'Old Description',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 10000,
            currentBalanceCents: 10000,
            colorHex: '#FF0000',
            iconName: 'old_icon',
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          final formData = {
            'name': 'Updated Name',
            'description': 'Updated Description',
            'accountType': {
              'type': AccountType.savings,
              'classification': AccountClassification.asset,
            },
            'initialBalance': '2000.00',
            'color': '#00FF00',
            'icon': 'new_icon',
          };

          final config = AccountFormConfig.edit(
            account: existingAccount,
            repository: MockProviders.mockAccountRepository,
          );
          final mapper = config.dataMapper!;
          final updatedAccount = mapper.updateEntityWithFormData(
            existingAccount,
            formData,
          );

          expect(updatedAccount.id, 'existing-id');
          expect(updatedAccount.userId, 'user-id');
          expect(updatedAccount.name, 'Updated Name');
          expect(updatedAccount.description, 'Updated Description');
          expect(updatedAccount.type, AccountType.savings);
          expect(updatedAccount.initialBalanceCents, 200000);
          expect(updatedAccount.colorHex, '#00FF00');
          expect(updatedAccount.iconName, 'new_icon');
          expect(updatedAccount.isActive, true);
        });

        test('should handle invalid balance gracefully', () {
          final existingAccount = Account(
            id: 'existing-id',
            userId: 'user-id',
            name: 'Test Account',
            type: AccountType.checking,
            classification: AccountClassification.asset,
            initialBalanceCents: 10000,
            currentBalanceCents: 10000,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          final formData = {
            'name': 'Updated Name',
            'accountType': {
              'type': AccountType.savings,
              'classification': AccountClassification.asset,
            },
            'initialBalance': 'invalid_balance',
          };

          final config = AccountFormConfig.edit(
            account: existingAccount,
            repository: MockProviders.mockAccountRepository,
          );
          final mapper = config.dataMapper!;
          final updatedAccount = mapper.updateEntityWithFormData(
            existingAccount,
            formData,
          );

          expect(updatedAccount.name, 'Updated Name');
          expect(updatedAccount.type, AccountType.savings);
          expect(
            updatedAccount.initialBalanceCents,
            10000,
          ); // Original balance preserved
        });
      });
    });

    group('CRUD Operations', () {
      test('should handle create operation successfully', () async {
        final config = AccountFormConfig.create(
          repository: MockProviders.mockAccountRepository,
        );
        final formData = {
          'name': 'New Account',
          'accountType': {
            'type': AccountType.checking,
            'classification': AccountClassification.asset,
          },
          'initialBalance': '1000.00',
        };

        await config.onSubmit(formData);
        // Verify the account was created through the repository
        // This test passes if no exception is thrown
      });

      test('should handle update operation successfully', () async {
        final existingAccount = Account(
          id: 'existing-id',
          userId: 'user-id',
          name: 'Existing Account',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 10000,
          currentBalanceCents: 10000,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final config = AccountFormConfig.edit(
          account: existingAccount,
          repository: MockProviders.mockAccountRepository,
        );
        final formData = {
          'name': 'Updated Account',
          'accountType': {
            'type': AccountType.savings,
            'classification': AccountClassification.asset,
          },
          'initialBalance': '2000.00',
        };

        await config.onSubmit(formData);
        // Verify the account was updated through the repository
        // This test passes if no exception is thrown
      });

      test('should handle delete operation successfully', () async {
        final existingAccount = Account(
          id: 'existing-id',
          userId: 'user-id',
          name: 'Account to Delete',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 10000,
          currentBalanceCents: 10000,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final config = AccountFormConfig.edit(
          account: existingAccount,
          repository: MockProviders.mockAccountRepository,
        );

        await config.onDelete!();
        // Verify the account was deactivated through the repository
        // This test passes if no exception is thrown
      });
    });

    group('Error Handling', () {
      test('should handle invalid balance parsing', () {
        expect(() {
          final formData = {
            'name': 'Test Account',
            'accountType': {
              'type': AccountType.checking,
              'classification': AccountClassification.asset,
            },
            'initialBalance': 'not_a_number',
          };

          final config = AccountFormConfig.create(
            repository: MockProviders.mockAccountRepository,
          );
          final mapper = config.dataMapper!;
          mapper.formDataToEntity(formData);
        }, throwsA(isA<Exception>()));
      });

      test('should handle missing required fields', () {
        expect(() {
          final formData = <String, dynamic>{'description': 'Test Description'};

          final config = AccountFormConfig.create(
            repository: MockProviders.mockAccountRepository,
          );
          final mapper = config.dataMapper!;
          mapper.formDataToEntity(formData);
        }, throwsA(isA<TypeError>()));
      });
    });

    group('Repository Integration', () {
      test('should call repository methods with correct parameters', () async {
        final config = AccountFormConfig.create(
          repository: MockProviders.mockAccountRepository,
        );
        final formData = {
          'name': 'Repository Test Account',
          'description': 'Testing repository integration',
          'accountType': {
            'type': AccountType.savings,
            'classification': AccountClassification.asset,
          },
          'initialBalance': '5000.00',
          'color': '#4CAF50',
          'icon': 'savings',
        };

        // This indirectly tests that the repository is called correctly
        await config.onSubmit(formData);
        // Test passes if no exception is thrown and repository mock handles the call
      });
    });
  });
}
