import 'package:budapp/data/models/goal.dart';
import 'package:budapp/data/repositories/interfaces/goal_repository.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/config/generic_form_config.dart';
import 'package:budapp/widgets/forms/configs/goal_form_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/mock_data_factory.dart';

// Mock classes
class MockGoalRepository extends Mock implements IGoalRepository {}

void main() {
  setUpAll(() {
    // Use MockDataFactory to create fallback values instead of Fake classes
    registerFallbackValue(MockDataFactory.createGoal());
  });
  group('GoalFormConfig Tests', () {
    late MockGoalRepository mockRepository;
    late Goal testGoal;

    setUp(() {
      mockRepository = MockGoalRepository();
      testGoal = Goal.create(
        userId: 'test-user-123',
        name: 'Test Goal',
        description: 'Test Description',
        targetAmountCents: 100000, // $1000.00
        targetDate: DateTime(2025, 12, 31),
        status: GoalStatus.active,
        colorHex: '#2196F3',
        iconName: 'account_balance',
      );
    });

    group('GoalFormConfig.create() Tests', () {
      test('should create GenericFormConfig for goal creation', () {
        final config = GoalFormConfig.create(repository: mockRepository);

        expect(config, isA<GenericFormConfig<Goal>>());
        expect(config.title, equals('Create Goal'));
        expect(config.isCreateMode, isTrue);
        expect(config.isEditMode, isFalse);
        expect(config.initialData, isNull);
        expect(config.showDeleteButton, isFalse);
        expect(config.onDelete, isNull);
        expect(config.dataMapper, isNotNull);
        expect(config.fields, isNotEmpty);
        expect(
          config.fields.length,
          equals(7),
        ); // name, description, targetAmount, targetDate, status, color, icon
      });

      test('should create config with onFieldChanged callback', () {
        var fieldChangedCalled = false;
        String? changedFieldKey;
        dynamic changedValue;

        final config = GoalFormConfig.create(
          repository: mockRepository,
          onFieldChanged: (fieldKey, value) {
            fieldChangedCalled = true;
            changedFieldKey = fieldKey;
            changedValue = value;
          },
        );

        expect(config.onFieldChanged, isNotNull);

        // Test the callback
        config.onFieldChanged!('test_field', 'test_value');
        expect(fieldChangedCalled, isTrue);
        expect(changedFieldKey, equals('test_field'));
        expect(changedValue, equals('test_value'));
      });

      test('should have correct onSubmit callback for creation', () async {
        when(
          () => mockRepository.createGoal(any()),
        ).thenAnswer((_) async => 'goal-id');

        final config = GoalFormConfig.create(repository: mockRepository);

        final formData = {
          'name': 'New Goal',
          'description': 'New Description',
          'targetAmount': '500.00',
          'targetDate': DateTime(2025, 6, 15),
          'status': GoalStatus.active,
          'color': '#4CAF50',
          'icon': 'savings',
        };

        // Test onSubmit callback
        await config.onSubmit(formData);

        verify(() => mockRepository.createGoal(any())).called(1);
      });
    });

    group('GoalFormConfig.edit() Tests', () {
      test('should create GenericFormConfig for goal editing', () {
        final config = GoalFormConfig.edit(
          goal: testGoal,
          repository: mockRepository,
        );

        expect(config, isA<GenericFormConfig<Goal>>());
        expect(config.title, equals('Edit Goal'));
        expect(config.isCreateMode, isFalse);
        expect(config.isEditMode, isTrue);
        expect(config.initialData, equals(testGoal));
        expect(config.showDeleteButton, isTrue);
        expect(config.onDelete, isNotNull);
        expect(config.dataMapper, isNotNull);
        expect(config.fields, isNotEmpty);
        expect(config.fields.length, equals(7));
      });

      test('should create config with onFieldChanged callback for editing', () {
        var fieldChangedCalled = false;
        String? changedFieldKey;
        dynamic changedValue;

        final config = GoalFormConfig.edit(
          goal: testGoal,
          repository: mockRepository,
          onFieldChanged: (fieldKey, value) {
            fieldChangedCalled = true;
            changedFieldKey = fieldKey;
            changedValue = value;
          },
        );

        expect(config.onFieldChanged, isNotNull);

        // Test the callback
        config.onFieldChanged!('name', 'Updated Goal Name');
        expect(fieldChangedCalled, isTrue);
        expect(changedFieldKey, equals('name'));
        expect(changedValue, equals('Updated Goal Name'));
      });

      test('should have correct onSubmit callback for editing', () async {
        when(
          () => mockRepository.updateGoal(any(), any()),
        ).thenAnswer((_) async {});

        final config = GoalFormConfig.edit(
          goal: testGoal,
          repository: mockRepository,
        );

        final formData = {
          'name': 'Updated Goal',
          'description': 'Updated Description',
          'targetAmount': '750.00',
          'targetDate': DateTime(2025, 8, 20),
          'status': GoalStatus.paused,
          'color': '#FF9800',
          'icon': 'star',
        };

        // Test onSubmit callback
        await config.onSubmit(formData);

        verify(() => mockRepository.updateGoal(testGoal.id, any())).called(1);
      });

      test('should have correct onDelete callback for editing', () async {
        when(() => mockRepository.deleteGoal(any())).thenAnswer((_) async {});

        final config = GoalFormConfig.edit(
          goal: testGoal,
          repository: mockRepository,
        );

        // Test onDelete callback
        await config.onDelete!();

        verify(() => mockRepository.deleteGoal(testGoal.id)).called(1);
      });
    });

    group('Form Field Configuration Tests', () {
      late List<FormFieldConfig<dynamic>> fields;

      setUp(() {
        final config = GoalFormConfig.create(repository: mockRepository);
        fields = config.fields;
      });

      test('should have correct number and types of fields', () {
        expect(fields.length, equals(7));

        // Check field keys and types
        expect(fields[0].key, equals('name'));
        expect(fields[0], isA<TextFieldConfig>());

        expect(fields[1].key, equals('description'));
        expect(fields[1], isA<TextFieldConfig>());

        expect(fields[2].key, equals('targetAmount'));
        expect(fields[2], isA<TextFieldConfig>());

        expect(fields[3].key, equals('targetDate'));
        expect(fields[3], isA<FormFieldConfig<DateTime>>());
        expect(fields[3].type, equals(FormFieldType.datePicker));

        expect(fields[4].key, equals('status'));
        expect(fields[4], isA<DropdownFieldConfig<GoalStatus>>());

        expect(fields[5].key, equals('color'));
        expect(fields[5], isA<ColorPickerFieldConfig>());

        expect(fields[6].key, equals('icon'));
        expect(fields[6], isA<IconPickerFieldConfig>());
      });

      test('should configure name field correctly', () {
        final nameField = fields[0] as TextFieldConfig;

        expect(nameField.key, equals('name'));
        expect(nameField.label, equals('Goal Name'));
        expect(nameField.hintText, equals('Enter goal name'));
        expect(nameField.isRequired, isTrue);
        expect(nameField.maxLength, equals(100));
        expect(nameField.validator, isNotNull);
      });

      test('should configure description field correctly', () {
        final descriptionField = fields[1] as TextFieldConfig;

        expect(descriptionField.key, equals('description'));
        expect(descriptionField.label, equals('Description'));
        expect(
          descriptionField.hintText,
          equals('Enter goal description (optional)'),
        );
        expect(descriptionField.isRequired, isFalse);
        expect(descriptionField.maxLength, equals(500));
        expect(descriptionField.maxLines, equals(3));
        expect(descriptionField.validator, isNotNull);
      });

      test('should configure target amount field correctly', () {
        final targetAmountField = fields[2] as TextFieldConfig;

        expect(targetAmountField.key, equals('targetAmount'));
        expect(targetAmountField.label, equals('Target Amount'));
        expect(targetAmountField.hintText, equals('0.00'));
        expect(targetAmountField.isRequired, isTrue);
        expect(
          targetAmountField.keyboardType,
          equals(const TextInputType.numberWithOptions(decimal: true)),
        );
        expect(targetAmountField.validator, isNotNull);
      });

      test('should configure target date field correctly', () {
        final targetDateField = fields[3];

        expect(targetDateField.key, equals('targetDate'));
        expect(targetDateField.type, equals(FormFieldType.datePicker));
        expect(targetDateField.label, equals('Target Date'));
        expect(
          targetDateField.hintText,
          equals('Select target date (optional)'),
        );
        expect(targetDateField.isRequired, isFalse);
        // Note: validator check skipped due to type complexity
      });

      test('should configure status field correctly', () {
        final statusField = fields[4] as DropdownFieldConfig<GoalStatus>;

        expect(statusField.key, equals('status'));
        expect(statusField.label, equals('Status'));
        expect(statusField.items, equals(GoalStatus.values));
        expect(statusField.isRequired, isTrue);
        expect(statusField.initialValue, equals(GoalStatus.active));
        expect(statusField.displayStringForItem, isNotNull);

        // Test display string function
        expect(
          statusField.displayStringForItem!(GoalStatus.active),
          equals('Active'),
        );
        expect(
          statusField.displayStringForItem!(GoalStatus.paused),
          equals('Paused'),
        );
        expect(
          statusField.displayStringForItem!(GoalStatus.completed),
          equals('Completed'),
        );
        expect(
          statusField.displayStringForItem!(GoalStatus.cancelled),
          equals('Cancelled'),
        );
      });

      test('should configure color field correctly', () {
        final colorField = fields[5] as ColorPickerFieldConfig;

        expect(colorField.key, equals('color'));
        expect(colorField.label, equals('Goal Color'));
        expect(colorField.isRequired, isFalse);
        expect(colorField.availableColors, isNotNull);
      });

      test('should configure icon field correctly', () {
        final iconField = fields[6] as IconPickerFieldConfig;

        expect(iconField.key, equals('icon'));
        expect(iconField.label, equals('Goal Icon'));
        expect(iconField.isRequired, isFalse);
        expect(iconField.availableIcons, isNotNull);
      });
    });

    group('Field Validator Tests', () {
      late List<FormFieldConfig<dynamic>> fields;

      setUp(() {
        final config = GoalFormConfig.create(repository: mockRepository);
        fields = config.fields;
      });

      group('Name Field Validator', () {
        late String? Function(String?) validator;

        setUp(() {
          final nameField = fields[0] as TextFieldConfig;
          validator = nameField.validator!;
        });

        test('should validate required name field', () {
          expect(validator(null), equals('Goal name is required'));
          expect(validator(''), equals('Goal name is required'));
          expect(validator('   '), equals('Goal name is required'));
        });

        test('should validate minimum name length', () {
          expect(
            validator('a'),
            equals('Goal name must be at least 2 characters'),
          );
          expect(
            validator(' a '),
            equals('Goal name must be at least 2 characters'),
          );
        });

        test('should validate maximum name length', () {
          final longName = 'a' * 101;
          expect(
            validator(longName),
            equals('Goal name must be 100 characters or less'),
          );
        });

        test('should accept valid name', () {
          expect(validator('Valid Goal Name'), isNull);
          expect(validator('ab'), isNull);
          expect(validator('a' * 100), isNull);
        });
      });

      group('Description Field Validator', () {
        late String? Function(String?) validator;

        setUp(() {
          final descriptionField = fields[1] as TextFieldConfig;
          validator = descriptionField.validator!;
        });

        test('should accept null and empty description', () {
          expect(validator(null), isNull);
          expect(validator(''), isNull);
          expect(validator('   '), isNull);
        });

        test('should validate maximum description length', () {
          final longDescription = 'a' * 501;
          expect(
            validator(longDescription),
            equals('Description must be 500 characters or less'),
          );
        });

        test('should accept valid description', () {
          expect(validator('Valid description'), isNull);
          expect(validator('a' * 500), isNull);
        });
      });

      group('Target Amount Field Validator', () {
        late String? Function(String?) validator;

        setUp(() {
          final targetAmountField = fields[2] as TextFieldConfig;
          validator = targetAmountField.validator!;
        });

        test('should validate required target amount field', () {
          expect(validator(null), equals('Target amount is required'));
          expect(validator(''), equals('Target amount is required'));
          expect(validator('   '), equals('Target amount is required'));
        });

        test('should validate numeric format', () {
          expect(validator('abc'), equals('Please enter a valid amount'));
          expect(validator('12.34.56'), equals('Please enter a valid amount'));
          expect(validator(r'$100'), equals('Please enter a valid amount'));
        });

        test('should validate positive amount', () {
          expect(
            validator('0'),
            equals('Target amount must be greater than 0'),
          );
          expect(
            validator('-100'),
            equals('Target amount must be greater than 0'),
          );
        });

        test('should accept valid amounts', () {
          expect(validator('100'), isNull);
          expect(validator('100.50'), isNull);
          expect(validator('0.01'), isNull);
          expect(validator('999999.99'), isNull);
        });
      });

      // Note: Target date field validator tests are skipped due to type complexity
      // The validator functionality is tested through integration tests
    });

    group('Data Mapper entityToFormData() Tests', () {
      late GenericFormDataMapper<Goal> dataMapper;

      setUp(() {
        final config = GoalFormConfig.create(repository: mockRepository);
        dataMapper = config.dataMapper!;
      });

      test('should convert Goal entity to form data with all fields', () {
        final goal = Goal.create(
          userId: 'user-123',
          name: 'Test Goal',
          description: 'Test Description',
          targetAmountCents: 150075, // $1500.75
          targetDate: DateTime(2025, 6, 15),
          status: GoalStatus.paused,
          colorHex: '#FF5722',
          iconName: 'star',
        );

        final formData = dataMapper.entityToFormData(goal);

        expect(formData['name'], equals('Test Goal'));
        expect(formData['description'], equals('Test Description'));
        expect(formData['targetAmount'], equals('1500.75'));
        expect(formData['targetDate'], equals(DateTime(2025, 6, 15)));
        expect(formData['status'], equals(GoalStatus.paused));
        expect(formData['color'], equals('#FF5722'));
        expect(formData['icon'], equals('star'));
      });

      test('should convert Goal entity with null optional fields', () {
        final goal = Goal.create(
          userId: 'user-123',
          name: 'Minimal Goal',
          targetAmountCents: 50000, // $500.00
          status: GoalStatus.active,
        );

        final formData = dataMapper.entityToFormData(goal);

        expect(formData['name'], equals('Minimal Goal'));
        expect(formData['description'], equals(''));
        expect(formData['targetAmount'], equals('500.00'));
        expect(formData['targetDate'], isNull);
        expect(formData['status'], equals(GoalStatus.active));
        expect(formData['color'], isNull);
        expect(formData['icon'], isNull);
      });

      test('should handle zero amount correctly', () {
        final goal = Goal.create(
          userId: 'user-123',
          name: 'Zero Goal',
          targetAmountCents: 0,
          status: GoalStatus.active,
        );

        final formData = dataMapper.entityToFormData(goal);

        expect(formData['targetAmount'], equals('0.00'));
      });

      test('should handle large amounts correctly', () {
        final goal = Goal.create(
          userId: 'user-123',
          name: 'Large Goal',
          targetAmountCents: 999999999, // $9,999,999.99
          status: GoalStatus.active,
        );

        final formData = dataMapper.entityToFormData(goal);

        expect(formData['targetAmount'], equals('9999999.99'));
      });
    });

    group('Form Data to Entity Conversion', () {
      test('should convert form data with string amount to Goal entity', () {
        // Simulate form data as it comes from the form
        final formData = {
          'name': 'Emergency Fund',
          'description': 'Save for emergencies',
          'targetAmount': '1000.50', // String as expected from TextFieldConfig
          'targetDate': DateTime(2025, 12, 31),
          'status': GoalStatus.active,
          'color': '#2196F3',
          'icon': 'account_balance',
        };

        // Convert using the data mapper
        final config = GoalFormConfig.create(repository: mockRepository);
        final goal = config.dataMapper!.formDataToEntity(formData);

        // Verify conversion
        expect(goal.name, equals('Emergency Fund'));
        expect(goal.description, equals('Save for emergencies'));
        expect(
          goal.targetAmountCents,
          equals(100050),
        ); // $1000.50 = 100050 cents
        expect(goal.targetDate, equals(DateTime(2025, 12, 31)));
        expect(goal.status, equals(GoalStatus.active));
        expect(goal.colorHex, equals('#2196F3'));
        expect(goal.iconName, equals('account_balance'));
      });

      test('should convert form data with numeric amount to Goal entity', () {
        // Simulate form data with numeric amount (as seen in debug output)
        final formData = {
          'name': 'Vacation Fund',
          'description': null,
          'targetAmount': 500, // Numeric value as seen in debug
          'targetDate': null,
          'status': GoalStatus.active,
          'color': '#4CAF50',
          'icon': 'flight',
        };

        // Convert using the data mapper
        final config = GoalFormConfig.create(repository: mockRepository);
        final goal = config.dataMapper!.formDataToEntity(formData);

        // Verify conversion
        expect(goal.name, equals('Vacation Fund'));
        expect(goal.description, isNull);
        expect(goal.targetAmountCents, equals(50000)); // $500 = 50000 cents
        expect(goal.targetDate, isNull);
        expect(goal.status, equals(GoalStatus.active));
        expect(goal.colorHex, equals('#4CAF50'));
        expect(goal.iconName, equals('flight'));
      });

      test('should handle edge cases in amount conversion', () {
        final testCases = [
          {'input': '0', 'expected': 0, 'description': 'zero string'},
          {'input': '0.01', 'expected': 1, 'description': 'minimum cents'},
          {
            'input': '999999.99',
            'expected': 99999999,
            'description': 'large amount',
          },
          {'input': 0, 'expected': 0, 'description': 'zero number'},
          {'input': 0.01, 'expected': 1, 'description': 'minimum cents number'},
          {
            'input': 999999.99,
            'expected': 99999999,
            'description': 'large amount number',
          },
          {'input': '', 'expected': 0, 'description': 'empty string'},
          {'input': null, 'expected': 0, 'description': 'null value'},
          {'input': 'invalid', 'expected': 0, 'description': 'invalid string'},
        ];

        for (final testCase in testCases) {
          final formData = {
            'name': 'Test Goal',
            'targetAmount': testCase['input'],
            'status': GoalStatus.active,
          };

          final config = GoalFormConfig.create(repository: mockRepository);
          final goal = config.dataMapper!.formDataToEntity(formData);

          expect(
            goal.targetAmountCents,
            equals(testCase['expected']),
            reason: 'Failed for ${testCase['description']}',
          );
        }
      });

      test('should validate Goal entity matches Firestore requirements', () {
        // Test the exact form data from debug output
        final formData = {
          'name': 'test',
          'description': null,
          'targetAmount': 123,
          'targetDate': null,
          'status': GoalStatus.active,
          'color': '#2196F3',
          'icon': 'account_balance',
        };

        final config = GoalFormConfig.create(repository: MockGoalRepository());
        final goal = config.dataMapper!.formDataToEntity(formData);

        // Convert to JSON as it would be sent to Firestore
        final goalJson = goal.toJson();

        // Validate against Firestore rules requirements
        expect(goalJson.containsKey('id'), isTrue);
        expect(goalJson.containsKey('userId'), isTrue);
        expect(goalJson.containsKey('name'), isTrue);
        expect(goalJson.containsKey('targetAmountCents'), isTrue);
        expect(goalJson.containsKey('createdAt'), isTrue);
        expect(goalJson.containsKey('updatedAt'), isTrue);

        // Check field types
        expect(goalJson['name'], isA<String>());
        expect(goalJson['targetAmountCents'], isA<int>());
        expect(goalJson['targetAmountCents'], equals(12300)); // 123 * 100

        // Check constraints
        expect((goalJson['name'] as String).length, greaterThanOrEqualTo(2));
        expect(goalJson['targetAmountCents'] as int, greaterThan(0));
      });

      test('should handle null and empty description correctly', () {
        final testCases = [
          {'description': null, 'expected': null},
          {'description': '', 'expected': null},
          {'description': '   ', 'expected': null}, // Whitespace only
          {'description': 'Valid description', 'expected': 'Valid description'},
        ];

        for (final testCase in testCases) {
          final formData = {
            'name': 'Test Goal',
            'description': testCase['description'],
            'targetAmount': 100,
            'status': GoalStatus.active,
          };

          final config = GoalFormConfig.create(
            repository: MockGoalRepository(),
          );
          final goal = config.dataMapper!.formDataToEntity(formData);

          expect(goal.description, equals(testCase['expected']));
        }
      });

      test('should handle default status when not provided', () {
        final formData = {
          'name': 'Test Goal',
          'targetAmount': 100,
          // status not provided
        };

        final config = GoalFormConfig.create(repository: MockGoalRepository());
        final goal = config.dataMapper!.formDataToEntity(formData);

        expect(goal.status, equals(GoalStatus.active)); // Default value
      });
    });

    group('Firestore Rules Compliance', () {
      test('should create Goal that passes all Firestore validation rules', () {
        // Create a goal using the form config process
        final formData = {
          'name': 'Emergency Fund',
          'description': 'Six months of expenses',
          'targetAmount': '10000.00',
          'targetDate': DateTime(2025, 12, 31),
          'status': GoalStatus.active,
          'color': '#2196F3',
          'icon': 'account_balance',
        };

        final localMockRepository = MockGoalRepository();
        final config = GoalFormConfig.create(repository: localMockRepository);
        final goal = config.dataMapper!.formDataToEntity(formData);

        // Simulate what happens in the repository
        final goalWithUserAndId = goal.copyWith(
          userId: 'test-user-123',
          id: 'test-goal-456',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final goalJson = goalWithUserAndId.toJson();

        // Validate all Firestore rules requirements

        // Required fields check
        final requiredFields = [
          'id',
          'userId',
          'name',
          'targetAmountCents',
          'createdAt',
          'updatedAt',
        ];
        for (final field in requiredFields) {
          expect(
            goalJson.containsKey(field),
            isTrue,
            reason: 'Missing required field: $field',
          );
        }

        // Type validations
        expect(goalJson['id'], isA<String>());
        expect(goalJson['userId'], isA<String>());
        expect(goalJson['name'], isA<String>());
        expect(goalJson['targetAmountCents'], isA<int>());

        // Constraint validations
        expect((goalJson['name'] as String).length, inInclusiveRange(2, 100));
        expect(goalJson['targetAmountCents'] as int, greaterThan(0));
        expect(
          goalJson['targetAmountCents'] as int,
          lessThanOrEqualTo(999999999999),
        );

        // Optional field validations
        if (goalJson['description'] != null) {
          expect(goalJson['description'], isA<String>());
          expect(
            (goalJson['description'] as String).length,
            lessThanOrEqualTo(500),
          );
        }

        if (goalJson['colorHex'] != null) {
          expect(goalJson['colorHex'], isA<String>());
          expect(
            RegExp(
              r'^#[0-9A-Fa-f]{6}$',
            ).hasMatch(goalJson['colorHex'] as String),
            isTrue,
          );
        }

        if (goalJson['iconName'] != null) {
          expect(goalJson['iconName'], isA<String>());
          expect(
            RegExp(
              r'^[a-zA-Z][a-zA-Z0-9_]*$',
            ).hasMatch(goalJson['iconName'] as String),
            isTrue,
          );
          expect(
            (goalJson['iconName'] as String).length,
            lessThanOrEqualTo(50),
          );
        }

        if (goalJson['status'] != null) {
          expect(
            [
              'active',
              'paused',
              'completed',
              'cancelled',
            ].contains(goalJson['status']),
            isTrue,
          );
        }
      });
    });
  });
}
