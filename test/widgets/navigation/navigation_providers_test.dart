import 'package:budapp/routing/app_router.dart';
import 'package:budapp/widgets/navigation/navigation_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('NavigationDestinationData Tests', () {
    test('should create navigation destination with all required fields', () {
      const destination = NavigationDestinationData(
        route: '/test',
        label: 'Test',
        icon: Icons.home_outlined,
        selectedIcon: Icons.home,
      );

      expect(destination.route, '/test');
      expect(destination.label, 'Test');
      expect(destination.icon, Icons.home_outlined);
      expect(destination.selectedIcon, Icons.home);
      expect(destination.badge, isNull);
    });

    test('should create navigation destination with optional badge', () {
      const destination = NavigationDestinationData(
        route: '/test',
        label: 'Test',
        icon: Icons.home_outlined,
        selectedIcon: Icons.home,
        badge: '5',
      );

      expect(destination.badge, '5');
    });

    test('should be immutable', () {
      const destination = NavigationDestinationData(
        route: '/test',
        label: 'Test',
        icon: Icons.home_outlined,
        selectedIcon: Icons.home,
      );

      // Verify that NavigationDestinationData is immutable by checking it's annotated with @immutable
      expect(destination, isA<NavigationDestinationData>());
    });
  });

  group('bottomNavigationDestinations Tests', () {
    test('should contain all expected navigation destinations', () {
      expect(bottomNavigationDestinations.length, 5);

      // Verify each destination
      expect(bottomNavigationDestinations[0].route, AppRoutes.home);
      expect(bottomNavigationDestinations[0].label, 'Home');
      expect(bottomNavigationDestinations[0].icon, Icons.home_outlined);
      expect(bottomNavigationDestinations[0].selectedIcon, Icons.home);

      expect(bottomNavigationDestinations[1].route, '/transactions');
      expect(bottomNavigationDestinations[1].label, 'Transactions');
      expect(bottomNavigationDestinations[1].icon, Icons.receipt_long_outlined);
      expect(bottomNavigationDestinations[1].selectedIcon, Icons.receipt_long);

      expect(bottomNavigationDestinations[2].route, AppRoutes.accounts);
      expect(bottomNavigationDestinations[2].label, 'Wallet');
      expect(
        bottomNavigationDestinations[2].icon,
        Icons.account_balance_wallet_outlined,
      );
      expect(
        bottomNavigationDestinations[2].selectedIcon,
        Icons.account_balance_wallet,
      );

      expect(bottomNavigationDestinations[3].route, AppRoutes.categories);
      expect(bottomNavigationDestinations[3].label, 'Categories');
      expect(bottomNavigationDestinations[3].icon, Icons.category_outlined);
      expect(bottomNavigationDestinations[3].selectedIcon, Icons.category);

      expect(bottomNavigationDestinations[4].route, '/profile');
      expect(bottomNavigationDestinations[4].label, 'Profile');
      expect(bottomNavigationDestinations[4].icon, Icons.person_outlined);
      expect(bottomNavigationDestinations[4].selectedIcon, Icons.person);
    });

    test('should have no badges by default', () {
      for (final destination in bottomNavigationDestinations) {
        expect(destination.badge, isNull);
      }
    });

    test('should have unique routes', () {
      final routes = bottomNavigationDestinations.map((d) => d.route).toList();
      final uniqueRoutes = routes.toSet();
      expect(routes.length, uniqueRoutes.length);
    });

    test('should have unique labels', () {
      final labels = bottomNavigationDestinations.map((d) => d.label).toList();
      final uniqueLabels = labels.toSet();
      expect(labels.length, uniqueLabels.length);
    });
  });

  group('NavigationIndex Provider Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should initialize with index 0', () {
      final index = container.read(navigationIndexProvider);
      expect(index, 0);
    });

    test('should update index when setIndex is called with valid index', () {
      final notifier = container.read(navigationIndexProvider.notifier);

      notifier.setIndex(2);
      final index = container.read(navigationIndexProvider);

      expect(index, 2);
    });

    test(
      'should not update index when setIndex is called with negative index',
      () {
        final notifier = container.read(navigationIndexProvider.notifier);

        notifier.setIndex(-1);
        final index = container.read(navigationIndexProvider);

        expect(index, 0); // Should remain at initial value
      },
    );

    test(
      'should not update index when setIndex is called with index >= destinations length',
      () {
        final notifier = container.read(navigationIndexProvider.notifier);

        notifier.setIndex(bottomNavigationDestinations.length);
        final index = container.read(navigationIndexProvider);

        expect(index, 0); // Should remain at initial value
      },
    );

    test('should set index from route correctly', () {
      final notifier = container.read(navigationIndexProvider.notifier);

      // Test home route
      notifier.setIndexFromRoute(AppRoutes.home);
      expect(container.read(navigationIndexProvider), 0);

      // Test transactions route
      notifier.setIndexFromRoute('/transactions');
      expect(container.read(navigationIndexProvider), 1);

      // Test accounts route
      notifier.setIndexFromRoute(AppRoutes.accounts);
      expect(container.read(navigationIndexProvider), 2);

      // Test categories route
      notifier.setIndexFromRoute(AppRoutes.categories);
      expect(container.read(navigationIndexProvider), 3);

      // Test profile route
      notifier.setIndexFromRoute('/profile');
      expect(container.read(navigationIndexProvider), 4);
    });

    test('should handle route prefixes correctly', () {
      final notifier = container.read(navigationIndexProvider.notifier);

      // Test route with sub-paths
      notifier.setIndexFromRoute('/transactions/create');
      expect(container.read(navigationIndexProvider), 1);

      notifier.setIndexFromRoute('/accounts/123');
      expect(container.read(navigationIndexProvider), 2);

      notifier.setIndexFromRoute('/categories/edit/456');
      expect(container.read(navigationIndexProvider), 3);
    });

    test('should not change index for unknown routes', () {
      final notifier = container.read(navigationIndexProvider.notifier);

      // Set to a known index first
      notifier.setIndex(2);
      expect(container.read(navigationIndexProvider), 2);

      // Try to set from unknown route
      notifier.setIndexFromRoute('/unknown-route');
      expect(
        container.read(navigationIndexProvider),
        2,
      ); // Should remain unchanged
    });
  });

  group('CurrentRoute Provider Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should initialize with home route', () {
      final route = container.read(currentRouteProvider);
      expect(route, AppRoutes.home);
    });

    test('should update route when set', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = '/transactions';
      final route = container.read(currentRouteProvider);

      expect(route, '/transactions');
    });

    test('should provide getter for current route', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = '/accounts';
      expect(notifier.route, '/accounts');
    });

    test('should handle multiple route changes', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = '/transactions';
      expect(container.read(currentRouteProvider), '/transactions');

      notifier.route = '/categories';
      expect(container.read(currentRouteProvider), '/categories');

      notifier.route = '/profile';
      expect(container.read(currentRouteProvider), '/profile');
    });
  });

  group('fabActionRoute Provider Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should return correct action route for home/dashboard', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = AppRoutes.home;
      expect(container.read(fabActionRouteProvider), '/transactions/create');

      notifier.route = '/dashboard';
      expect(container.read(fabActionRouteProvider), '/transactions/create');

      notifier.route = '/dashboard/overview';
      expect(container.read(fabActionRouteProvider), '/transactions/create');
    });

    test('should return correct action route for transactions', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = '/transactions';
      expect(container.read(fabActionRouteProvider), '/transactions/create');

      notifier.route = '/transactions/list';
      expect(container.read(fabActionRouteProvider), '/transactions/create');
    });

    test('should return correct action route for accounts', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = AppRoutes.accounts;
      expect(container.read(fabActionRouteProvider), AppRoutes.accountCreate);

      notifier.route = '${AppRoutes.accounts}/123';
      expect(container.read(fabActionRouteProvider), AppRoutes.accountCreate);
    });

    test('should return correct action route for categories', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = AppRoutes.categories;
      expect(container.read(fabActionRouteProvider), '/categories/create');

      notifier.route = '${AppRoutes.categories}/edit';
      expect(container.read(fabActionRouteProvider), '/categories/create');
    });

    test('should return correct action route for tags', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = AppRoutes.tags;
      expect(container.read(fabActionRouteProvider), AppRoutes.tagCreate);

      notifier.route = '${AppRoutes.tags}/123';
      expect(container.read(fabActionRouteProvider), AppRoutes.tagCreate);
    });

    test('should return null for profile routes', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = '/profile';
      expect(container.read(fabActionRouteProvider), isNull);

      notifier.route = '/profile/settings';
      expect(container.read(fabActionRouteProvider), isNull);
    });

    test('should return null for unknown routes', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = '/unknown';
      expect(container.read(fabActionRouteProvider), isNull);

      notifier.route = '/some/random/route';
      expect(container.read(fabActionRouteProvider), isNull);
    });
  });

  group('fabIcon Provider Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should return correct icon for home/dashboard', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = AppRoutes.home;
      expect(container.read(fabIconProvider), Icons.add);

      notifier.route = '/dashboard';
      expect(container.read(fabIconProvider), Icons.add);
    });

    test('should return correct icon for transactions', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = '/transactions';
      expect(container.read(fabIconProvider), Icons.receipt_long);
    });

    test('should return correct icon for accounts', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = AppRoutes.accounts;
      expect(container.read(fabIconProvider), Icons.account_balance_wallet);
    });

    test('should return correct icon for categories', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = AppRoutes.categories;
      expect(container.read(fabIconProvider), Icons.category);
    });

    test('should return correct icon for tags', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = AppRoutes.tags;
      expect(container.read(fabIconProvider), Icons.local_offer);
    });

    test('should return default icon for profile and unknown routes', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = '/profile';
      expect(container.read(fabIconProvider), Icons.add);

      notifier.route = '/unknown';
      expect(container.read(fabIconProvider), Icons.add);
    });
  });

  group('fabTooltip Provider Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should return correct tooltip for home/dashboard', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = AppRoutes.home;
      expect(container.read(fabTooltipProvider), 'Add Transaction');

      notifier.route = '/dashboard';
      expect(container.read(fabTooltipProvider), 'Add Transaction');
    });

    test('should return correct tooltip for transactions', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = '/transactions';
      expect(container.read(fabTooltipProvider), 'Add Transaction');
    });

    test('should return correct tooltip for accounts', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = AppRoutes.accounts;
      expect(container.read(fabTooltipProvider), 'Add Account');
    });

    test('should return correct tooltip for categories', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = AppRoutes.categories;
      expect(container.read(fabTooltipProvider), 'Add Category');
    });

    test('should return correct tooltip for tags', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = AppRoutes.tags;
      expect(container.read(fabTooltipProvider), 'Add Tag');
    });

    test('should return default tooltip for profile and unknown routes', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = '/profile';
      expect(container.read(fabTooltipProvider), 'Add');

      notifier.route = '/unknown';
      expect(container.read(fabTooltipProvider), 'Add');
    });
  });

  group('isRouteImplemented Provider Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should return true for implemented routes', () {
      final notifier = container.read(currentRouteProvider.notifier);

      // Test all implemented routes
      final implementedRoutes = [
        AppRoutes.home,
        AppRoutes.accounts,
        AppRoutes.categories,
        AppRoutes.tags,
        '/transactions',
        '/dashboard',
        '/profile',
      ];

      for (final route in implementedRoutes) {
        notifier.route = route;
        expect(
          container.read(isRouteImplementedProvider),
          true,
          reason: 'Route $route should be implemented',
        );
      }
    });

    test('should return true for route prefixes', () {
      final notifier = container.read(currentRouteProvider.notifier);

      // Test route prefixes
      final routePrefixes = [
        '${AppRoutes.accounts}/123',
        '${AppRoutes.categories}/edit',
        '${AppRoutes.tags}/create',
        '/transactions/create',
        '/dashboard/overview',
        '/profile/settings',
      ];

      for (final route in routePrefixes) {
        notifier.route = route;
        expect(
          container.read(isRouteImplementedProvider),
          true,
          reason: 'Route $route should be implemented',
        );
      }
    });

    test('should return false for unimplemented routes', () {
      final notifier = container.read(currentRouteProvider.notifier);

      final unimplementedRoutes = [
        '/unknown',
        '/some/random/route',
        '/budgets', // Not in the implemented list
        '/goals', // Not in the implemented list
        '/settings/advanced',
      ];

      for (final route in unimplementedRoutes) {
        notifier.route = route;
        expect(
          container.read(isRouteImplementedProvider),
          false,
          reason: 'Route $route should not be implemented',
        );
      }
    });
  });

  group('Provider Integration Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should update all dependent providers when route changes', () {
      final routeNotifier = container.read(currentRouteProvider.notifier);

      // Change to transactions route
      routeNotifier.route = '/transactions';

      expect(container.read(currentRouteProvider), '/transactions');
      expect(container.read(fabActionRouteProvider), '/transactions/create');
      expect(container.read(fabIconProvider), Icons.receipt_long);
      expect(container.read(fabTooltipProvider), 'Add Transaction');
      expect(container.read(isRouteImplementedProvider), true);

      // Change to accounts route
      routeNotifier.route = AppRoutes.accounts;

      expect(container.read(currentRouteProvider), AppRoutes.accounts);
      expect(container.read(fabActionRouteProvider), AppRoutes.accountCreate);
      expect(container.read(fabIconProvider), Icons.account_balance_wallet);
      expect(container.read(fabTooltipProvider), 'Add Account');
      expect(container.read(isRouteImplementedProvider), true);
    });

    test('should handle navigation index and route synchronization', () {
      final routeNotifier = container.read(currentRouteProvider.notifier);
      final navNotifier = container.read(navigationIndexProvider.notifier);

      // Set route and sync navigation index
      routeNotifier.route = '/transactions';
      navNotifier.setIndexFromRoute('/transactions');

      expect(container.read(currentRouteProvider), '/transactions');
      expect(container.read(navigationIndexProvider), 1);

      // Change navigation index and verify it doesn't affect route
      navNotifier.setIndex(3);
      expect(container.read(navigationIndexProvider), 3);
      expect(
        container.read(currentRouteProvider),
        '/transactions',
      ); // Should remain unchanged
    });
  });

  group('Edge Cases and Error Handling', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should handle empty route strings', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = '';
      expect(container.read(currentRouteProvider), '');
      expect(container.read(fabActionRouteProvider), isNull);
      expect(container.read(fabIconProvider), Icons.add);
      expect(container.read(fabTooltipProvider), 'Add');
      expect(container.read(isRouteImplementedProvider), false);
    });

    test('should handle very long route strings', () {
      final notifier = container.read(currentRouteProvider.notifier);

      final longRoute = '/transactions/${'a' * 1000}';
      notifier.route = longRoute;

      expect(container.read(currentRouteProvider), longRoute);
      expect(container.read(fabActionRouteProvider), '/transactions/create');
      expect(container.read(isRouteImplementedProvider), true);
    });

    test('should handle special characters in routes', () {
      final notifier = container.read(currentRouteProvider.notifier);

      notifier.route = '/transactions/create?param=value&other=123';
      expect(container.read(fabActionRouteProvider), '/transactions/create');
      expect(container.read(isRouteImplementedProvider), true);
    });

    test('should handle case sensitivity correctly', () {
      final notifier = container.read(currentRouteProvider.notifier);

      // Routes should be case sensitive
      notifier.route = '/TRANSACTIONS';
      expect(container.read(isRouteImplementedProvider), false);

      notifier.route = '/transactions';
      expect(container.read(isRouteImplementedProvider), true);
    });
  });
}
