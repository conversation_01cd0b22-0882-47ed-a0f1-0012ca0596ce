import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/error_boundary_widget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_test/flutter_test.dart';

// Type aliases for testing
typedef ErrorCallback = void Function(FlutterErrorDetails details);

/// Widget that works normally
class WorkingWidget extends StatelessWidget {
  const WorkingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Text('Working widget');
  }
}

/// Widget that throws an error during build
class ErrorThrowingWidget extends StatelessWidget {
  const ErrorThrowingWidget({
    super.key,
    this.errorMessage = 'Widget build error occurred',
  });

  final String errorMessage;

  @override
  Widget build(BuildContext context) {
    throw Exception(errorMessage);
  }
}

/// Test implementation that mimics the real ErrorBoundaryWidget for controlled testing
class TestableErrorBoundaryWidget extends StatefulWidget {
  const TestableErrorBoundaryWidget({
    required this.child,
    this.fallbackBuilder,
    this.onError,
    this.showDetails = false,
    this.canRetry = true,
    super.key,
  });

  final Widget child;
  final Widget Function(
    BuildContext context,
    Object error,
    StackTrace? stackTrace,
    VoidCallback? retry,
  )?
  fallbackBuilder;
  final void Function(Object error, StackTrace? stackTrace)? onError;
  final bool showDetails;
  final bool canRetry;

  @override
  State<TestableErrorBoundaryWidget> createState() =>
      _TestableErrorBoundaryWidgetState();
}

class _TestableErrorBoundaryWidgetState
    extends State<TestableErrorBoundaryWidget> {
  Object? _error;
  StackTrace? _stackTrace;
  bool _hasError = false;

  /// Simulate the real ErrorBoundaryWidget's _shouldCatchError method
  bool _shouldCatchError(Object error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('renderbox') ||
        errorString.contains('renderflex') ||
        errorString.contains('widget') ||
        errorString.contains('build') ||
        errorString.contains('layout');
  }

  /// Simulate the real ErrorBoundaryWidget's _handleError method
  void handleError(Object error, StackTrace? stackTrace) {
    // Log error for crash reporting (simulates ErrorService.logError)
    if (kDebugMode) {
      // Simulate ErrorService.logError debug output
      print('ERROR: $error');
    }

    // Call custom error handler if provided
    try {
      widget.onError?.call(error, stackTrace);
    } on Exception {
      // Ignore errors in onError callback - error boundary should still work
    }

    // Update UI state
    if (mounted) {
      setState(() {
        _error = error;
        _stackTrace = stackTrace;
        _hasError = true;
      });
    }
  }

  /// Simulate the real ErrorBoundaryWidget's _retry method
  void retry() {
    // Simulate ErrorService.logUserAction
    if (kDebugMode) {
      print(
        'User action: Error boundary retry - ${{'error': _error.toString()}}',
      );
    }

    setState(() {
      _error = null;
      _stackTrace = null;
      _hasError = false;
    });
  }

  /// Simulate the real ErrorBoundaryWidget's _reportError method
  void reportError(BuildContext context) {
    if (_error == null) return;

    // Simulate ErrorService.logError for user-reported error
    if (kDebugMode) {
      print('User reported error from ErrorBoundary: $_error');
    }

    // Simulate ErrorService.showInfoSnackBar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(AppLocalizations.of(context)!.errorReported)),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      // Use custom fallback builder if provided
      if (widget.fallbackBuilder != null) {
        return widget.fallbackBuilder!(
          context,
          _error!,
          _stackTrace,
          widget.canRetry ? retry : null,
        );
      }

      // Use default error UI (simulates _buildDefaultErrorUI)
      return _buildDefaultErrorUI(context);
    }

    // Return the child widget normally
    return widget.child;
  }

  /// Simulate the real ErrorBoundaryWidget's _buildDefaultErrorUI method
  Widget _buildDefaultErrorUI(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Error icon
          Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
          const SizedBox(height: 16),

          // Error title
          Text(
            l10n.errorBoundaryTitle,
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.error,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),

          // Error message
          Text(
            l10n.errorBoundaryMessage,
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),

          // Error details (debug mode only)
          if (kDebugMode && widget.showDetails && _error != null) ...[
            ExpansionTile(
              title: Text(
                'Error Details (Debug)',
                style: theme.textTheme.bodySmall,
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Text(
                    _error.toString(),
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],

          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Retry button
              if (widget.canRetry) ...[
                ElevatedButton.icon(
                  onPressed: retry,
                  icon: const Icon(Icons.refresh),
                  label: Text(l10n.retry),
                ),
                const SizedBox(width: 8),
              ],

              // Report button (debug mode only)
              if (kDebugMode)
                OutlinedButton.icon(
                  onPressed: () => reportError(context),
                  icon: const Icon(Icons.bug_report),
                  label: Text(l10n.reportError),
                ),
            ],
          ),
        ],
      ),
    );
  }

  // Public getters for testing
  Object? get error => _error;
  StackTrace? get stackTrace => _stackTrace;
  bool get hasError => _hasError;
  bool shouldCatchErrorForTesting(Object error) => _shouldCatchError(error);
}

void main() {
  group('ErrorBoundaryWidget Comprehensive Tests', () {
    // ignore: unused_local_variable, needed for comprehensive test setup
    FlutterErrorDetails? capturedError;
    late ErrorCallback? originalOnError;

    setUp(() {
      capturedError = null;
      originalOnError = FlutterError.onError;
    });

    tearDown(() {
      if (originalOnError != null) {
        FlutterError.onError = originalOnError;
      }
    });

    /// Helper widget to wrap tests with localization
    Widget createTestWidget({required Widget child}) {
      return MaterialApp(
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [Locale('en', '')],
        home: Scaffold(body: child),
      );
    }

    group('Basic Functionality', () {
      testWidgets('displays child widget when no error occurs', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(child: WorkingWidget()),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('Working widget'), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsNothing);
      });

      testWidgets('shows default error UI when error occurs', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(child: WorkingWidget()),
          ),
        );

        // Get the error boundary state and simulate error
        final errorBoundaryFinder = find.byType(TestableErrorBoundaryWidget);
        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(errorBoundaryFinder);

        errorBoundaryState.handleError(
          Exception('Test UI error'),
          StackTrace.current,
        );
        await tester.pumpAndSettle();

        // Verify default error UI components
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
        expect(find.text('Something went wrong'), findsOneWidget);
        expect(
          find.text(
            "We've encountered an unexpected error. You can try again or contact support if the problem persists.",
          ),
          findsOneWidget,
        );
        expect(find.text('Retry'), findsOneWidget);
        expect(find.byIcon(Icons.refresh), findsOneWidget);
      });

      testWidgets('shows retry button by default in error state', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(child: WorkingWidget()),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );
        errorBoundaryState.handleError(Exception('Test error'), null);
        await tester.pumpAndSettle();

        expect(find.text('Retry'), findsOneWidget);
        expect(find.byIcon(Icons.refresh), findsOneWidget);
      });

      testWidgets('hides retry button when canRetry is false', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(
              canRetry: false,
              child: WorkingWidget(),
            ),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );
        errorBoundaryState.handleError(Exception('Test error'), null);
        await tester.pumpAndSettle();

        expect(find.text('Retry'), findsNothing);
        expect(find.byIcon(Icons.refresh), findsNothing);
      });

      testWidgets('shows debug report button in debug mode', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(child: WorkingWidget()),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );
        errorBoundaryState.handleError(Exception('Test error'), null);
        await tester.pumpAndSettle();

        // In debug mode (kDebugMode = true in tests)
        expect(find.text('Report Error'), findsOneWidget);
        expect(find.byIcon(Icons.bug_report), findsOneWidget);
      });

      testWidgets(
        'shows error details in debug mode when showDetails is true',
        (tester) async {
          final testError = Exception('Detailed test error');

          await tester.pumpWidget(
            createTestWidget(
              child: const TestableErrorBoundaryWidget(
                showDetails: true,
                child: WorkingWidget(),
              ),
            ),
          );

          final errorBoundaryState = tester
              .state<_TestableErrorBoundaryWidgetState>(
                find.byType(TestableErrorBoundaryWidget),
              );
          errorBoundaryState.handleError(testError, null);
          await tester.pumpAndSettle();

          // Should show expansion tile with error details in debug mode
          expect(find.byType(ExpansionTile), findsOneWidget);
          expect(find.text('Error Details (Debug)'), findsOneWidget);

          // Expand the details
          await tester.tap(find.byType(ExpansionTile));
          await tester.pumpAndSettle();

          // Should show error details
          expect(find.textContaining('Detailed test error'), findsOneWidget);
        },
      );
    });

    group('Error Type Detection', () {
      testWidgets('shouldCatchError correctly identifies UI errors', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(child: WorkingWidget()),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );

        // Test UI-related errors that should be caught
        final uiErrors = [
          Exception('RenderBox error occurred'),
          Exception('RenderFlex overflow error'),
          Exception('Widget build error'),
          Exception('Layout constraint error'),
          Exception('renderbox constraint failed'),
        ];

        for (final error in uiErrors) {
          final shouldCatch = errorBoundaryState.shouldCatchErrorForTesting(
            error,
          );
          expect(shouldCatch, isTrue, reason: 'Should catch UI error: $error');
        }

        // Test non-UI errors that should NOT be caught
        final nonUiErrors = [
          Exception('Network connection error'),
          Exception('Database query failed'),
          Exception('Authentication error'),
          Exception('File system error'),
          Exception('Generic application error'),
        ];

        for (final error in nonUiErrors) {
          final shouldCatch = errorBoundaryState.shouldCatchErrorForTesting(
            error,
          );
          expect(
            shouldCatch,
            isFalse,
            reason: 'Should NOT catch non-UI error: $error',
          );
        }
      });
    });

    group('Custom Fallback Builder', () {
      testWidgets('uses custom fallback builder when provided', (tester) async {
        final testError = Exception('Custom fallback test error');

        await tester.pumpWidget(
          createTestWidget(
            child: TestableErrorBoundaryWidget(
              fallbackBuilder: (context, error, stackTrace, retry) {
                return Column(
                  children: [
                    const Text('Custom Error UI'),
                    Text('Error: $error'),
                    if (retry != null)
                      ElevatedButton(
                        onPressed: retry,
                        child: const Text('Custom Retry'),
                      ),
                  ],
                );
              },
              child: const WorkingWidget(),
            ),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );
        errorBoundaryState.handleError(testError, null);
        await tester.pumpAndSettle();

        expect(find.text('Custom Error UI'), findsOneWidget);
        expect(
          find.text('Error: Exception: Custom fallback test error'),
          findsOneWidget,
        );
        expect(find.text('Custom Retry'), findsOneWidget);

        // Should not show default error UI
        expect(find.text('Something went wrong'), findsNothing);
      });

      testWidgets('passes null retry when canRetry is false', (tester) async {
        VoidCallback? capturedRetry;

        await tester.pumpWidget(
          createTestWidget(
            child: TestableErrorBoundaryWidget(
              canRetry: false,
              fallbackBuilder: (context, error, stackTrace, retry) {
                capturedRetry = retry;
                return const Text('No Retry');
              },
              child: const WorkingWidget(),
            ),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );
        errorBoundaryState.handleError(Exception('Test error'), null);
        await tester.pumpAndSettle();

        expect(capturedRetry, isNull);
        expect(find.text('No Retry'), findsOneWidget);
      });
    });

    group('Error Handling and Callbacks', () {
      testWidgets('calls onError callback when error occurs', (tester) async {
        Object? capturedError;
        StackTrace? capturedStackTrace;
        final testError = Exception('Callback test error');
        final testStackTrace = StackTrace.current;

        await tester.pumpWidget(
          createTestWidget(
            child: TestableErrorBoundaryWidget(
              onError: (error, stackTrace) {
                capturedError = error;
                capturedStackTrace = stackTrace;
              },
              child: const WorkingWidget(),
            ),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );
        errorBoundaryState.handleError(testError, testStackTrace);
        await tester.pumpAndSettle();

        expect(capturedError, equals(testError));
        expect(capturedStackTrace, equals(testStackTrace));
      });

      testWidgets('handles errors gracefully when onError throws', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            child: TestableErrorBoundaryWidget(
              onError: (error, stackTrace) {
                throw Exception('Error in onError callback');
              },
              child: const WorkingWidget(),
            ),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );

        // This should not throw despite the onError callback throwing
        expect(
          () => errorBoundaryState.handleError(Exception('Test error'), null),
          returnsNormally,
        );

        await tester.pumpAndSettle();

        // Should still show error UI despite callback error
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
      });
    });

    group('Retry Functionality', () {
      testWidgets('retry button clears error state', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(child: WorkingWidget()),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );
        errorBoundaryState.handleError(Exception('Test error'), null);
        await tester.pumpAndSettle();

        // Should show error UI
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
        expect(find.text('Retry'), findsOneWidget);

        // Tap retry
        await tester.tap(find.text('Retry'));
        await tester.pumpAndSettle();

        // Should show working widget (error state cleared)
        expect(find.text('Working widget'), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsNothing);
      });
    });

    group('Debug Features', () {
      testWidgets('report error button shows confirmation', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(child: WorkingWidget()),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );
        errorBoundaryState.handleError(Exception('Test error'), null);
        await tester.pumpAndSettle();

        // Tap report error button
        await tester.tap(find.text('Report Error'));
        await tester.pumpAndSettle();

        // Should show confirmation message
        expect(
          find.text(
            'Error reported successfully. Thank you for helping us improve the app.',
          ),
          findsOneWidget,
        );
      });

      testWidgets('report error handles null error gracefully', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(child: WorkingWidget()),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );

        // Test the reportError method with no error state
        expect(
          () => errorBoundaryState.reportError(
            tester.element(find.byType(TestableErrorBoundaryWidget)),
          ),
          returnsNormally,
        );
      });
    });

    group('SafeWidget', () {
      testWidgets('SafeWidget displays child widget normally', (tester) async {
        await tester.pumpWidget(
          createTestWidget(child: const SafeWidget(child: WorkingWidget())),
        );

        await tester.pumpAndSettle();

        expect(find.text('Working widget'), findsOneWidget);
      });

      testWidgets('SafeWidget uses fallback when error occurs', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const SafeWidget(
              fallback: Text('Safe fallback'),
              child: WorkingWidget(),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Get the underlying ErrorBoundaryWidget and simulate an error
        final errorBoundaryFinder = find.byType(ErrorBoundaryWidget);
        expect(errorBoundaryFinder, findsOneWidget);

        // With no error, should show original child
        expect(find.text('Working widget'), findsOneWidget);
        expect(find.text('Safe fallback'), findsNothing);
      });

      testWidgets('SafeWidget onError callback gets called', (tester) async {
        // ignore: unused_local_variable, needed for testing callback parameters
        Object? capturedError;
        // ignore: unused_local_variable, needed for testing callback parameters
        StackTrace? capturedStackTrace;

        await tester.pumpWidget(
          createTestWidget(
            child: SafeWidget(
              onError: (error, stackTrace) {
                capturedError = error;
                capturedStackTrace = stackTrace;
              },
              child: const WorkingWidget(),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify SafeWidget creates an ErrorBoundaryWidget
        expect(find.byType(ErrorBoundaryWidget), findsOneWidget);
      });
    });

    group('Extension Methods', () {
      testWidgets('withErrorBoundary extension creates ErrorBoundaryWidget', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const WorkingWidget().withErrorBoundary(
              fallbackBuilder: (context, error, stackTrace, retry) {
                return const Text('Extension fallback');
              },
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should show working widget since no error occurs
        expect(find.text('Working widget'), findsOneWidget);

        // Verify ErrorBoundaryWidget was created
        expect(find.byType(ErrorBoundaryWidget), findsOneWidget);
      });

      testWidgets('withErrorBoundary passes parameters correctly', (
        tester,
      ) async {
        // ignore: unused_local_variable, needed for testing error callback setup
        var onErrorCalled = false;

        await tester.pumpWidget(
          createTestWidget(
            child: const WorkingWidget().withErrorBoundary(
              showDetails: true,
              canRetry: false,
              onError: (error, stackTrace) {
                onErrorCalled = true;
              },
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should show working widget
        expect(find.text('Working widget'), findsOneWidget);

        // Verify ErrorBoundaryWidget was created with correct parameters
        final errorBoundaryWidget = tester.widget<ErrorBoundaryWidget>(
          find.byType(ErrorBoundaryWidget),
        );

        expect(errorBoundaryWidget.showDetails, isTrue);
        expect(errorBoundaryWidget.canRetry, isFalse);
        expect(errorBoundaryWidget.onError, isNotNull);
      });

      testWidgets('safely extension creates SafeWidget', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const WorkingWidget().safely(
              fallback: const Text('Safely fallback'),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should show working widget since no error occurs
        expect(find.text('Working widget'), findsOneWidget);

        // Verify SafeWidget was created
        expect(find.byType(SafeWidget), findsOneWidget);
      });

      testWidgets('safely extension shows fallback on error', (tester) async {
        // ignore: unused_local_variable, needed for testing error callback setup
        var onErrorCalled = false;

        await tester.pumpWidget(
          createTestWidget(
            child: const WorkingWidget().safely(
              fallback: const Text('Safely fallback'),
              onError: (error, stackTrace) {
                onErrorCalled = true;
              },
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify SafeWidget was created with correct parameters
        final safeWidget = tester.widget<SafeWidget>(find.byType(SafeWidget));
        expect(safeWidget.fallback, isA<Text>());
        expect(safeWidget.onError, isNotNull);
      });
    });

    group('State Management', () {
      testWidgets('error state variables are properly managed', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(child: WorkingWidget()),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );

        // Initially should have no error
        expect(errorBoundaryState.hasError, isFalse);
        expect(errorBoundaryState.error, isNull);
        expect(errorBoundaryState.stackTrace, isNull);

        // Simulate error
        final testError = Exception('State management test');
        final testStackTrace = StackTrace.current;
        errorBoundaryState.handleError(testError, testStackTrace);

        await tester.pumpAndSettle();

        // Should have error state
        expect(errorBoundaryState.hasError, isTrue);
        expect(errorBoundaryState.error, equals(testError));
        expect(errorBoundaryState.stackTrace, equals(testStackTrace));

        // Call retry
        errorBoundaryState.retry();

        await tester.pumpAndSettle();

        // Error state should be cleared
        expect(errorBoundaryState.hasError, isFalse);
        expect(errorBoundaryState.error, isNull);
        expect(errorBoundaryState.stackTrace, isNull);
      });

      testWidgets('maintains error state across rebuilds', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(child: WorkingWidget()),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );
        errorBoundaryState.handleError(Exception('State test error'), null);
        await tester.pumpAndSettle();

        // Should show error UI
        expect(find.byIcon(Icons.error_outline), findsOneWidget);

        // Rebuild the same widget
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(child: WorkingWidget()),
          ),
        );

        await tester.pumpAndSettle();

        // Should still show error UI (state maintained)
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
      });
    });

    group('Edge Cases and Error Handling', () {
      testWidgets('handles multiple consecutive errors', (tester) async {
        var errorCount = 0;

        await tester.pumpWidget(
          createTestWidget(
            child: TestableErrorBoundaryWidget(
              onError: (error, stackTrace) {
                errorCount++;
              },
              child: const WorkingWidget(),
            ),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );

        // First error
        errorBoundaryState.handleError(Exception('First error'), null);
        await tester.pumpAndSettle();

        expect(errorCount, 1);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);

        // Retry should reset the error state
        await tester.tap(find.text('Retry'));
        await tester.pumpAndSettle();

        expect(find.text('Working widget'), findsOneWidget);

        // Second error
        errorBoundaryState.handleError(Exception('Second error'), null);
        await tester.pumpAndSettle();

        expect(errorCount, 2);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
      });

      testWidgets('handles widget rebuild after error state', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(child: WorkingWidget()),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );

        // Simulate error
        errorBoundaryState.handleError(Exception('Rebuild test error'), null);
        await tester.pumpAndSettle();

        // Should show error UI
        expect(find.byIcon(Icons.error_outline), findsOneWidget);

        // Rebuild the same widget tree
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(child: WorkingWidget()),
          ),
        );
        await tester.pumpAndSettle();

        // Error state should persist after rebuild
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
      });

      testWidgets('multiple error boundaries work independently', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const Column(
              children: [
                TestableErrorBoundaryWidget(child: WorkingWidget()),
                TestableErrorBoundaryWidget(child: WorkingWidget()),
              ],
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Get both error boundary states
        final errorBoundaryFinders = find.byType(TestableErrorBoundaryWidget);
        final firstBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              errorBoundaryFinders.at(0),
            );
        tester.state<_TestableErrorBoundaryWidgetState>(
          errorBoundaryFinders.at(1),
        );

        // Simulate error in first boundary only
        firstBoundaryState.handleError(Exception('First boundary error'), null);
        await tester.pumpAndSettle();

        // First should show error, second should show working widget
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
        expect(find.text('Working widget'), findsOneWidget);
      });

      testWidgets('handles null fallback builder gracefully', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(
              fallbackBuilder: null,
              child: WorkingWidget(),
            ),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );
        errorBoundaryState.handleError(Exception('Null fallback test'), null);
        await tester.pumpAndSettle();

        // Should show default error UI
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
        expect(find.text('Something went wrong'), findsOneWidget);
      });
    });

    group('Performance and Memory', () {
      testWidgets('does not leak memory with repeated errors and retries', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(child: WorkingWidget()),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );

        // Simulate multiple error/retry cycles
        for (var i = 0; i < 5; i++) {
          errorBoundaryState.handleError(Exception('Error $i'), null);
          await tester.pumpAndSettle();

          expect(find.byIcon(Icons.error_outline), findsOneWidget);

          await tester.tap(find.text('Retry'));
          await tester.pumpAndSettle();

          expect(find.text('Working widget'), findsOneWidget);
        }

        // Should not crash or cause performance issues
      });

      testWidgets('handles rapid error state changes', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const TestableErrorBoundaryWidget(child: WorkingWidget()),
          ),
        );

        final errorBoundaryState = tester
            .state<_TestableErrorBoundaryWidgetState>(
              find.byType(TestableErrorBoundaryWidget),
            );

        // Rapid error state changes
        errorBoundaryState.handleError(Exception('Error 1'), null);
        errorBoundaryState.retry();
        errorBoundaryState.handleError(Exception('Error 2'), null);
        errorBoundaryState.retry();

        await tester.pumpAndSettle();

        // Should end up in non-error state
        expect(find.text('Working widget'), findsOneWidget);
      });
    });

    group('Real ErrorBoundaryWidget Integration', () {
      testWidgets('real ErrorBoundaryWidget creates proper widget tree', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ErrorBoundaryWidget(child: WorkingWidget()),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('Working widget'), findsOneWidget);
        expect(find.byType(ErrorBoundaryWidget), findsOneWidget);
      });

      testWidgets('real SafeWidget creates proper widget tree', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const SafeWidget(
              fallback: Text('Safe fallback'),
              child: WorkingWidget(),
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('Working widget'), findsOneWidget);
        expect(find.byType(SafeWidget), findsOneWidget);
        expect(find.byType(ErrorBoundaryWidget), findsOneWidget);
      });
    });
  });
}
