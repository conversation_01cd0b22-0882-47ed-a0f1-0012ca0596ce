#!/bin/bash

# Strict Analysis Script - Match Codacy's Strictness
# Makes local Flutter tools catch the same issues as Codacy
# Usage: ./scripts/analyze-strict.sh

set -e

echo "🔍 Running strict analysis to match Codacy standards..."
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 1. Run Flutter analyze with strict mode
echo "📋 Step 1: Running Flutter analyze..."
if command_exists flutter; then
    flutter analyze --fatal-infos
    echo "✅ Flutter analyze completed"
else
    echo "❌ Flutter not found"
    exit 1
fi

echo ""

# 2. Run dart analyze for more detailed analysis
echo "📋 Step 2: Running Dart analyze..."
if command_exists dart; then
    dart analyze --fatal-infos
    echo "✅ Dart analyze completed"
else
    echo "❌ Dart not found"
fi

echo ""

# 3. Check formatting
echo "📋 Step 3: Checking code formatting..."
if dart format --set-exit-if-changed . > /dev/null 2>&1; then
    echo "✅ Code formatting is correct"
else
    echo "⚠️  Code formatting issues found. Run 'dart format .' to fix"
fi

echo ""

# 4. Run custom linting for trailing commas (if needed)
echo "📋 Step 4: Checking for trailing comma issues..."
echo "💡 If you see trailing comma issues in Codacy but not here,"
echo "💡 it means your analysis_options.yaml excludes test files"
echo "💡 while Codacy analyzes them."

echo ""
echo "✅ Strict analysis complete!"
echo ""
echo "📊 To match Codacy exactly:"
echo "   - Codacy: ./.codacy/analyze-lib.sh (business logic only)"
echo "   - Local:  ./scripts/analyze-strict.sh (this script)"
echo ""
echo "💡 If results differ, check .codacy.yaml exclusions"
