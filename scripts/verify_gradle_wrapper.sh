#!/bin/bash

# Gradle Wrapper Verification Script
# This script verifies that all Gradle wrapper files are present and functional

set -e

echo "🔍 Verifying Gradle wrapper setup..."

# Check if all required files exist
REQUIRED_FILES=(
    "android/gradlew"
    "android/gradlew.bat"
    "android/gradle/wrapper/gradle-wrapper.jar"
    "android/gradle/wrapper/gradle-wrapper.properties"
)

echo "📋 Checking required files:"
for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file - Found"
    else
        echo "❌ $file - Missing"
        exit 1
    fi
done

# Check if gradlew is executable
if [ -x "android/gradlew" ]; then
    echo "✅ android/gradlew - Executable"
else
    echo "⚠️ android/gradlew - Not executable, fixing..."
    chmod +x android/gradlew
    echo "✅ android/gradlew - Made executable"
fi

# Test Gradle wrapper functionality
echo "🧪 Testing Gradle wrapper functionality..."
cd android
if ./gradlew --version > /dev/null 2>&1; then
    echo "✅ Gradle wrapper is functional"
    ./gradlew --version | head -5
else
    echo "❌ Gradle wrapper test failed"
    exit 1
fi

echo "🎉 All Gradle wrapper files are present and functional!"
