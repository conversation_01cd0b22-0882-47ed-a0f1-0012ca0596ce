#!/bin/bash

# Start Firebase Emulators for Testing
# This script starts the Firebase emulator suite for local testing

set -e

echo "🔥 Starting Firebase Emulators for Testing..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "npm install -g firebase-tools"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "firebase.json" ]; then
    echo "❌ firebase.json not found. Please run this script from the project root."
    exit 1
fi

# Start emulators
echo "🚀 Starting Firebase Auth and Firestore emulators..."
echo "📱 Auth Emulator: http://localhost:9099"
echo "🗄️  Firestore Emulator: http://localhost:8080"
echo "🎛️  Emulator UI: http://localhost:4000"
echo ""
echo "Press Ctrl+C to stop the emulators"
echo ""

# Start emulators with import/export for data persistence
firebase emulators:start --import=./emulator-data --export-on-exit=./emulator-data
