#!/bin/bash

# Setup iOS Schemes for BudApp
# This script creates the necessary build configurations and schemes for iOS

set -e

echo "Setting up iOS schemes for BudApp..."

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    echo "Error: This script must be run from the Flutter project root directory"
    exit 1
fi

# Check if Xcode is installed
if ! command -v xcodebuild &> /dev/null; then
    echo "Error: Xcode command line tools are not installed"
    echo "Please install Xcode and run: xcode-select --install"
    exit 1
fi

PROJECT_PATH="ios/Runner.xcodeproj"

echo "Creating build configurations..."

# Add Dev build configuration
xcodebuild -project "$PROJECT_PATH" -list > /dev/null 2>&1

echo "Build configurations and schemes need to be created manually in Xcode."
echo "Please follow the instructions in docs/IOS_SETUP.md"

echo ""
echo "Quick setup steps:"
echo "1. Open ios/Runner.xcodeproj in Xcode"
echo "2. Select the Runner project in the navigator"
echo "3. Go to the Info tab"
echo "4. Duplicate the Debug configuration and rename it to 'Dev'"
echo "5. Duplicate the Release configuration and rename it to 'Staging'"
echo "6. Duplicate the Release configuration and rename it to 'Prod'"
echo "7. For each configuration, set the Configuration File to the corresponding .xcconfig file:"
echo "   - Dev: ios/Flutter/Dev.xcconfig"
echo "   - Staging: ios/Flutter/Staging.xcconfig"
echo "   - Prod: ios/Flutter/Prod.xcconfig"
echo "8. Create schemes for each configuration"
echo ""
echo "For detailed instructions, see docs/IOS_SETUP.md"
