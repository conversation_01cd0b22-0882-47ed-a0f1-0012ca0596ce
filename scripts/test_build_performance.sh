#!/bin/bash

# Test Build Performance Script
# This script helps test the build performance locally before pushing to CI

set -e

echo "🚀 Testing build performance locally..."

# Clean everything first
echo "🧹 Cleaning build artifacts..."
flutter clean
cd android
./gradlew clean
cd ..

echo "📦 Getting dependencies..."
flutter pub get

echo "🔥 Pre-warming Gradle daemon..."
cd android
./gradlew --version
./gradlew tasks --all > /dev/null 2>&1 || true
cd ..

echo "🏗️ Building APK with timing..."
time flutter build apk --flavor dev --dart-define=FLAVOR=dev --release --verbose

echo "✅ Build completed! Check the timing above."
echo "💡 If build takes more than 10 minutes locally, it will likely timeout in CI."
