#!/bin/bash

# Fix all unpinned GitHub Actions in workflow files
# This script pins all actions to their latest stable commit SHAs

WORKFLOW_FILE=".github/workflows/code-quality.yml"

echo "🔒 Pinning all GitHub Actions to commit SHAs for security..."

# Define the mappings of action@version to action@commit-sha
declare -A ACTION_PINS=(
    ["actions/checkout@v4"]="actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7"
    ["actions/checkout@v3"]="actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3.6.0"
    ["subosito/flutter-action@v2"]="subosito/flutter-action@48cafc24713cca54bbe03cdc3a423187d413aafa # v2.18.0"
    ["codecov/codecov-action@v4"]="codecov/codecov-action@b9fd7d16f6d7d1b5d2bec1a2887e65ceed900238 # v4.6.0"
    ["actions/upload-artifact@v4"]="actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0"
    ["actions/download-artifact@v4"]="actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8"
)

# Make backup
cp "$WORKFLOW_FILE" "${WORKFLOW_FILE}.backup"

# Apply each replacement
for pattern in "${!ACTION_PINS[@]}"; do
    replacement="${ACTION_PINS[$pattern]}"
    if grep -q "$pattern" "$WORKFLOW_FILE"; then
        echo "📌 Pinning: $pattern -> $replacement"
        sed -i.tmp "s|$pattern|$replacement|g" "$WORKFLOW_FILE"
        rm "${WORKFLOW_FILE}.tmp"
    fi
done

echo "✅ All actions have been pinned to commit SHAs"
echo "📋 Summary of changes:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Show all pinned actions
grep -n "uses:" "$WORKFLOW_FILE" | head -20

echo ""
echo "🔍 Verify no unpinned actions remain:"
if grep -n "@v[0-9]" "$WORKFLOW_FILE" | grep -v "#"; then
    echo "❌ Found unpinned actions above"
    exit 1
else
    echo "✅ All actions are properly pinned"
fi
