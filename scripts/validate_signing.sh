#!/bin/bash

# Android Build Signing Validation Script for BudApp
# Usage: ./scripts/validate_signing.sh <apk_or_aab_file> [environment]
# Example: ./scripts/validate_signing.sh build/app/outputs/flutter-apk/app-dev-release.apk dev

set -e

BUILD_FILE=$1
ENVIRONMENT=${2:-"unknown"}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_usage() {
    echo "Usage: $0 <apk_or_aab_file> [environment]"
    echo "       $0 --check-keystores"
    echo ""
    echo "Validates Android app signing and security for Bud<PERSON><PERSON> builds."
    echo ""
    echo "Arguments:"
    echo "  apk_or_aab_file  Path to the APK or AAB file to validate"
    echo "  environment      Environment name (dev, staging, prod) - optional"
    echo ""
    echo "Options:"
    echo "  --check-keystores  Check keystore security without validating a build file"
    echo ""
    echo "Examples:"
    echo "  $0 build/app/outputs/flutter-apk/app-dev-release.apk dev"
    echo "  $0 build/app/outputs/bundle/prodRelease/app-prod-release.aab prod"
    echo "  $0 --check-keystores"
}

check_keystore_security() {
    echo -e "${BLUE}🔍 Checking keystore security...${NC}"
    
    # Check if keystores are properly excluded from version control
    echo -e "${YELLOW}Checking version control exclusion...${NC}"
    
    if [ -f "$PROJECT_ROOT/.gitignore" ]; then
        if grep -q "\.jks\|\.keystore" "$PROJECT_ROOT/.gitignore" || grep -q "keystore/" "$PROJECT_ROOT/.gitignore"; then
            echo -e "${GREEN}✅ Keystores properly excluded in root .gitignore${NC}"
        else
            echo -e "${YELLOW}⚠️  Warning: Keystore patterns not found in root .gitignore${NC}"
        fi
    fi
    
    if [ -f "$PROJECT_ROOT/android/keystore/.gitignore" ]; then
        echo -e "${GREEN}✅ Keystore directory has dedicated .gitignore${NC}"
    else
        echo -e "${RED}❌ Missing .gitignore in keystore directory${NC}"
    fi
    
    # Check for keystore files that are tracked by git (they should not be committed)
    echo -e "${YELLOW}Checking for committed keystores...${NC}"
    
    # Find keystore files and check if they are tracked by git
    KEYSTORE_FILES=$(find "$PROJECT_ROOT" -name "*.jks" -o -name "*.keystore" | grep -v ".git" | grep -v "/build/")
    
    if [ ! -z "$KEYSTORE_FILES" ]; then
        TRACKED_KEYSTORES=""
        while IFS= read -r keystore_file; do
            if [ -f "$keystore_file" ]; then
                # Check if file is tracked by git
                if git ls-files --error-unmatch "$keystore_file" >/dev/null 2>&1; then
                    TRACKED_KEYSTORES="$TRACKED_KEYSTORES\n  $keystore_file"
                fi
            fi
        done <<< "$KEYSTORE_FILES"
        
        if [ ! -z "$TRACKED_KEYSTORES" ]; then
            echo -e "${RED}❌ ERROR: Keystore files are tracked by git and will be committed!${NC}"
            echo -e "${RED}Tracked keystore files:${TRACKED_KEYSTORES}${NC}"
            return 1
        else
            echo -e "${GREEN}✅ Keystore files found but properly ignored by git${NC}"
        fi
    else
        echo -e "${GREEN}✅ No keystore files found in repository${NC}"
    fi
    
    # Check gradle.properties for exposed passwords
    echo -e "${YELLOW}Checking gradle.properties for security issues...${NC}"
    
    if [ -f "$PROJECT_ROOT/android/gradle.properties" ]; then
        # Look for uncommented password lines
        if grep -E "^[^#]*PASSWORD.*=" "$PROJECT_ROOT/android/gradle.properties" >/dev/null 2>&1; then
            echo -e "${YELLOW}⚠️  WARNING: Passwords found in gradle.properties${NC}"
            echo "   These should only be used for local development"
            echo "   Production builds should use environment variables"
        else
            echo -e "${GREEN}✅ No exposed passwords in gradle.properties${NC}"
        fi
    fi
    
    echo -e "${GREEN}🔐 Keystore security check completed${NC}"
    return 0
}

validate_build_signature() {
    local build_file="$1"
    local environment="$2"
    
    echo -e "${BLUE}🔍 Validating build signature for $environment environment...${NC}"
    echo -e "${YELLOW}File: $build_file${NC}"
    
    # Check if file exists
    if [ ! -f "$build_file" ]; then
        echo -e "${RED}❌ ERROR: Build file not found: $build_file${NC}"
        return 1
    fi
    
    # Check file extension
    case "$build_file" in
        *.apk)
            echo -e "${BLUE}Validating APK file...${NC}"
            ;;
        *.aab)
            echo -e "${BLUE}Validating AAB file...${NC}"
            ;;
        *)
            echo -e "${RED}❌ ERROR: Unsupported file type. Only .apk and .aab files are supported.${NC}"
            return 1
            ;;
    esac
    
    # Verify the signature
    echo -e "${YELLOW}Verifying signature...${NC}"
    
    if jarsigner -verify "$build_file" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ File is properly signed${NC}"
    else
        echo -e "${RED}❌ ERROR: File is not properly signed or signature is invalid${NC}"
        return 1
    fi
    
    # Get detailed signature information
    echo -e "${YELLOW}Getting signature details...${NC}"
    
    SIGNATURE_INFO=$(jarsigner -verify -verbose -certs "$build_file" 2>/dev/null | head -20)
    
    # Check for debug signature
    if echo "$SIGNATURE_INFO" | grep -qi "debug\|android debug"; then
        echo -e "${RED}❌ CRITICAL ERROR: Debug signature detected!${NC}"
        echo -e "${RED}   This build is using debug keys and MUST NOT be used for production${NC}"
        return 1
    else
        echo -e "${GREEN}✅ No debug signature detected${NC}"
    fi
    
    # Extract certificate information
    CERT_SUBJECT=$(echo "$SIGNATURE_INFO" | grep -i "X.509" | head -1)
    
    if [ ! -z "$CERT_SUBJECT" ]; then
        echo -e "${BLUE}Certificate subject:${NC}"
        echo "   $CERT_SUBJECT"
        
        # Validate environment-specific certificate
        case "$environment" in
            "dev")
                if echo "$CERT_SUBJECT" | grep -qi "budapp.*dev\|dev.*budapp"; then
                    echo -e "${GREEN}✅ Certificate matches development environment${NC}"
                else
                    echo -e "${YELLOW}⚠️  Warning: Certificate may not match development environment${NC}"
                fi
                ;;
            "staging")
                if echo "$CERT_SUBJECT" | grep -qi "budapp.*staging\|staging.*budapp"; then
                    echo -e "${GREEN}✅ Certificate matches staging environment${NC}"
                else
                    echo -e "${YELLOW}⚠️  Warning: Certificate may not match staging environment${NC}"
                fi
                ;;
            "prod")
                if echo "$CERT_SUBJECT" | grep -qi "budapp.*prod\|prod.*budapp\|budapp"; then
                    echo -e "${GREEN}✅ Certificate matches production environment${NC}"
                else
                    echo -e "${YELLOW}⚠️  Warning: Certificate may not match production environment${NC}"
                fi
                ;;
        esac
    fi
    
    # Check signature algorithm
    SIGNATURE_ALGORITHM=$(echo "$SIGNATURE_INFO" | grep -i "signature algorithm" | head -1)
    if [ ! -z "$SIGNATURE_ALGORITHM" ]; then
        echo -e "${BLUE}Signature algorithm:${NC}"
        echo "   $SIGNATURE_ALGORITHM"
        
        if echo "$SIGNATURE_ALGORITHM" | grep -qi "sha256"; then
            echo -e "${GREEN}✅ Using secure SHA-256 signature algorithm${NC}"
        else
            echo -e "${YELLOW}⚠️  Warning: Not using SHA-256 signature algorithm${NC}"
        fi
    fi
    
    # File size and basic info
    FILE_SIZE=$(ls -lh "$build_file" | awk '{print $5}')
    echo -e "${BLUE}File size:${NC} $FILE_SIZE"
    
    echo -e "${GREEN}🔐 Build signature validation completed successfully${NC}"
    return 0
}

main() {
    if [ $# -eq 0 ]; then
        echo -e "${RED}Error: No arguments provided${NC}"
        print_usage
        exit 1
    fi
    
    case "$1" in
        "--check-keystores")
            check_keystore_security
            exit $?
            ;;
        "-h"|"--help")
            print_usage
            exit 0
            ;;
        *)
            if [ -z "$BUILD_FILE" ]; then
                echo -e "${RED}Error: Build file not specified${NC}"
                print_usage
                exit 1
            fi
            
            echo -e "${BLUE}🛡️  BudApp Build Signature Validation${NC}"
            echo ""
            
            # Run keystore security check first
            check_keystore_security
            KEYSTORE_CHECK_RESULT=$?
            
            echo ""
            
            # Validate the build signature
            validate_build_signature "$BUILD_FILE" "$ENVIRONMENT"
            BUILD_CHECK_RESULT=$?
            
            echo ""
            
            # Final summary
            if [ $KEYSTORE_CHECK_RESULT -eq 0 ] && [ $BUILD_CHECK_RESULT -eq 0 ]; then
                echo -e "${GREEN}🎉 All validation checks passed!${NC}"
                echo -e "${GREEN}   The build is ready for distribution${NC}"
                exit 0
            else
                echo -e "${RED}❌ Validation failed!${NC}"
                echo -e "${RED}   Please fix the issues before distributing this build${NC}"
                exit 1
            fi
            ;;
    esac
}

main "$@"