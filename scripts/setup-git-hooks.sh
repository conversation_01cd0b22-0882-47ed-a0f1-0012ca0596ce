#!/bin/bash

# BudApp Git Hooks Setup Script
# This script installs and configures Git hooks for code quality enforcement

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in a git repository
check_git_repo() {
    if [ ! -d ".git" ]; then
        print_error "Not in a Git repository"
        exit 1
    fi
}

# Check if we're in the project root
check_project_root() {
    if [ ! -f "pubspec.yaml" ]; then
        print_error "Not in the Flutter project root directory"
        exit 1
    fi
}

# Install pre-commit hook
install_pre_commit_hook() {
    local hook_source="scripts/pre-commit-hook.sh"
    local hook_target=".git/hooks/pre-commit"
    
    if [ ! -f "$hook_source" ]; then
        print_error "Pre-commit hook source not found: $hook_source"
        exit 1
    fi
    
    # Backup existing hook if it exists
    if [ -f "$hook_target" ]; then
        print_warning "Existing pre-commit hook found"
        cp "$hook_target" "$hook_target.backup.$(date +%Y%m%d_%H%M%S)"
        print_status "Backed up existing hook to $hook_target.backup.*"
    fi
    
    # Copy and make executable
    cp "$hook_source" "$hook_target"
    chmod +x "$hook_target"
    
    print_success "Pre-commit hook installed successfully"
}

# Install commit-msg hook for conventional commits (optional)
install_commit_msg_hook() {
    local hook_target=".git/hooks/commit-msg"
    
    # Create a simple commit message validation hook
    cat > "$hook_target" << 'EOF'
#!/bin/bash

# Commit message validation for conventional commits
# Format: type(scope): description
# Example: feat(auth): add biometric authentication

commit_regex='^(feat|fix|docs|style|refactor|test|chore|perf|ci|build)(\(.+\))?: .{1,50}'

error_msg="Invalid commit message format!

Commit message should follow conventional commits format:
  type(scope): description

Types:
  feat:     A new feature
  fix:      A bug fix
  docs:     Documentation only changes
  style:    Changes that do not affect the meaning of the code
  refactor: A code change that neither fixes a bug nor adds a feature
  test:     Adding missing tests or correcting existing tests
  chore:    Changes to the build process or auxiliary tools
  perf:     A code change that improves performance
  ci:       Changes to CI configuration files and scripts
  build:    Changes that affect the build system or external dependencies

Examples:
  feat(auth): add biometric authentication
  fix(transactions): resolve duplicate transaction issue
  docs(readme): update installation instructions
  refactor(providers): simplify state management logic"

if ! grep -qE "$commit_regex" "$1"; then
    echo "$error_msg" >&2
    exit 1
fi
EOF
    
    chmod +x "$hook_target"
    print_success "Commit message validation hook installed"
}

# Install pre-push hook for additional checks
install_pre_push_hook() {
    local hook_target=".git/hooks/pre-push"
    
    cat > "$hook_target" << 'EOF'
#!/bin/bash

# Pre-push hook for additional quality checks
# This runs more comprehensive checks before pushing to remote

set -e

echo "🚀 Running pre-push quality checks..."

# Check if we're pushing to main/development branches
protected_branches="main development"
current_branch=$(git rev-parse --abbrev-ref HEAD)

for branch in $protected_branches; do
    if [ "$current_branch" = "$branch" ]; then
        echo "⚠️  Pushing to protected branch: $branch"
        echo "🧪 Running comprehensive test suite..."
        
        # Run full test suite
        if ! flutter test --coverage; then
            echo "❌ Tests failed! Push aborted."
            exit 1
        fi
        
        # Run static analysis
        if ! flutter analyze; then
            echo "❌ Static analysis failed! Push aborted."
            exit 1
        fi
        
        echo "✅ All pre-push checks passed for protected branch"
        break
    fi
done

echo "✅ Pre-push checks completed successfully"
EOF
    
    chmod +x "$hook_target"
    print_success "Pre-push hook installed"
}

# Create git hook configuration
create_hook_config() {
    local config_file=".git/hooks/config"
    
    cat > "$config_file" << EOF
# BudApp Git Hooks Configuration
# Generated on $(date)

# Pre-commit hook settings
SKIP_TESTS=false          # Set to true to skip tests in pre-commit
SKIP_FORMATTING=false     # Set to true to skip formatting checks
SKIP_ANALYSIS=false       # Set to true to skip static analysis

# Commit message settings
ENFORCE_CONVENTIONAL_COMMITS=true

# Pre-push settings
COMPREHENSIVE_TESTS_ON_PROTECTED_BRANCHES=true
EOF
    
    print_success "Hook configuration created: $config_file"
}

# Main installation function
main() {
    print_status "🔧 Setting up BudApp Git hooks..."
    echo ""
    
    # Validation
    check_git_repo
    check_project_root
    
    # Create hooks directory if it doesn't exist
    mkdir -p .git/hooks
    
    # Install hooks
    print_status "Installing pre-commit hook..."
    install_pre_commit_hook
    echo ""
    
    print_status "Installing commit message validation hook..."
    install_commit_msg_hook
    echo ""
    
    print_status "Installing pre-push hook..."
    install_pre_push_hook
    echo ""
    
    print_status "Creating hook configuration..."
    create_hook_config
    echo ""
    
    # Final instructions
    print_success "🎉 Git hooks setup completed successfully!"
    echo ""
    print_status "📋 What was installed:"
    print_status "  ✅ Pre-commit hook (code quality checks)"
    print_status "  ✅ Commit message validation (conventional commits)"
    print_status "  ✅ Pre-push hook (comprehensive checks for protected branches)"
    print_status "  ✅ Hook configuration file"
    echo ""
    print_status "🔧 Configuration:"
    print_status "  - Edit .git/hooks/config to customize hook behavior"
    print_status "  - Set SKIP_TESTS=true to skip tests in pre-commit"
    print_status "  - Hooks will run automatically on git commit/push"
    echo ""
    print_status "📖 Usage:"
    print_status "  - Hooks run automatically during git operations"
    print_status "  - To bypass hooks temporarily: git commit --no-verify"
    print_status "  - To test pre-commit hook: .git/hooks/pre-commit"
    echo ""
    print_warning "⚠️  Important:"
    print_warning "  - Hooks are local to your repository"
    print_warning "  - Team members need to run this script individually"
    print_warning "  - Consider adding this to your onboarding documentation"
    echo ""
}

# Handle script interruption
trap 'print_error "Hook setup interrupted"; exit 1' INT TERM

# Run main function
main "$@"
