#!/bin/bash

# Script to select the appropriate Firebase configuration file for Android builds
# Usage: ./scripts/select_firebase_config.sh <flavor>
# Where flavor is one of: dev, staging, prod

set -e

FLAVOR=$1
ANDROID_APP_DIR="android/app"

if [ -z "$FLAVOR" ]; then
    echo "Usage: $0 <flavor>"
    echo "Available flavors: dev, staging, prod"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    echo "Error: This script must be run from the Flutter project root directory"
    exit 1
fi

case $FLAVOR in
    "dev")
        SOURCE_FILE="$ANDROID_APP_DIR/dev.google-services.json"
        ;;
    "staging")
        SOURCE_FILE="$ANDROID_APP_DIR/staging.google-services.json"
        ;;
    "prod")
        SOURCE_FILE="$ANDROID_APP_DIR/prod.google-services.json"
        ;;
    *)
        echo "Error: Unknown flavor '$FLAVOR'"
        echo "Available flavors: dev, staging, prod"
        exit 1
        ;;
esac

TARGET_FILE="$ANDROID_APP_DIR/google-services.json"

# Check if source file exists
if [ ! -f "$SOURCE_FILE" ]; then
    echo "Error: Firebase configuration file '$SOURCE_FILE' not found"
    exit 1
fi

# Copy the appropriate configuration file
echo "Selecting Firebase configuration for $FLAVOR environment..."
cp "$SOURCE_FILE" "$TARGET_FILE"
echo "✓ Copied $SOURCE_FILE to $TARGET_FILE"
