#!/bin/bash

# Android Keystore Generation Script for BudApp
# Usage: ./scripts/generate_keystore.sh <environment>
# Where environment is one of: dev, staging, prod

set -e

ENVIRONMENT=$1
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
KEYSTORE_DIR="$PROJECT_ROOT/android/keystore"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_usage() {
    echo "Usage: $0 <environment>"
    echo "Available environments: dev, staging, prod"
    echo ""
    echo "This script generates Android signing keystores for the specified environment."
    echo "Generated keystores are stored in android/keystore/ directory."
    echo ""
    echo "Example:"
    echo "  $0 dev      # Generate development keystore"
    echo "  $0 staging  # Generate staging keystore"
    echo "  $0 prod     # Generate production keystore"
}

if [ -z "$ENVIRONMENT" ]; then
    echo -e "${RED}Error: Environment not specified${NC}"
    print_usage
    exit 1
fi

# Validate environment
case $ENVIRONMENT in
    "dev"|"staging"|"prod")
        ;;
    *)
        echo -e "${RED}Error: Unknown environment '$ENVIRONMENT'${NC}"
        print_usage
        exit 1
        ;;
esac

# Set environment-specific variables
KEYSTORE_FILE="$KEYSTORE_DIR/${ENVIRONMENT}-release.jks"
KEY_ALIAS="${ENVIRONMENT}-key"

echo -e "${BLUE}🔐 Generating Android Keystore for $ENVIRONMENT environment${NC}"
echo ""

# Check if keystore directory exists
if [ ! -d "$KEYSTORE_DIR" ]; then
    echo -e "${YELLOW}Creating keystore directory...${NC}"
    mkdir -p "$KEYSTORE_DIR"
fi

# Check if keystore already exists
if [ -f "$KEYSTORE_FILE" ]; then
    echo -e "${YELLOW}⚠️  Keystore already exists: $KEYSTORE_FILE${NC}"
    read -p "Do you want to overwrite it? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}Operation cancelled.${NC}"
        exit 0
    fi
    echo -e "${YELLOW}Backing up existing keystore...${NC}"
    cp "$KEYSTORE_FILE" "${KEYSTORE_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
fi

# Get certificate information
echo -e "${BLUE}Please provide certificate information:${NC}"
echo -e "${YELLOW}Note: This information will be embedded in the certificate${NC}"
echo ""

ENV_TITLE=$(echo "$ENVIRONMENT" | sed 's/.*/\u&/')
read -p "First and Last Name [BudApp $ENV_TITLE]: " CN
CN=${CN:-"BudApp $ENV_TITLE"}

read -p "Organizational Unit [Development Team]: " OU
OU=${OU:-"Development Team"}

read -p "Organization [Digitau]: " O
O=${O:-"Digitau"}

read -p "City or Locality []: " L
L=${L:-""}

read -p "State or Province []: " ST  
ST=${ST:-""}

read -p "Country Code (2 letters) [US]: " C
C=${C:-"US"}

# Construct the distinguished name
DNAME="CN=$CN, OU=$OU, O=$O"
if [ ! -z "$L" ]; then
    DNAME="$DNAME, L=$L"
fi
if [ ! -z "$ST" ]; then
    DNAME="$DNAME, ST=$ST"
fi
DNAME="$DNAME, C=$C"

echo ""
echo -e "${BLUE}Certificate Distinguished Name:${NC}"
echo -e "${YELLOW}$DNAME${NC}"
echo ""

# Get passwords
echo -e "${BLUE}Keystore Security Configuration:${NC}"
while true; do
    read -s -p "Enter keystore password: " KEYSTORE_PASSWORD
    echo
    read -s -p "Confirm keystore password: " KEYSTORE_PASSWORD_CONFIRM
    echo
    
    if [ "$KEYSTORE_PASSWORD" = "$KEYSTORE_PASSWORD_CONFIRM" ]; then
        break
    else
        echo -e "${RED}Passwords do not match. Please try again.${NC}"
    fi
done

while true; do
    read -s -p "Enter key password: " KEY_PASSWORD
    echo
    read -s -p "Confirm key password: " KEY_PASSWORD_CONFIRM
    echo
    
    if [ "$KEY_PASSWORD" = "$KEY_PASSWORD_CONFIRM" ]; then
        break
    else
        echo -e "${RED}Passwords do not match. Please try again.${NC}"
    fi
done

echo ""
echo -e "${BLUE}🔨 Generating keystore...${NC}"

# Generate the keystore
keytool -genkey -v \
    -keystore "$KEYSTORE_FILE" \
    -keyalg RSA \
    -keysize 2048 \
    -validity 10000 \
    -alias "$KEY_ALIAS" \
    -dname "$DNAME" \
    -storepass "$KEYSTORE_PASSWORD" \
    -keypass "$KEY_PASSWORD"

if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}✅ Keystore generated successfully!${NC}"
    echo ""
    echo -e "${BLUE}Keystore Information:${NC}"
    echo -e "${YELLOW}File:${NC} $KEYSTORE_FILE"
    echo -e "${YELLOW}Alias:${NC} $KEY_ALIAS"
    echo -e "${YELLOW}Algorithm:${NC} RSA 2048-bit"
    echo -e "${YELLOW}Validity:${NC} 10000 days (~27 years)"
    echo ""
    
    # Display keystore details
    echo -e "${BLUE}Certificate Details:${NC}"
    keytool -list -v -keystore "$KEYSTORE_FILE" -alias "$KEY_ALIAS" -storepass "$KEYSTORE_PASSWORD"
    
    echo ""
    echo -e "${BLUE}📋 Next Steps:${NC}"
    ENV_UPPER=$(echo "$ENVIRONMENT" | tr '[:lower:]' '[:upper:]')
    echo -e "${YELLOW}1.${NC} Update your local gradle.properties with signing configuration:"
    echo "   ${ENV_UPPER}_KEYSTORE_FILE=keystore/${ENVIRONMENT}-release.jks"
    echo "   ${ENV_UPPER}_KEY_ALIAS=$KEY_ALIAS"
    echo "   ${ENV_UPPER}_KEYSTORE_PASSWORD=<your-keystore-password>"
    echo "   ${ENV_UPPER}_KEY_PASSWORD=<your-key-password>"
    echo ""
    echo -e "${YELLOW}2.${NC} For CI/CD, encode the keystore for GitHub Secrets:"
    echo "   base64 -i $KEYSTORE_FILE | pbcopy"
    echo ""
    echo -e "${YELLOW}3.${NC} Add the following GitHub Secrets:"
    echo "   ${ENV_UPPER}_KEYSTORE_FILE - Base64 encoded keystore file"
    echo "   ${ENV_UPPER}_KEY_ALIAS - $KEY_ALIAS"
    echo "   ${ENV_UPPER}_KEYSTORE_PASSWORD - Your keystore password"
    echo "   ${ENV_UPPER}_KEY_PASSWORD - Your key password"
    echo ""
    echo -e "${RED}⚠️  IMPORTANT:${NC}"
    echo -e "${RED}   • Store passwords securely (password manager)${NC}"
    echo -e "${RED}   • Never commit keystore files to version control${NC}"
    echo -e "${RED}   • Keep secure backups of this keystore${NC}"
    echo -e "${RED}   • This keystore is required for all future app updates${NC}"
    
else
    echo -e "${RED}❌ Failed to generate keystore${NC}"
    exit 1
fi