#!/bin/bash

# Test Coverage Script with Generated File Exclusions
# This script runs Flutter tests with coverage and excludes generated files

set -e

echo "🧪 Running Flutter tests with coverage..."

# Run Flutter tests with coverage
flutter test --coverage

echo "📊 Excluding generated files from coverage report..."

# Remove generated files from coverage
lcov --remove coverage/lcov.info \
  '**/*.g.dart' \
  '**/*.freezed.dart' \
  '**/generated_plugin_registrant.dart' \
  --output-file coverage/lcov_clean.info

echo "📈 Generating clean coverage report..."

# Generate HTML report from clean coverage
genhtml coverage/lcov_clean.info \
  --output-directory coverage/html_clean \
  --title "BudApp Test Coverage (Excluding Generated Files)"

echo "✅ Clean coverage report generated!"
echo "📁 HTML report: coverage/html_clean/index.html"

# Display coverage summary
echo "📊 Coverage Summary (Excluding Generated Files):"
lcov --summary coverage/lcov_clean.info
