#!/bin/bash

# Run Flutter tests with Firebase emulators
# This script starts emulators, runs tests, and cleans up

set -e

echo "🧪 Running Flutter Tests with Firebase Emulators..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "npm install -g firebase-tools"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "firebase.json" ]; then
    echo "❌ firebase.json not found. Please run this script from the project root."
    exit 1
fi

# Function to cleanup emulators
cleanup() {
    echo "🧹 Cleaning up emulators..."
    firebase emulators:exec --only auth,firestore "echo 'Stopping emulators...'" || true
}

# Set trap to cleanup on exit
trap cleanup EXIT

echo "🚀 Starting Firebase emulators..."

# Start emulators and run tests
firebase emulators:exec --only auth,firestore "flutter test test/email_verification_integration_test.dart"

echo "✅ Tests completed!"
