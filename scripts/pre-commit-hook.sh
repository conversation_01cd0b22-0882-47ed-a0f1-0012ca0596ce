#!/bin/bash

# BudApp Pre-commit Hook
# This script runs comprehensive code quality checks before allowing commits
# 
# To install this hook, run:
# cp scripts/pre-commit-hook.sh .git/hooks/pre-commit
# chmod +x .git/hooks/pre-commit

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Flutter is available
check_flutter() {
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi
}

# Function to check if we're in a Flutter project
check_flutter_project() {
    if [ ! -f "pubspec.yaml" ]; then
        print_error "Not in a Flutter project directory"
        exit 1
    fi
}

# Function to get list of staged Dart files
get_staged_dart_files() {
    git diff --cached --name-only --diff-filter=ACM | grep '\.dart$' || true
}

# Main pre-commit checks
main() {
    print_status "🚀 Running BudApp pre-commit quality checks..."
    echo ""

    # Basic environment checks
    check_flutter
    check_flutter_project

    # Get staged files
    STAGED_DART_FILES=$(get_staged_dart_files)
    
    if [ -z "$STAGED_DART_FILES" ]; then
        print_status "No Dart files staged for commit. Skipping Flutter-specific checks."
        exit 0
    fi

    print_status "Found $(echo "$STAGED_DART_FILES" | wc -l) staged Dart files"
    echo ""

    # 1. Check dependencies
    print_status "📦 Checking Flutter dependencies..."
    if ! flutter pub get > /dev/null 2>&1; then
        print_error "Failed to get Flutter dependencies"
        exit 1
    fi
    print_success "Dependencies are up to date"
    echo ""

    # 2. Code formatting check
    print_status "🎨 Checking code formatting..."
    if ! dart format --set-exit-if-changed . > /dev/null 2>&1; then
        print_error "Code formatting issues found!"
        print_status "Running dart format to fix formatting..."
        dart format .
        print_warning "Code has been formatted. Please review and stage the changes."
        exit 1
    fi
    print_success "Code formatting is consistent"
    echo ""

    # 3. Static analysis
    print_status "🔍 Running static analysis..."
    ANALYSIS_OUTPUT=$(flutter analyze --no-pub 2>&1)
    ANALYSIS_EXIT_CODE=$?
    
    if [ $ANALYSIS_EXIT_CODE -ne 0 ]; then
        print_error "Static analysis failed!"
        echo "$ANALYSIS_OUTPUT"
        print_status "Please fix the analysis issues before committing"
        exit 1
    fi
    print_success "Static analysis passed"
    echo ""

    # 4. Import organization check
    print_status "📦 Checking import organization..."
    IMPORT_ISSUES=0
    
    # Check for relative imports in lib/
    if echo "$STAGED_DART_FILES" | grep "^lib/" | xargs grep -l "import '\.\." 2>/dev/null; then
        print_warning "Relative imports found in lib/ directory"
        IMPORT_ISSUES=$((IMPORT_ISSUES + 1))
    fi
    
    if [ $IMPORT_ISSUES -eq 0 ]; then
        print_success "Import organization looks good"
    else
        print_warning "Import organization issues found (non-blocking)"
    fi
    echo ""

    # 5. Run tests (optional, can be disabled for faster commits)
    if [ "${SKIP_TESTS:-false}" != "true" ]; then
        print_status "🧪 Running tests..."
        if ! flutter test --no-pub > /dev/null 2>&1; then
            print_error "Tests failed!"
            print_status "Run 'flutter test' to see detailed test results"
            print_status "To skip tests in pre-commit, set SKIP_TESTS=true"
            exit 1
        fi
        print_success "All tests passed"
        echo ""
    else
        print_warning "Tests skipped (SKIP_TESTS=true)"
        echo ""
    fi

    # 6. Check for common issues
    print_status "🔍 Checking for common issues..."
    
    # Check for TODO/FIXME in staged files
    TODO_COUNT=0
    for file in $STAGED_DART_FILES; do
        if [ -f "$file" ]; then
            TODO_IN_FILE=$(grep -c "TODO\|FIXME" "$file" 2>/dev/null || echo "0")
            TODO_COUNT=$((TODO_COUNT + TODO_IN_FILE))
        fi
    done
    
    if [ $TODO_COUNT -gt 0 ]; then
        print_warning "Found $TODO_COUNT TODO/FIXME comments in staged files"
    fi

    # Check for potential secrets
    SECRET_PATTERNS=("password.*=" "secret.*=" "key.*=" "token.*=" "api_key.*=")
    SECRETS_FOUND=0
    
    for pattern in "${SECRET_PATTERNS[@]}"; do
        for file in $STAGED_DART_FILES; do
            if [ -f "$file" ] && grep -qi "$pattern" "$file" 2>/dev/null; then
                if ! grep -q "// ignore.*secret" "$file" 2>/dev/null; then
                    print_warning "Potential hardcoded secret in $file: $pattern"
                    SECRETS_FOUND=$((SECRETS_FOUND + 1))
                fi
            fi
        done
    done
    
    if [ $SECRETS_FOUND -eq 0 ]; then
        print_success "No obvious security issues found"
    else
        print_warning "$SECRETS_FOUND potential security issues found (review recommended)"
    fi
    echo ""

    # 7. Performance checks
    print_status "⚡ Checking for performance issues..."
    PERF_ISSUES=0
    
    # Check for missing const constructors in staged files
    for file in $STAGED_DART_FILES; do
        if [ -f "$file" ]; then
            # Look for widget constructors without const
            if grep -q "class.*Widget" "$file" && grep -q "Widget.*(" "$file" && ! grep -q "const.*Widget.*(" "$file"; then
                MISSING_CONST=$(grep -c "Widget.*(" "$file" | grep -v "const " || echo "0")
                if [ "$MISSING_CONST" -gt 0 ]; then
                    PERF_ISSUES=$((PERF_ISSUES + 1))
                fi
            fi
        fi
    done
    
    if [ $PERF_ISSUES -eq 0 ]; then
        print_success "No obvious performance issues found"
    else
        print_warning "$PERF_ISSUES potential performance issues found (consider adding const constructors)"
    fi
    echo ""

    # Final summary
    print_success "🎉 All pre-commit checks passed!"
    print_status "Commit is ready to proceed"
    echo ""
    
    # Optional: Show commit statistics
    TOTAL_LINES=$(echo "$STAGED_DART_FILES" | xargs wc -l 2>/dev/null | tail -1 | awk '{print $1}' || echo "0")
    print_status "📊 Commit statistics:"
    print_status "  - Files: $(echo "$STAGED_DART_FILES" | wc -l)"
    print_status "  - Lines: $TOTAL_LINES"
    
    if [ $TODO_COUNT -gt 0 ]; then
        print_status "  - TODOs: $TODO_COUNT"
    fi
    
    echo ""
}

# Handle script interruption
trap 'print_error "Pre-commit checks interrupted"; exit 1' INT TERM

# Run main function
main "$@"
