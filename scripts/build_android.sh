#!/bin/bash

# Build script for Android with automatic Firebase configuration selection
# Usage: ./scripts/build_android.sh <flavor> [build_type]
# Where flavor is one of: dev, staging, prod
# Where build_type is one of: apk, appbundle (default: apk)

set -e

FLAVOR=$1
BUILD_TYPE=${2:-apk}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

if [ -z "$FLAVOR" ]; then
    echo "Usage: $0 <flavor> [build_type]"
    echo "Available flavors: dev, staging, prod"
    echo "Available build types: apk, appbundle"
    exit 1
fi

# Validate build type
case $BUILD_TYPE in
    "apk"|"appbundle")
        ;;
    *)
        echo "Error: Unknown build type '$BUILD_TYPE'"
        echo "Available build types: apk, appbundle"
        exit 1
        ;;
esac

echo "🔧 Building Android $BUILD_TYPE for $FLAVOR environment..."

# Change to project root
cd "$PROJECT_ROOT"

# Select the appropriate Firebase configuration
echo "📱 Selecting Firebase configuration..."
"$SCRIPT_DIR/select_firebase_config.sh" "$FLAVOR"

# Build the app
echo "🏗️  Building Flutter $BUILD_TYPE..."
if [ "$BUILD_TYPE" = "apk" ]; then
    flutter build apk --flavor "$FLAVOR" --release
    BUILD_OUTPUT="build/app/outputs/flutter-apk/app-$FLAVOR-release.apk"
else
    flutter build appbundle --flavor "$FLAVOR" --release
    BUILD_OUTPUT="build/app/outputs/bundle/${FLAVOR}Release/app-$FLAVOR-release.aab"
fi

echo "✅ Build completed successfully!"
echo "📦 Output location: $BUILD_OUTPUT"

# Validate the build signature
echo ""
echo "🔐 Validating build signature..."
if [ -f "$BUILD_OUTPUT" ]; then
    "$SCRIPT_DIR/validate_signing.sh" "$BUILD_OUTPUT" "$FLAVOR"
    VALIDATION_RESULT=$?
    
    if [ $VALIDATION_RESULT -eq 0 ]; then
        echo ""
        echo "🎉 Build validation successful!"
        echo "📋 Build Summary:"
        echo "   Environment: $FLAVOR"
        echo "   Build Type: $BUILD_TYPE"
        echo "   Output: $BUILD_OUTPUT"
        echo "   Size: $(ls -lh "$BUILD_OUTPUT" | awk '{print $5}')"
        echo ""
        echo "🚀 Your build is ready for distribution!"
    else
        echo ""
        echo "❌ Build validation failed!"
        echo "Please fix the validation issues before distributing this build."
        exit 1
    fi
else
    echo "⚠️  Warning: Build output not found at expected location: $BUILD_OUTPUT"
    echo "Skipping signature validation."
fi
