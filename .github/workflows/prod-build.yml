name: Production Build & Deploy

on:
  workflow_dispatch:
    inputs:
      build_type:
        description: 'Build type'
        required: true
        default: 'appbundle'
        type: choice
        options:
        - apk
        - appbundle
      confirmation:
        description: 'Type "PRODUCTION" to confirm production deployment'
        required: true
        type: string

env:
  FLUTTER_VERSION: '3.32.6'
  JAVA_VERSION: '17'
  ENVIRONMENT: 'production'

jobs:
  # Job 1: Pre-deployment validation
  pre-deployment-validation:
    runs-on: self-hosted
    environment: production
    steps:
    - name: Validate branch
      env:
        BRANCH_NAME: ${{ github.ref_name }}
      run: |
        echo "🔍 Validating deployment branch..."
        if [ "$BRANCH_NAME" != "production" ]; then
          echo "❌ ERROR: Production builds can only be triggered from the 'production' branch!"
          echo "Current branch: $BRANCH_NAME"
          exit 1
        fi
        echo "✅ Branch validation passed"

    - name: Validate confirmation
      env:
        USER_CONFIRMATION: ${{ github.event.inputs.confirmation }}
      run: |
        echo "🔍 Validating deployment confirmation..."
        if [ "$USER_CONFIRMATION" != "PRODUCTION" ]; then
          echo "❌ ERROR: Production deployment requires 'PRODUCTION' confirmation!"
          echo "Provided: $USER_CONFIRMATION"
          exit 1
        fi
        echo "✅ Confirmation validation passed"

    - name: Log deployment attempt
      env:
        BRANCH_NAME: ${{ github.ref_name }}
        COMMIT_SHA: ${{ github.sha }}
        GITHUB_ACTOR: ${{ github.actor }}
        BUILD_TYPE: ${{ github.event.inputs.build_type }}
      run: |
        echo "🚨 PRODUCTION DEPLOYMENT INITIATED"
        echo "====================================="
        echo "Timestamp: $(date -u)"
        echo "Branch: $BRANCH_NAME"
        echo "Commit: $COMMIT_SHA"
        echo "Actor: $GITHUB_ACTOR"
        echo "Build Type: $BUILD_TYPE"
        echo "====================================="

  # Job 2: Comprehensive testing for production
  comprehensive-test:
    runs-on: self-hosted
    environment: production
    needs: pre-deployment-validation
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}

    - name: Setup Flutter
      uses: subosito/flutter-action@44ac965b96f18d999802d4b807e3256d5a3f9f1d # v2.12.0
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Cache Flutter dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.pub-cache
          ~/.flutter
        key: ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-${{ hashFiles('**/pubspec.lock') }}
        restore-keys: |
          ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-
          ${{ runner.os }}-flutter-

    - name: Get Flutter dependencies
      run: flutter pub get

    - name: Verify Flutter installation
      run: flutter doctor -v

    - name: Check code formatting
      run: |
        echo "🎨 Checking code formatting..."
        dart format --set-exit-if-changed .
        if [ $? -ne 0 ]; then
          echo "❌ Code formatting issues found. Run 'dart format .' to fix."
          exit 1
        fi
        echo "✅ Code formatting is consistent"

    - name: Run comprehensive static analysis
      run: |
        echo "🔍 Running comprehensive static analysis..."
        flutter analyze --fatal-infos --fatal-warnings
        if [ $? -ne 0 ]; then
          echo "❌ Static analysis failed. Please fix all issues before proceeding."
          exit 1
        fi
        echo "✅ Static analysis passed with zero issues"

    - name: Run Flutter tests
      run: |
        echo "🧪 Running comprehensive test suite..."
        flutter test --reporter=expanded
        if [ $? -ne 0 ]; then
          echo "❌ Tests failed. Please fix failing tests before proceeding."
          exit 1
        fi
        echo "✅ All tests passed"

  # Job 3: Security audit for production
  security-audit:
    runs-on: self-hosted
    environment: production
    needs: pre-deployment-validation
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Flutter
      uses: subosito/flutter-action@44ac965b96f18d999802d4b807e3256d5a3f9f1d # v2.12.0
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Get Flutter dependencies
      run: flutter pub get

    - name: Run security audit
      run: |
        echo "🔒 Running comprehensive security audit..."
        
        # Check for security vulnerabilities in dependencies
        flutter pub deps --style=compact
        
        # Check for hardcoded secrets
        SECRET_PATTERNS=("password" "secret" "key" "token" "api_key" "firebase")
        SECRETS_FOUND=0
        
        for pattern in "${SECRET_PATTERNS[@]}"; do
          if grep -ri "$pattern.*=" lib/ --include="*.dart" | grep -v "// ignore" | grep -v "TODO" | grep -v "Key(" | grep -v "ValueKey"; then
            echo "⚠️  Potential hardcoded secret found: $pattern"
            SECRETS_FOUND=$((SECRETS_FOUND + 1))
          fi
        done
        
        if [ "$SECRETS_FOUND" -gt 0 ]; then
          echo "❌ ERROR: $SECRETS_FOUND potential security issues found in production build!"
          exit 1
        fi
        
        echo "✅ Security audit passed"

  # Job 4: Build Android APK/AAB for production
  build-production:
    runs-on: self-hosted
    needs: [pre-deployment-validation, comprehensive-test, security-audit]
    environment: production
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}

    - name: Setup Flutter
      uses: subosito/flutter-action@44ac965b96f18d999802d4b807e3256d5a3f9f1d # v2.12.0
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Cache Flutter dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.pub-cache
          ~/.flutter
        key: ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-${{ hashFiles('**/pubspec.lock') }}
        restore-keys: |
          ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-
          ${{ runner.os }}-flutter-

    - name: Cache Gradle dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: Get Flutter dependencies
      run: flutter pub get

    - name: Setup keystore for production environment
      env:
        PROD_KEYSTORE_FILE: ${{ secrets.PROD_KEYSTORE_FILE }}
        PROD_KEY_ALIAS: ${{ secrets.PROD_KEY_ALIAS }}
        PROD_KEYSTORE_PASSWORD: ${{ secrets.PROD_KEYSTORE_PASSWORD }}
        PROD_KEY_PASSWORD: ${{ secrets.PROD_KEY_PASSWORD }}
      run: |
        echo "🔐 Setting up production keystore..."
        
        # Create keystore directory
        mkdir -p android/keystore
        
        # Decode and save keystore file
        echo "$PROD_KEYSTORE_FILE" | base64 -d > android/keystore/prod-release.jks
        
        # Verify keystore was created successfully
        ls -la android/keystore/
        
        # Set environment variables for Gradle
        echo "PROD_KEYSTORE_FILE=$PWD/android/keystore/prod-release.jks" >> $GITHUB_ENV
        echo "PROD_KEY_ALIAS=$PROD_KEY_ALIAS" >> $GITHUB_ENV
        echo "PROD_KEYSTORE_PASSWORD=$PROD_KEYSTORE_PASSWORD" >> $GITHUB_ENV
        echo "PROD_KEY_PASSWORD=$PROD_KEY_PASSWORD" >> $GITHUB_ENV
        
        echo "✅ Production keystore configured"

    - name: Build for production environment
      env:
        BUILD_TYPE: ${{ github.event.inputs.build_type }}
      run: |
        echo "🏗️ Building for production environment..."
        
        # Select production Firebase configuration
        ./scripts/select_firebase_config.sh prod
        
        # Build based on input parameter
        if [ "$BUILD_TYPE" == "appbundle" ]; then
          echo "Building Production App Bundle..."
          flutter build appbundle --flavor prod --dart-define=FLAVOR=prod --release --no-tree-shake-icons
        else
          echo "Building Production APK..."
          flutter build apk --flavor prod --dart-define=FLAVOR=prod --release --no-tree-shake-icons
        fi
        
        echo "✅ Production build completed successfully"

    - name: Validate production build signature
      env:
        BUILD_TYPE: ${{ github.event.inputs.build_type }}
      run: |
        echo "🔍 Validating production build signature..."
        
        # Find the built file
        if [ "$BUILD_TYPE" == "appbundle" ]; then
          BUILD_FILE="build/app/outputs/bundle/prodRelease/app-prod-release.aab"
        else
          BUILD_FILE="build/app/outputs/flutter-apk/app-prod-release.apk"
        fi
        
        # Verify the build is properly signed
        jarsigner -verify -verbose "$BUILD_FILE"
        
        # Critical validation for production builds
        if jarsigner -verify -verbose -certs "$BUILD_FILE" | grep -i "debug"; then
          echo "❌ CRITICAL ERROR: Debug keys detected in production build!"
          exit 1
        else
          echo "✅ Production build is properly signed with production keys"
        fi
        
        # Additional production validation
        echo "🔐 Running additional production validation..."
        
        # Check certificate validity
        jarsigner -verify -verbose -certs "$BUILD_FILE" | grep -A 5 -B 5 "Valid"
        
        echo "✅ Production build signature validation passed"

    - name: Upload production build artifact
      uses: actions/upload-artifact@v4
      with:
        name: app-prod-release-${{ github.event.inputs.build_type }}-${{ github.sha }}
        path: |
          build/app/outputs/flutter-apk/app-prod-release.apk
          build/app/outputs/bundle/prodRelease/app-prod-release.aab
        retention-days: 365  # Keep production builds for 1 year

  # Job 5: Final security validation
  final-security-check:
    runs-on: self-hosted
    environment: production
    needs: [build-production]
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download production build artifacts
      uses: actions/download-artifact@v4
      with:
        name: app-prod-release-${{ github.event.inputs.build_type }}-${{ github.sha }}
        path: ./artifacts/

    - name: Final security validation
      env:
        BUILD_TYPE: ${{ github.event.inputs.build_type }}
      run: |
        echo "🔍 Running final security validation..."
        
        # Check that keystore files are not in the repository
        if find . -name "*.jks" -o -name "*.keystore" | grep -v ".git"; then
          echo "❌ CRITICAL ERROR: Keystore files found in repository!"
          exit 1
        fi
        
        # Verify build signature
        if [ "$BUILD_TYPE" == "appbundle" ]; then
          BUILD_FILE="./artifacts/app-prod-release.aab"
        else
          BUILD_FILE="./artifacts/app-prod-release.apk"
        fi
        
        if [ -f "$BUILD_FILE" ]; then
          # Verify signature
          jarsigner -verify "$BUILD_FILE"
          
          # Check for production package name
          if command -v aapt &> /dev/null; then
            PACKAGE_NAME=$(aapt dump badging "$BUILD_FILE" | grep package | awk '{print $2}' | sed "s/name='//" | sed "s/'.*//")
            echo "Production package name: $PACKAGE_NAME"
            
            # Verify production package name doesn't contain debug indicators
            if [[ "$PACKAGE_NAME" == *"debug"* ]] || [[ "$PACKAGE_NAME" == *"test"* ]]; then
              echo "❌ CRITICAL ERROR: Production package name contains debug/test indicators!"
              exit 1
            fi
          fi
          
          echo "✅ Final security validation passed"
        else
          echo "❌ ERROR: Production build file not found!"
          exit 1
        fi

  # Job 6: Production deployment notification
  production-deployment-complete:
    runs-on: self-hosted
    environment: production
    needs: [build-production, final-security-check]
    if: always()
    steps:
    - name: Production deployment notification
      env:
        ENVIRONMENT: ${{ env.ENVIRONMENT }}
        BRANCH_NAME: ${{ github.ref_name }}
        COMMIT_SHA: ${{ github.sha }}
        GITHUB_ACTOR: ${{ github.actor }}
        BUILD_TYPE: ${{ github.event.inputs.build_type }}
        BUILD_STATUS: ${{ needs.build-production.result }}
        SECURITY_STATUS: ${{ needs.final-security-check.result }}
      run: |
        echo "🚀 PRODUCTION DEPLOYMENT COMPLETED"
        echo "===================================="
        echo "Timestamp: $(date -u)"
        echo "Environment: $ENVIRONMENT"
        echo "Branch: $BRANCH_NAME"
        echo "Commit: $COMMIT_SHA"
        echo "Actor: $GITHUB_ACTOR"
        echo "Build Type: $BUILD_TYPE"
        echo "Build Status: $BUILD_STATUS"
        echo "Security Status: $SECURITY_STATUS"
        echo "===================================="
        
        if [ "$BUILD_STATUS" == "success" ] && [ "$SECURITY_STATUS" == "success" ]; then
          echo "✅ PRODUCTION DEPLOYMENT SUCCESSFUL!"
          echo "🎉 Ready for app store submission"
        else
          echo "❌ PRODUCTION DEPLOYMENT FAILED!"
          echo "🚨 Production deployment requires immediate attention"
        fi