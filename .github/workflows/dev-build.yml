name: Dev Build & Deploy

on:
  workflow_dispatch:
    inputs:
      build_type:
        description: 'Build type'
        required: true
        default: 'apk'
        type: choice
        options:
        - apk
        - appbundle

env:
  FLUTTER_VERSION: '3.32.6'
  JAVA_VERSION: '17'
  ENVIRONMENT: 'dev'

jobs:
  # Job 1: Run tests and validation
  test:
    runs-on: self-hosted
    environment: dev
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Cache Flutter dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.pub-cache
          ~/.flutter
        key: ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-${{ hashFiles('**/pubspec.lock') }}
        restore-keys: |
          ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-
          ${{ runner.os }}-flutter-

    - name: Get Flutter dependencies
      run: flutter pub get

    - name: Verify Flutter installation
      run: flutter doctor -v

    - name: Check code formatting
      run: |
        echo "🎨 Checking code formatting..."
        dart format --set-exit-if-changed .
        if [ $? -ne 0 ]; then
          echo "❌ Code formatting issues found. Run 'dart format .' to fix."
          exit 1
        fi
        echo "✅ Code formatting is consistent"

    - name: Run comprehensive static analysis
      run: |
        echo "🔍 Running comprehensive static analysis..."
        flutter analyze --fatal-infos --fatal-warnings
        if [ $? -ne 0 ]; then
          echo "❌ Static analysis failed. Please fix all issues before proceeding."
          exit 1
        fi
        echo "✅ Static analysis passed with zero issues"

    - name: Run Flutter tests
      run: |
        echo "🧪 Running comprehensive test suite..."
        flutter test --reporter=expanded
        if [ $? -ne 0 ]; then
          echo "❌ Tests failed. Please fix failing tests before proceeding."
          exit 1
        fi
        echo "✅ All tests passed"

  # Job 2: Build Android APK/AAB for development environment
  build-dev:
    runs-on: self-hosted
    environment: dev
    needs: test
    timeout-minutes: 30
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Cache Flutter dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.pub-cache
          ~/.flutter
        key: ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-${{ hashFiles('**/pubspec.lock') }}
        restore-keys: |
          ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-
          ${{ runner.os }}-flutter-

    - name: Cache Gradle dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
          ~/.gradle/daemon
          ~/.android/build-cache
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: Cache Android NDK
      uses: actions/cache@v4
      with:
        path: |
          /usr/local/lib/android/sdk/ndk
          /usr/local/lib/android/sdk/cmake
        key: ${{ runner.os }}-android-ndk-cmake-${{ env.JAVA_VERSION }}
        restore-keys: |
          ${{ runner.os }}-android-ndk-cmake-

    - name: Get Flutter dependencies
      run: flutter pub get

    - name: Verify Gradle wrapper
      run: |
        chmod +x scripts/verify_gradle_wrapper.sh
        ./scripts/verify_gradle_wrapper.sh

    - name: Clean build artifacts
      run: |
        echo "🧹 Cleaning Flutter build artifacts..."
        flutter clean
        echo "🧹 Cleaning Gradle build artifacts..."
        cd android
        ./gradlew clean || echo "⚠️ Gradle clean failed, continuing..."

    - name: Pre-warm Gradle daemon
      run: |
        cd android
        ./gradlew --version
        ./gradlew tasks --all > /dev/null 2>&1 || true

    - name: Setup keystore for dev environment
      env:
        DEV_KEYSTORE_FILE: ${{ secrets.DEV_KEYSTORE_FILE }}
        DEV_KEY_ALIAS: ${{ secrets.DEV_KEY_ALIAS }}
        DEV_KEYSTORE_PASSWORD: ${{ secrets.DEV_KEYSTORE_PASSWORD }}
        DEV_KEY_PASSWORD: ${{ secrets.DEV_KEY_PASSWORD }}
      run: |
        # Create keystore directory
        mkdir -p android/keystore
        
        # Decode and save keystore file
        echo "$DEV_KEYSTORE_FILE" | base64 -d > android/keystore/dev-release.jks
        
        # Verify keystore was created successfully
        ls -la android/keystore/
        
        # Set environment variables for Gradle
        echo "DEV_KEYSTORE_FILE=$PWD/android/keystore/dev-release.jks" >> $GITHUB_ENV
        echo "DEV_KEY_ALIAS=$DEV_KEY_ALIAS" >> $GITHUB_ENV
        echo "DEV_KEYSTORE_PASSWORD=$DEV_KEYSTORE_PASSWORD" >> $GITHUB_ENV
        echo "DEV_KEY_PASSWORD=$DEV_KEY_PASSWORD" >> $GITHUB_ENV

    - name: Build for dev environment
      run: |
        echo "🏗️ Building for development environment..."
        echo "Build started at: $(date)"

        # Select dev Firebase configuration
        ./scripts/select_firebase_config.sh dev

        # Set Gradle options for faster builds
        export GRADLE_OPTS="-Dorg.gradle.daemon=true -Dorg.gradle.parallel=true -Dorg.gradle.configureondemand=true -Dorg.gradle.jvmargs=-Xmx4g"

        # Show system resources
        echo "Available memory: $(free -h | grep '^Mem:' | awk '{print $7}')"
        echo "CPU cores: $(nproc)"

        # Build based on input parameter or default to APK
        BUILD_TYPE="${{ github.event.inputs.build_type || 'apk' }}"
        if [ "$BUILD_TYPE" == "appbundle" ]; then
          echo "Building App Bundle..."
          time flutter build appbundle --flavor dev --dart-define=FLAVOR=dev --release --verbose
        else
          echo "Building APK..."
          time flutter build apk --flavor dev --dart-define=FLAVOR=dev --release --verbose
        fi

        echo "Build completed at: $(date)"
        echo "✅ Build completed successfully"
      timeout-minutes: 25

    - name: Validate build signature
      run: |
        echo "🔍 Validating build signature..."
        
        # Find the built file
        BUILD_TYPE="${{ github.event.inputs.build_type || 'apk' }}"
        if [ "$BUILD_TYPE" == "appbundle" ]; then
          BUILD_FILE="build/app/outputs/bundle/devRelease/app-dev-release.aab"
        else
          BUILD_FILE="build/app/outputs/flutter-apk/app-dev-release.apk"
        fi
        
        # Verify the build is properly signed
        jarsigner -verify -verbose "$BUILD_FILE"
        
        # Check that debug keys are NOT used
        if jarsigner -verify -verbose -certs "$BUILD_FILE" | grep -i "debug"; then
          echo "❌ ERROR: Debug keys detected in dev release build!"
          exit 1
        else
          echo "✅ Build is properly signed with dev release keys"
        fi

    - name: Upload build artifact
      uses: actions/upload-artifact@v4
      with:
        name: app-dev-release-${{ github.event.inputs.build_type || 'apk' }}
        path: |
          build/app/outputs/flutter-apk/app-dev-release.apk
          build/app/outputs/bundle/devRelease/app-dev-release.aab
        retention-days: 7  # Short retention for dev builds

  # Job 3: Security validation
  security-check:
    runs-on: self-hosted
    environment: dev
    needs: [build-dev]
    if: always()
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: app-dev-release-${{ github.event.inputs.build_type || 'apk' }}
        path: ./artifacts/

    - name: Run security validation
      run: |
        echo "🔍 Running security validation..."
        
        # Check that keystore files are not in the repository
        if find . -name "*.jks" -o -name "*.keystore" | grep -v ".git"; then
          echo "❌ ERROR: Keystore files found in repository!"
          exit 1
        fi
        
        # Check that passwords are not in gradle.properties
        if grep -E "(PASSWORD|password)" android/gradle.properties | grep -v "^#"; then
          echo "⚠️  WARNING: Passwords found in gradle.properties - ensure they are for documentation only"
        fi
        
        # Verify build signature if artifact exists
        BUILD_TYPE="${{ github.event.inputs.build_type || 'apk' }}"
        if [ "$BUILD_TYPE" == "appbundle" ]; then
          BUILD_FILE="./artifacts/app-dev-release.aab"
        else
          BUILD_FILE="./artifacts/app-dev-release.apk"
        fi
        
        if [ -f "$BUILD_FILE" ]; then
          jarsigner -verify "$BUILD_FILE"
          echo "✅ Build signature verified"
        fi
        
        echo "🔐 Security validation completed"

  # Job 4: Deployment notification
  notify:
    runs-on: self-hosted
    environment: dev
    needs: [build-dev, security-check]
    if: always()
    steps:
    - name: Deployment notification
      run: |
        echo "📱 Development build completed!"
        echo "Environment: ${{ env.ENVIRONMENT }}"
        echo "Branch: ${{ github.ref_name }}"
        echo "Commit: ${{ github.sha }}"
        echo "Build Status: ${{ needs.build-dev.result }}"
        echo "Security Status: ${{ needs.security-check.result }}"
        
        if [ "${{ needs.build-dev.result }}" == "success" ] && [ "${{ needs.security-check.result }}" == "success" ]; then
          echo "✅ Development deployment successful!"
        else
          echo "❌ Development deployment failed!"
        fi