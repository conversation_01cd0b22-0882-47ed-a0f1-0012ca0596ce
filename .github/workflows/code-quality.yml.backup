name: Code Quality & Static Analysis

on:
  push:
    branches: [ main, development ]
  pull_request:
    branches: [ main, development ]
  workflow_dispatch:
  schedule:
    # Run daily at 2 AM UTC for continuous quality monitoring
    - cron: '0 2 * * *'

env:
  FLUTTER_VERSION: '3.32.6'

jobs:
  # Job 1: Comprehensive static analysis and linting
  static-analysis:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7

    - name: Setup Flutter
      uses: subosito/flutter-action@48cafc24713cca54bbe03cdc3a423187d413aafa # v2.18.0
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Get Flutter dependencies
      run: flutter pub get

    - name: Verify analysis configuration
      run: |
        echo "🔧 Verifying analysis configuration..."
        if [ ! -f "analysis_options.yaml" ]; then
          echo "❌ analysis_options.yaml not found!"
          exit 1
        fi
        
        # Check if very_good_analysis is included
        if ! grep -q "very_good_analysis" analysis_options.yaml; then
          echo "⚠️  Warning: very_good_analysis not detected in configuration"
        fi
        
        echo "✅ Analysis configuration verified"

    - name: Run comprehensive static analysis
      run: |
        echo "🔍 Running comprehensive static analysis with very_good_analysis..."
        flutter analyze --fatal-infos --fatal-warnings
        echo "✅ Static analysis completed successfully"

    - name: Check for TODO/FIXME comments
      run: |
        echo "📝 Scanning for TODO/FIXME comments..."
        TODO_COUNT=$(grep -r "TODO\|FIXME" lib/ test/ --include="*.dart" | wc -l || echo "0")
        echo "Found $TODO_COUNT TODO/FIXME comments"
        
        if [ "$TODO_COUNT" -gt 50 ]; then
          echo "⚠️  Warning: High number of TODO/FIXME comments ($TODO_COUNT)"
          echo "Consider addressing some of these technical debt items"
        fi

    - name: Analyze code complexity
      run: |
        echo "📊 Analyzing code complexity..."
        # Count lines of code
        DART_FILES=$(find lib/ -name "*.dart" | wc -l)
        TOTAL_LINES=$(find lib/ -name "*.dart" -exec wc -l {} + | tail -1 | awk '{print $1}')
        AVG_LINES=$((TOTAL_LINES / DART_FILES))
        
        echo "📈 Code metrics:"
        echo "  - Dart files: $DART_FILES"
        echo "  - Total lines: $TOTAL_LINES"
        echo "  - Average lines per file: $AVG_LINES"
        
        if [ "$AVG_LINES" -gt 300 ]; then
          echo "⚠️  Warning: High average lines per file ($AVG_LINES)"
          echo "Consider breaking down large files"
        fi

  # Job 2: Code formatting validation
  formatting:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7

    - name: Setup Flutter
      uses: subosito/flutter-action@48cafc24713cca54bbe03cdc3a423187d413aafa # v2.18.0
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Check code formatting
      run: |
        echo "🎨 Checking code formatting consistency..."
        dart format --set-exit-if-changed .
        echo "✅ Code formatting is consistent"

    - name: Validate import organization
      run: |
        echo "📦 Validating import organization..."
        # Check for common import issues
        IMPORT_ISSUES=0
        
        # Check for relative imports in lib/
        if grep -r "import '\.\." lib/ --include="*.dart"; then
          echo "❌ Relative imports found in lib/ directory"
          IMPORT_ISSUES=$((IMPORT_ISSUES + 1))
        fi
        
        # Check for dart: imports not at the top
        if grep -A 5 -B 5 "import 'dart:" lib/ --include="*.dart" | grep -B 5 "import 'package:"; then
          echo "⚠️  Warning: dart: imports should come before package: imports"
        fi
        
        if [ "$IMPORT_ISSUES" -eq 0 ]; then
          echo "✅ Import organization looks good"
        else
          echo "❌ Import organization issues found"
          exit 1
        fi

  # Job 3: Documentation validation
  documentation:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7

    - name: Setup Flutter
      uses: subosito/flutter-action@48cafc24713cca54bbe03cdc3a423187d413aafa # v2.18.0
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Get Flutter dependencies
      run: flutter pub get

    - name: Check public API documentation
      run: |
        echo "📚 Checking public API documentation..."
        # Run analysis with documentation warnings as errors
        flutter analyze --fatal-infos 2>&1 | tee analysis_output.txt
        
        # Count documentation warnings
        DOC_WARNINGS=$(grep -c "public_member_api_docs" analysis_output.txt || echo "0")
        echo "Found $DOC_WARNINGS documentation warnings"
        
        if [ "$DOC_WARNINGS" -gt 0 ]; then
          echo "⚠️  Warning: $DOC_WARNINGS public members missing documentation"
          echo "Consider adding documentation for better code maintainability"
        else
          echo "✅ All public APIs are documented"
        fi

    - name: Validate README and documentation files
      run: |
        echo "📖 Validating documentation files..."
        
        # Check if README exists and is not empty
        if [ ! -f "README.md" ] || [ ! -s "README.md" ]; then
          echo "❌ README.md is missing or empty"
          exit 1
        fi
        
        # Check for essential documentation files
        REQUIRED_DOCS=("docs/code_quality_standards.md")
        for doc in "${REQUIRED_DOCS[@]}"; do
          if [ ! -f "$doc" ]; then
            echo "⚠️  Warning: $doc not found"
          else
            echo "✅ $doc exists"
          fi
        done

  # Job 4: Security and dependency analysis
  security:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7

    - name: Setup Flutter
      uses: subosito/flutter-action@48cafc24713cca54bbe03cdc3a423187d413aafa # v2.18.0
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Get Flutter dependencies
      run: flutter pub get

    - name: Check for security vulnerabilities
      run: |
        echo "🔒 Checking for security vulnerabilities..."
        
        # Check for hardcoded secrets (basic patterns)
        SECRET_PATTERNS=("password" "secret" "key" "token" "api_key")
        SECRETS_FOUND=0
        
        for pattern in "${SECRET_PATTERNS[@]}"; do
          if grep -ri "$pattern.*=" lib/ --include="*.dart" | grep -v "// ignore" | grep -v "TODO"; then
            echo "⚠️  Potential hardcoded secret found: $pattern"
            SECRETS_FOUND=$((SECRETS_FOUND + 1))
          fi
        done
        
        if [ "$SECRETS_FOUND" -eq 0 ]; then
          echo "✅ No obvious hardcoded secrets found"
        else
          echo "⚠️  Warning: $SECRETS_FOUND potential security issues found"
          echo "Please review and ensure no sensitive data is hardcoded"
        fi

    - name: Analyze dependencies for vulnerabilities
      run: |
        echo "📦 Analyzing dependencies..."
        flutter pub deps --style=compact
        
        # Check for outdated packages
        echo "🔄 Checking for outdated packages..."
        flutter pub outdated --mode=null-safety || true
        
        echo "✅ Dependency analysis completed"

  # Job 5: Performance analysis
  performance:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7

    - name: Setup Flutter
      uses: subosito/flutter-action@48cafc24713cca54bbe03cdc3a423187d413aafa # v2.18.0
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Get Flutter dependencies
      run: flutter pub get

    - name: Analyze build performance
      run: |
        echo "⚡ Analyzing build performance..."
        
        # Check for performance anti-patterns
        PERF_ISSUES=0
        
        # Check for missing const constructors
        if grep -r "Widget.*(" lib/ --include="*.dart" | grep -v "const " | grep -v "// ignore"; then
          echo "⚠️  Potential missing const constructors found"
          PERF_ISSUES=$((PERF_ISSUES + 1))
        fi
        
        # Check for unnecessary containers
        UNNECESSARY_CONTAINERS=$(grep -r "Container(" lib/ --include="*.dart" | wc -l || echo "0")
        if [ "$UNNECESSARY_CONTAINERS" -gt 20 ]; then
          echo "⚠️  High number of Container widgets ($UNNECESSARY_CONTAINERS)"
          echo "Consider using more specific widgets like SizedBox, DecoratedBox, etc."
        fi
        
        echo "✅ Performance analysis completed"

  # Job 6: Test coverage analysis
  test-coverage:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7

    - name: Setup Flutter
      uses: subosito/flutter-action@48cafc24713cca54bbe03cdc3a423187d413aafa # v2.18.0
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Get Flutter dependencies
      run: flutter pub get

    - name: Run Flutter tests with coverage
      run: |
        echo "🧪 Running comprehensive test suite with coverage..."
        flutter test --coverage --reporter=expanded
        echo "✅ Tests completed"

    - name: Generate coverage report
      run: |
        echo "📊 Generating coverage report..."
        # Install lcov for coverage reporting
        sudo apt-get update
        sudo apt-get install -y lcov

        # Generate HTML coverage report
        genhtml coverage/lcov.info -o coverage/html

        # Calculate coverage percentage
        COVERAGE=$(lcov --summary coverage/lcov.info 2>&1 | grep "lines" | awk '{print $2}' | sed 's/%//')
        echo "📈 Test coverage: ${COVERAGE}%"

        # Set coverage threshold
        MIN_COVERAGE=80
        if (( $(echo "$COVERAGE < $MIN_COVERAGE" | bc -l) )); then
          echo "⚠️  Coverage ${COVERAGE}% is below recommended threshold of ${MIN_COVERAGE}%"
        else
          echo "✅ Coverage meets recommended threshold"
        fi

    - name: Verify coverage file exists
      run: |
        echo "🔍 Verifying coverage file..."
        if [ -f "coverage/lcov.info" ]; then
          echo "✅ Coverage file exists"
          echo "📄 File size: $(du -h coverage/lcov.info)"
          echo "📊 First few lines:"
          head -10 coverage/lcov.info
        else
          echo "❌ Coverage file not found!"
          exit 1
        fi

    - name: Verify coverage file exists
      run: |
        echo "Checking for coverage file..."
        ls -la coverage/
        echo "Coverage file size:"
        wc -l coverage/lcov.info
        echo "First few lines of coverage file:"
        head -10 coverage/lcov.info

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@b9fd7d16f6d7d1b5d2bec1a2887e65ceed900238 # v4.6.0
      with:
        files: ./coverage/lcov.info
        fail_ci_if_error: true
        verbose: true
        name: codecov-umbrella
        token: ${{ secrets.CODECOV_TOKEN }}
        commit_parent: ${{ github.event.before }}
        override_branch: ${{ github.ref_name }}
        override_commit: ${{ github.sha }}
        slug: hropov/budapp
        working-directory: ./
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

    - name: Upload coverage to Codacy
      run: |
        echo "📊 Uploading coverage to Codacy..."
        # Filter generated files from coverage for more accurate metrics
        lcov --remove coverage/lcov.info \
          '**/*.g.dart' \
          '**/*.freezed.dart' \
          '**/*.config.dart' \
          '**/*.gr.dart' \
          '**/generated_plugin_registrant.dart' \
          'build/**' \
          '.dart_tool/**' \
          'coverage/**' \
          'test/.test_coverage.dart' \
          'integration_test/driver.dart' \
          --output-file coverage/lcov_filtered.info
        
        # Upload filtered coverage to Codacy
        bash <(curl -Ls https://coverage.codacy.com/get.sh) report \
          --coverage-reports coverage/lcov_filtered.info \
          --project-token ${{ secrets.CODACY_PROJECT_TOKEN }} \
          --commit-uuid ${{ github.sha }}
        
        echo "✅ Coverage uploaded to Codacy successfully"
      env:
        CODACY_PROJECT_TOKEN: ${{ secrets.CODACY_PROJECT_TOKEN }}

    - name: Upload coverage artifacts
      uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      with:
        name: coverage-report
        path: |
          coverage/lcov.info
          coverage/html/
        retention-days: 10

  # Job 7: Codacy analysis
  codacy-analysis:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7

    - name: Setup Flutter
      uses: subosito/flutter-action@48cafc24713cca54bbe03cdc3a423187d413aafa # v2.18.0
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Get Flutter dependencies
      run: flutter pub get

    - name: Install Codacy CLI
      run: |
        echo "📦 Installing Codacy CLI..."
        curl -L https://github.com/codacy/codacy-analysis-cli/releases/latest/download/codacy-analysis-cli-linux -o codacy-analysis-cli
        chmod +x codacy-analysis-cli
        echo "✅ Codacy CLI installed successfully"

    - name: Run Codacy Analysis
      run: |
        echo "🔍 Running Codacy analysis with business logic focus..."
        ./codacy-analysis-cli analyze \
          --tool dartanalyzer \
          --directory . \
          --format json \
          --output codacy-results.json \
          --max-allowed-issues 100 \
          --parallel 4 \
          || true  # Don't fail the build on analysis issues
        
        echo "✅ Codacy analysis completed"

    - name: Process Codacy Results
      run: |
        echo "📊 Processing Codacy analysis results..."
        if [ -f "codacy-results.json" ]; then
          ISSUE_COUNT=$(jq '.results | length' codacy-results.json 2>/dev/null || echo "0")
          echo "Found $ISSUE_COUNT issues in business logic code"
          
          # Show top issues if any found
          if [ "$ISSUE_COUNT" -gt 0 ]; then
            echo "🔍 Top issues found:"
            jq -r '.results[0:5] | .[] | "  - \(.filename):\(.line) - \(.patternId): \(.message)"' codacy-results.json 2>/dev/null || echo "  Error parsing results"
          fi
          
          echo "✅ Results processed successfully"
        else
          echo "⚠️  No results file generated"
        fi

    - name: Upload Codacy results as artifact
      uses: actions/upload-artifact@50769540e7f4bd5e21e526ee35c689e35e0d6874 # v4.4.0
      if: always()
      with:
        name: codacy-analysis-results
        path: codacy-results.json
        retention-days: 10

  # Job 8: Generate quality report
  quality-report:
    runs-on: ubuntu-latest
    needs: [static-analysis, formatting, documentation, security, performance, test-coverage, codacy-analysis]
    if: always()
    steps:
    - name: Generate quality summary
      run: |
        echo "📊 Code Quality Summary"
        echo "======================"
        echo "✅ Static Analysis: ${{ needs.static-analysis.result }}"
        echo "✅ Code Formatting: ${{ needs.formatting.result }}"
        echo "✅ Documentation: ${{ needs.documentation.result }}"
        echo "✅ Security Check: ${{ needs.security.result }}"
        echo "✅ Performance Analysis: ${{ needs.performance.result }}"
        echo "✅ Test Coverage: ${{ needs.test-coverage.result }}"
        echo "✅ Codacy Analysis: ${{ needs.codacy-analysis.result }}"
        echo ""

        if [ "${{ needs.static-analysis.result }}" == "success" ] && \
           [ "${{ needs.formatting.result }}" == "success" ] && \
           [ "${{ needs.documentation.result }}" == "success" ] && \
           [ "${{ needs.security.result }}" == "success" ] && \
           [ "${{ needs.performance.result }}" == "success" ] && \
           [ "${{ needs.test-coverage.result }}" == "success" ] && \
           [ "${{ needs.codacy-analysis.result }}" == "success" ]; then
          echo "🎉 All quality checks passed!"
        else
          echo "⚠️  Some quality checks failed or had warnings"
        fi
