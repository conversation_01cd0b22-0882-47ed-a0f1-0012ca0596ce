name: Branch Protection Setup

on:
  workflow_dispatch:
    inputs:
      action:
        description: 'Action to perform'
        required: true
        default: 'validate'
        type: choice
        options:
        - validate
        - setup
        - remove

permissions:
  contents: read
  actions: read
  checks: read

jobs:
  branch-protection-setup:
    runs-on: self-hosted
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup GitHub CLI
      run: |
        # GitHub CLI is pre-installed on GitHub runners
        gh --version

    - name: Validate current branch protection rules
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        echo "🔍 Validating current branch protection rules..."
        
        # Check protection rules for each branch
        BRANCHES=("main" "development" "production")
        
        for branch in "${BRANCHES[@]}"; do
          echo "Checking branch: $branch"
          
          # Check if branch exists
          if gh api repos/${{ github.repository }}/branches/$branch > /dev/null 2>&1; then
            echo "✅ Branch $branch exists"
            
            # Check protection rules
            if gh api repos/${{ github.repository }}/branches/$branch/protection > /dev/null 2>&1; then
              echo "✅ Branch $branch has protection rules"
              gh api repos/${{ github.repository }}/branches/$branch/protection --jq '.required_status_checks.contexts[]' | head -5
            else
              echo "⚠️  Branch $branch has no protection rules"
            fi
          else
            echo "❌ Branch $branch does not exist"
          fi
          echo "---"
        done

    - name: Setup branch protection rules
      if: github.event.inputs.action == 'setup'
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        echo "🛡️ Setting up branch protection rules..."
        
        # Function to setup protection for a branch
        setup_protection() {
          local branch=$1
          local required_reviews=$2
          local required_checks=$3
          local dismiss_stale_reviews=$4
          local restrict_pushes=$5
          
          echo "Setting up protection for branch: $branch"
          
          # Create protection rule
          gh api \
            --method PUT \
            -H "Accept: application/vnd.github.v3+json" \
            repos/${{ github.repository }}/branches/$branch/protection \
            -f required_status_checks='{
              "strict": true,
              "contexts": '"$required_checks"'
            }' \
            -f enforce_admins=true \
            -f required_pull_request_reviews='{
              "required_approving_review_count": '"$required_reviews"',
              "dismiss_stale_reviews": '"$dismiss_stale_reviews"',
              "require_code_owner_reviews": true
            }' \
            -f restrictions='{
              "users": [],
              "teams": []
            }' \
            -f allow_force_pushes=false \
            -f allow_deletions=false
        }
        
        # Setup protection for main branch (staging)
        setup_protection "main" 1 '["test", "code-quality"]' true false
        
        # Setup protection for development branch (dev)
        setup_protection "development" 1 '["test", "code-quality"]' true false
        
        # Setup protection for production branch (production) - strictest rules
        setup_protection "production" 2 '["test", "code-quality", "security-audit"]' true true
        
        echo "✅ Branch protection rules setup completed"

    - name: Remove branch protection rules
      if: github.event.inputs.action == 'remove'
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        echo "🗑️ Removing branch protection rules..."
        
        BRANCHES=("main" "development" "production")
        
        for branch in "${BRANCHES[@]}"; do
          echo "Removing protection for branch: $branch"
          
          if gh api repos/${{ github.repository }}/branches/$branch/protection > /dev/null 2>&1; then
            gh api --method DELETE repos/${{ github.repository }}/branches/$branch/protection
            echo "✅ Protection removed for $branch"
          else
            echo "⚠️  No protection found for $branch"
          fi
        done

    - name: Generate branch protection report
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        echo "📊 Branch Protection Report"
        echo "=========================="
        echo "Generated: $(date -u)"
        echo "Repository: ${{ github.repository }}"
        echo ""
        
        BRANCHES=("main" "development" "production")
        
        for branch in "${BRANCHES[@]}"; do
          echo "Branch: $branch"
          echo "---"
          
          if gh api repos/${{ github.repository }}/branches/$branch/protection > /dev/null 2>&1; then
            echo "✅ Protected: Yes"
            
            # Get protection details
            protection_info=$(gh api repos/${{ github.repository }}/branches/$branch/protection)
            
            # Required status checks
            required_checks=$(echo "$protection_info" | jq -r '.required_status_checks.contexts[]?' | tr '\n' ', ' | sed 's/,$//')
            echo "Required checks: $required_checks"
            
            # Required reviews
            required_reviews=$(echo "$protection_info" | jq -r '.required_pull_request_reviews.required_approving_review_count // 0')
            echo "Required reviews: $required_reviews"
            
            # Dismiss stale reviews
            dismiss_stale=$(echo "$protection_info" | jq -r '.required_pull_request_reviews.dismiss_stale_reviews // false')
            echo "Dismiss stale reviews: $dismiss_stale"
            
            # Enforce admins
            enforce_admins=$(echo "$protection_info" | jq -r '.enforce_admins.enabled // false')
            echo "Enforce for admins: $enforce_admins"
            
          else
            echo "❌ Protected: No"
          fi
          echo ""
        done
        
        echo "📋 Recommended Settings:"
        echo "- main (staging): 1 review, test + code-quality checks"
        echo "- development (dev): 1 review, test + code-quality checks"
        echo "- production: 2 reviews, test + code-quality + security-audit checks"