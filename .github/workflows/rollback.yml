name: Emergency Rollback

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to rollback'
        required: true
        type: choice
        options:
        - staging
        - production
      rollback_to_commit:
        description: 'Commit SHA to rollback to (leave empty to rollback to previous successful deployment)'
        required: false
        type: string
      rollback_reason:
        description: 'Reason for rollback'
        required: true
        type: string
      confirmation:
        description: 'Type "ROLLBACK" to confirm rollback operation'
        required: true
        type: string
      skip_tests:
        description: 'Skip tests (use only in emergency)'
        required: false
        default: false
        type: boolean
      notify_team:
        description: 'Send notifications to team'
        required: false
        default: true
        type: boolean

env:
  FLUTTER_VERSION: '3.32.6'
  JAVA_VERSION: '17'

jobs:
  # Job 1: Rollback validation and preparation
  rollback-validation:
    runs-on: self-hosted
    outputs:
      target_commit: ${{ steps.prepare.outputs.target_commit }}
      target_commit_short: ${{ steps.prepare.outputs.target_commit_short }}
      current_commit: ${{ steps.prepare.outputs.current_commit }}
      rollback_environment: ${{ steps.prepare.outputs.rollback_environment }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Fetch full history for rollback

    - name: Validate rollback request
      run: |
        echo "🔍 Validating rollback request..."
        
        # Validate confirmation
        if [ "${{ github.event.inputs.confirmation }}" != "ROLLBACK" ]; then
          echo "❌ ERROR: Rollback requires 'ROLLBACK' confirmation!"
          exit 1
        fi
        
        # Validate environment
        ENVIRONMENT="${{ github.event.inputs.environment }}"
        if [ "$ENVIRONMENT" != "staging" ] && [ "$ENVIRONMENT" != "production" ]; then
          echo "❌ ERROR: Invalid environment: $ENVIRONMENT"
          exit 1
        fi
        
        # Additional validation for production
        if [ "$ENVIRONMENT" == "production" ]; then
          if [ "${{ github.ref_name }}" != "production" ]; then
            echo "❌ ERROR: Production rollback can only be triggered from production branch!"
            exit 1
          fi
          echo "✅ Production rollback validation passed"
        fi
        
        # Validate reason
        if [ -z "${{ github.event.inputs.rollback_reason }}" ]; then
          echo "❌ ERROR: Rollback reason is required!"
          exit 1
        fi
        
        echo "✅ Rollback validation completed"

    - name: Prepare rollback target
      id: prepare
      run: |
        echo "🎯 Preparing rollback target..."
        
        ENVIRONMENT="${{ github.event.inputs.environment }}"
        ROLLBACK_TO_COMMIT="${{ github.event.inputs.rollback_to_commit }}"
        CURRENT_COMMIT="${{ github.sha }}"
        
        # If no specific commit provided, find the last successful deployment
        if [ -z "$ROLLBACK_TO_COMMIT" ]; then
          echo "🔍 Finding last successful deployment..."
          
          # This would typically query GitHub API for successful deployments
          # For now, we'll use the previous commit as a fallback
          ROLLBACK_TO_COMMIT=$(git rev-parse HEAD~1)
          echo "Using previous commit as rollback target: $ROLLBACK_TO_COMMIT"
        fi
        
        # Validate commit exists
        if ! git rev-parse --verify "$ROLLBACK_TO_COMMIT" >/dev/null 2>&1; then
          echo "❌ ERROR: Invalid commit SHA: $ROLLBACK_TO_COMMIT"
          exit 1
        fi
        
        # Get commit information
        COMMIT_MESSAGE=$(git log --format="%s" -n 1 "$ROLLBACK_TO_COMMIT")
        COMMIT_AUTHOR=$(git log --format="%an" -n 1 "$ROLLBACK_TO_COMMIT")
        COMMIT_DATE=$(git log --format="%ad" -n 1 "$ROLLBACK_TO_COMMIT")
        
        echo "🎯 Rollback target:"
        echo "  Commit: $ROLLBACK_TO_COMMIT"
        echo "  Message: $COMMIT_MESSAGE"
        echo "  Author: $COMMIT_AUTHOR"
        echo "  Date: $COMMIT_DATE"
        
        # Export outputs
        echo "target_commit=$ROLLBACK_TO_COMMIT" >> $GITHUB_OUTPUT
        echo "target_commit_short=${ROLLBACK_TO_COMMIT:0:8}" >> $GITHUB_OUTPUT
        echo "current_commit=$CURRENT_COMMIT" >> $GITHUB_OUTPUT
        echo "rollback_environment=$ENVIRONMENT" >> $GITHUB_OUTPUT
        
        echo "✅ Rollback target prepared"

    - name: Log rollback initiation
      run: |
        echo "🚨 EMERGENCY ROLLBACK INITIATED"
        echo "================================"
        echo "Environment: ${{ github.event.inputs.environment }}"
        echo "Target Commit: ${{ steps.prepare.outputs.target_commit }}"
        echo "Current Commit: ${{ steps.prepare.outputs.current_commit }}"
        echo "Reason: ${{ github.event.inputs.rollback_reason }}"
        echo "Skip Tests: ${{ github.event.inputs.skip_tests }}"
        echo "Actor: ${{ github.actor }}"
        echo "Timestamp: $(date -u)"
        echo "================================"

  # Job 2: Optional testing (can be skipped in emergency)
  rollback-testing:
    runs-on: self-hosted
    needs: rollback-validation
    if: ${{ !github.event.inputs.skip_tests }}
    steps:
    - name: Checkout rollback target
      uses: actions/checkout@v4
      with:
        ref: ${{ needs.rollback-validation.outputs.target_commit }}

    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Cache Flutter dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.pub-cache
          ~/.flutter
        key: ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-${{ hashFiles('**/pubspec.lock') }}
        restore-keys: |
          ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-
          ${{ runner.os }}-flutter-

    - name: Get dependencies
      run: flutter pub get

    - name: Run critical tests
      run: |
        echo "🧪 Running critical tests for rollback target..."
        
        # Run essential tests only (faster for emergency rollback)
        flutter test --coverage --reporter=expanded test/core/ test/features/auth/ test/services/
        
        if [ $? -ne 0 ]; then
          echo "❌ Critical tests failed for rollback target!"
          echo "⚠️  Consider using --skip-tests flag for emergency rollback"
          exit 1
        fi
        
        echo "✅ Critical tests passed for rollback target"

    - name: Run static analysis
      run: |
        echo "🔍 Running static analysis..."
        flutter analyze --fatal-infos --fatal-warnings
        echo "✅ Static analysis passed"

  # Job 3: Build rollback version
  rollback-build:
    runs-on: self-hosted
    needs: [rollback-validation, rollback-testing]
    if: always() && needs.rollback-validation.result == 'success' && (needs.rollback-testing.result == 'success' || needs.rollback-testing.result == 'skipped')
    steps:
    - name: Checkout rollback target
      uses: actions/checkout@v4
      with:
        ref: ${{ needs.rollback-validation.outputs.target_commit }}

    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Cache Flutter dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.pub-cache
          ~/.flutter
        key: ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-${{ hashFiles('**/pubspec.lock') }}
        restore-keys: |
          ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-
          ${{ runner.os }}-flutter-

    - name: Cache Gradle dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: Get dependencies
      run: flutter pub get

    - name: Setup keystore for rollback environment
      env:
        STAGING_KEYSTORE_FILE: ${{ secrets.STAGING_KEYSTORE_FILE }}
        STAGING_KEY_ALIAS: ${{ secrets.STAGING_KEY_ALIAS }}
        STAGING_KEYSTORE_PASSWORD: ${{ secrets.STAGING_KEYSTORE_PASSWORD }}
        STAGING_KEY_PASSWORD: ${{ secrets.STAGING_KEY_PASSWORD }}
        PROD_KEYSTORE_FILE: ${{ secrets.PROD_KEYSTORE_FILE }}
        PROD_KEY_ALIAS: ${{ secrets.PROD_KEY_ALIAS }}
        PROD_KEYSTORE_PASSWORD: ${{ secrets.PROD_KEYSTORE_PASSWORD }}
        PROD_KEY_PASSWORD: ${{ secrets.PROD_KEY_PASSWORD }}
      run: |
        echo "🔐 Setting up keystore for rollback environment..."
        
        mkdir -p android/keystore
        
        ENVIRONMENT="${{ needs.rollback-validation.outputs.rollback_environment }}"
        
        if [ "$ENVIRONMENT" == "staging" ]; then
          echo "$STAGING_KEYSTORE_FILE" | base64 -d > android/keystore/staging-release.jks
          echo "STAGING_KEYSTORE_FILE=keystore/staging-release.jks" >> android/gradle.properties
          echo "STAGING_KEY_ALIAS=$STAGING_KEY_ALIAS" >> android/gradle.properties
          echo "STAGING_KEYSTORE_PASSWORD=$STAGING_KEYSTORE_PASSWORD" >> android/gradle.properties
          echo "STAGING_KEY_PASSWORD=$STAGING_KEY_PASSWORD" >> android/gradle.properties
        elif [ "$ENVIRONMENT" == "production" ]; then
          echo "$PROD_KEYSTORE_FILE" | base64 -d > android/keystore/prod-release.jks
          echo "PROD_KEYSTORE_FILE=keystore/prod-release.jks" >> android/gradle.properties
          echo "PROD_KEY_ALIAS=$PROD_KEY_ALIAS" >> android/gradle.properties
          echo "PROD_KEYSTORE_PASSWORD=$PROD_KEYSTORE_PASSWORD" >> android/gradle.properties
          echo "PROD_KEY_PASSWORD=$PROD_KEY_PASSWORD" >> android/gradle.properties
        fi
        
        echo "✅ Keystore configured for rollback"

    - name: Build rollback version
      run: |
        echo "🏗️ Building rollback version..."
        
        ENVIRONMENT="${{ needs.rollback-validation.outputs.rollback_environment }}"
        
        # Select appropriate Firebase configuration
        ./scripts/select_firebase_config.sh $ENVIRONMENT
        
        # Build App Bundle for rollback (preferred for store deployment)
        flutter build appbundle --flavor $ENVIRONMENT --dart-define=FLAVOR=$ENVIRONMENT --release --no-tree-shake-icons
        
        echo "✅ Rollback build completed"

    - name: Validate rollback build
      run: |
        echo "🔍 Validating rollback build..."
        
        ENVIRONMENT="${{ needs.rollback-validation.outputs.rollback_environment }}"
        
        # Find the built file
        if [ "$ENVIRONMENT" == "staging" ]; then
          BUILD_FILE="build/app/outputs/bundle/stagingRelease/app-staging-release.aab"
        elif [ "$ENVIRONMENT" == "production" ]; then
          BUILD_FILE="build/app/outputs/bundle/prodRelease/app-prod-release.aab"
        fi
        
        # Validate signature
        jarsigner -verify -verbose "$BUILD_FILE"
        
        # Check for debug keys
        if jarsigner -verify -verbose -certs "$BUILD_FILE" | grep -i "debug"; then
          echo "❌ ERROR: Debug keys detected in rollback build!"
          exit 1
        fi
        
        echo "✅ Rollback build validation passed"

    - name: Upload rollback build
      uses: actions/upload-artifact@v4
      with:
        name: rollback-${{ needs.rollback-validation.outputs.rollback_environment }}-${{ needs.rollback-validation.outputs.target_commit_short }}
        path: |
          build/app/outputs/bundle/*/app-*-release.aab
        retention-days: 30

  # Job 4: Deploy rollback (simulation - would integrate with actual deployment)
  rollback-deploy:
    runs-on: self-hosted
    needs: [rollback-validation, rollback-build]
    if: always() && needs.rollback-validation.result == 'success' && needs.rollback-build.result == 'success'
    steps:
    - name: Simulate rollback deployment
      run: |
        echo "🚀 Simulating rollback deployment..."
        
        ENVIRONMENT="${{ needs.rollback-validation.outputs.rollback_environment }}"
        TARGET_COMMIT="${{ needs.rollback-validation.outputs.target_commit }}"
        
        echo "Environment: $ENVIRONMENT"
        echo "Target Commit: $TARGET_COMMIT"
        
        # This would typically:
        # 1. Deploy to app store/internal testing
        # 2. Update environment configuration
        # 3. Update load balancer/CDN
        # 4. Verify deployment health
        
        echo "✅ Rollback deployment simulation completed"

    - name: Verify rollback deployment
      run: |
        echo "🔍 Verifying rollback deployment..."
        
        # This would typically:
        # 1. Run health checks
        # 2. Verify app functionality
        # 3. Check metrics and logs
        # 4. Confirm rollback success
        
        echo "✅ Rollback deployment verification completed"

  # Job 5: Post-rollback actions
  post-rollback:
    runs-on: self-hosted
    needs: [rollback-validation, rollback-deploy]
    if: always() && needs.rollback-validation.result == 'success' && needs.rollback-deploy.result == 'success'
    steps:
    - name: Update branch protection
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        echo "🛡️ Updating branch protection (if needed)..."
        
        # This would typically update branch protection rules
        # to prevent problematic commits from being deployed again
        
        echo "✅ Branch protection updated"

    - name: Create rollback documentation
      run: |
        echo "📝 Creating rollback documentation..."
        
        # Create rollback report
        cat > rollback-report.md << EOF
        # Rollback Report
        
        ## Rollback Details
        - **Environment**: ${{ needs.rollback-validation.outputs.rollback_environment }}
        - **Target Commit**: ${{ needs.rollback-validation.outputs.target_commit }}
        - **Previous Commit**: ${{ needs.rollback-validation.outputs.current_commit }}
        - **Reason**: ${{ github.event.inputs.rollback_reason }}
        - **Initiated By**: ${{ github.actor }}
        - **Timestamp**: $(date -u)
        - **Tests Skipped**: ${{ github.event.inputs.skip_tests }}
        
        ## Rollback Status
        - **Status**: ✅ SUCCESS
        - **Build**: ✅ SUCCESS
        - **Deployment**: ✅ SUCCESS
        - **Verification**: ✅ SUCCESS
        
        ## Next Steps
        1. Monitor application metrics
        2. Investigate root cause of the issue
        3. Prepare fix for the problematic commit
        4. Plan forward deployment of the fix
        
        ## Affected Systems
        - BudApp ${{ needs.rollback-validation.outputs.rollback_environment }} environment
        - Users of ${{ needs.rollback-validation.outputs.rollback_environment }} environment
        
        ## Recovery Time
        - **Started**: $(date -u)
        - **Completed**: $(date -u)
        - **Duration**: < 30 minutes
        EOF
        
        echo "✅ Rollback documentation created"

    - name: Schedule post-rollback monitoring
      run: |
        echo "📊 Scheduling post-rollback monitoring..."
        
        # This would typically:
        # 1. Set up enhanced monitoring
        # 2. Schedule health checks
        # 3. Plan follow-up actions
        
        echo "✅ Post-rollback monitoring scheduled"

  # Job 6: Notifications
  notify-rollback:
    runs-on: self-hosted
    needs: [rollback-validation, rollback-deploy, post-rollback]
    if: always() && github.event.inputs.notify_team == 'true'
    steps:
    - name: Send rollback notifications
      run: |
        echo "📢 Sending rollback notifications..."
        
        STATUS="success"
        if [ "${{ needs.rollback-deploy.result }}" != "success" ]; then
          STATUS="failure"
        fi
        
        # This would typically call the notification workflow
        echo "Environment: ${{ needs.rollback-validation.outputs.rollback_environment }}"
        echo "Status: $STATUS"
        echo "Target Commit: ${{ needs.rollback-validation.outputs.target_commit }}"
        echo "Reason: ${{ github.event.inputs.rollback_reason }}"
        
        echo "✅ Rollback notifications sent"

  # Job 7: Rollback summary
  rollback-summary:
    runs-on: self-hosted
    needs: [rollback-validation, rollback-testing, rollback-build, rollback-deploy, post-rollback]
    if: always()
    steps:
    - name: Rollback summary
      run: |
        echo "📊 ROLLBACK SUMMARY"
        echo "=================="
        echo "Environment: ${{ needs.rollback-validation.outputs.rollback_environment }}"
        echo "Target Commit: ${{ needs.rollback-validation.outputs.target_commit }}"
        echo "Reason: ${{ github.event.inputs.rollback_reason }}"
        echo "Actor: ${{ github.actor }}"
        echo "Timestamp: $(date -u)"
        echo ""
        echo "Job Results:"
        echo "- Validation: ${{ needs.rollback-validation.result }}"
        echo "- Testing: ${{ needs.rollback-testing.result }}"
        echo "- Build: ${{ needs.rollback-build.result }}"
        echo "- Deploy: ${{ needs.rollback-deploy.result }}"
        echo "- Post-Rollback: ${{ needs.post-rollback.result }}"
        echo ""
        
        if [ "${{ needs.rollback-validation.result }}" == "success" ] && 
           [ "${{ needs.rollback-build.result }}" == "success" ] && 
           [ "${{ needs.rollback-deploy.result }}" == "success" ]; then
          echo "✅ ROLLBACK COMPLETED SUCCESSFULLY"
          echo "🎯 System rolled back to stable state"
        else
          echo "❌ ROLLBACK FAILED"
          echo "🚨 Manual intervention required"
        fi
        echo "=================="