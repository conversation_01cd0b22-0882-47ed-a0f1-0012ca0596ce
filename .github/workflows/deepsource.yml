# GitHub Actions config to run dart-analyze and report to DeepSource
name: DeepSource Analysis

on:
  # Note that both `push` and `pull_request` triggers should be present for GitHub to consistently present dart-analyze
  # SARIF reports.
  push:
    branches: [main, development, production]
  pull_request:

jobs:
  scan:
    runs-on: self-hosted
    permissions:
      contents: read # for actions/checkout to fetch code
      security-events: write # for github/codeql-action/upload-sarif to upload SARIF results
      actions: read # only required for a private repository by github/codeql-action/upload-sarif to get the Action run status
    steps:
      - name: Code Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Setup Dart
        uses: dart-lang/setup-dart@v1

      - name: Get Flutter dependencies
        run: flutter pub get

      - name: Run Dart Analyze
        run: dart analyze > dart_analyze.txt || true

      - name: Dart Analyze to SARIF
        uses: advanced-security/dart-analyzer-sarif@main
        with:
          input: dart_analyze.txt
          output: dart_analyze.sarif

      - name: Upload SARIF report to DeepSource
        run: |
          # Install the CLI
          curl https://deepsource.io/cli | sh

          # Send the report to DeepSource
          ./bin/deepsource report --analyzer dart-analyze --analyzer-type community --value-file ./dart_analyze.sarif

        env:
          DEEPSOURCE_DSN: ${{ secrets.DEEPSOURCE_DSN }}