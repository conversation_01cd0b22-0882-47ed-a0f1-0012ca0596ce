name: Environment Promotion

on:
  workflow_dispatch:
    inputs:
      from_environment:
        description: 'Source environment'
        required: true
        type: choice
        options:
        - dev
        - staging
      to_environment:
        description: 'Target environment'
        required: true
        type: choice
        options:
        - staging
        - production
      build_type:
        description: 'Build type'
        required: true
        default: 'appbundle'
        type: choice
        options:
        - apk
        - appbundle
      confirmation:
        description: 'Type "PROMOTE" to confirm promotion'
        required: true
        type: string
      run_tests:
        description: 'Run full test suite before promotion'
        required: true
        default: true
        type: boolean

env:
  FLUTTER_VERSION: '3.32.6'
  JAVA_VERSION: '17'

jobs:
  # Job 1: Validation and pre-promotion checks
  pre-promotion-validation:
    runs-on: self-hosted
    steps:
    - name: Validate promotion request
      run: |
        echo "🔍 Validating promotion request..."
        
        # Validate confirmation
        if [ "${{ github.event.inputs.confirmation }}" != "PROMOTE" ]; then
          echo "❌ ERROR: Promotion requires 'PROMOTE' confirmation!"
          exit 1
        fi
        
        # Validate promotion path
        FROM_ENV="${{ github.event.inputs.from_environment }}"
        TO_ENV="${{ github.event.inputs.to_environment }}"
        
        # Check valid promotion paths
        if [ "$FROM_ENV" == "dev" ] && [ "$TO_ENV" == "staging" ]; then
          echo "✅ Valid promotion: dev → staging"
        elif [ "$FROM_ENV" == "staging" ] && [ "$TO_ENV" == "production" ]; then
          echo "✅ Valid promotion: staging → production"
        else
          echo "❌ ERROR: Invalid promotion path: $FROM_ENV → $TO_ENV"
          echo "Valid paths: dev → staging, staging → production"
          exit 1
        fi
        
        # Additional validation for production promotion
        if [ "$TO_ENV" == "production" ]; then
          if [ "${{ github.ref_name }}" != "production" ]; then
            echo "❌ ERROR: Production promotion can only be triggered from production branch!"
            exit 1
          fi
          echo "✅ Production promotion validation passed"
        fi
        
        echo "✅ Promotion validation completed"

    - name: Log promotion attempt
      run: |
        echo "🚀 ENVIRONMENT PROMOTION INITIATED"
        echo "=================================="
        echo "From: ${{ github.event.inputs.from_environment }}"
        echo "To: ${{ github.event.inputs.to_environment }}"
        echo "Build Type: ${{ github.event.inputs.build_type }}"
        echo "Run Tests: ${{ github.event.inputs.run_tests }}"
        echo "Timestamp: $(date -u)"
        echo "Branch: ${{ github.ref_name }}"
        echo "Actor: ${{ github.actor }}"
        echo "=================================="

  # Job 2: Source environment validation
  source-validation:
    runs-on: self-hosted
    needs: pre-promotion-validation
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Get dependencies
      run: flutter pub get

    - name: Validate source environment build
      run: |
        echo "🔍 Validating source environment: ${{ github.event.inputs.from_environment }}"
        
        # Check if source environment build exists
        FROM_ENV="${{ github.event.inputs.from_environment }}"
        BUILD_TYPE="${{ github.event.inputs.build_type }}"
        
        # This would typically check for existing artifacts or trigger a build
        # For now, we'll validate the environment configuration
        
        if [ "$FROM_ENV" == "dev" ]; then
          ./scripts/select_firebase_config.sh dev
          echo "✅ Dev environment configuration validated"
        elif [ "$FROM_ENV" == "staging" ]; then
          ./scripts/select_firebase_config.sh staging
          echo "✅ Staging environment configuration validated"
        fi
        
        echo "✅ Source environment validation completed"

  # Job 3: Optional comprehensive testing
  comprehensive-testing:
    runs-on: self-hosted
    needs: [pre-promotion-validation, source-validation]
    if: ${{ github.event.inputs.run_tests == 'true' }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Cache Flutter dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.pub-cache
          ~/.flutter
        key: ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-${{ hashFiles('**/pubspec.lock') }}
        restore-keys: |
          ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-
          ${{ runner.os }}-flutter-

    - name: Get dependencies
      run: flutter pub get

    - name: Run comprehensive test suite
      run: |
        echo "🧪 Running comprehensive test suite for promotion..."
        
        # Run all tests with coverage
        flutter test --coverage --reporter=expanded
        
        # Calculate coverage for promotion validation
        if command -v lcov &> /dev/null; then
          sudo apt-get update && sudo apt-get install -y lcov
          COVERAGE=$(lcov --summary coverage/lcov.info 2>&1 | grep "lines" | awk '{print $2}' | sed 's/%//')
          echo "Test coverage: ${COVERAGE}%"
          
          # Set minimum coverage based on target environment
          if [ "${{ github.event.inputs.to_environment }}" == "production" ]; then
            MIN_COVERAGE=90
          else
            MIN_COVERAGE=85
          fi
          
          if (( $(echo "$COVERAGE < $MIN_COVERAGE" | bc -l) )); then
            echo "❌ Coverage ${COVERAGE}% is below minimum threshold of ${MIN_COVERAGE}%"
            exit 1
          fi
          echo "✅ Coverage meets promotion requirements"
        fi

    - name: Run static analysis
      run: |
        echo "🔍 Running static analysis..."
        flutter analyze --fatal-infos --fatal-warnings
        echo "✅ Static analysis passed"

  # Job 4: Target environment build
  target-build:
    runs-on: self-hosted
    needs: [pre-promotion-validation, source-validation, comprehensive-testing]
    if: always() && (needs.pre-promotion-validation.result == 'success' && needs.source-validation.result == 'success' && (needs.comprehensive-testing.result == 'success' || needs.comprehensive-testing.result == 'skipped'))
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Cache Flutter dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.pub-cache
          ~/.flutter
        key: ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-${{ hashFiles('**/pubspec.lock') }}
        restore-keys: |
          ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-
          ${{ runner.os }}-flutter-

    - name: Cache Gradle dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: Get dependencies
      run: flutter pub get

    - name: Setup keystore for target environment
      env:
        STAGING_KEYSTORE_FILE: ${{ secrets.STAGING_KEYSTORE_FILE }}
        STAGING_KEY_ALIAS: ${{ secrets.STAGING_KEY_ALIAS }}
        STAGING_KEYSTORE_PASSWORD: ${{ secrets.STAGING_KEYSTORE_PASSWORD }}
        STAGING_KEY_PASSWORD: ${{ secrets.STAGING_KEY_PASSWORD }}
        PROD_KEYSTORE_FILE: ${{ secrets.PROD_KEYSTORE_FILE }}
        PROD_KEY_ALIAS: ${{ secrets.PROD_KEY_ALIAS }}
        PROD_KEYSTORE_PASSWORD: ${{ secrets.PROD_KEYSTORE_PASSWORD }}
        PROD_KEY_PASSWORD: ${{ secrets.PROD_KEY_PASSWORD }}
      run: |
        echo "🔐 Setting up keystore for target environment..."
        
        mkdir -p android/keystore
        
        TO_ENV="${{ github.event.inputs.to_environment }}"
        
        if [ "$TO_ENV" == "staging" ]; then
          echo "$STAGING_KEYSTORE_FILE" | base64 -d > android/keystore/staging-release.jks
          echo "STAGING_KEYSTORE_FILE=keystore/staging-release.jks" >> android/gradle.properties
          echo "STAGING_KEY_ALIAS=$STAGING_KEY_ALIAS" >> android/gradle.properties
          echo "STAGING_KEYSTORE_PASSWORD=$STAGING_KEYSTORE_PASSWORD" >> android/gradle.properties
          echo "STAGING_KEY_PASSWORD=$STAGING_KEY_PASSWORD" >> android/gradle.properties
          echo "✅ Staging keystore configured"
        elif [ "$TO_ENV" == "production" ]; then
          echo "$PROD_KEYSTORE_FILE" | base64 -d > android/keystore/prod-release.jks
          echo "PROD_KEYSTORE_FILE=keystore/prod-release.jks" >> android/gradle.properties
          echo "PROD_KEY_ALIAS=$PROD_KEY_ALIAS" >> android/gradle.properties
          echo "PROD_KEYSTORE_PASSWORD=$PROD_KEYSTORE_PASSWORD" >> android/gradle.properties
          echo "PROD_KEY_PASSWORD=$PROD_KEY_PASSWORD" >> android/gradle.properties
          echo "✅ Production keystore configured"
        fi

    - name: Build for target environment
      run: |
        echo "🏗️ Building for target environment: ${{ github.event.inputs.to_environment }}"
        
        TO_ENV="${{ github.event.inputs.to_environment }}"
        BUILD_TYPE="${{ github.event.inputs.build_type }}"
        
        # Select appropriate Firebase configuration
        ./scripts/select_firebase_config.sh $TO_ENV
        
        # Build for target environment
        if [ "$BUILD_TYPE" == "appbundle" ]; then
          flutter build appbundle --flavor $TO_ENV --dart-define=FLAVOR=$TO_ENV --release
        else
          flutter build apk --flavor $TO_ENV --dart-define=FLAVOR=$TO_ENV --release
        fi
        
        echo "✅ Build completed for $TO_ENV environment"

    - name: Validate target build
      run: |
        echo "🔍 Validating target build..."
        
        TO_ENV="${{ github.event.inputs.to_environment }}"
        BUILD_TYPE="${{ github.event.inputs.build_type }}"
        
        # Find the built file
        if [ "$BUILD_TYPE" == "appbundle" ]; then
          if [ "$TO_ENV" == "staging" ]; then
            BUILD_FILE="build/app/outputs/bundle/stagingRelease/app-staging-release.aab"
          elif [ "$TO_ENV" == "production" ]; then
            BUILD_FILE="build/app/outputs/bundle/prodRelease/app-prod-release.aab"
          fi
        else
          BUILD_FILE="build/app/outputs/flutter-apk/app-$TO_ENV-release.apk"
        fi
        
        # Validate signature
        jarsigner -verify -verbose "$BUILD_FILE"
        
        # Check for debug keys
        if jarsigner -verify -verbose -certs "$BUILD_FILE" | grep -i "debug"; then
          echo "❌ ERROR: Debug keys detected in promoted build!"
          exit 1
        fi
        
        echo "✅ Target build validation passed"

    - name: Upload promoted build
      uses: actions/upload-artifact@v4
      with:
        name: promoted-${{ github.event.inputs.to_environment }}-${{ github.event.inputs.build_type }}-${{ github.sha }}
        path: |
          build/app/outputs/flutter-apk/app-${{ github.event.inputs.to_environment }}-release.apk
          build/app/outputs/bundle/*/app-${{ github.event.inputs.to_environment }}-release.aab
        retention-days: 90

  # Job 5: Post-promotion validation
  post-promotion-validation:
    runs-on: self-hosted
    needs: [target-build]
    if: always() && needs.target-build.result == 'success'
    steps:
    - name: Validation summary
      run: |
        echo "✅ PROMOTION VALIDATION SUMMARY"
        echo "==============================="
        echo "Source: ${{ github.event.inputs.from_environment }}"
        echo "Target: ${{ github.event.inputs.to_environment }}"
        echo "Build Type: ${{ github.event.inputs.build_type }}"
        echo "Tests Run: ${{ github.event.inputs.run_tests }}"
        echo "Status: SUCCESS"
        echo "==============================="

    - name: Generate promotion report
      run: |
        echo "📊 Promotion Report"
        echo "=================="
        echo "Promotion ID: promotion-${{ github.run_number }}"
        echo "From Environment: ${{ github.event.inputs.from_environment }}"
        echo "To Environment: ${{ github.event.inputs.to_environment }}"
        echo "Build Type: ${{ github.event.inputs.build_type }}"
        echo "Branch: ${{ github.ref_name }}"
        echo "Commit: ${{ github.sha }}"
        echo "Actor: ${{ github.actor }}"
        echo "Timestamp: $(date -u)"
        echo "Tests Executed: ${{ github.event.inputs.run_tests }}"
        echo "Status: ✅ SUCCESS"
        echo ""
        echo "Next Steps:"
        if [ "${{ github.event.inputs.to_environment }}" == "staging" ]; then
          echo "- Verify staging deployment"
          echo "- Run acceptance tests"
          echo "- Consider promotion to production"
        elif [ "${{ github.event.inputs.to_environment }}" == "production" ]; then
          echo "- Verify production deployment"
          echo "- Monitor production metrics"
          echo "- Update release documentation"
        fi

  # Job 6: Notification
  notification:
    runs-on: self-hosted
    needs: [target-build, post-promotion-validation]
    if: always()
    steps:
    - name: Promotion notification
      run: |
        echo "🚀 ENVIRONMENT PROMOTION COMPLETED"
        echo "=================================="
        echo "Promotion: ${{ github.event.inputs.from_environment }} → ${{ github.event.inputs.to_environment }}"
        echo "Build Status: ${{ needs.target-build.result }}"
        echo "Validation Status: ${{ needs.post-promotion-validation.result }}"
        echo "Timestamp: $(date -u)"
        echo "=================================="
        
        if [ "${{ needs.target-build.result }}" == "success" ] && [ "${{ needs.post-promotion-validation.result }}" == "success" ]; then
          echo "✅ PROMOTION SUCCESSFUL!"
          echo "🎯 Environment promoted successfully"
        else
          echo "❌ PROMOTION FAILED!"
          echo "🚨 Promotion requires immediate attention"
        fi