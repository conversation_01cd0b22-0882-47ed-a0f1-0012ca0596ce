name: Deployment Notifications

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: 'Environment (dev, staging, production)'
      status:
        required: true
        type: string
        description: 'Deployment status (success, failure, started)'
      build_type:
        required: false
        type: string
        default: 'apk'
        description: 'Build type (apk, appbundle)'
      commit_sha:
        required: false
        type: string
        default: ''
        description: 'Commit SHA'
      actor:
        required: false
        type: string
        default: ''
        description: 'Actor who triggered the deployment'
      workflow_run_id:
        required: false
        type: string
        default: ''
        description: 'Workflow run ID'
      additional_info:
        required: false
        type: string
        default: ''
        description: 'Additional information'
  
  workflow_dispatch:
    inputs:
      environment:
        required: true
        type: choice
        options:
        - dev
        - staging
        - production
        description: 'Environment'
      status:
        required: true
        type: choice
        options:
        - success
        - failure
        - started
        description: 'Deployment status'
      build_type:
        required: false
        type: choice
        default: 'apk'
        options:
        - apk
        - appbundle
        description: 'Build type'
      test_notification:
        required: false
        type: boolean
        default: false
        description: 'Send as test notification'

jobs:
  send-notifications:
    runs-on: self-hosted
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Prepare notification data
      id: prepare
      run: |
        echo "🔧 Preparing notification data..."
        
        # Get input values
        ENVIRONMENT="${{ inputs.environment }}"
        STATUS="${{ inputs.status }}"
        BUILD_TYPE="${{ inputs.build_type || 'apk' }}"
        COMMIT_SHA="${{ inputs.commit_sha || github.sha }}"
        ACTOR="${{ inputs.actor || github.actor }}"
        WORKFLOW_RUN_ID="${{ inputs.workflow_run_id || github.run_id }}"
        ADDITIONAL_INFO="${{ inputs.additional_info }}"
        
        # Set environment-specific colors and emojis
        case $ENVIRONMENT in
          "dev")
            ENV_COLOR="#FF9500"
            ENV_EMOJI="🧪"
            ;;
          "staging")
            ENV_COLOR="#007AFF"
            ENV_EMOJI="🚀"
            ;;
          "production")
            ENV_COLOR="#34C759"
            ENV_EMOJI="🎯"
            ;;
          *)
            ENV_COLOR="#8E8E93"
            ENV_EMOJI="⚙️"
            ;;
        esac
        
        # Set status-specific colors and emojis
        case $STATUS in
          "success")
            STATUS_COLOR="#34C759"
            STATUS_EMOJI="✅"
            ;;
          "failure")
            STATUS_COLOR="#FF3B30"
            STATUS_EMOJI="❌"
            ;;
          "started")
            STATUS_COLOR="#FF9500"
            STATUS_EMOJI="🔄"
            ;;
          *)
            STATUS_COLOR="#8E8E93"
            STATUS_EMOJI="ℹ️"
            ;;
        esac
        
        # Format timestamp
        TIMESTAMP=$(date -u "+%Y-%m-%d %H:%M:%S UTC")
        
        # Create notification title
        TITLE="$ENV_EMOJI BudApp $ENVIRONMENT Deployment $STATUS_EMOJI"
        
        # Create notification message
        MESSAGE="**Environment**: $ENVIRONMENT"$'\n'
        MESSAGE="$MESSAGE**Status**: $STATUS"$'\n'
        MESSAGE="$MESSAGE**Build Type**: $BUILD_TYPE"$'\n'
        MESSAGE="$MESSAGE**Commit**: \`${COMMIT_SHA:0:8}\`"$'\n'
        MESSAGE="$MESSAGE**Actor**: $ACTOR"$'\n'
        MESSAGE="$MESSAGE**Timestamp**: $TIMESTAMP"$'\n'
        
        if [ -n "$ADDITIONAL_INFO" ]; then
          MESSAGE="$MESSAGE**Additional Info**: $ADDITIONAL_INFO"$'\n'
        fi
        
        # Add workflow link
        WORKFLOW_URL="https://github.com/${{ github.repository }}/actions/runs/$WORKFLOW_RUN_ID"
        MESSAGE="$MESSAGE**Workflow**: [View Details]($WORKFLOW_URL)"
        
        # Export variables for other steps
        echo "title=$TITLE" >> $GITHUB_OUTPUT
        echo "message=$MESSAGE" >> $GITHUB_OUTPUT
        echo "env_color=$ENV_COLOR" >> $GITHUB_OUTPUT
        echo "status_color=$STATUS_COLOR" >> $GITHUB_OUTPUT
        echo "environment=$ENVIRONMENT" >> $GITHUB_OUTPUT
        echo "status=$STATUS" >> $GITHUB_OUTPUT
        
        echo "✅ Notification data prepared"

    - name: Send Slack notification
      if: ${{ !inputs.test_notification }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      run: |
        echo "📱 Sending Slack notification..."
        
        # Check if Slack webhook is configured
        if [ -z "$SLACK_WEBHOOK_URL" ]; then
          echo "⚠️ Slack webhook not configured, skipping Slack notification"
          exit 0
        fi
        
        # Create Slack message payload
        SLACK_PAYLOAD=$(cat <<EOF
        {
          "text": "${{ steps.prepare.outputs.title }}",
          "attachments": [
            {
              "color": "${{ steps.prepare.outputs.status_color }}",
              "fields": [
                {
                  "title": "Environment",
                  "value": "${{ steps.prepare.outputs.environment }}",
                  "short": true
                },
                {
                  "title": "Status",
                  "value": "${{ steps.prepare.outputs.status }}",
                  "short": true
                },
                {
                  "title": "Build Type",
                  "value": "${{ inputs.build_type || 'apk' }}",
                  "short": true
                },
                {
                  "title": "Actor",
                  "value": "${{ inputs.actor || github.actor }}",
                  "short": true
                },
                {
                  "title": "Commit",
                  "value": "<https://github.com/${{ github.repository }}/commit/${{ inputs.commit_sha || github.sha }}|${{ inputs.commit_sha || github.sha }}>"
                },
                {
                  "title": "Workflow",
                  "value": "<https://github.com/${{ github.repository }}/actions/runs/${{ inputs.workflow_run_id || github.run_id }}|View Details>"
                }
              ],
              "footer": "BudApp CI/CD",
              "ts": $(date +%s)
            }
          ]
        }
        EOF
        )
        
        # Send to Slack
        curl -X POST -H 'Content-type: application/json' \
          --data "$SLACK_PAYLOAD" \
          "$SLACK_WEBHOOK_URL"
        
        echo "✅ Slack notification sent"

    - name: Send Teams notification
      if: ${{ !inputs.test_notification }}
      env:
        TEAMS_WEBHOOK_URL: ${{ secrets.TEAMS_WEBHOOK_URL }}
      run: |
        echo "📧 Sending Teams notification..."
        
        # Check if Teams webhook is configured
        if [ -z "$TEAMS_WEBHOOK_URL" ]; then
          echo "⚠️ Teams webhook not configured, skipping Teams notification"
          exit 0
        fi
        
        # Create Teams message payload
        TEAMS_PAYLOAD=$(cat <<EOF
        {
          "@type": "MessageCard",
          "@context": "https://schema.org/extensions",
          "summary": "${{ steps.prepare.outputs.title }}",
          "themeColor": "${{ steps.prepare.outputs.status_color }}",
          "sections": [
            {
              "activityTitle": "${{ steps.prepare.outputs.title }}",
              "activityImage": "https://github.com/identicons/${{ github.repository_owner }}.png",
              "facts": [
                {
                  "name": "Environment",
                  "value": "${{ steps.prepare.outputs.environment }}"
                },
                {
                  "name": "Status",
                  "value": "${{ steps.prepare.outputs.status }}"
                },
                {
                  "name": "Build Type",
                  "value": "${{ inputs.build_type || 'apk' }}"
                },
                {
                  "name": "Actor",
                  "value": "${{ inputs.actor || github.actor }}"
                },
                {
                  "name": "Commit",
                  "value": "${{ inputs.commit_sha || github.sha }}"
                },
                {
                  "name": "Repository",
                  "value": "${{ github.repository }}"
                }
              ]
            }
          ],
          "potentialAction": [
            {
              "@type": "OpenUri",
              "name": "View Workflow",
              "targets": [
                {
                  "os": "default",
                  "uri": "https://github.com/${{ github.repository }}/actions/runs/${{ inputs.workflow_run_id || github.run_id }}"
                }
              ]
            },
            {
              "@type": "OpenUri",
              "name": "View Commit",
              "targets": [
                {
                  "os": "default",
                  "uri": "https://github.com/${{ github.repository }}/commit/${{ inputs.commit_sha || github.sha }}"
                }
              ]
            }
          ]
        }
        EOF
        )
        
        # Send to Teams
        curl -X POST -H 'Content-type: application/json' \
          --data "$TEAMS_PAYLOAD" \
          "$TEAMS_WEBHOOK_URL"
        
        echo "✅ Teams notification sent"

    - name: Send email notification
      if: ${{ (inputs.environment == 'production' || inputs.status == 'failure') && !inputs.test_notification }}
      env:
        EMAIL_SMTP_HOST: ${{ secrets.EMAIL_SMTP_HOST }}
        EMAIL_SMTP_PORT: ${{ secrets.EMAIL_SMTP_PORT }}
        EMAIL_USERNAME: ${{ secrets.EMAIL_USERNAME }}
        EMAIL_PASSWORD: ${{ secrets.EMAIL_PASSWORD }}
        EMAIL_FROM: ${{ secrets.EMAIL_FROM }}
        EMAIL_TO: ${{ secrets.EMAIL_TO }}
      run: |
        echo "📧 Sending email notification..."
        
        # Check if email is configured
        if [ -z "$EMAIL_SMTP_HOST" ] || [ -z "$EMAIL_TO" ]; then
          echo "⚠️ Email not configured, skipping email notification"
          exit 0
        fi
        
        # Create email content
        EMAIL_SUBJECT="BudApp ${{ steps.prepare.outputs.environment }} Deployment ${{ steps.prepare.outputs.status }}"
        EMAIL_BODY=$(cat <<EOF
        BudApp Deployment Notification
        
        Environment: ${{ steps.prepare.outputs.environment }}
        Status: ${{ steps.prepare.outputs.status }}
        Build Type: ${{ inputs.build_type || 'apk' }}
        Commit: ${{ inputs.commit_sha || github.sha }}
        Actor: ${{ inputs.actor || github.actor }}
        Timestamp: $(date -u)
        
        Workflow: https://github.com/${{ github.repository }}/actions/runs/${{ inputs.workflow_run_id || github.run_id }}
        Commit: https://github.com/${{ github.repository }}/commit/${{ inputs.commit_sha || github.sha }}
        
        ${{ inputs.additional_info }}
        
        This is an automated notification from BudApp CI/CD.
        EOF
        )
        
        # Send email using Python (available on GitHub runners)
        python3 -c "
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        import os
        
        # Email configuration
        smtp_host = os.environ.get('EMAIL_SMTP_HOST', 'smtp.gmail.com')
        smtp_port = int(os.environ.get('EMAIL_SMTP_PORT', '587'))
        username = os.environ.get('EMAIL_USERNAME')
        password = os.environ.get('EMAIL_PASSWORD')
        from_email = os.environ.get('EMAIL_FROM', username)
        to_email = os.environ.get('EMAIL_TO')
        
        if not all([username, password, to_email]):
            print('Email credentials not configured')
            exit(0)
        
        # Create message
        msg = MIMEMultipart()
        msg['From'] = from_email
        msg['To'] = to_email
        msg['Subject'] = '$EMAIL_SUBJECT'
        
        body = '''$EMAIL_BODY'''
        msg.attach(MIMEText(body, 'plain'))
        
        # Send email
        try:
            server = smtplib.SMTP(smtp_host, smtp_port)
            server.starttls()
            server.login(username, password)
            server.send_message(msg)
            server.quit()
            print('Email sent successfully')
        except Exception as e:
            print(f'Failed to send email: {e}')
        "
        
        echo "✅ Email notification processed"

    - name: Create deployment status
      if: ${{ !inputs.test_notification }}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        echo "📊 Creating deployment status..."
        
        # Set deployment status
        DEPLOYMENT_STATUS="success"
        if [ "${{ steps.prepare.outputs.status }}" == "failure" ]; then
          DEPLOYMENT_STATUS="failure"
        elif [ "${{ steps.prepare.outputs.status }}" == "started" ]; then
          DEPLOYMENT_STATUS="in_progress"
        fi
        
        # Create deployment status
        gh api repos/${{ github.repository }}/deployments \
          --method POST \
          --field ref='${{ inputs.commit_sha || github.sha }}' \
          --field environment='${{ steps.prepare.outputs.environment }}' \
          --field description="BudApp ${{ steps.prepare.outputs.environment }} deployment" \
          --field auto_merge=false \
          --field required_contexts='[]' > deployment.json
        
        DEPLOYMENT_ID=$(cat deployment.json | jq -r '.id')
        
        # Update deployment status
        gh api repos/${{ github.repository }}/deployments/$DEPLOYMENT_ID/statuses \
          --method POST \
          --field state="$DEPLOYMENT_STATUS" \
          --field description="Deployment ${{ steps.prepare.outputs.status }}" \
          --field environment_url="https://budapp-${{ steps.prepare.outputs.environment }}.web.app" \
          --field log_url="https://github.com/${{ github.repository }}/actions/runs/${{ inputs.workflow_run_id || github.run_id }}"
        
        echo "✅ Deployment status created"

    - name: Update deployment metrics
      if: ${{ !inputs.test_notification }}
      run: |
        echo "📈 Updating deployment metrics..."
        
        # Create metrics file
        METRICS_FILE="deployment-metrics-$(date +%Y%m%d).json"
        
        # Create or update metrics
        cat > $METRICS_FILE << EOF
        {
          "timestamp": "$(date -u --iso-8601=seconds)",
          "environment": "${{ steps.prepare.outputs.environment }}",
          "status": "${{ steps.prepare.outputs.status }}",
          "build_type": "${{ inputs.build_type || 'apk' }}",
          "commit_sha": "${{ inputs.commit_sha || github.sha }}",
          "actor": "${{ inputs.actor || github.actor }}",
          "workflow_run_id": "${{ inputs.workflow_run_id || github.run_id }}",
          "repository": "${{ github.repository }}",
          "branch": "${{ github.ref_name }}"
        }
        EOF
        
        echo "📊 Metrics recorded:"
        cat $METRICS_FILE
        
        echo "✅ Deployment metrics updated"

    - name: Test notification summary
      if: ${{ inputs.test_notification }}
      run: |
        echo "🧪 TEST NOTIFICATION SUMMARY"
        echo "============================="
        echo "Title: ${{ steps.prepare.outputs.title }}"
        echo "Environment: ${{ steps.prepare.outputs.environment }}"
        echo "Status: ${{ steps.prepare.outputs.status }}"
        echo "Message:"
        echo "${{ steps.prepare.outputs.message }}"
        echo "============================="
        echo "✅ Test notification completed (no external notifications sent)"

    - name: Notification summary
      if: ${{ !inputs.test_notification }}
      run: |
        echo "📱 NOTIFICATION SUMMARY"
        echo "======================="
        echo "Environment: ${{ steps.prepare.outputs.environment }}"
        echo "Status: ${{ steps.prepare.outputs.status }}"
        echo "Notifications sent:"
        echo "- Slack: ${{ secrets.SLACK_WEBHOOK_URL != '' && '✅' || '⚠️ Not configured' }}"
        echo "- Teams: ${{ secrets.TEAMS_WEBHOOK_URL != '' && '✅' || '⚠️ Not configured' }}"
        echo "- Email: ${{ (inputs.environment == 'production' || inputs.status == 'failure') && secrets.EMAIL_TO != '' && '✅' || '⚠️ Not sent' }}"
        echo "- GitHub Deployment: ✅"
        echo "- Metrics: ✅"
        echo "======================="
        echo "✅ All notifications processed"