name: Staging Build & Deploy

on:
  workflow_dispatch:
    inputs:
      build_type:
        description: 'Build type'
        required: true
        default: 'appbundle'
        type: choice
        options:
        - apk
        - appbundle

env:
  FLUTTER_VERSION: '3.32.6'
  JAVA_VERSION: '17'
  ENVIRONMENT: 'staging'

jobs:
  # Job 1: Run tests and validation
  test:
    runs-on: self-hosted
    environment: staging
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Cache Flutter dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.pub-cache
          ~/.flutter
        key: ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-${{ hashFiles('**/pubspec.lock') }}
        restore-keys: |
          ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-
          ${{ runner.os }}-flutter-

    - name: Get Flutter dependencies
      run: flutter pub get

    - name: Verify Flutter installation
      run: flutter doctor -v

    - name: Check code formatting
      run: |
        echo "🎨 Checking code formatting..."
        dart format --set-exit-if-changed .
        if [ $? -ne 0 ]; then
          echo "❌ Code formatting issues found. Run 'dart format .' to fix."
          exit 1
        fi
        echo "✅ Code formatting is consistent"

    - name: Run comprehensive static analysis
      run: |
        echo "🔍 Running comprehensive static analysis..."
        flutter analyze --fatal-infos --fatal-warnings
        if [ $? -ne 0 ]; then
          echo "❌ Static analysis failed. Please fix all issues before proceeding."
          exit 1
        fi
        echo "✅ Static analysis passed with zero issues"

    - name: Run Flutter tests
      run: |
        echo "🧪 Running comprehensive test suite..."
        flutter test --reporter=expanded
        if [ $? -ne 0 ]; then
          echo "❌ Tests failed. Please fix failing tests before proceeding."
          exit 1
        fi
        echo "✅ All tests passed"

  # Job 2: Build Android APK/AAB for staging environment
  build-staging:
    runs-on: self-hosted
    environment: staging
    needs: test
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Cache Flutter dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.pub-cache
          ~/.flutter
        key: ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-${{ hashFiles('**/pubspec.lock') }}
        restore-keys: |
          ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-
          ${{ runner.os }}-flutter-

    - name: Cache Gradle dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: Get Flutter dependencies
      run: flutter pub get

    - name: Setup keystore for staging environment
      env:
        STAGING_KEYSTORE_FILE: ${{ secrets.STAGING_KEYSTORE_FILE }}
        STAGING_KEY_ALIAS: ${{ secrets.STAGING_KEY_ALIAS }}
        STAGING_KEYSTORE_PASSWORD: ${{ secrets.STAGING_KEYSTORE_PASSWORD }}
        STAGING_KEY_PASSWORD: ${{ secrets.STAGING_KEY_PASSWORD }}
      run: |
        # Create keystore directory
        mkdir -p android/keystore
        
        # Decode and save keystore file
        echo "$STAGING_KEYSTORE_FILE" | base64 -d > android/keystore/staging-release.jks
        
        # Verify keystore was created successfully
        ls -la android/keystore/
        
        # Set environment variables for Gradle
        echo "STAGING_KEYSTORE_FILE=$PWD/android/keystore/staging-release.jks" >> $GITHUB_ENV
        echo "STAGING_KEY_ALIAS=$STAGING_KEY_ALIAS" >> $GITHUB_ENV
        echo "STAGING_KEYSTORE_PASSWORD=$STAGING_KEYSTORE_PASSWORD" >> $GITHUB_ENV
        echo "STAGING_KEY_PASSWORD=$STAGING_KEY_PASSWORD" >> $GITHUB_ENV

    - name: Build for staging environment
      run: |
        echo "🏗️ Building for staging environment..."
        
        # Select staging Firebase configuration
        ./scripts/select_firebase_config.sh staging
        
        # Build based on input parameter or default to App Bundle
        BUILD_TYPE="${{ github.event.inputs.build_type || 'appbundle' }}"
        if [ "$BUILD_TYPE" == "appbundle" ]; then
          echo "Building App Bundle..."
          flutter build appbundle --flavor staging --dart-define=FLAVOR=staging --release
        else
          echo "Building APK..."
          flutter build apk --flavor staging --dart-define=FLAVOR=staging --release
        fi
        
        echo "✅ Build completed successfully"

    - name: Validate staging build signature
      run: |
        echo "🔍 Validating build signature..."
        
        # Find the built file
        BUILD_TYPE="${{ github.event.inputs.build_type || 'appbundle' }}"
        if [ "$BUILD_TYPE" == "appbundle" ]; then
          BUILD_FILE="build/app/outputs/bundle/stagingRelease/app-staging-release.aab"
        else
          BUILD_FILE="build/app/outputs/flutter-apk/app-staging-release.apk"
        fi
        
        # Verify the build is properly signed
        jarsigner -verify -verbose "$BUILD_FILE"
        
        # Check that debug keys are NOT used
        if jarsigner -verify -verbose -certs "$BUILD_FILE" | grep -i "debug"; then
          echo "❌ ERROR: Debug keys detected in staging release build!"
          exit 1
        else
          echo "✅ Build is properly signed with staging release keys"
        fi

    - name: Upload staging build artifact
      uses: actions/upload-artifact@v4
      with:
        name: app-staging-release-${{ github.event.inputs.build_type || 'appbundle' }}
        path: |
          build/app/outputs/flutter-apk/app-staging-release.apk
          build/app/outputs/bundle/stagingRelease/app-staging-release.aab
        retention-days: 30  # Longer retention for staging builds

  # Job 3: Enhanced security validation for staging
  security-check:
    runs-on: self-hosted
    environment: staging
    needs: [build-staging]
    if: always()
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: app-staging-release-${{ github.event.inputs.build_type || 'appbundle' }}
        path: ./artifacts/

    - name: Enhanced security validation
      run: |
        echo "🔍 Running enhanced security validation for staging..."
        
        # Check that keystore files are not in the repository
        if find . -name "*.jks" -o -name "*.keystore" | grep -v ".git"; then
          echo "❌ ERROR: Keystore files found in repository!"
          exit 1
        fi
        
        # Check that passwords are not in gradle.properties
        if grep -E "(PASSWORD|password)" android/gradle.properties | grep -v "^#"; then
          echo "⚠️  WARNING: Passwords found in gradle.properties - ensure they are for documentation only"
        fi
        
        # Verify build signature if artifact exists
        BUILD_TYPE="${{ github.event.inputs.build_type || 'appbundle' }}"
        if [ "$BUILD_TYPE" == "appbundle" ]; then
          BUILD_FILE="./artifacts/app-staging-release.aab"
        else
          BUILD_FILE="./artifacts/app-staging-release.apk"
        fi
        
        if [ -f "$BUILD_FILE" ]; then
          jarsigner -verify "$BUILD_FILE"
          echo "✅ Build signature verified"
          
          # Additional security checks for staging
          echo "🔐 Running additional security checks..."
          
          # Check for proper package name
          if command -v aapt &> /dev/null; then
            PACKAGE_NAME=$(aapt dump badging "$BUILD_FILE" | grep package | awk '{print $2}' | sed "s/name='//" | sed "s/'.*//")
            echo "Package name: $PACKAGE_NAME"
            
            # Verify staging package name
            if [[ "$PACKAGE_NAME" != *"staging"* ]]; then
              echo "⚠️  WARNING: Package name doesn't contain 'staging' identifier"
            fi
          fi
        fi
        
        echo "🔐 Enhanced security validation completed"

  # Job 4: Staging deployment notification
  notify:
    runs-on: self-hosted
    environment: staging
    needs: [build-staging, security-check]
    if: always()
    steps:
    - name: Staging deployment notification
      run: |
        echo "🚀 Staging build completed!"
        echo "Environment: ${{ env.ENVIRONMENT }}"
        echo "Branch: ${{ github.ref_name }}"
        echo "Commit: ${{ github.sha }}"
        echo "Build Status: ${{ needs.build-staging.result }}"
        echo "Security Status: ${{ needs.security-check.result }}"
        
        if [ "${{ needs.build-staging.result }}" == "success" ] && [ "${{ needs.security-check.result }}" == "success" ]; then
          echo "✅ Staging deployment successful!"
          echo "🎯 Ready for pre-production testing"
        else
          echo "❌ Staging deployment failed!"
        fi