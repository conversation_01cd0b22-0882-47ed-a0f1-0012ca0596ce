# BudApp Project Reconstruction Guide

This document provides a comprehensive guide for rebuilding the BudApp project from scratch. It incorporates lessons learned from the previous implementation and leverages modern, industry-standard best practices for Flutter development.

## 1. Technical Architecture Overview

The reconstructed BudApp will be built using a modern, scalable, and maintainable architecture based on the following principles:

- **Offline-First:** The application will be fully functional without an internet connection, with seamless data synchronization when a connection is available.
- **Feature-Based Project Structure:** The codebase will be organized by features, promoting modularity and separation of concerns.
- **Clean Architecture:** The application will follow the principles of Clean Architecture, with a clear separation between the presentation, domain, and data layers.
- **Reactive State Management:** Riverpod will be used for reactive state management, ensuring a predictable and maintainable application state.

### Technology Stack

- **Frontend:** Flutter SDK (latest stable version) with Dart 3
- **Backend:** Firebase (Firestore, Firebase Authentication, Firebase Storage, Firebase Remote Config, Firebase Cloud Messaging)
- **State Management:** Riverpod
- **Navigation:** go_router
- **Local Database:** Hive for fast, lightweight local storage
- **Data Models:** Freezed for immutable data models

### Architectural Layers

The application will be structured into the following layers:

- **Presentation Layer:** Contains the UI components (widgets), screens, and view models. This layer will be responsible for displaying data to the user and handling user input.
- **Domain Layer:** Contains the business logic of the application, including use cases, entities, and value objects. This layer will be independent of any specific framework or technology.
- **Data Layer:** Contains the repositories and data sources responsible for fetching and storing data. This layer will abstract the data sources (local and remote) from the rest of the application.


## 2. Dependencies and Versions

The following dependencies are recommended for the reconstructed BudApp project. Versions should be kept up-to-date with the latest stable releases.

### Core Dependencies

- `flutter_sdk`: `^3.x.x`
- `firebase_core`: `^2.x.x`
- `firebase_auth`: `^4.x.x`
- `cloud_firestore`: `^4.x.x`
- `firebase_storage`: `^11.x.x`
- `firebase_remote_config`: `^4.x.x`
- `firebase_messaging`: `^14.x.x`
- `flutter_riverpod`: `^2.x.x`
- `riverpod_annotation`: `^2.x.x`
- `go_router`: `^10.x.x`
- `freezed`: `^2.x.x`
- `freezed_annotation`: `^2.x.x`
- `hive`: `^2.x.x`
- `hive_flutter`: `^1.x.x`
- `connectivity_plus`: `^4.x.x`
- `google_sign_in`: `^6.x.x`
- `sign_in_with_apple`: `^5.x.x`
- `local_auth`: `^2.x.x`

### Development Dependencies

- `build_runner`: `^2.x.x`
- `riverpod_generator`: `^2.x.x`
- `hive_generator`: `^2.x.x`
- `flutter_lints`: `^2.x.x`
- `test`: `^1.x.x`
- `flutter_test`: `sdk: flutter`
- `mockito`: `^5.x.x`


## 3. Firebase Configuration and Security Rules

### Firebase Setup

The following Firebase services need to be enabled and configured for the BudApp project:

- **Firebase Authentication:** Enable Email/Password, Google, and Apple sign-in providers.
- **Cloud Firestore:** Create a Firestore database and configure it for the appropriate region.
- **Firebase Storage:** Set up a Cloud Storage bucket for storing user-generated content (e.g., receipt images).
- **Firebase Remote Config:** Use Remote Config to manage feature flags and other dynamic configuration.
- **Firebase Cloud Messaging:** Configure FCM for sending push notifications.

### Firestore Security Rules

The following is a template for the Firestore security rules. These rules should be adapted and extended to meet the specific needs of the application.

```
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read, write: if request.auth.uid == userId;

      match /accounts/{accountId} {
        allow read, write: if request.auth.uid == userId;
      }

      match /transactions/{transactionId} {
        allow read, write: if request.auth.uid == userId;
      }

      match /categories/{categoryId} {
        allow read, write: if request.auth.uid == userId;
      }

      match /budgets/{budgetId} {
        allow read, write: if request.auth.uid == userId;
      }

      match /goals/{goalId} {
        allow read, write: if request.auth.uid == userId;
      }
    }
  }
}
```


## 4. Folder Structure and File Organization

The following folder structure is recommended for the reconstructed BudApp project:

```
lib/
├── src/
│   ├── core/
│   │   ├── config/
│   │   ├── constants/
│   │   ├── di/
│   │   ├── navigation/
│   │   └── theme/
│   ├── data/
│   │   ├── local/
│   │   │   ├── daos/
│   │   │   └── database.dart
│   │   ├── remote/
│   │   │   ├── firebase_auth_service.dart
│   │   │   └── firestore_service.dart
│   │   └── repositories/
│   ├── domain/
│   │   ├── entities/
│   │   ├── enums/
│   │   ├── failures/
│   │   ├── repositories/
│   │   └── usecases/
│   └── features/
│       ├── auth/
│       │   ├── presentation/
│       │   │   ├── providers/
│       │   │   ├── screens/
│       │   │   └── widgets/
│       │   ├── data/
│       │   └── domain/
│       ├── dashboard/
│       └── ... (other features)
└── main.dart
```


## 5. Key Design Patterns and Architectural Decisions

- **Feature-Based Architecture:** The project will be organized by features (e.g., auth, dashboard, accounts) to promote modularity and separation of concerns. Each feature will have its own presentation, data, and domain layers.
- **Repository Pattern:** The repository pattern will be used to abstract the data layer from the rest of the application. This will allow for easy swapping of data sources (e.g., from Firebase to a different backend) without affecting the business logic.
- **Dependency Injection:** Riverpod will be used for dependency injection, making it easy to provide dependencies to different parts of the application.
- **Immutable Data Models:** Freezed will be used to create immutable data models, which helps to prevent bugs and makes the application state more predictable.
- **Offline-First:** The application will be designed to be fully functional offline. This will be achieved by using a local database (Hive) as the single source of truth and synchronizing it with the remote server (Firebase) when a connection is available.


## 6. State Management with Riverpod

Riverpod will be used as the primary state management solution in the reconstructed BudApp. The following providers will be used:

- **Provider:** For providing simple, read-only values (e.g., API keys, configuration objects).
- **StateProvider:** For managing simple, mutable state (e.g., form input, UI state).
- **FutureProvider:** For managing asynchronous state that comes from a `Future` (e.g., fetching data from an API).
- **StreamProvider:** For managing asynchronous state that comes from a `Stream` (e.g., listening to a Firestore collection).
- **NotifierProvider:** For managing more complex, mutable state that requires business logic.

Providers will be organized by feature and will be defined in the `presentation/providers` directory of each feature.

## 7. Offline-First Implementation Strategy

The reconstructed BudApp will be designed to be fully functional offline. This will be achieved by using a local database (Hive) as the single source of truth for the application. The local database will be synchronized with the remote server (Firebase) when a connection is available.

### Data Synchronization

Data synchronization will be handled by a dedicated `SyncService`. This service will be responsible for:

- **Detecting network status:** The `connectivity_plus` package will be used to monitor the network status.
- **Queueing offline changes:** When the application is offline, any changes made by the user will be queued in the local database.
- **Synchronizing data:** When a connection is available, the `SyncService` will synchronize the queued changes with the remote server.
- **Conflict resolution:** A "last-write-wins" strategy will be used to resolve any conflicts that may occur during synchronization.


## 8. Testing Strategy and Coverage Approach

A comprehensive testing strategy will be implemented to ensure the quality and reliability of the reconstructed BudApp. The following types of tests will be written:

- **Unit Tests:** These will test the individual units of code (e.g., functions, classes) in isolation. The `test` package will be used for writing unit tests.
- **Widget Tests:** These will test the individual widgets in isolation. The `flutter_test` package will be used for writing widget tests.
- **Integration Tests:** These will test the complete application or a large part of it. The `integration_test` package will be used for writing integration tests.

### Code Coverage

A target code coverage of **90%** will be set for the reconstructed BudApp. This will ensure that all critical parts of the application are well-tested.

## 9. UI/UX Design System and Components

A consistent and visually appealing UI/UX design system will be implemented in the reconstructed BudApp. The design system will be based on Material 3 and will include the following:

- **Color Palette:** A primary and secondary color palette will be defined to ensure a consistent look and feel throughout the application.
- **Typography:** A set of typography styles will be defined for headings, subtitles, body text, and captions.
- **Reusable Components:** A library of reusable UI components will be created, including buttons, text fields, cards, and dialogs. These components will be designed to be flexible and customizable.


## 10. Lessons Learned

The following are the key lessons learned from the previous implementation of BudApp:

- **Lack of a clear architecture:** The previous implementation lacked a clear and consistent architecture, which made it difficult to maintain and scale.
- **Tight coupling between components:** The components in the previous implementation were tightly coupled, which made it difficult to test and reuse them.
- **Inconsistent state management:** State management was inconsistent in the previous implementation, which led to bugs and unpredictable behavior.
- **No offline support:** The previous implementation did not have offline support, which made it unusable in areas with poor or no internet connectivity.
- **Inadequate testing:** The previous implementation was not well-tested, which resulted in a high number of bugs and regressions.

### Recommendations for the Reconstructed Project

- **Adopt a Clean Architecture:** A Clean Architecture will be adopted to ensure a clear separation of concerns and a more maintainable codebase.
- **Use a feature-based project structure:** A feature-based project structure will be used to promote modularity and make it easier to work on different features in parallel.
- **Use Riverpod for state management:** Riverpod will be used for state management to ensure a predictable and maintainable application state.
- **Implement offline-first:** The application will be designed to be fully functional offline to provide a better user experience.
- **Write comprehensive tests:** A comprehensive testing strategy will be implemented to ensure the quality and reliability of the application.
