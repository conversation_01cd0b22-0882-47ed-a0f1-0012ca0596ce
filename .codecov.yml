# Codecov Configuration for BudApp
# https://docs.codecov.com/docs/codecov-yaml

# Repository configuration
codecov:
  require_ci_to_pass: yes
  notify:
    wait_for_ci: yes

coverage:
  # Set precision for coverage percentages
  precision: 2
  # Round to nearest integer
  round: nearest
  # Acceptable range for coverage to be considered stable
  range: "95...100"

  # Status checks configuration
  status:
    project:
      default:
        # Target coverage percentage
        target: 100%
        # Allow 5% drop in coverage
        threshold: 1%
        # Only report when patch changes coverage
        if_not_found: success
    patch:
      default:
        target: 100%
        threshold: 1%
        if_not_found: success

  # Files to ignore in coverage reporting
  ignore:
    # Generated files
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "lib/firebase_options*.dart"
    - "lib/l10n/app_localizations*.dart"
    # Test files
    - "test/**/*"
    - "integration_test/**/*"
    # Platform-specific code
    - "ios/**/*"
    - "android/**/*"
    - "linux/**/*"
    - "macos/**/*"
    - "windows/**/*"
    - "web/**/*"

# Comment configuration for pull requests
comment:
  layout: "reach,diff,flags,tree,reach"
  behavior: default
  require_changes: true
  require_base: false
  require_head: false

# GitHub integration
github_checks:
  annotations: true

# Flags for different parts of the codebase
flags:
  core:
    paths:
      - lib/data/
      - lib/services/
      - lib/providers/
  features:
    paths:
      - lib/features/
  widgets:
    paths:
      - lib/widgets/