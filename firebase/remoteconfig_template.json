{"conditions": [{"name": "premium_users", "expression": "user.properties.subscription_status == 'premium'"}, {"name": "beta_testers", "expression": "user.properties.beta_tester == 'true'"}, {"name": "app_version_1_1", "expression": "app.version.gte('1.1.0')"}], "parameters": {"predefined_income_categories": {"defaultValue": {"value": "[\"Salary\", \"Freelance\", \"Investment\", \"Business\", \"Other Income\"]"}, "description": "Predefined income categories shown to users", "valueType": "JSON"}, "predefined_expense_categories": {"defaultValue": {"value": "[\"Food & Dining\", \"Transportation\", \"Shopping\", \"Entertainment\", \"Bills & Utilities\", \"Healthcare\", \"Education\", \"Travel\", \"Other Expenses\"]"}, "description": "Predefined expense categories shown to users", "valueType": "JSON"}, "max_accounts_free": {"defaultValue": {"value": "3"}, "description": "Maximum number of accounts for free tier users", "valueType": "NUMBER"}, "max_accounts_premium": {"defaultValue": {"value": "50"}, "conditionalValues": {"premium_users": {"value": "100"}}, "description": "Maximum number of accounts for premium tier users", "valueType": "NUMBER"}, "max_custom_categories_free": {"defaultValue": {"value": "5"}, "description": "Maximum number of custom categories for free tier users", "valueType": "NUMBER"}, "max_custom_categories_premium": {"defaultValue": {"value": "100"}, "description": "Maximum number of custom categories for premium tier users", "valueType": "NUMBER"}, "max_budgets_free": {"defaultValue": {"value": "10"}, "description": "Maximum number of budgets for free tier users", "valueType": "NUMBER"}, "max_budgets_premium": {"defaultValue": {"value": "100"}, "description": "Maximum number of budgets for premium tier users", "valueType": "NUMBER"}, "max_goals_free": {"defaultValue": {"value": "5"}, "description": "Maximum number of goals for free tier users", "valueType": "NUMBER"}, "max_goals_premium": {"defaultValue": {"value": "50"}, "description": "Maximum number of goals for premium tier users", "valueType": "NUMBER"}, "enable_feature_x": {"defaultValue": {"value": "false"}, "conditionalValues": {"beta_testers": {"value": "true"}, "app_version_1_1": {"value": "true"}}, "description": "Enable experimental feature X for beta testing", "valueType": "BOOLEAN"}, "enable_beta_features": {"defaultValue": {"value": "false"}, "conditionalValues": {"beta_testers": {"value": "true"}}, "description": "Enable all beta features for beta testers", "valueType": "BOOLEAN"}, "minimum_app_version": {"defaultValue": {"value": "1.0.0"}, "description": "Minimum required app version for compatibility", "valueType": "STRING"}, "force_update_required": {"defaultValue": {"value": "false"}, "description": "Force users to update to the latest app version", "valueType": "BOOLEAN"}, "maintenance_mode": {"defaultValue": {"value": "false"}, "description": "Enable maintenance mode to show maintenance screen", "valueType": "BOOLEAN"}, "maintenance_message": {"defaultValue": {"value": "BudApp is currently undergoing maintenance. Please try again later."}, "description": "Message to display during maintenance mode", "valueType": "STRING"}}, "version": {"versionNumber": "1", "description": "Initial Remote Config template for BudApp with predefined categories, premium limits, and feature flags"}}