# Firebase Security Rules: Referential Integrity Limitations

## Issue Overview

**Issue ID**: #4 - Weak Referential Integrity Enforcement in Firestore Rules
**Priority**: High
**Risk Level**: High Impact, Low Likelihood

## Problem Description

The current Firestore Security Rules implementation has a significant limitation in enforcing referential integrity. The `canDeleteAccount()`, `canDeleteCategory()`, and `canDeleteTag()` functions only check if the `isActive` flag is `false`, but they do **NOT** verify that no dependent records exist.

### Current Implementation (Weak)
```javascript
function canDeleteAccount(accountData) {
  return accountData.isActive == false; // Only checks flag!
}

function canDeleteCategory(categoryData) {
  return categoryData.isActive == false; // Only checks flag!
}

function canDeleteTag(tagData) {
  return tagData.isActive == false; // Only checks flag!
}
```

### Security Risk
- **Direct API calls** could bypass app logic and delete accounts/categories that have dependent transactions
- This would create **orphaned transaction records** pointing to non-existent accounts/categories
- **Data corruption** could occur without proper referential integrity enforcement

## Root Cause: Firestore Security Rules Limitations

Firestore Security Rules have inherent limitations that prevent robust referential integrity checks:

1. **Query Restrictions**: Rules cannot perform complex queries to check for dependent records
2. **Document Access Limits**: Maximum 10 `get()`/`exists()` calls per request
3. **Performance Constraints**: Checking all transactions for account references would be prohibitively expensive
4. **Scalability Issues**: As transaction count grows, dependency checks become impractical

### Why We Can't Fix This in Rules Alone

```javascript
// This would be ideal but is NOT possible in Firestore Rules:
function canDeleteAccount(accountData) {
  // ❌ Cannot query for transactions that reference this account
  // ❌ Would hit the 10 get() call limit quickly
  // ❌ Would be extremely slow with many transactions
  return accountData.isActive == false && 
         !hasTransactionsForAccount(accountData.id); // NOT POSSIBLE
}
```

## MVP Approach (Phase 1): Accept and Mitigate

Since we're in MVP phase with no Cloud Functions, we must accept this limitation but implement strong mitigation strategies.

### 1. App-Level Defensive Programming

The Flutter app **MUST** perform comprehensive dependency checks before allowing any deletion:

```dart
// Example: Account deletion in Flutter app
Future<bool> canDeleteAccount(String accountId) async {
  // Check for transactions referencing this account
  final transactionsQuery = await _firestore
      .collection('users/${userId}/transactions')
      .where('fromAccountId', isEqualTo: accountId)
      .limit(1)
      .get();
      
  if (transactionsQuery.docs.isNotEmpty) {
    return false; // Has dependent transactions
  }
  
  final toTransactionsQuery = await _firestore
      .collection('users/${userId}/transactions')
      .where('toAccountId', isEqualTo: accountId)
      .limit(1)
      .get();
      
  return toTransactionsQuery.docs.isEmpty; // Only allow if no dependencies
}

Future<void> deleteAccount(String accountId) async {
  // 1. Perform dependency check
  if (!await canDeleteAccount(accountId)) {
    throw Exception('Cannot delete account with existing transactions');
  }
  
  // 2. Soft delete first (set isActive = false)
  await _firestore
      .collection('users/${userId}/accounts')
      .doc(accountId)
      .update({'isActive': false, 'deletedAt': FieldValue.serverTimestamp()});
  
  // 3. Only hard delete after confirmation period (optional)
  // Consider keeping soft-deleted records for recovery
}
```

### 2. Soft Deletion Strategy

Instead of hard deletion, implement soft deletion with recovery capability:

```dart
// Soft deletion approach
class Account {
  final String id;
  final bool isActive;
  final DateTime? deletedAt;
  final String? deletedReason;
  
  // Soft delete method
  Account markAsDeleted(String reason) {
    return copyWith(
      isActive: false,
      deletedAt: DateTime.now(),
      deletedReason: reason,
    );
  }
}
```

### 3. UI Safeguards

Implement robust UI safeguards to prevent accidental deletions:

```dart
// Multi-step deletion confirmation
Future<void> requestAccountDeletion(Account account) async {
  // Step 1: Show dependency warning
  final dependencies = await checkAccountDependencies(account.id);
  if (dependencies.isNotEmpty) {
    showDependencyWarning(dependencies);
    return;
  }
  
  // Step 2: Confirmation dialog
  final confirmed = await showDeleteConfirmation(account);
  if (!confirmed) return;
  
  // Step 3: Final dependency check (race condition protection)
  if (!await canDeleteAccount(account.id)) {
    showError('Account cannot be deleted - new transactions found');
    return;
  }
  
  // Step 4: Perform soft deletion
  await accountRepository.softDeleteAccount(account.id);
}
```

### 4. Data Integrity Monitoring

Implement monitoring to detect orphaned records:

```dart
// Periodic data integrity check
Future<List<String>> findOrphanedTransactions() async {
  // This would be run periodically to detect data corruption
  final transactions = await getAllTransactions();
  final accounts = await getAllAccounts();
  final accountIds = accounts.map((a) => a.id).toSet();
  
  return transactions
      .where((t) => t.fromAccountId != null && !accountIds.contains(t.fromAccountId))
      .map((t) => t.id)
      .toList();
}
```

## Production Solution (Phase 2): Cloud Functions

For production deployment, implement Cloud Functions to enforce true referential integrity:

### 1. Secure Deletion Cloud Function

```javascript
// Cloud Function: deleteAccount
const functions = require('firebase-functions');
const admin = require('firebase-admin');

exports.deleteAccount = functions.https.onCall(async (data, context) => {
  // Verify authentication
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  const userId = context.auth.uid;
  const accountId = data.accountId;
  
  // Check for dependent transactions
  const transactionsSnapshot = await admin.firestore()
    .collection(`users/${userId}/transactions`)
    .where('fromAccountId', '==', accountId)
    .limit(1)
    .get();
    
  if (!transactionsSnapshot.empty) {
    throw new functions.https.HttpsError('failed-precondition', 
      'Cannot delete account with existing transactions');
  }
  
  // Check for toAccount references
  const toTransactionsSnapshot = await admin.firestore()
    .collection(`users/${userId}/transactions`)
    .where('toAccountId', '==', accountId)
    .limit(1)
    .get();
    
  if (!toTransactionsSnapshot.empty) {
    throw new functions.https.HttpsError('failed-precondition', 
      'Cannot delete account with existing transactions');
  }
  
  // Safe to delete
  await admin.firestore()
    .collection(`users/${userId}/accounts`)
    .doc(accountId)
    .delete();
    
  return { success: true };
});
```

### 2. Updated Security Rules (Phase 2)

```javascript
// Force all deletions through Cloud Functions
match /users/{userId}/accounts/{accountId} {
  allow delete: if false; // Block direct deletions
  // All other operations remain the same
}

match /users/{userId}/categories/{categoryId} {
  allow delete: if false; // Block direct deletions
}

match /users/{userId}/tags/{tagId} {
  allow delete: if false; // Block direct deletions
}
```

## Implementation Checklist

### ✅ Immediate Actions (MVP)
- [x] Document the limitation in security rules comments
- [x] Document the limitation in security requirements
- [x] Create this comprehensive documentation
- [ ] Implement robust app-level dependency checks
- [ ] Add soft deletion capability to account/category/tag models
- [ ] Implement multi-step deletion confirmation UI
- [ ] Add data integrity monitoring functions
- [ ] Update repository classes with dependency validation
- [ ] Write comprehensive tests for deletion edge cases

### 🔄 Future Actions (Production)
- [ ] Implement Cloud Functions for secure deletion
- [ ] Update security rules to block direct deletions
- [ ] Implement cascade deletion logic in Cloud Functions
- [ ] Add audit logging for all deletion operations
- [ ] Create data recovery utilities for soft-deleted records
- [ ] Implement automated data integrity monitoring

## Risk Assessment

| Factor | Assessment | Notes |
|--------|------------|-------- |
| **Impact** | High | Data corruption, orphaned records |
| **Likelihood** | Low | Requires direct API access outside app |
| **Detection** | Medium | Can be monitored through data integrity checks |
| **Recovery** | Medium | Soft deletion allows recovery, hard deletion does not |
| **Mitigation** | High | Strong app-level validation + planned Cloud Functions |

## Conclusion

This limitation is a known constraint of the MVP approach. While not ideal, it can be safely managed through:

1. **Robust app-level validation** - Never rely solely on security rules
2. **Soft deletion strategy** - Allow recovery from mistakes
3. **Clear documentation** - Ensure all developers understand the limitation
4. **Planned migration** - Cloud Functions in Phase 2 will provide true referential integrity

The risk is manageable for MVP deployment as long as:
- All deletions go through the app (not direct API calls)
- App-level dependency checking is bulletproof
- Soft deletion is implemented for recovery capability
- Data integrity monitoring is in place

**Status**: Documented and mitigated for MVP. Full resolution planned for Phase 2 with Cloud Functions. 