# BudApp Firestore Security Rules Requirements

## Enhanced Security Implementation (2024 Best Practices)

**Status**: ✅ **IMPLEMENTED** - Enhanced Firestore Security Rules with 2024 best practices

This document outlines the comprehensive security requirements and implementation for BudApp's Firestore Security Rules, including the enhanced security features implemented in Task 31.5.

### Security Enhancement Summary
- **621-line comprehensive security rules** with modern validation patterns
- **Granular operation controls** (get/list vs create/update/delete permissions)
- **Enhanced input sanitization** and injection prevention
- **Financial security validation** with amount limits and business logic
- **Comprehensive test coverage** with 66 security test cases
- **Backward compatibility** maintained with existing functionality

## Data Model Overview

### Collection Structure
```
users/{userId}                          # User profiles (top-level)
├── accounts/{accountId}                # Financial accounts (subcollection)
├── transactions/{transactionId}        # All transactions (subcollection)
├── categories/{categoryId}             # User custom categories (subcollection)
├── budgets/{budgetId}                  # User budgets (subcollection)
├── goals/{goalId}                      # Financial goals (subcollection)
└── tags/{tagId}                        # User-defined tags (subcollection)
```

### Key Design Principles
1. **User Data Isolation**: All user-specific data in subcollections under `/users/{userId}/`
2. **Authentication Required**: All operations require authenticated users
3. **Owner-Only Access**: Users can only access their own data
4. **Enhanced Data Validation**: Comprehensive field validation, input sanitization, and business rules
5. **Granular Operation Controls**: Separate permissions for read vs write operations
6. **Security-First Architecture**: XSS prevention, injection protection, and financial security
7. **Referential Integrity**: Prevent deletion of entities with dependent data
8. **Premium Feature Limits**: Enforce account limits based on subscription tier

### Enhanced Security Features (Task 31.5)
- **Input Sanitization**: XSS and injection attack prevention
- **Financial Validation**: Amount limits and business logic enforcement
- **Granular Permissions**: Operation-specific access controls
- **Field-Level Security**: Enhanced validation for sensitive data
- **Attack Vector Protection**: Comprehensive defense against common threats

## Security Requirements

### 1. Authentication & Authorization

#### Basic Authentication
- All operations require `request.auth != null`
- User ID must match document path: `request.auth.uid == userId`
- No anonymous access to financial data

#### User Data Isolation
```javascript
// Pattern for all user subcollections
match /users/{userId}/collection/{docId} {
  allow read, write: if request.auth != null && request.auth.uid == userId;
}
```

### 2. Data Validation Requirements

#### User Profile (`/users/{userId}`)
- **Required Fields**: `uid`, `email`, `schemaVersion`, `createdAt`, `lastLoginAt`
- **Field Types**:
  - `uid`: string (must match auth.uid)
  - `email`: string (valid email format)
  - `schemaVersion`: integer (current: 1)
  - `displayName`: string (optional)
  - `createdAt`, `lastLoginAt`: timestamp
  - `preferences`: map
  - `isEmailVerified`: boolean
  - `authProviders`: array of strings

#### Account (`/users/{userId}/accounts/{accountId}`)
- **Required Fields**: `id`, `userId`, `name`, `type`, `classification`, `initialBalanceCents`, `schemaVersion`, `createdAt`, `updatedAt`
- **Enhanced Security**: Input sanitization, field validation, type/classification consistency checks
- **Field Types**:
  - `id`: string
  - `userId`: string (must match path userId)
  - `name`: string (1-100 characters, XSS protected)
  - `type`: enum ['checking', 'savings', 'creditCard', 'cash', 'investment', 'loan']
  - `classification`: enum ['asset', 'liability']
  - `initialBalanceCents`: number (integer, validated)
  - `schemaVersion`: integer (current: 1)
  - `isPrimary`: boolean
  - `isActive`: boolean
  - `createdAt`, `updatedAt`: timestamp
  - `description`: string (optional, 0-500 chars, XSS protected)
  - `iconName`: string (optional, 1-50 chars)
  - `colorHex`: string (optional, hex color format)
  - `metadata`: map (optional)

#### Transaction (`/users/{userId}/transactions/{transactionId}`)
- **Required Fields**: `id`, `userId`, `type`, `status`, `amountCents`, `schemaVersion`, `transactionDate`, `createdAt`, `updatedAt`
- **Enhanced Security**: Financial validation, timestamp validation, input sanitization
- **Field Types**:
  - `id`: string
  - `userId`: string (must match path userId)
  - `type`: enum ['income', 'expense', 'transfer']
  - `status`: enum ['pending', 'completed', 'cancelled']
  - `amountCents`: number (integer, > 0, limit: $9.9M)
  - `schemaVersion`: integer (current: 1)
  - `fromAccountId`, `toAccountId`: string (optional, must reference existing accounts)
  - `categoryId`: string (optional, must reference existing category)
  - `description`: string (optional, max 500 chars, XSS protected)
  - `transactionDate`: timestamp (future limit: 7 days)
  - `tags`: array of strings
  - `createdAt`, `updatedAt`: timestamp

#### Category (`/users/{userId}/categories/{categoryId}`)
- **Required Fields**: `id`, `userId`, `name`, `type`, `createdAt`, `updatedAt`
- **Field Types**:
  - `id`: string
  - `userId`: string (must match path userId, null for system categories)
  - `name`: string (1-50 characters)
  - `type`: enum ['income', 'expense', 'both']
  - `parentCategoryId`: string (optional, must reference existing category)
  - `isActive`: boolean
  - `isSystem`: boolean (read-only for users)
  - `sortOrder`: number (integer)

#### Budget (`/users/{userId}/budgets/{budgetId}`)
- **Required Fields**: `id`, `userId`, `name`, `period`, `status`, `targetAmountCents`, `currencyCode`, `startDate`, `endDate`, `createdAt`, `updatedAt`
- **Field Types**:
  - `id`: string
  - `userId`: string (must match path userId)
  - `name`: string (1-100 characters)
  - `period`: enum ['weekly', 'monthly', 'quarterly', 'yearly']
  - `status`: enum ['active', 'paused', 'completed', 'cancelled']
  - `targetAmountCents`: number (integer, > 0)
  - `currencyCode`: string (ISO 4217)
  - `categoryId`: string (optional, must reference existing category)
  - `startDate`, `endDate`: timestamp
  - `isRecurring`: boolean
  - `alertsEnabled`: boolean
  - `alertThresholdPercent`: number (integer, 1-100)

### 3. Business Logic Validation

#### Account Limits (Premium Features)
- Free tier: Maximum 5 accounts per user
- Premium tier: Unlimited accounts
- Validation using Remote Config values

#### Transaction Validation
- `amountCents` must be positive integer
- For transfers: both `fromAccountId` and `toAccountId` required
- For income: `toAccountId` required, `fromAccountId` optional
- For expense: `fromAccountId` required, `toAccountId` optional
- Account references must exist and belong to same user

#### Category Validation
- Category name must be unique per user
- `parentCategoryId` must reference existing category
- Cannot create circular references in category hierarchy
- System categories (`isSystem: true`) cannot be modified by users

#### Budget Validation
- `endDate` must be after `startDate`
- Only one active budget per category per period
- `alertThresholdPercent` must be between 1-100

### 4. Referential Integrity

#### Account Deletion
- Prevent deletion if account has associated transactions
- Prevent deletion if account is referenced in budgets

#### Category Deletion
- Prevent deletion if category has associated transactions
- Prevent deletion if category has associated budgets
- Prevent deletion if category has subcategories

#### Transaction Dependencies
- Validate account references exist before creation
- Validate category references exist before creation

#### **KNOWN LIMITATION: Weak Referential Integrity in MVP**

**⚠️ SECURITY RISK**: The current MVP implementation has a known limitation regarding referential integrity enforcement in Firestore Security Rules.

**Problem**: 
- The `canDeleteAccount()`, `canDeleteCategory()`, and `canDeleteTag()` functions only check if `isActive == false`
- They do NOT verify that no dependent transactions exist
- This relies entirely on app logic to handle dependency checks before deletion
- Direct API calls could bypass app logic and delete accounts/categories with transactions, creating orphaned data

**Current Implementation (Weak)**:
```javascript
function canDeleteAccount(accountData) {
  return accountData.isActive == false; // Only checks flag, not dependencies
}
```

**Root Cause**: 
Firestore Security Rules have limited query capabilities - they cannot efficiently query for the existence of related documents without hitting the 10 `get()/exists()` call limit per request.

**MVP Approach (Phase 1)**:
1. **Accept the limitation** - Document this as a known technical debt
2. **Strengthen app-level logic** - Ensure the Flutter app performs thorough dependency checks before setting `isActive = false`
3. **Add defensive documentation** - Clearly document that deletion should only happen through the app, never via direct API calls
4. **Consider additional safeguards** - Add timestamp-based soft deletion to make recovery possible

**Production Solution (Phase 2 - Cloud Functions)**:
```javascript
// Future implementation with Cloud Functions
allow delete: if false; // Force all deletions through Cloud Functions

// Cloud Function: deleteAccount
// - Verify user authentication
// - Check for dependent transactions
// - Perform cascade deletion or prevent deletion
// - Maintain audit trail
```

**Immediate Actions Required**:
1. Document this limitation in all relevant places
2. Ensure app logic is bulletproof for dependency checking
3. Consider implementing soft deletion with recovery capability
4. Plan Cloud Function implementation for production

**Risk Assessment**:
- **Impact**: High (data corruption, orphaned records)
- **Likelihood**: Low (requires direct API access outside app)
- **Mitigation**: Strong app-level validation + documentation + planned Cloud Functions

### 5. Security Functions

#### Helper Functions Needed
```javascript
// Check if user has premium subscription
function isPremiumUser() {
  return request.auth.token.premium == true ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.subscriptionTier == 'premium';
}

// Count user's accounts
function getUserAccountCount() {
  // Implementation using aggregation or counter document
}

// Check if account exists and belongs to user
function accountBelongsToUser(accountId) {
  return exists(/databases/$(database)/documents/users/$(request.auth.uid)/accounts/$(accountId));
}

// Check if category exists and belongs to user
function categoryBelongsToUser(categoryId) {
  return exists(/databases/$(database)/documents/users/$(request.auth.uid)/categories/$(categoryId));
}

// Validate required fields
function hasRequiredFields(data, fields) {
  return data.keys().hasAll(fields);
}

// Validate field types
function isValidTimestamp(value) {
  return value is timestamp;
}

function isValidEnum(value, allowedValues) {
  return value in allowedValues;
}
```

### 6. Performance Considerations

#### Document Access Limits
- Maximum 10 `get()`, `exists()`, `getAfter()` calls per request
- Cache frequently accessed validation data
- Use batch operations for multi-document validation

#### Index Requirements
- Composite indexes for user-scoped queries
- Single-field indexes for common filters

### 7. Enhanced Security Implementation (Task 31.5)

#### Security Enhancements Implemented
1. **Granular Operation Controls**
   - Separate `get`/`list` permissions for read operations
   - Enhanced `create`/`update`/`delete` controls with validation
   - Operation-specific security requirements

2. **Input Sanitization & Injection Prevention**
   ```javascript
   function isValidSanitizedString(value, minLength, maxLength) {
     return value is string && 
            value.size() >= minLength && 
            value.size() <= maxLength &&
            !value.matches('.*[<>"\'].*') && // Prevent injection attacks
            !value.matches('.*script.*') && // Prevent XSS attempts
            !value.matches('.*eval\\(.*') && // Prevent code execution
            !value.matches('.*javascript:.*') && // Prevent JavaScript URLs
            value.matches('^[\\w\\s\\-\\.,$@#()&]+$'); // Allow only safe financial chars
   }
   ```

3. **Financial Security Validation**
   - Amount limits: $9.9M maximum transaction amount
   - Negative amount prevention
   - Financial precision validation
   - Business logic enforcement

4. **Enhanced Timestamp Security**
   - Future transaction date limits (7 days)
   - Timestamp validation and consistency checks
   - Creation/update timestamp enforcement

5. **Field-Level Access Controls**
   - Sensitive field protection
   - Unauthorized field addition prevention
   - Field modification restrictions

6. **Enhanced Validation Functions**
   - `isValidFinancialAmount()` - Financial amount validation
   - `isValidTransactionTiming()` - Timestamp security
   - `isValidStringLength()` - Safe string validation
   - `isValidEnum()` - Enum value validation

#### Security Test Coverage (66 Tests)
- **Basic Security Tests**: 11 tests (authentication, isolation, CRUD)
- **Enhanced Security Tests**: 19 tests (XSS, injection, financial validation)
- **Account Security Tests**: 36 tests (comprehensive account validation)

### 8. Testing Requirements

#### Test Scenarios
1. **Authentication Tests**
   - Unauthenticated access denied
   - Cross-user access denied
   - Valid user access allowed

2. **Enhanced Security Tests**
   - XSS injection prevention
   - HTML injection prevention
   - JavaScript injection prevention
   - Financial validation (negative amounts, large amounts)
   - Timestamp validation (future dates)
   - Field protection (unauthorized additions)

3. **Data Validation Tests**
   - Required fields validation
   - Field type validation
   - Enum value validation
   - Business rule validation
   - Schema version validation

4. **Referential Integrity Tests**
   - Account reference validation
   - Category reference validation
   - Deletion prevention with dependencies

5. **Premium Feature Tests**
   - Account limit enforcement
   - Premium feature access control

6. **Edge Cases**
   - Invalid data types
   - Missing required fields
   - Circular references
   - Boundary value testing
   - Attack vector simulations

## Implementation Priority

1. **Phase 1**: Basic authentication and user data isolation
2. **Phase 2**: Core data validation (required fields, types)
3. **Phase 3**: Business logic validation and referential integrity
4. **Phase 4**: Premium feature limits and advanced validation
5. **Phase 5**: Performance optimization and comprehensive testing
