/**
 * Security Tests for Malicious Input Scenarios - BudApp
 * 
 * Tests Firebase Security Rules against security attack vectors that our
 * rules are designed to protect against: data structure attacks, type
 * manipulation, authorization bypass, and resource exhaustion.
 * 
 * Run with: npm test security-malicious-input.test.js
 */

const { initializeTestEnvironment, assertFails, assertSucceeds } = require('@firebase/rules-unit-testing');
const { doc, setDoc, updateDoc, deleteDoc } = require('firebase/firestore');

let testEnv;

// Test user IDs
const USER_1_ID = 'user1';
const USER_2_ID = 'user2';

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId: 'budapp-test',
    firestore: {
      rules: require('fs').readFileSync('../../firestore.rules', 'utf8'),
      host: 'localhost',
      port: 8080,
    },
  });
});

afterAll(async () => {
  await testEnv.cleanup();
});

beforeEach(async () => {
  await testEnv.clearFirestore();
});

describe('Malicious Input Security Tests', () => {
  
  describe('Data Type Manipulation Attacks', () => {
    test('rejects type confusion attacks in user profiles', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const userRef = doc(user1Db, `users/${USER_1_ID}`);
      
      // Try to inject different data types where strings are expected
      const maliciousProfiles = [
        {
          id: USER_1_ID,
          email: 123, // Should be string
          displayName: 'Test User',
          isEmailVerified: true,
          schemaVersion: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {}
        },
        {
          id: USER_1_ID,
          email: '<EMAIL>',
          displayName: ['array', 'instead', 'of', 'string'], // Should be string
          isEmailVerified: true,
          schemaVersion: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {}
        },
        {
          id: USER_1_ID,
          email: '<EMAIL>',
          displayName: 'Test User',
          isEmailVerified: 'true', // Should be boolean
          schemaVersion: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {}
        },
        {
          id: USER_1_ID,
          email: '<EMAIL>',
          displayName: 'Test User',
          isEmailVerified: true,
          schemaVersion: '1', // Should be number
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {}
        },
        {
          id: USER_1_ID,
          email: '<EMAIL>',
          displayName: 'Test User',
          isEmailVerified: true,
          schemaVersion: 1,
          createdAt: 'not_a_date', // Should be timestamp
          updatedAt: new Date(),
          metadata: {}
        }
      ];
      
      for (const maliciousProfile of maliciousProfiles) {
        await assertFails(setDoc(userRef, maliciousProfile));
      }
    });
    
    test('rejects type manipulation in transaction amounts', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/test_transaction`);
      
      const maliciousTransactions = [
        {
          id: 'test_transaction',
          userId: USER_1_ID,
          type: 'expense',
          status: 'pending',
          amountCents: '1000', // Should be number
          fromAccountId: 'account1',
          transactionDate: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'test_transaction',
          userId: USER_1_ID,
          type: 'expense',
          status: 'pending',
          amountCents: -1000, // Should be positive
          fromAccountId: 'account1',
          transactionDate: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'test_transaction',
          userId: USER_1_ID,
          type: 'expense',
          status: 'pending',
          amountCents: 0, // Should be greater than 0
          fromAccountId: 'account1',
          transactionDate: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
      
      for (const maliciousTransaction of maliciousTransactions) {
        await assertFails(setDoc(transactionRef, maliciousTransaction));
      }
    });
  });
  
  describe('Authorization Bypass Attempts', () => {
    test('rejects attempts to bypass user isolation', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const userRef = doc(user1Db, `users/${USER_1_ID}`);
      
      // Try to create profile with different userId
      const maliciousProfile = {
        id: USER_2_ID, // Different from authenticated user
        email: '<EMAIL>',
        displayName: 'Malicious User',
        isEmailVerified: true,
        schemaVersion: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
        metadata: {}
      };
      
      await assertFails(setDoc(userRef, maliciousProfile));
    });
    
    test('rejects cross-user data access attempts', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const user2Db = testEnv.authenticatedContext(USER_2_ID).firestore();
      
      // User 1 creates a transaction
      const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/test_transaction`);
      const validTransaction = {
        id: 'test_transaction',
        userId: USER_1_ID,
        type: 'expense',
        status: 'pending',
        amountCents: 1000,
        fromAccountId: 'account1',
        transactionDate: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await assertSucceeds(setDoc(transactionRef, validTransaction));
      
      // User 2 tries to access User 1's transaction
      const user2TransactionRef = doc(user2Db, `users/${USER_1_ID}/transactions/test_transaction`);
      await assertFails(updateDoc(user2TransactionRef, { notes: 'Hacked!' }));
    });
    
    test('rejects attempts to manipulate userId in nested collections', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const accountRef = doc(user1Db, `users/${USER_1_ID}/accounts/test_account`);
      
      // Try to create account with different userId
      const maliciousAccount = {
        id: 'test_account',
        userId: USER_2_ID, // Different from path
        name: 'Malicious Account',
        type: 'checking',
        currentBalanceCents: 0,
        schemaVersion: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await assertFails(setDoc(accountRef, maliciousAccount));
    });
  });
  
  describe('Field Length and Size Attacks', () => {
    test('rejects oversized strings in category names', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/test_category`);
      
      // Test name length validation (should be 2-50 chars)
      const oversizedCategory = {
        id: 'test_category',
        userId: USER_1_ID,
        name: 'A'.repeat(51), // Too long
        type: 'expense',
        schemaVersion: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await assertFails(setDoc(categoryRef, oversizedCategory));
    });
    
    test('rejects oversized strings in transaction notes', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/test_transaction`);
      
      // Test notes length validation (should be max 500 chars)
      const oversizedTransaction = {
        id: 'test_transaction',
        userId: USER_1_ID,
        type: 'expense',
        status: 'pending',
        amountCents: 1000,
        fromAccountId: 'account1',
        notes: 'A'.repeat(501), // Too long
        transactionDate: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await assertFails(setDoc(transactionRef, oversizedTransaction));
    });
    
    test('rejects oversized tag names', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/test_tag`);
      
      // Test tag name length validation (should be 2-50 chars)
      const oversizedTag = {
        id: 'test_tag',
        userId: USER_1_ID,
        name: 'A'.repeat(51), // Too long
        schemaVersion: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await assertFails(setDoc(tagRef, oversizedTag));
    });
  });
  
  describe('Enum Manipulation Attacks', () => {
    test('rejects invalid transaction types', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/test_transaction`);
      
      const invalidTypes = ['invalid', 'admin', 'system', 'hack', ''];
      
      for (const invalidType of invalidTypes) {
        const maliciousTransaction = {
          id: 'test_transaction',
          userId: USER_1_ID,
          type: invalidType,
          status: 'pending',
          amountCents: 1000,
          fromAccountId: 'account1',
          transactionDate: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        await assertFails(setDoc(transactionRef, maliciousTransaction));
      }
    });
    
    test('rejects invalid account types', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const accountRef = doc(user1Db, `users/${USER_1_ID}/accounts/test_account`);
      
      const invalidTypes = ['invalid', 'admin', 'system', 'hack', ''];
      
      for (const invalidType of invalidTypes) {
        const maliciousAccount = {
          id: 'test_account',
          userId: USER_1_ID,
          name: 'Test Account',
          type: invalidType,
          currentBalanceCents: 0,
          schemaVersion: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        await assertFails(setDoc(accountRef, maliciousAccount));
      }
    });
  });
  
  describe('Immutable Field Manipulation', () => {
    test('rejects attempts to modify immutable fields in updates', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const accountRef = doc(user1Db, `users/${USER_1_ID}/accounts/test_account`);
      
      // Create initial account
      const initialAccount = {
        id: 'test_account',
        userId: USER_1_ID,
        name: 'Test Account',
        type: 'checking',
        currentBalanceCents: 0,
        schemaVersion: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await assertSucceeds(setDoc(accountRef, initialAccount));
      
      // Try to modify immutable fields that our rules actually protect
      const maliciousUpdates = [
        { id: 'different_id' },
        { userId: USER_2_ID },
        { createdAt: new Date() }
      ];
      
      for (const maliciousUpdate of maliciousUpdates) {
        await assertFails(updateDoc(accountRef, maliciousUpdate));
      }
      
      // Test that schemaVersion changes are allowed (not protected as immutable)
      await assertSucceeds(updateDoc(accountRef, { schemaVersion: 2 }));
    });
  });
  
  describe('Resource Exhaustion Attempts', () => {
    test('validates that large documents are handled correctly', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/test_transaction`);
      
      // Create reasonably sized tagIds array (our rules don't explicitly limit array size)
      const tagIds = [];
      for (let i = 0; i < 100; i++) {
        tagIds.push(`tag_${i}`);
      }
      
      const validTransaction = {
        id: 'test_transaction',
        userId: USER_1_ID,
        type: 'expense',
        status: 'pending',
        amountCents: 1000,
        fromAccountId: 'account1',
        tagIds: tagIds,
        transactionDate: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      // This should succeed as our rules don't explicitly limit array size
      // Firestore itself has document size limits that would eventually kick in
      await assertSucceeds(setDoc(transactionRef, validTransaction));
    });
  });
  
  describe('Business Logic Bypass Attempts', () => {
    test('rejects transactions with invalid account logic', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/test_transaction`);
      
      // Income transaction without required toAccountId
      const invalidIncomeTransaction = {
        id: 'test_transaction',
        userId: USER_1_ID,
        type: 'income',
        status: 'pending',
        amountCents: 1000,
        fromAccountId: 'account1', // Should not have fromAccountId for income
        transactionDate: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await assertFails(setDoc(transactionRef, invalidIncomeTransaction));
    });
    
    test('rejects goals with invalid target amounts', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/test_goal`);
      
      const invalidGoals = [
        {
          id: 'test_goal',
          userId: USER_1_ID,
          name: 'Test Goal',
          targetAmountCents: 0, // Should be positive
          schemaVersion: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'test_goal',
          userId: USER_1_ID,
          name: 'Test Goal',
          targetAmountCents: -1000, // Should be positive
          schemaVersion: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'test_goal',
          userId: USER_1_ID,
          name: 'Test Goal',
          targetAmountCents: 1000000000000, // Too large (over $10B)
          schemaVersion: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
      
      for (const invalidGoal of invalidGoals) {
        await assertFails(setDoc(goalRef, invalidGoal));
      }
    });
  });
  
  describe('Timestamp Manipulation Attacks', () => {
    test('validates timestamp fields are properly typed', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/test_transaction`);
      
      // Test valid timestamp
      const validTransaction = {
        id: 'test_transaction',
        userId: USER_1_ID,
        type: 'expense',
        status: 'pending',
        amountCents: 1000,
        fromAccountId: 'account1',
        transactionDate: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await assertSucceeds(setDoc(transactionRef, validTransaction));
      
      // Test invalid timestamp types
      const invalidTransaction = {
        id: 'test_transaction',
        userId: USER_1_ID,
        type: 'expense',
        status: 'pending',
        amountCents: 1000,
        fromAccountId: 'account1',
        transactionDate: 'not_a_date', // Invalid type
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await assertFails(setDoc(transactionRef, invalidTransaction));
    });
  });
  
  describe('Color Format Attacks', () => {
    test('rejects invalid color formats in goals', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/test_goal`);
      
      const invalidColors = [
        'FF0000',     // Missing #
        '#GG0000',    // Invalid hex
        '#FF00',      // Too short
        '#FF000000',  // Too long
        'red',        // Named color
        '',           // Empty
        '#'           // Just #
      ];
      
      for (const invalidColor of invalidColors) {
        const invalidGoal = {
          id: 'test_goal',
          userId: USER_1_ID,
          name: 'Test Goal',
          targetAmountCents: 100000,
          colorHex: invalidColor,
          schemaVersion: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        await assertFails(setDoc(goalRef, invalidGoal));
      }
    });
  });
});