/**
 * Goal and Goal Contribution Firestore Security Rules Tests for BudApp
 * 
 * Tests the goal and contribution CRUD operations and validation rules.
 * Run with: npm test or firebase emulators:exec --only firestore "npm test"
 */

const { initializeTestEnvironment, assertFails, assertSucceeds } = require('@firebase/rules-unit-testing');
const { doc, getDoc, setDoc, collection, addDoc, deleteDoc, updateDoc, getDocs, Timestamp } = require('firebase/firestore');

let testEnv;

// Test user IDs
const USER_1_ID = 'user1';
const USER_2_ID = 'user2';

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId: 'budapp-test',
    firestore: {
      rules: require('fs').readFileSync('../../firestore.rules', 'utf8'),
      host: 'localhost',
      port: 8080,
    },
  });
});

afterAll(async () => {
  await testEnv.cleanup();
});

beforeEach(async () => {
  await testEnv.clearFirestore();
});

describe('Goal Security Rules', () => {
  const validGoal = {
    id: 'goal1',
    userId: USER_1_ID,
    name: 'Emergency Fund',
    description: 'Save for emergencies',
    targetAmountCents: 500000, // $5000.00
    currentAmountCents: 100000, // $1000.00
    targetDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    isCompleted: false,
    colorHex: '#FF0000',
    iconName: 'emergency_fund',
    status: 'active',
    isActive: true,
    schemaVersion: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    metadata: {}
  };

  describe('Authentication Requirements', () => {
    test('unauthenticated users cannot read goals', async () => {
      const unauthedDb = testEnv.unauthenticatedContext().firestore();
      const goalRef = doc(unauthedDb, `users/${USER_1_ID}/goals/goal1`);
      await assertFails(getDoc(goalRef));
    });

    test('unauthenticated users cannot create goals', async () => {
      const unauthedDb = testEnv.unauthenticatedContext().firestore();
      const goalRef = doc(unauthedDb, `users/${USER_1_ID}/goals/goal1`);
      await assertFails(setDoc(goalRef, validGoal));
    });

    test('authenticated users can read their own goals', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
      
      // First create the goal
      await setDoc(goalRef, validGoal);
      
      // Then read it
      await assertSucceeds(getDoc(goalRef));
    });

    test('authenticated users cannot read other users goals', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const user2Db = testEnv.authenticatedContext(USER_2_ID).firestore();
      
      // User 1 creates a goal
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
      await setDoc(goalRef, validGoal);
      
      // User 2 tries to read User 1's goal
      const user2GoalRef = doc(user2Db, `users/${USER_1_ID}/goals/goal1`);
      await assertFails(getDoc(user2GoalRef));
    });
  });

  describe('Goal Creation Validation', () => {
    test('valid goal creation succeeds', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
      await assertSucceeds(setDoc(goalRef, validGoal));
    });

    test('goal creation from Flutter app data structure succeeds', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/flutter_goal`);

      // This matches the exact data structure from Flutter app debug output
      const flutterGoal = {
        id: 'flutter_goal',
        userId: USER_1_ID,
        name: 'test',
        description: null,
        targetAmountCents: 12300, // 123 * 100
        currentAmountCents: 0,
        targetDate: null,
        isCompleted: false,
        colorHex: '#2196F3',
        iconName: 'account_balance',
        status: 'active',
        isActive: true,
        schemaVersion: 1,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        metadata: {}
      };

      console.log('Testing Flutter goal data structure:', JSON.stringify(flutterGoal, null, 2));
      await assertSucceeds(setDoc(goalRef, flutterGoal));
    });

    test('debug goal creation validation step by step', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/debug_goal`);

      // Test minimal valid goal first
      const minimalGoal = {
        id: 'debug_goal',
        userId: USER_1_ID,
        name: 'test',
        targetAmountCents: 12300,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      console.log('Testing minimal goal:', JSON.stringify(minimalGoal, null, 2));
      try {
        await assertSucceeds(setDoc(goalRef, minimalGoal));
        console.log('✅ Minimal goal creation succeeded');
      } catch (error) {
        console.log('❌ Minimal goal creation failed:', error.message);
      }

      // Clear and test with additional fields one by one
      await testEnv.clearFirestore();

      // Test each optional field individually
      const optionalFields = [
        { description: null },
        { currentAmountCents: 0 },
        { targetDate: null },
        { isCompleted: false },
        { colorHex: '#2196F3' },
        { iconName: 'account_balance' },
        { status: 'active' },
        { isActive: true },
        { schemaVersion: 1 },
        { metadata: {} }
      ];

      for (let i = 0; i < optionalFields.length; i++) {
        await testEnv.clearFirestore();
        const testGoal = { ...minimalGoal, ...optionalFields[i] };
        const fieldName = Object.keys(optionalFields[i])[0];

        console.log(`Testing field '${fieldName}':`, optionalFields[i]);
        try {
          await assertSucceeds(setDoc(goalRef, testGoal));
          console.log(`✅ Field '${fieldName}' validation passed`);
        } catch (error) {
          console.log(`❌ Field '${fieldName}' validation failed:`, error.message);
          break; // Stop at first failing field
        }
      }
    });

    test('goal creation fails with missing required fields', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
      
      const invalidGoal = { ...validGoal };
      delete invalidGoal.name;
      
      await assertFails(setDoc(goalRef, invalidGoal));
    });

    test('goal creation fails with invalid targetAmountCents', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
      
      // Negative amount
      const invalidGoal1 = { ...validGoal, targetAmountCents: -1000 };
      await assertFails(setDoc(goalRef, invalidGoal1));
      
      // Zero amount
      const invalidGoal2 = { ...validGoal, targetAmountCents: 0 };
      await assertFails(setDoc(goalRef, invalidGoal2));
      
      // Too large amount (over $10B)
      const invalidGoal3 = { ...validGoal, targetAmountCents: 1000000000000 };
      await assertFails(setDoc(goalRef, invalidGoal3));
    });

    test('goal creation fails with invalid colorHex format', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
      
      // Invalid hex color formats
      const invalidColors = ['FF0000', '#GG0000', '#FF00', '#FF00000', 'red'];
      
      for (const color of invalidColors) {
        const invalidGoal = { ...validGoal, colorHex: color };
        await assertFails(setDoc(goalRef, invalidGoal));
      }
    });

    test('goal creation fails with invalid iconName format', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
      
      // Invalid icon name formats
      const invalidIcons = ['', '123invalid', 'icon-with-dash', 'icon with space'];
      
      for (const icon of invalidIcons) {
        const invalidGoal = { ...validGoal, iconName: icon };
        await assertFails(setDoc(goalRef, invalidGoal));
      }
    });

    test('goal creation fails with invalid status', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
      
      const invalidGoal = { ...validGoal, status: 'invalid_status' };
      await assertFails(setDoc(goalRef, invalidGoal));
    });

    test('goal creation fails with past targetDate', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
      
      const invalidGoal = { ...validGoal, targetDate: new Date(Date.now() - 24 * 60 * 60 * 1000) }; // Yesterday
      await assertFails(setDoc(goalRef, invalidGoal));
    });

    test('goal creation fails with wrong userId', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
      
      const invalidGoal = { ...validGoal, userId: USER_2_ID };
      await assertFails(setDoc(goalRef, invalidGoal));
    });
  });

  describe('Goal Update Validation', () => {
    beforeEach(async () => {
      // Create a goal for update tests
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
      await setDoc(goalRef, validGoal);
    });

    test('valid goal update succeeds', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
      
      const updatedGoal = { 
        ...validGoal, 
        name: 'Updated Emergency Fund',
        updatedAt: new Date()
      };
      
      await assertSucceeds(updateDoc(goalRef, updatedGoal));
    });

    test('goal update fails when changing immutable fields', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
      
      // Try to change createdAt
      await assertFails(updateDoc(goalRef, { createdAt: new Date() }));
      
      // Try to change schemaVersion
      await assertFails(updateDoc(goalRef, { schemaVersion: 2 }));
    });

    test('goal deletion succeeds for owner', async () => {
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
      
      await assertSucceeds(deleteDoc(goalRef));
    });

    test('goal deletion fails for non-owner', async () => {
      const user2Db = testEnv.authenticatedContext(USER_2_ID).firestore();
      const goalRef = doc(user2Db, `users/${USER_1_ID}/goals/goal1`);

      await assertFails(deleteDoc(goalRef));
    });
  });

  describe('Goal Contribution Security Rules', () => {
    const validContribution = {
      id: 'contribution1',
      userId: USER_1_ID,
      goalId: 'goal1',
      amountCents: 50000, // $500.00
      contributionDate: new Date(),
      description: 'Monthly savings',
      isActive: true,
      schemaVersion: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {}
    };

    beforeEach(async () => {
      // Create a goal for contribution tests
      const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
      const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
      await setDoc(goalRef, validGoal);
    });

    describe('Contribution Authentication Requirements', () => {
      test('unauthenticated users cannot read contributions', async () => {
        const unauthedDb = testEnv.unauthenticatedContext().firestore();
        const contributionRef = doc(unauthedDb, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);
        await assertFails(getDoc(contributionRef));
      });

      test('unauthenticated users cannot create contributions', async () => {
        const unauthedDb = testEnv.unauthenticatedContext().firestore();
        const contributionRef = doc(unauthedDb, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);
        await assertFails(setDoc(contributionRef, validContribution));
      });

      test('authenticated users can read their own contributions', async () => {
        const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
        const contributionRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);

        // First create the contribution
        await setDoc(contributionRef, validContribution);

        // Then read it
        await assertSucceeds(getDoc(contributionRef));
      });

      test('authenticated users cannot read other users contributions', async () => {
        const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
        const user2Db = testEnv.authenticatedContext(USER_2_ID).firestore();

        // User 1 creates a contribution
        const contributionRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);
        await setDoc(contributionRef, validContribution);

        // User 2 tries to read User 1's contribution
        const user2ContributionRef = doc(user2Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);
        await assertFails(getDoc(user2ContributionRef));
      });
    });

    describe('Contribution Creation Validation', () => {
      test('valid contribution creation succeeds', async () => {
        const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
        const contributionRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);
        await assertSucceeds(setDoc(contributionRef, validContribution));
      });

      test('contribution creation fails with missing required fields', async () => {
        const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
        const contributionRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);

        const invalidContribution = { ...validContribution };
        delete invalidContribution.amountCents;

        await assertFails(setDoc(contributionRef, invalidContribution));
      });

      test('contribution creation fails with invalid amountCents', async () => {
        const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
        const contributionRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);

        // Negative amount
        const invalidContribution1 = { ...validContribution, amountCents: -1000 };
        await assertFails(setDoc(contributionRef, invalidContribution1));

        // Zero amount
        const invalidContribution2 = { ...validContribution, amountCents: 0 };
        await assertFails(setDoc(contributionRef, invalidContribution2));

        // Too large amount (over $10B)
        const invalidContribution3 = { ...validContribution, amountCents: 1000000000000 };
        await assertFails(setDoc(contributionRef, invalidContribution3));
      });

      test('contribution creation fails with future contributionDate', async () => {
        const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
        const contributionRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);

        const invalidContribution = {
          ...validContribution,
          contributionDate: new Date(Date.now() + 24 * 60 * 60 * 1000) // Tomorrow
        };
        await assertFails(setDoc(contributionRef, invalidContribution));
      });

      test('contribution creation fails with very old contributionDate', async () => {
        const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
        const contributionRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);

        const invalidContribution = {
          ...validContribution,
          contributionDate: new Date('2010-01-01') // Too old (before 2014)
        };
        await assertFails(setDoc(contributionRef, invalidContribution));
      });

      test('contribution creation fails with wrong userId', async () => {
        const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
        const contributionRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);

        const invalidContribution = { ...validContribution, userId: USER_2_ID };
        await assertFails(setDoc(contributionRef, invalidContribution));
      });

      test('contribution creation fails with wrong goalId', async () => {
        const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
        const contributionRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);

        const invalidContribution = { ...validContribution, goalId: 'wrong_goal_id' };
        await assertFails(setDoc(contributionRef, invalidContribution));
      });
    });

    describe('Contribution Update and Delete Operations', () => {
      beforeEach(async () => {
        // Create a contribution for update tests
        const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
        const contributionRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);
        await setDoc(contributionRef, validContribution);
      });

      test('valid contribution update succeeds', async () => {
        const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
        const contributionRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);

        const updatedContribution = {
          ...validContribution,
          description: 'Updated monthly savings',
          updatedAt: new Date()
        };

        await assertSucceeds(updateDoc(contributionRef, updatedContribution));
      });

      test('contribution update fails when changing immutable fields', async () => {
        const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
        const contributionRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);

        // Try to change createdAt
        await assertFails(updateDoc(contributionRef, { createdAt: new Date() }));

        // Try to change schemaVersion
        await assertFails(updateDoc(contributionRef, { schemaVersion: 2 }));

        // Try to change goalId
        await assertFails(updateDoc(contributionRef, { goalId: 'different_goal' }));
      });

      test('contribution deletion succeeds for owner', async () => {
        const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
        const contributionRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);

        await assertSucceeds(deleteDoc(contributionRef));
      });

      test('contribution deletion fails for non-owner', async () => {
        const user2Db = testEnv.authenticatedContext(USER_2_ID).firestore();
        const contributionRef = doc(user2Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);

        await assertFails(deleteDoc(contributionRef));
      });
    });
  });
});
