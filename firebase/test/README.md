# BudApp Firestore Security Rules Tests

This directory contains comprehensive tests for BudApp's Firestore Security Rules using the Firebase Emulator Suite.

## Setup

1. **Install Dependencies**
   ```bash
   cd firebase/test
   npm install
   ```

2. **Install Firebase CLI** (if not already installed)
   ```bash
   npm install -g firebase-tools
   ```

3. **Start Firebase Emulators**
   ```bash
   # From project root
   firebase emulators:start --only firestore,auth
   ```

## Running Tests

### Basic Test Execution
```bash
# Run all tests
npm test

# Run tests with watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Using Firebase Emulator
```bash
# Run tests within emulator environment (from project root)
firebase emulators:exec --only firestore,auth "cd firebase/test && npm test"
```

## Test Structure

### Current Test Files

1. **security-rules-basic.test.js**
   - Authentication requirements
   - User data isolation
   - Collection access control
   - Basic CRUD operations

### Planned Test Files

2. **security-rules-validation.test.js** (Phase 3)
   - Data validation rules
   - Required fields validation
   - Field type validation
   - Enum value validation

3. **security-rules-integrity.test.js** (Phase 4)
   - Referential integrity
   - Account reference validation
   - Category reference validation
   - Deletion prevention with dependencies

4. **security-rules-premium.test.js** (Phase 4)
   - Premium feature limits
   - Account limit enforcement
   - Subscription tier validation

5. **security-rules-performance.test.js** (Phase 5)
   - Document access limits
   - Query performance
   - Rule optimization

## Test Categories

### Phase 1: Basic Authentication (✅ Implemented)
- [x] Unauthenticated access denied
- [x] User data isolation
- [x] Owner-only access
- [x] Collection access control

### Phase 2: Data Validation (🚧 Next)
- [ ] Required fields validation
- [ ] Field type validation
- [ ] String length validation
- [ ] Enum value validation
- [ ] Currency code validation

### Phase 3: Business Logic (📋 Planned)
- [ ] Account limits (premium features)
- [ ] Transaction validation
- [ ] Category validation
- [ ] Budget validation

### Phase 4: Referential Integrity (📋 Planned)
- [ ] Account reference validation
- [ ] Category reference validation
- [ ] Deletion prevention
- [ ] Cross-document validation

### Phase 5: Performance & Edge Cases (📋 Planned)
- [ ] Document access limits
- [ ] Batch operation validation
- [ ] Edge case handling
- [ ] Error message validation

## Test Data

### Sample User Profile
```javascript
{
  uid: 'user1',
  email: '<EMAIL>',
  displayName: 'Test User 1',
  createdAt: new Date(),
  lastLoginAt: new Date(),
  preferences: {},
  isEmailVerified: true,
  authProviders: ['password']
}
```

### Sample Account
```javascript
{
  id: 'account1',
  userId: 'user1',
  name: 'Test Checking Account',
  type: 'asset',
  classification: 'checking',
  initialBalanceCents: 100000,
  currencyCode: 'USD',
  isPrimary: true,
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  metadata: {}
}
```

### Sample Transaction
```javascript
{
  id: 'transaction1',
  userId: 'user1',
  type: 'expense',
  status: 'completed',
  amountCents: 2500,
  currencyCode: 'USD',
  fromAccountId: 'account1',
  categoryId: 'category1',
  description: 'Test transaction',
  transactionDate: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
  tags: []
}
```

## Debugging

### Emulator UI
Access the Firebase Emulator UI at http://localhost:4000 to:
- View Firestore data
- Monitor rule evaluations
- Debug rule failures

### Verbose Logging
Enable verbose logging for detailed rule evaluation:
```bash
export FIRESTORE_EMULATOR_HOST=localhost:8080
export FIREBASE_AUTH_EMULATOR_HOST=localhost:9099
```

### Common Issues

1. **Port Conflicts**: Ensure ports 8080 (Firestore) and 9099 (Auth) are available
2. **Rule Syntax Errors**: Check firestore.rules syntax using Firebase CLI
3. **Test Timeouts**: Increase Jest timeout for slow operations

## Contributing

When adding new tests:
1. Follow the existing test structure
2. Use descriptive test names
3. Include both positive and negative test cases
4. Add appropriate documentation
5. Ensure tests are isolated and repeatable

## Security Rule Validation

Before deploying rules to production:
1. Run all tests: `npm test`
2. Validate rule syntax: `firebase firestore:rules:validate`
3. Test with real data scenarios
4. Review rule performance implications
