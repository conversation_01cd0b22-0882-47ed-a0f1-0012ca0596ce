/**
 * Edge Case and Boundary Condition Tests for BudApp
 * 
 * Tests edge cases and boundary conditions including:
 * - Maximum and minimum field lengths
 * - Numeric boundary values
 * - Date edge cases
 * - Special characters and Unicode handling
 * - Large data structures
 * - Concurrent operations
 */

const { initializeTestEnvironment, assertFails, assertSucceeds } = require('@firebase/rules-unit-testing');
const { doc, getDoc, setDoc, updateDoc, deleteDoc, Timestamp } = require('firebase/firestore');

let testEnv;

// Test user IDs
const USER_1_ID = 'user1';

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId: 'budapp-test',
    firestore: {
      rules: require('fs').readFileSync('../../firestore.rules', 'utf8'),
      host: 'localhost',
      port: 8080,
    },
  });
});

afterAll(async () => {
  await testEnv.cleanup();
});

beforeEach(async () => {
  await testEnv.clearFirestore();
});

describe('String Length Boundary Tests', () => {
  test('account name at maximum length (100 chars) succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    const maxLengthName = 'A'.repeat(100); // Exactly 100 characters
    const accountData = {
      id: 'account1',
      userId: USER_1_ID,
      name: maxLengthName,
      type: 'checking',
      classification: 'asset',
      initialBalanceCents: 100000,
      currentBalanceCents: 100000,
      schemaVersion: 1,
      isPrimary: true,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {}
    };
    
    const accountRef = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    await assertSucceeds(setDoc(accountRef, accountData));
  });

  test('account name exceeding maximum length (101 chars) fails', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    const tooLongName = 'A'.repeat(101); // 101 characters
    const accountData = {
      id: 'account1',
      userId: USER_1_ID,
      name: tooLongName,
      type: 'checking',
      classification: 'asset',
      initialBalanceCents: 100000,
      currentBalanceCents: 100000,
      schemaVersion: 1,
      isPrimary: true,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {}
    };
    
    const accountRef = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    await assertFails(setDoc(accountRef, accountData));
  });

  test('tag name at minimum length (1 char) succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    const tagData = {
      id: 'tag1',
      userId: USER_1_ID,
      name: 'A', // Minimum 1 character
      color: '#FF0000',
      usageCount: 0,
      schemaVersion: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    };
    
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/tag1`);
    await assertSucceeds(setDoc(tagRef, tagData));
  });

  test('tag name at maximum length (50 chars) succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    const maxLengthName = 'A'.repeat(50); // Exactly 50 characters
    const tagData = {
      id: 'tag1',
      userId: USER_1_ID,
      name: maxLengthName,
      color: '#FF0000',
      usageCount: 0,
      schemaVersion: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    };
    
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/tag1`);
    await assertSucceeds(setDoc(tagRef, tagData));
  });

  test('empty tag name fails', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    const tagData = {
      id: 'tag1',
      userId: USER_1_ID,
      name: '', // Empty string
      color: '#FF0000',
      usageCount: 0,
      schemaVersion: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    };
    
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/tag1`);
    await assertFails(setDoc(tagRef, tagData));
  });

  test('transaction notes at maximum length (500 chars) succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    // Create account first
    const accountData = {
      id: 'account1',
      userId: USER_1_ID,
      name: 'Test Account',
      type: 'checking',
      classification: 'asset',
      initialBalanceCents: 100000,
      currentBalanceCents: 100000,
      schemaVersion: 1,
      isPrimary: true,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {}
    };
    const accountRef = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    await assertSucceeds(setDoc(accountRef, accountData));
    
    const maxLengthNotes = 'A'.repeat(500); // Exactly 500 characters
    const transactionData = {
      id: 'transaction1',
      userId: USER_1_ID,
      type: 'expense',
      status: 'completed',
      amountCents: 2500,
      fromAccountId: 'account1', // Expense transactions use fromAccountId
      notes: maxLengthNotes,
      description: 'Test transaction',
      schemaVersion: 1,
      transactionDate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    };
    
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/transaction1`);
    await assertSucceeds(setDoc(transactionRef, transactionData));
  });

  test('transaction notes exceeding maximum length (501 chars) fails', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    // Create account first
    const accountData = {
      id: 'account1',
      userId: USER_1_ID,
      name: 'Test Account',
      type: 'checking',
      classification: 'asset',
      initialBalanceCents: 100000,
      currentBalanceCents: 100000,
      schemaVersion: 1,
      isPrimary: true,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {}
    };
    const accountRef = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    await assertSucceeds(setDoc(accountRef, accountData));
    
    const tooLongNotes = 'A'.repeat(501); // 501 characters
    const transactionData = {
      id: 'transaction1',
      userId: USER_1_ID,
      type: 'expense',
      status: 'completed',
      amountCents: 2500,
      fromAccountId: 'account1', // Expense transactions use fromAccountId
      notes: tooLongNotes,
      description: 'Test transaction',
      schemaVersion: 1,
      transactionDate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    };
    
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/transaction1`);
    await assertFails(setDoc(transactionRef, transactionData));
  });
});

describe('Numeric Boundary Tests', () => {
  test('transaction amount at maximum value (********* cents) succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    // Create account first
    const accountData = {
      id: 'account1',
      userId: USER_1_ID,
      name: 'Test Account',
      type: 'checking',
      classification: 'asset',
      initialBalanceCents: 100000,
      currentBalanceCents: 100000,
      schemaVersion: 1,
      isPrimary: true,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {}
    };
    const accountRef = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    await assertSucceeds(setDoc(accountRef, accountData));
    
    const transactionData = {
      id: 'transaction1',
      userId: USER_1_ID,
      type: 'expense',
      status: 'completed',
      amountCents: *********, // Maximum allowed amount
      fromAccountId: 'account1', // Expense transactions use fromAccountId
      description: 'Large transaction',
      schemaVersion: 1,
      transactionDate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    };
    
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/transaction1`);
    await assertSucceeds(setDoc(transactionRef, transactionData));
  });

  test('zero transaction amount fails', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    // Create account first
    const accountData = {
      id: 'account1',
      userId: USER_1_ID,
      name: 'Test Account',
      type: 'checking',
      classification: 'asset',
      initialBalanceCents: 100000,
      currentBalanceCents: 100000,
      schemaVersion: 1,
      isPrimary: true,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {}
    };
    const accountRef = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    await assertSucceeds(setDoc(accountRef, accountData));
    
    const transactionData = {
      id: 'transaction1',
      userId: USER_1_ID,
      type: 'expense',
      status: 'completed',
      amountCents: 0, // Zero amount should fail
      fromAccountId: 'account1', // Expense transactions use fromAccountId
      description: 'Zero transaction',
      schemaVersion: 1,
      transactionDate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    };
    
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/transaction1`);
    await assertFails(setDoc(transactionRef, transactionData));
  });

  test('negative transaction amount fails', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    // Create account first
    const accountData = {
      id: 'account1',
      userId: USER_1_ID,
      name: 'Test Account',
      type: 'checking',
      classification: 'asset',
      initialBalanceCents: 100000,
      currentBalanceCents: 100000,
      schemaVersion: 1,
      isPrimary: true,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {}
    };
    const accountRef = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    await assertSucceeds(setDoc(accountRef, accountData));
    
    const transactionData = {
      id: 'transaction1',
      userId: USER_1_ID,
      type: 'expense',
      status: 'completed',
      amountCents: -1000, // Negative amount should fail
      fromAccountId: 'account1', // Expense transactions use fromAccountId
      description: 'Negative transaction',
      schemaVersion: 1,
      transactionDate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    };
    
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/transaction1`);
    await assertFails(setDoc(transactionRef, transactionData));
  });

  test('tag usage count at maximum value succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    const tagData = {
      id: 'tag1',
      userId: USER_1_ID,
      name: 'High Usage Tag',
      color: '#FF0000',
      usageCount: **********, // Maximum 32-bit integer
      schemaVersion: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    };
    
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/tag1`);
    await assertSucceeds(setDoc(tagRef, tagData));
  });

  test('negative tag usage count fails', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    const tagData = {
      id: 'tag1',
      userId: USER_1_ID,
      name: 'Invalid Tag',
      color: '#FF0000',
      usageCount: -1, // Negative usage count should fail
      schemaVersion: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    };
    
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/tag1`);
    await assertFails(setDoc(tagRef, tagData));
  });
});

describe('Date and Time Edge Cases', () => {
  test('very old date (10 years ago) succeeds for goal contribution', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    // Create goal first
    const goalData = {
      id: 'goal1',
      userId: USER_1_ID,
      name: 'Test Goal',
      targetAmountCents: 100000,
      currentAmountCents: 0,
      targetDate: new Date('2025-12-31'),
      status: 'active',
      colorHex: '#FF0000',
      iconName: 'savings',
      schemaVersion: 1,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {}
    };
    const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
    await assertSucceeds(setDoc(goalRef, goalData));
    
    const tenYearsAgo = new Date();
    tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10);
    
    const contributionData = {
      id: 'contribution1',
      userId: USER_1_ID,
      goalId: 'goal1',
      amountCents: 5000,
      contributionDate: tenYearsAgo,
      description: 'Old contribution',
      schemaVersion: 1,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {}
    };
    
    const contributionRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);
    await assertSucceeds(setDoc(contributionRef, contributionData));
  });

  test('future date fails for goal contribution', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    // Create goal first
    const goalData = {
      id: 'goal1',
      userId: USER_1_ID,
      name: 'Test Goal',
      targetAmountCents: 100000,
      currentAmountCents: 0,
      targetDate: new Date('2025-12-31'),
      status: 'active',
      colorHex: '#FF0000',
      iconName: 'savings',
      schemaVersion: 1,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {}
    };
    const goalRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1`);
    await assertSucceeds(setDoc(goalRef, goalData));
    
    const futureDate = new Date();
    futureDate.setFullYear(futureDate.getFullYear() + 1);
    
    const contributionData = {
      id: 'contribution1',
      userId: USER_1_ID,
      goalId: 'goal1',
      amountCents: 5000,
      contributionDate: futureDate,
      description: 'Future contribution',
      schemaVersion: 1,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {}
    };
    
    const contributionRef = doc(user1Db, `users/${USER_1_ID}/goals/goal1/contributions/contribution1`);
    await assertFails(setDoc(contributionRef, contributionData));
  });
});

describe('Special Characters and Unicode Tests', () => {
  test('account name with Unicode characters succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    const accountData = {
      id: 'account1',
      userId: USER_1_ID,
      name: '测试账户 🏦 Тест', // Unicode characters
      type: 'checking',
      classification: 'asset',
      initialBalanceCents: 100000,
      currentBalanceCents: 100000,
      schemaVersion: 1,
      isPrimary: true,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {}
    };
    
    const accountRef = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    await assertSucceeds(setDoc(accountRef, accountData));
  });

  test('tag name with emojis succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    const tagData = {
      id: 'tag1',
      userId: USER_1_ID,
      name: '🏠 Home 🏡', // Emoji characters
      color: '#FF0000',
      usageCount: 0,
      schemaVersion: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    };
    
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/tag1`);
    await assertSucceeds(setDoc(tagRef, tagData));
  });

  test('category name with special characters succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    const categoryData = {
      id: 'category1',
      userId: USER_1_ID,
      name: 'Food & Dining - 50% off!', // Special characters
      type: 'expense',
      schemaVersion: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
      sortOrder: 1
    };
    
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/category1`);
    await assertSucceeds(setDoc(categoryRef, categoryData));
  });
});
