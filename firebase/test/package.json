{"name": "budapp-firestore-rules-tests", "version": "1.0.0", "description": "Firestore Security Rules tests for BudApp", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["firebase", "firestore", "security-rules", "testing"], "author": "BudApp Team", "license": "MIT", "devDependencies": {"@firebase/rules-unit-testing": "^3.0.4", "firebase": "^10.14.1", "jest": "^29.7.0"}, "jest": {"testEnvironment": "node", "testMatch": ["**/*.test.js"], "testTimeout": 15000, "maxWorkers": 1, "collectCoverageFrom": ["**/*.js", "!**/node_modules/**"]}}