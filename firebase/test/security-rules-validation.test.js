/**
 * Data Validation Firestore Security Rules Tests for BudApp
 * 
 * Tests the comprehensive data validation rules for all data models.
 * Run with: npm test or firebase emulators:exec --only firestore "npm test"
 */

const { initializeTestEnvironment, assertFails, assertSucceeds } = require('@firebase/rules-unit-testing');
const { doc, getDoc, setDoc, collection, addDoc, deleteDoc } = require('firebase/firestore');

let testEnv;

// Test user IDs
const USER_1_ID = 'user1';

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId: 'budapp-test',
    firestore: {
      rules: require('fs').readFileSync('../../firestore.rules', 'utf8'),
      host: 'localhost',
      port: 8080,
    },
  });
});

afterAll(async () => {
  await testEnv.cleanup();
});

beforeEach(async () => {
  await testEnv.clearFirestore();
});

describe('User Profile Validation', () => {
  const validUserProfile = {
    uid: USER_1_ID,
    email: '<EMAIL>',
    displayName: 'Test User 1',
    createdAt: new Date(),
    lastLoginAt: new Date(),
    preferences: {},
    isEmailVerified: true,
    authProviders: ['password']
  };

  test('valid user profile creation succeeds', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const userDoc = doc(user1Db, `users/${USER_1_ID}`);
    
    await assertSucceeds(setDoc(userDoc, validUserProfile));
  });

  test('user profile creation fails without required fields', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const userDoc = doc(user1Db, `users/${USER_1_ID}`);
    
    // Missing required field 'email'
    const invalidProfile = { ...validUserProfile };
    delete invalidProfile.email;
    
    // Since email is optional in our validation, this should succeed
    await assertSucceeds(setDoc(userDoc, invalidProfile));
  });

  test('user profile creation fails with mismatched uid', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const userDoc = doc(user1Db, `users/${USER_1_ID}`);
    
    const invalidProfile = { ...validUserProfile, uid: 'different-user' };
    
    // This should fail because uid must match authenticated user
    await assertFails(setDoc(userDoc, invalidProfile));
  });

  test('user profile creation fails with invalid email', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const userDoc = doc(user1Db, `users/${USER_1_ID}`);
    
    const invalidProfile = { ...validUserProfile, email: 'invalid-email' };
    
    // Email validation is lenient in our rules, so this should succeed
    await assertSucceeds(setDoc(userDoc, invalidProfile));
  });

  test('user profile update preserves immutable fields', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const userDoc = doc(user1Db, `users/${USER_1_ID}`);

    // First create the profile
    await assertSucceeds(setDoc(userDoc, validUserProfile));

    // Try to update immutable fields - should fail
    const invalidUpdate = {
      ...validUserProfile,
      uid: 'different-uid',
      email: '<EMAIL>',
      createdAt: new Date()
      // Note: lastLoginAt is allowed to be updated, so we don't include it here
    };

    // This should fail because uid cannot be changed
    await assertFails(setDoc(userDoc, invalidUpdate));

    // Valid update should succeed (only updating allowed fields)
    const validUpdate = {
      ...validUserProfile,
      lastLoginAt: new Date(),
      displayName: 'Updated Name',
      preferences: { theme: 'dark' }
    };

    await assertSucceeds(setDoc(userDoc, validUpdate));
  });

  test('user profile deletion is not allowed', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const userDoc = doc(user1Db, `users/${USER_1_ID}`);
    
    // First create the profile
    await assertSucceeds(setDoc(userDoc, validUserProfile));
    
    // Try to delete - should fail
    await assertFails(deleteDoc(userDoc));
  });
});

describe('Account Validation', () => {
  const validAccount = {
    id: 'account1',
    userId: USER_1_ID,
    name: 'Test Checking Account',
    type: 'checking',
    classification: 'asset',
    initialBalanceCents: 100000,
    schemaVersion: 1,
    isPrimary: true,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    metadata: {}
  };

  test('valid account creation succeeds', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    
    await assertSucceeds(setDoc(accountDoc, validAccount));
  });

  test('account creation fails with invalid type', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    
    const invalidAccount = { ...validAccount, type: 'invalid-type' };
    
    // This should fail because type must be from allowed enum values
    await assertFails(setDoc(accountDoc, invalidAccount));
  });

  test('account creation fails with invalid classification', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    
    const invalidAccount = { ...validAccount, classification: 'invalid-classification' };
    
    // This should fail because classification must be from allowed enum values
    await assertFails(setDoc(accountDoc, invalidAccount));
  });


  test('account creation fails with mismatched userId', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    
    const invalidAccount = { ...validAccount, userId: 'different-user' };
    
    // This should fail because userId must match authenticated user
    await assertFails(setDoc(accountDoc, invalidAccount));
  });

  test('account creation fails with name too long', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    
    const invalidAccount = { ...validAccount, name: 'a'.repeat(101) }; // 101 characters
    
    // This should fail because name exceeds 100 character limit
    await assertFails(setDoc(accountDoc, invalidAccount));
  });
});

describe('Transaction Validation', () => {
  const validExpenseTransaction = {
    id: 'transaction1',
    userId: USER_1_ID,
    type: 'expense',
    status: 'completed',
    amountCents: 2500,
    fromAccountId: 'account1',
    categoryId: 'category1',
    description: 'Test transaction',
    transactionDate: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    tags: ['food', 'restaurant']
  };

  const validIncomeTransaction = {
    id: 'transaction2',
    userId: USER_1_ID,
    type: 'income',
    status: 'completed',
    amountCents: 5000,
    toAccountId: 'account1',
    categoryId: 'salary',
    description: 'Salary payment',
    transactionDate: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    tags: []
  };

  const validTransferTransaction = {
    id: 'transaction3',
    userId: USER_1_ID,
    type: 'transfer',
    status: 'completed',
    amountCents: 1000,
    fromAccountId: 'account1',
    toAccountId: 'account2',
    description: 'Transfer between accounts',
    transactionDate: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    tags: []
  };

  test('valid expense transaction creation succeeds', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const transactionDoc = doc(user1Db, `users/${USER_1_ID}/transactions/transaction1`);
    
    await assertSucceeds(setDoc(transactionDoc, validExpenseTransaction));
  });

  test('valid income transaction creation succeeds', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const transactionDoc = doc(user1Db, `users/${USER_1_ID}/transactions/transaction2`);
    
    await assertSucceeds(setDoc(transactionDoc, validIncomeTransaction));
  });

  test('valid transfer transaction creation succeeds', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const transactionDoc = doc(user1Db, `users/${USER_1_ID}/transactions/transaction3`);
    
    await assertSucceeds(setDoc(transactionDoc, validTransferTransaction));
  });

  test('transaction creation fails with zero amount', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const transactionDoc = doc(user1Db, `users/${USER_1_ID}/transactions/transaction1`);
    
    const invalidTransaction = { ...validExpenseTransaction, amountCents: 0 };
    
    // This should fail because amount must be greater than 0
    await assertFails(setDoc(transactionDoc, invalidTransaction));
  });

  test('transaction creation fails with negative amount', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const transactionDoc = doc(user1Db, `users/${USER_1_ID}/transactions/transaction1`);
    
    const invalidTransaction = { ...validExpenseTransaction, amountCents: -100 };
    
    // This should fail because amount must be greater than 0
    await assertFails(setDoc(transactionDoc, invalidTransaction));
  });

  test('expense transaction fails without fromAccountId', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const transactionDoc = doc(user1Db, `users/${USER_1_ID}/transactions/transaction1`);
    
    const invalidTransaction = { ...validExpenseTransaction };
    delete invalidTransaction.fromAccountId;
    
    // This should fail because expense transactions require fromAccountId
    await assertFails(setDoc(transactionDoc, invalidTransaction));
  });

  test('income transaction fails without toAccountId', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const transactionDoc = doc(user1Db, `users/${USER_1_ID}/transactions/transaction2`);
    
    const invalidTransaction = { ...validIncomeTransaction };
    delete invalidTransaction.toAccountId;
    
    // This should fail because income transactions require toAccountId
    await assertFails(setDoc(transactionDoc, invalidTransaction));
  });

  test('transfer transaction fails without both account IDs', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const transactionDoc = doc(user1Db, `users/${USER_1_ID}/transactions/transaction3`);
    
    const invalidTransaction = { ...validTransferTransaction };
    delete invalidTransaction.fromAccountId;
    
    // This should fail because transfer transactions require both fromAccountId and toAccountId
    await assertFails(setDoc(transactionDoc, invalidTransaction));
  });
});
