/**
 * Basic Firestore Security Rules Tests for BudApp
 * 
 * Tests the fundamental authentication and user data isolation rules.
 * Run with: npm test or firebase emulators:exec --only firestore "npm test"
 */

const { initializeTestEnvironment, assertFails, assertSucceeds } = require('@firebase/rules-unit-testing');
const { doc, getDoc, setDoc, collection, addDoc, deleteDoc } = require('firebase/firestore');

let testEnv;

// Test user IDs
const USER_1_ID = 'user1';
const USER_2_ID = 'user2';

// Sample test data
const sampleUserProfile = {
  uid: USER_1_ID,
  email: '<EMAIL>',
  displayName: 'Test User 1',
  schemaVersion: 1,
  createdAt: new Date(),
  lastLoginAt: new Date(),
  preferences: {},
  isEmailVerified: true,
  authProviders: ['password']
};

const sampleAccount = {
  id: 'account1',
  userId: USER_1_ID,
  name: 'Test Checking Account',
  type: 'checking',
  classification: 'asset',
  initialBalanceCents: 100000, // $1000.00
  currentBalanceCents: 100000, // $1000.00
  schemaVersion: 1,
  isPrimary: true,
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  metadata: {}
};

const sampleTransaction = {
  id: 'transaction1',
  userId: USER_1_ID,
  type: 'expense',
  status: 'completed',
  amountCents: 2500, // $25.00
  schemaVersion: 1,
  fromAccountId: 'account1',
  categoryId: 'category1',
  description: 'Test transaction',
  transactionDate: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
  tags: []
};

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId: 'budapp-test',
    firestore: {
      rules: require('fs').readFileSync('../../firestore.rules', 'utf8'),
      host: 'localhost',
      port: 8080,
    },
  });
});

afterAll(async () => {
  await testEnv.cleanup();
});

beforeEach(async () => {
  await testEnv.clearFirestore();
});

describe('Authentication Requirements', () => {
  test('unauthenticated users cannot read user profiles', async () => {
    const unauthedDb = testEnv.unauthenticatedContext().firestore();
    const userDoc = doc(unauthedDb, `users/${USER_1_ID}`);
    
    await assertFails(getDoc(userDoc));
  });

  test('unauthenticated users cannot write user profiles', async () => {
    const unauthedDb = testEnv.unauthenticatedContext().firestore();
    const userDoc = doc(unauthedDb, `users/${USER_1_ID}`);
    
    await assertFails(setDoc(userDoc, sampleUserProfile));
  });

  test('unauthenticated users cannot access user subcollections', async () => {
    const unauthedDb = testEnv.unauthenticatedContext().firestore();
    
    // Test accounts subcollection
    const accountDoc = doc(unauthedDb, `users/${USER_1_ID}/accounts/account1`);
    await assertFails(getDoc(accountDoc));
    await assertFails(setDoc(accountDoc, sampleAccount));

    // Test transactions subcollection
    const transactionDoc = doc(unauthedDb, `users/${USER_1_ID}/transactions/transaction1`);
    await assertFails(getDoc(transactionDoc));
    await assertFails(setDoc(transactionDoc, sampleTransaction));
  });
});

describe('User Data Isolation', () => {
  test('users can read and write their own profile', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const userDoc = doc(user1Db, `users/${USER_1_ID}`);
    
    // User can write their own profile
    await assertSucceeds(setDoc(userDoc, sampleUserProfile));
    
    // User can read their own profile
    await assertSucceeds(getDoc(userDoc));
  });

  test('users cannot read other users profiles', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const user2Doc = doc(user1Db, `users/${USER_2_ID}`);
    
    await assertFails(getDoc(user2Doc));
  });

  test('users cannot write to other users profiles', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const user2Doc = doc(user1Db, `users/${USER_2_ID}`);
    
    await assertFails(setDoc(user2Doc, { ...sampleUserProfile, uid: USER_2_ID }));
  });

  test('users can access their own subcollections', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    
    // Test accounts subcollection
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    await assertSucceeds(setDoc(accountDoc, sampleAccount));
    await assertSucceeds(getDoc(accountDoc));

    // Test transactions subcollection
    const transactionDoc = doc(user1Db, `users/${USER_1_ID}/transactions/transaction1`);
    await assertSucceeds(setDoc(transactionDoc, sampleTransaction));
    await assertSucceeds(getDoc(transactionDoc));
  });

  test('users cannot access other users subcollections', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    
    // Test accounts subcollection
    const user2AccountDoc = doc(user1Db, `users/${USER_2_ID}/accounts/account1`);
    await assertFails(getDoc(user2AccountDoc));
    await assertFails(setDoc(user2AccountDoc, { ...sampleAccount, userId: USER_2_ID }));

    // Test transactions subcollection
    const user2TransactionDoc = doc(user1Db, `users/${USER_2_ID}/transactions/transaction1`);
    await assertFails(getDoc(user2TransactionDoc));
    await assertFails(setDoc(user2TransactionDoc, { ...sampleTransaction, userId: USER_2_ID }));
  });
});

describe('Collection Access Control', () => {
  test('users can access all their subcollections', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();

    // Test accounts subcollection with proper data
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/test1`);
    const accountData = {
      id: 'test1',
      userId: USER_1_ID,
      name: 'Test Account',
      type: 'checking',
      classification: 'asset',
      initialBalanceCents: 50000,
      schemaVersion: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    };
    await assertSucceeds(setDoc(accountDoc, accountData));
    await assertSucceeds(getDoc(accountDoc));

    // Test transactions subcollection with proper data
    const transactionDoc = doc(user1Db, `users/${USER_1_ID}/transactions/test1`);
    const transactionData = {
      id: 'test1',
      userId: USER_1_ID,
      type: 'expense',
      status: 'completed',
      amountCents: 1000,
      schemaVersion: 1,
      fromAccountId: 'test1',
      description: 'Test transaction',
      transactionDate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    await assertSucceeds(setDoc(transactionDoc, transactionData));
    await assertSucceeds(getDoc(transactionDoc));

    // Test categories subcollection with proper data
    const categoryDoc = doc(user1Db, `users/${USER_1_ID}/categories/test1`);
    const categoryData = {
      id: 'test1',
      userId: USER_1_ID,
      name: 'Test Category',
      type: 'expense',
      isActive: true,
      sortOrder: 0,
      schemaVersion: 1,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    await assertSucceeds(setDoc(categoryDoc, categoryData));
    await assertSucceeds(getDoc(categoryDoc));

    // Test budgets subcollection with proper data
    const budgetDoc = doc(user1Db, `users/${USER_1_ID}/budgets/test1`);
    const budgetData = {
      id: 'test1',
      userId: USER_1_ID,
      type: 'expense',
      plannedAmountCents: 100000,
      currentAmountCents: 0,
      period: 'monthly',
      periodStart: new Date(),
      isActive: true,
      schemaVersion: 1,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    await assertSucceeds(setDoc(budgetDoc, budgetData));
    await assertSucceeds(getDoc(budgetDoc));

    // Test goals subcollection with proper data
    const goalDoc = doc(user1Db, `users/${USER_1_ID}/goals/test1`);
    const goalData = {
      id: 'test1',
      userId: USER_1_ID,
      name: 'Test Goal',
      targetAmountCents: 50000,
      targetDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
      createdAt: new Date(),
      updatedAt: new Date()
    };
    await assertSucceeds(setDoc(goalDoc, goalData));
    await assertSucceeds(getDoc(goalDoc));

    // Test tags subcollection with proper data
    const tagDoc = doc(user1Db, `users/${USER_1_ID}/tags/test1`);
    const tagData = {
      id: 'test1',
      userId: USER_1_ID,
      name: 'Test Tag',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    await assertSucceeds(setDoc(tagDoc, tagData));
    await assertSucceeds(getDoc(tagDoc));
  });

  test('access to undefined paths is denied', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    
    // Test access to non-existent collection
    const invalidDoc = doc(user1Db, 'invalid-collection/doc1');
    await assertFails(getDoc(invalidDoc));
    await assertFails(setDoc(invalidDoc, { test: 'data' }));

    // Test access to non-user path
    const nonUserDoc = doc(user1Db, 'public/doc1');
    await assertFails(getDoc(nonUserDoc));
    await assertFails(setDoc(nonUserDoc, { test: 'data' }));
  });
});

describe('Basic CRUD Operations', () => {
  test('users can perform CRUD operations on their data', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    
    // Create
    await assertSucceeds(setDoc(accountDoc, sampleAccount));
    
    // Read
    await assertSucceeds(getDoc(accountDoc));
    
    // Update
    await assertSucceeds(setDoc(accountDoc, { ...sampleAccount, name: 'Updated Account' }, { merge: true }));
    
    // Delete (must set isActive to false first)
    await assertSucceeds(setDoc(accountDoc, { ...sampleAccount, isActive: false }, { merge: true }));
    await assertSucceeds(deleteDoc(accountDoc));
  });
});
