/**
 * Transaction Security Rules Tests for BudApp
 * 
 * Comprehensive tests for transaction collection security rules validation.
 * Tests transaction creation, updates, deletion, and field validation.
 * Covers complex business logic for account relationships and transaction types.
 */

const { initializeTestEnvironment, assertFails, assertSucceeds } = require('@firebase/rules-unit-testing');
const { doc, getDoc, setDoc, updateDoc, deleteDoc, Timestamp } = require('firebase/firestore');

let testEnv;

// Test user IDs
const USER_1_ID = 'user1';
const USER_2_ID = 'user2';
const TRANSACTION_ID = 'transaction1';

// Sample account IDs for testing
const ACCOUNT_1_ID = 'account1';
const ACCOUNT_2_ID = 'account2';
const CATEGORY_ID = 'category1';
const TAG_ID = 'tag1';

// Sample transaction data for different types
const validIncomeTransaction = {
  id: TRANSACTION_ID,
  userId: USER_1_ID,
  type: 'income',
  status: 'pending',
  amountCents: 50000, // $500.00
  toAccountId: ACCOUNT_1_ID,
  categoryId: CATEGORY_ID,
  description: 'Salary payment',
  notes: 'Monthly salary',
  tagIds: [TAG_ID],
  schemaVersion: 1,
  transactionDate: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
  metadata: {}
};

const validExpenseTransaction = {
  id: TRANSACTION_ID,
  userId: USER_1_ID,
  type: 'expense',
  status: 'completed',
  amountCents: 2500, // $25.00
  fromAccountId: ACCOUNT_1_ID,
  categoryId: CATEGORY_ID,
  description: 'Coffee shop',
  notes: 'Morning coffee',
  tagIds: [TAG_ID],
  schemaVersion: 1,
  transactionDate: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
  metadata: {}
};

const validTransferTransaction = {
  id: TRANSACTION_ID,
  userId: USER_1_ID,
  type: 'transfer',
  status: 'completed',
  amountCents: 10000, // $100.00
  fromAccountId: ACCOUNT_1_ID,
  toAccountId: ACCOUNT_2_ID,
  description: 'Transfer to savings',
  notes: 'Monthly savings transfer',
  tagIds: [],
  schemaVersion: 1,
  transactionDate: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
  metadata: {}
};

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId: 'budapp-test',
    firestore: {
      rules: require('fs').readFileSync('../../firestore.rules', 'utf8'),
      host: 'localhost',
      port: 8080,
    },
  });
});

afterAll(async () => {
  await testEnv.cleanup();
});

beforeEach(async () => {
  await testEnv.clearFirestore();
});

describe('Transaction Access Control', () => {
  test('authenticated user can read their own transactions', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    // First create a transaction
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    await assertSucceeds(setDoc(transactionRef, validIncomeTransaction));
    
    // Then read it
    await assertSucceeds(getDoc(transactionRef));
  });

  test('authenticated user cannot read other users transactions', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user2Context = testEnv.authenticatedContext(USER_2_ID);
    const user1Db = user1Context.firestore();
    const user2Db = user2Context.firestore();
    
    // User 1 creates a transaction
    const transactionRef1 = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    await assertSucceeds(setDoc(transactionRef1, validIncomeTransaction));
    
    // User 2 tries to read User 1's transaction
    const transactionRef2 = doc(user2Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    await assertFails(getDoc(transactionRef2));
  });

  test('unauthenticated user cannot access transactions', async () => {
    const unauthedDb = testEnv.unauthenticatedContext().firestore();
    const transactionRef = doc(unauthedDb, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    await assertFails(getDoc(transactionRef));
    await assertFails(setDoc(transactionRef, validIncomeTransaction));
  });
});

describe('Transaction Creation Validation', () => {
  test('valid income transaction creation succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    await assertSucceeds(setDoc(transactionRef, validIncomeTransaction));
  });

  test('valid expense transaction creation succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    await assertSucceeds(setDoc(transactionRef, validExpenseTransaction));
  });

  test('valid transfer transaction creation succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    await assertSucceeds(setDoc(transactionRef, validTransferTransaction));
  });

  test('transaction creation validates userId matches path and auth', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Wrong userId
    const invalidTransaction = {
      ...validIncomeTransaction,
      userId: USER_2_ID
    };
    
    await assertFails(setDoc(transactionRef, invalidTransaction));
  });

  test('transaction creation validates type enum', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Invalid type
    const invalidTransaction = {
      ...validIncomeTransaction,
      type: 'invalid_type'
    };
    
    await assertFails(setDoc(transactionRef, invalidTransaction));
    
    // Valid types
    const validTypes = ['income', 'expense', 'transfer'];
    
    for (let i = 0; i < validTypes.length; i++) {
      const type = validTypes[i];
      const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/transaction_${i}`);
      let transactionData;
      
      if (type === 'income') {
        transactionData = { ...validIncomeTransaction, id: `transaction_${i}`, type: type };
      } else if (type === 'expense') {
        transactionData = { ...validExpenseTransaction, id: `transaction_${i}`, type: type };
      } else { // transfer
        transactionData = { ...validTransferTransaction, id: `transaction_${i}`, type: type };
      }
      
      await assertSucceeds(setDoc(transactionRef, transactionData));
    }
  });

  test('transaction creation validates status enum', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Invalid status
    const invalidTransaction = {
      ...validIncomeTransaction,
      status: 'invalid_status'
    };
    
    await assertFails(setDoc(transactionRef, invalidTransaction));
    
    // Valid statuses
    const validStatuses = ['pending', 'completed', 'cancelled', 'failed'];
    
    for (let i = 0; i < validStatuses.length; i++) {
      const status = validStatuses[i];
      const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/transaction_${i}`);
      const transactionData = {
        ...validIncomeTransaction,
        id: `transaction_${i}`,
        status: status
      };
      
      await assertSucceeds(setDoc(transactionRef, transactionData));
    }
  });

  test('transaction creation validates amountCents is positive integer', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Negative amount
    const negativeTransaction = {
      ...validIncomeTransaction,
      amountCents: -1000
    };
    
    await assertFails(setDoc(transactionRef, negativeTransaction));
    
    // Zero amount
    const zeroTransaction = {
      ...validIncomeTransaction,
      amountCents: 0
    };
    
    await assertFails(setDoc(transactionRef, zeroTransaction));
    
    // String amount
    const stringTransaction = {
      ...validIncomeTransaction,
      amountCents: "1000"
    };
    
    await assertFails(setDoc(transactionRef, stringTransaction));
    
    // Valid positive amount
    const validTransaction = {
      ...validIncomeTransaction,
      amountCents: 1000
    };
    
    await assertSucceeds(setDoc(transactionRef, validTransaction));
  });

  test('income transaction requires toAccountId', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Income without toAccountId
    const invalidTransaction = {
      ...validIncomeTransaction,
      toAccountId: undefined
    };
    delete invalidTransaction.toAccountId;
    
    await assertFails(setDoc(transactionRef, invalidTransaction));
    
    // Income with toAccountId
    await assertSucceeds(setDoc(transactionRef, validIncomeTransaction));
  });

  test('expense transaction requires fromAccountId', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Expense without fromAccountId
    const invalidTransaction = {
      ...validExpenseTransaction,
      fromAccountId: undefined
    };
    delete invalidTransaction.fromAccountId;
    
    await assertFails(setDoc(transactionRef, invalidTransaction));
    
    // Expense with fromAccountId
    await assertSucceeds(setDoc(transactionRef, validExpenseTransaction));
  });

  test('transfer transaction requires both fromAccountId and toAccountId', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Transfer without fromAccountId
    const invalidTransaction1 = {
      ...validTransferTransaction,
      fromAccountId: undefined
    };
    delete invalidTransaction1.fromAccountId;
    
    await assertFails(setDoc(transactionRef, invalidTransaction1));
    
    // Transfer without toAccountId
    const invalidTransaction2 = {
      ...validTransferTransaction,
      toAccountId: undefined
    };
    delete invalidTransaction2.toAccountId;
    
    await assertFails(setDoc(transactionRef, invalidTransaction2));
    
    // Transfer with both accounts
    await assertSucceeds(setDoc(transactionRef, validTransferTransaction));
  });

  test('transaction creation validates optional string fields', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Empty string fields should fail
    const invalidTransaction = {
      ...validIncomeTransaction,
      description: '',
      notes: '',
      categoryId: '',
      fromAccountId: '',
      toAccountId: ''
    };
    
    await assertFails(setDoc(transactionRef, invalidTransaction));
    
    // Valid string fields
    const validTransaction = {
      ...validIncomeTransaction,
      description: 'Valid description',
      notes: 'Valid notes',
      categoryId: 'valid_category',
      toAccountId: 'valid_account'
    };
    
    await assertSucceeds(setDoc(transactionRef, validTransaction));
  });

  test('transaction creation validates notes length (max 500 characters)', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Too long notes (501 characters)
    const invalidTransaction = {
      ...validIncomeTransaction,
      notes: 'a'.repeat(501)
    };
    
    await assertFails(setDoc(transactionRef, invalidTransaction));
    
    // Valid maximum length (500 characters)
    const validTransaction = {
      ...validIncomeTransaction,
      notes: 'a'.repeat(500)
    };
    
    await assertSucceeds(setDoc(transactionRef, validTransaction));
  });

  test('transaction creation validates schemaVersion is positive integer', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Invalid schemaVersion
    const invalidTransaction = {
      ...validIncomeTransaction,
      schemaVersion: 0
    };
    
    await assertFails(setDoc(transactionRef, invalidTransaction));
    
    // Valid schemaVersion
    const validTransaction = {
      ...validIncomeTransaction,
      schemaVersion: 1
    };
    
    await assertSucceeds(setDoc(transactionRef, validTransaction));
  });

  test('transaction creation validates timestamps', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Invalid timestamps
    const invalidTransaction = {
      ...validIncomeTransaction,
      transactionDate: "2024-01-01T00:00:00Z",
      createdAt: "invalid_timestamp",
      updatedAt: 1234567890
    };
    
    await assertFails(setDoc(transactionRef, invalidTransaction));
    
    // Valid timestamps
    const validTransaction = {
      ...validIncomeTransaction,
      transactionDate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await assertSucceeds(setDoc(transactionRef, validTransaction));
  });

  test('transaction creation validates tagIds is list', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Invalid tagIds (not a list)
    const invalidTransaction = {
      ...validIncomeTransaction,
      tagIds: "not_a_list"
    };
    
    await assertFails(setDoc(transactionRef, invalidTransaction));
    
    // Valid tagIds (list)
    const validTransaction = {
      ...validIncomeTransaction,
      tagIds: ['tag1', 'tag2']
    };
    
    await assertSucceeds(setDoc(transactionRef, validTransaction));
  });

  test('transaction creation allows optional fields to be missing', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Minimal transaction without optional fields
    const minimalTransaction = {
      userId: USER_1_ID,
      type: 'income',
      status: 'pending',
      amountCents: 1000,
      toAccountId: ACCOUNT_1_ID,
      transactionDate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await assertSucceeds(setDoc(transactionRef, minimalTransaction));
  });
});

describe('Transaction Update Validation', () => {
  beforeEach(async () => {
    // Create a transaction for update tests
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    await setDoc(transactionRef, validIncomeTransaction);
  });

  test('valid transaction update succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    const updatedData = {
      ...validIncomeTransaction,
      description: 'Updated description',
      notes: 'Updated notes',
      status: 'completed',
      amountCents: 60000,
      updatedAt: new Date()
    };
    
    await assertSucceeds(updateDoc(transactionRef, updatedData));
  });

  test('transaction update prevents id changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    const updatedData = {
      ...validIncomeTransaction,
      id: 'different_id'
    };
    
    await assertFails(updateDoc(transactionRef, updatedData));
  });

  test('transaction update prevents userId changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    const updatedData = {
      ...validIncomeTransaction,
      userId: USER_2_ID
    };
    
    await assertFails(updateDoc(transactionRef, updatedData));
  });

  test('transaction update prevents createdAt changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    const updatedData = {
      ...validIncomeTransaction,
      createdAt: new Date()
    };
    
    await assertFails(updateDoc(transactionRef, updatedData));
  });

  test('transaction update validates type enum', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Invalid type
    const invalidData = {
      ...validIncomeTransaction,
      type: 'invalid_type'
    };
    
    await assertFails(updateDoc(transactionRef, invalidData));
    
    // Valid type change (income to expense with account adjustment)
    const validData = {
      ...validIncomeTransaction,
      type: 'expense',
      fromAccountId: ACCOUNT_1_ID,
      toAccountId: undefined
    };
    delete validData.toAccountId;
    
    await assertSucceeds(updateDoc(transactionRef, validData));
  });

  test('transaction update validates status enum', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Invalid status
    const invalidData = {
      ...validIncomeTransaction,
      status: 'invalid_status'
    };
    
    await assertFails(updateDoc(transactionRef, invalidData));
    
    // Valid status
    const validData = {
      ...validIncomeTransaction,
      status: 'completed'
    };
    
    await assertSucceeds(updateDoc(transactionRef, validData));
  });

  test('transaction update validates amountCents is positive integer', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Invalid amount
    const invalidData = {
      ...validIncomeTransaction,
      amountCents: -100
    };
    
    await assertFails(updateDoc(transactionRef, invalidData));
    
    // Valid amount
    const validData = {
      ...validIncomeTransaction,
      amountCents: 75000
    };
    
    await assertSucceeds(updateDoc(transactionRef, validData));
  });

  test('transaction update validates account logic on type change', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Change income to transfer but missing fromAccountId
    const invalidData = {
      ...validIncomeTransaction,
      type: 'transfer',
      // Still has toAccountId from original income transaction
      // But missing fromAccountId required for transfer
    };
    
    await assertFails(updateDoc(transactionRef, invalidData));
    
    // Change income to transfer with both accounts
    const validData = {
      ...validIncomeTransaction,
      type: 'transfer',
      fromAccountId: ACCOUNT_1_ID,
      toAccountId: ACCOUNT_2_ID
    };
    
    await assertSucceeds(updateDoc(transactionRef, validData));
  });

  test('transaction update validates notes length (max 500 characters)', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Too long notes
    const invalidData = {
      ...validIncomeTransaction,
      notes: 'a'.repeat(501)
    };
    
    await assertFails(updateDoc(transactionRef, invalidData));
    
    // Valid notes
    const validData = {
      ...validIncomeTransaction,
      notes: 'Updated notes within limit'
    };
    
    await assertSucceeds(updateDoc(transactionRef, validData));
  });

  test('transaction update validates schemaVersion is positive integer', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Invalid schemaVersion
    const invalidData = {
      ...validIncomeTransaction,
      schemaVersion: 0
    };
    
    await assertFails(updateDoc(transactionRef, invalidData));
    
    // Valid schemaVersion
    const validData = {
      ...validIncomeTransaction,
      schemaVersion: 2
    };
    
    await assertSucceeds(updateDoc(transactionRef, validData));
  });

  test('transaction update validates timestamps', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Invalid updatedAt
    const invalidData = {
      ...validIncomeTransaction,
      updatedAt: "invalid_timestamp"
    };
    
    await assertFails(updateDoc(transactionRef, invalidData));
    
    // Valid updatedAt
    const validData = {
      ...validIncomeTransaction,
      updatedAt: new Date()
    };
    
    await assertSucceeds(updateDoc(transactionRef, validData));
  });

  test('transaction update validates tagIds is list', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Invalid tagIds
    const invalidData = {
      ...validIncomeTransaction,
      tagIds: "not_a_list"
    };
    
    await assertFails(updateDoc(transactionRef, invalidData));
    
    // Valid tagIds
    const validData = {
      ...validIncomeTransaction,
      tagIds: ['tag1', 'tag2', 'tag3']
    };
    
    await assertSucceeds(updateDoc(transactionRef, validData));
  });
});

describe('Transaction Deletion', () => {
  beforeEach(async () => {
    // Create a transaction for deletion tests
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    await setDoc(transactionRef, validIncomeTransaction);
  });

  test('transaction owner can delete transaction', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    await assertSucceeds(deleteDoc(transactionRef));
  });

  test('other users cannot delete transaction', async () => {
    const user2Context = testEnv.authenticatedContext(USER_2_ID);
    const user2Db = user2Context.firestore();
    const transactionRef = doc(user2Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    await assertFails(deleteDoc(transactionRef));
  });

  test('unauthenticated user cannot delete transaction', async () => {
    const unauthedDb = testEnv.unauthenticatedContext().firestore();
    const transactionRef = doc(unauthedDb, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    await assertFails(deleteDoc(transactionRef));
  });
});

describe('Transaction Business Logic', () => {
  test('transaction creation validates all data types', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Wrong data types
    const invalidTransaction = {
      userId: 123, // Should be string
      type: 123, // Should be string
      status: 123, // Should be string
      amountCents: "1000", // Should be number
      toAccountId: 123, // Should be string
      categoryId: 123, // Should be string
      description: 123, // Should be string
      notes: 123, // Should be string
      tagIds: "not_a_list", // Should be list
      schemaVersion: "1", // Should be number
      transactionDate: "invalid", // Should be timestamp
      createdAt: "invalid", // Should be timestamp
      updatedAt: "invalid", // Should be timestamp
      metadata: "not_a_map" // Should be map
    };
    
    await assertFails(setDoc(transactionRef, invalidTransaction));
  });

  test('transaction supports all valid type and status combinations', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    // Test all valid combinations
    const validCombinations = [
      { type: 'income', status: 'pending', accounts: { toAccountId: ACCOUNT_1_ID } },
      { type: 'income', status: 'completed', accounts: { toAccountId: ACCOUNT_1_ID } },
      { type: 'income', status: 'cancelled', accounts: { toAccountId: ACCOUNT_1_ID } },
      { type: 'income', status: 'failed', accounts: { toAccountId: ACCOUNT_1_ID } },
      { type: 'expense', status: 'pending', accounts: { fromAccountId: ACCOUNT_1_ID } },
      { type: 'expense', status: 'completed', accounts: { fromAccountId: ACCOUNT_1_ID } },
      { type: 'expense', status: 'cancelled', accounts: { fromAccountId: ACCOUNT_1_ID } },
      { type: 'expense', status: 'failed', accounts: { fromAccountId: ACCOUNT_1_ID } },
      { type: 'transfer', status: 'pending', accounts: { fromAccountId: ACCOUNT_1_ID, toAccountId: ACCOUNT_2_ID } },
      { type: 'transfer', status: 'completed', accounts: { fromAccountId: ACCOUNT_1_ID, toAccountId: ACCOUNT_2_ID } },
      { type: 'transfer', status: 'cancelled', accounts: { fromAccountId: ACCOUNT_1_ID, toAccountId: ACCOUNT_2_ID } },
      { type: 'transfer', status: 'failed', accounts: { fromAccountId: ACCOUNT_1_ID, toAccountId: ACCOUNT_2_ID } }
    ];
    
    for (let i = 0; i < validCombinations.length; i++) {
      const combination = validCombinations[i];
      const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/transaction_${i}`);
      const transactionData = {
        userId: USER_1_ID,
        type: combination.type,
        status: combination.status,
        amountCents: 1000,
        ...combination.accounts,
        transactionDate: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await assertSucceeds(setDoc(transactionRef, transactionData));
    }
  });

  test('transaction creation handles large amount values', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Large but valid amount (represents $999,999.99)
    const largeAmountTransaction = {
      ...validIncomeTransaction,
      amountCents: ********
    };
    
    await assertSucceeds(setDoc(transactionRef, largeAmountTransaction));
  });

  test('transaction creation handles empty optional fields correctly', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Transaction with empty optional fields
    const emptyOptionalTransaction = {
      userId: USER_1_ID,
      type: 'income',
      status: 'pending',
      amountCents: 1000,
      toAccountId: ACCOUNT_1_ID,
      tagIds: [],
      metadata: {},
      transactionDate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      schemaVersion: 1
    };
    
    await assertSucceeds(setDoc(transactionRef, emptyOptionalTransaction));
  });

  test('transaction creation validates required account fields for each type', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const transactionRef = doc(user1Db, `users/${USER_1_ID}/transactions/${TRANSACTION_ID}`);
    
    // Income without required toAccountId should fail
    const invalidIncome = {
      ...validIncomeTransaction,
      toAccountId: undefined
    };
    delete invalidIncome.toAccountId;
    
    await assertFails(setDoc(transactionRef, invalidIncome));
    
    // Expense without required fromAccountId should fail
    const invalidExpense = {
      ...validExpenseTransaction,
      fromAccountId: undefined
    };
    delete invalidExpense.fromAccountId;
    
    await assertFails(setDoc(transactionRef, invalidExpense));
    
    // Transfer without required fromAccountId should fail
    const invalidTransfer1 = {
      ...validTransferTransaction,
      fromAccountId: undefined
    };
    delete invalidTransfer1.fromAccountId;
    
    await assertFails(setDoc(transactionRef, invalidTransfer1));
    
    // Transfer without required toAccountId should fail
    const invalidTransfer2 = {
      ...validTransferTransaction,
      toAccountId: undefined
    };
    delete invalidTransfer2.toAccountId;
    
    await assertFails(setDoc(transactionRef, invalidTransfer2));
  });
});