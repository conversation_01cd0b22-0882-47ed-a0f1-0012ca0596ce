/**
 * Category Security Rules Tests for BudApp
 * 
 * Comprehensive tests for category collection security rules validation.
 * Tests category creation, updates, deletion, and field validation.
 */

const { initializeTestEnvironment, assertFails, assertSucceeds } = require('@firebase/rules-unit-testing');
const { doc, getDoc, setDoc, updateDoc, deleteDoc, Timestamp } = require('firebase/firestore');

let testEnv;

// Test user IDs
const USER_1_ID = 'user1';
const USER_2_ID = 'user2';
const CATEGORY_ID = 'category1';
const PARENT_CATEGORY_ID = 'parent_category1';

// Sample category data
const validCategoryData = {
  id: CATEGORY_ID,
  userId: USER_1_ID,
  name: 'Test Category',
  type: 'expense',
  parentId: null,
  description: 'Test category description',
  color: '#FF0000',
  icon: 'food',
  isActive: true,
  sortOrder: 0,
  schemaVersion: 1,
  createdAt: new Date(),
  updatedAt: new Date(),
  metadata: {}
};

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId: 'budapp-test',
    firestore: {
      rules: require('fs').readFileSync('../../firestore.rules', 'utf8'),
      host: 'localhost',
      port: 8080,
    },
  });
});

afterAll(async () => {
  await testEnv.cleanup();
});

beforeEach(async () => {
  await testEnv.clearFirestore();
});

describe('Category Access Control', () => {
  test('authenticated user can read their own categories', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    // First create a category
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    await assertSucceeds(setDoc(categoryRef, validCategoryData));
    
    // Then read it
    await assertSucceeds(getDoc(categoryRef));
  });

  test('authenticated user cannot read other users categories', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user2Context = testEnv.authenticatedContext(USER_2_ID);
    const user1Db = user1Context.firestore();
    const user2Db = user2Context.firestore();
    
    // User 1 creates a category
    const categoryRef1 = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    await assertSucceeds(setDoc(categoryRef1, validCategoryData));
    
    // User 2 tries to read User 1's category
    const categoryRef2 = doc(user2Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    await assertFails(getDoc(categoryRef2));
  });

  test('unauthenticated user cannot access categories', async () => {
    const unauthedDb = testEnv.unauthenticatedContext().firestore();
    const categoryRef = doc(unauthedDb, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    await assertFails(getDoc(categoryRef));
    await assertFails(setDoc(categoryRef, validCategoryData));
  });
});

describe('Category Creation Validation', () => {
  test('valid category creation succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    await assertSucceeds(setDoc(categoryRef, validCategoryData));
  });

  test('category creation requires all required fields', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Missing required fields
    const invalidCategoryData = {
      id: CATEGORY_ID,
      userId: USER_1_ID,
      // Missing name, type, schemaVersion, createdAt, updatedAt
    };
    
    await assertFails(setDoc(categoryRef, invalidCategoryData));
  });

  test('category creation validates userId matches path and auth', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Wrong userId
    const invalidCategoryData = {
      ...validCategoryData,
      userId: USER_2_ID
    };
    
    await assertFails(setDoc(categoryRef, invalidCategoryData));
  });

  test('category creation validates name length (2-50 characters)', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Too short name (1 character)
    const invalidCategoryData1 = {
      ...validCategoryData,
      name: 'a'
    };
    
    await assertFails(setDoc(categoryRef, invalidCategoryData1));
    
    // Too long name (51 characters)
    const invalidCategoryData2 = {
      ...validCategoryData,
      name: 'a'.repeat(51)
    };
    
    await assertFails(setDoc(categoryRef, invalidCategoryData2));
    
    // Valid minimum length (2 characters)
    const validCategoryData1 = {
      ...validCategoryData,
      name: 'ab'
    };
    
    await assertSucceeds(setDoc(categoryRef, validCategoryData1));
  });

  test('category creation validates maximum name length (50 characters)', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Valid maximum length (50 characters)
    const validCategoryData50 = {
      ...validCategoryData,
      name: 'a'.repeat(50)
    };
    
    await assertSucceeds(setDoc(categoryRef, validCategoryData50));
  });

  test('category creation validates type enum', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Invalid type
    const invalidCategoryData = {
      ...validCategoryData,
      type: 'invalid_type'
    };
    
    await assertFails(setDoc(categoryRef, invalidCategoryData));
    
    // Valid types
    const validTypes = ['income', 'expense'];
    
    for (let i = 0; i < validTypes.length; i++) {
      const type = validTypes[i];
      const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/category_${i}`);
      const categoryData = {
        ...validCategoryData,
        id: `category_${i}`,
        type: type
      };
      
      await assertSucceeds(setDoc(categoryRef, categoryData));
    }
  });

  test('category creation validates description length (max 200 characters)', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Too long description (201 characters)
    const invalidCategoryData = {
      ...validCategoryData,
      description: 'a'.repeat(201)
    };
    
    await assertFails(setDoc(categoryRef, invalidCategoryData));
    
    // Valid maximum length (200 characters)
    const validCategoryData200 = {
      ...validCategoryData,
      description: 'a'.repeat(200)
    };
    
    await assertSucceeds(setDoc(categoryRef, validCategoryData200));
  });

  test('category creation validates color is valid hex format', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Valid hex colors
    const validColors = ['#FF0000', '#00FF00', '#0000FF', '#FFFFFF', '#000000', '#123', '#ABC'];
    
    for (let i = 0; i < validColors.length; i++) {
      const color = validColors[i];
      const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/category_${i}`);
      const categoryData = {
        ...validCategoryData,
        id: `category_${i}`,
        color: color
      };
      
      await assertSucceeds(setDoc(categoryRef, categoryData));
    }
  });

  test('category creation validates color rejects invalid hex format', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Invalid colors
    const invalidColors = ['FF0000', '#GG0000', '#FF00', '#FF000000', 'red', '', '#'];
    
    for (let i = 0; i < invalidColors.length; i++) {
      const color = invalidColors[i];
      const categoryData = {
        ...validCategoryData,
        color: color
      };
      
      await assertFails(setDoc(categoryRef, categoryData));
    }
  });

  test('category creation validates sortOrder is non-negative integer', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Negative sortOrder
    const invalidCategoryData1 = {
      ...validCategoryData,
      sortOrder: -1
    };
    
    await assertFails(setDoc(categoryRef, invalidCategoryData1));
    
    // Valid zero sortOrder
    const validCategoryData1 = {
      ...validCategoryData,
      sortOrder: 0
    };
    
    await assertSucceeds(setDoc(categoryRef, validCategoryData1));
    
    // Valid positive sortOrder
    const validCategoryData2 = {
      ...validCategoryData,
      sortOrder: 10
    };
    
    await assertSucceeds(setDoc(categoryRef, validCategoryData2));
  });

  test('category creation validates schemaVersion is positive integer', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Invalid schemaVersion
    const invalidCategoryData1 = {
      ...validCategoryData,
      schemaVersion: 0
    };
    
    await assertFails(setDoc(categoryRef, invalidCategoryData1));
    
    // Valid schemaVersion
    const validCategoryData1 = {
      ...validCategoryData,
      schemaVersion: 1
    };
    
    await assertSucceeds(setDoc(categoryRef, validCategoryData1));
  });

  test('category creation validates timestamps', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Invalid createdAt
    const invalidCategoryData1 = {
      ...validCategoryData,
      createdAt: "2024-01-01T00:00:00Z"
    };
    
    await assertFails(setDoc(categoryRef, invalidCategoryData1));
    
    // Invalid updatedAt
    const invalidCategoryData2 = {
      ...validCategoryData,
      updatedAt: "2024-01-01T00:00:00Z"
    };
    
    await assertFails(setDoc(categoryRef, invalidCategoryData2));
  });

  test('category creation validates isActive is boolean', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Invalid isActive
    const invalidCategoryData = {
      ...validCategoryData,
      isActive: "true"
    };
    
    await assertFails(setDoc(categoryRef, invalidCategoryData));
    
    // Valid isActive
    const validCategoryData1 = {
      ...validCategoryData,
      isActive: true
    };
    
    const validCategoryData2 = {
      ...validCategoryData,
      isActive: false
    };
    
    await assertSucceeds(setDoc(categoryRef, validCategoryData1));
    
    // Clear for next test
    await testEnv.clearFirestore();
    await assertSucceeds(setDoc(categoryRef, validCategoryData2));
  });

  test('category creation validates metadata is map', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Invalid metadata
    const invalidCategoryData = {
      ...validCategoryData,
      metadata: "not a map"
    };
    
    await assertFails(setDoc(categoryRef, invalidCategoryData));
    
    // Valid metadata
    const validCategoryData1 = {
      ...validCategoryData,
      metadata: { note: 'Test metadata' }
    };
    
    await assertSucceeds(setDoc(categoryRef, validCategoryData1));
  });
});

describe('Category Update Validation', () => {
  beforeEach(async () => {
    // Create a category for update tests
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    await setDoc(categoryRef, validCategoryData);
  });

  test('valid category update succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    const updatedData = {
      ...validCategoryData,
      name: 'Updated Category Name',
      description: 'Updated description',
      color: '#00FF00',
      sortOrder: 5,
      updatedAt: new Date()
    };
    
    await assertSucceeds(updateDoc(categoryRef, updatedData));
  });

  test('category update prevents id changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    const updatedData = {
      ...validCategoryData,
      id: 'different_id'
    };
    
    await assertFails(updateDoc(categoryRef, updatedData));
  });

  test('category update prevents userId changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    const updatedData = {
      ...validCategoryData,
      userId: USER_2_ID
    };
    
    await assertFails(updateDoc(categoryRef, updatedData));
  });

  test('category update prevents type changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    const updatedData = {
      ...validCategoryData,
      type: 'income'
    };
    
    await assertFails(updateDoc(categoryRef, updatedData));
  });

  test('category update prevents createdAt changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    const updatedData = {
      ...validCategoryData,
      createdAt: new Date()
    };
    
    await assertFails(updateDoc(categoryRef, updatedData));
  });

  test('category update prevents schemaVersion changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    const updatedData = {
      ...validCategoryData,
      schemaVersion: 2
    };
    
    await assertFails(updateDoc(categoryRef, updatedData));
  });

  test('category update validates name length (2-50 characters)', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Too short name
    const invalidCategoryData1 = {
      ...validCategoryData,
      name: 'a'
    };
    
    await assertFails(updateDoc(categoryRef, invalidCategoryData1));
    
    // Too long name
    const invalidCategoryData2 = {
      ...validCategoryData,
      name: 'a'.repeat(51)
    };
    
    await assertFails(updateDoc(categoryRef, invalidCategoryData2));
    
    // Valid name
    const validCategoryData1 = {
      ...validCategoryData,
      name: 'Valid Category Name'
    };
    
    await assertSucceeds(updateDoc(categoryRef, validCategoryData1));
  });

  test('category update validates description length (max 200 characters)', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Too long description
    const invalidCategoryData = {
      ...validCategoryData,
      description: 'a'.repeat(201)
    };
    
    await assertFails(updateDoc(categoryRef, invalidCategoryData));
    
    // Valid description
    const validCategoryData1 = {
      ...validCategoryData,
      description: 'Updated valid description'
    };
    
    await assertSucceeds(updateDoc(categoryRef, validCategoryData1));
  });

  test('category update validates color is valid hex format', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Invalid color
    const invalidCategoryData = {
      ...validCategoryData,
      color: 'invalid'
    };
    
    await assertFails(updateDoc(categoryRef, invalidCategoryData));
    
    // Valid color
    const validCategoryData1 = {
      ...validCategoryData,
      color: '#00FF00'
    };
    
    await assertSucceeds(updateDoc(categoryRef, validCategoryData1));
  });

  test('category update validates sortOrder is non-negative integer', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Negative sortOrder
    const invalidCategoryData = {
      ...validCategoryData,
      sortOrder: -1
    };
    
    await assertFails(updateDoc(categoryRef, invalidCategoryData));
    
    // Valid sortOrder
    const validCategoryData1 = {
      ...validCategoryData,
      sortOrder: 10
    };
    
    await assertSucceeds(updateDoc(categoryRef, validCategoryData1));
  });

  test('category update validates isActive is boolean', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Invalid isActive
    const invalidCategoryData = {
      ...validCategoryData,
      isActive: "false"
    };
    
    await assertFails(updateDoc(categoryRef, invalidCategoryData));
    
    // Valid isActive
    const validCategoryData1 = {
      ...validCategoryData,
      isActive: false
    };
    
    await assertSucceeds(updateDoc(categoryRef, validCategoryData1));
  });

  test('category update validates metadata is map', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Invalid metadata
    const invalidCategoryData = {
      ...validCategoryData,
      metadata: "not a map"
    };
    
    await assertFails(updateDoc(categoryRef, invalidCategoryData));
    
    // Valid metadata
    const validCategoryData1 = {
      ...validCategoryData,
      metadata: { note: 'Updated metadata' }
    };
    
    await assertSucceeds(updateDoc(categoryRef, validCategoryData1));
  });
});

describe('Category Deletion', () => {
  beforeEach(async () => {
    // Create a category for deletion tests
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    await setDoc(categoryRef, validCategoryData);
  });

  test('category owner can delete category', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // First deactivate the category
    const deactivatedData = {
      ...validCategoryData,
      isActive: false
    };
    
    await assertSucceeds(updateDoc(categoryRef, deactivatedData));
    
    // Then delete the inactive category
    await assertSucceeds(deleteDoc(categoryRef));
  });

  test('other users cannot delete category', async () => {
    const user2Context = testEnv.authenticatedContext(USER_2_ID);
    const user2Db = user2Context.firestore();
    const categoryRef = doc(user2Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    await assertFails(deleteDoc(categoryRef));
  });

  test('unauthenticated user cannot delete category', async () => {
    const unauthedDb = testEnv.unauthenticatedContext().firestore();
    const categoryRef = doc(unauthedDb, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    await assertFails(deleteDoc(categoryRef));
  });
});

describe('Category Business Logic', () => {
  test('category creation allows optional fields to be missing', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Category without optional fields
    const minimalCategoryData = {
      id: CATEGORY_ID,
      userId: USER_1_ID,
      name: 'Minimal Category',
      type: 'expense',
      schemaVersion: 1,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await assertSucceeds(setDoc(categoryRef, minimalCategoryData));
  });

  test('category creation validates all data types', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Wrong data types
    const invalidCategoryData = {
      id: 123, // Should be string
      userId: USER_1_ID,
      name: 123, // Should be string
      type: 'expense',
      description: 123, // Should be string
      color: '#FF0000',
      icon: 123, // Should be string
      isActive: "true", // Should be boolean
      sortOrder: "0", // Should be number
      schemaVersion: "1", // Should be number
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: "not a map" // Should be map
    };
    
    await assertFails(setDoc(categoryRef, invalidCategoryData));
  });

  test('category creation validates string fields are not empty', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Empty string fields
    const invalidCategoryData = {
      ...validCategoryData,
      name: ''
    };
    
    await assertFails(setDoc(categoryRef, invalidCategoryData));
  });

  test('category supports hierarchical structure with parentId', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    // Create parent category
    const parentCategoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${PARENT_CATEGORY_ID}`);
    const parentCategoryData = {
      ...validCategoryData,
      id: PARENT_CATEGORY_ID,
      name: 'Parent Category',
      parentId: null
    };
    
    await assertSucceeds(setDoc(parentCategoryRef, parentCategoryData));
    
    // Create child category
    const childCategoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    const childCategoryData = {
      ...validCategoryData,
      id: CATEGORY_ID,
      name: 'Child Category',
      parentId: PARENT_CATEGORY_ID
    };
    
    await assertSucceeds(setDoc(childCategoryRef, childCategoryData));
  });

  test('category creation validates parentId is string when provided', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Invalid parentId type
    const invalidCategoryData = {
      ...validCategoryData,
      parentId: 123
    };
    
    await assertFails(setDoc(categoryRef, invalidCategoryData));
  });

  test('category creation validates icon is string when provided', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // Invalid icon type
    const invalidCategoryData = {
      ...validCategoryData,
      icon: 123
    };
    
    await assertFails(setDoc(categoryRef, invalidCategoryData));
    
    // Valid icon
    const validCategoryData1 = {
      ...validCategoryData,
      icon: 'food'
    };
    
    await assertSucceeds(setDoc(categoryRef, validCategoryData1));
  });

  test('category update validates timestamp fields', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const categoryRef = doc(user1Db, `users/${USER_1_ID}/categories/${CATEGORY_ID}`);
    
    // First create the category
    await setDoc(categoryRef, validCategoryData);
    
    // Invalid updatedAt
    const invalidCategoryData = {
      ...validCategoryData,
      updatedAt: "invalid"
    };
    
    await assertFails(updateDoc(categoryRef, invalidCategoryData));
  });

  test('category supports both income and expense types', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    // Test income category
    const incomeCategoryRef = doc(user1Db, `users/${USER_1_ID}/categories/income_category`);
    const incomeCategoryData = {
      ...validCategoryData,
      id: 'income_category',
      name: 'Income Category',
      type: 'income'
    };
    
    await assertSucceeds(setDoc(incomeCategoryRef, incomeCategoryData));
    
    // Test expense category
    const expenseCategoryRef = doc(user1Db, `users/${USER_1_ID}/categories/expense_category`);
    const expenseCategoryData = {
      ...validCategoryData,
      id: 'expense_category',
      name: 'Expense Category',
      type: 'expense'
    };
    
    await assertSucceeds(setDoc(expenseCategoryRef, expenseCategoryData));
  });
});