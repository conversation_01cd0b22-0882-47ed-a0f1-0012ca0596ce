/**
 * Tag Security Rules Tests for BudApp
 * 
 * Comprehensive tests for tag collection security rules validation.
 * Tests tag creation, updates, deletion, and field validation.
 */

const { initializeTestEnvironment, assertFails, assertSucceeds } = require('@firebase/rules-unit-testing');
const { doc, getDoc, setDoc, updateDoc, deleteDoc, Timestamp } = require('firebase/firestore');

let testEnv;

// Test user IDs
const USER_1_ID = 'user1';
const USER_2_ID = 'user2';
const TAG_ID = 'tag1';

// Sample tag data
const validTagData = {
  id: TAG_ID,
  userId: USER_1_ID,
  name: 'Test Tag',
  color: '#FF0000',
  usageCount: 0,
  schemaVersion: 1,
  createdAt: new Date(),
  updatedAt: new Date(),
  isActive: true
};

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId: 'budapp-test',
    firestore: {
      rules: require('fs').readFileSync('../../firestore.rules', 'utf8'),
      host: 'localhost',
      port: 8080,
    },
  });
});

afterAll(async () => {
  await testEnv.cleanup();
});

beforeEach(async () => {
  await testEnv.clearFirestore();
});

describe('Tag Access Control', () => {
  test('authenticated user can read their own tags', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    // First create a tag
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    await assertSucceeds(setDoc(tagRef, validTagData));
    
    // Then read it
    await assertSucceeds(getDoc(tagRef));
  });

  test('authenticated user cannot read other users tags', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user2Context = testEnv.authenticatedContext(USER_2_ID);
    const user1Db = user1Context.firestore();
    const user2Db = user2Context.firestore();
    
    // User 1 creates a tag
    const tagRef1 = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    await assertSucceeds(setDoc(tagRef1, validTagData));
    
    // User 2 tries to read User 1's tag
    const tagRef2 = doc(user2Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    await assertFails(getDoc(tagRef2));
  });

  test('unauthenticated user cannot access tags', async () => {
    const unauthedDb = testEnv.unauthenticatedContext().firestore();
    const tagRef = doc(unauthedDb, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    await assertFails(getDoc(tagRef));
    await assertFails(setDoc(tagRef, validTagData));
  });
});

describe('Tag Creation Validation', () => {
  test('valid tag creation succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    await assertSucceeds(setDoc(tagRef, validTagData));
  });

  test('tag creation requires all required fields', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Missing required fields
    const invalidTagData = {
      id: TAG_ID,
      userId: USER_1_ID,
      // Missing name, color, createdAt, updatedAt
    };
    
    await assertFails(setDoc(tagRef, invalidTagData));
  });

  test('tag creation validates userId matches path and auth', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Wrong userId
    const invalidTagData = {
      ...validTagData,
      userId: USER_2_ID
    };
    
    await assertFails(setDoc(tagRef, invalidTagData));
  });

  test('tag creation validates name length (1-50 characters)', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Empty name
    const invalidTagData1 = {
      ...validTagData,
      name: ''
    };
    
    await assertFails(setDoc(tagRef, invalidTagData1));
    
    // Too long name (51 characters)
    const invalidTagData2 = {
      ...validTagData,
      name: 'a'.repeat(51)
    };
    
    await assertFails(setDoc(tagRef, invalidTagData2));
    
    // Valid minimum length (1 character)
    const validTagData1 = {
      ...validTagData,
      name: 'a'
    };
    
    await assertSucceeds(setDoc(tagRef, validTagData1));
  });

  test('tag creation validates maximum name length (50 characters)', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Valid maximum length (50 characters)
    const validTagData50 = {
      ...validTagData,
      name: 'a'.repeat(50)
    };
    
    await assertSucceeds(setDoc(tagRef, validTagData50));
  });

  test('tag creation validates color is valid hex format', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Valid hex colors
    const validColors = ['#FF0000', '#00FF00', '#0000FF', '#FFFFFF', '#000000', '#123', '#ABC'];
    
    for (let i = 0; i < validColors.length; i++) {
      const color = validColors[i];
      const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/tag_${i}`);
      const tagData = {
        ...validTagData,
        id: `tag_${i}`,
        color: color
      };
      
      await assertSucceeds(setDoc(tagRef, tagData));
    }
  });

  test('tag creation validates color rejects invalid hex format', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Invalid colors
    const invalidColors = ['FF0000', '#GG0000', '#FF00', '#FF000000', 'red', '', '#'];
    
    for (let i = 0; i < invalidColors.length; i++) {
      const color = invalidColors[i];
      const tagData = {
        ...validTagData,
        color: color
      };
      
      await assertFails(setDoc(tagRef, tagData));
    }
  });

  test('tag creation validates usageCount is non-negative integer', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Negative usageCount
    const invalidTagData1 = {
      ...validTagData,
      usageCount: -1
    };
    
    await assertFails(setDoc(tagRef, invalidTagData1));
    
    // Valid zero usageCount
    const validTagData1 = {
      ...validTagData,
      usageCount: 0
    };
    
    await assertSucceeds(setDoc(tagRef, validTagData1));
    
    // Valid positive usageCount
    const validTagData2 = {
      ...validTagData,
      usageCount: 10
    };
    
    await assertSucceeds(setDoc(tagRef, validTagData2));
  });

  test('tag creation validates schemaVersion is positive integer', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Invalid schemaVersion
    const invalidTagData1 = {
      ...validTagData,
      schemaVersion: 0
    };
    
    await assertFails(setDoc(tagRef, invalidTagData1));
    
    // Valid schemaVersion
    const validTagData1 = {
      ...validTagData,
      schemaVersion: 1
    };
    
    await assertSucceeds(setDoc(tagRef, validTagData1));
  });

  test('tag creation validates timestamps', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Invalid createdAt
    const invalidTagData1 = {
      ...validTagData,
      createdAt: "2024-01-01T00:00:00Z"
    };
    
    await assertFails(setDoc(tagRef, invalidTagData1));
    
    // Invalid updatedAt
    const invalidTagData2 = {
      ...validTagData,
      updatedAt: "2024-01-01T00:00:00Z"
    };
    
    await assertFails(setDoc(tagRef, invalidTagData2));
  });

  test('tag creation validates isActive is boolean', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Invalid isActive
    const invalidTagData = {
      ...validTagData,
      isActive: "true"
    };
    
    await assertFails(setDoc(tagRef, invalidTagData));
    
    // Valid isActive
    const validTagData1 = {
      ...validTagData,
      isActive: true
    };
    
    const validTagData2 = {
      ...validTagData,
      isActive: false
    };
    
    await assertSucceeds(setDoc(tagRef, validTagData1));
    
    // Clear for next test
    await testEnv.clearFirestore();
    await assertSucceeds(setDoc(tagRef, validTagData2));
  });
});

describe('Tag Update Validation', () => {
  beforeEach(async () => {
    // Create a tag for update tests
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    await setDoc(tagRef, validTagData);
  });

  test('valid tag update succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    const updatedData = {
      ...validTagData,
      name: 'Updated Tag Name',
      color: '#00FF00',
      usageCount: 5,
      updatedAt: new Date()
    };
    
    await assertSucceeds(updateDoc(tagRef, updatedData));
  });

  test('tag update prevents id changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    const updatedData = {
      ...validTagData,
      id: 'different_id'
    };
    
    await assertFails(updateDoc(tagRef, updatedData));
  });

  test('tag update prevents userId changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    const updatedData = {
      ...validTagData,
      userId: USER_2_ID
    };
    
    await assertFails(updateDoc(tagRef, updatedData));
  });

  test('tag update prevents createdAt changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    const updatedData = {
      ...validTagData,
      createdAt: new Date()
    };
    
    await assertFails(updateDoc(tagRef, updatedData));
  });

  test('tag update prevents schemaVersion changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    const updatedData = {
      ...validTagData,
      schemaVersion: 2
    };
    
    await assertFails(updateDoc(tagRef, updatedData));
  });

  test('tag update validates name length (1-50 characters)', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Empty name
    const invalidTagData1 = {
      ...validTagData,
      name: ''
    };
    
    await assertFails(updateDoc(tagRef, invalidTagData1));
    
    // Too long name
    const invalidTagData2 = {
      ...validTagData,
      name: 'a'.repeat(51)
    };
    
    await assertFails(updateDoc(tagRef, invalidTagData2));
    
    // Valid name
    const validTagData1 = {
      ...validTagData,
      name: 'Valid Tag Name'
    };
    
    await assertSucceeds(updateDoc(tagRef, validTagData1));
  });

  test('tag update validates color is valid hex format', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Invalid color
    const invalidTagData = {
      ...validTagData,
      color: 'invalid'
    };
    
    await assertFails(updateDoc(tagRef, invalidTagData));
    
    // Valid color
    const validTagData1 = {
      ...validTagData,
      color: '#00FF00'
    };
    
    await assertSucceeds(updateDoc(tagRef, validTagData1));
  });

  test('tag update validates usageCount is non-negative integer', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Negative usageCount
    const invalidTagData = {
      ...validTagData,
      usageCount: -1
    };
    
    await assertFails(updateDoc(tagRef, invalidTagData));
    
    // Valid usageCount
    const validTagData1 = {
      ...validTagData,
      usageCount: 10
    };
    
    await assertSucceeds(updateDoc(tagRef, validTagData1));
  });

  test('tag update validates isActive is boolean', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Invalid isActive
    const invalidTagData = {
      ...validTagData,
      isActive: "false"
    };
    
    await assertFails(updateDoc(tagRef, invalidTagData));
    
    // Valid isActive
    const validTagData1 = {
      ...validTagData,
      isActive: false
    };
    
    await assertSucceeds(updateDoc(tagRef, validTagData1));
  });
});

describe('Tag Deletion', () => {
  beforeEach(async () => {
    // Create a tag for deletion tests
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    await setDoc(tagRef, validTagData);
  });

  test('tag deletion requires tag to be inactive', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Cannot delete active tag
    await assertFails(deleteDoc(tagRef));
    
    // First deactivate the tag
    const deactivatedData = {
      ...validTagData,
      isActive: false
    };
    
    await assertSucceeds(updateDoc(tagRef, deactivatedData));
    
    // Now can delete inactive tag
    await assertSucceeds(deleteDoc(tagRef));
  });

  test('other users cannot delete tag', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const user2Context = testEnv.authenticatedContext(USER_2_ID);
    const user2Db = user2Context.firestore();
    
    // First deactivate the tag as owner
    const tagRef1 = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    const deactivatedData = {
      ...validTagData,
      isActive: false
    };
    await assertSucceeds(updateDoc(tagRef1, deactivatedData));
    
    // User 2 tries to delete User 1's tag
    const tagRef2 = doc(user2Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    await assertFails(deleteDoc(tagRef2));
  });

  test('unauthenticated user cannot delete tag', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const unauthedDb = testEnv.unauthenticatedContext().firestore();
    
    // First deactivate the tag as owner
    const tagRef1 = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    const deactivatedData = {
      ...validTagData,
      isActive: false
    };
    await assertSucceeds(updateDoc(tagRef1, deactivatedData));
    
    // Unauthenticated user tries to delete tag
    const tagRef2 = doc(unauthedDb, `users/${USER_1_ID}/tags/${TAG_ID}`);
    await assertFails(deleteDoc(tagRef2));
  });
});

describe('Tag Business Logic', () => {
  test('tag creation allows optional fields to be missing', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Tag without optional fields
    const minimalTagData = {
      id: TAG_ID,
      userId: USER_1_ID,
      name: 'Minimal Tag',
      color: '#FF0000',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await assertSucceeds(setDoc(tagRef, minimalTagData));
  });

  test('tag creation validates all data types', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Wrong data types
    const invalidTagData = {
      id: 123, // Should be string
      userId: USER_1_ID,
      name: 123, // Should be string
      color: '#FF0000',
      usageCount: "0", // Should be number
      schemaVersion: "1", // Should be number
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: "true" // Should be boolean
    };
    
    await assertFails(setDoc(tagRef, invalidTagData));
  });

  test('tag creation validates string fields are not empty', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // Empty string fields
    const invalidTagData = {
      ...validTagData,
      name: '',
      color: ''
    };
    
    await assertFails(setDoc(tagRef, invalidTagData));
  });

  test('tag update validates timestamp fields', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/${TAG_ID}`);
    
    // First create the tag
    await setDoc(tagRef, validTagData);
    
    // Invalid updatedAt
    const invalidTagData = {
      ...validTagData,
      updatedAt: "invalid"
    };
    
    await assertFails(updateDoc(tagRef, invalidTagData));
  });

  test('tag supports hex color codes of different lengths', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    // Test 3-character hex codes
    const shortHexColors = ['#F00', '#0F0', '#00F', '#FFF', '#000'];
    
    for (let i = 0; i < shortHexColors.length; i++) {
      const color = shortHexColors[i];
      const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/tag_${i}`);
      const tagData = {
        ...validTagData,
        id: `tag_${i}`,
        color: color
      };
      
      await assertSucceeds(setDoc(tagRef, tagData));
    }
    
    // Test 6-character hex codes
    const longHexColors = ['#FF0000', '#00FF00', '#0000FF', '#FFFFFF', '#000000'];
    
    for (let i = 0; i < longHexColors.length; i++) {
      const color = longHexColors[i];
      const tagRef = doc(user1Db, `users/${USER_1_ID}/tags/tag_long_${i}`);
      const tagData = {
        ...validTagData,
        id: `tag_long_${i}`,
        color: color
      };
      
      await assertSucceeds(setDoc(tagRef, tagData));
    }
  });
});