/**
 * Budget Security Rules Tests for BudApp
 * 
 * Comprehensive tests for budget collection security rules validation.
 * Tests budget creation, updates, deletion, and field validation.
 */

const { initializeTestEnvironment, assertFails, assertSucceeds } = require('@firebase/rules-unit-testing');
const { doc, getDoc, setDoc, updateDoc, deleteDoc, serverTimestamp } = require('firebase/firestore');

let testEnv;

// Test user IDs
const USER_1_ID = 'user1';
const USER_2_ID = 'user2';
const BUDGET_ID = 'budget1';

// Sample budget data
const validBudgetData = {
  id: BUDGET_ID,
  userId: USER_1_ID,
  type: 'expense',
  plannedAmountCents: 50000, // $500.00
  currentAmountCents: 15000, // $150.00
  period: 'monthly',
  periodStart: new Date('2024-01-01'),
  categoryId: 'category1',
  parentBudgetId: null,
  isActive: true,
  schemaVersion: 1,
  createdAt: new Date(),
  updatedAt: new Date(),
  metadata: {}
};

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId: 'budapp-test',
    firestore: {
      rules: require('fs').readFileSync('../../firestore.rules', 'utf8'),
      host: 'localhost',
      port: 8080,
    },
  });
});

afterAll(async () => {
  await testEnv.cleanup();
});

beforeEach(async () => {
  await testEnv.clearFirestore();
});

describe('Budget Access Control', () => {
  test('authenticated user can read their own budgets', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    // First create a budget
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    await assertSucceeds(setDoc(budgetRef, validBudgetData));
    
    // Then read it
    await assertSucceeds(getDoc(budgetRef));
  });

  test('authenticated user cannot read other users budgets', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user2Context = testEnv.authenticatedContext(USER_2_ID);
    const user1Db = user1Context.firestore();
    const user2Db = user2Context.firestore();
    
    // User 1 creates a budget
    const budgetRef1 = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    await assertSucceeds(setDoc(budgetRef1, validBudgetData));
    
    // User 2 tries to read User 1's budget
    const budgetRef2 = doc(user2Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    await assertFails(getDoc(budgetRef2));
  });

  test('unauthenticated user cannot access budgets', async () => {
    const unauthedDb = testEnv.unauthenticatedContext().firestore();
    const budgetRef = doc(unauthedDb, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    await assertFails(getDoc(budgetRef));
    await assertFails(setDoc(budgetRef, validBudgetData));
  });
});

describe('Budget Creation Validation', () => {
  test('valid budget creation succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    await assertSucceeds(setDoc(budgetRef, validBudgetData));
  });

  test('budget creation requires all required fields', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    // Missing required fields
    const invalidBudgetData = {
      id: BUDGET_ID,
      userId: USER_1_ID,
      // Missing type, plannedAmountCents, currentAmountCents, period, periodStart, isActive, schemaVersion, createdAt, updatedAt
    };
    
    await assertFails(setDoc(budgetRef, invalidBudgetData));
  });

  test('budget creation validates userId matches path and auth', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    // Wrong userId
    const invalidBudgetData = {
      ...validBudgetData,
      userId: USER_2_ID
    };
    
    await assertFails(setDoc(budgetRef, invalidBudgetData));
  });

  test('budget creation validates type enum', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    // Invalid type
    const invalidBudgetData = {
      ...validBudgetData,
      type: 'invalid_type'
    };
    
    await assertFails(setDoc(budgetRef, invalidBudgetData));
  });

  test('budget creation validates period enum', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    // Invalid period
    const invalidBudgetData = {
      ...validBudgetData,
      period: 'invalid_period'
    };
    
    await assertFails(setDoc(budgetRef, invalidBudgetData));
  });

  test('budget creation validates plannedAmountCents is positive integer', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    // Negative amount
    const invalidBudgetData1 = {
      ...validBudgetData,
      plannedAmountCents: -1000
    };
    
    await assertFails(setDoc(budgetRef, invalidBudgetData1));
    
    // Zero amount
    const invalidBudgetData2 = {
      ...validBudgetData,
      plannedAmountCents: 0
    };
    
    await assertFails(setDoc(budgetRef, invalidBudgetData2));
    
    // String amount
    const invalidBudgetData3 = {
      ...validBudgetData,
      plannedAmountCents: "1000"
    };
    
    await assertFails(setDoc(budgetRef, invalidBudgetData3));
  });

  test('budget creation validates currentAmountCents is non-negative integer', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    // Negative amount
    const invalidBudgetData1 = {
      ...validBudgetData,
      currentAmountCents: -500
    };
    
    await assertFails(setDoc(budgetRef, invalidBudgetData1));
    
    // Valid zero amount
    const validBudgetData2 = {
      ...validBudgetData,
      currentAmountCents: 0
    };
    
    await assertSucceeds(setDoc(budgetRef, validBudgetData2));
  });

  test('budget creation validates periodStart is timestamp', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    // Invalid periodStart
    const invalidBudgetData = {
      ...validBudgetData,
      periodStart: "2024-01-01"
    };
    
    await assertFails(setDoc(budgetRef, invalidBudgetData));
  });

  test('budget creation validates periodStart is not in future', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    // Future periodStart
    const futureDate = new Date();
    futureDate.setFullYear(futureDate.getFullYear() + 1);
    
    const invalidBudgetData = {
      ...validBudgetData,
      periodStart: futureDate
    };
    
    await assertFails(setDoc(budgetRef, invalidBudgetData));
  });

  test('budget creation validates optional fields', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    // Valid with optional fields
    const validBudgetWithOptional = {
      ...validBudgetData,
      categoryId: 'category123',
      parentBudgetId: 'parent_budget_123',
      metadata: { note: 'Test budget' }
    };
    
    await assertSucceeds(setDoc(budgetRef, validBudgetWithOptional));
  });

  test('budget creation validates categoryId is string', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    // Invalid categoryId
    const invalidBudgetData = {
      ...validBudgetData,
      categoryId: 123
    };
    
    await assertFails(setDoc(budgetRef, invalidBudgetData));
  });

  test('budget creation validates parentBudgetId is string', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    // Invalid parentBudgetId
    const invalidBudgetData = {
      ...validBudgetData,
      parentBudgetId: 123
    };
    
    await assertFails(setDoc(budgetRef, invalidBudgetData));
  });

  test('budget creation validates metadata is map', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    // Invalid metadata
    const invalidBudgetData = {
      ...validBudgetData,
      metadata: "not a map"
    };
    
    await assertFails(setDoc(budgetRef, invalidBudgetData));
  });
});

describe('Budget Update Validation', () => {
  beforeEach(async () => {
    // Create a budget for update tests
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    await setDoc(budgetRef, validBudgetData);
  });

  test('valid budget update succeeds', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    const updatedData = {
      ...validBudgetData,
      plannedAmountCents: 60000, // $600.00
      currentAmountCents: 20000, // $200.00
      updatedAt: new Date()
    };
    
    await assertSucceeds(updateDoc(budgetRef, updatedData));
  });

  test('budget update prevents id changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    const updatedData = {
      ...validBudgetData,
      id: 'different_id'
    };
    
    await assertFails(updateDoc(budgetRef, updatedData));
  });

  test('budget update prevents userId changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    const updatedData = {
      ...validBudgetData,
      userId: USER_2_ID
    };
    
    await assertFails(updateDoc(budgetRef, updatedData));
  });

  test('budget update prevents createdAt changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    const updatedData = {
      ...validBudgetData,
      createdAt: new Date()
    };
    
    await assertFails(updateDoc(budgetRef, updatedData));
  });

  test('budget update prevents schemaVersion changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    const updatedData = {
      ...validBudgetData,
      schemaVersion: 2
    };
    
    await assertFails(updateDoc(budgetRef, updatedData));
  });

  test('budget update prevents period changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    const updatedData = {
      ...validBudgetData,
      period: 'yearly'
    };
    
    await assertFails(updateDoc(budgetRef, updatedData));
  });

  test('budget update prevents periodStart changes', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    const updatedData = {
      ...validBudgetData,
      periodStart: new Date('2024-02-01')
    };
    
    await assertFails(updateDoc(budgetRef, updatedData));
  });

  test('budget update validates plannedAmountCents is positive integer', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    const updatedData = {
      ...validBudgetData,
      plannedAmountCents: 0
    };
    
    await assertFails(updateDoc(budgetRef, updatedData));
  });

  test('budget update validates currentAmountCents is non-negative integer', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    const updatedData = {
      ...validBudgetData,
      currentAmountCents: -100
    };
    
    await assertFails(updateDoc(budgetRef, updatedData));
  });
});

describe('Budget Deletion', () => {
  beforeEach(async () => {
    // Create a budget for deletion tests
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    await setDoc(budgetRef, validBudgetData);
  });

  test('budget owner can delete budget', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    await assertSucceeds(deleteDoc(budgetRef));
  });

  test('other users cannot delete budget', async () => {
    const user2Context = testEnv.authenticatedContext(USER_2_ID);
    const user2Db = user2Context.firestore();
    const budgetRef = doc(user2Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    await assertFails(deleteDoc(budgetRef));
  });

  test('unauthenticated user cannot delete budget', async () => {
    const unauthedDb = testEnv.unauthenticatedContext().firestore();
    const budgetRef = doc(unauthedDb, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    await assertFails(deleteDoc(budgetRef));
  });
});

describe('Budget Business Logic', () => {
  test('budget creation allows valid type/period combinations', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    
    // Test all valid combinations
    const validCombinations = [
      { type: 'expense', period: 'monthly' },
      { type: 'expense', period: 'yearly' },
      { type: 'income', period: 'monthly' },
      { type: 'income', period: 'yearly' }
    ];
    
    for (let i = 0; i < validCombinations.length; i++) {
      const combination = validCombinations[i];
      const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/budget_${i}`);
      const budgetData = {
        ...validBudgetData,
        id: `budget_${i}`,
        type: combination.type,
        period: combination.period
      };
      
      await assertSucceeds(setDoc(budgetRef, budgetData));
    }
  });

  test('budget creation validates boolean fields', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    // Invalid isActive
    const invalidBudgetData = {
      ...validBudgetData,
      isActive: "true"
    };
    
    await assertFails(setDoc(budgetRef, invalidBudgetData));
  });

  test('budget creation validates timestamp fields', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    // Invalid createdAt
    const invalidBudgetData = {
      ...validBudgetData,
      createdAt: "2024-01-01T00:00:00Z"
    };
    
    await assertFails(setDoc(budgetRef, invalidBudgetData));
  });

  test('budget creation validates schemaVersion is positive integer', async () => {
    const user1Context = testEnv.authenticatedContext(USER_1_ID);
    const user1Db = user1Context.firestore();
    const budgetRef = doc(user1Db, `users/${USER_1_ID}/budgets/${BUDGET_ID}`);
    
    // Invalid schemaVersion
    const invalidBudgetData = {
      ...validBudgetData,
      schemaVersion: 0
    };
    
    await assertFails(setDoc(budgetRef, invalidBudgetData));
  });
});