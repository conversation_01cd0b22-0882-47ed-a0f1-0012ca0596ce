/**
 * Account Management Firestore Security Rules Tests for BudApp
 * 
 * Tests the account CRUD operations and validation rules.
 * Run with: npm test or firebase emulators:exec --only firestore "npm test"
 */

const { initializeTestEnvironment, assertFails, assertSucceeds } = require('@firebase/rules-unit-testing');
const { doc, getDoc, setDoc, collection, addDoc, deleteDoc, updateDoc } = require('firebase/firestore');

let testEnv;

// Test user IDs
const USER_1_ID = 'user1';
const USER_2_ID = 'user2';

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId: 'budapp-test',
    firestore: {
      rules: require('fs').readFileSync('../../firestore.rules', 'utf8'),
      host: 'localhost',
      port: 8080,
    },
  });
});

afterAll(async () => {
  await testEnv.cleanup();
});

beforeEach(async () => {
  await testEnv.clearFirestore();
});

describe('Account Management Security Rules', () => {
  const validAccount = {
    id: 'account1',
    userId: USER_1_ID,
    name: 'Test Checking Account',
    type: 'checking',
    classification: 'asset',
    initialBalanceCents: 100000, // $1000.00
    currentBalanceCents: 100000, // $1000.00
    schemaVersion: 1,
    isPrimary: true,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    metadata: {}
  };

  test('authenticated user can create their own account', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    
    await assertSucceeds(setDoc(accountDoc, validAccount));
  });

  test('authenticated user can read their own accounts', async () => {
    // First create the account
    const adminDb = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(adminDb, `users/${USER_1_ID}/accounts/account1`);
    await setDoc(accountDoc, validAccount);

    // Then test reading
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const readDoc = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    
    await assertSucceeds(getDoc(readDoc));
  });

  test('authenticated user can update their own account', async () => {
    // First create the account
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    await setDoc(accountDoc, validAccount);

    // Then test updating
    const updatedAccount = {
      ...validAccount,
      name: 'Updated Account Name',
      updatedAt: new Date()
    };
    
    await assertSucceeds(updateDoc(accountDoc, updatedAccount));
  });

  test('authenticated user cannot read other users accounts', async () => {
    // Create account for user1
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    await setDoc(accountDoc, validAccount);

    // Try to read as user2
    const user2Db = testEnv.authenticatedContext(USER_2_ID).firestore();
    const readDoc = doc(user2Db, `users/${USER_1_ID}/accounts/account1`);
    
    await assertFails(getDoc(readDoc));
  });

  test('unauthenticated user cannot access accounts', async () => {
    const unauthDb = testEnv.unauthenticatedContext().firestore();
    const accountDoc = doc(unauthDb, `users/${USER_1_ID}/accounts/account1`);
    
    await assertFails(setDoc(accountDoc, validAccount));
    await assertFails(getDoc(accountDoc));
  });

  test('account creation fails with mismatched userId', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    
    const invalidAccount = {
      ...validAccount,
      userId: USER_2_ID // Wrong user ID
    };
    
    await assertFails(setDoc(accountDoc, invalidAccount));
  });

  test('account creation fails with invalid type/classification pair', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    
    const invalidAccount = {
      ...validAccount,
      type: 'checking',
      classification: 'liability' // Invalid: checking should be asset
    };
    
    await assertFails(setDoc(accountDoc, invalidAccount));
  });

  test('account creation fails with missing required fields', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    
    const invalidAccount = {
      id: 'account1',
      // Missing required userId field
      name: 'Test Account',
      type: 'checking',
      classification: 'asset'
    };
    
    await assertFails(setDoc(accountDoc, invalidAccount));
  });

  test('account deletion requires isActive=false', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    
    // Create active account
    await setDoc(accountDoc, validAccount);
    
    // Try to delete active account (should fail)
    await assertFails(deleteDoc(accountDoc));
    
    // Set isActive to false
    await setDoc(accountDoc, { ...validAccount, isActive: false }, { merge: true });
    
    // Now deletion should succeed
    await assertSucceeds(deleteDoc(accountDoc));
  });

  test('valid credit card account creation succeeds', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/credit1`);
    
    const creditCardAccount = {
      ...validAccount,
      id: 'credit1',
      name: 'Credit Card',
      type: 'creditCard',
      classification: 'liability',
      initialBalanceCents: -50000 // -$500.00 balance
    };
    
    await assertSucceeds(setDoc(accountDoc, creditCardAccount));
  });


  test('account with invalid enum values fails', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/account1`);
    
    const invalidAccount = {
      ...validAccount,
      type: 'invalidType' // Not in allowed enum values
    };
    
    await assertFails(setDoc(accountDoc, invalidAccount));
  });
});

describe('Account Type/Classification Validation', () => {
  const baseAccount = {
    id: 'test-account',
    userId: USER_1_ID,
    name: 'Test Account',
    initialBalanceCents: 0,
    isPrimary: false,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  test('checking account must be asset', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/checking-test`);
    
    const validCheckingAccount = {
      ...baseAccount,
      id: 'checking-test',
      type: 'checking',
      classification: 'asset'
    };
    
    await assertSucceeds(setDoc(accountDoc, validCheckingAccount));
  });

  test('savings account must be asset', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/savings-test`);
    
    const validSavingsAccount = {
      ...baseAccount,
      id: 'savings-test',
      type: 'savings',
      classification: 'asset'
    };
    
    await assertSucceeds(setDoc(accountDoc, validSavingsAccount));
  });

  test('credit card account must be liability', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/credit-test`);
    
    const validCreditAccount = {
      ...baseAccount,
      id: 'credit-test',
      type: 'creditCard',
      classification: 'liability'
    };
    
    await assertSucceeds(setDoc(accountDoc, validCreditAccount));
  });

  test('loan account must be liability', async () => {
    const user1Db = testEnv.authenticatedContext(USER_1_ID).firestore();
    const accountDoc = doc(user1Db, `users/${USER_1_ID}/accounts/loan-test`);
    
    const validLoanAccount = {
      ...baseAccount,
      id: 'loan-test',
      type: 'loan',
      classification: 'liability'
    };
    
    await assertSucceeds(setDoc(accountDoc, validLoanAccount));
  });
});