# Task ID: 22
# Title: User Settings and Preferences
# Status: pending
# Dependencies: 1
# Priority: medium
# Description: Implement various user settings, including currency selection, date/time formats, notification preferences, Dark/Light Mode, and 'Secure Mode'.
# Details:
Create a settings screen UI. Store user preferences directly in the `User` document in Firestore (e.g., `preferredCurrency`, `dateFormat`, `timeFormat`, `darkMode`, `secureMode`, `notificationPreferences`). Implement logic to apply these settings globally across the app, managed client-side via a Repository pattern with Riverpod state management. For currency, ensure amounts are displayed correctly based on the selected currency symbol (without historical conversion for MVP). 'Secure Mode' should mask sensitive financial figures on screen. Firestore Security Rules will be used for data validation and access control.

# Test Strategy:
Change various settings and verify they are applied correctly throughout the app. Test currency display changes. Toggle Dark/Light Mode. Activate 'Secure Mode' and confirm balances are masked. Verify notification preferences are saved and respected.

# Subtasks:
## 1. Design User Settings UI/UX [pending]
### Dependencies: None
### Description: Create wireframes and mockups for the user settings page, including sections for general preferences and a dedicated toggle/options for 'Secure Mode'. Define interaction flows.
### Details:
Focus on intuitive layout, clear labeling, and responsiveness for various screen sizes. Include specific UI elements for 'Secure Mode' activation and related options.

## 2. Define User Preferences Data Schema [pending]
### Dependencies: 22.1
### Description: Design the data structure (e.g., Firestore document structure) for storing user preferences within the `User` document, including boolean flags, string values, and specifically the 'secureMode' setting.
### Details:
Ensure the schema is extensible for future settings. Define data types, default values, and validation rules for each preference, especially for 'secureMode'. Consider how this maps to a sub-collection or a map within the User document in Firestore. Define corresponding validation rules for Firestore Security Rules.

## 3. Implement Preference Persistence Layer [pending]
### Dependencies: 22.2
### Description: Develop client-side Repository methods for saving, loading, and updating user preferences directly with Firestore.
### Details:
Implement CRUD operations for the defined preference schema using direct Firestore interactions. Utilize the Repository pattern for abstracting these interactions. Ensure secure handling of user data via Firestore Security Rules and proper error handling for persistence operations.

## 4. Develop Global Preference State Management [pending]
### Dependencies: 22.3
### Description: Set up a global state management system using Riverpod to make user preferences, including 'Secure Mode', accessible throughout the application.
### Details:
Implement mechanisms to load preferences on app startup via the Repository, update the global state when preferences change, and notify relevant components of state changes.

## 5. Implement 'Secure Mode' Application Logic [pending]
### Dependencies: 22.4
### Description: Develop the specific logic to apply 'Secure Mode' settings across various UI components and application functionalities, such as disabling certain features or altering display.
### Details:
Identify all components affected by 'Secure Mode' (e.g., data masking, restricted actions, visual indicators) and implement conditional rendering/behavior based on the global 'secureMode' state.

## 6. Integrate Settings UI with Preference Management [pending]
### Dependencies: 22.1, 22.3, 22.4
### Description: Connect the designed user settings UI (from subtask 1) with the preference persistence layer (subtask 3) and global state management (subtask 4).
### Details:
Enable users to view their current settings, modify them via the UI, and trigger the save/update operations. Ensure the UI reflects the current global state of preferences.

## 7. Comprehensive Testing and Validation [pending]
### Dependencies: 22.1, 22.2, 22.3, 22.4, 22.5, 22.6
### Description: Perform thorough testing of all user settings and preferences, including persistence, global application, and specific 'Secure Mode' functionality.
### Details:
Conduct unit, integration, and end-to-end tests. Verify that settings persist correctly across sessions in Firestore, 'Secure Mode' applies consistently across the app, and edge cases are handled. Include testing of Firestore Security Rules for preference access and validation.

