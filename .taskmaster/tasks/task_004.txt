# Task ID: 4
# Title: Biometric Authentication (Face ID/Fingerprint)
# Status: done
# Dependencies: 2
# Priority: medium
# Description: Implement biometric authentication (Face ID/Fingerprint) for quick and secure login.
# Details:
Integrate `local_auth` package. Check for biometric availability using `LocalAuthentication().canCheckBiometrics` and `isDeviceSupported`. Implement `authenticate` method to prompt for biometric scan. Upon successful authentication, use `firebase_auth` to sign in the user (e.g., by re-using stored credentials securely or linking to an existing Firebase Auth session). Ensure this is an optional login method, not a primary registration method.

# Test Strategy:
Test biometric login on devices with Face ID/Fingerprint. Verify successful login and secure access. Ensure fallback to traditional login methods if biometrics fail or are not configured.

# Subtasks:
## 1. Check Biometric Hardware and Software Availability [done]
### Dependencies: None
### Description: Utilize `local_auth` to determine if the device supports biometric authentication (e.g., fingerprint, face ID) and if biometrics are enrolled. This includes checking for `canCheckBiometrics` and `getAvailableBiometrics`.
### Details:
Implement a service or utility function that queries the `local_auth` plugin for biometric capability and enrollment status. Handle cases where biometrics are not available or not set up.

## 2. Implement Core Biometric Authentication Flow [done]
### Dependencies: 4.1
### Description: Integrate the `authenticate` method from `local_auth` to prompt the user for biometric verification. Handle success and failure callbacks.
### Details:
Create a function that triggers the biometric prompt. Define success and failure handlers, including user cancellation, lockout, or hardware errors. Ensure appropriate messages are displayed to the user.

## 3. Securely Link Biometric Auth to User Session [done]
### Dependencies: 4.2
### Description: Upon successful biometric authentication, securely associate this event with the existing user session (e.g., refresh token, unlock sensitive data, re-authenticate silently).
### Details:
Design and implement the mechanism to link a successful biometric verification to the application's authentication state. This might involve calling an API endpoint to validate the biometric success, or decrypting local sensitive data. Ensure no sensitive data is directly exposed by the biometric success alone.

## 4. Develop UI for Biometric Feature Management [done]
### Dependencies: None
### Description: Create user interface elements (e.g., toggle switch, settings screen) allowing users to enable or disable biometric authentication for future logins or sensitive actions.
### Details:
Design and implement the UI components for the settings screen where users can manage their biometric authentication preference. This UI should clearly indicate the current status and provide an intuitive way to change it.

## 5. Implement Enable/Disable Logic and Persistence [done]
### Dependencies: 4.3, 4.4
### Description: Connect the UI controls to the application logic for enabling/disabling biometric authentication, including persisting the user's preference locally (e.g., SharedPreferences, secure storage).
### Details:
Implement the logic that responds to user interaction with the biometric settings UI. This involves updating a local flag (e.g., in `SharedPreferences` or `flutter_secure_storage`) and ensuring this preference is respected during subsequent authentication attempts or access to protected features.

## 6. Handle Edge Cases and Provide User Feedback [done]
### Dependencies: 4.1, 4.2, 4.5
### Description: Implement robust error handling, provide clear user feedback for various scenarios (e.g., no biometrics enrolled, too many failed attempts, hardware not available), and guide users on how to resolve issues.
### Details:
Review all possible failure modes and edge cases (e.g., user cancels, device policy prevents, sensor not available, biometric not set up). Display informative messages to the user, guiding them to system settings if necessary, or suggesting alternative authentication methods. Ensure a smooth fallback experience.

