# Task ID: 2
# Title: User Authentication (Email/Password, Google, Apple)
# Status: done
# Dependencies: 1
# Priority: high
# Description: Implement the core user authentication flows including email/password registration and login, Google and Apple OAuth, and secure session management using JWTs.
# Details:
Utilize `firebase_auth` for all authentication methods. For email/password, implement `FirebaseAuth.instance.createUserWithEmailAndPassword` and `signInWithEmailAndPassword`. For Google and Apple sign-in, use `GoogleSignIn` and `SignInWithApple` packages respectively, integrating them with `firebase_auth` credentials. Session management is handled automatically by Firebase Auth, which issues JWTs. Ensure secure storage of tokens using `flutter_secure_storage` for sensitive data. Implement strong password policies and email verification flow (`FirebaseAuth.instance.sendEmailVerification`).

# Test Strategy:
Successfully register and log in with email/password, Google, and Apple accounts. Verify email verification link is sent and account is verified upon click. Test secure session persistence across app restarts. Confirm password reset flow functions correctly.

# Subtasks:
## 1. Firebase Project Setup & Authentication Enablement [done]
### Dependencies: None
### Description: Initialize a new Firebase project, configure the necessary platform-specific settings (e.g., iOS bundle ID, Android package name, web app config), and enable Email/Password, Google, and Apple authentication providers within Firebase Authentication.
### Details:
This includes setting up Firebase SDKs in the project, adding necessary configuration files (GoogleService-Info.plist, google-services.json), and enabling the required sign-in methods in the Firebase console.

## 2. Design & Implement Common Authentication UI/UX [done]
### Dependencies: 2.1
### Description: Create the user interface for login and signup screens, including input fields for email/password, and buttons for Google and Apple sign-in. Focus on intuitive design, clear error messages, and consistent branding.
### Details:
Develop wireframes or mockups for the authentication flow, then implement the UI components using the chosen framework (e.g., React Native, Flutter, Swift UI, Android XML/Compose). Ensure accessibility and responsiveness.

## 3. Implement Email/Password Authentication Logic [done]
### Dependencies: 2.2
### Description: Develop the backend logic for user registration (signup) and login using email and password, integrating with Firebase Authentication APIs. Handle secure password storage and user creation.
### Details:
Implement `createUserWithEmailAndPassword` and `signInWithEmailAndPassword` methods. Include client-side validation for email format and password strength. Store user credentials securely.

## 4. Integrate Email Verification Flow [done]
### Dependencies: 2.3
### Description: Implement the process for sending email verification links to newly registered users and handling the verification status upon user login. Provide UI feedback for unverified accounts.
### Details:
Utilize Firebase's `sendEmailVerification` method. Design a user flow that prompts unverified users to check their email and allows for re-sending verification links. Update UI based on `emailVerified` status.

## 5. Integrate Password Reset Flow [done]
### Dependencies: 2.3
### Description: Develop the functionality for users to request and complete password resets. This includes a 'Forgot Password' UI and handling the Firebase `sendPasswordResetEmail` method.
### Details:
Create a dedicated 'Forgot Password' screen. Implement the call to `sendPasswordResetEmail` and provide clear user feedback on success or failure. Consider custom email templates for the reset link.

## 6. Implement Google Sign-In Integration [done]
### Dependencies: 2.1, 2.2
### Description: Integrate Google Sign-In into the application, allowing users to authenticate using their Google accounts. This involves configuring Google Developer Console, Firebase, and the client-side SDK.
### Details:
Set up OAuth 2.0 client IDs in Google Cloud Console. Implement the client-side Google Sign-In button and handle the authentication token exchange with Firebase using `signInWithCredential`.

## 7. Implement Apple Sign-In Integration [deferred]
### Dependencies: 2.1, 2.2
### Description: Integrate Apple Sign-In for iOS and web platforms, enabling users to authenticate securely with their Apple IDs. This requires Apple Developer account configuration and Firebase integration.
### Details:
Configure Sign in with Apple capabilities in Xcode/Apple Developer portal. Implement the Apple Sign-In button and handle the authorization flow, passing the identity token to Firebase for `signInWithCredential`.

## 8. Handle User Session Management & State [done]
### Dependencies: 2.3, 2.6, 2.7
### Description: Manage the authenticated user's session state across application restarts and ensure proper redirection to authenticated content or login screens based on user status.
### Details:
Utilize Firebase's `onAuthStateChanged` listener to observe user login/logout events. Implement logic to persist user sessions (e.g., using local storage) and automatically sign in returning users. Redirect users to the appropriate app section post-authentication.

## 9. Implement Robust Error Handling & Edge Cases [done]
### Dependencies: 2.3, 2.4, 2.5, 2.6, 2.7, 2.8
### Description: Develop comprehensive error handling for all authentication flows, providing user-friendly messages for common issues (e.g., invalid credentials, network errors, account already exists). Address edge cases like account linking.
### Details:
Catch specific Firebase Auth exceptions (e.g., `auth/wrong-password`, `auth/user-not-found`, `auth/email-already-in-use`). Display clear, actionable error messages to the user. Consider scenarios like linking social accounts to existing email accounts.

