# Task ID: 10
# Title: Transaction Recording (Income, Expense, Transfer)
# Status: done
# Dependencies: 6, 9
# Priority: high
# Description: Implement the core functionality for recording income, expense, and transfer transactions using Flutter and Firestore, including details like amount, date, category, and accounts.
# Details:
Create a Flutter UI for transaction entry. Implement transaction recording using a Repository pattern with Riverpod for state management, directly interacting with Firestore. Transactions will be stored in `users/{userId}/transactions` collection following the `Transaction` schema (id, amount, currency, date, type, description, categoryId, accountId, toAccountId, tagIds, notes, schemaVersion, createdAt, updatedAt). Ensure `amount` is stored in the smallest currency unit (e.g., cents) to avoid floating-point issues. Implement client-side validation. All business logic, including account balance updates for income, expense, and transfers, will be handled client-side using Firestore batch writes for atomicity. Firestore Security Rules will be used for data validation and access control.

# Test Strategy:
Record income, expense, and transfer transactions via the Flutter UI. Verify all fields are saved correctly in the `users/{userId}/transactions` Firestore subcollection. Check that account balances in Firestore are correctly updated client-side after each transaction, including atomic updates for transfers. Test client-side validation and Firestore Security Rule enforcement for various scenarios, including edge cases like zero-amount transactions or invalid dates.

# Subtasks:
## 1. Design Transaction Input Form UI/UX [done]
### Dependencies: None
### Description: Design the overall layout, fields, and user experience for recording new transactions, considering different types (income, expense, transfer) and their specific input requirements.
### Details:
Includes wireframes or mockups for the transaction entry form.

## 2. Define Transaction Database Schema [done]
### Dependencies: None
### Description: Create the detailed database schema for storing transactions, including fields for type, amount, date, description, category, associated accounts (source/destination), and any other relevant attributes.
### Details:
Specify table names, column names, data types, constraints, and relationships (e.g., to accounts, categories).

## 3. Implement UI Components for Income Transaction [done]
### Dependencies: 10.1
### Description: Develop the specific UI components and logic within the main transaction form for recording income transactions, ensuring all necessary fields are present and user-friendly.
### Details:
Focus on fields like amount, date, description, and target account.

## 4. Implement UI Components for Expense Transaction [done]
### Dependencies: 10.1
### Description: Develop the specific UI components and logic within the main transaction form for recording expense transactions, including category selection and other relevant details.
### Details:
Focus on fields like amount, date, description, category, and source account.

## 5. Implement UI Components for Transfer Transaction [done]
### Dependencies: 10.1
### Description: Develop the specific UI components and logic within the main transaction form for recording transfer transactions, including distinct fields for source and destination accounts.
### Details:
Focus on fields like amount, date, description, source account, and destination account.

## 6. Implement Client-Side Validation for Transaction Form [done]
### Dependencies: 10.3, 10.4, 10.5
### Description: Add robust client-side validation rules for all transaction types (income, expense, transfer) to ensure data integrity and provide immediate feedback before submission.
### Details:
Validation rules for required fields, amount format (numeric, positive), date format, valid account selection, and ensuring source/destination accounts differ for transfers.

## 7. Implement Firestore Repository for Transaction Creation [done]
### Dependencies: 10.2, 10.6
### Description: Develop the client-side logic within a Repository pattern to interact directly with Firestore for creating new transactions, handling different transaction types.
### Details:
Define methods within a `TransactionRepository` (or similar) to add income, expense, and transfer transactions to the `users/{userId}/transactions` Firestore subcollection. Utilize Riverpod for state management and dependency injection.

## 8. Implement Client-Side Logic for Income/Expense Account Updates (Firestore) [done]
### Dependencies: 10.7
### Description: Develop the client-side logic to correctly update the balance of the associated account(s) in Firestore when an income or expense transaction is successfully recorded.
### Details:
Logic to fetch the account document, add/subtract the transaction amount, and persist the updated account balance in Firestore. This should be part of the transaction creation flow within the client-side application logic, potentially using Firestore transactions or batch writes for atomicity.

## 9. Implement Client-Side Logic for Transfer Account Updates (Firestore) [done]
### Dependencies: 10.7, 10.8
### Description: Develop the specific client-side logic to correctly update the balances of both the source and destination accounts in Firestore when a transfer transaction is recorded.
### Details:
Logic to subtract the amount from the source account and add it to the destination account. This must be performed atomically using Firestore batch writes to ensure data consistency.

