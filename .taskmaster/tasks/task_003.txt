# Task ID: 3
# Title: User Profile Management and Account Recovery
# Status: done
# Dependencies: 2
# Priority: medium
# Description: Develop the user profile management features for a pure client-side Flutter application, allowing users to update their username, change password, and manage other profile-related settings using Firebase Authentication and Firestore.
# Details:
Implement user profile management using Firebase Authentication client-side methods directly, such as `FirebaseAuth.instance.currentUser.updateDisplayName`, `updatePassword`, and `updateEmail`. Ensure proper re-authentication prompts for sensitive operations like password or email changes using `reauthenticateWithCredential`. Implement a 'Forgot Password' flow using `FirebaseAuth.instance.sendPasswordResetEmail`. For account deletion, implement `FirebaseAuth.instance.currentUser.delete()` and ensure client-side deletion of user's associated data in Firestore collections. All Firebase interactions should be encapsulated within a Repository pattern. Firestore Security Rules will be used for data validation and access control.

# Test Strategy:
Verify user can update display name, change password, and update email via the Flutter UI, confirming changes reflect in Firebase Auth and Firestore. Test 'Forgot Password' functionality end-to-end. Confirm account deletion successfully removes the Firebase Auth user and associated Firestore data (client-side for MVP). Verify Firestore Security Rules prevent unauthorized profile modifications.

# Subtasks:
## 1. Implement User Profile Updates (Name, Email) using Firebase Auth and Firestore [done]
### Dependencies: None
### Description: Implement the logic for updating user display name and email address using `FirebaseAuth.instance.currentUser.updateDisplayName` and direct Firestore document updates. Encapsulate this logic within a dedicated user profile repository.
### Details:
This involves methods in a `UserProfileRepository` to interact with `FirebaseAuth` and `FirebaseFirestore` for profile data. Ensure Firestore Security Rules are in place to validate and protect profile data.

## 2. Implement Password Update with Re-authentication using Firebase Auth [done]
### Dependencies: None
### Description: Implement the secure logic for users to change their password. This must enforce a re-authentication step using `reauthenticateWithCredential` before allowing the password change via `updatePassword`. Encapsulate this logic within the user authentication repository.
### Details:
This involves methods in an `AuthRepository` to handle re-authentication and password updates. Ensure proper error handling for re-authentication failures.

## 3. Implement Password Reset Flow using Firebase Auth [done]
### Dependencies: None
### Description: Implement the full password reset flow, including sending a reset email using `FirebaseAuth.instance.sendPasswordResetEmail`. Encapsulate this logic within the user authentication repository.
### Details:
This involves methods in an `AuthRepository` to trigger the password reset email. The Firebase console handles the token validation and new password setting via its hosted pages.

## 4. Implement Account Deletion with Re-authentication using Firebase Auth and Firestore [done]
### Dependencies: None
### Description: Implement the secure logic for account deletion. This must strictly require a re-authentication step using `reauthenticateWithCredential` before proceeding with `FirebaseAuth.instance.currentUser.delete()`. Also, implement client-side deletion of associated user data from Firestore collections. Encapsulate this logic within the user authentication and profile repositories.
### Details:
This involves methods in `AuthRepository` for Firebase Auth deletion and `UserProfileRepository` for Firestore data cleanup. Ensure Firestore Security Rules are configured to allow the user to delete their own data.

## 5. Implement Client-side UI/UX for Profile Management [done]
### Dependencies: 3.1
### Description: Develop the user interface for updating name and email. Integrate with the respective Firebase-based repository methods (Subtask 1). Ensure clear feedback and error handling.
### Details:
This includes forms for name/email update, integrating with `UserProfileRepository` methods.

## 6. Implement Client-side UI/UX for Password Reset Flow [done]
### Dependencies: 3.3
### Description: Develop the user interface for initiating a password reset (forgot password form). Integrate with the Firebase-based repository method (Subtask 3).
### Details:
This includes a 'Forgot Password' link and an email input field, integrating with `AuthRepository` methods for sending the reset email.

## 7. Implement Client-side UI/UX for Account Deletion with Re-authentication Prompt [done]
### Dependencies: 3.4
### Description: Develop the user interface for account deletion, including a prominent re-authentication prompt (e.g., modal asking for password) before sending the deletion request to the Firebase-based repository method (Subtask 4).
### Details:
This includes a 'Delete Account' button, a confirmation dialog, and a re-authentication step, integrating with `AuthRepository` methods.

## 8. Implement Client-side Data Cleanup and Session Invalidation [done]
### Dependencies: 3.4, 3.7
### Description: Develop client-side logic to clear sensitive user data (e.g., local storage, session storage) and invalidate the user's session upon successful account deletion or after sensitive operations requiring re-authentication. This includes client-side Firestore data cleanup.
### Details:
This ensures no stale user data remains on the client after account deletion and handles Firebase Auth session management (e.g., signing out after deletion). This will involve calling methods from the `UserProfileRepository` for Firestore data cleanup.

