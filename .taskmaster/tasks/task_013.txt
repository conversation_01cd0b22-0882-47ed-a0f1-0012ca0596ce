# Task ID: 13
# Title: Budget Creation and Progress Tracking
# Status: done
# Dependencies: 6, 9, 10
# Priority: high
# Description: Implement the ability to set overall monthly budgets and monthly budgets for specific categories, with visual progress indicators, using a pure client-side Flutter application with direct Firebase (Firestore) integration.
# Details:
Store budgets in `users/{userId}/budgets` collection following the `Budget` schema (id, name, type, amount, currency, period, categoryId, isActive, schemaVersion, createdAt, updatedAt). For MVP, `period` is strictly 'monthly' (calendar month). Implement client-side logic to calculate budget progress by aggregating transactions within the current month for the relevant category or overall, using direct Firestore queries. Display progress using visual indicators (e.g., progress bars) and color-coding (green, orange, red). Ensure budget hierarchy roll-up (sub-category spending counts towards parent budget) and prevent overlapping budgets (sub-category budget if parent has one) primarily through client-side logic and Firestore Security Rules.

# Test Strategy:
Create overall and category-specific budgets. Record transactions that impact budgets and verify progress indicators update correctly. Test budget roll-up for subcategories. Attempt to create overlapping budgets and confirm prevention. Test mid-month budget creation/editing and its impact on calculations, verifying direct Firestore interactions and client-side logic.

# Subtasks:
## 1. Design Database Schema for Budgets and Transactions [done]
### Dependencies: None
### Description: Define the Firestore collections and document structures for budgets, categories, and transactions. Include fields for budget period, amount, type (e.g., income/expense), associated categories, and initial considerations for hierarchical relationships.
### Details:
Schema should support one-to-many relationships between budgets and categories, and transactions linked to categories. Consider fields for 'parent_budget_id' for hierarchy. Include `schemaVersion` field for future migrations.

## 2. Implement Firestore Repository for Budget Management (CRUD) [done]
### Dependencies: 13.1
### Description: Implement a Firestore-based Repository pattern for creating, reading, updating, and deleting budget entries, interacting directly with the defined Firestore collections. Utilize Riverpod for state management.
### Details:
The Repository should handle direct Firestore operations. Client-side validation will be performed before writing to Firestore, and Firestore Security Rules will provide an additional layer of validation and access control.

## 3. Implement Frontend UI for Budget Setup [done]
### Dependencies: 13.2
### Description: Develop user interface components allowing users to create new budgets, specify budget details (amount, period, categories), and edit existing budgets.
### Details:
Include forms for budget name, amount, start/end dates, and multi-select for categories. Provide clear feedback on client-side input validation.

## 4. Develop Client-Side Transaction Aggregation Logic [done]
### Dependencies: 13.1
### Description: Implement client-side logic to filter and aggregate transaction data relevant to specific budgets based on categories, dates, and other criteria.
### Details:
This logic will fetch relevant transactions directly from Firestore and sum their amounts based on the budget's defined scope (categories, time period).

## 5. Implement Client-Side Budget Progress Calculation [done]
### Dependencies: 13.4
### Description: Develop client-side algorithms to calculate budget progress (e.g., percentage spent/earned, remaining amount) by comparing aggregated transactions against budget amounts.
### Details:
Calculations should account for different budget types (e.g., expense budgets tracking spending, income budgets tracking earnings).

## 6. Design and Implement UI for Budget Progress Visualization [done]
### Dependencies: 13.5
### Description: Create and integrate visual indicators (e.g., progress bars, charts, color-coding) to display budget progress and status on the frontend.
### Details:
Visualizations should clearly communicate budget status (e.g., 'on track', 'over budget', 'under budget') and remaining funds/time.

## 7. Implement Client-Side Logic and Firestore Security Rules for Budget Hierarchy Management [done]
### Dependencies: 13.2
### Description: Develop client-side logic to manage parent-child relationships between budgets and define Firestore Security Rules to enforce these relationships, ensuring proper aggregation and display for hierarchical structures.
### Details:
This involves client-side logic to link budgets (e.g., setting `parent_budget_id`) and Firestore Security Rules to enforce hierarchy constraints and ensure proper data access for aggregated views. Client-side calculations will roll up spending from child categories to parent budgets.

## 8. Develop Client-Side Validation and Firestore Security Rules for Budget Overlap Prevention [done]
### Dependencies: 13.2, 13.7
### Description: Implement client-side validation and define Firestore Security Rules to prevent overlapping budget periods or categories for non-hierarchical budgets, ensuring data integrity and accurate reporting.
### Details:
Client-side validation should occur during budget creation/editing. Firestore Security Rules will provide a final layer of enforcement. For example, two distinct 'Groceries' budgets should not cover the same time period unless one is a child of the other.

## 9. Conduct End-to-End Integration and User Acceptance Testing [done]
### Dependencies: 13.3, 13.6, 13.7, 13.8
### Description: Perform comprehensive integration testing of all components (schema, Firestore Repository, UI, client-side logic) and conduct user acceptance testing for budget creation, progress tracking, hierarchy, and overlap prevention features.
### Details:
Test various scenarios including complex hierarchies, overlapping budgets (where allowed), and edge cases for progress calculation, verifying direct Firestore interactions and client-side logic.

## 10. Define and Implement Firestore Security Rules for Budgets [done]
### Dependencies: 13.1
### Description: Write and test comprehensive Firestore Security Rules to enforce data validation, access control (read/write permissions based on user ID), and support budget hierarchy and overlap prevention logic.
### Details:
Rules should ensure users can only access their own budget data, validate budget fields (e.g., amount > 0, valid period), and prevent creation of invalid hierarchical or overlapping budgets where applicable.

