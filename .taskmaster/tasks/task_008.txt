# Task ID: 8
# Title: Firebase Remote Config Integration for Categories and Limits
# Status: done
# Dependencies: 1, 5
# Priority: high
# Description: Integrate Firebase Remote Config to deliver predefined income and expense categories and manage premium feature limits.
# Details:
Configure `firebase_remote_config` to fetch predefined categories (e.g., `predefined_income_categories`, `predefined_expense_categories`) and premium limits (`max_accounts_free`, `max_accounts_premium`, `max_custom_categories_free`). Define these parameters in the Firebase Remote Config console and `firebase/remoteconfig/template.json`. Implement a fetch policy (e.g., fetch on app startup with 12-hour cache expiration) and ensure hardcoded defaults are used as fallbacks. Use `firebase_remote_config.RemoteConfig.instance.activate()` to apply fetched values.

# Test Strategy:
Verify predefined categories are loaded and displayed correctly from Remote Config. Test app behavior when Remote Config values are changed (e.g., update a category name, change a limit). Verify fallback to defaults when offline or fetch fails. Test premium limits are enforced based on Remote Config values.

# Subtasks:
## 1. Set Up Firebase Project and Enable Remote Config [done]
### Dependencies: None
### Description: Create or select an existing Firebase project in the Firebase console and enable the Remote Config feature for the project.
### Details:
This involves navigating to the Firebase console, selecting the project, and then finding the 'Remote Config' section to ensure it's active and ready for parameter definition.

## 2. Define Remote Config Parameters in Firebase Console [done]
### Dependencies: 8.1
### Description: Add the 'categories' and 'premium_limits' parameters to the Remote Config template in the Firebase console, specifying their data types (e.g., JSON for categories, number for premium_limits) and initial default values.
### Details:
For 'categories', define it as a JSON string (e.g., '["Sports", "News", "Tech"]'). For 'premium_limits', define it as a number (e.g., '5').

## 3. Integrate Firebase SDK and Initialize Remote Config Client [done]
### Dependencies: None
### Description: Add the Firebase SDK to the client application (iOS, Android, Web, etc.) and initialize the Firebase Remote Config client instance within the app's startup code.
### Details:
Follow the platform-specific instructions for adding Firebase to your project and ensure the Remote Config module is included and initialized correctly.

## 4. Set In-App Default Parameter Values [done]
### Dependencies: 8.3
### Description: Define a set of in-app default values for 'categories' and 'premium_limits' within the client application. These values will be used if the app cannot fetch values from the Firebase backend.
### Details:
Use `setDefaultsAsync()` or equivalent method to provide fallback values. For example, `{'categories': '["Default1", "Default2"]', 'premium_limits': 3}`.

## 5. Implement Remote Config Fetch and Activate Logic [done]
### Dependencies: 8.3, 8.4
### Description: Write code to fetch the latest parameter values from the Firebase Remote Config backend and then activate them, making them available for the app to use.
### Details:
Implement a fetch strategy (e.g., on app startup, periodically). Use `fetchAndActivate()` or separate `fetch()` and `activate()` calls. Consider fetch throttles and caching.

## 6. Retrieve and Apply 'categories' Parameter [done]
### Dependencies: 8.5
### Description: After activation, retrieve the 'categories' parameter value from Remote Config and use it to dynamically populate UI elements or control application logic related to content categories.
### Details:
Access the parameter using `getString('categories')` and parse the JSON string into a list or array of categories. Update relevant UI components (e.g., navigation menus, filter options).

## 7. Retrieve and Apply 'premium_limits' Parameter [done]
### Dependencies: 8.5
### Description: After activation, retrieve the 'premium_limits' parameter value from Remote Config and use it to enforce usage limits for premium features or content.
### Details:
Access the parameter using `getLong('premium_limits')` or `getDouble('premium_limits')` and apply it to the logic that governs user access to premium features (e.g., number of articles, daily usage).

## 8. Implement Remote Config Error Handling and Fallback [done]
### Dependencies: 8.5, 8.6, 8.7
### Description: Add robust error handling for fetch operations and ensure that the application gracefully falls back to in-app default values or previously fetched values if Remote Config fetching fails.
### Details:
Include try-catch blocks or error callbacks for fetch operations. Verify that the app behaves correctly when network is unavailable or fetch fails, relying on the in-app defaults or cached values.

