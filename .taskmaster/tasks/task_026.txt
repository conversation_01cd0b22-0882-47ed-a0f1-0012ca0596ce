# Task ID: 26
# Title: Deletion Constraints and Guided Re-assignment
# Status: pending
# Dependencies: 6, 9, 10, 11, 14, 15
# Priority: high
# Description: Implement robust deletion constraints and user guidance for accounts, categories, and goals with related transactions, entirely client-side using Firebase services, including a guided re-assignment flow.
# Details:
Enhance deletion logic for `Account`, `Category`, and `Goal` models within a pure client-side Flutter application. Before allowing deletion, perform client-side checks using Firestore queries to see if there are any associated `transactions` or `subcategories`. If dependencies exist, display a clear modal explaining the blockage, showing the number of related items. Provide options: 'Re-assign Transactions' (primary action), 'View Transactions First', or 'Cancel'. For re-assignment, implement a UI to select a new destination (category/account) and perform a bulk update of affected transactions using Firestore batch operations via the Repository pattern. Confirm deletion with clear warnings. Leverage Riverpod for state management and ensure Firestore Security Rules provide additional validation.

# Test Strategy:
Attempt to delete accounts, categories, and goals that have associated transactions. Verify the system prevents deletion and displays informative messages based on client-side Firestore queries. Test the guided re-assignment flow for categories and accounts, ensuring all linked transactions are correctly updated via Firestore batch operations and the original item can then be deleted. Confirm clear warning messages for all deletions. Verify that Firestore Security Rules correctly enforce deletion constraints and re-assignment operations.

# Subtasks:
## 1. Define Data Models and Relationships for Deletion Constraints [pending]
### Dependencies: None
### Description: Analyze existing data models for Accounts, Categories, and Goals to identify all direct and indirect dependencies with transactions and other entities. This forms the basis for client-side checks and Firestore updates.
### Details:
Identify foreign keys, relationships, and potential cascading effects for Accounts, Categories, and Goals relative to Transactions and other related entities within the Firestore data structure.

## 2. Implement Client-Side Dependency Check Logic [pending]
### Dependencies: 26.1
### Description: Develop Flutter/Dart functions to perform real-time checks using Firestore queries for associated transactions or other linked entities when a user attempts to delete an Account, Category, or Goal.
### Details:
This logic should identify *if* dependencies exist and count them, running before any warning modal is displayed, utilizing the Repository pattern for Firestore access.

## 3. Design and Implement Deletion Warning Modal UI [pending]
### Dependencies: 26.2
### Description: Create a user interface for a modal dialog that appears when dependencies are found. This modal should clearly state the implications of deletion and offer options for re-assignment or forced deletion.
### Details:
Include dynamic text based on the type of entity being deleted (Account, Category, Goal) and the number of associated items. Provide clear 'Re-assign' and 'Delete Anyway' options.

## 4. Implement Guided Re-assignment Selection Logic [pending]
### Dependencies: 26.3
### Description: Develop the client-side logic within the warning modal to allow users to select a new Account, Category, or Goal to re-assign associated transactions to.
### Details:
This includes dropdowns or search fields to pick the new entity, and client-side validation to ensure a valid selection is made before proceeding, leveraging Riverpod for state management.

## 5. Develop Client-Side Firestore Logic for Bulk Transaction Re-assignment [pending]
### Dependencies: 26.1
### Description: Implement the client-side logic using Firestore batch operations to update all associated transactions when a user chooses to re-assign them from an old entity (account, category, or goal) to a new one.
### Details:
This logic should be implemented via the Repository pattern, ensuring atomicity of the update using Firestore transactions/batches. It must handle different entity types (account, category, goal) and their respective transaction relationships.

## 6. Integrate Client-Side Re-assignment with Firestore Batch Operations [pending]
### Dependencies: 26.4, 26.5
### Description: Connect the client-side re-assignment logic from the warning modal to the Firestore batch operations for bulk updates.
### Details:
When the user confirms re-assignment, the client should execute the necessary Firestore batch operations via the Repository pattern, managing state with Riverpod. Handle success and error responses from Firestore.

## 7. Implement Forced Deletion Logic (No Re-assignment) [pending]
### Dependencies: 26.3
### Description: Develop the client-side logic for scenarios where a user chooses to force delete an entity, which should also delete all associated transactions.
### Details:
This option should be clearly marked as destructive. The client-side deletion logic must use Firestore batch operations for cascading deletes or explicit deletion of associated transactions, ensuring compliance with Firestore Security Rules.

## 8. Implement UI Feedback and Error Handling [pending]
### Dependencies: 26.6, 26.7
### Description: Add visual feedback (e.g., loading spinners, success messages, error alerts) for the user during the re-assignment or deletion process.
### Details:
Ensure clear communication to the user about the status of their action, especially for bulk Firestore operations and potential errors returned by Firestore or Security Rules.

## 9. Comprehensive Testing and Edge Case Validation [pending]
### Dependencies: 26.8
### Description: Perform thorough testing of all deletion and re-assignment flows, including edge cases like no dependencies, many dependencies, invalid re-assignment targets, and network errors.
### Details:
Write unit and integration tests for client-side logic, Firestore operations, and Riverpod state management. Test with various data volumes and user interaction sequences, specifically validating against Firestore Security Rules.

