# Task ID: 11
# Title: Transaction Editing and Deletion
# Status: done
# Dependencies: 10
# Priority: high
# Description: Implement the ability to edit and delete existing transactions, ensuring account balances are correctly adjusted.
# Details:
Provide a simple 'Edit' flow where users can modify any field of a transaction. When editing, retrieve the transaction data, pre-fill the form, and update the document in Firestore. For deletion, use `doc.delete()`. All operations will be handled entirely client-side within the Flutter application, utilizing a Repository pattern and Riverpod for state management. Ensure that editing/deleting a transaction correctly reverses or adjusts its impact on the associated account balances. This will involve client-side calculation and adjustment of account balances using Firestore batch operations to maintain atomicity. Firestore Security Rules will be used for data validation and access control.

# Test Strategy:
Edit various fields of different transaction types and verify changes are reflected and account balances are correct. Delete transactions and confirm account balances are accurately adjusted. Test concurrent edits/deletions if possible (Firestore's LWW handles this).

# Subtasks:
## 1. Implement Transaction Action UI (Edit/Delete Buttons) [done]
### Dependencies: None
### Description: Add user interface elements (e.g., buttons, context menu items) to transaction listings that allow users to initiate edit or delete actions for a specific transaction. This includes visual cues for selected transactions.
### Details:
Design and implement the UI components for edit and delete actions within the transaction list view. Consider mobile-friendly interactions like swipe actions or long-press menus.

## 2. Develop Transaction Edit Form/Modal [done]
### Dependencies: 11.1
### Description: Create a reusable form or modal component that is pre-populated with existing transaction data when an edit action is triggered. This form should allow modification of all relevant transaction details (amount, category, date, description, associated account, etc.).
### Details:
Ensure the form handles various input types (number for amount, date picker, dropdowns for categories/accounts). Implement validation for input fields.

## 3. Implement Firestore Transaction Update Operation [done]
### Dependencies: 11.2
### Description: Implement the client-side logic to update an existing transaction document in Firestore based on the data submitted from the edit form. This operation, including associated account balance adjustments, must be atomic using Firestore batch operations.
### Details:
Implement this logic within a Repository pattern, leveraging Riverpod for state management. Use Firestore's `update` method as part of a batch operation that also updates the associated account balance. Ensure all related document changes (transaction, account balance) are committed atomically.

## 4. Implement Firestore Transaction Deletion Operation [done]
### Dependencies: 11.1
### Description: Implement the client-side logic to delete a specific transaction document from Firestore when the delete action is confirmed by the user. This operation, including associated account balance adjustments, must be atomic using Firestore batch operations.
### Details:
Implement this logic within a Repository pattern, leveraging Riverpod for state management. Use Firestore's `delete` method as part of a batch operation that also updates the associated account balance. Implement a confirmation dialog before permanent deletion.

## 5. Develop Account Balance Adjustment Logic for Transaction Edits [done]
### Dependencies: 11.3
### Description: Develop the client-side Account Balance Adjustment Logic for Transaction Edits. Implement the critical logic to accurately adjust the associated account's balance when a transaction is edited. This involves calculating the difference between the old and new transaction amounts and applying this delta to the account balance, ensuring atomicity via Firestore batch operations and handling potential changes in the associated account.
### Details:
This logic will be part of the client-side Repository. Retrieve the old transaction amount and the new amount. Calculate `new_amount - old_amount`. Apply this delta to the account balance. If the associated account changes, reverse the old amount from the old account and apply the new amount to the new account. All these updates must be performed within a single Firestore batch operation to guarantee atomicity. Leverage Riverpod for managing transaction and account data.

## 6. Develop Account Balance Adjustment Logic for Transaction Deletions [done]
### Dependencies: 11.4
### Description: Develop the client-side Account Balance Adjustment Logic for Transaction Deletions. Implement the critical logic to accurately adjust the associated account's balance when a transaction is deleted. This involves reversing the original transaction's impact on the account balance, ensuring atomicity via Firestore batch operations.
### Details:
This logic will be part of the client-side Repository. When a transaction is deleted, its original amount (and type, e.g., income/expense) needs to be reversed from the associated account's balance. For example, if an expense of $50 was deleted, add $50 back to the account balance. This update must be performed within a single Firestore batch operation to guarantee atomicity. Leverage Riverpod for managing transaction and account data.

## 7. Integrate UI with Client-side Logic & User Feedback [done]
### Dependencies: 11.1, 11.3, 11.4, 11.5, 11.6
### Description: Connect the UI actions (edit/delete buttons, form submission) to their respective Firestore operations and client-side balance adjustment logic. Provide appropriate user feedback (e.g., loading states, success/error messages, toast notifications) for all operations.
### Details:
Implement error handling and display user-friendly messages. Ensure the UI reflects the updated data immediately after successful operations. Handle cases where network issues or Firestore errors occur. This integration will utilize Riverpod for state management and data flow.

