# Task ID: 21
# Title: Subscription Management (RevenueCat Integration)
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Integrate RevenueCat for managing free and premium subscription tiers, including in-app purchase flows.
# Details:
Integrate `purchases_flutter` (RevenueCat SDK) for a purely client-side subscription management. Define product entitlements and offerings in RevenueCat dashboard. Implement logic to check user's subscription status and unlock premium features based on entitlements, entirely handled client-side through RevenueCat's SDK methods. For MVP (Phase 1), define free tier limits (e.g., `max_accounts_free`, `max_custom_categories_free` from Remote Config) and implement logic to prompt for upgrade when limits are hit. Implement the secure in-app purchase flow using RevenueCat's APIs. Server-side webhook listeners are explicitly out of scope for Phase 1.

# Test Strategy:
Test free tier limits (e.g., attempt to create more than 2 accounts). Verify upgrade prompts appear. Simulate successful premium subscription purchase and confirm premium features are unlocked (e.g., increased account limit). Test subscription restoration.

# Subtasks:
## 1. Integrate RevenueCat SDK into Client Application [pending]
### Dependencies: None
### Description: Add the RevenueCat SDK to the client-side project (e.g., iOS, Android, Web) and configure necessary build settings and permissions.
### Details:
This involves adding the SDK via package manager (CocoaPods, Gradle, npm, etc.) and ensuring the project compiles successfully with the new dependency.

## 2. Configure Products in RevenueCat Dashboard [pending]
### Dependencies: None
### Description: Define and link in-app purchase products (subscriptions, one-time purchases) from app stores (Apple App Store Connect, Google Play Console) to RevenueCat.
### Details:
Create corresponding products in RevenueCat, linking them to their respective store IDs and setting up product types (e.g., monthly, annual subscriptions).

## 3. Configure Offerings in RevenueCat Dashboard [pending]
### Dependencies: 21.2
### Description: Group configured products into logical offerings within the RevenueCat dashboard for presentation to users.
### Details:
Create one or more offerings (e.g., 'Premium', 'Pro') and assign the relevant products to them, defining the order and presentation.

## 4. Implement Client-Side RevenueCat SDK Initialization [pending]
### Dependencies: 21.1
### Description: Initialize the RevenueCat SDK in the client application upon launch with the appropriate API key.
### Details:
Ensure the SDK is initialized early in the application lifecycle, typically in the `AppDelegate` or equivalent, passing the public API key.

## 5. Implement Fetching Available Offerings [pending]
### Dependencies: 21.3, 21.4
### Description: Develop client-side logic to fetch the currently configured offerings from RevenueCat to display to the user.
### Details:
Use the RevenueCat SDK's `getOfferings` method to retrieve the available subscription and purchase options for display on the paywall or upgrade screen.

## 6. Implement In-App Purchase Flow [pending]
### Dependencies: 21.5
### Description: Develop the client-side user interface and logic for initiating and completing an in-app purchase using RevenueCat.
### Details:
Handle user selection of an offering, call the RevenueCat `purchasePackage` method, and manage the success or failure of the purchase transaction.

## 7. Implement Client-Side Entitlement Checking [pending]
### Dependencies: 21.4, 21.6
### Description: Develop logic to check a user's current entitlements (active subscriptions, purchased features) using the RevenueCat SDK.
### Details:
Utilize `Purchases.shared.getCustomerInfo` to retrieve the user's current subscription status and entitlements, updating the UI accordingly.

## 8. Handle Free Tier Limits and Upgrade Prompts [pending]
### Dependencies: 21.7
### Description: Implement application logic to enforce free tier usage limits and display upgrade prompts based on entitlement checks.
### Details:
Monitor user actions against defined free tier limits. When limits are reached, use the entitlement status to determine if an upgrade prompt should be displayed, guiding the user to the purchase flow.

