# Task ID: 25
# Title: CSV Transaction Import
# Status: pending
# Dependencies: 6, 9, 10
# Priority: medium
# Description: Implement CSV import functionality for transaction history entirely client-side within the Flutter app, leveraging Firebase services directly. This includes client-side CSV parsing, column mapping, validation, and efficient bulk insertion into Firestore.
# Details:
Create a UI for CSV import. Use the `flutter_csv` package to parse the CSV file. Implement column detection and manual mapping (Date, Amount, Description, Category). Support multiple date formats and handle positive/negative amounts. Implement robust client-side validation (file size, row-level data integrity) and provide clear error messages with row numbers. Allow partial imports. Preview parsed data before final import. Transactions should be imported into a user-selected account. Ensure categories are matched or created. All business logic and data processing will occur client-side, with Firestore Security Rules providing final data integrity checks.

# Test Strategy:
Import various CSV files (valid, invalid data, large files). Test column mapping. Verify client-side error messages for malformed data. Confirm successful client-side import of valid transactions and correct handling of invalid rows. Check that imported transactions correctly update account balances and categories are handled as expected via direct Firestore operations. Verify Firestore Security Rules prevent invalid data from being written.

# Subtasks:
## 1. Implement File Upload UI & Initial CSV Parsing [pending]
### Dependencies: None
### Description: Develop a user interface component for selecting and uploading a CSV file. Perform initial client-side parsing using the `flutter_csv` package to extract column headers and a sample of rows for display.
### Details:
This subtask focuses on the initial user interaction and basic file processing to prepare for column mapping.

## 2. Develop Dynamic Column Mapping UI [pending]
### Dependencies: 25.1
### Description: Create an interactive UI that allows users to map detected CSV columns to predefined internal transaction fields (e.g., 'Date', 'Amount', 'Description', 'Category'). Include options for ignoring columns and previewing mapped data.
### Details:
This is a critical UI component for handling the variability of user CSV files.

## 3. Implement Client-Side Schema & Type Validation [pending]
### Dependencies: 25.2
### Description: Add client-side validation logic to ensure that mapped columns conform to expected data types (e.g., 'Date' column contains valid date formats, 'Amount' is a number). Provide immediate visual feedback on errors.
### Details:
Early validation reduces server load and improves user experience.

## 4. Implement Client-Side Data Content Validation [pending]
### Dependencies: 25.3
### Description: Develop more granular client-side validation rules based on business logic (e.g., 'Amount' must be positive, 'Category' must be from a predefined list, 'Date' within a reasonable range).
### Details:
This ensures data quality before submission, catching common user errors.

## 5. Design & Implement Interactive Error Display and Correction UI [pending]
### Dependencies: 25.4
### Description: Create a user interface to clearly display all client-side validation errors, highlighting problematic rows or cells. Allow users to directly edit or correct data within the UI before proceeding with the import.
### Details:
Crucial for robust error handling and user experience, enabling self-correction.

## 6. Develop Client-Side Data Transformation & Preparation [pending]
### Dependencies: 25.5
### Description: Implement logic to transform the validated and mapped CSV data into the final structured JSON format required for direct Firestore insertion. This includes type conversions, default value assignments, and formatting.
### Details:
Prepares the data for efficient Firestore batch writes.

## 7. Implement Client-Side Firestore Data Preparation and Batching [pending]
### Dependencies: 25.6
### Description: Develop client-side logic to prepare the transformed transaction data for efficient bulk insertion into Firestore using batched writes. This involves structuring data according to Firestore document models and preparing the batch operations.
### Details:
This is the core client-side component for initiating the bulk import process directly to Firestore.

## 8. Implement Client-Side Business Logic & Firestore Security Rules Definition [pending]
### Dependencies: 25.7
### Description: Move and implement all necessary business logic (e.g., duplicate transaction checks, category matching, account balance updates) client-side. Additionally, define and implement robust Firestore Security Rules to enforce critical data integrity and security policies, acting as a final gatekeeper for data written to Firestore.
### Details:
Essential for data integrity and security in a client-only architecture, leveraging Firestore's built-in security features.

## 9. Implement Efficient Firestore Bulk Insertion [pending]
### Dependencies: 25.8
### Description: Develop a strategy for efficiently inserting a large number of transactions into Firestore directly from the client using batched writes. Optimize performance and avoid rate limits by leveraging Firestore's batch write capabilities.
### Details:
Focuses on the performance and scalability of writing data to Firestore from the client.

## 10. Develop Client-Side Error Handling & Import Reporting [pending]
### Dependencies: 25.9
### Description: Implement robust client-side error handling for data processing and Firestore write operations during the import. Generate detailed import reports to display to the user, indicating success, partial success, or failure with specific reasons for each transaction, based on client-side validation and Firestore write results.
### Details:
Provides critical feedback to the user about the import outcome and any issues encountered during client-side processing and Firestore interaction.

