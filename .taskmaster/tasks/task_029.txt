# Task ID: 29
# Title: Architectural Refactoring to Riverpod and Repository Pattern
# Status: done
# Dependencies: 1, 2
# Priority: high
# Description: Refactor BudApp's architecture from a static service pattern to a modern Riverpod state management with a repository pattern, including migration of UI components and service dependencies.
# Details:
Implement a comprehensive architectural refactoring of the BudApp codebase. This involves:

1.  **Riverpod Infrastructure Setup:** Integrate the Riverpod state management library. This includes setting up `ProviderScope` at the root of the application, defining various types of providers (e.g., `StateProvider`, `ChangeNotifierProvider`, `FutureProvider`, `StreamProvider`) for managing application state, and establishing a clear provider organization strategy.
2.  **Repository Layer Implementation:** Design and implement an abstract repository layer that defines interfaces for data access operations (e.g., `UserRepository`, `TransactionRepository`, `AccountRepository`). Create concrete implementations of these repositories that interact with Firestore and potentially other data sources. This layer will abstract away the underlying data storage mechanisms.
3.  **UI Component Migration:** Systematically migrate existing `StatelessWidget` and `StatefulWidget` UI components to `ConsumerWidget` or `ConsumerStatefulWidget` as appropriate. Update UI logic to utilize `ref.watch` and `ref.read` for accessing and reacting to state changes from Riverpod providers.
4.  **Static Service Transformation:** Identify and refactor existing static service classes (e.g., `AuthService`, `FirestoreService`) into injectable dependencies managed by Riverpod. This will involve converting static methods into instance methods and providing them through Riverpod providers, enabling easier testing and dependency inversion.
5.  **Enhanced Error Handling:** Integrate robust error handling mechanisms throughout the new architecture, particularly within the repository layer and providers. Utilize Riverpod's `AsyncValue.error` to propagate errors effectively and implement consistent error presentation in the UI.
6.  **Testing Infrastructure Creation:** Establish a comprehensive testing strategy for the new Riverpod and repository architecture. This includes writing unit tests for individual providers and repository methods using `ProviderContainer` for isolated testing, and integration tests to verify the data flow and UI reactions within the new state management system.

Refer to `docs/refactor/001-refactor_summary.md` for full architectural details, design principles, and specific migration guidelines.

# Test Strategy:
1.  **Unit Tests:** Write comprehensive unit tests for all new Riverpod providers (e.g., `StateProvider`, `FutureProvider`, `StreamProvider`) to ensure they manage state correctly and emit expected values. Test all methods within the new repository implementations to verify correct data access, manipulation, and error handling.
2.  **Integration Tests:** Develop integration tests to verify the end-to-end data flow through the new architecture. This includes testing UI components' interaction with Riverpod providers and the repository layer, ensuring that UI updates correctly reflect state changes and data operations.
3.  **Regression Testing:** Conduct thorough regression testing of existing features to ensure that the architectural refactoring has not introduced any regressions or broken existing functionality. Pay special attention to core features like authentication, transaction management, and account balances.
4.  **Performance Testing:** Monitor application performance (e.g., UI responsiveness, data loading times) before and after the refactoring to ensure that the new architecture does not negatively impact performance. Optimize where necessary.
5.  **Error Scenario Testing:** Systematically test various error scenarios (e.g., network issues, invalid data, permission errors) to ensure that the enhanced error handling mechanisms correctly catch, propagate, and display errors to the user.

# Subtasks:
## 1. Initialize Riverpod `ProviderScope` and Core Providers [done]
### Dependencies: None
### Description: Set up `ProviderScope` at the root of the application. Define initial core providers like `StateProvider` for simple UI state (e.g., loading indicators) and `Provider` for constants or simple dependencies. Establish a basic provider organization (e.g., `lib/providers` directory).
### Details:
Add `flutter_riverpod` to `pubspec.yaml`. Wrap `MaterialApp` or `CupertinoApp` with `ProviderScope`. Create `lib/providers/core_providers.dart` for initial definitions. Ensure the application still compiles and runs without functional changes.

## 2. Define Abstract Repository Interfaces [done]
### Dependencies: 29.1
### Description: Create abstract classes or interfaces for core data access operations, such as `IUserRepository`, `ITransactionRepository`, and `IAccountRepository`. These interfaces will define the contract for data operations without specifying implementation details.
### Details:
Create `lib/data/repositories/interfaces` directory. Define `abstract class IUserRepository { Future<User> getUserById(String id); Future<void> updateUser(User user); }`, `abstract class ITransactionRepository { ... }`, etc., with methods reflecting required data operations. Do not implement concrete classes yet.

## 3. Implement Initial Concrete Repository (`UserRepository`) and Provide via Riverpod [done]
### Dependencies: 29.2
### Description: Create a concrete implementation of `IUserRepository` (e.g., `UserRepositoryImpl`) that interacts with Firestore. Provide this implementation via a Riverpod provider (e.g., `Provider<IUserRepository>`). This will be the first concrete repository integrated.
### Details:
Create `lib/data/repositories/implementations/user_repository_impl.dart`. Implement methods using existing Firestore logic. Define `userRepositoryProvider = Provider<IUserRepository>((ref) => UserRepositoryImpl());` in `lib/providers/repository_providers.dart`. Ensure `UserRepositoryImpl` uses existing Firestore logic without direct dependency on `FirestoreService` yet.

## 4. Migrate Authentication UI and Logic to Riverpod [done]
### Dependencies: 29.3
### Description: Refactor the authentication-related UI components (e.g., login, signup screens, user profile) to use `ConsumerWidget` or `ConsumerStatefulWidget`. Update their logic to interact with `userRepositoryProvider` for user data and status, preparing for `AuthService` refactoring.
### Details:
Identify `AuthScreen`, `SignupScreen`, and `UserProfileScreen`. Convert them to `ConsumerWidget`/`ConsumerStatefulWidget`. Replace direct calls to static `AuthService` methods for user data retrieval with `ref.read(userRepositoryProvider)`. Focus on reading user data or status from the provider.

## 5. Refactor `AuthService` to Riverpod Provider [done]
### Dependencies: 29.4
### Description: Convert the existing static `AuthService` into an instance class and provide it via a Riverpod provider (e.g., `authServiceProvider`). This provider will encapsulate authentication logic and potentially depend on `userRepositoryProvider`.
### Details:
Create `lib/services/auth_service.dart` (non-static). Define `authServiceProvider = Provider((ref) => AuthService(ref.read(userRepositoryProvider)));`. Update all call sites from `AuthService.method()` to `ref.read(authServiceProvider).method()`. Ensure all authentication flows (login, logout, signup) are functional.

## 6. Refactor `FirestoreService` to Riverpod Provider [done]
### Dependencies: 29.5
### Description: Convert the existing static `FirestoreService` into an instance class and provide it via a Riverpod provider (e.g., `firestoreServiceProvider`). This will allow other repositories and services to depend on it via Riverpod, centralizing Firestore access.
### Details:
Create `lib/services/firestore_service.dart` (non-static). Define `firestoreServiceProvider = Provider((ref) => FirestoreService());`. Update all call sites that directly use `FirestoreService.instance` or static methods to `ref.read(firestoreServiceProvider).method()`. Ensure `UserRepositoryImpl` is updated to depend on `firestoreServiceProvider`.

## 7. Implement Remaining Concrete Repositories (`Transaction` and `Account`) [done]
### Dependencies: 29.6
### Description: Create concrete implementations for `ITransactionRepository` and `IAccountRepository` (e.g., `TransactionRepositoryImpl`, `AccountRepositoryImpl`). These will interact with Firestore via `firestoreServiceProvider` and be provided via Riverpod.
### Details:
Implement `TransactionRepositoryImpl` and `AccountRepositoryImpl` in `lib/data/repositories/implementations`. Define `transactionRepositoryProvider` and `accountRepositoryProvider` in `lib/providers/repository_providers.dart`, ensuring they depend on `ref.read(firestoreServiceProvider)`.

## 8. Bulk Migrate Remaining UI Components to Riverpod [done]
### Dependencies: 29.7
### Description: Systematically migrate all other `StatelessWidget` and `StatefulWidget` UI components to `ConsumerWidget` or `ConsumerStatefulWidget`. Update their logic to utilize `ref.watch` and `ref.read` for accessing state and data from the newly created Riverpod providers (e.g., `transactionRepositoryProvider`, `accountRepositoryProvider`).
### Details:
Identify all remaining UI components that interact with data or state. Convert them. Replace direct data access or static service calls with Riverpod provider interactions. Prioritize screens with high data interaction (e.g., transaction list, account details).

## 9. Implement Enhanced Error Handling with `AsyncValue` [done]
### Dependencies: 29.8
### Description: Integrate robust error handling mechanisms throughout the new architecture. Utilize Riverpod's `AsyncValue.error` to propagate errors effectively from repositories and providers. Implement consistent error presentation in the UI using `AsyncValue.when`.
### Details:
Modify repository methods and providers to return `Future<AsyncValue<T>>` or `Stream<AsyncValue<T>>` where appropriate. Implement `try-catch` blocks in repositories to catch Firestore errors and wrap them in `AsyncValue.error`. Update UI components to handle `AsyncValue.when` for loading, data, and error states, displaying user-friendly messages.

## 10. Establish Testing Infrastructure and Write Initial Tests [done]
### Dependencies: 29.9
### Description: Set up a comprehensive testing strategy for the new Riverpod and repository architecture. This includes writing unit tests for individual providers and repository methods using `ProviderContainer` for isolated testing, and integration tests to verify the data flow and UI reactions within the new state management system.
### Details:
Configure `flutter_test` and `mocktail` (if not already). Write unit tests for `UserRepositoryImpl`, `AuthService`, `TransactionRepositoryImpl`, and key Riverpod providers using `ProviderContainer` to mock dependencies. Write a few integration tests for critical user flows (e.g., adding a transaction, viewing account balance) that involve multiple layers of the new architecture.

