# Task ID: 14
# Title: Budget Editing and Deletion
# Status: done
# Dependencies: 13
# Priority: medium
# Description: Implement the ability to edit and delete existing budgets using direct Firestore operations within the Flutter client application.
# Details:
Provide UI for editing budget amounts. When a budget amount is changed, ensure the progress calculation updates immediately. Implement budget deletion, which will be a soft deletion (marking as inactive) to preserve past transaction data integrity. Ensure client-side logic handles the removal of the budget from the user's view. All data operations will be performed directly against Firestore using a Repository pattern and managed with Riverpod for state. Firestore Security Rules will be used for data validation and access control.

# Test Strategy:
Edit budget amounts and verify progress indicators adjust. Delete budgets (soft deletion) and confirm they are removed from the budget overview, marked as inactive in Firestore, and do not affect historical transactions. Verify Firestore Security Rules enforce correct access and validation.

# Subtasks:
## 1. Implement Firestore Operations for Budget Editing (Repository Layer) [done]
### Dependencies: None
### Description: Implement the client-side logic within the Repository layer to update budget documents in Firestore, including data validation and persistence.
### Details:
Develop methods within the BudgetRepository to perform `update` operations on budget documents in Firestore. Ensure data validation is handled either client-side before the write, or primarily via Firestore Security Rules. Implement robust error handling for Firestore operations.

## 2. Implement Budget Editing User Interface [done]
### Dependencies: 14.1
### Description: Create or modify the frontend UI to allow users to edit existing budget details, including pre-populating fields and a save mechanism.
### Details:
Develop a form or modal that displays current budget information, allows users to modify fields, and dispatches actions via Riverpod to the BudgetRepository (Subtask 1) to update the data in Firestore upon submission.

## 3. Implement Firestore Operations for Non-Destructive Budget Deletion (Repository Layer) [done]
### Dependencies: None
### Description: Implement the client-side logic within the Repository layer to perform a soft deletion of budgets in Firestore by marking them as inactive or archived, ensuring data integrity.
### Details:
Develop methods within the BudgetRepository to perform `update` operations on budget documents in Firestore, specifically setting a status flag (e.g., `isActive` to `false` or `status` to `archived`) for the specified budget ID. Ensure this is a soft deletion.

## 4. Implement Budget Deletion UI and Real-time Progress Updates [done]
### Dependencies: 14.2, 14.3
### Description: Add a delete option to the budget UI with a confirmation dialog, and ensure that budget lists and progress indicators update in real-time after both edit and delete operations.
### Details:
Integrate a delete button into the budget display, implement a confirmation modal before dispatching a soft deletion action via Riverpod to the BudgetRepository (Subtask 3). Implement mechanisms (e.g., Riverpod state updates, Firestore stream listeners) to reflect changes from both editing (Subtask 2) and soft deletion operations immediately in the UI, including progress bars and budget list updates.

## 5. Define and Implement Firestore Security Rules for Budgets [done]
### Dependencies: None
### Description: Write and deploy Firestore Security Rules to enforce data validation, access control, and ensure only authorized users can perform budget edit and soft deletion operations.
### Details:
Define rules for the 'budgets' collection to allow read access, and write/update access only for authenticated users who own the budget. Ensure rules prevent hard deletion if soft deletion is the chosen method. Validate incoming data structure and types for budget updates.

