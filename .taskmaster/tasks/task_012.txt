# Task ID: 12
# Title: Tag Management
# Status: done
# Dependencies: 10
# Priority: medium
# Description: Implement custom tag management for transactions, allowing users to create, edit, and delete tags.
# Details:
Store user-defined tags in `users/{userId}/tags` collection following the `Tag` schema (id, name, color, usageCount, schemaVersion, createdAt, updatedAt). Implement UI for creating, editing, and deleting tags. When a tag is deleted, ensure it's removed from any associated transactions. The `tagIds` field in the `Transaction` schema will store references to these tag documents.

# Test Strategy:
Create, edit, and delete custom tags. Apply tags to transactions. Verify tags are correctly associated with transactions. Delete a tag and confirm it's removed from all linked transactions.

# Subtasks:
## 1. Define Database Schema for Tags and Associations [done]
### Dependencies: None
### Description: Design and implement the database tables for `Tags` (e.g., `id`, `name`, `color`) and a many-to-many `TagTransaction` join table (e.g., `tag_id`, `transaction_id`).
### Details:
This includes defining data types, constraints, and relationships for both the core tag entity and the linking table to transactions.

## 2. Implement Backend API for Tag CRUD Operations [done]
### Dependencies: 12.1
### Description: Develop RESTful API endpoints for creating, reading (listing and by ID), updating, and soft-deleting tags.
### Details:
Focus on robust error handling, validation, and efficient database interactions for tag management.

## 3. Develop Frontend UI for Tag Management [done]
### Dependencies: 12.2
### Description: Create a user interface page or component allowing users to perform CRUD operations on tags (e.g., add new tags, edit existing ones, view all tags).
### Details:
This UI should provide an intuitive way to manage the list of available tags, including input forms and display tables.

## 4. Implement Backend API for Tag-Transaction Association [done]
### Dependencies: 12.1
### Description: Develop API endpoints to associate (link) and disassociate (unlink) tags with specific transactions. This involves managing entries in the `TagTransaction` join table.
### Details:
Ensure atomic operations for linking/unlinking and proper handling of existing associations.

## 5. Develop Frontend UI for Tag-Transaction Association [done]
### Dependencies: 12.3, 12.4
### Description: Create UI components within the transaction details view or an editing flow that allows users to select and apply existing tags to a transaction, and remove them.
### Details:
This UI could involve a multi-select dropdown or a tag input field with auto-completion for existing tags.

## 6. Implement Tag Deletion Data Consistency Logic [done]
### Dependencies: 12.2, 12.4
### Description: Ensure that when a tag is deleted (soft-delete recommended), all its associations with transactions are also handled appropriately (e.g., removed from the `TagTransaction` table or marked as inactive).
### Details:
This is crucial for data integrity. If soft-deleting tags, ensure the UI for association doesn't show deleted tags, and existing associations with deleted tags are handled gracefully.

