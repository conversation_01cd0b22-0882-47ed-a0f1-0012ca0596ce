# Task ID: 9
# Title: Custom Category and Subcategory Management
# Status: done
# Dependencies: 8
# Priority: high
# Description: Enable users to create, edit, and delete custom income and expense categories and subcategories, assigning custom icons and colors.
# Details:
Store custom categories in Firestore subcollections under `users/{userId}/categories/` following the `Category` schema (id, name, type, parentId, color, icon, isActive, schemaVersion, createdAt, updatedAt). Implement UI for managing custom categories. Ensure that categories cannot be deleted if they have associated transactions or subcategories (enforced by Firestore Security Rules and client-side validation). Provide a guided re-assignment flow for transactions before deletion.

# Test Strategy:
Create, edit, and delete custom categories and subcategories. Assign icons and colors. Attempt to delete categories with associated transactions/subcategories and verify the system prevents deletion and prompts for re-assignment. Test the re-assignment flow.

# Subtasks:
## 1. Design Hierarchical Category Data Model [done]
### Dependencies: None
### Description: Define the database schema for categories and subcategories, including parent-child relationships, unique identifiers, names, and any other relevant attributes. Consider self-referencing relationships for arbitrary depth.
### Details:
Entity-Relationship Diagram (ERD), field definitions, data types, primary/foreign keys, and indexing strategies.

## 2. Implement Database Schema for Categories & Subcategories [done]
### Dependencies: 9.1
### Description: Create the necessary database tables and relationships based on the designed data model. This includes migration scripts for schema creation and initial seeding if required.
### Details:
SQL DDL scripts, ORM model definitions (if applicable), and database migration files.

## 3. Implement Client-Side Firestore Operations for Category CRUD [done]
### Dependencies: None
### Description: Implement client-side logic using the Repository pattern and Riverpod for creating, reading (listing all, getting by ID), updating, and soft-deleting top-level categories directly in Firestore.
### Details:
Flutter code for Firestore CRUD operations, Repository methods, Riverpod providers for state management, and basic client-side input validation for category data.

## 4. Implement Client-Side Firestore Operations for Subcategory CRUD [done]
### Dependencies: 9.3
### Description: Implement client-side logic using the Repository pattern and Riverpod for creating, reading, updating, and soft-deleting subcategories directly in Firestore, ensuring they are correctly linked to a parent category.
### Details:
Flutter code for Firestore CRUD operations, Repository methods, Riverpod providers, validation, and handling of parent category ID for subcategory creation/updates.

## 5. Implement Frontend UI for Category & Subcategory Listing [done]
### Dependencies: 9.3, 9.4
### Description: Develop the user interface to display categories and their nested subcategories in a hierarchical, readable format. Include basic actions like view details and navigation.
### Details:
React/Angular/Vue components, data fetching from category/subcategory APIs, and hierarchical rendering logic (e.g., tree view or nested lists).

## 6. Implement Frontend UI for Category & Subcategory Creation/Editing [done]
### Dependencies: 9.3, 9.4, 9.5
### Description: Develop forms and UI elements for users to create new categories and subcategories, and to edit existing ones. This includes selecting parent categories for subcategories.
### Details:
Form components, client-side input validation, API integration for POST/PUT requests, and success/error handling feedback to the user.

## 7. Implement Client-Side Logic and Firestore Security Rules for Deletion Constraints [done]
### Dependencies: 9.3, 9.4
### Description: Develop client-side logic to prevent the deletion of categories or subcategories that have associated transactions or child subcategories. Also, define Firestore Security Rules to enforce these constraints at the database level. Return appropriate error messages/feedback to the user.
### Details:
Flutter code for checking linked transactions and child categories before attempting deletion, and defining Firestore Security Rules to prevent unauthorized or invalid deletions. Handle error feedback in the UI.

## 8. Implement Client-Side Logic for Transaction Re-assignment Pre-deletion [done]
### Dependencies: 9.7
### Description: Develop client-side mechanism to identify transactions associated with a category/subcategory marked for deletion and provide a client-side function to re-assign them to a new, valid category/subcategory directly in Firestore.
### Details:
Flutter code for re-assignment logic, direct Firestore transaction updates, validation of the new category ID, and handling of bulk re-assignments.

## 9. Implement Frontend UI for Guided Deletion & Re-assignment Flow [done]
### Dependencies: 9.5, 9.6, 9.7, 9.8
### Description: Develop the user interface to guide users through the deletion process when constraints are met. This includes displaying associated transactions/children and providing options for re-assignment before final deletion.
### Details:
Modal dialogs or dedicated screens, conditional rendering based on deletion constraints, dropdowns/search for selecting new categories for re-assignment, confirmation steps, and API integration for deletion and re-assignment.

## 10. Define and Implement Firestore Collection Structure for Categories & Subcategories [done]
### Dependencies: 9.1
### Description: Based on the data model, define the specific Firestore collection structure (subcollections under `users/{userId}/categories/`), document fields, and data types for categories and subcategories. Implement the necessary data models in Flutter.
### Details:
Firestore collection paths, document structure, field definitions (e.g., `id`, `name`, `type`, `parentId`, `color`, `icon`, `isActive`, `createdAt`, `updatedAt`), and Flutter `Category` model class with `fromFirestore` and `toFirestore` methods.

