# Task ID: 6
# Title: Account Management (CRUD)
# Status: done
# Dependencies: 1, 5
# Priority: high
# Description: Implement the ability for users to create, edit, and manage multiple financial accounts (Checking, Savings, Credit Card, Cash) with initial balances and account types (Asset/Liability).
# Details:
Create a Flutter UI for account creation and editing. Store account data in `users/{userId}/accounts` collection following the `Account` schema (id, name, type, subtype, initialBalance, currency, color, icon, isPrimary, isActive, schemaVersion, createdAt, updatedAt). Ensure `type` is locked after creation. Implement client-side validation for input fields. Use `cloud_firestore` to perform CRUD operations. Account balances will be computed dynamically from transactions.

# Test Strategy:
Create various account types with initial balances. Edit account details (name, icon, color). Verify account type cannot be changed after creation. Confirm accounts are stored correctly in Firestore and accessible only by the owning user.

# Subtasks:
## 1. Define Account Data Schema [done]
### Dependencies: None
### Description: Design the Firestore data model for user accounts, including fields like userId, email, displayName, creationDate, lastLogin, roles, etc. Consider data types, indexing, and security implications.
### Details:
This involves defining the structure of the 'accounts' collection in Firestore, specifying fields, their types, and any necessary sub-collections or nested objects. Also, consider how user-specific data access will be managed.

## 2. Design Account Management UI/UX [done]
### Dependencies: 6.1
### Description: Create wireframes and mockups for the Account Management interface, including forms for creation/editing, a list view for existing accounts, and confirmation dialogs for deletion. Focus on user-friendliness and clarity.
### Details:
Develop UI designs for account creation, viewing account details, editing account information, and a table/list to display multiple accounts. Include elements for search, pagination (if applicable), and action buttons.

## 3. Set Up Firestore Security Rules & Basic Integration [done]
### Dependencies: 6.1
### Description: Configure Firestore security rules to ensure proper read/write access control for account data. Implement basic client-side Firestore initialization and connection.
### Details:
Write Firestore security rules that enforce user-specific data access and validate incoming data based on the defined schema. Establish the initial connection to Firestore from the client application.

## 4. Implement Client-Side Validation Logic [done]
### Dependencies: 6.2
### Description: Develop and integrate client-side validation rules for all account-related input fields (e.g., email format, password strength, required fields) before data submission to Firestore.
### Details:
Write JavaScript/TypeScript functions to validate form inputs for account creation and updates. Provide immediate feedback to the user for invalid entries. This includes regex checks, length constraints, and uniqueness checks (if applicable).

## 5. Implement Account Creation (CRUD - Create) [done]
### Dependencies: 6.2, 6.3, 6.4
### Description: Develop the functionality to create new user accounts, including UI form submission, client-side validation, and writing the new account data to Firestore.
### Details:
Connect the 'create account' UI form to the backend. On submission, trigger client-side validation. If valid, send the data to a Firestore 'add' operation, handling success and error states.

## 6. Implement Account Reading & Display (CRUD - Read) [done]
### Dependencies: 6.2, 6.3
### Description: Develop the functionality to fetch and display existing user account data from Firestore in the UI, including single account views and a list/table of accounts.
### Details:
Implement Firestore 'get' and 'onSnapshot' operations to retrieve account data. Populate the account list/table and individual account detail views with the fetched data. Consider pagination and filtering if necessary.

## 7. Implement Account Update & Deletion (CRUD - Update/Delete) [done]
### Dependencies: 6.2, 6.3, 6.4, 6.6
### Description: Develop the functionality to modify existing account details and to permanently delete accounts from Firestore, including UI interactions, validation (for update), and Firestore operations.
### Details:
For updates: connect the 'edit account' UI form, apply client-side validation, and perform a Firestore 'update' operation. For deletion: implement a confirmation dialog and execute a Firestore 'delete' operation, handling success/error for both.

