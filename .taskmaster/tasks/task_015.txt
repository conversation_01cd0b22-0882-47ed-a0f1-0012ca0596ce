# Task ID: 15
# Title: Financial Goal Tracking and Contributions
# Status: pending
# Dependencies: 6, 10
# Priority: medium
# Description: Implement the ability to create and track savings goals with target amounts and manual contributions using direct Firestore operations in a Flutter client-side application.
# Details:
Store goals in `users/{userId}/goals` collection following the `Goal` schema (id, name, description, targetAmount, currentAmount, currency, targetDate, isCompleted, color, icon, schemaVersion, createdAt, updatedAt). Store manual contributions in a sub-collection `users/{userId}/goals/{goalId}/contributions` using `GoalContribution` schema. `currentAmount` in the `Goal` document should be a denormalized field updated by client-side logic using Firestore batch operations when contributions are added, updated, or deleted. Implement UI for goal creation, contribution, and visual progress indicators using Riverpod for state management and a Repository pattern for Firestore interactions. Mark goals as 'Completed' when `currentAmount` reaches `targetAmount`. Firestore Security Rules must be implemented for data validation and access control.

# Test Strategy:
Create savings goals with target amounts. Add manual contributions and verify `currentAmount` and visual progress update. Test goal completion status. Delete contributions and verify `currentAmount` adjusts. Delete goals and confirm associated contributions are also deleted.

# Subtasks:
## 1. Design Firestore Goal Data Model [pending]
### Dependencies: None
### Description: Define the Firestore document structure for financial goals, including fields like `id`, `name`, `targetAmount`, `currentAmount` (denormalized), `targetDate`, `createdAt`, `updatedAt`, `isCompleted`, `color`, `icon`, and `schemaVersion`. Specify collection path `users/{userId}/goals`.
### Details:
This data model will be the foundation for storing all goal-related information directly in Firestore. Consider data types, indexing, and potential future extensions. Ensure alignment with the `Goal` schema provided in the main task details.

## 2. Design Firestore Contribution Data Model [pending]
### Dependencies: 15.1
### Description: Define the Firestore document structure for contributions, including fields like `id`, `goalId`, `amount`, `contributionDate`, and `description`. Specify subcollection path `users/{userId}/goals/{goalId}/contributions`.
### Details:
This data model will track individual contributions made towards specific goals. Ensure proper subcollection relationships to the goal documents. Align with the `GoalContribution` schema.

## 3. Implement Firestore Repository for Goal Management [pending]
### Dependencies: 15.1
### Description: Develop client-side logic using a Repository pattern for CRUD (Create, Read, Update, Delete) operations on financial goals directly with Firestore, integrated with Riverpod for state management.
### Details:
This includes methods for adding new goals, fetching user-specific goals, updating goal details, and deleting goals. Ensure proper error handling and data serialization/deserialization for Firestore documents. Utilize Riverpod for exposing data streams.

## 4. Implement Firestore Repository for Contribution Management [pending]
### Dependencies: 15.2, 15.3
### Description: Develop client-side logic using a Repository pattern for CRUD operations on contributions directly with Firestore, ensuring they are linked to specific goals and integrated with Riverpod.
### Details:
This includes methods for adding new contributions to a specific goal, fetching contributions for a goal, updating contributions, and deleting contributions. Ensure proper error handling and data serialization/deserialization for Firestore documents. Utilize Riverpod for exposing data streams.

## 5. Develop Frontend UI for Goal Creation and Editing [pending]
### Dependencies: 15.3
### Description: Build user interface components allowing users to create new financial goals and modify existing ones, interacting with the Firestore Goal Repository.
### Details:
Design forms for inputting goal details, validation, and displaying success/error messages. Ensure a user-friendly experience. Use Riverpod to interact with the goal repository.

## 6. Develop Frontend UI for Contribution Entry [pending]
### Dependencies: 15.4, 15.5
### Description: Build user interface components for users to record contributions towards specific financial goals, interacting with the Firestore Contribution Repository.
### Details:
Design forms for inputting contribution amounts and dates, linking them to existing goals. Provide a clear way to select the target goal. Use Riverpod to interact with the contribution repository.

## 7. Implement Client-side Logic for Denormalized Current Amount Updates [pending]
### Dependencies: 15.3, 15.4
### Description: Develop client-side logic to automatically update the `currentAmount` field in the goal document whenever a contribution is added, updated, or deleted for that goal, using Firestore batch operations.
### Details:
This ensures the `currentAmount` on the goal object is always up-to-date. Implement this logic within the Contribution Repository or a dedicated service, ensuring atomicity using Firestore batch writes for simultaneous updates to contribution and goal documents.

## 8. Develop Frontend UI for Visual Progress Indicators [pending]
### Dependencies: 15.5, 15.7
### Description: Create interactive UI elements (e.g., progress bars, charts, percentage displays) to visually display the progress of each financial goal based on its `currentAmount` and `targetAmount`.
### Details:
Fetch goal data, including the denormalized current amount, and render compelling visual indicators. Consider responsiveness and accessibility. Use Riverpod to observe goal data changes.

## 9. Implement Firestore Security Rules for Goals and Contributions [pending]
### Dependencies: 15.1, 15.2
### Description: Write and deploy Firestore Security Rules to enforce data validation, access control (read/write permissions based on user authentication and ownership), and data structure integrity for `users/{userId}/goals` and `users/{userId}/goals/{goalId}/contributions`.
### Details:
Ensure only authenticated users can access their own data. Validate incoming data against the defined schemas (e.g., `targetAmount` is a number, `currentAmount` cannot exceed `targetAmount` directly, `contributionDate` is valid). Prevent unauthorized modifications.

