# System Patterns: BudApp

## System Architecture

### Overall Architecture
**Offline-First Mobile App** with **Firebase Backend**
- Flutter mobile application with Firebase services (Firestore, Auth, Remote Config, FCM)
- Client-side business logic with server-side configuration
- Direct Firestore SDK communication with automatic sync

### Multi-Environment Configuration
- **Three Firebase Projects**: budapp-dev, budapp-staging-1, budapp-prod
- **Android Flavors**: dev, staging, prod with distinct package names
- **Unified Entry Point**: Single main.dart with automatic environment detection
- **Default Development Workflow**: `flutter run` defaults to dev environment

## Key Technical Decisions

### Frontend Architecture
- **Framework**: Flutter with Dart 3 for cross-platform mobile development
- **State Management**: Riverpod (AsyncNotifier/Notifier pattern) for predictable state handling
- **Architecture Pattern**: Repository pattern with dependency injection via Riverpod providers
- **Data Models**: Immutable models using freezed with JSON serialization for Firestore
- **Navigation**: go_router with hub-based navigation and global FAB system for consistent user experience
- **UI Framework**: Material 3 & Cupertino for platform-appropriate design with global text overflow system

### Backend Architecture
- **Database**: Cloud Firestore (NoSQL) with comprehensive Security Rules
- **Authentication**: Firebase Authentication with email/password + OAuth (Google, Apple) + Biometric (Face ID/Fingerprint)
- **Configuration**: Firebase Remote Config for server-side configuration management
- **Messaging**: Firebase Cloud Messaging (FCM) for in-app notifications
- **No Server-Side Logic**: MVP uses only Firebase managed services (no Cloud Functions)

### Security Architecture
- **Firestore Security Rules**: Comprehensive server-side validation and access control
- **User Data Isolation**: All user data stored in subcollections under `/users/{userId}/`
- **Authentication Required**: All operations require authenticated users with strict user ID matching
- **Biometric Security**: Secure biometric authentication with platform-specific hardware integration
- **Secure Storage**: Sensitive data (biometric keys, preferences) stored using flutter_secure_storage
- **Data Validation**: Complete field type validation, required fields, and business logic enforcement
- **Centralized Error Handling**: Firebase Crashlytics integration with PII protection and environment-aware crash reporting
- **Error Recovery**: Global error boundaries with graceful UI degradation and automatic recovery mechanisms

## Design Patterns in Use

### Repository Pattern (IMPLEMENTED & ENFORCED)
```
UI Layer (ConsumerWidget) → Riverpod Providers → Repository Layer → Firestore SDK
```
- **Repository Interfaces**: Clean abstractions for data operations (IUserRepository, IAccountRepository, IGoalRepository, etc.)
- **Concrete Implementations**: Firestore-backed repositories with comprehensive CRUD operations
- **Dependency Injection**: Riverpod providers manage repository instances and dependencies
- **Strict Layer Separation**: **Task 31.4 Complete** - Eliminated all direct Firebase access from business logic
- **Service Abstractions**: Dedicated interfaces for cross-cutting concerns (e.g., IFirebaseConnectivityService)
- **Architecture Enforcement**: Comprehensive guidelines preventing `FirebaseAuth.instance` and `FirebaseFirestore.instance` usage outside providers
- **UI Integration**: ConsumerWidget/ConsumerStatefulWidget for reactive state management

### Generic Form System ✅ COMPLETE
- **Configuration-Driven**: GenericFormConfig with type-safe field definitions
- **Reusable Components**: FormFieldFactory, BaseEditableFormScreen, GenericFormScreen
- **Common Patterns**: Standardized selectors (color, icon, type) and form fields
- **Custom Field Support**: Extensible system for complex field types (AccountTypeFormFieldImpl)
- **Validation Integration**: Centralized validation with existing validator services
- **Repository Pattern**: Clean integration with existing repository providers
- **Type Safety**: Full TypeScript-like type safety for form configurations and data mapping
- **Code Efficiency**: Proven to reduce form code by 60-80% while maintaining functionality

### Material 3 Floating Label Forms ✅ COMPLETE
- **Enhanced Components**: AppTextFormField and AuthFormField with Material 3 floating label behavior
- **100% Coverage**: All authentication, profile, entity, transaction, and budget forms use Material 3 patterns
- **Specialized Support**: Transaction and budget forms enhanced while preserving specialized functionality
- **Backward Compatibility**: Legacy static label mode available via useFloatingLabel parameter
- **Consistent Styling**: Unified Material 3 styling with proper accessibility and smooth animations
- **Quality Assurance**: All 554 tests passing with flutter analyze clean and proper code formatting

### Centralized Error Handling Pattern ✅ COMPLETE
- **GlobalErrorHandler Service**: Firebase Crashlytics integration with comprehensive crash reporting
- **Global Error Handlers**: Captures Flutter framework errors, platform dispatcher errors, and isolate errors
- **Error Boundary System**: React-style error boundaries for graceful UI degradation with retry mechanisms
- **Enhanced ErrorService**: Firebase error translation with crash reporting integration and user-friendly messaging
- **PII Protection**: Automatic data sanitization in all error reporting and logging to protect financial data
- **Environment-Aware Configuration**: Debug/staging/production specific crash collection and logging settings
- **User Context Tracking**: Associates crashes with user sessions while maintaining privacy compliance
- **Provider Integration**: Riverpod providers automatically initialize error handling at app startup
- **Recovery Mechanisms**: Built-in retry logic, user-initiated recovery, and graceful degradation patterns
- **Material 3 Error UI**: Consistent error interfaces following design system with accessibility support
- **Production Readiness**: Zero test failures, clean static analysis, security validated, performance optimized

### Offline-First Pattern
- **Local Cache**: Firestore offline persistence as primary data source
- **Optimistic Updates**: Immediate UI updates with background sync
- **Data Pre-population**: Strategic caching of user data for complete offline access
- **Sync Reconciliation**: Automatic conflict resolution when connectivity restored

### Configuration-Driven Development ✅ IMPLEMENTED
- **Remote Config**: Server-side control of app behavior without updates
- **Feature Flags**: Gradual rollout and A/B testing capabilities
- **Premium Limits**: Dynamic subscription tier enforcement
- **Category Management**: Predefined categories delivered via Remote Config

### Security-First Architecture
- **Zero Trust**: All data access validated through Firestore Security Rules
- **User Isolation**: Sub-collection structure ensures users only access their data
- **Client Validation**: Immediate feedback with server-side rule enforcement
- **Secure Storage**: Platform-specific secure storage for sensitive data

## Data Patterns

### Firestore Collection Structure
```
users/{userId}                          # User profiles (top-level)
├── accounts/{accountId}                # Financial accounts (subcollection)
├── transactions/{transactionId}        # All transactions (subcollection)
├── categories/{categoryId}             # User custom categories (subcollection)
├── budgets/{budgetId}                  # User budgets (subcollection)
├── goals/{goalId}                      # Financial goals (subcollection)
└── tags/{tagId}                        # User-defined tags (subcollection)
```

### Account Balance Tracking Pattern
- **Dual Balance Architecture**: Accounts maintain both `initialBalanceCents` and `currentBalanceCents`
- **Atomic Balance Updates**: All transaction operations use Firestore transactions for data consistency
- **Financial Precision**: All amounts stored as integers (cents) to avoid floating-point precision issues

### Budget Management Pattern
- **Repository Pattern**: BudgetRepository interface with Firebase implementation
- **State Management**: Riverpod AsyncNotifier providers for budget operations
- **Real-Time Tracking**: currentAmountCents field automatically updated by BudgetTransactionService
- **Dual Budget System**: Both category-specific and total budgets created automatically during transactions
- **Period Management**: Precise period-based budget management with `periodStart` field for exact period identification

### Goal Management Pattern ✅ COMPLETE
- **Repository Pattern**: IGoalRepository interface with comprehensive CRUD operations and real-time streams
- **Data Model**: Goal model with Freezed, JSON serialization, and comprehensive validation system
- **Progress Tracking**: Automatic progress calculation with currentAmountCents updates and completion detection
- **Status Management**: GoalStatus enum (active, paused, completed, cancelled) with proper state transitions
- **Search & Analytics**: Name/description search, statistics calculation, user summaries, and deadline tracking
- **Real-Time Updates**: Firestore snapshots for live UI updates with watchUserGoals and watchGoal streams
- **Validation System**: Business rule enforcement, uniqueness checking, and Goal model validation integration
- **User Isolation**: Collection path users/{userId}/goals following established security patterns

### Goal Contribution Management Pattern ✅ COMPLETE
- **Repository Pattern**: IGoalContributionRepository interface with 24 methods covering comprehensive contribution management
- **Subcollection Architecture**: users/{userId}/goals/{goalId}/contributions following hierarchical data patterns
- **Data Model**: GoalContribution model with Freezed, JSON serialization, and comprehensive validation system
- **CRUD Operations**: Full create, read, update, delete operations with proper user isolation and soft deletion
- **Real-Time Streams**: Firestore snapshots for live updates with watchContributionsForGoal and watchAllUserContributions
- **Analytics & Statistics**: Comprehensive statistics calculation including total amounts, counts, averages, and date ranges
- **Date-Based Filtering**: Advanced filtering with getContributionsByDateRange, getTodaysContributions, getThisWeeksContributions
- **Goal Integration**: Goal-specific operations and user-level operations for cross-goal contribution queries
- **Validation System**: Integration with GoalContribution model validation, goal existence validation, and business rule enforcement
- **Provider Integration**: Seamless Riverpod integration with goalContributionRepositoryProvider and established patterns

### Goal Data Model Pattern ✅ COMPLETE
- **Freezed Implementation**: Complete Goal model with JSON serialization following established BudApp patterns
- **Comprehensive Fields**: All required fields (id, name, targetAmountCents, currentAmountCents, targetDate, isCompleted, colorHex, iconName, schemaVersion) plus standard BudApp fields (userId, description, isActive, metadata, createdAt, updatedAt)
- **Status Management**: GoalStatus enum (active, paused, completed, cancelled) with backward compatibility for boolean isCompleted field
- **Collection Path**: users/{userId}/goals following security and performance patterns
- **Validation System**: 11 comprehensive business rules covering name length, amounts, dates, color format, icon format, and consistency validation
- **Progress Calculations**: Built-in methods for progressPercentage, remainingAmountCents, isAchieved, daysRemaining, requiredDailySavingsCents
- **Factory Methods**: Goal.create(), Goal.fromJson(), Goal.fromFirestore() with proper defaults and backward compatibility
- **Monetary Precision**: targetAmountCents and currentAmountCents stored as integers to avoid floating-point issues
- **Visual Customization**: colorHex (#RRGGBB format) and iconName fields for UI theming and iconography
- **Schema Versioning**: Version 1 baseline with migration support for future data model evolution
- **Uniqueness Constraints**: One budget per category per time period enforced at both client and database level
- **Fallback Logic**: `findLatestBudgetFromPreviousPeriod` method for displaying previous period budgets when none exist
- **Migration Support**: BudgetMigrationService for schema version upgrades and data migration
- **Bulk Operations**: BulkBudgetService for percentage adjustments and batch operations
- **Progress Calculation**: BudgetProgressService for real-time progress tracking without transaction aggregation

### Navigation System Pattern ✅ COMPLETE
- **Hub-Based Architecture**: Central home screen with 6 feature cards (Accounts, Transactions, Budgets, Goals, Statistics, Settings) in responsive 2x3 grid
- **Global FAB System**: Consistent 3-FAB system across all screens with Material 3 design:
  - Green FAB (bottom right): Add transaction - available on all screens for consistent action access
  - Grey FAB (bottom left): Back navigation - appears on all screens except home for intuitive navigation
  - Grey FAB (center bottom): Home navigation - for screens deeper than one step from home
- **Route Architecture**: Simplified go_router configuration with top-level GoRoutes replacing complex ShellRoute system
- **FAB Visibility Management**: Route-based visibility logic with FabVisibilityNotifier for intelligent FAB display
- **Extension Method Integration**: GlobalFabSystemExtension provides `.withGlobalFabs()` for easy screen integration
- **Material 3 Compliance**: Modern FloatingActionButton styling with proper elevation, colors, and accessibility
- **Screen Integration Pattern**: All screens follow consistent pattern with Scaffold content wrapped by global FAB system
- **Accessibility Support**: Semantic labels, proper tap targets (44x44), and screen reader compatibility

## Component Relationships

### Core Domain Models
```
User → Accounts → Transactions
User → Categories (custom) + Remote Categories (predefined)
User → Budgets → Budget Progress (calculated)
User → Goals → Goal Contributions
User → Tags → Transaction Tags
```

### Service Dependencies (RIVERPOD PROVIDER PATTERN)
- **AuthService**: ✅ COMPLETED - Instance-based service with dependency injection
- **UserRepository**: ✅ COMPLETED - Complete implementation with Firestore backend
- **AccountRepository**: ✅ COMPLETED - Full CRUD operations with balance tracking
- **CategoryRepository**: ✅ COMPLETED - Hierarchical categories with deletion constraints
- **TransactionRepository**: ✅ COMPLETED - Atomic transaction operations with balance updates
- **RemoteConfigService**: ✅ COMPLETED - Server-side configuration management

## Critical Implementation Paths

### User Authentication Flow
1. **App Launch** → AuthWrapper watches authStateChanges via authServiceProvider
2. **Authentication State** → Reactive UI updates via Consumer widgets and Riverpod providers
3. **First Time** → Registration/OAuth → Automatic profile creation → Onboarding
4. **Returning User** → Biometric Gate (if enabled) → SessionService → Main app

### Biometric Authentication Gate Pattern
1. **Router Redirect Logic** → BiometricGateStateNotifier checks if gate is required
2. **Gate Requirements** → User authenticated + biometric enabled + not completed in session
3. **BiometricGateScreen** → Automatic biometric prompt + retry/settings/signout options
4. **Session Tracking** → Gate completion persists until app restart or user change
5. **State Management** → Reactive router updates based on authentication state changes

### Transaction Entry Flow
1. **User Input** → Form validation → Temporary local storage
2. **Save Action** → Optimistic UI update → Firestore write with atomic balance updates
3. **Background Sync** → Real-time listener updates → UI reconciliation
4. **Offline Handling** → Queue writes → Sync on connectivity

### Data Synchronization Flow
1. **App Start** → Fetch Remote Config → Pre-populate user data
2. **Real-time** → Firestore listeners → State updates → UI refresh
3. **Offline** → Local cache operations → Write queue management
4. **Online** → Pending sync → Conflict resolution → State reconciliation

### Error Handling & Recovery Flow
1. **App Initialization** → GlobalErrorHandler.initialize() → Setup Crashlytics + global handlers
2. **Error Capture** → Flutter/Platform/Isolate errors → Automatic crash reporting with context
3. **UI Error Boundaries** → Widget errors → ErrorBoundaryWidget → Graceful fallback UI
4. **User Error Reporting** → ErrorService → User-friendly messages + crash logging
5. **Recovery Actions** → Retry mechanisms → Session restoration → User context preservation
6. **PII Protection** → LoggingService sanitization → Safe crash reporting → Privacy compliance

### Dashboard Analytics Pattern (NAVIGATION REFACTORING PROJECT)
1. **Enhanced Dashboard Architecture** → Comprehensive financial analytics with four main components
2. **Current Period Balance Card** → Monthly income/expense aggregation with navigation to transactions
3. **Recent Transactions Card** → Last 3 transactions display with "View All" functionality
4. **Top Categories Analytics** → Top 3 expense and income categories with visual indicators
5. **Provider Composition** → Dashboard providers aggregate data from multiple repositories
6. **Robust Icon Handling** → Backward compatibility for both integer codes and string names
7. **Error Handling** → Graceful fallbacks and loading states for all dashboard components
8. **Navigation Integration** → Card-based navigation to filtered views and detailed screens

### Modern Navigation Patterns (COMPLETED)
1. **Streamlined Navigation** → Removed traditional app drawer for modern mobile UX
2. **Card-Based Navigation** → Direct navigation from dashboard cards to filtered views
3. **FAB Integration** → Floating Action Buttons on list screens for quick access
4. **Material 3 Compliance** → Consistent design language across all navigation elements

### Modal Design Patterns (TIME PERIOD MODAL REDESIGN)
1. **Grid-Based Selection** → 3x4 month grid layout for intuitive date selection
2. **Header Enhancement** → Gradient backgrounds with current selection display
3. **Year Navigation** → Up/down arrow controls for year selection with validation
4. **Visual Feedback** → Circular selection indicators and current period dots
5. **State Preservation** → Maintains existing Riverpod state management integration
6. **Color Consistency** → App-specific color scheme adaptation for brand alignment
7. **Accessibility Compliance** → Proper touch targets and semantic labels for screen readers

### Global Text Overflow System (COMPLETED)
1. **Smart Text Widget (AppText)** → Context-aware text widget with automatic overflow configuration
2. **Context Detection** → Automatic detection of text context based on style properties (fontSize, fontWeight)
3. **Predefined Configurations** → Ready-to-use configurations for title, body, label, note, critical, button, listItem contexts
4. **Design Token Integration** → Seamlessly integrated with existing design tokens system
5. **Extension Methods** → Easy migration path from existing Text widgets through extension methods
6. **Adaptive Text Support** → Optional adaptive text scaling with FittedBox for responsive design
7. **Backward Compatibility** → Existing Text widgets continue to work while providing easy migration
8. **Theme System Integration** → Full compatibility with Material 3 theming and light/dark mode support

### AppBar Architecture System (COMPLETED)
1. **AppBarHelpers Factory Pattern** → Centralized factory methods for consistent AppBar creation across all screens using traditional Scaffold pattern
2. **TimePeriod Integration** → `createTimePeriodScrollableAppBar()` for time-dependent screens with TimePeriodSelector integration
3. **Standard AppBar Methods** → `createStandardScrollableAppBar()` for general screens with consistent styling and behavior
4. **Scaffold + SingleChildScrollView Pattern** → All screens use traditional Scaffold architecture with AppBar and SingleChildScrollView body
5. **Overscroll Behavior** → Both AppBar and content stretch together during overscroll for consistent user experience
6. **Widget Pattern Conversion** → Systematic conversion from sliver widgets (SliverToBoxAdapter, SliverList, SliverFillRemaining) to regular widgets
7. **Material 3 Compliance** → Consistent Material 3 design language across all AppBar implementations with proper elevation and theming
8. **Test Infrastructure** → Comprehensive test coverage for AppBar functionality with proper widget testing patterns

---
*For detailed implementation history and technical achievements, see `docs/implementation-history.md`*
