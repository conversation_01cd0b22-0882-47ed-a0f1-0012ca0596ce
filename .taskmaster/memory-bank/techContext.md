# Tech Context: BudApp

## Technologies Used

### Frontend Stack
- **Flutter SDK**: Latest stable version with Dart 3
- **State Management**: Riverpod (AsyncNotifier/Notifier pattern)
- **Navigation**: go_router for declarative routing
- **UI Framework**: Material 3 & Cupertino widgets
- **Forms**: Flutter Form/TextFormField + flutter_hooks
- **Local Storage**: shared_preferences (settings), SecureStorageService with flutter_secure_storage (sensitive data)
- **Internationalization**: flutter_localizations with comprehensive app_en.arb (100+ keys)

### Backend Services (Firebase)
- **Database**: Cloud Firestore with offline persistence
- **Authentication**: Firebase Auth (email/password, OAuth Google/Apple)
- **Configuration**: Firebase Remote Config for feature flags and app settings
- **Messaging**: Firebase Cloud Messaging (FCM) for notifications
- **Analytics**: Firebase Analytics, Crashlytics, Performance Monitoring
- **Security**: Firestore Security Rules for data access control

### Development Tools
- **Build System**: GitHub Actions CI/CD pipeline
- **Testing**: flutter_test, integration_test, Firebase Emulator Suite
- **Code Quality**: very_good_analysis, dart analyze
- **Code Generation**: build_runner, json_serializable, freezed, riverpod_generator
- **Design**: flutter_launcher_icons, flutter_native_splash

### Testing Infrastructure
- **Comprehensive Test Suite**: 554+ tests across unit, widget, integration, and repository layers
- **Firebase Testing Foundation**: firebase_auth_mocks + fake_cloud_firestore for reliable Firebase simulation
- **FirebaseTestSetup**: Central utility for creating Firebase test environments with authenticated users
- **Testing Patterns by Layer**: Repository (FirebaseTestSetup), Service (mocktail), Widget (MockProviders), Integration (FirebaseTestSetup)
- **Security Rules Ready**: Foundation prepared for comprehensive security rules testing with proper auth context
- **Firebase Emulator Integration**: Auth (port 9099) and Firestore (port 8080) for integration testing
- **Test Coverage**: Authentication, repositories, services, widgets, and integration flows with real Firebase behavior simulation
- **Quality Gates**: flutter test and flutter analyze validation in CI/CD pipeline
- **Team Documentation**: Comprehensive guides for maintaining and extending Firebase testing patterns

### Third-Party Services
- **Subscriptions**: RevenueCat for premium tier management
- **Authentication**: Google OAuth, Apple Sign-In
- **Biometrics**: local_auth for fingerprint/face recognition
- **CSV Import**: csv package for data import functionality

## Development Setup

### Environment Configuration
- **Development**: Firebase development project with emulators
- **Staging**: Firebase staging project for pre-production testing  
- **Production**: Firebase production project for live app
- **Local Development**: Firebase Emulator Suite for offline development

### Firebase Project Setup
- **Projects**: Separate Firebase projects for dev/staging/production
- **Security Rules**: Firestore rules for user data isolation and validation
- **Remote Config**: Feature flags, premium limits, predefined categories
- **Indexes**: Composite indexes for efficient querying

### Firebase Deployment Requirements
⚠️ **CRITICAL**: Firebase CLI requires specific file locations for deployment:
- **`firestore.rules`** - MUST be in project root directory
- **`firestore.indexes.json`** - MUST be in project root directory
- **`firebase.json`** - MUST be in project root directory

## Technical Constraints

### Firebase Limitations
- **Query Constraints**: Firestore not suited for complex aggregate queries
- **Reporting Limitations**: Limited ad-hoc reporting capabilities, requires materialized views
- **Cost Considerations**: Document reads/writes can scale with usage
- **Offline Data Limits**: Large datasets may hit device storage constraints
- **Real-time Listener Costs**: Active listeners can be expensive at scale

### Mobile Development Constraints
- **Platform Differences**: iOS vs Android specific implementations
- **Device Performance**: Must support mid-range devices (Pixel 6a, iPhone 12)
- **Storage Limitations**: Offline cache size management required
- **Battery Optimization**: Background sync must be efficient
- **App Store Policies**: Subscription flows must comply with platform requirements

### Development Constraints
- **Flutter Learning Curve**: Team expertise in Flutter/Dart ecosystem
- **Firebase Vendor Lock-in**: Architecture tied to Firebase services
- **Testing Complexity**: Firebase Emulator setup for comprehensive testing
- **Deployment Coordination**: Multiple environments and platform stores

## Dependencies

### Core Flutter Dependencies
- **Firebase Core**: firebase_core, firebase_auth, cloud_firestore, firebase_remote_config
- **State Management**: flutter_riverpod, riverpod_annotation
- **Data Models**: freezed, json_annotation, json_serializable
- **Navigation**: go_router
- **UI/UX**: flutter_localizations, shared_preferences, flutter_secure_storage
- **Authentication**: google_sign_in, sign_in_with_apple, local_auth

### Development Dependencies
- **Code Generation**: build_runner, riverpod_generator, freezed, json_serializable
- **Testing**: flutter_test, integration_test, mockito, build_runner
- **Analysis & Quality**: very_good_analysis

### External Dependencies
- **RevenueCat**: Subscription management and premium feature control
- **Firebase Services**: All backend functionality depends on Firebase availability
- **Platform Services**: Google Play Services, Apple App Store for authentication and purchases
- **Device APIs**: Biometric authentication, secure storage, notifications

## Tool Usage Patterns

### Development Workflow
1. **Local Development**: Firebase Emulators for offline development
2. **Code Generation**: build_runner for models and providers
3. **Testing**: Firebase Emulator Suite + flutter_test for comprehensive testing
4. **Analysis**: Continuous dart analyze and very_good_analysis linting
5. **Deployment**: GitHub Actions for automated builds and Firebase deployment

### Firebase Management
- **Security Rules**: Version-controlled rules with emulator testing
- **Remote Config**: Environment-specific configuration with gradual rollout
- **Database Design**: Document-based schema with composite indexes
- **Monitoring**: Real-time analytics and crash reporting

### State Management Patterns
- **Riverpod Providers**: AsyncNotifierProvider for data fetching
- **Repository Pattern**: Clean separation between UI and data layers
- **Cache Management**: Smart caching with TTL for computed values
- **Real-time Updates**: Firestore listeners integrated with Riverpod

### Atomic Transaction Implementation
- **Firestore Transactions**: Used for financial operations requiring atomicity
- **Transaction Type-Specific Logic**: Dedicated methods for each transaction type
- **Error Handling**: Comprehensive validation and rollback capabilities

### Testing Strategy
- **Unit Tests**: Business logic and utility functions
- **Widget Tests**: UI components and user interactions  
- **Integration Tests**: End-to-end user flows with Firebase Emulators
- **Performance Tests**: Startup time, UI responsiveness, memory usage
- **Security Tests**: Firestore Security Rules validation

### Build and Deployment
- **CI/CD Pipeline**: GitHub Actions with automated testing and builds
- **Code Quality Gates**: Linting, testing, and security checks
- **Multi-Environment**: Automated deployment to dev/staging/production
- **App Store Deployment**: TestFlight (iOS) and Google Play Internal Testing (Android)

---
*For detailed implementation history and technical achievements, see `docs/implementation-history.md`*
