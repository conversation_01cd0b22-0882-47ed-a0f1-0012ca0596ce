{"indexes": [{"collectionGroup": "categories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "sortOrder", "order": "ASCENDING"}]}, {"collectionGroup": "categories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "parentId", "order": "ASCENDING"}, {"fieldPath": "sortOrder", "order": "ASCENDING"}]}, {"collectionGroup": "categories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "parentId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "sortOrder", "order": "ASCENDING"}]}, {"collectionGroup": "categories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "sortOrder", "order": "ASCENDING"}]}, {"collectionGroup": "categories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "sortOrder", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "categories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "sortOrder", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "transactionDate", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "transactionDate", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "transactionDate", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "categoryId", "order": "ASCENDING"}, {"fieldPath": "transactionDate", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "fromAccountId", "order": "ASCENDING"}, {"fieldPath": "transactionDate", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "toAccountId", "order": "ASCENDING"}, {"fieldPath": "transactionDate", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "tags", "order": "ASCENDING"}, {"fieldPath": "transactionDate", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "amountCents", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "categoryId", "order": "ASCENDING"}, {"fieldPath": "transactionDate", "order": "DESCENDING"}]}, {"collectionGroup": "budgets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "budgets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "period", "order": "ASCENDING"}, {"fieldPath": "periodStart", "order": "ASCENDING"}]}, {"collectionGroup": "budgets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "period", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "periodStart", "order": "DESCENDING"}]}, {"collectionGroup": "budgets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "period", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "categoryId", "order": "ASCENDING"}, {"fieldPath": "periodStart", "order": "DESCENDING"}]}, {"collectionGroup": "budgets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "categoryId", "order": "ASCENDING"}, {"fieldPath": "period", "order": "ASCENDING"}, {"fieldPath": "periodStart", "order": "ASCENDING"}]}, {"collectionGroup": "budgets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "categoryId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "period", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "periodStart", "order": "DESCENDING"}]}, {"collectionGroup": "goals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "goals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "contributions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "contributionDate", "order": "DESCENDING"}]}, {"collectionGroup": "accounts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "accounts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "accounts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "classification", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "accounts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "isPrimary", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "accounts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}], "fieldOverrides": []}