import 'package:flutter/material.dart';

/// Defines different text contexts for automatic overflow handling
enum TextContext {
  /// Large headings and titles - single line with ellipsis
  title,

  /// Body text and descriptions - multi-line with ellipsis
  body,

  /// Form labels and small text - single line with ellipsis
  label,

  /// Critical information like amounts - adaptive scaling or wrap
  critical,

  /// Secondary information and notes - multi-line with fade
  note,

  /// Button text - single line with ellipsis or scaling
  button,

  /// List item text - context-dependent
  listItem,
}

/// Defines different overflow handling strategies
enum OverflowStrategy {
  /// Standard "..." truncation
  ellipsis,

  /// Gradient fade at the end
  fade,

  /// Hard cut-off
  clip,

  /// Allow overflow (for debugging)
  visible,

  /// Reduce font size to fit
  scale,

  /// Allow text wrapping
  wrap,
}

/// Configuration class for text overflow behavior
class TextOverflowConfig {
  const TextOverflowConfig({
    this.maxLines,
    this.overflow = TextOverflow.ellipsis,
    this.softWrap = true,
    this.minScaleFactor,
    this.maxScaleFactor,
    this.fadeLength,
    this.adaptive = false,
  });

  /// Maximum number of lines before overflow
  final int? maxLines;

  /// Type of overflow handling
  final TextOverflow overflow;

  /// Whether to allow soft line breaks
  final bool softWrap;

  /// Minimum scale factor for adaptive scaling
  final double? minScaleFactor;

  /// Maximum scale factor for adaptive scaling
  final double? maxScaleFactor;

  /// Length of fade effect (for fade overflow)
  final double? fadeLength;

  /// Whether this configuration is adaptive to content
  final bool adaptive;

  /// Creates a copy with modified properties
  TextOverflowConfig copyWith({
    int? maxLines,
    TextOverflow? overflow,
    bool? softWrap,
    double? minScaleFactor,
    double? maxScaleFactor,
    double? fadeLength,
    bool? adaptive,
  }) {
    return TextOverflowConfig(
      maxLines: maxLines ?? this.maxLines,
      overflow: overflow ?? this.overflow,
      softWrap: softWrap ?? this.softWrap,
      minScaleFactor: minScaleFactor ?? this.minScaleFactor,
      maxScaleFactor: maxScaleFactor ?? this.maxScaleFactor,
      fadeLength: fadeLength ?? this.fadeLength,
      adaptive: adaptive ?? this.adaptive,
    );
  }
}

/// Predefined text overflow configurations for different contexts
// ignore: avoid_classes_with_only_static_members
class AppTextOverflow {
  /// Configuration for title text - single line with ellipsis
  static const title = TextOverflowConfig(
    maxLines: 1,
    overflow: TextOverflow.ellipsis,
    softWrap: false,
  );

  /// Configuration for body text - multi-line with ellipsis
  static const body = TextOverflowConfig(
    maxLines: 3,
    overflow: TextOverflow.ellipsis,
    softWrap: true,
  );

  /// Configuration for label text - single line with ellipsis
  static const label = TextOverflowConfig(
    maxLines: 1,
    overflow: TextOverflow.ellipsis,
    softWrap: false,
  );

  /// Configuration for critical text - adaptive with wrapping
  static const critical = TextOverflowConfig(
    overflow: TextOverflow.visible,
    softWrap: true,
    adaptive: true,
    minScaleFactor: 0.8,
    maxScaleFactor: 1.2,
  );

  /// Configuration for note text - multi-line with ellipsis
  static const note = TextOverflowConfig(
    maxLines: 2,
    overflow: TextOverflow.ellipsis,
    softWrap: true,
  );

  /// Configuration for button text - single line with scaling
  static const button = TextOverflowConfig(
    maxLines: 1,
    overflow: TextOverflow.ellipsis,
    softWrap: false,
    adaptive: true,
    minScaleFactor: 0.85,
  );

  /// Configuration for list item text - single line with ellipsis
  static const listItem = TextOverflowConfig(
    maxLines: 1,
    overflow: TextOverflow.ellipsis,
    softWrap: false,
  );

  /// Configuration for compact text - single line with ellipsis
  static const compact = TextOverflowConfig(
    maxLines: 1,
    overflow: TextOverflow.ellipsis,
    softWrap: false,
  );

  /// Configuration for expanded text - multi-line with ellipsis
  static const expanded = TextOverflowConfig(
    maxLines: 5,
    overflow: TextOverflow.ellipsis,
    softWrap: true,
  );

  /// Configuration for unlimited text - no overflow restrictions
  static const unlimited = TextOverflowConfig(
    overflow: TextOverflow.visible,
    softWrap: true,
  );

  /// Gets the appropriate configuration for a given context
  static TextOverflowConfig forContext(TextContext context) {
    switch (context) {
      case TextContext.title:
        return title;
      case TextContext.body:
        return body;
      case TextContext.label:
        return label;
      case TextContext.critical:
        return critical;
      case TextContext.note:
        return note;
      case TextContext.button:
        return button;
      case TextContext.listItem:
        return listItem;
    }
  }

  /// Automatically detects context based on TextStyle
  static TextOverflowConfig detectFromStyle(TextStyle? style) {
    if (style == null) return body;

    final fontSize = style.fontSize ?? 16.0;
    final fontWeight = style.fontWeight ?? FontWeight.normal;

    // Large text is likely a title
    if (fontSize >= 20.0) {
      return title;
    }

    // Bold text is likely a title or label
    if (fontWeight.index >= FontWeight.w600.index) {
      return fontSize >= 16.0 ? title : label;
    }

    // Small text is likely a note or label
    if (fontSize <= 12.0) {
      return note;
    }

    // Default to body text
    return body;
  }
}
