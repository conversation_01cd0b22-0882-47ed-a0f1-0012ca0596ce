import 'package:flutter/material.dart';

/// Design tokens for consistent theming across the BudApp application.
///
/// This class provides Material 3 color scheme tokens, typography scale,
/// spacing values, border radius, and elevation specifications for
/// consistent visual design throughout the application.
class AppColors {
  // Primary colors (Teal #01a2a1)
  /// The primary color for the application's brand identity.
  static const primary = Color(0xFF01A2A1);

  /// Lighter variant of the primary color for additional contrast.
  static const primaryLight = Color(0xFF4DD4D3);

  /// Darker variant of the primary color for depth and emphasis.
  static const primaryDark = Color(0xFF007372);

  /// Text color that provides good contrast against the primary color.
  static const onPrimary = Color(0xFFFFFFFF);

  /// Container background color for primary-themed surfaces.
  static const primaryContainer = Color(0xFFB2DFDE);

  /// Text color for content on primary container surfaces.
  static const onPrimaryContainer = Color(0xFF002020);

  // Secondary colors (Coral Red #fc2f20 as accent)
  /// Secondary color for accent elements and highlights.
  static const secondary = Color(0xFFFC2F20);

  /// Lighter variant of the secondary color.
  static const secondaryLight = Color(0xFFFF6B5B);

  /// Darker variant of the secondary color.
  static const secondaryDark = Color(0xFFC30000);

  /// Text color for content on secondary surfaces.
  static const onSecondary = Color(0xFFFFFFFF);

  /// Background color for secondary container surfaces.
  static const secondaryContainer = Color(0xFFFFDAD6);

  /// Text color for content on secondary container surfaces.
  static const onSecondaryContainer = Color(0xFF410002);

  // Tertiary colors (Complementary warm tone)
  /// Tertiary color for complementary warm tone accents.
  static const tertiary = Color(0xFFFF8F00);

  /// Lighter variant of the tertiary color.
  static const tertiaryLight = Color(0xFFFFBF47);

  /// Darker variant of the tertiary color.
  static const tertiaryDark = Color(0xFFC56000);

  /// Text color for content on tertiary surfaces.
  static const onTertiary = Color(0xFFFFFFFF);

  /// Background color for tertiary container surfaces.
  static const tertiaryContainer = Color(0xFFFFE0B2);

  /// Text color for content on tertiary container surfaces.
  static const onTertiaryContainer = Color(0xFF2A1800);

  // Error colors
  /// Primary error color for error states and warnings.
  static const error = Color(0xFFBA1A1A);

  /// Lighter variant of the error color.
  static const errorLight = Color(0xFFFFB4AB);

  /// Darker variant of the error color.
  static const errorDark = Color(0xFF93000A);

  /// Text color for content on error surfaces.
  static const onError = Color(0xFFFFFFFF);

  /// Background color for error container surfaces.
  static const errorContainer = Color(0xFFFFDAD6);

  /// Text color for content on error container surfaces.
  static const onErrorContainer = Color(0xFF410002);

  // Background colors - Light theme
  /// Primary background color for light theme.
  static const background = Color(0xFFFAFDFD);

  /// Text color for content on light theme background.
  static const onBackground = Color(0xFF191C1C);

  /// Surface color for light theme.
  static const surface = Color(0xFFFAFDFD);

  /// Text color for content on light theme surface.
  static const onSurface = Color(0xFF191C1C);

  /// Variant surface color for light theme.
  static const surfaceVariant = Color(0xFFDAE5E4);

  /// Text color for content on light theme surface variant.
  static const onSurfaceVariant = Color(0xFF3F4948);

  /// Tint color for light theme surfaces.
  static const surfaceTint = Color(0xFF01A2A1);

  // Background colors - Dark theme
  /// Primary background color for dark theme.
  static const backgroundDark = Color(0xFF0F1415);

  /// Text color for content on dark theme background.
  static const onBackgroundDark = Color(0xFFDDE3E3);

  /// Surface color for dark theme.
  static const surfaceDark = Color(0xFF0F1415);

  /// Text color for content on dark theme surface.
  static const onSurfaceDark = Color(0xFFDDE3E3);

  /// Variant surface color for dark theme.
  static const surfaceVariantDark = Color(0xFF3F4948);

  /// Text color for content on dark theme surface variant.
  static const onSurfaceVariantDark = Color(0xFFBEC9C8);

  // Outline colors
  /// Primary outline color for borders and dividers.
  static const outline = Color(0xFF6F7978);

  /// Variant outline color for subtle borders.
  static const outlineVariant = Color(0xFFBEC9C8);

  /// Primary outline color for dark theme borders.
  static const outlineDark = Color(0xFF899392);

  /// Variant outline color for dark theme subtle borders.
  static const outlineVariantDark = Color(0xFF3F4948);

  // Text colors (derived from surface colors)
  /// Primary text color for light theme.
  static const textPrimary = Color(0xFF191C1C);

  /// Secondary text color for light theme.
  static const textSecondary = Color(0xFF5F6363);

  /// Disabled text color for light theme.
  static const textDisabled = Color(0xFF9E9E9E);

  /// Hint text color for light theme.
  static const textHint = Color(0xFF9E9E9E);

  /// Primary text color for dark theme.
  static const textPrimaryDark = Color(0xFFDDE3E3);

  /// Secondary text color for dark theme.
  static const textSecondaryDark = Color(0xFFBEC9C8);

  // Splash screen background
  /// Background color for splash screen.
  static const splashBackground = Color(0xFF01A2A1);

  // Success colors (for financial positive indicators)
  /// Success color for financial positive indicators.
  static const success = Color(0xFF2E7D32);

  /// Lighter variant of the success color.
  static const successLight = Color(0xFF66BB6A);

  /// Darker variant of the success color.
  static const successDark = Color(0xFF1B5E20);

  /// Text color for content on success surfaces.
  static const onSuccess = Color(0xFFFFFFFF);

  // Warning colors (for financial alerts)
  /// Warning color for financial alerts.
  static const warning = Color(0xFFED6C02);

  /// Lighter variant of the warning color.
  static const warningLight = Color(0xFFFFB74D);

  /// Darker variant of the warning color.
  static const warningDark = Color(0xFFE65100);

  /// Text color for content on warning surfaces.
  static const onWarning = Color(0xFFFFFFFF);

  // Info colors (for neutral financial information)
  /// Info color for neutral financial information.
  static const info = Color(0xFF0288D1);

  /// Lighter variant of the info color.
  static const infoLight = Color(0xFF64B5F6);

  /// Darker variant of the info color.
  static const infoDark = Color(0xFF01579B);

  /// Text color for content on info surfaces.
  static const onInfo = Color(0xFFFFFFFF);

  // Neutral colors (for general UI elements)
  /// Neutral color for general UI elements.
  static const neutral = Color(0xFF6F7978);

  /// Lighter variant of the neutral color.
  static const neutralLight = Color(0xFF9E9E9E);

  /// Darker variant of the neutral color.
  static const neutralDark = Color(0xFF424242);
}

/// Spacing constants for consistent layout spacing throughout the app.
class AppSpacing {
  /// Extra small spacing (4px).
  static const xs = 4.0;

  /// Small spacing (8px).
  static const sm = 8.0;

  /// Medium spacing (16px).
  static const md = 16.0;

  /// Large spacing (24px).
  static const lg = 24.0;

  /// Extra large spacing (32px).
  static const xl = 32.0;

  /// Extra extra large spacing (48px).
  static const xxl = 48.0;
}

/// Typography constants for consistent text styling throughout the app.
class AppTypography {
  /// Default font family for the application.
  static const fontFamily = 'Inter';

  /// Extra small font size (12px).
  static const fontSizeXs = 12.0;

  /// Small font size (14px).
  static const fontSizeSm = 14.0;

  /// Medium font size (16px).
  static const fontSizeMd = 16.0;

  /// Large font size (18px).
  static const fontSizeLg = 18.0;

  /// Extra large font size (20px).
  static const fontSizeXl = 20.0;

  /// Extra extra large font size (24px).
  static const fontSizeXxl = 24.0;

  /// Extra extra extra large font size (30px).
  static const fontSizeXxxl = 30.0;

  /// Regular font weight (400).
  static const FontWeight fontWeightRegular = FontWeight.w400;

  /// Medium font weight (500).
  static const FontWeight fontWeightMedium = FontWeight.w500;

  /// Semi-bold font weight (600).
  static const FontWeight fontWeightSemiBold = FontWeight.w600;

  /// Bold font weight (700).
  static const FontWeight fontWeightBold = FontWeight.w700;
}

/// Text overflow configuration tokens for consistent text handling.
class AppTextOverflowTokens {
  // Maximum lines for different text contexts
  /// Maximum lines for single-line text contexts.
  static const maxLinesSingle = 1;

  /// Maximum lines for compact text contexts.
  static const maxLinesCompact = 2;

  /// Maximum lines for body text contexts.
  static const maxLinesBody = 3;

  /// Maximum lines for expanded text contexts.
  static const maxLinesExpanded = 5;

  // Scale factors for adaptive text
  /// Minimum scale factor for adaptive text.
  static const minScaleFactor = 0.8;

  /// Maximum scale factor for adaptive text.
  static const maxScaleFactor = 1.2;

  /// Minimum scale factor for button text.
  static const buttonMinScaleFactor = 0.85;

  // Fade effect lengths
  /// Short fade effect length.
  static const fadeShort = 20.0;

  /// Medium fade effect length.
  static const fadeMedium = 40.0;

  /// Long fade effect length.
  static const fadeLong = 60.0;

  // Common overflow behaviors
  /// Default text overflow behavior (ellipsis).
  static const TextOverflow defaultOverflow = TextOverflow.ellipsis;

  /// Clip text overflow behavior.
  static const TextOverflow clipOverflow = TextOverflow.clip;

  /// Visible text overflow behavior.
  static const TextOverflow visibleOverflow = TextOverflow.visible;

  /// Fade text overflow behavior.
  static const TextOverflow fadeOverflow = TextOverflow.fade;
}

/// Border radius constants for consistent corner rounding.
class AppBorderRadius {
  /// Extra small border radius (2px).
  static const xs = 2.0;

  /// Small border radius (4px).
  static const sm = 4.0;

  /// Medium border radius (8px).
  static const md = 8.0;

  /// Large border radius (12px).
  static const lg = 12.0;

  /// Extra large border radius (16px).
  static const xl = 16.0;

  /// Extra extra large border radius (24px).
  static const xxl = 24.0;

  /// Fully rounded border radius.
  static const round = 9999.0;
}

/// Elevation constants for consistent shadow and depth effects.
class AppElevation {
  /// No elevation (0px).
  static const none = 0.0;

  /// Small elevation (1px).
  static const sm = 1.0;

  /// Medium elevation (2px).
  static const md = 2.0;

  /// Large elevation (4px).
  static const lg = 4.0;

  /// Extra large elevation (8px).
  static const xl = 8.0;

  /// Extra extra large elevation (12px).
  static const xxl = 12.0;
}

/// Unified design tokens class for easy access to all design constants
class DesignTokens {
  // Spacing
  /// Spacing value of 2px.
  static const spacing2 = 2.0;

  /// Spacing value of 4px.
  static const double spacing4 = AppSpacing.xs;

  /// Spacing value of 8px.
  static const double spacing8 = AppSpacing.sm;

  /// Spacing value of 12px.
  static const double spacing12 = 12;

  /// Spacing value of 16px.
  static const double spacing16 = AppSpacing.md;

  /// Spacing value of 24px.
  static const double spacing24 = AppSpacing.lg;

  /// Spacing value of 32px.
  static const double spacing32 = AppSpacing.xl;

  /// Spacing value of 48px.
  static const double spacing48 = AppSpacing.xxl;

  // Border radius
  /// Border radius value of 4px.
  static const double borderRadius4 = AppBorderRadius.sm;

  /// Border radius value of 8px.
  static const double borderRadius8 = AppBorderRadius.md;

  /// Border radius value of 12px.
  static const double borderRadius12 = AppBorderRadius.lg;

  /// Border radius value of 16px.
  static const double borderRadius16 = AppBorderRadius.xl;

  /// Border radius value of 24px.
  static const double borderRadius24 = AppBorderRadius.xxl;

  // Elevation
  /// Elevation value of 0px.
  static const double elevation0 = AppElevation.none;

  /// Elevation value of 1px.
  static const double elevation1 = AppElevation.sm;

  /// Elevation value of 2px.
  static const double elevation2 = AppElevation.md;

  /// Elevation value of 4px.
  static const double elevation4 = AppElevation.lg;

  /// Elevation value of 8px.
  static const double elevation8 = AppElevation.xl;

  /// Elevation value of 12px.
  static const double elevation12 = AppElevation.xxl;
}
