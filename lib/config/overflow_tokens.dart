import 'package:flutter/material.dart';

import 'package:budapp/config/design_tokens.dart';

/// Device size categories for responsive design
enum DeviceSize {
  /// Mobile devices (< 600px width)
  mobile,

  /// Tablet devices (600px - 1200px width)
  tablet,

  /// Desktop devices (> 1200px width)
  desktop,
}

/// Different overflow handling strategies for widgets
enum OverflowStrategy {
  /// Standard ellipsis truncation
  ellipsis,

  /// Gradient fade at the end
  fade,

  /// Hard cut-off clipping
  clip,

  /// Allow overflow (for debugging)
  visible,

  /// Reduce size to fit content
  scale,

  /// Allow text/content wrapping
  wrap,

  /// Make content scrollable
  scroll,

  /// Adaptive strategy based on content and context
  adaptive,
}

/// Container constraint configuration for cards and dialogs
class ContainerConstraints {
  const ContainerConstraints({
    this.minHeight,
    this.maxHeight,
    this.minWidth,
    this.maxWidth,
    this.aspectRatio,
    this.responsiveHeights,
    this.responsiveWidths,
  });

  /// Minimum height constraint
  final double? minHeight;

  /// Maximum height constraint
  final double? maxHeight;

  /// Minimum width constraint
  final double? minWidth;

  /// Maximum width constraint
  final double? maxWidth;

  /// Preferred aspect ratio (width/height)
  final double? aspectRatio;

  /// Device-specific height constraints
  final Map<DeviceSize, double>? responsiveHeights;

  /// Device-specific width constraints
  final Map<DeviceSize, double>? responsiveWidths;

  /// Gets height for specific device size
  double? getHeightForDevice(DeviceSize deviceSize) {
    return responsiveHeights?[deviceSize] ?? maxHeight;
  }

  /// Gets width for specific device size
  double? getWidthForDevice(DeviceSize deviceSize) {
    return responsiveWidths?[deviceSize] ?? maxWidth;
  }
}

/// Grid layout constraint configuration
class GridConstraints {
  const GridConstraints({
    this.minChildHeight,
    this.maxChildHeight,
    this.minChildWidth,
    this.maxChildWidth,
    this.responsiveColumns,
    this.responsiveAspectRatios,
    this.spacing,
    this.runSpacing,
  });

  /// Minimum height for grid children
  final double? minChildHeight;

  /// Maximum height for grid children
  final double? maxChildHeight;

  /// Minimum width for grid children
  final double? minChildWidth;

  /// Maximum width for grid children
  final double? maxChildWidth;

  /// Number of columns per device size
  final Map<DeviceSize, int>? responsiveColumns;

  /// Aspect ratios per device size
  final Map<DeviceSize, double>? responsiveAspectRatios;

  /// Spacing between grid items
  final double? spacing;

  /// Spacing between grid rows
  final double? runSpacing;

  /// Gets column count for specific device size
  int getColumnsForDevice(DeviceSize deviceSize) {
    return responsiveColumns?[deviceSize] ?? 2;
  }

  /// Gets aspect ratio for specific device size
  double getAspectRatioForDevice(DeviceSize deviceSize) {
    return responsiveAspectRatios?[deviceSize] ?? 1;
  }
}

/// Scrollable region constraint configuration
class ScrollableConstraints {
  const ScrollableConstraints({
    this.maxHeight,
    this.maxWidth,
    this.enableScrollbar = true,
    this.fadeEdges = false,
    this.fadeLength,
    this.scrollDirection = Axis.vertical,
    this.physics,
  });

  /// Maximum height before scrolling
  final double? maxHeight;

  /// Maximum width before scrolling
  final double? maxWidth;

  /// Whether to show scrollbar
  final bool enableScrollbar;

  /// Whether to fade edges when scrolling
  final bool fadeEdges;

  /// Length of fade effect
  final double? fadeLength;

  /// Direction of scrolling
  final Axis scrollDirection;

  /// Custom scroll physics
  final ScrollPhysics? physics;
}

/// Form field overflow configuration
class FormFieldConstraints {
  const FormFieldConstraints({
    this.maxLines,
    this.minLines,
    this.maxHeight,
    this.textOverflow = TextOverflow.ellipsis,
    this.expandable = false,
    this.showCharacterCount = false,
    this.maxLength,
  });

  /// Maximum lines for text input
  final int? maxLines;

  /// Minimum lines for text input
  final int? minLines;

  /// Maximum height for field
  final double? maxHeight;

  /// Text overflow behavior
  final TextOverflow textOverflow;

  /// Whether field can expand
  final bool expandable;

  /// Whether to show character count
  final bool showCharacterCount;

  /// Maximum character length
  final int? maxLength;
}

/// Responsive breakpoint configuration
class ResponsiveBreakpoints {
  const ResponsiveBreakpoints({
    this.mobile = 600,
    this.tablet = 1200,
    this.desktop = 1920,
  });

  /// Mobile breakpoint (px)
  final double mobile;

  /// Tablet breakpoint (px)
  final double tablet;

  /// Desktop breakpoint (px)
  final double desktop;

  /// Gets device size based on screen width
  DeviceSize getDeviceSize(double width) {
    if (width < mobile) return DeviceSize.mobile;
    if (width < tablet) return DeviceSize.tablet;
    return DeviceSize.desktop;
  }
}

/// Comprehensive overflow configuration tokens for the entire application
class AppOverflowTokens {
  // Responsive breakpoints
  /// Default responsive breakpoints for device detection
  static const ResponsiveBreakpoints breakpoints = ResponsiveBreakpoints();

  // Container constraints
  /// Standard card constraints with responsive behavior
  static const ContainerConstraints cardConstraints = ContainerConstraints(
    minHeight: 80,
    maxHeight: 400,
    aspectRatio: 1.2,
    responsiveHeights: {
      DeviceSize.mobile: 300,
      DeviceSize.tablet: 350,
      DeviceSize.desktop: 400,
    },
  );

  /// Compact card constraints for dense layouts
  static const ContainerConstraints compactCardConstraints =
      ContainerConstraints(
        minHeight: 60,
        maxHeight: 200,
        aspectRatio: 1.5,
        responsiveHeights: {
          DeviceSize.mobile: 160,
          DeviceSize.tablet: 180,
          DeviceSize.desktop: 200,
        },
      );

  /// Dialog constraints for modal content
  static const ContainerConstraints dialogConstraints = ContainerConstraints(
    minHeight: 200,
    maxHeight: 600,
    minWidth: 280,
    maxWidth: 560,
    responsiveHeights: {
      DeviceSize.mobile: 400,
      DeviceSize.tablet: 500,
      DeviceSize.desktop: 600,
    },
  );

  /// Bottom sheet constraints
  static const ContainerConstraints bottomSheetConstraints =
      ContainerConstraints(
        minHeight: 200,
        maxHeight: 600,
        responsiveHeights: {
          DeviceSize.mobile: 400,
          DeviceSize.tablet: 500,
          DeviceSize.desktop: 600,
        },
      );

  // Grid constraints
  /// Feature grid constraints (like hub screen)
  static const GridConstraints featureGridConstraints = GridConstraints(
    minChildHeight: 120,
    maxChildHeight: 200,
    spacing: AppSpacing.md,
    runSpacing: AppSpacing.md,
    responsiveColumns: {
      DeviceSize.mobile: 2,
      DeviceSize.tablet: 3,
      DeviceSize.desktop: 4,
    },
    responsiveAspectRatios: {
      DeviceSize.mobile: 1.1,
      DeviceSize.tablet: 1.2,
      DeviceSize.desktop: 1.3,
    },
  );

  /// List grid constraints for dense content
  static const GridConstraints listGridConstraints = GridConstraints(
    minChildHeight: 80,
    maxChildHeight: 150,
    spacing: AppSpacing.sm,
    runSpacing: AppSpacing.sm,
    responsiveColumns: {
      DeviceSize.mobile: 1,
      DeviceSize.tablet: 2,
      DeviceSize.desktop: 3,
    },
    responsiveAspectRatios: {
      DeviceSize.mobile: 4,
      DeviceSize.tablet: 3,
      DeviceSize.desktop: 3.5,
    },
  );

  // Scrollable constraints
  /// Standard scrollable content constraints
  static const ScrollableConstraints scrollableConstraints =
      ScrollableConstraints(
        maxHeight: 300,
        enableScrollbar: true,
        fadeEdges: true,
        fadeLength: AppSpacing.lg,
      );

  /// Compact scrollable constraints for limited space
  static const ScrollableConstraints compactScrollableConstraints =
      ScrollableConstraints(
        maxHeight: 200,
        enableScrollbar: false,
        fadeEdges: true,
        fadeLength: AppSpacing.md,
      );

  /// Expanded scrollable constraints for large content
  static const ScrollableConstraints expandedScrollableConstraints =
      ScrollableConstraints(
        maxHeight: 500,
        enableScrollbar: true,
        fadeEdges: false,
      );

  // Form field constraints
  /// Standard form field constraints
  static const FormFieldConstraints formFieldConstraints = FormFieldConstraints(
    maxLines: 1,
    maxHeight: 56,
    textOverflow: TextOverflow.ellipsis,
  );

  /// Multi-line form field constraints
  static const FormFieldConstraints multiLineFormFieldConstraints =
      FormFieldConstraints(
        minLines: 3,
        maxLines: 6,
        maxHeight: 150,
        textOverflow: TextOverflow.ellipsis,
        expandable: true,
        showCharacterCount: true,
        maxLength: 500,
      );

  /// Compact form field constraints
  static const FormFieldConstraints compactFormFieldConstraints =
      FormFieldConstraints(
        maxLines: 1,
        maxHeight: 40,
        textOverflow: TextOverflow.ellipsis,
      );

  // Default overflow strategies by context
  /// Default overflow strategies for different widget types
  static const Map<Type, OverflowStrategy> defaultStrategies = {
    Card: OverflowStrategy.scroll,
    Container: OverflowStrategy.adaptive,
    Column: OverflowStrategy.scroll,
    Row: OverflowStrategy.wrap,
    GridView: OverflowStrategy.adaptive,
    ListView: OverflowStrategy.scroll,
    Text: OverflowStrategy.ellipsis,
    TextFormField: OverflowStrategy.ellipsis,
  };

  // Constraint helpers
  /// Gets appropriate container constraints based on context
  static ContainerConstraints getContainerConstraints(String context) {
    switch (context.toLowerCase()) {
      case 'card':
      case 'feature':
        return cardConstraints;
      case 'compact':
      case 'list':
        return compactCardConstraints;
      case 'dialog':
      case 'modal':
        return dialogConstraints;
      case 'bottomsheet':
      case 'sheet':
        return bottomSheetConstraints;
      default:
        return cardConstraints;
    }
  }

  /// Gets appropriate grid constraints based on context
  static GridConstraints getGridConstraints(String context) {
    switch (context.toLowerCase()) {
      case 'feature':
      case 'hub':
        return featureGridConstraints;
      case 'list':
      case 'compact':
        return listGridConstraints;
      default:
        return featureGridConstraints;
    }
  }

  /// Gets appropriate scrollable constraints based on context
  static ScrollableConstraints getScrollableConstraints(String context) {
    switch (context.toLowerCase()) {
      case 'compact':
        return compactScrollableConstraints;
      case 'expanded':
      case 'large':
        return expandedScrollableConstraints;
      default:
        return scrollableConstraints;
    }
  }

  /// Gets appropriate form field constraints based on context
  static FormFieldConstraints getFormFieldConstraints(String context) {
    switch (context.toLowerCase()) {
      case 'multiline':
      case 'textarea':
        return multiLineFormFieldConstraints;
      case 'compact':
        return compactFormFieldConstraints;
      default:
        return formFieldConstraints;
    }
  }

  /// Gets device size from current context
  static DeviceSize getDeviceSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return breakpoints.getDeviceSize(width);
  }

  /// Gets overflow strategy for widget type
  static OverflowStrategy getStrategyForType(Type widgetType) {
    return defaultStrategies[widgetType] ?? OverflowStrategy.adaptive;
  }
}
