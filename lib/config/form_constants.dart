/// Shared constants for form fields across the application
///
/// This file provides unified color palettes and icon sets that can be used
/// consistently across all form fields and entity types.
class FormConstants {
  /// Standard color palette for entity color selection
  ///
  /// This palette is designed to work well with both light and dark themes
  /// and provides good contrast for accessibility.
  static const List<String> standardColors = [
    '#F44336', // Red
    '#E91E63', // Pink
    '#9C27B0', // Purple
    '#673AB7', // Deep Purple
    '#3F51B5', // Indigo
    '#2196F3', // Blue
    '#03A9F4', // Light Blue
    '#00BCD4', // <PERSON>an
    '#009688', // Teal
    '#4CAF50', // Green
    '#8BC34A', // Light Green
    '#CDDC39', // Lime
    '#FFEB3B', // Yellow
    '#FFC107', // Amber
    '#FF9800', // Orange
    '#FF5722', // Deep Orange
    '#795548', // Brown
    '#607D8B', // <PERSON> Grey
  ];

  /// Standard icon set for entity icon selection
  ///
  /// This icon set covers common categories and use cases across
  /// different entity types (categories, accounts, tags, etc.).
  static const List<String> standardIcons = [
    // Financial icons
    'account_balance_wallet',
    'savings',
    'credit_card',
    'attach_money',
    'account_balance',
    'business',
    'paid',
    'payment',
    'local_atm',
    'currency_exchange',

    // Lifestyle icons
    'home',
    'directions_car',
    'shopping_cart',
    'restaurant',
    'local_gas_station',
    'medical_services',
    'school',
    'fitness_center',
    'movie',
    'music_note',
    'work',
    'flight',
    'hotel',
    'local_grocery_store',
    'local_pharmacy',
    'pets',
    'sports_esports',
    'directions_bike',

    // General purpose icons
    'category',
    'label',
    'bookmark',
    'folder',
    'star',
    'favorite',
    'analytics',
    'pie_chart',
    'bar_chart',
    'show_chart',
  ];

  /// Category-specific color palette
  ///
  /// Optimized for category visualization and grouping
  static const List<String> categoryColors = standardColors;

  /// Category-specific icon set
  ///
  /// Icons commonly used for expense and income categories
  static const List<String> categoryIcons = [
    'shopping_cart',
    'restaurant',
    'local_gas_station',
    'home',
    'directions_car',
    'medical_services',
    'school',
    'sports_esports',
    'movie',
    'fitness_center',
    'pets',
    'flight',
    'hotel',
    'work',
    'attach_money',
    'savings',
    'credit_card',
    'account_balance',
    'business',
    'local_grocery_store',
  ];

  /// Account-specific color palette
  ///
  /// Colors suitable for account visualization
  static const List<String> accountColors = standardColors;

  /// Account-specific icon set
  ///
  /// Icons commonly used for different account types
  static const List<String> accountIcons = [
    'account_balance_wallet',
    'savings',
    'credit_card',
    'account_balance',
    'business',
    'paid',
    'local_atm',
    'currency_exchange',
    'home',
    'work',
  ];

  /// Tag-specific color palette
  ///
  /// Colors suitable for tag visualization
  static const List<String> tagColors = standardColors;

  /// Tag-specific icon set
  ///
  /// Icons commonly used for tags and labels
  static const List<String> tagIcons = [
    'label',
    'bookmark',
    'category',
    'folder',
    'star',
    'favorite',
    'analytics',
    'pie_chart',
    'bar_chart',
    'show_chart',
  ];

  /// Goal-specific color palette
  ///
  /// Colors suitable for goal visualization and progress tracking
  static const List<String> goalColors = standardColors;

  /// Goal-specific icon set
  ///
  /// Icons commonly used for financial goals and savings targets
  static const List<String> goalIcons = [
    'savings',
    'account_balance',
    'trending_up',
    'star',
    'flag',
    'home',
    'directions_car',
    'flight',
    'school',
    'medical_services',
    'fitness_center',
    'shopping_cart',
    'business',
    'attach_money',
    'credit_card',
    'wallet',
    'piggy_bank',
    'target',
    'trophy',
    'diamond',
  ];
}
