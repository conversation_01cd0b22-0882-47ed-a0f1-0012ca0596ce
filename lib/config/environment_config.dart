import 'package:budapp/firebase_options_dev.dart';
import 'package:budapp/firebase_options_prod.dart';
import 'package:budapp/firebase_options_staging.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Environment configuration that detects the current flavor and provides
/// environment-specific settings for Firebase, theming, and app metadata.
// ignore: avoid_classes_with_only_static_members
class EnvironmentConfig {
  static const String _flavor = String.fromEnvironment(
    'FLAVOR',
    defaultValue: 'dev',
  );

  /// Current environment flavor (dev, staging, prod)
  static String get flavor => _flavor;

  /// Environment display name
  static String get environmentName {
    switch (_flavor) {
      case 'staging':
        return 'Staging';
      case 'prod':
        return 'Production';
      case 'dev':
      default:
        return 'Development';
    }
  }

  /// App title for the current environment
  static String get appTitle {
    switch (_flavor) {
      case 'staging':
        return 'BudApp Staging';
      case 'prod':
        return 'BudApp';
      case 'dev':
      default:
        return 'BudApp Dev';
    }
  }

  /// Theme color for the current environment
  static Color get themeColor {
    switch (_flavor) {
      case 'staging':
        return Colors.blue;
      case 'prod':
        return Colors.green;
      case 'dev':
      default:
        return Colors.orange;
    }
  }

  /// Firebase project name for display
  static String get firebaseProjectName {
    switch (_flavor) {
      case 'staging':
        return 'budapp-staging-1';
      case 'prod':
        return 'budapp-prod';
      case 'dev':
      default:
        return 'budapp-dev';
    }
  }

  /// Firebase options for the current environment
  static FirebaseOptions get firebaseOptions {
    switch (_flavor) {
      case 'staging':
        return FirebaseOptionsStaging.currentPlatform;
      case 'prod':
        return FirebaseOptionsProd.currentPlatform;
      case 'dev':
      default:
        return FirebaseOptionsDev.currentPlatform;
    }
  }

  /// Whether to show Firebase testing features
  /// Available in all environments for debugging purposes
  static bool get showFirebaseTesting => true;

  /// Whether this is a development environment
  static bool get isDevelopment => _flavor == 'dev';

  /// Whether this is a staging environment
  static bool get isStaging => _flavor == 'staging';

  /// Whether this is a production environment
  static bool get isProduction => _flavor == 'prod';

  /// Debug print message for Firebase initialization
  static String get firebaseInitMessage =>
      'Firebase initialized successfully for $environmentName environment';

  /// App home page title
  static String get homePageTitle => '$appTitle - $environmentName';

  /// Whether to use debug App Check provider
  static bool get useDebugAppCheckProvider => isDevelopment || isStaging;

  /// App Check web reCAPTCHA site key (placeholder for production)
  static String get appCheckWebRecaptchaSiteKey {
    // In production, this should be configured via environment variables
    // For now, using debug provider for all environments
    return 'debug-site-key';
  }

  /// Get Android App Check provider based on environment
  static AndroidProvider get androidAppCheckProvider {
    if (useDebugAppCheckProvider) {
      return AndroidProvider.debug;
    }
    // In production, use Play Integrity provider
    return AndroidProvider.playIntegrity;
  }

  /// Get Apple App Check provider based on environment
  static AppleProvider get appleAppCheckProvider {
    if (useDebugAppCheckProvider) {
      return AppleProvider.debug;
    }
    // In production, use App Attest provider with Device Check fallback
    return AppleProvider.appAttestWithDeviceCheckFallback;
  }

  /// Initialize Firebase App Check with environment-specific configuration
  static Future<void> initializeAppCheck() async {
    try {
      await FirebaseAppCheck.instance.activate(
        // Web reCAPTCHA provider (for web support)
        webProvider: kIsWeb
            ? ReCaptchaV3Provider(appCheckWebRecaptchaSiteKey)
            : null,
        // Android provider based on environment
        androidProvider: androidAppCheckProvider,
        // Apple provider based on environment
        appleProvider: appleAppCheckProvider,
      );

      debugPrint(
        'Firebase App Check activated for $environmentName environment',
      );
      debugPrint('Using debug provider: $useDebugAppCheckProvider');
    } on Exception catch (e) {
      debugPrint('Error activating Firebase App Check: $e');
      // Continue app execution even if App Check fails
    }
  }

  /// Environment summary for debugging
  static Map<String, dynamic> get environmentSummary => {
    'flavor': flavor,
    'environment': environmentName,
    'appTitle': appTitle,
    'firebaseProject': firebaseProjectName,
    'themeColor': themeColor.toString(),
    'isDevelopment': isDevelopment,
    'isStaging': isStaging,
    'isProduction': isProduction,
    'appCheckDebugMode': useDebugAppCheckProvider,
  };
}
