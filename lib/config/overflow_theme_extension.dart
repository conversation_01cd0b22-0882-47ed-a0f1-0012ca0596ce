import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/config/overflow_tokens.dart';
import 'package:flutter/material.dart';

/// Theme extension for global overflow handling configuration
///
/// This extension allows overflow settings to be configured globally
/// and accessed through Theme.of(context).extension&lt;OverflowThemeExtension&gt;()
@immutable
class OverflowThemeExtension extends ThemeExtension<OverflowThemeExtension> {
  const OverflowThemeExtension({
    this.defaultStrategy = OverflowStrategy.adaptive,
    this.autoApplyOverflowHandling = true,
    this.cardConstraints = AppOverflowTokens.cardConstraints,
    this.gridConstraints = AppOverflowTokens.featureGridConstraints,
    this.scrollableConstraints = AppOverflowTokens.scrollableConstraints,
    this.formFieldConstraints = AppOverflowTokens.formFieldConstraints,
    this.responsiveBreakpoints = AppOverflowTokens.breakpoints,
    this.enableDebugMode = false,
  });

  /// Gets the current overflow theme from context with fallback
  factory OverflowThemeExtension.defaultTheme(BuildContext context) {
    return of(context) ?? const OverflowThemeExtension();
  }

  /// Default overflow strategy for widgets without explicit configuration
  final OverflowStrategy defaultStrategy;

  /// Whether to automatically apply overflow handling to widgets
  final bool autoApplyOverflowHandling;

  /// Default container constraints for cards and containers
  final ContainerConstraints cardConstraints;

  /// Default grid constraints for grid layouts
  final GridConstraints gridConstraints;

  /// Default scrollable constraints for scrollable content
  final ScrollableConstraints scrollableConstraints;

  /// Default form field constraints for input fields
  final FormFieldConstraints formFieldConstraints;

  /// Responsive breakpoints for device size detection
  final ResponsiveBreakpoints responsiveBreakpoints;

  /// Whether to enable debug mode with overflow indicators
  final bool enableDebugMode;

  /// Gets the current overflow theme from context
  static OverflowThemeExtension? of(BuildContext context) {
    return Theme.of(context).extension<OverflowThemeExtension>();
  }

  @override
  OverflowThemeExtension copyWith({
    OverflowStrategy? defaultStrategy,
    bool? autoApplyOverflowHandling,
    ContainerConstraints? cardConstraints,
    GridConstraints? gridConstraints,
    ScrollableConstraints? scrollableConstraints,
    FormFieldConstraints? formFieldConstraints,
    ResponsiveBreakpoints? responsiveBreakpoints,
    bool? enableDebugMode,
  }) {
    return OverflowThemeExtension(
      defaultStrategy: defaultStrategy ?? this.defaultStrategy,
      autoApplyOverflowHandling:
          autoApplyOverflowHandling ?? this.autoApplyOverflowHandling,
      cardConstraints: cardConstraints ?? this.cardConstraints,
      gridConstraints: gridConstraints ?? this.gridConstraints,
      scrollableConstraints:
          scrollableConstraints ?? this.scrollableConstraints,
      formFieldConstraints: formFieldConstraints ?? this.formFieldConstraints,
      responsiveBreakpoints:
          responsiveBreakpoints ?? this.responsiveBreakpoints,
      enableDebugMode: enableDebugMode ?? this.enableDebugMode,
    );
  }

  @override
  OverflowThemeExtension lerp(OverflowThemeExtension? other, double t) {
    if (other == null) return this;

    return OverflowThemeExtension(
      defaultStrategy: t < 0.5 ? defaultStrategy : other.defaultStrategy,
      autoApplyOverflowHandling: t < 0.5
          ? autoApplyOverflowHandling
          : other.autoApplyOverflowHandling,
      cardConstraints: t < 0.5 ? cardConstraints : other.cardConstraints,
      gridConstraints: t < 0.5 ? gridConstraints : other.gridConstraints,
      scrollableConstraints: t < 0.5
          ? scrollableConstraints
          : other.scrollableConstraints,
      formFieldConstraints: t < 0.5
          ? formFieldConstraints
          : other.formFieldConstraints,
      responsiveBreakpoints: t < 0.5
          ? responsiveBreakpoints
          : other.responsiveBreakpoints,
      enableDebugMode: t < 0.5 ? enableDebugMode : other.enableDebugMode,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other.runtimeType != runtimeType) return false;

    return other is OverflowThemeExtension &&
        other.defaultStrategy == defaultStrategy &&
        other.autoApplyOverflowHandling == autoApplyOverflowHandling &&
        other.cardConstraints == cardConstraints &&
        other.gridConstraints == gridConstraints &&
        other.scrollableConstraints == scrollableConstraints &&
        other.formFieldConstraints == formFieldConstraints &&
        other.responsiveBreakpoints == responsiveBreakpoints &&
        other.enableDebugMode == enableDebugMode;
  }

  @override
  int get hashCode {
    return Object.hash(
      defaultStrategy,
      autoApplyOverflowHandling,
      cardConstraints,
      gridConstraints,
      scrollableConstraints,
      formFieldConstraints,
      responsiveBreakpoints,
      enableDebugMode,
    );
  }

  @override
  String toString() {
    return 'OverflowThemeExtension('
        'defaultStrategy: $defaultStrategy, '
        'autoApplyOverflowHandling: $autoApplyOverflowHandling, '
        'enableDebugMode: $enableDebugMode'
        ')';
  }
}

/// Enhanced app theme with overflow handling integration
class AppThemeWithOverflow {
  /// Creates light theme with overflow extensions
  static ThemeData createLightTheme({
    OverflowThemeExtension? overflowExtension,
  }) {
    return ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.light,
      ),
      useMaterial3: true,
      fontFamily: AppTypography.fontFamily,
      textTheme: _createTextTheme(Brightness.light),
      inputDecorationTheme: _createInputDecorationTheme(Brightness.light),
      extensions: [
        overflowExtension ?? const OverflowThemeExtension(),
      ],
    );
  }

  /// Creates dark theme with overflow extensions
  static ThemeData createDarkTheme({
    OverflowThemeExtension? overflowExtension,
  }) {
    return ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.dark,
      ),
      useMaterial3: true,
      fontFamily: AppTypography.fontFamily,
      textTheme: _createTextTheme(Brightness.dark),
      inputDecorationTheme: _createInputDecorationTheme(Brightness.dark),
      extensions: [
        overflowExtension ?? const OverflowThemeExtension(),
      ],
    );
  }

  /// Creates text theme with overflow-aware configurations
  static TextTheme _createTextTheme(Brightness brightness) {
    final baseColor = brightness == Brightness.light
        ? AppColors.textPrimary
        : AppColors.textPrimaryDark;

    return TextTheme(
      displayLarge: TextStyle(
        fontSize: AppTypography.fontSizeXxxl,
        fontWeight: AppTypography.fontWeightBold,
        color: baseColor,
        height: 1.2,
        overflow: TextOverflow.ellipsis,
      ),
      displayMedium: TextStyle(
        fontSize: AppTypography.fontSizeXxl,
        fontWeight: AppTypography.fontWeightBold,
        color: baseColor,
        height: 1.3,
        overflow: TextOverflow.ellipsis,
      ),
      displaySmall: TextStyle(
        fontSize: AppTypography.fontSizeXl,
        fontWeight: AppTypography.fontWeightSemiBold,
        color: baseColor,
        height: 1.3,
        overflow: TextOverflow.ellipsis,
      ),
      headlineLarge: TextStyle(
        fontSize: AppTypography.fontSizeXl,
        fontWeight: AppTypography.fontWeightSemiBold,
        color: baseColor,
        height: 1.3,
        overflow: TextOverflow.ellipsis,
      ),
      headlineMedium: TextStyle(
        fontSize: AppTypography.fontSizeLg,
        fontWeight: AppTypography.fontWeightSemiBold,
        color: baseColor,
        height: 1.4,
        overflow: TextOverflow.ellipsis,
      ),
      headlineSmall: TextStyle(
        fontSize: AppTypography.fontSizeMd,
        fontWeight: AppTypography.fontWeightSemiBold,
        color: baseColor,
        height: 1.4,
        overflow: TextOverflow.ellipsis,
      ),
      titleLarge: TextStyle(
        fontSize: AppTypography.fontSizeLg,
        fontWeight: AppTypography.fontWeightMedium,
        color: baseColor,
        height: 1.4,
        overflow: TextOverflow.ellipsis,
      ),
      titleMedium: TextStyle(
        fontSize: AppTypography.fontSizeMd,
        fontWeight: AppTypography.fontWeightMedium,
        color: baseColor,
        height: 1.5,
        overflow: TextOverflow.ellipsis,
      ),
      titleSmall: TextStyle(
        fontSize: AppTypography.fontSizeSm,
        fontWeight: AppTypography.fontWeightMedium,
        color: baseColor,
        height: 1.5,
        overflow: TextOverflow.ellipsis,
      ),
      bodyLarge: TextStyle(
        fontSize: AppTypography.fontSizeMd,
        fontWeight: AppTypography.fontWeightRegular,
        color: baseColor,
        height: 1.6,
        overflow: TextOverflow.ellipsis,
      ),
      bodyMedium: TextStyle(
        fontSize: AppTypography.fontSizeSm,
        fontWeight: AppTypography.fontWeightRegular,
        color: baseColor,
        height: 1.6,
        overflow: TextOverflow.ellipsis,
      ),
      bodySmall: TextStyle(
        fontSize: AppTypography.fontSizeXs,
        fontWeight: AppTypography.fontWeightRegular,
        color: brightness == Brightness.light
            ? AppColors.textSecondary
            : AppColors.textSecondaryDark,
        height: 1.6,
        overflow: TextOverflow.ellipsis,
      ),
      labelLarge: TextStyle(
        fontSize: AppTypography.fontSizeSm,
        fontWeight: AppTypography.fontWeightMedium,
        color: baseColor,
        height: 1.4,
        overflow: TextOverflow.ellipsis,
      ),
      labelMedium: TextStyle(
        fontSize: AppTypography.fontSizeXs,
        fontWeight: AppTypography.fontWeightMedium,
        color: baseColor,
        height: 1.4,
        overflow: TextOverflow.ellipsis,
      ),
      labelSmall: TextStyle(
        fontSize: AppTypography.fontSizeXs,
        fontWeight: AppTypography.fontWeightRegular,
        color: brightness == Brightness.light
            ? AppColors.textSecondary
            : AppColors.textSecondaryDark,
        height: 1.4,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Creates input decoration theme with overflow handling
  static InputDecorationTheme _createInputDecorationTheme(
    Brightness brightness,
  ) {
    return InputDecorationTheme(
      filled: true,
      isDense: true,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: const BorderSide(color: AppColors.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: const BorderSide(color: AppColors.error, width: 2),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: const BorderSide(color: AppColors.error, width: 2),
      ),
    );
  }
}

/// Overflow theme data for development and testing
class OverflowThemePresets {
  /// Default production theme with balanced overflow handling
  static const OverflowThemeExtension production = OverflowThemeExtension(
    defaultStrategy: OverflowStrategy.adaptive,
    autoApplyOverflowHandling: true,
    enableDebugMode: false,
  );

  /// Development theme with debug indicators enabled
  static const OverflowThemeExtension development = OverflowThemeExtension(
    defaultStrategy: OverflowStrategy.adaptive,
    autoApplyOverflowHandling: true,
    enableDebugMode: true,
  );

  /// Conservative theme with minimal overflow handling
  static const OverflowThemeExtension conservative = OverflowThemeExtension(
    defaultStrategy: OverflowStrategy.ellipsis,
    autoApplyOverflowHandling: false,
    enableDebugMode: false,
  );

  /// Aggressive theme with maximum overflow protection
  static const OverflowThemeExtension aggressive = OverflowThemeExtension(
    defaultStrategy: OverflowStrategy.scroll,
    autoApplyOverflowHandling: true,
    cardConstraints: AppOverflowTokens.compactCardConstraints,
    scrollableConstraints: AppOverflowTokens.compactScrollableConstraints,
    enableDebugMode: false,
  );

  /// Mobile-optimized theme for small screens
  static const OverflowThemeExtension mobile = OverflowThemeExtension(
    defaultStrategy: OverflowStrategy.adaptive,
    autoApplyOverflowHandling: true,
    cardConstraints: AppOverflowTokens.compactCardConstraints,
    scrollableConstraints: AppOverflowTokens.compactScrollableConstraints,
    enableDebugMode: false,
  );

  /// Desktop-optimized theme for large screens
  static const OverflowThemeExtension desktop = OverflowThemeExtension(
    defaultStrategy: OverflowStrategy.wrap,
    autoApplyOverflowHandling: true,
    scrollableConstraints: AppOverflowTokens.expandedScrollableConstraints,
    enableDebugMode: false,
  );
}
