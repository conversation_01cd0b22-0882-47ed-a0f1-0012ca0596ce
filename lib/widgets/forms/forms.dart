// Comprehensive form system exports
//
// This file provides a single import point for all form-related components,
// making it easy to use the generic form system across the application.

// Configuration classes
export 'config/form_field_config.dart';
export 'config/generic_form_config.dart';
// Entity-specific form configurations
export 'configs/tag_form_config.dart';
// Example implementations
export 'examples/tag_form_example.dart';
// Factory for creating form fields
export 'factory/form_field_factory.dart';
// Helper utilities
export 'helpers/common_form_fields.dart';
export 'implementations/color_picker_form_field_impl.dart';
export 'implementations/date_picker_form_field_impl.dart';
export 'implementations/dropdown_form_field_impl.dart';
export 'implementations/icon_picker_form_field_impl.dart';
// Form field implementations
export 'implementations/text_form_field_impl.dart';
// Core interfaces and base classes
export 'interfaces/form_field_interface.dart';
// Base screen for form handling
export 'screens/base_editable_form_screen.dart';
export 'screens/generic_form_screen.dart';
export 'selectors/color_selector.dart';
// Reusable selector widgets
export 'selectors/generic_selector.dart';
export 'selectors/icon_selector.dart';
export 'selectors/type_selector.dart';
