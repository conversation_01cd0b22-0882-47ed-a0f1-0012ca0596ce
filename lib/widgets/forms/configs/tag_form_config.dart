import 'package:budapp/data/models/tag.dart';
import 'package:budapp/data/repositories/interfaces/tag_repository.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/config/generic_form_config.dart';
import 'package:budapp/widgets/forms/helpers/common_form_fields.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Form configuration factory for Tag entities
class TagFormConfig {
  TagFormConfig._();

  /// Create configuration for creating a new tag
  static GenericFormConfig<Tag> create({
    required TagRepository repository,
    void Function(String fieldKey, dynamic value)? onFieldChanged,
  }) {
    return GenericFormConfig<Tag>(
      title: 'Create Tag',
      fields: _getTagFields(),
      onSubmit: (data) => _handleCreate(repository, data),
      dataMapper: _TagDataMapper(),
      onFieldChanged: onFieldChanged,
    );
  }

  /// Create configuration for editing an existing tag
  static GenericFormConfig<Tag> edit({
    required Tag tag,
    required TagRepository repository,
    void Function(String fieldKey, dynamic value)? onFieldChanged,
  }) {
    return GenericFormConfig<Tag>(
      title: 'Edit Tag',
      fields: _getTagFields(),
      initialData: tag,
      onSubmit: (data) => _handleUpdate(repository, tag, data),
      onDelete: () => _handleDelete(repository, tag),
      showDeleteButton: true,
      dataMapper: _TagDataMapper(),
      onFieldChanged: onFieldChanged,
    );
  }

  /// Get the field definitions for tag forms
  static List<FormFieldConfig<dynamic>> _getTagFields() {
    return [
      // Name field
      CommonFormFields.name(
        label: 'Tag Name',
        hintText: 'Enter tag name',
        isRequired: true,
      ),

      // Color field
      CommonFormFields.colorSelector(
        label: 'Tag Color',
        isRequired: true,
        customValidator: (value) {
          if (value == null || value.isEmpty) {
            return 'Please select a color for the tag';
          }
          return null;
        },
      ),
    ];
  }

  /// Handle creating a new tag
  static Future<void> _handleCreate(
    TagRepository repository,
    Map<String, dynamic> data,
  ) async {
    final tag = Tag.create(
      userId: '', // Will be set by the repository
      name: data['name'] as String,
      color: data['color'] as String,
    );

    await repository.createTag(tag);
  }

  /// Handle updating an existing tag
  static Future<void> _handleUpdate(
    TagRepository repository,
    Tag originalTag,
    Map<String, dynamic> data,
  ) async {
    final updatedTag = originalTag.copyWith(
      name: data['name'] as String,
      color: data['color'] as String,
      updatedAt: Timestamp.now(),
    );

    await repository.updateTag(updatedTag);
  }

  /// Handle deleting a tag
  static Future<void> _handleDelete(TagRepository repository, Tag tag) async {
    await repository.deleteTag(tag.id);
  }
}

/// Data mapper for Tag entities
class _TagDataMapper extends GenericFormDataMapper<Tag> {
  @override
  Map<String, dynamic> entityToFormData(Tag entity) {
    return {'name': entity.name, 'color': entity.color};
  }

  @override
  Tag formDataToEntity(Map<String, dynamic> formData) {
    return Tag.create(
      userId: '', // Will be set by the repository
      name: formData['name'] as String,
      color: formData['color'] as String,
    );
  }

  @override
  Tag updateEntityWithFormData(Tag entity, Map<String, dynamic> formData) {
    return entity.copyWith(
      name: formData['name'] as String,
      color: formData['color'] as String,
      updatedAt: Timestamp.now(),
    );
  }
}
