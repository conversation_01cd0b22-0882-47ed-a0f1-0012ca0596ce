import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/interfaces/transaction_repository.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/config/generic_form_config.dart';
import 'package:budapp/widgets/forms/helpers/common_form_fields.dart';

/// Enhanced form configuration factory for Transaction entities
///
/// This configuration demonstrates how to use the specialized form field
/// implementations to create enhanced transaction forms while maintaining
/// the existing sophisticated business logic.
class TransactionFormConfig {
  TransactionFormConfig._();

  /// Create configuration for creating a new transaction
  static GenericFormConfig<Transaction> create({
    required ITransactionRepository repository,
    void Function(String fieldKey, dynamic value)? onFieldChanged,
  }) {
    return GenericFormConfig<Transaction>(
      title: 'Create Transaction',
      fields: _getTransactionFields(),
      onSubmit: (data) => _handleCreate(repository, data),
      dataMapper: _TransactionDataMapper(),
      onFieldChanged: onFieldChanged,
    );
  }

  /// Create configuration for editing an existing transaction
  static GenericFormConfig<Transaction> edit({
    required Transaction transaction,
    required ITransactionRepository repository,
    void Function(String fieldKey, dynamic value)? onFieldChanged,
  }) {
    return GenericFormConfig<Transaction>(
      title: 'Edit Transaction',
      fields: _getTransactionFields(),
      initialData: transaction,
      onSubmit: (data) => _handleUpdate(repository, transaction, data),
      onDelete: () => _handleDelete(repository, transaction),
      showDeleteButton: true,
      dataMapper: _TransactionDataMapper(),
      onFieldChanged: onFieldChanged,
    );
  }

  /// Get the enhanced field definitions for transaction forms
  static List<FormFieldConfig<dynamic>> _getTransactionFields() {
    return [
      // Transaction Type - using specialized implementation
      FormFieldConfig<TransactionType>(
        key: 'transactionType',
        type: FormFieldType.custom,
        label: 'Transaction Type',
        isRequired: true,
        initialValue: TransactionType.expense,
        validator: (value) {
          if (value == null) {
            return 'Please select a transaction type';
          }
          return null;
        },
      ),

      // Amount - using enhanced amount field
      FormFieldConfig<double>(
        key: 'amount',
        type: FormFieldType.custom,
        label: 'Amount',
        hintText: '0.00',
        isRequired: true,
        validator: (value) {
          if (value == null || value <= 0) {
            return 'Please enter a valid amount greater than 0';
          }
          return null;
        },
      ),

      // From Account - using enhanced account selector
      FormFieldConfig<String?>(
        key: 'fromAccountId',
        type: FormFieldType.custom,
        label: 'From Account',
        hintText: 'Select source account',
        isRequired: true,
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Please select a source account';
          }
          return null;
        },
        customProperties: {
          'transactionType':
              TransactionType.expense, // Will be updated dynamically
        },
      ),

      // To Account - for transfers and income
      FormFieldConfig<String?>(
        key: 'toAccountId',
        type: FormFieldType.custom,
        label: 'To Account',
        hintText: 'Select destination account',
        isRequired: false, // Required only for transfers
        validator: (value) {
          // Validation will be handled dynamically based on transaction type
          return null;
        },
        customProperties: {'transactionType': TransactionType.transfer},
      ),

      // Category - using existing dropdown
      FormFieldConfig<String?>(
        key: 'categoryId',
        type: FormFieldType.dropdown,
        label: 'Category',
        hintText: 'Select category (optional for transfers)',
        isRequired: false, // Required for income/expense, not for transfers
        customProperties: {
          'items': <dynamic>[], // Will be populated dynamically
          'displayStringForItem': (dynamic item) => item.toString(),
        },
      ),

      // Date - using existing date picker
      CommonFormFields.datePicker(
        label: 'Transaction Date',
        isRequired: true,
        initialValue: DateTime.now(),
      ),

      // Description
      CommonFormFields.description(
        label: 'Description',
        hintText: 'Optional transaction description',
        isRequired: false,
      ),

      // Notes
      const FormFieldConfig<String>(
        key: 'notes',
        type: FormFieldType.text,
        label: 'Notes',
        hintText: 'Additional notes (optional)',
        isRequired: false,
        customProperties: {'maxLines': 3},
      ),
    ];
  }

  /// Handle creating a new transaction
  static Future<void> _handleCreate(
    ITransactionRepository repository,
    Map<String, dynamic> data,
  ) async {
    // This would integrate with existing transaction creation logic
    // For now, this is a placeholder showing the structure

    final transaction = Transaction(
      id: '', // Will be generated by repository
      userId: '', // Will be set by repository
      type: data['transactionType'] as TransactionType,
      status: TransactionStatus.completed,
      amountCents: ((data['amount'] as double) * 100).round(),
      fromAccountId: data['fromAccountId'] as String?,
      toAccountId: data['toAccountId'] as String?,
      categoryId: data['categoryId'] as String?,
      description: data['description'] as String?,
      notes: data['notes'] as String?,
      transactionDate: data['date'] as DateTime,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await repository.createTransaction(transaction);
  }

  /// Handle updating an existing transaction
  static Future<void> _handleUpdate(
    ITransactionRepository repository,
    Transaction originalTransaction,
    Map<String, dynamic> data,
  ) async {
    final updatedTransaction = originalTransaction.copyWith(
      type: data['transactionType'] as TransactionType,
      amountCents: ((data['amount'] as double) * 100).round(),
      fromAccountId: data['fromAccountId'] as String?,
      toAccountId: data['toAccountId'] as String?,
      categoryId: data['categoryId'] as String?,
      description: data['description'] as String?,
      notes: data['notes'] as String?,
      transactionDate: data['date'] as DateTime,
      updatedAt: DateTime.now(),
    );

    await repository.updateTransaction(
      originalTransaction.id,
      updatedTransaction,
    );
  }

  /// Handle deleting a transaction
  static Future<void> _handleDelete(
    ITransactionRepository repository,
    Transaction transaction,
  ) async {
    await repository.deleteTransaction(transaction.userId, transaction.id);
  }
}

/// Enhanced data mapper for Transaction entities
class _TransactionDataMapper extends GenericFormDataMapper<Transaction> {
  @override
  Map<String, dynamic> entityToFormData(Transaction entity) {
    return {
      'transactionType': entity.type,
      'amount': entity.amountCents / 100.0,
      'fromAccountId': entity.fromAccountId,
      'toAccountId': entity.toAccountId,
      'categoryId': entity.categoryId,
      'date': entity.transactionDate,
      'description': entity.description ?? '',
      'notes': entity.notes ?? '',
    };
  }

  @override
  Transaction formDataToEntity(Map<String, dynamic> formData) {
    return Transaction(
      id: '', // Will be generated
      userId: '', // Will be set by repository
      type: formData['transactionType'] as TransactionType,
      status: TransactionStatus.completed,
      amountCents: ((formData['amount'] as double) * 100).round(),
      fromAccountId: formData['fromAccountId'] as String?,
      toAccountId: formData['toAccountId'] as String?,
      categoryId: formData['categoryId'] as String?,
      description: formData['description'] as String?,
      notes: formData['notes'] as String?,
      transactionDate: formData['date'] as DateTime,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  @override
  Transaction updateEntityWithFormData(
    Transaction entity,
    Map<String, dynamic> formData,
  ) {
    return entity.copyWith(
      type: formData['transactionType'] as TransactionType,
      amountCents: ((formData['amount'] as double) * 100).round(),
      fromAccountId: formData['fromAccountId'] as String?,
      toAccountId: formData['toAccountId'] as String?,
      categoryId: formData['categoryId'] as String?,
      description: formData['description'] as String?,
      notes: formData['notes'] as String?,
      transactionDate: formData['date'] as DateTime,
      updatedAt: DateTime.now(),
    );
  }
}
