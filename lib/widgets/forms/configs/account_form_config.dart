import 'package:budapp/config/form_constants.dart';
import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/repositories/interfaces/account_repository.dart';
import 'package:budapp/features/accounts/services/account_validators.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/config/generic_form_config.dart';

/// Form configuration factory for Account entities
class AccountFormConfig {
  AccountFormConfig._();

  /// Normalize optional string - returns null if string is null or empty
  static String? _normalizeOptionalString(String? value) {
    return (value?.isEmpty ?? true) ? null : value;
  }

  /// Extract and validate account data from form data
  static ({
    Map<String, dynamic> accountTypeData,
    int balanceCents,
    String name,
    String? description,
    String? colorHex,
    String? iconName,
  })
  _extractAccountData(Map<String, dynamic> data) {
    final accountTypeData = data['accountType'] as Map<String, dynamic>;
    final balanceCents = AccountValidators.parseBalanceToCents(
      data['initialBalance'] as String,
    );

    if (balanceCents == null) {
      throw Exception('Invalid balance amount');
    }

    return (
      accountTypeData: accountTypeData,
      balanceCents: balanceCents,
      name: data['name'] as String,
      description: _normalizeOptionalString(data['description'] as String?),
      colorHex: data['color'] as String?,
      iconName: data['icon'] as String?,
    );
  }

  /// Create configuration for creating a new account
  static GenericFormConfig<Account> create({
    required IAccountRepository repository,
    void Function(String fieldKey, dynamic value)? onFieldChanged,
  }) {
    return GenericFormConfig<Account>(
      title: 'Create Account',
      fields: _getAccountFields(),
      onSubmit: (data) => _handleCreate(repository, data),
      dataMapper: _AccountDataMapper(),
      onFieldChanged: onFieldChanged,
    );
  }

  /// Create configuration for editing an existing account
  static GenericFormConfig<Account> edit({
    required Account account,
    required IAccountRepository repository,
    void Function(String fieldKey, dynamic value)? onFieldChanged,
  }) {
    return GenericFormConfig<Account>(
      title: 'Edit Account',
      fields: _getAccountFields(),
      initialData: account,
      onSubmit: (data) => _handleUpdate(repository, account, data),
      onDelete: () => _handleDelete(repository, account),
      showDeleteButton: true,
      dataMapper: _AccountDataMapper(),
      onFieldChanged: onFieldChanged,
    );
  }

  /// Get the field definitions for account forms
  static List<FormFieldConfig<dynamic>> _getAccountFields() {
    return [
      // Name field
      FormFieldConfig<String>(
        key: 'name',
        type: FormFieldType.text,
        label: 'Account Name',
        hintText: 'Enter account name',
        isRequired: true,
        validator: (String? value) {
          if (value == null || value.isEmpty) {
            return 'Account name is required';
          }
          if (value.length > 100) {
            return 'Account name must be 100 characters or less';
          }
          return null;
        },
      ),

      // Description field
      FormFieldConfig<String>(
        key: 'description',
        type: FormFieldType.multilineText,
        label: 'Description',
        hintText: 'Optional account description',
        isRequired: false,
        validator: (String? value) {
          if (value != null && value.length > 500) {
            return 'Description must be 500 characters or less';
          }
          return null;
        },
      ),

      // Account type selector (custom field)
      FormFieldConfig<Map<String, dynamic>>(
        key: 'accountType',
        type: FormFieldType.custom,
        label: 'Account Type',
        isRequired: true,
        validator: (Map<String, dynamic>? value) {
          if (value == null || value['type'] == null) {
            return 'Please select an account type';
          }
          return null;
        },
      ),

      // Initial balance field
      FormFieldConfig<String>(
        key: 'initialBalance',
        type: FormFieldType.text,
        label: 'Initial Balance',
        hintText: '0.00',
        isRequired: true,
        validator: (String? value) {
          if (value == null || value.isEmpty) {
            return 'Initial balance is required';
          }
          final balanceCents = AccountValidators.parseBalanceToCents(value);
          if (balanceCents == null) {
            return 'Please enter a valid balance amount';
          }
          return null;
        },
      ),

      // Color field
      const ColorPickerFieldConfig(
        key: 'color',
        label: 'Account Color',
        isRequired: false,
        availableColors: FormConstants.accountColors,
      ),

      // Icon field
      const IconPickerFieldConfig(
        key: 'icon',
        label: 'Account Icon',
        isRequired: false,
        availableIcons: FormConstants.accountIcons,
      ),
    ];
  }

  /// Handle creating a new account
  static Future<void> _handleCreate(
    IAccountRepository repository,
    Map<String, dynamic> data,
  ) async {
    final extractedData = _extractAccountData(data);

    // Generate unique account ID
    final accountId = DateTime.now().millisecondsSinceEpoch.toString();

    final account = Account(
      id: accountId,
      userId: '', // Will be set by the repository to current user ID
      name: extractedData.name,
      type: extractedData.accountTypeData['type'] as AccountType,
      classification:
          extractedData.accountTypeData['classification']
              as AccountClassification,
      description: extractedData.description,
      initialBalanceCents: extractedData.balanceCents,
      currentBalanceCents: extractedData.balanceCents,
      colorHex: extractedData.colorHex,
      iconName: extractedData.iconName,
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await repository.createAccount(account);
  }

  /// Handle updating an existing account
  static Future<void> _handleUpdate(
    IAccountRepository repository,
    Account originalAccount,
    Map<String, dynamic> data,
  ) async {
    final extractedData = _extractAccountData(data);

    final updatedAccount = originalAccount.copyWith(
      name: extractedData.name,
      type: extractedData.accountTypeData['type'] as AccountType,
      classification:
          extractedData.accountTypeData['classification']
              as AccountClassification,
      description: extractedData.description,
      initialBalanceCents: extractedData.balanceCents,
      colorHex: extractedData.colorHex,
      iconName: extractedData.iconName,
      updatedAt: DateTime.now(),
    );

    await repository.updateAccount(originalAccount.id, updatedAccount);
  }

  /// Handle deleting an account
  static Future<void> _handleDelete(
    IAccountRepository repository,
    Account account,
  ) async {
    await repository.deactivateAccount(account.id);
  }
}

/// Data mapper for Account entities
class _AccountDataMapper extends GenericFormDataMapper<Account> {
  @override
  Map<String, dynamic> entityToFormData(Account entity) {
    return {
      'name': entity.name,
      'description': entity.description ?? '',
      'accountType': {
        'type': entity.type,
        'classification': entity.classification,
      },
      'initialBalance': AccountValidators.formatCentsToBalance(
        entity.initialBalanceCents,
      ),
      'color': entity.colorHex,
      'icon': entity.iconName,
    };
  }

  @override
  Account formDataToEntity(Map<String, dynamic> formData) {
    final extractedData = AccountFormConfig._extractAccountData(formData);

    return Account(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: '', // Will be set by the repository
      name: extractedData.name,
      type: extractedData.accountTypeData['type'] as AccountType,
      classification:
          extractedData.accountTypeData['classification']
              as AccountClassification,
      description: extractedData.description,
      initialBalanceCents: extractedData.balanceCents,
      currentBalanceCents: extractedData.balanceCents,
      colorHex: extractedData.colorHex,
      iconName: extractedData.iconName,
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  @override
  Account updateEntityWithFormData(
    Account entity,
    Map<String, dynamic> formData,
  ) {
    try {
      final extractedData = AccountFormConfig._extractAccountData(formData);

      return entity.copyWith(
        name: extractedData.name,
        type: extractedData.accountTypeData['type'] as AccountType,
        classification:
            extractedData.accountTypeData['classification']
                as AccountClassification,
        description: extractedData.description,
        initialBalanceCents: extractedData.balanceCents,
        colorHex: extractedData.colorHex,
        iconName: extractedData.iconName,
        updatedAt: DateTime.now(),
      );
    } on Exception {
      // Fallback to original balance if parsing fails
      final balanceCents =
          AccountValidators.parseBalanceToCents(
            formData['initialBalance'] as String,
          ) ??
          entity.initialBalanceCents;

      return entity.copyWith(
        name: formData['name'] as String,
        type:
            (formData['accountType'] as Map<String, dynamic>)['type']
                as AccountType,
        classification:
            (formData['accountType'] as Map<String, dynamic>)['classification']
                as AccountClassification,
        description: AccountFormConfig._normalizeOptionalString(
          formData['description'] as String?,
        ),
        initialBalanceCents: balanceCents,
        colorHex: formData['color'] as String?,
        iconName: formData['icon'] as String?,
        updatedAt: DateTime.now(),
      );
    }
  }
}
