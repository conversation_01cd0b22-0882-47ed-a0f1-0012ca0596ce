import 'package:budapp/data/models/goal_contribution.dart';
import 'package:budapp/data/repositories/interfaces/i_goal_contribution_repository.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/config/generic_form_config.dart';
import 'package:budapp/widgets/forms/helpers/common_form_fields.dart';

/// Form configuration factory for GoalContribution entities
class GoalContributionFormConfig {
  GoalContributionFormConfig._();

  /// Create configuration for creating a new contribution
  static GenericFormConfig<GoalContribution> create({
    required String goalId,
    required IGoalContributionRepository repository,
    void Function(String fieldKey, dynamic value)? onFieldChanged,
  }) {
    return GenericFormConfig<GoalContribution>(
      title: 'Add Contribution',
      fields: _getContributionFields(),
      onSubmit: (data) => _handleCreate(repository, goalId, data),
      dataMapper: _GoalContributionDataMapper(goalId: goalId),
      onFieldChanged: onFieldChanged,
    );
  }

  /// Create configuration for editing an existing contribution
  static GenericFormConfig<GoalContribution> edit({
    required GoalContribution contribution,
    required IGoalContributionRepository repository,
    void Function(String fieldKey, dynamic value)? onFieldChanged,
  }) {
    return GenericFormConfig<GoalContribution>(
      title: 'Edit Contribution',
      fields: _getContributionFields(),
      initialData: contribution,
      onSubmit: (data) => _handleUpdate(repository, contribution, data),
      onDelete: () => _handleDelete(repository, contribution),
      showDeleteButton: true,
      dataMapper: _GoalContributionDataMapper(goalId: contribution.goalId),
      onFieldChanged: onFieldChanged,
    );
  }

  /// Get form field configurations for contribution
  static List<FormFieldConfig<dynamic>> _getContributionFields() {
    return [
      // Amount field
      CommonFormFields.amount(
        label: 'Contribution Amount',
        hintText: '0.00',
        isRequired: true,
        customValidator: (value) {
          if (value == null || value.isEmpty) {
            return 'Contribution amount is required';
          }
          final amount = double.tryParse(value);
          if (amount == null) {
            return 'Please enter a valid amount';
          }
          if (amount <= 0) {
            return 'Contribution amount must be greater than zero';
          }
          if (amount > 1000000) {
            return r'Contribution amount cannot exceed $1,000,000';
          }
          return null;
        },
      ),

      // Contribution date field
      DatePickerFieldConfig(
        key: 'contributionDate',
        label: 'Contribution Date',
        hintText: 'Select contribution date',
        isRequired: true,
        firstDate: DateTime.now().subtract(
          const Duration(days: 3650),
        ), // 10 years ago
        lastDate: DateTime.now(),
        validator: (value) {
          if (value == null) {
            return 'Contribution date is required';
          }
          if (value.isAfter(DateTime.now())) {
            return 'Contribution date cannot be in the future';
          }
          final tenYearsAgo = DateTime.now().subtract(
            const Duration(days: 3650),
          );
          if (value.isBefore(tenYearsAgo)) {
            return 'Contribution date cannot be more than 10 years ago';
          }
          return null;
        },
      ),

      // Description field (optional)
      CommonFormFields.description(
        label: 'Description',
        hintText: 'Optional description for this contribution',
        isRequired: false,
        maxLength: 500,
      ),
    ];
  }

  /// Handle contribution creation
  static Future<void> _handleCreate(
    IGoalContributionRepository repository,
    String goalId,
    Map<String, dynamic> formData,
  ) async {
    final contribution = _GoalContributionDataMapper(
      goalId: goalId,
    ).formDataToEntity(formData);
    await repository.createContribution(goalId, contribution);
  }

  /// Handle contribution update
  static Future<void> _handleUpdate(
    IGoalContributionRepository repository,
    GoalContribution originalContribution,
    Map<String, dynamic> formData,
  ) async {
    final updatedContribution = _GoalContributionDataMapper(
      goalId: originalContribution.goalId,
    ).updateEntityWithFormData(originalContribution, formData);

    await repository.updateContribution(
      originalContribution.goalId,
      originalContribution.id,
      updatedContribution,
    );
  }

  /// Handle contribution deletion
  static Future<void> _handleDelete(
    IGoalContributionRepository repository,
    GoalContribution contribution,
  ) async {
    await repository.deleteContribution(contribution.goalId, contribution.id);
  }
}

/// Data mapper for GoalContribution entities
class _GoalContributionDataMapper
    extends GenericFormDataMapper<GoalContribution> {
  _GoalContributionDataMapper({required this.goalId});

  final String goalId;

  @override
  Map<String, dynamic> entityToFormData(GoalContribution entity) {
    return {
      'amount': (entity.amountCents / 100.0).toStringAsFixed(2),
      'contributionDate': entity.contributionDate,
      'description': entity.description ?? '',
    };
  }

  @override
  GoalContribution formDataToEntity(Map<String, dynamic> formData) {
    // Parse amount from string to cents
    final amountStr = formData['amount'] as String? ?? '0';
    final amount = double.tryParse(amountStr) ?? 0.0;
    final amountCents = (amount * 100).round();

    return GoalContribution.create(
      userId: '', // Will be set by repository
      goalId: goalId,
      amountCents: amountCents,
      contributionDate:
          formData['contributionDate'] as DateTime? ?? DateTime.now(),
      description: (formData['description'] as String?)?.isNotEmpty ?? false
          ? formData['description'] as String
          : null,
    );
  }

  @override
  GoalContribution updateEntityWithFormData(
    GoalContribution entity,
    Map<String, dynamic> formData,
  ) {
    // Parse amount from string to cents
    final amountStr = formData['amount'] as String? ?? '0';
    final amount = double.tryParse(amountStr) ?? 0.0;
    final amountCents = (amount * 100).round();

    return entity.copyWith(
      amountCents: amountCents,
      contributionDate:
          formData['contributionDate'] as DateTime? ?? entity.contributionDate,
      description: (formData['description'] as String?)?.isNotEmpty ?? false
          ? formData['description'] as String
          : null,
      updatedAt: DateTime.now(),
    );
  }
}
