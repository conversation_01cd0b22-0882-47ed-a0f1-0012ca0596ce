import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/repositories/interfaces/budget_repository.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/config/generic_form_config.dart';

/// Enhanced form configuration factory for Budget entities
///
/// This configuration demonstrates how to use the specialized form field
/// implementations to create enhanced budget forms while maintaining
/// the existing sophisticated validation and business logic.
class BudgetFormConfig {
  BudgetFormConfig._();

  /// Create configuration for creating a new budget
  static GenericFormConfig<Budget> create({
    required BudgetRepository repository,
    void Function(String fieldKey, dynamic value)? onFieldChanged,
  }) {
    return GenericFormConfig<Budget>(
      title: 'Create Budget',
      fields: _getBudgetFields(),
      onSubmit: (data) => _handleCreate(repository, data),
      dataMapper: _BudgetDataMapper(),
      onFieldChanged: onFieldChanged,
    );
  }

  /// Create configuration for editing an existing budget
  static GenericFormConfig<Budget> edit({
    required Budget budget,
    required BudgetRepository repository,
    void Function(String fieldKey, dynamic value)? onFieldChanged,
  }) {
    return GenericFormConfig<Budget>(
      title: 'Edit Budget',
      fields: _getBudgetFields(),
      initialData: budget,
      onSubmit: (data) => _handleUpdate(repository, budget, data),
      onDelete: () => _handleDelete(repository, budget),
      showDeleteButton: true,
      dataMapper: _BudgetDataMapper(),
      onFieldChanged: onFieldChanged,
    );
  }

  /// Get the enhanced field definitions for budget forms
  static List<FormFieldConfig<dynamic>> _getBudgetFields() {
    return [
      // Note: Budget doesn't have a name field - it's identified by category and period

      // Budget Amount - using enhanced amount field
      FormFieldConfig<double>(
        key: 'amount',
        type: FormFieldType.custom,
        label: 'Budget Amount',
        hintText: '0.00',
        isRequired: true,
        validator: (value) {
          if (value == null || value <= 0) {
            return 'Please enter a valid budget amount greater than 0';
          }
          return null;
        },
      ),

      // Budget Type - using specialized implementation
      FormFieldConfig<BudgetType>(
        key: 'budgetType',
        type: FormFieldType.custom,
        label: 'Budget Type',
        hintText: 'Choose whether this budget tracks income or expenses',
        isRequired: true,
        initialValue: BudgetType.expense,
        validator: (value) {
          if (value == null) {
            return 'Please select a budget type';
          }
          return null;
        },
      ),

      // Budget Period - using specialized implementation
      FormFieldConfig<BudgetPeriod>(
        key: 'budgetPeriod',
        type: FormFieldType.custom,
        label: 'Budget Period',
        hintText: 'Choose the time period for this budget',
        isRequired: true,
        initialValue: BudgetPeriod.monthly,
        validator: (value) {
          if (value == null) {
            return 'Please select a budget period';
          }
          return null;
        },
      ),

      // Category - using existing dropdown
      FormFieldConfig<String?>(
        key: 'categoryId',
        type: FormFieldType.dropdown,
        label: 'Category',
        hintText: 'Select category (optional for total budgets)',
        isRequired: false,
        customProperties: {
          'items': <dynamic>[], // Will be populated dynamically
          'displayStringForItem': (dynamic item) => item.toString(),
        },
      ),

      // Parent Budget - for sub-budgets
      FormFieldConfig<String?>(
        key: 'parentBudgetId',
        type: FormFieldType.dropdown,
        label: 'Parent Budget',
        hintText: 'Select parent budget (optional)',
        isRequired: false,
        customProperties: {
          'items': <dynamic>[], // Will be populated dynamically
          'displayStringForItem': (dynamic item) => item.toString(),
        },
      ),
    ];
  }

  /// Handle creating a new budget
  static Future<void> _handleCreate(
    BudgetRepository repository,
    Map<String, dynamic> data,
  ) async {
    // This would integrate with existing budget creation logic
    // For now, this is a placeholder showing the structure

    final now = DateTime.now();
    final period = data['budgetPeriod'] as BudgetPeriod;
    final periodStart = period == BudgetPeriod.monthly
        ? DateTime(now.year, now.month, 1)
        : DateTime(now.year, 1, 1);

    final budget = Budget.create(
      userId: '', // Will be set by repository
      type: data['budgetType'] as BudgetType,
      plannedAmountCents: ((data['amount'] as double) * 100).round(),
      period: period,
      periodStart: periodStart,
      categoryId: data['categoryId'] as String?,
      parentBudgetId: data['parentBudgetId'] as String?,
    );

    await repository.createBudget(budget);
  }

  /// Handle updating an existing budget
  static Future<void> _handleUpdate(
    BudgetRepository repository,
    Budget originalBudget,
    Map<String, dynamic> data,
  ) async {
    final updatedBudget = originalBudget.copyWith(
      plannedAmountCents: ((data['amount'] as double) * 100).round(),
      type: data['budgetType'] as BudgetType,
      period: data['budgetPeriod'] as BudgetPeriod,
      categoryId: data['categoryId'] as String?,
      parentBudgetId: data['parentBudgetId'] as String?,
      updatedAt: DateTime.now(),
    );

    await repository.updateBudget(updatedBudget);
  }

  /// Handle deleting a budget
  static Future<void> _handleDelete(
    BudgetRepository repository,
    Budget budget,
  ) async {
    await repository.deleteBudget(budget.id);
  }
}

/// Enhanced data mapper for Budget entities
class _BudgetDataMapper extends GenericFormDataMapper<Budget> {
  @override
  Map<String, dynamic> entityToFormData(Budget entity) {
    return {
      // Budget doesn't have a name field, using category-based identification
      'amount': entity.plannedAmountCents / 100.0,
      'budgetType': entity.type,
      'budgetPeriod': entity.period,
      'categoryId': entity.categoryId,
      'parentBudgetId': entity.parentBudgetId,
    };
  }

  @override
  Budget formDataToEntity(Map<String, dynamic> formData) {
    final now = DateTime.now();
    final period = formData['budgetPeriod'] as BudgetPeriod;
    final periodStart = period == BudgetPeriod.monthly
        ? DateTime(now.year, now.month, 1)
        : DateTime(now.year, 1, 1);

    return Budget.create(
      userId: '', // Will be set by repository
      type: formData['budgetType'] as BudgetType,
      plannedAmountCents: ((formData['amount'] as double) * 100).round(),
      period: period,
      periodStart: periodStart,
      categoryId: formData['categoryId'] as String?,
      parentBudgetId: formData['parentBudgetId'] as String?,
    );
  }

  @override
  Budget updateEntityWithFormData(
    Budget entity,
    Map<String, dynamic> formData,
  ) {
    return entity.copyWith(
      plannedAmountCents: ((formData['amount'] as double) * 100).round(),
      type: formData['budgetType'] as BudgetType,
      period: formData['budgetPeriod'] as BudgetPeriod,
      categoryId: formData['categoryId'] as String?,
      parentBudgetId: formData['parentBudgetId'] as String?,
      updatedAt: DateTime.now(),
    );
  }
}
