import 'package:budapp/config/form_constants.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/repositories/interfaces/category_repository.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/config/generic_form_config.dart';

/// Unified form configuration factory for Category entities
///
/// This unified approach handles both root categories and subcategories
/// through a single form interface with optional parent selection.
/// Eliminates the need for separate subcategory screens and forms.
class CategoryFormConfig {
  CategoryFormConfig._();

  /// Create configuration for creating a new category (root or subcategory)
  static GenericFormConfig<Category> create({
    required ICategoryRepository repository,
    String? parentId,
    void Function(String fieldKey, dynamic value)? onFieldChanged,
  }) {
    final isSubcategory = parentId != null;

    return GenericFormConfig<Category>(
      title: isSubcategory ? 'Create Subcategory' : 'Create Category',
      fields: _getCategoryFields(),
      onSubmit: (data) => _handleCreate(repository, data),
      dataMapper: _CategoryDataMapper(initialParentId: parentId),
      onFieldChanged: onFieldChanged,
    );
  }

  /// Create configuration for editing an existing category
  static GenericFormConfig<Category> edit({
    required Category category,
    required ICategoryRepository repository,
    void Function(String fieldKey, dynamic value)? onFieldChanged,
  }) {
    final isSubcategory = category.parentId != null;

    return GenericFormConfig<Category>(
      title: isSubcategory ? 'Edit Subcategory' : 'Edit Category',
      fields: _getCategoryFields(),
      initialData: category,
      onSubmit: (data) => _handleUpdate(repository, category, data),
      onDelete: () => _handleDelete(repository, category),
      showDeleteButton: true,
      dataMapper: _CategoryDataMapper(),
      onFieldChanged: onFieldChanged,
    );
  }

  /// Get the unified field definitions for category forms
  static List<FormFieldConfig<dynamic>> _getCategoryFields() {
    return [
      // Name field
      FormFieldConfig<String>(
        key: 'name',
        type: FormFieldType.text,
        label: 'Category Name',
        hintText: 'Enter category name',
        isRequired: true,
        validator: (String? value) {
          if (value == null || value.isEmpty) {
            return 'Category name is required';
          }
          if (value.length > 100) {
            return 'Category name must be 100 characters or less';
          }
          return null;
        },
      ),

      // Parent category selector (for creating subcategories)
      FormFieldConfig<String?>(
        key: 'parentId',
        type: FormFieldType.custom,
        label: 'Parent Category',
        hintText: 'Select parent category (optional)',
        isRequired: false,
        validator: (String? value) {
          // Parent is optional - null means root category
          return null;
        },
      ),

      // Type field - using custom implementation to avoid generic type issues
      FormFieldConfig<CategoryType>(
        key: 'type',
        type: FormFieldType.custom,
        label: 'Category Type',
        isRequired: true,
        validator: (CategoryType? value) =>
            value == null ? 'Please select a category type' : null,
      ),

      // Description field
      FormFieldConfig<String>(
        key: 'description',
        type: FormFieldType.multilineText,
        label: 'Description',
        hintText: 'Optional category description',
        isRequired: false,
        validator: (String? value) {
          if (value != null && value.length > 500) {
            return 'Description must be 500 characters or less';
          }
          return null;
        },
      ),

      // Color field
      const ColorPickerFieldConfig(
        key: 'color',
        label: 'Category Color',
        isRequired: false,
        availableColors: FormConstants.categoryColors,
      ),

      // Icon field
      const IconPickerFieldConfig(
        key: 'icon',
        label: 'Category Icon',
        isRequired: false,
        availableIcons: FormConstants.categoryIcons,
      ),
    ];
  }

  /// Handle creating a new category (root or subcategory)
  static Future<void> _handleCreate(
    ICategoryRepository repository,
    Map<String, dynamic> data,
  ) async {
    final parentId = data['parentId'] as String?;

    // Validate hierarchy constraints before creating
    if (parentId != null) {
      await _validateHierarchyConstraints(repository, parentId);
    }

    final category = Category.create(
      userId: '', // Will be set by the repository
      name: data['name'] as String,
      type: data['type'] as CategoryType,
      parentId: parentId,
      description: ((data['description'] as String?)?.isEmpty ?? false)
          ? null
          : data['description'] as String?,
      color: data['color'] as String?,
      icon: data['icon'] as String?,
    );

    if (parentId != null) {
      await repository.createSubcategory(category, parentId);
    } else {
      await repository.createCategory(category);
    }
  }

  /// Handle updating an existing category
  static Future<void> _handleUpdate(
    ICategoryRepository repository,
    Category originalCategory,
    Map<String, dynamic> data,
  ) async {
    final newParentId = data['parentId'] as String?;

    // Validate hierarchy constraints if parent is changing
    if (newParentId != originalCategory.parentId) {
      if (newParentId != null) {
        await _validateHierarchyConstraints(repository, newParentId);
      }
    }

    final updatedCategory = originalCategory.copyWith(
      name: data['name'] as String,
      type: data['type'] as CategoryType,
      parentId: newParentId,
      description: ((data['description'] as String?)?.isEmpty ?? false)
          ? null
          : data['description'] as String?,
      color: data['color'] as String?,
      icon: data['icon'] as String?,
      updatedAt: DateTime.now(),
    );

    // Handle different transition scenarios
    final wasSubcategory = originalCategory.parentId != null;
    final willBeSubcategory = updatedCategory.parentId != null;

    if (wasSubcategory && willBeSubcategory) {
      // Subcategory to subcategory (including parent change)
      await repository.updateSubcategory(originalCategory.id, updatedCategory);
    } else if (wasSubcategory && !willBeSubcategory) {
      // Subcategory to root category - use updateCategory
      await repository.updateCategory(originalCategory.id, updatedCategory);
    } else if (!wasSubcategory && willBeSubcategory) {
      // Root category to subcategory - use updateCategory (the category exists, just changing parentId)
      await repository.updateCategory(originalCategory.id, updatedCategory);
    } else {
      // Root category to root category
      await repository.updateCategory(originalCategory.id, updatedCategory);
    }
  }

  /// Handle deleting a category
  static Future<void> _handleDelete(
    ICategoryRepository repository,
    Category category,
  ) async {
    if (category.parentId != null) {
      await repository.deleteSubcategory(category.id, category.parentId!);
    } else {
      await repository.deactivateCategory(category.id);
    }
  }

  /// Validate hierarchy constraints to ensure 2-level maximum
  static Future<void> _validateHierarchyConstraints(
    ICategoryRepository repository,
    String parentId,
  ) async {
    // Get all active categories for the current user and find the parent
    final allCategories = await repository.getActiveCategories();
    final parentCategory = allCategories
        .where((category) => category.id == parentId)
        .firstOrNull;

    if (parentCategory == null) {
      throw Exception('Selected parent category not found.');
    }

    // Check if the parent is actually a root category (has no parent itself)
    if (parentCategory.parentId != null) {
      throw Exception(
        'Cannot create subcategory: Selected parent is already a subcategory. Maximum 2 levels allowed.',
      );
    }
  }
}

/// Unified data mapper for Category entities
class _CategoryDataMapper extends GenericFormDataMapper<Category> {
  _CategoryDataMapper({this.initialParentId});
  final String? initialParentId;

  @override
  Map<String, dynamic> entityToFormData(Category entity) {
    return {
      'name': entity.name,
      'parentId': entity.parentId,
      'type': entity.type,
      'description': entity.description ?? '',
      'color': entity.color,
      'icon': entity.icon,
    };
  }

  @override
  Category formDataToEntity(Map<String, dynamic> formData) {
    return Category.create(
      userId: '', // Will be set by the repository
      name: formData['name'] as String,
      type: formData['type'] as CategoryType,
      parentId: formData['parentId'] as String? ?? initialParentId,
      description: ((formData['description'] as String?)?.isEmpty ?? false)
          ? null
          : formData['description'] as String?,
      color: formData['color'] as String?,
      icon: formData['icon'] as String?,
    );
  }

  @override
  Category updateEntityWithFormData(
    Category entity,
    Map<String, dynamic> formData,
  ) {
    return entity.copyWith(
      name: formData['name'] as String,
      type: formData['type'] as CategoryType,
      parentId: formData['parentId'] as String?,
      description: ((formData['description'] as String?)?.isEmpty ?? false)
          ? null
          : formData['description'] as String?,
      color: formData['color'] as String?,
      icon: formData['icon'] as String?,
      updatedAt: DateTime.now(),
    );
  }
}
