import 'package:budapp/config/form_constants.dart';
import 'package:budapp/data/models/goal.dart';
import 'package:budapp/data/repositories/interfaces/goal_repository.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/config/generic_form_config.dart';
import 'package:flutter/material.dart';

/// Form configuration factory for Goal entities
class GoalFormConfig {
  GoalFormConfig._();

  /// Create configuration for creating a new goal
  static GenericFormConfig<Goal> create({
    required IGoalRepository repository,
    void Function(String fieldKey, dynamic value)? onFieldChanged,
  }) {
    return GenericFormConfig<Goal>(
      title: 'Create Goal',
      fields: _getGoalFields(),
      onSubmit: (data) => _handleCreate(repository, data),
      dataMapper: _GoalDataMapper(),
      onFieldChanged: onFieldChanged,
    );
  }

  /// Create configuration for editing an existing goal
  static GenericFormConfig<Goal> edit({
    required Goal goal,
    required IGoalRepository repository,
    void Function(String fieldKey, dynamic value)? onFieldChanged,
  }) {
    return GenericFormConfig<Goal>(
      title: 'Edit Goal',
      fields: _getGoalFields(),
      initialData: goal,
      onSubmit: (data) => _handleUpdate(repository, goal, data),
      onDelete: () => _handleDelete(repository, goal),
      showDeleteButton: true,
      dataMapper: _GoalDataMapper(),
      onFieldChanged: onFieldChanged,
    );
  }

  /// Get the list of form fields for goal forms
  static List<FormFieldConfig<dynamic>> _getGoalFields() {
    return [
      // Name field
      TextFieldConfig(
        key: 'name',
        label: 'Goal Name',
        hintText: 'Enter goal name',
        isRequired: true,
        maxLength: 100,
        validator: (String? value) {
          if (value == null || value.trim().isEmpty) {
            return 'Goal name is required';
          }
          if (value.trim().length < 2) {
            return 'Goal name must be at least 2 characters';
          }
          if (value.length > 100) {
            return 'Goal name must be 100 characters or less';
          }
          return null;
        },
      ),

      // Description field
      TextFieldConfig(
        key: 'description',
        label: 'Description',
        hintText: 'Enter goal description (optional)',
        isRequired: false,
        maxLength: 500,
        maxLines: 3,
        validator: (String? value) {
          if (value != null && value.length > 500) {
            return 'Description must be 500 characters or less';
          }
          return null;
        },
      ),

      // Target amount field
      TextFieldConfig(
        key: 'targetAmount',
        label: 'Target Amount',
        hintText: '0.00',
        isRequired: true,
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        validator: (String? value) {
          if (value == null || value.trim().isEmpty) {
            return 'Target amount is required';
          }
          final amount = double.tryParse(value.trim());
          if (amount == null) {
            return 'Please enter a valid amount';
          }
          if (amount <= 0) {
            return 'Target amount must be greater than 0';
          }
          if (amount > 999999999.99) {
            return 'Target amount is too large';
          }
          return null;
        },
      ),

      // Target date field
      FormFieldConfig<DateTime>(
        key: 'targetDate',
        type: FormFieldType.datePicker,
        label: 'Target Date',
        hintText: 'Select target date (optional)',
        isRequired: false,
        validator: (DateTime? value) {
          if (value != null && value.isBefore(DateTime.now())) {
            return 'Target date cannot be in the past';
          }
          return null;
        },
      ),

      // Status field
      DropdownFieldConfig<GoalStatus>(
        key: 'status',
        label: 'Status',
        items: GoalStatus.values,
        isRequired: true,
        initialValue: GoalStatus.active, // Set default value
        displayStringForItem: (GoalStatus status) {
          switch (status) {
            case GoalStatus.active:
              return 'Active';
            case GoalStatus.paused:
              return 'Paused';
            case GoalStatus.completed:
              return 'Completed';
            case GoalStatus.cancelled:
              return 'Cancelled';
          }
        },
      ),

      // Color field
      const ColorPickerFieldConfig(
        key: 'color',
        label: 'Goal Color',
        isRequired: false,
        availableColors: FormConstants.goalColors,
      ),

      // Icon field
      const IconPickerFieldConfig(
        key: 'icon',
        label: 'Goal Icon',
        isRequired: false,
        availableIcons: FormConstants.goalIcons,
      ),
    ];
  }

  /// Handle goal creation
  static Future<void> _handleCreate(
    IGoalRepository repository,
    Map<String, dynamic> formData,
  ) async {
    final goal = _GoalDataMapper().formDataToEntity(formData);
    await repository.createGoal(goal);
  }

  /// Handle goal update
  static Future<void> _handleUpdate(
    IGoalRepository repository,
    Goal originalGoal,
    Map<String, dynamic> formData,
  ) async {
    final updatedGoal = _GoalDataMapper().formDataToEntity(formData);
    await repository.updateGoal(originalGoal.id, updatedGoal);
  }

  /// Handle goal deletion
  static Future<void> _handleDelete(
    IGoalRepository repository,
    Goal goal,
  ) async {
    await repository.deleteGoal(goal.id);
  }
}

/// Data mapper for Goal entities
class _GoalDataMapper extends GenericFormDataMapper<Goal> {
  @override
  Map<String, dynamic> entityToFormData(Goal entity) {
    return {
      'name': entity.name,
      'description': entity.description ?? '',
      'targetAmount': (entity.targetAmountCents / 100.0).toStringAsFixed(2),
      'targetDate': entity.targetDate,
      'status': entity.status,
      'color': entity.colorHex,
      'icon': entity.iconName,
    };
  }

  @override
  Goal formDataToEntity(Map<String, dynamic> formData) {
    // Parse target amount from string or number to cents
    final targetAmountValue = formData['targetAmount'];
    var targetAmount = 0.0;

    if (targetAmountValue is String) {
      targetAmount = double.tryParse(targetAmountValue) ?? 0;
    } else if (targetAmountValue is num) {
      targetAmount = targetAmountValue.toDouble();
    }

    final targetAmountCents = (targetAmount * 100).round();

    return Goal.create(
      userId: '', // Will be set by repository
      name: formData['name'] as String? ?? '',
      description: _cleanDescription(formData['description'] as String?),
      targetAmountCents: targetAmountCents,
      targetDate: formData['targetDate'] as DateTime?,
      status: formData['status'] as GoalStatus? ?? GoalStatus.active,
      colorHex: formData['color'] as String?,
      iconName: formData['icon'] as String?,
    );
  }

  /// Clean description by converting empty or whitespace-only strings to null
  String? _cleanDescription(String? description) {
    if (description == null || description.trim().isEmpty) {
      return null;
    }
    return description;
  }
}
