import 'package:flutter/material.dart';

/// Generic interface for form fields that can be used in the generic form system
///
/// This interface provides a contract for form fields to integrate with the
/// BaseEditableFormScreen and FormFieldFactory. It supports type-safe value
/// management, validation, and error handling.
abstract class IFormField<T> {
  /// The unique key identifying this form field
  String get key;

  /// The current value of the form field
  T? get value;

  /// Sets the value of the form field
  set value(T? newValue);

  /// Whether this field is required
  bool get isRequired;

  /// The current error message, if any
  String? get errorText;

  /// Sets the error message for this field
  set errorText(String? error);

  /// Whether this field has been modified from its initial value
  bool get isDirty;

  /// Validates the current value and returns an error message if invalid
  String? validate();

  /// Clears any validation errors
  void clearError();

  /// Resets the field to its initial state
  void reset();

  /// Builds the widget representation of this form field
  Widget build(BuildContext context);

  /// Called when the field value changes
  void onChanged(T? newValue);

  /// Called when the field loses focus
  void onFieldSubmitted();

  /// Disposes of any resources used by this form field
  void dispose();
}

/// Base implementation of IFormField that provides common functionality
abstract class BaseFormField<T> implements IFormField<T> {
  BaseFormField({
    required this.key,
    this.initialValue,
    this.isRequired = false,
    this.validator,
    this.onValueChanged,
  }) : _value = initialValue;

  @override
  final String key;

  @override
  final bool isRequired;

  /// Initial value of the field
  final T? initialValue;

  /// Custom validator function
  final String? Function(T? value)? validator;

  /// Callback when value changes
  final void Function(T? value)? onValueChanged;

  T? _value;
  String? _errorText;
  bool _isDirty = false;

  @override
  T? get value => _value;

  @override
  set value(T? newValue) {
    if (_value != newValue) {
      _value = newValue;
      _isDirty = true;
      onChanged(newValue);
    }
  }

  @override
  String? get errorText => _errorText;

  @override
  set errorText(String? error) {
    _errorText = error;
  }

  @override
  bool get isDirty => _isDirty;

  @override
  String? validate() {
    // Clear previous error
    _errorText = null;

    // Run custom validator first if provided
    if (validator != null) {
      _errorText = validator!(value);
      if (_errorText != null) {
        return _errorText;
      }
    }

    // Check required validation only if no custom validator or custom validator passed
    if (isRequired && (value == null || _isEmptyValue(value))) {
      return _errorText = 'This field is required';
    }

    return _errorText;
  }

  @override
  void clearError() {
    _errorText = null;
  }

  @override
  void reset() {
    _value = initialValue;
    _errorText = null;
    _isDirty = false;
  }

  @override
  void onChanged(T? newValue) {
    onValueChanged?.call(newValue);
  }

  @override
  void onFieldSubmitted() {
    // Default implementation - can be overridden
  }

  @override
  void dispose() {
    // Default implementation - can be overridden
  }

  /// Checks if a value is considered empty for validation purposes
  bool _isEmptyValue(T? value) {
    if (value == null) return true;
    if (value is String) return value.trim().isEmpty;
    if (value is List) return value.isEmpty;
    if (value is Map) return value.isEmpty;
    return false;
  }
}
