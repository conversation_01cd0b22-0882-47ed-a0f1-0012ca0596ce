import 'package:budapp/data/models/tag.dart';
import 'package:budapp/data/repositories/interfaces/tag_repository.dart';
import 'package:budapp/widgets/forms/configs/tag_form_config.dart';
import 'package:budapp/widgets/forms/screens/generic_form_screen.dart';
import 'package:flutter/material.dart';

/// Example usage of GenericFormScreen for Tag entities
///
/// This demonstrates how to use the generic form system to create
/// both create and edit screens for tags with minimal code.

/// Create Tag Screen Example
class CreateTagScreenExample extends StatelessWidget {
  const CreateTagScreenExample({super.key, required this.tagRepository});

  final TagRepository tagRepository;

  @override
  Widget build(BuildContext context) {
    final config = TagFormConfig.create(
      repository: tagRepository,
      onFieldChanged: (fieldKey, value) {
        // Optional: Handle field changes for real-time validation or updates
        debugPrint('Tag field $fieldKey changed to: $value');
      },
    );

    return GenericFormScreen<Tag>(config: config);
  }
}
