import 'package:budapp/data/models/budget.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/interfaces/form_field_interface.dart';
import 'package:flutter/material.dart';

/// Implementation of IFormField for budget type selection
///
/// This implementation provides a segmented button for selecting budget type
/// (income vs expense) with appropriate visual styling and validation.
class BudgetTypeFormFieldImpl extends BaseFormField<BudgetType> {
  BudgetTypeFormFieldImpl(FormFieldConfig<BudgetType> config)
    : _config = config,
      super(
        key: config.key,
        initialValue: config.initialValue,
        isRequired: config.isRequired,
        validator: config.validator,
        onValueChanged: config.onChanged,
      );

  final FormFieldConfig<BudgetType> _config;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (_config.label.isNotEmpty) ...[
          Text(
            _config.label,
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
        ],

        // Budget Type Segmented Button
        SegmentedButton<BudgetType>(
          segments: [
            ButtonSegment(
              value: BudgetType.expense,
              label: Text(l10n.expense),
              icon: const Icon(Icons.trending_down),
            ),
            ButtonSegment(
              value: BudgetType.income,
              label: Text(l10n.income),
              icon: const Icon(Icons.trending_up),
            ),
          ],
          selected: value != null ? {value!} : <BudgetType>{},
          emptySelectionAllowed: true,
          onSelectionChanged: (Set<BudgetType> selection) {
            if (selection.isNotEmpty) {
              value = selection.first;
            } else {
              value = null;
            }
          },
          showSelectedIcon: false,
          style: SegmentedButton.styleFrom(
            backgroundColor: theme.colorScheme.surface,
            foregroundColor: theme.colorScheme.onSurface,
            selectedBackgroundColor: theme.colorScheme.primaryContainer,
            selectedForegroundColor: theme.colorScheme.onPrimaryContainer,
          ),
        ),

        // Error text
        if (errorText != null) ...[
          const SizedBox(height: 4),
          Text(
            errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],

        // Help text
        if ((_config.hintText?.isNotEmpty ?? false) && errorText == null) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _config.hintText!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  @override
  void onFieldSubmitted() {
    // Budget type selector doesn't have a traditional submit action
  }

  @override
  void dispose() {
    // No resources to dispose for this field
  }
}
