import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/interfaces/form_field_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Implementation of IFormField for amount input with currency formatting
///
/// This implementation provides an enhanced amount input field with:
/// - Currency symbol display
/// - Proper decimal formatting
/// - Input validation
/// - Visual feedback for validation state
class AmountFormFieldImpl extends BaseFormField<double> {
  AmountFormFieldImpl(FormFieldConfig<double> config)
    : _config = config,
      super(
        key: config.key,
        initialValue: config.initialValue,
        isRequired: config.isRequired,
        validator: config.validator,
        onValueChanged: config.onChanged,
      );

  final FormFieldConfig<double> _config;

  @override
  Widget build(BuildContext context) {
    return _AmountFormFieldWidget(config: _config, field: this);
  }
}

/// Stateful widget for the amount form field implementation
class _AmountFormFieldWidget extends StatefulWidget {
  const _AmountFormFieldWidget({required this.config, required this.field});

  final FormFieldConfig<double> config;
  final AmountFormFieldImpl field;

  @override
  State<_AmountFormFieldWidget> createState() => _AmountFormFieldWidgetState();
}

class _AmountFormFieldWidgetState extends State<_AmountFormFieldWidget> {
  late final TextEditingController _controller;
  late final FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _focusNode = FocusNode();

    // Initialize controller with initial value if provided
    if (widget.config.initialValue != null) {
      _controller.text = widget.config.initialValue!.toStringAsFixed(2);
    }

    // Add listeners
    _controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _focusNode.removeListener(_onFocusChanged);
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final text = _controller.text.trim();
    if (text.isEmpty) {
      widget.field.value = null;
    } else {
      final parsedValue = double.tryParse(text);
      if (parsedValue != null) {
        widget.field.value = parsedValue;
      }
    }
    // Trigger validation to update errorText
    widget.field.validate();
    // Trigger rebuild to update validation icon
    setState(() {});
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus && widget.field.value != null) {
      // Format the value when focus is lost
      _controller.text = widget.field.value!.toStringAsFixed(2);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Consumer(
      builder: (context, ref, child) {
        final currencyFormatter = ref.watch(currencyFormatterProvider);

        return _buildAmountField(context, theme, currencyFormatter.symbol);
      },
    );
  }

  Widget _buildAmountField(
    BuildContext context,
    ThemeData theme,
    String currencySymbol,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (widget.config.label.isNotEmpty) ...[
          Text(
            widget.config.label,
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
        ],

        // Amount Input Field
        TextFormField(
          controller: _controller,
          focusNode: _focusNode,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
          ],
          decoration: InputDecoration(
            hintText: widget.config.hintText ?? '0.00',
            prefixIcon: Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                currencySymbol,
                style: theme.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ),
            prefixIconConstraints: const BoxConstraints(minWidth: 0),
            suffixIcon: _buildValidationIcon(theme),
            errorText: widget.field.errorText,
            border: const OutlineInputBorder(),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.end,
          validator: (text) {
            if (widget.config.validator != null) {
              final amount = (text?.isEmpty ?? false)
                  ? null
                  : double.tryParse(text!);
              return widget.config.validator!(amount);
            }
            return null;
          },
        ),

        // Help text
        if ((widget.config.hintText?.isNotEmpty ?? false) &&
            widget.field.errorText == null) ...[
          const SizedBox(height: 4),
          Text(
            'Enter the transaction amount in $currencySymbol',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ],
    );
  }

  Widget? _buildValidationIcon(ThemeData theme) {
    if (_controller.text.isEmpty) return null;

    final amount = double.tryParse(_controller.text);
    if (amount == null) {
      return Icon(Icons.error, color: theme.colorScheme.error, size: 20);
    }

    if (widget.field.errorText == null && amount > 0) {
      return Icon(
        Icons.check_circle,
        color: theme.colorScheme.primary,
        size: 20,
      );
    }

    return null;
  }
}
