import 'package:budapp/widgets/common/app_text_form_field.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/interfaces/form_field_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Implementation of IFormField for text-based form fields
///
/// This implementation uses the existing AppTextFormField component and
/// provides integration with the generic form system.
class TextFormFieldImpl<T> extends BaseFormField<T> {
  TextFormFieldImpl(FormFieldConfig<T> config)
    : _config = config,
      _controller = TextEditingController(
        text: config.initialValue?.toString() ?? '',
      ),
      super(
        key: config.key,
        initialValue: config.initialValue,
        isRequired: config.isRequired,
        validator: config.validator,
        onValueChanged: config.onChanged,
      ) {
    _controller.addListener(_onTextChanged);
  }

  final FormFieldConfig<T> _config;
  final TextEditingController _controller;

  @override
  Widget build(BuildContext context) {
    return AppTextFormField(
      key: ValueKey(key), // Pass the field key to the widget
      label: _config.label,
      controller: _controller,
      hintText: _config.hintText,
      validator: _createValidator(),
      enabled: _config.enabled,
      isRequired: _config.isRequired,
      errorText: errorText,
      useFloatingLabel: true,
      keyboardType: _getKeyboardType(),
      textInputAction: _getTextInputAction(),
      textCapitalization: _getTextCapitalization(),
      maxLength: _getMaxLength(),
      maxLines: _getMaxLines(),
      minLines: _getMinLines(),
      prefixIcon: _getPrefixIcon(),
      suffixIcon: _getSuffixIcon(),
      isPassword: _shouldObscureText(),
      inputFormatters: _getInputFormatters(),
      onChanged: _updateValue,
      onFieldSubmitted: (value) {
        onFieldSubmitted();
      },
    );
  }

  @override
  void dispose() {
    _controller
      ..removeListener(_onTextChanged)
      ..dispose();
    super.dispose();
  }

  void _onTextChanged() {
    _updateValue(_controller.text);
  }

  void _updateValue(String? textValue) {
    T? newValue;

    if (textValue == null || textValue.isEmpty) {
      newValue = null;
    } else {
      // Convert string to the appropriate type
      if (T == String) {
        newValue = textValue as T;
      } else if (T == int) {
        newValue = int.tryParse(textValue) as T?;
      } else if (T == double) {
        newValue = double.tryParse(textValue) as T?;
      } else {
        // For other types, try to parse as string
        newValue = textValue as T?;
      }
    }

    value = newValue;
  }

  /// Creates a type-safe validator function for the underlying Flutter form field
  String? Function(String?)? _createValidator() {
    return (String? textValue) {
      // Update the internal value first
      _updateValue(textValue);
      // Then validate using our generic validation system
      return validate();
    };
  }

  // Helper methods to extract configuration properties

  TextInputType _getKeyboardType() {
    // Check if config is TextFieldConfig and has keyboardType
    if (_config is TextFieldConfig) {
      return (_config as TextFieldConfig).keyboardType;
    }

    // Default based on field type
    switch (_config.type) {
      case FormFieldType.text:
        return TextInputType.text;
      case FormFieldType.email:
        return TextInputType.emailAddress;
      case FormFieldType.number:
        return TextInputType.number;
      case FormFieldType.currency:
        return const TextInputType.numberWithOptions(decimal: true);
      case FormFieldType.multilineText:
        return TextInputType.multiline;
      case FormFieldType.password:
        return TextInputType.visiblePassword;
      case FormFieldType.dropdown:
      case FormFieldType.colorPicker:
      case FormFieldType.iconPicker:
      case FormFieldType.datePicker:
      case FormFieldType.typeSelector:
      case FormFieldType.accountSelector:
      case FormFieldType.categorySelector:
      case FormFieldType.custom:
        return TextInputType.text;
    }
  }

  TextInputAction _getTextInputAction() {
    if (_config is TextFieldConfig) {
      return (_config as TextFieldConfig).textInputAction;
    }
    return _getMaxLines() > 1 ? TextInputAction.newline : TextInputAction.next;
  }

  TextCapitalization _getTextCapitalization() {
    if (_config is TextFieldConfig) {
      return (_config as TextFieldConfig).textCapitalization;
    }

    switch (_config.type) {
      case FormFieldType.email:
        return TextCapitalization.none;
      case FormFieldType.text:
      case FormFieldType.password:
      case FormFieldType.multilineText:
      case FormFieldType.number:
      case FormFieldType.currency:
      case FormFieldType.dropdown:
      case FormFieldType.colorPicker:
      case FormFieldType.iconPicker:
      case FormFieldType.datePicker:
      case FormFieldType.typeSelector:
      case FormFieldType.accountSelector:
      case FormFieldType.categorySelector:
      case FormFieldType.custom:
        return TextCapitalization.sentences;
    }
  }

  int? _getMaxLength() {
    if (_config is TextFieldConfig) {
      return (_config as TextFieldConfig).maxLength;
    }
    return null;
  }

  int _getMaxLines() {
    if (_config is TextFieldConfig) {
      return (_config as TextFieldConfig).maxLines;
    }
    return 1;
  }

  int? _getMinLines() {
    if (_config is TextFieldConfig) {
      return (_config as TextFieldConfig).minLines;
    }
    return null;
  }

  Widget? _getPrefixIcon() {
    if (_config is TextFieldConfig) {
      return (_config as TextFieldConfig).prefixIcon;
    }
    return null;
  }

  Widget? _getSuffixIcon() {
    if (_config is TextFieldConfig) {
      return (_config as TextFieldConfig).suffixIcon;
    }
    return null;
  }

  bool _shouldObscureText() {
    return _config.type == FormFieldType.password;
  }

  List<TextInputFormatter>? _getInputFormatters() {
    switch (_config.type) {
      case FormFieldType.number:
        return [FilteringTextInputFormatter.digitsOnly];
      case FormFieldType.currency:
        return [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}'))];
      case FormFieldType.text:
      case FormFieldType.email:
      case FormFieldType.password:
      case FormFieldType.multilineText:
      case FormFieldType.dropdown:
      case FormFieldType.colorPicker:
      case FormFieldType.iconPicker:
      case FormFieldType.datePicker:
      case FormFieldType.typeSelector:
      case FormFieldType.accountSelector:
      case FormFieldType.categorySelector:
      case FormFieldType.custom:
        return null;
    }
  }

  @override
  set value(T? newValue) {
    super.value = newValue;
    // Update the controller text if the value was set programmatically
    final textValue = newValue?.toString() ?? '';
    if (_controller.text != textValue) {
      _controller.text = textValue;
    }
  }
}
