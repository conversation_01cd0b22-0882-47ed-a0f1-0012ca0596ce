import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/interfaces/form_field_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Implementation of IFormField for account selection
///
/// This implementation provides a dropdown for selecting accounts with:
/// - Async account loading from repository
/// - Support for transfer logic (from/to account selection)
/// - Account filtering based on transaction type
/// - Balance display for better user context
class AccountSelectorFormFieldImpl extends BaseFormField<String?> {
  AccountSelectorFormFieldImpl(FormFieldConfig<String?> config)
    : _config = config,
      super(
        key: config.key,
        initialValue: config.initialValue,
        isRequired: config.isRequired,
        validator: config.validator,
        onValueChanged: config.onChanged,
      );

  final FormFieldConfig<String?> _config;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Consumer(
      builder: (context, ref, child) {
        final accountsAsync = ref.watch(accountListProvider);
        final currencyAsync = ref.watch(currencyFormatterProvider);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            if (_config.label.isNotEmpty) ...[
              Text(
                _config.label,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
            ],

            // Account Selector
            accountsAsync.when(
              data: (accounts) => _buildAccountDropdown(
                context,
                theme,
                accounts,
                currencyAsync.symbol,
              ),
              loading: () => _buildLoadingDropdown(context, theme),
              error: (error, _) =>
                  _buildErrorDropdown(context, theme, error.toString()),
            ),

            // Help text
            if ((_config.hintText?.isNotEmpty ?? false) &&
                errorText == null) ...[
              const SizedBox(height: 4),
              Text(
                _getHelpText(),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  Widget _buildAccountDropdown(
    BuildContext context,
    ThemeData theme,
    List<Account> accounts,
    String currencySymbol,
  ) {
    // Filter accounts based on context if needed
    final filteredAccounts = _filterAccounts(accounts);

    return DropdownButtonFormField<String?>(
      value: value,
      decoration: InputDecoration(
        hintText: _config.hintText ?? 'Select account',
        errorText: errorText,
        border: const OutlineInputBorder(),
        prefixIcon: const Icon(Icons.account_balance_wallet),
      ),
      items: [
        // None option if not required
        if (!isRequired)
          const DropdownMenuItem<String?>(value: null, child: Text('None')),
        // Account options
        ...filteredAccounts.map((account) {
          return DropdownMenuItem<String?>(
            value: account.id,
            child: IntrinsicWidth(
              child: _buildAccountItem(account, currencySymbol),
            ),
          );
        }),
      ],
      onChanged: (newValue) {
        value = newValue;
      },
      validator: (selectedValue) {
        if (_config.validator != null) {
          return _config.validator!(selectedValue);
        }
        return null;
      },
    );
  }

  Widget _buildAccountItem(Account account, String currencySymbol) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Account icon
        Icon(_getAccountIcon(account.type), size: 20, color: Colors.grey[600]),
        const SizedBox(width: 12),

        // Account name
        Expanded(
          child: Text(
            account.name,
            style: const TextStyle(fontWeight: FontWeight.w500),
            overflow: TextOverflow.ellipsis,
          ),
        ),

        const SizedBox(width: 8),

        // Account balance
        Text(
          '$currencySymbol${(account.currentBalanceCents / 100).toStringAsFixed(2)}',
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildLoadingDropdown(BuildContext context, ThemeData theme) {
    return DropdownButtonFormField<String?>(
      value: null,
      decoration: InputDecoration(
        hintText: 'Loading accounts...',
        errorText: errorText,
        border: const OutlineInputBorder(),
        prefixIcon: const SizedBox(
          width: 20,
          height: 20,
          child: Center(child: CircularProgressIndicator(strokeWidth: 2)),
        ),
      ),
      items: const [],
      onChanged: null,
    );
  }

  Widget _buildErrorDropdown(
    BuildContext context,
    ThemeData theme, [
    String? error,
  ]) {
    return Container(
      height: 56,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: theme.colorScheme.error),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Icons.error, color: theme.colorScheme.error),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              error ?? 'Error loading accounts',
              style: TextStyle(color: theme.colorScheme.error),
            ),
          ),
        ],
      ),
    );
  }

  List<Account> _filterAccounts(List<Account> accounts) {
    // Get transaction type from custom properties if available
    final excludeAccountId =
        _config.customProperties['excludeAccountId'] as String?;

    var filtered = accounts.where((account) => account.isActive).toList();

    // Exclude specific account (useful for transfer destination selection)
    if (excludeAccountId != null) {
      filtered = filtered
          .where((account) => account.id != excludeAccountId)
          .toList();
    }

    return filtered;
  }

  IconData _getAccountIcon(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return Icons.account_balance;
      case AccountType.savings:
        return Icons.savings;
      case AccountType.creditCard:
        return Icons.credit_card;
      case AccountType.investment:
        return Icons.trending_up;
      case AccountType.cash:
        return Icons.payments;
      case AccountType.loan:
        return Icons.account_balance_wallet;
    }
  }

  String _getHelpText() {
    final transactionType =
        _config.customProperties['transactionType'] as TransactionType?;

    switch (transactionType) {
      case TransactionType.income:
        return 'Select the account where money will be deposited';
      case TransactionType.expense:
        return 'Select the account money will be withdrawn from';
      case TransactionType.transfer:
        return _config.key == 'toAccountId'
            ? 'Select the destination account for the transfer'
            : 'Select the source account for the transfer';
      case null:
        return _config.hintText ?? 'Select an account';
    }
  }

  @override
  void onFieldSubmitted() {
    // Account selector doesn't have a traditional submit action
  }

  @override
  void dispose() {
    // No resources to dispose for this field
  }
}
