import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/interfaces/form_field_interface.dart';
import 'package:flutter/material.dart';

/// Implementation of IFormField for dropdown/selection form fields
///
/// This implementation provides a simple dropdown interface that can be
/// used with the generic form system.
class DropdownFormFieldImpl<T> extends BaseFormField<T> {
  DropdownFormFieldImpl(FormFieldConfig<T> config)
    : _config = config,
      super(
        key: config.key,
        initialValue: config.initialValue,
        isRequired: config.isRequired,
        validator: config.validator,
        onValueChanged: config.onChanged,
      );

  final FormFieldConfig<T> _config;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final items = _getItems();
    final itemBuilder = _getItemBuilder();
    final displayStringForItem = _getDisplayStringForItem();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Text(
          _config.label,
          style: theme.textTheme.labelLarge?.copyWith(
            fontWeight: AppTypography.fontWeightMedium,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),

        // Dropdown
        DropdownButtonFormField<T>(
          value: value,
          onChanged: _config.enabled
              ? (newValue) {
                  value = newValue;
                }
              : null,
          validator: _createValidator(),
          decoration: InputDecoration(
            hintText: _config.hintText,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSpacing.sm),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSpacing.sm),
              borderSide: BorderSide(color: theme.colorScheme.outline),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSpacing.sm),
              borderSide: BorderSide(
                color: theme.colorScheme.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSpacing.sm),
              borderSide: BorderSide(color: theme.colorScheme.error),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSpacing.sm),
              borderSide: BorderSide(color: theme.colorScheme.error, width: 2),
            ),
            filled: true,
            fillColor: theme.colorScheme.surfaceContainerHighest.withValues(
              alpha: 0.3,
            ),
          ),
          items: items.map((item) {
            return DropdownMenuItem<T>(
              value: item,
              child:
                  itemBuilder?.call(item) ??
                  Text(
                    displayStringForItem?.call(item) ?? item.toString(),
                    style: theme.textTheme.bodyMedium,
                    overflow: TextOverflow.ellipsis,
                  ),
            );
          }).toList(),
          isExpanded: true,
        ),

        // Error text
        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  List<T> _getItems() {
    if (_config is DropdownFieldConfig<T>) {
      return _config.items;
    }
    return <T>[];
  }

  Widget Function(T item)? _getItemBuilder() {
    if (_config is DropdownFieldConfig<T>) {
      return _config.itemBuilder;
    }
    return null;
  }

  String Function(T item)? _getDisplayStringForItem() {
    if (_config is DropdownFieldConfig<T>) {
      return _config.displayStringForItem;
    }
    return null;
  }

  /// Creates a type-safe validator function for the underlying Flutter form field
  String? Function(T?)? _createValidator() {
    if (validator == null) return null;

    return (T? value) {
      // Update the internal value first
      this.value = value;
      // Run custom validator directly with proper type
      return validator!(value);
    };
  }
}
