import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/interfaces/form_field_interface.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// Implementation of IFormField for date picker form fields
///
/// This implementation provides a date selection interface that can be
/// used with the generic form system.
class DatePickerFormFieldImpl extends BaseFormField<DateTime> {
  DatePickerFormFieldImpl(FormFieldConfig<DateTime> config)
    : _config = config,
      super(
        key: config.key,
        initialValue: config.initialValue,
        isRequired: config.isRequired,
        validator: config.validator,
        onValueChanged: config.onChanged,
      );

  final FormFieldConfig<DateTime> _config;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final dateFormat = _getDateFormat();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Text(
          _config.label,
          style: theme.textTheme.labelLarge?.copyWith(
            fontWeight: AppTypography.fontWeightMedium,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),

        // Date picker field
        InkWell(
          onTap: _config.enabled ? () => _showDatePicker(context) : null,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.md,
              vertical: AppSpacing.md,
            ),
            decoration: BoxDecoration(
              border: Border.all(
                color: errorText != null
                    ? theme.colorScheme.error
                    : theme.colorScheme.outline,
              ),
              borderRadius: BorderRadius.circular(AppSpacing.sm),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: theme.colorScheme.onSurface,
                  size: 20,
                ),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: Text(
                    value != null
                        ? dateFormat.format(value!)
                        : _config.hintText ?? 'Select date',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: value != null
                          ? theme.colorScheme.onSurface
                          : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ),
                if (value != null && _config.enabled)
                  IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      value = null;
                    },
                    iconSize: 20,
                  ),
              ],
            ),
          ),
        ),

        // Error text
        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _showDatePicker(BuildContext context) async {
    final firstDate = _getFirstDate();
    final lastDate = _getLastDate();
    final initialDate = value ?? DateTime.now();

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
    );

    if (selectedDate != null) {
      value = selectedDate;
    }
  }

  DateFormat _getDateFormat() {
    String? formatString;

    if (_config is DatePickerFieldConfig) {
      formatString = _config.dateFormat;
    }

    if (formatString != null) {
      return DateFormat(formatString);
    }

    return DateFormat.yMd(); // Default format
  }

  DateTime _getFirstDate() {
    if (_config is DatePickerFieldConfig) {
      return _config.firstDate ?? DateTime(1900);
    }
    return DateTime(1900);
  }

  DateTime _getLastDate() {
    if (_config is DatePickerFieldConfig) {
      return _config.lastDate ?? DateTime(2100);
    }
    return DateTime(2100);
  }
}
