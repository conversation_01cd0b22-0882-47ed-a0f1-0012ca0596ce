import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/repositories/interfaces/category_repository.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/interfaces/form_field_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Implementation of IFormField for parent category selection
///
/// This implementation provides a dropdown for selecting a parent category
/// with constraints to ensure proper hierarchy (only root categories can be parents).
class ParentCategoryFormFieldImpl extends BaseFormField<String?> {
  ParentCategoryFormFieldImpl(FormFieldConfig<String?> config)
    : _config = config,
      super(
        key: config.key,
        initialValue: config.initialValue,
        isRequired: config.isRequired,
        validator: config.validator,
        onValueChanged: config.onChanged,
      );

  final FormFieldConfig<String?> _config;

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final repository = ref.watch(categoryRepositoryProvider);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            if (_config.label.isNotEmpty) ...[
              Text(
                _config.label,
                style: Theme.of(
                  context,
                ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
            ],

            // Parent Category Dropdown
            FutureBuilder<List<Category>>(
              future: _loadRootCategories(repository),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const SizedBox(
                    height: 56,
                    child: Center(child: CircularProgressIndicator()),
                  );
                }

                if (snapshot.hasError) {
                  return Container(
                    height: 56,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Theme.of(context).colorScheme.error,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'Error loading categories: ${snapshot.error}',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  );
                }

                final rootCategories = snapshot.data ?? [];

                return DropdownButtonFormField<String?>(
                  value: value,
                  decoration: InputDecoration(
                    hintText:
                        _config.hintText ?? 'Select parent category (optional)',
                    errorText: errorText,
                    border: const OutlineInputBorder(),
                  ),
                  items: [
                    // None option for root categories
                    const DropdownMenuItem<String?>(
                      value: null,
                      child: Text('None (Root Category)'),
                    ),
                    // Root categories as options
                    ...rootCategories.map((category) {
                      return DropdownMenuItem<String?>(
                        value: category.id,
                        child: Text(
                          '${category.name} (${_getCategoryTypeDisplayName(category.type)})',
                        ),
                      );
                    }),
                  ],
                  onChanged: (newValue) {
                    value = newValue;
                  },
                  validator: (selectedValue) {
                    // Use the provided validator if any
                    if (_config.validator != null) {
                      return _config.validator!(selectedValue);
                    }
                    return null;
                  },
                );
              },
            ),

            // Help text
            if (_config.hintText?.isNotEmpty ?? false) ...[
              const SizedBox(height: 4),
              Text(
                'Select a parent category to create a subcategory, or leave empty for a root category.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  @override
  void onFieldSubmitted() {
    // Parent category selector doesn't have a traditional submit action
  }

  @override
  void dispose() {
    // No resources to dispose for this field
  }

  /// Load root categories (categories with parentId = null) from repository
  Future<List<Category>> _loadRootCategories(
    ICategoryRepository repository,
  ) async {
    // Get all active categories and filter for root categories
    final allCategories = await repository.getActiveCategories();
    return allCategories
        .where((category) => category.parentId == null)
        .toList();
  }

  /// Get display name for category type
  String _getCategoryTypeDisplayName(CategoryType type) {
    switch (type) {
      case CategoryType.income:
        return 'Income';
      case CategoryType.expense:
        return 'Expense';
    }
  }
}
