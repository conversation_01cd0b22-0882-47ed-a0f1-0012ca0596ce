import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/utils/icon_utils.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/interfaces/form_field_interface.dart';
import 'package:flutter/material.dart';

/// Implementation of IFormField for icon picker form fields
///
/// This implementation provides an icon selection interface that can be
/// used with the generic form system.
class IconPickerFormFieldImpl extends BaseFormField<String> {
  IconPickerFormFieldImpl(FormFieldConfig<String> config)
    : _config = config,
      super(
        key: config.key,
        initialValue: config.initialValue,
        isRequired: config.isRequired,
        validator: config.validator,
        onValueChanged: config.onChanged,
      );

  final FormFieldConfig<String> _config;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final availableIcons = _getAvailableIcons();
    final iconSize = _getIconSize();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Text(
          _config.label,
          style: theme.textTheme.labelLarge?.copyWith(
            fontWeight: AppTypography.fontWeightMedium,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),

        // Icon selection grid
        Wrap(
          spacing: AppSpacing.sm,
          runSpacing: AppSpacing.sm,
          children: availableIcons.map((iconName) {
            final isSelected = value == iconName;
            final iconData = _getIconData(iconName);

            return GestureDetector(
              onTap: _config.enabled
                  ? () {
                      value = iconName;
                    }
                  : null,
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: isSelected
                      ? theme.colorScheme.primaryContainer
                      : theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(AppSpacing.sm),
                  border: Border.all(
                    color: isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.outline,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Icon(
                  iconData,
                  size: iconSize,
                  color: isSelected
                      ? theme.colorScheme.onPrimaryContainer
                      : theme.colorScheme.onSurface,
                ),
              ),
            );
          }).toList(),
        ),

        // Error text
        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  List<String> _getAvailableIcons() {
    if (_config is IconPickerFieldConfig) {
      return _config.availableIcons.isNotEmpty
          ? _config.availableIcons
          : _getDefaultIcons();
    }

    // Fallback to default icons
    return _getDefaultIcons();
  }

  List<String> _getDefaultIcons() {
    return [
      'account_balance_wallet',
      'savings',
      'credit_card',
      'attach_money',
      'account_balance',
      'business',
      'home',
      'directions_car',
      'shopping_cart',
      'restaurant',
      'local_gas_station',
      'medical_services',
      'school',
      'fitness_center',
      'movie',
      'music_note',
    ];
  }

  double _getIconSize() {
    if (_config is IconPickerFieldConfig) {
      return _config.iconSize;
    }
    return 24;
  }

  IconData _getIconData(String iconName) {
    return iconFromName(iconName, fallback: Icons.help_outline);
  }
}
