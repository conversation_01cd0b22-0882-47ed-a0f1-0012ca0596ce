import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/interfaces/form_field_interface.dart';
import 'package:flutter/material.dart';

/// Implementation of IFormField for transaction type selection
///
/// This implementation provides a segmented button for selecting transaction type
/// with conditional logic for handling transfers vs income/expense transactions.
class TransactionTypeFormFieldImpl extends BaseFormField<TransactionType> {
  TransactionTypeFormFieldImpl(FormFieldConfig<TransactionType> config)
    : _config = config,
      super(
        key: config.key,
        initialValue: config.initialValue,
        isRequired: config.isRequired,
        validator: config.validator,
        onValueChanged: config.onChanged,
      );

  final FormFieldConfig<TransactionType> _config;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (_config.label.isNotEmpty) ...[
          Text(
            _config.label,
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
        ],

        // Transaction Type Segmented Button
        SegmentedButton<TransactionType>(
          segments: [
            ButtonSegment(
              value: TransactionType.income,
              label: Text(l10n.income),
              icon: const Icon(Icons.trending_up),
            ),
            ButtonSegment(
              value: TransactionType.expense,
              label: Text(l10n.expense),
              icon: const Icon(Icons.trending_down),
            ),
            ButtonSegment(
              value: TransactionType.transfer,
              label: Text(l10n.transfer),
              icon: const Icon(Icons.swap_horiz),
            ),
          ],
          selected: value != null ? {value!} : <TransactionType>{},
          emptySelectionAllowed: true,
          onSelectionChanged: (Set<TransactionType> selection) {
            if (selection.isNotEmpty) {
              value = selection.first;
            } else {
              value = null;
            }
          },
          showSelectedIcon: false,
          style: SegmentedButton.styleFrom(
            backgroundColor: theme.colorScheme.surface,
            foregroundColor: theme.colorScheme.onSurface,
            selectedBackgroundColor: theme.colorScheme.primaryContainer,
            selectedForegroundColor: theme.colorScheme.onPrimaryContainer,
          ),
        ),

        // Error text
        if (errorText != null) ...[
          const SizedBox(height: 4),
          Text(
            errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],

        // Help text for transfers
        if (value == TransactionType.transfer) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: theme.colorScheme.primary.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Transfers move money between your accounts. Categories are not used for transfers.',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  @override
  void onFieldSubmitted() {
    // Transaction type selector doesn't have a traditional submit action
  }

  @override
  void dispose() {
    // No resources to dispose for this field
  }
}
