import 'package:budapp/data/models/account.dart';
import 'package:budapp/features/accounts/presentation/widgets/account_type_selector.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/interfaces/form_field_interface.dart';
import 'package:flutter/material.dart';

/// Implementation of IFormField for account type selection
///
/// This implementation provides an account type and classification selection
/// interface that can be used with the generic form system.
class AccountTypeFormFieldImpl extends BaseFormField<Map<String, dynamic>> {
  AccountTypeFormFieldImpl(FormFieldConfig<Map<String, dynamic>> config)
    : _config = config,
      super(
        key: config.key,
        initialValue: config.initialValue,
        isRequired: config.isRequired,
        validator: config.validator,
        onValueChanged: config.onChanged,
      );

  final FormFieldConfig<Map<String, dynamic>> _config;

  AccountType? get selectedType => value?['type'] as AccountType?;
  AccountClassification? get selectedClassification =>
      value?['classification'] as AccountClassification?;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (_config.label.isNotEmpty) ...[
          Text(
            _config.label,
            style: Theme.of(
              context,
            ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
        ],

        // Account Type Selector
        AccountTypeSelector(
          selectedType: selectedType,
          selectedClassification: selectedClassification,
          onTypeChanged: (type, classification) {
            if (type != null && classification != null) {
              value = {'type': type, 'classification': classification};
            } else {
              value = null;
            }
          },
          errorText: errorText,
          showLabel:
              false, // Don't show label since form field already provides one
        ),
      ],
    );
  }

  @override
  void onFieldSubmitted() {
    // Account type selector doesn't have a traditional submit action
  }

  @override
  void dispose() {
    // No resources to dispose for this field
  }
}
