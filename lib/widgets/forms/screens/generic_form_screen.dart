import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/config/generic_form_config.dart';
import 'package:budapp/widgets/forms/screens/base_editable_form_screen.dart';
import 'package:flutter/material.dart';

/// Generic form screen that can handle any entity type
///
/// This screen uses a configuration-driven approach to build forms dynamically
/// based on the provided GenericFormConfig. It supports both create and edit
/// modes with consistent validation and submission handling.
class GenericFormScreen<T> extends StatelessWidget {
  const GenericFormScreen({super.key, required this.config});

  /// Configuration that defines the form structure and behavior
  final GenericFormConfig<T> config;

  @override
  Widget build(BuildContext context) {
    // Get initial form data from entity if in edit mode
    final initialFormData = config.getInitialFormData();

    // Update field configs with initial values from entity data
    final updatedFieldConfigs = _updateFieldConfigsWithInitialValues(
      config.fields,
      initialFormData,
    );

    return BaseEditableFormScreen(
      title: config.title,
      fieldConfigs: updatedFieldConfigs,
      onSubmit: config.onSubmit,
      initialEntity: config.initialData,
      isLoading: false,
      submitButtonText: config.getEffectiveSaveButtonText(),
      showDeleteButton: config.showDeleteButton && config.onDelete != null,
      onDelete: config.onDelete,
      deleteButtonText: config.getEffectiveDeleteButtonText(),
      customValidator: config.validator,
      onFieldChanged: config.onFieldChanged,
      customActions: config.customActions,
    );
  }

  /// Updates form field configurations with initial values from entity data
  List<FormFieldConfig<dynamic>> _updateFieldConfigsWithInitialValues(
    List<FormFieldConfig<dynamic>> fieldConfigs,
    Map<String, dynamic> initialFormData,
  ) {
    return fieldConfigs.map((config) {
      final initialValue = initialFormData[config.key];

      // Only update if we have initial data for this field
      if (initialValue != null) {
        return config.copyWith(initialValue: initialValue);
      }

      return config;
    }).toList();
  }
}
