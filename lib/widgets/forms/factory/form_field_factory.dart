import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/implementations/account_selector_form_field_impl.dart';
import 'package:budapp/widgets/forms/implementations/account_type_form_field_impl.dart';
import 'package:budapp/widgets/forms/implementations/amount_form_field_impl.dart';
import 'package:budapp/widgets/forms/implementations/budget_period_form_field_impl.dart';
import 'package:budapp/widgets/forms/implementations/budget_type_form_field_impl.dart';
import 'package:budapp/widgets/forms/implementations/category_type_form_field_impl.dart';
import 'package:budapp/widgets/forms/implementations/color_picker_form_field_impl.dart';
import 'package:budapp/widgets/forms/implementations/date_picker_form_field_impl.dart';
import 'package:budapp/widgets/forms/implementations/dropdown_form_field_impl.dart';
import 'package:budapp/widgets/forms/implementations/icon_picker_form_field_impl.dart';
import 'package:budapp/widgets/forms/implementations/parent_category_form_field_impl.dart';
import 'package:budapp/widgets/forms/implementations/text_form_field_impl.dart';
import 'package:budapp/widgets/forms/implementations/transaction_type_form_field_impl.dart';
import 'package:budapp/widgets/forms/interfaces/form_field_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Factory class for creating form field widgets from configuration
///
/// This factory takes FormFieldConfig objects and creates the appropriate
/// IFormField implementations. It serves as the bridge between declarative
/// configuration and actual widget creation.
// ignore: avoid_classes_with_only_static_members
class FormFieldFactory {
  /// Creates a form field widget from the given configuration
  static IFormField<T> createField<T>(FormFieldConfig<T> config) {
    switch (config.type) {
      case FormFieldType.text:
      case FormFieldType.email:
      case FormFieldType.password:
      case FormFieldType.multilineText:
        return TextFormFieldImpl<String>(config as FormFieldConfig<String>)
            as IFormField<T>;

      case FormFieldType.number:
      case FormFieldType.currency:
        return TextFormFieldImpl<String>(config as FormFieldConfig<String>)
            as IFormField<T>;

      case FormFieldType.dropdown:
        return DropdownFormFieldImpl<T>(config);

      case FormFieldType.colorPicker:
        return ColorPickerFormFieldImpl(config as ColorPickerFieldConfig)
            as IFormField<T>;

      case FormFieldType.iconPicker:
        return IconPickerFormFieldImpl(config as FormFieldConfig<String>)
            as IFormField<T>;

      case FormFieldType.datePicker:
        return DatePickerFormFieldImpl(config as FormFieldConfig<DateTime>)
            as IFormField<T>;

      case FormFieldType.typeSelector:
      case FormFieldType.accountSelector:
      case FormFieldType.categorySelector:
        return DropdownFormFieldImpl<T>(config);

      case FormFieldType.custom:
        // Handle custom field types based on the field key
        if (config.key == 'accountType') {
          return AccountTypeFormFieldImpl(
                config as FormFieldConfig<Map<String, dynamic>>,
              )
              as IFormField<T>;
        }
        if (config.key == 'parentId') {
          return ParentCategoryFormFieldImpl(config as FormFieldConfig<String?>)
              as IFormField<T>;
        }
        if (config.key == 'transactionType') {
          return TransactionTypeFormFieldImpl(
                config as FormFieldConfig<TransactionType>,
              )
              as IFormField<T>;
        }
        if (config.key == 'amount') {
          return AmountFormFieldImpl(config as FormFieldConfig<double>)
              as IFormField<T>;
        }
        if (config.key == 'accountId' ||
            config.key == 'fromAccountId' ||
            config.key == 'toAccountId') {
          return AccountSelectorFormFieldImpl(
                config as FormFieldConfig<String?>,
              )
              as IFormField<T>;
        }
        if (config.key == 'budgetType') {
          return BudgetTypeFormFieldImpl(config as FormFieldConfig<BudgetType>)
              as IFormField<T>;
        }
        if (config.key == 'budgetPeriod') {
          return BudgetPeriodFormFieldImpl(
                config as FormFieldConfig<BudgetPeriod>,
              )
              as IFormField<T>;
        }
        if (config.key == 'type') {
          return CategoryTypeFormFieldImpl(
                config as FormFieldConfig<CategoryType>,
              )
              as IFormField<T>;
        }
        throw UnsupportedError(
          'Unsupported custom field type for key: ${config.key}',
        );
    }
  }

  /// Creates a widget from a form field configuration
  static Widget createWidget<T>(
    FormFieldConfig<T> config, {
    IFormField<T>? existingField,
  }) {
    final field = existingField ?? createField(config);
    return _FormFieldWrapper<T>(field: field, config: config);
  }
}

/// Wrapper widget that handles the rendering of form fields
class _FormFieldWrapper<T> extends ConsumerWidget {
  const _FormFieldWrapper({required this.field, required this.config});

  final IFormField<T> field;
  final FormFieldConfig<T> config;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Build the actual form field
        field.build(context),

        // Add spacing if there's an error
        if (field.errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            field.errorText!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }
}

/// Provider for form field factory
final formFieldFactoryProvider = Provider<FormFieldFactory>((ref) {
  return FormFieldFactory();
});

/// Extension methods for easier form field creation
extension FormFieldConfigExtensions on FormFieldConfig<dynamic> {
  /// Creates a form field widget from this configuration
  Widget toWidget({IFormField<dynamic>? existingField}) {
    return FormFieldFactory.createWidget(this, existingField: existingField);
  }

  /// Creates a form field implementation from this configuration
  IFormField<dynamic> createField() {
    return FormFieldFactory.createField(this);
  }
}

/// Convenience methods for creating common field configurations
// ignore: avoid_classes_with_only_static_members
class FormFieldConfigs {
  /// Creates a text field configuration
  static TextFieldConfig text({
    required String key,
    required String label,
    String? hintText,
    String? initialValue,
    bool isRequired = false,
    String? Function(String? value)? validator,
    bool enabled = true,
    bool autofocus = false,
    void Function(String? value)? onChanged,
    int? maxLength,
    int maxLines = 1,
    TextCapitalization textCapitalization = TextCapitalization.sentences,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return TextFieldConfig(
      key: key,
      label: label,
      hintText: hintText,
      initialValue: initialValue,
      isRequired: isRequired,
      validator: validator,
      enabled: enabled,
      autofocus: autofocus,
      onChanged: onChanged,
      maxLength: maxLength,
      maxLines: maxLines,
      textCapitalization: textCapitalization,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
    );
  }

  /// Creates an email field configuration
  static TextFieldConfig email({
    required String key,
    required String label,
    String? hintText,
    String? initialValue,
    bool isRequired = false,
    String? Function(String? value)? validator,
    bool enabled = true,
    bool autofocus = false,
    void Function(String? value)? onChanged,
  }) {
    return TextFieldConfig(
      key: key,
      label: label,
      hintText: hintText ?? '<EMAIL>',
      initialValue: initialValue,
      isRequired: isRequired,
      validator: validator,
      enabled: enabled,
      autofocus: autofocus,
      onChanged: onChanged,
      keyboardType: TextInputType.emailAddress,
      textCapitalization: TextCapitalization.none,
      prefixIcon: const Icon(Icons.email_outlined),
    );
  }

  /// Creates a dropdown field configuration
  static DropdownFieldConfig<T> dropdown<T>({
    required String key,
    required String label,
    required List<T> items,
    String? hintText,
    T? initialValue,
    bool isRequired = false,
    String? Function(T? value)? validator,
    bool enabled = true,
    bool autofocus = false,
    void Function(T? value)? onChanged,
    Widget Function(T item)? itemBuilder,
    String Function(T item)? displayStringForItem,
  }) {
    return DropdownFieldConfig<T>(
      key: key,
      label: label,
      items: items,
      hintText: hintText,
      initialValue: initialValue,
      isRequired: isRequired,
      validator: validator,
      enabled: enabled,
      autofocus: autofocus,
      onChanged: onChanged,
      itemBuilder: itemBuilder,
      displayStringForItem: displayStringForItem,
    );
  }

  /// Creates a color picker field configuration
  static ColorPickerFieldConfig colorPicker({
    required String key,
    required String label,
    String? hintText,
    String? initialValue,
    bool isRequired = false,
    String? Function(String? value)? validator,
    bool enabled = true,
    bool autofocus = false,
    void Function(String? value)? onChanged,
    List<String> availableColors = const [],
    bool showCustomColorOption = true,
  }) {
    return ColorPickerFieldConfig(
      key: key,
      label: label,
      hintText: hintText,
      initialValue: initialValue,
      isRequired: isRequired,
      validator: validator,
      enabled: enabled,
      autofocus: autofocus,
      onChanged: onChanged,
      availableColors: availableColors,
      showCustomColorOption: showCustomColorOption,
    );
  }
}
