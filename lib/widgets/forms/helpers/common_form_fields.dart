import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:flutter/material.dart';

/// Helper class providing common form field configurations
///
/// This class reduces boilerplate by providing predefined configurations
/// for commonly used form fields across different entity types.
class CommonFormFields {
  CommonFormFields._();

  /// Standard name field configuration
  static TextFieldConfig name({
    String? initialValue,
    String? label,
    String? hintText,
    bool isRequired = true,
    String? Function(String?)? customValidator,
  }) {
    return TextFieldConfig(
      key: 'name',
      label: label ?? 'Name',
      hintText: hintText ?? 'Enter name',
      initialValue: initialValue,
      isRequired: isRequired,
      validator:
          customValidator ??
          (value) {
            if (isRequired && (value == null || value.isEmpty)) {
              return 'Name is required';
            }
            if (value != null && value.length > 100) {
              return 'Name must be 100 characters or less';
            }
            return null;
          },
      keyboardType: TextInputType.text,
      textCapitalization: TextCapitalization.words,
      maxLength: 100,
    );
  }

  /// Standard description field configuration
  static TextFieldConfig description({
    String? initialValue,
    String? label,
    String? hintText,
    bool isRequired = false,
    String? Function(String?)? customValidator,
    int maxLines = 3,
    int? maxLength = 500,
  }) {
    return TextFieldConfig(
      key: 'description',
      label: label ?? 'Description',
      hintText: hintText ?? 'Enter description (optional)',
      initialValue: initialValue,
      isRequired: isRequired,
      validator:
          customValidator ??
          (value) {
            if (value == null || value.isEmpty) return null; // Optional field
            if (value.length > (maxLength ?? 500)) {
              return 'Description must be ${maxLength ?? 500} characters or less';
            }
            return null;
          },
      keyboardType: TextInputType.multiline,
      textCapitalization: TextCapitalization.sentences,
      maxLines: maxLines,
      maxLength: maxLength,
    );
  }

  /// Standard amount field configuration
  static TextFieldConfig amount({
    String? initialValue,
    String? label,
    String? hintText,
    bool isRequired = true,
    String? Function(String?)? customValidator,
  }) {
    return TextFieldConfig(
      key: 'amount',
      label: label ?? 'Amount',
      hintText: hintText ?? '0.00',
      initialValue: initialValue,
      isRequired: isRequired,
      validator:
          customValidator ??
          (value) {
            if (isRequired && (value == null || value.isEmpty)) {
              return 'Amount is required';
            }
            if (value != null && value.isNotEmpty) {
              final amount = double.tryParse(value);
              if (amount == null) {
                return 'Please enter a valid amount';
              }
              if (amount < 0) {
                return 'Amount cannot be negative';
              }
            }
            return null;
          },
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      prefixIcon: const Icon(Icons.attach_money),
    );
  }

  /// Standard email field configuration
  static TextFieldConfig email({
    String? initialValue,
    String? label,
    String? hintText,
    bool isRequired = true,
    String? Function(String?)? customValidator,
  }) {
    return TextFieldConfig(
      key: 'email',
      label: label ?? 'Email',
      hintText: hintText ?? '<EMAIL>',
      initialValue: initialValue,
      isRequired: isRequired,
      validator:
          customValidator ??
          (value) {
            if (!isRequired && (value == null || value.isEmpty)) return null;

            if (value == null || value.isEmpty) {
              return 'Email is required';
            }

            final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
            if (!emailRegex.hasMatch(value)) {
              return 'Please enter a valid email address';
            }

            return null;
          },
      keyboardType: TextInputType.emailAddress,
      textCapitalization: TextCapitalization.none,
      prefixIcon: const Icon(Icons.email_outlined),
    );
  }

  /// Standard password field configuration
  static TextFieldConfig password({
    String? initialValue,
    String? label,
    String? hintText,
    bool isRequired = true,
    String? Function(String?)? customValidator,
    int minLength = 8,
  }) {
    return TextFieldConfig(
      key: 'password',
      label: label ?? 'Password',
      hintText: hintText ?? 'Enter password',
      initialValue: initialValue,
      isRequired: isRequired,
      validator:
          customValidator ??
          (value) {
            if (value == null || value.isEmpty) {
              return 'Password is required';
            }
            if (value.length < minLength) {
              return 'Password must be at least $minLength characters';
            }
            return null;
          },
      keyboardType: TextInputType.visiblePassword,
      prefixIcon: const Icon(Icons.lock_outlined),
    );
  }

  /// Color selector field configuration
  static ColorPickerFieldConfig colorSelector({
    String? initialValue,
    String? label,
    List<String>? availableColors,
    bool isRequired = false,
    String? Function(String?)? customValidator,
  }) {
    return ColorPickerFieldConfig(
      key: 'color',
      label: label ?? 'Color',
      initialValue: initialValue,
      isRequired: isRequired,
      validator: customValidator,
      availableColors:
          availableColors ??
          [
            '#F44336',
            '#E91E63',
            '#9C27B0',
            '#673AB7',
            '#3F51B5',
            '#2196F3',
            '#03A9F4',
            '#00BCD4',
            '#009688',
            '#4CAF50',
            '#8BC34A',
            '#CDDC39',
            '#FFEB3B',
            '#FFC107',
            '#FF9800',
            '#FF5722',
          ],
    );
  }

  /// Icon selector field configuration
  static IconPickerFieldConfig iconSelector({
    String? initialValue,
    String? label,
    List<String>? availableIcons,
    bool isRequired = false,
    String? Function(String?)? customValidator,
    double iconSize = 24.0,
  }) {
    return IconPickerFieldConfig(
      key: 'icon',
      label: label ?? 'Icon',
      initialValue: initialValue,
      isRequired: isRequired,
      validator: customValidator,
      availableIcons:
          availableIcons ??
          [
            'account_balance_wallet',
            'savings',
            'credit_card',
            'attach_money',
            'account_balance',
            'business',
            'home',
            'car_rental',
            'shopping_cart',
            'restaurant',
            'local_gas_station',
            'medical_services',
            'school',
            'fitness_center',
            'movie',
            'music_note',
          ],
      iconSize: iconSize,
    );
  }

  /// Date picker field configuration
  static DatePickerFieldConfig datePicker({
    DateTime? initialValue,
    String? label,
    String? hintText,
    bool isRequired = true,
    String? Function(DateTime?)? customValidator,
    DateTime? firstDate,
    DateTime? lastDate,
    String? dateFormat,
  }) {
    return DatePickerFieldConfig(
      key: 'date',
      label: label ?? 'Date',
      hintText: hintText ?? 'Select date',
      initialValue: initialValue,
      isRequired: isRequired,
      validator:
          customValidator ??
          (value) {
            if (!isRequired && value == null) return null;
            if (value == null) return 'Date is required';
            return null;
          },
      firstDate: firstDate ?? DateTime(1900),
      lastDate: lastDate ?? DateTime(2100),
      dateFormat: dateFormat,
    );
  }

  /// Account type dropdown configuration
  static DropdownFieldConfig<AccountType> accountTypeDropdown({
    AccountType? initialValue,
    String? label,
    bool isRequired = true,
    String? Function(AccountType?)? customValidator,
  }) {
    return DropdownFieldConfig<AccountType>(
      key: 'accountType',
      label: label ?? 'Account Type',
      initialValue: initialValue,
      isRequired: isRequired,
      validator:
          customValidator ??
          (value) {
            if (isRequired && value == null) return 'Account type is required';
            return null;
          },
      items: AccountType.values,
      displayStringForItem: (type) => type.toString().split('.').last,
    );
  }

  /// Category type dropdown configuration
  static DropdownFieldConfig<CategoryType> categoryTypeDropdown({
    CategoryType? initialValue,
    String? label,
    bool isRequired = true,
    String? Function(CategoryType?)? customValidator,
  }) {
    return DropdownFieldConfig<CategoryType>(
      key: 'categoryType',
      label: label ?? 'Category Type',
      initialValue: initialValue,
      isRequired: isRequired,
      validator:
          customValidator ??
          (value) {
            if (isRequired && value == null) return 'Category type is required';
            return null;
          },
      items: CategoryType.values,
      displayStringForItem: (type) => type.toString().split('.').last,
    );
  }

  /// Transaction type dropdown configuration
  static DropdownFieldConfig<TransactionType> transactionTypeDropdown({
    TransactionType? initialValue,
    String? label,
    bool isRequired = true,
    String? Function(TransactionType?)? customValidator,
  }) {
    return DropdownFieldConfig<TransactionType>(
      key: 'transactionType',
      label: label ?? 'Transaction Type',
      initialValue: initialValue,
      isRequired: isRequired,
      validator:
          customValidator ??
          (value) {
            if (isRequired && value == null) {
              return 'Transaction type is required';
            }
            return null;
          },
      items: TransactionType.values,
      displayStringForItem: (type) => type.toString().split('.').last,
    );
  }
}
