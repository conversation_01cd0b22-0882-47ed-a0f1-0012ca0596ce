import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:flutter/material.dart';

/// Configuration class for GenericFormScreen
///
/// This class defines all the necessary configuration for creating a generic
/// form screen that can handle both create and edit modes for any entity type.
class GenericFormConfig<T> {
  const GenericFormConfig({
    required this.title,
    required this.fields,
    required this.onSubmit,
    this.initialData,
    this.onDelete,
    this.saveButtonText,
    this.deleteButtonText,
    this.showDeleteButton = false,
    this.dataMapper,
    this.validator,
    this.onFieldChanged,
    this.customActions,
  });

  /// Title for the form screen
  final String title;

  /// List of field configurations that define the form structure
  final List<FormFieldConfig<dynamic>> fields;

  /// Initial data for edit mode (null for create mode)
  final T? initialData;

  /// Callback for form submission
  /// Receives the form data as Map&lt;String, dynamic&gt;
  final Future<void> Function(Map<String, dynamic> data) onSubmit;

  /// Optional callback for delete action (only shown if provided)
  final Future<void> Function()? onDelete;

  /// Custom text for the save button (defaults to "Save" or "Create")
  final String? saveButtonText;

  /// Custom text for the delete button (defaults to "Delete")
  final String? deleteButtonText;

  /// Whether to show the delete button (only relevant for edit mode)
  final bool showDeleteButton;

  /// Optional data mapper to convert between entity and form data
  final GenericFormDataMapper<T>? dataMapper;

  /// Optional custom form validator
  final String? Function(Map<String, dynamic> data)? validator;

  /// Optional callback when any field value changes
  final void Function(String fieldKey, dynamic value)? onFieldChanged;

  /// Optional custom actions to show in the app bar or form
  final List<Widget>? customActions;

  /// Whether this is create mode (initialData is null)
  bool get isCreateMode => initialData == null;

  /// Whether this is edit mode (initialData is not null)
  bool get isEditMode => initialData != null;

  /// Get the effective save button text
  String getEffectiveSaveButtonText() {
    if (saveButtonText != null) return saveButtonText!;
    return isCreateMode ? 'Create' : 'Save';
  }

  /// Get the effective delete button text
  String getEffectiveDeleteButtonText() {
    return deleteButtonText ?? 'Delete';
  }

  /// Convert initial data to form data map
  Map<String, dynamic> getInitialFormData() {
    if (initialData == null) return {};

    if (dataMapper != null) {
      return dataMapper!.entityToFormData(initialData as T);
    }

    // Default: try to use toJson if available
    if (initialData is Map<String, dynamic>) {
      return initialData! as Map<String, dynamic>;
    }

    // Try to call toJson method if it exists
    try {
      final dynamic data = initialData;
      if (data != null && data.runtimeType.toString().contains('toJson')) {
        return (data as dynamic).toJson() as Map<String, dynamic>;
      }
    } on Exception {
      // Ignore and return empty map
    }

    return {};
  }

  /// Create a copy with updated properties
  GenericFormConfig<T> copyWith({
    String? title,
    List<FormFieldConfig<dynamic>>? fields,
    T? initialData,
    Future<void> Function(Map<String, dynamic> data)? onSubmit,
    Future<void> Function()? onDelete,
    String? saveButtonText,
    String? deleteButtonText,
    bool? showDeleteButton,
    GenericFormDataMapper<T>? dataMapper,
    String? Function(Map<String, dynamic> data)? validator,
    void Function(String fieldKey, dynamic value)? onFieldChanged,
    List<Widget>? customActions,
  }) {
    return GenericFormConfig<T>(
      title: title ?? this.title,
      fields: fields ?? this.fields,
      initialData: initialData ?? this.initialData,
      onSubmit: onSubmit ?? this.onSubmit,
      onDelete: onDelete ?? this.onDelete,
      saveButtonText: saveButtonText ?? this.saveButtonText,
      deleteButtonText: deleteButtonText ?? this.deleteButtonText,
      showDeleteButton: showDeleteButton ?? this.showDeleteButton,
      dataMapper: dataMapper ?? this.dataMapper,
      validator: validator ?? this.validator,
      onFieldChanged: onFieldChanged ?? this.onFieldChanged,
      customActions: customActions ?? this.customActions,
    );
  }
}

/// Data mapper interface for converting between entity objects and form data
abstract class GenericFormDataMapper<T> {
  /// Convert entity object to form data map
  Map<String, dynamic> entityToFormData(T entity);

  /// Convert form data map to entity object
  T formDataToEntity(Map<String, dynamic> formData);

  /// Update existing entity with form data
  T updateEntityWithFormData(T entity, Map<String, dynamic> formData) {
    // Default implementation: create new entity from form data
    return formDataToEntity(formData);
  }
}

/// Default data mapper that uses toJson/fromJson methods
class DefaultFormDataMapper<T> extends GenericFormDataMapper<T> {
  DefaultFormDataMapper({required this.fromJsonFactory, this.toJsonMethod});

  /// Factory function to create entity from JSON
  final T Function(Map<String, dynamic> json) fromJsonFactory;

  /// Optional custom toJson method (uses entity.toJson() if not provided)
  final Map<String, dynamic> Function(T entity)? toJsonMethod;

  @override
  Map<String, dynamic> entityToFormData(T entity) {
    if (toJsonMethod != null) {
      return toJsonMethod!(entity);
    }

    // Try to call toJson method
    try {
      final dynamic data = entity;
      return (data as dynamic).toJson() as Map<String, dynamic>;
    } on Exception {
      throw Exception(
        'Entity does not have a toJson method and no custom toJsonMethod provided',
      );
    }
  }

  @override
  T formDataToEntity(Map<String, dynamic> formData) {
    return fromJsonFactory(formData);
  }
}

/// Form validation result
class FormValidationResult {
  const FormValidationResult({
    required this.isValid,
    this.errors = const {},
    this.globalError,
  });

  /// Create an invalid result with errors
  const FormValidationResult.invalid({
    Map<String, String> errors = const {},
    String? globalError,
  }) : this(isValid: false, errors: errors, globalError: globalError);

  /// Whether the form is valid
  final bool isValid;

  /// Field-specific error messages
  final Map<String, String> errors;

  /// Global form error message
  final String? globalError;

  /// Create a valid result
  static const FormValidationResult valid = FormValidationResult(isValid: true);
}
