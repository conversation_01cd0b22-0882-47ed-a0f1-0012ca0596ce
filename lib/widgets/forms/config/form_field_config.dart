import 'package:flutter/material.dart';

/// Enum defining the types of form fields supported by the generic form system
enum FormFieldType {
  text,
  email,
  password,
  multilineText,
  number,
  currency,
  dropdown,
  colorPicker,
  iconPicker,
  datePicker,
  typeSelector,
  accountSelector,
  categorySelector,
  custom,
}

/// Base configuration class for form fields
///
/// This class provides a declarative way to configure form fields for use
/// with the generic form system. It contains all the common properties
/// that form fields need.
class FormFieldConfig<T> {
  const FormFieldConfig({
    required this.key,
    required this.type,
    required this.label,
    this.hintText,
    this.initialValue,
    this.isRequired = false,
    this.validator,
    this.enabled = true,
    this.autofocus = false,
    this.onChanged,
    this.customProperties = const {},
  });

  /// Unique identifier for this field
  final String key;

  /// Type of form field to create
  final FormFieldType type;

  /// Label text displayed for the field
  final String label;

  /// Hint text displayed in the field
  final String? hintText;

  /// Initial value for the field
  final T? initialValue;

  /// Whether this field is required
  final bool isRequired;

  /// Custom validator function
  final String? Function(T? value)? validator;

  /// Whether the field is enabled
  final bool enabled;

  /// Whether the field should autofocus
  final bool autofocus;

  /// Callback when field value changes
  final void Function(T? value)? onChanged;

  /// Custom properties specific to field types
  final Map<String, dynamic> customProperties;

  /// Creates a copy of this config with updated properties
  FormFieldConfig<T> copyWith({
    String? key,
    FormFieldType? type,
    String? label,
    String? hintText,
    T? initialValue,
    bool? isRequired,
    String? Function(T? value)? validator,
    bool? enabled,
    bool? autofocus,
    void Function(T? value)? onChanged,
    Map<String, dynamic>? customProperties,
  }) {
    return FormFieldConfig<T>(
      key: key ?? this.key,
      type: type ?? this.type,
      label: label ?? this.label,
      hintText: hintText ?? this.hintText,
      initialValue: initialValue ?? this.initialValue,
      isRequired: isRequired ?? this.isRequired,
      validator: validator ?? this.validator,
      enabled: enabled ?? this.enabled,
      autofocus: autofocus ?? this.autofocus,
      onChanged: onChanged ?? this.onChanged,
      customProperties: customProperties ?? this.customProperties,
    );
  }
}

/// Specialized configuration for text fields
class TextFieldConfig extends FormFieldConfig<String> {
  const TextFieldConfig({
    required super.key,
    required super.label,
    super.hintText,
    super.initialValue,
    super.isRequired = false,
    super.validator,
    super.enabled = true,
    super.autofocus = false,
    super.onChanged,
    this.maxLength,
    this.maxLines = 1,
    this.minLines,
    this.textCapitalization = TextCapitalization.none,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.next,
    this.prefixIcon,
    this.suffixIcon,
  }) : super(type: FormFieldType.text);

  final int? maxLength;
  final int maxLines;
  final int? minLines;
  final TextCapitalization textCapitalization;
  final TextInputType keyboardType;
  final TextInputAction textInputAction;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
}

/// Specialized configuration for dropdown fields
class DropdownFieldConfig<T> extends FormFieldConfig<T> {
  const DropdownFieldConfig({
    required super.key,
    required super.label,
    required this.items,
    super.hintText,
    super.initialValue,
    super.isRequired = false,
    super.validator,
    super.enabled = true,
    super.autofocus = false,
    super.onChanged,
    this.itemBuilder,
    this.displayStringForItem,
  }) : super(type: FormFieldType.dropdown);

  final List<T> items;
  final Widget Function(T item)? itemBuilder;
  final String Function(T item)? displayStringForItem;
}

/// Specialized configuration for color picker fields
class ColorPickerFieldConfig extends FormFieldConfig<String> {
  const ColorPickerFieldConfig({
    required super.key,
    required super.label,
    super.hintText,
    super.initialValue,
    super.isRequired = false,
    super.validator,
    super.enabled = true,
    super.autofocus = false,
    super.onChanged,
    this.availableColors = const [],
    this.showCustomColorOption = true,
    this.allowNone = true,
    this.noneLabel = 'Default',
  }) : super(type: FormFieldType.colorPicker);

  final List<String> availableColors;
  final bool showCustomColorOption;
  final bool allowNone;
  final String noneLabel;

  @override
  ColorPickerFieldConfig copyWith({
    String? key,
    FormFieldType? type,
    String? label,
    String? hintText,
    String? initialValue,
    bool? isRequired,
    String? Function(String? value)? validator,
    bool? enabled,
    bool? autofocus,
    void Function(String? value)? onChanged,
    Map<String, dynamic>? customProperties,
    List<String>? availableColors,
    bool? showCustomColorOption,
    bool? allowNone,
    String? noneLabel,
  }) {
    return ColorPickerFieldConfig(
      key: key ?? this.key,
      label: label ?? this.label,
      hintText: hintText ?? this.hintText,
      initialValue: initialValue ?? this.initialValue,
      isRequired: isRequired ?? this.isRequired,
      validator: validator ?? this.validator,
      enabled: enabled ?? this.enabled,
      autofocus: autofocus ?? this.autofocus,
      onChanged: onChanged ?? this.onChanged,
      availableColors: availableColors ?? this.availableColors,
      showCustomColorOption:
          showCustomColorOption ?? this.showCustomColorOption,
      allowNone: allowNone ?? this.allowNone,
      noneLabel: noneLabel ?? this.noneLabel,
    );
  }
}

/// Specialized configuration for icon picker fields
class IconPickerFieldConfig extends FormFieldConfig<String> {
  const IconPickerFieldConfig({
    required super.key,
    required super.label,
    super.hintText,
    super.initialValue,
    super.isRequired = false,
    super.validator,
    super.enabled = true,
    super.autofocus = false,
    super.onChanged,
    this.availableIcons = const [],
    this.iconSize = 24.0,
    this.allowNone = true,
    this.noneLabel = 'Default',
  }) : super(type: FormFieldType.iconPicker);

  final List<String> availableIcons;
  final double iconSize;
  final bool allowNone;
  final String noneLabel;
}

/// Specialized configuration for date picker fields
class DatePickerFieldConfig extends FormFieldConfig<DateTime> {
  const DatePickerFieldConfig({
    required super.key,
    required super.label,
    super.hintText,
    super.initialValue,
    super.isRequired = false,
    super.validator,
    super.enabled = true,
    super.autofocus = false,
    super.onChanged,
    this.firstDate,
    this.lastDate,
    this.dateFormat,
  }) : super(type: FormFieldType.datePicker);

  final DateTime? firstDate;
  final DateTime? lastDate;
  final String? dateFormat;
}
