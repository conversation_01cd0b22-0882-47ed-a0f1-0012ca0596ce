import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/account.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/forms/selectors/generic_selector.dart';
import 'package:flutter/material.dart';

/// Generic type selector for enum-based selections
///
/// This widget provides a consistent interface for selecting from enum values
/// with proper localization and visual styling.
class TypeSelector<T> extends StatelessWidget {
  const TypeSelector({
    super.key,
    required this.items,
    required this.selectedValue,
    required this.onChanged,
    required this.getDisplayName,
    required this.getIcon,
    this.label,
    this.errorText,
    this.enabled = true,
    this.layout = SelectorLayout.list,
    this.getDescription,
    this.getColor,
  });

  /// List of enum values to select from
  final List<T> items;

  /// Currently selected value
  final T? selectedValue;

  /// Callback when selection changes
  final ValueChanged<T?> onChanged;

  /// Function to get display name for an item
  final String Function(T item, BuildContext context) getDisplayName;

  /// Function to get icon for an item
  final IconData Function(T item) getIcon;

  /// Optional function to get description for an item
  final String Function(T item, BuildContext context)? getDescription;

  /// Optional function to get color for an item
  final Color Function(T item, ThemeData theme)? getColor;

  /// Optional label for the selector
  final String? label;

  /// Error text to display
  final String? errorText;

  /// Whether the selector is enabled
  final bool enabled;

  /// Layout style for the selector
  final SelectorLayout layout;

  @override
  Widget build(BuildContext context) {
    return GenericSelector<T>(
      items: items,
      selectedValue: selectedValue,
      onChanged: onChanged,
      itemBuilder: _buildTypeItem,
      label: label,
      errorText: errorText,
      enabled: enabled,
      layout: layout,
      allowNone: false, // Type selectors typically require a selection
    );
  }

  Widget _buildTypeItem(
    BuildContext context,
    T item, {
    required bool isSelected,
  }) {
    final theme = Theme.of(context);
    final displayName = getDisplayName(item, context);
    final icon = getIcon(item);
    final description = getDescription?.call(item, context);
    final color = getColor?.call(item, theme);

    if (layout == SelectorLayout.segmented) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: AppSpacing.xs),
          Text(displayName),
        ],
      );
    }

    return Row(
      children: [
        // Type icon
        Container(
          padding: const EdgeInsets.all(AppSpacing.sm),
          decoration: BoxDecoration(
            color: color ?? theme.colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(AppBorderRadius.sm),
          ),
          child: Icon(
            icon,
            color: color != null ? Colors.white : theme.colorScheme.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: AppSpacing.md),

        // Type details
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                displayName,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: isSelected
                      ? AppTypography.fontWeightSemiBold
                      : AppTypography.fontWeightMedium,
                  color: isSelected
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurface,
                ),
              ),
              if (description != null) ...[
                const SizedBox(height: AppSpacing.xs),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ],
          ),
        ),

        // Selection indicator
        if (layout == SelectorLayout.list)
          Radio<T>(
            value: item,
            groupValue: selectedValue,
            onChanged: enabled ? onChanged : null,
            activeColor: theme.colorScheme.primary,
          ),
      ],
    );
  }
}

/// Specialized type selector for account types
class AccountTypeSelector extends StatelessWidget {
  const AccountTypeSelector({
    super.key,
    required this.selectedType,
    required this.selectedClassification,
    required this.onTypeChanged,
    this.errorText,
    this.enabled = true,
    this.showLabel = true,
  });

  final AccountType? selectedType;
  final AccountClassification? selectedClassification;
  final void Function(AccountType?, AccountClassification?) onTypeChanged;
  final String? errorText;
  final bool enabled;
  final bool showLabel;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showLabel) ...[
          Text(
            l10n.accountType,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: AppTypography.fontWeightMedium,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
        ],

        // Asset accounts section
        _buildSectionHeader(l10n.assets, context),
        const SizedBox(height: AppSpacing.xs),
        _buildTypeOption(
          context,
          AccountType.checking,
          AccountClassification.asset,
          l10n.checkingAccount,
          Icons.account_balance,
        ),
        _buildTypeOption(
          context,
          AccountType.savings,
          AccountClassification.asset,
          l10n.savingsAccount,
          Icons.savings,
        ),
        _buildTypeOption(
          context,
          AccountType.cash,
          AccountClassification.asset,
          l10n.cashAccount,
          Icons.payments,
        ),
        _buildTypeOption(
          context,
          AccountType.investment,
          AccountClassification.asset,
          l10n.investmentAccount,
          Icons.trending_up,
        ),

        const SizedBox(height: AppSpacing.md),

        // Liability accounts section
        _buildSectionHeader(l10n.liabilities, context),
        const SizedBox(height: AppSpacing.xs),
        _buildTypeOption(
          context,
          AccountType.creditCard,
          AccountClassification.liability,
          l10n.creditCardAccount,
          Icons.credit_card,
        ),
        _buildTypeOption(
          context,
          AccountType.loan,
          AccountClassification.liability,
          l10n.loanAccount,
          Icons.money_off,
        ),

        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSectionHeader(String title, BuildContext context) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleSmall?.copyWith(
        fontWeight: AppTypography.fontWeightSemiBold,
        color: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildTypeOption(
    BuildContext context,
    AccountType type,
    AccountClassification classification,
    String label,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    final isSelected = selectedType == type;

    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.xs),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: enabled ? () => onTypeChanged(type, classification) : null,
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
          child: Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.outline,
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(AppBorderRadius.md),
              color: isSelected
                  ? theme.colorScheme.primaryContainer.withValues(alpha: 0.3)
                  : Colors.transparent,
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: isSelected
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurfaceVariant,
                  size: 24,
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Text(
                    label,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurface,
                      fontWeight: isSelected
                          ? AppTypography.fontWeightMedium
                          : AppTypography.fontWeightRegular,
                    ),
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
