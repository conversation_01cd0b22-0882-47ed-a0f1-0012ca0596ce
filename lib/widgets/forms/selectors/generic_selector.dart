import 'package:budapp/config/design_tokens.dart';
import 'package:flutter/material.dart';

/// Generic selector widget that can be used for various selection types
///
/// This widget provides a flexible foundation for creating selectors with
/// different layouts (grid, list, segmented) and selection behaviors.
class GenericSelector<T> extends StatelessWidget {
  const GenericSelector({
    super.key,
    required this.items,
    required this.selectedValue,
    required this.onChanged,
    required this.itemBuilder,
    this.label,
    this.errorText,
    this.enabled = true,
    this.layout = SelectorLayout.grid,
    this.crossAxisCount = 4,
    this.spacing = AppSpacing.sm,
    this.runSpacing = AppSpacing.sm,
    this.allowNone = false,
    this.noneLabel = 'None',
    this.noneIcon,
  });

  /// List of items to select from
  final List<T> items;

  /// Currently selected value
  final T? selectedValue;

  /// Callback when selection changes
  final ValueChanged<T?> onChanged;

  /// Builder for individual item widgets
  final Widget Function(
    BuildContext context,
    T item, {
    required bool isSelected,
  })
  itemBuilder;

  /// Optional label for the selector
  final String? label;

  /// Error text to display
  final String? errorText;

  /// Whether the selector is enabled
  final bool enabled;

  /// Layout style for the selector
  final SelectorLayout layout;

  /// Number of columns for grid layout
  final int crossAxisCount;

  /// Spacing between items
  final double spacing;

  /// Run spacing for grid layout
  final double runSpacing;

  /// Whether to allow selecting "none" (null value)
  final bool allowNone;

  /// Label for the "none" option
  final String noneLabel;

  /// Icon for the "none" option
  final IconData? noneIcon;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (label != null) ...[
          Text(
            label!,
            style: theme.textTheme.labelLarge?.copyWith(
              fontWeight: AppTypography.fontWeightMedium,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
        ],

        // Selector content
        _buildSelector(context, theme),

        // Error text
        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSelector(BuildContext context, ThemeData theme) {
    switch (layout) {
      case SelectorLayout.grid:
        return _buildGridSelector(context, theme);
      case SelectorLayout.list:
        return _buildListSelector(context, theme);
      case SelectorLayout.segmented:
        return _buildSegmentedSelector(context, theme);
    }
  }

  Widget _buildGridSelector(BuildContext context, ThemeData theme) {
    final allItems = <T?>[];
    if (allowNone) allItems.add(null);
    allItems.addAll(items);

    return Wrap(
      spacing: spacing,
      runSpacing: runSpacing,
      children: allItems.map((item) {
        if (item == null) {
          return _buildNoneOption(context, theme);
        }
        return GestureDetector(
          onTap: enabled ? () => onChanged(item) : null,
          child: itemBuilder(context, item, isSelected: selectedValue == item),
        );
      }).toList(),
    );
  }

  Widget _buildListSelector(BuildContext context, ThemeData theme) {
    return DecoratedBox(
      decoration: BoxDecoration(
        border: Border.all(
          color: errorText != null
              ? theme.colorScheme.error
              : theme.colorScheme.outline,
        ),
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
      ),
      child: Column(
        children: [
          if (allowNone) _buildNoneListOption(context, theme),
          ...items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final isSelected = selectedValue == item;
            final isLast = index == items.length - 1;

            return InkWell(
              onTap: enabled ? () => onChanged(item) : null,
              borderRadius: BorderRadius.vertical(
                bottom: isLast
                    ? const Radius.circular(AppBorderRadius.md)
                    : Radius.zero,
              ),
              child: Container(
                padding: const EdgeInsets.all(AppSpacing.md),
                decoration: BoxDecoration(
                  color: isSelected
                      ? theme.colorScheme.primaryContainer.withValues(
                          alpha: 0.3,
                        )
                      : null,
                  border: !isLast
                      ? Border(
                          bottom: BorderSide(
                            color: theme.colorScheme.outline.withValues(
                              alpha: 0.5,
                            ),
                          ),
                        )
                      : null,
                ),
                child: itemBuilder(context, item, isSelected: isSelected),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildSegmentedSelector(BuildContext context, ThemeData theme) {
    // For segmented buttons, we need to handle the type conversion
    // This is a simplified implementation - real usage might need more type safety
    return SegmentedButton<T>(
      segments: items.map((item) {
        return ButtonSegment<T>(
          value: item,
          label: itemBuilder(context, item, isSelected: selectedValue == item),
        );
      }).toList(),
      selected: selectedValue != null ? {selectedValue as T} : {},
      onSelectionChanged: enabled
          ? (Set<T> selection) {
              onChanged(selection.isNotEmpty ? selection.first : null);
            }
          : null,
    );
  }

  Widget _buildNoneOption(BuildContext context, ThemeData theme) {
    final isSelected = selectedValue == null;

    return GestureDetector(
      onTap: enabled ? () => onChanged(null) : null,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          border: Border.all(
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.outline,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        ),
        child: Icon(
          noneIcon ?? Icons.clear,
          color: isSelected
              ? theme.colorScheme.primary
              : theme.colorScheme.onSurfaceVariant,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildNoneListOption(BuildContext context, ThemeData theme) {
    final isSelected = selectedValue == null;

    return InkWell(
      onTap: enabled ? () => onChanged(null) : null,
      borderRadius: const BorderRadius.vertical(
        top: Radius.circular(AppBorderRadius.md),
      ),
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.md),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primaryContainer.withValues(alpha: 0.3)
              : null,
          border: Border(
            bottom: BorderSide(
              color: theme.colorScheme.outline.withValues(alpha: 0.5),
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(
              noneIcon ?? Icons.clear,
              color: isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: AppSpacing.md),
            Text(
              noneLabel,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface,
                fontWeight: isSelected
                    ? AppTypography.fontWeightMedium
                    : AppTypography.fontWeightRegular,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Layout options for the generic selector
enum SelectorLayout { grid, list, segmented }
