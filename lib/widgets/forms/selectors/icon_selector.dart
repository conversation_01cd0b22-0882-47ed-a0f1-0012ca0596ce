import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/utils/icon_utils.dart';
import 'package:budapp/widgets/forms/selectors/generic_selector.dart';
import 'package:flutter/material.dart';

/// Reusable icon selector widget
///
/// This widget provides a consistent icon selection interface that can be
/// used across different entity types (accounts, categories, tags).
class IconSelector extends StatelessWidget {
  const IconSelector({
    super.key,
    required this.selectedIcon,
    required this.onIconChanged,
    this.label,
    this.errorText,
    this.enabled = true,
    this.availableIcons,
    this.allowNone = true,
    this.noneLabel = 'Default',
    this.layout = SelectorLayout.grid,
  });

  /// Currently selected icon name
  final String? selectedIcon;

  /// Callback when icon selection changes
  final ValueChanged<String?> onIconChanged;

  /// Optional label for the selector
  final String? label;

  /// Error text to display
  final String? errorText;

  /// Whether the selector is enabled
  final bool enabled;

  /// Custom list of available icon names
  /// If null, uses default icon set
  final List<String>? availableIcons;

  /// Whether to allow selecting no icon (null)
  final bool allowNone;

  /// Label for the "no icon" option
  final String noneLabel;

  /// Layout style for the selector
  final SelectorLayout layout;

  /// Default icon set
  static const List<String> defaultIcons = [
    'account_balance_wallet',
    'savings',
    'credit_card',
    'attach_money',
    'account_balance',
    'business',
    'home',
    'car_rental',
    'shopping_cart',
    'restaurant',
    'local_gas_station',
    'medical_services',
    'school',
    'fitness_center',
    'movie',
    'music_note',
    'work',
    'flight',
    'hotel',
    'local_grocery_store',
    'local_pharmacy',
    'pets',
    'sports_esports',
    'directions_bike',
  ];

  @override
  Widget build(BuildContext context) {
    final icons = availableIcons ?? defaultIcons;

    return GenericSelector<String>(
      items: icons,
      selectedValue: selectedIcon,
      onChanged: onIconChanged,
      itemBuilder: _buildIconItem,
      label: label,
      errorText: errorText,
      enabled: enabled,
      layout: layout,
      allowNone: allowNone,
      noneLabel: noneLabel,
      noneIcon: Icons.category_outlined,
      crossAxisCount: 5,
    );
  }

  Widget _buildIconItem(
    BuildContext context,
    String iconName, {
    required bool isSelected,
  }) {
    final theme = Theme.of(context);
    final iconData = iconFromName(iconName);

    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: isSelected
            ? theme.colorScheme.primaryContainer
            : theme.colorScheme.surfaceContainerHighest,
        border: Border.all(
          color: isSelected
              ? theme.colorScheme.primary
              : theme.colorScheme.outline,
          width: isSelected ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
      ),
      child: Icon(
        iconData,
        color: isSelected
            ? theme.colorScheme.primary
            : theme.colorScheme.onSurfaceVariant,
        size: 24,
      ),
    );
  }
}

/// Specialized icon selector for accounts
class AccountIconSelector extends StatelessWidget {
  const AccountIconSelector({
    super.key,
    required this.selectedIcon,
    required this.onIconChanged,
    this.errorText,
    this.enabled = true,
  });

  final String? selectedIcon;
  final ValueChanged<String?> onIconChanged;
  final String? errorText;
  final bool enabled;

  static const List<String> accountIcons = [
    'account_balance_wallet',
    'account_balance',
    'savings',
    'credit_card',
    'attach_money',
    'payments',
    'trending_up',
    'business',
    'home',
    'work',
    'local_atm',
    'account_box',
  ];

  @override
  Widget build(BuildContext context) {
    return IconSelector(
      selectedIcon: selectedIcon,
      onIconChanged: onIconChanged,
      label: 'Account Icon',
      errorText: errorText,
      enabled: enabled,
      availableIcons: accountIcons,
      allowNone: true,
      noneLabel: 'Default Icon',
    );
  }
}

/// Specialized icon selector for categories
class CategoryIconSelector extends StatelessWidget {
  const CategoryIconSelector({
    super.key,
    required this.selectedIcon,
    required this.onIconChanged,
    this.errorText,
    this.enabled = true,
  });

  final String? selectedIcon;
  final ValueChanged<String?> onIconChanged;
  final String? errorText;
  final bool enabled;

  static const List<String> categoryIcons = [
    'shopping_cart',
    'restaurant',
    'local_gas_station',
    'medical_services',
    'school',
    'fitness_center',
    'movie',
    'music_note',
    'flight',
    'hotel',
    'local_grocery_store',
    'local_pharmacy',
    'pets',
    'sports_esports',
    'directions_bike',
    'car_rental',
    'home',
    'work',
    'attach_money',
    'savings',
  ];

  @override
  Widget build(BuildContext context) {
    return IconSelector(
      selectedIcon: selectedIcon,
      onIconChanged: onIconChanged,
      label: 'Category Icon',
      errorText: errorText,
      enabled: enabled,
      availableIcons: categoryIcons,
      allowNone: true,
      noneLabel: 'Default Icon',
    );
  }
}

/// Specialized icon selector for tags
class TagIconSelector extends StatelessWidget {
  const TagIconSelector({
    super.key,
    required this.selectedIcon,
    required this.onIconChanged,
    this.errorText,
    this.enabled = true,
  });

  final String? selectedIcon;
  final ValueChanged<String?> onIconChanged;
  final String? errorText;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    return IconSelector(
      selectedIcon: selectedIcon,
      onIconChanged: onIconChanged,
      label: 'Tag Icon',
      errorText: errorText,
      enabled: enabled,
      allowNone: true,
      noneLabel: 'No Icon',
    );
  }
}
