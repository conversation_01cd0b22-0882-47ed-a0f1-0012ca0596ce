import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/utils/color_utils.dart';
import 'package:budapp/widgets/forms/selectors/generic_selector.dart';
import 'package:flutter/material.dart';

/// Reusable color selector widget
///
/// This widget provides a consistent color selection interface that can be
/// used across different entity types (accounts, categories, tags).
class ColorSelector extends StatelessWidget {
  const ColorSelector({
    super.key,
    required this.selectedColor,
    required this.onColorChanged,
    this.label,
    this.errorText,
    this.enabled = true,
    this.availableColors,
    this.allowNone = true,
    this.noneLabel = 'Default',
    this.layout = SelectorLayout.grid,
  });

  /// Currently selected color (hex string)
  final String? selectedColor;

  /// Callback when color selection changes
  final ValueChanged<String?> onColorChanged;

  /// Optional label for the selector
  final String? label;

  /// Error text to display
  final String? errorText;

  /// Whether the selector is enabled
  final bool enabled;

  /// Custom list of available colors (hex strings)
  /// If null, uses default color palette
  final List<String>? availableColors;

  /// Whether to allow selecting no color (null)
  final bool allowNone;

  /// Label for the "no color" option
  final String noneLabel;

  /// Layout style for the selector
  final SelectorLayout layout;

  /// Default color palette
  static const List<String> defaultColors = [
    '#F44336', // Red
    '#E91E63', // Pink
    '#9C27B0', // Purple
    '#673AB7', // Deep Purple
    '#3F51B5', // Indigo
    '#2196F3', // Blue
    '#03A9F4', // Light Blue
    '#00BCD4', // Cyan
    '#009688', // Teal
    '#4CAF50', // Green
    '#8BC34A', // Light Green
    '#CDDC39', // Lime
    '#FFEB3B', // Yellow
    '#FFC107', // Amber
    '#FF9800', // Orange
    '#FF5722', // Deep Orange
    '#795548', // Brown
    '#607D8B', // Blue Grey
  ];

  @override
  Widget build(BuildContext context) {
    final colors = availableColors ?? defaultColors;

    return GenericSelector<String>(
      items: colors,
      selectedValue: selectedColor,
      onChanged: onColorChanged,
      itemBuilder: _buildColorItem,
      label: label,
      errorText: errorText,
      enabled: enabled,
      layout: layout,
      allowNone: allowNone,
      noneLabel: noneLabel,
      noneIcon: Icons.palette_outlined,
      crossAxisCount: 6,
    );
  }

  Widget _buildColorItem(
    BuildContext context,
    String colorHex, {
    required bool isSelected,
  }) {
    final theme = Theme.of(context);
    final color = parseHex(colorHex);

    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: color,
        border: Border.all(
          color: isSelected
              ? theme.colorScheme.primary
              : theme.colorScheme.outline,
          width: isSelected ? 3 : 1,
        ),
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: theme.colorScheme.primary.withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: isSelected
          ? Icon(Icons.check, color: contrastOfHex(colorHex), size: 20)
          : null,
    );
  }
}

/// Specialized color selector for accounts
class AccountColorSelector extends StatelessWidget {
  const AccountColorSelector({
    super.key,
    required this.selectedColor,
    required this.onColorChanged,
    this.errorText,
    this.enabled = true,
  });

  final String? selectedColor;
  final ValueChanged<String?> onColorChanged;
  final String? errorText;
  final bool enabled;

  static const List<String> accountColors = [
    '#2196F3', // Blue
    '#4CAF50', // Green
    '#FF9800', // Orange
    '#9C27B0', // Purple
    '#F44336', // Red
    '#795548', // Brown
    '#607D8B', // Blue Grey
    '#E91E63', // Pink
    '#009688', // Teal
    '#FFC107', // Amber
    '#3F51B5', // Indigo
    '#8BC34A', // Light Green
  ];

  @override
  Widget build(BuildContext context) {
    return ColorSelector(
      selectedColor: selectedColor,
      onColorChanged: onColorChanged,
      label: 'Account Color',
      errorText: errorText,
      enabled: enabled,
      availableColors: accountColors,
      allowNone: true,
      noneLabel: 'Default Color',
    );
  }
}

/// Specialized color selector for categories
class CategoryColorSelector extends StatelessWidget {
  const CategoryColorSelector({
    super.key,
    required this.selectedColor,
    required this.onColorChanged,
    this.errorText,
    this.enabled = true,
  });

  final String? selectedColor;
  final ValueChanged<String?> onColorChanged;
  final String? errorText;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    return ColorSelector(
      selectedColor: selectedColor,
      onColorChanged: onColorChanged,
      label: 'Category Color',
      errorText: errorText,
      enabled: enabled,
      allowNone: false, // Categories typically require a color
    );
  }
}

/// Specialized color selector for tags
class TagColorSelector extends StatelessWidget {
  const TagColorSelector({
    super.key,
    required this.selectedColor,
    required this.onColorChanged,
    this.errorText,
    this.enabled = true,
  });

  final String? selectedColor;
  final ValueChanged<String?> onColorChanged;
  final String? errorText;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    return ColorSelector(
      selectedColor: selectedColor,
      onColorChanged: onColorChanged,
      label: 'Tag Color',
      errorText: errorText,
      enabled: enabled,
      allowNone: false, // Tags typically require a color
    );
  }
}
