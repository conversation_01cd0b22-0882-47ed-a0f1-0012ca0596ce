import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Splash screen widget to show during Firebase initialization
class SplashScreen extends ConsumerWidget {
  const SplashScreen({
    super.key,
    required this.environment,
    required this.themeColor,
  });
  final String environment;
  final Color themeColor;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: themeColor.withValues(alpha: 0.1),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo/Icon placeholder
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: themeColor,
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.account_balance_wallet,
                size: 60,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 32),

            // App Name
            Text(
              'BudApp',
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                color: themeColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            // Environment indicator
            Text(
              environment,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: themeColor.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 48),

            // Loading indicator
            SizedBox(
              width: 40,
              height: 40,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(themeColor),
              ),
            ),
            const SizedBox(height: 16),

            // Loading text
            Text(
              'Initializing Firebase Services...',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: themeColor.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
