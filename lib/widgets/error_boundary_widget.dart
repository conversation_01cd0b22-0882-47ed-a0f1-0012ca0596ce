import 'dart:async';

import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/services/error_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Error boundary widget for graceful error handling and UI degradation
///
/// This widget catches errors that occur in its child widget tree and
/// displays a user-friendly error UI instead of crashing the app.
/// It integrates with crash reporting to log errors for debugging.
class ErrorBoundaryWidget extends StatefulWidget {
  const ErrorBoundaryWidget({
    required this.child,
    this.fallbackBuilder,
    this.onError,
    this.showDetails = false,
    this.canRetry = true,
    super.key,
  });

  /// The child widget to wrap with error boundary
  final Widget child;

  /// Custom error UI builder (optional)
  final Widget Function(
    BuildContext context,
    Object error,
    StackTrace? stackTrace,
    VoidCallback? retry,
  )?
  fallbackBuilder;

  /// Custom error handler (optional)
  final void Function(Object error, StackTrace? stackTrace)? onError;

  /// Whether to show error details in debug mode
  final bool showDetails;

  /// Whether the error UI should include a retry button
  final bool canRetry;

  @override
  State<ErrorBoundaryWidget> createState() => _ErrorBoundaryWidgetState();
}

class _ErrorBoundaryWidgetState extends State<ErrorBoundaryWidget> {
  Object? _error;
  StackTrace? _stackTrace;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    // Set up error handling
    FlutterError.onError = (FlutterErrorDetails details) {
      // Let the global handler process it first
      FlutterError.presentError(details);

      // Check if this error should be caught by this boundary
      if (mounted && _shouldCatchError(details.exception)) {
        _handleError(details.exception, details.stack);
      }
    };
  }

  /// Determine if this error boundary should catch the error
  bool _shouldCatchError(Object error) {
    // Only catch UI-related errors, not global system errors
    final errorString = error.toString().toLowerCase();
    return errorString.contains('renderbox') ||
        errorString.contains('renderflex') ||
        errorString.contains('widget') ||
        errorString.contains('build') ||
        errorString.contains('layout');
  }

  /// Handle error and update UI state
  void _handleError(Object error, StackTrace? stackTrace) {
    // Log error for crash reporting
    ErrorService.logError(
      error,
      stackTrace,
      context: 'ErrorBoundary',
      additionalData: {
        'widget': widget.runtimeType.toString(),
        'canRetry': widget.canRetry,
      },
      fatal: false,
    );

    // Call custom error handler if provided
    widget.onError?.call(error, stackTrace);

    // Update UI state
    if (mounted) {
      setState(() {
        _error = error;
        _stackTrace = stackTrace;
        _hasError = true;
      });
    }
  }

  /// Retry loading the child widget
  void _retry() {
    ErrorService.logUserAction('Error boundary retry', {
      'error': _error.toString(),
    });

    setState(() {
      _error = null;
      _stackTrace = null;
      _hasError = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      // Use custom fallback builder if provided
      if (widget.fallbackBuilder != null) {
        return widget.fallbackBuilder!(
          context,
          _error!,
          _stackTrace,
          widget.canRetry ? _retry : null,
        );
      }

      // Use default error UI
      return _buildDefaultErrorUI(context);
    }

    // Return the child widget normally
    return widget.child;
  }

  /// Build the default error UI
  Widget _buildDefaultErrorUI(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Error icon
          Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
          const SizedBox(height: 16),

          // Error title
          Text(
            l10n.errorBoundaryTitle,
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.error,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),

          // Error message
          Text(
            l10n.errorBoundaryMessage,
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),

          // Error details (debug mode only)
          if (kDebugMode && widget.showDetails && _error != null) ...[
            ExpansionTile(
              title: Text(
                'Error Details (Debug)',
                style: theme.textTheme.bodySmall,
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Text(
                    _error.toString(),
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],

          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Retry button
              if (widget.canRetry) ...[
                ElevatedButton.icon(
                  onPressed: _retry,
                  icon: const Icon(Icons.refresh),
                  label: Text(l10n.retry),
                ),
                const SizedBox(width: 8),
              ],

              // Report button (debug mode only)
              if (kDebugMode)
                OutlinedButton.icon(
                  onPressed: () => _reportError(context),
                  icon: const Icon(Icons.bug_report),
                  label: Text(l10n.reportError),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// Report error with additional context
  void _reportError(BuildContext context) {
    if (_error == null) return;

    unawaited(
      ErrorService.logError(
        _error!,
        _stackTrace,
        context: 'User reported error from ErrorBoundary',
        additionalData: {
          'userReported': true,
          'widget': widget.runtimeType.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
        fatal: false,
      ),
    );

    ErrorService.showInfoSnackBar(
      context,
      AppLocalizations.of(context)!.errorReported,
    );
  }
}

/// Helper widget for wrapping specific widgets with error boundaries
class SafeWidget extends StatelessWidget {
  const SafeWidget({
    required this.child,
    this.fallback,
    this.onError,
    super.key,
  });

  final Widget child;
  final Widget? fallback;
  final void Function(Object error, StackTrace? stackTrace)? onError;

  @override
  Widget build(BuildContext context) {
    return ErrorBoundaryWidget(
      onError: onError,
      fallbackBuilder: fallback != null
          ? (context, error, stackTrace, retry) => fallback!
          : null,
      child: child,
    );
  }
}

/// Extension for easy error boundary wrapping
extension WidgetErrorBoundary on Widget {
  /// Wrap this widget with an error boundary
  Widget withErrorBoundary({
    Widget Function(
      BuildContext context,
      Object error,
      StackTrace? stackTrace,
      VoidCallback? retry,
    )?
    fallbackBuilder,
    void Function(Object error, StackTrace? stackTrace)? onError,
    bool showDetails = false,
    bool canRetry = true,
  }) {
    return ErrorBoundaryWidget(
      fallbackBuilder: fallbackBuilder,
      onError: onError,
      showDetails: showDetails,
      canRetry: canRetry,
      child: this,
    );
  }

  /// Wrap this widget with a safe error boundary (simpler API)
  Widget safely({
    Widget? fallback,
    void Function(Object error, StackTrace? stackTrace)? onError,
  }) {
    return SafeWidget(fallback: fallback, onError: onError, child: this);
  }
}
