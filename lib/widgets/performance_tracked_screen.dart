import 'package:budapp/services/performance_service.dart';
import 'package:flutter/material.dart';

/// A wrapper widget that automatically tracks screen load performance
class PerformanceTrackedScreen extends StatefulWidget {
  const PerformanceTrackedScreen({
    super.key,
    required this.screenName,
    required this.child,
  });

  final String screenName;
  final Widget child;

  @override
  State<PerformanceTrackedScreen> createState() =>
      _PerformanceTrackedScreenState();
}

class _PerformanceTrackedScreenState extends State<PerformanceTrackedScreen> {
  @override
  void initState() {
    super.initState();
    // Start tracking screen load when widget is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      PerformanceService.trackScreenLoad(widget.screenName);
    });
  }

  @override
  void dispose() {
    // Complete tracking when widget is disposed
    PerformanceService.completeScreenLoad(widget.screenName);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Extension to easily wrap any widget with performance tracking
extension PerformanceTrackingExtension on Widget {
  /// Wrap this widget with performance tracking
  Widget trackPerformance(String screenName) {
    return PerformanceTrackedScreen(screenName: screenName, child: this);
  }
}
