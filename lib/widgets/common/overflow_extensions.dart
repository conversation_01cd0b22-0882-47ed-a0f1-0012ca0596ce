import 'package:flutter/material.dart';

import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/config/overflow_tokens.dart';

/// Universal overflow extension methods that can be applied to any Widget
///
/// These extensions provide a consistent API for handling overflow across
/// the entire application, building on the existing design token system.
extension OverflowExtensions on Widget {
  /// Applies smart overflow handling based on widget type and context
  Widget withSmartOverflow({
    OverflowStrategy? strategy,
    String? context,
    DeviceSize? deviceSize,
  }) {
    return SmartOverflowWrapper(
      strategy: strategy,
      context: context,
      deviceSize: deviceSize,
      child: this,
    );
  }

  /// Applies height constraints with overflow protection
  Widget withConstrainedHeight({
    double? maxHeight,
    double? minHeight,
    String? context,
  }) {
    final constraints = context != null
        ? AppOverflowTokens.getContainerConstraints(context)
        : AppOverflowTokens.cardConstraints;

    return ConstrainedBox(
      constraints: BoxConstraints(
        minHeight: minHeight ?? constraints.minHeight ?? 0.0,
        maxHeight: maxHeight ?? constraints.maxHeight ?? double.infinity,
      ),
      child: this,
    );
  }

  /// Makes content scrollable when it exceeds available space
  Widget withScrollableContent({
    Axis scrollDirection = Axis.vertical,
    bool? enableScrollbar,
    ScrollPhysics? physics,
    String? context,
  }) {
    final scrollConstraints = context != null
        ? AppOverflowTokens.getScrollableConstraints(context)
        : AppOverflowTokens.scrollableConstraints;

    Widget scrollable = SingleChildScrollView(
      scrollDirection: scrollDirection,
      physics: physics ?? scrollConstraints.physics,
      child: this,
    );

    // Add scrollbar if enabled
    if (enableScrollbar ?? scrollConstraints.enableScrollbar) {
      scrollable = Scrollbar(child: scrollable);
    }

    // Add fade edges if enabled
    if (scrollConstraints.fadeEdges) {
      scrollable = _addFadeEdges(scrollable, scrollConstraints.fadeLength);
    }

    return scrollable;
  }

  /// Applies responsive layout constraints based on screen size
  Widget withResponsiveLayout({String? context}) {
    return ResponsiveLayoutWrapper(
      context: context,
      child: this,
    );
  }

  /// Makes content expandable with a "show more" functionality
  Widget withExpandableContent({
    String expandText = 'Show more',
    String collapseText = 'Show less',
    int initialMaxLines = 3,
  }) {
    return ExpandableContentWrapper(
      expandText: expandText,
      collapseText: collapseText,
      initialMaxLines: initialMaxLines,
      child: this,
    );
  }

  /// Wraps widget with safe overflow handling (error boundary)
  Widget safely({
    Widget? fallback,
    void Function(Object error, StackTrace stackTrace)? onError,
  }) {
    return OverflowErrorBoundary(
      fallback: fallback,
      onError: onError,
      child: this,
    );
  }

  /// Applies adaptive sizing that scales content to fit available space
  Widget withAdaptiveSize({
    BoxFit fit = BoxFit.scaleDown,
    Alignment alignment = Alignment.center,
    double? minFontSize,
    double? maxFontSize,
  }) {
    return AdaptiveSizeWrapper(
      fit: fit,
      alignment: alignment,
      minFontSize: minFontSize,
      maxFontSize: maxFontSize,
      child: this,
    );
  }

  /// Applies flexible layout behavior within Flex containers
  Widget withFlexible({
    int flex = 1,
    FlexFit fit = FlexFit.loose,
  }) {
    return Flexible(
      flex: flex,
      fit: fit,
      child: this,
    );
  }

  /// Applies expanded layout behavior within Flex containers
  Widget withExpanded({int flex = 1}) {
    return Expanded(
      flex: flex,
      child: this,
    );
  }

  /// Helper method to add fade edges to scrollable content
  Widget _addFadeEdges(Widget child, double? fadeLength) {
    return ShaderMask(
      shaderCallback: (Rect bounds) {
        return LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: const [
            Colors.black,
            Colors.black,
            Colors.transparent,
          ],
          stops: [
            0.0,
            1.0 - (fadeLength ?? AppSpacing.lg) / bounds.height,
            1.0,
          ],
        ).createShader(bounds);
      },
      blendMode: BlendMode.dstIn,
      child: child,
    );
  }
}

/// Container-specific overflow extensions
extension ContainerOverflowExtensions on Container {
  /// Applies container-specific overflow handling
  Widget withContainerOverflow({
    OverflowStrategy strategy = OverflowStrategy.adaptive,
    String? context,
  }) {
    final constraints = context != null
        ? AppOverflowTokens.getContainerConstraints(context)
        : AppOverflowTokens.cardConstraints;

    return Container(
      constraints: BoxConstraints(
        minHeight: constraints.minHeight ?? 0.0,
        maxHeight: constraints.maxHeight ?? double.infinity,
        minWidth: constraints.minWidth ?? 0.0,
        maxWidth: constraints.maxWidth ?? double.infinity,
      ),
      decoration: decoration,
      padding: padding,
      margin: margin,
      transform: transform,
      transformAlignment: transformAlignment,
      child: strategy == OverflowStrategy.scroll && child != null
          ? SingleChildScrollView(child: child)
          : child,
    );
  }
}

/// Card-specific overflow extensions
extension CardOverflowExtensions on Card {
  /// Applies card-specific overflow handling with Material 3 styling
  Widget withCardOverflow({
    OverflowStrategy strategy = OverflowStrategy.scroll,
    String? context,
  }) {
    final constraints = context != null
        ? AppOverflowTokens.getContainerConstraints(context)
        : AppOverflowTokens.cardConstraints;

    return ConstrainedBox(
      constraints: BoxConstraints(
        minHeight: constraints.minHeight ?? 0.0,
        maxHeight: constraints.maxHeight ?? double.infinity,
      ),
      child: Card(
        elevation: elevation,
        color: color,
        shadowColor: shadowColor,
        surfaceTintColor: surfaceTintColor,
        margin: margin,
        clipBehavior: clipBehavior,
        shape: shape,
        borderOnForeground: borderOnForeground,
        child: strategy == OverflowStrategy.scroll && child != null
            ? SingleChildScrollView(child: child)
            : child,
      ),
    );
  }
}

/// Column-specific overflow extensions
extension ColumnOverflowExtensions on Column {
  /// Applies column-specific overflow handling
  Widget withColumnOverflow({
    OverflowStrategy strategy = OverflowStrategy.scroll,
    bool shrinkWrap = false,
  }) {
    if (strategy == OverflowStrategy.scroll) {
      return SingleChildScrollView(
        child: Column(
          mainAxisAlignment: mainAxisAlignment,
          crossAxisAlignment: crossAxisAlignment,
          mainAxisSize: mainAxisSize,
          textDirection: textDirection,
          verticalDirection: verticalDirection,
          textBaseline: textBaseline,
          children: children,
        ),
      );
    }

    return IntrinsicHeight(
      child: Column(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisSize: MainAxisSize.min,
        textDirection: textDirection,
        verticalDirection: verticalDirection,
        textBaseline: textBaseline,
        children: children.map((child) => child.withFlexible()).toList(),
      ),
    );
  }
}

/// Row-specific overflow extensions
extension RowOverflowExtensions on Row {
  /// Applies row-specific overflow handling
  Widget withRowOverflow({
    OverflowStrategy strategy = OverflowStrategy.wrap,
  }) {
    if (strategy == OverflowStrategy.wrap) {
      return Wrap(
        direction: Axis.horizontal,
        alignment: _convertMainAxisAlignment(mainAxisAlignment),
        crossAxisAlignment: _convertCrossAxisAlignment(crossAxisAlignment),
        spacing: AppSpacing.sm,
        runSpacing: AppSpacing.xs,
        children: children,
      );
    }

    if (strategy == OverflowStrategy.scroll) {
      return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisAlignment: mainAxisAlignment,
          crossAxisAlignment: crossAxisAlignment,
          mainAxisSize: mainAxisSize,
          textDirection: textDirection,
          verticalDirection: verticalDirection,
          textBaseline: textBaseline,
          children: children,
        ),
      );
    }

    return IntrinsicWidth(
      child: Row(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisSize: MainAxisSize.min,
        textDirection: textDirection,
        verticalDirection: verticalDirection,
        textBaseline: textBaseline,
        children: children.map((child) => child.withFlexible()).toList(),
      ),
    );
  }

  /// Converts MainAxisAlignment to WrapAlignment
  WrapAlignment _convertMainAxisAlignment(MainAxisAlignment alignment) {
    switch (alignment) {
      case MainAxisAlignment.start:
        return WrapAlignment.start;
      case MainAxisAlignment.end:
        return WrapAlignment.end;
      case MainAxisAlignment.center:
        return WrapAlignment.center;
      case MainAxisAlignment.spaceBetween:
        return WrapAlignment.spaceBetween;
      case MainAxisAlignment.spaceAround:
        return WrapAlignment.spaceAround;
      case MainAxisAlignment.spaceEvenly:
        return WrapAlignment.spaceEvenly;
    }
  }

  /// Converts CrossAxisAlignment to WrapCrossAlignment
  WrapCrossAlignment _convertCrossAxisAlignment(CrossAxisAlignment alignment) {
    switch (alignment) {
      case CrossAxisAlignment.start:
        return WrapCrossAlignment.start;
      case CrossAxisAlignment.end:
        return WrapCrossAlignment.end;
      case CrossAxisAlignment.center:
        return WrapCrossAlignment.center;
      case CrossAxisAlignment.stretch:
        return WrapCrossAlignment.start;
      case CrossAxisAlignment.baseline:
        return WrapCrossAlignment.start;
    }
  }
}

/// Smart overflow wrapper that automatically detects and applies appropriate overflow handling
class SmartOverflowWrapper extends StatelessWidget {
  const SmartOverflowWrapper({
    super.key,
    required this.child,
    this.strategy,
    this.context,
    this.deviceSize,
  });

  final Widget child;
  final OverflowStrategy? strategy;
  final String? context;
  final DeviceSize? deviceSize;

  @override
  Widget build(BuildContext context) {
    final detectedStrategy =
        strategy ?? AppOverflowTokens.getStrategyForType(child.runtimeType);

    switch (detectedStrategy) {
      case OverflowStrategy.scroll:
        return child.withScrollableContent(context: this.context);
      case OverflowStrategy.adaptive:
        return child.withAdaptiveSize();
      case OverflowStrategy.wrap:
        if (child is Row) {
          return (child as Row).withRowOverflow(
            strategy: OverflowStrategy.wrap,
          );
        }
        return child.withScrollableContent();
      case OverflowStrategy.scale:
        return child.withAdaptiveSize(fit: BoxFit.scaleDown);
      case OverflowStrategy.ellipsis:
        return child.withConstrainedHeight(context: this.context);
      case OverflowStrategy.visible:
        return child;
      case OverflowStrategy.fade:
        return child.withScrollableContent(context: this.context);
      case OverflowStrategy.clip:
        return ClipRect(child: child);
    }
  }
}

/// Responsive layout wrapper that applies device-specific constraints
class ResponsiveLayoutWrapper extends StatelessWidget {
  const ResponsiveLayoutWrapper({
    super.key,
    required this.child,
    this.context,
  });

  final Widget child;
  final String? context;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final deviceSize = AppOverflowTokens.getDeviceSize(context);
        final containerConstraints = this.context != null
            ? AppOverflowTokens.getContainerConstraints(this.context!)
            : AppOverflowTokens.cardConstraints;

        final maxHeight = containerConstraints.getHeightForDevice(deviceSize);
        final maxWidth = containerConstraints.getWidthForDevice(deviceSize);

        return ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: maxHeight ?? constraints.maxHeight,
            maxWidth: maxWidth ?? constraints.maxWidth,
          ),
          child: child,
        );
      },
    );
  }
}

/// Expandable content wrapper with show more/less functionality
class ExpandableContentWrapper extends StatefulWidget {
  const ExpandableContentWrapper({
    super.key,
    required this.child,
    this.expandText = 'Show more',
    this.collapseText = 'Show less',
    this.initialMaxLines = 3,
  });

  final Widget child;
  final String expandText;
  final String collapseText;
  final int initialMaxLines;

  @override
  State<ExpandableContentWrapper> createState() =>
      _ExpandableContentWrapperState();
}

class _ExpandableContentWrapperState extends State<ExpandableContentWrapper> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!_isExpanded)
          ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight:
                  widget.initialMaxLines * 24.0, // Approximate line height
            ),
            child: SingleChildScrollView(
              physics: const NeverScrollableScrollPhysics(),
              child: widget.child,
            ),
          )
        else
          widget.child,
        const SizedBox(height: AppSpacing.xs),
        InkWell(
          onTap: () => setState(() => _isExpanded = !_isExpanded),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
            child: Text(
              _isExpanded ? widget.collapseText : widget.expandText,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: AppTypography.fontWeightMedium,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// Adaptive size wrapper that scales content to fit available space
class AdaptiveSizeWrapper extends StatelessWidget {
  const AdaptiveSizeWrapper({
    super.key,
    required this.child,
    this.fit = BoxFit.scaleDown,
    this.alignment = Alignment.center,
    this.minFontSize,
    this.maxFontSize,
  });

  final Widget child;
  final BoxFit fit;
  final Alignment alignment;
  final double? minFontSize;
  final double? maxFontSize;

  @override
  Widget build(BuildContext context) {
    return FittedBox(
      fit: fit,
      alignment: alignment,
      child: child,
    );
  }
}

/// Error boundary for overflow-related issues
class OverflowErrorBoundary extends StatelessWidget {
  const OverflowErrorBoundary({
    super.key,
    required this.child,
    this.fallback,
    this.onError,
  });

  final Widget child;
  final Widget? fallback;
  final void Function(Object error, StackTrace stackTrace)? onError;

  @override
  Widget build(BuildContext context) {
    // Simple error boundary implementation
    try {
      return child;
    } on Exception catch (error, stackTrace) {
      onError?.call(error, stackTrace);
      return fallback ??
          Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: const Icon(
              Icons.error_outline,
              color: AppColors.error,
            ),
          );
    }
  }
}
