import 'package:budapp/config/design_tokens.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Generic text form field with consistent styling and behavior
///
/// This widget provides a reusable text input pattern with Material 3 design,
/// validation support, and common input configurations.
///
/// Features Material 3 floating label behavior where the label starts inside
/// the field and animates to the border when focused or when text is entered.
class AppTextFormField extends StatefulWidget {
  const AppTextFormField({
    super.key,
    required this.label,
    this.hintText,
    this.controller,
    this.validator,
    this.isPassword = false,
    this.isRequired = false,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.next,
    this.onFieldSubmitted,
    this.onChanged,
    this.enabled = true,
    this.readOnly = false,
    this.prefixIcon,
    this.suffixIcon,
    this.autofocus = false,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.inputFormatters,
    this.initialValue,
    this.focusNode,
    this.onTap,
    this.textCapitalization = TextCapitalization.none,
    this.textAlign = TextAlign.start,
    this.helperText,
    this.errorText,
    this.useFloatingLabel = true,
  });
  final String label;
  final String? hintText;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final bool isPassword;
  final bool isRequired;
  final TextInputType keyboardType;
  final TextInputAction textInputAction;
  final void Function(String)? onFieldSubmitted;
  final void Function(String)? onChanged;
  final bool enabled;
  final bool readOnly;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool autofocus;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final String? initialValue;
  final FocusNode? focusNode;
  final VoidCallback? onTap;
  final TextCapitalization textCapitalization;
  final TextAlign textAlign;
  final String? helperText;
  final String? errorText;
  final bool useFloatingLabel;

  @override
  State<AppTextFormField> createState() => _AppTextFormFieldState();
}

class _AppTextFormFieldState extends State<AppTextFormField> {
  bool _isObscured = true;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Build label text with required indicator
    var labelText = widget.label;
    if (widget.isRequired && widget.useFloatingLabel) {
      labelText = '$labelText *';
    }

    if (widget.useFloatingLabel) {
      // Material 3 floating label implementation
      return TextFormField(
        controller: widget.controller,
        validator: widget.validator,
        obscureText: widget.isPassword && _isObscured,
        keyboardType: widget.keyboardType,
        textInputAction: widget.textInputAction,
        onFieldSubmitted: widget.onFieldSubmitted,
        onChanged: widget.onChanged,
        enabled: widget.enabled,
        readOnly: widget.readOnly,
        autofocus: widget.autofocus,
        maxLines: widget.isPassword ? 1 : widget.maxLines,
        minLines: widget.minLines,
        maxLength: widget.maxLength,
        inputFormatters: widget.inputFormatters,
        initialValue: widget.initialValue,
        focusNode: widget.focusNode,
        onTap: widget.onTap,
        textCapitalization: widget.textCapitalization,
        textAlign: widget.textAlign,
        style: theme.textTheme.bodyLarge?.copyWith(
          fontSize: AppTypography.fontSizeMd,
        ),
        decoration: InputDecoration(
          labelText: labelText,
          hintText: widget.hintText,
          helperText: widget.helperText,
          errorText: widget.errorText,
          floatingLabelBehavior: FloatingLabelBehavior.auto,
          prefixIcon: widget.prefixIcon,
          suffixIcon: widget.isPassword
              ? IconButton(
                  icon: Icon(
                    _isObscured ? Icons.visibility : Icons.visibility_off,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  onPressed: () {
                    setState(() {
                      _isObscured = !_isObscured;
                    });
                  },
                )
              : widget.suffixIcon,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
            borderSide: BorderSide(color: theme.colorScheme.outline, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
            borderSide: BorderSide(color: theme.colorScheme.primary, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
            borderSide: BorderSide(color: theme.colorScheme.error, width: 1),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
            borderSide: BorderSide(color: theme.colorScheme.error, width: 2),
          ),
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
            borderSide: BorderSide(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.12),
              width: 1,
            ),
          ),
        ),
      );
    }

    // Legacy implementation with static label above field
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with optional required indicator
        Row(
          children: [
            Text(
              widget.label,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: AppTypography.fontWeightMedium,
                color: theme.colorScheme.onSurface,
              ),
            ),
            if (widget.isRequired) ...[
              const SizedBox(width: AppSpacing.xs),
              Text(
                '*',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.error,
                  fontWeight: AppTypography.fontWeightMedium,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: AppSpacing.sm),

        // Text form field
        TextFormField(
          controller: widget.controller,
          validator: widget.validator,
          obscureText: widget.isPassword && _isObscured,
          keyboardType: widget.keyboardType,
          textInputAction: widget.textInputAction,
          onFieldSubmitted: widget.onFieldSubmitted,
          onChanged: widget.onChanged,
          enabled: widget.enabled,
          readOnly: widget.readOnly,
          autofocus: widget.autofocus,
          maxLines: widget.isPassword ? 1 : widget.maxLines,
          minLines: widget.minLines,
          maxLength: widget.maxLength,
          inputFormatters: widget.inputFormatters,
          initialValue: widget.initialValue,
          focusNode: widget.focusNode,
          onTap: widget.onTap,
          textCapitalization: widget.textCapitalization,
          textAlign: widget.textAlign,
          style: theme.textTheme.bodyLarge?.copyWith(
            fontSize: AppTypography.fontSizeMd,
          ),
          decoration: InputDecoration(
            hintText: widget.hintText,
            helperText: widget.helperText,
            errorText: widget.errorText,
            prefixIcon: widget.prefixIcon,
            suffixIcon: widget.isPassword
                ? IconButton(
                    icon: Icon(
                      _isObscured ? Icons.visibility : Icons.visibility_off,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    onPressed: () {
                      setState(() {
                        _isObscured = !_isObscured;
                      });
                    },
                  )
                : widget.suffixIcon,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.md),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.md),
              borderSide: BorderSide(color: theme.colorScheme.outline),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.md),
              borderSide: BorderSide(
                color: theme.colorScheme.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.md),
              borderSide: BorderSide(color: theme.colorScheme.error),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.md),
              borderSide: BorderSide(color: theme.colorScheme.error, width: 2),
            ),
            filled: true,
            fillColor: theme.colorScheme.surfaceContainerHighest.withValues(
              alpha: 0.3,
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.md,
              vertical: AppSpacing.md,
            ),
          ),
        ),
      ],
    );
  }
}

/// Specialized text form field for currency amounts
class AppCurrencyFormField extends StatelessWidget {
  const AppCurrencyFormField({
    super.key,
    required this.label,
    this.controller,
    this.validator,
    this.onChanged,
    this.enabled = true,
    this.hintText,
    this.errorText,
    this.isRequired = false,
  });
  final String label;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final bool enabled;
  final String? hintText;
  final String? errorText;
  final bool isRequired;

  @override
  Widget build(BuildContext context) {
    return AppTextFormField(
      label: label,
      controller: controller,
      validator: validator,
      onChanged: onChanged,
      enabled: enabled,
      hintText: hintText ?? '0.00',
      errorText: errorText,
      isRequired: isRequired,
      useFloatingLabel: true,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      textInputAction: TextInputAction.next,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
      ],
      prefixIcon: const Icon(Icons.attach_money),
      textAlign: TextAlign.end,
    );
  }
}

/// Specialized text form field for email addresses
class AppEmailFormField extends StatelessWidget {
  const AppEmailFormField({
    super.key,
    this.label = 'Email',
    this.controller,
    this.validator,
    this.onChanged,
    this.enabled = true,
    this.hintText,
    this.errorText,
    this.isRequired = true,
  });
  final String label;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final bool enabled;
  final String? hintText;
  final String? errorText;
  final bool isRequired;

  @override
  Widget build(BuildContext context) {
    return AppTextFormField(
      label: label,
      controller: controller,
      validator: validator,
      onChanged: onChanged,
      enabled: enabled,
      hintText: hintText ?? '<EMAIL>',
      errorText: errorText,
      isRequired: isRequired,
      useFloatingLabel: true,
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      textCapitalization: TextCapitalization.none,
      prefixIcon: const Icon(Icons.email_outlined),
    );
  }
}

/// Specialized text form field for passwords
class AppPasswordFormField extends StatelessWidget {
  const AppPasswordFormField({
    super.key,
    this.label = 'Password',
    this.controller,
    this.validator,
    this.onChanged,
    this.enabled = true,
    this.hintText,
    this.errorText,
    this.isRequired = true,
  });
  final String label;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final bool enabled;
  final String? hintText;
  final String? errorText;
  final bool isRequired;

  @override
  Widget build(BuildContext context) {
    return AppTextFormField(
      label: label,
      controller: controller,
      validator: validator,
      onChanged: onChanged,
      enabled: enabled,
      hintText: hintText,
      errorText: errorText,
      isRequired: isRequired,
      useFloatingLabel: true,
      isPassword: true,
      keyboardType: TextInputType.visiblePassword,
      textInputAction: TextInputAction.done,
      prefixIcon: const Icon(Icons.lock_outlined),
    );
  }
}
