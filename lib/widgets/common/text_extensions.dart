import 'package:budapp/config/text_overflow_config.dart';
import 'package:flutter/material.dart';

/// Extension methods for TextStyle to easily apply overflow configurations
extension TextStyleOverflowExtensions on TextStyle {
  /// Applies title text overflow configuration
  TextStyle get withTitleOverflow => this;

  /// Applies body text overflow configuration
  TextStyle get withBodyOverflow => this;

  /// Applies label text overflow configuration
  TextStyle get withLabelOverflow => this;

  /// Applies critical text overflow configuration
  TextStyle get withCriticalOverflow => this;

  /// Applies note text overflow configuration
  TextStyle get withNoteOverflow => this;

  /// Applies button text overflow configuration
  TextStyle get withButtonOverflow => this;

  /// Applies list item text overflow configuration
  TextStyle get withListItemOverflow => this;
}

/// Extension methods for Text widget to easily apply overflow handling
extension TextOverflowExtensions on Text {
  /// Converts Text to AppText with automatic overflow handling
  Widget get withSmartOverflow {
    return Builder(
      builder: (context) {
        final config = AppTextOverflow.detectFromStyle(style);
        return Text(
          data ?? '',
          style: style,
          textAlign: textAlign,
          textDirection: textDirection,
          locale: locale,
          softWrap: softWrap ?? config.softWrap,
          overflow: overflow ?? config.overflow,
          maxLines: maxLines ?? config.maxLines,
          textScaler: textScaler,
          semanticsLabel: semanticsLabel,
          textWidthBasis: textWidthBasis,
          textHeightBehavior: textHeightBehavior,
          selectionColor: selectionColor,
        );
      },
    );
  }

  /// Applies title overflow configuration
  Widget get withTitleOverflow {
    const config = AppTextOverflow.title;
    return Text(
      data ?? '',
      style: style,
      textAlign: textAlign,
      textDirection: textDirection,
      locale: locale,
      softWrap: softWrap ?? config.softWrap,
      overflow: overflow ?? config.overflow,
      maxLines: maxLines ?? config.maxLines,
      textScaler: textScaler,
      semanticsLabel: semanticsLabel,
      textWidthBasis: textWidthBasis,
      textHeightBehavior: textHeightBehavior,
      selectionColor: selectionColor,
    );
  }

  /// Applies body overflow configuration
  Widget get withBodyOverflow {
    const config = AppTextOverflow.body;
    return Text(
      data ?? '',
      style: style,
      textAlign: textAlign,
      textDirection: textDirection,
      locale: locale,
      softWrap: softWrap ?? config.softWrap,
      overflow: overflow ?? config.overflow,
      maxLines: maxLines ?? config.maxLines,
      textScaler: textScaler,
      semanticsLabel: semanticsLabel,
      textWidthBasis: textWidthBasis,
      textHeightBehavior: textHeightBehavior,
      selectionColor: selectionColor,
    );
  }

  /// Applies label overflow configuration
  Widget get withLabelOverflow {
    const config = AppTextOverflow.label;
    return Text(
      data ?? '',
      style: style,
      textAlign: textAlign,
      textDirection: textDirection,
      locale: locale,
      softWrap: softWrap ?? config.softWrap,
      overflow: overflow ?? config.overflow,
      maxLines: maxLines ?? config.maxLines,
      textScaler: textScaler,
      semanticsLabel: semanticsLabel,
      textWidthBasis: textWidthBasis,
      textHeightBehavior: textHeightBehavior,
      selectionColor: selectionColor,
    );
  }

  /// Applies note overflow configuration
  Widget get withNoteOverflow {
    const config = AppTextOverflow.note;
    return Text(
      data ?? '',
      style: style,
      textAlign: textAlign,
      textDirection: textDirection,
      locale: locale,
      softWrap: softWrap ?? config.softWrap,
      overflow: overflow ?? config.overflow,
      maxLines: maxLines ?? config.maxLines,
      textScaler: textScaler,
      semanticsLabel: semanticsLabel,
      textWidthBasis: textWidthBasis,
      textHeightBehavior: textHeightBehavior,
      selectionColor: selectionColor,
    );
  }
}

/// Utility class for common text overflow patterns
// ignore: avoid_classes_with_only_static_members
class TextOverflowUtils {
  /// Creates a Text widget with single line ellipsis
  static Widget singleLineEllipsis(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
  }) {
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      softWrap: false,
    );
  }

  /// Creates a Text widget with multi-line ellipsis
  static Widget multiLineEllipsis(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int maxLines = 3,
  }) {
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: TextOverflow.ellipsis,
      softWrap: true,
    );
  }

  /// Creates a Text widget with adaptive scaling
  static Widget adaptiveText(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
  }) {
    return FittedBox(
      fit: BoxFit.scaleDown,
      alignment: _getAlignmentFromTextAlign(textAlign),
      child: Text(
        text,
        style: style,
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: TextOverflow.visible,
      ),
    );
  }

  /// Creates a Text widget with fade overflow effect
  static Widget fadeOverflow(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
  }) {
    return ShaderMask(
      shaderCallback: (bounds) {
        return const LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [Colors.black, Colors.black, Colors.transparent],
          stops: [0, 0.8, 1],
        ).createShader(bounds);
      },
      blendMode: BlendMode.dstIn,
      child: Text(
        text,
        style: style,
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: TextOverflow.clip,
        softWrap: false,
      ),
    );
  }

  /// Helper method to convert TextAlign to Alignment
  static Alignment _getAlignmentFromTextAlign(TextAlign? textAlign) {
    switch (textAlign) {
      case TextAlign.left:
      case TextAlign.start:
        return Alignment.centerLeft;
      case TextAlign.right:
      case TextAlign.end:
        return Alignment.centerRight;
      case TextAlign.center:
        return Alignment.center;
      case TextAlign.justify:
        return Alignment.centerLeft;
      case null:
        return Alignment.centerLeft;
    }
  }
}

/// Helper widget for responsive text that adapts to available space
class ResponsiveText extends StatelessWidget {
  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.minFontSize,
    this.maxFontSize,
  });
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final double? minFontSize;
  final double? maxFontSize;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final textStyle = style ?? Theme.of(context).textTheme.bodyMedium!;

        // Calculate if text fits in available space
        final textPainter = TextPainter(
          text: TextSpan(text: text, style: textStyle),
          textDirection: TextDirection.ltr,
          maxLines: maxLines,
        )..layout(maxWidth: constraints.maxWidth);

        // If text fits, use normal Text widget
        if (!textPainter.didExceedMaxLines) {
          return Text(
            text,
            style: textStyle,
            textAlign: textAlign,
            maxLines: maxLines,
          );
        }

        // Otherwise, use adaptive scaling
        return FittedBox(
          fit: BoxFit.scaleDown,
          alignment: TextOverflowUtils._getAlignmentFromTextAlign(textAlign),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minWidth: constraints.maxWidth,
              maxWidth: constraints.maxWidth,
            ),
            child: Text(
              text,
              style: textStyle,
              textAlign: textAlign,
              maxLines: maxLines,
            ),
          ),
        );
      },
    );
  }
}
