import 'package:budapp/config/design_tokens.dart';
import 'package:flutter/material.dart';

/// Base card widget that provides consistent card styling and layout patterns
///
/// This widget consolidates common card patterns used across the application
/// with consistent Material 3 design, elevation, and interaction handling.
class BaseCard extends StatelessWidget {
  const BaseCard({
    super.key,
    this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.actions,
    this.onTap,
    this.onLongPress,
    this.elevation,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.borderRadius,
    this.showActions = false,
    this.child,
  });
  final Widget? leading;
  final Widget title;
  final Widget? subtitle;
  final Widget? trailing;
  final List<Widget>? actions;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final double? elevation;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final bool showActions;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cardBorderRadius =
        borderRadius ?? BorderRadius.circular(AppBorderRadius.lg);

    return Card(
      elevation: elevation ?? AppElevation.sm,
      margin: margin ?? EdgeInsets.zero,
      color: backgroundColor,
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: cardBorderRadius,
        child: Padding(
          padding: padding ?? const EdgeInsets.all(AppSpacing.md),
          child: child ?? _buildDefaultLayout(context, theme),
        ),
      ),
    );
  }

  Widget _buildDefaultLayout(BuildContext context, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header row with leading, title/subtitle, trailing, and actions
        Row(
          children: [
            // Leading widget (icon, avatar, etc.)
            if (leading != null) ...[
              leading!,
              const SizedBox(width: AppSpacing.md),
            ],

            // Title and subtitle
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  title,
                  if (subtitle != null) ...[
                    const SizedBox(height: AppSpacing.xs),
                    subtitle!,
                  ],
                ],
              ),
            ),

            // Trailing widget
            if (trailing != null) ...[
              const SizedBox(width: AppSpacing.sm),
              trailing!,
            ],

            // Actions menu
            if (showActions && actions != null && actions!.isNotEmpty) ...[
              const SizedBox(width: AppSpacing.sm),
              PopupMenuButton<VoidCallback>(
                onSelected: (callback) => callback(),
                itemBuilder: (context) => actions!
                    .asMap()
                    .entries
                    .map(
                      (entry) => PopupMenuItem<VoidCallback>(
                        value: () {}, // Will be handled by the action widget
                        child: entry.value,
                      ),
                    )
                    .toList(),
                icon: Icon(
                  Icons.more_vert,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }
}

/// Specialized card for displaying financial information
class FinancialCard extends StatelessWidget {
  const FinancialCard({
    super.key,
    required this.icon,
    required this.title,
    required this.amount,
    this.subtitle,
    this.amountColor,
    this.actions,
    this.onTap,
    this.showActions = false,
  });
  final Widget icon;
  final String title;
  final String amount;
  final String? subtitle;
  final Color? amountColor;
  final List<Widget>? actions;
  final VoidCallback? onTap;
  final bool showActions;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BaseCard(
      onTap: onTap,
      showActions: showActions,
      actions: actions,
      leading: icon,
      title: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            )
          : null,
      trailing: Text(
        amount,
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: AppTypography.fontWeightSemiBold,
          color: amountColor ?? theme.colorScheme.onSurface,
        ),
      ),
    );
  }
}

/// Specialized card for displaying list items with consistent styling
class ListItemCard extends StatelessWidget {
  const ListItemCard({
    super.key,
    this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.actions,
    this.onTap,
    this.showActions = false,
    this.isActive = true,
  });
  final Widget? leading;
  final String title;
  final String? subtitle;
  final Widget? trailing;
  final List<Widget>? actions;
  final VoidCallback? onTap;
  final bool showActions;
  final bool isActive;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BaseCard(
      onTap: onTap,
      showActions: showActions,
      actions: actions,
      leading: leading,
      title: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: isActive
                    ? theme.colorScheme.onSurface
                    : theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ),
          if (!isActive) ...[
            const SizedBox(width: AppSpacing.xs),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.sm,
                vertical: AppSpacing.xs,
              ),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(AppBorderRadius.sm),
              ),
              child: Text(
                'Inactive',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  fontWeight: AppTypography.fontWeightMedium,
                ),
              ),
            ),
          ],
        ],
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            )
          : null,
      trailing: trailing,
    );
  }
}

/// Specialized card for displaying category information
class CategoryCard extends StatelessWidget {
  const CategoryCard({
    super.key,
    required this.icon,
    required this.name,
    this.description,
    this.backgroundColor,
    this.actions,
    this.onTap,
    this.showActions = false,
  });
  final Widget icon;
  final String name;
  final String? description;
  final Color? backgroundColor;
  final List<Widget>? actions;
  final VoidCallback? onTap;
  final bool showActions;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BaseCard(
      onTap: onTap,
      showActions: showActions,
      actions: actions,
      backgroundColor: backgroundColor,
      leading: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: backgroundColor ?? theme.colorScheme.primaryContainer,
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
        ),
        child: icon,
      ),
      title: Text(
        name,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: description != null
          ? Text(
              description!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            )
          : null,
    );
  }
}
