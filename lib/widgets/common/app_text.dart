import 'package:budapp/config/text_overflow_config.dart';
import 'package:flutter/material.dart';

/// A smart text widget that automatically handles text overflow based on context
class AppText extends StatelessWidget {
  const AppText(
    this.data, {
    super.key,
    this.style,
    this.context,
    this.overflowConfig,
    this.textAlign,
    this.textDirection,
    this.locale,
    this.softWrap,
    this.overflow,
    this.maxLines,
    this.textScaler,
    this.semanticsLabel,
    this.textWidthBasis,
    this.textHeightBehavior,
    this.selectionColor,
  });

  /// Creates AppText with title context
  const AppText.title(
    this.data, {
    super.key,
    this.style,
    this.overflowConfig,
    this.textAlign,
    this.textDirection,
    this.locale,
    this.softWrap,
    this.overflow,
    this.maxLines,
    this.textScaler,
    this.semanticsLabel,
    this.textWidthBasis,
    this.textHeightBehavior,
    this.selectionColor,
  }) : context = TextContext.title;

  /// Creates AppText with body context
  const AppText.body(
    this.data, {
    super.key,
    this.style,
    this.overflowConfig,
    this.textAlign,
    this.textDirection,
    this.locale,
    this.softWrap,
    this.overflow,
    this.maxLines,
    this.textScaler,
    this.semanticsLabel,
    this.textWidthBasis,
    this.textHeightBehavior,
    this.selectionColor,
  }) : context = TextContext.body;

  /// Creates AppText with label context
  const AppText.label(
    this.data, {
    super.key,
    this.style,
    this.overflowConfig,
    this.textAlign,
    this.textDirection,
    this.locale,
    this.softWrap,
    this.overflow,
    this.maxLines,
    this.textScaler,
    this.semanticsLabel,
    this.textWidthBasis,
    this.textHeightBehavior,
    this.selectionColor,
  }) : context = TextContext.label;

  /// Creates AppText with critical context
  const AppText.critical(
    this.data, {
    super.key,
    this.style,
    this.overflowConfig,
    this.textAlign,
    this.textDirection,
    this.locale,
    this.softWrap,
    this.overflow,
    this.maxLines,
    this.textScaler,
    this.semanticsLabel,
    this.textWidthBasis,
    this.textHeightBehavior,
    this.selectionColor,
  }) : context = TextContext.critical;

  /// Creates AppText with note context
  const AppText.note(
    this.data, {
    super.key,
    this.style,
    this.overflowConfig,
    this.textAlign,
    this.textDirection,
    this.locale,
    this.softWrap,
    this.overflow,
    this.maxLines,
    this.textScaler,
    this.semanticsLabel,
    this.textWidthBasis,
    this.textHeightBehavior,
    this.selectionColor,
  }) : context = TextContext.note;

  /// Creates AppText with button context
  const AppText.button(
    this.data, {
    super.key,
    this.style,
    this.overflowConfig,
    this.textAlign,
    this.textDirection,
    this.locale,
    this.softWrap,
    this.overflow,
    this.maxLines,
    this.textScaler,
    this.semanticsLabel,
    this.textWidthBasis,
    this.textHeightBehavior,
    this.selectionColor,
  }) : context = TextContext.button;

  /// Creates AppText with list item context
  const AppText.listItem(
    this.data, {
    super.key,
    this.style,
    this.overflowConfig,
    this.textAlign,
    this.textDirection,
    this.locale,
    this.softWrap,
    this.overflow,
    this.maxLines,
    this.textScaler,
    this.semanticsLabel,
    this.textWidthBasis,
    this.textHeightBehavior,
    this.selectionColor,
  }) : context = TextContext.listItem;

  /// The text to display
  final String data;

  /// The text style to apply
  final TextStyle? style;

  /// Text context for automatic overflow handling
  final TextContext? context;

  /// Manual overflow configuration (overrides context)
  final TextOverflowConfig? overflowConfig;

  /// Text alignment
  final TextAlign? textAlign;

  /// Text direction
  final TextDirection? textDirection;

  /// Locale for text rendering
  final Locale? locale;

  /// Whether the text should break at soft line breaks
  final bool? softWrap;

  /// How visual overflow should be handled
  final TextOverflow? overflow;

  /// Maximum number of lines
  final int? maxLines;

  /// Text scaler for accessibility
  final TextScaler? textScaler;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Width basis for text layout
  final TextWidthBasis? textWidthBasis;

  /// Text height behavior
  final TextHeightBehavior? textHeightBehavior;

  /// Selection color
  final Color? selectionColor;

  @override
  Widget build(BuildContext context) {
    // Get the appropriate overflow configuration
    final config = _getOverflowConfig();

    // Build the text widget with adaptive scaling if needed
    if (config.adaptive && config.minScaleFactor != null) {
      return _buildAdaptiveText(config);
    }

    return _buildStandardText(config);
  }

  /// Gets the appropriate overflow configuration
  TextOverflowConfig _getOverflowConfig() {
    // Manual configuration takes precedence
    if (overflowConfig != null) {
      return overflowConfig!;
    }

    // Use explicit context if provided
    if (context != null) {
      return AppTextOverflow.forContext(context!);
    }

    // Auto-detect from style
    return AppTextOverflow.detectFromStyle(style);
  }

  /// Builds standard text widget with overflow configuration
  Widget _buildStandardText(TextOverflowConfig config) {
    return Text(
      data,
      style: style,
      textAlign: textAlign,
      textDirection: textDirection,
      locale: locale,
      softWrap: softWrap ?? config.softWrap,
      overflow: overflow ?? config.overflow,
      maxLines: maxLines ?? config.maxLines,
      textScaler: textScaler,
      semanticsLabel: semanticsLabel,
      textWidthBasis: textWidthBasis,
      textHeightBehavior: textHeightBehavior,
      selectionColor: selectionColor,
    );
  }

  /// Builds adaptive text widget that can scale to fit
  Widget _buildAdaptiveText(TextOverflowConfig config) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return FittedBox(
          fit: BoxFit.scaleDown,
          alignment: _getAlignment(),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minWidth: constraints.maxWidth,
              maxWidth: constraints.maxWidth,
            ),
            child: Text(
              data,
              style: style,
              textAlign: textAlign,
              textDirection: textDirection,
              locale: locale,
              softWrap: softWrap ?? config.softWrap,
              overflow: overflow ?? config.overflow,
              maxLines: maxLines ?? config.maxLines,
              textScaler: textScaler,
              semanticsLabel: semanticsLabel,
              textWidthBasis: textWidthBasis,
              textHeightBehavior: textHeightBehavior,
              selectionColor: selectionColor,
            ),
          ),
        );
      },
    );
  }

  /// Gets alignment for FittedBox based on text alignment
  Alignment _getAlignment() {
    switch (textAlign) {
      case TextAlign.left:
      case TextAlign.start:
        return Alignment.centerLeft;
      case TextAlign.right:
      case TextAlign.end:
        return Alignment.centerRight;
      case TextAlign.center:
        return Alignment.center;
      case TextAlign.justify:
        return Alignment.centerLeft;
      case null:
        return Alignment.centerLeft;
    }
  }
}
