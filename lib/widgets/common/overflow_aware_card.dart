import 'package:flutter/material.dart';

import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/config/overflow_theme_extension.dart';
import 'package:budapp/config/overflow_tokens.dart';
import 'package:budapp/widgets/common/overflow_extensions.dart';

/// Enhanced BaseCard with automatic overflow protection and responsive behavior
/// 
/// This widget builds on the existing BaseCard architecture while adding
/// comprehensive overflow handling, responsive constraints, and adaptive content management.
class OverflowAwareCard extends StatelessWidget {
  const OverflowAwareCard({
    super.key,
    this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.actions,
    this.onTap,
    this.onLongPress,
    this.elevation,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.borderRadius,
    this.showActions = false,
    this.child,
    this.overflowStrategy,
    this.constraints,
    this.enableScrolling = true,
    this.expandable = false,
    this.maxLines,
  });

  final Widget? leading;
  final Widget title;
  final Widget? subtitle;
  final Widget? trailing;
  final List<Widget>? actions;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final double? elevation;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final bool showActions;
  final Widget? child;
  
  // Overflow-specific properties
  final OverflowStrategy? overflowStrategy;
  final ContainerConstraints? constraints;
  final bool enableScrolling;
  final bool expandable;
  final int? maxLines;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final overflowTheme = OverflowThemeExtension.defaultTheme(context);
    final deviceSize = AppOverflowTokens.getDeviceSize(context);
    
    final cardConstraints = constraints ?? overflowTheme.cardConstraints;
    final strategy = overflowStrategy ?? overflowTheme.defaultStrategy;
    
    final cardBorderRadius = borderRadius ?? BorderRadius.circular(AppBorderRadius.lg);
    final maxHeight = cardConstraints.getHeightForDevice(deviceSize);
    final maxWidth = cardConstraints.getWidthForDevice(deviceSize);

    var cardContent = child ?? _buildDefaultLayout(context, theme);
    
    // Apply overflow handling based on strategy
    if (overflowTheme.autoApplyOverflowHandling || overflowStrategy != null) {
      cardContent = _applyOverflowHandling(cardContent, strategy, overflowTheme);
    }

    return ConstrainedBox(
      constraints: BoxConstraints(
        minHeight: cardConstraints.minHeight ?? 0.0,
        maxHeight: maxHeight ?? double.infinity,
        minWidth: cardConstraints.minWidth ?? 0.0,
        maxWidth: maxWidth ?? double.infinity,
      ),
      child: Card(
        elevation: elevation ?? AppElevation.sm,
        margin: margin ?? EdgeInsets.zero,
        color: backgroundColor,
        clipBehavior: Clip.antiAlias,
        shape: RoundedRectangleBorder(borderRadius: cardBorderRadius),
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          borderRadius: cardBorderRadius,
          child: Padding(
            padding: padding ?? const EdgeInsets.all(AppSpacing.md),
            child: cardContent,
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultLayout(BuildContext context, ThemeData theme) {
    final widgets = <Widget>[];

    // Header row with leading, title/subtitle, trailing, and actions
    widgets.add(
      Row(
        children: [
          // Leading widget (icon, avatar, etc.)
          if (leading != null) ...[
            leading!,
            const SizedBox(width: AppSpacing.md),
          ],

          // Title and subtitle with overflow protection
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTitleWithOverflow(theme),
                if (subtitle != null) ...[
                  const SizedBox(height: AppSpacing.xs),
                  _buildSubtitleWithOverflow(theme),
                ],
              ],
            ),
          ),

          // Trailing widget
          if (trailing != null) ...[
            const SizedBox(width: AppSpacing.sm),
            trailing!,
          ],

          // Actions menu
          if (showActions && actions != null && actions!.isNotEmpty) ...[
            const SizedBox(width: AppSpacing.sm),
            _buildActionsMenu(context, theme),
          ],
        ],
      ),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: widgets,
    );
  }

  Widget _buildTitleWithOverflow(ThemeData theme) {
    if (title is Text) {
      final textWidget = title as Text;
      return Text(
        textWidget.data ?? '',
        style: textWidget.style ?? theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
        maxLines: maxLines ?? (expandable ? null : 2),
        overflow: expandable ? null : TextOverflow.ellipsis,
        textAlign: textWidget.textAlign,
      );
    }
    return title;
  }

  Widget _buildSubtitleWithOverflow(ThemeData theme) {
    if (subtitle is Text) {
      final textWidget = subtitle! as Text;
      return Text(
        textWidget.data ?? '',
        style: textWidget.style ?? theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.onSurfaceVariant,
        ),
        maxLines: maxLines ?? (expandable ? null : 2),
        overflow: expandable ? null : TextOverflow.ellipsis,
        textAlign: textWidget.textAlign,
      );
    }
    return subtitle!;
  }

  Widget _buildActionsMenu(BuildContext context, ThemeData theme) {
    return PopupMenuButton<VoidCallback>(
      onSelected: (callback) => callback(),
      itemBuilder: (context) => actions!
          .asMap()
          .entries
          .map(
            (entry) => PopupMenuItem<VoidCallback>(
              value: () {}, // Will be handled by the action widget
              child: entry.value,
            ),
          )
          .toList(),
      icon: Icon(
        Icons.more_vert,
        color: theme.colorScheme.onSurfaceVariant,
      ),
    );
  }

  Widget _applyOverflowHandling(
    Widget content, 
    OverflowStrategy strategy, 
    OverflowThemeExtension overflowTheme,
  ) {
    switch (strategy) {
      case OverflowStrategy.scroll:
        return enableScrolling 
            ? content.withScrollableContent(context: 'card')
            : content;
      
      case OverflowStrategy.adaptive:
        return content.withSmartOverflow(strategy: strategy, context: 'card');
      
      case OverflowStrategy.wrap:
        return content.withResponsiveLayout(context: 'card');
      
      case OverflowStrategy.fade:
        return ShaderMask(
          shaderCallback: (bounds) {
            return const LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.black, Colors.black, Colors.transparent],
              stops: [0.0, 0.7, 1.0],
            ).createShader(bounds);
          },
          blendMode: BlendMode.dstIn,
          child: content,
        );
      
      case OverflowStrategy.scale:
        return content.withAdaptiveSize(fit: BoxFit.scaleDown);
      
      case OverflowStrategy.clip:
        return ClipRect(child: content);
      
      case OverflowStrategy.visible:
        return content;
      
      case OverflowStrategy.ellipsis:
        return content;
    }
  }
}

/// Enhanced FinancialCard with overflow protection
class OverflowAwareFinancialCard extends StatelessWidget {
  const OverflowAwareFinancialCard({
    super.key,
    required this.icon,
    required this.title,
    required this.amount,
    this.subtitle,
    this.amountColor,
    this.actions,
    this.onTap,
    this.showActions = false,
    this.overflowStrategy,
    this.constraints,
  });

  final Widget icon;
  final String title;
  final String amount;
  final String? subtitle;
  final Color? amountColor;
  final List<Widget>? actions;
  final VoidCallback? onTap;
  final bool showActions;
  final OverflowStrategy? overflowStrategy;
  final ContainerConstraints? constraints;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return OverflowAwareCard(
      onTap: onTap,
      showActions: showActions,
      actions: actions,
      leading: icon,
      overflowStrategy: overflowStrategy,
      constraints: constraints,
      title: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            )
          : null,
      trailing: Text(
        amount,
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: AppTypography.fontWeightSemiBold,
          color: amountColor ?? theme.colorScheme.onSurface,
        ),
      ),
    );
  }
}

/// Enhanced ListItemCard with overflow protection
class OverflowAwareListItemCard extends StatelessWidget {
  const OverflowAwareListItemCard({
    super.key,
    this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.actions,
    this.onTap,
    this.showActions = false,
    this.isActive = true,
    this.overflowStrategy,
    this.constraints,
    this.maxLines,
  });

  final Widget? leading;
  final String title;
  final String? subtitle;
  final Widget? trailing;
  final List<Widget>? actions;
  final VoidCallback? onTap;
  final bool showActions;
  final bool isActive;
  final OverflowStrategy? overflowStrategy;
  final ContainerConstraints? constraints;
  final int? maxLines;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return OverflowAwareCard(
      onTap: onTap,
      showActions: showActions,
      actions: actions,
      leading: leading,
      overflowStrategy: overflowStrategy,
      constraints: constraints ?? AppOverflowTokens.compactCardConstraints,
      maxLines: maxLines,
      title: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: isActive
                    ? theme.colorScheme.onSurface
                    : theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              maxLines: maxLines ?? 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (!isActive) ...[
            const SizedBox(width: AppSpacing.xs),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.sm,
                vertical: AppSpacing.xs,
              ),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(AppBorderRadius.sm),
              ),
              child: Text(
                'Inactive',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  fontWeight: AppTypography.fontWeightMedium,
                ),
              ),
            ),
          ],
        ],
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              maxLines: maxLines ?? 2,
              overflow: TextOverflow.ellipsis,
            )
          : null,
      trailing: trailing,
    );
  }
}

/// Enhanced CategoryCard with overflow protection
class OverflowAwareCategoryCard extends StatelessWidget {
  const OverflowAwareCategoryCard({
    super.key,
    required this.icon,
    required this.name,
    this.description,
    this.backgroundColor,
    this.actions,
    this.onTap,
    this.showActions = false,
    this.overflowStrategy,
    this.constraints,
    this.maxLines,
  });

  final Widget icon;
  final String name;
  final String? description;
  final Color? backgroundColor;
  final List<Widget>? actions;
  final VoidCallback? onTap;
  final bool showActions;
  final OverflowStrategy? overflowStrategy;
  final ContainerConstraints? constraints;
  final int? maxLines;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return OverflowAwareCard(
      onTap: onTap,
      showActions: showActions,
      actions: actions,
      backgroundColor: backgroundColor,
      overflowStrategy: overflowStrategy,
      constraints: constraints,
      maxLines: maxLines,
      leading: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: backgroundColor ?? theme.colorScheme.primaryContainer,
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
        ),
        child: icon,
      ),
      title: Text(
        name,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: description != null
          ? Text(
              description!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            )
          : null,
    );
  }
}

/// Feature card specifically for hub/dashboard grids with enhanced overflow protection
class OverflowAwareFeatureCard extends StatelessWidget {
  const OverflowAwareFeatureCard({
    super.key,
    required this.icon,
    required this.iconColor,
    required this.title,
    required this.subtitle,
    required this.actionText,
    required this.onTap,
    this.overflowStrategy = OverflowStrategy.adaptive,
    this.enableScrolling = false,
    this.maxLines,
  });

  final IconData icon;
  final Color iconColor;
  final String title;
  final String subtitle;
  final String actionText;
  final VoidCallback onTap;
  final OverflowStrategy overflowStrategy;
  final bool enableScrolling;
  final int? maxLines;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final deviceSize = AppOverflowTokens.getDeviceSize(context);
    const gridConstraints = AppOverflowTokens.featureGridConstraints;
    
    final maxHeight = gridConstraints.maxChildHeight;
    final aspectRatio = gridConstraints.getAspectRatioForDevice(deviceSize);

    return LayoutBuilder(
      builder: (context, constraints) {
        final calculatedHeight = constraints.maxWidth / aspectRatio;
        final finalHeight = maxHeight != null 
            ? calculatedHeight.clamp(gridConstraints.minChildHeight ?? 0.0, maxHeight)
            : calculatedHeight;

        return SizedBox(
          height: finalHeight,
          child: OverflowAwareCard(
            title: Text(title),
            onTap: onTap,
            padding: const EdgeInsets.all(AppSpacing.lg),
            overflowStrategy: overflowStrategy,
            enableScrolling: enableScrolling,
            constraints: ContainerConstraints(
              maxHeight: finalHeight,
              minHeight: gridConstraints.minChildHeight,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: iconColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppBorderRadius.md),
                  ),
                  child: Icon(
                    icon,
                    color: iconColor,
                    size: 28,
                  ),
                ),
                const SizedBox(height: AppSpacing.md),

                // Title with overflow protection
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: AppTypography.fontWeightSemiBold,
                    color: theme.colorScheme.onSurface,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: maxLines ?? 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: AppSpacing.xs),

                // Subtitle with overflow protection
                Flexible(
                  child: Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: maxLines ?? 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(height: AppSpacing.sm),

                // Action text with overflow protection
                Text(
                  actionText,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: iconColor,
                    fontWeight: AppTypography.fontWeightMedium,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
