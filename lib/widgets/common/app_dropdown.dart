import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Generic dropdown widget that handles async data loading with consistent styling
///
/// This widget provides a reusable dropdown pattern for selecting from async data
/// with loading states, error handling, and consistent Material 3 design.
class AppDropdown<T> extends ConsumerWidget {
  const AppDropdown({
    super.key,
    required this.label,
    this.isRequired = false,
    required this.selectedValue,
    required this.onChanged,
    required this.asyncData,
    required this.getDisplayText,
    required this.getValue,
    this.buildCustomItem,
    this.errorText,
    this.enabled = true,
    this.hintText,
    this.emptyMessage,
    this.prefixIcon,
  });
  final String label;
  final bool isRequired;
  final T? selectedValue;
  final ValueChanged<T?> onChanged;
  final AsyncValue<List<T>> asyncData;
  final String Function(T) getDisplayText;
  final String Function(T) getValue;
  final Widget Function(T)? buildCustomItem;
  final String? errorText;
  final bool enabled;
  final String? hintText;
  final String? emptyMessage;
  final Widget? prefixIcon;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with optional required indicator
        Row(
          children: [
            Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: AppTypography.fontWeightMedium,
              ),
            ),
            if (isRequired) ...[
              const SizedBox(width: AppSpacing.xs),
              Text(
                '*',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.error,
                  fontWeight: AppTypography.fontWeightMedium,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: AppSpacing.sm),

        // Dropdown with async data handling
        asyncData.when(
          data: (items) => _buildDropdown(context, theme, l10n, items),
          loading: () => _buildLoadingDropdown(context, theme, l10n),
          error: (error, stack) => _buildErrorDropdown(context, theme, l10n),
        ),

        // Error text
        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDropdown(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
    List<T> items,
  ) {
    return DropdownButtonFormField<T>(
      value: selectedValue,
      onChanged: enabled ? onChanged : null,
      decoration: InputDecoration(
        hintText: hintText ?? l10n.pleaseSelect,
        prefixIcon: prefixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
          borderSide: BorderSide(color: theme.colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
          borderSide: BorderSide(color: theme.colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
          borderSide: BorderSide(color: theme.colorScheme.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
          borderSide: BorderSide(color: theme.colorScheme.error, width: 2),
        ),
        filled: true,
        fillColor: theme.colorScheme.surfaceContainerHighest.withValues(
          alpha: 0.3,
        ),
      ),
      items: items.isEmpty
          ? []
          : items.map((item) {
              return DropdownMenuItem<T>(
                value: item,
                child:
                    buildCustomItem?.call(item) ??
                    Text(
                      getDisplayText(item),
                      style: theme.textTheme.bodyMedium,
                      overflow: TextOverflow.ellipsis,
                    ),
              );
            }).toList(),
      hint: items.isEmpty
          ? Text(
              emptyMessage ?? l10n.noItemsAvailable,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            )
          : null,
      isExpanded: true,
      icon: Icon(
        Icons.arrow_drop_down,
        color: enabled
            ? theme.colorScheme.onSurfaceVariant
            : theme.colorScheme.onSurface.withValues(alpha: 0.38),
      ),
    );
  }

  Widget _buildLoadingDropdown(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return DropdownButtonFormField<String>(
      onChanged: null,
      decoration: InputDecoration(
        hintText: l10n.loading,
        prefixIcon: prefixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
        ),
        filled: true,
        fillColor: theme.colorScheme.surfaceContainerHighest.withValues(
          alpha: 0.3,
        ),
      ),
      items: const [],
      hint: Row(
        children: [
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          const SizedBox(width: AppSpacing.sm),
          Text(
            l10n.loading,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
      isExpanded: true,
    );
  }

  Widget _buildErrorDropdown(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return DropdownButtonFormField<String>(
      onChanged: null,
      decoration: InputDecoration(
        hintText: l10n.errorLoadingData,
        prefixIcon: prefixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
          borderSide: BorderSide(color: theme.colorScheme.error),
        ),
        filled: true,
        fillColor: theme.colorScheme.errorContainer.withValues(alpha: 0.1),
      ),
      items: const [],
      hint: Row(
        children: [
          Icon(Icons.error_outline, size: 16, color: theme.colorScheme.error),
          const SizedBox(width: AppSpacing.sm),
          Text(
            l10n.errorLoadingData,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ),
      isExpanded: true,
    );
  }
}
