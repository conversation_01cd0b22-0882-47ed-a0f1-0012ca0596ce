// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'custom_bottom_navigation.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$shouldShowBottomNavHash() =>
    r'811cf6e4c7124c5dabd070ea0e7bc61a7ddf1a87';

/// Provider to track if bottom navigation should be visible
///
/// Copied from [shouldShowBottomNav].
@ProviderFor(shouldShowBottomNav)
final shouldShowBottomNavProvider = AutoDisposeProvider<bool>.internal(
  shouldShowBottomNav,
  name: r'shouldShowBottomNavProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$shouldShowBottomNavHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ShouldShowBottomNavRef = AutoDisposeProviderRef<bool>;
String _$bottomNavHeightHash() => r'c3923cf76c489fd10f7e497e3892e80a9e360f62';

/// Provider for bottom navigation height (for layout calculations)
///
/// Copied from [bottomNavHeight].
@ProviderFor(bottomNavHeight)
final bottomNavHeightProvider = AutoDisposeProvider<double>.internal(
  bottomNavHeight,
  name: r'bottomNavHeightProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$bottomNavHeightHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BottomNavHeightRef = AutoDisposeProviderRef<double>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
