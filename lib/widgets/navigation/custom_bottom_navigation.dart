import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/widgets/navigation/bottom_nav_item.dart';
import 'package:budapp/widgets/navigation/bottom_nav_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'custom_bottom_navigation.g.dart';

/// Custom bottom navigation bar with special center button and toggle functionality
class CustomBottomNavigation extends ConsumerStatefulWidget {
  const CustomBottomNavigation({super.key});

  @override
  ConsumerState<CustomBottomNavigation> createState() =>
      _CustomBottomNavigationState();
}

class _CustomBottomNavigationState
    extends ConsumerState<CustomBottomNavigation> {
  void _onItemTap(int index) {
    final items = ref.read(bottomNavItemsProvider);
    if (index >= 0 && index < items.length) {
      final item = items[index];

      // Update navigation index
      ref.read(bottomNavIndexProvider.notifier).setIndex(index);

      // Navigate to route
      context.go(item.route);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final items = ref.watch(bottomNavItemsProvider);
    final currentIndex = ref.watch(bottomNavIndexProvider);

    return DecoratedBox(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Container(
          height:
              64, // Reduced height as requested (increased to prevent overflow)
          padding: const EdgeInsets.symmetric(horizontal: AppSpacing.xs),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: List.generate(items.length, (index) {
              final item = items[index];
              final isSelected = currentIndex == index;

              // Special handling for center add button
              if (item.isSpecial) {
                return CenterAddButton(onTap: () => _onItemTap(index));
              }

              // Regular navigation items
              return BottomNavItemWidget(
                item: item,
                isSelected: isSelected,
                onTap: () => _onItemTap(index),
                index: index,
              );
            }),
          ),
        ),
      ),
    );
  }
}

/// Extension to provide semantic information for bottom navigation
extension BottomNavSemantics on CustomBottomNavigation {
  Widget withSemantics() {
    return Semantics(
      container: true,
      label: 'Bottom navigation',
      hint: 'Navigate between main sections of the app',
      child: this,
    );
  }
}

/// Provider to track if bottom navigation should be visible
@riverpod
bool shouldShowBottomNav(Ref ref) {
  // You can add logic here to hide bottom nav on certain screens
  // For now, always show it
  return true;
}

/// Provider for bottom navigation height (for layout calculations)
@riverpod
double bottomNavHeight(Ref ref) {
  return 60; // Base height + safe area will be added by SafeArea widget
}
