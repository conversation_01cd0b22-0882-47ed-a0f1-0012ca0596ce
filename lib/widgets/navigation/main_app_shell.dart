import 'package:budapp/widgets/navigation/bottom_nav_providers.dart';
import 'package:budapp/widgets/navigation/custom_bottom_navigation.dart';
import 'package:budapp/widgets/navigation/navigation_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Main application shell that provides consistent navigation structure
/// for all authenticated screens
class MainAppShell extends ConsumerStatefulWidget {
  const MainAppShell({super.key, required this.child});

  final Widget child;

  @override
  ConsumerState<MainAppShell> createState() => _MainAppShellState();
}

class _MainAppShellState extends ConsumerState<MainAppShell> {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Update current route when the app shell rebuilds
    final location = GoRouterState.of(context).uri.path;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(currentRouteProvider.notifier).route = location;
      // Update both old and new navigation providers for compatibility
      ref.read(navigationIndexProvider.notifier).setIndexFromRoute(location);
      ref.read(bottomNavIndexProvider.notifier).setIndexFromRoute(location);
    });
  }

  @override
  Widget build(BuildContext context) {
    final shouldShowBottomNav = ref.watch(shouldShowBottomNavProvider);

    return Scaffold(
      body: widget.child,
      bottomNavigationBar: shouldShowBottomNav
          ? const CustomBottomNavigation()
          : null,
      // Remove FAB since we have integrated add button in bottom nav
    );
  }
}

/// Shell route builder that wraps content in MainAppShell
Widget shellRouteBuilder(
  BuildContext context,
  GoRouterState state,
  Widget child,
) {
  return MainAppShell(child: child);
}
