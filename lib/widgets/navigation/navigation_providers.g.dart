// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'navigation_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fabActionRouteHash() => r'5eada3cedc28349bdb09a0861d5ea5a02aaa10e5';

/// Provider for FAB action route based on current route
///
/// Copied from [fabActionRoute].
@ProviderFor(fabActionRoute)
final fabActionRouteProvider = AutoDisposeProvider<String?>.internal(
  fabActionRoute,
  name: r'fabActionRouteProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fabActionRouteHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FabActionRouteRef = AutoDisposeProviderRef<String?>;
String _$fabIconHash() => r'ef2261e247f4a34c0f123d84708f50490aaa9b74';

/// Provider for FAB icon based on current route
///
/// Copied from [fabIcon].
@ProviderFor(fabIcon)
final fabIconProvider = AutoDisposeProvider<IconData>.internal(
  fabIcon,
  name: r'fabIconProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fabIconHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FabIconRef = AutoDisposeProviderRef<IconData>;
String _$fabTooltipHash() => r'74ef3926a4abc44456f811e19d52b88921ccfa8a';

/// Provider for FAB tooltip based on current route
///
/// Copied from [fabTooltip].
@ProviderFor(fabTooltip)
final fabTooltipProvider = AutoDisposeProvider<String>.internal(
  fabTooltip,
  name: r'fabTooltipProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fabTooltipHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FabTooltipRef = AutoDisposeProviderRef<String>;
String _$isRouteImplementedHash() =>
    r'844da660108d76d3457d8001cc8f318031da7fa2';

/// Provider to check if current route is implemented
///
/// Copied from [isRouteImplemented].
@ProviderFor(isRouteImplemented)
final isRouteImplementedProvider = AutoDisposeProvider<bool>.internal(
  isRouteImplemented,
  name: r'isRouteImplementedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isRouteImplementedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsRouteImplementedRef = AutoDisposeProviderRef<bool>;
String _$navigationIndexHash() => r'9e3303f1a1403611188d74c6fb71b9a7485391a0';

/// Provider for current navigation index
///
/// Copied from [NavigationIndex].
@ProviderFor(NavigationIndex)
final navigationIndexProvider =
    AutoDisposeNotifierProvider<NavigationIndex, int>.internal(
      NavigationIndex.new,
      name: r'navigationIndexProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$navigationIndexHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$NavigationIndex = AutoDisposeNotifier<int>;
String _$currentRouteHash() => r'a2c268f9b52cb7573b5f85b39fa141288395863f';

/// Provider for current route path
///
/// Copied from [CurrentRoute].
@ProviderFor(CurrentRoute)
final currentRouteProvider =
    AutoDisposeNotifierProvider<CurrentRoute, String>.internal(
      CurrentRoute.new,
      name: r'currentRouteProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$currentRouteHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CurrentRoute = AutoDisposeNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
