import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'bottom_nav_providers.g.dart';

/// Bottom navigation item data
class BottomNavItem {
  // For center add button

  const BottomNavItem({
    required this.label,
    required this.route,
    required this.icon,
    required this.selectedIcon,
    required this.semanticLabel,
    this.isSpecial = false,
  });
  final String label;
  final String route;
  final IconData icon;
  final IconData selectedIcon;
  final String semanticLabel;
  final bool isSpecial;
}

/// Provider for bottom navigation items
@riverpod
List<BottomNavItem> bottomNavItems(Ref ref) {
  return [
    const BottomNavItem(
      label: 'Home',
      route: '/home',
      icon: Icons.home_outlined,
      selectedIcon: Icons.home_outlined, // Keep outlined
      semanticLabel: 'Navigate to home dashboard',
    ),
    const BottomNavItem(
      label: 'Accounts',
      route: '/accounts',
      icon: Icons.account_balance_wallet_outlined,
      selectedIcon: Icons.account_balance_wallet_outlined, // Keep outlined
      semanticLabel: 'Navigate to accounts list',
    ),
    const BottomNavItem(
      label: 'Add Transaction',
      route: '/transactions/create',
      icon: Icons.add,
      selectedIcon: Icons.add,
      semanticLabel: 'Add new transaction',
      isSpecial: true, // Special center button
    ),
    const BottomNavItem(
      label: 'Budgets',
      route: '/budgets',
      icon: Icons.pie_chart_outline,
      selectedIcon: Icons.pie_chart_outline, // Keep outlined
      semanticLabel: 'Navigate to budgets',
    ),
    const BottomNavItem(
      label: 'Profile',
      route: '/profile',
      icon: Icons.menu,
      selectedIcon: Icons.menu, // Hamburger menu icon
      semanticLabel: 'Navigate to profile and settings',
    ),
  ];
}

/// Provider for current bottom navigation index
@riverpod
class BottomNavIndex extends _$BottomNavIndex {
  @override
  int build() => 0; // Default to home

  void setIndex(int index) {
    final items = ref.read(bottomNavItemsProvider);
    if (index >= 0 && index < items.length) {
      state = index;
    }
  }

  void setIndexFromRoute(String route) {
    final items = ref.read(bottomNavItemsProvider);
    for (var i = 0; i < items.length; i++) {
      if (route.startsWith(items[i].route)) {
        state = i;
      }
    }
  }
}
