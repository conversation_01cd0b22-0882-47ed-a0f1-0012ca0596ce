// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bottom_nav_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$bottomNavItemsHash() => r'ecc3c7a0388e80bbae1d3001da4ab60ff7f353a1';

/// Provider for bottom navigation items
///
/// Copied from [bottomNavItems].
@ProviderFor(bottomNavItems)
final bottomNavItemsProvider =
    AutoDisposeProvider<List<BottomNavItem>>.internal(
      bottomNavItems,
      name: r'bottomNavItemsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$bottomNavItemsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BottomNavItemsRef = AutoDisposeProviderRef<List<BottomNavItem>>;
String _$bottomNavIndexHash() => r'a70316c2d96f685404e9480086d5ba3e554bda8b';

/// Provider for current bottom navigation index
///
/// Copied from [BottomNavIndex].
@ProviderFor(BottomNavIndex)
final bottomNavIndexProvider =
    AutoDisposeNotifierProvider<BottomNavIndex, int>.internal(
      BottomNavIndex.new,
      name: r'bottomNavIndexProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$bottomNavIndexHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$BottomNavIndex = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
