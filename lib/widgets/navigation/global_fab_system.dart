import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Global FAB system that provides consistent floating action buttons across all screens
class GlobalFabSystem extends ConsumerWidget {
  const GlobalFabSystem({
    super.key,
    required this.child,
    this.showBackFab = true,
    this.showHomeFab = false,
    this.showAddTransactionFab = true,
  });

  final Widget child;
  final bool showBackFab;
  final bool showHomeFab;
  final bool showAddTransactionFab;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: child,
      floatingActionButton: _buildFabStack(context),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildFabStack(BuildContext context) {
    final fabs = <Widget>[];

    // Add Transaction FAB (Green, bottom right)
    if (showAddTransactionFab) {
      fabs.add(
        Positioned(
          bottom: 16,
          right: 16,
          child: _AddTransactionFab(),
        ),
      );
    }

    // Back FAB (Grey, bottom left)
    if (showBackFab) {
      fabs.add(
        Positioned(
          bottom: 16,
          left: 16,
          child: _BackFab(),
        ),
      );
    }

    // Home FAB (Grey, center bottom)
    if (showHomeFab) {
      fabs.add(
        Positioned(
          bottom: 16,
          left: 0,
          right: 0,
          child: Center(
            child: _HomeFab(),
          ),
        ),
      );
    }

    return Stack(
      children: fabs,
    );
  }
}

/// Green FAB for adding transactions (appears on all screens)
class _AddTransactionFab extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return FloatingActionButton(
      onPressed: () => context.push('/transactions/create'),
      backgroundColor: theme.colorScheme.primary,
      foregroundColor: theme.colorScheme.onPrimary,
      elevation: 6,
      child: const Icon(Icons.add, size: 28),
    );
  }
}

/// Grey FAB for going back (appears on all screens except home)
class _BackFab extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return FloatingActionButton(
      onPressed: () {
        if (context.canPop()) {
          context.pop();
        } else {
          context.go('/home');
        }
      },
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      foregroundColor: theme.colorScheme.onSurface,
      elevation: 6,
      child: const Icon(Icons.arrow_back, size: 24),
    );
  }
}

/// Grey FAB for going to home (appears on screens deeper than one step from home)
class _HomeFab extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return FloatingActionButton(
      onPressed: () => context.go('/home'),
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      foregroundColor: theme.colorScheme.onSurface,
      elevation: 6,
      child: const Icon(Icons.home, size: 24),
    );
  }
}

/// Extension to easily wrap screens with FAB system
extension GlobalFabSystemExtension on Widget {
  Widget withGlobalFabs({
    bool showBackFab = true,
    bool showHomeFab = false,
    bool showAddTransactionFab = true,
  }) {
    return GlobalFabSystem(
      showBackFab: showBackFab,
      showHomeFab: showHomeFab,
      showAddTransactionFab: showAddTransactionFab,
      child: this,
    );
  }
}

/// Provider to determine FAB visibility based on current route
final fabVisibilityProvider = Provider<FabVisibility>((ref) {
  // This will be updated by route changes
  return const FabVisibility();
});

/// Data class for FAB visibility configuration
class FabVisibility {
  const FabVisibility({
    this.showBackFab = true,
    this.showHomeFab = false,
    this.showAddTransactionFab = true,
  });

  final bool showBackFab;
  final bool showHomeFab;
  final bool showAddTransactionFab;

  FabVisibility copyWith({
    bool? showBackFab,
    bool? showHomeFab,
    bool? showAddTransactionFab,
  }) {
    return FabVisibility(
      showBackFab: showBackFab ?? this.showBackFab,
      showHomeFab: showHomeFab ?? this.showHomeFab,
      showAddTransactionFab:
          showAddTransactionFab ?? this.showAddTransactionFab,
    );
  }
}

/// Notifier to manage FAB visibility state
class FabVisibilityNotifier extends Notifier<FabVisibility> {
  @override
  FabVisibility build() {
    return const FabVisibility();
  }

  void updateVisibility({
    bool? showBackFab,
    bool? showHomeFab,
    bool? showAddTransactionFab,
  }) {
    state = state.copyWith(
      showBackFab: showBackFab,
      showHomeFab: showHomeFab,
      showAddTransactionFab: showAddTransactionFab,
    );
  }

  void setForRoute(String route) {
    if (route == '/home') {
      // Home screen: only show add transaction FAB
      state = const FabVisibility(
        showBackFab: false,
        showHomeFab: false,
        showAddTransactionFab: true,
      );
    } else if (_isDeepRoute(route)) {
      // Deep routes: show all FABs
      state = const FabVisibility(
        showBackFab: true,
        showHomeFab: true,
        showAddTransactionFab: true,
      );
    } else {
      // Regular routes: show back and add transaction FABs
      state = const FabVisibility(
        showBackFab: true,
        showHomeFab: false,
        showAddTransactionFab: true,
      );
    }
  }

  bool _isDeepRoute(String route) {
    // Routes that are more than one step from home
    final deepRoutes = [
      '/accounts/create',
      '/accounts/:id/edit',
      '/categories/create',
      '/categories/:id/edit',
      '/transactions/create',
      '/transactions/:id/edit',
      '/tags/create',
      '/tags/:id/edit',
      '/budgets/:id/edit',
      '/goals/create',
      '/goals/:id/edit',
      '/profile/manage',
      '/profile/forgot-password',
    ];

    return deepRoutes.any((pattern) => _matchesPattern(route, pattern));
  }

  bool _matchesPattern(String route, String pattern) {
    // Simple pattern matching for routes with parameters
    final patternParts = pattern.split('/');
    final routeParts = route.split('/');

    if (patternParts.length != routeParts.length) return false;

    for (var i = 0; i < patternParts.length; i++) {
      if (patternParts[i].startsWith(':')) continue; // Parameter
      if (patternParts[i] != routeParts[i]) return false;
    }

    return true;
  }
}

final fabVisibilityNotifierProvider =
    NotifierProvider<FabVisibilityNotifier, FabVisibility>(
      FabVisibilityNotifier.new,
    );
