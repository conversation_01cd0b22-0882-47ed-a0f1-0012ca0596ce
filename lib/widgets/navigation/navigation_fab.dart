import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/widgets/navigation/navigation_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Context-aware floating action button that changes behavior based on current route
class NavigationFab extends ConsumerWidget {
  const NavigationFab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final fabActionRoute = ref.watch(fabActionRouteProvider);
    final fabIcon = ref.watch(fabIconProvider);
    final fabTooltip = ref.watch(fabTooltipProvider);

    // Don't show FAB if no action route is available
    if (fabActionRoute == null) {
      return const SizedBox.shrink();
    }

    return FloatingActionButton(
      onPressed: () => context.go(fabActionRoute),
      tooltip: fabTooltip,
      backgroundColor: theme.colorScheme.primary,
      foregroundColor: theme.colorScheme.onPrimary,
      elevation: AppElevation.lg,
      heroTag: 'nav_fab_${fabActionRoute.hashCode}',
      child: Icon(fabIcon, size: 24),
    );
  }
}

/// Extended floating action button with label
class NavigationExtendedFab extends ConsumerWidget {
  const NavigationExtendedFab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final fabActionRoute = ref.watch(fabActionRouteProvider);
    final fabIcon = ref.watch(fabIconProvider);
    final fabTooltip = ref.watch(fabTooltipProvider);

    // Don't show FAB if no action route is available
    if (fabActionRoute == null) {
      return const SizedBox.shrink();
    }

    return FloatingActionButton.extended(
      onPressed: () => context.go(fabActionRoute),
      tooltip: fabTooltip,
      backgroundColor: theme.colorScheme.primary,
      foregroundColor: theme.colorScheme.onPrimary,
      elevation: AppElevation.lg,
      heroTag: 'nav_extended_fab_${fabActionRoute.hashCode}',
      icon: Icon(fabIcon, size: 20),
      label: Text(
        fabTooltip,
        style: theme.textTheme.labelLarge?.copyWith(
          color: theme.colorScheme.onPrimary,
          fontWeight: AppTypography.fontWeightMedium,
        ),
      ),
    );
  }
}
