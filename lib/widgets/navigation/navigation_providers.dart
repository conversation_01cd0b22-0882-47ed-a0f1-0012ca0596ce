import 'package:budapp/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'navigation_providers.g.dart';

/// Navigation destination data model
@immutable
class NavigationDestinationData {
  const NavigationDestinationData({
    required this.route,
    required this.label,
    required this.icon,
    required this.selectedIcon,
    this.badge,
  });

  final String route;
  final String label;
  final IconData icon;
  final IconData selectedIcon;
  final String? badge;
}

/// Bottom navigation destinations
const List<NavigationDestinationData> bottomNavigationDestinations = [
  NavigationDestinationData(
    route: AppRoutes.home,
    label: 'Home',
    icon: Icons.home_outlined,
    selectedIcon: Icons.home,
  ),
  NavigationDestinationData(
    route: '/transactions',
    label: 'Transactions',
    icon: Icons.receipt_long_outlined,
    selectedIcon: Icons.receipt_long,
  ),
  NavigationDestinationData(
    route: AppRoutes.accounts,
    label: 'Wallet',
    icon: Icons.account_balance_wallet_outlined,
    selectedIcon: Icons.account_balance_wallet,
  ),
  NavigationDestinationData(
    route: AppRoutes.categories,
    label: 'Categories',
    icon: Icons.category_outlined,
    selectedIcon: Icons.category,
  ),
  NavigationDestinationData(
    route: '/profile',
    label: 'Profile',
    icon: Icons.person_outlined,
    selectedIcon: Icons.person,
  ),
];

/// Provider for current navigation index
@riverpod
class NavigationIndex extends _$NavigationIndex {
  @override
  int build() => 0;

  void setIndex(int index) {
    if (index >= 0 && index < bottomNavigationDestinations.length) {
      state = index;
    }
  }

  void setIndexFromRoute(String route) {
    for (var i = 0; i < bottomNavigationDestinations.length; i++) {
      final destination = bottomNavigationDestinations[i];
      if (route.startsWith(destination.route)) {
        state = i;
      }
    }
  }
}

/// Provider for current route path
@riverpod
class CurrentRoute extends _$CurrentRoute {
  @override
  String build() => AppRoutes.home;

  /// Get the current route
  String get route => state;

  /// Set the current route
  set route(String route) {
    state = route;
  }
}

/// Provider for FAB action route based on current route
@riverpod
String? fabActionRoute(Ref ref) {
  final route = ref.watch(currentRouteProvider);

  return switch (route) {
    final String r when r.startsWith('/dashboard') || r == AppRoutes.home =>
      '/transactions/create',
    final String r when r.startsWith('/transactions') => '/transactions/create',
    final String r when r.startsWith(AppRoutes.accounts) =>
      AppRoutes.accountCreate,
    final String r when r.startsWith(AppRoutes.categories) =>
      '/categories/create',
    final String r when r.startsWith(AppRoutes.tags) => AppRoutes.tagCreate,
    final String r when r.startsWith('/profile') => null,
    _ => null,
  };
}

/// Provider for FAB icon based on current route
@riverpod
IconData fabIcon(Ref ref) {
  final route = ref.watch(currentRouteProvider);

  return switch (route) {
    final String r when r.startsWith('/dashboard') || r == AppRoutes.home =>
      Icons.add,
    final String r when r.startsWith('/transactions') => Icons.receipt_long,
    final String r when r.startsWith(AppRoutes.accounts) =>
      Icons.account_balance_wallet,
    final String r when r.startsWith(AppRoutes.categories) => Icons.category,
    final String r when r.startsWith(AppRoutes.tags) => Icons.local_offer,
    final String r when r.startsWith('/profile') => Icons.add,
    _ => Icons.add,
  };
}

/// Provider for FAB tooltip based on current route
@riverpod
String fabTooltip(Ref ref) {
  final route = ref.watch(currentRouteProvider);

  return switch (route) {
    final String r when r.startsWith('/dashboard') || r == AppRoutes.home =>
      'Add Transaction',
    final String r when r.startsWith('/transactions') => 'Add Transaction',
    final String r when r.startsWith(AppRoutes.accounts) => 'Add Account',
    final String r when r.startsWith(AppRoutes.categories) => 'Add Category',
    final String r when r.startsWith(AppRoutes.tags) => 'Add Tag',
    final String r when r.startsWith('/profile') => 'Add',
    _ => 'Add',
  };
}

/// Provider to check if current route is implemented
@riverpod
bool isRouteImplemented(Ref ref) {
  final route = ref.watch(currentRouteProvider);

  // Currently accounts, categories, transactions, tags, and profile features are implemented
  return route.startsWith(AppRoutes.accounts) ||
      route.startsWith(AppRoutes.categories) ||
      route.startsWith('/transactions') ||
      route.startsWith('/dashboard') ||
      route.startsWith('/profile') ||
      route.startsWith(AppRoutes.tags) ||
      route == AppRoutes.home;
}
