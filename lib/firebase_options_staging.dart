// Firebase configuration for Staging environment
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Firebase options for Staging environment
class FirebaseOptionsStaging {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'FirebaseOptionsStaging have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'FirebaseOptionsStaging have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'FirebaseOptionsStaging have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'FirebaseOptionsStaging have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'FirebaseOptionsStaging are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDV9MFofuM8uzOH-_RKxhB2T4621XsE1M0',
    appId: '1:552643559728:android:95892848efbb65967c48f5',
    messagingSenderId: '552643559728',
    projectId: 'budapp-staging-1',
    storageBucket: 'budapp-staging-1.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDV9MFofuM8uzOH-_RKxhB2T4621XsE1M0',
    appId: '1:552643559728:ios:95892848efbb65967c48f5',
    messagingSenderId: '552643559728',
    projectId: 'budapp-staging-1',
    storageBucket: 'budapp-staging-1.firebasestorage.app',
    iosBundleId: 'com.digitau.budapp.staging',
  );
}
