import 'package:budapp/features/common/models/time_period.dart';
import 'package:budapp/features/common/services/time_period_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'time_period_providers.g.dart';

/// Notifier for managing the global time period state
@riverpod
class TimePeriodNotifier extends _$TimePeriodNotifier {
  @override
  TimePeriod build() {
    // Return current month immediately - no async operations in build
    // Storage loading is handled by initialize() method called from app startup
    return TimePeriodService.getCurrentMonth();
  }

  /// Initialize the provider by loading the selected period from SharedPreferences
  /// This should be called during app startup to pre-warm the provider
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storedPeriod = prefs.getString(TimePeriodService.storageKey);
      final period = TimePeriodService.periodFromStorageString(storedPeriod);

      if (period != null) {
        state = period;
      }
    } on Exception {
      // If loading fails, keep the default current month
      // Log error in production
    }
  }

  /// Save the selected period to SharedPreferences
  Future<void> _saveToStorage(TimePeriod period) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storageString = TimePeriodService.periodToStorageString(period);
      await prefs.setString(TimePeriodService.storageKey, storageString);
    } on Exception {
      // Log error in production, but don't throw
    }
  }

  /// Select a new time period
  Future<void> selectPeriod(TimePeriod period) async {
    if (!TimePeriodService.isPeriodSelectable(period)) {
      throw ArgumentError('Cannot select future periods');
    }

    state = period;
    await _saveToStorage(period);
  }

  /// Navigate to the previous month
  Future<void> navigateToPrevious() async {
    if (state.type != PeriodType.monthly) {
      throw UnsupportedError('Navigation only supported for monthly periods');
    }

    final previousPeriod = TimePeriodService.getPreviousMonth(state);
    if (TimePeriodService.isPeriodSelectable(previousPeriod)) {
      await selectPeriod(previousPeriod);
    }
  }

  /// Navigate to the next month (if not future)
  Future<void> navigateToNext() async {
    if (state.type != PeriodType.monthly) {
      throw UnsupportedError('Navigation only supported for monthly periods');
    }

    final nextPeriod = TimePeriodService.getNextMonth(state);
    if (TimePeriodService.isPeriodSelectable(nextPeriod)) {
      await selectPeriod(nextPeriod);
    }
  }

  /// Navigate to the next month for budget viewing (allows future periods)
  /// This is used specifically for budget template viewing
  Future<void> navigateToNextForBudgets() async {
    if (state.type != PeriodType.monthly) {
      throw UnsupportedError('Navigation only supported for monthly periods');
    }

    final nextPeriod = TimePeriodService.getNextMonth(state);
    // Allow viewing future periods for budget templates (up to 12 months ahead)
    final now = DateTime.now();
    final maxFuture = DateTime(now.year + 1, now.month, 1);

    if (nextPeriod.startDate.isBefore(maxFuture) ||
        nextPeriod.startDate.isAtSameMomentAs(maxFuture)) {
      state = nextPeriod;
      // Don't save future periods to storage
      if (TimePeriodService.isPeriodSelectable(nextPeriod)) {
        await _saveToStorage(nextPeriod);
      }
    }
  }

  /// Reset to current month
  Future<void> resetToCurrentMonth() async {
    final currentMonth = TimePeriodService.getCurrentMonth();
    await selectPeriod(currentMonth);
  }

  /// Check if navigation to previous period is possible
  bool get canNavigateToPrevious {
    if (state.type != PeriodType.monthly) return false;

    try {
      final previousPeriod = TimePeriodService.getPreviousMonth(state);
      return TimePeriodService.isPeriodSelectable(previousPeriod);
    } on Exception {
      return false;
    }
  }

  /// Check if navigation to next period is possible
  bool get canNavigateToNext {
    if (state.type != PeriodType.monthly) return false;

    try {
      final nextPeriod = TimePeriodService.getNextMonth(state);
      return TimePeriodService.isPeriodSelectable(nextPeriod);
    } on Exception {
      return false;
    }
  }
}

/// Provider for the list of available periods for selection
@riverpod
List<TimePeriod> availablePeriods(Ref ref) {
  return TimePeriodService.getAvailablePeriods();
}

/// Provider for checking if the current period is the current month
@riverpod
bool isCurrentMonth(Ref ref) {
  final selectedPeriod = ref.watch(timePeriodNotifierProvider);
  final currentMonth = TimePeriodService.getCurrentMonth();

  return selectedPeriod.year == currentMonth.year &&
      selectedPeriod.month == currentMonth.month;
}

/// Provider for checking if the current period is a future period
/// Used for budget template mode indication
@riverpod
bool isFuturePeriod(Ref ref) {
  final selectedPeriod = ref.watch(timePeriodNotifierProvider);
  final now = DateTime.now();
  final currentMonth = DateTime(now.year, now.month, 1);
  final selectedMonth = DateTime(
    selectedPeriod.year,
    selectedPeriod.month ?? 1,
    1,
  );

  return selectedMonth.isAfter(currentMonth);
}

/// Provider for getting the display text for the current period
@riverpod
String currentPeriodDisplayText(Ref ref) {
  final selectedPeriod = ref.watch(timePeriodNotifierProvider);
  return selectedPeriod.displayName;
}

/// Provider for getting the date range text for the current period
@riverpod
String currentPeriodDateRangeText(Ref ref) {
  final selectedPeriod = ref.watch(timePeriodNotifierProvider);
  return selectedPeriod.dateRangeText;
}

/// Provider for checking if a specific date falls within the selected period
@riverpod
bool isDateInSelectedPeriod(Ref ref, DateTime date) {
  final selectedPeriod = ref.watch(timePeriodNotifierProvider);
  return selectedPeriod.containsDate(date);
}

/// Provider for getting the start and end dates of the selected period
@riverpod
({DateTime startDate, DateTime endDate}) selectedPeriodDateRange(Ref ref) {
  final selectedPeriod = ref.watch(timePeriodNotifierProvider);
  return (startDate: selectedPeriod.startDate, endDate: selectedPeriod.endDate);
}
