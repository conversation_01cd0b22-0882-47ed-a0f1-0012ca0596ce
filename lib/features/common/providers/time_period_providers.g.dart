// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'time_period_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$availablePeriodsHash() => r'22aeef288377141a8a5111d79b74cc0f26b5f7b7';

/// Provider for the list of available periods for selection
///
/// Copied from [availablePeriods].
@ProviderFor(availablePeriods)
final availablePeriodsProvider = AutoDisposeProvider<List<TimePeriod>>.internal(
  availablePeriods,
  name: r'availablePeriodsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$availablePeriodsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AvailablePeriodsRef = AutoDisposeProviderRef<List<TimePeriod>>;
String _$isCurrentMonthHash() => r'564d9bf3122a5c30d74a9924449498674374b393';

/// Provider for checking if the current period is the current month
///
/// Copied from [isCurrentMonth].
@ProviderFor(isCurrentMonth)
final isCurrentMonthProvider = AutoDisposeProvider<bool>.internal(
  isCurrentMonth,
  name: r'isCurrentMonthProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isCurrentMonthHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsCurrentMonthRef = AutoDisposeProviderRef<bool>;
String _$isFuturePeriodHash() => r'6494774eb067d89bb5eb16ecbec3742c22516c3e';

/// Provider for checking if the current period is a future period
/// Used for budget template mode indication
///
/// Copied from [isFuturePeriod].
@ProviderFor(isFuturePeriod)
final isFuturePeriodProvider = AutoDisposeProvider<bool>.internal(
  isFuturePeriod,
  name: r'isFuturePeriodProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isFuturePeriodHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsFuturePeriodRef = AutoDisposeProviderRef<bool>;
String _$currentPeriodDisplayTextHash() =>
    r'1c0c5d62ca7ddcb4cea13cf3cd419743433f1404';

/// Provider for getting the display text for the current period
///
/// Copied from [currentPeriodDisplayText].
@ProviderFor(currentPeriodDisplayText)
final currentPeriodDisplayTextProvider = AutoDisposeProvider<String>.internal(
  currentPeriodDisplayText,
  name: r'currentPeriodDisplayTextProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentPeriodDisplayTextHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentPeriodDisplayTextRef = AutoDisposeProviderRef<String>;
String _$currentPeriodDateRangeTextHash() =>
    r'cf1d5b8884e4e223cbd2cedb968f991d1d103de3';

/// Provider for getting the date range text for the current period
///
/// Copied from [currentPeriodDateRangeText].
@ProviderFor(currentPeriodDateRangeText)
final currentPeriodDateRangeTextProvider = AutoDisposeProvider<String>.internal(
  currentPeriodDateRangeText,
  name: r'currentPeriodDateRangeTextProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentPeriodDateRangeTextHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentPeriodDateRangeTextRef = AutoDisposeProviderRef<String>;
String _$isDateInSelectedPeriodHash() =>
    r'9e65ff9eda4ae977e0fac64ed67ba3f347cb3b07';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for checking if a specific date falls within the selected period
///
/// Copied from [isDateInSelectedPeriod].
@ProviderFor(isDateInSelectedPeriod)
const isDateInSelectedPeriodProvider = IsDateInSelectedPeriodFamily();

/// Provider for checking if a specific date falls within the selected period
///
/// Copied from [isDateInSelectedPeriod].
class IsDateInSelectedPeriodFamily extends Family<bool> {
  /// Provider for checking if a specific date falls within the selected period
  ///
  /// Copied from [isDateInSelectedPeriod].
  const IsDateInSelectedPeriodFamily();

  /// Provider for checking if a specific date falls within the selected period
  ///
  /// Copied from [isDateInSelectedPeriod].
  IsDateInSelectedPeriodProvider call(DateTime date) {
    return IsDateInSelectedPeriodProvider(date);
  }

  @override
  IsDateInSelectedPeriodProvider getProviderOverride(
    covariant IsDateInSelectedPeriodProvider provider,
  ) {
    return call(provider.date);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'isDateInSelectedPeriodProvider';
}

/// Provider for checking if a specific date falls within the selected period
///
/// Copied from [isDateInSelectedPeriod].
class IsDateInSelectedPeriodProvider extends AutoDisposeProvider<bool> {
  /// Provider for checking if a specific date falls within the selected period
  ///
  /// Copied from [isDateInSelectedPeriod].
  IsDateInSelectedPeriodProvider(DateTime date)
    : this._internal(
        (ref) => isDateInSelectedPeriod(ref as IsDateInSelectedPeriodRef, date),
        from: isDateInSelectedPeriodProvider,
        name: r'isDateInSelectedPeriodProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$isDateInSelectedPeriodHash,
        dependencies: IsDateInSelectedPeriodFamily._dependencies,
        allTransitiveDependencies:
            IsDateInSelectedPeriodFamily._allTransitiveDependencies,
        date: date,
      );

  IsDateInSelectedPeriodProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.date,
  }) : super.internal();

  final DateTime date;

  @override
  Override overrideWith(
    bool Function(IsDateInSelectedPeriodRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: IsDateInSelectedPeriodProvider._internal(
        (ref) => create(ref as IsDateInSelectedPeriodRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        date: date,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<bool> createElement() {
    return _IsDateInSelectedPeriodProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is IsDateInSelectedPeriodProvider && other.date == date;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, date.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin IsDateInSelectedPeriodRef on AutoDisposeProviderRef<bool> {
  /// The parameter `date` of this provider.
  DateTime get date;
}

class _IsDateInSelectedPeriodProviderElement
    extends AutoDisposeProviderElement<bool>
    with IsDateInSelectedPeriodRef {
  _IsDateInSelectedPeriodProviderElement(super.provider);

  @override
  DateTime get date => (origin as IsDateInSelectedPeriodProvider).date;
}

String _$selectedPeriodDateRangeHash() =>
    r'e26aa3dc200ecfd804a544bfbe50b68d860a3e7a';

/// Provider for getting the start and end dates of the selected period
///
/// Copied from [selectedPeriodDateRange].
@ProviderFor(selectedPeriodDateRange)
final selectedPeriodDateRangeProvider =
    AutoDisposeProvider<({DateTime startDate, DateTime endDate})>.internal(
      selectedPeriodDateRange,
      name: r'selectedPeriodDateRangeProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$selectedPeriodDateRangeHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SelectedPeriodDateRangeRef =
    AutoDisposeProviderRef<({DateTime startDate, DateTime endDate})>;
String _$timePeriodNotifierHash() =>
    r'b29170e90a186a1a33e08b2431d4a5aa879a0612';

/// Notifier for managing the global time period state
///
/// Copied from [TimePeriodNotifier].
@ProviderFor(TimePeriodNotifier)
final timePeriodNotifierProvider =
    AutoDisposeNotifierProvider<TimePeriodNotifier, TimePeriod>.internal(
      TimePeriodNotifier.new,
      name: r'timePeriodNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$timePeriodNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TimePeriodNotifier = AutoDisposeNotifier<TimePeriod>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
