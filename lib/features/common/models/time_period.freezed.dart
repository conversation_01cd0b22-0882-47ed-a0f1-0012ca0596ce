// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'time_period.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TimePeriod {

/// Type of the period (monthly, weekly, etc.)
 PeriodType get type;/// Start date of the period (inclusive)
 DateTime get startDate;/// End date of the period (inclusive)
 DateTime get endDate;/// Display name for the period (e.g., "December 2024")
 String get displayName;/// Date range text for display (e.g., "01 Dec - 31 Dec")
 String get dateRangeText;/// Year of the period for easy filtering
 int get year;/// Month of the period (1-12) for monthly periods, null for others
 int? get month;/// Whether this period is in the past
 bool get isPast;/// Whether this period is the current period
 bool get isCurrent;
/// Create a copy of TimePeriod
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TimePeriodCopyWith<TimePeriod> get copyWith => _$TimePeriodCopyWithImpl<TimePeriod>(this as TimePeriod, _$identity);

  /// Serializes this TimePeriod to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TimePeriod&&(identical(other.type, type) || other.type == type)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.dateRangeText, dateRangeText) || other.dateRangeText == dateRangeText)&&(identical(other.year, year) || other.year == year)&&(identical(other.month, month) || other.month == month)&&(identical(other.isPast, isPast) || other.isPast == isPast)&&(identical(other.isCurrent, isCurrent) || other.isCurrent == isCurrent));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,startDate,endDate,displayName,dateRangeText,year,month,isPast,isCurrent);

@override
String toString() {
  return 'TimePeriod(type: $type, startDate: $startDate, endDate: $endDate, displayName: $displayName, dateRangeText: $dateRangeText, year: $year, month: $month, isPast: $isPast, isCurrent: $isCurrent)';
}


}

/// @nodoc
abstract mixin class $TimePeriodCopyWith<$Res>  {
  factory $TimePeriodCopyWith(TimePeriod value, $Res Function(TimePeriod) _then) = _$TimePeriodCopyWithImpl;
@useResult
$Res call({
 PeriodType type, DateTime startDate, DateTime endDate, String displayName, String dateRangeText, int year, int? month, bool isPast, bool isCurrent
});




}
/// @nodoc
class _$TimePeriodCopyWithImpl<$Res>
    implements $TimePeriodCopyWith<$Res> {
  _$TimePeriodCopyWithImpl(this._self, this._then);

  final TimePeriod _self;
  final $Res Function(TimePeriod) _then;

/// Create a copy of TimePeriod
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? type = null,Object? startDate = null,Object? endDate = null,Object? displayName = null,Object? dateRangeText = null,Object? year = null,Object? month = freezed,Object? isPast = null,Object? isCurrent = null,}) {
  return _then(_self.copyWith(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as PeriodType,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,displayName: null == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String,dateRangeText: null == dateRangeText ? _self.dateRangeText : dateRangeText // ignore: cast_nullable_to_non_nullable
as String,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,month: freezed == month ? _self.month : month // ignore: cast_nullable_to_non_nullable
as int?,isPast: null == isPast ? _self.isPast : isPast // ignore: cast_nullable_to_non_nullable
as bool,isCurrent: null == isCurrent ? _self.isCurrent : isCurrent // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [TimePeriod].
extension TimePeriodPatterns on TimePeriod {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TimePeriod value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TimePeriod() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TimePeriod value)  $default,){
final _that = this;
switch (_that) {
case _TimePeriod():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TimePeriod value)?  $default,){
final _that = this;
switch (_that) {
case _TimePeriod() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( PeriodType type,  DateTime startDate,  DateTime endDate,  String displayName,  String dateRangeText,  int year,  int? month,  bool isPast,  bool isCurrent)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TimePeriod() when $default != null:
return $default(_that.type,_that.startDate,_that.endDate,_that.displayName,_that.dateRangeText,_that.year,_that.month,_that.isPast,_that.isCurrent);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( PeriodType type,  DateTime startDate,  DateTime endDate,  String displayName,  String dateRangeText,  int year,  int? month,  bool isPast,  bool isCurrent)  $default,) {final _that = this;
switch (_that) {
case _TimePeriod():
return $default(_that.type,_that.startDate,_that.endDate,_that.displayName,_that.dateRangeText,_that.year,_that.month,_that.isPast,_that.isCurrent);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( PeriodType type,  DateTime startDate,  DateTime endDate,  String displayName,  String dateRangeText,  int year,  int? month,  bool isPast,  bool isCurrent)?  $default,) {final _that = this;
switch (_that) {
case _TimePeriod() when $default != null:
return $default(_that.type,_that.startDate,_that.endDate,_that.displayName,_that.dateRangeText,_that.year,_that.month,_that.isPast,_that.isCurrent);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TimePeriod implements TimePeriod {
  const _TimePeriod({required this.type, required this.startDate, required this.endDate, required this.displayName, required this.dateRangeText, required this.year, this.month, this.isPast = false, this.isCurrent = false});
  factory _TimePeriod.fromJson(Map<String, dynamic> json) => _$TimePeriodFromJson(json);

/// Type of the period (monthly, weekly, etc.)
@override final  PeriodType type;
/// Start date of the period (inclusive)
@override final  DateTime startDate;
/// End date of the period (inclusive)
@override final  DateTime endDate;
/// Display name for the period (e.g., "December 2024")
@override final  String displayName;
/// Date range text for display (e.g., "01 Dec - 31 Dec")
@override final  String dateRangeText;
/// Year of the period for easy filtering
@override final  int year;
/// Month of the period (1-12) for monthly periods, null for others
@override final  int? month;
/// Whether this period is in the past
@override@JsonKey() final  bool isPast;
/// Whether this period is the current period
@override@JsonKey() final  bool isCurrent;

/// Create a copy of TimePeriod
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TimePeriodCopyWith<_TimePeriod> get copyWith => __$TimePeriodCopyWithImpl<_TimePeriod>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TimePeriodToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TimePeriod&&(identical(other.type, type) || other.type == type)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.dateRangeText, dateRangeText) || other.dateRangeText == dateRangeText)&&(identical(other.year, year) || other.year == year)&&(identical(other.month, month) || other.month == month)&&(identical(other.isPast, isPast) || other.isPast == isPast)&&(identical(other.isCurrent, isCurrent) || other.isCurrent == isCurrent));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,startDate,endDate,displayName,dateRangeText,year,month,isPast,isCurrent);

@override
String toString() {
  return 'TimePeriod(type: $type, startDate: $startDate, endDate: $endDate, displayName: $displayName, dateRangeText: $dateRangeText, year: $year, month: $month, isPast: $isPast, isCurrent: $isCurrent)';
}


}

/// @nodoc
abstract mixin class _$TimePeriodCopyWith<$Res> implements $TimePeriodCopyWith<$Res> {
  factory _$TimePeriodCopyWith(_TimePeriod value, $Res Function(_TimePeriod) _then) = __$TimePeriodCopyWithImpl;
@override @useResult
$Res call({
 PeriodType type, DateTime startDate, DateTime endDate, String displayName, String dateRangeText, int year, int? month, bool isPast, bool isCurrent
});




}
/// @nodoc
class __$TimePeriodCopyWithImpl<$Res>
    implements _$TimePeriodCopyWith<$Res> {
  __$TimePeriodCopyWithImpl(this._self, this._then);

  final _TimePeriod _self;
  final $Res Function(_TimePeriod) _then;

/// Create a copy of TimePeriod
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? type = null,Object? startDate = null,Object? endDate = null,Object? displayName = null,Object? dateRangeText = null,Object? year = null,Object? month = freezed,Object? isPast = null,Object? isCurrent = null,}) {
  return _then(_TimePeriod(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as PeriodType,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,displayName: null == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String,dateRangeText: null == dateRangeText ? _self.dateRangeText : dateRangeText // ignore: cast_nullable_to_non_nullable
as String,year: null == year ? _self.year : year // ignore: cast_nullable_to_non_nullable
as int,month: freezed == month ? _self.month : month // ignore: cast_nullable_to_non_nullable
as int?,isPast: null == isPast ? _self.isPast : isPast // ignore: cast_nullable_to_non_nullable
as bool,isCurrent: null == isCurrent ? _self.isCurrent : isCurrent // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
