import 'package:freezed_annotation/freezed_annotation.dart';

part 'time_period.freezed.dart';
part 'time_period.g.dart';

/// Enum representing different types of time periods
enum PeriodType { monthly, weekly, quarterly, yearly, custom }

/// Extension to provide display names for period types
extension PeriodTypeExtension on PeriodType {
  String get displayName {
    switch (this) {
      case PeriodType.monthly:
        return 'Monthly';
      case PeriodType.weekly:
        return 'Weekly';
      case PeriodType.quarterly:
        return 'Quarterly';
      case PeriodType.yearly:
        return 'Yearly';
      case PeriodType.custom:
        return 'Custom';
    }
  }
}

/// Time period data model representing a specific date range with display information
@freezed
sealed class TimePeriod with _$TimePeriod {
  const factory TimePeriod({
    /// Type of the period (monthly, weekly, etc.)
    required PeriodType type,

    /// Start date of the period (inclusive)
    required DateTime startDate,

    /// End date of the period (inclusive)
    required DateTime endDate,

    /// Display name for the period (e.g., "December 2024")
    required String displayName,

    /// Date range text for display (e.g., "01 Dec - 31 Dec")
    required String dateRangeText,

    /// Year of the period for easy filtering
    required int year,

    /// Month of the period (1-12) for monthly periods, null for others
    int? month,

    /// Whether this period is in the past
    @Default(false) bool isPast,

    /// Whether this period is the current period
    @Default(false) bool isCurrent,
  }) = _TimePeriod;

  factory TimePeriod.fromJson(Map<String, dynamic> json) =>
      _$TimePeriodFromJson(json);
}

/// Extension to provide additional functionality for TimePeriod
extension TimePeriodExtension on TimePeriod {
  /// Check if this period contains the given date
  bool containsDate(DateTime date) {
    final dateOnly = DateTime(date.year, date.month, date.day);
    final startOnly = DateTime(startDate.year, startDate.month, startDate.day);
    final endOnly = DateTime(endDate.year, endDate.month, endDate.day);

    return (dateOnly.isAtSameMomentAs(startOnly) ||
            dateOnly.isAfter(startOnly)) &&
        (dateOnly.isAtSameMomentAs(endOnly) || dateOnly.isBefore(endOnly));
  }

  /// Get the duration of this period in days
  int get durationInDays {
    return endDate.difference(startDate).inDays + 1;
  }

  /// Check if this period is valid (start date is before or equal to end date)
  bool get isValid {
    return startDate.isBefore(endDate) || startDate.isAtSameMomentAs(endDate);
  }

  /// Get a unique identifier for this period
  String get identifier {
    switch (type) {
      case PeriodType.monthly:
        return '${year}_${month?.toString().padLeft(2, '0')}';
      case PeriodType.yearly:
        return year.toString();
      case PeriodType.weekly:
      case PeriodType.quarterly:
      case PeriodType.custom:
        return '${startDate.toIso8601String()}_${endDate.toIso8601String()}';
    }
  }
}
