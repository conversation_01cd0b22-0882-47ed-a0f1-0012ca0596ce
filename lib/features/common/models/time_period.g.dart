// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'time_period.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TimePeriod _$TimePeriodFromJson(Map<String, dynamic> json) => _TimePeriod(
  type: $enumDecode(_$PeriodTypeEnumMap, json['type']),
  startDate: DateTime.parse(json['startDate'] as String),
  endDate: DateTime.parse(json['endDate'] as String),
  displayName: json['displayName'] as String,
  dateRangeText: json['dateRangeText'] as String,
  year: (json['year'] as num).toInt(),
  month: (json['month'] as num?)?.toInt(),
  isPast: json['isPast'] as bool? ?? false,
  isCurrent: json['isCurrent'] as bool? ?? false,
);

Map<String, dynamic> _$TimePeriodToJson(_TimePeriod instance) =>
    <String, dynamic>{
      'type': _$PeriodTypeEnumMap[instance.type]!,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'displayName': instance.displayName,
      'dateRangeText': instance.dateRangeText,
      'year': instance.year,
      'month': instance.month,
      'isPast': instance.isPast,
      'isCurrent': instance.isCurrent,
    };

const _$PeriodTypeEnumMap = {
  PeriodType.monthly: 'monthly',
  PeriodType.weekly: 'weekly',
  PeriodType.quarterly: 'quarterly',
  PeriodType.yearly: 'yearly',
  PeriodType.custom: 'custom',
};
