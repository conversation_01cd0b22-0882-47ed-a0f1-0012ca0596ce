import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/common/models/time_period.dart';
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/features/common/services/time_period_service.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Modal bottom sheet for selecting time periods with year/month grid layout
class TimePeriodModal extends ConsumerStatefulWidget {
  const TimePeriodModal({super.key});

  @override
  ConsumerState<TimePeriodModal> createState() => _TimePeriodModalState();
}

class _TimePeriodModalState extends ConsumerState<TimePeriodModal> {
  late int _selectedYear;
  late int _selectedMonth;
  TimePeriod? _selectedPeriod;

  @override
  void initState() {
    super.initState();
    final currentPeriod = ref.read(timePeriodNotifierProvider);
    _selectedYear = currentPeriod.year;
    _selectedMonth = currentPeriod.month ?? DateTime.now().month;
    _selectedPeriod = currentPeriod;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final now = DateTime.now();
    final currentMonth = DateTime(now.year, now.month);

    return DecoratedBox(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with gradient background
          Container(
            width: double.infinity,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [AppColors.primary, AppColors.primaryDark],
              ),
              borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
            ),
            child: SafeArea(
              bottom: false,
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Current period display
                    Text(
                      _selectedPeriod?.displayName ?? 'Select Period',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        color: AppColors.onPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Year selector with navigation
                    Row(
                      children: [
                        Text(
                          _selectedYear.toString(),
                          style: theme.textTheme.displaySmall?.copyWith(
                            color: AppColors.onPrimary,
                            fontWeight: FontWeight.w300,
                          ),
                        ),
                        const Spacer(),
                        // Year navigation buttons
                        IconButton(
                          onPressed: _canNavigateToYear(_selectedYear - 1)
                              ? () => _changeYear(_selectedYear - 1)
                              : null,
                          icon: const Icon(Icons.keyboard_arrow_up),
                          style: IconButton.styleFrom(
                            foregroundColor: AppColors.onPrimary,
                          ),
                        ),
                        IconButton(
                          onPressed: _canNavigateToYear(_selectedYear + 1)
                              ? () => _changeYear(_selectedYear + 1)
                              : null,
                          icon: const Icon(Icons.keyboard_arrow_down),
                          style: IconButton.styleFrom(
                            foregroundColor: AppColors.onPrimary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Month grid
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // Month grid (3x4)
                for (int row = 0; row < 4; row++)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Row(
                      children: [
                        for (int col = 0; col < 3; col++)
                          Expanded(
                            child: _buildMonthButton(
                              context,
                              row * 3 + col + 1,
                              currentMonth,
                            ),
                          ),
                      ],
                    ),
                  ),

                const SizedBox(height: 24),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          side: BorderSide(color: theme.colorScheme.outline),
                        ),
                        child: Text(
                          l10n.cancel,
                          style: TextStyle(
                            color: theme.colorScheme.onSurface,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: FilledButton(
                        onPressed: _selectedPeriod != null
                            ? () => Navigator.of(context).pop(_selectedPeriod)
                            : null,
                        style: FilledButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: const Text(
                          'OK',
                          style: TextStyle(
                            color: AppColors.onPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Bottom padding for safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  /// Check if navigation to a specific year is allowed
  bool _canNavigateToYear(int year) {
    final now = DateTime.now();
    // Allow future years up to 5 years ahead
    return year <= now.year + 5;
  }

  /// Change the selected year
  void _changeYear(int newYear) {
    setState(() {
      _selectedYear = newYear;
      // Update selected period if the current month exists in the new year
      final newPeriod = TimePeriodService.getMonthPeriod(
        newYear,
        _selectedMonth,
      );
      if (TimePeriodService.isPeriodSelectable(newPeriod)) {
        _selectedPeriod = newPeriod;
      }
    });
  }

  /// Build a month button for the grid
  Widget _buildMonthButton(
    BuildContext context,
    int month,
    DateTime currentMonth,
  ) {
    final theme = Theme.of(context);
    final monthPeriod = TimePeriodService.getMonthPeriod(_selectedYear, month);
    final isSelectable = TimePeriodService.isPeriodSelectable(monthPeriod);
    final isSelected =
        _selectedMonth == month && _selectedYear == _selectedPeriod?.year;
    final isCurrent =
        monthPeriod.year == currentMonth.year &&
        monthPeriod.month == currentMonth.month;

    // Month abbreviations
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isSelectable ? () => _selectMonth(month) : null,
          borderRadius: BorderRadius.circular(32),
          child: Container(
            height: 64,
            decoration: BoxDecoration(
              color: isSelected ? AppColors.primary : Colors.transparent,
              borderRadius: BorderRadius.circular(32),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    monthNames[month - 1],
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: isSelected
                          ? AppColors.onPrimary
                          : isSelectable
                          ? theme.colorScheme.onSurface
                          : theme.colorScheme.onSurface.withValues(alpha: 0.4),
                      fontWeight: isSelected
                          ? FontWeight.w600
                          : FontWeight.w500,
                    ),
                  ),
                  if (isCurrent && !isSelected)
                    Container(
                      margin: const EdgeInsets.only(top: 2),
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Select a month and update the period
  void _selectMonth(int month) {
    setState(() {
      _selectedMonth = month;
      _selectedPeriod = TimePeriodService.getMonthPeriod(_selectedYear, month);
    });
  }
}
