import 'package:budapp/features/common/models/time_period.dart';
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/features/common/services/time_period_service.dart';
import 'package:budapp/features/common/widgets/time_period_modal.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// A widget that displays the current time period and allows selection of different periods
class TimePeriodSelector extends ConsumerWidget {
  const TimePeriodSelector({
    super.key,
    this.showNavigation = false,
    this.showDateRange = true,
    this.periodNameStyle,
    this.dateRangeStyle,
    this.onPeriodChanged,
    this.compact = false,
    this.allowFutureNavigation = false,
  });

  /// Whether to show navigation arrows for previous/next period
  final bool showNavigation;

  /// Whether to show the date range text below the period name
  final bool showDateRange;

  /// Custom text style for the period name
  final TextStyle? periodNameStyle;

  /// Custom text style for the date range
  final TextStyle? dateRangeStyle;

  /// Callback when period is changed
  final VoidCallback? onPeriodChanged;

  /// Whether the selector is in compact mode (single line)
  final bool compact;

  /// Whether to allow navigation to future periods (for budget templates)
  final bool allowFutureNavigation;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final selectedPeriod = ref.watch(timePeriodNotifierProvider);
    final notifier = ref.read(timePeriodNotifierProvider.notifier);

    final defaultPeriodStyle =
        periodNameStyle ??
        theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
          color: theme.colorScheme.onSurface,
        );

    final defaultDateRangeStyle =
        dateRangeStyle ??
        theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.onSurfaceVariant,
        );

    return Semantics(
      label: l10n.periodSelector,
      button: true,
      child: InkWell(
        onTap: () => _showPeriodModal(context, ref),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (showNavigation) ...[
                _NavigationButton(
                  icon: Icons.chevron_left,
                  onPressed: notifier.canNavigateToPrevious
                      ? () => _navigateToPrevious(ref)
                      : null,
                  tooltip: l10n.previousPeriod,
                ),
                const SizedBox(width: 8),
              ],

              Flexible(
                child: compact
                    ? _buildCompactContent(
                        selectedPeriod,
                        defaultPeriodStyle,
                        defaultDateRangeStyle,
                      )
                    : _buildExpandedContent(
                        selectedPeriod,
                        defaultPeriodStyle,
                        defaultDateRangeStyle,
                      ),
              ),

              const SizedBox(width: 8),
              Icon(
                Icons.expand_more,
                size: 20,
                color: theme.colorScheme.onSurfaceVariant,
              ),

              if (showNavigation) ...[
                const SizedBox(width: 8),
                _NavigationButton(
                  icon: Icons.chevron_right,
                  onPressed: _canNavigateToNext(ref)
                      ? () => _navigateToNext(ref)
                      : null,
                  tooltip: l10n.nextPeriod,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCompactContent(
    TimePeriod period,
    TextStyle? periodStyle,
    TextStyle? dateStyle,
  ) {
    return Text(
      '${period.displayName} • ${period.dateRangeText}',
      style: periodStyle,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildExpandedContent(
    TimePeriod period,
    TextStyle? periodStyle,
    TextStyle? dateStyle,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          period.displayName,
          style: periodStyle,
          overflow: TextOverflow.ellipsis,
        ),
        if (showDateRange) ...[
          const SizedBox(height: 2),
          Text(
            period.dateRangeText,
            style: dateStyle,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Future<void> _showPeriodModal(BuildContext context, WidgetRef ref) async {
    final selectedPeriod = await showModalBottomSheet<TimePeriod>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const TimePeriodModal(),
    );

    if (selectedPeriod != null) {
      await ref
          .read(timePeriodNotifierProvider.notifier)
          .selectPeriod(selectedPeriod);
      onPeriodChanged?.call();
    }
  }

  Future<void> _navigateToPrevious(WidgetRef ref) async {
    try {
      await ref.read(timePeriodNotifierProvider.notifier).navigateToPrevious();
      onPeriodChanged?.call();
    } on Exception {
      // Handle navigation error silently or show snackbar
    }
  }

  /// Check if navigation to next period is possible
  bool _canNavigateToNext(WidgetRef ref) {
    final notifier = ref.read(timePeriodNotifierProvider.notifier);

    if (allowFutureNavigation) {
      // For budget templates, allow navigation up to 12 months in the future
      final selectedPeriod = ref.read(timePeriodNotifierProvider);
      final now = DateTime.now();
      final maxFuture = DateTime(now.year + 1, now.month, 1);
      final nextPeriod = TimePeriodService.getNextMonth(selectedPeriod);

      return nextPeriod.startDate.isBefore(maxFuture) ||
          nextPeriod.startDate.isAtSameMomentAs(maxFuture);
    } else {
      // Standard navigation (past/current only)
      return notifier.canNavigateToNext;
    }
  }

  Future<void> _navigateToNext(WidgetRef ref) async {
    try {
      if (allowFutureNavigation) {
        await ref
            .read(timePeriodNotifierProvider.notifier)
            .navigateToNextForBudgets();
      } else {
        await ref.read(timePeriodNotifierProvider.notifier).navigateToNext();
      }
      onPeriodChanged?.call();
    } on Exception {
      // Handle navigation error silently or show snackbar
    }
  }
}

/// Navigation button widget for previous/next period navigation
class _NavigationButton extends StatelessWidget {
  const _NavigationButton({
    required this.icon,
    required this.onPressed,
    required this.tooltip,
  });
  final IconData icon;
  final VoidCallback? onPressed;
  final String tooltip;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: onPressed != null
                ? theme.colorScheme.surfaceContainerHighest
                : theme.colorScheme.surfaceContainerHighest.withValues(
                    alpha: 0.3,
                  ),
          ),
          child: Icon(
            icon,
            size: 18,
            color: onPressed != null
                ? theme.colorScheme.onSurface
                : theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
        ),
      ),
    );
  }
}

/// Compact version of the time period selector for use in app bars
class CompactTimePeriodSelector extends StatelessWidget {
  const CompactTimePeriodSelector({super.key, this.onPeriodChanged});
  final VoidCallback? onPeriodChanged;

  @override
  Widget build(BuildContext context) {
    return TimePeriodSelector(
      compact: true,
      showDateRange: false,
      showNavigation: false,
      onPeriodChanged: onPeriodChanged,
      periodNameStyle: Theme.of(
        context,
      ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
    );
  }
}
