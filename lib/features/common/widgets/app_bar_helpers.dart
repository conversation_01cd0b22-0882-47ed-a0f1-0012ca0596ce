import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/common/widgets/time_period_selector.dart';
import 'package:flutter/material.dart';

/// Helper utilities for creating consistent AppBar widgets across the application
///
/// This class provides standardized AppBar creation methods that ensure
/// consistent Material 3 design, proper TimePeriodSelector integration,
/// and unified styling across all screens using the traditional Scaffold + AppBar pattern.
// ignore: avoid_classes_with_only_static_members
class AppBarHelpers {
  /// Creates an AppBar with TimePeriodSelector integration for time-dependent screens
  ///
  /// This is used for screens that need time period filtering (Home, Transactions, Budgets).
  /// The TimePeriodSelector is positioned in the left side of the title area.
  ///
  /// Parameters:
  /// - [title]: The screen title text to display on the right side
  /// - [actions]: Optional action buttons for the right side of the AppBar
  /// - [bottom]: Optional bottom widget (e.g., TabBar)
  /// - [elevation]: Custom elevation (defaults to AppElevation.sm)
  /// - [backgroundColor]: Custom background color (defaults to theme surface)
  /// - [foregroundColor]: Custom foreground color (defaults to theme onSurface)
  static AppBar createTimePeriodAppBar({
    required String title,
    List<Widget>? actions,
    PreferredSizeWidget? bottom,
    double? elevation,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    return AppBar(
      title: _buildTimePeriodTitle(title),
      titleSpacing: 16,
      actions: actions,
      bottom: bottom,
      elevation: elevation ?? AppElevation.sm,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
    );
  }

  /// Creates an AppBar with TimePeriodSelector integration for scrollable screens
  ///
  /// This is used for screens that need time period filtering (Home, Transactions, Budgets).
  /// The TimePeriodSelector is positioned in the left side of the title area.
  ///
  /// Parameters:
  /// - [title]: The screen title text to display on the right side
  /// - [actions]: Optional action buttons for the right side of the AppBar
  /// - [bottom]: Optional bottom widget (e.g., TabBar)
  /// - [elevation]: Custom elevation (defaults to AppElevation.sm)
  /// - [backgroundColor]: Custom background color (defaults to theme surface)
  /// - [foregroundColor]: Custom foreground color (defaults to theme onSurface)
  /// - [allowFutureNavigation]: Whether to allow navigation to future periods (for budget templates)
  static AppBar createTimePeriodScrollableAppBar({
    required String title,
    List<Widget>? actions,
    PreferredSizeWidget? bottom,
    double? elevation,
    Color? backgroundColor,
    Color? foregroundColor,
    bool allowFutureNavigation = false,
  }) {
    return AppBar(
      title: _buildTimePeriodTitle(
        title,
        allowFutureNavigation: allowFutureNavigation,
      ),
      titleSpacing: 16,
      actions: actions,
      bottom: bottom,
      elevation: elevation ?? AppElevation.sm,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
    );
  }

  /// Creates a standard AppBar for screens that don't need time period filtering
  ///
  /// This is used for screens like Profile, Settings, Account Details, etc.
  ///
  /// Parameters:
  /// - [title]: The screen title text to display
  /// - [actions]: Optional action buttons for the right side of the AppBar
  /// - [bottom]: Optional bottom widget (e.g., TabBar)
  /// - [centerTitle]: Whether to center the title (default: true)
  /// - [elevation]: Custom elevation (defaults to AppElevation.sm)
  /// - [backgroundColor]: Custom background color (defaults to theme surface)
  /// - [foregroundColor]: Custom foreground color (defaults to theme onSurface)
  static AppBar createStandardAppBar({
    required String title,
    List<Widget>? actions,
    PreferredSizeWidget? bottom,
    bool centerTitle = true,
    double? elevation,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    return AppBar(
      title: Text(title),
      centerTitle: centerTitle,
      actions: actions,
      bottom: bottom,
      elevation: elevation ?? AppElevation.sm,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
    );
  }

  /// Creates a standard AppBar for scrollable screens without time period filtering
  ///
  /// This is used for screens like Categories, Tags, etc. that use SingleChildScrollView
  /// but don't need time period functionality.
  ///
  /// Parameters:
  /// - [title]: The screen title text to display
  /// - [actions]: Optional action buttons for the right side of the AppBar
  /// - [bottom]: Optional bottom widget (e.g., TabBar)
  /// - [elevation]: Custom elevation (defaults to AppElevation.sm)
  /// - [backgroundColor]: Custom background color (defaults to theme surface)
  /// - [foregroundColor]: Custom foreground color (defaults to theme onSurface)
  static AppBar createStandardScrollableAppBar({
    required String title,
    List<Widget>? actions,
    PreferredSizeWidget? bottom,
    double? elevation,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    return AppBar(
      title: Text(title),
      actions: actions,
      bottom: bottom,
      elevation: elevation ?? AppElevation.sm,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
    );
  }

  /// Builds the title widget with TimePeriodSelector integration
  ///
  /// This creates a Row layout with TimePeriodSelector on the left,
  /// a Spacer in the middle, and the screen title on the right.
  static Widget _buildTimePeriodTitle(
    String title, {
    bool allowFutureNavigation = false,
  }) {
    return Row(
      children: [
        // TimePeriodSelector in top-left corner
        TimePeriodSelector(
          showDateRange: true,
          compact: false,
          allowFutureNavigation: allowFutureNavigation,
        ),
        const Spacer(),
        // Title on the right side
        Flexible(child: Text(title)),
      ],
    );
  }
}
