import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/common/widgets/app_bar_helpers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/services/currency_service.dart';
import 'package:budapp/widgets/common/app_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Screen for managing currency settings
class CurrencySettingsScreen extends ConsumerStatefulWidget {
  const CurrencySettingsScreen({super.key});

  @override
  ConsumerState<CurrencySettingsScreen> createState() =>
      _CurrencySettingsScreenState();
}

class _CurrencySettingsScreenState
    extends ConsumerState<CurrencySettingsScreen> {
  String? _selectedCurrency;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Initialize with current currency preference
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final currentCurrency = ref.read(currencyPreferenceProvider);
      setState(() {
        _selectedCurrency = currentCurrency;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final supportedCurrencies = ref.watch(supportedCurrenciesProvider);
    final currentCurrency = ref.watch(currencyPreferenceProvider);
    final currencyFormatter = ref.watch(currencyFormatterProvider);

    return Scaffold(
      appBar: AppBarHelpers.createStandardAppBar(title: l10n.currencySettings),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section
            AppText.title(
              l10n.selectCurrency,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            AppText.body(
              l10n.currencySettingsDescription,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: AppSpacing.xl),

            // Currency preview section
            _buildCurrencyPreview(theme, l10n, currencyFormatter),
            const SizedBox(height: AppSpacing.xl),

            // Currency selection section
            AppText.title(
              l10n.availableCurrencies,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppSpacing.md),

            // Currency list
            _buildCurrencyList(theme, supportedCurrencies, currentCurrency),
            const SizedBox(height: AppSpacing.xl),

            // Save button
            if (_selectedCurrency != null &&
                _selectedCurrency != currentCurrency)
              SizedBox(
                width: double.infinity,
                child: FilledButton(
                  onPressed: _isLoading ? null : _saveCurrencyPreference,
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(l10n.save),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrencyPreview(
    ThemeData theme,
    AppLocalizations l10n,
    CurrencyFormatter formatter,
  ) {
    return Card(
      elevation: AppElevation.sm,
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.preview_outlined,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: AppSpacing.sm),
                AppText.title(
                  l10n.preview,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.md),

            // Sample amounts
            _buildPreviewItem(
              l10n.income,
              formatter.formatAmountWithSign(125000, showPositiveSign: true),
              theme.colorScheme.primary,
            ),
            const SizedBox(height: AppSpacing.sm),
            _buildPreviewItem(
              l10n.expense,
              formatter.formatAmountWithSign(-75000),
              theme.colorScheme.error,
            ),
            const SizedBox(height: AppSpacing.sm),
            _buildPreviewItem(
              l10n.balance,
              formatter.formatAmount(50000),
              theme.colorScheme.onSurface,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewItem(String label, String amount, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        AppText.body(label),
        AppText.title(
          amount,
          style: TextStyle(color: color, fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  Widget _buildCurrencyList(
    ThemeData theme,
    List<String> currencies,
    String currentCurrency,
  ) {
    return Column(
      children: currencies.map((currency) {
        final isSelected = _selectedCurrency == currency;
        final isCurrent = currency == currentCurrency;

        return Card(
          elevation: isSelected ? AppElevation.md : AppElevation.sm,
          color: isSelected ? theme.colorScheme.primaryContainer : null,
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.surfaceContainerHighest,
              child: Text(
                CurrencyService.getCurrencySymbol(currency),
                style: TextStyle(
                  color: isSelected
                      ? theme.colorScheme.onPrimary
                      : theme.colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            title: AppText.title(
              currency,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: isSelected ? theme.colorScheme.onPrimaryContainer : null,
              ),
            ),
            subtitle: AppText.body(
              CurrencyService.getCurrencyDisplayName(currency),
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected
                    ? theme.colorScheme.onPrimaryContainer.withValues(
                        alpha: 0.7,
                      )
                    : theme.colorScheme.onSurfaceVariant,
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isCurrent)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.sm,
                      vertical: AppSpacing.xs,
                    ),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.secondary,
                      borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                    ),
                    child: AppText.label(
                      'Current',
                      style: TextStyle(
                        color: theme.colorScheme.onSecondary,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                if (isSelected && !isCurrent) ...[
                  const SizedBox(width: AppSpacing.sm),
                  Icon(
                    Icons.check_circle,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
                ],
              ],
            ),
            onTap: () {
              setState(() {
                _selectedCurrency = currency;
              });
            },
          ),
        );
      }).toList(),
    );
  }

  Future<void> _saveCurrencyPreference() async {
    if (_selectedCurrency == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final notifier = ref.read(currencyPreferenceProvider.notifier);
      final success = await notifier.setCurrency(_selectedCurrency!);

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                AppLocalizations.of(context)!.currencyUpdatedSuccessfully,
              ),
              backgroundColor: Theme.of(context).colorScheme.primary,
            ),
          );
          context.pop();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                AppLocalizations.of(context)!.failedToUpdateCurrency,
              ),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    } on Exception {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.failedToUpdateCurrency),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
