import 'package:budapp/data/models/user_profile.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'profile_providers.g.dart';

/// Provider for current user profile data
@riverpod
Future<UserProfile?> userProfile(Ref ref) async {
  final userRepository = ref.watch(userRepositoryProvider);
  return userRepository.getCurrentUserProfile();
}

/// Provider for profile update operations
@riverpod
class ProfileUpdate extends _$ProfileUpdate {
  @override
  FutureOr<void> build() {
    return null;
  }

  /// Update user display name
  Future<void> updateDisplayName(String displayName) async {
    state = const AsyncLoading();

    try {
      final userRepository = ref.read(userRepositoryProvider);
      await userRepository.updateDisplayName(displayName);

      // Invalidate the user profile to refresh the UI
      ref.invalidate(userProfileProvider);

      state = const AsyncData(null);
    } on Exception catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
      rethrow;
    }
  }

  /// Update user email
  Future<void> updateEmail(String email) async {
    state = const AsyncLoading();

    try {
      final userRepository = ref.read(userRepositoryProvider);
      await userRepository.updateEmail(email);

      // Invalidate the user profile to refresh the UI
      ref.invalidate(userProfileProvider);

      state = const AsyncData(null);
    } on Exception catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
      rethrow;
    }
  }

  /// Update complete user profile
  Future<void> updateProfile(UserProfile userProfile) async {
    state = const AsyncLoading();

    try {
      final userRepository = ref.read(userRepositoryProvider);
      await userRepository.updateUserProfile(userProfile);

      // Invalidate the user profile to refresh the UI
      ref.invalidate(userProfileProvider);

      state = const AsyncData(null);
    } on Exception catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
      rethrow;
    }
  }
}

/// Provider for password change operations
@riverpod
class PasswordChange extends _$PasswordChange {
  @override
  FutureOr<void> build() {
    return null;
  }

  /// Change user password with re-authentication
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    state = const AsyncLoading();

    try {
      final authService = ref.read(authServiceProvider);

      // Re-authenticate first
      await authService.reauthenticate(currentPassword);

      // Update password
      await authService.updatePassword(newPassword);

      state = const AsyncData(null);
    } on Exception catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
      rethrow;
    }
  }
}

/// Provider for account deletion operations
@riverpod
class AccountDeletion extends _$AccountDeletion {
  @override
  FutureOr<void> build() {
    return null;
  }

  /// Delete user account with re-authentication
  Future<void> deleteAccount(String password) async {
    state = const AsyncLoading();

    try {
      final authService = ref.read(authServiceProvider);
      await authService.deleteAccountWithReauth(password);

      state = const AsyncData(null);
    } on Exception catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
      rethrow;
    }
  }
}

/// Provider for password reset operations
@riverpod
class PasswordReset extends _$PasswordReset {
  @override
  FutureOr<void> build() {
    return null;
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    state = const AsyncLoading();

    try {
      final authService = ref.read(authServiceProvider);
      await authService.sendPasswordResetEmail(email: email);

      state = const AsyncData(null);
    } on Exception catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
      rethrow;
    }
  }
}
