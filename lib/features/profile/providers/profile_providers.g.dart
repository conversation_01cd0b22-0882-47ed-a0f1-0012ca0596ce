// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userProfileHash() => r'66db6d05e5a57e2c286ad67db13c214f5a99089b';

/// Provider for current user profile data
///
/// Copied from [userProfile].
@ProviderFor(userProfile)
final userProfileProvider = AutoDisposeFutureProvider<UserProfile?>.internal(
  userProfile,
  name: r'userProfileProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userProfileHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserProfileRef = AutoDisposeFutureProviderRef<UserProfile?>;
String _$profileUpdateHash() => r'1369a64b28043f1b3b80bd7cfa871c1d177f853b';

/// Provider for profile update operations
///
/// Copied from [ProfileUpdate].
@ProviderFor(ProfileUpdate)
final profileUpdateProvider =
    AutoDisposeAsyncNotifierProvider<ProfileUpdate, void>.internal(
      ProfileUpdate.new,
      name: r'profileUpdateProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$profileUpdateHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ProfileUpdate = AutoDisposeAsyncNotifier<void>;
String _$passwordChangeHash() => r'96177798cc72c4f6387f11ff9697a5dc3dbad718';

/// Provider for password change operations
///
/// Copied from [PasswordChange].
@ProviderFor(PasswordChange)
final passwordChangeProvider =
    AutoDisposeAsyncNotifierProvider<PasswordChange, void>.internal(
      PasswordChange.new,
      name: r'passwordChangeProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$passwordChangeHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PasswordChange = AutoDisposeAsyncNotifier<void>;
String _$accountDeletionHash() => r'35d7d5282e76b23be0169170f3a9e8f0c9337e27';

/// Provider for account deletion operations
///
/// Copied from [AccountDeletion].
@ProviderFor(AccountDeletion)
final accountDeletionProvider =
    AutoDisposeAsyncNotifierProvider<AccountDeletion, void>.internal(
      AccountDeletion.new,
      name: r'accountDeletionProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$accountDeletionHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AccountDeletion = AutoDisposeAsyncNotifier<void>;
String _$passwordResetHash() => r'63d2feeb56b57ed6c4d17d1ae62e99a92c51e813';

/// Provider for password reset operations
///
/// Copied from [PasswordReset].
@ProviderFor(PasswordReset)
final passwordResetProvider =
    AutoDisposeAsyncNotifierProvider<PasswordReset, void>.internal(
      PasswordReset.new,
      name: r'passwordResetProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$passwordResetHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PasswordReset = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
