import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/profile/services/profile_validators.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Reusable dialog widget for re-authentication
class ReAuthenticationDialog extends ConsumerStatefulWidget {
  const ReAuthenticationDialog({
    super.key,
    this.title = 'Confirm Identity',
    this.message = 'Please enter your current password to continue.',
    this.confirmButtonText = 'Confirm',
  });
  final String title;
  final String message;
  final String confirmButtonText;

  @override
  ConsumerState<ReAuthenticationDialog> createState() =>
      _ReAuthenticationDialogState();
}

class _ReAuthenticationDialogState
    extends ConsumerState<ReAuthenticationDialog> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  bool _obscurePassword = true;
  String? _errorMessage;

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _handleReAuthentication() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authService = ref.read(authServiceProvider);
      await authService.reauthenticate(_passwordController.text);

      if (mounted) {
        Navigator.of(context).pop(true); // Return success
      }
    } on Exception catch (e) {
      setState(() {
        _errorMessage = _getErrorMessage(e);
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _getErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('wrong-password') ||
        errorString.contains('invalid-credential')) {
      return 'Incorrect password. Please try again.';
    } else if (errorString.contains('too-many-requests')) {
      return 'Too many failed attempts. Please try again later.';
    } else if (errorString.contains('network')) {
      return 'Network error. Please check your connection.';
    } else {
      return 'Authentication failed. Please try again.';
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(widget.message),
            const SizedBox(height: 16),
            TextFormField(
              controller: _passwordController,
              obscureText: _obscurePassword,
              decoration: InputDecoration(
                labelText: 'Current Password',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscurePassword ? Icons.visibility : Icons.visibility_off,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                ),
                errorText: _errorMessage,
              ),
              validator: ProfileValidators.validateCurrentPassword,
              enabled: !_isLoading,
              autofocus: true,
              onFieldSubmitted: (_) => _handleReAuthentication(),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(false),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _handleReAuthentication,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(widget.confirmButtonText),
        ),
      ],
    );
  }
}

/// Show re-authentication dialog and return the result
Future<bool> showReAuthenticationDialog(
  BuildContext context, {
  String? title,
  String? message,
  String? confirmButtonText,
}) async {
  final result = await showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => ReAuthenticationDialog(
      title: title ?? 'Confirm Identity',
      message: message ?? 'Please enter your current password to continue.',
      confirmButtonText: confirmButtonText ?? 'Confirm',
    ),
  );

  return result ?? false;
}
