import 'package:budapp/features/profile/presentation/widgets/re_authentication_dialog.dart';
import 'package:budapp/features/profile/providers/profile_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Comprehensive dialog widget for account deletion with multiple confirmation steps
class AccountDeletionDialog extends ConsumerStatefulWidget {
  const AccountDeletionDialog({super.key});

  @override
  ConsumerState<AccountDeletionDialog> createState() =>
      _AccountDeletionDialogState();
}

class _AccountDeletionDialogState extends ConsumerState<AccountDeletionDialog> {
  bool _isDeleting = false;
  bool _hasConfirmedDataLoss = false;
  String? _errorMessage;

  final List<String> _warningMessages = [
    'All your financial data will be permanently deleted',
    'Your accounts, transactions, and budgets will be lost',
    'This action cannot be undone',
    'You will need to create a new account to use the app again',
  ];

  Future<void> _handleAccountDeletion() async {
    setState(() {
      _isDeleting = true;
      _errorMessage = null;
    });

    try {
      // Step 1: Show re-authentication dialog
      final isAuthenticated = await showReAuthenticationDialog(
        context,
        title: 'Confirm Account Deletion',
        message: 'Enter your password to permanently delete your account.',
        confirmButtonText: 'Delete Account',
      );

      if (!isAuthenticated) {
        setState(() {
          _isDeleting = false;
        });
        return;
      }

      // Step 2: Get password from re-authentication (we'll need to modify this)
      // For now, we'll show another password dialog specifically for deletion
      final password = await _showPasswordConfirmationDialog();

      if (password == null) {
        setState(() {
          _isDeleting = false;
        });
        return;
      }

      // Step 3: Delete the account
      await ref.read(accountDeletionProvider.notifier).deleteAccount(password);

      if (mounted) {
        Navigator.of(context).pop(true); // Return success
      }
    } on Exception catch (e) {
      setState(() {
        _errorMessage = _getErrorMessage(e);
      });
    } finally {
      if (mounted) {
        setState(() {
          _isDeleting = false;
        });
      }
    }
  }

  Future<String?> _showPasswordConfirmationDialog() async {
    final controller = TextEditingController();

    final result = await showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Final Confirmation'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Enter your password one final time to delete your account:',
            ),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'Password',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: () => Navigator.of(context).pop(controller.text),
            child: const Text('Delete Account'),
          ),
        ],
      ),
    );

    controller.dispose();
    return result;
  }

  String _getErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('requires-recent-login')) {
      return 'Please sign out and sign back in, then try again.';
    } else if (errorString.contains('network')) {
      return 'Network error. Please check your connection.';
    } else {
      return 'Failed to delete account. Please try again.';
    }
  }

  Widget _buildWarningStep() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Warning: Account Deletion',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'You are about to permanently delete your account. This action will:',
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 12),
          ...(_warningMessages.map(
            (message) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(Icons.warning, color: Colors.red, size: 20),
                  const SizedBox(width: 8),
                  Expanded(child: Text(message)),
                ],
              ),
            ),
          )),
          const SizedBox(height: 16),
          CheckboxListTile(
            value: _hasConfirmedDataLoss,
            onChanged: (value) {
              setState(() {
                _hasConfirmedDataLoss = value ?? false;
              });
            },
            title: const Text(
              'I understand that all my data will be permanently deleted',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            controlAffinity: ListTileControlAffinity.leading,
          ),
          if (_errorMessage != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                border: Border.all(color: Colors.red.shade200),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.error, color: Colors.red),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Delete Account'),
      content: _buildWarningStep(),
      actions: [
        TextButton(
          onPressed: _isDeleting
              ? null
              : () => Navigator.of(context).pop(false),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
          ),
          onPressed: (_isDeleting || !_hasConfirmedDataLoss)
              ? null
              : _handleAccountDeletion,
          child: _isDeleting
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('Delete Account'),
        ),
      ],
    );
  }
}

/// Show account deletion dialog and return the result
Future<bool> showAccountDeletionDialog(BuildContext context) async {
  final result = await showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => const AccountDeletionDialog(),
  );

  return result ?? false;
}
