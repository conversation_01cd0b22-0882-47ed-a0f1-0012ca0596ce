import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/auth/presentation/widgets/biometric_error_banner.dart';
import 'package:budapp/features/auth/presentation/widgets/biometric_settings_tile.dart';
import 'package:budapp/features/auth/providers/biometric_providers.dart';
import 'package:budapp/features/auth/services/biometric_error_service.dart'
    hide BiometricAuthError;
import 'package:budapp/features/profile/providers/profile_providers.dart';
import 'package:budapp/features/profile/services/profile_validators.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/common/app_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Unified screen for managing all profile-related settings
class UnifiedProfileManagementScreen extends ConsumerStatefulWidget {
  const UnifiedProfileManagementScreen({super.key});

  @override
  ConsumerState<UnifiedProfileManagementScreen> createState() =>
      _UnifiedProfileManagementScreenState();
}

class _UnifiedProfileManagementScreenState
    extends ConsumerState<UnifiedProfileManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Profile Edit Form
  final _profileFormKey = GlobalKey<FormState>();
  final _displayNameController = TextEditingController();
  final _emailController = TextEditingController();
  bool _hasProfileChanges = false;
  bool _isProfileInitialized = false;

  // Password Change Form
  final _passwordFormKey = GlobalKey<FormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _displayNameController.dispose();
    _emailController.dispose();
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _initializeProfileFields() {
    if (_isProfileInitialized) return;

    ref
        .read(userProfileProvider.future)
        .then((userProfile) {
          if (userProfile != null && mounted) {
            setState(() {
              _displayNameController.text = userProfile.displayName ?? '';
              _emailController.text = userProfile.email ?? '';
              _isProfileInitialized = true;
            });
          }
        })
        .catchError((error) {
          // Handle error case - profile might not exist yet
          if (mounted) {
            setState(() {
              _isProfileInitialized = true;
            });
          }
        });
  }

  void _onProfileFieldChanged() {
    setState(() {
      _hasProfileChanges = true;
    });
  }

  Future<void> _handleProfileSave() async {
    // Validate form first
    if (!_profileFormKey.currentState!.validate()) {
      return;
    }

    final newDisplayName = _displayNameController.text.trim();
    final newEmail = _emailController.text.trim();

    // Additional check: at least one field must be non-empty
    if (newDisplayName.isEmpty && newEmail.isEmpty) {
      _showErrorSnackBar(
        'Please provide at least one field (display name or email).',
      );
      return;
    }

    try {
      final profileUpdateNotifier = ref.read(profileUpdateProvider.notifier);
      final currentProfile = await ref.read(userProfileProvider.future);

      // Handle display name update
      if (newDisplayName.isNotEmpty &&
          newDisplayName != (currentProfile?.displayName ?? '')) {
        await profileUpdateNotifier.updateDisplayName(newDisplayName);
      }

      // Handle email update
      if (newEmail.isNotEmpty && newEmail != (currentProfile?.email ?? '')) {
        await profileUpdateNotifier.updateEmail(newEmail);
      }

      // Show success message even if no profile exists yet (first-time setup)
      if (mounted) {
        _showSuccessSnackBar('Profile updated successfully');
        setState(() {
          _hasProfileChanges = false;
        });
      }
    } on Exception catch (e) {
      if (mounted) {
        _showErrorSnackBar(_getProfileErrorMessage(e));
      }
    }
  }

  Future<void> _handlePasswordChange() async {
    if (!_passwordFormKey.currentState!.validate()) return;

    try {
      await ref
          .read(passwordChangeProvider.notifier)
          .changePassword(
            currentPassword: _currentPasswordController.text,
            newPassword: _newPasswordController.text,
          );

      if (mounted) {
        _showSuccessSnackBar('Password changed successfully');
        _currentPasswordController.clear();
        _newPasswordController.clear();
        _confirmPasswordController.clear();
      }
    } on Exception catch (e) {
      if (mounted) {
        _showErrorSnackBar(_getPasswordErrorMessage(e));
      }
    }
  }

  String _getProfileErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();
    if (errorString.contains('email-already-in-use')) {
      return 'This email address is already in use by another account.';
    } else if (errorString.contains('invalid-email')) {
      return 'Please enter a valid email address.';
    } else if (errorString.contains('requires-recent-login')) {
      return 'Please sign out and sign back in, then try again.';
    } else if (errorString.contains('network')) {
      return 'Network error. Please check your connection.';
    } else {
      return 'Failed to update profile. Please try again.';
    }
  }

  String _getPasswordErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();
    if (errorString.contains('wrong-password')) {
      return 'Current password is incorrect.';
    } else if (errorString.contains('weak-password')) {
      return 'New password is too weak. Please choose a stronger password.';
    } else if (errorString.contains('requires-recent-login')) {
      return 'Please sign out and sign back in, then try again.';
    } else if (errorString.contains('network')) {
      return 'Network error. Please check your connection.';
    } else {
      return 'Failed to change password. Please try again.';
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }

  void _showBiometricSetupDialog(BuildContext context, AppLocalizations l10n) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Biometric Setup'),
        content: const Text(
          'Please enable biometric authentication in your device settings to use this feature.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Open device settings - this would need platform-specific implementation
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    _initializeProfileFields();

    final profileUpdateState = ref.watch(profileUpdateProvider);
    final passwordChangeState = ref.watch(passwordChangeProvider);
    final isProfileLoading = profileUpdateState.isLoading;
    final isPasswordLoading = passwordChangeState.isLoading;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile Management'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Profile', icon: Icon(Icons.person_outline)),
            Tab(text: 'Password', icon: Icon(Icons.lock_outline)),
            Tab(text: 'Security', icon: Icon(Icons.fingerprint_outlined)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildProfileTab(isProfileLoading),
          _buildPasswordTab(isPasswordLoading),
          _buildSecurityTab(l10n, theme),
        ],
      ),
    );
  }

  Widget _buildProfileTab(bool isLoading) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Form(
        key: _profileFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Edit Your Profile',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.xl),

            // Display Name Field
            AppTextFormField(
              label: 'Display Name',
              controller: _displayNameController,
              prefixIcon: const Icon(Icons.person),
              validator: (value) {
                // Allow empty if email is valid
                if (value == null || value.trim().isEmpty) {
                  final email = _emailController.text.trim();
                  if (email.isNotEmpty &&
                      ProfileValidators.validateEmail(email) == null) {
                    return null; // Allow empty display name if email is valid
                  }
                }
                return ProfileValidators.validateDisplayName(value);
              },
              enabled: !isLoading,
              onChanged: (_) => _onProfileFieldChanged(),
            ),
            const SizedBox(height: AppSpacing.lg),

            // Email Field
            AppTextFormField(
              label: 'Email Address',
              controller: _emailController,
              keyboardType: TextInputType.emailAddress,
              prefixIcon: const Icon(Icons.email),
              helperText: 'You will need to verify your new email address',
              validator: (value) {
                // Allow empty if display name is valid
                if (value == null || value.trim().isEmpty) {
                  final displayName = _displayNameController.text.trim();
                  if (displayName.isNotEmpty &&
                      ProfileValidators.validateDisplayName(displayName) ==
                          null) {
                    return null; // Allow empty email if display name is valid
                  }
                }
                return ProfileValidators.validateEmail(value);
              },
              enabled: !isLoading,
              onChanged: (_) => _onProfileFieldChanged(),
            ),
            const SizedBox(height: AppSpacing.xl),

            // Save Button
            ElevatedButton(
              onPressed: (_hasProfileChanges && !isLoading)
                  ? _handleProfileSave
                  : null,
              child: isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Save Changes'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordTab(bool isLoading) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Form(
        key: _passwordFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Change Password',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.xl),

            // Current Password Field
            AppTextFormField(
              label: 'Current Password',
              controller: _currentPasswordController,
              isPassword: true,
              prefixIcon: const Icon(Icons.lock),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your current password';
                }
                return null;
              },
              enabled: !isLoading,
            ),
            const SizedBox(height: AppSpacing.lg),

            // New Password Field
            AppTextFormField(
              label: 'New Password',
              controller: _newPasswordController,
              isPassword: true,
              prefixIcon: const Icon(Icons.lock_outline),
              validator: ProfileValidators.validatePassword,
              enabled: !isLoading,
              onChanged: (_) => setState(() {}),
            ),
            const SizedBox(height: AppSpacing.lg),

            // Confirm Password Field
            AppTextFormField(
              label: 'Confirm New Password',
              controller: _confirmPasswordController,
              isPassword: true,
              prefixIcon: const Icon(Icons.lock_outline),
              validator: (value) {
                if (value != _newPasswordController.text) {
                  return 'Passwords do not match';
                }
                return null;
              },
              enabled: !isLoading,
            ),
            const SizedBox(height: AppSpacing.xl),

            // Change Password Button
            ElevatedButton(
              onPressed: isLoading ? null : _handlePasswordChange,
              child: isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Change Password'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityTab(AppLocalizations l10n, ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Error display for biometric operations
          Consumer(
            builder: (context, ref, child) {
              final biometricState = ref.watch(biometricAuthNotifierProvider);

              if (biometricState is BiometricAuthError) {
                final biometricError =
                    BiometricErrorService.handleBiometricError(
                      Exception(biometricState.message),
                      l10n,
                    );
                return BiometricErrorBanner(
                  error: biometricError,
                  onRetry: biometricError.isRetryable
                      ? () async {
                          final biometricNotifier = ref.read(
                            biometricAuthNotifierProvider.notifier,
                          );
                          await biometricNotifier.authenticate();
                        }
                      : null,
                  onDismiss: () {
                    ref.invalidate(biometricAuthNotifierProvider);
                  },
                  onSetupBiometrics: () {
                    _showBiometricSetupDialog(context, l10n);
                  },
                );
              }

              return const SizedBox.shrink();
            },
          ),

          // Security Settings Section
          Text(
            l10n.securitySettings,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: AppTypography.fontWeightSemiBold,
              color: theme.colorScheme.onSurface,
            ),
          ),

          const SizedBox(height: AppSpacing.sm),

          Text(
            l10n.biometricSettingsDescription,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),

          const SizedBox(height: AppSpacing.lg),

          // Biometric Authentication Settings
          Card(
            elevation: 0,
            color: theme.colorScheme.surfaceContainerLow,
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.sm),
              child: Column(
                children: [
                  BiometricSettingsTile(
                    onChanged: () {
                      // Refresh the UI when biometric settings change
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
