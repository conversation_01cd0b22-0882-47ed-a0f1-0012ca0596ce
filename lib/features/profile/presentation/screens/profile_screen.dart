import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/config/environment_config.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/common/widgets/app_bar_helpers.dart';
import 'package:budapp/features/profile/presentation/widgets/account_deletion_dialog.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Profile screen that provides access to user profile and app settings
class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  Map<String, dynamic>? _firebaseStatus;
  bool _isTestingFirebase = false;

  Future<void> _testFirebaseConnectivity() async {
    setState(() {
      _isTestingFirebase = true;
    });

    try {
      // Use the FirebaseConnectivityService instead of direct Firebase access
      final connectivityService = ref.read(firebaseConnectivityServiceProvider);
      final results = await connectivityService.testFirebaseConnectivity();

      setState(() {
        _firebaseStatus = results;
        _isTestingFirebase = false;
      });
    } on Exception catch (e) {
      setState(() {
        _firebaseStatus = {'error': e.toString()};
        _isTestingFirebase = false;
      });
    }
  }

  List<Widget> _buildFirebaseStatusWidgets() {
    if (_firebaseStatus == null) return [];

    final widgets = <Widget>[];

    // Auth status
    if (_firebaseStatus!.containsKey('auth')) {
      final authStatus = _firebaseStatus!['auth'] as Map<String, dynamic>?;
      final isConnected = authStatus?['status'] == 'connected';
      widgets.add(
        Row(
          children: [
            Icon(
              isConnected ? Icons.check_circle : Icons.error,
              color: isConnected ? AppColors.success : AppColors.error,
              size: 16,
            ),
            const SizedBox(width: AppSpacing.sm),
            Text(
              '${AppLocalizations.of(context)!.firebaseAuth} ${authStatus?['status'] ?? 'unknown'}',
            ),
          ],
        ),
      );
    }

    // Firestore status
    if (_firebaseStatus!.containsKey('firestore')) {
      final firestoreStatus =
          _firebaseStatus!['firestore'] as Map<String, dynamic>?;
      final isConnected = firestoreStatus?['status'] == 'connected';
      widgets
        ..add(const SizedBox(height: AppSpacing.xs))
        ..add(
          Row(
            children: [
              Icon(
                isConnected ? Icons.check_circle : Icons.error,
                color: isConnected ? AppColors.success : AppColors.error,
                size: 16,
              ),
              const SizedBox(width: AppSpacing.sm),
              Text(
                '${AppLocalizations.of(context)!.firestore} ${firestoreStatus?['status'] ?? 'unknown'}',
              ),
            ],
          ),
        );
    }

    return widgets;
  }

  Future<void> _signOut(WidgetRef ref, BuildContext context) async {
    try {
      final authService = ref.read(authServiceProvider);
      await authService.signOut();
    } on Exception catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context)!.signOutFailed(e.toString()),
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _showComingSoonSnackBar(BuildContext context, String featureName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '$featureName - ${AppLocalizations.of(context)!.comingSoon}',
        ),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _showDeleteAccountDialog(BuildContext context) async {
    final result = await showAccountDeletionDialog(context);

    if (result && context.mounted) {
      // Account was successfully deleted, user will be automatically signed out
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Account deleted successfully'),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final user = ref.watch(currentUserProvider);
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBarHelpers.createStandardAppBar(title: l10n.profile),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User Profile Card
            Card(
              elevation: AppElevation.sm,
              child: InkWell(
                onTap: () => context.push('/profile/manage'),
                borderRadius: BorderRadius.circular(AppBorderRadius.md),
                child: Padding(
                  padding: const EdgeInsets.all(AppSpacing.lg),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 32,
                        backgroundColor: theme.colorScheme.primaryContainer,
                        child: Icon(
                          Icons.person,
                          size: 36,
                          color: theme.colorScheme.onPrimaryContainer,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.md),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              l10n.welcome,
                              style: theme.textTheme.titleLarge?.copyWith(
                                fontWeight: AppTypography.fontWeightBold,
                              ),
                            ),
                            const SizedBox(height: AppSpacing.xs),
                            Text(
                              user?.email ?? l10n.noEmail,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            if (user?.emailVerified ?? false) ...[
                              const SizedBox(height: AppSpacing.xs),
                              Row(
                                children: [
                                  Icon(
                                    Icons.verified,
                                    size: 16,
                                    color: theme.colorScheme.primary,
                                  ),
                                  const SizedBox(width: AppSpacing.xs),
                                  Text(
                                    'Verified',
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: theme.colorScheme.primary,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(height: AppSpacing.lg),

            // Profile Management Section
            _buildSectionHeader(context, 'Profile Management'),
            const SizedBox(height: AppSpacing.sm),

            _buildSettingsItem(
              context,
              icon: Icons.edit_outlined,
              title: 'Manage Profile',
              subtitle: 'Edit profile, change password, and security settings',
              onTap: () => context.push('/profile/manage'),
            ),

            const SizedBox(height: AppSpacing.lg),

            // Financial Management Section
            _buildSectionHeader(context, 'Financial Management'),
            const SizedBox(height: AppSpacing.sm),

            _buildSettingsItem(
              context,
              icon: Icons.category_outlined,
              title: 'Categories',
              subtitle: 'Manage income and expense categories',
              onTap: () => context.go('/categories'),
            ),

            _buildSettingsItem(
              context,
              icon: Icons.account_balance_wallet_outlined,
              title: 'Budgets',
              subtitle: 'Manage your monthly and yearly budgets',
              onTap: () => context.go('/budgets'),
            ),

            _buildSettingsItem(
              context,
              icon: Icons.flag_outlined,
              title: 'Goals',
              subtitle: 'Track your financial goals and progress',
              onTap: () => context.go('/goals'),
            ),

            _buildSettingsItem(
              context,
              icon: Icons.label_outline,
              title: 'Tags',
              subtitle: 'Organize transactions with custom tags',
              onTap: () => context.go('/tags'),
            ),

            const SizedBox(height: AppSpacing.lg),

            // Settings Section
            _buildSectionHeader(context, 'Settings & Preferences'),
            const SizedBox(height: AppSpacing.sm),

            _buildSettingsItem(
              context,
              icon: Icons.currency_exchange_outlined,
              title: l10n.currencySettings,
              subtitle: 'Choose your preferred currency for displaying amounts',
              onTap: () => context.push('/settings/currency'),
            ),

            _buildSettingsItem(
              context,
              icon: Icons.settings_outlined,
              title: l10n.settings,
              subtitle: 'App preferences and configuration',
              onTap: () => _showComingSoonSnackBar(context, l10n.settings),
            ),

            _buildSettingsItem(
              context,
              icon: Icons.import_export_outlined,
              title: l10n.importExport,
              subtitle: 'Backup and restore your data',
              onTap: () => _showComingSoonSnackBar(context, l10n.importExport),
            ),

            _buildSettingsItem(
              context,
              icon: Icons.analytics_outlined,
              title: l10n.reports,
              subtitle: 'View detailed financial reports',
              onTap: () => _showComingSoonSnackBar(context, l10n.reports),
            ),

            const SizedBox(height: AppSpacing.lg),

            // Support Section
            _buildSectionHeader(context, 'Help & Support'),
            const SizedBox(height: AppSpacing.sm),

            _buildSettingsItem(
              context,
              icon: Icons.help_outline,
              title: l10n.help,
              subtitle: 'Get help and view tutorials',
              onTap: () => _showComingSoonSnackBar(context, l10n.help),
            ),

            _buildSettingsItem(
              context,
              icon: Icons.feedback_outlined,
              title: l10n.feedback,
              subtitle: 'Send feedback and suggestions',
              onTap: () => _showComingSoonSnackBar(context, l10n.feedback),
            ),

            const SizedBox(height: AppSpacing.lg),

            // Development Info (only in dev)
            if (EnvironmentConfig.showFirebaseTesting) ...[
              _buildSectionHeader(context, 'Development Info'),
              const SizedBox(height: AppSpacing.sm),

              Card(
                elevation: AppElevation.sm,
                child: Padding(
                  padding: const EdgeInsets.all(AppSpacing.md),
                  child: Column(
                    children: [
                      Text(
                        '${l10n.environment} ${EnvironmentConfig.environmentName}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: AppTypography.fontWeightBold,
                        ),
                      ),
                      const SizedBox(height: AppSpacing.sm),
                      Text(
                        '${l10n.firebaseProject} ${EnvironmentConfig.firebaseProjectName}',
                        style: theme.textTheme.bodyMedium,
                      ),
                      const SizedBox(height: AppSpacing.md),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _isTestingFirebase
                              ? null
                              : _testFirebaseConnectivity,
                          child: _isTestingFirebase
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                              : Text(l10n.testFirebaseServices),
                        ),
                      ),
                      if (_firebaseStatus != null) ...[
                        const SizedBox(height: AppSpacing.md),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(AppSpacing.sm),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.surfaceContainerHighest
                                .withValues(alpha: 0.5),
                            borderRadius: BorderRadius.circular(
                              AppBorderRadius.sm,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                l10n.firebaseServicesStatus,
                                style: theme.textTheme.titleSmall?.copyWith(
                                  fontWeight: AppTypography.fontWeightBold,
                                ),
                              ),
                              const SizedBox(height: AppSpacing.sm),
                              ..._buildFirebaseStatusWidgets(),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              const SizedBox(height: AppSpacing.lg),
            ],

            // Account Actions Section
            _buildSectionHeader(context, 'Account Actions'),
            const SizedBox(height: AppSpacing.sm),

            // Sign Out Button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _signOut(ref, context),
                icon: Icon(Icons.logout, color: theme.colorScheme.error),
                label: Text(
                  l10n.signOut,
                  style: TextStyle(color: theme.colorScheme.error),
                ),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: theme.colorScheme.error),
                  padding: const EdgeInsets.all(AppSpacing.md),
                ),
              ),
            ),

            const SizedBox(height: AppSpacing.sm),

            // Delete Account Button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _showDeleteAccountDialog(context),
                icon: Icon(
                  Icons.delete_forever,
                  color: theme.colorScheme.error,
                ),
                label: Text(
                  'Delete Account',
                  style: TextStyle(color: theme.colorScheme.error),
                ),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: theme.colorScheme.error),
                  padding: const EdgeInsets.all(AppSpacing.md),
                ),
              ),
            ),

            const SizedBox(height: AppSpacing.xl),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final theme = Theme.of(context);
    return Text(
      title,
      style: theme.textTheme.titleMedium?.copyWith(
        fontWeight: AppTypography.fontWeightMedium,
        color: theme.colorScheme.primary,
      ),
    );
  }

  Widget _buildSettingsItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return Card(
      elevation: AppElevation.sm,
      margin: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: ListTile(
        leading: Icon(icon, color: theme.colorScheme.primary),
        title: Text(
          title,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: AppTypography.fontWeightMedium,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        trailing: Icon(
          Icons.chevron_right,
          color: theme.colorScheme.onSurfaceVariant,
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
        ),
      ),
    );
  }
}
