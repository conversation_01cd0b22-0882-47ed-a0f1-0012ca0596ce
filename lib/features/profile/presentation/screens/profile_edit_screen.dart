import 'package:budapp/features/profile/providers/profile_providers.dart';
import 'package:budapp/features/profile/services/profile_validators.dart';
import 'package:budapp/services/error_service.dart';
import 'package:budapp/widgets/common/app_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Screen for editing user profile information (name and email)
class ProfileEditScreen extends ConsumerStatefulWidget {
  const ProfileEditScreen({super.key});

  @override
  ConsumerState<ProfileEditScreen> createState() => _ProfileEditScreenState();
}

class _ProfileEditScreenState extends ConsumerState<ProfileEditScreen> {
  final _formKey = GlobalKey<FormState>();
  final _displayNameController = TextEditingController();
  final _emailController = TextEditingController();

  bool _hasChanges = false;

  @override
  void dispose() {
    _displayNameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    // Initialize fields after the first frame to ensure ref is available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeFields();
    });
  }

  void _initializeFields() {
    ref.read(userProfileProvider).whenData((userProfile) {
      if (userProfile != null && mounted) {
        _displayNameController.text = userProfile.displayName ?? '';
        _emailController.text = userProfile.email ?? '';
      }
    });
  }

  void _onFieldChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final profileUpdateNotifier = ref.read(profileUpdateProvider.notifier);

      // Update display name if changed
      final currentProfile = await ref.read(userProfileProvider.future);
      if (currentProfile != null) {
        if (_displayNameController.text.trim() !=
            (currentProfile.displayName ?? '')) {
          await profileUpdateNotifier.updateDisplayName(
            _displayNameController.text.trim(),
          );
        }

        if (_emailController.text.trim() != (currentProfile.email ?? '')) {
          await profileUpdateNotifier.updateEmail(_emailController.text.trim());
        }
      }

      if (mounted) {
        ErrorService.showSuccessSnackBar(
          context,
          'Profile updated successfully',
        );
        context.pop();
      }
    } on Exception catch (e) {
      if (mounted) {
        ErrorService.showErrorSnackBar(context, e);
      }
    }
  }

  Future<bool> _onWillPop() async {
    if (!_hasChanges) return true;

    final shouldDiscard = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Discard Changes?'),
        content: const Text(
          'You have unsaved changes. Are you sure you want to discard them?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Discard'),
          ),
        ],
      ),
    );

    return shouldDiscard ?? false;
  }

  /// Handles navigation with unsaved changes check
  Future<void> _handleNavigation() async {
    if (_hasChanges) {
      final shouldDiscard = await _onWillPop();
      if (shouldDiscard && mounted) {
        context.pop();
      }
    } else {
      context.pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final userProfileAsync = ref.watch(userProfileProvider);
    final profileUpdateState = ref.watch(profileUpdateProvider);
    final isLoading = profileUpdateState.isLoading;

    return PopScope(
      canPop: !_hasChanges,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          final navigator = Navigator.of(context);
          final shouldPop = await _onWillPop();
          if (shouldPop && mounted) {
            navigator.pop();
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Edit Profile'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: _handleNavigation,
          ),
          actions: [
            TextButton(
              onPressed: (_hasChanges && !isLoading) ? _handleSave : null,
              child: isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Save'),
            ),
          ],
        ),
        body: userProfileAsync.when(
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text('Error loading profile: $error'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => ref.invalidate(userProfileProvider),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
          data: (userProfile) {
            if (userProfile == null) {
              return const Center(child: Text('No profile data available'));
            }

            return SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const Text(
                        'Edit Your Profile',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 32),

                      // Display Name Field
                      AppTextFormField(
                        label: 'Display Name',
                        controller: _displayNameController,
                        validator: ProfileValidators.validateDisplayName,
                        enabled: !isLoading,
                        onChanged: (_) => _onFieldChanged(),
                        prefixIcon: const Icon(Icons.person),
                        isRequired: true,
                        useFloatingLabel: true,
                        textInputAction: TextInputAction.next,
                      ),
                      const SizedBox(height: 16),

                      // Email Field
                      AppTextFormField(
                        label: 'Email Address',
                        controller: _emailController,
                        validator: ProfileValidators.validateEmail,
                        enabled: !isLoading,
                        onChanged: (_) => _onFieldChanged(),
                        helperText:
                            'You will need to verify your new email address',
                        isRequired: true,
                        keyboardType: TextInputType.emailAddress,
                        useFloatingLabel: true,
                      ),
                      const SizedBox(height: 32),

                      // Save Button
                      ElevatedButton(
                        onPressed: (_hasChanges && !isLoading)
                            ? _handleSave
                            : null,
                        child: isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : const Text('Save Changes'),
                      ),
                      const SizedBox(height: 16),

                      // Cancel Button
                      TextButton(
                        onPressed: isLoading ? null : _handleNavigation,
                        child: const Text('Cancel'),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
