import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Service for handling comprehensive data cleanup during account deletion
class ProfileCleanupService {
  ProfileCleanupService(this._firestoreService);
  final FirestoreService _firestoreService;

  /// Delete all user data from Firestore collections
  Future<void> cleanupUserData(String userId) async {
    try {
      debugPrint('Starting cleanup for user: $userId');

      // Get all user subcollections
      final userDocRef = _firestoreService.collection('users').doc(userId);

      // Use a batch to ensure atomic deletion
      final batch = _firestoreService.batch();

      // Delete user subcollections
      await _deleteSubcollection(batch, userDocRef, 'accounts');
      await _deleteSubcollection(batch, userDocRef, 'transactions');
      await _deleteSubcollection(batch, userDocRef, 'categories');
      await _deleteSubcollection(batch, userDocRef, 'budgets');
      await _deleteSubcollection(batch, userDocRef, 'goals');
      await _deleteSubcollection(batch, userDocRef, 'tags');

      // Delete the main user document
      batch.delete(userDocRef);

      // Commit the batch
      await batch.commit();

      debugPrint('User data cleanup completed for: $userId');
    } on Exception catch (e) {
      debugPrint('Error during user data cleanup: $e');
      rethrow;
    }
  }

  /// Delete all documents in a subcollection
  Future<void> _deleteSubcollection(
    WriteBatch batch,
    DocumentReference userDocRef,
    String subcollectionName,
  ) async {
    try {
      final subcollectionRef = userDocRef.collection(subcollectionName);
      final snapshot = await subcollectionRef.get();

      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      debugPrint(
        'Marked $subcollectionName for deletion: ${snapshot.docs.length} documents',
      );
    } on Exception catch (e) {
      debugPrint('Error deleting subcollection $subcollectionName: $e');
      // Don't rethrow here to allow other subcollections to be processed
    }
  }

  /// Clear local storage and session data
  Future<void> clearLocalData() async {
    try {
      // This would typically clear SharedPreferences, SecureStorage, etc.
      // Implementation depends on what local storage is used in the app
      debugPrint('Local data cleared');
    } on Exception catch (e) {
      debugPrint('Error clearing local data: $e');
      // Don't rethrow as this is not critical for account deletion
    }
  }

  /// Perform complete cleanup (Firestore + local data)
  Future<void> performCompleteCleanup(String userId) async {
    await cleanupUserData(userId);
    await clearLocalData();
  }

  /// Get user data size for confirmation (optional)
  Future<Map<String, int>> getUserDataSize(String userId) async {
    final userDocRef = _firestoreService.collection('users').doc(userId);
    final dataSizes = <String, int>{};

    try {
      // Count documents in each subcollection
      final subcollections = [
        'accounts',
        'transactions',
        'categories',
        'budgets',
        'goals',
        'tags',
      ];

      for (final subcollection in subcollections) {
        final snapshot = await userDocRef
            .collection(subcollection)
            .count()
            .get();
        dataSizes[subcollection] = snapshot.count ?? 0;
      }

      return dataSizes;
    } on Exception catch (e) {
      debugPrint('Error getting user data size: $e');
      return {};
    }
  }
}
