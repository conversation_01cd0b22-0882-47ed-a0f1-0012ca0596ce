/// Validation functions for profile-related form inputs
// ignore: avoid_classes_with_only_static_members
class ProfileValidators {
  /// Validate display name
  static String? validateDisplayName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Display name is required';
    }

    final trimmedValue = value.trim();

    if (trimmedValue.length < 2) {
      return 'Display name must be at least 2 characters long';
    }

    if (trimmedValue.length > 50) {
      return 'Display name must be less than 50 characters';
    }

    // Check for allowed characters (letters, numbers, spaces, hyphens, underscores)
    final allowedPattern = RegExp(r'^[a-zA-Z0-9\s\-_]+$');
    if (!allowedPattern.hasMatch(trimmedValue)) {
      return 'Display name can only contain letters, numbers, spaces, hyphens, and underscores';
    }

    return null;
  }

  /// Validate email format
  static String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }

    final trimmedValue = value.trim();

    // Basic email regex pattern
    final emailPattern = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );

    if (!emailPattern.hasMatch(trimmedValue)) {
      return 'Please enter a valid email address';
    }

    if (trimmedValue.length > 254) {
      return 'Email address is too long';
    }

    return null;
  }

  /// Validate password strength
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }

    if (value.length < 8) {
      return 'Password must be at least 8 characters long';
    }

    if (value.length > 128) {
      return 'Password must be less than 128 characters';
    }

    // Check for at least one uppercase letter
    if (!RegExp('[A-Z]').hasMatch(value)) {
      return 'Password must contain at least one uppercase letter';
    }

    // Check for at least one lowercase letter
    if (!RegExp('[a-z]').hasMatch(value)) {
      return 'Password must contain at least one lowercase letter';
    }

    // Check for at least one digit
    if (!RegExp('[0-9]').hasMatch(value)) {
      return 'Password must contain at least one number';
    }

    // Check for at least one special character
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
      return 'Password must contain at least one special character';
    }

    return null;
  }

  /// Validate password confirmation
  static String? validatePasswordConfirmation(
    String? value,
    String? originalPassword,
  ) {
    if (value == null || value.isEmpty) {
      return 'Password confirmation is required';
    }

    if (value != originalPassword) {
      return 'Passwords do not match';
    }

    return null;
  }

  /// Validate current password (for re-authentication)
  static String? validateCurrentPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Current password is required';
    }

    return null;
  }

  /// Get password strength score (0-4)
  static int getPasswordStrength(String password) {
    var score = 0;

    if (password.length >= 8) score++;
    if (RegExp('[A-Z]').hasMatch(password)) score++;
    if (RegExp('[a-z]').hasMatch(password)) score++;
    if (RegExp('[0-9]').hasMatch(password)) score++;
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) score++;

    return score;
  }

  /// Get password strength description
  static String getPasswordStrengthDescription(int strength) {
    switch (strength) {
      case 0:
      case 1:
        return 'Very Weak';
      case 2:
        return 'Weak';
      case 3:
        return 'Fair';
      case 4:
        return 'Good';
      case 5:
        return 'Strong';
      default:
        return 'Unknown';
    }
  }

  /// Get password strength color (for UI indicators)
  static String getPasswordStrengthColor(int strength) {
    switch (strength) {
      case 0:
      case 1:
        return 'red';
      case 2:
        return 'orange';
      case 3:
        return 'yellow';
      case 4:
      case 5:
        return 'green';
      default:
        return 'gray';
    }
  }

  /// Validate form fields together (for profile edit form)
  static Map<String, String?> validateProfileForm({
    required String? displayName,
    required String? email,
  }) {
    return {
      'displayName': validateDisplayName(displayName),
      'email': validateEmail(email),
    };
  }

  /// Validate password change form
  static Map<String, String?> validatePasswordChangeForm({
    required String? currentPassword,
    required String? newPassword,
    required String? confirmPassword,
  }) {
    return {
      'currentPassword': validateCurrentPassword(currentPassword),
      'newPassword': validatePassword(newPassword),
      'confirmPassword': validatePasswordConfirmation(
        confirmPassword,
        newPassword,
      ),
    };
  }
}
