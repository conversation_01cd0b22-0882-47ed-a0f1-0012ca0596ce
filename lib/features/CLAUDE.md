# Features Directory - CLAUDE.md

This directory contains feature-based modules following clean architecture principles.

## Directory Structure & Purpose

Each feature module is organized as:
```
feature_name/
├── presentation/       # UI layer (screens, widgets)
├── providers/         # Riverpod state management
└── services/          # Business logic and validation
```

## Current Features

### Core Features (Complete)
- **auth/** - Authentication system with email/password and biometric support
- **accounts/** - Account management with full CRUD operations  
- **transactions/** - Transaction processing with balance updates
- **budgets/** - Budget management with period tracking
- **categories/** - Hierarchical category system
- **tags/** - Tagging system with many-to-many relationships
- **goals/** - Goal tracking with contribution management

### Supporting Features
- **common/** - Shared models and widgets (time periods)
- **dashboard/** - Home screen and overview widgets
- **profile/** - User profile management
- **settings/** - App configuration screens

## Architecture Guidelines

### Key Patterns
- Use `ConsumerWidget`/`ConsumerStatefulWidget` for Riverpod integration
- Repository pattern with dependency injection via Riverpod providers
- Immutable data models with Freezed
- Feature isolation - each feature should be self-contained

### Presentation Layer Rules
- Only access data through services/repository interfaces
- Never directly access Firebase services from UI components
- Use AsyncNotifier pattern for complex state management
- Implement proper error handling with AsyncValue.guard()

## Agent Notes

When working in this directory:
1. Maintain feature isolation - don't cross-reference between features unnecessarily
2. Follow the established patterns in existing features
3. Use the repository interfaces defined in `lib/data/repositories/interfaces/`
4. Ensure proper validation using feature-specific validator services
5. Test new features following patterns in corresponding test directories

## Recent Changes & Context

- All features now use unified form architecture via `lib/widgets/forms/`
- Material 3 design system implemented across all UI components
- Production-safe logging implemented with PII protection
- Multi-environment support (dev/staging/prod) configured