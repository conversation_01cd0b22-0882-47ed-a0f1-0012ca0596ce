import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/categories/services/category_deletion_service.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'category_providers.g.dart';

/// Helper function to get authenticated user or throw exception
String _getAuthenticatedUserId(Ref ref) {
  final authService = ref.watch(authServiceProvider);
  final user = authService.currentUser;
  if (user == null) throw Exception('User not authenticated');
  return user.uid;
}

/// Provider for the list of all user categories (real-time)
final categoryListProvider = StreamProvider<List<Category>>((ref) {
  final userId = _getAuthenticatedUserId(ref);
  final categoryRepository = ref.watch(categoryRepositoryProvider);
  return categoryRepository.watchUserCategories(userId);
});

/// Provider for a specific category by ID (using stream)
final StreamProviderFamily<Category?, String> categoryProvider =
    StreamProvider.family<Category?, String>((
      ref,
      categoryId,
    ) {
      final userId = _getAuthenticatedUserId(ref);
      final categoryRepository = ref.watch(categoryRepositoryProvider);
      return categoryRepository.watchCategoryForUser(userId, categoryId);
    });

/// Provider for active categories only
final activeCategoriesProvider = FutureProvider<List<Category>>((ref) async {
  final categories = await ref.watch(categoryListProvider.future);
  return categories.where((category) => category.isActive).toList();
});

/// Provider for income categories
final incomeCategoriesProvider = FutureProvider<List<Category>>((ref) async {
  final categories = await ref.watch(categoryListProvider.future);
  return categories
      .where(
        (category) => category.type == CategoryType.income && category.isActive,
      )
      .toList();
});

/// Provider for expense categories
final expenseCategoriesProvider = FutureProvider<List<Category>>((ref) async {
  final categories = await ref.watch(categoryListProvider.future);
  return categories
      .where(
        (category) =>
            category.type == CategoryType.expense && category.isActive,
      )
      .toList();
});

/// Provider for root categories (no parent)
final rootCategoriesProvider = FutureProvider<List<Category>>((ref) async {
  final categories = await ref.watch(categoryListProvider.future);
  return categories
      .where((category) => category.isRoot && category.isActive)
      .toList();
});

/// Provider for root categories filtered by type
final FutureProviderFamily<List<Category>, CategoryType>
rootCategoriesByTypeProvider =
    FutureProvider.family<List<Category>, CategoryType>((ref, type) async {
      final categories = await ref.watch(categoryListProvider.future);
      return categories
          .where(
            (category) =>
                category.parentId == null &&
                category.type == type &&
                category.isActive,
          )
          .toList();
    });

/// Provider for category statistics
final categoryStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final userId = _getAuthenticatedUserId(ref);
  final categoryRepository = ref.watch(categoryRepositoryProvider);
  return categoryRepository.getUserCategorySummary(userId);
});

/// AsyncNotifier for category creation
@riverpod
class CategoryCreateNotifier extends _$CategoryCreateNotifier {
  @override
  FutureOr<void> build() {}

  /// Create new category with error handling
  Future<void> createCategory(Category category) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final categoryRepository = ref.read(categoryRepositoryProvider);
      await categoryRepository.createCategory(category);

      // Invalidate the category list to refresh the UI
      _invalidateCategoryProviders();
    });
  }

  /// Helper method to invalidate common category providers
  void _invalidateCategoryProviders({String? categoryId}) {
    ref.invalidate(categoryListProvider);
    if (categoryId != null) {
      ref.invalidate(categoryProvider(categoryId));
    }
  }
}

/// AsyncNotifier for category updates
@riverpod
class CategoryUpdateNotifier extends _$CategoryUpdateNotifier {
  @override
  FutureOr<void> build() {}

  /// Update category with error handling
  Future<void> updateCategory(String categoryId, Category category) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final categoryRepository = ref.read(categoryRepositoryProvider);
      await categoryRepository.updateCategory(categoryId, category);

      // Invalidate providers to refresh the UI
      _invalidateBasicCategory(categoryId);
    });
  }

  /// Helper method to invalidate basic category providers
  void _invalidateBasicCategory(String categoryId) {
    ref
      ..invalidate(categoryListProvider)
      ..invalidate(categoryProvider(categoryId));
  }

  /// Deactivate category (soft delete)
  Future<void> deactivateCategory(String categoryId, Category category) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final categoryRepository = ref.read(categoryRepositoryProvider);
      final deactivatedCategory = category.copyWith(
        isActive: false,
        updatedAt: DateTime.now(),
      );
      await categoryRepository.updateCategory(categoryId, deactivatedCategory);

      // Invalidate providers to refresh the UI
      _invalidateBasicCategory(categoryId);
    });
  }

  /// Reactivate category
  Future<void> reactivateCategory(String categoryId, Category category) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final categoryRepository = ref.read(categoryRepositoryProvider);
      final reactivatedCategory = category.copyWith(
        isActive: true,
        updatedAt: DateTime.now(),
      );
      await categoryRepository.updateCategory(categoryId, reactivatedCategory);

      // Invalidate providers to refresh the UI
      _invalidateBasicCategory(categoryId);
    });
  }
}

/// AsyncNotifier for category validation
@riverpod
class CategoryValidationNotifier extends _$CategoryValidationNotifier {
  @override
  FutureOr<bool> build() => true;

  /// Validate category name uniqueness
  Future<bool> validateCategoryName(
    String userId,
    String name, {
    String? excludeCategoryId,
  }) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final categoryRepository = ref.read(categoryRepositoryProvider);
      return categoryRepository.isCategoryNameUnique(
        userId,
        name,
        excludeCategoryId: excludeCategoryId,
      );
    });

    return !state.hasError && (state.value ?? false);
  }

  /// Validate complete category
  Future<bool> validateCategory(Category category) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final categoryRepository = ref.read(categoryRepositoryProvider);
      return categoryRepository.validateCategory(category);
    });

    return !state.hasError && (state.value ?? false);
  }
}

// Subcategory-specific providers

/// Provider for subcategories of a specific parent category (real-time)
final StreamProviderFamily<List<Category>, String> subcategoriesProvider =
    StreamProvider.family<List<Category>, String>((
      ref,
      parentId,
    ) {
      final userId = _getAuthenticatedUserId(ref);
      final categoryRepository = ref.watch(categoryRepositoryProvider);
      return categoryRepository.watchSubcategories(userId, parentId);
    });

/// Provider for subcategory count for a specific parent
final FutureProviderFamily<int, String> subcategoryCountProvider =
    FutureProvider.family<int, String>((
      ref,
      parentId,
    ) async {
      final userId = _getAuthenticatedUserId(ref);
      final categoryRepository = ref.watch(categoryRepositoryProvider);
      return categoryRepository.getSubcategoryCount(userId, parentId);
    });

/// Provider to check if a category can have subcategories
final FutureProviderFamily<bool, String> canHaveSubcategoriesProvider =
    FutureProvider.family<bool, String>((
      ref,
      categoryId,
    ) async {
      final userId = _getAuthenticatedUserId(ref);
      final categoryRepository = ref.watch(categoryRepositoryProvider);
      return categoryRepository.canHaveSubcategories(userId, categoryId);
    });

/// AsyncNotifier for subcategory creation
@riverpod
class SubcategoryCreateNotifier extends _$SubcategoryCreateNotifier {
  @override
  FutureOr<void> build() {}

  /// Create new subcategory with error handling
  Future<void> createSubcategory(Category subcategory, String parentId) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final categoryRepository = ref.read(categoryRepositoryProvider);
      await categoryRepository.createSubcategory(subcategory, parentId);

      // Invalidate relevant providers to refresh the UI
      _invalidateSubcategoryCreation(parentId);
    });
  }

  /// Helper method to invalidate providers after subcategory creation
  void _invalidateSubcategoryCreation(String parentId) {
    ref
      ..invalidate(categoryListProvider)
      ..invalidate(subcategoriesProvider(parentId))
      ..invalidate(subcategoryCountProvider(parentId));
  }
}

/// AsyncNotifier for subcategory updates
@riverpod
class SubcategoryUpdateNotifier extends _$SubcategoryUpdateNotifier {
  @override
  FutureOr<void> build() {}

  /// Update subcategory with error handling
  Future<void> updateSubcategory(
    String subcategoryId,
    Category subcategory,
  ) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final categoryRepository = ref.read(categoryRepositoryProvider);
      await categoryRepository.updateSubcategory(subcategoryId, subcategory);

      // Invalidate providers to refresh the UI
      _invalidateSubcategoryUpdate(subcategoryId, subcategory.parentId);
    });
  }

  /// Delete subcategory (soft delete)
  Future<void> deleteSubcategory(String subcategoryId, String parentId) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final categoryRepository = ref.read(categoryRepositoryProvider);
      await categoryRepository.deleteSubcategory(subcategoryId, parentId);

      // Invalidate providers to refresh the UI
      _invalidateSubcategoryDeletion(subcategoryId, parentId);
    });
  }

  /// Move subcategory to different parent
  Future<void> moveSubcategoryToParent(
    String subcategoryId,
    String oldParentId,
    String newParentId,
  ) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final categoryRepository = ref.read(categoryRepositoryProvider);
      await categoryRepository.moveSubcategoryToParent(
        subcategoryId,
        oldParentId,
        newParentId,
      );

      // Invalidate providers to refresh the UI
      _invalidateSubcategoryMove(subcategoryId, oldParentId, newParentId);
    });
  }

  /// Helper method to invalidate providers after subcategory update
  void _invalidateSubcategoryUpdate(String subcategoryId, String? parentId) {
    ref
      ..invalidate(categoryListProvider)
      ..invalidate(categoryProvider(subcategoryId));
    if (parentId != null) {
      ref
        ..invalidate(subcategoriesProvider(parentId))
        ..invalidate(subcategoryCountProvider(parentId));
    }
  }

  /// Helper method to invalidate providers after subcategory deletion
  void _invalidateSubcategoryDeletion(String subcategoryId, String parentId) {
    ref
      ..invalidate(categoryListProvider)
      ..invalidate(categoryProvider(subcategoryId))
      ..invalidate(subcategoriesProvider(parentId))
      ..invalidate(subcategoryCountProvider(parentId));
  }

  /// Helper method to invalidate providers after subcategory move
  void _invalidateSubcategoryMove(
    String subcategoryId,
    String oldParentId,
    String newParentId,
  ) {
    ref
      ..invalidate(categoryListProvider)
      ..invalidate(categoryProvider(subcategoryId))
      ..invalidate(subcategoriesProvider(oldParentId))
      ..invalidate(subcategoriesProvider(newParentId))
      ..invalidate(subcategoryCountProvider(oldParentId))
      ..invalidate(subcategoryCountProvider(newParentId));
  }
}

/// AsyncNotifier for subcategory validation
@riverpod
class SubcategoryValidationNotifier extends _$SubcategoryValidationNotifier {
  @override
  FutureOr<bool> build() => true;

  /// Validate subcategory name uniqueness within parent
  Future<bool> validateSubcategoryName(
    String userId,
    String parentId,
    String name, {
    String? excludeSubcategoryId,
  }) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final categoryRepository = ref.read(categoryRepositoryProvider);
      return categoryRepository.isSubcategoryNameUnique(
        userId,
        parentId,
        name,
        excludeSubcategoryId: excludeSubcategoryId,
      );
    });

    return state.value ?? false;
  }

  /// Validate subcategory hierarchy (prevent circular references)
  Future<bool> validateSubcategoryHierarchy(
    String parentId,
    String subcategoryId,
  ) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final categoryRepository = ref.read(categoryRepositoryProvider);
      return categoryRepository.validateSubcategoryHierarchy(
        parentId,
        subcategoryId,
      );
    });

    return state.value ?? false;
  }

  /// Get category depth in hierarchy
  Future<int> getCategoryDepth(String userId, String categoryId) async {
    final categoryRepository = ref.read(categoryRepositoryProvider);
    return categoryRepository.getCategoryDepth(userId, categoryId);
  }
}

/// Provider for CategoryDeletionService
///
/// This provider creates a CategoryDeletionService instance with proper
/// dependency injection for both category and transaction repositories.
final categoryDeletionServiceProvider = Provider<CategoryDeletionService>((
  ref,
) {
  final categoryRepository = ref.watch(categoryRepositoryProvider);
  final transactionRepository = ref.watch(transactionRepositoryProvider);
  return CategoryDeletionService(categoryRepository, transactionRepository);
});
