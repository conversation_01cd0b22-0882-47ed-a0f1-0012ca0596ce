// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$categoryCreateNotifierHash() =>
    r'3f6e3b281dc1aec56391925953bb1e40fbfb8f51';

/// AsyncNotifier for category creation
///
/// Copied from [CategoryCreateNotifier].
@ProviderFor(CategoryCreateNotifier)
final categoryCreateNotifierProvider =
    AutoDisposeAsyncNotifierProvider<CategoryCreateNotifier, void>.internal(
      CategoryCreateNotifier.new,
      name: r'categoryCreateNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$categoryCreateNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CategoryCreateNotifier = AutoDisposeAsyncNotifier<void>;
String _$categoryUpdateNotifierHash() =>
    r'cd0162c4e9ba343705b9c821c4f17d8072c8b5c1';

/// AsyncNotifier for category updates
///
/// Copied from [CategoryUpdateNotifier].
@ProviderFor(CategoryUpdateNotifier)
final categoryUpdateNotifierProvider =
    AutoDisposeAsyncNotifierProvider<CategoryUpdateNotifier, void>.internal(
      CategoryUpdateNotifier.new,
      name: r'categoryUpdateNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$categoryUpdateNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CategoryUpdateNotifier = AutoDisposeAsyncNotifier<void>;
String _$categoryValidationNotifierHash() =>
    r'a0a4e76960f6e58662dcf09d0bf2ef1f8931e823';

/// AsyncNotifier for category validation
///
/// Copied from [CategoryValidationNotifier].
@ProviderFor(CategoryValidationNotifier)
final categoryValidationNotifierProvider =
    AutoDisposeAsyncNotifierProvider<CategoryValidationNotifier, bool>.internal(
      CategoryValidationNotifier.new,
      name: r'categoryValidationNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$categoryValidationNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CategoryValidationNotifier = AutoDisposeAsyncNotifier<bool>;
String _$subcategoryCreateNotifierHash() =>
    r'94827deffa3afac0200d7163d69c0636980d06ad';

/// AsyncNotifier for subcategory creation
///
/// Copied from [SubcategoryCreateNotifier].
@ProviderFor(SubcategoryCreateNotifier)
final subcategoryCreateNotifierProvider =
    AutoDisposeAsyncNotifierProvider<SubcategoryCreateNotifier, void>.internal(
      SubcategoryCreateNotifier.new,
      name: r'subcategoryCreateNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subcategoryCreateNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SubcategoryCreateNotifier = AutoDisposeAsyncNotifier<void>;
String _$subcategoryUpdateNotifierHash() =>
    r'cb9add60e37969ecf05acc3b7a2202312cbb2f3c';

/// AsyncNotifier for subcategory updates
///
/// Copied from [SubcategoryUpdateNotifier].
@ProviderFor(SubcategoryUpdateNotifier)
final subcategoryUpdateNotifierProvider =
    AutoDisposeAsyncNotifierProvider<SubcategoryUpdateNotifier, void>.internal(
      SubcategoryUpdateNotifier.new,
      name: r'subcategoryUpdateNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subcategoryUpdateNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SubcategoryUpdateNotifier = AutoDisposeAsyncNotifier<void>;
String _$subcategoryValidationNotifierHash() =>
    r'b7d8c77ea4ae84a2b2d6572fb8f575ebd23b3035';

/// AsyncNotifier for subcategory validation
///
/// Copied from [SubcategoryValidationNotifier].
@ProviderFor(SubcategoryValidationNotifier)
final subcategoryValidationNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      SubcategoryValidationNotifier,
      bool
    >.internal(
      SubcategoryValidationNotifier.new,
      name: r'subcategoryValidationNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subcategoryValidationNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SubcategoryValidationNotifier = AutoDisposeAsyncNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
