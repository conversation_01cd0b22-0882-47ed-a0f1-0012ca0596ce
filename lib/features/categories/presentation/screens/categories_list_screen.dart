import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/categories/presentation/widgets/category_deletion_dialog.dart';
import 'package:budapp/features/categories/presentation/widgets/category_tree_widget.dart';
import 'package:budapp/features/categories/presentation/widgets/empty_categories_state.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/features/common/widgets/app_bar_helpers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Main screen for displaying and managing categories
class CategoriesListScreen extends ConsumerStatefulWidget {
  const CategoriesListScreen({super.key});

  @override
  ConsumerState<CategoriesListScreen> createState() =>
      _CategoriesListScreenState();
}

class _CategoriesListScreenState extends ConsumerState<CategoriesListScreen> {
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBarHelpers.createStandardScrollableAppBar(
          title: l10n.categories,
          actions: [
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => context.push('/categories/create'),
              tooltip: 'Add Category',
            ),
          ],
          bottom: TabBar(
            tabs: [
              // Expenses tab first (moved to left position)
              Tab(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.trending_down),
                    const SizedBox(width: AppSpacing.xs),
                    Text(l10n.expense),
                  ],
                ),
              ),
              // Income tab second (moved to right position)
              Tab(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.trending_up),
                    const SizedBox(width: AppSpacing.xs),
                    Text(l10n.income),
                  ],
                ),
              ),
            ],
          ),
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(categoryListProvider);
          },
          child: TabBarView(
            children: [
              // Reordered to match tab order: expenses first, income second
              _buildCategoriesTab(CategoryType.expense),
              _buildCategoriesTab(CategoryType.income),
            ],
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () => context.push('/categories/create'),
          tooltip: 'Add Category',
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildCategoriesTab(CategoryType type) {
    final categoriesAsync = ref.watch(categoryListProvider);

    return categoriesAsync.when(
      data: (categories) {
        // Filter categories by type only
        final filteredCategories = categories
            .where((category) => category.type == type)
            .toList();

        if (filteredCategories.isEmpty) {
          return const EmptyCategoriesState();
        }

        return _buildCategoriesList(filteredCategories);
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorState(error),
    );
  }

  Widget _buildCategoriesList(List<Category> categories) {
    // Build category tree structure
    final rootCategories = categories.where((c) => c.isRoot).toList();
    final subcategoriesMap = <String, List<Category>>{};

    for (final category in categories.where((c) => c.isSubcategory)) {
      final parentId = category.parentId!;
      subcategoriesMap.putIfAbsent(parentId, () => []).add(category);
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(categoryListProvider);
      },
      child: SingleChildScrollView(
        child: Column(
          children: [
            // Top spacing between tabs and categories
            const SizedBox(height: AppSpacing.sm),

            // Categories list
            ...rootCategories.map((category) {
              final subcategories = subcategoriesMap[category.id] ?? [];

              return CategoryTreeWidget(
                category: category,
                subcategories: subcategories,
                onCategoryTap: _onCategoryTap,
                onSubcategoryTap: _onCategoryTap,
                onCategoryMenuAction: _onCategoryMenuAction,
                onSubcategoryMenuAction: _onCategoryMenuAction,
              );
            }),

            // Bottom padding
            const SizedBox(height: AppSpacing.xl),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
          const SizedBox(height: AppSpacing.md),
          Text(
            l10n.errorLoadingCategories,
            style: theme.textTheme.headlineSmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            error.toString(),
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.md),
          ElevatedButton(
            onPressed: () => ref.invalidate(categoryListProvider),
            child: Text(l10n.retry),
          ),
        ],
      ),
    );
  }

  void _onCategoryTap(Category category) {
    // Navigate to filtered transactions view for this category
    context.push('/transactions/category/${category.id}');
  }

  void _onCategoryMenuAction(Category category, String action) {
    switch (action) {
      case 'edit':
        // Navigate to edit category screen
        context.push('/categories/${category.id}/edit');
      case 'add_subcategory':
        // Navigate to create subcategory screen with parent
        context.push(
          '/categories/subcategories/create?parentId=${category.id}',
        );
      case 'activate':
      case 'deactivate':
        _toggleCategoryStatus(category);
      case 'delete':
        _showGuidedDeletion(category);
    }
  }

  void _toggleCategoryStatus(Category category) {
    final categoryNotifier = ref.read(categoryUpdateNotifierProvider.notifier);
    final updatedCategory = category.copyWith(
      isActive: !category.isActive,
      updatedAt: DateTime.now(),
    );

    categoryNotifier.updateCategory(category.id, updatedCategory);

    final statusText = category.isActive ? 'deactivated' : 'activated';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Category "${category.name}" has been $statusText'),
        action: SnackBarAction(
          label: 'Undo',
          onPressed: () {
            // Revert the change
            ref
                .read(categoryUpdateNotifierProvider.notifier)
                .updateCategory(category.id, category);
          },
        ),
      ),
    );
  }

  void _showGuidedDeletion(Category category) {
    showDialog<void>(
      context: context,
      builder: (context) => CategoryDeletionDialog(
        category: category,
        onDeleted: () {
          // Refresh the categories list
          ref.invalidate(categoryListProvider);
        },
      ),
    );
  }
}
