import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/widgets/forms/configs/category_form_config.dart';
import 'package:budapp/widgets/forms/screens/generic_form_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Unified category editing screen using the generic form system
/// Handles both root categories and subcategories automatically
class CategoryEditScreen extends ConsumerWidget {
  const CategoryEditScreen({super.key, required this.categoryId});
  final String categoryId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final repository = ref.watch(categoryRepositoryProvider);
    final categoryAsync = ref.watch(categoryProvider(categoryId));

    return categoryAsync.when(
      data: (category) {
        if (category == null) {
          return Scaffold(
            appBar: AppBar(title: const Text('Category Not Found')),
            body: const Center(
              child: Text('The requested category could not be found.'),
            ),
          );
        }

        final config = CategoryFormConfig.edit(
          category: category,
          repository: repository,
          onFieldChanged: (fieldKey, value) {
            // Optional: Handle field changes for real-time validation or updates
            debugPrint('Category field $fieldKey changed to: $value');
          },
        );

        return GenericFormScreen<Category>(config: config);
      },
      loading: () => Scaffold(
        appBar: AppBar(title: const Text('Edit Category')),
        body: const Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(child: Text('Error loading category: $error')),
      ),
    );
  }
}
