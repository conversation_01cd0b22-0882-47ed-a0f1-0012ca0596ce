import 'package:budapp/data/models/category.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/widgets/forms/configs/category_form_config.dart';
import 'package:budapp/widgets/forms/screens/generic_form_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Unified category creation screen using the generic form system
/// Handles both root categories and subcategories based on parentId parameter
class CategoryCreateScreen extends ConsumerWidget {
  const CategoryCreateScreen({super.key, this.parentId});
  final String? parentId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final repository = ref.watch(categoryRepositoryProvider);

    // Extract parentId from query parameters if not provided directly
    // Handle case where GoRouter context is not available (e.g., in tests)
    var routeParentId = parentId;
    try {
      routeParentId =
          parentId ?? GoRouterState.of(context).uri.queryParameters['parentId'];
    } on Exception {
      // GoRouter context not available, use provided parentId or null
      routeParentId = parentId;
    }

    final config = CategoryFormConfig.create(
      repository: repository,
      parentId: routeParentId,
      onFieldChanged: (fieldKey, value) {
        // Optional: Handle field changes for real-time validation or updates
        debugPrint('Category field $fieldKey changed to: $value');
      },
    );

    return GenericFormScreen<Category>(config: config);
  }
}
