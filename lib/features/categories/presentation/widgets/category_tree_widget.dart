import 'dart:math' as math;

import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/categories/presentation/widgets/category_card.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';

/// Widget for displaying categories in a hierarchical tree structure
class CategoryTreeWidget extends StatefulWidget {
  const CategoryTreeWidget({
    super.key,
    required this.category,
    required this.subcategories,
    this.onCategoryTap,
    this.onSubcategoryTap,
    this.onCategoryMenuAction,
    this.onSubcategoryMenuAction,
    this.initiallyExpanded = false,
  });

  final Category category;
  final List<Category> subcategories;
  final void Function(Category)? onCategoryTap;
  final void Function(Category)? onSubcategoryTap;
  final void Function(Category, String)? onCategoryMenuAction;
  final void Function(Category, String)? onSubcategoryMenuAction;
  final bool initiallyExpanded;

  @override
  State<CategoryTreeWidget> createState() => _CategoryTreeWidgetState();
}

class _CategoryTreeWidgetState extends State<CategoryTreeWidget>
    with SingleTickerProviderStateMixin {
  late bool _isExpanded;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    if (_isExpanded) {
      _animationController.value = 1;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Column(
      children: [
        // Parent category
        _buildParentCategory(theme, l10n),

        // Subcategories (expandable)
        if (widget.subcategories.isNotEmpty)
          AnimatedBuilder(
            animation: _expandAnimation,
            builder: (context, child) {
              return ClipRect(
                child: Align(
                  alignment: Alignment.topCenter,
                  heightFactor: _expandAnimation.value,
                  child: child,
                ),
              );
            },
            child: _buildSubcategories(),
          ),
      ],
    );
  }

  Widget _buildParentCategory(ThemeData theme, AppLocalizations l10n) {
    return Stack(
      children: [
        // Category card
        CategoryCard(
          category: widget.category,
          subcategoryCount: widget.subcategories.length,
          depth: 0,
          onTap: () => widget.onCategoryTap?.call(widget.category),
          onMenuAction: (action) =>
              widget.onCategoryMenuAction?.call(widget.category, action),
        ),

        // Expand/collapse button (if has subcategories)
        if (widget.subcategories.isNotEmpty)
          Positioned(
            left: AppSpacing.md - 4,
            top: 0,
            bottom: 0,
            child: Center(child: _buildExpandButton(theme, l10n)),
          ),
      ],
    );
  }

  Widget _buildExpandButton(ThemeData theme, AppLocalizations l10n) {
    return GestureDetector(
      onTap: _toggleExpanded,
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          border: Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.3),
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: AnimatedRotation(
          turns: _isExpanded ? 0.25 : 0,
          duration: const Duration(milliseconds: 200),
          child: Icon(
            Icons.chevron_right,
            size: 16,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ),
    );
  }

  Widget _buildSubcategories() {
    return Column(
      children: [
        // Connection lines
        _buildConnectionLines(),

        // Subcategory cards
        ...widget.subcategories.map((subcategory) {
          return CategoryCard(
            category: subcategory,
            depth: 1,
            onTap: () => widget.onSubcategoryTap?.call(subcategory),
            onMenuAction: (action) =>
                widget.onSubcategoryMenuAction?.call(subcategory, action),
          );
        }),
      ],
    );
  }

  Widget _buildConnectionLines() {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(left: AppSpacing.md + 12),
      child: Column(
        children: widget.subcategories.asMap().entries.map((entry) {
          final index = entry.key;
          final isLast = index == widget.subcategories.length - 1;

          return SizedBox(
            height: 20,
            child: Row(
              children: [
                // Vertical line
                Container(
                  width: 1,
                  height: isLast ? 10 : 20,
                  color: theme.colorScheme.outline.withValues(alpha: 0.3),
                ),

                // Horizontal line
                Container(
                  width: AppSpacing.md,
                  height: 1,
                  margin: const EdgeInsets.only(top: 10),
                  color: theme.colorScheme.outline.withValues(alpha: 0.3),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }
}

/// Simplified category tree widget for read-only display
class SimpleCategoryTreeWidget extends StatelessWidget {
  const SimpleCategoryTreeWidget({
    super.key,
    required this.categoryTree,
    this.onCategoryTap,
    this.maxDepth = 3,
  });

  final CategoryTree categoryTree;
  final void Function(Category)? onCategoryTap;
  final int maxDepth;

  @override
  Widget build(BuildContext context) {
    return _buildCategoryNode(categoryTree, 0);
  }

  Widget _buildCategoryNode(CategoryTree node, int depth) {
    if (depth > maxDepth) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        // Current category
        CategoryCard(
          category: node.category,
          subcategoryCount: node.children.length,
          depth: depth,
          onTap: () => onCategoryTap?.call(node.category),
          showMenuButton: false,
        ),

        // Children (if any)
        ...node.children.map((child) => _buildCategoryNode(child, depth + 1)),
      ],
    );
  }
}

/// Category tree statistics widget
class CategoryTreeStats extends StatelessWidget {
  const CategoryTreeStats({super.key, required this.categoryTree});

  final CategoryTree categoryTree;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    final totalCount = categoryTree.totalCount;
    final maxDepth = _calculateMaxDepth(categoryTree);

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            l10n.categories,
            totalCount.toString(),
            Icons.category,
            theme,
          ),
          _buildStatItem(
            'Depth',
            maxDepth.toString(),
            Icons.account_tree,
            theme,
          ),
          _buildStatItem(
            l10n.subcategories,
            (totalCount - 1).toString(),
            Icons.subdirectory_arrow_right,
            theme,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    ThemeData theme,
  ) {
    return Column(
      children: [
        Icon(icon, color: theme.colorScheme.primary),
        const SizedBox(height: AppSpacing.xs),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  int _calculateMaxDepth(CategoryTree tree) {
    if (tree.children.isEmpty) {
      return 1;
    }

    var maxChildDepth = 0;
    for (final child in tree.children) {
      maxChildDepth = math.max(maxChildDepth, _calculateMaxDepth(child));
    }

    return 1 + maxChildDepth;
  }
}
