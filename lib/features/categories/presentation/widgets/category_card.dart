import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/utils/color_utils.dart';
import 'package:budapp/utils/icon_utils.dart';
import 'package:budapp/widgets/common/app_text.dart';
import 'package:flutter/material.dart';

/// Widget for displaying individual category information
class CategoryCard extends StatelessWidget {
  const CategoryCard({
    super.key,
    required this.category,
    this.subcategoryCount = 0,
    this.depth = 0,
    this.onTap,
    this.onMenuAction,
    this.showMenuButton = true,
  });

  final Category category;
  final int subcategoryCount;
  final int depth;
  final VoidCallback? onTap;
  final void Function(String action)? onMenuAction;
  final bool showMenuButton;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Container(
      margin: EdgeInsets.only(
        left: AppSpacing.md + (depth * AppSpacing.lg),
        right: AppSpacing.md,
        bottom: 8, // 8px spacing between cards
      ),
      child: Card(
        elevation: AppElevation.sm,
        margin: EdgeInsets.zero,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: Row(
              children: [
                // Category icon (large, colored)
                _buildCategoryIcon(theme),

                const SizedBox(width: AppSpacing.md),

                // Category information
                Expanded(child: _buildCategoryInfo(theme, l10n)),

                // Menu button
                if (showMenuButton) ...[
                  const SizedBox(width: AppSpacing.sm),
                  _buildMenuButton(context, l10n),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryIcon(ThemeData theme) {
    final iconData = _getIconData();
    final categoryColor = _getCategoryColor(theme);

    return Icon(
      iconData,
      color: categoryColor,
      size:
          48, // Large icon size to make it as big as possible for default card size
    );
  }

  Widget _buildCategoryInfo(ThemeData theme, AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Category name
        Text(
          category.name,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: category.isActive
                ? theme.colorScheme.onSurface
                : theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
        ),

        // Category description (if available)
        if (category.description != null &&
            category.description!.isNotEmpty) ...[
          const SizedBox(height: AppSpacing.xs),
          AppText.note(
            category.description!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],

        // Subcategory count (if any)
        if (subcategoryCount > 0) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            l10n.subcategoryCount(subcategoryCount),
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildMenuButton(BuildContext context, AppLocalizations l10n) {
    return PopupMenuButton<String>(
      onSelected: onMenuAction,
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              const Icon(Icons.edit, size: 18),
              const SizedBox(width: AppSpacing.sm),
              Text(l10n.editCategory),
            ],
          ),
        ),
        if (category.isRoot) ...[
          PopupMenuItem(
            value: 'add_subcategory',
            child: Row(
              children: [
                const Icon(Icons.add, size: 18),
                const SizedBox(width: AppSpacing.sm),
                Text(l10n.addSubcategory),
              ],
            ),
          ),
        ],
        PopupMenuItem(
          value: category.isActive ? 'deactivate' : 'activate',
          child: Row(
            children: [
              Icon(
                category.isActive ? Icons.visibility_off : Icons.visibility,
                size: 18,
              ),
              const SizedBox(width: AppSpacing.sm),
              Text(category.isActive ? l10n.deactivateCategory : l10n.activate),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(
                Icons.delete,
                size: 18,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(width: AppSpacing.sm),
              Text(
                l10n.deleteCategory,
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            ],
          ),
        ),
      ],
      child: Icon(
        Icons.more_vert,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
    );
  }

  IconData _getIconData() {
    if (category.icon != null && category.icon!.isNotEmpty) {
      return iconFromName(category.icon!);
    }

    // Default icons based on category type
    return category.type == CategoryType.income
        ? Icons.trending_up
        : Icons.trending_down;
  }

  Color _getCategoryColor(ThemeData theme) {
    if (category.color != null && category.color!.isNotEmpty) {
      try {
        return parseHex(category.color!);
      } on Exception {
        // Fall back to default color if parsing fails
      }
    }

    // Default colors based on category type
    return category.type == CategoryType.income
        ? AppColors.success
        : AppColors.error;
  }
}
