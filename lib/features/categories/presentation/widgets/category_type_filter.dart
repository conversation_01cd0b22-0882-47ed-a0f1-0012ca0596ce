import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';

/// Bottom sheet widget for filtering categories by type
class CategoryTypeFilter extends StatefulWidget {
  const CategoryTypeFilter({
    super.key,
    this.selectedType,
    this.showInactive = false,
    this.onTypeChanged,
    this.onShowInactiveChanged,
  });

  final CategoryType? selectedType;
  final bool showInactive;
  final void Function(CategoryType?)? onTypeChanged;
  final void Function({required bool showInactive})? onShowInactiveChanged;

  @override
  State<CategoryTypeFilter> createState() => _CategoryTypeFilterState();
}

class _CategoryTypeFilterState extends State<CategoryTypeFilter> {
  late CategoryType? _selectedType;
  late bool _showInactive;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.selectedType;
    _showInactive = widget.showInactive;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Container(
      padding: EdgeInsets.only(
        left: AppSpacing.md,
        right: AppSpacing.md,
        top: AppSpacing.md,
        bottom: MediaQuery.of(context).viewInsets.bottom + AppSpacing.md,
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppBorderRadius.lg),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(theme, l10n),
          const SizedBox(height: AppSpacing.lg),

          // Category type filter
          _buildCategoryTypeSection(theme, l10n),
          const SizedBox(height: AppSpacing.lg),

          // Show inactive toggle
          _buildShowInactiveSection(theme, l10n),
          const SizedBox(height: AppSpacing.xl),

          // Action buttons
          _buildActionButtons(theme, l10n),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, AppLocalizations l10n) {
    return Row(
      children: [
        Expanded(
          child: Text(
            l10n.filterCategories,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
          tooltip: l10n.close,
        ),
      ],
    );
  }

  Widget _buildCategoryTypeSection(ThemeData theme, AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.categoryType,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppSpacing.md),

        // Type options
        Wrap(
          spacing: AppSpacing.sm,
          children: [
            _buildFilterChip(
              label: l10n.allCategories,
              isSelected: _selectedType == null,
              onSelected: () => setState(() => _selectedType = null),
              theme: theme,
            ),
            _buildFilterChip(
              label: l10n.incomeCategories,
              isSelected: _selectedType == CategoryType.income,
              onSelected: () =>
                  setState(() => _selectedType = CategoryType.income),
              theme: theme,
              color: AppColors.success,
            ),
            _buildFilterChip(
              label: l10n.expenseCategories,
              isSelected: _selectedType == CategoryType.expense,
              onSelected: () =>
                  setState(() => _selectedType = CategoryType.expense),
              theme: theme,
              color: AppColors.error,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildShowInactiveSection(ThemeData theme, AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.showInactiveCategories,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),

        SwitchListTile(
          value: _showInactive,
          onChanged: (value) => setState(() => _showInactive = value),
          title: Text(l10n.includeInactiveCategoriesInList),
          contentPadding: EdgeInsets.zero,
          dense: true,
        ),
      ],
    );
  }

  Widget _buildActionButtons(ThemeData theme, AppLocalizations l10n) {
    return Row(
      children: [
        // Clear all button
        Expanded(
          child: OutlinedButton(
            onPressed: _clearAllFilters,
            child: Text(l10n.clearAll),
          ),
        ),
        const SizedBox(width: AppSpacing.md),

        // Apply button
        Expanded(
          child: ElevatedButton(
            onPressed: _applyFilters,
            child: Text(l10n.apply),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onSelected,
    required ThemeData theme,
    Color? color,
  }) {
    final chipColor = color ?? theme.colorScheme.primary;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onSelected(),
      backgroundColor: theme.colorScheme.surface,
      selectedColor: chipColor.withValues(alpha: 0.1),
      checkmarkColor: chipColor,
      labelStyle: TextStyle(
        color: isSelected ? chipColor : theme.colorScheme.onSurface,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
      side: BorderSide(
        color: isSelected
            ? chipColor
            : theme.colorScheme.outline.withValues(alpha: 0.3),
      ),
    );
  }

  void _clearAllFilters() {
    setState(() {
      _selectedType = null;
      _showInactive = false;
    });
  }

  void _applyFilters() {
    widget.onTypeChanged?.call(_selectedType);
    widget.onShowInactiveChanged?.call(showInactive: _showInactive);
    Navigator.of(context).pop();
  }
}
