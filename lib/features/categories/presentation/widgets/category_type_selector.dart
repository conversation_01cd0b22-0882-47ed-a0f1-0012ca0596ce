import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/categories/services/category_validators.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';

/// Widget for selecting category type (income/expense)
class CategoryTypeSelector extends StatelessWidget {
  const CategoryTypeSelector({
    super.key,
    required this.selectedType,
    required this.onTypeChanged,
    this.errorText,
  });
  final CategoryType? selectedType;
  final ValueChanged<CategoryType?> onTypeChanged;
  final String? errorText;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.selectCategoryType,
          style: theme.textTheme.labelLarge?.copyWith(
            fontWeight: AppTypography.fontWeightMedium,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),

        // Category type options
        DecoratedBox(
          decoration: BoxDecoration(
            border: Border.all(
              color: errorText != null
                  ? theme.colorScheme.error
                  : theme.colorScheme.outline,
            ),
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
          ),
          child: Column(
            children: CategoryValidators.getAvailableCategoryTypes()
                .map((type) => _buildTypeOption(context, type))
                .toList(),
          ),
        ),

        // Error text
        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTypeOption(BuildContext context, CategoryType type) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final isSelected = selectedType == type;
    final isFirst = type == CategoryType.income;
    final isLast = type == CategoryType.expense;

    return InkWell(
      onTap: () => onTypeChanged(type),
      borderRadius: BorderRadius.vertical(
        top: isFirst ? const Radius.circular(AppBorderRadius.md) : Radius.zero,
        bottom: isLast
            ? const Radius.circular(AppBorderRadius.md)
            : Radius.zero,
      ),
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.md),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primaryContainer.withValues(alpha: 0.3)
              : null,
          border: !isLast
              ? Border(
                  bottom: BorderSide(
                    color: theme.colorScheme.outline.withValues(alpha: 0.5),
                  ),
                )
              : null,
        ),
        child: Row(
          children: [
            // Type icon
            Container(
              padding: const EdgeInsets.all(AppSpacing.sm),
              decoration: BoxDecoration(
                color: _getTypeColor(type, theme),
                borderRadius: BorderRadius.circular(AppBorderRadius.sm),
              ),
              child: Icon(_getTypeIcon(type), color: Colors.white, size: 20),
            ),
            const SizedBox(width: AppSpacing.md),

            // Type details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    CategoryValidators.getCategoryTypeDisplayName(
                      type,
                      context,
                    ),
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: isSelected
                          ? AppTypography.fontWeightSemiBold
                          : AppTypography.fontWeightMedium,
                      color: isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: AppSpacing.xs),
                  Text(
                    _getTypeDescription(type, l10n),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),

            // Selection indicator
            Radio<CategoryType>(
              value: type,
              groupValue: selectedType,
              onChanged: onTypeChanged,
              activeColor: theme.colorScheme.primary,
            ),
          ],
        ),
      ),
    );
  }

  Color _getTypeColor(CategoryType type, ThemeData theme) {
    switch (type) {
      case CategoryType.income:
        return AppColors.success;
      case CategoryType.expense:
        return AppColors.error;
    }
  }

  IconData _getTypeIcon(CategoryType type) {
    switch (type) {
      case CategoryType.income:
        return Icons.trending_up;
      case CategoryType.expense:
        return Icons.trending_down;
    }
  }

  String _getTypeDescription(CategoryType type, AppLocalizations l10n) {
    switch (type) {
      case CategoryType.income:
        return l10n.incomeDescription;
      case CategoryType.expense:
        return l10n.expenseDescription;
    }
  }
}
