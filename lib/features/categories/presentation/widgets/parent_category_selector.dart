import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/utils/color_utils.dart';
import 'package:budapp/utils/icon_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Widget for selecting parent category for subcategories
class ParentCategorySelector extends ConsumerWidget {
  // Exclude this category from options (for editing)

  const ParentCategorySelector({
    super.key,
    required this.selectedParentId,
    required this.categoryType,
    required this.onParentChanged,
    this.errorText,
    this.excludeCategoryId,
  });
  final String? selectedParentId;
  final CategoryType categoryType;
  final ValueChanged<String?> onParentChanged;
  final String? errorText;
  final String? excludeCategoryId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    // Get root categories of the same type
    final rootCategoriesAsync = ref.watch(
      rootCategoriesByTypeProvider(categoryType),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.selectParentCategory,
          style: theme.textTheme.labelLarge?.copyWith(
            fontWeight: AppTypography.fontWeightMedium,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),

        // Parent category dropdown
        DecoratedBox(
          decoration: BoxDecoration(
            border: Border.all(
              color: errorText != null
                  ? theme.colorScheme.error
                  : theme.colorScheme.outline,
            ),
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
          ),
          child: rootCategoriesAsync.when(
            data: (categories) => _buildCategoryDropdown(context, categories),
            loading: () => _buildLoadingState(context),
            error: (error, stack) => _buildErrorState(context, error),
          ),
        ),

        // Error text
        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCategoryDropdown(
    BuildContext context,
    List<Category> categories,
  ) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    // Filter out the category being edited (to prevent self-reference)
    final availableCategories = categories
        .where((category) => category.id != excludeCategoryId)
        .toList();

    return DropdownButtonFormField<String?>(
      value: selectedParentId,
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        border: InputBorder.none,
        hintText: l10n.selectParentCategory,
      ),
      items: [
        // No parent option
        DropdownMenuItem<String?>(
          value: null,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                ),
                child: Icon(
                  Icons.folder_outlined,
                  size: 20,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Text(l10n.noParentCategory),
            ],
          ),
        ),

        // Available parent categories
        ...availableCategories.map(
          (category) => DropdownMenuItem<String?>(
            value: category.id,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Category icon
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: category.color != null
                        ? parseHex(category.color!)
                        : theme.colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                  ),
                  child: Icon(
                    iconFromName(category.icon ?? 'category'),
                    size: 20,
                    color: category.color != null
                        ? contrastOfHex(category.color!)
                        : theme.colorScheme.onPrimaryContainer,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),

                // Category name
                Expanded(
                  child: Text(
                    category.name,
                    style: theme.textTheme.bodyMedium,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
      onChanged: onParentChanged,
      validator: (value) => errorText,
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(width: AppSpacing.md),
          Text(
            'Loading categories...',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, Object error) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Row(
        children: [
          Icon(Icons.error_outline, size: 20, color: theme.colorScheme.error),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Text(
              'Error loading categories',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
