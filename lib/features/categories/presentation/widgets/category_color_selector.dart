import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/utils/color_utils.dart';
import 'package:flutter/material.dart';

/// Widget for selecting category color
class CategoryColorSelector extends StatelessWidget {
  const CategoryColorSelector({
    super.key,
    required this.selectedColor,
    required this.onColorChanged,
    this.errorText,
  });
  final String? selectedColor;
  final ValueChanged<String?> onColorChanged;
  final String? errorText;

  // Predefined color options for categories
  static const List<String> _colorOptions = [
    '#F44336', // Red
    '#E91E63', // Pink
    '#9C27B0', // Purple
    '#673AB7', // Deep Purple
    '#3F51B5', // Indigo
    '#2196F3', // Blue
    '#03A9F4', // Light Blue
    '#00BCD4', // Cyan
    '#009688', // Teal
    '#4CAF50', // Green
    '#8BC34A', // Light Green
    '#CDDC39', // Lime
    '#FFEB3B', // Yellow
    '#FFC107', // Amber
    '#FF9800', // Orange
    '#FF5722', // Deep Orange
    '#795548', // Brown
    '#607D8B', // Blue Grey
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.selectColor,
          style: theme.textTheme.labelLarge?.copyWith(
            fontWeight: AppTypography.fontWeightMedium,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),

        // Color grid
        Container(
          padding: const EdgeInsets.all(AppSpacing.md),
          decoration: BoxDecoration(
            border: Border.all(
              color: errorText != null
                  ? theme.colorScheme.error
                  : theme.colorScheme.outline,
            ),
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
          ),
          child: Column(
            children: [
              // Default option (no color)
              _buildDefaultOption(context),
              const SizedBox(height: AppSpacing.md),

              // Color options grid
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 6,
                  crossAxisSpacing: AppSpacing.sm,
                  mainAxisSpacing: AppSpacing.sm,
                  childAspectRatio: 1,
                ),
                itemCount: _colorOptions.length,
                itemBuilder: (context, index) {
                  final color = _colorOptions[index];
                  return _buildColorOption(context, color);
                },
              ),
            ],
          ),
        ),

        // Error text
        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDefaultOption(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final isSelected = selectedColor == null;

    return InkWell(
      onTap: () => onColorChanged(null),
      borderRadius: BorderRadius.circular(AppBorderRadius.sm),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primaryContainer.withValues(alpha: 0.3)
              : null,
          border: Border.all(
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.outline.withValues(alpha: 0.5),
          ),
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                border: Border.all(color: theme.colorScheme.outline),
              ),
              child: Icon(
                Icons.palette_outlined,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(width: AppSpacing.sm),
            Text(
              l10n.defaultColor,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface,
                fontWeight: isSelected
                    ? AppTypography.fontWeightMedium
                    : AppTypography.fontWeightRegular,
              ),
            ),
            if (isSelected) ...[
              const SizedBox(width: AppSpacing.sm),
              Icon(Icons.check, size: 16, color: theme.colorScheme.primary),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildColorOption(BuildContext context, String colorHex) {
    final theme = Theme.of(context);
    final isSelected = selectedColor == colorHex;
    final color = parseHex(colorHex);

    return InkWell(
      onTap: () => onColorChanged(colorHex),
      borderRadius: BorderRadius.circular(AppBorderRadius.sm),
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
          border: Border.all(
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.outline.withValues(alpha: 0.3),
            width: isSelected ? 3 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: theme.colorScheme.primary.withValues(alpha: 0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: isSelected
            ? Icon(Icons.check, color: contrastOf(color), size: 20)
            : null,
      ),
    );
  }
}
