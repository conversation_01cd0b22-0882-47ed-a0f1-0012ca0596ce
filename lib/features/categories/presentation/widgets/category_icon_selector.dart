import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';

/// Widget for selecting category icon
class CategoryIconSelector extends StatelessWidget {
  const CategoryIconSelector({
    super.key,
    required this.selectedIcon,
    required this.onIconChanged,
    this.errorText,
  });
  final String? selectedIcon;
  final ValueChanged<String?> onIconChanged;
  final String? errorText;

  // Predefined icon options for categories
  static const List<CategoryIconOption> _iconOptions = [
    CategoryIconOption('shopping_cart', Icons.shopping_cart),
    CategoryIconOption('restaurant', Icons.restaurant),
    CategoryIconOption('local_gas_station', Icons.local_gas_station),
    CategoryIconOption('home', Icons.home),
    CategoryIconOption('directions_car', Icons.directions_car),
    CategoryIconOption('medical_services', Icons.medical_services),
    CategoryIconOption('school', Icons.school),
    CategoryIconOption('sports_esports', Icons.sports_esports),
    CategoryIconOption('movie', Icons.movie),
    CategoryIconOption('fitness_center', Icons.fitness_center),
    CategoryIconOption('work', Icons.work),
    CategoryIconOption('account_balance', Icons.account_balance),
    CategoryIconOption('savings', Icons.savings),
    CategoryIconOption('trending_up', Icons.trending_up),
    CategoryIconOption('card_giftcard', Icons.card_giftcard),
    CategoryIconOption('attach_money', Icons.attach_money),
    CategoryIconOption('receipt', Icons.receipt),
    CategoryIconOption('category', Icons.category),
    CategoryIconOption('label', Icons.label),
    CategoryIconOption('bookmark', Icons.bookmark),
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.selectIcon,
          style: theme.textTheme.labelLarge?.copyWith(
            fontWeight: AppTypography.fontWeightMedium,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),

        // Icon grid
        Container(
          padding: const EdgeInsets.all(AppSpacing.md),
          decoration: BoxDecoration(
            border: Border.all(
              color: errorText != null
                  ? theme.colorScheme.error
                  : theme.colorScheme.outline,
            ),
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
          ),
          child: Column(
            children: [
              // Default option (no icon)
              _buildDefaultOption(context),
              const SizedBox(height: AppSpacing.md),

              // Icon options grid
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 5,
                  crossAxisSpacing: AppSpacing.sm,
                  mainAxisSpacing: AppSpacing.sm,
                  childAspectRatio: 1,
                ),
                itemCount: _iconOptions.length,
                itemBuilder: (context, index) {
                  final iconOption = _iconOptions[index];
                  return _buildIconOption(context, iconOption);
                },
              ),
            ],
          ),
        ),

        // Error text
        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDefaultOption(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final isSelected = selectedIcon == null;

    return InkWell(
      onTap: () => onIconChanged(null),
      borderRadius: BorderRadius.circular(AppBorderRadius.sm),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primaryContainer.withValues(alpha: 0.3)
              : null,
          border: Border.all(
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.outline.withValues(alpha: 0.5),
          ),
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                border: Border.all(color: theme.colorScheme.outline),
              ),
              child: Icon(
                Icons.category_outlined,
                size: 20,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(width: AppSpacing.sm),
            Text(
              l10n.defaultIcon,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface,
                fontWeight: isSelected
                    ? AppTypography.fontWeightMedium
                    : AppTypography.fontWeightRegular,
              ),
            ),
            if (isSelected) ...[
              const SizedBox(width: AppSpacing.sm),
              Icon(Icons.check, size: 16, color: theme.colorScheme.primary),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildIconOption(BuildContext context, CategoryIconOption iconOption) {
    final theme = Theme.of(context);
    final isSelected = selectedIcon == iconOption.name;

    return InkWell(
      onTap: () => onIconChanged(iconOption.name),
      borderRadius: BorderRadius.circular(AppBorderRadius.sm),
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primaryContainer.withValues(alpha: 0.3)
              : theme.colorScheme.surfaceContainerHighest.withValues(
                  alpha: 0.3,
                ),
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
          border: Border.all(
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.outline.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Stack(
          children: [
            Center(
              child: Icon(
                iconOption.iconData,
                size: 24,
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurfaceVariant,
              ),
            ),
            if (isSelected)
              Positioned(
                top: 2,
                right: 2,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check,
                    size: 12,
                    color: theme.colorScheme.onPrimary,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Data class for category icon options
class CategoryIconOption {
  const CategoryIconOption(this.name, this.iconData);
  final String name;
  final IconData iconData;
}
