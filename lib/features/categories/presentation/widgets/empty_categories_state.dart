import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Widget displayed when no categories are available
class EmptyCategoriesState extends StatelessWidget {
  const EmptyCategoriesState({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.xl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Illustration
            _buildIllustration(theme),
            const SizedBox(height: AppSpacing.xl),

            // Title
            Text(
              l10n.noCategoriesYet,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.md),

            // Description
            Text(
              l10n.noCategoriesDescription,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.xl),

            // Action buttons
            _buildActionButtons(context, theme, l10n),
            const SizedBox(height: AppSpacing.lg),

            // Help link
            TextButton.icon(
              onPressed: () => _showHelpDialog(context, theme, l10n),
              icon: const Icon(Icons.help_outline, size: 18),
              label: Text(l10n.learnAboutCategories),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIllustration(ThemeData theme) {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(60),
      ),
      child: Icon(
        Icons.category_outlined,
        size: 64,
        color: theme.colorScheme.primary,
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return Column(
      children: [
        // Primary action - Create first category
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              context.push('/categories/create');
            },
            icon: const Icon(Icons.add),
            label: Text(l10n.createFirstCategory),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.lg,
                vertical: AppSpacing.md,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showHelpDialog(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.aboutCategories),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Description
              Text(
                l10n.categoriesHelpDescription,
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: AppSpacing.md),

              // Category types section
              Text(
                l10n.categoryTypesTitle,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: AppSpacing.sm),

              // Income categories
              _buildHelpItem(
                icon: Icons.trending_up,
                color: AppColors.success,
                title: l10n.income,
                description: l10n.incomeDescription,
                theme: theme,
              ),
              const SizedBox(height: AppSpacing.sm),

              // Expense categories
              _buildHelpItem(
                icon: Icons.trending_down,
                color: AppColors.error,
                title: l10n.expense,
                description: l10n.expenseDescription,
                theme: theme,
              ),
              const SizedBox(height: AppSpacing.md),

              // Subcategories info
              _buildHelpItem(
                icon: Icons.subdirectory_arrow_right,
                color: theme.colorScheme.primary,
                title: l10n.subcategories,
                description: l10n.subcategoriesDescription,
                theme: theme,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.gotIt),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpItem({
    required IconData icon,
    required Color color,
    required String title,
    required String description,
    required ThemeData theme,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppBorderRadius.sm),
          ),
          child: Icon(icon, size: 18, color: color),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
              const SizedBox(height: AppSpacing.xs),
              Text(
                description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
