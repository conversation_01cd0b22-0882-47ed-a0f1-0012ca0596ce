import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/features/categories/services/category_deletion_service.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Dialog for guided category deletion with constraint checking and reassignment options
class CategoryDeletionDialog extends ConsumerStatefulWidget {
  const CategoryDeletionDialog({
    super.key,
    required this.category,
    this.onDeleted,
  });
  final Category category;
  final VoidCallback? onDeleted;

  @override
  ConsumerState<CategoryDeletionDialog> createState() =>
      _CategoryDeletionDialogState();
}

class _CategoryDeletionDialogState
    extends ConsumerState<CategoryDeletionDialog> {
  bool _isLoading = false;
  CategoryDeletionResult? _constraintResult;
  List<Transaction>? _associatedTransactions;
  Category? _selectedReassignmentCategory;

  @override
  void initState() {
    super.initState();
    _checkDeletionConstraints();
  }

  Future<void> _checkDeletionConstraints() async {
    setState(() => _isLoading = true);

    try {
      final authService = ref.read(authServiceProvider);
      final user = authService.currentUser;
      if (user == null) return;

      final deletionService = ref.read(categoryDeletionServiceProvider);

      // Check if category can be deleted
      final result = await deletionService.canDeleteCategory(
        user.uid,
        widget.category.id,
      );

      if (!result.isSuccess) {
        // Get associated transactions for display
        final transactions = await deletionService.getTransactionsForCategory(
          user.uid,
          widget.category.id,
        );

        setState(() {
          _constraintResult = result;
          _associatedTransactions = transactions;
        });
      } else {
        // No constraints, show simple confirmation
        _showSimpleConfirmation();
      }
    } on Exception catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _constraintResult = CategoryDeletionResult.error(
            'Error checking deletion constraints: $e',
          );
        });
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showSimpleConfirmation() {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.deleteCategory),
        content: Text(l10n.deleteCategoryConfirmation),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performDeletion();
            },
            style: FilledButton.styleFrom(
              backgroundColor: theme.colorScheme.error,
              foregroundColor: theme.colorScheme.onError,
            ),
            child: Text(l10n.delete),
          ),
        ],
      ),
    );
  }

  Future<void> _performDeletion() async {
    setState(() => _isLoading = true);

    try {
      final authService = ref.read(authServiceProvider);
      final user = authService.currentUser;
      if (user == null) return;

      final deletionService = ref.read(categoryDeletionServiceProvider);
      final result = await deletionService.deleteCategory(
        user.uid,
        widget.category.id,
      );

      if (result.isSuccess) {
        // Invalidate providers to refresh UI
        ref.invalidate(categoryListProvider);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                AppLocalizations.of(context)!.categoryDeletedSuccessfully,
              ),
              backgroundColor: Theme.of(context).colorScheme.primary,
            ),
          );
          Navigator.of(context).pop();
          widget.onDeleted?.call();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.errorMessage ?? 'Failed to delete category'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    } on Exception catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting category: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _performReassignmentAndDeletion() async {
    if (_selectedReassignmentCategory == null) return;

    setState(() => _isLoading = true);

    try {
      final authService = ref.read(authServiceProvider);
      final user = authService.currentUser;
      if (user == null) return;

      final deletionService = ref.read(categoryDeletionServiceProvider);
      final result = await deletionService.deleteWithReassignment(
        user.uid,
        widget.category.id,
        _selectedReassignmentCategory!.id,
      );

      if (result.isSuccess) {
        // Invalidate providers to refresh UI
        ref.invalidate(categoryListProvider);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.successMessage),
              backgroundColor: Theme.of(context).colorScheme.primary,
            ),
          );
          Navigator.of(context).pop();
          widget.onDeleted?.call();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                result.errorMessage ?? 'Failed to reassign and delete',
              ),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    } on Exception catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error during reassignment: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showReassignmentDialog() {
    showDialog<void>(
      context: context,
      builder: (context) => CategoryReassignmentDialog(
        category: widget.category,
        onCategorySelected: (category) {
          setState(() => _selectedReassignmentCategory = category);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    if (_isLoading) {
      return AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: AppSpacing.md),
            Text(l10n.loading),
          ],
        ),
      );
    }

    if (_constraintResult == null) {
      return const SizedBox.shrink();
    }

    return AlertDialog(
      title: Text(l10n.categoryDeletionConstraints),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Category info
            Text(
              'Category: ${widget.category.name}',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppSpacing.md),

            // Error message display
            if (_constraintResult!.errorMessage != null) ...[
              Container(
                padding: const EdgeInsets.all(AppSpacing.sm),
                decoration: BoxDecoration(
                  color: theme.colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error, color: theme.colorScheme.error, size: 20),
                    const SizedBox(width: AppSpacing.sm),
                    Expanded(
                      child: Text(
                        _constraintResult!.errorMessage!,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onErrorContainer,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: AppSpacing.md),
            ],

            // Constraint information
            if (_associatedTransactions != null &&
                _associatedTransactions!.isNotEmpty) ...[
              Row(
                children: [
                  Icon(Icons.warning, color: theme.colorScheme.error, size: 20),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: Text(
                      l10n.categoryHasTransactions(
                        _associatedTransactions!.length,
                      ),
                      style: theme.textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppSpacing.sm),

              // View transactions button
              TextButton.icon(
                onPressed: _showTransactionsList,
                icon: const Icon(Icons.list),
                label: Text(l10n.viewTransactions),
              ),
              const SizedBox(height: AppSpacing.md),
            ],

            // Reassignment selection
            if (_selectedReassignmentCategory != null) ...[
              Text(
                'Reassign to: ${_selectedReassignmentCategory!.name}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: AppSpacing.md),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(l10n.cancel),
        ),
        if (_associatedTransactions != null &&
            _associatedTransactions!.isNotEmpty) ...[
          TextButton(
            onPressed: _showReassignmentDialog,
            child: Text(l10n.reassignTransactions),
          ),
          if (_selectedReassignmentCategory != null)
            FilledButton(
              onPressed: _performReassignmentAndDeletion,
              child: Text(l10n.reassignmentConfirmation),
            ),
        ],
        FilledButton(
          onPressed: _performDeletion,
          style: FilledButton.styleFrom(
            backgroundColor: theme.colorScheme.error,
            foregroundColor: theme.colorScheme.onError,
          ),
          child: Text(l10n.deleteAnyway),
        ),
      ],
    );
  }

  void _showTransactionsList() {
    if (_associatedTransactions == null) return;

    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          AppLocalizations.of(
            context,
          )!.transactionsInCategory(widget.category.name),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Consumer(
            builder: (context, ref, child) {
              final currencyFormatter = ref.watch(currencyFormatterProvider);
              return ListView.builder(
                shrinkWrap: true,
                itemCount: _associatedTransactions!.length,
                itemBuilder: (context, index) {
                  final transaction = _associatedTransactions![index];
                  return ListTile(
                    title: Text(transaction.description ?? 'No description'),
                    subtitle: Text(
                      currencyFormatter.formatAmount(transaction.amountCents),
                    ),
                    trailing: Text(
                      transaction.transactionDate.toString().split(' ')[0],
                    ),
                  );
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context)!.close),
          ),
        ],
      ),
    );
  }
}

/// Dialog for selecting a category for reassignment
class CategoryReassignmentDialog extends ConsumerWidget {
  const CategoryReassignmentDialog({
    super.key,
    required this.category,
    required this.onCategorySelected,
  });
  final Category category;
  final ValueChanged<Category> onCategorySelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;

    // Get categories of the same type, excluding the current category
    final categoriesAsync = ref.watch(categoryListProvider);

    return AlertDialog(
      title: Text(l10n.selectNewCategory),
      content: SizedBox(
        width: double.maxFinite,
        child: categoriesAsync.when(
          data: (categories) {
            final availableCategories = categories
                .where(
                  (c) =>
                      c.id != category.id &&
                      c.type == category.type &&
                      c.isActive,
                )
                .toList();

            if (availableCategories.isEmpty) {
              return Text(l10n.noAvailableCategories);
            }

            return ListView.builder(
              shrinkWrap: true,
              itemCount: availableCategories.length,
              itemBuilder: (context, index) {
                final cat = availableCategories[index];
                return ListTile(
                  title: Text(cat.name),
                  subtitle: Text(cat.type.name),
                  onTap: () {
                    onCategorySelected(cat);
                    Navigator.of(context).pop();
                  },
                );
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Text('Error: $error'),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(l10n.cancel),
        ),
      ],
    );
  }
}
