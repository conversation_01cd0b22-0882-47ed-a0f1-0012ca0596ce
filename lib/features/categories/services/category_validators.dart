import 'package:budapp/data/models/category.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';

/// Client-side validation service for category forms
///
/// This service provides immediate validation feedback for category forms
/// while leveraging repository-level validation for complex business rules.
// ignore: avoid_classes_with_only_static_members
class CategoryValidators {
  /// Validate category name field
  static String? categoryName(String? value, BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    if (value == null || value.trim().isEmpty) {
      return l10n.fieldRequired;
    }

    final trimmedValue = value.trim();

    // Length validation
    if (trimmedValue.length < 2) {
      return l10n.categoryNameTooShort;
    }

    if (trimmedValue.length > 50) {
      return l10n.categoryNameTooLong;
    }

    // Character validation - allow letters, numbers, spaces, hyphens, apostrophes
    final validPattern = RegExp(r"^[a-zA-Z0-9\s\-']+$");
    if (!validPattern.hasMatch(trimmedValue)) {
      return l10n.categoryNameInvalidCharacters;
    }

    return null;
  }

  /// Validate category description field
  static String? categoryDescription(String? value, BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    // Description is optional
    if (value == null || value.trim().isEmpty) {
      return null;
    }

    final trimmedValue = value.trim();

    // Length validation
    if (trimmedValue.length > 200) {
      return l10n.categoryDescriptionTooLong;
    }

    return null;
  }

  /// Validate category type selection
  static String? categoryType(CategoryType? value, BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    if (value == null) {
      return l10n.categoryTypeRequired;
    }

    return null;
  }

  /// Validate hex color format
  static String? categoryColor(String? value, BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    // Color is optional
    if (value == null || value.trim().isEmpty) {
      return null;
    }

    final trimmedValue = value.trim();

    // Hex color validation (#RRGGBB format)
    final hexPattern = RegExp(r'^#[0-9A-Fa-f]{6}$');
    if (!hexPattern.hasMatch(trimmedValue)) {
      return l10n.categoryColorInvalidFormat;
    }

    return null;
  }

  /// Validate icon identifier format
  static String? categoryIcon(String? value, BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    // Icon is optional
    if (value == null || value.trim().isEmpty) {
      return null;
    }

    final trimmedValue = value.trim();

    // Icon identifier validation (letters, numbers, underscores, must start with letter)
    final iconPattern = RegExp(r'^[a-zA-Z][a-zA-Z0-9_]*$');
    if (!iconPattern.hasMatch(trimmedValue)) {
      return l10n.categoryIconInvalidFormat;
    }

    return null;
  }

  /// Validate complete category form
  static Map<String, String> validateCategoryForm({
    required String? name,
    required CategoryType? type,
    String? description,
    String? color,
    String? icon,
    required BuildContext context,
  }) {
    final errors = <String, String>{};

    // Validate each field
    final nameError = categoryName(name, context);
    if (nameError != null) {
      errors['name'] = nameError;
    }

    final typeError = categoryType(type, context);
    if (typeError != null) {
      errors['type'] = typeError;
    }

    final descriptionError = categoryDescription(description, context);
    if (descriptionError != null) {
      errors['description'] = descriptionError;
    }

    final colorError = categoryColor(color, context);
    if (colorError != null) {
      errors['color'] = colorError;
    }

    final iconError = categoryIcon(icon, context);
    if (iconError != null) {
      errors['icon'] = iconError;
    }

    return errors;
  }

  /// Validate complete subcategory form
  static Map<String, String> validateSubcategoryForm({
    required String? name,
    required CategoryType? type,
    required String? parentId,
    String? description,
    String? color,
    String? icon,
    required BuildContext context,
  }) {
    final errors = validateCategoryForm(
      name: name,
      type: type,
      description: description,
      color: color,
      icon: icon,
      context: context,
    );

    // Additional validation for subcategories
    final l10n = AppLocalizations.of(context)!;

    if (parentId == null || parentId.trim().isEmpty) {
      errors['parentId'] = l10n.parentCategoryRequired;
    }

    return errors;
  }

  /// Create a category object from form data
  static Category createCategoryFromForm({
    required String userId,
    required String name,
    required CategoryType type,
    String? description,
    String? color,
    String? icon,
    String? parentId,
  }) {
    return Category.create(
      userId: userId,
      name: name.trim(),
      type: type,
      parentId: (parentId?.trim().isEmpty ?? false) ? null : parentId?.trim(),
      description: (description?.trim().isEmpty ?? false)
          ? null
          : description?.trim(),
      color: (color?.trim().isEmpty ?? false) ? null : color?.trim(),
      icon: (icon?.trim().isEmpty ?? false) ? null : icon?.trim(),
    );
  }

  /// Update a category object from form data
  static Category updateCategoryFromForm({
    required Category existingCategory,
    required String name,
    required CategoryType type,
    String? description,
    String? color,
    String? icon,
    String? parentId,
  }) {
    return existingCategory.copyWith(
      name: name.trim(),
      type: type,
      parentId: (parentId?.trim().isEmpty ?? false) ? null : parentId?.trim(),
      description: (description?.trim().isEmpty ?? false)
          ? null
          : description?.trim(),
      color: (color?.trim().isEmpty ?? false) ? null : color?.trim(),
      icon: (icon?.trim().isEmpty ?? false) ? null : icon?.trim(),
      updatedAt: DateTime.now(),
    );
  }

  /// Get display name for category type
  static String getCategoryTypeDisplayName(
    CategoryType type,
    BuildContext context,
  ) {
    final l10n = AppLocalizations.of(context)!;
    switch (type) {
      case CategoryType.income:
        return l10n.income;
      case CategoryType.expense:
        return l10n.expense;
    }
  }

  /// Get available category types for selection
  static List<CategoryType> getAvailableCategoryTypes() {
    return CategoryType.values;
  }
}
