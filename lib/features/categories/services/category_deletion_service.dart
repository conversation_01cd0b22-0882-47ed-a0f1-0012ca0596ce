import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/interfaces/category_repository.dart';
import 'package:budapp/data/repositories/interfaces/transaction_repository.dart';

/// Service for handling category deletion with proper constraint checking
///
/// This service centralizes the logic for validating category deletion constraints
/// and provides user-friendly error messages for different scenarios.
class CategoryDeletionService {
  CategoryDeletionService(
    this._categoryRepository,
    this._transactionRepository,
  );
  final ICategoryRepository _categoryRepository;
  final ITransactionRepository _transactionRepository;

  /// Check if a category can be safely deleted
  ///
  /// Returns a [CategoryDeletionResult] indicating whether deletion is allowed
  /// and providing appropriate error messages if not.
  Future<CategoryDeletionResult> canDeleteCategory(
    String userId,
    String categoryId,
  ) async {
    try {
      // Check if category exists
      final category = await _categoryRepository.getCategoryById(
        userId,
        categoryId,
      );
      if (category == null) {
        return CategoryDeletionResult.error('Category not found');
      }

      // Check for dependent transactions
      final hasTransactions = await _categoryRepository
          .hasDependentTransactionsForUser(userId, categoryId);
      if (hasTransactions) {
        return CategoryDeletionResult.error(
          'Cannot delete category "${category.name}": it has associated transactions. '
          'Please reassign or delete the transactions first.',
        );
      }

      // Check for child subcategories
      final hasChildren = await _categoryRepository.hasChildSubcategories(
        userId,
        categoryId,
      );
      if (hasChildren) {
        return CategoryDeletionResult.error(
          'Cannot delete category "${category.name}": it has subcategories. '
          'Please delete or move the subcategories first.',
        );
      }

      return CategoryDeletionResult.success();
    } on Exception catch (e) {
      return CategoryDeletionResult.error(
        'Error checking deletion constraints: $e',
      );
    }
  }

  /// Check if a subcategory can be safely deleted
  ///
  /// Returns a [CategoryDeletionResult] indicating whether deletion is allowed
  /// and providing appropriate error messages if not.
  Future<CategoryDeletionResult> canDeleteSubcategory(
    String userId,
    String subcategoryId,
    String parentId,
  ) async {
    try {
      // Check if subcategory exists and belongs to parent
      final subcategory = await _categoryRepository.getCategoryById(
        userId,
        subcategoryId,
      );
      if (subcategory == null) {
        return CategoryDeletionResult.error('Subcategory not found');
      }

      if (subcategory.parentId != parentId) {
        return CategoryDeletionResult.error(
          'Subcategory does not belong to the specified parent category',
        );
      }

      // Check for dependent transactions
      final hasTransactions = await _categoryRepository
          .hasDependentTransactionsForUser(userId, subcategoryId);
      if (hasTransactions) {
        return CategoryDeletionResult.error(
          'Cannot delete subcategory "${subcategory.name}": it has associated transactions. '
          'Please reassign or delete the transactions first.',
        );
      }

      // Check for child subcategories (subcategories can have their own children)
      final hasChildren = await _categoryRepository.hasChildSubcategories(
        userId,
        subcategoryId,
      );
      if (hasChildren) {
        return CategoryDeletionResult.error(
          'Cannot delete subcategory "${subcategory.name}": it has child subcategories. '
          'Please delete or move the child subcategories first.',
        );
      }

      return CategoryDeletionResult.success();
    } on Exception catch (e) {
      return CategoryDeletionResult.error(
        'Error checking deletion constraints: $e',
      );
    }
  }

  /// Safely delete a category with constraint checking
  ///
  /// Performs all necessary validation before deletion and returns a result
  /// indicating success or failure with appropriate error messages.
  Future<CategoryDeletionResult> deleteCategory(
    String userId,
    String categoryId,
  ) async {
    // First check if deletion is allowed
    final canDelete = await canDeleteCategory(userId, categoryId);
    if (!canDelete.isSuccess) {
      return canDelete;
    }

    try {
      // Perform the deletion
      await _categoryRepository.deleteCategoryWithConstraints(
        userId,
        categoryId,
      );
      return CategoryDeletionResult.success();
    } on Exception catch (e) {
      return CategoryDeletionResult.error('Failed to delete category: $e');
    }
  }

  /// Safely delete a subcategory with constraint checking
  ///
  /// Performs all necessary validation before deletion and returns a result
  /// indicating success or failure with appropriate error messages.
  Future<CategoryDeletionResult> deleteSubcategory(
    String userId,
    String subcategoryId,
    String parentId,
  ) async {
    // First check if deletion is allowed
    final canDelete = await canDeleteSubcategory(
      userId,
      subcategoryId,
      parentId,
    );
    if (!canDelete.isSuccess) {
      return canDelete;
    }

    try {
      // Perform the deletion
      await _categoryRepository.deleteSubcategoryWithConstraints(
        userId,
        subcategoryId,
        parentId,
      );
      return CategoryDeletionResult.success();
    } on Exception catch (e) {
      return CategoryDeletionResult.error('Failed to delete subcategory: $e');
    }
  }

  /// Get list of transactions that would be affected by category deletion
  ///
  /// Returns a list of transactions that reference the specified category.
  /// This is useful for showing users what transactions would need to be
  /// reassigned before deletion.
  Future<List<Transaction>> getTransactionsForCategory(
    String userId,
    String categoryId,
  ) async {
    try {
      return await _transactionRepository.getTransactionsByCategory(
        userId,
        categoryId,
      );
    } on Exception {
      // Return empty list if there's an error - this is safer than throwing
      return [];
    }
  }

  /// Validate that a category is suitable as a reassignment target
  ///
  /// Checks that the new category exists, is active, belongs to the same user,
  /// has the same type, and is not the same as the old category.
  Future<CategoryReassignmentResult> validateReassignmentTarget(
    String userId,
    String oldCategoryId,
    String newCategoryId,
  ) async {
    try {
      // Check if categories are the same
      if (oldCategoryId == newCategoryId) {
        return CategoryReassignmentResult.error(
          'Cannot reassign to the same category',
        );
      }

      // Get both categories
      final oldCategory = await _categoryRepository.getCategoryById(
        userId,
        oldCategoryId,
      );
      final newCategory = await _categoryRepository.getCategoryById(
        userId,
        newCategoryId,
      );

      if (oldCategory == null) {
        return CategoryReassignmentResult.error('Source category not found');
      }

      if (newCategory == null) {
        return CategoryReassignmentResult.error('Target category not found');
      }

      // Check if new category is active
      if (!newCategory.isActive) {
        return CategoryReassignmentResult.error(
          'Cannot reassign to inactive category "${newCategory.name}"',
        );
      }

      // Check if categories have the same type
      if (oldCategory.type != newCategory.type) {
        return CategoryReassignmentResult.error(
          'Cannot reassign from ${oldCategory.type.name} category to ${newCategory.type.name} category',
        );
      }

      // Check if new category belongs to the same user
      if (newCategory.userId != userId) {
        return CategoryReassignmentResult.error(
          'Target category does not belong to the current user',
        );
      }

      return CategoryReassignmentResult.success(
        oldCategoryName: oldCategory.name,
        newCategoryName: newCategory.name,
      );
    } on Exception catch (e) {
      return CategoryReassignmentResult.error(
        'Error validating reassignment target: $e',
      );
    }
  }

  /// Reassign all transactions from one category to another
  ///
  /// This method performs the actual reassignment of transactions and returns
  /// a result indicating success or failure with the number of affected transactions.
  Future<CategoryReassignmentResult> reassignCategoryTransactions(
    String userId,
    String oldCategoryId,
    String newCategoryId,
  ) async {
    try {
      // First validate the reassignment target
      final validation = await validateReassignmentTarget(
        userId,
        oldCategoryId,
        newCategoryId,
      );
      if (!validation.isSuccess) {
        return validation;
      }

      // Get the transactions that will be affected
      final transactions = await getTransactionsForCategory(
        userId,
        oldCategoryId,
      );

      if (transactions.isEmpty) {
        return CategoryReassignmentResult.success(
          oldCategoryName: validation.oldCategoryName!,
          newCategoryName: validation.newCategoryName!,
          transactionCount: 0,
        );
      }

      // Perform the reassignment
      await _transactionRepository.reassignTransactionsToCategoryForUser(
        userId,
        oldCategoryId,
        newCategoryId,
      );

      return CategoryReassignmentResult.success(
        oldCategoryName: validation.oldCategoryName!,
        newCategoryName: validation.newCategoryName!,
        transactionCount: transactions.length,
        transactionIds: transactions.map((t) => t.id).toList(),
      );
    } on Exception catch (e) {
      return CategoryReassignmentResult.error(
        'Failed to reassign transactions: $e',
      );
    }
  }

  /// Delete a category after reassigning its transactions to another category
  ///
  /// This is a convenience method that combines transaction reassignment
  /// and category deletion in a single operation.
  Future<CategoryReassignmentResult> deleteWithReassignment(
    String userId,
    String categoryId,
    String newCategoryId,
  ) async {
    try {
      // First reassign the transactions
      final reassignmentResult = await reassignCategoryTransactions(
        userId,
        categoryId,
        newCategoryId,
      );

      if (!reassignmentResult.isSuccess) {
        return reassignmentResult;
      }

      // Now delete the category
      final deletionResult = await deleteCategory(userId, categoryId);
      if (!deletionResult.isSuccess) {
        return CategoryReassignmentResult.error(
          'Transactions reassigned successfully, but failed to delete category: ${deletionResult.errorMessage}',
        );
      }

      return reassignmentResult;
    } on Exception catch (e) {
      return CategoryReassignmentResult.error(
        'Failed to delete category with reassignment: $e',
      );
    }
  }

  /// Delete a subcategory after reassigning its transactions to another category
  ///
  /// This is a convenience method that combines transaction reassignment
  /// and subcategory deletion in a single operation.
  Future<CategoryReassignmentResult> deleteSubcategoryWithReassignment(
    String userId,
    String subcategoryId,
    String parentId,
    String newCategoryId,
  ) async {
    try {
      // First reassign the transactions
      final reassignmentResult = await reassignCategoryTransactions(
        userId,
        subcategoryId,
        newCategoryId,
      );

      if (!reassignmentResult.isSuccess) {
        return reassignmentResult;
      }

      // Now delete the subcategory
      final deletionResult = await deleteSubcategory(
        userId,
        subcategoryId,
        parentId,
      );
      if (!deletionResult.isSuccess) {
        return CategoryReassignmentResult.error(
          'Transactions reassigned successfully, but failed to delete subcategory: ${deletionResult.errorMessage}',
        );
      }

      return reassignmentResult;
    } on Exception catch (e) {
      return CategoryReassignmentResult.error(
        'Failed to delete subcategory with reassignment: $e',
      );
    }
  }
}

/// Result of a category deletion operation
class CategoryDeletionResult {
  const CategoryDeletionResult._({required this.isSuccess, this.errorMessage});

  /// Create a successful result
  factory CategoryDeletionResult.success() {
    return const CategoryDeletionResult._(isSuccess: true);
  }

  /// Create an error result with a message
  factory CategoryDeletionResult.error(String message) {
    return CategoryDeletionResult._(isSuccess: false, errorMessage: message);
  }
  final bool isSuccess;
  final String? errorMessage;

  /// Whether the operation failed
  bool get isError => !isSuccess;
}

/// Result of a category transaction reassignment operation
class CategoryReassignmentResult {
  const CategoryReassignmentResult._({
    required this.isSuccess,
    this.errorMessage,
    this.oldCategoryName,
    this.newCategoryName,
    this.transactionCount = 0,
    this.transactionIds = const [],
  });

  /// Create a successful result
  factory CategoryReassignmentResult.success({
    required String oldCategoryName,
    required String newCategoryName,
    int transactionCount = 0,
    List<String> transactionIds = const [],
  }) {
    return CategoryReassignmentResult._(
      isSuccess: true,
      oldCategoryName: oldCategoryName,
      newCategoryName: newCategoryName,
      transactionCount: transactionCount,
      transactionIds: transactionIds,
    );
  }

  /// Create an error result with a message
  factory CategoryReassignmentResult.error(String message) {
    return CategoryReassignmentResult._(
      isSuccess: false,
      errorMessage: message,
    );
  }
  final bool isSuccess;
  final String? errorMessage;
  final String? oldCategoryName;
  final String? newCategoryName;
  final int transactionCount;
  final List<String> transactionIds;

  /// Whether the operation failed
  bool get isError => !isSuccess;

  /// Get a user-friendly success message
  String get successMessage {
    if (!isSuccess) return '';

    if (transactionCount == 0) {
      return 'No transactions to reassign';
    } else if (transactionCount == 1) {
      return 'Successfully reassigned 1 transaction from "$oldCategoryName" to "$newCategoryName"';
    } else {
      return 'Successfully reassigned $transactionCount transactions from "$oldCategoryName" to "$newCategoryName"';
    }
  }
}
