// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'goal_contribution_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$goalContributionsHash() => r'cfdc3d86f88252e5aa8f57760a219b68a9e7e8ae';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for contributions for a specific goal
///
/// Copied from [goalContributions].
@ProviderFor(goalContributions)
const goalContributionsProvider = GoalContributionsFamily();

/// Provider for contributions for a specific goal
///
/// Copied from [goalContributions].
class GoalContributionsFamily
    extends Family<AsyncValue<List<GoalContribution>>> {
  /// Provider for contributions for a specific goal
  ///
  /// Copied from [goalContributions].
  const GoalContributionsFamily();

  /// Provider for contributions for a specific goal
  ///
  /// Copied from [goalContributions].
  GoalContributionsProvider call(String goalId) {
    return GoalContributionsProvider(goalId);
  }

  @override
  GoalContributionsProvider getProviderOverride(
    covariant GoalContributionsProvider provider,
  ) {
    return call(provider.goalId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'goalContributionsProvider';
}

/// Provider for contributions for a specific goal
///
/// Copied from [goalContributions].
class GoalContributionsProvider
    extends AutoDisposeFutureProvider<List<GoalContribution>> {
  /// Provider for contributions for a specific goal
  ///
  /// Copied from [goalContributions].
  GoalContributionsProvider(String goalId)
    : this._internal(
        (ref) => goalContributions(ref as GoalContributionsRef, goalId),
        from: goalContributionsProvider,
        name: r'goalContributionsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$goalContributionsHash,
        dependencies: GoalContributionsFamily._dependencies,
        allTransitiveDependencies:
            GoalContributionsFamily._allTransitiveDependencies,
        goalId: goalId,
      );

  GoalContributionsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.goalId,
  }) : super.internal();

  final String goalId;

  @override
  Override overrideWith(
    FutureOr<List<GoalContribution>> Function(GoalContributionsRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GoalContributionsProvider._internal(
        (ref) => create(ref as GoalContributionsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        goalId: goalId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<GoalContribution>> createElement() {
    return _GoalContributionsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GoalContributionsProvider && other.goalId == goalId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, goalId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin GoalContributionsRef
    on AutoDisposeFutureProviderRef<List<GoalContribution>> {
  /// The parameter `goalId` of this provider.
  String get goalId;
}

class _GoalContributionsProviderElement
    extends AutoDisposeFutureProviderElement<List<GoalContribution>>
    with GoalContributionsRef {
  _GoalContributionsProviderElement(super.provider);

  @override
  String get goalId => (origin as GoalContributionsProvider).goalId;
}

String _$activeGoalContributionsHash() =>
    r'42c40efcc3b0a5d708d0f4e4d8579bd161f8304d';

/// Provider for active contributions for a specific goal
///
/// Copied from [activeGoalContributions].
@ProviderFor(activeGoalContributions)
const activeGoalContributionsProvider = ActiveGoalContributionsFamily();

/// Provider for active contributions for a specific goal
///
/// Copied from [activeGoalContributions].
class ActiveGoalContributionsFamily
    extends Family<AsyncValue<List<GoalContribution>>> {
  /// Provider for active contributions for a specific goal
  ///
  /// Copied from [activeGoalContributions].
  const ActiveGoalContributionsFamily();

  /// Provider for active contributions for a specific goal
  ///
  /// Copied from [activeGoalContributions].
  ActiveGoalContributionsProvider call(String goalId) {
    return ActiveGoalContributionsProvider(goalId);
  }

  @override
  ActiveGoalContributionsProvider getProviderOverride(
    covariant ActiveGoalContributionsProvider provider,
  ) {
    return call(provider.goalId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'activeGoalContributionsProvider';
}

/// Provider for active contributions for a specific goal
///
/// Copied from [activeGoalContributions].
class ActiveGoalContributionsProvider
    extends AutoDisposeFutureProvider<List<GoalContribution>> {
  /// Provider for active contributions for a specific goal
  ///
  /// Copied from [activeGoalContributions].
  ActiveGoalContributionsProvider(String goalId)
    : this._internal(
        (ref) =>
            activeGoalContributions(ref as ActiveGoalContributionsRef, goalId),
        from: activeGoalContributionsProvider,
        name: r'activeGoalContributionsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$activeGoalContributionsHash,
        dependencies: ActiveGoalContributionsFamily._dependencies,
        allTransitiveDependencies:
            ActiveGoalContributionsFamily._allTransitiveDependencies,
        goalId: goalId,
      );

  ActiveGoalContributionsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.goalId,
  }) : super.internal();

  final String goalId;

  @override
  Override overrideWith(
    FutureOr<List<GoalContribution>> Function(
      ActiveGoalContributionsRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ActiveGoalContributionsProvider._internal(
        (ref) => create(ref as ActiveGoalContributionsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        goalId: goalId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<GoalContribution>> createElement() {
    return _ActiveGoalContributionsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ActiveGoalContributionsProvider && other.goalId == goalId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, goalId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ActiveGoalContributionsRef
    on AutoDisposeFutureProviderRef<List<GoalContribution>> {
  /// The parameter `goalId` of this provider.
  String get goalId;
}

class _ActiveGoalContributionsProviderElement
    extends AutoDisposeFutureProviderElement<List<GoalContribution>>
    with ActiveGoalContributionsRef {
  _ActiveGoalContributionsProviderElement(super.provider);

  @override
  String get goalId => (origin as ActiveGoalContributionsProvider).goalId;
}

String _$goalContributionHash() => r'406a20a3945ea6f27eb2bef29f4431b71ebfb7fa';

/// Provider for a specific contribution by ID
///
/// Copied from [goalContribution].
@ProviderFor(goalContribution)
const goalContributionProvider = GoalContributionFamily();

/// Provider for a specific contribution by ID
///
/// Copied from [goalContribution].
class GoalContributionFamily extends Family<AsyncValue<GoalContribution?>> {
  /// Provider for a specific contribution by ID
  ///
  /// Copied from [goalContribution].
  const GoalContributionFamily();

  /// Provider for a specific contribution by ID
  ///
  /// Copied from [goalContribution].
  GoalContributionProvider call(String goalId, String contributionId) {
    return GoalContributionProvider(goalId, contributionId);
  }

  @override
  GoalContributionProvider getProviderOverride(
    covariant GoalContributionProvider provider,
  ) {
    return call(provider.goalId, provider.contributionId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'goalContributionProvider';
}

/// Provider for a specific contribution by ID
///
/// Copied from [goalContribution].
class GoalContributionProvider
    extends AutoDisposeFutureProvider<GoalContribution?> {
  /// Provider for a specific contribution by ID
  ///
  /// Copied from [goalContribution].
  GoalContributionProvider(String goalId, String contributionId)
    : this._internal(
        (ref) => goalContribution(
          ref as GoalContributionRef,
          goalId,
          contributionId,
        ),
        from: goalContributionProvider,
        name: r'goalContributionProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$goalContributionHash,
        dependencies: GoalContributionFamily._dependencies,
        allTransitiveDependencies:
            GoalContributionFamily._allTransitiveDependencies,
        goalId: goalId,
        contributionId: contributionId,
      );

  GoalContributionProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.goalId,
    required this.contributionId,
  }) : super.internal();

  final String goalId;
  final String contributionId;

  @override
  Override overrideWith(
    FutureOr<GoalContribution?> Function(GoalContributionRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GoalContributionProvider._internal(
        (ref) => create(ref as GoalContributionRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        goalId: goalId,
        contributionId: contributionId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<GoalContribution?> createElement() {
    return _GoalContributionProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GoalContributionProvider &&
        other.goalId == goalId &&
        other.contributionId == contributionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, goalId.hashCode);
    hash = _SystemHash.combine(hash, contributionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin GoalContributionRef on AutoDisposeFutureProviderRef<GoalContribution?> {
  /// The parameter `goalId` of this provider.
  String get goalId;

  /// The parameter `contributionId` of this provider.
  String get contributionId;
}

class _GoalContributionProviderElement
    extends AutoDisposeFutureProviderElement<GoalContribution?>
    with GoalContributionRef {
  _GoalContributionProviderElement(super.provider);

  @override
  String get goalId => (origin as GoalContributionProvider).goalId;
  @override
  String get contributionId =>
      (origin as GoalContributionProvider).contributionId;
}

String _$watchGoalContributionsHash() =>
    r'b4b0064a5b2800bca3e5968a11332c61718424cb';

/// Provider for watching contributions for a goal (real-time stream)
///
/// Copied from [watchGoalContributions].
@ProviderFor(watchGoalContributions)
const watchGoalContributionsProvider = WatchGoalContributionsFamily();

/// Provider for watching contributions for a goal (real-time stream)
///
/// Copied from [watchGoalContributions].
class WatchGoalContributionsFamily
    extends Family<AsyncValue<List<GoalContribution>>> {
  /// Provider for watching contributions for a goal (real-time stream)
  ///
  /// Copied from [watchGoalContributions].
  const WatchGoalContributionsFamily();

  /// Provider for watching contributions for a goal (real-time stream)
  ///
  /// Copied from [watchGoalContributions].
  WatchGoalContributionsProvider call(String goalId) {
    return WatchGoalContributionsProvider(goalId);
  }

  @override
  WatchGoalContributionsProvider getProviderOverride(
    covariant WatchGoalContributionsProvider provider,
  ) {
    return call(provider.goalId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'watchGoalContributionsProvider';
}

/// Provider for watching contributions for a goal (real-time stream)
///
/// Copied from [watchGoalContributions].
class WatchGoalContributionsProvider
    extends AutoDisposeStreamProvider<List<GoalContribution>> {
  /// Provider for watching contributions for a goal (real-time stream)
  ///
  /// Copied from [watchGoalContributions].
  WatchGoalContributionsProvider(String goalId)
    : this._internal(
        (ref) =>
            watchGoalContributions(ref as WatchGoalContributionsRef, goalId),
        from: watchGoalContributionsProvider,
        name: r'watchGoalContributionsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$watchGoalContributionsHash,
        dependencies: WatchGoalContributionsFamily._dependencies,
        allTransitiveDependencies:
            WatchGoalContributionsFamily._allTransitiveDependencies,
        goalId: goalId,
      );

  WatchGoalContributionsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.goalId,
  }) : super.internal();

  final String goalId;

  @override
  Override overrideWith(
    Stream<List<GoalContribution>> Function(WatchGoalContributionsRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: WatchGoalContributionsProvider._internal(
        (ref) => create(ref as WatchGoalContributionsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        goalId: goalId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<List<GoalContribution>> createElement() {
    return _WatchGoalContributionsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WatchGoalContributionsProvider && other.goalId == goalId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, goalId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WatchGoalContributionsRef
    on AutoDisposeStreamProviderRef<List<GoalContribution>> {
  /// The parameter `goalId` of this provider.
  String get goalId;
}

class _WatchGoalContributionsProviderElement
    extends AutoDisposeStreamProviderElement<List<GoalContribution>>
    with WatchGoalContributionsRef {
  _WatchGoalContributionsProviderElement(super.provider);

  @override
  String get goalId => (origin as WatchGoalContributionsProvider).goalId;
}

String _$watchActiveGoalContributionsHash() =>
    r'a187fd11605de26b10e5363628a742e1d6fc5a9b';

/// Provider for watching active contributions for a goal (real-time stream)
///
/// Copied from [watchActiveGoalContributions].
@ProviderFor(watchActiveGoalContributions)
const watchActiveGoalContributionsProvider =
    WatchActiveGoalContributionsFamily();

/// Provider for watching active contributions for a goal (real-time stream)
///
/// Copied from [watchActiveGoalContributions].
class WatchActiveGoalContributionsFamily
    extends Family<AsyncValue<List<GoalContribution>>> {
  /// Provider for watching active contributions for a goal (real-time stream)
  ///
  /// Copied from [watchActiveGoalContributions].
  const WatchActiveGoalContributionsFamily();

  /// Provider for watching active contributions for a goal (real-time stream)
  ///
  /// Copied from [watchActiveGoalContributions].
  WatchActiveGoalContributionsProvider call(String goalId) {
    return WatchActiveGoalContributionsProvider(goalId);
  }

  @override
  WatchActiveGoalContributionsProvider getProviderOverride(
    covariant WatchActiveGoalContributionsProvider provider,
  ) {
    return call(provider.goalId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'watchActiveGoalContributionsProvider';
}

/// Provider for watching active contributions for a goal (real-time stream)
///
/// Copied from [watchActiveGoalContributions].
class WatchActiveGoalContributionsProvider
    extends AutoDisposeStreamProvider<List<GoalContribution>> {
  /// Provider for watching active contributions for a goal (real-time stream)
  ///
  /// Copied from [watchActiveGoalContributions].
  WatchActiveGoalContributionsProvider(String goalId)
    : this._internal(
        (ref) => watchActiveGoalContributions(
          ref as WatchActiveGoalContributionsRef,
          goalId,
        ),
        from: watchActiveGoalContributionsProvider,
        name: r'watchActiveGoalContributionsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$watchActiveGoalContributionsHash,
        dependencies: WatchActiveGoalContributionsFamily._dependencies,
        allTransitiveDependencies:
            WatchActiveGoalContributionsFamily._allTransitiveDependencies,
        goalId: goalId,
      );

  WatchActiveGoalContributionsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.goalId,
  }) : super.internal();

  final String goalId;

  @override
  Override overrideWith(
    Stream<List<GoalContribution>> Function(
      WatchActiveGoalContributionsRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: WatchActiveGoalContributionsProvider._internal(
        (ref) => create(ref as WatchActiveGoalContributionsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        goalId: goalId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<List<GoalContribution>> createElement() {
    return _WatchActiveGoalContributionsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WatchActiveGoalContributionsProvider &&
        other.goalId == goalId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, goalId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WatchActiveGoalContributionsRef
    on AutoDisposeStreamProviderRef<List<GoalContribution>> {
  /// The parameter `goalId` of this provider.
  String get goalId;
}

class _WatchActiveGoalContributionsProviderElement
    extends AutoDisposeStreamProviderElement<List<GoalContribution>>
    with WatchActiveGoalContributionsRef {
  _WatchActiveGoalContributionsProviderElement(super.provider);

  @override
  String get goalId => (origin as WatchActiveGoalContributionsProvider).goalId;
}

String _$goalContributionStatsHash() =>
    r'6b92920708bfdba6744a76c222c2ed1d7c567461';

/// Provider for contribution statistics for a goal
///
/// Copied from [goalContributionStats].
@ProviderFor(goalContributionStats)
const goalContributionStatsProvider = GoalContributionStatsFamily();

/// Provider for contribution statistics for a goal
///
/// Copied from [goalContributionStats].
class GoalContributionStatsFamily
    extends Family<AsyncValue<Map<String, dynamic>>> {
  /// Provider for contribution statistics for a goal
  ///
  /// Copied from [goalContributionStats].
  const GoalContributionStatsFamily();

  /// Provider for contribution statistics for a goal
  ///
  /// Copied from [goalContributionStats].
  GoalContributionStatsProvider call(String goalId) {
    return GoalContributionStatsProvider(goalId);
  }

  @override
  GoalContributionStatsProvider getProviderOverride(
    covariant GoalContributionStatsProvider provider,
  ) {
    return call(provider.goalId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'goalContributionStatsProvider';
}

/// Provider for contribution statistics for a goal
///
/// Copied from [goalContributionStats].
class GoalContributionStatsProvider
    extends AutoDisposeFutureProvider<Map<String, dynamic>> {
  /// Provider for contribution statistics for a goal
  ///
  /// Copied from [goalContributionStats].
  GoalContributionStatsProvider(String goalId)
    : this._internal(
        (ref) => goalContributionStats(ref as GoalContributionStatsRef, goalId),
        from: goalContributionStatsProvider,
        name: r'goalContributionStatsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$goalContributionStatsHash,
        dependencies: GoalContributionStatsFamily._dependencies,
        allTransitiveDependencies:
            GoalContributionStatsFamily._allTransitiveDependencies,
        goalId: goalId,
      );

  GoalContributionStatsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.goalId,
  }) : super.internal();

  final String goalId;

  @override
  Override overrideWith(
    FutureOr<Map<String, dynamic>> Function(GoalContributionStatsRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GoalContributionStatsProvider._internal(
        (ref) => create(ref as GoalContributionStatsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        goalId: goalId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, dynamic>> createElement() {
    return _GoalContributionStatsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GoalContributionStatsProvider && other.goalId == goalId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, goalId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin GoalContributionStatsRef
    on AutoDisposeFutureProviderRef<Map<String, dynamic>> {
  /// The parameter `goalId` of this provider.
  String get goalId;
}

class _GoalContributionStatsProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, dynamic>>
    with GoalContributionStatsRef {
  _GoalContributionStatsProviderElement(super.provider);

  @override
  String get goalId => (origin as GoalContributionStatsProvider).goalId;
}

String _$totalGoalContributionAmountHash() =>
    r'806ead7f5727a3a87c20c15b14c1f8778f9b4587';

/// Provider for total contribution amount for a goal
///
/// Copied from [totalGoalContributionAmount].
@ProviderFor(totalGoalContributionAmount)
const totalGoalContributionAmountProvider = TotalGoalContributionAmountFamily();

/// Provider for total contribution amount for a goal
///
/// Copied from [totalGoalContributionAmount].
class TotalGoalContributionAmountFamily extends Family<AsyncValue<int>> {
  /// Provider for total contribution amount for a goal
  ///
  /// Copied from [totalGoalContributionAmount].
  const TotalGoalContributionAmountFamily();

  /// Provider for total contribution amount for a goal
  ///
  /// Copied from [totalGoalContributionAmount].
  TotalGoalContributionAmountProvider call(String goalId) {
    return TotalGoalContributionAmountProvider(goalId);
  }

  @override
  TotalGoalContributionAmountProvider getProviderOverride(
    covariant TotalGoalContributionAmountProvider provider,
  ) {
    return call(provider.goalId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'totalGoalContributionAmountProvider';
}

/// Provider for total contribution amount for a goal
///
/// Copied from [totalGoalContributionAmount].
class TotalGoalContributionAmountProvider
    extends AutoDisposeFutureProvider<int> {
  /// Provider for total contribution amount for a goal
  ///
  /// Copied from [totalGoalContributionAmount].
  TotalGoalContributionAmountProvider(String goalId)
    : this._internal(
        (ref) => totalGoalContributionAmount(
          ref as TotalGoalContributionAmountRef,
          goalId,
        ),
        from: totalGoalContributionAmountProvider,
        name: r'totalGoalContributionAmountProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$totalGoalContributionAmountHash,
        dependencies: TotalGoalContributionAmountFamily._dependencies,
        allTransitiveDependencies:
            TotalGoalContributionAmountFamily._allTransitiveDependencies,
        goalId: goalId,
      );

  TotalGoalContributionAmountProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.goalId,
  }) : super.internal();

  final String goalId;

  @override
  Override overrideWith(
    FutureOr<int> Function(TotalGoalContributionAmountRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TotalGoalContributionAmountProvider._internal(
        (ref) => create(ref as TotalGoalContributionAmountRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        goalId: goalId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<int> createElement() {
    return _TotalGoalContributionAmountProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TotalGoalContributionAmountProvider &&
        other.goalId == goalId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, goalId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TotalGoalContributionAmountRef on AutoDisposeFutureProviderRef<int> {
  /// The parameter `goalId` of this provider.
  String get goalId;
}

class _TotalGoalContributionAmountProviderElement
    extends AutoDisposeFutureProviderElement<int>
    with TotalGoalContributionAmountRef {
  _TotalGoalContributionAmountProviderElement(super.provider);

  @override
  String get goalId => (origin as TotalGoalContributionAmountProvider).goalId;
}

String _$goalContributionCountHash() =>
    r'8d2c59482b033af07847874e2188cc7a0e6dccaa';

/// Provider for contribution count for a goal
///
/// Copied from [goalContributionCount].
@ProviderFor(goalContributionCount)
const goalContributionCountProvider = GoalContributionCountFamily();

/// Provider for contribution count for a goal
///
/// Copied from [goalContributionCount].
class GoalContributionCountFamily extends Family<AsyncValue<int>> {
  /// Provider for contribution count for a goal
  ///
  /// Copied from [goalContributionCount].
  const GoalContributionCountFamily();

  /// Provider for contribution count for a goal
  ///
  /// Copied from [goalContributionCount].
  GoalContributionCountProvider call(String goalId) {
    return GoalContributionCountProvider(goalId);
  }

  @override
  GoalContributionCountProvider getProviderOverride(
    covariant GoalContributionCountProvider provider,
  ) {
    return call(provider.goalId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'goalContributionCountProvider';
}

/// Provider for contribution count for a goal
///
/// Copied from [goalContributionCount].
class GoalContributionCountProvider extends AutoDisposeFutureProvider<int> {
  /// Provider for contribution count for a goal
  ///
  /// Copied from [goalContributionCount].
  GoalContributionCountProvider(String goalId)
    : this._internal(
        (ref) => goalContributionCount(ref as GoalContributionCountRef, goalId),
        from: goalContributionCountProvider,
        name: r'goalContributionCountProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$goalContributionCountHash,
        dependencies: GoalContributionCountFamily._dependencies,
        allTransitiveDependencies:
            GoalContributionCountFamily._allTransitiveDependencies,
        goalId: goalId,
      );

  GoalContributionCountProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.goalId,
  }) : super.internal();

  final String goalId;

  @override
  Override overrideWith(
    FutureOr<int> Function(GoalContributionCountRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GoalContributionCountProvider._internal(
        (ref) => create(ref as GoalContributionCountRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        goalId: goalId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<int> createElement() {
    return _GoalContributionCountProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GoalContributionCountProvider && other.goalId == goalId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, goalId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin GoalContributionCountRef on AutoDisposeFutureProviderRef<int> {
  /// The parameter `goalId` of this provider.
  String get goalId;
}

class _GoalContributionCountProviderElement
    extends AutoDisposeFutureProviderElement<int>
    with GoalContributionCountRef {
  _GoalContributionCountProviderElement(super.provider);

  @override
  String get goalId => (origin as GoalContributionCountProvider).goalId;
}

String _$recentGoalContributionsHash() =>
    r'277ad9fa2727f2a63ce272ac41ea823d0783c186';

/// Provider for recent contributions for a goal
///
/// Copied from [recentGoalContributions].
@ProviderFor(recentGoalContributions)
const recentGoalContributionsProvider = RecentGoalContributionsFamily();

/// Provider for recent contributions for a goal
///
/// Copied from [recentGoalContributions].
class RecentGoalContributionsFamily
    extends Family<AsyncValue<List<GoalContribution>>> {
  /// Provider for recent contributions for a goal
  ///
  /// Copied from [recentGoalContributions].
  const RecentGoalContributionsFamily();

  /// Provider for recent contributions for a goal
  ///
  /// Copied from [recentGoalContributions].
  RecentGoalContributionsProvider call(String goalId, {int limit = 5}) {
    return RecentGoalContributionsProvider(goalId, limit: limit);
  }

  @override
  RecentGoalContributionsProvider getProviderOverride(
    covariant RecentGoalContributionsProvider provider,
  ) {
    return call(provider.goalId, limit: provider.limit);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'recentGoalContributionsProvider';
}

/// Provider for recent contributions for a goal
///
/// Copied from [recentGoalContributions].
class RecentGoalContributionsProvider
    extends AutoDisposeFutureProvider<List<GoalContribution>> {
  /// Provider for recent contributions for a goal
  ///
  /// Copied from [recentGoalContributions].
  RecentGoalContributionsProvider(String goalId, {int limit = 5})
    : this._internal(
        (ref) => recentGoalContributions(
          ref as RecentGoalContributionsRef,
          goalId,
          limit: limit,
        ),
        from: recentGoalContributionsProvider,
        name: r'recentGoalContributionsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$recentGoalContributionsHash,
        dependencies: RecentGoalContributionsFamily._dependencies,
        allTransitiveDependencies:
            RecentGoalContributionsFamily._allTransitiveDependencies,
        goalId: goalId,
        limit: limit,
      );

  RecentGoalContributionsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.goalId,
    required this.limit,
  }) : super.internal();

  final String goalId;
  final int limit;

  @override
  Override overrideWith(
    FutureOr<List<GoalContribution>> Function(
      RecentGoalContributionsRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: RecentGoalContributionsProvider._internal(
        (ref) => create(ref as RecentGoalContributionsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        goalId: goalId,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<GoalContribution>> createElement() {
    return _RecentGoalContributionsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is RecentGoalContributionsProvider &&
        other.goalId == goalId &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, goalId.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin RecentGoalContributionsRef
    on AutoDisposeFutureProviderRef<List<GoalContribution>> {
  /// The parameter `goalId` of this provider.
  String get goalId;

  /// The parameter `limit` of this provider.
  int get limit;
}

class _RecentGoalContributionsProviderElement
    extends AutoDisposeFutureProviderElement<List<GoalContribution>>
    with RecentGoalContributionsRef {
  _RecentGoalContributionsProviderElement(super.provider);

  @override
  String get goalId => (origin as RecentGoalContributionsProvider).goalId;
  @override
  int get limit => (origin as RecentGoalContributionsProvider).limit;
}

String _$goalContributionCreationHash() =>
    r'a98fd56fa5bb099904224f0a29253afce23e5763';

/// Provider for contribution creation state management
///
/// Copied from [GoalContributionCreation].
@ProviderFor(GoalContributionCreation)
final goalContributionCreationProvider =
    AutoDisposeNotifierProvider<
      GoalContributionCreation,
      AsyncValue<void>
    >.internal(
      GoalContributionCreation.new,
      name: r'goalContributionCreationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$goalContributionCreationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$GoalContributionCreation = AutoDisposeNotifier<AsyncValue<void>>;
String _$goalContributionUpdateHash() =>
    r'a8916831e8af962aed6d9f74b39c14e3bcc07a88';

/// Provider for contribution update state management
///
/// Copied from [GoalContributionUpdate].
@ProviderFor(GoalContributionUpdate)
final goalContributionUpdateProvider =
    AutoDisposeNotifierProvider<
      GoalContributionUpdate,
      AsyncValue<void>
    >.internal(
      GoalContributionUpdate.new,
      name: r'goalContributionUpdateProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$goalContributionUpdateHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$GoalContributionUpdate = AutoDisposeNotifier<AsyncValue<void>>;
String _$goalContributionDeletionHash() =>
    r'3c95a5e7eb4664e5ec7cb973ac3aa33ad1f8870b';

/// Provider for contribution deletion state management
///
/// Copied from [GoalContributionDeletion].
@ProviderFor(GoalContributionDeletion)
final goalContributionDeletionProvider =
    AutoDisposeNotifierProvider<
      GoalContributionDeletion,
      AsyncValue<void>
    >.internal(
      GoalContributionDeletion.new,
      name: r'goalContributionDeletionProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$goalContributionDeletionHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$GoalContributionDeletion = AutoDisposeNotifier<AsyncValue<void>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
