import 'package:budapp/data/models/goal_contribution.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'goal_contribution_providers.g.dart';

/// Provider for contributions for a specific goal
@riverpod
Future<List<GoalContribution>> goalContributions(Ref ref, String goalId) async {
  final repository = ref.watch(goalContributionRepositoryProvider);
  return repository.getContributionsForGoal(goalId);
}

/// Provider for active contributions for a specific goal
@riverpod
Future<List<GoalContribution>> activeGoalContributions(
  Ref ref,
  String goalId,
) async {
  final repository = ref.watch(goalContributionRepositoryProvider);
  return repository.getActiveContributionsForGoal(goalId);
}

/// Provider for a specific contribution by ID
@riverpod
Future<GoalContribution?> goalContribution(
  Ref ref,
  String goalId,
  String contributionId,
) async {
  final repository = ref.watch(goalContributionRepositoryProvider);
  return repository.getContributionById(goalId, contributionId);
}

/// Provider for watching contributions for a goal (real-time stream)
@riverpod
Stream<List<GoalContribution>> watchGoalContributions(Ref ref, String goalId) {
  final repository = ref.watch(goalContributionRepositoryProvider);
  return repository.watchContributionsForGoal(goalId);
}

/// Provider for watching active contributions for a goal (real-time stream)
@riverpod
Stream<List<GoalContribution>> watchActiveGoalContributions(
  Ref ref,
  String goalId,
) {
  final repository = ref.watch(goalContributionRepositoryProvider);
  return repository.watchActiveContributionsForGoal(goalId);
}

/// Provider for contribution statistics for a goal
@riverpod
Future<Map<String, dynamic>> goalContributionStats(
  Ref ref,
  String goalId,
) async {
  final repository = ref.watch(goalContributionRepositoryProvider);
  return repository.getContributionStatistics(goalId);
}

/// Provider for total contribution amount for a goal
@riverpod
Future<int> totalGoalContributionAmount(Ref ref, String goalId) async {
  final repository = ref.watch(goalContributionRepositoryProvider);
  return repository.getTotalContributionAmountForGoal(goalId);
}

/// Provider for contribution count for a goal
@riverpod
Future<int> goalContributionCount(Ref ref, String goalId) async {
  final repository = ref.watch(goalContributionRepositoryProvider);
  return repository.getContributionCountForGoal(goalId);
}

/// Provider for recent contributions for a goal
@riverpod
Future<List<GoalContribution>> recentGoalContributions(
  Ref ref,
  String goalId, {
  int limit = 5,
}) async {
  final repository = ref.watch(goalContributionRepositoryProvider);
  return repository.getRecentContributions(goalId, limit: limit);
}

/// Provider for contribution creation state management
@riverpod
class GoalContributionCreation extends _$GoalContributionCreation {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  /// Create a new contribution
  Future<void> createContribution(
    String goalId,
    GoalContribution contribution,
  ) async {
    state = const AsyncValue.loading();
    try {
      final repository = ref.read(goalContributionRepositoryProvider);
      await repository.createContribution(goalId, contribution);
      state = const AsyncValue.data(null);

      // Invalidate related providers to refresh data
      ref
        ..invalidate(goalContributionsProvider(goalId))
        ..invalidate(activeGoalContributionsProvider(goalId))
        ..invalidate(goalContributionStatsProvider(goalId))
        ..invalidate(totalGoalContributionAmountProvider(goalId))
        ..invalidate(goalContributionCountProvider(goalId))
        ..invalidate(recentGoalContributionsProvider(goalId));
    } on Exception catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Provider for contribution update state management
@riverpod
class GoalContributionUpdate extends _$GoalContributionUpdate {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  /// Update an existing contribution
  Future<void> updateContribution(
    String goalId,
    String contributionId,
    GoalContribution contribution,
  ) async {
    state = const AsyncValue.loading();
    try {
      final repository = ref.read(goalContributionRepositoryProvider);
      await repository.updateContribution(goalId, contributionId, contribution);
      state = const AsyncValue.data(null);

      // Invalidate related providers to refresh data
      ref
        ..invalidate(goalContributionsProvider(goalId))
        ..invalidate(activeGoalContributionsProvider(goalId))
        ..invalidate(goalContributionProvider(goalId, contributionId))
        ..invalidate(goalContributionStatsProvider(goalId))
        ..invalidate(totalGoalContributionAmountProvider(goalId))
        ..invalidate(recentGoalContributionsProvider(goalId));
    } on Exception catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Provider for contribution deletion state management
@riverpod
class GoalContributionDeletion extends _$GoalContributionDeletion {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  /// Delete a contribution
  Future<void> deleteContribution(String goalId, String contributionId) async {
    state = const AsyncValue.loading();
    try {
      final repository = ref.read(goalContributionRepositoryProvider);
      await repository.deleteContribution(goalId, contributionId);
      state = const AsyncValue.data(null);

      // Invalidate related providers to refresh data
      ref
        ..invalidate(goalContributionsProvider(goalId))
        ..invalidate(activeGoalContributionsProvider(goalId))
        ..invalidate(goalContributionProvider(goalId, contributionId))
        ..invalidate(goalContributionStatsProvider(goalId))
        ..invalidate(totalGoalContributionAmountProvider(goalId))
        ..invalidate(goalContributionCountProvider(goalId))
        ..invalidate(recentGoalContributionsProvider(goalId));
    } on Exception catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
