import 'package:budapp/data/models/goal.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'goal_providers.g.dart';

/// Provider for active user goals only
@riverpod
Future<List<Goal>> activeUserGoals(Ref ref) async {
  final repository = ref.watch(goalRepositoryProvider);
  return repository.getActiveGoals();
}

/// Provider for a specific goal by ID
@riverpod
Future<Goal?> goal(Ref ref, String goalId) async {
  final repository = ref.watch(goalRepositoryProvider);
  return repository.getGoalById(goalId);
}

/// Provider for watching user goals (real-time stream)
@riverpod
Stream<List<Goal>> watchUserGoals(Ref ref, String userId) {
  final repository = ref.watch(goalRepositoryProvider);
  return repository.watchUserGoals(userId);
}

/// Provider for watching a specific goal (real-time stream)
@riverpod
Stream<Goal?> watchGoal(Ref ref, String goalId) {
  final repository = ref.watch(goalRepositoryProvider);
  return repository.watchGoal(goalId);
}

/// Provider for goal statistics
@riverpod
Future<Map<String, dynamic>> goalStats(Ref ref, String goalId) async {
  final repository = ref.watch(goalRepositoryProvider);
  return repository.getGoalStats(goalId);
}

/// Provider for user goal summary
@riverpod
Future<Map<String, dynamic>> userGoalSummary(Ref ref, String userId) async {
  final repository = ref.watch(goalRepositoryProvider);
  return repository.getUserGoalSummary(userId);
}

/// Provider for goal creation state management
@riverpod
class GoalCreation extends _$GoalCreation {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  /// Create a new goal
  Future<void> createGoal(Goal goal) async {
    state = const AsyncValue.loading();
    try {
      final repository = ref.read(goalRepositoryProvider);
      await repository.createGoal(goal);
      state = const AsyncValue.data(null);

      // Invalidate related providers to refresh data
      ref.invalidate(activeUserGoalsProvider);
    } on Exception catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Provider for goal update state management
@riverpod
class GoalUpdate extends _$GoalUpdate {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  /// Update an existing goal
  Future<void> updateGoal(String goalId, Goal goal) async {
    state = const AsyncValue.loading();
    try {
      final repository = ref.read(goalRepositoryProvider);
      await repository.updateGoal(goalId, goal);
      state = const AsyncValue.data(null);

      // Invalidate related providers to refresh data
      ref
        ..invalidate(activeUserGoalsProvider)
        ..invalidate(goalProvider(goalId));
    } on Exception catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Provider for goal deletion state management
@riverpod
class GoalDeletion extends _$GoalDeletion {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  /// Delete a goal
  Future<void> deleteGoal(String goalId) async {
    state = const AsyncValue.loading();
    try {
      final repository = ref.read(goalRepositoryProvider);
      await repository.deleteGoal(goalId);
      state = const AsyncValue.data(null);

      // Invalidate related providers to refresh data
      ref
        ..invalidate(activeUserGoalsProvider)
        ..invalidate(goalProvider(goalId));
    } on Exception catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
