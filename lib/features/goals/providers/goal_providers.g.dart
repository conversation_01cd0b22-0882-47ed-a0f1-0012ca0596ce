// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'goal_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$activeUserGoalsHash() => r'2bebcd960add812fbbe33968972823176ee9b670';

/// Provider for active user goals only
///
/// Copied from [activeUserGoals].
@ProviderFor(activeUserGoals)
final activeUserGoalsProvider = AutoDisposeFutureProvider<List<Goal>>.internal(
  activeUserGoals,
  name: r'activeUserGoalsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$activeUserGoalsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ActiveUserGoalsRef = AutoDisposeFutureProviderRef<List<Goal>>;
String _$goalHash() => r'5a3c08a40da8c273747a6256805f19d2219a4cf7';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for a specific goal by ID
///
/// Copied from [goal].
@ProviderFor(goal)
const goalProvider = GoalFamily();

/// Provider for a specific goal by ID
///
/// Copied from [goal].
class GoalFamily extends Family<AsyncValue<Goal?>> {
  /// Provider for a specific goal by ID
  ///
  /// Copied from [goal].
  const GoalFamily();

  /// Provider for a specific goal by ID
  ///
  /// Copied from [goal].
  GoalProvider call(String goalId) {
    return GoalProvider(goalId);
  }

  @override
  GoalProvider getProviderOverride(covariant GoalProvider provider) {
    return call(provider.goalId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'goalProvider';
}

/// Provider for a specific goal by ID
///
/// Copied from [goal].
class GoalProvider extends AutoDisposeFutureProvider<Goal?> {
  /// Provider for a specific goal by ID
  ///
  /// Copied from [goal].
  GoalProvider(String goalId)
    : this._internal(
        (ref) => goal(ref as GoalRef, goalId),
        from: goalProvider,
        name: r'goalProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$goalHash,
        dependencies: GoalFamily._dependencies,
        allTransitiveDependencies: GoalFamily._allTransitiveDependencies,
        goalId: goalId,
      );

  GoalProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.goalId,
  }) : super.internal();

  final String goalId;

  @override
  Override overrideWith(FutureOr<Goal?> Function(GoalRef provider) create) {
    return ProviderOverride(
      origin: this,
      override: GoalProvider._internal(
        (ref) => create(ref as GoalRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        goalId: goalId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Goal?> createElement() {
    return _GoalProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GoalProvider && other.goalId == goalId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, goalId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin GoalRef on AutoDisposeFutureProviderRef<Goal?> {
  /// The parameter `goalId` of this provider.
  String get goalId;
}

class _GoalProviderElement extends AutoDisposeFutureProviderElement<Goal?>
    with GoalRef {
  _GoalProviderElement(super.provider);

  @override
  String get goalId => (origin as GoalProvider).goalId;
}

String _$watchUserGoalsHash() => r'1e590ce6b97b11c3e605d2e2d60ae372ca7419a0';

/// Provider for watching user goals (real-time stream)
///
/// Copied from [watchUserGoals].
@ProviderFor(watchUserGoals)
const watchUserGoalsProvider = WatchUserGoalsFamily();

/// Provider for watching user goals (real-time stream)
///
/// Copied from [watchUserGoals].
class WatchUserGoalsFamily extends Family<AsyncValue<List<Goal>>> {
  /// Provider for watching user goals (real-time stream)
  ///
  /// Copied from [watchUserGoals].
  const WatchUserGoalsFamily();

  /// Provider for watching user goals (real-time stream)
  ///
  /// Copied from [watchUserGoals].
  WatchUserGoalsProvider call(String userId) {
    return WatchUserGoalsProvider(userId);
  }

  @override
  WatchUserGoalsProvider getProviderOverride(
    covariant WatchUserGoalsProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'watchUserGoalsProvider';
}

/// Provider for watching user goals (real-time stream)
///
/// Copied from [watchUserGoals].
class WatchUserGoalsProvider extends AutoDisposeStreamProvider<List<Goal>> {
  /// Provider for watching user goals (real-time stream)
  ///
  /// Copied from [watchUserGoals].
  WatchUserGoalsProvider(String userId)
    : this._internal(
        (ref) => watchUserGoals(ref as WatchUserGoalsRef, userId),
        from: watchUserGoalsProvider,
        name: r'watchUserGoalsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$watchUserGoalsHash,
        dependencies: WatchUserGoalsFamily._dependencies,
        allTransitiveDependencies:
            WatchUserGoalsFamily._allTransitiveDependencies,
        userId: userId,
      );

  WatchUserGoalsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    Stream<List<Goal>> Function(WatchUserGoalsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: WatchUserGoalsProvider._internal(
        (ref) => create(ref as WatchUserGoalsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<List<Goal>> createElement() {
    return _WatchUserGoalsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WatchUserGoalsProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WatchUserGoalsRef on AutoDisposeStreamProviderRef<List<Goal>> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _WatchUserGoalsProviderElement
    extends AutoDisposeStreamProviderElement<List<Goal>>
    with WatchUserGoalsRef {
  _WatchUserGoalsProviderElement(super.provider);

  @override
  String get userId => (origin as WatchUserGoalsProvider).userId;
}

String _$watchGoalHash() => r'47738e7be8dded9bb894ef0f303b2b8d25209bc6';

/// Provider for watching a specific goal (real-time stream)
///
/// Copied from [watchGoal].
@ProviderFor(watchGoal)
const watchGoalProvider = WatchGoalFamily();

/// Provider for watching a specific goal (real-time stream)
///
/// Copied from [watchGoal].
class WatchGoalFamily extends Family<AsyncValue<Goal?>> {
  /// Provider for watching a specific goal (real-time stream)
  ///
  /// Copied from [watchGoal].
  const WatchGoalFamily();

  /// Provider for watching a specific goal (real-time stream)
  ///
  /// Copied from [watchGoal].
  WatchGoalProvider call(String goalId) {
    return WatchGoalProvider(goalId);
  }

  @override
  WatchGoalProvider getProviderOverride(covariant WatchGoalProvider provider) {
    return call(provider.goalId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'watchGoalProvider';
}

/// Provider for watching a specific goal (real-time stream)
///
/// Copied from [watchGoal].
class WatchGoalProvider extends AutoDisposeStreamProvider<Goal?> {
  /// Provider for watching a specific goal (real-time stream)
  ///
  /// Copied from [watchGoal].
  WatchGoalProvider(String goalId)
    : this._internal(
        (ref) => watchGoal(ref as WatchGoalRef, goalId),
        from: watchGoalProvider,
        name: r'watchGoalProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$watchGoalHash,
        dependencies: WatchGoalFamily._dependencies,
        allTransitiveDependencies: WatchGoalFamily._allTransitiveDependencies,
        goalId: goalId,
      );

  WatchGoalProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.goalId,
  }) : super.internal();

  final String goalId;

  @override
  Override overrideWith(Stream<Goal?> Function(WatchGoalRef provider) create) {
    return ProviderOverride(
      origin: this,
      override: WatchGoalProvider._internal(
        (ref) => create(ref as WatchGoalRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        goalId: goalId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<Goal?> createElement() {
    return _WatchGoalProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WatchGoalProvider && other.goalId == goalId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, goalId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WatchGoalRef on AutoDisposeStreamProviderRef<Goal?> {
  /// The parameter `goalId` of this provider.
  String get goalId;
}

class _WatchGoalProviderElement extends AutoDisposeStreamProviderElement<Goal?>
    with WatchGoalRef {
  _WatchGoalProviderElement(super.provider);

  @override
  String get goalId => (origin as WatchGoalProvider).goalId;
}

String _$goalStatsHash() => r'36c51f45fdfe11c0b0c1689a6d730072d83eea63';

/// Provider for goal statistics
///
/// Copied from [goalStats].
@ProviderFor(goalStats)
const goalStatsProvider = GoalStatsFamily();

/// Provider for goal statistics
///
/// Copied from [goalStats].
class GoalStatsFamily extends Family<AsyncValue<Map<String, dynamic>>> {
  /// Provider for goal statistics
  ///
  /// Copied from [goalStats].
  const GoalStatsFamily();

  /// Provider for goal statistics
  ///
  /// Copied from [goalStats].
  GoalStatsProvider call(String goalId) {
    return GoalStatsProvider(goalId);
  }

  @override
  GoalStatsProvider getProviderOverride(covariant GoalStatsProvider provider) {
    return call(provider.goalId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'goalStatsProvider';
}

/// Provider for goal statistics
///
/// Copied from [goalStats].
class GoalStatsProvider
    extends AutoDisposeFutureProvider<Map<String, dynamic>> {
  /// Provider for goal statistics
  ///
  /// Copied from [goalStats].
  GoalStatsProvider(String goalId)
    : this._internal(
        (ref) => goalStats(ref as GoalStatsRef, goalId),
        from: goalStatsProvider,
        name: r'goalStatsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$goalStatsHash,
        dependencies: GoalStatsFamily._dependencies,
        allTransitiveDependencies: GoalStatsFamily._allTransitiveDependencies,
        goalId: goalId,
      );

  GoalStatsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.goalId,
  }) : super.internal();

  final String goalId;

  @override
  Override overrideWith(
    FutureOr<Map<String, dynamic>> Function(GoalStatsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GoalStatsProvider._internal(
        (ref) => create(ref as GoalStatsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        goalId: goalId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, dynamic>> createElement() {
    return _GoalStatsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GoalStatsProvider && other.goalId == goalId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, goalId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin GoalStatsRef on AutoDisposeFutureProviderRef<Map<String, dynamic>> {
  /// The parameter `goalId` of this provider.
  String get goalId;
}

class _GoalStatsProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, dynamic>>
    with GoalStatsRef {
  _GoalStatsProviderElement(super.provider);

  @override
  String get goalId => (origin as GoalStatsProvider).goalId;
}

String _$userGoalSummaryHash() => r'f99b417fd8ea43d2aeca4d74657d5a4abe5d2f75';

/// Provider for user goal summary
///
/// Copied from [userGoalSummary].
@ProviderFor(userGoalSummary)
const userGoalSummaryProvider = UserGoalSummaryFamily();

/// Provider for user goal summary
///
/// Copied from [userGoalSummary].
class UserGoalSummaryFamily extends Family<AsyncValue<Map<String, dynamic>>> {
  /// Provider for user goal summary
  ///
  /// Copied from [userGoalSummary].
  const UserGoalSummaryFamily();

  /// Provider for user goal summary
  ///
  /// Copied from [userGoalSummary].
  UserGoalSummaryProvider call(String userId) {
    return UserGoalSummaryProvider(userId);
  }

  @override
  UserGoalSummaryProvider getProviderOverride(
    covariant UserGoalSummaryProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'userGoalSummaryProvider';
}

/// Provider for user goal summary
///
/// Copied from [userGoalSummary].
class UserGoalSummaryProvider
    extends AutoDisposeFutureProvider<Map<String, dynamic>> {
  /// Provider for user goal summary
  ///
  /// Copied from [userGoalSummary].
  UserGoalSummaryProvider(String userId)
    : this._internal(
        (ref) => userGoalSummary(ref as UserGoalSummaryRef, userId),
        from: userGoalSummaryProvider,
        name: r'userGoalSummaryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$userGoalSummaryHash,
        dependencies: UserGoalSummaryFamily._dependencies,
        allTransitiveDependencies:
            UserGoalSummaryFamily._allTransitiveDependencies,
        userId: userId,
      );

  UserGoalSummaryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    FutureOr<Map<String, dynamic>> Function(UserGoalSummaryRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UserGoalSummaryProvider._internal(
        (ref) => create(ref as UserGoalSummaryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, dynamic>> createElement() {
    return _UserGoalSummaryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UserGoalSummaryProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UserGoalSummaryRef on AutoDisposeFutureProviderRef<Map<String, dynamic>> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _UserGoalSummaryProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, dynamic>>
    with UserGoalSummaryRef {
  _UserGoalSummaryProviderElement(super.provider);

  @override
  String get userId => (origin as UserGoalSummaryProvider).userId;
}

String _$goalCreationHash() => r'8a91464895605cc3eb1a025eec0e16125f7ee0e5';

/// Provider for goal creation state management
///
/// Copied from [GoalCreation].
@ProviderFor(GoalCreation)
final goalCreationProvider =
    AutoDisposeNotifierProvider<GoalCreation, AsyncValue<void>>.internal(
      GoalCreation.new,
      name: r'goalCreationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$goalCreationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$GoalCreation = AutoDisposeNotifier<AsyncValue<void>>;
String _$goalUpdateHash() => r'a7b5d06d1efa177f152261c518237d55ca4f6271';

/// Provider for goal update state management
///
/// Copied from [GoalUpdate].
@ProviderFor(GoalUpdate)
final goalUpdateProvider =
    AutoDisposeNotifierProvider<GoalUpdate, AsyncValue<void>>.internal(
      GoalUpdate.new,
      name: r'goalUpdateProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$goalUpdateHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$GoalUpdate = AutoDisposeNotifier<AsyncValue<void>>;
String _$goalDeletionHash() => r'fe692ce8c4fdf35b3f37158e689e9b0739fd4f9a';

/// Provider for goal deletion state management
///
/// Copied from [GoalDeletion].
@ProviderFor(GoalDeletion)
final goalDeletionProvider =
    AutoDisposeNotifierProvider<GoalDeletion, AsyncValue<void>>.internal(
      GoalDeletion.new,
      name: r'goalDeletionProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$goalDeletionHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$GoalDeletion = AutoDisposeNotifier<AsyncValue<void>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
