import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/goal.dart';
import 'package:budapp/features/goals/presentation/widgets/goal_progress_bar.dart';
import 'package:budapp/features/goals/presentation/widgets/goal_progress_chart.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Summary display types for multiple goals
enum GoalSummaryType { list, grid, compact, dashboard }

/// Widget for displaying progress summary of multiple goals
class GoalProgressSummary extends ConsumerWidget {
  const GoalProgressSummary({
    super.key,
    required this.goals,
    this.type = GoalSummaryType.list,
    this.maxItems,
    this.showOverallProgress = true,
    this.onGoalTap,
  });

  final List<Goal> goals;
  final GoalSummaryType type;
  final int? maxItems;
  final bool showOverallProgress;
  final void Function(Goal goal)? onGoalTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (goals.isEmpty) {
      return const SizedBox.shrink();
    }

    final displayGoals = maxItems != null
        ? goals.take(maxItems!).toList()
        : goals;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showOverallProgress) ...[
          _buildOverallProgress(context, ref),
          const SizedBox(height: DesignTokens.spacing16),
        ],
        _buildGoalsSummary(context, ref, displayGoals),
      ],
    );
  }

  Widget _buildOverallProgress(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final currencyFormatter = ref.watch(currencyFormatterProvider);

    // Calculate overall statistics
    final totalTargetAmount = goals.fold<int>(
      0,
      (sum, goal) => sum + goal.targetAmountCents,
    );
    final totalCurrentAmount = goals.fold<int>(
      0,
      (sum, goal) => sum + goal.currentAmountCents,
    );
    final overallProgress = totalTargetAmount > 0
        ? (totalCurrentAmount / totalTargetAmount).clamp(0.0, 1.0)
        : 0.0;
    final completedGoals = goals.where((goal) => goal.isAchieved).length;

    return Card(
      elevation: 0,
      color: colorScheme.surfaceContainerLow,
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(Icons.flag, color: colorScheme.primary, size: 20),
                const SizedBox(width: DesignTokens.spacing8),
                Text(
                  'Goals Overview',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),

            const SizedBox(height: DesignTokens.spacing12),

            // Statistics row
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Total Progress',
                    '${(overallProgress * 100).toStringAsFixed(1)}%',
                    Icons.trending_up,
                    colorScheme.primary,
                  ),
                ),
                const SizedBox(width: DesignTokens.spacing12),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Completed',
                    '$completedGoals/${goals.length}',
                    Icons.check_circle,
                    colorScheme.secondary,
                  ),
                ),
                const SizedBox(width: DesignTokens.spacing12),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Total Saved',
                    currencyFormatter.formatAmount(totalCurrentAmount),
                    Icons.savings,
                    colorScheme.tertiary,
                  ),
                ),
              ],
            ),

            const SizedBox(height: DesignTokens.spacing12),

            // Overall progress bar
            LinearProgressIndicator(
              value: overallProgress,
              backgroundColor: colorScheme.surfaceContainerHighest.withValues(
                alpha: 0.3,
              ),
              valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
            ),

            const SizedBox(height: DesignTokens.spacing4),

            Text(
              '${currencyFormatter.formatAmount(totalCurrentAmount)} of ${currencyFormatter.formatAmount(totalTargetAmount)} total target',
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacing12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: DesignTokens.spacing4),
          Text(
            value,
            style: theme.textTheme.titleSmall?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildGoalsSummary(
    BuildContext context,
    WidgetRef ref,
    List<Goal> displayGoals,
  ) {
    switch (type) {
      case GoalSummaryType.list:
        return _buildListSummary(context, ref, displayGoals);
      case GoalSummaryType.grid:
        return _buildGridSummary(context, ref, displayGoals);
      case GoalSummaryType.compact:
        return _buildCompactSummary(context, ref, displayGoals);
      case GoalSummaryType.dashboard:
        return _buildDashboardSummary(context, ref, displayGoals);
    }
  }

  Widget _buildListSummary(
    BuildContext context,
    WidgetRef ref,
    List<Goal> displayGoals,
  ) {
    return Column(
      children: displayGoals.map((goal) {
        return Padding(
          padding: const EdgeInsets.only(bottom: DesignTokens.spacing8),
          child: Card(
            elevation: 0,
            color: Theme.of(
              context,
            ).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
            child: InkWell(
              onTap: () => onGoalTap?.call(goal),
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.all(DesignTokens.spacing12),
                child: Row(
                  children: [
                    // Goal icon
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: _getGoalColor(
                          goal,
                          Theme.of(context).colorScheme,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        _getGoalIcon(goal),
                        color: _getGoalColor(
                          goal,
                          Theme.of(context).colorScheme,
                        ),
                        size: 20,
                      ),
                    ),

                    const SizedBox(width: DesignTokens.spacing12),

                    // Goal info and progress
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            goal.name,
                            style: Theme.of(context).textTheme.titleSmall
                                ?.copyWith(fontWeight: FontWeight.w600),
                          ),
                          const SizedBox(height: DesignTokens.spacing4),
                          GoalProgressBar(
                            goal: goal,
                            type: GoalProgressType.compact,
                            showPercentage: true,
                            showAmounts: false,
                            animated: false,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildGridSummary(
    BuildContext context,
    WidgetRef ref,
    List<Goal> displayGoals,
  ) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: DesignTokens.spacing12,
        mainAxisSpacing: DesignTokens.spacing12,
        childAspectRatio: 1.2,
      ),
      itemCount: displayGoals.length,
      itemBuilder: (context, index) {
        final goal = displayGoals[index];
        return Card(
          elevation: 0,
          color: Theme.of(
            context,
          ).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
          child: InkWell(
            onTap: () => onGoalTap?.call(goal),
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(DesignTokens.spacing12),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GoalProgressChart(
                    goal: goal,
                    type: GoalChartType.progressRing,
                    size: 60,
                    strokeWidth: 6,
                    showLabels: true,
                    showAmounts: false,
                    animated: false,
                  ),
                  const SizedBox(height: DesignTokens.spacing8),
                  Text(
                    goal.name,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCompactSummary(
    BuildContext context,
    WidgetRef ref,
    List<Goal> displayGoals,
  ) {
    return Column(
      children: displayGoals.map((goal) {
        return Padding(
          padding: const EdgeInsets.only(bottom: DesignTokens.spacing4),
          child: InkWell(
            onTap: () => onGoalTap?.call(goal),
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: DesignTokens.spacing8,
                vertical: DesignTokens.spacing4,
              ),
              child: GoalProgressBar(
                goal: goal,
                type: GoalProgressType.compact,
                showPercentage: true,
                showAmounts: false,
                animated: false,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDashboardSummary(
    BuildContext context,
    WidgetRef ref,
    List<Goal> displayGoals,
  ) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: displayGoals.map((goal) {
          return Padding(
            padding: const EdgeInsets.only(right: DesignTokens.spacing12),
            child: SizedBox(
              width: 140,
              child: Card(
                elevation: 0,
                color: Theme.of(
                  context,
                ).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                child: InkWell(
                  onTap: () => onGoalTap?.call(goal),
                  borderRadius: BorderRadius.circular(12),
                  child: Padding(
                    padding: const EdgeInsets.all(DesignTokens.spacing12),
                    child: Column(
                      children: [
                        GoalProgressChart(
                          goal: goal,
                          type: GoalChartType.donut,
                          size: 80,
                          strokeWidth: 8,
                          showLabels: true,
                          showAmounts: false,
                          animated: false,
                        ),
                        const SizedBox(height: DesignTokens.spacing8),
                        Text(
                          goal.name,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(fontWeight: FontWeight.w600),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Color _getGoalColor(Goal goal, ColorScheme colorScheme) {
    if (goal.colorHex != null) {
      try {
        return Color(int.parse('0xFF${goal.colorHex!.substring(1)}'));
      } on FormatException {
        // Fall back to theme color if parsing fails
      }
    }
    return colorScheme.primary;
  }

  IconData _getGoalIcon(Goal goal) {
    // You could extend this to use goal.iconName if available
    if (goal.isAchieved) {
      return Icons.check_circle;
    }
    return Icons.flag;
  }
}
