import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/goal.dart';
import 'package:flutter/material.dart';

/// Visual progress indicator types for goals
enum GoalProgressType { linear, circular, compact }

/// Reusable goal progress bar widget for visual progress indication
class GoalProgressBar extends StatelessWidget {
  const GoalProgressBar({
    super.key,
    required this.goal,
    this.type = GoalProgressType.linear,
    this.height = 8.0,
    this.size = 48.0,
    this.showPercentage = true,
    this.showAmounts = false,
    this.animated = true,
  });

  final Goal goal;
  final GoalProgressType type;
  final double height;
  final double size;
  final bool showPercentage;
  final bool showAmounts;
  final bool animated;

  @override
  Widget build(BuildContext context) {
    switch (type) {
      case GoalProgressType.linear:
        return _buildLinearProgress(context);
      case GoalProgressType.circular:
        return _buildCircularProgress(context);
      case GoalProgressType.compact:
        return _buildCompactProgress(context);
    }
  }

  Widget _buildLinearProgress(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final progressValue = goal.progressPercentage;
    final progressColor = _getProgressColor(colorScheme);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Progress bar
        Stack(
          children: [
            // Background track
            Container(
              height: height,
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withValues(
                  alpha: 0.3,
                ),
                borderRadius: BorderRadius.circular(height / 2),
              ),
            ),

            // Progress indicator with animation
            if (animated)
              AnimatedContainer(
                duration: const Duration(milliseconds: 800),
                curve: Curves.easeOutCubic,
                height: height,
                width: MediaQuery.of(context).size.width * progressValue,
                decoration: BoxDecoration(
                  color: progressColor,
                  borderRadius: BorderRadius.circular(height / 2),
                ),
              )
            else
              Container(
                height: height,
                width: MediaQuery.of(context).size.width * progressValue,
                decoration: BoxDecoration(
                  color: progressColor,
                  borderRadius: BorderRadius.circular(height / 2),
                ),
              ),

            // Percentage text overlay (if enabled and progress bar is tall enough)
            if (showPercentage && height >= 20)
              Positioned.fill(
                child: Center(
                  child: Text(
                    '${(progressValue * 100).toStringAsFixed(0)}%',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: _getTextColor(theme, progressColor),
                      fontWeight: FontWeight.w600,
                      fontSize: 10,
                    ),
                  ),
                ),
              ),
          ],
        ),

        // Additional info below progress bar
        if (showPercentage || showAmounts) ...[
          const SizedBox(height: DesignTokens.spacing4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (showPercentage && height < 20)
                Text(
                  '${(progressValue * 100).toStringAsFixed(1)}%',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              if (showAmounts)
                Text(
                  '${goal.currentAmountCents ~/ 100} / ${goal.targetAmountCents ~/ 100}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildCircularProgress(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final progressValue = goal.progressPercentage;
    final progressColor = _getProgressColor(colorScheme);

    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Background circle
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              value: 1,
              strokeWidth: 4,
              valueColor: AlwaysStoppedAnimation<Color>(
                colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
              ),
            ),
          ),

          // Progress circle with animation
          SizedBox(
            width: size,
            height: size,
            child: animated
                ? TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 1200),
                    curve: Curves.easeOutCubic,
                    tween: Tween(begin: 0, end: progressValue),
                    builder: (context, value, child) {
                      return CircularProgressIndicator(
                        value: value,
                        strokeWidth: 4,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          progressColor,
                        ),
                      );
                    },
                  )
                : CircularProgressIndicator(
                    value: progressValue,
                    strokeWidth: 4,
                    valueColor: AlwaysStoppedAnimation<Color>(progressColor),
                  ),
          ),

          // Center text
          if (showPercentage)
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${(progressValue * 100).toStringAsFixed(0)}%',
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (showAmounts) ...[
                  const SizedBox(height: 2),
                  Text(
                    '${goal.currentAmountCents ~/ 100}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildCompactProgress(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final progressValue = goal.progressPercentage;
    final progressColor = _getProgressColor(colorScheme);

    return Row(
      children: [
        // Mini circular progress
        SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            value: progressValue,
            strokeWidth: 2.5,
            valueColor: AlwaysStoppedAnimation<Color>(progressColor),
            backgroundColor: colorScheme.surfaceContainerHighest.withValues(
              alpha: 0.3,
            ),
          ),
        ),

        const SizedBox(width: DesignTokens.spacing8),

        // Progress text
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (showPercentage)
                Text(
                  '${(progressValue * 100).toStringAsFixed(1)}% complete',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              if (showAmounts)
                Text(
                  '${goal.currentAmountCents ~/ 100} of ${goal.targetAmountCents ~/ 100}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getProgressColor(ColorScheme colorScheme) {
    // Use goal's custom color if available
    if (goal.colorHex != null) {
      try {
        return Color(int.parse('0xFF${goal.colorHex!.substring(1)}'));
      } on FormatException {
        // Fall back to theme color if parsing fails
      }
    }

    // Determine color based on progress
    final progress = goal.progressPercentage;
    if (progress >= 1.0) {
      return colorScheme.primary; // Completed
    } else if (progress >= 0.8) {
      return colorScheme.primary; // Near completion
    } else if (progress >= 0.5) {
      return colorScheme.secondary; // Good progress
    } else {
      return colorScheme.tertiary; // Early progress
    }
  }

  Color _getTextColor(ThemeData theme, Color backgroundColor) {
    // Calculate contrast and return appropriate text color
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }
}
