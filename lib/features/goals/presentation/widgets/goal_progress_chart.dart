import 'dart:math' as math;

import 'package:budapp/data/models/goal.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Chart types for goal progress visualization
enum GoalChartType { donut, radialProgress, progressRing }

/// Advanced goal progress chart widget with multiple visualization options
class GoalProgress<PERSON>hart extends ConsumerWidget {
  const GoalProgressChart({
    super.key,
    required this.goal,
    this.type = GoalChartType.donut,
    this.size = 120.0,
    this.strokeWidth = 12.0,
    this.showLabels = true,
    this.showAmounts = true,
    this.animated = true,
  });

  final Goal goal;
  final GoalChartType type;
  final double size;
  final double strokeWidth;
  final bool showLabels;
  final bool showAmounts;
  final bool animated;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    switch (type) {
      case GoalChartType.donut:
        return _buildDonutChart(context, ref);
      case GoalChartType.radialProgress:
        return _buildRadialProgress(context, ref);
      case GoalChartType.progressRing:
        return _buildProgressRing(context, ref);
    }
  }

  Widget _buildDonutChart(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final currencyFormatter = ref.watch(currencyFormatterProvider);

    final progressValue = goal.progressPercentage;
    final progressColor = _getProgressColor(colorScheme);
    final remainingColor = colorScheme.surfaceContainerHighest.withValues(
      alpha: 0.3,
    );

    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Custom painted donut chart
          CustomPaint(
            size: Size(size, size),
            painter: DonutChartPainter(
              progress: animated ? progressValue : progressValue,
              progressColor: progressColor,
              backgroundColor: remainingColor,
              strokeWidth: strokeWidth,
              animated: animated,
            ),
          ),

          // Center content
          SizedBox(
            width: size - strokeWidth * 2 - 16,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (showLabels) ...[
                  Text(
                    '${(progressValue * 100).toStringAsFixed(0)}%',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      color: colorScheme.onSurface,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                ],
                if (showAmounts) ...[
                  Text(
                    currencyFormatter.formatAmount(goal.currentAmountCents),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  Text(
                    'of ${currencyFormatter.formatAmount(goal.targetAmountCents)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRadialProgress(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final currencyFormatter = ref.watch(currencyFormatterProvider);

    final progressValue = goal.progressPercentage;
    final progressColor = _getProgressColor(colorScheme);

    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Radial progress indicator
          SizedBox(
            width: size,
            height: size,
            child: animated
                ? TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 1500),
                    curve: Curves.easeOutCubic,
                    tween: Tween(begin: 0, end: progressValue),
                    builder: (context, value, child) {
                      return CustomPaint(
                        painter: RadialProgressPainter(
                          progress: value,
                          progressColor: progressColor,
                          backgroundColor: colorScheme.surfaceContainerHighest
                              .withValues(alpha: 0.2),
                          strokeWidth: strokeWidth,
                        ),
                      );
                    },
                  )
                : CustomPaint(
                    painter: RadialProgressPainter(
                      progress: progressValue,
                      progressColor: progressColor,
                      backgroundColor: colorScheme.surfaceContainerHighest
                          .withValues(alpha: 0.2),
                      strokeWidth: strokeWidth,
                    ),
                  ),
          ),

          // Center content
          if (showLabels || showAmounts)
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (showLabels)
                  Text(
                    '${(progressValue * 100).toStringAsFixed(1)}%',
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: colorScheme.onSurface,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                if (showAmounts) ...[
                  const SizedBox(height: 4),
                  Text(
                    currencyFormatter.formatAmount(goal.remainingAmountCents),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                  Text(
                    'remaining',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildProgressRing(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final currencyFormatter = ref.watch(currencyFormatterProvider);

    final progressValue = goal.progressPercentage;
    final progressColor = _getProgressColor(colorScheme);

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Progress ring
          SizedBox(
            width: size - 16,
            height: size - 16,
            child: animated
                ? TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 1200),
                    curve: Curves.easeOutCubic,
                    tween: Tween(begin: 0, end: progressValue),
                    builder: (context, value, child) {
                      return CircularProgressIndicator(
                        value: value,
                        strokeWidth: strokeWidth,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          progressColor,
                        ),
                        backgroundColor: colorScheme.surfaceContainerHighest
                            .withValues(alpha: 0.3),
                      );
                    },
                  )
                : CircularProgressIndicator(
                    value: progressValue,
                    strokeWidth: strokeWidth,
                    valueColor: AlwaysStoppedAnimation<Color>(progressColor),
                    backgroundColor: colorScheme.surfaceContainerHighest
                        .withValues(alpha: 0.3),
                  ),
          ),

          // Center content
          Padding(
            padding: EdgeInsets.all(strokeWidth + 8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (showLabels) ...[
                  Icon(
                    goal.isAchieved ? Icons.check_circle : Icons.flag,
                    color: goal.isAchieved
                        ? colorScheme.primary
                        : progressColor,
                    size: 24,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${(progressValue * 100).toStringAsFixed(0)}%',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: colorScheme.onSurface,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
                if (showAmounts) ...[
                  const SizedBox(height: 4),
                  Text(
                    currencyFormatter.formatAmount(goal.currentAmountCents),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getProgressColor(ColorScheme colorScheme) {
    // Use goal's custom color if available
    if (goal.colorHex != null) {
      try {
        return Color(int.parse('0xFF${goal.colorHex!.substring(1)}'));
      } on FormatException {
        // Fall back to theme color if parsing fails
      }
    }

    // Determine color based on progress
    final progress = goal.progressPercentage;
    if (progress >= 1.0) {
      return colorScheme.primary; // Completed
    } else if (progress >= 0.8) {
      return colorScheme.primary; // Near completion
    } else if (progress >= 0.5) {
      return colorScheme.secondary; // Good progress
    } else {
      return colorScheme.tertiary; // Early progress
    }
  }
}

/// Custom painter for donut chart
class DonutChartPainter extends CustomPainter {
  const DonutChartPainter({
    required this.progress,
    required this.progressColor,
    required this.backgroundColor,
    required this.strokeWidth,
    this.animated = true,
  });

  final double progress;
  final Color progressColor;
  final Color backgroundColor;
  final double strokeWidth;
  final bool animated;

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // Background circle
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius, backgroundPaint);

    // Progress arc
    final progressPaint = Paint()
      ..color = progressColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    const startAngle = -math.pi / 2; // Start from top
    final sweepAngle = 2 * math.pi * progress;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Custom painter for radial progress
class RadialProgressPainter extends CustomPainter {
  const RadialProgressPainter({
    required this.progress,
    required this.progressColor,
    required this.backgroundColor,
    required this.strokeWidth,
  });

  final double progress;
  final Color progressColor;
  final Color backgroundColor;
  final double strokeWidth;

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // Background circle
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    canvas.drawCircle(center, radius, backgroundPaint);

    // Progress arc with gradient
    final progressPaint = Paint()
      ..shader = LinearGradient(
        colors: [progressColor.withValues(alpha: 0.7), progressColor],
      ).createShader(Rect.fromCircle(center: center, radius: radius))
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    const startAngle = -math.pi / 2; // Start from top
    final sweepAngle = 2 * math.pi * progress;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
