import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/widgets/forms/configs/goal_contribution_form_config.dart';
import 'package:budapp/widgets/forms/screens/generic_form_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Screen for creating a new contribution to a goal
class GoalContributionCreateScreen extends ConsumerWidget {
  const GoalContributionCreateScreen({super.key, required this.goalId});

  final String goalId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final repository = ref.watch(goalContributionRepositoryProvider);

    return GenericFormScreen(
      config: GoalContributionFormConfig.create(
        goalId: goalId,
        repository: repository,
        onFieldChanged: (fieldKey, value) {
          // Handle field changes if needed for real-time validation
        },
      ),
    );
  }
}
