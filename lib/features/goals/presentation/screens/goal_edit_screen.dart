import 'package:budapp/data/models/goal.dart';
import 'package:budapp/features/goals/providers/goal_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/widgets/forms/configs/goal_form_config.dart';
import 'package:budapp/widgets/forms/screens/generic_form_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Screen for editing an existing goal
class GoalEditScreen extends ConsumerWidget {
  const GoalEditScreen({super.key, required this.goalId});

  final String goalId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final goalAsync = ref.watch(goalProvider(goalId));
    final repository = ref.watch(goalRepositoryProvider);

    return goalAsync.when(
      data: (goal) {
        if (goal == null) {
          return Scaffold(
            appBar: AppBar(title: const Text('Goal Not Found')),
            body: const Center(
              child: Text('The requested goal could not be found.'),
            ),
          );
        }

        return GenericFormScreen<Goal>(
          config: GoalFormConfig.edit(
            goal: goal,
            repository: repository,
            onFieldChanged: (fieldKey, value) {
              // Handle field changes if needed for real-time validation
            },
          ),
        );
      },
      loading: () =>
          const Scaffold(body: Center(child: CircularProgressIndicator())),
      error: (error, stackTrace) => Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              const Text('Failed to load goal'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.invalidate(goalProvider(goalId)),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
