import 'package:budapp/features/goals/providers/goal_contribution_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/widgets/forms/configs/goal_contribution_form_config.dart';
import 'package:budapp/widgets/forms/screens/generic_form_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Screen for editing an existing contribution
class GoalContributionEditScreen extends ConsumerWidget {
  const GoalContributionEditScreen({
    super.key,
    required this.goalId,
    required this.contributionId,
  });

  final String goalId;
  final String contributionId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final contributionAsync = ref.watch(
      goalContributionProvider(goalId, contributionId),
    );
    final repository = ref.watch(goalContributionRepositoryProvider);

    return contributionAsync.when(
      data: (contribution) {
        if (contribution == null) {
          return Scaffold(
            appBar: AppBar(title: const Text('Contribution Not Found')),
            body: const Center(
              child: Text('The requested contribution could not be found.'),
            ),
          );
        }

        return GenericFormScreen(
          config: GoalContributionFormConfig.edit(
            contribution: contribution,
            repository: repository,
            onFieldChanged: (fieldKey, value) {
              // Handle field changes if needed for real-time validation
            },
          ),
        );
      },
      loading: () =>
          const Scaffold(body: Center(child: CircularProgressIndicator())),
      error: (error, stackTrace) => Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              const Text('Failed to load contribution'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.invalidate(
                  goalContributionProvider(goalId, contributionId),
                ),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
