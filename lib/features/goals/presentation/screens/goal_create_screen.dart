import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/widgets/forms/configs/goal_form_config.dart';
import 'package:budapp/widgets/forms/screens/generic_form_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Screen for creating a new goal
class GoalCreateScreen extends ConsumerWidget {
  const GoalCreateScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final repository = ref.watch(goalRepositoryProvider);

    return GenericFormScreen(
      config: GoalFormConfig.create(
        repository: repository,
        onFieldChanged: (fieldKey, value) {
          // Handle field changes if needed for real-time validation
        },
      ),
    );
  }
}
