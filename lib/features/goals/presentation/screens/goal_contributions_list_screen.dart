import 'package:budapp/data/models/goal_contribution.dart';
import 'package:budapp/features/common/widgets/app_bar_helpers.dart';
import 'package:budapp/features/goals/presentation/widgets/contribution_card.dart';
import 'package:budapp/features/goals/providers/goal_contribution_providers.dart';
import 'package:budapp/widgets/common/empty_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Screen displaying a list of contributions for a specific goal
class GoalContributionsListScreen extends ConsumerWidget {
  const GoalContributionsListScreen({super.key, required this.goalId});

  final String goalId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final contributionsAsync = ref.watch(
      activeGoalContributionsProvider(goalId),
    );

    return Scaffold(
      appBar: AppBarHelpers.createStandardScrollableAppBar(
        title: 'Contributions',
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(activeGoalContributionsProvider(goalId));
        },
        child: contributionsAsync.when(
          data: (contributions) =>
              _buildContributionsList(context, ref, contributions),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                const Text('Failed to load contributions'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () =>
                      ref.invalidate(activeGoalContributionsProvider(goalId)),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.push('/goals/$goalId/contributions/create'),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildContributionsList(
    BuildContext context,
    WidgetRef ref,
    List<GoalContribution> contributions,
  ) {
    if (contributions.isEmpty) {
      return const Center(
        child: EmptyState(
          icon: Icons.add_circle_outline,
          title: 'No Contributions Yet',
          message:
              'Start tracking your progress by adding your first contribution to this goal.',
        ),
      );
    }

    // Sort contributions by date (newest first)
    final sortedContributions = List<GoalContribution>.from(contributions)
      ..sort((a, b) {
        final aDate =
            a.contributionDate ?? DateTime.fromMillisecondsSinceEpoch(0);
        final bDate =
            b.contributionDate ?? DateTime.fromMillisecondsSinceEpoch(0);
        return bDate.compareTo(aDate);
      });

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: sortedContributions.length,
      itemBuilder: (context, index) {
        final contribution = sortedContributions[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: ContributionCard(
            contribution: contribution,
            onEdit: () => _editContribution(context, contribution),
            onDelete: () => _deleteContribution(ref, contribution),
          ),
        );
      },
    );
  }

  /// Navigate to edit contribution screen
  void _editContribution(BuildContext context, GoalContribution contribution) {
    context.push(
      '/goals/${contribution.goalId}/contributions/${contribution.id}/edit',
    );
  }

  /// Delete a contribution
  void _deleteContribution(WidgetRef ref, GoalContribution contribution) {
    ref
        .read(goalContributionDeletionProvider.notifier)
        .deleteContribution(contribution.goalId, contribution.id);
  }
}
