import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/models/budget_progress.dart';
import 'package:budapp/data/models/category.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'category_budget_info.freezed.dart';

/// Combined category and budget information for unified display
@freezed
sealed class CategoryBudgetInfo with _$CategoryBudgetInfo {
  const factory CategoryBudgetInfo({
    required Category category,
    Budget? budget, // Null if no budget set for this category
    BudgetProgress? progress, // Null if no budget or no progress data
    @Default(0) double actualSpent, // Actual spending for the period
    @Default(false)
    bool isFallbackBudget, // True if budget is from a previous period
  }) = _CategoryBudgetInfo;

  const CategoryBudgetInfo._();

  /// Whether this category has a budget set
  bool get hasBudget => budget != null;

  /// Budget amount or 0 if no budget
  double get budgetAmount => budget?.plannedAmountCents != null
      ? budget!.plannedAmountCents / 100.0
      : 0;

  /// Progress percentage (0-100+), null if no budget
  double? get progressPercentage => progress?.percentage;

  /// Remaining budget amount, null if no budget
  double? get remainingAmount => progress?.remaining;

  /// Whether the budget is over budget
  bool get isOverBudget => progress?.isOverBudget ?? false;

  /// Whether the budget is at warning level (75% or more)
  bool get isAtWarning =>
      progress?.status == BudgetProgressStatus.warning || isOverBudget;

  /// Budget status for display
  BudgetProgressStatus? get budgetStatus => progress?.status;

  // Removed currencyCode getter - currency is now global preference
}

/// Overall budget summary for a category type
@freezed
sealed class CategoryTypeBudgetSummary with _$CategoryTypeBudgetSummary {
  const factory CategoryTypeBudgetSummary({
    required CategoryType categoryType,
    required double totalBudgetAmount,
    required double totalActualAmount,
    required String currency,
    @Default(0) int categoriesWithBudgets,
    @Default(0) int totalCategories,
  }) = _CategoryTypeBudgetSummary;

  const CategoryTypeBudgetSummary._();

  /// Progress percentage for all budgets of this type
  double get overallProgressPercentage {
    if (totalBudgetAmount == 0) return 0;
    return (totalActualAmount / totalBudgetAmount) * 100;
  }

  /// Remaining amount across all budgets
  double get totalRemaining => totalBudgetAmount - totalActualAmount;

  /// Whether any budgets are over budget
  bool get hasOverBudgets => totalActualAmount > totalBudgetAmount;

  /// Whether at warning level (75% or more)
  bool get isAtWarning => overallProgressPercentage >= 75.0;
}
