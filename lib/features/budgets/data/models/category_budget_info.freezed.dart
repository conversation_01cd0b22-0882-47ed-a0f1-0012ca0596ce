// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'category_budget_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$CategoryBudgetInfo {

 Category get category; Budget? get budget;// Null if no budget set for this category
 BudgetProgress? get progress;// Null if no budget or no progress data
 double get actualSpent;// Actual spending for the period
 bool get isFallbackBudget;
/// Create a copy of CategoryBudgetInfo
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CategoryBudgetInfoCopyWith<CategoryBudgetInfo> get copyWith => _$CategoryBudgetInfoCopyWithImpl<CategoryBudgetInfo>(this as CategoryBudgetInfo, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CategoryBudgetInfo&&(identical(other.category, category) || other.category == category)&&(identical(other.budget, budget) || other.budget == budget)&&(identical(other.progress, progress) || other.progress == progress)&&(identical(other.actualSpent, actualSpent) || other.actualSpent == actualSpent)&&(identical(other.isFallbackBudget, isFallbackBudget) || other.isFallbackBudget == isFallbackBudget));
}


@override
int get hashCode => Object.hash(runtimeType,category,budget,progress,actualSpent,isFallbackBudget);

@override
String toString() {
  return 'CategoryBudgetInfo(category: $category, budget: $budget, progress: $progress, actualSpent: $actualSpent, isFallbackBudget: $isFallbackBudget)';
}


}

/// @nodoc
abstract mixin class $CategoryBudgetInfoCopyWith<$Res>  {
  factory $CategoryBudgetInfoCopyWith(CategoryBudgetInfo value, $Res Function(CategoryBudgetInfo) _then) = _$CategoryBudgetInfoCopyWithImpl;
@useResult
$Res call({
 Category category, Budget? budget, BudgetProgress? progress, double actualSpent, bool isFallbackBudget
});


$CategoryCopyWith<$Res> get category;$BudgetCopyWith<$Res>? get budget;$BudgetProgressCopyWith<$Res>? get progress;

}
/// @nodoc
class _$CategoryBudgetInfoCopyWithImpl<$Res>
    implements $CategoryBudgetInfoCopyWith<$Res> {
  _$CategoryBudgetInfoCopyWithImpl(this._self, this._then);

  final CategoryBudgetInfo _self;
  final $Res Function(CategoryBudgetInfo) _then;

/// Create a copy of CategoryBudgetInfo
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? category = null,Object? budget = freezed,Object? progress = freezed,Object? actualSpent = null,Object? isFallbackBudget = null,}) {
  return _then(_self.copyWith(
category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as Category,budget: freezed == budget ? _self.budget : budget // ignore: cast_nullable_to_non_nullable
as Budget?,progress: freezed == progress ? _self.progress : progress // ignore: cast_nullable_to_non_nullable
as BudgetProgress?,actualSpent: null == actualSpent ? _self.actualSpent : actualSpent // ignore: cast_nullable_to_non_nullable
as double,isFallbackBudget: null == isFallbackBudget ? _self.isFallbackBudget : isFallbackBudget // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of CategoryBudgetInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CategoryCopyWith<$Res> get category {
  
  return $CategoryCopyWith<$Res>(_self.category, (value) {
    return _then(_self.copyWith(category: value));
  });
}/// Create a copy of CategoryBudgetInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BudgetCopyWith<$Res>? get budget {
    if (_self.budget == null) {
    return null;
  }

  return $BudgetCopyWith<$Res>(_self.budget!, (value) {
    return _then(_self.copyWith(budget: value));
  });
}/// Create a copy of CategoryBudgetInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BudgetProgressCopyWith<$Res>? get progress {
    if (_self.progress == null) {
    return null;
  }

  return $BudgetProgressCopyWith<$Res>(_self.progress!, (value) {
    return _then(_self.copyWith(progress: value));
  });
}
}


/// Adds pattern-matching-related methods to [CategoryBudgetInfo].
extension CategoryBudgetInfoPatterns on CategoryBudgetInfo {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CategoryBudgetInfo value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CategoryBudgetInfo() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CategoryBudgetInfo value)  $default,){
final _that = this;
switch (_that) {
case _CategoryBudgetInfo():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CategoryBudgetInfo value)?  $default,){
final _that = this;
switch (_that) {
case _CategoryBudgetInfo() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( Category category,  Budget? budget,  BudgetProgress? progress,  double actualSpent,  bool isFallbackBudget)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CategoryBudgetInfo() when $default != null:
return $default(_that.category,_that.budget,_that.progress,_that.actualSpent,_that.isFallbackBudget);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( Category category,  Budget? budget,  BudgetProgress? progress,  double actualSpent,  bool isFallbackBudget)  $default,) {final _that = this;
switch (_that) {
case _CategoryBudgetInfo():
return $default(_that.category,_that.budget,_that.progress,_that.actualSpent,_that.isFallbackBudget);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( Category category,  Budget? budget,  BudgetProgress? progress,  double actualSpent,  bool isFallbackBudget)?  $default,) {final _that = this;
switch (_that) {
case _CategoryBudgetInfo() when $default != null:
return $default(_that.category,_that.budget,_that.progress,_that.actualSpent,_that.isFallbackBudget);case _:
  return null;

}
}

}

/// @nodoc


class _CategoryBudgetInfo extends CategoryBudgetInfo {
  const _CategoryBudgetInfo({required this.category, this.budget, this.progress, this.actualSpent = 0, this.isFallbackBudget = false}): super._();
  

@override final  Category category;
@override final  Budget? budget;
// Null if no budget set for this category
@override final  BudgetProgress? progress;
// Null if no budget or no progress data
@override@JsonKey() final  double actualSpent;
// Actual spending for the period
@override@JsonKey() final  bool isFallbackBudget;

/// Create a copy of CategoryBudgetInfo
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CategoryBudgetInfoCopyWith<_CategoryBudgetInfo> get copyWith => __$CategoryBudgetInfoCopyWithImpl<_CategoryBudgetInfo>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CategoryBudgetInfo&&(identical(other.category, category) || other.category == category)&&(identical(other.budget, budget) || other.budget == budget)&&(identical(other.progress, progress) || other.progress == progress)&&(identical(other.actualSpent, actualSpent) || other.actualSpent == actualSpent)&&(identical(other.isFallbackBudget, isFallbackBudget) || other.isFallbackBudget == isFallbackBudget));
}


@override
int get hashCode => Object.hash(runtimeType,category,budget,progress,actualSpent,isFallbackBudget);

@override
String toString() {
  return 'CategoryBudgetInfo(category: $category, budget: $budget, progress: $progress, actualSpent: $actualSpent, isFallbackBudget: $isFallbackBudget)';
}


}

/// @nodoc
abstract mixin class _$CategoryBudgetInfoCopyWith<$Res> implements $CategoryBudgetInfoCopyWith<$Res> {
  factory _$CategoryBudgetInfoCopyWith(_CategoryBudgetInfo value, $Res Function(_CategoryBudgetInfo) _then) = __$CategoryBudgetInfoCopyWithImpl;
@override @useResult
$Res call({
 Category category, Budget? budget, BudgetProgress? progress, double actualSpent, bool isFallbackBudget
});


@override $CategoryCopyWith<$Res> get category;@override $BudgetCopyWith<$Res>? get budget;@override $BudgetProgressCopyWith<$Res>? get progress;

}
/// @nodoc
class __$CategoryBudgetInfoCopyWithImpl<$Res>
    implements _$CategoryBudgetInfoCopyWith<$Res> {
  __$CategoryBudgetInfoCopyWithImpl(this._self, this._then);

  final _CategoryBudgetInfo _self;
  final $Res Function(_CategoryBudgetInfo) _then;

/// Create a copy of CategoryBudgetInfo
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? category = null,Object? budget = freezed,Object? progress = freezed,Object? actualSpent = null,Object? isFallbackBudget = null,}) {
  return _then(_CategoryBudgetInfo(
category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as Category,budget: freezed == budget ? _self.budget : budget // ignore: cast_nullable_to_non_nullable
as Budget?,progress: freezed == progress ? _self.progress : progress // ignore: cast_nullable_to_non_nullable
as BudgetProgress?,actualSpent: null == actualSpent ? _self.actualSpent : actualSpent // ignore: cast_nullable_to_non_nullable
as double,isFallbackBudget: null == isFallbackBudget ? _self.isFallbackBudget : isFallbackBudget // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of CategoryBudgetInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CategoryCopyWith<$Res> get category {
  
  return $CategoryCopyWith<$Res>(_self.category, (value) {
    return _then(_self.copyWith(category: value));
  });
}/// Create a copy of CategoryBudgetInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BudgetCopyWith<$Res>? get budget {
    if (_self.budget == null) {
    return null;
  }

  return $BudgetCopyWith<$Res>(_self.budget!, (value) {
    return _then(_self.copyWith(budget: value));
  });
}/// Create a copy of CategoryBudgetInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BudgetProgressCopyWith<$Res>? get progress {
    if (_self.progress == null) {
    return null;
  }

  return $BudgetProgressCopyWith<$Res>(_self.progress!, (value) {
    return _then(_self.copyWith(progress: value));
  });
}
}

/// @nodoc
mixin _$CategoryTypeBudgetSummary {

 CategoryType get categoryType; double get totalBudgetAmount; double get totalActualAmount; String get currency; int get categoriesWithBudgets; int get totalCategories;
/// Create a copy of CategoryTypeBudgetSummary
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CategoryTypeBudgetSummaryCopyWith<CategoryTypeBudgetSummary> get copyWith => _$CategoryTypeBudgetSummaryCopyWithImpl<CategoryTypeBudgetSummary>(this as CategoryTypeBudgetSummary, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CategoryTypeBudgetSummary&&(identical(other.categoryType, categoryType) || other.categoryType == categoryType)&&(identical(other.totalBudgetAmount, totalBudgetAmount) || other.totalBudgetAmount == totalBudgetAmount)&&(identical(other.totalActualAmount, totalActualAmount) || other.totalActualAmount == totalActualAmount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.categoriesWithBudgets, categoriesWithBudgets) || other.categoriesWithBudgets == categoriesWithBudgets)&&(identical(other.totalCategories, totalCategories) || other.totalCategories == totalCategories));
}


@override
int get hashCode => Object.hash(runtimeType,categoryType,totalBudgetAmount,totalActualAmount,currency,categoriesWithBudgets,totalCategories);

@override
String toString() {
  return 'CategoryTypeBudgetSummary(categoryType: $categoryType, totalBudgetAmount: $totalBudgetAmount, totalActualAmount: $totalActualAmount, currency: $currency, categoriesWithBudgets: $categoriesWithBudgets, totalCategories: $totalCategories)';
}


}

/// @nodoc
abstract mixin class $CategoryTypeBudgetSummaryCopyWith<$Res>  {
  factory $CategoryTypeBudgetSummaryCopyWith(CategoryTypeBudgetSummary value, $Res Function(CategoryTypeBudgetSummary) _then) = _$CategoryTypeBudgetSummaryCopyWithImpl;
@useResult
$Res call({
 CategoryType categoryType, double totalBudgetAmount, double totalActualAmount, String currency, int categoriesWithBudgets, int totalCategories
});




}
/// @nodoc
class _$CategoryTypeBudgetSummaryCopyWithImpl<$Res>
    implements $CategoryTypeBudgetSummaryCopyWith<$Res> {
  _$CategoryTypeBudgetSummaryCopyWithImpl(this._self, this._then);

  final CategoryTypeBudgetSummary _self;
  final $Res Function(CategoryTypeBudgetSummary) _then;

/// Create a copy of CategoryTypeBudgetSummary
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? categoryType = null,Object? totalBudgetAmount = null,Object? totalActualAmount = null,Object? currency = null,Object? categoriesWithBudgets = null,Object? totalCategories = null,}) {
  return _then(_self.copyWith(
categoryType: null == categoryType ? _self.categoryType : categoryType // ignore: cast_nullable_to_non_nullable
as CategoryType,totalBudgetAmount: null == totalBudgetAmount ? _self.totalBudgetAmount : totalBudgetAmount // ignore: cast_nullable_to_non_nullable
as double,totalActualAmount: null == totalActualAmount ? _self.totalActualAmount : totalActualAmount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,categoriesWithBudgets: null == categoriesWithBudgets ? _self.categoriesWithBudgets : categoriesWithBudgets // ignore: cast_nullable_to_non_nullable
as int,totalCategories: null == totalCategories ? _self.totalCategories : totalCategories // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [CategoryTypeBudgetSummary].
extension CategoryTypeBudgetSummaryPatterns on CategoryTypeBudgetSummary {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CategoryTypeBudgetSummary value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CategoryTypeBudgetSummary() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CategoryTypeBudgetSummary value)  $default,){
final _that = this;
switch (_that) {
case _CategoryTypeBudgetSummary():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CategoryTypeBudgetSummary value)?  $default,){
final _that = this;
switch (_that) {
case _CategoryTypeBudgetSummary() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( CategoryType categoryType,  double totalBudgetAmount,  double totalActualAmount,  String currency,  int categoriesWithBudgets,  int totalCategories)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CategoryTypeBudgetSummary() when $default != null:
return $default(_that.categoryType,_that.totalBudgetAmount,_that.totalActualAmount,_that.currency,_that.categoriesWithBudgets,_that.totalCategories);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( CategoryType categoryType,  double totalBudgetAmount,  double totalActualAmount,  String currency,  int categoriesWithBudgets,  int totalCategories)  $default,) {final _that = this;
switch (_that) {
case _CategoryTypeBudgetSummary():
return $default(_that.categoryType,_that.totalBudgetAmount,_that.totalActualAmount,_that.currency,_that.categoriesWithBudgets,_that.totalCategories);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( CategoryType categoryType,  double totalBudgetAmount,  double totalActualAmount,  String currency,  int categoriesWithBudgets,  int totalCategories)?  $default,) {final _that = this;
switch (_that) {
case _CategoryTypeBudgetSummary() when $default != null:
return $default(_that.categoryType,_that.totalBudgetAmount,_that.totalActualAmount,_that.currency,_that.categoriesWithBudgets,_that.totalCategories);case _:
  return null;

}
}

}

/// @nodoc


class _CategoryTypeBudgetSummary extends CategoryTypeBudgetSummary {
  const _CategoryTypeBudgetSummary({required this.categoryType, required this.totalBudgetAmount, required this.totalActualAmount, required this.currency, this.categoriesWithBudgets = 0, this.totalCategories = 0}): super._();
  

@override final  CategoryType categoryType;
@override final  double totalBudgetAmount;
@override final  double totalActualAmount;
@override final  String currency;
@override@JsonKey() final  int categoriesWithBudgets;
@override@JsonKey() final  int totalCategories;

/// Create a copy of CategoryTypeBudgetSummary
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CategoryTypeBudgetSummaryCopyWith<_CategoryTypeBudgetSummary> get copyWith => __$CategoryTypeBudgetSummaryCopyWithImpl<_CategoryTypeBudgetSummary>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CategoryTypeBudgetSummary&&(identical(other.categoryType, categoryType) || other.categoryType == categoryType)&&(identical(other.totalBudgetAmount, totalBudgetAmount) || other.totalBudgetAmount == totalBudgetAmount)&&(identical(other.totalActualAmount, totalActualAmount) || other.totalActualAmount == totalActualAmount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.categoriesWithBudgets, categoriesWithBudgets) || other.categoriesWithBudgets == categoriesWithBudgets)&&(identical(other.totalCategories, totalCategories) || other.totalCategories == totalCategories));
}


@override
int get hashCode => Object.hash(runtimeType,categoryType,totalBudgetAmount,totalActualAmount,currency,categoriesWithBudgets,totalCategories);

@override
String toString() {
  return 'CategoryTypeBudgetSummary(categoryType: $categoryType, totalBudgetAmount: $totalBudgetAmount, totalActualAmount: $totalActualAmount, currency: $currency, categoriesWithBudgets: $categoriesWithBudgets, totalCategories: $totalCategories)';
}


}

/// @nodoc
abstract mixin class _$CategoryTypeBudgetSummaryCopyWith<$Res> implements $CategoryTypeBudgetSummaryCopyWith<$Res> {
  factory _$CategoryTypeBudgetSummaryCopyWith(_CategoryTypeBudgetSummary value, $Res Function(_CategoryTypeBudgetSummary) _then) = __$CategoryTypeBudgetSummaryCopyWithImpl;
@override @useResult
$Res call({
 CategoryType categoryType, double totalBudgetAmount, double totalActualAmount, String currency, int categoriesWithBudgets, int totalCategories
});




}
/// @nodoc
class __$CategoryTypeBudgetSummaryCopyWithImpl<$Res>
    implements _$CategoryTypeBudgetSummaryCopyWith<$Res> {
  __$CategoryTypeBudgetSummaryCopyWithImpl(this._self, this._then);

  final _CategoryTypeBudgetSummary _self;
  final $Res Function(_CategoryTypeBudgetSummary) _then;

/// Create a copy of CategoryTypeBudgetSummary
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? categoryType = null,Object? totalBudgetAmount = null,Object? totalActualAmount = null,Object? currency = null,Object? categoriesWithBudgets = null,Object? totalCategories = null,}) {
  return _then(_CategoryTypeBudgetSummary(
categoryType: null == categoryType ? _self.categoryType : categoryType // ignore: cast_nullable_to_non_nullable
as CategoryType,totalBudgetAmount: null == totalBudgetAmount ? _self.totalBudgetAmount : totalBudgetAmount // ignore: cast_nullable_to_non_nullable
as double,totalActualAmount: null == totalActualAmount ? _self.totalActualAmount : totalActualAmount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,categoriesWithBudgets: null == categoriesWithBudgets ? _self.categoriesWithBudgets : categoriesWithBudgets // ignore: cast_nullable_to_non_nullable
as int,totalCategories: null == totalCategories ? _self.totalCategories : totalCategories // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
