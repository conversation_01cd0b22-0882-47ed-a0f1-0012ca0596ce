import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Empty state widget for when no budgets exist
class EmptyBudgetsState extends StatelessWidget {
  const EmptyBudgetsState({
    super.key,
    this.isFiltered = false,
    this.filterDescription,
  });
  final bool isFiltered;
  final String? filterDescription;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(DesignTokens.spacing24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Illustration
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer.withValues(
                  alpha: 0.3,
                ),
                shape: BoxShape.circle,
              ),
              child: Icon(
                isFiltered
                    ? Icons.filter_list_off
                    : Icons.account_balance_wallet_outlined,
                size: 60,
                color: theme.colorScheme.primary,
              ),
            ),

            const SizedBox(height: DesignTokens.spacing24),

            // Title
            Text(
              isFiltered ? l10n.noBudgetsForMonth : l10n.noBudgetsYet,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: DesignTokens.spacing12),

            // Description
            Text(
              isFiltered
                  ? (filterDescription ?? 'No budgets for selected period')
                  : l10n.noBudgetsDescription,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: DesignTokens.spacing32),

            // Educational content (always show for empty state)
            if (!isFiltered) ...[
              _buildEducationalContent(context, l10n, theme),
              const SizedBox(height: DesignTokens.spacing16),
            ],

            // Action button
            if (!isFiltered) ...[
              // For now, show a message about budget management
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(DesignTokens.spacing16),
                  child: Column(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Theme.of(context).colorScheme.primary,
                        size: 32,
                      ),
                      const SizedBox(height: DesignTokens.spacing8),
                      Text(
                        'Budget management features coming soon!',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ] else ...[
              // For filtered state, show different action
              OutlinedButton.icon(
                onPressed: () => _showAllBudgets(context),
                icon: const Icon(Icons.visibility),
                label: Text(l10n.viewAllBudgets),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build educational content about budgets
  Widget _buildEducationalContent(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    return Card(
      elevation: DesignTokens.elevation1,
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Why Use Budgets?',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),

            const SizedBox(height: DesignTokens.spacing12),

            _buildBenefitItem(
              context,
              Icons.track_changes,
              'Track your spending in real-time',
              theme,
            ),

            const SizedBox(height: DesignTokens.spacing8),

            _buildBenefitItem(
              context,
              Icons.savings,
              'Save money by staying within limits',
              theme,
            ),

            const SizedBox(height: DesignTokens.spacing8),

            _buildBenefitItem(
              context,
              Icons.insights,
              'Get insights into your spending habits',
              theme,
            ),
          ],
        ),
      ),
    );
  }

  /// Build individual benefit item
  Widget _buildBenefitItem(
    BuildContext context,
    IconData icon,
    String text,
    ThemeData theme,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: theme.colorScheme.primary),
        const SizedBox(width: DesignTokens.spacing8),
        Expanded(child: Text(text, style: theme.textTheme.bodyMedium)),
      ],
    );
  }

  /// Show all budgets (remove filter)
  void _showAllBudgets(BuildContext context) {
    // This would typically trigger a callback to clear filters
    // For now, just navigate back to the main budgets screen
    context.go('/budgets');
  }
}

/// Specialized empty state for specific scenarios
class FilteredEmptyBudgetsState extends EmptyBudgetsState {
  const FilteredEmptyBudgetsState({super.key, required this.monthYear})
    : super(isFiltered: true, filterDescription: null);
  final String monthYear;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return EmptyBudgetsState(
      isFiltered: true,
      filterDescription: l10n.noBudgetsForMonth,
    );
  }
}

/// Empty state for when user has budgets but none match current criteria
class NoMatchingBudgetsState extends EmptyBudgetsState {
  const NoMatchingBudgetsState({super.key, required this.criteria})
    : super(isFiltered: true);
  final String criteria;

  @override
  Widget build(BuildContext context) {
    return EmptyBudgetsState(
      isFiltered: true,
      filterDescription: 'No budgets matching criteria: $criteria',
    );
  }
}
