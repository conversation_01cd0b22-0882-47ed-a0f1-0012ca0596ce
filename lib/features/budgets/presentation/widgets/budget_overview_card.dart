import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/budgets/data/models/category_budget_info.dart';
import 'package:flutter/material.dart';

/// Card widget that displays total budget overview for a category type
class BudgetOverviewCard extends StatelessWidget {
  const BudgetOverviewCard({super.key, required this.summary, this.onTap});
  final CategoryTypeBudgetSummary summary;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final hasAnyBudgets = summary.categoriesWithBudgets > 0;

    return Card(
      elevation: DesignTokens.elevation2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(DesignTokens.borderRadius12),
        child: Container(
          padding: const EdgeInsets.all(DesignTokens.spacing16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(DesignTokens.borderRadius12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                _getGradientStartColor(theme),
                _getGradientEndColor(theme),
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with icon and title
              Row(
                children: [
                  Icon(_getTypeIcon(), color: Colors.white, size: 24),
                  const SizedBox(width: DesignTokens.spacing12),
                  Expanded(
                    child: Text(
                      '${_getTypeName()} Budgets',
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  if (hasAnyBudgets)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: DesignTokens.spacing8,
                        vertical: DesignTokens.spacing4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(
                          DesignTokens.borderRadius12,
                        ),
                      ),
                      child: Text(
                        '${summary.categoriesWithBudgets}/${summary.totalCategories}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: DesignTokens.spacing16),

              if (hasAnyBudgets) ...[
                // Budget amounts
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildAmountColumn(
                      'Budgeted',
                      _formatCurrency(summary.totalBudgetAmount),
                      theme,
                    ),
                    _buildAmountColumn(
                      summary.categoryType == CategoryType.expense
                          ? 'Spent'
                          : 'Earned',
                      _formatCurrency(summary.totalActualAmount),
                      theme,
                    ),
                    _buildAmountColumn(
                      'Remaining',
                      _formatCurrency(summary.totalRemaining),
                      theme,
                    ),
                  ],
                ),

                const SizedBox(height: DesignTokens.spacing16),

                // Progress bar
                _buildProgressBar(theme),

                const SizedBox(height: DesignTokens.spacing8),

                // Progress text
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${summary.overallProgressPercentage.toStringAsFixed(1)}% used',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                    if (summary.hasOverBudgets)
                      Text(
                        'Over budget',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      )
                    else if (summary.isAtWarning)
                      Text(
                        'Warning',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                  ],
                ),
              ] else ...[
                // No budgets set
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'No budgets set',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: DesignTokens.spacing8),
                    Text(
                      'Set budgets for your ${_getTypeName().toLowerCase()} categories to track your spending.',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAmountColumn(String label, String amount, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
        const SizedBox(height: DesignTokens.spacing4),
        Text(
          amount,
          style: theme.textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildProgressBar(ThemeData theme) {
    final progress = summary.overallProgressPercentage / 100.0;
    final progressClamped = progress.clamp(0, 1);

    return Container(
      height: 8,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadius4),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Container(
            width: constraints.maxWidth * progressClamped,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(DesignTokens.borderRadius4),
            ),
          );
        },
      ),
    );
  }

  Color _getGradientStartColor(ThemeData theme) {
    switch (summary.categoryType) {
      case CategoryType.expense:
        return summary.hasOverBudgets
            ? Colors.red.shade400
            : summary.isAtWarning
            ? Colors.orange.shade400
            : AppColors.primary;
      case CategoryType.income:
        return AppColors.success;
    }
  }

  Color _getGradientEndColor(ThemeData theme) {
    switch (summary.categoryType) {
      case CategoryType.expense:
        return summary.hasOverBudgets
            ? Colors.red.shade600
            : summary.isAtWarning
            ? Colors.orange.shade600
            : AppColors.primaryDark;
      case CategoryType.income:
        return Colors.green.shade700;
    }
  }

  IconData _getTypeIcon() {
    switch (summary.categoryType) {
      case CategoryType.expense:
        return Icons.trending_down;
      case CategoryType.income:
        return Icons.trending_up;
    }
  }

  String _getTypeName() {
    switch (summary.categoryType) {
      case CategoryType.expense:
        return 'Expense';
      case CategoryType.income:
        return 'Income';
    }
  }

  String _formatCurrency(double amount) {
    // Simple formatting - in production you'd use intl package for proper currency formatting
    switch (summary.currency) {
      case 'USD':
        return '\$${amount.toStringAsFixed(2)}';
      case 'EUR':
        return '€${amount.toStringAsFixed(2)}';
      case 'GBP':
        return '£${amount.toStringAsFixed(2)}';
      default:
        return '${summary.currency} ${amount.toStringAsFixed(2)}';
    }
  }
}
