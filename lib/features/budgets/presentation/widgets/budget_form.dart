import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/budget.dart';
import 'package:budapp/features/budgets/services/budget_validators.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/features/transactions/presentation/widgets/amount_input_field.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/widgets/common/app_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Reusable budget form widget for create and edit scenarios
class BudgetForm extends ConsumerStatefulWidget {
  const BudgetForm({
    super.key,
    this.initialBudget,
    required this.onSubmit,
    this.isLoading = false,
  });
  final Budget? initialBudget;
  final void Function(Budget) onSubmit;
  final bool isLoading;

  @override
  ConsumerState<BudgetForm> createState() => _BudgetFormState();
}

class _BudgetFormState extends ConsumerState<BudgetForm> {
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();

  BudgetType _selectedType = BudgetType.expense;
  BudgetPeriod _selectedPeriod = BudgetPeriod.monthly;
  String? _selectedCategoryId;
  String? _selectedParentBudgetId;

  // Enhanced validation state tracking
  String? _nameError;
  String? _amountError;
  String? _categoryError;
  bool _hasValidationErrors = false;
  bool _showValidationErrors = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
    _setupValidationListeners();
  }

  /// Setup real-time validation listeners
  void _setupValidationListeners() {
    _nameController.addListener(_validateName);
    _amountController.addListener(_validateAmount);
  }

  /// Real-time name validation
  void _validateName() {
    final result = BudgetValidators.validateBudgetName(_nameController.text);
    setState(() {
      _nameError = result.isValid ? null : result.errors.first;
      _updateValidationState();
    });
  }

  /// Real-time amount validation
  void _validateAmount() {
    final amount = double.tryParse(_amountController.text) ?? 0;
    final result = BudgetValidators.validateBudgetAmount(amount);
    setState(() {
      _amountError = result.isValid ? null : result.errors.first;
      _updateValidationState();
    });
  }

  /// Update overall validation state
  void _updateValidationState() {
    _hasValidationErrors =
        _nameError != null || _amountError != null || _categoryError != null;
  }

  /// Check if form has validation errors
  bool get hasValidationErrors => _hasValidationErrors;

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  /// Initialize form with existing budget data if editing
  void _initializeForm() {
    final budget = widget.initialBudget;
    if (budget != null) {
      _amountController.text = (budget.plannedAmountCents / 100).toString();
      _selectedType = budget.type;
      _selectedPeriod = budget.period;
      _selectedCategoryId = budget.categoryId;
      _selectedParentBudgetId = budget.parentBudgetId;
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final categoriesAsync = ref.watch(expenseCategoriesProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Budget name field with enhanced validation
        AppTextFormField(
          label: l10n.budgetName,
          controller: _nameController,
          hintText: 'Enter budget name',
          prefixIcon: const Icon(Icons.label_outline),
          errorText: _showValidationErrors ? _nameError : null,
          suffixIcon: _nameController.text.isNotEmpty
              ? (_nameError == null
                    ? Icon(
                        Icons.check_circle,
                        color: theme.colorScheme.primary,
                        size: 20,
                      )
                    : Icon(
                        Icons.error,
                        color: theme.colorScheme.error,
                        size: 20,
                      ))
              : null,
          textInputAction: TextInputAction.next,
          validator: (value) {
            setState(() {
              _showValidationErrors = true;
            });
            final result = BudgetValidators.validateBudgetName(value ?? '');
            return result.isValid ? null : result.errors.first;
          },
          enabled: !widget.isLoading,
          onChanged: (value) {
            // Real-time validation is handled by the listener
            setState(() {}); // Trigger rebuild for suffix icon
          },
        ),

        const SizedBox(height: DesignTokens.spacing16),

        // Budget amount field
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.budgetAmount,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            AmountInputField(
              controller: _amountController,
              enabled: !widget.isLoading,
              errorText: _getAmountError(),
            ),
          ],
        ),

        const SizedBox(height: DesignTokens.spacing16),

        // Budget type selection
        Text('Budget Type', style: theme.textTheme.titleSmall),
        const SizedBox(height: DesignTokens.spacing8),
        SegmentedButton<BudgetType>(
          segments: [
            ButtonSegment(
              value: BudgetType.expense,
              label: Text(l10n.expense),
              icon: const Icon(Icons.trending_down),
            ),
            ButtonSegment(
              value: BudgetType.income,
              label: Text(l10n.income),
              icon: const Icon(Icons.trending_up),
            ),
          ],
          selected: {_selectedType},
          onSelectionChanged: widget.isLoading
              ? null
              : (Set<BudgetType> selection) {
                  setState(() {
                    _selectedType = selection.first;
                  });
                },
        ),

        const SizedBox(height: DesignTokens.spacing16),

        // Budget period selection
        Text(l10n.budgetPeriod, style: theme.textTheme.titleSmall),
        const SizedBox(height: DesignTokens.spacing8),
        SegmentedButton<BudgetPeriod>(
          segments: [
            ButtonSegment(
              value: BudgetPeriod.monthly,
              label: Text(l10n.monthly),
              icon: const Icon(Icons.calendar_month),
            ),
            ButtonSegment(
              value: BudgetPeriod.yearly,
              label: Text(l10n.yearly),
              icon: const Icon(Icons.calendar_today),
            ),
          ],
          selected: {_selectedPeriod},
          onSelectionChanged: widget.isLoading
              ? null
              : (Set<BudgetPeriod> selection) {
                  setState(() {
                    _selectedPeriod = selection.first;
                  });
                },
        ),

        const SizedBox(height: DesignTokens.spacing16),

        // Category selection
        categoriesAsync.when(
          loading: () => const LinearProgressIndicator(),
          error: (error, stackTrace) => Text(
            l10n.errorLoadingCategories,
            style: TextStyle(color: theme.colorScheme.error),
          ),
          data: (categories) => DropdownButtonFormField<String?>(
            value: _selectedCategoryId,
            decoration: InputDecoration(
              labelText: l10n.budgetCategory,
              hintText: 'Select category (optional)',
              prefixIcon: const Icon(Icons.category_outlined),
            ),
            items: [
              DropdownMenuItem<String?>(
                value: null,
                child: Text(l10n.allCategories),
              ),
              ...categories.map(
                (category) => DropdownMenuItem(
                  value: category.id,
                  child: Text(category.name),
                ),
              ),
            ],
            onChanged: widget.isLoading
                ? null
                : (value) {
                    setState(() {
                      _selectedCategoryId = value;
                    });
                  },
          ),
        ),

        const SizedBox(height: DesignTokens.spacing24),

        // Form submission handled by parent widget
      ],
    );
  }

  /// Get the budget data from the form
  Budget getBudgetData() {
    final user = ref.read(currentUserProvider);
    if (user == null) {
      throw Exception('User not authenticated');
    }

    final now = DateTime.now();
    final amount = double.tryParse(_amountController.text) ?? 0;
    final amountCents = (amount * 100).round();

    if (widget.initialBudget != null) {
      // Editing existing budget
      return widget.initialBudget!.copyWith(
        plannedAmountCents: amountCents,
        type: _selectedType,
        period: _selectedPeriod,
        categoryId: _selectedCategoryId,
        parentBudgetId: _selectedParentBudgetId,
        updatedAt: now,
      );
    } else {
      // Creating new budget
      final now = DateTime.now();
      final periodStart = _selectedPeriod == BudgetPeriod.monthly
          ? DateTime(now.year, now.month, 1)
          : DateTime(now.year, 1, 1);

      return Budget.create(
        userId: user.uid,
        type: _selectedType,
        plannedAmountCents: amountCents,
        period: _selectedPeriod,
        periodStart: periodStart,
        categoryId: _selectedCategoryId,
        parentBudgetId: _selectedParentBudgetId,
      );
    }
  }

  /// Get amount validation error with enhanced feedback
  String? _getAmountError() {
    if (_amountController.text.isEmpty) return null;

    // Use real-time validation state if available
    if (_showValidationErrors && _amountError != null) {
      return _amountError;
    }

    // Fallback to immediate validation
    final amount = double.tryParse(_amountController.text);
    final result = BudgetValidators.validateBudgetAmount(amount ?? 0);
    return result.isValid ? null : result.errors.first;
  }

  /// Validate the form
  bool validate() {
    // Validate name
    final nameResult = BudgetValidators.validateBudgetName(
      _nameController.text,
    );
    if (!nameResult.isValid) return false;

    // Validate amount
    final amount = double.tryParse(_amountController.text);
    final amountResult = BudgetValidators.validateBudgetAmount(amount ?? 0);
    if (!amountResult.isValid) return false;

    return true;
  }

  /// Submit the form
  void submit() {
    if (validate()) {
      final budget = getBudgetData();
      widget.onSubmit(budget);
    }
  }
}
