import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/budget.dart';
import 'package:budapp/features/budgets/providers/budget_providers.dart';
import 'package:budapp/features/budgets/services/budget_error_service.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/widgets/common/loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Enhanced budget deletion dialog with better UX
class BudgetDeletionDialog extends ConsumerStatefulWidget {
  const BudgetDeletionDialog({super.key, required this.budget, this.onDeleted});
  final Budget budget;
  final VoidCallback? onDeleted;

  @override
  ConsumerState<BudgetDeletionDialog> createState() =>
      _BudgetDeletionDialogState();
}

class _BudgetDeletionDialogState extends ConsumerState<BudgetDeletionDialog> {
  bool _isDeleting = false;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: theme.colorScheme.error,
            size: 28,
          ),
          const SizedBox(width: DesignTokens.spacing12),
          Expanded(
            child: Text(
              l10n.deleteBudget,
              style: theme.textTheme.titleLarge?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Budget info card
          Container(
            padding: const EdgeInsets.all(DesignTokens.spacing12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(DesignTokens.borderRadius8),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.budget.getDisplayName(),
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: DesignTokens.spacing4),
                Consumer(
                  builder: (context, ref, _) {
                    final currencyFormatter = ref.watch(
                      currencyFormatterProvider,
                    );
                    return Text(
                      currencyFormatter.formatAmount(
                        widget.budget.plannedAmountCents,
                      ),
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    );
                  },
                ),
                const SizedBox(height: DesignTokens.spacing4),
                Text(
                  '${widget.budget.type.name} • ${widget.budget.period.name}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: DesignTokens.spacing16),

          // Warning message
          Container(
            padding: const EdgeInsets.all(DesignTokens.spacing12),
            decoration: BoxDecoration(
              color: theme.colorScheme.errorContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(DesignTokens.borderRadius8),
              border: Border.all(
                color: theme.colorScheme.error.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.info_outline,
                  color: theme.colorScheme.error,
                  size: 20,
                ),
                const SizedBox(width: DesignTokens.spacing8),
                Expanded(
                  child: Text(
                    'This action cannot be undone. All budget data and progress will be permanently removed.',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.error,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Error message if any
          if (_errorMessage != null) ...[
            const SizedBox(height: DesignTokens.spacing12),
            Container(
              padding: const EdgeInsets.all(DesignTokens.spacing12),
              decoration: BoxDecoration(
                color: theme.colorScheme.errorContainer,
                borderRadius: BorderRadius.circular(DesignTokens.borderRadius8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: theme.colorScheme.error,
                    size: 20,
                  ),
                  const SizedBox(width: DesignTokens.spacing8),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onErrorContainer,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Loading indicator
          if (_isDeleting) ...[
            const SizedBox(height: DesignTokens.spacing16),
            const Center(
              child: LoadingIndicator(message: 'Deleting budget...'),
            ),
          ],
        ],
      ),
      actions: [
        // Cancel button
        TextButton(
          onPressed: _isDeleting
              ? null
              : () => Navigator.of(context).pop(false),
          child: Text(l10n.cancel),
        ),

        // Delete button
        FilledButton(
          onPressed: _isDeleting ? null : _deleteBudget,
          style: FilledButton.styleFrom(
            backgroundColor: theme.colorScheme.error,
            foregroundColor: theme.colorScheme.onError,
          ),
          child: _isDeleting
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(l10n.delete),
        ),
      ],
    );
  }

  /// Delete the budget with enhanced error handling
  Future<void> _deleteBudget() async {
    setState(() {
      _isDeleting = true;
      _errorMessage = null;
    });

    try {
      // Use the budget controller to delete the budget
      final budgetsController = ref.read(budgetsControllerProvider.notifier);
      await budgetsController.deleteBudget(widget.budget.id);

      if (mounted) {
        // Check if the operation was successful by checking controller state
        final controllerState = ref.read(budgetsControllerProvider);

        if (controllerState.hasError) {
          // Operation failed - show error
          setState(() {
            _isDeleting = false;
            final error = controllerState.error;

            // Use BudgetErrorService to get user-friendly message
            if (error is BudgetException) {
              _errorMessage = BudgetErrorService.getUserFriendlyMessage(error);
            } else {
              final budgetError = BudgetErrorService.handleError(error);
              _errorMessage = BudgetErrorService.getUserFriendlyMessage(
                budgetError,
              );
            }
          });
          // Don't call onDeleted or close dialog on error
          return;
        }

        // Operation succeeded
        widget.onDeleted?.call();
        Navigator.of(context).pop(true);
      }
    } on Exception catch (error) {
      if (mounted) {
        setState(() {
          _isDeleting = false;

          // Use BudgetErrorService to get user-friendly message
          if (error is BudgetException) {
            _errorMessage = BudgetErrorService.getUserFriendlyMessage(error);
          } else {
            final budgetError = BudgetErrorService.handleError(error);
            _errorMessage = BudgetErrorService.getUserFriendlyMessage(
              budgetError,
            );
          }
        });
      }
    }
  }
}
