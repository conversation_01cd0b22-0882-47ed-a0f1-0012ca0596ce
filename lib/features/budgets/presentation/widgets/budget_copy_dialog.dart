import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/budgets/providers/budget_providers.dart';
import 'package:budapp/features/budgets/services/budget_copy_service.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/common/error_display.dart';
import 'package:budapp/widgets/common/loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Dialog for selecting a source period to copy budgets from
class BudgetCopyDialog extends ConsumerStatefulWidget {
  const BudgetCopyDialog({super.key});

  @override
  ConsumerState<BudgetCopyDialog> createState() => _BudgetCopyDialogState();
}

class _BudgetCopyDialogState extends ConsumerState<BudgetCopyDialog> {
  DateTime? selectedSourcePeriod;
  List<DateTime> availablePeriods = [];
  bool isLoadingPeriods = true;

  @override
  void initState() {
    super.initState();
    _loadAvailablePeriods();
  }

  Future<void> _loadAvailablePeriods() async {
    try {
      final currentPeriod = ref.read(currentTimePeriodProvider);
      final periodType = ref.read(currentBudgetPeriodProvider);
      final controller = ref.read(budgetCopyControllerProvider.notifier);

      final periods = await controller.getAvailableSourcePeriods(
        currentPeriod: currentPeriod,
        periodType: periodType,
      );

      if (mounted) {
        setState(() {
          availablePeriods = periods;
          isLoadingPeriods = false;
        });
      }
    } on Exception catch (error) {
      if (mounted) {
        setState(() {
          isLoadingPeriods = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading available periods: $error'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final copyState = ref.watch(budgetCopyControllerProvider);
    final periodType = ref.watch(currentBudgetPeriodProvider);
    final controller = ref.read(budgetCopyControllerProvider.notifier);

    return AlertDialog(
      title: const Text('Copy Budgets from Previous Period'),
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Select a previous period to copy budgets from:',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: DesignTokens.spacing16),

              if (isLoadingPeriods)
                const Center(child: LoadingIndicator())
              else if (availablePeriods.isEmpty)
                Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 48,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(height: DesignTokens.spacing8),
                      Text(
                        'No previous periods with budgets found.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              else
                ConstrainedBox(
                  constraints: const BoxConstraints(maxHeight: 200),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: availablePeriods.length,
                    itemBuilder: (context, index) {
                      final period = availablePeriods[index];
                      final formattedPeriod = controller.formatPeriod(
                        period,
                        periodType,
                      );

                      return RadioListTile<DateTime>(
                        title: Text(formattedPeriod),
                        value: period,
                        groupValue: selectedSourcePeriod,
                        onChanged: (value) {
                          setState(() {
                            selectedSourcePeriod = value;
                          });
                        },
                        dense: true,
                      );
                    },
                  ),
                ),

              if (copyState.hasError)
                Padding(
                  padding: const EdgeInsets.only(top: DesignTokens.spacing16),
                  child: ErrorDisplay(
                    error: copyState.error!,
                    onRetry: controller.clearResult,
                  ),
                ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: copyState.isLoading
              ? null
              : () {
                  controller.clearResult();
                  Navigator.of(context).pop();
                },
          child: Text(l10n.cancel),
        ),
        FilledButton(
          onPressed:
              copyState.isLoading ||
                  selectedSourcePeriod == null ||
                  availablePeriods.isEmpty
              ? null
              : _copyBudgets,
          child: copyState.isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Copy Budgets'),
        ),
      ],
    );
  }

  Future<void> _copyBudgets() async {
    if (selectedSourcePeriod == null) return;

    final currentPeriod = ref.read(currentTimePeriodProvider);
    final periodType = ref.read(currentBudgetPeriodProvider);
    final controller = ref.read(budgetCopyControllerProvider.notifier);

    await controller.copyBudgetsFromPreviousPeriod(
      sourcePeriod: selectedSourcePeriod!,
      targetPeriod: currentPeriod,
      periodType: periodType,
    );

    final copyState = ref.read(budgetCopyControllerProvider);

    if (copyState.hasValue && copyState.value != null) {
      final result = copyState.value!;

      if (mounted) {
        Navigator.of(context).pop();

        // Show result feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_formatCopyResult(result)),
            backgroundColor: result.success
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 4),
          ),
        );

        // Refresh the budget lists
        ref
          ..invalidate(categoryBudgetInfoProvider)
          ..invalidate(categoryTypeBudgetSummaryProvider);
      }
    }
  }

  String _formatCopyResult(BudgetCopyResult result) {
    if (!result.success) {
      final errorMessage = result.errors.isNotEmpty
          ? result.errors.first
          : 'Unknown error occurred';
      return 'Failed to copy budgets: $errorMessage';
    }

    if (result.copiedCount == 0) {
      final reasonMessage = result.skippedReasons.isNotEmpty
          ? result.skippedReasons.first
          : 'No budgets found to copy';
      return 'No budgets were copied. $reasonMessage';
    }

    final budgetText = result.copiedCount == 1 ? 'budget' : 'budgets';
    var message = 'Successfully copied ${result.copiedCount} $budgetText';

    if (result.skippedCount > 0) {
      final skippedText = result.skippedCount == 1 ? 'budget' : 'budgets';
      message += ', skipped ${result.skippedCount} $skippedText';
    }

    return '$message.';
  }
}
