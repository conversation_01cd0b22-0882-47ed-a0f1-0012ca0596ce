import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/budget_progress.dart';
import 'package:flutter/material.dart';

/// Reusable budget progress bar widget for visual progress indication
class BudgetProgressBar extends StatelessWidget {
  const BudgetProgressBar({
    super.key,
    required this.progress,
    this.height = 8.0,
    this.showPercentage = true,
  });
  final BudgetProgress progress;
  final double height;
  final bool showPercentage;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progressValue = _getProgressValue();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Progress bar with percentage overlay
        Stack(
          children: [
            // Background progress bar
            Container(
              height: height,
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(height / 2),
              ),
            ),

            // Progress indicator
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              height: height,
              width: MediaQuery.of(context).size.width * progressValue,
              decoration: BoxDecoration(
                color: progress.statusColor,
                borderRadius: BorderRadius.circular(height / 2),
              ),
            ),

            // Percentage text overlay (if enabled and progress bar is tall enough)
            if (showPercentage && height >= 20)
              Positioned.fill(
                child: Center(
                  child: Text(
                    '${progress.percentage.toStringAsFixed(0)}%',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: _getTextColor(theme),
                      fontWeight: FontWeight.w600,
                      fontSize: 10,
                    ),
                  ),
                ),
              ),
          ],
        ),

        // Percentage text below (if not shown on bar)
        if (showPercentage && height < 20) ...[
          const SizedBox(height: DesignTokens.spacing4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${progress.percentage.toStringAsFixed(1)}%',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: progress.statusColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              _buildStatusChip(theme),
            ],
          ),
        ],
      ],
    );
  }

  /// Get progress value clamped between 0 and 1 for the progress bar
  double _getProgressValue() {
    final value = progress.percentage / 100.0;
    return value.clamp(0, 1);
  }

  /// Get appropriate text color for overlay text
  Color _getTextColor(ThemeData theme) {
    // Use white text on dark progress bars, dark text on light progress bars
    final luminance = progress.statusColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }

  /// Build status chip showing budget status
  Widget _buildStatusChip(ThemeData theme) {
    final statusText = _getStatusText();

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacing8,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: progress.statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadius12),
        border: Border.all(
          color: progress.statusColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        statusText,
        style: theme.textTheme.bodySmall?.copyWith(
          color: progress.statusColor,
          fontWeight: FontWeight.w600,
          fontSize: 10,
        ),
      ),
    );
  }

  /// Get status text based on budget progress status
  String _getStatusText() {
    switch (progress.status) {
      case BudgetProgressStatus.onTrack:
        return 'On Track';
      case BudgetProgressStatus.warning:
        return 'Warning';
      case BudgetProgressStatus.overBudget:
        return 'Over Budget';
    }
  }
}

/// Extended budget progress bar with additional details
class DetailedBudgetProgressBar extends StatelessWidget {
  const DetailedBudgetProgressBar({
    super.key,
    required this.progress,
    required this.currency,
  });
  final BudgetProgress progress;
  final String currency;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main progress bar
        BudgetProgressBar(
          progress: progress,
          height: 12,
          showPercentage: false,
        ),

        const SizedBox(height: DesignTokens.spacing8),

        // Detailed information
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Spent',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                Text(
                  _formatCurrency(progress.spent),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  '${progress.percentage.toStringAsFixed(1)}%',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: progress.statusColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _getStatusText(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: progress.statusColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  progress.remaining >= 0 ? 'Remaining' : 'Over by',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                Text(
                  _formatCurrency(progress.remaining.abs()),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: progress.remaining < 0
                        ? theme.colorScheme.error
                        : null,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  /// Format currency amount
  String _formatCurrency(double amount) {
    return '\$${amount.toStringAsFixed(2)}';
  }

  /// Get status text based on budget progress status
  String _getStatusText() {
    switch (progress.status) {
      case BudgetProgressStatus.onTrack:
        return 'On Track';
      case BudgetProgressStatus.warning:
        return 'Warning';
      case BudgetProgressStatus.overBudget:
        return 'Over Budget';
    }
  }
}
