import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/budgets/data/models/category_budget_info.dart';
import 'package:budapp/features/budgets/presentation/widgets/budget_progress_bar.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/utils/color_utils.dart';
import 'package:budapp/utils/icon_utils.dart';
import 'package:budapp/widgets/common/app_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Card widget that displays a category with its budget information
class CategoryBudgetCard extends ConsumerWidget {
  const CategoryBudgetCard({
    super.key,
    required this.categoryBudgetInfo,
    this.onTap,
    this.onEditBudget,
    this.isSelectionMode = false,
    this.isSelected = false,
    this.onSelectionToggle,
    this.isEditMode = false,
    this.budgetController,
    this.onBudgetAmountChanged,
  });
  final CategoryBudgetInfo categoryBudgetInfo;
  final VoidCallback? onTap;
  final VoidCallback? onEditBudget;
  final bool isSelectionMode;
  final bool isSelected;
  final VoidCallback? onSelectionToggle;
  final bool isEditMode;
  final TextEditingController? budgetController;
  final ValueChanged<String>? onBudgetAmountChanged;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final category = categoryBudgetInfo.category;
    final hasBudget = categoryBudgetInfo.hasBudget;
    final currencyFormatter = ref.watch(currencyFormatterProvider);

    return Card(
      elevation: DesignTokens.elevation1,
      color: isSelected ? theme.colorScheme.primaryContainer : null,
      child: InkWell(
        onTap: isSelectionMode
            ? onSelectionToggle
            : isEditMode
            ? null // Disable tap in edit mode
            : onTap,
        borderRadius: BorderRadius.circular(DesignTokens.borderRadius12),
        child: Padding(
          padding: const EdgeInsets.all(DesignTokens.spacing16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with category icon, name, and actions
              Row(
                children: [
                  // Selection checkbox (only in selection mode)
                  if (isSelectionMode) ...[
                    Checkbox(
                      value: isSelected,
                      onChanged: (_) => onSelectionToggle?.call(),
                    ),
                    const SizedBox(width: DesignTokens.spacing8),
                  ],

                  // Category icon
                  Icon(
                    _getIconData(),
                    color: _getCategoryColor(theme),
                    size: 24,
                  ),
                  const SizedBox(width: DesignTokens.spacing12),

                  // Category name and spending info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          category.name,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: DesignTokens.spacing4),
                        Text(
                          'Spent: ${currencyFormatter.formatAmount((categoryBudgetInfo.actualSpent * 100).round())}',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Budget action button (hidden in selection mode and edit mode)
                  if (!isSelectionMode && !isEditMode)
                    _buildActionButton(context, theme),
                ],
              ),

              const SizedBox(height: DesignTokens.spacing16),

              // Budget information and progress
              if (hasBudget) ...[
                _buildBudgetInfo(context, ref, theme),
              ] else ...[
                _buildNoBudgetInfo(context, ref, theme),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(BuildContext context, ThemeData theme) {
    if (categoryBudgetInfo.hasBudget) {
      return IconButton(
        onPressed: onEditBudget,
        icon: const Icon(Icons.edit_outlined),
        tooltip: 'Edit Budget',
        iconSize: 20,
      );
    } else {
      // No action button for categories without budgets
      return const SizedBox.shrink();
    }
  }

  Widget _buildBudgetInfo(
    BuildContext context,
    WidgetRef ref,
    ThemeData theme,
  ) {
    final progress = categoryBudgetInfo.progress;

    return Column(
      children: [
        // Budget amount and remaining
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Budget amount - editable in edit mode
            Expanded(
              child: isEditMode && budgetController != null
                  ? Row(
                      children: [
                        Text(
                          'Budget: ',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Expanded(
                          child: AppTextFormField(
                            label: 'Budget Amount',
                            controller: budgetController,
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            suffixIcon: Padding(
                              padding: const EdgeInsets.only(right: 12),
                              child: Text(
                                ref
                                    .watch(currencyFormatterProvider)
                                    .currencyCode,
                                style: theme.textTheme.bodyMedium,
                              ),
                            ),
                            onChanged: onBudgetAmountChanged,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Required';
                              }
                              final amount = double.tryParse(value);
                              if (amount == null || amount < 0) {
                                return 'Invalid amount';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Text(
                          'Budget: ${ref.watch(currencyFormatterProvider).formatAmount((categoryBudgetInfo.budgetAmount * 100).round())}',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        if (categoryBudgetInfo.isFallbackBudget) ...[
                          const SizedBox(width: 8),
                          Tooltip(
                            message: 'Budget from previous period',
                            child: Icon(
                              Icons.history,
                              size: 16,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ],
                    ),
            ),
            const SizedBox(width: DesignTokens.spacing8),
            Text(
              'Remaining: ${ref.watch(currencyFormatterProvider).formatAmount(((categoryBudgetInfo.remainingAmount ?? 0) * 100).round())}',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: _getRemainingAmountColor(
                  context,
                  categoryBudgetInfo.remainingAmount ?? 0,
                ),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),

        // Only show progress information if progress data is available
        if (progress != null) ...[
          const SizedBox(height: DesignTokens.spacing8),

          // Progress bar
          BudgetProgressBar(progress: progress),

          const SizedBox(height: DesignTokens.spacing4),

          // Progress percentage
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${progress.percentage.toStringAsFixed(1)}% used',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              if (categoryBudgetInfo.isOverBudget)
                Text(
                  'Over budget',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.error,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
        ] else ...[
          // Show a message when there's a budget but no progress data
          const SizedBox(height: DesignTokens.spacing8),
          Container(
            padding: const EdgeInsets.all(DesignTokens.spacing8),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest.withValues(
                alpha: 0.3,
              ),
              borderRadius: BorderRadius.circular(DesignTokens.borderRadius8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: DesignTokens.spacing8),
                Expanded(
                  child: Text(
                    'Budget set, progress data loading...',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildNoBudgetInfo(
    BuildContext context,
    WidgetRef ref,
    ThemeData theme,
  ) {
    if (categoryBudgetInfo.actualSpent > 0) {
      return Container(
        padding: const EdgeInsets.all(DesignTokens.spacing12),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest.withValues(
            alpha: 0.3,
          ),
          borderRadius: BorderRadius.circular(DesignTokens.borderRadius8),
        ),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              size: 16,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: DesignTokens.spacing8),
            Expanded(
              child: Text(
                "No budget set. You've spent ${ref.watch(currencyFormatterProvider).formatAmount((categoryBudgetInfo.actualSpent * 100).round())} this period.",
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(DesignTokens.spacing12),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest.withValues(
            alpha: 0.3,
          ),
          borderRadius: BorderRadius.circular(DesignTokens.borderRadius8),
        ),
        child: Row(
          children: [
            Icon(
              Icons.schedule_outlined,
              size: 16,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: DesignTokens.spacing8),
            Expanded(
              child: Text(
                'No budget set and no spending in this period.',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ],
        ),
      );
    }
  }

  Color _getRemainingAmountColor(BuildContext context, double remaining) {
    final theme = Theme.of(context);
    if (remaining < 0) {
      return theme.colorScheme.error;
    } else if (categoryBudgetInfo.isAtWarning) {
      return Colors.orange;
    } else {
      return theme.colorScheme.onSurfaceVariant;
    }
  }

  // Removed _formatCurrency method - now using global currency formatter

  IconData _getIconData() {
    final category = categoryBudgetInfo.category;
    if (category.icon != null && category.icon!.isNotEmpty) {
      return iconFromName(category.icon!);
    }

    // Default icons based on category type
    return category.type == CategoryType.income
        ? Icons.trending_up
        : Icons.trending_down;
  }

  Color _getCategoryColor(ThemeData theme) {
    final category = categoryBudgetInfo.category;
    if (category.color != null && category.color!.isNotEmpty) {
      try {
        return parseHex(category.color!);
      } on Exception {
        // Fall back to default color if parsing fails
      }
    }

    // Default colors based on category type
    return category.type == CategoryType.income
        ? AppColors.success
        : AppColors.error;
  }
}
