import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/budgets/providers/budget_providers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/common/app_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Dialog for performing bulk operations on selected budgets
class BulkBudgetOperationsDialog extends ConsumerStatefulWidget {
  const BulkBudgetOperationsDialog({
    super.key,
    required this.selectedBudgetIds,
    required this.onOperationComplete,
  });
  final List<String> selectedBudgetIds;
  final VoidCallback onOperationComplete;

  @override
  ConsumerState<BulkBudgetOperationsDialog> createState() =>
      _BulkBudgetOperationsDialogState();
}

class _BulkBudgetOperationsDialogState
    extends ConsumerState<BulkBudgetOperationsDialog> {
  final _percentageController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  BulkOperationType _selectedOperation = BulkOperationType.adjustPercentage;
  bool _isLoading = false;

  @override
  void dispose() {
    _percentageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.edit_outlined, color: theme.colorScheme.primary),
          const SizedBox(width: DesignTokens.spacing8),
          Expanded(
            child: Text(
              l10n.bulkBudgetOperations,
              style: theme.textTheme.titleLarge,
            ),
          ),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Selected budgets info
                Container(
                  padding: const EdgeInsets.all(DesignTokens.spacing12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(
                      DesignTokens.borderRadius8,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 16,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: DesignTokens.spacing8),
                      Text(
                        l10n.selectedBudgets(widget.selectedBudgetIds.length),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: DesignTokens.spacing16),

                // Operation selection
                Text(l10n.selectOperation, style: theme.textTheme.titleSmall),
                const SizedBox(height: DesignTokens.spacing8),

                // Adjust by percentage option
                RadioListTile<BulkOperationType>(
                  title: Text(l10n.adjustByPercentage),
                  subtitle: Text(l10n.adjustByPercentageDescription),
                  value: BulkOperationType.adjustPercentage,
                  groupValue: _selectedOperation,
                  onChanged: (value) {
                    setState(() {
                      _selectedOperation = value!;
                    });
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                // Percentage input field
                if (_selectedOperation ==
                    BulkOperationType.adjustPercentage) ...[
                  const SizedBox(height: DesignTokens.spacing8),
                  AppTextFormField(
                    label: l10n.percentageChange,
                    controller: _percentageController,
                    hintText: l10n.percentageChangeHint,
                    suffixIcon: const Padding(
                      padding: EdgeInsets.only(right: 12),
                      child: Text('%'),
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                      signed: true,
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                        RegExp(r'^-?\d*\.?\d*'),
                      ),
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return l10n.percentageRequired;
                      }
                      final percentage = double.tryParse(value);
                      if (percentage == null) {
                        return l10n.invalidPercentage;
                      }
                      if (percentage <= -100) {
                        return l10n.percentageTooLow;
                      }
                      if (percentage > 500) {
                        return l10n.percentageTooHigh;
                      }
                      return null;
                    },
                  ),
                ],

                // Deactivate budgets option
                RadioListTile<BulkOperationType>(
                  title: Text(l10n.deactivateBudgets),
                  subtitle: Text(l10n.deactivateBudgetsDescription),
                  value: BulkOperationType.deactivate,
                  groupValue: _selectedOperation,
                  onChanged: (value) {
                    setState(() {
                      _selectedOperation = value!;
                    });
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                // Activate budgets option
                RadioListTile<BulkOperationType>(
                  title: Text(l10n.activateBudgets),
                  subtitle: Text(l10n.activateBudgetsDescription),
                  value: BulkOperationType.activate,
                  groupValue: _selectedOperation,
                  onChanged: (value) {
                    setState(() {
                      _selectedOperation = value!;
                    });
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                // Delete budgets option
                RadioListTile<BulkOperationType>(
                  title: Text(
                    l10n.deleteBudgets,
                    style: TextStyle(color: theme.colorScheme.error),
                  ),
                  subtitle: Text(l10n.deleteBudgetsDescription),
                  value: BulkOperationType.delete,
                  groupValue: _selectedOperation,
                  onChanged: (value) {
                    setState(() {
                      _selectedOperation = value!;
                    });
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: Text(l10n.cancel),
        ),
        FilledButton(
          onPressed: _isLoading ? null : _performOperation,
          style: _selectedOperation == BulkOperationType.delete
              ? FilledButton.styleFrom(
                  backgroundColor: theme.colorScheme.error,
                  foregroundColor: theme.colorScheme.onError,
                )
              : null,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(_getActionButtonText(l10n)),
        ),
      ],
    );
  }

  String _getActionButtonText(AppLocalizations l10n) {
    switch (_selectedOperation) {
      case BulkOperationType.adjustPercentage:
        return l10n.adjustBudgets;
      case BulkOperationType.activate:
        return l10n.activateBudgets;
      case BulkOperationType.deactivate:
        return l10n.deactivateBudgets;
      case BulkOperationType.delete:
        return l10n.deleteBudgets;
    }
  }

  Future<void> _performOperation() async {
    if (_selectedOperation == BulkOperationType.adjustPercentage) {
      if (!_formKey.currentState!.validate()) {
        return;
      }
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final controller = ref.read(bulkBudgetControllerProvider.notifier);

      switch (_selectedOperation) {
        case BulkOperationType.adjustPercentage:
          final percentage = double.parse(_percentageController.text);
          await controller.adjustBudgetsByPercentage(
            budgetIds: widget.selectedBudgetIds,
            percentageChange: percentage,
          );
        case BulkOperationType.activate:
          await controller.changeBudgetStatus(
            budgetIds: widget.selectedBudgetIds,
            isActive: true,
          );
        case BulkOperationType.deactivate:
          await controller.changeBudgetStatus(
            budgetIds: widget.selectedBudgetIds,
            isActive: false,
          );
        case BulkOperationType.delete:
          await controller.deleteBudgets(budgetIds: widget.selectedBudgetIds);
      }

      // Check the controller state for errors
      final controllerState = ref.read(bulkBudgetControllerProvider);
      if (controllerState.hasError) {
        final error = controllerState.error!;
        if (error is Exception) {
          throw error;
        } else {
          throw Exception(error.toString());
        }
      }

      if (mounted) {
        Navigator.of(context).pop();
        widget.onOperationComplete();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_getSuccessMessage()),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    } on Exception {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.bulkOperationFailed),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _getSuccessMessage() {
    final l10n = AppLocalizations.of(context)!;
    switch (_selectedOperation) {
      case BulkOperationType.adjustPercentage:
        return l10n.budgetsAdjusted;
      case BulkOperationType.activate:
        return l10n.budgetsActivated;
      case BulkOperationType.deactivate:
        return l10n.budgetsDeactivated;
      case BulkOperationType.delete:
        return l10n.budgetsDeleted;
    }
  }
}

/// Types of bulk operations available
enum BulkOperationType { adjustPercentage, activate, deactivate, delete }
