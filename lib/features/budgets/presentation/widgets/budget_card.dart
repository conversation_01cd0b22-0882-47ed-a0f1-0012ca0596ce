import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/budget.dart';
import 'package:budapp/features/budgets/presentation/widgets/budget_progress_bar.dart';
import 'package:budapp/features/budgets/providers/budget_providers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Budget card widget displaying budget information and progress
class BudgetCard extends ConsumerWidget {
  const BudgetCard({
    super.key,
    required this.budget,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.currentDate,
  });
  final Budget budget;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final DateTime? currentDate;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final progressAsync = ref.watch(
      budgetProgressProvider(budget.id, currentDate ?? DateTime.now()),
    );

    return Card(
      elevation: DesignTokens.elevation1,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(DesignTokens.borderRadius12),
        child: Padding(
          padding: const EdgeInsets.all(DesignTokens.spacing16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with name and overflow menu
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          budget.getDisplayName(),
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: DesignTokens.spacing4),
                        Consumer(
                          builder: (context, ref, _) {
                            final currencyFormatter = ref.watch(
                              currencyFormatterProvider,
                            );
                            return Text(
                              currencyFormatter.formatAmount(
                                budget.plannedAmountCents,
                              ),
                              style: theme.textTheme.bodyLarge?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                  if (onEdit != null || onDelete != null)
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            onEdit?.call();
                          case 'delete':
                            onDelete?.call();
                        }
                      },
                      itemBuilder: (context) => [
                        if (onEdit != null)
                          PopupMenuItem(
                            value: 'edit',
                            child: Row(
                              children: [
                                const Icon(Icons.edit),
                                const SizedBox(width: DesignTokens.spacing8),
                                Text(l10n.edit),
                              ],
                            ),
                          ),
                        if (onDelete != null)
                          PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(
                                  Icons.delete,
                                  color: theme.colorScheme.error,
                                ),
                                const SizedBox(width: DesignTokens.spacing8),
                                Text(
                                  l10n.delete,
                                  style: TextStyle(
                                    color: theme.colorScheme.error,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                ],
              ),

              const SizedBox(height: DesignTokens.spacing16),

              // Progress section
              progressAsync.when(
                loading: () => const LinearProgressIndicator(),
                error: (error, stackTrace) => Text(
                  'Error loading progress',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.error,
                  ),
                ),
                data: (progress) => Column(
                  children: [
                    BudgetProgressBar(progress: progress),
                    const SizedBox(height: DesignTokens.spacing8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Consumer(
                          builder: (context, ref, _) {
                            final currencyFormatter = ref.watch(
                              currencyFormatterProvider,
                            );
                            return Text(
                              '${l10n.spent}: ${currencyFormatter.formatAmount((progress.spent * 100).round())}',
                              style: theme.textTheme.bodySmall,
                            );
                          },
                        ),
                        Consumer(
                          builder: (context, ref, _) {
                            final currencyFormatter = ref.watch(
                              currencyFormatterProvider,
                            );
                            return Text(
                              '${l10n.remaining}: ${currencyFormatter.formatAmount((progress.remaining * 100).round())}',
                              style: theme.textTheme.bodySmall,
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Budget metadata
              const SizedBox(height: DesignTokens.spacing12),
              Row(
                children: [
                  Icon(
                    _getBudgetTypeIcon(),
                    size: 16,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: DesignTokens.spacing4),
                  Text(
                    _getBudgetTypeLabel(l10n),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(width: DesignTokens.spacing16),
                  Icon(
                    Icons.schedule,
                    size: 16,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: DesignTokens.spacing4),
                  Text(
                    _getBudgetPeriodLabel(l10n),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Removed currency formatting methods - now using global currency formatter

  /// Get budget type icon
  IconData _getBudgetTypeIcon() {
    switch (budget.type) {
      case BudgetType.expense:
        return Icons.trending_down;
      case BudgetType.income:
        return Icons.trending_up;
    }
  }

  /// Get budget type label
  String _getBudgetTypeLabel(AppLocalizations l10n) {
    switch (budget.type) {
      case BudgetType.expense:
        return l10n.expense;
      case BudgetType.income:
        return l10n.income;
    }
  }

  /// Get budget period label
  String _getBudgetPeriodLabel(AppLocalizations l10n) {
    switch (budget.period) {
      case BudgetPeriod.monthly:
        return l10n.monthly;
      case BudgetPeriod.yearly:
        return l10n.yearly;
    }
  }
}
