import 'package:flutter/foundation.dart';

/// Custom exception for budget-related operations
class BudgetException implements Exception {
  const BudgetException(
    this.message, {
    this.code,
    this.originalError,
    this.stackTrace,
  });
  final String message;
  final String? code;
  final dynamic originalError;
  final StackTrace? stackTrace;

  @override
  String toString() {
    if (code != null) {
      return 'BudgetException [$code]: $message';
    }
    return 'BudgetException: $message';
  }
}

/// Specific budget error types
class BudgetErrorCodes {
  static const String notFound = 'BUDGET_NOT_FOUND';
  static const String validationFailed = 'VALIDATION_FAILED';
  static const String overlapDetected = 'OVERLAP_DETECTED';
  static const String unauthorized = 'UNAUTHORIZED';
  static const String networkError = 'NETWORK_ERROR';
  static const String firestoreError = 'FIRESTORE_ERROR';
  static const String invalidOperation = 'INVALID_OPERATION';
  static const String concurrencyError = 'CONCURRENCY_ERROR';
  static const String quotaExceeded = 'QUOTA_EXCEEDED';
  static const String permissionDenied = 'PERMISSION_DENIED';
}

/// Service for handling budget-related errors and providing user-friendly messages
// ignore: avoid_classes_with_only_static_members
class BudgetErrorService {
  /// Convert a generic error to a BudgetException with appropriate error code
  static BudgetException handleError(dynamic error, [StackTrace? stackTrace]) {
    if (error is BudgetException) {
      return error;
    }

    // Handle Firestore errors
    if (error.toString().contains('permission-denied')) {
      return BudgetException(
        'You do not have permission to perform this operation',
        code: BudgetErrorCodes.permissionDenied,
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    if (error.toString().contains('quota-exceeded')) {
      return BudgetException(
        'Service quota exceeded. Please try again later',
        code: BudgetErrorCodes.quotaExceeded,
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    if (error.toString().contains('unavailable') ||
        error.toString().contains('deadline-exceeded')) {
      return BudgetException(
        'Network error. Please check your connection and try again',
        code: BudgetErrorCodes.networkError,
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    // Handle validation errors
    if (error.toString().contains('validation failed') ||
        error.toString().contains('invalid')) {
      return BudgetException(
        'Budget data validation failed',
        code: BudgetErrorCodes.validationFailed,
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    // Handle overlap errors
    if (error.toString().contains('overlap')) {
      return BudgetException(
        'A budget already exists for this category and period',
        code: BudgetErrorCodes.overlapDetected,
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    // Handle not found errors
    if (error.toString().contains('not found')) {
      return BudgetException(
        'Budget not found',
        code: BudgetErrorCodes.notFound,
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    // Handle unauthorized errors
    if (error.toString().contains('not belong') ||
        error.toString().contains('unauthorized')) {
      return BudgetException(
        'You are not authorized to access this budget',
        code: BudgetErrorCodes.unauthorized,
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    // Generic Firestore error
    if (error.toString().contains('firestore') ||
        error.toString().contains('cloud-firestore')) {
      return BudgetException(
        'Database error occurred. Please try again',
        code: BudgetErrorCodes.firestoreError,
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    // Default error
    return BudgetException(
      'An unexpected error occurred: $error',
      originalError: error,
      stackTrace: stackTrace,
    );
  }

  /// Get user-friendly error message for display
  static String getUserFriendlyMessage(BudgetException error) {
    switch (error.code) {
      case BudgetErrorCodes.notFound:
        return 'Budget not found. It may have been deleted.';
      case BudgetErrorCodes.validationFailed:
        return 'Please check your budget information and try again.';
      case BudgetErrorCodes.overlapDetected:
        return 'A budget already exists for this category and period.';
      case BudgetErrorCodes.unauthorized:
        return 'You do not have permission to access this budget.';
      case BudgetErrorCodes.networkError:
        return 'Network error. Please check your connection.';
      case BudgetErrorCodes.firestoreError:
        return 'Database error. Please try again later.';
      case BudgetErrorCodes.invalidOperation:
        return 'This operation is not allowed.';
      case BudgetErrorCodes.concurrencyError:
        return 'Budget was modified by another user. Please refresh and try again.';
      case BudgetErrorCodes.quotaExceeded:
        return 'Service limit reached. Please try again later.';
      case BudgetErrorCodes.permissionDenied:
        return 'Permission denied. Please check your account permissions.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Log error for debugging (in debug mode only)
  static void logError(BudgetException error) {
    if (kDebugMode) {
      debugPrint('BudgetError [${error.code}]: ${error.message}');
      if (error.originalError != null) {
        debugPrint('Original error: ${error.originalError}');
      }
      if (error.stackTrace != null) {
        debugPrint('Stack trace: ${error.stackTrace}');
      }
    }
  }

  /// Create specific error types
  static BudgetException notFound(String budgetId) {
    return BudgetException(
      'Budget with ID $budgetId not found',
      code: BudgetErrorCodes.notFound,
    );
  }

  static BudgetException validationFailed(List<String> errors) {
    return BudgetException(
      'Budget validation failed: ${errors.join(', ')}',
      code: BudgetErrorCodes.validationFailed,
    );
  }

  static BudgetException overlapDetected(String categoryId, String period) {
    return BudgetException(
      'A budget already exists for category $categoryId in period $period',
      code: BudgetErrorCodes.overlapDetected,
    );
  }

  static BudgetException unauthorized(String budgetId) {
    return BudgetException(
      'You are not authorized to access budget $budgetId',
      code: BudgetErrorCodes.unauthorized,
    );
  }

  static BudgetException invalidOperation(String operation) {
    return BudgetException(
      'Invalid operation: $operation',
      code: BudgetErrorCodes.invalidOperation,
    );
  }

  static BudgetException concurrencyError() {
    return const BudgetException(
      'Budget was modified by another user. Please refresh and try again',
      code: BudgetErrorCodes.concurrencyError,
    );
  }
}
