import 'package:budapp/data/models/budget.dart';
import 'package:budapp/utils/validation_result.dart';

/// Service for validating budget data and business rules
// ignore: avoid_classes_with_only_static_members
class BudgetValidators {
  /// Validates budget name
  static ValidationResult validateBudgetName(String name) {
    final trimmedName = name.trim();

    if (trimmedName.isEmpty) {
      return ValidationResult.singleError('Budget name is required');
    }

    if (trimmedName.length < 2) {
      return ValidationResult.singleError(
        'Budget name must be at least 2 characters',
      );
    }

    if (trimmedName.length > 50) {
      return ValidationResult.singleError(
        'Budget name must be 50 characters or less',
      );
    }

    // Check for invalid characters
    final invalidChars = RegExp(r'[<>:"/\\|?*]');
    if (invalidChars.hasMatch(trimmedName)) {
      return ValidationResult.singleError(
        'Budget name contains invalid characters',
      );
    }

    return ValidationResult.success();
  }

  /// Validates budget amount
  static ValidationResult validateBudgetAmount(double amount) {
    if (amount <= 0) {
      return ValidationResult.singleError(
        'Budget amount must be greater than 0',
      );
    }

    if (amount > 999999999.99) {
      return ValidationResult.singleError('Budget amount is too large');
    }

    // Check for reasonable decimal places (max 2)
    final amountString = amount.toStringAsFixed(2);
    final parsedAmount = double.parse(amountString);
    if ((amount - parsedAmount).abs() > 0.001) {
      return ValidationResult.singleError(
        'Budget amount can have at most 2 decimal places',
      );
    }

    return ValidationResult.success();
  }

  /// Validates budget period
  static ValidationResult validateBudgetPeriod(BudgetPeriod period) {
    // All enum values are valid, but we can add business logic here
    switch (period) {
      case BudgetPeriod.monthly:
      case BudgetPeriod.yearly:
        return ValidationResult.success();
    }
  }

  /// Validates budget hierarchy relationships
  static ValidationResult validateBudgetHierarchy(
    String? parentBudgetId,
    String? categoryId,
  ) {
    // If no parent budget, hierarchy is valid
    if (parentBudgetId == null) {
      return ValidationResult.success();
    }

    // If has parent budget, must have a category
    if (categoryId == null) {
      return ValidationResult.singleError(
        'Child budgets must be associated with a category',
      );
    }

    // Additional hierarchy validation would require access to existing budgets
    // This would be handled in the repository layer
    return ValidationResult.success();
  }

  /// Validates budget overlap prevention
  static ValidationResult validateBudgetOverlap(
    Budget budget,
    List<Budget> existingBudgets,
  ) {
    // If no category specified (overall budget), no overlap check needed
    if (budget.categoryId == null) {
      return ValidationResult.success();
    }

    // Check for overlapping budgets with same category and period
    for (final existingBudget in existingBudgets) {
      // Skip if this is the same budget (for updates)
      if (existingBudget.id == budget.id) {
        continue;
      }

      // Skip if not active
      if (!existingBudget.isActive) {
        continue;
      }

      // Check for overlap using precise period matching
      if (existingBudget.categoryId == budget.categoryId &&
          existingBudget.period == budget.period &&
          existingBudget.isForPeriod(budget.periodStart)) {
        return ValidationResult.singleError(
          'A budget already exists for this category and period',
        );
      }
    }

    return ValidationResult.success();
  }

  /// Validates a complete budget object
  static ValidationResult validateBudget(Budget budget) {
    final errors = <String>[];

    // Validate amount
    final amountResult = validateBudgetAmount(
      budget.plannedAmountCents / 100.0,
    );
    if (!amountResult.isValid) {
      errors.addAll(amountResult.errors);
    }

    // Validate period
    final periodResult = validateBudgetPeriod(budget.period);
    if (!periodResult.isValid) {
      errors.addAll(periodResult.errors);
    }

    // Validate hierarchy
    final hierarchyResult = validateBudgetHierarchy(
      budget.parentBudgetId,
      budget.categoryId,
    );
    if (!hierarchyResult.isValid) {
      errors.addAll(hierarchyResult.errors);
    }

    // Removed currency validation - currency is now global preference

    if (errors.isEmpty) {
      return ValidationResult.success();
    } else {
      return ValidationResult.failure(errors);
    }
  }

  /// Validates budget for creation (additional checks)
  static ValidationResult validateBudgetForCreation(
    Budget budget,
    List<Budget> existingBudgets,
  ) {
    final errors = <String>[];

    // Run standard validation
    final standardResult = validateBudget(budget);
    if (!standardResult.isValid) {
      errors.addAll(standardResult.errors);
    }

    // Check for overlaps
    final overlapResult = validateBudgetOverlap(budget, existingBudgets);
    if (!overlapResult.isValid) {
      errors.addAll(overlapResult.errors);
    }

    if (errors.isEmpty) {
      return ValidationResult.success();
    } else {
      return ValidationResult.failure(errors);
    }
  }

  /// Validates budget for update (additional checks)
  static ValidationResult validateBudgetForUpdate(
    Budget budget,
    Budget originalBudget,
    List<Budget> existingBudgets,
  ) {
    final errors = <String>[];

    // Run standard validation
    final standardResult = validateBudget(budget);
    if (!standardResult.isValid) {
      errors.addAll(standardResult.errors);
    }

    // Check for overlaps (excluding the current budget)
    final overlapResult = validateBudgetOverlap(budget, existingBudgets);
    if (!overlapResult.isValid) {
      errors.addAll(overlapResult.errors);
    }

    // Validate that critical fields haven't changed inappropriately
    if (budget.id != originalBudget.id) {
      errors.add('Budget ID cannot be changed');
    }

    if (budget.userId != originalBudget.userId) {
      errors.add('Budget user cannot be changed');
    }

    if (errors.isEmpty) {
      return ValidationResult.success();
    } else {
      return ValidationResult.failure(errors);
    }
  }
}
