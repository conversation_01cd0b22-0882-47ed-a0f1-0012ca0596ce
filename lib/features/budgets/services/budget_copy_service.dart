import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/repositories/interfaces/budget_repository.dart';
import 'package:budapp/data/repositories/interfaces/category_repository.dart';

/// Result of a budget copying operation
class BudgetCopyResult {
  const BudgetCopyResult({
    required this.success,
    required this.copiedCount,
    required this.skippedCount,
    required this.errors,
    required this.skippedReasons,
  });

  factory BudgetCopyResult.success({
    required int copiedCount,
    required int skippedCount,
    List<String> skippedReasons = const [],
  }) {
    return BudgetCopyResult(
      success: true,
      copiedCount: copiedCount,
      skippedCount: skippedCount,
      errors: const [],
      skippedReasons: skippedReasons,
    );
  }

  factory BudgetCopyResult.failure({
    required List<String> errors,
    int copiedCount = 0,
    int skippedCount = 0,
  }) {
    return BudgetCopyResult(
      success: false,
      copiedCount: copiedCount,
      skippedCount: skippedCount,
      errors: errors,
      skippedReasons: const [],
    );
  }
  final bool success;
  final int copiedCount;
  final int skippedCount;
  final List<String> errors;
  final List<String> skippedReasons;
}

/// Service for copying budgets between time periods
class BudgetCopyService {
  const BudgetCopyService({
    required this.budgetRepository,
    required this.categoryRepository,
  });
  final BudgetRepository budgetRepository;
  final ICategoryRepository categoryRepository;

  /// Copy budgets from a previous period to the current period
  ///
  /// This method:
  /// 1. Gets all budgets from the source period
  /// 2. Filters out budgets for inactive categories
  /// 3. Skips budgets that already exist in the target period
  /// 4. Creates new budgets in the target period with the same amounts
  ///
  /// Returns a [BudgetCopyResult] with details about the operation
  Future<BudgetCopyResult> copyBudgetsFromPreviousPeriod({
    required DateTime sourcePeriod,
    required DateTime targetPeriod,
    required BudgetPeriod periodType,
  }) async {
    try {
      // Get budgets from source period
      final sourceBudgets = await budgetRepository.getBudgetsByPeriod(
        sourcePeriod,
        periodType,
      );

      if (sourceBudgets.isEmpty) {
        return BudgetCopyResult.success(
          copiedCount: 0,
          skippedCount: 0,
          skippedReasons: ['No budgets found in source period'],
        );
      }

      // Get existing budgets in target period
      final existingBudgets = await budgetRepository.getBudgetsByPeriod(
        targetPeriod,
        periodType,
      );

      // Get active categories to validate against
      final activeCategories = await categoryRepository.getActiveCategories();
      final activeCategoryIds = activeCategories.map((c) => c.id).toSet();

      // Process each source budget
      final budgetsToCreate = <Budget>[];
      final skippedReasons = <String>[];
      var skippedCount = 0;

      for (final sourceBudget in sourceBudgets) {
        // Skip if category is no longer active
        if (sourceBudget.categoryId != null &&
            !activeCategoryIds.contains(sourceBudget.categoryId)) {
          skippedCount++;
          skippedReasons.add(
            'Category no longer active: ${sourceBudget.categoryId}',
          );
          continue;
        }

        // Skip if budget already exists for this category in target period
        final existingBudgetForCategory = existingBudgets.any(
          (budget) => budget.categoryId == sourceBudget.categoryId,
        );

        if (existingBudgetForCategory) {
          skippedCount++;
          skippedReasons.add(
            'Budget already exists for category: ${sourceBudget.categoryId}',
          );
          continue;
        }

        // Create new budget for target period
        final targetPeriodStart = periodType == BudgetPeriod.monthly
            ? DateTime(targetPeriod.year, targetPeriod.month, 1)
            : DateTime(targetPeriod.year, 1, 1);

        final newBudget = Budget.create(
          userId: sourceBudget.userId,
          type: sourceBudget.type,
          plannedAmountCents: sourceBudget.plannedAmountCents,
          // Removed currencyCode - now using global currency preference
          period: periodType,
          periodStart: targetPeriodStart,
          categoryId: sourceBudget.categoryId,
          parentBudgetId: sourceBudget.parentBudgetId,
        );

        budgetsToCreate.add(newBudget);
      }

      // Create all budgets in a batch operation
      if (budgetsToCreate.isNotEmpty) {
        await budgetRepository.batchCreateBudgets(budgetsToCreate);
      }

      return BudgetCopyResult.success(
        copiedCount: budgetsToCreate.length,
        skippedCount: skippedCount,
        skippedReasons: skippedReasons,
      );
    } on Exception catch (error) {
      return BudgetCopyResult.failure(errors: [error.toString()]);
    }
  }

  /// Get available source periods that have budgets
  ///
  /// Returns a list of periods that contain budgets and can be used
  /// as source periods for copying. Excludes the current period.
  Future<List<DateTime>> getAvailableSourcePeriods({
    required DateTime currentPeriod,
    required BudgetPeriod periodType,
  }) async {
    try {
      // Get all budgets for the user
      final allBudgets = await budgetRepository.getAllBudgets();

      // Filter budgets by period type
      final budgetsForPeriodType = allBudgets
          .where((budget) => budget.period == periodType && budget.isActive)
          .toList();

      // Extract unique periods, excluding current period
      final periods = <DateTime>{};

      for (final budget in budgetsForPeriodType) {
        DateTime period;

        switch (periodType) {
          case BudgetPeriod.monthly:
            period = DateTime(budget.createdAt.year, budget.createdAt.month, 1);
          case BudgetPeriod.yearly:
            period = DateTime(budget.createdAt.year, 1, 1);
        }

        // Only include periods before the current period
        if (period.isBefore(currentPeriod)) {
          periods.add(period);
        }
      }

      // Sort periods in descending order (most recent first)
      final sortedPeriods = periods.toList()..sort((a, b) => b.compareTo(a));

      return sortedPeriods;
    } on Exception {
      // Return empty list on error
      return [];
    }
  }

  /// Get the previous period for a given period and period type
  ///
  /// Helper method to calculate the immediate previous period
  DateTime getPreviousPeriod(DateTime currentPeriod, BudgetPeriod periodType) {
    switch (periodType) {
      case BudgetPeriod.monthly:
        if (currentPeriod.month == 1) {
          return DateTime(currentPeriod.year - 1, 12, 1);
        } else {
          return DateTime(currentPeriod.year, currentPeriod.month - 1, 1);
        }
      case BudgetPeriod.yearly:
        return DateTime(currentPeriod.year - 1, 1, 1);
    }
  }

  /// Format a period for display
  ///
  /// Helper method to format periods for UI display
  String formatPeriod(DateTime period, BudgetPeriod periodType) {
    switch (periodType) {
      case BudgetPeriod.monthly:
        final months = [
          'January',
          'February',
          'March',
          'April',
          'May',
          'June',
          'July',
          'August',
          'September',
          'October',
          'November',
          'December',
        ];
        return '${months[period.month - 1]} ${period.year}';
      case BudgetPeriod.yearly:
        return '${period.year}';
    }
  }
}
