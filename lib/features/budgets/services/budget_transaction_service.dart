import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/models/transaction.dart' as app_models;
import 'package:budapp/data/repositories/interfaces/budget_repository.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart' as firestore;

/// Service for handling automatic budget updates when transactions are created, updated, or deleted
///
/// This service provides the core logic for transaction-budget integration by:
/// - Finding budgets that match transaction criteria (category, period, currency, type)
/// - Calculating budget amount updates based on transaction amounts
/// - Providing methods for both applying and reverting budget updates
/// - Ensuring atomic operations with comprehensive error handling
class BudgetTransactionService {
  const BudgetTransactionService(
    this._budgetRepository,
    this._firestoreService,
  );
  final BudgetRepository _budgetRepository;
  final FirestoreService _firestoreService;

  /// Update budgets when a transaction is created or updated
  ///
  /// Finds all budgets that match the transaction criteria and updates their
  /// currentAmountCents field. For expense transactions, adds to expense budget
  /// amounts. For income transactions, adds to income budget amounts.
  /// Transfer transactions do not affect budgets.
  ///
  /// Returns a list of budget updates that were applied for potential rollback.
  Future<List<BudgetUpdate>> updateBudgetsForTransaction(
    app_models.Transaction transaction,
    firestore.Transaction? firestoreTransaction,
  ) async {
    // Skip budget updates for transfer transactions
    if (transaction.type == app_models.TransactionType.transfer) {
      return [];
    }

    // Skip budget updates for non-completed transactions
    if (transaction.status != app_models.TransactionStatus.completed) {
      return [];
    }

    // Find or create matching budgets
    final matchingBudgets = await _findOrCreateMatchingBudgets(
      transaction,
      firestoreTransaction,
    );

    if (matchingBudgets.isEmpty) {
      return [];
    }

    // Calculate budget updates
    final budgetUpdates = _calculateBudgetUpdates(transaction, matchingBudgets);

    // Apply updates
    await _applyBudgetUpdates(budgetUpdates, firestoreTransaction);

    return budgetUpdates;
  }

  /// Revert budget updates when a transaction is deleted or updated
  ///
  /// Takes the original transaction and reverts any budget updates that were
  /// applied when the transaction was created. This ensures budget amounts
  /// remain accurate when transactions are removed or modified.
  Future<void> revertBudgetsForTransaction(
    app_models.Transaction originalTransaction,
    firestore.Transaction? firestoreTransaction,
  ) async {
    // Skip budget updates for transfer transactions
    if (originalTransaction.type == app_models.TransactionType.transfer) {
      return;
    }

    // Skip budget updates for non-completed transactions
    if (originalTransaction.status != app_models.TransactionStatus.completed) {
      return;
    }

    // Find matching budgets
    final matchingBudgets = await _findMatchingBudgets(originalTransaction);

    if (matchingBudgets.isEmpty) {
      return;
    }

    // Calculate reverse budget updates (subtract instead of add)
    final budgetUpdates = _calculateBudgetUpdates(
      originalTransaction,
      matchingBudgets,
      reverse: true,
    );

    // Apply reverse updates
    await _applyBudgetUpdates(budgetUpdates, firestoreTransaction);
  }

  /// Update budgets when a transaction is modified
  ///
  /// Handles the complex case where a transaction is updated by:
  /// 1. Reverting the budget impact of the original transaction
  /// 2. Applying the budget impact of the updated transaction
  ///
  /// This ensures budget amounts remain accurate when transaction details change.
  Future<List<BudgetUpdate>> updateBudgetsForTransactionUpdate(
    app_models.Transaction originalTransaction,
    app_models.Transaction updatedTransaction,
    firestore.Transaction? firestoreTransaction,
  ) async {
    // First, revert the original transaction's budget impact
    await revertBudgetsForTransaction(
      originalTransaction,
      firestoreTransaction,
    );

    // Then, apply the updated transaction's budget impact
    return updateBudgetsForTransaction(
      updatedTransaction,
      firestoreTransaction,
    );
  }

  /// Find budgets that match the transaction criteria
  ///
  /// Matches budgets based on:
  /// - Transaction type (expense/income) matches budget type (expense/income)
  /// - Transaction categoryId matches budget categoryId (for category budgets)
  /// - Total budgets (categoryId == null) match transaction type
  /// - Transaction date falls within budget period (monthly/yearly)
  /// - Transaction currencyCode matches budget currencyCode
  /// - Budget is active (isActive = true)
  Future<List<Budget>> _findMatchingBudgets(
    app_models.Transaction transaction,
  ) async {
    // Get all active budgets for the user
    final allBudgets = await _budgetRepository.getAllBudgets();
    final activeBudgets = allBudgets
        .where((budget) => budget.isActive)
        .toList();

    // Filter budgets that match transaction criteria
    final matchingBudgets = <Budget>[];

    for (final budget in activeBudgets) {
      if (_doesBudgetMatchTransaction(budget, transaction)) {
        matchingBudgets.add(budget);
      }
    }

    return matchingBudgets;
  }

  /// Find budgets that match the transaction criteria, creating them if none exist
  ///
  /// This method first attempts to find existing budgets that match the transaction.
  /// If no matching budgets are found, it automatically creates:
  /// 1. A category-specific budget for the transaction's category
  /// 2. A total budget for the transaction's type (expense/income) if it doesn't exist
  Future<List<Budget>> _findOrCreateMatchingBudgets(
    app_models.Transaction transaction,
    firestore.Transaction? firestoreTransaction,
  ) async {
    // First, try to find existing matching budgets
    final existingBudgets = await _findMatchingBudgets(transaction);

    if (existingBudgets.isNotEmpty) {
      return existingBudgets;
    }

    // No existing budgets found, create default budgets
    final budgetType = _getBudgetTypeForTransaction(transaction.type);
    if (budgetType == null) {
      // Transfer transactions don't get budgets
      return [];
    }

    final createdBudgets = <Budget>[];

    // Create a category-specific budget for this transaction
    final categoryBudget = await _createDefaultBudget(
      transaction,
      budgetType,
      firestoreTransaction,
      categoryId: transaction.categoryId,
    );
    createdBudgets.add(categoryBudget);

    // Create or find total budget for this transaction type
    final totalBudget = await _findOrCreateTotalBudget(
      transaction,
      budgetType,
      firestoreTransaction,
    );
    if (totalBudget != null) {
      createdBudgets.add(totalBudget);
    }

    return createdBudgets;
  }

  /// Create a default budget for a transaction when none exists
  ///
  /// Creates a budget with:
  /// - Type matching the transaction type (expense/income)
  /// - Category matching the provided categoryId (null for total budgets)
  /// - Monthly period (default)
  /// - Currency matching the transaction currency
  /// - Planned amount of 0 (user can set later)
  /// - Current amount of 0 (will be updated by transaction)
  Future<Budget> _createDefaultBudget(
    app_models.Transaction transaction,
    BudgetType budgetType,
    firestore.Transaction? firestoreTransaction, {
    String? categoryId,
  }) async {
    // Calculate the period start date based on transaction date
    final transactionDate = transaction.transactionDate ?? DateTime.now();
    final periodStartDate = DateTime(
      transactionDate.year,
      transactionDate.month,
      1,
    );

    // Create the default budget
    final defaultBudget = Budget(
      id: '', // Will be set when saved
      userId: transaction.userId,
      type: budgetType,
      categoryId:
          categoryId, // Use provided categoryId (null for total budgets)
      period: BudgetPeriod.monthly, // Default to monthly
      periodStart: periodStartDate, // Explicit period start
      plannedAmountCents: 0, // Default planned amount - user can set later
      currentAmountCents: 0, // Will be updated by transaction
      // Removed currencyCode - now using global currency preference
      isActive: true,
      schemaVersion: 1,
      createdAt:
          periodStartDate, // Use period start for budget period calculation
      updatedAt: DateTime.now(),
    );

    // Save the budget to Firebase
    if (firestoreTransaction != null) {
      // We're in a Firestore transaction, create the budget manually
      final budgetRef = _firestoreService.instance
          .collection('users')
          .doc(transaction.userId)
          .collection('budgets')
          .doc();

      final budgetWithId = defaultBudget.copyWith(
        id: budgetRef.id,
        createdAt: periodStartDate,
        updatedAt: DateTime.now(),
      );

      firestoreTransaction.set(budgetRef, budgetWithId.toJson());
      return budgetWithId;
    } else {
      // Use the repository to create the budget
      return _budgetRepository.createBudget(defaultBudget);
    }
  }

  /// Find or create a total budget for the transaction type and period
  ///
  /// Total budgets have categoryId == null and aggregate all category budgets
  /// within their respective expense/income types for the period.
  Future<Budget?> _findOrCreateTotalBudget(
    app_models.Transaction transaction,
    BudgetType budgetType,
    firestore.Transaction? firestoreTransaction,
  ) async {
    // Calculate the period start date based on transaction date
    final transactionDate = transaction.transactionDate ?? DateTime.now();
    final periodStartDate = DateTime(
      transactionDate.year,
      transactionDate.month,
      1,
    );

    // Check if a total budget already exists for this type and period
    final allBudgets = await _budgetRepository.getAllBudgets();
    final existingTotalBudget = allBudgets.firstWhere(
      (budget) =>
          budget.isActive &&
          budget.type == budgetType &&
          budget.categoryId == null && // Total budget
          true && // Removed currency comparison - now using global currency
          _isDateInSameMonth(budget.createdAt, periodStartDate),
      orElse: () => Budget(
        id: '',
        userId: '',
        type: budgetType,
        categoryId: null,
        period: BudgetPeriod.monthly,
        periodStart: DateTime.now(),
        plannedAmountCents: 0,
        currentAmountCents: 0,
        // Removed currencyCode - now using global currency preference
        isActive: false,
        schemaVersion: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    // If total budget exists, return it
    if (existingTotalBudget.isActive) {
      return existingTotalBudget;
    }

    // Create a new total budget
    return _createDefaultBudget(
      transaction,
      budgetType,
      firestoreTransaction,
      categoryId: null, // Total budget has no category
    );
  }

  /// Check if a budget matches a transaction based on type, category, period, and currency
  bool _doesBudgetMatchTransaction(
    Budget budget,
    app_models.Transaction transaction,
  ) {
    // 1. Check transaction type matches budget type
    final budgetType = _getBudgetTypeForTransaction(transaction.type);
    if (budgetType == null || budget.type != budgetType) {
      return false;
    }

    // 2. Check category match
    if (budget.categoryId == null) {
      // This is a total budget - it matches all transactions of the same type
      // Continue to other checks
    } else if (transaction.categoryId != null) {
      // Both have categories - they must match
      if (budget.categoryId != transaction.categoryId) {
        return false;
      }
    } else {
      // Budget has category but transaction doesn't - no match
      return false;
    }

    // 3. Removed currency check - now using global currency preference

    // 4. Check period match
    final transactionDate = transaction.transactionDate;
    if (transactionDate == null ||
        !_doesTransactionDateMatchBudgetPeriod(
          transactionDate,
          budget,
        )) {
      return false;
    }

    return true;
  }

  /// Get the corresponding budget type for a transaction type
  BudgetType? _getBudgetTypeForTransaction(
    app_models.TransactionType transactionType,
  ) {
    switch (transactionType) {
      case app_models.TransactionType.expense:
        return BudgetType.expense;
      case app_models.TransactionType.income:
        return BudgetType.income;
      case app_models.TransactionType.transfer:
        return null; // Transfers don't affect budgets
    }
  }

  /// Check if a transaction date falls within a budget's period
  bool _doesTransactionDateMatchBudgetPeriod(
    DateTime transactionDate,
    Budget budget,
  ) {
    switch (budget.period) {
      case BudgetPeriod.monthly:
        return _isDateInSameMonth(transactionDate, budget.createdAt);
      case BudgetPeriod.yearly:
        return _isDateInSameYear(transactionDate, budget.createdAt);
    }
  }

  /// Check if two dates are in the same month and year
  bool _isDateInSameMonth(DateTime date1, DateTime date2) {
    return date1.year == date2.year && date1.month == date2.month;
  }

  /// Check if two dates are in the same year
  bool _isDateInSameYear(DateTime date1, DateTime date2) {
    return date1.year == date2.year;
  }

  /// Calculate budget updates needed for a transaction
  List<BudgetUpdate> _calculateBudgetUpdates(
    app_models.Transaction transaction,
    List<Budget> matchingBudgets, {
    bool reverse = false,
  }) {
    final updates = <BudgetUpdate>[];

    for (final budget in matchingBudgets) {
      final amountChange = reverse
          ? -transaction.amountCents
          : transaction.amountCents;
      final newCurrentAmount = budget.currentAmountCents + amountChange;

      // Ensure currentAmountCents doesn't go below 0
      final finalCurrentAmount = newCurrentAmount < 0 ? 0 : newCurrentAmount;

      final updatedBudget = budget.copyWith(
        currentAmountCents: finalCurrentAmount,
        updatedAt: DateTime.now(),
      );

      updates.add(
        BudgetUpdate(
          budgetId: budget.id,
          originalBudget: budget,
          updatedBudget: updatedBudget,
          amountChange: amountChange,
        ),
      );
    }

    return updates;
  }

  /// Apply budget updates using the budget repository
  Future<void> _applyBudgetUpdates(
    List<BudgetUpdate> updates,
    firestore.Transaction? firestoreTransaction,
  ) async {
    if (updates.isEmpty) return;

    final budgetsToUpdate = updates
        .map((update) => update.updatedBudget)
        .toList();

    if (firestoreTransaction != null) {
      // If we're in a Firestore transaction, we need to update budgets manually
      // since batchUpdateBudgets doesn't support external transactions
      for (final update in updates) {
        final budgetRef = _firestoreService.instance
            .collection('users')
            .doc(update.updatedBudget.userId)
            .collection('budgets')
            .doc(update.budgetId);

        firestoreTransaction.update(budgetRef, update.updatedBudget.toJson());
      }
    } else {
      // Use the repository's batch update method
      await _budgetRepository.batchUpdateBudgets(budgetsToUpdate);
    }
  }
}

/// Represents a budget update operation for potential rollback
class BudgetUpdate {
  const BudgetUpdate({
    required this.budgetId,
    required this.originalBudget,
    required this.updatedBudget,
    required this.amountChange,
  });
  final String budgetId;
  final Budget originalBudget;
  final Budget updatedBudget;
  final int amountChange;

  @override
  String toString() {
    return 'BudgetUpdate(budgetId: $budgetId, amountChange: $amountChange)';
  }
}
