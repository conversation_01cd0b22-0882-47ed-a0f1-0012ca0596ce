import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/models/budget_progress.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/interfaces/transaction_repository.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'budget_progress_service.g.dart';

/// Service for calculating budget progress and spending analysis
class BudgetProgressService {
  BudgetProgressService({required ITransactionRepository transactionRepository})
    : _transactionRepository = transactionRepository;
  final ITransactionRepository _transactionRepository;

  /// Calculate progress for a single budget using real-time currentAmountCents
  Future<BudgetProgress> calculateBudgetProgress(
    Budget budget,
    DateTime month,
  ) async {
    // Use the real-time currentAmountCents field that's automatically updated
    // by BudgetTransactionService when transactions are created/modified/deleted
    final spentAmount = budget.currentAmountCents / 100.0;

    // Create progress object
    return BudgetProgress.fromBudgetAndSpent(
      budgetId: budget.id,
      budgetAmount: budget.plannedAmountCents / 100.0,
      spentAmount: spentAmount,
    );
  }

  /// Calculate progress for multiple budgets
  Future<List<BudgetProgress>> calculateMultipleBudgetProgress(
    List<Budget> budgets,
    DateTime month,
  ) async {
    final progressList = <BudgetProgress>[];

    for (final budget in budgets) {
      final progress = await calculateBudgetProgress(budget, month);
      progressList.add(progress);
    }

    return progressList;
  }

  /// Get spending breakdown by category for a budget period
  Future<Map<String, double>> getSpendingBreakdown(
    String userId,
    List<String> categoryIds,
    DateTime month,
  ) async {
    final breakdown = <String, double>{};

    for (final categoryId in categoryIds) {
      final transactions = await _transactionRepository
          .watchTransactionsByMonthAndCategories(
            userId: userId,
            month: month,
            categoryIds: [categoryId],
          )
          .first;

      final spent = _calculateSpentAmountFromTransactions(transactions);
      breakdown[categoryId] = spent;
    }

    return breakdown;
  }

  /// Get total spending for a month across all categories
  Future<double> getTotalSpending(String userId, DateTime month) async {
    final transactions = await _transactionRepository
        .watchTransactionsByMonthAndCategories(
          userId: userId,
          month: month,
          categoryIds: [], // Empty list means all categories
        )
        .first;

    return _calculateSpentAmountFromTransactions(transactions);
  }

  /// Get spending trend over multiple months
  Future<Map<DateTime, double>> getSpendingTrend(
    String userId,
    String? categoryId,
    List<DateTime> months,
  ) async {
    final trend = <DateTime, double>{};

    for (final month in months) {
      final categoryIds = categoryId != null ? [categoryId] : <String>[];
      final transactions = await _transactionRepository
          .watchTransactionsByMonthAndCategories(
            userId: userId,
            month: month,
            categoryIds: categoryIds,
          )
          .first;

      final spent = _calculateSpentAmountFromTransactions(transactions);
      trend[month] = spent;
    }

    return trend;
  }

  /// Calculate budget utilization percentage
  double calculateUtilization(Budget budget, double spentAmount) {
    final budgetAmount = budget.plannedAmountCents / 100.0;
    if (budgetAmount <= 0) return 0;
    return (spentAmount / budgetAmount) * 100;
  }

  /// Check if budget is over limit
  bool isBudgetOverLimit(Budget budget, double spentAmount) {
    return spentAmount > (budget.plannedAmountCents / 100.0);
  }

  /// Get remaining budget amount
  double getRemainingBudget(Budget budget, double spentAmount) {
    return (budget.plannedAmountCents / 100.0) - spentAmount;
  }

  /// Calculate spent amount from transactions (for general use)
  double _calculateSpentAmountFromTransactions(List<Transaction> transactions) {
    return transactions
        .where((transaction) => transaction.type == TransactionType.expense)
        .fold(
          0,
          (sum, transaction) => sum + (transaction.amountCents / 100).abs(),
        );
  }

  /// Get budget period start date
  DateTime getBudgetPeriodStart(Budget budget, DateTime referenceDate) {
    switch (budget.period) {
      case BudgetPeriod.monthly:
        return DateTime(referenceDate.year, referenceDate.month, 1);
      case BudgetPeriod.yearly:
        return DateTime(referenceDate.year, 1, 1);
    }
  }

  /// Get budget period end date
  DateTime getBudgetPeriodEnd(Budget budget, DateTime referenceDate) {
    switch (budget.period) {
      case BudgetPeriod.monthly:
        final nextMonth = referenceDate.month == 12
            ? DateTime(referenceDate.year + 1, 1, 1)
            : DateTime(referenceDate.year, referenceDate.month + 1, 1);
        return nextMonth.subtract(const Duration(days: 1));
      case BudgetPeriod.yearly:
        return DateTime(referenceDate.year, 12, 31);
    }
  }

  /// Check if a date falls within the budget period
  bool isDateInBudgetPeriod(
    Budget budget,
    DateTime date,
    DateTime referenceDate,
  ) {
    final periodStart = getBudgetPeriodStart(budget, referenceDate);
    final periodEnd = getBudgetPeriodEnd(budget, referenceDate);

    return date.isAfter(periodStart.subtract(const Duration(days: 1))) &&
        date.isBefore(periodEnd.add(const Duration(days: 1)));
  }

  /// Get next budget period date
  DateTime getNextBudgetPeriod(Budget budget, DateTime currentDate) {
    switch (budget.period) {
      case BudgetPeriod.monthly:
        return currentDate.month == 12
            ? DateTime(currentDate.year + 1, 1, 1)
            : DateTime(currentDate.year, currentDate.month + 1, 1);
      case BudgetPeriod.yearly:
        return DateTime(currentDate.year + 1, 1, 1);
    }
  }

  /// Get previous budget period date
  DateTime getPreviousBudgetPeriod(Budget budget, DateTime currentDate) {
    switch (budget.period) {
      case BudgetPeriod.monthly:
        return currentDate.month == 1
            ? DateTime(currentDate.year - 1, 12, 1)
            : DateTime(currentDate.year, currentDate.month - 1, 1);
      case BudgetPeriod.yearly:
        return DateTime(currentDate.year - 1, 1, 1);
    }
  }
}

/// Provider for BudgetProgressService
@riverpod
BudgetProgressService budgetProgressService(Ref ref) {
  final transactionRepository = ref.watch(transactionRepositoryProvider);
  return BudgetProgressService(transactionRepository: transactionRepository);
}
