import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/repositories/interfaces/budget_repository.dart';

/// Service for performing bulk operations on budgets
class BulkBudgetService {
  BulkBudgetService({required this.budgetRepository});
  final BudgetRepository budgetRepository;

  /// Adjust multiple budgets by a percentage
  ///
  /// [budgetIds] - List of budget IDs to adjust
  /// [percentageChange] - Percentage to adjust by (e.g., 10.0 for +10%, -20.0 for -20%)
  ///
  /// Validates percentage is within reasonable bounds (-99% to +500%)
  /// Enforces minimum budget amount of $0.01
  Future<BulkOperationResult> adjustBudgetsByPercentage({
    required List<String> budgetIds,
    required double percentageChange,
  }) async {
    // Validate percentage bounds
    if (percentageChange <= -100.0) {
      throw ArgumentError('Percentage change cannot be -100% or lower');
    }
    if (percentageChange > 500.0) {
      throw ArgumentError('Percentage change cannot exceed +500%');
    }

    if (budgetIds.isEmpty) {
      return BulkOperationResult.success(
        processedCount: 0,
        warnings: ['No budgets provided for adjustment'],
      );
    }

    var processedCount = 0;
    var failedCount = 0;
    final errors = <String>[];
    final budgetsToUpdate = <Budget>[];

    try {
      // Fetch all budgets first
      for (final budgetId in budgetIds) {
        try {
          final budget = await budgetRepository.getBudgetById(budgetId);
          if (budget == null) {
            errors.add('Budget $budgetId not found');
            failedCount++;
            continue;
          }

          // Calculate new amount in cents
          final currentAmountCents = budget.plannedAmountCents;
          final adjustmentAmountCents =
              (currentAmountCents * (percentageChange / 100.0)).round();
          final newAmountCents = currentAmountCents + adjustmentAmountCents;

          // Enforce minimum amount (1 cent)
          final finalAmountCents = newAmountCents < 1 ? 1 : newAmountCents;

          // Create updated budget
          final updatedBudget = budget.copyWith(
            plannedAmountCents: finalAmountCents,
          );
          budgetsToUpdate.add(updatedBudget);
          processedCount++;
        } on Exception catch (e) {
          errors.add('Failed to process budget $budgetId: $e');
          failedCount++;
        }
      }

      // Perform batch update if we have budgets to update
      if (budgetsToUpdate.isNotEmpty) {
        await budgetRepository.batchUpdateBudgets(budgetsToUpdate);
      }

      // If all operations failed, return failure
      if (processedCount == 0 && failedCount > 0) {
        return BulkOperationResult.failure(
          errors: errors,
          failedCount: failedCount,
        );
      }

      return BulkOperationResult.success(
        processedCount: processedCount,
        failedCount: failedCount,
        errors: errors,
      );
    } on Exception catch (e) {
      return BulkOperationResult.failure(
        errors: ['Bulk adjustment failed: $e'],
        processedCount: processedCount,
        failedCount: failedCount,
      );
    }
  }

  /// Change the active status of multiple budgets
  ///
  /// [budgetIds] - List of budget IDs to update
  /// [isActive] - New active status for the budgets
  Future<BulkOperationResult> changeBudgetStatus({
    required List<String> budgetIds,
    required bool isActive,
  }) async {
    if (budgetIds.isEmpty) {
      return BulkOperationResult.success(
        processedCount: 0,
        warnings: ['No budgets provided for status change'],
      );
    }

    var processedCount = 0;
    var failedCount = 0;
    final errors = <String>[];
    final budgetsToUpdate = <Budget>[];

    try {
      // Fetch all budgets first
      for (final budgetId in budgetIds) {
        try {
          final budget = await budgetRepository.getBudgetById(budgetId);
          if (budget == null) {
            errors.add('Budget $budgetId not found');
            failedCount++;
            continue;
          }

          // Create updated budget with new status
          final updatedBudget = budget.copyWith(isActive: isActive);
          budgetsToUpdate.add(updatedBudget);
          processedCount++;
        } on Exception catch (e) {
          errors.add('Failed to process budget $budgetId: $e');
          failedCount++;
        }
      }

      // Perform batch update if we have budgets to update
      if (budgetsToUpdate.isNotEmpty) {
        await budgetRepository.batchUpdateBudgets(budgetsToUpdate);
      }

      // If all operations failed, return failure
      if (processedCount == 0 && failedCount > 0) {
        return BulkOperationResult.failure(
          errors: errors,
          failedCount: failedCount,
        );
      }

      return BulkOperationResult.success(
        processedCount: processedCount,
        failedCount: failedCount,
        errors: errors,
      );
    } on Exception catch (e) {
      return BulkOperationResult.failure(
        errors: ['Bulk status change failed: $e'],
        processedCount: processedCount,
        failedCount: failedCount,
      );
    }
  }

  /// Delete multiple budgets
  ///
  /// [budgetIds] - List of budget IDs to delete
  /// Performs soft deletion by setting isActive to false
  Future<BulkOperationResult> deleteBudgets({
    required List<String> budgetIds,
  }) async {
    if (budgetIds.isEmpty) {
      return BulkOperationResult.success(
        processedCount: 0,
        warnings: ['No budgets provided'],
      );
    }

    try {
      await budgetRepository.batchDeleteBudgets(budgetIds);

      return BulkOperationResult.success(processedCount: budgetIds.length);
    } on Exception catch (e) {
      return BulkOperationResult.failure(
        errors: ['Bulk deletion failed: $e'],
        failedCount: budgetIds.length,
      );
    }
  }
}

/// Result class for bulk operations
class BulkOperationResult {
  const BulkOperationResult._({
    required this.success,
    required this.processedCount,
    required this.failedCount,
    required this.errors,
    required this.warnings,
  });

  /// Create a successful result
  factory BulkOperationResult.success({
    required int processedCount,
    int failedCount = 0,
    List<String> errors = const [],
    List<String> warnings = const [],
  }) {
    return BulkOperationResult._(
      success: true,
      processedCount: processedCount,
      failedCount: failedCount,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Create a failure result
  factory BulkOperationResult.failure({
    required List<String> errors,
    int processedCount = 0,
    int failedCount = 0,
    List<String> warnings = const [],
  }) {
    return BulkOperationResult._(
      success: false,
      processedCount: processedCount,
      failedCount: failedCount,
      errors: errors,
      warnings: warnings,
    );
  }
  final bool success;
  final int processedCount;
  final int failedCount;
  final List<String> errors;
  final List<String> warnings;

  /// Get a human-readable summary of the operation
  String get summary {
    if (!success) {
      final errorMessage = errors.isNotEmpty ? errors.first : 'Unknown error';
      return 'Operation failed: $errorMessage';
    }

    if (processedCount == 0) {
      final warningMessage = warnings.isNotEmpty
          ? warnings.first
          : 'No items processed';
      return warningMessage;
    }

    final itemText = processedCount == 1 ? 'budget' : 'budgets';
    var message = 'Successfully processed $processedCount $itemText';

    if (failedCount > 0) {
      final failedText = failedCount == 1 ? 'budget' : 'budgets';
      message += ' ($failedCount $failedText failed)';
    }

    return message;
  }
}
