// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'budget_progress_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$budgetProgressServiceHash() =>
    r'96cc4a37f06819cf9792c8e9b31e4fe2f015649d';

/// Provider for BudgetProgressService
///
/// Copied from [budgetProgressService].
@ProviderFor(budgetProgressService)
final budgetProgressServiceProvider =
    AutoDisposeProvider<BudgetProgressService>.internal(
      budgetProgressService,
      name: r'budgetProgressServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$budgetProgressServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BudgetProgressServiceRef =
    AutoDisposeProviderRef<BudgetProgressService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
