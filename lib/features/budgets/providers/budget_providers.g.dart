// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'budget_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$budgetsHash() => r'c0e5a7a2fd75fc69e031686b94e97cece5098e37';

/// Provider for watching all budgets
///
/// Copied from [budgets].
@ProviderFor(budgets)
final budgetsProvider = AutoDisposeStreamProvider<List<Budget>>.internal(
  budgets,
  name: r'budgetsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$budgetsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BudgetsRef = AutoDisposeStreamProviderRef<List<Budget>>;
String _$budgetsByMonthHash() => r'672dab619546320bdc06d45fae61f3ab801848b4';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for watching budgets for a specific month
///
/// Copied from [budgetsByMonth].
@ProviderFor(budgetsByMonth)
const budgetsByMonthProvider = BudgetsByMonthFamily();

/// Provider for watching budgets for a specific month
///
/// Copied from [budgetsByMonth].
class BudgetsByMonthFamily extends Family<AsyncValue<List<Budget>>> {
  /// Provider for watching budgets for a specific month
  ///
  /// Copied from [budgetsByMonth].
  const BudgetsByMonthFamily();

  /// Provider for watching budgets for a specific month
  ///
  /// Copied from [budgetsByMonth].
  BudgetsByMonthProvider call(DateTime month) {
    return BudgetsByMonthProvider(month);
  }

  @override
  BudgetsByMonthProvider getProviderOverride(
    covariant BudgetsByMonthProvider provider,
  ) {
    return call(provider.month);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'budgetsByMonthProvider';
}

/// Provider for watching budgets for a specific month
///
/// Copied from [budgetsByMonth].
class BudgetsByMonthProvider extends AutoDisposeStreamProvider<List<Budget>> {
  /// Provider for watching budgets for a specific month
  ///
  /// Copied from [budgetsByMonth].
  BudgetsByMonthProvider(DateTime month)
    : this._internal(
        (ref) => budgetsByMonth(ref as BudgetsByMonthRef, month),
        from: budgetsByMonthProvider,
        name: r'budgetsByMonthProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$budgetsByMonthHash,
        dependencies: BudgetsByMonthFamily._dependencies,
        allTransitiveDependencies:
            BudgetsByMonthFamily._allTransitiveDependencies,
        month: month,
      );

  BudgetsByMonthProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.month,
  }) : super.internal();

  final DateTime month;

  @override
  Override overrideWith(
    Stream<List<Budget>> Function(BudgetsByMonthRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: BudgetsByMonthProvider._internal(
        (ref) => create(ref as BudgetsByMonthRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        month: month,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<List<Budget>> createElement() {
    return _BudgetsByMonthProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is BudgetsByMonthProvider && other.month == month;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, month.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin BudgetsByMonthRef on AutoDisposeStreamProviderRef<List<Budget>> {
  /// The parameter `month` of this provider.
  DateTime get month;
}

class _BudgetsByMonthProviderElement
    extends AutoDisposeStreamProviderElement<List<Budget>>
    with BudgetsByMonthRef {
  _BudgetsByMonthProviderElement(super.provider);

  @override
  DateTime get month => (origin as BudgetsByMonthProvider).month;
}

String _$templateBudgetsForFuturePeriodHash() =>
    r'37b3250d656248470ace45c0b98fd23e2d39244f';

/// Provider for getting template budgets for future periods
/// Returns budgets from the most recent available period as templates
///
/// Copied from [templateBudgetsForFuturePeriod].
@ProviderFor(templateBudgetsForFuturePeriod)
const templateBudgetsForFuturePeriodProvider =
    TemplateBudgetsForFuturePeriodFamily();

/// Provider for getting template budgets for future periods
/// Returns budgets from the most recent available period as templates
///
/// Copied from [templateBudgetsForFuturePeriod].
class TemplateBudgetsForFuturePeriodFamily
    extends Family<AsyncValue<List<Budget>>> {
  /// Provider for getting template budgets for future periods
  /// Returns budgets from the most recent available period as templates
  ///
  /// Copied from [templateBudgetsForFuturePeriod].
  const TemplateBudgetsForFuturePeriodFamily();

  /// Provider for getting template budgets for future periods
  /// Returns budgets from the most recent available period as templates
  ///
  /// Copied from [templateBudgetsForFuturePeriod].
  TemplateBudgetsForFuturePeriodProvider call(DateTime futurePeriod) {
    return TemplateBudgetsForFuturePeriodProvider(futurePeriod);
  }

  @override
  TemplateBudgetsForFuturePeriodProvider getProviderOverride(
    covariant TemplateBudgetsForFuturePeriodProvider provider,
  ) {
    return call(provider.futurePeriod);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'templateBudgetsForFuturePeriodProvider';
}

/// Provider for getting template budgets for future periods
/// Returns budgets from the most recent available period as templates
///
/// Copied from [templateBudgetsForFuturePeriod].
class TemplateBudgetsForFuturePeriodProvider
    extends AutoDisposeFutureProvider<List<Budget>> {
  /// Provider for getting template budgets for future periods
  /// Returns budgets from the most recent available period as templates
  ///
  /// Copied from [templateBudgetsForFuturePeriod].
  TemplateBudgetsForFuturePeriodProvider(DateTime futurePeriod)
    : this._internal(
        (ref) => templateBudgetsForFuturePeriod(
          ref as TemplateBudgetsForFuturePeriodRef,
          futurePeriod,
        ),
        from: templateBudgetsForFuturePeriodProvider,
        name: r'templateBudgetsForFuturePeriodProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$templateBudgetsForFuturePeriodHash,
        dependencies: TemplateBudgetsForFuturePeriodFamily._dependencies,
        allTransitiveDependencies:
            TemplateBudgetsForFuturePeriodFamily._allTransitiveDependencies,
        futurePeriod: futurePeriod,
      );

  TemplateBudgetsForFuturePeriodProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.futurePeriod,
  }) : super.internal();

  final DateTime futurePeriod;

  @override
  Override overrideWith(
    FutureOr<List<Budget>> Function(TemplateBudgetsForFuturePeriodRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TemplateBudgetsForFuturePeriodProvider._internal(
        (ref) => create(ref as TemplateBudgetsForFuturePeriodRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        futurePeriod: futurePeriod,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Budget>> createElement() {
    return _TemplateBudgetsForFuturePeriodProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TemplateBudgetsForFuturePeriodProvider &&
        other.futurePeriod == futurePeriod;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, futurePeriod.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TemplateBudgetsForFuturePeriodRef
    on AutoDisposeFutureProviderRef<List<Budget>> {
  /// The parameter `futurePeriod` of this provider.
  DateTime get futurePeriod;
}

class _TemplateBudgetsForFuturePeriodProviderElement
    extends AutoDisposeFutureProviderElement<List<Budget>>
    with TemplateBudgetsForFuturePeriodRef {
  _TemplateBudgetsForFuturePeriodProviderElement(super.provider);

  @override
  DateTime get futurePeriod =>
      (origin as TemplateBudgetsForFuturePeriodProvider).futurePeriod;
}

String _$totalBudgetByMonthAndTypeHash() =>
    r'cb74e21c9dd3c740325ca9a088bd6b62ffa47069';

/// Provider for getting total budgets for a specific month and type
///
/// Copied from [totalBudgetByMonthAndType].
@ProviderFor(totalBudgetByMonthAndType)
const totalBudgetByMonthAndTypeProvider = TotalBudgetByMonthAndTypeFamily();

/// Provider for getting total budgets for a specific month and type
///
/// Copied from [totalBudgetByMonthAndType].
class TotalBudgetByMonthAndTypeFamily extends Family<AsyncValue<Budget?>> {
  /// Provider for getting total budgets for a specific month and type
  ///
  /// Copied from [totalBudgetByMonthAndType].
  const TotalBudgetByMonthAndTypeFamily();

  /// Provider for getting total budgets for a specific month and type
  ///
  /// Copied from [totalBudgetByMonthAndType].
  TotalBudgetByMonthAndTypeProvider call(
    DateTime month,
    BudgetType budgetType,
  ) {
    return TotalBudgetByMonthAndTypeProvider(month, budgetType);
  }

  @override
  TotalBudgetByMonthAndTypeProvider getProviderOverride(
    covariant TotalBudgetByMonthAndTypeProvider provider,
  ) {
    return call(provider.month, provider.budgetType);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'totalBudgetByMonthAndTypeProvider';
}

/// Provider for getting total budgets for a specific month and type
///
/// Copied from [totalBudgetByMonthAndType].
class TotalBudgetByMonthAndTypeProvider
    extends AutoDisposeFutureProvider<Budget?> {
  /// Provider for getting total budgets for a specific month and type
  ///
  /// Copied from [totalBudgetByMonthAndType].
  TotalBudgetByMonthAndTypeProvider(DateTime month, BudgetType budgetType)
    : this._internal(
        (ref) => totalBudgetByMonthAndType(
          ref as TotalBudgetByMonthAndTypeRef,
          month,
          budgetType,
        ),
        from: totalBudgetByMonthAndTypeProvider,
        name: r'totalBudgetByMonthAndTypeProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$totalBudgetByMonthAndTypeHash,
        dependencies: TotalBudgetByMonthAndTypeFamily._dependencies,
        allTransitiveDependencies:
            TotalBudgetByMonthAndTypeFamily._allTransitiveDependencies,
        month: month,
        budgetType: budgetType,
      );

  TotalBudgetByMonthAndTypeProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.month,
    required this.budgetType,
  }) : super.internal();

  final DateTime month;
  final BudgetType budgetType;

  @override
  Override overrideWith(
    FutureOr<Budget?> Function(TotalBudgetByMonthAndTypeRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TotalBudgetByMonthAndTypeProvider._internal(
        (ref) => create(ref as TotalBudgetByMonthAndTypeRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        month: month,
        budgetType: budgetType,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Budget?> createElement() {
    return _TotalBudgetByMonthAndTypeProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TotalBudgetByMonthAndTypeProvider &&
        other.month == month &&
        other.budgetType == budgetType;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, month.hashCode);
    hash = _SystemHash.combine(hash, budgetType.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TotalBudgetByMonthAndTypeRef on AutoDisposeFutureProviderRef<Budget?> {
  /// The parameter `month` of this provider.
  DateTime get month;

  /// The parameter `budgetType` of this provider.
  BudgetType get budgetType;
}

class _TotalBudgetByMonthAndTypeProviderElement
    extends AutoDisposeFutureProviderElement<Budget?>
    with TotalBudgetByMonthAndTypeRef {
  _TotalBudgetByMonthAndTypeProviderElement(super.provider);

  @override
  DateTime get month => (origin as TotalBudgetByMonthAndTypeProvider).month;
  @override
  BudgetType get budgetType =>
      (origin as TotalBudgetByMonthAndTypeProvider).budgetType;
}

String _$totalBudgetsByMonthHash() =>
    r'83c5d1f90ecd3c452d3b1ae66ea2217f504cb3ec';

/// Provider for getting all total budgets for a specific month
///
/// Copied from [totalBudgetsByMonth].
@ProviderFor(totalBudgetsByMonth)
const totalBudgetsByMonthProvider = TotalBudgetsByMonthFamily();

/// Provider for getting all total budgets for a specific month
///
/// Copied from [totalBudgetsByMonth].
class TotalBudgetsByMonthFamily extends Family<AsyncValue<List<Budget>>> {
  /// Provider for getting all total budgets for a specific month
  ///
  /// Copied from [totalBudgetsByMonth].
  const TotalBudgetsByMonthFamily();

  /// Provider for getting all total budgets for a specific month
  ///
  /// Copied from [totalBudgetsByMonth].
  TotalBudgetsByMonthProvider call(DateTime month) {
    return TotalBudgetsByMonthProvider(month);
  }

  @override
  TotalBudgetsByMonthProvider getProviderOverride(
    covariant TotalBudgetsByMonthProvider provider,
  ) {
    return call(provider.month);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'totalBudgetsByMonthProvider';
}

/// Provider for getting all total budgets for a specific month
///
/// Copied from [totalBudgetsByMonth].
class TotalBudgetsByMonthProvider
    extends AutoDisposeFutureProvider<List<Budget>> {
  /// Provider for getting all total budgets for a specific month
  ///
  /// Copied from [totalBudgetsByMonth].
  TotalBudgetsByMonthProvider(DateTime month)
    : this._internal(
        (ref) => totalBudgetsByMonth(ref as TotalBudgetsByMonthRef, month),
        from: totalBudgetsByMonthProvider,
        name: r'totalBudgetsByMonthProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$totalBudgetsByMonthHash,
        dependencies: TotalBudgetsByMonthFamily._dependencies,
        allTransitiveDependencies:
            TotalBudgetsByMonthFamily._allTransitiveDependencies,
        month: month,
      );

  TotalBudgetsByMonthProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.month,
  }) : super.internal();

  final DateTime month;

  @override
  Override overrideWith(
    FutureOr<List<Budget>> Function(TotalBudgetsByMonthRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TotalBudgetsByMonthProvider._internal(
        (ref) => create(ref as TotalBudgetsByMonthRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        month: month,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Budget>> createElement() {
    return _TotalBudgetsByMonthProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TotalBudgetsByMonthProvider && other.month == month;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, month.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TotalBudgetsByMonthRef on AutoDisposeFutureProviderRef<List<Budget>> {
  /// The parameter `month` of this provider.
  DateTime get month;
}

class _TotalBudgetsByMonthProviderElement
    extends AutoDisposeFutureProviderElement<List<Budget>>
    with TotalBudgetsByMonthRef {
  _TotalBudgetsByMonthProviderElement(super.provider);

  @override
  DateTime get month => (origin as TotalBudgetsByMonthProvider).month;
}

String _$budgetHash() => r'cec468bef9665a03f14d48a77be32e7ac9872880';

/// Provider for getting a specific budget by ID
///
/// Copied from [budget].
@ProviderFor(budget)
const budgetProvider = BudgetFamily();

/// Provider for getting a specific budget by ID
///
/// Copied from [budget].
class BudgetFamily extends Family<AsyncValue<Budget?>> {
  /// Provider for getting a specific budget by ID
  ///
  /// Copied from [budget].
  const BudgetFamily();

  /// Provider for getting a specific budget by ID
  ///
  /// Copied from [budget].
  BudgetProvider call(String budgetId) {
    return BudgetProvider(budgetId);
  }

  @override
  BudgetProvider getProviderOverride(covariant BudgetProvider provider) {
    return call(provider.budgetId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'budgetProvider';
}

/// Provider for getting a specific budget by ID
///
/// Copied from [budget].
class BudgetProvider extends AutoDisposeFutureProvider<Budget?> {
  /// Provider for getting a specific budget by ID
  ///
  /// Copied from [budget].
  BudgetProvider(String budgetId)
    : this._internal(
        (ref) => budget(ref as BudgetRef, budgetId),
        from: budgetProvider,
        name: r'budgetProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$budgetHash,
        dependencies: BudgetFamily._dependencies,
        allTransitiveDependencies: BudgetFamily._allTransitiveDependencies,
        budgetId: budgetId,
      );

  BudgetProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.budgetId,
  }) : super.internal();

  final String budgetId;

  @override
  Override overrideWith(FutureOr<Budget?> Function(BudgetRef provider) create) {
    return ProviderOverride(
      origin: this,
      override: BudgetProvider._internal(
        (ref) => create(ref as BudgetRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        budgetId: budgetId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Budget?> createElement() {
    return _BudgetProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is BudgetProvider && other.budgetId == budgetId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, budgetId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin BudgetRef on AutoDisposeFutureProviderRef<Budget?> {
  /// The parameter `budgetId` of this provider.
  String get budgetId;
}

class _BudgetProviderElement extends AutoDisposeFutureProviderElement<Budget?>
    with BudgetRef {
  _BudgetProviderElement(super.provider);

  @override
  String get budgetId => (origin as BudgetProvider).budgetId;
}

String _$budgetProgressHash() => r'5ad6452f1f0e9ad948a07cde4e532a2202a0ae54';

/// Provider for calculating budget progress for a specific budget and month
///
/// Copied from [budgetProgress].
@ProviderFor(budgetProgress)
const budgetProgressProvider = BudgetProgressFamily();

/// Provider for calculating budget progress for a specific budget and month
///
/// Copied from [budgetProgress].
class BudgetProgressFamily extends Family<AsyncValue<BudgetProgress>> {
  /// Provider for calculating budget progress for a specific budget and month
  ///
  /// Copied from [budgetProgress].
  const BudgetProgressFamily();

  /// Provider for calculating budget progress for a specific budget and month
  ///
  /// Copied from [budgetProgress].
  BudgetProgressProvider call(String budgetId, DateTime month) {
    return BudgetProgressProvider(budgetId, month);
  }

  @override
  BudgetProgressProvider getProviderOverride(
    covariant BudgetProgressProvider provider,
  ) {
    return call(provider.budgetId, provider.month);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'budgetProgressProvider';
}

/// Provider for calculating budget progress for a specific budget and month
///
/// Copied from [budgetProgress].
class BudgetProgressProvider extends AutoDisposeFutureProvider<BudgetProgress> {
  /// Provider for calculating budget progress for a specific budget and month
  ///
  /// Copied from [budgetProgress].
  BudgetProgressProvider(String budgetId, DateTime month)
    : this._internal(
        (ref) => budgetProgress(ref as BudgetProgressRef, budgetId, month),
        from: budgetProgressProvider,
        name: r'budgetProgressProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$budgetProgressHash,
        dependencies: BudgetProgressFamily._dependencies,
        allTransitiveDependencies:
            BudgetProgressFamily._allTransitiveDependencies,
        budgetId: budgetId,
        month: month,
      );

  BudgetProgressProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.budgetId,
    required this.month,
  }) : super.internal();

  final String budgetId;
  final DateTime month;

  @override
  Override overrideWith(
    FutureOr<BudgetProgress> Function(BudgetProgressRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: BudgetProgressProvider._internal(
        (ref) => create(ref as BudgetProgressRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        budgetId: budgetId,
        month: month,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<BudgetProgress> createElement() {
    return _BudgetProgressProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is BudgetProgressProvider &&
        other.budgetId == budgetId &&
        other.month == month;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, budgetId.hashCode);
    hash = _SystemHash.combine(hash, month.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin BudgetProgressRef on AutoDisposeFutureProviderRef<BudgetProgress> {
  /// The parameter `budgetId` of this provider.
  String get budgetId;

  /// The parameter `month` of this provider.
  DateTime get month;
}

class _BudgetProgressProviderElement
    extends AutoDisposeFutureProviderElement<BudgetProgress>
    with BudgetProgressRef {
  _BudgetProgressProviderElement(super.provider);

  @override
  String get budgetId => (origin as BudgetProgressProvider).budgetId;
  @override
  DateTime get month => (origin as BudgetProgressProvider).month;
}

String _$spendingBreakdownHash() => r'58d7510619db3ec0d5490f1a764cf455cb35f1f9';

/// Provider for getting spending breakdown by category for a month
///
/// Copied from [spendingBreakdown].
@ProviderFor(spendingBreakdown)
const spendingBreakdownProvider = SpendingBreakdownFamily();

/// Provider for getting spending breakdown by category for a month
///
/// Copied from [spendingBreakdown].
class SpendingBreakdownFamily extends Family<AsyncValue<Map<String, double>>> {
  /// Provider for getting spending breakdown by category for a month
  ///
  /// Copied from [spendingBreakdown].
  const SpendingBreakdownFamily();

  /// Provider for getting spending breakdown by category for a month
  ///
  /// Copied from [spendingBreakdown].
  SpendingBreakdownProvider call(List<String> categoryIds, DateTime month) {
    return SpendingBreakdownProvider(categoryIds, month);
  }

  @override
  SpendingBreakdownProvider getProviderOverride(
    covariant SpendingBreakdownProvider provider,
  ) {
    return call(provider.categoryIds, provider.month);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'spendingBreakdownProvider';
}

/// Provider for getting spending breakdown by category for a month
///
/// Copied from [spendingBreakdown].
class SpendingBreakdownProvider
    extends AutoDisposeFutureProvider<Map<String, double>> {
  /// Provider for getting spending breakdown by category for a month
  ///
  /// Copied from [spendingBreakdown].
  SpendingBreakdownProvider(List<String> categoryIds, DateTime month)
    : this._internal(
        (ref) =>
            spendingBreakdown(ref as SpendingBreakdownRef, categoryIds, month),
        from: spendingBreakdownProvider,
        name: r'spendingBreakdownProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$spendingBreakdownHash,
        dependencies: SpendingBreakdownFamily._dependencies,
        allTransitiveDependencies:
            SpendingBreakdownFamily._allTransitiveDependencies,
        categoryIds: categoryIds,
        month: month,
      );

  SpendingBreakdownProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryIds,
    required this.month,
  }) : super.internal();

  final List<String> categoryIds;
  final DateTime month;

  @override
  Override overrideWith(
    FutureOr<Map<String, double>> Function(SpendingBreakdownRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SpendingBreakdownProvider._internal(
        (ref) => create(ref as SpendingBreakdownRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryIds: categoryIds,
        month: month,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, double>> createElement() {
    return _SpendingBreakdownProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SpendingBreakdownProvider &&
        other.categoryIds == categoryIds &&
        other.month == month;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryIds.hashCode);
    hash = _SystemHash.combine(hash, month.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SpendingBreakdownRef
    on AutoDisposeFutureProviderRef<Map<String, double>> {
  /// The parameter `categoryIds` of this provider.
  List<String> get categoryIds;

  /// The parameter `month` of this provider.
  DateTime get month;
}

class _SpendingBreakdownProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, double>>
    with SpendingBreakdownRef {
  _SpendingBreakdownProviderElement(super.provider);

  @override
  List<String> get categoryIds =>
      (origin as SpendingBreakdownProvider).categoryIds;
  @override
  DateTime get month => (origin as SpendingBreakdownProvider).month;
}

String _$totalSpendingHash() => r'2ae172820d5e29d9c6c8f76c2c460e4924855111';

/// Provider for getting total spending for a month
///
/// Copied from [totalSpending].
@ProviderFor(totalSpending)
const totalSpendingProvider = TotalSpendingFamily();

/// Provider for getting total spending for a month
///
/// Copied from [totalSpending].
class TotalSpendingFamily extends Family<AsyncValue<double>> {
  /// Provider for getting total spending for a month
  ///
  /// Copied from [totalSpending].
  const TotalSpendingFamily();

  /// Provider for getting total spending for a month
  ///
  /// Copied from [totalSpending].
  TotalSpendingProvider call(DateTime month) {
    return TotalSpendingProvider(month);
  }

  @override
  TotalSpendingProvider getProviderOverride(
    covariant TotalSpendingProvider provider,
  ) {
    return call(provider.month);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'totalSpendingProvider';
}

/// Provider for getting total spending for a month
///
/// Copied from [totalSpending].
class TotalSpendingProvider extends AutoDisposeFutureProvider<double> {
  /// Provider for getting total spending for a month
  ///
  /// Copied from [totalSpending].
  TotalSpendingProvider(DateTime month)
    : this._internal(
        (ref) => totalSpending(ref as TotalSpendingRef, month),
        from: totalSpendingProvider,
        name: r'totalSpendingProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$totalSpendingHash,
        dependencies: TotalSpendingFamily._dependencies,
        allTransitiveDependencies:
            TotalSpendingFamily._allTransitiveDependencies,
        month: month,
      );

  TotalSpendingProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.month,
  }) : super.internal();

  final DateTime month;

  @override
  Override overrideWith(
    FutureOr<double> Function(TotalSpendingRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TotalSpendingProvider._internal(
        (ref) => create(ref as TotalSpendingRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        month: month,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<double> createElement() {
    return _TotalSpendingProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TotalSpendingProvider && other.month == month;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, month.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TotalSpendingRef on AutoDisposeFutureProviderRef<double> {
  /// The parameter `month` of this provider.
  DateTime get month;
}

class _TotalSpendingProviderElement
    extends AutoDisposeFutureProviderElement<double>
    with TotalSpendingRef {
  _TotalSpendingProviderElement(super.provider);

  @override
  DateTime get month => (origin as TotalSpendingProvider).month;
}

String _$spendingTrendHash() => r'c63041025412d4bdf145fda531072633b88c74e8';

/// Provider for getting spending trend over multiple months
///
/// Copied from [spendingTrend].
@ProviderFor(spendingTrend)
const spendingTrendProvider = SpendingTrendFamily();

/// Provider for getting spending trend over multiple months
///
/// Copied from [spendingTrend].
class SpendingTrendFamily extends Family<AsyncValue<Map<DateTime, double>>> {
  /// Provider for getting spending trend over multiple months
  ///
  /// Copied from [spendingTrend].
  const SpendingTrendFamily();

  /// Provider for getting spending trend over multiple months
  ///
  /// Copied from [spendingTrend].
  SpendingTrendProvider call(String? categoryId, List<DateTime> months) {
    return SpendingTrendProvider(categoryId, months);
  }

  @override
  SpendingTrendProvider getProviderOverride(
    covariant SpendingTrendProvider provider,
  ) {
    return call(provider.categoryId, provider.months);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'spendingTrendProvider';
}

/// Provider for getting spending trend over multiple months
///
/// Copied from [spendingTrend].
class SpendingTrendProvider
    extends AutoDisposeFutureProvider<Map<DateTime, double>> {
  /// Provider for getting spending trend over multiple months
  ///
  /// Copied from [spendingTrend].
  SpendingTrendProvider(String? categoryId, List<DateTime> months)
    : this._internal(
        (ref) => spendingTrend(ref as SpendingTrendRef, categoryId, months),
        from: spendingTrendProvider,
        name: r'spendingTrendProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$spendingTrendHash,
        dependencies: SpendingTrendFamily._dependencies,
        allTransitiveDependencies:
            SpendingTrendFamily._allTransitiveDependencies,
        categoryId: categoryId,
        months: months,
      );

  SpendingTrendProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
    required this.months,
  }) : super.internal();

  final String? categoryId;
  final List<DateTime> months;

  @override
  Override overrideWith(
    FutureOr<Map<DateTime, double>> Function(SpendingTrendRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SpendingTrendProvider._internal(
        (ref) => create(ref as SpendingTrendRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
        months: months,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<DateTime, double>> createElement() {
    return _SpendingTrendProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SpendingTrendProvider &&
        other.categoryId == categoryId &&
        other.months == months;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);
    hash = _SystemHash.combine(hash, months.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SpendingTrendRef on AutoDisposeFutureProviderRef<Map<DateTime, double>> {
  /// The parameter `categoryId` of this provider.
  String? get categoryId;

  /// The parameter `months` of this provider.
  List<DateTime> get months;
}

class _SpendingTrendProviderElement
    extends AutoDisposeFutureProviderElement<Map<DateTime, double>>
    with SpendingTrendRef {
  _SpendingTrendProviderElement(super.provider);

  @override
  String? get categoryId => (origin as SpendingTrendProvider).categoryId;
  @override
  List<DateTime> get months => (origin as SpendingTrendProvider).months;
}

String _$categoryBudgetInfoHash() =>
    r'e478e6da6c924bafdce5abb1d2ddcdf2893d0b93';

/// Provider for getting category budget information for the selected time period
/// Implements proper period-based budget management with fallback logic:
/// - Shows budgets belonging to the selected period
/// - If no budget exists for the selected period, shows the latest budget from a previous period
/// - Always shows all categories regardless of period
///
/// Copied from [categoryBudgetInfo].
@ProviderFor(categoryBudgetInfo)
const categoryBudgetInfoProvider = CategoryBudgetInfoFamily();

/// Provider for getting category budget information for the selected time period
/// Implements proper period-based budget management with fallback logic:
/// - Shows budgets belonging to the selected period
/// - If no budget exists for the selected period, shows the latest budget from a previous period
/// - Always shows all categories regardless of period
///
/// Copied from [categoryBudgetInfo].
class CategoryBudgetInfoFamily
    extends Family<AsyncValue<List<CategoryBudgetInfo>>> {
  /// Provider for getting category budget information for the selected time period
  /// Implements proper period-based budget management with fallback logic:
  /// - Shows budgets belonging to the selected period
  /// - If no budget exists for the selected period, shows the latest budget from a previous period
  /// - Always shows all categories regardless of period
  ///
  /// Copied from [categoryBudgetInfo].
  const CategoryBudgetInfoFamily();

  /// Provider for getting category budget information for the selected time period
  /// Implements proper period-based budget management with fallback logic:
  /// - Shows budgets belonging to the selected period
  /// - If no budget exists for the selected period, shows the latest budget from a previous period
  /// - Always shows all categories regardless of period
  ///
  /// Copied from [categoryBudgetInfo].
  CategoryBudgetInfoProvider call(CategoryType categoryType) {
    return CategoryBudgetInfoProvider(categoryType);
  }

  @override
  CategoryBudgetInfoProvider getProviderOverride(
    covariant CategoryBudgetInfoProvider provider,
  ) {
    return call(provider.categoryType);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categoryBudgetInfoProvider';
}

/// Provider for getting category budget information for the selected time period
/// Implements proper period-based budget management with fallback logic:
/// - Shows budgets belonging to the selected period
/// - If no budget exists for the selected period, shows the latest budget from a previous period
/// - Always shows all categories regardless of period
///
/// Copied from [categoryBudgetInfo].
class CategoryBudgetInfoProvider
    extends AutoDisposeFutureProvider<List<CategoryBudgetInfo>> {
  /// Provider for getting category budget information for the selected time period
  /// Implements proper period-based budget management with fallback logic:
  /// - Shows budgets belonging to the selected period
  /// - If no budget exists for the selected period, shows the latest budget from a previous period
  /// - Always shows all categories regardless of period
  ///
  /// Copied from [categoryBudgetInfo].
  CategoryBudgetInfoProvider(CategoryType categoryType)
    : this._internal(
        (ref) => categoryBudgetInfo(ref as CategoryBudgetInfoRef, categoryType),
        from: categoryBudgetInfoProvider,
        name: r'categoryBudgetInfoProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$categoryBudgetInfoHash,
        dependencies: CategoryBudgetInfoFamily._dependencies,
        allTransitiveDependencies:
            CategoryBudgetInfoFamily._allTransitiveDependencies,
        categoryType: categoryType,
      );

  CategoryBudgetInfoProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryType,
  }) : super.internal();

  final CategoryType categoryType;

  @override
  Override overrideWith(
    FutureOr<List<CategoryBudgetInfo>> Function(CategoryBudgetInfoRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CategoryBudgetInfoProvider._internal(
        (ref) => create(ref as CategoryBudgetInfoRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryType: categoryType,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<CategoryBudgetInfo>> createElement() {
    return _CategoryBudgetInfoProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategoryBudgetInfoProvider &&
        other.categoryType == categoryType;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryType.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategoryBudgetInfoRef
    on AutoDisposeFutureProviderRef<List<CategoryBudgetInfo>> {
  /// The parameter `categoryType` of this provider.
  CategoryType get categoryType;
}

class _CategoryBudgetInfoProviderElement
    extends AutoDisposeFutureProviderElement<List<CategoryBudgetInfo>>
    with CategoryBudgetInfoRef {
  _CategoryBudgetInfoProviderElement(super.provider);

  @override
  CategoryType get categoryType =>
      (origin as CategoryBudgetInfoProvider).categoryType;
}

String _$categoryTypeBudgetSummaryHash() =>
    r'4b44716aa8fd204c98f93747ac5dfeef6b0274db';

/// Provider for getting category type budget summary
///
/// Copied from [categoryTypeBudgetSummary].
@ProviderFor(categoryTypeBudgetSummary)
const categoryTypeBudgetSummaryProvider = CategoryTypeBudgetSummaryFamily();

/// Provider for getting category type budget summary
///
/// Copied from [categoryTypeBudgetSummary].
class CategoryTypeBudgetSummaryFamily
    extends Family<AsyncValue<CategoryTypeBudgetSummary>> {
  /// Provider for getting category type budget summary
  ///
  /// Copied from [categoryTypeBudgetSummary].
  const CategoryTypeBudgetSummaryFamily();

  /// Provider for getting category type budget summary
  ///
  /// Copied from [categoryTypeBudgetSummary].
  CategoryTypeBudgetSummaryProvider call(CategoryType categoryType) {
    return CategoryTypeBudgetSummaryProvider(categoryType);
  }

  @override
  CategoryTypeBudgetSummaryProvider getProviderOverride(
    covariant CategoryTypeBudgetSummaryProvider provider,
  ) {
    return call(provider.categoryType);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categoryTypeBudgetSummaryProvider';
}

/// Provider for getting category type budget summary
///
/// Copied from [categoryTypeBudgetSummary].
class CategoryTypeBudgetSummaryProvider
    extends AutoDisposeFutureProvider<CategoryTypeBudgetSummary> {
  /// Provider for getting category type budget summary
  ///
  /// Copied from [categoryTypeBudgetSummary].
  CategoryTypeBudgetSummaryProvider(CategoryType categoryType)
    : this._internal(
        (ref) => categoryTypeBudgetSummary(
          ref as CategoryTypeBudgetSummaryRef,
          categoryType,
        ),
        from: categoryTypeBudgetSummaryProvider,
        name: r'categoryTypeBudgetSummaryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$categoryTypeBudgetSummaryHash,
        dependencies: CategoryTypeBudgetSummaryFamily._dependencies,
        allTransitiveDependencies:
            CategoryTypeBudgetSummaryFamily._allTransitiveDependencies,
        categoryType: categoryType,
      );

  CategoryTypeBudgetSummaryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryType,
  }) : super.internal();

  final CategoryType categoryType;

  @override
  Override overrideWith(
    FutureOr<CategoryTypeBudgetSummary> Function(
      CategoryTypeBudgetSummaryRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CategoryTypeBudgetSummaryProvider._internal(
        (ref) => create(ref as CategoryTypeBudgetSummaryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryType: categoryType,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<CategoryTypeBudgetSummary> createElement() {
    return _CategoryTypeBudgetSummaryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategoryTypeBudgetSummaryProvider &&
        other.categoryType == categoryType;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryType.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategoryTypeBudgetSummaryRef
    on AutoDisposeFutureProviderRef<CategoryTypeBudgetSummary> {
  /// The parameter `categoryType` of this provider.
  CategoryType get categoryType;
}

class _CategoryTypeBudgetSummaryProviderElement
    extends AutoDisposeFutureProviderElement<CategoryTypeBudgetSummary>
    with CategoryTypeBudgetSummaryRef {
  _CategoryTypeBudgetSummaryProviderElement(super.provider);

  @override
  CategoryType get categoryType =>
      (origin as CategoryTypeBudgetSummaryProvider).categoryType;
}

String _$budgetCopyServiceHash() => r'3a31f79849a485c2c31389d28829ff5737aa2311';

/// Provider for BudgetCopyService
///
/// Copied from [budgetCopyService].
@ProviderFor(budgetCopyService)
final budgetCopyServiceProvider =
    AutoDisposeProvider<BudgetCopyService>.internal(
      budgetCopyService,
      name: r'budgetCopyServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$budgetCopyServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BudgetCopyServiceRef = AutoDisposeProviderRef<BudgetCopyService>;
String _$currentTimePeriodHash() => r'47207840785f0e4f64135c17b29c8ebdfecd3598';

/// Provider for current time period as DateTime
///
/// Copied from [currentTimePeriod].
@ProviderFor(currentTimePeriod)
final currentTimePeriodProvider = AutoDisposeProvider<DateTime>.internal(
  currentTimePeriod,
  name: r'currentTimePeriodProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentTimePeriodHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentTimePeriodRef = AutoDisposeProviderRef<DateTime>;
String _$currentBudgetPeriodHash() =>
    r'016b17cfeb3dd2bdd9dd7c6269a0c7b9289b7dd9';

/// Provider for current budget period type
///
/// Copied from [currentBudgetPeriod].
@ProviderFor(currentBudgetPeriod)
final currentBudgetPeriodProvider = AutoDisposeProvider<BudgetPeriod>.internal(
  currentBudgetPeriod,
  name: r'currentBudgetPeriodProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentBudgetPeriodHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentBudgetPeriodRef = AutoDisposeProviderRef<BudgetPeriod>;
String _$bulkBudgetServiceHash() => r'8af101044d23aa01408197248989096a548219f5';

/// Provider for BulkBudgetService
///
/// Copied from [bulkBudgetService].
@ProviderFor(bulkBudgetService)
final bulkBudgetServiceProvider =
    AutoDisposeProvider<BulkBudgetService>.internal(
      bulkBudgetService,
      name: r'bulkBudgetServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$bulkBudgetServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BulkBudgetServiceRef = AutoDisposeProviderRef<BulkBudgetService>;
String _$budgetTransactionServiceHash() =>
    r'911cba8638e4d9c6079930254b7182489d47d9a6';

/// Provider for BudgetTransactionService
///
/// Copied from [budgetTransactionService].
@ProviderFor(budgetTransactionService)
final budgetTransactionServiceProvider =
    AutoDisposeProvider<BudgetTransactionService>.internal(
      budgetTransactionService,
      name: r'budgetTransactionServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$budgetTransactionServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BudgetTransactionServiceRef =
    AutoDisposeProviderRef<BudgetTransactionService>;
String _$budgetEditHash() => r'6a520796afec11a232dae5cf3cd78827f03d6c4e';

abstract class _$BudgetEdit extends BuildlessAutoDisposeNotifier<Budget?> {
  late final String budgetId;

  Budget? build(String budgetId);
}

/// Provider for budget editing state management
///
/// Copied from [BudgetEdit].
@ProviderFor(BudgetEdit)
const budgetEditProvider = BudgetEditFamily();

/// Provider for budget editing state management
///
/// Copied from [BudgetEdit].
class BudgetEditFamily extends Family<Budget?> {
  /// Provider for budget editing state management
  ///
  /// Copied from [BudgetEdit].
  const BudgetEditFamily();

  /// Provider for budget editing state management
  ///
  /// Copied from [BudgetEdit].
  BudgetEditProvider call(String budgetId) {
    return BudgetEditProvider(budgetId);
  }

  @override
  BudgetEditProvider getProviderOverride(
    covariant BudgetEditProvider provider,
  ) {
    return call(provider.budgetId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'budgetEditProvider';
}

/// Provider for budget editing state management
///
/// Copied from [BudgetEdit].
class BudgetEditProvider
    extends AutoDisposeNotifierProviderImpl<BudgetEdit, Budget?> {
  /// Provider for budget editing state management
  ///
  /// Copied from [BudgetEdit].
  BudgetEditProvider(String budgetId)
    : this._internal(
        () => BudgetEdit()..budgetId = budgetId,
        from: budgetEditProvider,
        name: r'budgetEditProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$budgetEditHash,
        dependencies: BudgetEditFamily._dependencies,
        allTransitiveDependencies: BudgetEditFamily._allTransitiveDependencies,
        budgetId: budgetId,
      );

  BudgetEditProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.budgetId,
  }) : super.internal();

  final String budgetId;

  @override
  Budget? runNotifierBuild(covariant BudgetEdit notifier) {
    return notifier.build(budgetId);
  }

  @override
  Override overrideWith(BudgetEdit Function() create) {
    return ProviderOverride(
      origin: this,
      override: BudgetEditProvider._internal(
        () => create()..budgetId = budgetId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        budgetId: budgetId,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<BudgetEdit, Budget?> createElement() {
    return _BudgetEditProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is BudgetEditProvider && other.budgetId == budgetId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, budgetId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin BudgetEditRef on AutoDisposeNotifierProviderRef<Budget?> {
  /// The parameter `budgetId` of this provider.
  String get budgetId;
}

class _BudgetEditProviderElement
    extends AutoDisposeNotifierProviderElement<BudgetEdit, Budget?>
    with BudgetEditRef {
  _BudgetEditProviderElement(super.provider);

  @override
  String get budgetId => (origin as BudgetEditProvider).budgetId;
}

String _$budgetsControllerHash() => r'9c40672e4a0806f4150d662c343a4d99ae572ed9';

/// Provider for budget controller with CRUD operations
///
/// Copied from [BudgetsController].
@ProviderFor(BudgetsController)
final budgetsControllerProvider =
    AutoDisposeNotifierProvider<BudgetsController, AsyncValue<void>>.internal(
      BudgetsController.new,
      name: r'budgetsControllerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$budgetsControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$BudgetsController = AutoDisposeNotifier<AsyncValue<void>>;
String _$budgetCopyControllerHash() =>
    r'c72950179002774a77a862784a1027379c7d5360';

/// Provider for budget copy controller with copy operations
///
/// Copied from [BudgetCopyController].
@ProviderFor(BudgetCopyController)
final budgetCopyControllerProvider =
    AutoDisposeNotifierProvider<
      BudgetCopyController,
      AsyncValue<BudgetCopyResult?>
    >.internal(
      BudgetCopyController.new,
      name: r'budgetCopyControllerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$budgetCopyControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$BudgetCopyController =
    AutoDisposeNotifier<AsyncValue<BudgetCopyResult?>>;
String _$bulkBudgetControllerHash() =>
    r'cc20c4e689b4be441b103ad78618a6224fe3293a';

/// Controller for bulk budget operations
///
/// Copied from [BulkBudgetController].
@ProviderFor(BulkBudgetController)
final bulkBudgetControllerProvider =
    AutoDisposeNotifierProvider<
      BulkBudgetController,
      AsyncValue<BulkOperationResult?>
    >.internal(
      BulkBudgetController.new,
      name: r'bulkBudgetControllerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$bulkBudgetControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$BulkBudgetController =
    AutoDisposeNotifier<AsyncValue<BulkOperationResult?>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
