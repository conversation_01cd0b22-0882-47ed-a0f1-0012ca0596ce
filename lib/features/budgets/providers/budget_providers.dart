import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/models/budget_progress.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/budgets/data/models/category_budget_info.dart';
import 'package:budapp/features/budgets/services/budget_copy_service.dart';
import 'package:budapp/features/budgets/services/budget_progress_service.dart';
import 'package:budapp/features/budgets/services/budget_transaction_service.dart';
import 'package:budapp/features/budgets/services/bulk_budget_service.dart';
import 'package:budapp/features/categories/providers/category_providers.dart'
    show expenseCategoriesProvider, incomeCategoriesProvider;
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/providers/providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'budget_providers.g.dart';

/// Provider for watching all budgets
@riverpod
Stream<List<Budget>> budgets(Ref ref) {
  final repository = ref.watch(budgetRepositoryProvider);
  return repository.watchBudgets();
}

/// Provider for watching budgets for a specific month
@riverpod
Stream<List<Budget>> budgetsByMonth(Ref ref, DateTime month) {
  final repository = ref.watch(budgetRepositoryProvider);
  return repository.watchBudgetsByMonth(month);
}

/// Provider for getting template budgets for future periods
/// Returns budgets from the most recent available period as templates
@riverpod
Future<List<Budget>> templateBudgetsForFuturePeriod(
  Ref ref,
  DateTime futurePeriod,
) async {
  final repository = ref.watch(budgetRepositoryProvider);

  // Find the most recent period with budgets
  final now = DateTime.now();
  var searchPeriod = DateTime(now.year, now.month, 1);

  // Search backwards for up to 12 months to find budgets
  for (var i = 0; i < 12; i++) {
    final budgets = await repository.getBudgetsByPeriod(
      searchPeriod,
      BudgetPeriod.monthly,
    );

    if (budgets.isNotEmpty) {
      // Create template budgets for the future period
      return budgets
          .map(
            (budget) => budget.copyWith(
              id: '', // Template budgets don't have IDs until saved
              currentAmountCents: 0, // Reset current amount for new period
              createdAt: futurePeriod, // Use future period date
              updatedAt: futurePeriod,
            ),
          )
          .toList();
    }

    // Move to previous month
    if (searchPeriod.month == 1) {
      searchPeriod = DateTime(searchPeriod.year - 1, 12, 1);
    } else {
      searchPeriod = DateTime(searchPeriod.year, searchPeriod.month - 1, 1);
    }
  }

  // If no budgets found in the last 12 months, return empty list
  return [];
}

/// Provider for getting total budgets for a specific month and type
@riverpod
Future<Budget?> totalBudgetByMonthAndType(
  Ref ref,
  DateTime month,
  BudgetType budgetType,
) async {
  final budgets = await ref.watch(budgetsByMonthProvider(month).future);

  // Find total budget (categoryId == null) for the specified type
  // Use where().firstOrNull to avoid StateError when no element is found
  final totalBudgets = budgets.where(
    (budget) => budget.categoryId == null && budget.type == budgetType,
  );

  return totalBudgets.isNotEmpty ? totalBudgets.first : null;
}

/// Provider for getting all total budgets for a specific month
@riverpod
Future<List<Budget>> totalBudgetsByMonth(Ref ref, DateTime month) async {
  final budgets = await ref.watch(budgetsByMonthProvider(month).future);

  // Filter for total budgets (categoryId == null)
  return budgets.where((budget) => budget.categoryId == null).toList();
}

/// Provider for getting a specific budget by ID
@riverpod
Future<Budget?> budget(Ref ref, String budgetId) async {
  final repository = ref.watch(budgetRepositoryProvider);
  return repository.getBudgetById(budgetId);
}

/// Provider for calculating budget progress for a specific budget and month
@riverpod
Future<BudgetProgress> budgetProgress(
  Ref ref,
  String budgetId,
  DateTime month,
) async {
  final budgetRepository = ref.watch(budgetRepositoryProvider);
  final progressService = ref.watch(budgetProgressServiceProvider);

  // Get the budget
  final budget = await budgetRepository.getBudgetById(budgetId);
  if (budget == null) {
    throw Exception('Budget not found: $budgetId');
  }

  // Calculate progress
  return progressService.calculateBudgetProgress(budget, month);
}

/// Provider for getting spending breakdown by category for a month
@riverpod
Future<Map<String, double>> spendingBreakdown(
  Ref ref,
  List<String> categoryIds,
  DateTime month,
) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) throw Exception('User not authenticated');

  final progressService = ref.watch(budgetProgressServiceProvider);
  return progressService.getSpendingBreakdown(user.uid, categoryIds, month);
}

/// Provider for getting total spending for a month
@riverpod
Future<double> totalSpending(Ref ref, DateTime month) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) throw Exception('User not authenticated');

  final progressService = ref.watch(budgetProgressServiceProvider);
  return progressService.getTotalSpending(user.uid, month);
}

/// Provider for getting spending trend over multiple months
@riverpod
Future<Map<DateTime, double>> spendingTrend(
  Ref ref,
  String? categoryId,
  List<DateTime> months,
) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) throw Exception('User not authenticated');

  final progressService = ref.watch(budgetProgressServiceProvider);
  return progressService.getSpendingTrend(user.uid, categoryId, months);
}

/// Provider for budget editing state management
@riverpod
class BudgetEdit extends _$BudgetEdit {
  @override
  Budget? build(String budgetId) {
    // Watch the budget and return it as initial state
    final budgetAsync = ref.watch(budgetProvider(budgetId));
    return budgetAsync.when(
      data: (budget) => budget,
      loading: () => null,
      error: (_, _) => null,
    );
  }

  /// Get the current budget being edited
  Budget? get budget => state;

  /// Update the budget being edited
  set budget(Budget? budget) {
    state = budget;
  }

  void updateAmount(int amountCents) {
    if (state != null) {
      state = state!.copyWith(plannedAmountCents: amountCents);
    }
  }

  void updateType(BudgetType type) {
    if (state != null) {
      state = state!.copyWith(type: type);
    }
  }

  void updatePeriod(BudgetPeriod period) {
    if (state != null) {
      state = state!.copyWith(period: period);
    }
  }

  void updateCategory(String? categoryId) {
    if (state != null) {
      state = state!.copyWith(categoryId: categoryId);
    }
  }

  void updateParentBudget(String? parentBudgetId) {
    if (state != null) {
      state = state!.copyWith(parentBudgetId: parentBudgetId);
    }
  }

  // Removed updateCurrency method - currency is now global preference

  // Note: Budget model doesn't have description field, only name

  /// Save the budget changes
  Future<void> saveBudget() async {
    if (state != null) {
      final repository = ref.read(budgetRepositoryProvider);
      await repository.updateBudget(state!);
    }
  }

  /// Delete the budget
  Future<void> deleteBudget() async {
    if (state != null) {
      final repository = ref.read(budgetRepositoryProvider);
      await repository.deleteBudget(state!.id);
    }
  }
}

/// Provider for budget controller with CRUD operations
@riverpod
class BudgetsController extends _$BudgetsController {
  @override
  AsyncValue<void> build() {
    return const AsyncValue.data(null);
  }

  /// Update an existing budget
  Future<void> updateBudget(Budget budget) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final repository = ref.read(budgetRepositoryProvider);
      await repository.updateBudget(budget);
    });
  }

  /// Delete a budget
  Future<void> deleteBudget(String budgetId) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final repository = ref.read(budgetRepositoryProvider);
      await repository.deleteBudget(budgetId);
    });
  }
}

/// Provider for getting category budget information for the selected time period
/// Implements proper period-based budget management with fallback logic:
/// - Shows budgets belonging to the selected period
/// - If no budget exists for the selected period, shows the latest budget from a previous period
/// - Always shows all categories regardless of period
@riverpod
Future<List<CategoryBudgetInfo>> categoryBudgetInfo(
  Ref ref,
  CategoryType categoryType,
) async {
  // Watch ALL dependencies at the very beginning before any async operations
  final selectedPeriod = ref.watch(timePeriodNotifierProvider);
  final budgetRepository = ref.watch(budgetRepositoryProvider);
  final budgetProgressService = ref.watch(budgetProgressServiceProvider);
  final authService = ref.watch(authServiceProvider);

  final selectedMonth = DateTime(
    selectedPeriod.year,
    selectedPeriod.month ?? 1,
  );

  // Watch BOTH category providers (static dependencies)
  // This follows Riverpod best practice of watching all dependencies upfront
  final incomeCategories = await ref.watch(incomeCategoriesProvider.future);
  final expenseCategories = await ref.watch(expenseCategoriesProvider.future);
  final budgetsResult = await ref.watch(
    budgetsByMonthProvider(selectedMonth).future,
  );

  // THEN choose which categories to use based on the parameter
  final categoriesResult = categoryType == CategoryType.income
      ? incomeCategories
      : expenseCategories;

  // Create a map of budgets by categoryId for the selected period
  final budgetsByCategory = <String, Budget>{};
  for (final budget in budgetsResult) {
    if (budget.categoryId != null && budget.type.name == categoryType.name) {
      budgetsByCategory[budget.categoryId!] = budget;
    }
  }

  // Create CategoryBudgetInfo for each category
  final categoryBudgetList = <CategoryBudgetInfo>[];
  final budgetType = categoryType == CategoryType.income
      ? BudgetType.income
      : BudgetType.expense;

  for (final category in categoriesResult) {
    var budget = budgetsByCategory[category.id];
    var isFallbackBudget = false;

    // If no budget exists for this period, try to find the latest from previous period
    if (budget == null) {
      budget = await budgetRepository.findLatestBudgetFromPreviousPeriod(
        category.id,
        budgetType,
        selectedMonth,
        BudgetPeriod.monthly,
      );
      isFallbackBudget = budget != null;
    }

    BudgetProgress? progress;
    var actualSpent = 0.0;

    if (budget != null) {
      // Calculate progress for this budget
      progress = await budgetProgressService.calculateBudgetProgress(
        budget,
        selectedMonth,
      );
      actualSpent = progress.spent;
    } else {
      // Get actual spending for this category even without budget
      final user = authService.currentUser;
      if (user != null) {
        final spending = await budgetProgressService.getSpendingBreakdown(
          user.uid,
          [category.id],
          selectedMonth,
        );
        actualSpent = spending[category.id] ?? 0.0;
      }
    }

    categoryBudgetList.add(
      CategoryBudgetInfo(
        category: category,
        budget: budget,
        progress: progress,
        actualSpent: actualSpent,
        isFallbackBudget: isFallbackBudget,
      ),
    );
  }

  // Sort by category name
  categoryBudgetList.sort((a, b) => a.category.name.compareTo(b.category.name));

  return categoryBudgetList;
}

/// Provider for getting category type budget summary
@riverpod
Future<CategoryTypeBudgetSummary> categoryTypeBudgetSummary(
  Ref ref,
  CategoryType categoryType,
) async {
  final categoryBudgetList = await ref.watch(
    categoryBudgetInfoProvider(categoryType).future,
  );

  double totalBudgetAmount = 0;
  double totalActualAmount = 0;
  var categoriesWithBudgets = 0;
  const currency = 'USD';

  for (final item in categoryBudgetList) {
    totalActualAmount += item.actualSpent;
    if (item.hasBudget) {
      totalBudgetAmount += item.budgetAmount;
      categoriesWithBudgets++;
      // Removed currency from budget - now using global currency preference
    }
  }

  return CategoryTypeBudgetSummary(
    categoryType: categoryType,
    totalBudgetAmount: totalBudgetAmount,
    totalActualAmount: totalActualAmount,
    currency: currency,
    categoriesWithBudgets: categoriesWithBudgets,
    totalCategories: categoryBudgetList.length,
  );
}

/// Provider for BudgetCopyService
@riverpod
BudgetCopyService budgetCopyService(Ref ref) {
  final budgetRepository = ref.watch(budgetRepositoryProvider);
  final categoryRepository = ref.watch(categoryRepositoryProvider);
  return BudgetCopyService(
    budgetRepository: budgetRepository,
    categoryRepository: categoryRepository,
  );
}

/// Provider for current time period as DateTime
@riverpod
DateTime currentTimePeriod(Ref ref) {
  final selectedPeriod = ref.watch(timePeriodNotifierProvider);
  return DateTime(selectedPeriod.year, selectedPeriod.month ?? 1);
}

/// Provider for current budget period type
@riverpod
BudgetPeriod currentBudgetPeriod(Ref ref) {
  // Watch the time period to ensure this provider updates when period changes
  ref.watch(timePeriodNotifierProvider);
  // For now, we only support monthly periods in the time period selector
  // This can be extended later to support yearly periods
  return BudgetPeriod.monthly;
}

/// Provider for budget copy controller with copy operations
@riverpod
class BudgetCopyController extends _$BudgetCopyController {
  @override
  AsyncValue<BudgetCopyResult?> build() {
    return const AsyncValue.data(null);
  }

  /// Copy budgets from a previous period
  Future<void> copyBudgetsFromPreviousPeriod({
    required DateTime sourcePeriod,
    required DateTime targetPeriod,
    required BudgetPeriod periodType,
  }) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final copyService = ref.read(budgetCopyServiceProvider);
      final result = await copyService.copyBudgetsFromPreviousPeriod(
        sourcePeriod: sourcePeriod,
        targetPeriod: targetPeriod,
        periodType: periodType,
      );
      return result;
    });
  }

  /// Get available source periods for copying
  Future<List<DateTime>> getAvailableSourcePeriods({
    required DateTime currentPeriod,
    required BudgetPeriod periodType,
  }) async {
    final copyService = ref.read(budgetCopyServiceProvider);
    return copyService.getAvailableSourcePeriods(
      currentPeriod: currentPeriod,
      periodType: periodType,
    );
  }

  /// Format period for display
  String formatPeriod(DateTime period, BudgetPeriod periodType) {
    final copyService = ref.read(budgetCopyServiceProvider);
    return copyService.formatPeriod(period, periodType);
  }

  /// Clear the current result
  void clearResult() {
    state = const AsyncValue.data(null);
  }
}

/// Provider for BulkBudgetService
@riverpod
BulkBudgetService bulkBudgetService(Ref ref) {
  final budgetRepository = ref.watch(budgetRepositoryProvider);
  return BulkBudgetService(budgetRepository: budgetRepository);
}

/// Controller for bulk budget operations
@riverpod
class BulkBudgetController extends _$BulkBudgetController {
  @override
  AsyncValue<BulkOperationResult?> build() {
    return const AsyncValue.data(null);
  }

  /// Adjust budgets by percentage
  Future<void> adjustBudgetsByPercentage({
    required List<String> budgetIds,
    required double percentageChange,
  }) async {
    state = const AsyncValue.loading();

    try {
      final service = ref.read(bulkBudgetServiceProvider);
      final result = await service.adjustBudgetsByPercentage(
        budgetIds: budgetIds,
        percentageChange: percentageChange,
      );
      state = AsyncValue.data(result);
    } on Exception catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Change budget status
  Future<void> changeBudgetStatus({
    required List<String> budgetIds,
    required bool isActive,
  }) async {
    state = const AsyncValue.loading();

    try {
      final service = ref.read(bulkBudgetServiceProvider);
      final result = await service.changeBudgetStatus(
        budgetIds: budgetIds,
        isActive: isActive,
      );
      state = AsyncValue.data(result);
    } on Exception catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Delete budgets
  Future<void> deleteBudgets({required List<String> budgetIds}) async {
    state = const AsyncValue.loading();

    try {
      final service = ref.read(bulkBudgetServiceProvider);
      final result = await service.deleteBudgets(budgetIds: budgetIds);
      state = AsyncValue.data(result);
    } on Exception catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Clear the current result
  void clearResult() {
    state = const AsyncValue.data(null);
  }
}

/// Provider for BudgetTransactionService
@riverpod
BudgetTransactionService budgetTransactionService(Ref ref) {
  final budgetRepository = ref.watch(budgetRepositoryProvider);
  final firestoreService = ref.watch(firestoreServiceProvider);
  return BudgetTransactionService(budgetRepository, firestoreService);
}
