import 'package:budapp/l10n/app_localizations.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Service for handling transaction-specific errors and providing user-friendly messages
// ignore: avoid_classes_with_only_static_members
class TransactionErrorService {
  /// Convert a generic error into a user-friendly message for transaction operations
  static String getErrorMessage(Object error, AppLocalizations l10n) {
    if (error is FirebaseException) {
      return _handleFirebaseError(error, l10n);
    }

    if (error is ArgumentError) {
      return _handleArgumentError(error, l10n);
    }

    // Handle network connectivity errors
    if (error.toString().contains('network') ||
        error.toString().contains('connection') ||
        error.toString().contains('timeout')) {
      return l10n.networkError;
    }

    // Default fallback
    return l10n.transactionOperationError;
  }

  /// Handle Firebase-specific errors
  static String _handleFirebaseError(
    FirebaseException error,
    AppLocalizations l10n,
  ) {
    switch (error.code) {
      case 'permission-denied':
        return l10n.permissionDeniedError;
      case 'unavailable':
        return l10n.serviceUnavailableError;
      case 'deadline-exceeded':
      case 'timeout':
        return l10n.operationTimeoutError;
      case 'resource-exhausted':
        return l10n.quotaExceededError;
      case 'not-found':
        return l10n.transactionNotFoundError;
      case 'already-exists':
        return l10n.transactionAlreadyExistsError;
      case 'failed-precondition':
        return l10n.operationFailedError;
      case 'aborted':
        return l10n.operationAbortedError;
      case 'out-of-range':
        return l10n.invalidDataError;
      case 'unimplemented':
        return l10n.featureNotAvailableError;
      case 'internal':
        return l10n.internalServerError;
      case 'data-loss':
        return l10n.dataCorruptionError;
      case 'unauthenticated':
        return l10n.authenticationRequiredError;
      default:
        return '${l10n.firestoreError}: ${error.message ?? error.code}';
    }
  }

  /// Handle argument errors (validation errors)
  static String _handleArgumentError(
    ArgumentError error,
    AppLocalizations l10n,
  ) {
    final message = error.message?.toString() ?? '';

    if (message.contains('Transaction not found')) {
      return l10n.transactionNotFoundError;
    }

    if (message.contains('User not authenticated') ||
        message.contains('does not belong to the specified user')) {
      return l10n.authenticationRequiredError;
    }

    if (message.contains('Account not found')) {
      return l10n.accountNotFoundError;
    }

    if (message.contains('Invalid amount')) {
      return l10n.invalidAmountError;
    }

    if (message.contains('Same account')) {
      return l10n.sameAccountError;
    }

    // Default validation error
    return l10n.validationError;
  }

  /// Get specific error message for transaction creation
  static String getCreateErrorMessage(Object error, AppLocalizations l10n) {
    final baseMessage = getErrorMessage(error, l10n);
    return '${l10n.transactionCreateError}: $baseMessage';
  }

  /// Get specific error message for transaction updates
  static String getUpdateErrorMessage(Object error, AppLocalizations l10n) {
    final baseMessage = getErrorMessage(error, l10n);
    return '${l10n.transactionUpdateError}: $baseMessage';
  }

  /// Get specific error message for transaction deletion
  static String getDeleteErrorMessage(Object error, AppLocalizations l10n) {
    final baseMessage = getErrorMessage(error, l10n);
    return '${l10n.transactionDeleteError}: $baseMessage';
  }

  /// Check if an error is retryable
  static bool isRetryable(Object error) {
    if (error is FirebaseException) {
      switch (error.code) {
        case 'unavailable':
        case 'deadline-exceeded':
        case 'timeout':
        case 'resource-exhausted':
        case 'aborted':
        case 'internal':
          return true;
        default:
          return false;
      }
    }

    // Network errors are generally retryable
    if (error.toString().contains('network') ||
        error.toString().contains('connection') ||
        error.toString().contains('timeout')) {
      return true;
    }

    return false;
  }
}
