import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/utils/validation_helpers.dart';
import 'package:budapp/utils/validation_result.dart';
import 'package:flutter/material.dart';

/// Comprehensive validation service for transaction forms
// ignore: avoid_classes_with_only_static_members
class TransactionValidators {
  static const double _maxAmount = 999999999.99; // ~1 billion
  static const int _maxDescriptionLength = 100;
  static const int _maxNotesLength = 500;

  /// Validates transaction amount
  static ValidationResult validateAmount(BuildContext context, String? value) {
    final l10n = AppLocalizations.of(context)!;

    // Use ValidationHelpers for currency amount validation
    final results = [
      validateRequired(context, value, customMessage: l10n.amountRequired),
      validateCurrencyAmount(
        context,
        value,
        min: 0.01, // Transactions must be positive
        max: _maxAmount,
        allowNegative: false,
      ),
    ];

    return combineResults(results);
  }

  /// Validates account selection
  static ValidationResult validateAccount(
    BuildContext context,
    String? accountId,
  ) {
    final l10n = AppLocalizations.of(context)!;

    return validateRequired(
      context,
      accountId,
      customMessage: l10n.accountRequired,
    );
  }

  /// Validates destination account for transfers
  static ValidationResult validateDestinationAccount(
    BuildContext context,
    String? destinationAccountId,
    String? sourceAccountId,
    TransactionType transactionType,
  ) {
    final l10n = AppLocalizations.of(context)!;

    // Only required for transfers
    if (transactionType != TransactionType.transfer) {
      return ValidationResult.success();
    }

    if (destinationAccountId == null || destinationAccountId.trim().isEmpty) {
      return ValidationResult.singleError(l10n.destinationAccountRequired);
    }

    // Source and destination must be different
    if (sourceAccountId != null && destinationAccountId == sourceAccountId) {
      return ValidationResult.singleError(l10n.sameAccountError);
    }

    return ValidationResult.success();
  }

  /// Validates transaction date
  static ValidationResult validateDate(BuildContext context, DateTime? date) {
    final l10n = AppLocalizations.of(context)!;

    if (date == null) {
      return ValidationResult.singleError(l10n.dateRequired);
    }

    // Don't allow dates more than 1 year in the future
    final oneYearFromNow = DateTime.now().add(const Duration(days: 365));
    if (date.isAfter(oneYearFromNow)) {
      return ValidationResult.singleError(
        l10n.dateRequired,
      ); // Using generic message for now
    }

    return ValidationResult.success();
  }

  /// Validates transaction description
  static ValidationResult validateDescription(
    BuildContext context,
    String? description,
  ) {
    return validateLength(
      context,
      description,
      maxLength: _maxDescriptionLength,
      fieldName: 'Description',
    );
  }

  /// Validates transaction notes
  static ValidationResult validateNotes(BuildContext context, String? notes) {
    return validateLength(
      context,
      notes,
      maxLength: _maxNotesLength,
      fieldName: 'Notes',
    );
  }

  /// Validates category selection for income/expense transactions
  static ValidationResult validateCategory(
    BuildContext context,
    String? categoryId,
    TransactionType transactionType,
  ) {
    final l10n = AppLocalizations.of(context)!;

    // Category is only required for income and expense transactions
    if (transactionType == TransactionType.transfer) {
      return ValidationResult.success();
    }

    if (categoryId == null || categoryId.trim().isEmpty) {
      return ValidationResult.singleError(l10n.categoryRequired);
    }

    return ValidationResult.success();
  }

  // Currency validation removed - using global currency preference

  /// Comprehensive transaction form validation
  static Map<String, String> validateTransactionForm(
    BuildContext context, {
    required String? amount,
    required String? accountId,
    String? destinationAccountId,
    String? categoryId,
    required DateTime? date,
    String? description,
    String? notes,
    required TransactionType transactionType,
  }) {
    final errors = <String, String>{};

    // Validate amount
    final amountResult = validateAmount(context, amount);
    if (!amountResult.isValid) {
      errors['amount'] = amountResult.firstError!;
    }

    // Validate account
    final accountResult = validateAccount(context, accountId);
    if (!accountResult.isValid) {
      errors['account'] = accountResult.firstError!;
    }

    // Validate destination account for transfers
    final destinationResult = validateDestinationAccount(
      context,
      destinationAccountId,
      accountId,
      transactionType,
    );
    if (!destinationResult.isValid) {
      errors['destinationAccount'] = destinationResult.firstError!;
    }

    // Validate category for income/expense transactions
    final categoryResult = validateCategory(
      context,
      categoryId,
      transactionType,
    );
    if (!categoryResult.isValid) {
      errors['category'] = categoryResult.firstError!;
    }

    // Validate date
    final dateResult = validateDate(context, date);
    if (!dateResult.isValid) {
      errors['date'] = dateResult.firstError!;
    }

    // Validate description
    final descriptionResult = validateDescription(context, description);
    if (!descriptionResult.isValid) {
      errors['description'] = descriptionResult.firstError!;
    }

    // Validate notes
    final notesResult = validateNotes(context, notes);
    if (!notesResult.isValid) {
      errors['notes'] = notesResult.firstError!;
    }

    // Currency validation removed - using global currency preference

    return errors;
  }

  /// Check if form is valid (no validation errors)
  static bool isFormValid(
    BuildContext context, {
    required String? amount,
    required String? accountId,
    String? destinationAccountId,
    String? categoryId,
    required DateTime? date,
    String? description,
    String? notes,
    required TransactionType transactionType,
  }) {
    final errors = validateTransactionForm(
      context,
      amount: amount,
      accountId: accountId,
      destinationAccountId: destinationAccountId,
      categoryId: categoryId,
      date: date,
      description: description,
      notes: notes,
      transactionType: transactionType,
    );
    return errors.isEmpty;
  }
}
