import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'transaction_providers.g.dart';

/// Provider for the list of all user transactions (real-time)
final transactionListProvider = StreamProvider<List<Transaction>>((ref) {
  final authService = ref.watch(authServiceProvider);
  final user = authService.currentUser;
  if (user == null) throw Exception('User not authenticated');

  final transactionRepository = ref.watch(transactionRepositoryProvider);
  return transactionRepository.watchUserTransactions(user.uid);
});

/// Provider for creating transactions
@riverpod
class TransactionCreator extends _$TransactionCreator {
  @override
  FutureOr<void> build() {}

  /// Create a new transaction with automatic account balance updates
  /// Routes to appropriate type-specific method based on transaction type
  Future<void> createTransaction(Transaction transaction) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final transactionRepository = ref.read(transactionRepositoryProvider);

      // Route to appropriate type-specific method to ensure account balances are updated
      final transactionDate = transaction.transactionDate ?? DateTime.now();

      switch (transaction.type) {
        case TransactionType.income:
          await transactionRepository.createIncomeTransaction(
            userId: transaction.userId,
            toAccountId: transaction.toAccountId!,
            amountCents: transaction.amountCents,
            transactionDate: transactionDate,
            categoryId: transaction.categoryId,
            description: transaction.description,
            notes: transaction.notes,
            tags: transaction.tagIds,
            metadata: transaction.metadata,
          );
        case TransactionType.expense:
          await transactionRepository.createExpenseTransaction(
            userId: transaction.userId,
            fromAccountId: transaction.fromAccountId!,
            amountCents: transaction.amountCents,
            transactionDate: transactionDate,
            categoryId: transaction.categoryId,
            description: transaction.description,
            notes: transaction.notes,
            tags: transaction.tagIds,
            metadata: transaction.metadata,
          );
        case TransactionType.transfer:
          await transactionRepository.createTransferTransaction(
            userId: transaction.userId,
            fromAccountId: transaction.fromAccountId!,
            toAccountId: transaction.toAccountId!,
            amountCents: transaction.amountCents,
            transactionDate: transactionDate,
            description: transaction.description,
            notes: transaction.notes,
            tags: transaction.tagIds,
            metadata: transaction.metadata,
          );
      }
    });
  }

  /// Create an income transaction with enhanced validation
  Future<String> createIncomeTransaction({
    required String userId,
    required String toAccountId,
    required int amountCents,
    required DateTime transactionDate,
    String? categoryId,
    String? description,
    String? notes,
    List<String> tags = const [],
    Map<String, dynamic> metadata = const {},
  }) async {
    state = const AsyncValue.loading();

    try {
      final transactionRepository = ref.read(transactionRepositoryProvider);
      final transactionId = await transactionRepository.createIncomeTransaction(
        userId: userId,
        toAccountId: toAccountId,
        amountCents: amountCents,
        transactionDate: transactionDate,
        categoryId: categoryId,
        description: description,
        notes: notes,
        tags: tags,
        metadata: metadata,
      );

      state = const AsyncValue.data(null);
      return transactionId;
    } on Exception catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }

  /// Create an expense transaction with enhanced validation
  Future<String> createExpenseTransaction({
    required String userId,
    required String fromAccountId,
    required int amountCents,
    required DateTime transactionDate,
    String? categoryId,
    String? description,
    String? notes,
    List<String> tags = const [],
    Map<String, dynamic> metadata = const {},
  }) async {
    state = const AsyncValue.loading();

    try {
      final transactionRepository = ref.read(transactionRepositoryProvider);
      final transactionId = await transactionRepository
          .createExpenseTransaction(
            userId: userId,
            fromAccountId: fromAccountId,
            amountCents: amountCents,
            transactionDate: transactionDate,
            categoryId: categoryId,
            description: description,
            notes: notes,
            tags: tags,
            metadata: metadata,
          );

      state = const AsyncValue.data(null);
      return transactionId;
    } on Exception catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }

  /// Create a transfer transaction with enhanced validation
  Future<String> createTransferTransaction({
    required String userId,
    required String fromAccountId,
    required String toAccountId,
    required int amountCents,
    required DateTime transactionDate,
    String? description,
    String? notes,
    List<String> tags = const [],
    Map<String, dynamic> metadata = const {},
  }) async {
    state = const AsyncValue.loading();

    try {
      final transactionRepository = ref.read(transactionRepositoryProvider);
      final transactionId = await transactionRepository
          .createTransferTransaction(
            userId: userId,
            fromAccountId: fromAccountId,
            toAccountId: toAccountId,
            amountCents: amountCents,
            transactionDate: transactionDate,
            description: description,
            notes: notes,
            tags: tags,
            metadata: metadata,
          );

      state = const AsyncValue.data(null);
      return transactionId;
    } on Exception catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }
}

/// Provider for updating transactions
@riverpod
class TransactionUpdater extends _$TransactionUpdater {
  @override
  FutureOr<void> build() {}

  /// Update an existing transaction
  Future<void> updateTransaction(
    String transactionId,
    Transaction transaction,
  ) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final transactionRepository = ref.read(transactionRepositoryProvider);
      await transactionRepository.updateTransaction(transactionId, transaction);
    });
  }

  /// Update transaction status
  Future<void> updateTransactionStatus(
    String transactionId,
    TransactionStatus status,
  ) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final transactionRepository = ref.read(transactionRepositoryProvider);
      await transactionRepository.updateTransactionStatus(
        transactionId,
        status,
      );
    });
  }
}

/// Provider for deleting transactions
@riverpod
class TransactionDeleter extends _$TransactionDeleter {
  @override
  FutureOr<void> build() {}

  /// Delete a transaction
  Future<void> deleteTransaction(String userId, String transactionId) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final transactionRepository = ref.read(transactionRepositoryProvider);
      await transactionRepository.deleteTransaction(userId, transactionId);
    });
  }
}
