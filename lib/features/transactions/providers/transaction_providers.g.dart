// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$transactionCreatorHash() =>
    r'b04cdb0338ab928cbf0b47c9ebf99da3aef550ad';

/// Provider for creating transactions
///
/// Copied from [TransactionCreator].
@ProviderFor(TransactionCreator)
final transactionCreatorProvider =
    AutoDisposeAsyncNotifierProvider<TransactionCreator, void>.internal(
      TransactionCreator.new,
      name: r'transactionCreatorProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$transactionCreatorHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TransactionCreator = AutoDisposeAsyncNotifier<void>;
String _$transactionUpdaterHash() =>
    r'f3f418ae686ee7f9593d63bc688ccab169e117f8';

/// Provider for updating transactions
///
/// Copied from [TransactionUpdater].
@ProviderFor(TransactionUpdater)
final transactionUpdaterProvider =
    AutoDisposeAsyncNotifierProvider<TransactionUpdater, void>.internal(
      TransactionUpdater.new,
      name: r'transactionUpdaterProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$transactionUpdaterHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TransactionUpdater = AutoDisposeAsyncNotifier<void>;
String _$transactionDeleterHash() =>
    r'c64430d22f4d99b241ad93bd9ba6737c1d9332d9';

/// Provider for deleting transactions
///
/// Copied from [TransactionDeleter].
@ProviderFor(TransactionDeleter)
final transactionDeleterProvider =
    AutoDisposeAsyncNotifierProvider<TransactionDeleter, void>.internal(
      TransactionDeleter.new,
      name: r'transactionDeleterProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$transactionDeleterHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TransactionDeleter = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
