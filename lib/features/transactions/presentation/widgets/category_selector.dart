import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/utils/color_utils.dart';
import 'package:budapp/utils/icon_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Widget for selecting a category for transactions
class CategorySelector extends ConsumerWidget {
  const CategorySelector({
    super.key,
    required this.selectedCategoryId,
    required this.onChanged,
    required this.transactionType,
    this.errorText,
    this.enabled = true,
  });
  final String? selectedCategoryId;
  final ValueChanged<String?> onChanged;
  final TransactionType transactionType;
  final String? errorText;
  final bool enabled;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final categoriesAsync = ref.watch(categoryListProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              l10n.category,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: AppTypography.fontWeightMedium,
              ),
            ),
            const SizedBox(width: AppSpacing.sm),
            Text(
              l10n.optional,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.sm),
        categoriesAsync.when(
          data: (categories) =>
              _buildCategoryDropdown(context, theme, l10n, categories),
          loading: () => _buildLoadingDropdown(context, theme, l10n),
          error: (error, stack) => _buildErrorDropdown(context, theme, l10n),
        ),
      ],
    );
  }

  Widget _buildCategoryDropdown(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
    List<Category> categories,
  ) {
    // Filter categories by transaction type and active status
    final filteredCategories =
        categories.where((category) {
            return category.isActive &&
                _getCategoryTypeForTransaction(transactionType) ==
                    category.type;
          }).toList()
          // Sort categories: parent categories first, then children
          ..sort((a, b) {
            if (a.parentId == null && b.parentId != null) return -1;
            if (a.parentId != null && b.parentId == null) return 1;
            return a.name.compareTo(b.name);
          });

    return DropdownButtonFormField<String>(
      value: selectedCategoryId,
      onChanged: enabled ? onChanged : null,
      decoration: InputDecoration(
        hintText: l10n.selectCategory,
        errorText: errorText,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        prefixIcon: const Icon(Icons.category),
      ),
      items: [
        // None option
        DropdownMenuItem<String>(
          value: null,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.clear,
                size: 20,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: AppSpacing.sm),
              Text(
                l10n.noCategory,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontStyle: FontStyle.italic,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
        // Category options
        ...filteredCategories.map((category) {
          final isChild = category.parentId != null;
          final parentCategory = isChild
              ? categories.firstWhere(
                  (c) => c.id == category.parentId,
                  orElse: () => category,
                )
              : null;

          return DropdownMenuItem<String>(
            value: category.id,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Indentation for child categories
                if (isChild) const SizedBox(width: AppSpacing.md),
                // Category icon
                Icon(
                  _getCategoryIcon(category.icon),
                  size: 20,
                  color: _getCategoryColor(category.color, theme),
                ),
                const SizedBox(width: AppSpacing.sm),
                // Category name with parent context
                Flexible(
                  child: Text(
                    isChild && parentCategory != null
                        ? '${parentCategory.name} > ${category.name}'
                        : category.name,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: isChild
                          ? FontWeight.normal
                          : AppTypography.fontWeightMedium,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildLoadingDropdown(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return DropdownButtonFormField<String>(
      value: null,
      onChanged: null,
      decoration: InputDecoration(
        hintText: l10n.loading,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        prefixIcon: const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
      items: const [],
    );
  }

  Widget _buildErrorDropdown(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return DropdownButtonFormField<String>(
      value: null,
      onChanged: null,
      decoration: InputDecoration(
        hintText: 'Error loading categories',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        prefixIcon: Icon(Icons.error_outline, color: theme.colorScheme.error),
      ),
      items: const [],
    );
  }

  CategoryType _getCategoryTypeForTransaction(TransactionType transactionType) {
    switch (transactionType) {
      case TransactionType.income:
        return CategoryType.income;
      case TransactionType.expense:
        return CategoryType.expense;
      case TransactionType.transfer:
        // Transfers typically don't use categories
        return CategoryType.expense; // Default fallback
    }
  }

  IconData _getCategoryIcon(String? iconName) {
    return iconFromName(iconName ?? 'category');
  }

  Color _getCategoryColor(String? colorHex, ThemeData theme) {
    if (colorHex == null || colorHex.isEmpty) {
      return theme.colorScheme.onSurfaceVariant;
    }

    try {
      return parseHex(colorHex);
    } on Exception {
      return theme.colorScheme.onSurfaceVariant;
    }
  }
}
