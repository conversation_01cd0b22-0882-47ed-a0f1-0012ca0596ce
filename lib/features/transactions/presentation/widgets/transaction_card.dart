import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/utils/color_utils.dart';
import 'package:budapp/utils/icon_utils.dart';
import 'package:budapp/widgets/common/app_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

/// A card widget that displays transaction information in a list
class TransactionCard extends ConsumerWidget {
  const TransactionCard({
    super.key,
    required this.transaction,
    this.onTap,
    this.showActions = false,
    this.onEdit,
    this.onDelete,
  });
  final Transaction transaction;
  final VoidCallback? onTap;
  final bool showActions;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Card(
      elevation: AppElevation.sm,
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with category/transfer icon, title, and amount
              Row(
                children: [
                  _buildLeftIcon(context, ref, theme),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppText.title(
                          _getTransactionTitle(ref, l10n),
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: AppSpacing.xs),
                        AppText.label(
                          _formatDate(transaction.transactionDate, l10n),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  _buildAmountText(theme, ref),

                  // Actions menu
                  if (showActions)
                    PopupMenuButton<String>(
                      icon: Icon(
                        Icons.more_vert,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            onEdit?.call();
                          case 'delete':
                            onDelete?.call();
                        }
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              const Icon(Icons.edit_outlined),
                              const SizedBox(width: AppSpacing.sm),
                              Text(l10n.edit),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(
                                Icons.delete_outline,
                                color: theme.colorScheme.error,
                              ),
                              const SizedBox(width: AppSpacing.sm),
                              Text(
                                l10n.delete,
                                style: TextStyle(
                                  color: theme.colorScheme.error,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                ],
              ),

              // Notes if present
              if (transaction.notes?.isNotEmpty ?? false) ...[
                const SizedBox(height: AppSpacing.sm),
                Container(
                  padding: const EdgeInsets.all(AppSpacing.sm),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.note_outlined,
                        size: 16,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: AppSpacing.xs),
                      Expanded(
                        child: AppText.note(
                          transaction.notes!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLeftIcon(BuildContext context, WidgetRef ref, ThemeData theme) {
    if (transaction.type == TransactionType.transfer) {
      // For transfers, show transfer icon and navigate to destination account
      return GestureDetector(
        onTap: () {
          if (transaction.toAccountId != null) {
            context.go('/transactions/account/${transaction.toAccountId}');
          }
        },
        child: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: theme.colorScheme.tertiary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
          ),
          child: Icon(
            Icons.swap_horiz,
            color: theme.colorScheme.tertiary,
            size: 24,
          ),
        ),
      );
    }

    // For income/expense, show category icon
    if (transaction.categoryId != null) {
      final categoryAsync = ref.watch(
        categoryProvider(transaction.categoryId!),
      );
      return categoryAsync.when(
        data: (category) => GestureDetector(
          onTap: () {
            context.go('/transactions/category/${transaction.categoryId}');
          },
          child: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: _getCategoryColor(category, theme).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppBorderRadius.md),
            ),
            child: Icon(
              _getCategoryIcon(category),
              color: _getCategoryColor(category, theme),
              size: 24,
            ),
          ),
        ),
        loading: () => _buildDefaultTypeIcon(theme),
        error: (_, _) => _buildDefaultTypeIcon(theme),
      );
    }

    // Fallback to default type icon
    return _buildDefaultTypeIcon(theme);
  }

  Widget _buildDefaultTypeIcon(ThemeData theme) {
    IconData iconData;
    Color iconColor;

    switch (transaction.type) {
      case TransactionType.income:
        iconData = Icons.trending_up;
        iconColor = theme.colorScheme.primary;
      case TransactionType.expense:
        iconData = Icons.trending_down;
        iconColor = theme.colorScheme.error;
      case TransactionType.transfer:
        iconData = Icons.swap_horiz;
        iconColor = theme.colorScheme.tertiary;
    }

    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
      ),
      child: Icon(iconData, size: 24, color: iconColor),
    );
  }

  Widget _buildAmountText(ThemeData theme, WidgetRef ref) {
    final currencyFormatter = ref.watch(currencyFormatterProvider);

    Color amountColor;
    String formattedAmount;

    switch (transaction.type) {
      case TransactionType.income:
        amountColor = theme.colorScheme.primary;
        formattedAmount = currencyFormatter.formatAmountWithSign(
          transaction.amountCents,
          showPositiveSign: true,
        );
      case TransactionType.expense:
        amountColor = theme.colorScheme.error;
        formattedAmount = currencyFormatter.formatAmountWithSign(
          -transaction.amountCents,
        );
      case TransactionType.transfer:
        amountColor = theme.colorScheme.onSurface;
        formattedAmount = currencyFormatter.formatAmount(
          transaction.amountCents,
        );
    }

    return Text(
      formattedAmount,
      style: theme.textTheme.titleMedium?.copyWith(
        color: amountColor,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  String _getTransactionTitle(WidgetRef ref, AppLocalizations l10n) {
    // 1. Primary: Use description if available
    if (transaction.description != null &&
        transaction.description!.isNotEmpty) {
      return transaction.description!;
    }

    // 2. Secondary: Use category name for income/expense
    if (transaction.categoryId != null &&
        transaction.type != TransactionType.transfer) {
      final categoryAsync = ref.read(categoryProvider(transaction.categoryId!));
      return categoryAsync.when(
        data: (category) => category?.name ?? _getDefaultDescription(l10n),
        loading: () => _getDefaultDescription(l10n),
        error: (_, _) => _getDefaultDescription(l10n),
      );
    }

    // 3. Transfer fallback: Show "Source → Destination" format
    if (transaction.type == TransactionType.transfer) {
      final fromAccountAsync = transaction.fromAccountId != null
          ? ref.read(accountProvider(transaction.fromAccountId!))
          : null;
      final toAccountAsync = transaction.toAccountId != null
          ? ref.read(accountProvider(transaction.toAccountId!))
          : null;

      if (fromAccountAsync != null && toAccountAsync != null) {
        return fromAccountAsync.when(
          data: (fromAccount) => toAccountAsync.when(
            data: (toAccount) =>
                '${fromAccount?.name ?? 'Unknown'} → ${toAccount?.name ?? 'Unknown'}',
            loading: () => _getDefaultDescription(l10n),
            error: (_, _) => _getDefaultDescription(l10n),
          ),
          loading: () => _getDefaultDescription(l10n),
          error: (_, _) => _getDefaultDescription(l10n),
        );
      }
    }

    // Final fallback
    return _getDefaultDescription(l10n);
  }

  String _getDefaultDescription(AppLocalizations l10n) {
    switch (transaction.type) {
      case TransactionType.income:
        return l10n.income;
      case TransactionType.expense:
        return l10n.expense;
      case TransactionType.transfer:
        return l10n.transfer;
    }
  }

  IconData _getCategoryIcon(Category? category) {
    if (category?.icon != null && category!.icon!.isNotEmpty) {
      return iconFromName(category.icon!);
    }

    // Default icons based on category type
    return category?.type == CategoryType.income
        ? Icons.trending_up
        : Icons.trending_down;
  }

  Color _getCategoryColor(Category? category, ThemeData theme) {
    if (category?.color != null && category!.color!.isNotEmpty) {
      return parseHex(category.color!);
    }

    // Default colors based on category type
    return category?.type == CategoryType.income
        ? theme.colorScheme.primary
        : theme.colorScheme.error;
  }

  String _formatDate(DateTime? date, AppLocalizations l10n) {
    if (date == null) return 'N/A';

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final transactionDate = DateTime(date.year, date.month, date.day);

    if (transactionDate == today) {
      return l10n.today;
    } else if (transactionDate == yesterday) {
      return l10n.yesterday;
    } else {
      return DateFormat.yMMMd().format(date);
    }
  }
}
