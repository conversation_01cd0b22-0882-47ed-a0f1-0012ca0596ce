import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/account.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Widget for selecting an account for transactions
class AccountSelector extends ConsumerWidget {
  const AccountSelector({
    super.key,
    required this.selectedAccountId,
    required this.onChanged,
    required this.label,
    this.errorText,
    this.enabled = true,
  });
  final String? selectedAccountId;
  final ValueChanged<String?> onChanged;
  final String label;
  final String? errorText;
  final bool enabled;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final accountsAsync = ref.watch(accountListProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: AppTypography.fontWeightMedium,
              ),
            ),
            const SizedBox(width: AppSpacing.xs),
            Text(
              '*',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.error,
                fontWeight: AppTypography.fontWeightMedium,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.sm),
        accountsAsync.when(
          data: (accounts) =>
              _buildAccountDropdown(context, theme, l10n, accounts, ref),
          loading: () => _buildLoadingDropdown(context, theme, l10n),
          error: (error, stack) => _buildErrorDropdown(context, theme, l10n),
        ),
      ],
    );
  }

  Widget _buildAccountDropdown(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
    List<Account> accounts,
    WidgetRef ref,
  ) {
    // Filter to only active accounts
    final activeAccounts = accounts
        .where((account) => account.isActive)
        .toList();

    return DropdownButtonFormField<String>(
      value: selectedAccountId,
      onChanged: enabled ? onChanged : null,
      decoration: InputDecoration(
        hintText: l10n.selectAccount,
        errorText: errorText,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        prefixIcon: const Icon(Icons.account_balance_wallet),
      ),
      items: activeAccounts.map((account) {
        return DropdownMenuItem<String>(
          value: account.id,
          child: Row(
            children: [
              // Account type icon
              Icon(
                _getAccountIcon(account.type),
                size: 20,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: AppSpacing.sm),
              // Account name and balance in single line
              Text(
                '${account.name} - ${_formatBalance(account.currentBalanceCents, ref)}',
                style: theme.textTheme.bodyMedium,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildLoadingDropdown(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return DropdownButtonFormField<String>(
      value: null,
      onChanged: null,
      decoration: InputDecoration(
        hintText: l10n.loading,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        prefixIcon: const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
      items: const [],
    );
  }

  Widget _buildErrorDropdown(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return DropdownButtonFormField<String>(
      value: null,
      onChanged: null,
      decoration: InputDecoration(
        hintText: l10n.errorLoadingAccount,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        prefixIcon: Icon(Icons.error_outline, color: theme.colorScheme.error),
      ),
      items: const [],
    );
  }

  IconData _getAccountIcon(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return Icons.account_balance;
      case AccountType.savings:
        return Icons.savings;
      case AccountType.creditCard:
        return Icons.credit_card;
      case AccountType.cash:
        return Icons.payments;
      case AccountType.investment:
        return Icons.trending_up;
      case AccountType.loan:
        return Icons.account_balance_wallet;
    }
  }

  String _formatBalance(int balanceCents, WidgetRef ref) {
    final currencyFormatter = ref.watch(currencyFormatterProvider);
    return currencyFormatter.formatAmount(balanceCents);
  }
}
