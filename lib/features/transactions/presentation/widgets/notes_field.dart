import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/common/app_text_form_field.dart';
import 'package:flutter/material.dart';

/// Widget for inputting transaction notes
class NotesField extends StatelessWidget {
  const NotesField({
    super.key,
    required this.controller,
    this.errorText,
    this.enabled = true,
    this.maxLength = 500,
  });
  final TextEditingController controller;
  final String? errorText;
  final bool enabled;
  final int maxLength;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return AppTextFormField(
      label: '${l10n.notes} (${l10n.optional})',
      controller: controller,
      enabled: enabled,
      errorText: errorText,
      maxLength: maxLength,
      maxLines: 3,
      minLines: 2,
      prefixIcon: const Icon(Icons.notes),
      hintText: l10n.enterNotes,
    );
  }
}
