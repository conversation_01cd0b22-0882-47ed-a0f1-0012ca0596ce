import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/common/app_text_form_field.dart';
import 'package:flutter/material.dart';

/// Widget for inputting transaction description
class DescriptionField extends StatelessWidget {
  const DescriptionField({
    super.key,
    required this.controller,
    this.errorText,
    this.enabled = true,
    this.maxLength = 100,
  });
  final TextEditingController controller;
  final String? errorText;
  final bool enabled;
  final int maxLength;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return AppTextFormField(
      label: '${l10n.description} (${l10n.optional})',
      controller: controller,
      enabled: enabled,
      errorText: errorText,
      maxLength: maxLength,
      prefixIcon: const Icon(Icons.description),
      hintText: l10n.enterDescription,
    );
  }
}
