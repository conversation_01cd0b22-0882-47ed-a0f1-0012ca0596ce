import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

/// A card widget that displays transaction metadata information
class TransactionMetadataCard extends ConsumerWidget {
  const TransactionMetadataCard({
    super.key,
    required this.transaction,
    this.onDelete,
  });
  final Transaction transaction;
  final VoidCallback? onDelete;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Card(
      elevation: AppElevation.sm,
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and delete button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  l10n.metadata,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (onDelete != null)
                  IconButton(
                    icon: Icon(
                      Icons.delete_outline,
                      color: theme.colorScheme.error,
                    ),
                    onPressed: onDelete,
                    tooltip: l10n.delete,
                  ),
              ],
            ),
            const SizedBox(height: AppSpacing.md),

            // Metadata rows
            _buildDetailRow(l10n.transactionId, transaction.id, theme),
            _buildDetailRow(
              l10n.status,
              _getStatusDisplayName(transaction.status, l10n),
              theme,
            ),
            _buildDetailRow(
              l10n.createdAt,
              transaction.createdAt != null
                  ? DateFormat.yMMMd().add_jm().format(transaction.createdAt!)
                  : 'N/A',
              theme,
            ),
            _buildDetailRow(
              l10n.updatedAt,
              transaction.updatedAt != null
                  ? DateFormat.yMMMd().add_jm().format(transaction.updatedAt!)
                  : 'N/A',
              theme,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(child: Text(value, style: theme.textTheme.bodyMedium)),
        ],
      ),
    );
  }

  String _getStatusDisplayName(
    TransactionStatus status,
    AppLocalizations l10n,
  ) {
    switch (status) {
      case TransactionStatus.pending:
        return l10n.pending;
      case TransactionStatus.completed:
        return l10n.completed;
      case TransactionStatus.cancelled:
        return l10n.cancelled;
      case TransactionStatus.failed:
        return l10n.failed;
    }
  }
}
