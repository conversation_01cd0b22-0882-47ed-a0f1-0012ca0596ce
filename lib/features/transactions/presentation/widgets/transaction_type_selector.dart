import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';

/// Widget for selecting transaction type (Income, Expense, Transfer)
class TransactionTypeSelector extends StatelessWidget {
  const TransactionTypeSelector({
    super.key,
    required this.selectedType,
    required this.onChanged,
  });
  final TransactionType selectedType;
  final ValueChanged<TransactionType> onChanged;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.transactionType,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: AppTypography.fontWeightMedium,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        SegmentedButton<TransactionType>(
          segments: [
            ButtonSegment<TransactionType>(
              value: TransactionType.income,
              label: Text(l10n.income),
              icon: const Icon(Icons.trending_up),
            ),
            ButtonSegment<TransactionType>(
              value: TransactionType.expense,
              label: Text(l10n.expense),
              icon: const Icon(Icons.trending_down),
            ),
            ButtonSegment<TransactionType>(
              value: TransactionType.transfer,
              label: Text(l10n.transfer),
              icon: const Icon(Icons.swap_horiz),
            ),
          ],
          selected: {selectedType},
          onSelectionChanged: (Set<TransactionType> selection) {
            if (selection.isNotEmpty) {
              onChanged(selection.first);
            }
          },
          style: SegmentedButton.styleFrom(
            backgroundColor: theme.colorScheme.surfaceContainerHighest,
            foregroundColor: theme.colorScheme.onSurface,
            selectedBackgroundColor: theme.colorScheme.primary,
            selectedForegroundColor: theme.colorScheme.onPrimary,
          ),
        ),
      ],
    );
  }
}
