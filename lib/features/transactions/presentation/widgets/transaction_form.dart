import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/transactions/presentation/providers/transaction_form_providers.dart';
import 'package:budapp/features/transactions/presentation/widgets/account_selector.dart';
import 'package:budapp/features/transactions/presentation/widgets/amount_input_field.dart';
import 'package:budapp/features/transactions/presentation/widgets/category_selector.dart';
import 'package:budapp/features/transactions/presentation/widgets/date_picker_field.dart';
import 'package:budapp/features/transactions/presentation/widgets/description_field.dart';
import 'package:budapp/features/transactions/presentation/widgets/notes_field.dart';
import 'package:budapp/features/transactions/presentation/widgets/transaction_type_selector.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Main form widget for creating/editing transactions
class TransactionForm extends ConsumerStatefulWidget {
  const TransactionForm({super.key, this.initialTransaction, this.onSubmit});
  final Transaction? initialTransaction;
  final Future<void> Function(Transaction transaction)? onSubmit;

  @override
  ConsumerState<TransactionForm> createState() => _TransactionFormState();
}

class _TransactionFormState extends ConsumerState<TransactionForm> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _initializeForm() {
    if (widget.initialTransaction != null) {
      final transaction = widget.initialTransaction!;

      // Initialize form state with existing transaction data
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref
            .read(transactionFormProvider.notifier)
            .initializeWithTransaction(transaction);

        _descriptionController.text = transaction.description ?? '';
        _notesController.text = transaction.notes ?? '';
      });
    } else {
      // Initialize with default values for new transaction
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref
            .read(transactionFormProvider.notifier)
            .initializeForNewTransaction();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final formState = ref.watch(transactionFormProvider);
    final formNotifier = ref.read(transactionFormProvider.notifier);

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Transaction Type Selector
          TransactionTypeSelector(
            selectedType: formState.transactionType,
            onChanged: formNotifier.setTransactionType,
          ),

          const SizedBox(height: AppSpacing.lg),

          // Amount Input
          AmountInputField(
            controller: _amountController,
            onChanged: _handleAmountChanged,
            errorText: formState.amountError,
          ),

          const SizedBox(height: AppSpacing.lg),

          // Account Selector(s)
          if (formState.transactionType == TransactionType.transfer) ...[
            // Source Account for transfers
            AccountSelector(
              selectedAccountId: formState.accountId,
              onChanged: formNotifier.setAccountId,
              label: l10n.fromAccount,
              errorText: formState.accountError,
            ),
            const SizedBox(height: AppSpacing.lg),
            // Destination Account for transfers
            AccountSelector(
              selectedAccountId: formState.toAccountId,
              onChanged: formNotifier.setToAccountId,
              label: l10n.toAccount,
              errorText: formState.toAccountError,
            ),
          ] else ...[
            // Single Account for income/expense
            AccountSelector(
              selectedAccountId: formState.accountId,
              onChanged: formNotifier.setAccountId,
              label: formState.transactionType == TransactionType.income
                  ? l10n.toAccount
                  : l10n.fromAccount,
              errorText: formState.accountError,
            ),
          ],

          const SizedBox(height: AppSpacing.lg),

          // Category Selector (only for income/expense)
          if (formState.transactionType != TransactionType.transfer) ...[
            CategorySelector(
              selectedCategoryId: formState.categoryId,
              onChanged: formNotifier.setCategoryId,
              transactionType: formState.transactionType,
              errorText: formState.categoryError,
            ),
            const SizedBox(height: AppSpacing.lg),
          ],

          // Date Picker
          DatePickerField(
            selectedDate: formState.date,
            onChanged: formNotifier.setDate,
            errorText: formState.dateError,
          ),

          const SizedBox(height: AppSpacing.lg),

          // Description Field
          DescriptionField(
            controller: _descriptionController,
            errorText: formState.descriptionError,
          ),

          const SizedBox(height: AppSpacing.lg),

          // Notes Field
          NotesField(
            controller: _notesController,
            errorText: formState.notesError,
          ),

          const SizedBox(height: AppSpacing.xl),

          // Action Buttons
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _canSubmit(formState) ? _handleSubmit : null,
              child: Text(
                widget.initialTransaction != null
                    ? l10n.updateTransaction
                    : l10n.createTransaction,
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _canSubmit(TransactionFormState formState) {
    // Basic checks for required fields
    if (formState.amount == null ||
        formState.amount! <= 0 ||
        formState.accountId == null ||
        formState.date == null) {
      return false;
    }

    // Category is required for income and expense transactions
    if (formState.transactionType != TransactionType.transfer) {
      if (formState.categoryId == null || formState.categoryId!.isEmpty) {
        return false;
      }
    }

    // Additional check for transfers
    if (formState.transactionType == TransactionType.transfer) {
      if (formState.toAccountId == null ||
          formState.accountId == formState.toAccountId) {
        return false;
      }
    }

    return true;
  }

  void _handleAmountChanged(String value) {
    final formNotifier = ref.read(transactionFormProvider.notifier);
    final amountText = value.trim();

    if (amountText.isEmpty) {
      formNotifier.setAmount(null);
    } else {
      final amount = double.tryParse(amountText);
      if (amount != null && amount > 0) {
        formNotifier.setAmount((amount * 100).round()); // Convert to cents
      } else {
        formNotifier.setAmount(null);
      }
    }
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) return;

    final formNotifier = ref.read(transactionFormProvider.notifier);

    // Update form state with current text field values
    final amountText = _amountController.text.trim();
    if (amountText.isNotEmpty) {
      final amount = double.tryParse(amountText);
      if (amount != null) {
        formNotifier.setAmount((amount * 100).round()); // Convert to cents
      }
    }

    formNotifier
      ..setDescription(_descriptionController.text.trim())
      ..setNotes(_notesController.text.trim());

    // Validate the complete form
    if (!formNotifier.validateForm(context)) {
      return;
    }

    // Get current user ID
    final currentUser = ref.read(currentUserProvider);
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final transaction = formNotifier.buildTransaction(
      userId: currentUser.uid,
      existingId: widget.initialTransaction?.id,
    );

    // Call the submit callback if provided
    if (widget.onSubmit != null) {
      await widget.onSubmit!(transaction);
    }
  }
}
