import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Full-screen transaction filters screen
///
/// Replaces the modal bottom sheet approach with a full-screen interface
/// for better user experience and consistency with other screens.
class TransactionFiltersScreen extends StatefulWidget {
  const TransactionFiltersScreen({super.key});

  @override
  State<TransactionFiltersScreen> createState() =>
      _TransactionFiltersScreenState();
}

class _TransactionFiltersScreenState extends State<TransactionFiltersScreen> {
  TransactionType? _selectedType;
  DateTimeRange? _selectedDateRange;
  double? _minAmount;
  double? _maxAmount;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Filters'),
        actions: [
          TextButton(onPressed: _clearFilters, child: const Text('Clear')),
          TextButton(onPressed: _applyFilters, child: const Text('Apply')),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Transaction Type Filter
            Text(
              'Transaction Type',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            SegmentedButton<TransactionType?>(
              segments: [
                ButtonSegment(value: null, label: Text(l10n.all)),
                ButtonSegment(
                  value: TransactionType.income,
                  label: Text(l10n.income),
                  icon: const Icon(Icons.trending_up),
                ),
                ButtonSegment(
                  value: TransactionType.expense,
                  label: Text(l10n.expense),
                  icon: const Icon(Icons.trending_down),
                ),
                ButtonSegment(
                  value: TransactionType.transfer,
                  label: Text(l10n.transfer),
                  icon: const Icon(Icons.swap_horiz),
                ),
              ],
              selected: {_selectedType},
              onSelectionChanged: (Set<TransactionType?> selection) {
                setState(() {
                  _selectedType = selection.first;
                });
              },
              showSelectedIcon: false,
            ),

            const SizedBox(height: 24),

            // Date Range Filter
            Text(
              'Date Range',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Card(
              child: ListTile(
                leading: const Icon(Icons.date_range),
                title: Text(
                  _selectedDateRange == null
                      ? 'Select date range'
                      : '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}',
                ),
                trailing: _selectedDateRange != null
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _selectedDateRange = null;
                          });
                        },
                      )
                    : const Icon(Icons.chevron_right),
                onTap: _selectDateRange,
              ),
            ),

            const SizedBox(height: 24),

            // Amount Range Filter
            Text(
              'Amount Range',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Min Amount',
                      prefixText: r'$',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    onChanged: (value) {
                      setState(() {
                        _minAmount = double.tryParse(value);
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Max Amount',
                      prefixText: r'$',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    onChanged: (value) {
                      setState(() {
                        _maxAmount = double.tryParse(value);
                      });
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 32),

            // Quick Filter Buttons
            Text(
              'Quick Filters',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                FilterChip(
                  label: const Text('This Week'),
                  selected: false,
                  onSelected: (selected) => _setDateRange(_getThisWeek()),
                ),
                FilterChip(
                  label: const Text('This Month'),
                  selected: false,
                  onSelected: (selected) => _setDateRange(_getThisMonth()),
                ),
                FilterChip(
                  label: const Text('Last 30 Days'),
                  selected: false,
                  onSelected: (selected) => _setDateRange(_getLast30Days()),
                ),
                FilterChip(
                  label: const Text('This Year'),
                  selected: false,
                  onSelected: (selected) => _setDateRange(_getThisYear()),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _clearFilters() {
    setState(() {
      _selectedType = null;
      _selectedDateRange = null;
      _minAmount = null;
      _maxAmount = null;
    });
  }

  void _applyFilters() {
    // Apply filters and navigate back
    // In a real implementation, this would update the filter state
    // and pass the filters back to the transactions list
    context.pop({
      'type': _selectedType,
      'dateRange': _selectedDateRange,
      'minAmount': _minAmount,
      'maxAmount': _maxAmount,
    });
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
    }
  }

  void _setDateRange(DateTimeRange range) {
    setState(() {
      _selectedDateRange = range;
    });
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  DateTimeRange _getThisWeek() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    return DateTimeRange(start: startOfWeek, end: now);
  }

  DateTimeRange _getThisMonth() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    return DateTimeRange(start: startOfMonth, end: now);
  }

  DateTimeRange _getLast30Days() {
    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(const Duration(days: 30));
    return DateTimeRange(start: thirtyDaysAgo, end: now);
  }

  DateTimeRange _getThisYear() {
    final now = DateTime.now();
    final startOfYear = DateTime(now.year, 1, 1);
    return DateTimeRange(start: startOfYear, end: now);
  }
}
