import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Full-screen transaction search screen
///
/// Replaces the modal bottom sheet approach with a full-screen interface
/// for better search experience and consistency with other screens.
class TransactionSearchScreen extends StatefulWidget {
  const TransactionSearchScreen({super.key});

  @override
  State<TransactionSearchScreen> createState() =>
      _TransactionSearchScreenState();
}

class _TransactionSearchScreenState extends State<TransactionSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  List<String> _recentSearches = [];
  List<String> _searchSuggestions = [];

  @override
  void initState() {
    super.initState();
    _loadRecentSearches();
    _loadSearchSuggestions();

    // Auto-focus the search field
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _loadRecentSearches() {
    // In a real implementation, this would load from SharedPreferences
    setState(() {
      _recentSearches = [
        'Grocery',
        'Gas station',
        'Coffee',
        'Restaurant',
        'Amazon',
      ];
    });
  }

  void _loadSearchSuggestions() {
    // In a real implementation, this would load from the database
    setState(() {
      _searchSuggestions = [
        'Grocery store',
        'Gas station',
        'Coffee shop',
        'Restaurant',
        'Amazon purchase',
        'Salary',
        'Transfer',
        'ATM withdrawal',
        'Online payment',
        'Subscription',
      ];
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: TextField(
          controller: _searchController,
          focusNode: _searchFocusNode,
          decoration: InputDecoration(
            hintText: 'Search transactions...',
            border: InputBorder.none,
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                      setState(() {});
                    },
                  )
                : null,
          ),
          onChanged: (value) {
            setState(() {});
          },
          onSubmitted: _performSearch,
        ),
        actions: [
          TextButton(
            onPressed: () => _performSearch(_searchController.text),
            child: const Text('Search'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_searchController.text.isEmpty) ...[
              // Recent Searches
              if (_recentSearches.isNotEmpty) ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Recent Searches',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextButton(
                      onPressed: _clearRecentSearches,
                      child: const Text('Clear'),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _recentSearches.map((search) {
                    return ActionChip(
                      label: Text(search),
                      onPressed: () => _selectSearch(search),
                      avatar: const Icon(Icons.history, size: 18),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 24),
              ],

              // Search Suggestions
              Text(
                'Suggestions',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              ...(_searchSuggestions.map((suggestion) {
                return ListTile(
                  leading: const Icon(Icons.search),
                  title: Text(suggestion),
                  onTap: () => _selectSearch(suggestion),
                );
              })),
            ] else ...[
              // Search Results or Filtered Suggestions
              Text(
                'Search Results',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),

              // Filter suggestions based on current input
              ...(_getFilteredSuggestions().map((suggestion) {
                return ListTile(
                  leading: const Icon(Icons.search),
                  title: RichText(
                    text: TextSpan(
                      style: theme.textTheme.bodyLarge,
                      children: _highlightSearchTerm(
                        suggestion,
                        _searchController.text,
                      ),
                    ),
                  ),
                  onTap: () => _selectSearch(suggestion),
                );
              })),

              if (_getFilteredSuggestions().isEmpty) ...[
                const SizedBox(height: 32),
                Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 64,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No suggestions found',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Try a different search term',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  List<String> _getFilteredSuggestions() {
    final query = _searchController.text.toLowerCase();
    if (query.isEmpty) return _searchSuggestions;

    return _searchSuggestions
        .where((suggestion) => suggestion.toLowerCase().contains(query))
        .toList();
  }

  List<TextSpan> _highlightSearchTerm(String text, String searchTerm) {
    if (searchTerm.isEmpty) {
      return [TextSpan(text: text)];
    }

    final theme = Theme.of(context);
    final spans = <TextSpan>[];
    final lowerText = text.toLowerCase();
    final lowerSearchTerm = searchTerm.toLowerCase();

    var start = 0;
    var index = lowerText.indexOf(lowerSearchTerm);

    while (index != -1) {
      // Add text before the match
      if (index > start) {
        spans.add(TextSpan(text: text.substring(start, index)));
      }

      // Add highlighted match
      spans.add(
        TextSpan(
          text: text.substring(index, index + searchTerm.length),
          style: TextStyle(
            backgroundColor: theme.colorScheme.primaryContainer,
            color: theme.colorScheme.onPrimaryContainer,
            fontWeight: FontWeight.bold,
          ),
        ),
      );

      start = index + searchTerm.length;
      index = lowerText.indexOf(lowerSearchTerm, start);
    }

    // Add remaining text
    if (start < text.length) {
      spans.add(TextSpan(text: text.substring(start)));
    }

    return spans;
  }

  void _selectSearch(String searchTerm) {
    _searchController.text = searchTerm;
    _performSearch(searchTerm);
  }

  void _performSearch(String query) {
    if (query.trim().isEmpty) return;

    // Add to recent searches
    _addToRecentSearches(query.trim());

    // Navigate back with search results
    context.pop({'query': query.trim()});
  }

  void _addToRecentSearches(String search) {
    setState(() {
      _recentSearches
        ..remove(search) // Remove if already exists
        ..insert(0, search); // Add to beginning
      if (_recentSearches.length > 10) {
        _recentSearches = _recentSearches.take(10).toList(); // Keep only 10
      }
    });

    // In a real implementation, save to SharedPreferences
  }

  void _clearRecentSearches() {
    setState(() {
      _recentSearches.clear();
    });

    // In a real implementation, clear from SharedPreferences
  }
}
