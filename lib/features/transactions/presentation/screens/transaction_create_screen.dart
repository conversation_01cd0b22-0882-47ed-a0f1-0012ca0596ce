import 'package:budapp/features/common/widgets/app_bar_helpers.dart';
import 'package:budapp/features/transactions/presentation/widgets/transaction_form.dart';
import 'package:budapp/features/transactions/providers/transaction_providers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Screen for creating a new transaction
class TransactionCreateScreen extends ConsumerWidget {
  const TransactionCreateScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    // Watch the transaction creation state
    final createState = ref.watch(transactionCreatorProvider);

    return Scaffold(
      appBar: AppBarHelpers.createStandardScrollableAppBar(
        title: l10n.addTransaction,
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => context.pop(),
            tooltip: l10n.close,
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Progress indicator when creating transaction
            if (createState.isLoading)
              LinearProgressIndicator(
                backgroundColor: theme.colorScheme.surfaceContainerHighest,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              ),

            // Main form content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: TransactionForm(
                  onSubmit: (transaction) async {
                    // Create the transaction
                    await ref
                        .read(transactionCreatorProvider.notifier)
                        .createTransaction(transaction);

                    // Check if creation was successful
                    final state = ref.read(transactionCreatorProvider);
                    if (state.hasValue && !state.hasError) {
                      // Show success message
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(l10n.transactionCreated),
                            backgroundColor: theme.colorScheme.primary,
                            behavior: SnackBarBehavior.floating,
                          ),
                        );

                        // Navigate back
                        context.pop();
                      }
                    } else if (state.hasError) {
                      // Show error message
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(l10n.transactionCreateError),
                            backgroundColor: theme.colorScheme.error,
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      }
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
