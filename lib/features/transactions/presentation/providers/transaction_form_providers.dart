import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/transactions/services/transaction_validators.dart';
import 'package:budapp/providers/providers.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'transaction_form_providers.g.dart';

// Sentinel value for copyWith to distinguish between null and undefined
const Object _undefined = Object();

/// State class for transaction form
class TransactionFormState {
  const TransactionFormState({
    this.transactionType = TransactionType.expense,
    this.amount,
    this.accountId,
    this.toAccountId,
    this.categoryId,
    this.date,
    this.description,
    this.notes,
    this.amountError,
    this.accountError,
    this.toAccountError,
    this.categoryError,
    this.dateError,
    this.descriptionError,
    this.notesError,
  });
  final TransactionType transactionType;
  final int? amount; // Amount in cents
  final String?
  accountId; // Used for income/expense, or source account for transfers
  final String? toAccountId; // Used for destination account in transfers
  final String? categoryId;
  final DateTime? date;
  final String? description;
  final String? notes;

  // Validation errors
  final String? amountError;
  final String? accountError;
  final String? toAccountError;
  final String? categoryError;
  final String? dateError;
  final String? descriptionError;
  final String? notesError;

  TransactionFormState copyWith({
    Object? transactionType = _undefined,
    Object? amount = _undefined,
    Object? accountId = _undefined,
    Object? toAccountId = _undefined,
    Object? categoryId = _undefined,
    Object? date = _undefined,
    Object? description = _undefined,
    Object? notes = _undefined,
    Object? amountError = _undefined,
    Object? accountError = _undefined,
    Object? toAccountError = _undefined,
    Object? categoryError = _undefined,
    Object? dateError = _undefined,
    Object? descriptionError = _undefined,
    Object? notesError = _undefined,
  }) {
    return TransactionFormState(
      transactionType: transactionType == _undefined
          ? this.transactionType
          : transactionType! as TransactionType,
      amount: amount == _undefined ? this.amount : amount as int?,
      accountId: accountId == _undefined
          ? this.accountId
          : accountId as String?,
      toAccountId: toAccountId == _undefined
          ? this.toAccountId
          : toAccountId as String?,
      categoryId: categoryId == _undefined
          ? this.categoryId
          : categoryId as String?,
      date: date == _undefined ? this.date : date as DateTime?,
      description: description == _undefined
          ? this.description
          : description as String?,
      notes: notes == _undefined ? this.notes : notes as String?,
      amountError: amountError == _undefined
          ? this.amountError
          : amountError as String?,
      accountError: accountError == _undefined
          ? this.accountError
          : accountError as String?,
      toAccountError: toAccountError == _undefined
          ? this.toAccountError
          : toAccountError as String?,
      categoryError: categoryError == _undefined
          ? this.categoryError
          : categoryError as String?,
      dateError: dateError == _undefined
          ? this.dateError
          : dateError as String?,
      descriptionError: descriptionError == _undefined
          ? this.descriptionError
          : descriptionError as String?,
      notesError: notesError == _undefined
          ? this.notesError
          : notesError as String?,
    );
  }
}

/// Provider for managing transaction form state
@riverpod
class TransactionForm extends _$TransactionForm {
  @override
  TransactionFormState build() {
    return const TransactionFormState();
  }

  /// Initialize form for a new transaction
  void initializeForNewTransaction() {
    state = TransactionFormState(
      transactionType: TransactionType.expense,
      date: DateTime.now(),
    );
  }

  /// Initialize form with existing transaction data
  void initializeWithTransaction(Transaction transaction) {
    // Map the appropriate account ID based on transaction type
    String? accountId;
    String? toAccountId;

    switch (transaction.type) {
      case TransactionType.income:
        accountId = transaction.toAccountId;
      case TransactionType.expense:
        accountId = transaction.fromAccountId;
      case TransactionType.transfer:
        // For transfers, use both accounts
        accountId = transaction.fromAccountId; // Source account
        toAccountId = transaction.toAccountId; // Destination account
    }

    state = TransactionFormState(
      transactionType: transaction.type,
      amount: transaction.amountCents,
      accountId: accountId,
      toAccountId: toAccountId,
      categoryId: transaction.categoryId,
      date: transaction.transactionDate,
      description: transaction.description,
      notes: transaction.notes,
    );
  }

  /// Set transaction type
  void setTransactionType(TransactionType type) {
    // Determine if we should clear the category
    var newCategoryId = state.categoryId;

    if (type == TransactionType.transfer) {
      // Transfers don't use categories
      newCategoryId = null;
    } else if (state.categoryId != null) {
      // Check if current category is compatible with new transaction type
      // We need to clear it if switching between income/expense since categories
      // are type-specific and the current category might not be valid for the new type
      final currentType = state.transactionType;
      if ((currentType == TransactionType.income &&
              type == TransactionType.expense) ||
          (currentType == TransactionType.expense &&
              type == TransactionType.income)) {
        newCategoryId = null;
      }
    }

    state = state.copyWith(
      transactionType: type,
      categoryId: newCategoryId,
      // Clear toAccountId if switching away from transfer
      toAccountId: type != TransactionType.transfer ? null : state.toAccountId,
    );
  }

  /// Set amount in cents
  void setAmount(int? amount) {
    state = state.copyWith(
      amount: amount,
      amountError: null, // Clear error when user changes value
    );
  }

  /// Set account ID
  void setAccountId(String? accountId) {
    state = state.copyWith(
      accountId: accountId,
      accountError: null, // Clear error when user changes value
    );
  }

  /// Set destination account ID (for transfers)
  void setToAccountId(String? toAccountId) {
    state = state.copyWith(
      toAccountId: toAccountId,
      toAccountError: null, // Clear error when user changes value
    );
  }

  /// Set category ID
  void setCategoryId(String? categoryId) {
    state = state.copyWith(
      categoryId: categoryId,
      categoryError: null, // Clear error when user changes value
    );
  }

  /// Set date
  void setDate(DateTime date) {
    state = state.copyWith(
      date: date,
      dateError: null, // Clear error when user changes value
    );
  }

  /// Set description
  void setDescription(String? description) {
    state = state.copyWith(
      description: (description?.isEmpty ?? false) ? null : description,
      descriptionError: null, // Clear error when user changes value
    );
  }

  /// Set notes
  void setNotes(String? notes) {
    state = state.copyWith(
      notes: (notes?.isEmpty ?? false) ? null : notes,
      notesError: null, // Clear error when user changes value
    );
  }

  /// Validate the entire form using the validation service
  bool validateForm(BuildContext context) {
    // Convert amount from cents to string for validation
    final amountString = state.amount != null
        ? (state.amount! / 100.0).toString()
        : null;

    // Get validation errors from the service
    final errors = TransactionValidators.validateTransactionForm(
      context,
      amount: amountString,
      accountId: state.accountId,
      destinationAccountId: state.toAccountId,
      categoryId: state.categoryId,
      date: state.date,
      description: state.description,
      notes: state.notes,
      transactionType: state.transactionType,
    );

    // Update state with validation errors
    state = state.copyWith(
      amountError: errors['amount'],
      accountError: errors['account'],
      toAccountError: errors['destinationAccount'],
      categoryError: errors['category'],
      dateError: errors['date'],
      descriptionError: errors['description'],
      notesError: errors['notes'],
    );

    // Return true if no errors
    return errors.isEmpty;
  }

  /// Legacy validation method for backward compatibility (deprecated)
  @Deprecated('Use validateForm(BuildContext context) instead')
  bool validateFormLegacy() {
    String? amountError;
    String? accountError;
    String? toAccountError;
    String? dateError;

    // Validate amount
    if (state.amount == null || state.amount! <= 0) {
      amountError = 'Amount is required and must be greater than 0';
    }

    // Validate account
    if (state.accountId == null || state.accountId!.isEmpty) {
      accountError = 'Account is required';
    }

    // Validate destination account for transfers
    if (state.transactionType == TransactionType.transfer) {
      if (state.toAccountId == null || state.toAccountId!.isEmpty) {
        toAccountError = 'Destination account is required';
      } else if (state.accountId == state.toAccountId) {
        toAccountError = 'Source and destination accounts must be different';
      }
    }

    // Validate date
    if (state.date == null) {
      dateError = 'Date is required';
    }

    // Update state with validation errors
    state = state.copyWith(
      amountError: amountError,
      accountError: accountError,
      toAccountError: toAccountError,
      dateError: dateError,
    );

    // Return true if no errors
    return amountError == null &&
        accountError == null &&
        toAccountError == null &&
        dateError == null;
  }

  /// Build transaction object from current form state
  Transaction buildTransaction({required String userId, String? existingId}) {
    // Validate required fields based on transaction type
    if (state.amount == null || state.accountId == null || state.date == null) {
      throw Exception('Form validation failed');
    }

    // Additional validation for transfers
    if (state.transactionType == TransactionType.transfer &&
        state.toAccountId == null) {
      throw Exception('Destination account is required for transfers');
    }

    final now = DateTime.now();

    // Generate ID if not provided (for new transactions)
    final transactionId =
        existingId ??
        ref
            .read(firestoreServiceProvider)
            .collection('users')
            .doc(userId)
            .collection('transactions')
            .doc()
            .id;

    // Map account ID to appropriate field based on transaction type
    String? fromAccountId;
    String? toAccountId;

    switch (state.transactionType) {
      case TransactionType.income:
        toAccountId = state.accountId;
      case TransactionType.expense:
        fromAccountId = state.accountId;
      case TransactionType.transfer:
        // For transfers, use both accounts
        fromAccountId = state.accountId; // Source account
        toAccountId = state.toAccountId; // Destination account
    }

    return Transaction(
      id: transactionId,
      userId: userId,
      type: state.transactionType,
      status: TransactionStatus.completed,
      amountCents: state.amount!,
      fromAccountId: fromAccountId,
      toAccountId: toAccountId,
      categoryId: state.categoryId,
      description: state.description,
      notes: state.notes,
      transactionDate: state.date,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Reset form to initial state
  void reset() {
    state = const TransactionFormState();
  }
}
