// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_form_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$transactionFormHash() => r'b0f8c8dc31bda1061af44de9b22b149044fed53a';

/// Provider for managing transaction form state
///
/// Copied from [TransactionForm].
@ProviderFor(TransactionForm)
final transactionFormProvider =
    AutoDisposeNotifierProvider<TransactionForm, TransactionFormState>.internal(
      TransactionForm.new,
      name: r'transactionFormProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$transactionFormHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TransactionForm = AutoDisposeNotifier<TransactionFormState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
