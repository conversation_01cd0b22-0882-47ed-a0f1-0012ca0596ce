import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:budapp/features/auth/services/biometric_service.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/services/session_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_providers.g.dart';

/// Authentication-specific providers
///
/// These providers handle authentication state, operations, and related services.
/// They build on top of the core Firebase providers.

/// Provider for AuthService instance
///
/// This provider creates an AuthService instance with injected dependencies.
/// The service handles authentication operations and automatic user profile management.
final authServiceProvider = Provider<AuthService>((ref) {
  final auth = ref.watch(firebaseAuthProvider);
  final googleSignIn = ref.watch(googleSignInProvider);
  final userRepository = ref.watch(userRepositoryProvider);
  final secureStorage = ref.watch(secureStorageServiceProvider);

  return AuthService(
    auth: auth,
    googleSignIn: googleSignIn,
    userRepository: userRepository,
    biometricService: BiometricService(),
    secureStorage: secureStorage,
  );
});

/// Provider for AuthErrorService instance
///
/// This provider creates an instance of AuthErrorService for error handling.
final authErrorServiceProvider = Provider<AuthErrorService>((ref) {
  return AuthErrorService();
});

/// Provider for SessionService instance
///
/// This provider creates a SessionService with injected AuthService.
/// The SessionService manages user session state and authentication flow.
final sessionServiceProvider = Provider<SessionService>((ref) {
  final authService = ref.watch(authServiceProvider);
  return SessionService(authService);
});

/// Provider for user authentication status with email verification
///
/// This provider returns true only if the user is signed in AND email is verified.
final isFullyAuthenticatedProvider = Provider<bool>((ref) {
  final isAuthenticated = ref.watch(isAuthenticatedProvider);
  final isEmailVerified = ref.watch(isEmailVerifiedProvider);
  return isAuthenticated && isEmailVerified;
});

/// Provider for user provider information
///
/// This provider returns a list of authentication providers the user has linked.
final userProvidersProvider = Provider<List<String>>((ref) {
  final user = ref.watch(currentUserProvider);
  if (user == null) return [];

  return user.providerData.map((info) => info.providerId).toList();
});

/// Provider to check if user signed in with Google
///
/// This provider returns true if the user authenticated using Google.
final isSignedInWithGoogleProvider = Provider<bool>((ref) {
  final providers = ref.watch(userProvidersProvider);
  return providers.contains('google.com');
});

/// Provider to check if user signed in with email/password
///
/// This provider returns true if the user authenticated using email/password.
final isSignedInWithEmailPasswordProvider = Provider<bool>((ref) {
  final providers = ref.watch(userProvidersProvider);
  return providers.contains('password');
});

/// Provider for user display name
///
/// This provider returns the current user's display name or null.
final userDisplayNameProvider = Provider<String?>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.displayName;
});

/// Provider for user email
///
/// This provider returns the current user's email or null.
final userEmailProvider = Provider<String?>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.email;
});

/// Provider for user photo URL
///
/// This provider returns the current user's photo URL or null.
final userPhotoUrlProvider = Provider<String?>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.photoURL;
});

// AsyncNotifier providers for authentication operations with enhanced error handling

/// AsyncNotifier for email/password login operations
@riverpod
class LoginNotifier extends _$LoginNotifier {
  @override
  FutureOr<void> build() => null;

  /// Sign in with email and password using AsyncValue.guard for error handling
  Future<void> signInWithEmail(String email, String password) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final authService = ref.read(authServiceProvider);
      await authService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    });
  }
}

/// AsyncNotifier for email/password signup operations
@riverpod
class SignupNotifier extends _$SignupNotifier {
  @override
  FutureOr<void> build() => null;

  /// Create account with email and password using AsyncValue.guard for error handling
  Future<void> signUpWithEmail(String email, String password) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final authService = ref.read(authServiceProvider);
      await authService.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
    });
  }
}

/// AsyncNotifier for Google sign-in operations
@riverpod
class GoogleSignInNotifier extends _$GoogleSignInNotifier {
  @override
  FutureOr<void> build() => null;

  /// Sign in with Google using AsyncValue.guard for error handling
  Future<void> signInWithGoogle() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final authService = ref.read(authServiceProvider);
      await authService.signInWithGoogle();
    });
  }
}

/// AsyncNotifier for password reset operations
@riverpod
class PasswordResetNotifier extends _$PasswordResetNotifier {
  @override
  FutureOr<void> build() => null;

  /// Send password reset email using AsyncValue.guard for error handling
  Future<void> sendPasswordResetEmail(String email) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final authService = ref.read(authServiceProvider);
      await authService.sendPasswordResetEmail(email: email);
    });
  }
}

/// AsyncNotifier for email verification operations
@riverpod
class EmailVerificationNotifier extends _$EmailVerificationNotifier {
  @override
  FutureOr<void> build() => null;

  /// Send email verification using AsyncValue.guard for error handling
  Future<void> sendEmailVerification() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final authService = ref.read(authServiceProvider);
      await authService.sendEmailVerification();
    });
  }

  /// Reload user to check email verification status
  Future<void> reloadUser() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final authService = ref.read(authServiceProvider);
      await authService.reloadUser();
    });
  }
}

/// Provider for handling authentication errors with user-friendly messages
@riverpod
class AuthErrorHandler extends _$AuthErrorHandler {
  @override
  String? build() => null;

  /// Handle authentication error and return user-friendly message
  String handleError(Object error, {String? context}) {
    final authError = AuthErrorService.handleFirebaseAuthException(
      error as FirebaseAuthException,
    );

    // Update state with error message
    state = authError.userMessage;

    return authError.userMessage;
  }

  /// Clear error message
  void clearError() {
    state = null;
  }
}
