// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'biometric_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$biometricServiceHash() => r'd13b3194e57bb984c452857712b3300be3bc3346';

/// Provider for BiometricService instance
///
/// Copied from [biometricService].
@ProviderFor(biometricService)
final biometricServiceProvider = AutoDisposeProvider<BiometricService>.internal(
  biometricService,
  name: r'biometricServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$biometricServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BiometricServiceRef = AutoDisposeProviderRef<BiometricService>;
String _$biometricAvailabilityHash() =>
    r'c2b536805d15878d6f6e4fea7a6f778c2a8ad1b3';

/// Provider for checking biometric availability on the device
///
/// Copied from [biometricAvailability].
@ProviderFor(biometricAvailability)
final biometricAvailabilityProvider =
    AutoDisposeFutureProvider<BiometricAvailability>.internal(
      biometricAvailability,
      name: r'biometricAvailabilityProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$biometricAvailabilityHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BiometricAvailabilityRef =
    AutoDisposeFutureProviderRef<BiometricAvailability>;
String _$biometricPreferenceHash() =>
    r'bc7e36132200d1674c0131220609d71a6a0e68d3';

/// Provider for checking if biometric authentication is enabled for the current user
///
/// Copied from [biometricPreference].
@ProviderFor(biometricPreference)
final biometricPreferenceProvider = AutoDisposeFutureProvider<bool>.internal(
  biometricPreference,
  name: r'biometricPreferenceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$biometricPreferenceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BiometricPreferenceRef = AutoDisposeFutureProviderRef<bool>;
String _$biometricAuthReadyHash() =>
    r'65d32835fde8b48ea1edfa5e997f11e06bca380a';

/// Provider for checking if biometric authentication is both available and enabled
///
/// Copied from [biometricAuthReady].
@ProviderFor(biometricAuthReady)
final biometricAuthReadyProvider = AutoDisposeFutureProvider<bool>.internal(
  biometricAuthReady,
  name: r'biometricAuthReadyProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$biometricAuthReadyHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BiometricAuthReadyRef = AutoDisposeFutureProviderRef<bool>;
String _$biometricAuthNotifierHash() =>
    r'8308434115192756a72f7fdc332fab14b92c7e98';

/// Notifier for managing biometric authentication state
///
/// Copied from [BiometricAuthNotifier].
@ProviderFor(BiometricAuthNotifier)
final biometricAuthNotifierProvider =
    AutoDisposeNotifierProvider<
      BiometricAuthNotifier,
      BiometricAuthState
    >.internal(
      BiometricAuthNotifier.new,
      name: r'biometricAuthNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$biometricAuthNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$BiometricAuthNotifier = AutoDisposeNotifier<BiometricAuthState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
