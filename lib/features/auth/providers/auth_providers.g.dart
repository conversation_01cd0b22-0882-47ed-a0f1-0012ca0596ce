// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$loginNotifierHash() => r'76c5eac6c4073b661dc70fc71135d247f7b0f386';

/// AsyncNotifier for email/password login operations
///
/// Copied from [LoginNotifier].
@ProviderFor(LoginNotifier)
final loginNotifierProvider =
    AutoDisposeAsyncNotifierProvider<LoginNotifier, void>.internal(
      LoginNotifier.new,
      name: r'loginNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$loginNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$LoginNotifier = AutoDisposeAsyncNotifier<void>;
String _$signupNotifierHash() => r'98c1315715740df6d5c4dcd4698c68b3fc850df9';

/// AsyncNotifier for email/password signup operations
///
/// Copied from [SignupNotifier].
@ProviderFor(SignupNotifier)
final signupNotifierProvider =
    AutoDisposeAsyncNotifierProvider<SignupNotifier, void>.internal(
      SignupNotifier.new,
      name: r'signupNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$signupNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SignupNotifier = AutoDisposeAsyncNotifier<void>;
String _$googleSignInNotifierHash() =>
    r'9fbd9e99544235404146e6d3580c51e17be1edcc';

/// AsyncNotifier for Google sign-in operations
///
/// Copied from [GoogleSignInNotifier].
@ProviderFor(GoogleSignInNotifier)
final googleSignInNotifierProvider =
    AutoDisposeAsyncNotifierProvider<GoogleSignInNotifier, void>.internal(
      GoogleSignInNotifier.new,
      name: r'googleSignInNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$googleSignInNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$GoogleSignInNotifier = AutoDisposeAsyncNotifier<void>;
String _$passwordResetNotifierHash() =>
    r'fb189e30934be19cec33a4fc1e752dd8fe88a659';

/// AsyncNotifier for password reset operations
///
/// Copied from [PasswordResetNotifier].
@ProviderFor(PasswordResetNotifier)
final passwordResetNotifierProvider =
    AutoDisposeAsyncNotifierProvider<PasswordResetNotifier, void>.internal(
      PasswordResetNotifier.new,
      name: r'passwordResetNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$passwordResetNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PasswordResetNotifier = AutoDisposeAsyncNotifier<void>;
String _$emailVerificationNotifierHash() =>
    r'555f5754a026023b5f07780b1e6075634dcb7bbb';

/// AsyncNotifier for email verification operations
///
/// Copied from [EmailVerificationNotifier].
@ProviderFor(EmailVerificationNotifier)
final emailVerificationNotifierProvider =
    AutoDisposeAsyncNotifierProvider<EmailVerificationNotifier, void>.internal(
      EmailVerificationNotifier.new,
      name: r'emailVerificationNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$emailVerificationNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$EmailVerificationNotifier = AutoDisposeAsyncNotifier<void>;
String _$authErrorHandlerHash() => r'f140aea941b3e15a8e388bc056bab137a1809929';

/// Provider for handling authentication errors with user-friendly messages
///
/// Copied from [AuthErrorHandler].
@ProviderFor(AuthErrorHandler)
final authErrorHandlerProvider =
    AutoDisposeNotifierProvider<AuthErrorHandler, String?>.internal(
      AuthErrorHandler.new,
      name: r'authErrorHandlerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$authErrorHandlerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AuthErrorHandler = AutoDisposeNotifier<String?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
