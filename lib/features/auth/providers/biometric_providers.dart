import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/services/biometric_service.dart';
import 'package:budapp/providers/providers.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'biometric_providers.g.dart';

/// Provider for BiometricService instance
@riverpod
BiometricService biometricService(Ref ref) {
  return BiometricService();
}

/// Provider for checking biometric availability on the device
@riverpod
Future<BiometricAvailability> biometricAvailability(Ref ref) async {
  final biometricService = ref.watch(biometricServiceProvider);
  return biometricService.checkBiometricAvailability();
}

/// Provider for checking if biometric authentication is enabled for the current user
@riverpod
Future<bool> biometricPreference(Ref ref) async {
  final authService = ref.watch(authServiceProvider);
  return authService.isBiometricAuthEnabled();
}

/// Provider for checking if biometric authentication is both available and enabled
@riverpod
Future<bool> biometricAuthReady(Ref ref) async {
  final availability = await ref.watch(biometricAvailabilityProvider.future);
  final isEnabled = await ref.watch(biometricPreferenceProvider.future);

  return availability == BiometricAvailability.available && isEnabled;
}

/// State provider for biometric authentication loading state
final biometricAuthLoadingProvider = StateProvider<bool>((ref) => false);

/// State provider for biometric authentication error state
final biometricAuthErrorProvider = StateProvider<String?>((ref) => null);

/// Notifier for managing biometric authentication state
@riverpod
class BiometricAuthNotifier extends _$BiometricAuthNotifier {
  @override
  BiometricAuthState build() {
    return const BiometricAuthState.initial();
  }

  /// Perform biometric authentication
  Future<void> authenticate() async {
    state = const BiometricAuthState.loading();

    try {
      final authService = ref.read(authServiceProvider);
      final user = await authService.signInWithBiometrics();

      if (user != null) {
        state = const BiometricAuthState.success();
      } else {
        state = const BiometricAuthState.error('Authentication failed');
      }
    } on Exception catch (e) {
      state = BiometricAuthState.error(e.toString());
    }
  }

  /// Enable biometric authentication
  Future<void> enableBiometric() async {
    state = const BiometricAuthState.loading();

    try {
      final authService = ref.read(authServiceProvider);
      await authService.enableBiometricAuth();

      // Invalidate preference provider to refresh the state
      ref
        ..invalidate(biometricPreferenceProvider)
        ..invalidate(biometricAuthReadyProvider);

      state = const BiometricAuthState.success();
    } on Exception catch (e) {
      state = BiometricAuthState.error(e.toString());
    }
  }

  /// Disable biometric authentication
  Future<void> disableBiometric() async {
    state = const BiometricAuthState.loading();

    try {
      final authService = ref.read(authServiceProvider);
      await authService.disableBiometricAuth();

      // Invalidate preference provider to refresh the state
      ref
        ..invalidate(biometricPreferenceProvider)
        ..invalidate(biometricAuthReadyProvider);

      state = const BiometricAuthState.success();
    } on Exception catch (e) {
      state = BiometricAuthState.error(e.toString());
    }
  }

  /// Reset the authentication state
  void reset() {
    state = const BiometricAuthState.initial();
  }
}

/// State class for biometric authentication
sealed class BiometricAuthState {
  const BiometricAuthState();

  const factory BiometricAuthState.initial() = BiometricAuthInitial;
  const factory BiometricAuthState.loading() = BiometricAuthLoading;
  const factory BiometricAuthState.success() = BiometricAuthSuccess;
  const factory BiometricAuthState.error(String message) = BiometricAuthError;
}

class BiometricAuthInitial extends BiometricAuthState {
  const BiometricAuthInitial();
}

class BiometricAuthLoading extends BiometricAuthState {
  const BiometricAuthLoading();
}

class BiometricAuthSuccess extends BiometricAuthState {
  const BiometricAuthSuccess();
}

class BiometricAuthError extends BiometricAuthState {
  const BiometricAuthError(this.message);
  final String message;
}

/// Notifier that manages biometric gate state for router integration
///
/// This notifier extends ChangeNotifier to make routing reactive to biometric gate requirements.
/// It tracks whether biometric authentication is required before allowing access to the main app.
class BiometricGateStateNotifier extends ChangeNotifier {
  BiometricGateStateNotifier(this._ref) {
    // Listen to auth state changes and update gate requirement
    _authSubscription = _ref.listen<User?>(
      authStateProvider.select((asyncUser) => asyncUser.valueOrNull),
      (previous, current) {
        // Always reset session on auth state change, then update gate requirement
        resetSession();
        _updateGateRequirement();
      },
    );

    // Listen to biometric preference changes
    _biometricSubscription = _ref.listen<AsyncValue<bool>>(
      biometricPreferenceProvider,
      (_, _) {
        _updateGateRequirement();
      },
    );

    // Initial gate requirement check
    _updateGateRequirement();
  }

  final Ref _ref;
  ProviderSubscription<User?>? _authSubscription;
  ProviderSubscription<AsyncValue<bool>>? _biometricSubscription;

  bool _biometricGateRequired = false;
  bool _biometricCompletedInSession = false;

  /// Whether biometric gate is required before accessing the main app
  bool get biometricGateRequired => _biometricGateRequired;

  /// Current user from auth state
  User? get currentUser {
    final authState = _ref.read(authStateProvider);
    return authState.valueOrNull;
  }

  /// Mark biometric authentication as completed for this session
  void markBiometricCompleted() {
    _biometricCompletedInSession = true;
    _updateGateRequirement();
  }

  /// Reset biometric session state (called on sign out/in)
  void resetSession() {
    _biometricCompletedInSession = false;
    _updateGateRequirement();
  }

  /// Update the gate requirement based on current state
  void _updateGateRequirement() {
    final user = currentUser;
    var newRequirement = false;

    if (user != null && user.emailVerified && !_biometricCompletedInSession) {
      // Check if biometric auth is enabled (async, so we need to handle it carefully)
      final biometricPref = _ref.read(biometricPreferenceProvider);
      if (biometricPref.hasValue && (biometricPref.value ?? false)) {
        newRequirement = true;
      }
    }

    if (_biometricGateRequired != newRequirement) {
      _biometricGateRequired = newRequirement;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _authSubscription?.close();
    _biometricSubscription?.close();
    super.dispose();
  }
}

/// Provider for BiometricGateStateNotifier
final biometricGateStateNotifierProvider =
    ChangeNotifierProvider<BiometricGateStateNotifier>((ref) {
      return BiometricGateStateNotifier(ref);
    });
