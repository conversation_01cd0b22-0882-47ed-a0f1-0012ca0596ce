import 'package:budapp/features/auth/providers/biometric_providers.dart';
import 'package:budapp/features/auth/services/biometric_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// A settings tile for managing biometric authentication preferences
class BiometricSettingsTile extends ConsumerWidget {
  const BiometricSettingsTile({super.key, this.onChanged});
  final VoidCallback? onChanged;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final biometricAvailability = ref.watch(biometricAvailabilityProvider);
    final biometricPreference = ref.watch(biometricPreferenceProvider);
    final biometricState = ref.watch(biometricAuthNotifierProvider);

    return biometricAvailability.when(
      data: (availability) => _buildTile(
        context,
        ref,
        availability,
        biometricPreference,
        biometricState,
      ),
      loading: () => _buildLoadingTile(context),
      error: (error, _) => _buildErrorTile(context, error.toString()),
    );
  }

  Widget _buildTile(
    BuildContext context,
    WidgetRef ref,
    BiometricAvailability availability,
    AsyncValue<bool> preference,
    BiometricAuthState state,
  ) {
    final theme = Theme.of(context);
    final isLoading = state is BiometricAuthLoading;

    // Don't show the tile if biometrics are not supported
    if (availability == BiometricAvailability.notSupported) {
      return const SizedBox.shrink();
    }

    return preference.when(
      data: (isEnabled) => ListTile(
        leading: Icon(
          Icons.fingerprint,
          color: _getIconColor(context, availability, isEnabled),
        ),
        title: Text(
          'Biometric Authentication',
          style: theme.textTheme.titleMedium,
        ),
        subtitle: Text(_getSubtitleText(availability, isEnabled)),
        trailing: availability == BiometricAvailability.available
            ? Switch(
                value: isEnabled,
                onChanged: isLoading
                    ? null
                    : (value) => _handleToggle(ref, value),
              )
            : _getTrailingWidget(context, availability),
        enabled: availability == BiometricAvailability.available && !isLoading,
        onTap: availability == BiometricAvailability.available && !isLoading
            ? () => _handleToggle(ref, !isEnabled)
            : availability == BiometricAvailability.notEnrolled
            ? () => _showEnrollmentDialog(context)
            : null,
      ),
      loading: () => _buildLoadingTile(context),
      error: (error, _) => _buildErrorTile(context, error.toString()),
    );
  }

  Widget _buildLoadingTile(BuildContext context) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        Icons.fingerprint,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.38),
      ),
      title: Text(
        'Biometric Authentication',
        style: theme.textTheme.titleMedium,
      ),
      subtitle: const Text('Loading...'),
      trailing: const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }

  Widget _buildErrorTile(BuildContext context, String error) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(Icons.error_outline, color: theme.colorScheme.error),
      title: Text(
        'Biometric Authentication',
        style: theme.textTheme.titleMedium,
      ),
      subtitle: Text(
        'Error: $error',
        style: TextStyle(color: theme.colorScheme.error),
      ),
    );
  }

  Color _getIconColor(
    BuildContext context,
    BiometricAvailability availability,
    bool isEnabled,
  ) {
    final theme = Theme.of(context);

    switch (availability) {
      case BiometricAvailability.available:
        return isEnabled
            ? theme.colorScheme.primary
            : theme.colorScheme.onSurface;
      case BiometricAvailability.notEnrolled:
        return theme.colorScheme.onSurface.withValues(alpha: 0.6);
      case BiometricAvailability.notSupported:
        return theme.colorScheme.onSurface.withValues(alpha: 0.38);
    }
  }

  String _getSubtitleText(BiometricAvailability availability, bool isEnabled) {
    switch (availability) {
      case BiometricAvailability.available:
        return isEnabled
            ? 'Use fingerprint or face recognition to sign in'
            : 'Enable biometric authentication for quick access';
      case BiometricAvailability.notEnrolled:
        return 'No biometrics enrolled. Set up in device settings';
      case BiometricAvailability.notSupported:
        return 'Not supported on this device';
    }
  }

  Widget? _getTrailingWidget(
    BuildContext context,
    BiometricAvailability availability,
  ) {
    final theme = Theme.of(context);

    switch (availability) {
      case BiometricAvailability.notEnrolled:
        return TextButton(
          onPressed: () => _showEnrollmentDialog(context),
          child: const Text('Set up'),
        );
      case BiometricAvailability.notSupported:
        return Icon(
          Icons.block,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.38),
        );
      case BiometricAvailability.available:
        return null; // Switch is handled in the main tile
    }
  }

  Future<void> _handleToggle(WidgetRef ref, bool value) async {
    final notifier = ref.read(biometricAuthNotifierProvider.notifier);

    if (value) {
      await notifier.enableBiometric();
    } else {
      await notifier.disableBiometric();
    }

    final state = ref.read(biometricAuthNotifierProvider);

    if (state is BiometricAuthSuccess) {
      onChanged?.call();
    }
  }

  void _showEnrollmentDialog(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Set up biometric authentication'),
        content: const Text(
          'To use biometric authentication, you need to set up fingerprint or face recognition in your device settings first.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
