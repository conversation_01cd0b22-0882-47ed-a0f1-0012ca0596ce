import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:budapp/features/auth/services/biometric_error_service.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';

/// Error banner specifically designed for biometric authentication errors
class BiometricErrorBanner extends StatelessWidget {
  const BiometricErrorBanner({
    super.key,
    required this.error,
    this.onRetry,
    this.onDismiss,
    this.onSetupBiometrics,
    this.showActions = true,
  });

  final AuthError error;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final VoidCallback? onSetupBiometrics;
  final bool showActions;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;

    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.md),
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: _getBackgroundColor(theme),
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        border: Border.all(color: _getBorderColor(theme), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                BiometricErrorService.getErrorIcon(error.code),
                style: const TextStyle(fontSize: 20),
              ),
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getTitle(localizations),
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: AppTypography.fontWeightSemiBold,
                        color: _getTextColor(theme),
                      ),
                    ),
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      error.userMessage,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: _getTextColor(theme),
                      ),
                    ),
                  ],
                ),
              ),
              if (onDismiss != null)
                IconButton(
                  onPressed: onDismiss,
                  icon: Icon(
                    Icons.close,
                    color: _getTextColor(theme),
                    size: 20,
                  ),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                ),
            ],
          ),
          if (showActions && _shouldShowActions()) ...[
            const SizedBox(height: AppSpacing.md),
            _buildActionButtons(context, localizations),
          ],
        ],
      ),
    );
  }

  String _getTitle(AppLocalizations localizations) {
    switch (error.code) {
      case 'biometric_passcode_not_set':
        return 'Passcode Required';
      case 'biometric_not_enrolled':
        return localizations.biometricSetupRequired;
      case 'biometric_not_available':
        return 'Biometrics Unavailable';
      case 'biometric_locked_out':
      case 'biometric_permanently_locked_out':
      case 'biometric_too_many_attempts':
        return 'Biometrics Locked';
      default:
        return localizations.biometricAuthentication;
    }
  }

  Color _getBackgroundColor(ThemeData theme) {
    switch (error.severity) {
      case ErrorSeverity.error:
        return theme.colorScheme.errorContainer;
      case ErrorSeverity.warning:
        return theme.colorScheme.secondaryContainer;
      case ErrorSeverity.info:
        return theme.colorScheme.surfaceContainerHighest;
      case ErrorSeverity.critical:
        return theme.colorScheme.errorContainer;
    }
  }

  Color _getBorderColor(ThemeData theme) {
    switch (error.severity) {
      case ErrorSeverity.error:
        return theme.colorScheme.error;
      case ErrorSeverity.warning:
        return theme.colorScheme.secondary;
      case ErrorSeverity.info:
        return theme.colorScheme.outline;
      case ErrorSeverity.critical:
        return theme.colorScheme.error;
    }
  }

  Color _getTextColor(ThemeData theme) {
    switch (error.severity) {
      case ErrorSeverity.error:
        return theme.colorScheme.onErrorContainer;
      case ErrorSeverity.warning:
        return theme.colorScheme.onSecondaryContainer;
      case ErrorSeverity.info:
        return theme.colorScheme.onSurface;
      case ErrorSeverity.critical:
        return theme.colorScheme.onErrorContainer;
    }
  }

  bool _shouldShowActions() {
    return error.actionRequired != null || error.isRetryable;
  }

  Widget _buildActionButtons(
    BuildContext context,
    AppLocalizations localizations,
  ) {
    final actions = <Widget>[];

    // Add action-specific buttons
    final actionRequired = error.actionRequired;
    if (actionRequired != null) {
      switch (actionRequired) {
        case BiometricErrorAction.setupPasscode:
          actions.add(
            _ActionButton(
              text: localizations.goToSettings,
              onPressed: _openDeviceSettings,
              isPrimary: true,
            ),
          );
        case BiometricErrorAction.enrollBiometric:
          actions.add(
            _ActionButton(
              text: localizations.setupBiometrics,
              onPressed: onSetupBiometrics ?? _openDeviceSettings,
              isPrimary: true,
            ),
          );
        case BiometricErrorAction.useAlternativeAuth:
        // This would typically navigate to password login
        case BiometricErrorAction.waitAndRetry:
          if (onRetry != null) {
            actions.add(
              _ActionButton(
                text: 'Try Again Later',
                onPressed: onRetry,
                isPrimary: false,
              ),
            );
          }
      }
    }

    // Add retry button if retryable and no specific action
    if (error.isRetryable && actionRequired == null && onRetry != null) {
      actions.add(
        _ActionButton(text: 'Try Again', onPressed: onRetry, isPrimary: true),
      );
    }

    if (actions.isEmpty) return const SizedBox.shrink();

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: actions
          .map(
            (action) => Padding(
              padding: const EdgeInsets.only(left: AppSpacing.sm),
              child: action,
            ),
          )
          .toList(),
    );
  }

  Future<void> _openDeviceSettings() async {
    // This would open device settings - implementation depends on platform
    // For now, we'll just show a message
    debugPrint('Opening device settings for biometric setup');
  }
}

/// Internal action button widget for biometric error banner
class _ActionButton extends StatelessWidget {
  const _ActionButton({
    required this.text,
    required this.onPressed,
    this.isPrimary = false,
  });

  final String text;
  final VoidCallback? onPressed;
  final bool isPrimary;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (isPrimary) {
      return FilledButton(
        onPressed: onPressed,
        style: FilledButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.md,
            vertical: AppSpacing.sm,
          ),
          minimumSize: Size.zero,
        ),
        child: Text(text, style: theme.textTheme.labelMedium),
      );
    } else {
      return OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.md,
            vertical: AppSpacing.sm,
          ),
          minimumSize: Size.zero,
        ),
        child: Text(text, style: theme.textTheme.labelMedium),
      );
    }
  }
}
