import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Reusable error banner component for authentication screens
///
/// Provides consistent error display with:
/// - Severity-based styling
/// - Suggested actions
/// - Retry functionality
/// - Accessibility support
class AuthErrorBanner extends ConsumerWidget {
  const AuthErrorBanner({
    super.key,
    required this.error,
    this.context,
    this.onRetry,
    this.onDismiss,
    this.showSuggestedActions = true,
    this.showRetryButton = true,
  });
  final AuthError error;
  final String? context;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final bool showSuggestedActions;
  final bool showRetryButton;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Get severity-based colors
    final (backgroundColor, foregroundColor, iconColor) = _getSeverityColors(
      colorScheme,
    );

    // Get contextual message
    final message = this.context != null
        ? AuthErrorService.getContextualMessage(error, this.context!)
        : error.userMessage;

    // Get suggested actions
    final suggestedActions = showSuggestedActions
        ? AuthErrorService.getSuggestedActions(error)
        : <String>[];

    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.md),
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        border: Border.all(color: iconColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Error header
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(_getSeverityIcon(), color: iconColor, size: 20),
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getSeverityTitle(context),
                      style: theme.textTheme.titleSmall?.copyWith(
                        color: foregroundColor,
                        fontWeight: AppTypography.fontWeightBold,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      message,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: foregroundColor,
                      ),
                    ),
                  ],
                ),
              ),
              if (onDismiss != null)
                IconButton(
                  icon: Icon(
                    Icons.close,
                    color: foregroundColor.withValues(alpha: 0.7),
                    size: 18,
                  ),
                  onPressed: onDismiss,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                ),
            ],
          ),

          // Suggested actions
          if (suggestedActions.isNotEmpty) ...[
            const SizedBox(height: AppSpacing.sm),
            Text(
              AppLocalizations.of(context)!.suggestions,
              style: theme.textTheme.labelMedium?.copyWith(
                color: foregroundColor.withValues(alpha: 0.8),
                fontWeight: AppTypography.fontWeightMedium,
              ),
            ),
            const SizedBox(height: AppSpacing.xs),
            ...suggestedActions.map(
              (action) => Padding(
                padding: const EdgeInsets.only(left: AppSpacing.sm),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '• ',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: foregroundColor.withValues(alpha: 0.7),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        action,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: foregroundColor.withValues(alpha: 0.7),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],

          // Retry button
          if (showRetryButton && error.isRetryable && onRetry != null) ...[
            const SizedBox(height: AppSpacing.md),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: onRetry,
                icon: Icon(Icons.refresh, size: 16, color: iconColor),
                label: Text(
                  AppLocalizations.of(context)!.tryAgain,
                  style: TextStyle(color: iconColor),
                ),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: iconColor.withValues(alpha: 0.5)),
                  backgroundColor: Colors.transparent,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  (Color, Color, Color) _getSeverityColors(ColorScheme colorScheme) {
    switch (error.severity) {
      case ErrorSeverity.info:
        return (
          colorScheme.primaryContainer,
          colorScheme.onPrimaryContainer,
          colorScheme.primary,
        );
      case ErrorSeverity.warning:
        return (
          colorScheme.tertiaryContainer,
          colorScheme.onTertiaryContainer,
          colorScheme.tertiary,
        );
      case ErrorSeverity.error:
        return (
          colorScheme.errorContainer,
          colorScheme.onErrorContainer,
          colorScheme.error,
        );
      case ErrorSeverity.critical:
        return (
          colorScheme.errorContainer,
          colorScheme.onErrorContainer,
          colorScheme.error,
        );
    }
  }

  IconData _getSeverityIcon() {
    switch (error.severity) {
      case ErrorSeverity.info:
        return Icons.info_outline;
      case ErrorSeverity.warning:
        return Icons.warning_amber_outlined;
      case ErrorSeverity.error:
        return Icons.error_outline;
      case ErrorSeverity.critical:
        return Icons.dangerous_outlined;
    }
  }

  String _getSeverityTitle(BuildContext context) {
    switch (error.severity) {
      case ErrorSeverity.info:
        return AppLocalizations.of(context)!.information;
      case ErrorSeverity.warning:
        return AppLocalizations.of(context)!.warning;
      case ErrorSeverity.error:
        return AppLocalizations.of(context)!.error;
      case ErrorSeverity.critical:
        return AppLocalizations.of(context)!.criticalError;
    }
  }
}

/// Utility class for showing error banners via SnackBar
// ignore: avoid_classes_with_only_static_members
class AuthErrorSnackBar {
  /// Show error as SnackBar with consistent styling
  static void show(
    BuildContext context,
    AuthError error, {
    String? contextName,
    VoidCallback? onRetry,
  }) {
    final theme = Theme.of(context);
    final message = contextName != null
        ? AuthErrorService.getContextualMessage(error, contextName)
        : error.userMessage;

    // Get severity-based colors
    Color backgroundColor;
    Color foregroundColor;

    switch (error.severity) {
      case ErrorSeverity.info:
        backgroundColor = theme.colorScheme.primaryContainer;
        foregroundColor = theme.colorScheme.onPrimaryContainer;
      case ErrorSeverity.warning:
        backgroundColor = theme.colorScheme.tertiaryContainer;
        foregroundColor = theme.colorScheme.onTertiaryContainer;
      case ErrorSeverity.error:
      case ErrorSeverity.critical:
        backgroundColor = theme.colorScheme.errorContainer;
        foregroundColor = theme.colorScheme.onErrorContainer;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              _getSnackBarIcon(error.severity),
              color: foregroundColor,
              size: 20,
            ),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: Text(message, style: TextStyle(color: foregroundColor)),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        behavior: SnackBarBehavior.floating,
        duration: Duration(
          seconds: error.severity == ErrorSeverity.critical ? 6 : 4,
        ),
        action: error.isRetryable && onRetry != null
            ? SnackBarAction(
                label: 'Retry',
                textColor: foregroundColor,
                onPressed: onRetry,
              )
            : null,
      ),
    );
  }

  static IconData _getSnackBarIcon(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.info:
        return Icons.info_outline;
      case ErrorSeverity.warning:
        return Icons.warning_amber_outlined;
      case ErrorSeverity.error:
        return Icons.error_outline;
      case ErrorSeverity.critical:
        return Icons.dangerous_outlined;
    }
  }
}
