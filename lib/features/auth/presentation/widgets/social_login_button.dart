import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Social login button for authentication screens
class SocialLoginButton extends ConsumerWidget {
  const SocialLoginButton({
    super.key,
    required this.text,
    required this.icon,
    this.onPressed,
    this.isLoading = false,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
  });
  final String text;
  final IconData icon;
  final VoidCallback? onPressed;
  final bool isLoading;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? iconColor;

  /// Google Sign-In button with Google branding
  static Widget google({
    required BuildContext context,
    required VoidCallback? onPressed,
    bool isLoading = false,
  }) {
    final theme = Theme.of(context);
    return SocialLoginButton(
      text: AppLocalizations.of(context)!.continueWithGoogle,
      icon: Icons
          .g_mobiledata, // Using built-in icon, can be replaced with custom
      onPressed: onPressed,
      isLoading: isLoading,
      backgroundColor: theme.colorScheme.surface,
      textColor: theme.colorScheme.onSurface,
      iconColor: const Color(0xFF4285F4), // Keep Google brand blue
    );
  }

  /// Apple Sign-In button with Apple branding
  static Widget apple({
    required BuildContext context,
    required VoidCallback? onPressed,
    bool isLoading = false,
  }) {
    final theme = Theme.of(context);
    return SocialLoginButton(
      text: AppLocalizations.of(context)!.continueWithApple,
      icon: Icons.apple,
      onPressed: onPressed,
      isLoading: isLoading,
      backgroundColor: theme.colorScheme.inverseSurface,
      textColor: theme.colorScheme.onInverseSurface,
      iconColor: theme.colorScheme.onInverseSurface,
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    // Adjust colors for dark theme
    final effectiveBackgroundColor =
        backgroundColor ?? theme.colorScheme.surface;
    final effectiveTextColor = textColor ?? theme.colorScheme.onSurface;
    final effectiveIconColor = iconColor ?? effectiveTextColor;

    return SizedBox(
      width: double.infinity,
      height: 48,
      child: OutlinedButton(
        onPressed: isLoading ? null : onPressed,
        style: OutlinedButton.styleFrom(
          backgroundColor: effectiveBackgroundColor,
          side: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.5),
            width: 1,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.md,
            vertical: AppSpacing.sm,
          ),
        ),
        child: isLoading
            ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(effectiveTextColor),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(icon, color: effectiveIconColor, size: 20),
                  const SizedBox(width: AppSpacing.sm),
                  Text(
                    text,
                    style: theme.textTheme.labelLarge?.copyWith(
                      color: effectiveTextColor,
                      fontWeight: AppTypography.fontWeightMedium,
                      fontSize: AppTypography.fontSizeMd,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}

/// Divider with "OR" text for separating authentication methods
class AuthDivider extends ConsumerWidget {
  const AuthDivider({super.key, this.text});
  final String? text;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final dividerText = text ?? AppLocalizations.of(context)!.or;

    return Row(
      children: [
        Expanded(
          child: Divider(
            color: theme.colorScheme.outline.withValues(alpha: 0.5),
            thickness: 1,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
          child: Text(
            dividerText,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontWeight: AppTypography.fontWeightMedium,
            ),
          ),
        ),
        Expanded(
          child: Divider(
            color: theme.colorScheme.outline.withValues(alpha: 0.5),
            thickness: 1,
          ),
        ),
      ],
    );
  }
}
