import 'package:budapp/features/auth/providers/biometric_providers.dart';
import 'package:budapp/features/auth/services/biometric_service.dart'
    hide BiometricAuthError;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// A button widget for biometric authentication
class BiometricAuthButton extends ConsumerWidget {
  const BiometricAuthButton({
    super.key,
    this.onSuccess,
    this.onError,
    this.customText,
    this.enabled = true,
  });
  final VoidCallback? onSuccess;
  final VoidCallback? onError;
  final String? customText;
  final bool enabled;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final biometricAvailability = ref.watch(biometricAvailabilityProvider);
    final biometricReady = ref.watch(biometricAuthReadyProvider);
    final biometricState = ref.watch(biometricAuthNotifierProvider);

    return biometricAvailability.when(
      data: (availability) {
        if (availability != BiometricAvailability.available) {
          return const SizedBox.shrink();
        }

        return biometricReady.when(
          data: (isReady) {
            if (!isReady) {
              return const SizedBox.shrink();
            }

            return _buildButton(context, ref, biometricState);
          },
          loading: () => const SizedBox.shrink(),
          error: (_, _) => const SizedBox.shrink(),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, _) => const SizedBox.shrink(),
    );
  }

  Widget _buildButton(
    BuildContext context,
    WidgetRef ref,
    BiometricAuthState state,
  ) {
    final isLoading = state is BiometricAuthLoading;
    final theme = Theme.of(context);

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: OutlinedButton.icon(
        onPressed: enabled && !isLoading ? () => _handleTap(ref) : null,
        icon: isLoading
            ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
              )
            : _getBiometricIcon(context),
        label: Text(
          customText ?? _getButtonText(context),
          style: theme.textTheme.titleMedium?.copyWith(
            color: enabled && !isLoading
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurface.withValues(alpha: 0.38),
          ),
        ),
        style: OutlinedButton.styleFrom(
          side: BorderSide(
            color: enabled && !isLoading
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurface.withValues(alpha: 0.12),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _getBiometricIcon(BuildContext context) {
    final theme = Theme.of(context);

    // For now, we'll use a fingerprint icon as default
    // In a real implementation, you might want to check the actual biometric type
    return Icon(
      Icons.fingerprint,
      color: enabled
          ? theme.colorScheme.primary
          : theme.colorScheme.onSurface.withValues(alpha: 0.38),
    );
  }

  String _getButtonText(BuildContext context) {
    return 'Sign in with biometrics';
  }

  Future<void> _handleTap(WidgetRef ref) async {
    final notifier = ref.read(biometricAuthNotifierProvider.notifier);

    await notifier.authenticate();

    final state = ref.read(biometricAuthNotifierProvider);

    if (state is BiometricAuthSuccess) {
      onSuccess?.call();
    } else if (state is BiometricAuthError) {
      onError?.call();
    }
  }
}

/// A simplified biometric icon button for compact layouts
class BiometricIconButton extends ConsumerWidget {
  const BiometricIconButton({
    super.key,
    this.onSuccess,
    this.onError,
    this.size,
  });
  final VoidCallback? onSuccess;
  final VoidCallback? onError;
  final double? size;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final biometricAvailability = ref.watch(biometricAvailabilityProvider);
    final biometricReady = ref.watch(biometricAuthReadyProvider);
    final biometricState = ref.watch(biometricAuthNotifierProvider);

    return biometricAvailability.when(
      data: (availability) {
        if (availability != BiometricAvailability.available) {
          return const SizedBox.shrink();
        }

        return biometricReady.when(
          data: (isReady) {
            if (!isReady) {
              return const SizedBox.shrink();
            }

            return _buildIconButton(context, ref, biometricState);
          },
          loading: () => const SizedBox.shrink(),
          error: (_, _) => const SizedBox.shrink(),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, _) => const SizedBox.shrink(),
    );
  }

  Widget _buildIconButton(
    BuildContext context,
    WidgetRef ref,
    BiometricAuthState state,
  ) {
    final isLoading = state is BiometricAuthLoading;
    final theme = Theme.of(context);
    final iconSize = size ?? 24.0;

    return IconButton(
      onPressed: !isLoading ? () => _handleTap(ref) : null,
      icon: isLoading
          ? SizedBox(
              width: iconSize,
              height: iconSize,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              ),
            )
          : Icon(
              Icons.fingerprint,
              size: iconSize,
              color: theme.colorScheme.primary,
            ),
      tooltip: 'Sign in with biometrics',
    );
  }

  Future<void> _handleTap(WidgetRef ref) async {
    final notifier = ref.read(biometricAuthNotifierProvider.notifier);

    await notifier.authenticate();

    final state = ref.read(biometricAuthNotifierProvider);

    if (state is BiometricAuthSuccess) {
      onSuccess?.call();
    } else if (state is BiometricAuthError) {
      onError?.call();
    }
  }
}
