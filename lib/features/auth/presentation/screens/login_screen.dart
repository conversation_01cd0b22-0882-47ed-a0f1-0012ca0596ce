import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/auth/presentation/widgets/auth_button.dart';
import 'package:budapp/features/auth/presentation/widgets/auth_error_banner.dart';
import 'package:budapp/features/auth/presentation/widgets/auth_form_field.dart';
import 'package:budapp/features/auth/presentation/widgets/biometric_auth_button.dart';
import 'package:budapp/features/auth/presentation/widgets/biometric_error_banner.dart';
import 'package:budapp/features/auth/presentation/widgets/social_login_button.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/providers/biometric_providers.dart';
import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:budapp/features/auth/services/biometric_error_service.dart'
    hide BiometricAuthError;
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/routing/app_router.dart';
import 'package:budapp/widgets/performance_tracked_screen.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Login screen with email/password and social authentication options
class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  // Loading states are now managed by Riverpod providers

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _handleEmailLogin() async {
    if (!_formKey.currentState!.validate()) return;

    // Use the new AsyncNotifier for login with enhanced error handling
    final loginNotifier = ref.read(loginNotifierProvider.notifier);
    await loginNotifier.signInWithEmail(
      _emailController.text.trim(),
      _passwordController.text.trim(),
    );

    // Show success message if login was successful
    final loginState = ref.read(loginNotifierProvider);
    if (mounted && loginState.hasValue) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.welcomeBackSuccess),
          backgroundColor: AppColors.success,
        ),
      );
    }
  }

  Future<void> _handleGoogleLogin() async {
    // Use the new AsyncNotifier for Google sign-in with enhanced error handling
    final googleSignInNotifier = ref.read(
      googleSignInNotifierProvider.notifier,
    );
    await googleSignInNotifier.signInWithGoogle();

    // Show success message if sign-in was successful
    final googleSignInState = ref.read(googleSignInNotifierProvider);
    if (mounted && googleSignInState.hasValue) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.googleSignInSuccess),
          backgroundColor: AppColors.success,
        ),
      );
    }
  }

  Future<void> _handleAppleLogin() async {
    // Set loading state via Riverpod (using a placeholder provider for now)
    ref.read(globalLoadingProvider.notifier).state = true;

    try {
      // TODO(budapp): Implement Apple Sign-In
      await Future<void>.delayed(
        const Duration(seconds: 2),
      ); // Simulate API call

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.appleLoginSuccessful),
          ),
        );
      }
    } on Exception catch (e) {
      if (mounted) {
        final authService = ref.read(authServiceProvider);
        final authError = authService.handleAuthError(e);
        AuthErrorSnackBar.show(
          context,
          authError,
          contextName: 'login',
          onRetry: authError.isRetryable ? _handleAppleLogin : null,
        );
      }
    } finally {
      if (mounted) {
        // Clear loading state via Riverpod
        ref.read(globalLoadingProvider.notifier).state = false;
      }
    }
  }

  void _navigateToSignup() {
    context.push(AppRoutes.signup);
  }

  void _handleForgotPassword() {
    context.push(AppRoutes.forgotPassword);
  }

  void _showBiometricSetupDialog() {
    final localizations = AppLocalizations.of(context)!;
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.biometricSetupRequired),
        content: Text(localizations.biometricSetupMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.cancel),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO(budapp): Open device settings for biometric setup
            },
            child: Text(localizations.goToSettings),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: AppSpacing.xxl),

              // App Logo and Title
              Center(
                child: Column(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        borderRadius: BorderRadius.circular(AppBorderRadius.xl),
                      ),
                      child: Icon(
                        Icons.account_balance_wallet,
                        size: 40,
                        color: theme.colorScheme.onPrimary,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.md),
                    Text(
                      AppLocalizations.of(context)!.welcomeBack,
                      style: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: AppTypography.fontWeightBold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.sm),
                    Text(
                      AppLocalizations.of(context)!.signInToAccount,
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppSpacing.xxl),

              // Error display for AsyncValue states using AuthErrorBanner
              Consumer(
                builder: (context, ref, child) {
                  final loginState = ref.watch(loginNotifierProvider);
                  final googleSignInState = ref.watch(
                    googleSignInNotifierProvider,
                  );
                  final biometricState = ref.watch(
                    biometricAuthNotifierProvider,
                  );

                  // Handle biometric errors first (they have specialized UI)
                  if (biometricState is BiometricAuthError) {
                    final biometricError =
                        BiometricErrorService.handleBiometricError(
                          Exception(biometricState.message),
                          AppLocalizations.of(context)!,
                        );
                    return BiometricErrorBanner(
                      error: biometricError,
                      onRetry: biometricError.isRetryable
                          ? () async {
                              final biometricNotifier = ref.read(
                                biometricAuthNotifierProvider.notifier,
                              );
                              await biometricNotifier.authenticate();
                            }
                          : null,
                      onDismiss: () {
                        ref.invalidate(biometricAuthNotifierProvider);
                      },
                      onSetupBiometrics: _showBiometricSetupDialog,
                    );
                  }

                  // Show error if any other authentication operation failed
                  final error = loginState.error ?? googleSignInState.error;
                  if (error != null) {
                    // Convert Firebase exceptions to AuthError for consistent handling
                    AuthError authError;
                    if (error is FirebaseAuthException) {
                      authError = AuthErrorService.handleFirebaseAuthException(
                        error,
                      );
                    } else {
                      // Create generic AuthError for non-Firebase errors
                      authError = AuthError(
                        code: 'unknown_error',
                        userMessage: AppLocalizations.of(
                          context,
                        )!.unexpectedError,
                        technicalMessage: error.toString(),
                        severity: ErrorSeverity.error,
                        category: ErrorCategory.system,
                        isRetryable: true,
                      );
                    }

                    return AuthErrorBanner(
                      error: authError,
                      context: 'login',
                      onRetry: authError.isRetryable
                          ? () {
                              // Clear error states by invalidating providers
                              ref
                                ..invalidate(loginNotifierProvider)
                                ..invalidate(googleSignInNotifierProvider);
                            }
                          : null,
                      onDismiss: () {
                        // Clear error states by invalidating providers
                        ref
                          ..invalidate(loginNotifierProvider)
                          ..invalidate(googleSignInNotifierProvider);
                      },
                    );
                  }

                  return const SizedBox.shrink();
                },
              ),

              // Login Form
              Form(
                key: _formKey,
                child: Column(
                  children: [
                    AuthFormField(
                      label: AppLocalizations.of(context)!.email,
                      hintText: AppLocalizations.of(context)!.enterEmailAddress,
                      controller: _emailController,
                      validator: (value) =>
                          AuthValidators.email(value, context),
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.next,
                      prefixIcon: Icon(
                        Icons.email_outlined,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      autofocus: true,
                    ),

                    const SizedBox(height: AppSpacing.lg),

                    AuthFormField(
                      label: AppLocalizations.of(context)!.password,
                      hintText: AppLocalizations.of(context)!.enterPassword,
                      controller: _passwordController,
                      validator: (value) =>
                          AuthValidators.password(value, context),
                      isPassword: true,
                      textInputAction: TextInputAction.done,
                      onFieldSubmitted: (_) => _handleEmailLogin(),
                      prefixIcon: Icon(
                        Icons.lock_outline,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),

                    const SizedBox(height: AppSpacing.md),

                    // Forgot Password
                    Align(
                      alignment: Alignment.centerRight,
                      child: AuthTextButton(
                        text: AppLocalizations.of(context)!.forgotPassword,
                        onPressed: _handleForgotPassword,
                      ),
                    ),

                    const SizedBox(height: AppSpacing.xl),

                    // Login Button with AsyncValue loading state
                    Consumer(
                      builder: (context, ref, child) {
                        final loginState = ref.watch(loginNotifierProvider);
                        final isLoading = loginState.isLoading;
                        return AuthButton(
                          text: AppLocalizations.of(context)!.signIn,
                          onPressed: isLoading ? null : _handleEmailLogin,
                          isLoading: isLoading,
                        );
                      },
                    ),

                    const SizedBox(height: AppSpacing.lg),

                    // Biometric Authentication Button
                    BiometricAuthButton(
                      onSuccess: () {
                        // Show success message
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              AppLocalizations.of(context)!.welcomeBackSuccess,
                            ),
                            backgroundColor: AppColors.success,
                          ),
                        );
                      },
                      onError: () {
                        // Error handling is managed by the BiometricAuthNotifier
                        // and displayed through the error banner above
                      },
                    ),

                    const SizedBox(height: AppSpacing.lg),

                    // Divider
                    const AuthDivider(),

                    const SizedBox(height: AppSpacing.lg),

                    // Social Login Buttons with AsyncValue loading state
                    Consumer(
                      builder: (context, ref, child) {
                        final googleSignInState = ref.watch(
                          googleSignInNotifierProvider,
                        );
                        final isLoading = googleSignInState.isLoading;
                        return SocialLoginButton.google(
                          context: context,
                          onPressed: isLoading ? null : _handleGoogleLogin,
                          isLoading: isLoading,
                        );
                      },
                    ),

                    const SizedBox(height: AppSpacing.md),

                    Consumer(
                      builder: (context, ref, child) {
                        final isLoading = ref.watch(globalLoadingProvider);
                        return SocialLoginButton.apple(
                          context: context,
                          onPressed: _handleAppleLogin,
                          isLoading: isLoading,
                        );
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppSpacing.xl),

              // Sign Up Link
              Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.dontHaveAccount,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    AuthTextButton(
                      text: AppLocalizations.of(context)!.signUp,
                      onPressed: _navigateToSignup,
                      isUnderlined: true,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ).trackPerformance('login_screen');
  }
}
