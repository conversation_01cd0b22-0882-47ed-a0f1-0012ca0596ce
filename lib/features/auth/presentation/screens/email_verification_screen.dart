import 'dart:async';

import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/auth/presentation/widgets/auth_button.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Email verification screen for users who need to verify their email
class EmailVerificationScreen extends ConsumerStatefulWidget {
  const EmailVerificationScreen({super.key});

  @override
  ConsumerState<EmailVerificationScreen> createState() =>
      _EmailVerificationScreenState();
}

class _EmailVerificationScreenState
    extends ConsumerState<EmailVerificationScreen> {
  DateTime? _lastResendTime;
  Timer? _verificationTimer;

  // Constants for email verification timing
  static const int _kVerificationCheckIntervalSeconds = 3;
  static const int _kResendRateLimitSeconds = 60;
  static const Duration _kSnackBarDuration = Duration(seconds: 4);

  @override
  void initState() {
    super.initState();
    // Check verification status periodically
    _startVerificationCheck();
  }

  @override
  void dispose() {
    _verificationTimer?.cancel();
    super.dispose();
  }

  void _startVerificationCheck() {
    _verificationTimer?.cancel();
    _verificationTimer = Timer.periodic(
      const Duration(seconds: _kVerificationCheckIntervalSeconds),
      (timer) {
        if (mounted) {
          _checkEmailVerification();
        } else {
          timer.cancel();
        }
      },
    );
  }

  Future<void> _checkEmailVerification() async {
    await ref.read(emailVerificationNotifierProvider.notifier).reloadUser();
  }

  Future<void> _resendVerificationEmail() async {
    // Check if we can resend (rate limiting)
    if (_lastResendTime != null) {
      final timeSinceLastResend = DateTime.now().difference(_lastResendTime!);
      if (timeSinceLastResend.inSeconds < _kResendRateLimitSeconds) {
        final remainingSeconds =
            _kResendRateLimitSeconds - timeSinceLastResend.inSeconds;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Please wait $remainingSeconds seconds before resending.',
            ),
          ),
        );
        return;
      }
    }

    await ref
        .read(emailVerificationNotifierProvider.notifier)
        .sendEmailVerification();
    _lastResendTime = DateTime.now();
  }

  Future<void> _signOut() async {
    try {
      final authService = ref.read(authServiceProvider);
      await authService.signOut();
    } on Exception catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sign out failed: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authService = ref.read(authServiceProvider);
    final user = authService.currentUser;
    final emailVerificationState = ref.watch(emailVerificationNotifierProvider);

    // Listen for state changes to show success/error messages
    ref.listen<AsyncValue<void>>(emailVerificationNotifierProvider, (
      previous,
      next,
    ) {
      next.when(
        data: (_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Verification email sent! Please check your inbox.',
                ),
                backgroundColor: AppColors.success,
                duration: _kSnackBarDuration,
              ),
            );
          }
        },
        error: (error, stackTrace) {
          if (mounted) {
            // Process error through AuthErrorService for user-friendly messages
            String errorMessage;
            if (error is FirebaseAuthException) {
              final authError = AuthErrorService.handleFirebaseAuthException(
                error,
              );
              errorMessage = authError.userMessage;
            } else {
              // Fallback for non-Firebase errors
              errorMessage = 'An unexpected error occurred. Please try again.';
            }

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(errorMessage),
                backgroundColor: AppColors.error,
                duration: _kSnackBarDuration,
              ),
            );
          }
        },
        loading: () {
          // Loading state is handled by the button
        },
      );
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Verify Email'),
        automaticallyImplyLeading: false,
        actions: [
          TextButton(
            onPressed: _signOut,
            child: Text(
              'Sign Out',
              style: TextStyle(color: theme.colorScheme.primary),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight:
                    MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom -
                    (AppSpacing.lg * 2) -
                    kToolbarHeight,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Email verification icon
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primaryContainer,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.mark_email_unread_outlined,
                      size: 60,
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                  ),

                  const SizedBox(height: AppSpacing.xl),

                  // Title
                  Text(
                    AppLocalizations.of(context)!.verifyYourEmail,
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: AppTypography.fontWeightBold,
                      color: theme.colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: AppSpacing.md),

                  // Description
                  Text(
                    AppLocalizations.of(context)!.verificationEmailSentTo,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: AppSpacing.sm),

                  // Email address
                  Text(
                    user?.email ?? 'your email',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: AppTypography.fontWeightMedium,
                      color: theme.colorScheme.primary,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: AppSpacing.lg),

                  // Instructions
                  Text(
                    AppLocalizations.of(context)!.checkEmailForVerification,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: AppSpacing.xl),

                  // Resend button
                  AuthButton(
                    text: AppLocalizations.of(context)!.resendVerificationEmail,
                    onPressed: _resendVerificationEmail,
                    isLoading: emailVerificationState.isLoading,
                    isSecondary: true,
                  ),

                  const SizedBox(height: AppSpacing.md),

                  // Check verification button
                  AuthButton(
                    text: AppLocalizations.of(context)!.verifiedMyEmail,
                    onPressed: _checkEmailVerification,
                    isLoading: false,
                  ),

                  const SizedBox(height: AppSpacing.lg),

                  // Help text
                  Text(
                    "Didn't receive the email? Check your spam folder or try resending.",
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
