import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/auth/presentation/widgets/biometric_error_banner.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/providers/biometric_providers.dart';
import 'package:budapp/features/auth/services/biometric_error_service.dart'
    as error_service;
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Screen that prompts for biometric authentication on app startup
///
/// This screen appears when:
/// - User is authenticated and email verified
/// - User has biometric authentication enabled
/// - User hasn't completed biometric auth in current session
class BiometricGateScreen extends ConsumerStatefulWidget {
  const BiometricGateScreen({super.key});

  @override
  ConsumerState<BiometricGateScreen> createState() =>
      _BiometricGateScreenState();
}

class _BiometricGateScreenState extends ConsumerState<BiometricGateScreen> {
  bool _isAuthenticating = false;

  @override
  void initState() {
    super.initState();
    // Automatically trigger biometric authentication when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _performBiometricAuthentication();
    });
  }

  Future<void> _performBiometricAuthentication() async {
    if (_isAuthenticating) return;

    setState(() {
      _isAuthenticating = true;
    });

    try {
      final biometricNotifier = ref.read(
        biometricAuthNotifierProvider.notifier,
      );
      await biometricNotifier.authenticate();

      // Check if authentication was successful
      final biometricState = ref.read(biometricAuthNotifierProvider);
      if (biometricState is BiometricAuthSuccess) {
        // Mark biometric as completed in session
        ref
            .read(biometricGateStateNotifierProvider.notifier)
            .markBiometricCompleted();

        // Navigate to home screen
        if (mounted) {
          context.go(AppRoutes.home);
        }
      }
    } on Exception catch (e) {
      debugPrint('Biometric authentication error: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isAuthenticating = false;
        });
      }
    }
  }

  void _handleRetry() {
    // Reset biometric state and try again
    ref.invalidate(biometricAuthNotifierProvider);
    _performBiometricAuthentication();
  }

  void _handleGoToSettings() {
    // Navigate to authentication settings
    context.push('/profile/auth-settings');
  }

  Future<void> _handleSignOut() async {
    try {
      final authService = ref.read(authServiceProvider);
      await authService.signOut();
      // Router will automatically redirect to login screen
    } on Exception catch (e) {
      debugPrint('Sign out error: $e');
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to sign out: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final biometricState = ref.watch(biometricAuthNotifierProvider);

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App Logo/Icon
              Icon(
                Icons.fingerprint,
                size: 80,
                color: theme.colorScheme.primary,
              ),

              const SizedBox(height: AppSpacing.xl),

              // Title
              Text(
                l10n.biometricAuthRequired,
                style: theme.textTheme.headlineMedium?.copyWith(
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppSpacing.md),

              // Description
              Text(
                l10n.biometricAuthGateDescription,
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppSpacing.xl),

              // Error Banner (if any)
              if (biometricState is BiometricAuthError) ...[
                BiometricErrorBanner(
                  error:
                      error_service.BiometricErrorService.handleBiometricError(
                        Exception(biometricState.message),
                        l10n,
                      ),
                  onRetry: _handleRetry,
                  onDismiss: () {
                    ref.read(biometricAuthNotifierProvider.notifier).reset();
                  },
                  onSetupBiometrics: _handleGoToSettings,
                ),
                const SizedBox(height: AppSpacing.lg),
              ],

              // Loading indicator or action buttons
              if (_isAuthenticating ||
                  biometricState is BiometricAuthLoading) ...[
                const CircularProgressIndicator(),
                const SizedBox(height: AppSpacing.md),
                Text(
                  l10n.authenticating,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ] else ...[
                // Retry Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _handleRetry,
                    icon: const Icon(Icons.fingerprint),
                    label: Text(l10n.tryAgain),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        vertical: AppSpacing.md,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: AppSpacing.md),

                // Settings Button
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: _handleGoToSettings,
                    icon: const Icon(Icons.settings),
                    label: Text(l10n.authenticationSettings),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        vertical: AppSpacing.md,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: AppSpacing.lg),

                // Sign Out Button
                TextButton(
                  onPressed: _handleSignOut,
                  child: Text(
                    l10n.signOut,
                    style: TextStyle(color: theme.colorScheme.error),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
