import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/auth/presentation/widgets/biometric_error_banner.dart';
import 'package:budapp/features/auth/presentation/widgets/biometric_settings_tile.dart';
import 'package:budapp/features/auth/providers/biometric_providers.dart';
import 'package:budapp/features/auth/services/biometric_error_service.dart'
    hide BiometricAuthError;
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Screen for managing authentication settings including biometric authentication
class AuthSettingsScreen extends ConsumerWidget {
  const AuthSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localizations = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.authenticationSettings),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Error display for biometric operations
            Consumer(
              builder: (context, ref, child) {
                final biometricState = ref.watch(biometricAuthNotifierProvider);

                if (biometricState is BiometricAuthError) {
                  final biometricError =
                      BiometricErrorService.handleBiometricError(
                        Exception(biometricState.message),
                        localizations,
                      );
                  return BiometricErrorBanner(
                    error: biometricError,
                    onRetry: biometricError.isRetryable
                        ? () async {
                            final biometricNotifier = ref.read(
                              biometricAuthNotifierProvider.notifier,
                            );
                            await biometricNotifier.authenticate();
                          }
                        : null,
                    onDismiss: () {
                      ref.invalidate(biometricAuthNotifierProvider);
                    },
                    onSetupBiometrics: () {
                      _showBiometricSetupDialog(context, localizations);
                    },
                  );
                }

                return const SizedBox.shrink();
              },
            ),

            // Security Settings Section
            Text(
              localizations.securitySettings,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: AppTypography.fontWeightSemiBold,
                color: theme.colorScheme.onSurface,
              ),
            ),

            const SizedBox(height: AppSpacing.sm),

            Text(
              localizations.biometricSettingsDescription,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),

            const SizedBox(height: AppSpacing.lg),

            // Biometric Authentication Settings
            Card(
              elevation: 0,
              color: theme.colorScheme.surfaceContainerLow,
              child: Padding(
                padding: const EdgeInsets.all(AppSpacing.sm),
                child: Column(
                  children: [
                    BiometricSettingsTile(
                      onChanged: () {
                        // Refresh the UI when biometric settings change
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppSpacing.xl),

            // Additional Security Options (Future Implementation)
            Text(
              'Additional Security',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: AppTypography.fontWeightSemiBold,
                color: theme.colorScheme.onSurface,
              ),
            ),

            const SizedBox(height: AppSpacing.sm),

            Text(
              'More security options will be available in future updates.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),

            const SizedBox(height: AppSpacing.lg),

            Card(
              elevation: 0,
              color: theme.colorScheme.surfaceContainerLow,
              child: Padding(
                padding: const EdgeInsets.all(AppSpacing.sm),
                child: Column(
                  children: [
                    ListTile(
                      leading: Icon(
                        Icons.lock_outline,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      title: const Text('Two-Factor Authentication'),
                      subtitle: const Text('Coming soon'),
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      enabled: false,
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: Icon(
                        Icons.security,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      title: const Text('Security Questions'),
                      subtitle: const Text('Coming soon'),
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      enabled: false,
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: Icon(
                        Icons.devices,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      title: const Text('Trusted Devices'),
                      subtitle: const Text('Coming soon'),
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      enabled: false,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showBiometricSetupDialog(
    BuildContext context,
    AppLocalizations localizations,
  ) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.biometricSetupRequired),
        content: Text(localizations.biometricSetupMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.cancel),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO(budapp): Open device settings for biometric setup
              // This would typically use url_launcher or platform channels
              // to open the device's biometric settings
            },
            child: Text(localizations.goToSettings),
          ),
        ],
      ),
    );
  }
}
