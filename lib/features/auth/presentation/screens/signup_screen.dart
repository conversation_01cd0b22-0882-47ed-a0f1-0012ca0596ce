import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/auth/presentation/widgets/auth_button.dart';
import 'package:budapp/features/auth/presentation/widgets/auth_error_banner.dart';
import 'package:budapp/features/auth/presentation/widgets/auth_form_field.dart';
import 'package:budapp/features/auth/presentation/widgets/social_login_button.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/providers.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Signup screen with email/password registration and social authentication options
class SignupScreen extends ConsumerStatefulWidget {
  const SignupScreen({super.key});

  @override
  ConsumerState<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends ConsumerState<SignupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _acceptTerms = false;

  // Constants for UI timing
  static const Duration _kSuccessSnackBarDuration = Duration(seconds: 3);

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _handleEmailSignup() async {
    if (!_formKey.currentState!.validate()) return;

    if (!_acceptTerms) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context)!.pleaseAcceptTermsAndPrivacy,
            ),
          ),
        );
      }
      return;
    }

    // Use the AsyncNotifier for signup with enhanced error handling
    final signupNotifier = ref.read(signupNotifierProvider.notifier);
    await signupNotifier.signUpWithEmail(
      _emailController.text.trim(),
      _passwordController.text,
    );

    // Show success message if signup was successful and send email verification
    final signupState = ref.read(signupNotifierProvider);
    if (mounted && signupState.hasValue) {
      // Send email verification - the user is already signed in after signup
      final user = ref.read(currentUserProvider);
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context)!.accountCreatedSuccessfully,
            ),
            backgroundColor: AppColors.success,
            duration: _kSuccessSnackBarDuration,
          ),
        );
      }

      // AuthWrapper will automatically show EmailVerificationScreen
      // No need to navigate manually
    }
  }

  Future<void> _handleGoogleSignup() async {
    // Use the AsyncNotifier for Google sign-in with enhanced error handling
    final googleSignInNotifier = ref.read(
      googleSignInNotifierProvider.notifier,
    );
    await googleSignInNotifier.signInWithGoogle();

    // Show success message if sign-in was successful
    final googleSignInState = ref.read(googleSignInNotifierProvider);
    if (mounted && googleSignInState.hasValue) {
      final user = ref.read(currentUserProvider);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context)!.welcomeToApp(
                user?.displayName ??
                    user?.email ??
                    AppLocalizations.of(context)!.budApp,
              ),
            ),
            backgroundColor: AppColors.success,
          ),
        );
      }
    }
  }

  Future<void> _handleAppleSignup() async {
    // Set loading state via Riverpod (using global loading for now)
    ref.read(globalLoadingProvider.notifier).state = true;

    try {
      // TODO(budapp): Implement Apple Sign-In
      await Future<void>.delayed(
        const Duration(seconds: 2),
      ); // Simulate API call

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.appleSignupSuccessful),
          ),
        );
      }
    } on Exception catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context)!.appleSignupFailed(e.toString()),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        // Clear loading state via Riverpod
        ref.read(globalLoadingProvider.notifier).state = false;
      }
    }
  }

  void _navigateToLogin() {
    context.pop();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: _navigateToLogin,
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: AppSpacing.lg),

              // Title and Subtitle
              Center(
                child: Column(
                  children: [
                    Text(
                      AppLocalizations.of(context)!.createAccount,
                      style: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: AppTypography.fontWeightBold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.sm),
                    Text(
                      AppLocalizations.of(context)!.signUpToGetStarted,
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppSpacing.xxl),

              // Error display for AsyncValue states using AuthErrorBanner
              Consumer(
                builder: (context, ref, child) {
                  final signupState = ref.watch(signupNotifierProvider);
                  final googleSignInState = ref.watch(
                    googleSignInNotifierProvider,
                  );

                  // Show error if any authentication operation failed
                  final error = signupState.error ?? googleSignInState.error;
                  if (error != null) {
                    // Convert Firebase exceptions to AuthError for consistent handling
                    AuthError authError;
                    if (error is FirebaseAuthException) {
                      authError = AuthErrorService.handleFirebaseAuthException(
                        error,
                      );
                    } else {
                      // Create generic AuthError for non-Firebase errors
                      authError = AuthError(
                        code: 'unknown_error',
                        userMessage: AppLocalizations.of(
                          context,
                        )!.unexpectedError,
                        technicalMessage: error.toString(),
                        severity: ErrorSeverity.error,
                        category: ErrorCategory.system,
                        isRetryable: true,
                      );
                    }

                    return AuthErrorBanner(
                      error: authError,
                      context: 'signup',
                      onRetry: authError.isRetryable
                          ? () {
                              // Clear error states by invalidating providers
                              ref
                                ..invalidate(signupNotifierProvider)
                                ..invalidate(googleSignInNotifierProvider);
                            }
                          : null,
                      onDismiss: () {
                        // Clear error states by invalidating providers
                        ref
                          ..invalidate(signupNotifierProvider)
                          ..invalidate(googleSignInNotifierProvider);
                      },
                    );
                  }

                  return const SizedBox.shrink();
                },
              ),

              // Signup Form
              Form(
                key: _formKey,
                child: Column(
                  children: [
                    AuthFormField(
                      label: AppLocalizations.of(context)!.email,
                      hintText: AppLocalizations.of(context)!.enterYourEmail,
                      controller: _emailController,
                      validator: (value) =>
                          AuthValidators.email(value, context),
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.next,
                      prefixIcon: Icon(
                        Icons.email_outlined,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      autofocus: true,
                    ),

                    const SizedBox(height: AppSpacing.lg),

                    AuthFormField(
                      label: AppLocalizations.of(context)!.password,
                      hintText: AppLocalizations.of(context)!.createPassword,
                      controller: _passwordController,
                      validator: (value) =>
                          AuthValidators.password(value, context),
                      isPassword: true,
                      textInputAction: TextInputAction.next,
                      prefixIcon: Icon(
                        Icons.lock_outline,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),

                    const SizedBox(height: AppSpacing.lg),

                    AuthFormField(
                      label: AppLocalizations.of(context)!.confirmPassword,
                      hintText: AppLocalizations.of(
                        context,
                      )!.confirmYourPassword,
                      controller: _confirmPasswordController,
                      validator: (value) => AuthValidators.confirmPassword(
                        value,
                        _passwordController.text,
                        context,
                      ),
                      isPassword: true,
                      textInputAction: TextInputAction.done,
                      onFieldSubmitted: (_) => _handleEmailSignup(),
                      prefixIcon: Icon(
                        Icons.lock_outline,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),

                    const SizedBox(height: AppSpacing.lg),

                    // Terms and Conditions Checkbox
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Checkbox(
                          value: _acceptTerms,
                          onChanged: (value) {
                            setState(() {
                              _acceptTerms = value ?? false;
                            });
                          },
                          activeColor: theme.colorScheme.primary,
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(top: AppSpacing.sm),
                            child: RichText(
                              text: TextSpan(
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                                children: [
                                  TextSpan(
                                    text: AppLocalizations.of(
                                      context,
                                    )!.iAgreeToThe,
                                  ),
                                  TextSpan(
                                    text: AppLocalizations.of(
                                      context,
                                    )!.termsOfService,
                                    style: TextStyle(
                                      color: theme.colorScheme.primary,
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                  TextSpan(
                                    text: AppLocalizations.of(context)!.and,
                                  ),
                                  TextSpan(
                                    text: AppLocalizations.of(
                                      context,
                                    )!.privacyPolicy,
                                    style: TextStyle(
                                      color: theme.colorScheme.primary,
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppSpacing.xl),

                    // Signup Button with AsyncValue loading state
                    Consumer(
                      builder: (context, ref, child) {
                        final signupState = ref.watch(signupNotifierProvider);
                        final isLoading = signupState.isLoading;
                        return AuthButton(
                          text: AppLocalizations.of(context)!.createAccount,
                          onPressed: isLoading ? null : _handleEmailSignup,
                          isLoading: isLoading,
                        );
                      },
                    ),

                    const SizedBox(height: AppSpacing.lg),

                    // Divider
                    const AuthDivider(),

                    const SizedBox(height: AppSpacing.lg),

                    // Social Signup Buttons with AsyncValue loading state
                    Consumer(
                      builder: (context, ref, child) {
                        final googleSignInState = ref.watch(
                          googleSignInNotifierProvider,
                        );
                        final isLoading = googleSignInState.isLoading;
                        return SocialLoginButton.google(
                          context: context,
                          onPressed: isLoading ? null : _handleGoogleSignup,
                          isLoading: isLoading,
                        );
                      },
                    ),

                    const SizedBox(height: AppSpacing.md),

                    Consumer(
                      builder: (context, ref, child) {
                        final isLoading = ref.watch(globalLoadingProvider);
                        return SocialLoginButton.apple(
                          context: context,
                          onPressed: _handleAppleSignup,
                          isLoading: isLoading,
                        );
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppSpacing.xl),

              // Sign In Link
              Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.alreadyHaveAccount,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    AuthTextButton(
                      text: AppLocalizations.of(context)!.signIn,
                      onPressed: _navigateToLogin,
                      isUnderlined: true,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
