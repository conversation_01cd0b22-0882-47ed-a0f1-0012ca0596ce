import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/auth/presentation/widgets/auth_button.dart';
import 'package:budapp/features/auth/presentation/widgets/auth_error_banner.dart';
import 'package:budapp/features/auth/presentation/widgets/auth_form_field.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Forgot password screen for password reset functionality
class ForgotPasswordScreen extends ConsumerStatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  ConsumerState<ForgotPasswordScreen> createState() =>
      _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends ConsumerState<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();

  bool _emailSent = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  Future<void> _handlePasswordReset() async {
    if (!_formKey.currentState!.validate()) return;

    final email = _emailController.text.trim();
    await ref
        .read(passwordResetNotifierProvider.notifier)
        .sendPasswordResetEmail(email);
  }

  void _navigateBack() {
    context.pop();
  }

  void _resendEmail() {
    setState(() {
      _emailSent = false;
    });
    _handlePasswordReset();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final passwordResetState = ref.watch(passwordResetNotifierProvider);

    // Listen for state changes to show success/error messages
    ref.listen<AsyncValue<void>>(passwordResetNotifierProvider, (
      previous,
      next,
    ) {
      next.when(
        data: (_) {
          if (mounted) {
            setState(() {
              _emailSent = true;
            });

            // Create success AuthError for consistent display
            final successError = AuthError(
              code: 'password_reset_sent',
              userMessage: AppLocalizations.of(
                context,
              )!.passwordResetEmailSentSuccess,
              technicalMessage: 'Password reset email sent successfully',
              severity: ErrorSeverity.info,
              category: ErrorCategory.user,
              isRetryable: false,
            );

            AuthErrorSnackBar.show(
              context,
              successError,
              contextName: 'forgot_password',
            );
          }
        },
        error: (error, stackTrace) {
          if (mounted) {
            // Convert Firebase exceptions to AuthError for consistent handling
            AuthError authError;
            if (error is FirebaseAuthException) {
              authError = AuthErrorService.handleFirebaseAuthException(error);
            } else {
              // Create generic AuthError for non-Firebase errors
              authError = AuthError(
                code: 'unknown_error',
                userMessage: AppLocalizations.of(context)!.unexpectedError,
                technicalMessage: error.toString(),
                severity: ErrorSeverity.error,
                category: ErrorCategory.system,
                isRetryable: true,
              );
            }

            AuthErrorSnackBar.show(
              context,
              authError,
              contextName: 'forgot_password',
              onRetry: authError.isRetryable ? _handlePasswordReset : null,
            );
          }
        },
        loading: () {
          // Loading state is handled by the button
        },
      );
    });

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: _navigateBack,
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: AppSpacing.xl),

              // Header
              Center(
                child: Column(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppBorderRadius.xl),
                      ),
                      child: Icon(
                        Icons.lock_reset,
                        size: 40,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.md),
                    Text(
                      _emailSent
                          ? AppLocalizations.of(context)!.checkYourEmail
                          : AppLocalizations.of(context)!.resetPassword,
                      style: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: AppTypography.fontWeightBold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.sm),
                    Text(
                      _emailSent
                          ? AppLocalizations.of(context)!.passwordResetEmailSent
                          : AppLocalizations.of(context)!.enterEmailForReset,
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppSpacing.xxl),

              if (!_emailSent) ...[
                // Email Form
                Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      AuthFormField(
                        label: 'Email',
                        hintText: 'Enter your email address',
                        controller: _emailController,
                        validator: (value) =>
                            AuthValidators.email(value, context),
                        keyboardType: TextInputType.emailAddress,
                        textInputAction: TextInputAction.done,
                        onFieldSubmitted: (_) => _handlePasswordReset(),
                        prefixIcon: Icon(
                          Icons.email_outlined,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        autofocus: true,
                      ),

                      const SizedBox(height: AppSpacing.xl),

                      // Reset Password Button
                      AuthButton(
                        text: 'Send Reset Link',
                        onPressed: _handlePasswordReset,
                        isLoading: passwordResetState.isLoading,
                      ),
                    ],
                  ),
                ),
              ] else ...[
                // Success State
                Container(
                  padding: const EdgeInsets.all(AppSpacing.lg),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer.withValues(
                      alpha: 0.3,
                    ),
                    borderRadius: BorderRadius.circular(AppBorderRadius.md),
                    border: Border.all(
                      color: theme.colorScheme.primary.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.mark_email_read,
                        size: 48,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(height: AppSpacing.md),
                      Text(
                        'Email Sent Successfully!',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: AppTypography.fontWeightSemiBold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: AppSpacing.sm),
                      Text(
                        'Please check your email (${_emailController.text.trim()}) and click the reset link to create a new password.',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppSpacing.md),
                      Text(
                        "Don't forget to check your spam folder if you don't see the email.",
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontStyle: FontStyle.italic,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: AppSpacing.xl),

                // Resend Button
                AuthButton(
                  text: 'Resend Email',
                  onPressed: _resendEmail,
                  isSecondary: true,
                ),
              ],

              const SizedBox(height: AppSpacing.xl),

              // Back to Login
              Center(
                child: TextButton(
                  onPressed: _navigateBack,
                  child: Text(
                    'Back to Sign In',
                    style: TextStyle(
                      decoration: TextDecoration.underline,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
