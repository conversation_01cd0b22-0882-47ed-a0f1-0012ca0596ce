import 'package:budapp/data/repositories/interfaces/repositories.dart';
import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:budapp/features/auth/services/biometric_service.dart';
import 'package:budapp/services/performance_service.dart';
import 'package:budapp/services/secure_storage_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';

/// Service class to handle authentication operations
///
/// This service provides:
/// - Email/password authentication
/// - Google Sign-In authentication
/// - Password reset functionality
/// - User management operations
/// - Automatic user profile management via repository
class AuthService {
  /// Create an AuthService instance with injected dependencies
  AuthService({
    required FirebaseAuth auth,
    required GoogleSignIn googleSignIn,
    required IUserRepository userRepository,
    required BiometricService biometricService,
    required SecureStorageService secureStorage,
  }) : _auth = auth,
       _googleSignIn = googleSignIn,
       _userRepository = userRepository,
       _biometricService = biometricService,
       _secureStorage = secureStorage;
  final FirebaseAuth _auth;
  final GoogleSignIn _googleSignIn;
  final IUserRepository _userRepository;
  final BiometricService _biometricService;
  final SecureStorageService _secureStorage;

  /// Sign in with email and password
  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    await PerformanceService.trackAuthFlow('email_signin');
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Automatically create or update user profile
      if (credential.user != null) {
        await _userRepository.createOrUpdateFromFirebaseUser(credential.user!);
      }

      await PerformanceService.completeAuthFlow('email_signin');
      return credential;
    } on FirebaseAuthException catch (e) {
      if (kDebugMode) {
        debugPrint('Email/password sign-in error: ${e.code} - ${e.message}');
      }
      await PerformanceService.completeAuthFlow('email_signin');
      rethrow;
    } on Exception catch (e) {
      if (kDebugMode) {
        debugPrint('Unexpected sign-in error: $e');
      }
      await PerformanceService.completeAuthFlow('email_signin');
      rethrow;
    }
  }

  /// Create account with email and password
  Future<UserCredential> createUserWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Automatically create user profile
      if (credential.user != null) {
        await _userRepository.createOrUpdateFromFirebaseUser(credential.user!);
      }

      return credential;
    } on FirebaseAuthException catch (e) {
      if (kDebugMode) {
        debugPrint('Account creation error: ${e.code} - ${e.message}');
      }
      rethrow;
    } on Exception catch (e) {
      if (kDebugMode) {
        debugPrint('Unexpected account creation error: $e');
      }
      rethrow;
    }
  }

  /// Sign in with Google using Google Sign-In 7.x API
  Future<UserCredential> signInWithGoogle() async {
    await PerformanceService.trackAuthFlow('google_signin');
    try {
      // Initialize Google Sign-In if needed
      await _googleSignIn.initialize();

      // Authenticate with Google using 7.x API
      final googleAccount = await _googleSignIn.authenticate(
        scopeHint: [], // Empty scopes for basic authentication
      );

      // Get the authentication details from the request
      final googleAuth = googleAccount.authentication;

      // Get access token using authorization client
      final authClient = _googleSignIn.authorizationClient;
      final authorization = await authClient.authorizationForScopes([
        'email',
        'profile',
      ]);
      final accessToken = authorization?.accessToken;

      if (accessToken == null) {
        throw FirebaseAuthException(
          code: 'google_auth_failed',
          message: 'Failed to get access token from Google',
        );
      }

      // Create a new credential using the tokens
      final credential = GoogleAuthProvider.credential(
        accessToken: accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google credential
      final userCredential = await _auth.signInWithCredential(credential);

      // Automatically create or update user profile
      if (userCredential.user != null) {
        await _userRepository.createOrUpdateFromFirebaseUser(
          userCredential.user!,
        );
      }

      await PerformanceService.completeAuthFlow('google_signin');
      return userCredential;
    } on GoogleSignInException catch (e) {
      if (kDebugMode) {
        debugPrint('Google sign-in error: ${e.code.name} - ${e.description}');
      }
      await PerformanceService.completeAuthFlow('google_signin');

      // Convert GoogleSignInException to FirebaseAuthException for consistency
      throw FirebaseAuthException(
        code: e.code.name,
        message: e.description ?? 'Google sign-in failed',
      );
    } on FirebaseAuthException catch (e) {
      if (kDebugMode) {
        debugPrint('Google sign-in Firebase error: ${e.code} - ${e.message}');
      }
      await PerformanceService.completeAuthFlow('google_signin');
      rethrow;
    } on Exception catch (e) {
      if (kDebugMode) {
        debugPrint('Google sign-in unexpected error: $e');
      }
      await PerformanceService.completeAuthFlow('google_signin');
      throw FirebaseAuthException(
        code: 'google_sign_in_failed',
        message: 'Google sign-in failed: $e',
      );
    }
  }

  /// Sign up with Google (same as sign in for Google)
  Future<UserCredential> signUpWithGoogle() async {
    // Google Sign-In automatically creates an account if one doesn't exist
    return signInWithGoogle();
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail({required String email}) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      if (kDebugMode) {
        debugPrint('Password reset email sent successfully');
      }
    } on FirebaseAuthException catch (e) {
      if (kDebugMode) {
        debugPrint('Password reset error: ${e.code} - ${e.message}');
      }
      rethrow;
    } on Exception catch (e) {
      if (kDebugMode) {
        debugPrint('Unexpected password reset error: $e');
      }
      rethrow;
    }
  }

  /// Send email verification
  Future<void> sendEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
        if (kDebugMode) {
          debugPrint('Email verification sent successfully');
        }
      } else {
        throw FirebaseAuthException(
          code: 'no_user_or_verified',
          message: 'No user signed in or email already verified.',
        );
      }
    } on FirebaseAuthException catch (e) {
      if (kDebugMode) {
        debugPrint('Email verification error: ${e.code} - ${e.message}');
      }
      rethrow;
    } on Exception catch (e) {
      if (kDebugMode) {
        debugPrint('Unexpected email verification error: $e');
      }
      rethrow;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      // Always try to sign out from Google (it's safe to call even if not signed in)
      await _googleSignIn.signOut();
      if (kDebugMode) {
        debugPrint('Google sign-out successful');
      }

      // Sign out from Firebase
      await _auth.signOut();
      if (kDebugMode) {
        debugPrint('Firebase sign-out successful');
      }
    } on Exception catch (e) {
      if (kDebugMode) {
        debugPrint('Sign-out error: $e');
      }
      rethrow;
    }
  }

  /// Get current user
  User? get currentUser => _auth.currentUser;

  /// Check if user is signed in
  bool get isSignedIn => currentUser != null;

  /// Check if current user's email is verified
  bool get isEmailVerified => currentUser?.emailVerified ?? false;

  /// Get authentication state stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  /// Reload current user to get updated information
  Future<void> reloadUser() async {
    try {
      await currentUser?.reload();
      if (kDebugMode) {
        debugPrint('User data reloaded');
      }
    } on Exception catch (e) {
      if (kDebugMode) {
        debugPrint('Error reloading user: $e');
      }
      rethrow;
    }
  }

  /// Delete current user account
  Future<void> deleteAccount() async {
    try {
      final user = currentUser;
      if (user != null) {
        // Delete user profile from Firestore first
        await _userRepository.deleteUserProfile(user.uid);

        // Always try to sign out from Google (it's safe to call even if not signed in)
        await _googleSignIn.signOut();

        // Delete the Firebase Auth user
        await user.delete();
        if (kDebugMode) {
          debugPrint('User account deleted successfully');
        }
      } else {
        throw FirebaseAuthException(
          code: 'no_user',
          message: 'No user signed in to delete.',
        );
      }
    } on FirebaseAuthException catch (e) {
      if (kDebugMode) {
        debugPrint('Account deletion error: ${e.code} - ${e.message}');
      }
      rethrow;
    } on Exception catch (e) {
      if (kDebugMode) {
        debugPrint('Unexpected account deletion error: $e');
      }
      rethrow;
    }
  }

  /// Delete account with re-authentication
  Future<void> deleteAccountWithReauth(String password) async {
    try {
      // Re-authenticate first
      await reauthenticate(password);

      // Then delete the account
      await deleteAccount();
    } on Exception catch (e) {
      if (kDebugMode) {
        debugPrint('Account deletion with reauth error: $e');
      }
      rethrow;
    }
  }

  /// Update user display name
  Future<void> updateDisplayName(String displayName) async {
    try {
      await currentUser?.updateDisplayName(displayName);
      await reloadUser();
      if (kDebugMode) {
        debugPrint('Display name updated successfully');
      }
    } on Exception catch (e) {
      if (kDebugMode) {
        debugPrint('Error updating display name: $e');
      }
      rethrow;
    }
  }

  /// Update user email
  Future<void> updateEmail(String newEmail) async {
    try {
      await currentUser?.verifyBeforeUpdateEmail(newEmail);
      await reloadUser();
      if (kDebugMode) {
        debugPrint('Email verification sent for update');
      }
    } on Exception catch (e) {
      if (kDebugMode) {
        debugPrint('Error updating email: $e');
      }
      rethrow;
    }
  }

  /// Update user password
  Future<void> updatePassword(String newPassword) async {
    try {
      await currentUser?.updatePassword(newPassword);
      if (kDebugMode) {
        debugPrint('Password updated successfully');
      }
    } on Exception catch (e) {
      if (kDebugMode) {
        debugPrint('Error updating password: $e');
      }
      rethrow;
    }
  }

  /// Reauthenticate user with email and password
  Future<void> reauthenticateWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = EmailAuthProvider.credential(
        email: email,
        password: password,
      );
      await currentUser?.reauthenticateWithCredential(credential);
      if (kDebugMode) {
        debugPrint('Reauthentication successful');
      }
    } on Exception catch (e) {
      if (kDebugMode) {
        debugPrint('Reauthentication error: $e');
      }
      rethrow;
    }
  }

  /// Reauthenticate user with current password (simplified method)
  Future<void> reauthenticate(String password) async {
    final user = currentUser;
    if (user?.email == null) {
      throw FirebaseAuthException(
        code: 'no_user_email',
        message: 'No user email found for reauthentication.',
      );
    }

    await reauthenticateWithEmailAndPassword(
      email: user!.email!,
      password: password,
    );
  }

  /// Reauthenticate user with Google using Google Sign-In 7.x API
  Future<void> reauthenticateWithGoogle() async {
    try {
      // Initialize Google Sign-In if needed
      await _googleSignIn.initialize();

      // Authenticate with Google using 7.x API
      final googleAccount = await _googleSignIn.authenticate(
        scopeHint: [], // Empty scopes for basic authentication
      );

      // Get the authentication details from the request
      final googleAuth = googleAccount.authentication;

      // Get access token using authorization client
      final authClient = _googleSignIn.authorizationClient;
      final authorization = await authClient.authorizationForScopes([
        'email',
        'profile',
      ]);
      final accessToken = authorization?.accessToken;

      if (accessToken == null) {
        throw FirebaseAuthException(
          code: 'google_auth_failed',
          message: 'Failed to get access token from Google',
        );
      }

      // Create a new credential using the tokens
      final credential = GoogleAuthProvider.credential(
        accessToken: accessToken,
        idToken: googleAuth.idToken,
      );

      // Reauthenticate the current user with the Google credential
      await currentUser?.reauthenticateWithCredential(credential);

      if (kDebugMode) {
        debugPrint('Google reauthentication successful');
      }
    } on GoogleSignInException catch (e) {
      if (kDebugMode) {
        debugPrint(
          'Google reauthentication error: ${e.code.name} - ${e.description}',
        );
      }

      // Convert GoogleSignInException to FirebaseAuthException for consistency
      throw FirebaseAuthException(
        code: e.code.name,
        message: e.description ?? 'Google reauthentication failed',
      );
    } on Exception catch (e) {
      if (kDebugMode) {
        debugPrint('Google reauthentication error: $e');
      }
      rethrow;
    }
  }

  /// Get user provider data (to check which providers are linked)
  List<String> getUserProviders() {
    final user = currentUser;
    if (user == null) return [];

    return user.providerData.map((info) => info.providerId).toList();
  }

  /// Check if user signed in with Google
  bool get isSignedInWithGoogle {
    return getUserProviders().contains('google.com');
  }

  /// Check if user signed in with email/password
  bool get isSignedInWithEmailPassword {
    return getUserProviders().contains('password');
  }

  /// Check if biometric authentication is available on the device
  Future<bool> isBiometricAuthAvailable() async {
    final availability = await _biometricService.checkBiometricAvailability();
    return availability == BiometricAvailability.available;
  }

  /// Check if biometric authentication is enabled for the current user
  Future<bool> isBiometricAuthEnabled() async {
    if (currentUser == null) return false;
    return _secureStorage.getBiometricEnabled();
  }

  /// Sign in using biometric authentication
  /// This method requires that the user has previously signed in and enabled biometric auth
  Future<User?> signInWithBiometrics() async {
    try {
      // Check if biometric auth is available and enabled
      final isAvailable = await isBiometricAuthAvailable();
      if (!isAvailable) {
        throw Exception('Biometric authentication is not available');
      }

      final isEnabled = await isBiometricAuthEnabled();
      if (!isEnabled) {
        throw Exception('Biometric authentication is not enabled');
      }

      // Perform biometric authentication
      final authResult = await _biometricService.authenticate(
        localizedReason: 'Please authenticate to access your account',
        biometricOnly: true,
      );

      if (!authResult.isSuccess) {
        throw Exception(
          authResult.errorMessage ?? 'Biometric authentication failed',
        );
      }

      // If biometric auth succeeds, check if user is already signed in
      if (currentUser != null) {
        // User is already authenticated, return current user
        return currentUser;
      }

      // If no current user, this means we need to restore the session
      // For now, we'll throw an error as this requires additional session management
      throw Exception(
        'No active session found. Please sign in with email/password first.',
      );
    } on Exception catch (e) {
      if (kDebugMode) {
        debugPrint('Biometric sign-in error: $e');
      }
      rethrow;
    }
  }

  /// Enable biometric authentication for the current user
  Future<void> enableBiometricAuth() async {
    if (currentUser == null) {
      throw Exception('No user is currently signed in');
    }

    final isAvailable = await isBiometricAuthAvailable();
    if (!isAvailable) {
      throw Exception(
        'Biometric authentication is not available on this device',
      );
    }

    // Test biometric authentication before enabling
    final authResult = await _biometricService.authenticate(
      localizedReason: 'Please authenticate to enable biometric sign-in',
      biometricOnly: true,
    );

    if (!authResult.isSuccess) {
      throw Exception(
        authResult.errorMessage ?? 'Biometric authentication failed',
      );
    }

    // Save biometric preference
    await _secureStorage.setBiometricEnabled(enabled: true);
  }

  /// Disable biometric authentication for the current user
  Future<void> disableBiometricAuth() async {
    await _secureStorage.setBiometricEnabled(enabled: false);
  }

  /// Convert Firebase Auth exception to user-friendly error
  AuthError handleAuthError(dynamic error) {
    if (error is FirebaseAuthException) {
      return AuthErrorService.handleFirebaseAuthException(error);
    } else {
      return AuthErrorService.handleGeneralException(error);
    }
  }

  /// Get contextual error message for specific authentication flow
  String getContextualErrorMessage(dynamic error, String context) {
    final authError = handleAuthError(error);
    return AuthErrorService.getContextualMessage(authError, context);
  }
}
