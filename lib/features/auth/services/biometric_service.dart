import 'package:flutter/services.dart';
import 'package:local_auth/error_codes.dart' as auth_error;
import 'package:local_auth/local_auth.dart';

/// Service for handling biometric authentication operations
class BiometricService {
  final LocalAuthentication _localAuth = LocalAuthentication();

  /// Check if biometric authentication is available on the device
  Future<bool> canCheckBiometrics() async {
    try {
      return _localAuth.canCheckBiometrics;
    } on Exception {
      return false;
    }
  }

  /// Check if the device supports biometric authentication
  Future<bool> isDeviceSupported() async {
    try {
      return _localAuth.isDeviceSupported();
    } on Exception {
      return false;
    }
  }

  /// Get the list of available biometric types on the device
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return _localAuth.getAvailableBiometrics();
    } on Exception {
      return [];
    }
  }

  /// Check if biometric authentication is available and configured
  Future<BiometricAvailability> checkBiometricAvailability() async {
    try {
      final isSupported = await isDeviceSupported();
      if (!isSupported) {
        return BiometricAvailability.notSupported;
      }

      final canCheck = await canCheckBiometrics();
      if (!canCheck) {
        return BiometricAvailability.notEnrolled;
      }

      final availableBiometrics = await getAvailableBiometrics();
      if (availableBiometrics.isEmpty) {
        return BiometricAvailability.notEnrolled;
      }

      return BiometricAvailability.available;
    } on Exception {
      return BiometricAvailability.notSupported;
    }
  }

  /// Authenticate using biometric authentication
  Future<BiometricAuthResult> authenticate({
    String localizedReason = 'Please authenticate to access your account',
    bool biometricOnly = false,
    bool stickyAuth = false,
  }) async {
    try {
      final availability = await checkBiometricAvailability();
      if (availability != BiometricAvailability.available) {
        return BiometricAuthResult.failure(
          BiometricAuthError.notAvailable,
          _getErrorMessage(availability),
        );
      }

      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: localizedReason,
        options: AuthenticationOptions(
          biometricOnly: biometricOnly,
          stickyAuth: stickyAuth,
        ),
      );

      if (isAuthenticated) {
        return BiometricAuthResult.success();
      } else {
        return BiometricAuthResult.failure(
          BiometricAuthError.userCancel,
          'Authentication was cancelled',
        );
      }
    } on Exception catch (e) {
      return _handleAuthenticationError(e);
    }
  }

  /// Handle authentication errors and convert them to user-friendly results
  BiometricAuthResult _handleAuthenticationError(dynamic error) {
    if (error is PlatformException) {
      switch (error.code) {
        case auth_error.notAvailable:
          return BiometricAuthResult.failure(
            BiometricAuthError.notAvailable,
            'Biometric authentication is not available on this device',
          );
        case auth_error.notEnrolled:
          return BiometricAuthResult.failure(
            BiometricAuthError.notEnrolled,
            'No biometrics are enrolled on this device',
          );
        case auth_error.lockedOut:
          return BiometricAuthResult.failure(
            BiometricAuthError.lockedOut,
            'Biometric authentication is temporarily locked. Please try again later',
          );
        case auth_error.permanentlyLockedOut:
          return BiometricAuthResult.failure(
            BiometricAuthError.permanentlyLockedOut,
            'Biometric authentication is permanently locked. Please use device settings to unlock',
          );
        case auth_error.biometricOnlyNotSupported:
          return BiometricAuthResult.failure(
            BiometricAuthError.notSupported,
            'Biometric-only authentication is not supported on this device',
          );
        default:
          return BiometricAuthResult.failure(
            BiometricAuthError.unknown,
            error.message ?? 'An unknown error occurred during authentication',
          );
      }
    }

    return BiometricAuthResult.failure(
      BiometricAuthError.unknown,
      'An unexpected error occurred during authentication',
    );
  }

  /// Get error message for biometric availability status
  String _getErrorMessage(BiometricAvailability availability) {
    switch (availability) {
      case BiometricAvailability.notSupported:
        return 'Biometric authentication is not supported on this device';
      case BiometricAvailability.notEnrolled:
        return 'No biometrics are enrolled on this device';
      case BiometricAvailability.available:
        return '';
    }
  }
}

/// Enum representing biometric availability status
enum BiometricAvailability { available, notSupported, notEnrolled }

/// Enum representing biometric authentication error types
enum BiometricAuthError {
  notAvailable,
  notEnrolled,
  notSupported,
  userCancel,
  lockedOut,
  permanentlyLockedOut,
  unknown,
}

/// Result class for biometric authentication operations
class BiometricAuthResult {
  const BiometricAuthResult._({
    required this.isSuccess,
    this.error,
    this.errorMessage,
  });

  factory BiometricAuthResult.success() {
    return const BiometricAuthResult._(isSuccess: true);
  }

  factory BiometricAuthResult.failure(
    BiometricAuthError error,
    String message,
  ) {
    return BiometricAuthResult._(
      isSuccess: false,
      error: error,
      errorMessage: message,
    );
  }
  final bool isSuccess;
  final BiometricAuthError? error;
  final String? errorMessage;
}
