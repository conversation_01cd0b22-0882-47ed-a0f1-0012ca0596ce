import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

/// Error severity levels
enum ErrorSeverity {
  info, // Informational messages (e.g., email sent)
  warning, // Warnings that don't block functionality
  error, // Errors that block functionality
  critical, // Critical errors requiring immediate attention
}

/// Error categories for analytics and handling
enum ErrorCategory {
  authentication,
  network,
  validation,
  permission,
  rateLimit,
  system,
  user,
  biometric,
}

/// Comprehensive error information
class AuthError {
  const AuthError({
    required this.code,
    required this.userMessage,
    required this.technicalMessage,
    required this.severity,
    required this.category,
    this.isRetryable = true,
    this.metadata = const {},
  });
  final String code;
  final String userMessage;
  final String technicalMessage;
  final ErrorSeverity severity;
  final ErrorCategory category;
  final bool isRetryable;
  final Map<String, dynamic> metadata;

  @override
  String toString() => 'AuthError(code: $code, message: $userMessage)';
}

/// Centralized service for handling authentication errors
// ignore: avoid_classes_with_only_static_members
class AuthErrorService {
  /// Convert Firebase Auth exception to user-friendly error
  static AuthError handleFirebaseAuthException(
    FirebaseAuthException exception,
  ) {
    if (kDebugMode) {
      debugPrint(
        'Firebase Auth Error: ${exception.code} - ${exception.message}',
      );
    }

    switch (exception.code) {
      // Email/Password Authentication Errors
      case 'user-not-found':
        return const AuthError(
          code: 'user-not-found',
          userMessage:
              'No account found with this email address. Please check your email or create a new account.',
          technicalMessage: 'User not found in Firebase Auth',
          severity: ErrorSeverity.error,
          category: ErrorCategory.authentication,
          isRetryable: false,
        );

      case 'wrong-password':
        return const AuthError(
          code: 'wrong-password',
          userMessage:
              'Incorrect password. Please try again or reset your password.',
          technicalMessage: 'Invalid password provided',
          severity: ErrorSeverity.error,
          category: ErrorCategory.authentication,
          isRetryable: true,
        );

      case 'invalid-email':
        return const AuthError(
          code: 'invalid-email',
          userMessage: 'Please enter a valid email address.',
          technicalMessage: 'Invalid email format',
          severity: ErrorSeverity.error,
          category: ErrorCategory.validation,
          isRetryable: true,
        );

      case 'user-disabled':
        return const AuthError(
          code: 'user-disabled',
          userMessage:
              'This account has been disabled. Please contact support for assistance.',
          technicalMessage: 'User account is disabled',
          severity: ErrorSeverity.critical,
          category: ErrorCategory.permission,
          isRetryable: false,
        );

      case 'email-already-in-use':
        return const AuthError(
          code: 'email-already-in-use',
          userMessage:
              'An account already exists with this email address. Please sign in instead.',
          technicalMessage: 'Email already registered',
          severity: ErrorSeverity.error,
          category: ErrorCategory.authentication,
          isRetryable: false,
        );

      case 'weak-password':
        return const AuthError(
          code: 'weak-password',
          userMessage:
              'Password is too weak. Please choose a stronger password with at least 6 characters.',
          technicalMessage: 'Password does not meet security requirements',
          severity: ErrorSeverity.error,
          category: ErrorCategory.validation,
          isRetryable: true,
        );

      case 'operation-not-allowed':
        return const AuthError(
          code: 'operation-not-allowed',
          userMessage:
              'This sign-in method is not enabled. Please contact support.',
          technicalMessage: 'Authentication method not enabled in Firebase',
          severity: ErrorSeverity.critical,
          category: ErrorCategory.permission,
          isRetryable: false,
        );

      // Rate Limiting Errors
      case 'too-many-requests':
        return const AuthError(
          code: 'too-many-requests',
          userMessage:
              'Too many failed attempts. Please wait a few minutes before trying again.',
          technicalMessage: 'Rate limit exceeded',
          severity: ErrorSeverity.warning,
          category: ErrorCategory.rateLimit,
          isRetryable: true,
        );

      // Network Errors
      case 'network-request-failed':
        return const AuthError(
          code: 'network-request-failed',
          userMessage:
              'Network error. Please check your internet connection and try again.',
          technicalMessage: 'Network request failed',
          severity: ErrorSeverity.error,
          category: ErrorCategory.network,
          isRetryable: true,
        );

      // Google Sign-In Errors
      case 'sign_in_canceled':
        return const AuthError(
          code: 'sign_in_canceled',
          userMessage:
              'Sign-in was canceled. Please try again if you want to continue.',
          technicalMessage: 'User canceled OAuth flow',
          severity: ErrorSeverity.info,
          category: ErrorCategory.user,
          isRetryable: true,
        );

      case 'google_sign_in_failed':
        return const AuthError(
          code: 'google_sign_in_failed',
          userMessage:
              'Google sign-in failed. Please try again or use email/password.',
          technicalMessage: 'Google OAuth authentication failed',
          severity: ErrorSeverity.error,
          category: ErrorCategory.authentication,
          isRetryable: true,
        );

      // Account Linking Errors
      case 'account-exists-with-different-credential':
        return const AuthError(
          code: 'account-exists-with-different-credential',
          userMessage:
              'An account already exists with this email using a different sign-in method. Please sign in with your original method.',
          technicalMessage: 'Account exists with different credential',
          severity: ErrorSeverity.error,
          category: ErrorCategory.authentication,
          isRetryable: false,
        );

      case 'credential-already-in-use':
        return const AuthError(
          code: 'credential-already-in-use',
          userMessage:
              'This account is already linked to another user. Please use a different account.',
          technicalMessage: 'Credential already linked to different user',
          severity: ErrorSeverity.error,
          category: ErrorCategory.authentication,
          isRetryable: false,
        );

      // Email Verification Errors
      case 'no_user_or_verified':
        return const AuthError(
          code: 'no_user_or_verified',
          userMessage: 'No user signed in or email already verified.',
          technicalMessage: 'No current user or email already verified',
          severity: ErrorSeverity.info,
          category: ErrorCategory.validation,
          isRetryable: false,
        );

      // Session/Token Errors
      case 'invalid-user-token':
        return const AuthError(
          code: 'invalid-user-token',
          userMessage: 'Your session has expired. Please sign in again.',
          technicalMessage: 'Invalid or expired user token',
          severity: ErrorSeverity.error,
          category: ErrorCategory.authentication,
          isRetryable: false,
        );

      case 'user-token-expired':
        return const AuthError(
          code: 'user-token-expired',
          userMessage: 'Your session has expired. Please sign in again.',
          technicalMessage: 'User token expired',
          severity: ErrorSeverity.error,
          category: ErrorCategory.authentication,
          isRetryable: false,
        );

      // Requires Recent Login Errors
      case 'requires-recent-login':
        return const AuthError(
          code: 'requires-recent-login',
          userMessage:
              'This action requires recent authentication. Please sign in again.',
          technicalMessage: 'Operation requires recent login',
          severity: ErrorSeverity.warning,
          category: ErrorCategory.authentication,
          isRetryable: true,
        );

      // Default case for unknown errors
      default:
        return AuthError(
          code: exception.code,
          userMessage: 'An unexpected error occurred. Please try again.',
          technicalMessage: exception.message ?? 'Unknown Firebase Auth error',
          severity: ErrorSeverity.error,
          category: ErrorCategory.system,
          isRetryable: true,
          metadata: {'originalCode': exception.code},
        );
    }
  }

  /// Handle general exceptions (non-Firebase)
  static AuthError handleGeneralException(dynamic exception) {
    if (kDebugMode) {
      debugPrint('General Auth Error: $exception');
    }

    return AuthError(
      code: 'general_error',
      userMessage: 'An unexpected error occurred. Please try again.',
      technicalMessage: exception.toString(),
      severity: ErrorSeverity.error,
      category: ErrorCategory.system,
      isRetryable: true,
      metadata: {'originalError': exception.toString()},
    );
  }

  /// Get error message for specific context
  static String getContextualMessage(AuthError error, String context) {
    switch (context) {
      case 'login':
        if (error.code == 'user-not-found') {
          return 'No account found with this email. Please check your email or create a new account.';
        }
      case 'signup':
        if (error.code == 'email-already-in-use') {
          return 'An account already exists with this email. Please sign in instead.';
        }
      case 'password_reset':
        if (error.code == 'user-not-found') {
          return 'No account found with this email address.';
        }
    }

    return error.userMessage;
  }

  /// Check if error suggests user should try a different authentication method
  static bool shouldSuggestAlternativeAuth(AuthError error) {
    return [
      'account-exists-with-different-credential',
      'google_sign_in_failed',
      'operation-not-allowed',
    ].contains(error.code);
  }

  /// Get suggested actions for an error
  static List<String> getSuggestedActions(AuthError error) {
    switch (error.code) {
      case 'user-not-found':
        return ['Check your email address', 'Create a new account'];
      case 'wrong-password':
        return ['Try again', 'Reset your password'];
      case 'weak-password':
        return ['Use at least 6 characters', 'Include numbers and symbols'];
      case 'network-request-failed':
        return ['Check your internet connection', 'Try again in a moment'];
      case 'too-many-requests':
        return ['Wait a few minutes', 'Try again later'];
      case 'account-exists-with-different-credential':
        return [
          'Sign in with your original method',
          'Contact support if needed',
        ];
      default:
        return ['Try again', 'Contact support if the problem persists'];
    }
  }
}
