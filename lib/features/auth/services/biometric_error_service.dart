import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/services.dart';

/// Service for handling biometric authentication errors with user-friendly messages
// ignore: avoid_classes_with_only_static_members
class BiometricErrorService {
  /// Converts biometric authentication errors to user-friendly AuthError objects
  static AuthError handleBiometricError(
    Object error,
    AppLocalizations localizations,
  ) {
    if (error is PlatformException) {
      return _handlePlatformException(error, localizations);
    }

    // Generic error fallback
    return AuthError(
      code: 'biometric_unknown_error',
      userMessage: localizations.biometricUnknownError,
      technicalMessage: error.toString(),
      severity: ErrorSeverity.error,
      category: ErrorCategory.biometric,
      isRetryable: true,
    );
  }

  /// Handles platform-specific biometric errors
  static AuthError _handlePlatformException(
    PlatformException error,
    AppLocalizations localizations,
  ) {
    switch (error.code) {
      case 'BiometricOnlyPin':
      case 'PasscodeNotSet':
        return AuthError(
          code: 'biometric_passcode_not_set',
          userMessage: localizations.biometricPasscodeNotSet,
          technicalMessage: error.message ?? error.code,
          severity: ErrorSeverity.warning,
          category: ErrorCategory.biometric,
          isRetryable: false,
        );

      case 'NotEnrolled':
      case 'BiometricNotEnrolled':
        return AuthError(
          code: 'biometric_not_enrolled',
          userMessage: localizations.biometricNotEnrolled,
          technicalMessage: error.message ?? error.code,
          severity: ErrorSeverity.warning,
          category: ErrorCategory.biometric,
          isRetryable: false,
        );

      case 'NotAvailable':
      case 'BiometricNotAvailable':
        return AuthError(
          code: 'biometric_not_available',
          userMessage: localizations.biometricNotAvailable,
          technicalMessage: error.message ?? error.code,
          severity: ErrorSeverity.error,
          category: ErrorCategory.biometric,
          isRetryable: false,
        );

      case 'UserCancel':
      case 'BiometricUserCancel':
        return AuthError(
          code: 'biometric_user_cancel',
          userMessage: localizations.biometricUserCancelled,
          technicalMessage: error.message ?? error.code,
          severity: ErrorSeverity.info,
          category: ErrorCategory.biometric,
          isRetryable: true,
        );

      case 'UserFallback':
      case 'BiometricUserFallback':
        return AuthError(
          code: 'biometric_user_fallback',
          userMessage: localizations.biometricUserFallback,
          technicalMessage: error.message ?? error.code,
          severity: ErrorSeverity.info,
          category: ErrorCategory.biometric,
          isRetryable: true,
        );

      case 'SystemCancel':
      case 'BiometricSystemCancel':
        return AuthError(
          code: 'biometric_system_cancel',
          userMessage: localizations.biometricSystemCancelled,
          technicalMessage: error.message ?? error.code,
          severity: ErrorSeverity.warning,
          category: ErrorCategory.biometric,
          isRetryable: true,
        );

      case 'InvalidContext':
      case 'BiometricInvalidContext':
        return AuthError(
          code: 'biometric_invalid_context',
          userMessage: localizations.biometricInvalidContext,
          technicalMessage: error.message ?? error.code,
          severity: ErrorSeverity.error,
          category: ErrorCategory.biometric,
          isRetryable: true,
        );

      case 'LockedOut':
      case 'BiometricLockedOut':
        return AuthError(
          code: 'biometric_locked_out',
          userMessage: localizations.biometricLockedOut,
          technicalMessage: error.message ?? error.code,
          severity: ErrorSeverity.error,
          category: ErrorCategory.biometric,
          isRetryable: false,
        );

      case 'PermanentlyLockedOut':
      case 'BiometricPermanentlyLockedOut':
        return AuthError(
          code: 'biometric_permanently_locked_out',
          userMessage: localizations.biometricPermanentlyLockedOut,
          technicalMessage: error.message ?? error.code,
          severity: ErrorSeverity.error,
          category: ErrorCategory.biometric,
          isRetryable: false,
        );

      case 'TooManyAttempts':
      case 'BiometricTooManyAttempts':
        return AuthError(
          code: 'biometric_too_many_attempts',
          userMessage: localizations.biometricTooManyAttempts,
          technicalMessage: error.message ?? error.code,
          severity: ErrorSeverity.warning,
          category: ErrorCategory.biometric,
          isRetryable: false,
        );

      default:
        return AuthError(
          code: 'biometric_platform_error',
          userMessage: localizations.biometricPlatformError,
          technicalMessage: error.message ?? error.code,
          severity: ErrorSeverity.error,
          category: ErrorCategory.biometric,
          isRetryable: true,
        );
    }
  }

  /// Gets appropriate icon for biometric error type
  static String getErrorIcon(String errorCode) {
    switch (errorCode) {
      case 'biometric_passcode_not_set':
        return '🔒';
      case 'biometric_not_enrolled':
        return '👆';
      case 'biometric_not_available':
        return '❌';
      case 'biometric_user_cancel':
        return '🚫';
      case 'biometric_locked_out':
      case 'biometric_permanently_locked_out':
      case 'biometric_too_many_attempts':
        return '🔐';
      default:
        return '⚠️';
    }
  }
}

/// Actions that can be taken to resolve biometric errors
enum BiometricErrorAction {
  setupPasscode,
  enrollBiometric,
  waitAndRetry,
  useAlternativeAuth,
}

/// Extension to add biometric-specific properties to AuthError
extension BiometricAuthError on AuthError {
  BiometricErrorAction? get actionRequired {
    switch (code) {
      case 'biometric_passcode_not_set':
        return BiometricErrorAction.setupPasscode;
      case 'biometric_not_enrolled':
        return BiometricErrorAction.enrollBiometric;
      case 'biometric_locked_out':
      case 'biometric_too_many_attempts':
        return BiometricErrorAction.waitAndRetry;
      case 'biometric_permanently_locked_out':
        return BiometricErrorAction.useAlternativeAuth;
      default:
        return null;
    }
  }
}
