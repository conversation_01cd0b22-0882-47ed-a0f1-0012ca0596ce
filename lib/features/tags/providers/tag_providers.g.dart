// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tag_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userTagsHash() => r'0130264d32f230d959bf352dcc581a286d120c04';

/// Provider for watching all user tags
///
/// Copied from [userTags].
@ProviderFor(userTags)
final userTagsProvider = AutoDisposeStreamProvider<List<Tag>>.internal(
  userTags,
  name: r'userTagsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userTagsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserTagsRef = AutoDisposeStreamProviderRef<List<Tag>>;
String _$tagByIdHash() => r'1dff685bf00cb6b7c6e4e7518772dbc6a9b550ff';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for getting a specific tag by ID
///
/// Copied from [tagById].
@ProviderFor(tagById)
const tagByIdProvider = TagByIdFamily();

/// Provider for getting a specific tag by ID
///
/// Copied from [tagById].
class TagByIdFamily extends Family<AsyncValue<Tag?>> {
  /// Provider for getting a specific tag by ID
  ///
  /// Copied from [tagById].
  const TagByIdFamily();

  /// Provider for getting a specific tag by ID
  ///
  /// Copied from [tagById].
  TagByIdProvider call(String tagId) {
    return TagByIdProvider(tagId);
  }

  @override
  TagByIdProvider getProviderOverride(covariant TagByIdProvider provider) {
    return call(provider.tagId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'tagByIdProvider';
}

/// Provider for getting a specific tag by ID
///
/// Copied from [tagById].
class TagByIdProvider extends AutoDisposeFutureProvider<Tag?> {
  /// Provider for getting a specific tag by ID
  ///
  /// Copied from [tagById].
  TagByIdProvider(String tagId)
    : this._internal(
        (ref) => tagById(ref as TagByIdRef, tagId),
        from: tagByIdProvider,
        name: r'tagByIdProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$tagByIdHash,
        dependencies: TagByIdFamily._dependencies,
        allTransitiveDependencies: TagByIdFamily._allTransitiveDependencies,
        tagId: tagId,
      );

  TagByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.tagId,
  }) : super.internal();

  final String tagId;

  @override
  Override overrideWith(FutureOr<Tag?> Function(TagByIdRef provider) create) {
    return ProviderOverride(
      origin: this,
      override: TagByIdProvider._internal(
        (ref) => create(ref as TagByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        tagId: tagId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Tag?> createElement() {
    return _TagByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TagByIdProvider && other.tagId == tagId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, tagId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TagByIdRef on AutoDisposeFutureProviderRef<Tag?> {
  /// The parameter `tagId` of this provider.
  String get tagId;
}

class _TagByIdProviderElement extends AutoDisposeFutureProviderElement<Tag?>
    with TagByIdRef {
  _TagByIdProviderElement(super.provider);

  @override
  String get tagId => (origin as TagByIdProvider).tagId;
}

String _$tagsForTransactionHash() =>
    r'7f8cee2bb64d5ea972cfd539b46f8316a025d9cb';

/// Provider for getting tags associated with a specific transaction
///
/// Copied from [tagsForTransaction].
@ProviderFor(tagsForTransaction)
const tagsForTransactionProvider = TagsForTransactionFamily();

/// Provider for getting tags associated with a specific transaction
///
/// Copied from [tagsForTransaction].
class TagsForTransactionFamily extends Family<AsyncValue<List<Tag>>> {
  /// Provider for getting tags associated with a specific transaction
  ///
  /// Copied from [tagsForTransaction].
  const TagsForTransactionFamily();

  /// Provider for getting tags associated with a specific transaction
  ///
  /// Copied from [tagsForTransaction].
  TagsForTransactionProvider call(String transactionId) {
    return TagsForTransactionProvider(transactionId);
  }

  @override
  TagsForTransactionProvider getProviderOverride(
    covariant TagsForTransactionProvider provider,
  ) {
    return call(provider.transactionId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'tagsForTransactionProvider';
}

/// Provider for getting tags associated with a specific transaction
///
/// Copied from [tagsForTransaction].
class TagsForTransactionProvider extends AutoDisposeFutureProvider<List<Tag>> {
  /// Provider for getting tags associated with a specific transaction
  ///
  /// Copied from [tagsForTransaction].
  TagsForTransactionProvider(String transactionId)
    : this._internal(
        (ref) =>
            tagsForTransaction(ref as TagsForTransactionRef, transactionId),
        from: tagsForTransactionProvider,
        name: r'tagsForTransactionProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$tagsForTransactionHash,
        dependencies: TagsForTransactionFamily._dependencies,
        allTransitiveDependencies:
            TagsForTransactionFamily._allTransitiveDependencies,
        transactionId: transactionId,
      );

  TagsForTransactionProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.transactionId,
  }) : super.internal();

  final String transactionId;

  @override
  Override overrideWith(
    FutureOr<List<Tag>> Function(TagsForTransactionRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TagsForTransactionProvider._internal(
        (ref) => create(ref as TagsForTransactionRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        transactionId: transactionId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Tag>> createElement() {
    return _TagsForTransactionProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TagsForTransactionProvider &&
        other.transactionId == transactionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, transactionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TagsForTransactionRef on AutoDisposeFutureProviderRef<List<Tag>> {
  /// The parameter `transactionId` of this provider.
  String get transactionId;
}

class _TagsForTransactionProviderElement
    extends AutoDisposeFutureProviderElement<List<Tag>>
    with TagsForTransactionRef {
  _TagsForTransactionProviderElement(super.provider);

  @override
  String get transactionId =>
      (origin as TagsForTransactionProvider).transactionId;
}

String _$tagUsageCountHash() => r'0e6a7b4f30fd6737db55f4ec68036556536eafe8';

/// Provider for tag usage statistics
///
/// Copied from [tagUsageCount].
@ProviderFor(tagUsageCount)
const tagUsageCountProvider = TagUsageCountFamily();

/// Provider for tag usage statistics
///
/// Copied from [tagUsageCount].
class TagUsageCountFamily extends Family<AsyncValue<int>> {
  /// Provider for tag usage statistics
  ///
  /// Copied from [tagUsageCount].
  const TagUsageCountFamily();

  /// Provider for tag usage statistics
  ///
  /// Copied from [tagUsageCount].
  TagUsageCountProvider call(String tagId) {
    return TagUsageCountProvider(tagId);
  }

  @override
  TagUsageCountProvider getProviderOverride(
    covariant TagUsageCountProvider provider,
  ) {
    return call(provider.tagId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'tagUsageCountProvider';
}

/// Provider for tag usage statistics
///
/// Copied from [tagUsageCount].
class TagUsageCountProvider extends AutoDisposeFutureProvider<int> {
  /// Provider for tag usage statistics
  ///
  /// Copied from [tagUsageCount].
  TagUsageCountProvider(String tagId)
    : this._internal(
        (ref) => tagUsageCount(ref as TagUsageCountRef, tagId),
        from: tagUsageCountProvider,
        name: r'tagUsageCountProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$tagUsageCountHash,
        dependencies: TagUsageCountFamily._dependencies,
        allTransitiveDependencies:
            TagUsageCountFamily._allTransitiveDependencies,
        tagId: tagId,
      );

  TagUsageCountProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.tagId,
  }) : super.internal();

  final String tagId;

  @override
  Override overrideWith(
    FutureOr<int> Function(TagUsageCountRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TagUsageCountProvider._internal(
        (ref) => create(ref as TagUsageCountRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        tagId: tagId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<int> createElement() {
    return _TagUsageCountProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TagUsageCountProvider && other.tagId == tagId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, tagId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TagUsageCountRef on AutoDisposeFutureProviderRef<int> {
  /// The parameter `tagId` of this provider.
  String get tagId;
}

class _TagUsageCountProviderElement
    extends AutoDisposeFutureProviderElement<int>
    with TagUsageCountRef {
  _TagUsageCountProviderElement(super.provider);

  @override
  String get tagId => (origin as TagUsageCountProvider).tagId;
}

String _$tagSearchHash() => r'a9cce8e6368977b6ff696428e034cf4dfdcbdf9a';

/// Provider for tag search functionality
///
/// Copied from [TagSearch].
@ProviderFor(TagSearch)
final tagSearchProvider =
    AutoDisposeAsyncNotifierProvider<TagSearch, List<Tag>>.internal(
      TagSearch.new,
      name: r'tagSearchProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$tagSearchHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TagSearch = AutoDisposeAsyncNotifier<List<Tag>>;
String _$tagCreatorHash() => r'bcf3cee91312cf3ca4590fd733255e421f0aa297';

/// Provider for tag creation
///
/// Copied from [TagCreator].
@ProviderFor(TagCreator)
final tagCreatorProvider =
    AutoDisposeAsyncNotifierProvider<TagCreator, Tag?>.internal(
      TagCreator.new,
      name: r'tagCreatorProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$tagCreatorHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TagCreator = AutoDisposeAsyncNotifier<Tag?>;
String _$tagUpdaterHash() => r'53ca5e739c7b062bdd212fe99fbe995e3f9d2519';

/// Provider for tag updates
///
/// Copied from [TagUpdater].
@ProviderFor(TagUpdater)
final tagUpdaterProvider =
    AutoDisposeAsyncNotifierProvider<TagUpdater, Tag?>.internal(
      TagUpdater.new,
      name: r'tagUpdaterProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$tagUpdaterHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TagUpdater = AutoDisposeAsyncNotifier<Tag?>;
String _$tagDeleterHash() => r'8d08c37a1a6fbe5abfe2bf3b8091fcea431cec70';

/// Provider for tag deletion
///
/// Copied from [TagDeleter].
@ProviderFor(TagDeleter)
final tagDeleterProvider =
    AutoDisposeAsyncNotifierProvider<TagDeleter, void>.internal(
      TagDeleter.new,
      name: r'tagDeleterProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$tagDeleterHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TagDeleter = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
