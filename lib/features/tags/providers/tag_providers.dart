import 'package:budapp/data/models/tag.dart';
import 'package:budapp/data/repositories/interfaces/tag_repository.dart';
import 'package:budapp/features/tags/services/tag_validators.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'tag_providers.g.dart';

/// Provider for watching all user tags
@riverpod
Stream<List<Tag>> userTags(Ref ref) {
  final repository = ref.watch(tagRepositoryProvider);
  return repository.watchUserTags();
}

/// Provider for getting a specific tag by ID
@riverpod
Future<Tag?> tagById(Ref ref, String tagId) {
  final repository = ref.watch(tagRepositoryProvider);
  return repository.getTagById(tagId);
}

/// Provider for tag search functionality
@riverpod
class TagSearch extends _$TagSearch {
  @override
  Future<List<Tag>> build() async {
    // Initially return all tags
    final repository = ref.watch(tagRepositoryProvider);
    return repository.searchTags('');
  }

  /// Search tags by name
  Future<void> searchTags(String searchTerm) async {
    state = const AsyncValue.loading();

    try {
      final repository = ref.read(tagRepositoryProvider);
      final results = await repository.searchTags(searchTerm);
      state = AsyncValue.data(results);
    } on Exception catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Clear search and return all tags
  Future<void> clearSearch() async {
    await searchTags('');
  }
}

/// Provider for tag creation
@riverpod
class TagCreator extends _$TagCreator {
  @override
  FutureOr<Tag?> build() => null;

  /// Create a new tag
  Future<Tag> createTag({required String name, required String color}) async {
    state = const AsyncValue.loading();

    try {
      // Validate tag data
      final validator = ref.read(tagValidatorsProvider);
      final validationResult = validator.validateTagCreation(
        name: name,
        color: color,
      );

      if (!validationResult.isValid) {
        throw TagValidationException(
          'Tag validation failed',
          validationResult.errors,
        );
      }

      // Create the tag
      final repository = ref.read(tagRepositoryProvider);
      final tag = Tag.create(
        userId: '', // Will be set by repository based on current user
        name: name.trim(),
        color: color,
      );

      final createdTag = await repository.createTag(tag);
      state = AsyncValue.data(createdTag);

      // Invalidate related providers
      ref
        ..invalidate(userTagsProvider)
        ..invalidate(tagSearchProvider);

      return createdTag;
    } on Exception catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }
}

/// Provider for tag updates
@riverpod
class TagUpdater extends _$TagUpdater {
  @override
  FutureOr<Tag?> build() => null;

  /// Update an existing tag
  Future<Tag> updateTag({
    required String tagId,
    required String name,
    required String color,
  }) async {
    state = const AsyncValue.loading();

    try {
      // Get current tag
      final repository = ref.read(tagRepositoryProvider);
      final currentTag = await repository.getTagById(tagId);

      if (currentTag == null) {
        throw TagNotFoundException(tagId);
      }

      // Validate tag data
      final validator = ref.read(tagValidatorsProvider);
      final validationResult = validator.validateTagUpdate(
        currentTag: currentTag,
        newName: name,
        newColor: color,
      );

      if (!validationResult.isValid) {
        throw TagValidationException(
          'Tag validation failed',
          validationResult.errors,
        );
      }

      // Update the tag
      final updatedTag = currentTag.copyWith(name: name.trim(), color: color);

      final result = await repository.updateTag(updatedTag);
      state = AsyncValue.data(result);

      // Invalidate related providers
      ref
        ..invalidate(userTagsProvider)
        ..invalidate(tagSearchProvider)
        ..invalidate(tagByIdProvider(tagId));

      return result;
    } on Exception catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }
}

/// Provider for tag deletion
@riverpod
class TagDeleter extends _$TagDeleter {
  @override
  FutureOr<void> build() => null;

  /// Delete a tag and remove it from all transactions
  Future<void> deleteTag(String tagId) async {
    state = const AsyncValue.loading();

    try {
      final repository = ref.read(tagRepositoryProvider);
      await repository.deleteTag(tagId);

      state = const AsyncValue.data(null);

      // Invalidate related providers
      ref
        ..invalidate(userTagsProvider)
        ..invalidate(tagSearchProvider)
        ..invalidate(tagByIdProvider(tagId));
    } on Exception catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }

  /// Get usage count for a tag before deletion
  Future<int> getTagUsageCount(String tagId) async {
    final repository = ref.read(tagRepositoryProvider);
    return repository.getTagUsageCount(tagId);
  }
}

/// Provider for getting tags associated with a specific transaction
@riverpod
Future<List<Tag>> tagsForTransaction(Ref ref, String transactionId) {
  final repository = ref.watch(tagRepositoryProvider);
  return repository.getTagsForTransaction(transactionId);
}

/// Provider for tag usage statistics
@riverpod
Future<int> tagUsageCount(Ref ref, String tagId) {
  final repository = ref.watch(tagRepositoryProvider);
  return repository.getTagUsageCount(tagId);
}
