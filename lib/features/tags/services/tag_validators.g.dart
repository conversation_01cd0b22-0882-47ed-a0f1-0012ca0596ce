// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tag_validators.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$tagValidatorsHash() => r'f2f727c60ecb9500e7e8e4a4804c1d2ca7b38675';

/// See also [tagValidators].
@ProviderFor(tagValidators)
final tagValidatorsProvider = AutoDisposeProvider<TagValidators>.internal(
  tagValidators,
  name: r'tagValidatorsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$tagValidatorsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TagValidatorsRef = AutoDisposeProviderRef<TagValidators>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
