import 'package:budapp/data/models/tag.dart';
import 'package:budapp/utils/validation_result.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'tag_validators.g.dart';

@riverpod
TagValidators tagValidators(Ref ref) {
  return TagValidators();
}

/// Service for validating tag data
class TagValidators {
  /// Validate tag creation data
  ValidationResult validateTagCreation({
    required String name,
    required String color,
  }) {
    final errors = <String>[];

    // Validate name
    final nameValidation = validateTagName(name);
    if (!nameValidation.isValid) {
      errors.addAll(nameValidation.errors);
    }

    // Validate color
    final colorValidation = validateTagColor(color);
    if (!colorValidation.isValid) {
      errors.addAll(colorValidation.errors);
    }

    return ValidationResult(isValid: errors.isEmpty, errors: errors);
  }

  /// Validate tag update data
  ValidationResult validateTagUpdate({
    required Tag currentTag,
    required String newName,
    required String newColor,
  }) {
    final errors = <String>[];

    // Validate name (only if changed)
    if (newName != currentTag.name) {
      final nameValidation = validateTagName(newName);
      if (!nameValidation.isValid) {
        errors.addAll(nameValidation.errors);
      }
    }

    // Validate color (only if changed)
    if (newColor != currentTag.color) {
      final colorValidation = validateTagColor(newColor);
      if (!colorValidation.isValid) {
        errors.addAll(colorValidation.errors);
      }
    }

    return ValidationResult(isValid: errors.isEmpty, errors: errors);
  }

  /// Validate tag name
  ValidationResult validateTagName(String name) {
    final errors = <String>[];
    final trimmedName = name.trim();

    if (trimmedName.isEmpty) {
      errors.add('Tag name cannot be empty');
    } else if (trimmedName.length < 2) {
      errors.add('Tag name must be at least 2 characters long');
    } else if (trimmedName.length > 30) {
      errors.add('Tag name cannot exceed 30 characters');
    }

    // Check for invalid characters
    if (trimmedName.contains(RegExp(r'[<>"/\\|?*]'))) {
      errors.add('Tag name contains invalid characters');
    }

    // Check for only whitespace
    if (trimmedName.isNotEmpty &&
        trimmedName.replaceAll(RegExp(r'\s'), '').isEmpty) {
      errors.add('Tag name cannot contain only whitespace');
    }

    return ValidationResult(isValid: errors.isEmpty, errors: errors);
  }

  /// Validate tag color (hex format)
  ValidationResult validateTagColor(String color) {
    final errors = <String>[];

    if (color.isEmpty) {
      errors.add('Tag color cannot be empty');
      return ValidationResult(isValid: false, errors: errors);
    }

    // Check hex format
    final hexRegex = RegExp(r'^#[0-9A-Fa-f]{6}$');
    if (!hexRegex.hasMatch(color)) {
      errors.add('Tag color must be a valid hex color (e.g., #FF5722)');
    }

    return ValidationResult(isValid: errors.isEmpty, errors: errors);
  }

  /// Validate tag for deletion
  ValidationResult validateTagDeletion({
    required Tag tag,
    required int usageCount,
  }) {
    final errors = <String>[];

    if (usageCount > 0) {
      errors.add(
        'Tag is used by $usageCount transaction(s). Deleting will remove it from all transactions.',
      );
    }

    // This is more of a warning than an error, so we still allow deletion
    return ValidationResult(isValid: true, errors: errors);
  }

  /// Validate multiple tag IDs for transaction assignment
  ValidationResult validateTagIdsForTransaction(List<String> tagIds) {
    final errors = <String>[];

    if (tagIds.length > 10) {
      errors.add('Cannot assign more than 10 tags to a transaction');
    }

    // Check for duplicates
    final uniqueIds = tagIds.toSet();
    if (uniqueIds.length != tagIds.length) {
      errors.add('Duplicate tags are not allowed');
    }

    // Check for empty IDs
    if (tagIds.any((id) => id.trim().isEmpty)) {
      errors.add('Tag IDs cannot be empty');
    }

    return ValidationResult(isValid: errors.isEmpty, errors: errors);
  }

  /// Get suggested tag colors
  List<String> getSuggestedColors() {
    return [
      '#F44336', // Red
      '#E91E63', // Pink
      '#9C27B0', // Purple
      '#673AB7', // Deep Purple
      '#3F51B5', // Indigo
      '#2196F3', // Blue
      '#03A9F4', // Light Blue
      '#00BCD4', // Cyan
      '#009688', // Teal
      '#4CAF50', // Green
      '#8BC34A', // Light Green
      '#CDDC39', // Lime
      '#FFEB3B', // Yellow
      '#FFC107', // Amber
      '#FF9800', // Orange
      '#FF5722', // Deep Orange
      '#795548', // Brown
      '#607D8B', // Blue Grey
    ];
  }

  /// Check if a color is in the suggested colors list
  bool isSuggestedColor(String color) {
    return getSuggestedColors().contains(color.toUpperCase());
  }
}

/// Result of tag validation
