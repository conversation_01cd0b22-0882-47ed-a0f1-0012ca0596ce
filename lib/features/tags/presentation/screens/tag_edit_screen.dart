import 'package:budapp/data/models/tag.dart';
import 'package:budapp/features/tags/providers/tag_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/widgets/forms/configs/tag_form_config.dart';
import 'package:budapp/widgets/forms/screens/generic_form_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Tag editing screen using the generic form system
class TagEditScreen extends ConsumerWidget {
  const TagEditScreen({super.key, required this.tagId});
  final String tagId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final repository = ref.watch(tagRepositoryProvider);
    final tagAsync = ref.watch(tagByIdProvider(tagId));

    return tagAsync.when(
      data: (tag) {
        if (tag == null) {
          return Scaffold(
            appBar: AppBar(title: const Text('Tag Not Found')),
            body: const Center(
              child: Text('The requested tag could not be found.'),
            ),
          );
        }

        final config = TagFormConfig.edit(
          tag: tag,
          repository: repository,
          onFieldChanged: (fieldKey, value) {
            // Optional: Handle field changes for real-time validation or updates
            debugPrint('Tag field $fieldKey changed to: $value');
          },
        );

        return GenericFormScreen<Tag>(config: config);
      },
      loading: () => Scaffold(
        appBar: AppBar(title: const Text('Edit Tag')),
        body: const Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(child: Text('Error loading tag: $error')),
      ),
    );
  }
}
