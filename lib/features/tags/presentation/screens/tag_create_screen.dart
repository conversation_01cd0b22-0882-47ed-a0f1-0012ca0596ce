import 'package:budapp/data/models/tag.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/widgets/forms/configs/tag_form_config.dart';
import 'package:budapp/widgets/forms/screens/generic_form_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Tag creation screen using the generic form system
class TagCreateScreen extends ConsumerWidget {
  const TagCreateScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final repository = ref.watch(tagRepositoryProvider);

    final config = TagFormConfig.create(
      repository: repository,
      onFieldChanged: (fieldKey, value) {
        // Optional: Handle field changes for real-time validation or updates
        debugPrint('Tag field $fieldKey changed to: $value');
      },
    );

    return GenericFormScreen<Tag>(config: config);
  }
}
