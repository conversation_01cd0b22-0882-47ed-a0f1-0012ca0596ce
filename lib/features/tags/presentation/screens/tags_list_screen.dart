import 'package:budapp/data/models/tag.dart';
import 'package:budapp/features/common/widgets/app_bar_helpers.dart';
import 'package:budapp/features/tags/presentation/widgets/tag_card.dart';
import 'package:budapp/features/tags/presentation/widgets/tags_empty_state.dart';
import 'package:budapp/features/tags/providers/tag_providers.dart';
import 'package:budapp/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

class TagsListScreen extends ConsumerStatefulWidget {
  const TagsListScreen({super.key});

  @override
  ConsumerState<TagsListScreen> createState() => _TagsListScreenState();
}

class _TagsListScreenState extends ConsumerState<TagsListScreen> {
  final _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _startSearch() {
    setState(() {
      _isSearching = true;
    });
  }

  void _stopSearch() {
    setState(() {
      _isSearching = false;
    });
    _searchController.clear();
    ref.read(tagSearchProvider.notifier).clearSearch();
  }

  void _onSearchChanged(String query) {
    ref.read(tagSearchProvider.notifier).searchTags(query);
  }

  void _navigateToEditTag(Tag tag) {
    context.push(AppRoutes.tagEdit.replaceFirst(':id', tag.id));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarHelpers.createStandardScrollableAppBar(
        title: _isSearching ? 'Search Tags' : 'Tags',
        actions: [
          if (_isSearching)
            Expanded(
              child: TextField(
                controller: _searchController,
                autofocus: true,
                decoration: const InputDecoration(
                  hintText: 'Search tags...',
                  border: InputBorder.none,
                ),
                onChanged: _onSearchChanged,
              ),
            ),
          if (_isSearching)
            IconButton(icon: const Icon(Icons.close), onPressed: _stopSearch)
          else ...[
            IconButton(icon: const Icon(Icons.search), onPressed: _startSearch),
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => context.push('/tags/create'),
              tooltip: 'Add Tag',
            ),
          ],
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(userTagsProvider);
        },
        child: _isSearching ? _buildSearchResults() : _buildTagsList(),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.push('/tags/create'),
        tooltip: 'Add Tag',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildTagsList() {
    final tagsAsync = ref.watch(userTagsProvider);

    return tagsAsync.when(
      data: (tags) {
        if (tags.isEmpty) {
          return const TagsEmptyState();
        }

        return ListView.builder(
          padding: const EdgeInsets.fromLTRB(
            16,
            16,
            16,
            80,
          ), // Bottom padding for FAB
          itemCount: tags.length,
          itemBuilder: (context, index) {
            final tag = tags[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: TagCard(tag: tag, onTap: () => _navigateToEditTag(tag)),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load tags',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(userTagsProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResults() {
    final searchAsync = ref.watch(tagSearchProvider);

    return searchAsync.when(
      data: (tags) {
        if (tags.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.search_off,
                  size: 64,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(height: 16),
                Text(
                  'No tags found',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  'Try a different search term',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.fromLTRB(
            16,
            16,
            16,
            80,
          ), // Bottom padding for FAB
          itemCount: tags.length,
          itemBuilder: (context, index) {
            final tag = tags[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: TagCard(tag: tag, onTap: () => _navigateToEditTag(tag)),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Search failed',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
