import 'package:budapp/routing/app_router.dart';
import 'package:budapp/widgets/common/empty_state.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class TagsEmptyState extends StatelessWidget {
  const TagsEmptyState({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return EmptyState(
      icon: Icons.label_outline,
      title: 'No Tags Yet',
      message:
          'Tags help you organize and categorize your transactions for better tracking and analysis.',
      actionText: 'Create Your First Tag',
      onAction: () {
        try {
          final router = GoRouter.maybeOf(context);
          if (router != null) {
            context.push(AppRoutes.tagCreate);
          } else {
            debugPrint('GoRouter not available in context');
          }
        } on Exception catch (e) {
          // Handle case where GoRouter is not available (e.g., in tests)
          debugPrint('Navigation failed: $e');
        }
      },
      child: Card(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Benefits of Using Tags',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 16),
              _buildBenefit(
                context,
                Icons.filter_list,
                'Filter Transactions',
                'Quickly find transactions by specific tags',
              ),
              const SizedBox(height: 12),
              _buildBenefit(
                context,
                Icons.analytics_outlined,
                'Better Analysis',
                'Analyze spending patterns by categories',
              ),
              const SizedBox(height: 12),
              _buildBenefit(
                context,
                Icons.folder_outlined,
                'Stay Organized',
                'Keep your finances well-organized and structured',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBenefit(
    BuildContext context,
    IconData icon,
    String title,
    String description,
  ) {
    final theme = Theme.of(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, size: 18, color: theme.colorScheme.primary),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
