import 'package:budapp/data/models/tag.dart';
import 'package:budapp/features/tags/providers/tag_providers.dart';
import 'package:budapp/utils/color_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TagCard extends ConsumerWidget {
  const TagCard({
    super.key,
    required this.tag,
    this.onTap,
    this.showUsageCount = true,
    this.isSelectable = false,
    this.isSelected = false,
    this.onSelectionChanged,
  });
  final Tag tag;
  final VoidCallback? onTap;
  final bool showUsageCount;
  final bool isSelectable;
  final bool isSelected;
  final VoidCallback? onSelectionChanged;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Card(
      elevation: isSelected ? 4 : 1,
      child: InkWell(
        onTap: isSelectable ? onSelectionChanged : onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Selection checkbox (if selectable)
              if (isSelectable) ...[
                Checkbox(
                  value: isSelected,
                  onChanged: (_) => onSelectionChanged?.call(),
                ),
                const SizedBox(width: 12),
              ],

              // Tag color indicator
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: parseHex(tag.color),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: theme.colorScheme.outline.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // Tag information
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tag.name,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (showUsageCount) ...[
                      const SizedBox(height: 4),
                      Consumer(
                        builder: (context, ref, child) {
                          final usageAsync = ref.watch(
                            tagUsageCountProvider(tag.id),
                          );
                          return usageAsync.when(
                            data: (count) => Text(
                              '$count transaction${count == 1 ? '' : 's'}',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            loading: () => Text(
                              'Loading...',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            error: (_, _) => Text(
                              'Usage unknown',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.error,
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ],
                ),
              ),

              // Tag preview chip
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: parseHex(tag.color),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  tag.name,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: contrastOfHex(tag.color),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

              // Action button (if not selectable)
              if (!isSelectable && onTap != null) ...[
                const SizedBox(width: 8),
                Icon(
                  Icons.chevron_right,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// A compact version of TagCard for use in lists or chips
class TagChip extends StatelessWidget {
  const TagChip({
    super.key,
    required this.tag,
    this.onTap,
    this.onDelete,
    this.showDeleteButton = false,
  });
  final Tag tag;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;
  final bool showDeleteButton;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(right: 8, bottom: 4),
      child: InputChip(
        label: Text(
          tag.name,
          style: TextStyle(
            color: contrastOfHex(tag.color),
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: parseHex(tag.color),
        onPressed: onTap,
        onDeleted: showDeleteButton ? onDelete : null,
        deleteIconColor: contrastOfHex(tag.color),
      ),
    );
  }
}
