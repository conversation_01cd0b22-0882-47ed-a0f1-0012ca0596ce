import 'package:budapp/features/tags/presentation/widgets/tag_card.dart';
import 'package:budapp/features/tags/providers/tag_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

class TagSelector extends ConsumerStatefulWidget {
  const TagSelector({
    super.key,
    required this.selectedTagIds,
    required this.onSelectionChanged,
    this.maxSelections = 10,
    this.allowCreation = false,
  });
  final List<String> selectedTagIds;
  final ValueChanged<List<String>> onSelectionChanged;
  final int maxSelections;
  final bool allowCreation;

  @override
  ConsumerState<TagSelector> createState() => _TagSelectorState();
}

class _TagSelectorState extends ConsumerState<TagSelector> {
  final _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _toggleTag(String tagId) {
    final currentSelection = List<String>.from(widget.selectedTagIds);

    if (currentSelection.contains(tagId)) {
      currentSelection.remove(tagId);
    } else {
      if (currentSelection.length >= widget.maxSelections) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Maximum ${widget.maxSelections} tags allowed'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
        return;
      }
      currentSelection.add(tagId);
    }

    widget.onSelectionChanged(currentSelection);
  }

  void _startSearch() {
    setState(() {
      _isSearching = true;
    });
  }

  void _stopSearch() {
    setState(() {
      _isSearching = false;
    });
    _searchController.clear();
    ref.read(tagSearchProvider.notifier).clearSearch();
  }

  void _onSearchChanged(String query) {
    ref.read(tagSearchProvider.notifier).searchTags(query);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with search
        Row(
          children: [
            Expanded(
              child: Text(
                'Tags (${widget.selectedTagIds.length}/${widget.maxSelections})',
                style: theme.textTheme.titleMedium,
              ),
            ),
            IconButton(
              onPressed: _isSearching ? _stopSearch : _startSearch,
              icon: Icon(_isSearching ? Icons.close : Icons.search),
            ),
          ],
        ),

        // Search field
        if (_isSearching) ...[
          const SizedBox(height: 8),
          TextField(
            controller: _searchController,
            autofocus: true,
            decoration: const InputDecoration(
              hintText: 'Search tags...',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.search),
            ),
            onChanged: _onSearchChanged,
          ),
        ],

        const SizedBox(height: 16),

        // Selected tags display
        if (widget.selectedTagIds.isNotEmpty) ...[
          Text('Selected Tags', style: theme.textTheme.titleSmall),
          const SizedBox(height: 8),
          Wrap(
            children: widget.selectedTagIds.map((tagId) {
              return Consumer(
                builder: (context, ref, child) {
                  final tagAsync = ref.watch(tagByIdProvider(tagId));
                  return tagAsync.when(
                    data: (tag) {
                      if (tag == null) return const SizedBox.shrink();
                      return TagChip(
                        tag: tag,
                        showDeleteButton: true,
                        onDelete: () => _toggleTag(tagId),
                      );
                    },
                    loading: () => const Chip(label: Text('Loading...')),
                    error: (_, _) => const SizedBox.shrink(),
                  );
                },
              );
            }).toList(),
          ),
          const SizedBox(height: 16),
        ],

        // Available tags
        Text('Available Tags', style: theme.textTheme.titleSmall),
        const SizedBox(height: 8),

        // Tags list
        SizedBox(
          height: 300,
          child: _isSearching ? _buildSearchResults() : _buildAllTags(),
        ),
      ],
    );
  }

  Widget _buildAllTags() {
    final tagsAsync = ref.watch(userTagsProvider);

    return tagsAsync.when(
      data: (tags) {
        final availableTags = tags
            .where((tag) => !widget.selectedTagIds.contains(tag.id))
            .toList();

        if (availableTags.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.label_outline,
                  size: 48,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(height: 16),
                Text(
                  widget.selectedTagIds.isEmpty
                      ? 'No tags available'
                      : 'All tags selected',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                if (widget.allowCreation) ...[
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: () {
                      context.push('/tags/create');
                    },
                    child: const Text('Create New Tag'),
                  ),
                ],
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: availableTags.length,
          itemBuilder: (context, index) {
            final tag = availableTags[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: TagCard(
                tag: tag,
                isSelectable: true,
                isSelected: false,
                showUsageCount: false,
                onSelectionChanged: () => _toggleTag(tag.id),
              ),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load tags',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () => ref.invalidate(userTagsProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResults() {
    final searchAsync = ref.watch(tagSearchProvider);

    return searchAsync.when(
      data: (tags) {
        final availableTags = tags
            .where((tag) => !widget.selectedTagIds.contains(tag.id))
            .toList();

        if (availableTags.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.search_off,
                  size: 48,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(height: 16),
                Text(
                  'No tags found',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'Try a different search term',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: availableTags.length,
          itemBuilder: (context, index) {
            final tag = availableTags[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: TagCard(
                tag: tag,
                isSelectable: true,
                isSelected: false,
                showUsageCount: false,
                onSelectionChanged: () => _toggleTag(tag.id),
              ),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text('Search failed', style: Theme.of(context).textTheme.bodyLarge),
          ],
        ),
      ),
    );
  }
}
