import 'package:budapp/features/tags/services/tag_validators.dart';
import 'package:budapp/utils/color_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TagColorSelector extends ConsumerStatefulWidget {
  const TagColorSelector({
    super.key,
    required this.selectedColor,
    required this.onColorSelected,
    this.showCustomColorOption = true,
  });
  final String selectedColor;
  final ValueChanged<String> onColorSelected;
  final bool showCustomColorOption;

  @override
  ConsumerState<TagColorSelector> createState() => _TagColorSelectorState();
}

class _TagColorSelectorState extends ConsumerState<TagColorSelector> {
  final _customColorController = TextEditingController();
  bool _showCustomColorInput = false;

  @override
  void dispose() {
    _customColorController.dispose();
    super.dispose();
  }

  void _selectColor(String color) {
    widget.onColorSelected(color);
    setState(() {
      _showCustomColorInput = false;
    });
  }

  void _showCustomColorDialog() {
    _customColorController.text = widget.selectedColor;
    setState(() {
      _showCustomColorInput = true;
    });
  }

  void _applyCustomColor() {
    final color = _customColorController.text.trim();
    final validator = ref.read(tagValidatorsProvider);
    final result = validator.validateTagColor(color);

    if (result.isValid) {
      widget.onColorSelected(color);
      setState(() {
        _showCustomColorInput = false;
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result.firstError ?? 'Invalid color format'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  void _cancelCustomColor() {
    setState(() {
      _showCustomColorInput = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final validator = ref.watch(tagValidatorsProvider);
    final suggestedColors = validator.getSuggestedColors();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Suggested colors grid
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 6,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1,
          ),
          itemCount: suggestedColors.length,
          itemBuilder: (context, index) {
            final color = suggestedColors[index];
            final isSelected = color == widget.selectedColor;

            return GestureDetector(
              onTap: () => _selectColor(color),
              child: DecoratedBox(
                decoration: BoxDecoration(
                  color: parseHex(color),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.outline.withValues(alpha: 0.2),
                    width: isSelected ? 3 : 1,
                  ),
                ),
                child: isSelected
                    ? Icon(Icons.check, color: contrastOfHex(color), size: 20)
                    : null,
              ),
            );
          },
        ),

        if (widget.showCustomColorOption) ...[
          const SizedBox(height: 16),

          // Custom color section
          if (_showCustomColorInput) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Custom Color', style: theme.textTheme.titleSmall),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _customColorController,
                            decoration: const InputDecoration(
                              labelText: 'Hex Color',
                              hintText: '#FF5722',
                              border: OutlineInputBorder(),
                              prefixText: '#',
                            ),
                            onChanged: (value) {
                              // Auto-add # if not present
                              if (!value.startsWith('#')) {
                                _customColorController.text = '#$value';
                                _customColorController
                                    .selection = TextSelection.fromPosition(
                                  TextPosition(
                                    offset: _customColorController.text.length,
                                  ),
                                );
                              }
                            },
                          ),
                        ),
                        const SizedBox(width: 12),
                        // Color preview
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: _tryParseColor(_customColorController.text),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: theme.colorScheme.outline.withValues(
                                alpha: 0.2,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: _cancelCustomColor,
                          child: const Text('Cancel'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _applyCustomColor,
                          child: const Text('Apply'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ] else ...[
            // Custom color button
            OutlinedButton.icon(
              onPressed: _showCustomColorDialog,
              icon: const Icon(Icons.palette),
              label: const Text('Custom Color'),
            ),
          ],

          const SizedBox(height: 16),

          // Help text
          Text(
            'Tip: Choose colors that contrast well with both light and dark themes',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ],
    );
  }

  Color? _tryParseColor(String hexColor) {
    final color = parseHex(
      hexColor,
      fallback: Colors.grey.withValues(alpha: 0.3),
    );
    return color;
  }
}
