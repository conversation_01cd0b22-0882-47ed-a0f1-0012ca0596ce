import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/common/empty_state.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Widget displayed when user has no accounts
class EmptyAccountsState extends StatelessWidget {
  const EmptyAccountsState({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return EmptyState(
      icon: Icons.account_balance_wallet_outlined,
      title: l10n.noAccountsYet,
      message: l10n.noAccountsDescription,
      actionText: l10n.createFirstAccount,
      onAction: () => context.push('/accounts/create'),
      child: TextButton(
        onPressed: () => _showHelpDialog(context, theme, l10n),
        child: Text(l10n.learnAboutAccounts),
      ),
    );
  }

  void _showHelpDialog(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.aboutAccounts),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.accountsHelpDescription,
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              l10n.accountTypesTitle,
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: AppTypography.fontWeightSemiBold,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            _buildAccountTypeHelp(
              Icons.account_balance,
              l10n.checking,
              l10n.checkingDescription,
              theme,
            ),
            _buildAccountTypeHelp(
              Icons.savings,
              l10n.savings,
              l10n.savingsDescription,
              theme,
            ),
            _buildAccountTypeHelp(
              Icons.credit_card,
              l10n.creditCard,
              l10n.creditCardDescription,
              theme,
            ),
            _buildAccountTypeHelp(
              Icons.account_balance_wallet,
              l10n.cash,
              l10n.cashDescription,
              theme,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.gotIt),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountTypeHelp(
    IconData icon,
    String title,
    String description,
    ThemeData theme,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: theme.colorScheme.primary),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: AppTypography.fontWeightMedium,
                  ),
                ),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
