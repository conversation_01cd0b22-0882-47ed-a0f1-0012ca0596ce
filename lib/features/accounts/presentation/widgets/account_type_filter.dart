import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/account.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';

/// Bottom sheet widget for filtering accounts by type and status
class AccountTypeFilter extends StatefulWidget {
  const AccountTypeFilter({
    super.key,
    this.selectedType,
    required this.showInactive,
    required this.onTypeChanged,
    required this.onShowInactiveChanged,
  });
  final AccountType? selectedType;
  final bool showInactive;
  final ValueChanged<AccountType?> onTypeChanged;
  final ValueChanged<bool> onShowInactiveChanged;

  @override
  State<AccountTypeFilter> createState() => _AccountTypeFilterState();
}

class _AccountTypeFilterState extends State<AccountTypeFilter> {
  AccountType? _selectedType;
  bool _showInactive = false;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.selectedType;
    _showInactive = widget.showInactive;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Text(
                l10n.filterAccounts,
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: AppTypography.fontWeightSemiBold,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedType = null;
                    _showInactive = false;
                  });
                  widget.onTypeChanged(null);
                  widget.onShowInactiveChanged(false);
                },
                child: Text(l10n.clearAll),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.lg),

          // Account type filters
          Text(
            l10n.accountType,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: AppTypography.fontWeightMedium,
            ),
          ),
          const SizedBox(height: AppSpacing.md),

          // All accounts option
          _buildFilterChip(
            label: l10n.allAccounts,
            icon: Icons.account_balance_wallet,
            isSelected: _selectedType == null,
            onTap: () {
              setState(() => _selectedType = null);
              widget.onTypeChanged(null);
            },
            theme: theme,
          ),
          const SizedBox(height: AppSpacing.sm),

          // Individual account type chips
          Wrap(
            spacing: AppSpacing.sm,
            runSpacing: AppSpacing.sm,
            children: AccountType.values.map((type) {
              return _buildFilterChip(
                label: _getAccountTypeLabel(type, l10n),
                icon: _getAccountTypeIcon(type),
                isSelected: _selectedType == type,
                onTap: () {
                  setState(() => _selectedType = type);
                  widget.onTypeChanged(type);
                },
                theme: theme,
              );
            }).toList(),
          ),

          const SizedBox(height: AppSpacing.lg),
          const Divider(),
          const SizedBox(height: AppSpacing.md),

          // Show inactive accounts toggle
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      l10n.showInactiveAccounts,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: AppTypography.fontWeightMedium,
                      ),
                    ),
                    Text(
                      l10n.includeInactiveAccountsInList,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              Switch(
                value: _showInactive,
                onChanged: (value) {
                  setState(() => _showInactive = value);
                  widget.onShowInactiveChanged(value);
                },
              ),
            ],
          ),

          const SizedBox(height: AppSpacing.lg),

          // Apply button
          SizedBox(
            width: double.infinity,
            child: FilledButton(
              onPressed: () => Navigator.pop(context),
              child: Text(l10n.apply),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
    required ThemeData theme,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppBorderRadius.lg),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primaryContainer
              : theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(AppBorderRadius.lg),
          border: isSelected
              ? Border.all(color: theme.colorScheme.primary, width: 1)
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 20,
              color: isSelected
                  ? theme.colorScheme.onPrimaryContainer
                  : theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: AppSpacing.sm),
            Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected
                    ? theme.colorScheme.onPrimaryContainer
                    : theme.colorScheme.onSurfaceVariant,
                fontWeight: isSelected
                    ? AppTypography.fontWeightMedium
                    : AppTypography.fontWeightRegular,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getAccountTypeLabel(AccountType type, AppLocalizations l10n) {
    switch (type) {
      case AccountType.checking:
        return l10n.checking;
      case AccountType.savings:
        return l10n.savings;
      case AccountType.creditCard:
        return l10n.creditCard;
      case AccountType.cash:
        return l10n.cash;
      case AccountType.investment:
        return l10n.investment;
      case AccountType.loan:
        return l10n.loan;
    }
  }

  IconData _getAccountTypeIcon(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return Icons.account_balance;
      case AccountType.savings:
        return Icons.savings;
      case AccountType.creditCard:
        return Icons.credit_card;
      case AccountType.cash:
        return Icons.account_balance_wallet;
      case AccountType.investment:
        return Icons.trending_up;
      case AccountType.loan:
        return Icons.account_balance_outlined;
    }
  }
}
