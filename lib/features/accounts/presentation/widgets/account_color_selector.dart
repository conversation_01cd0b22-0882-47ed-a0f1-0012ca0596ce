import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/utils/color_utils.dart';
import 'package:flutter/material.dart';

/// Widget for selecting account color from predefined options
class AccountColorSelector extends StatelessWidget {
  const AccountColorSelector({
    super.key,
    required this.selectedColor,
    required this.onColorChanged,
    this.errorText,
  });

  final String? selectedColor;
  final void Function(String?) onColorChanged;
  final String? errorText;

  static const List<String> _availableColors = [
    '#2196F3', // Blue
    '#4CAF50', // Green
    '#FF9800', // Orange
    '#9C27B0', // Purple
    '#F44336', // Red
    '#795548', // Brown
    '#607D8B', // Blue Grey
    '#E91E63', // Pink
    '#009688', // Teal
    '#FFC107', // Amber
    '#3F51B5', // Indigo
    '#8BC34A', // Light Green
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              l10n.accountColor,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: AppTypography.fontWeightMedium,
              ),
            ),
            const SizedBox(width: AppSpacing.sm),
            Text(
              l10n.optional,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.sm),

        // Color grid
        Wrap(
          spacing: AppSpacing.sm,
          runSpacing: AppSpacing.sm,
          children: [
            // None option
            _buildColorOption(
              context,
              null,
              l10n.defaultColor,
              theme,
              icon: Icons.palette_outlined,
            ),
            // Color options
            ..._availableColors.map(
              (color) => _buildColorOption(context, color, null, theme),
            ),
          ],
        ),

        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildColorOption(
    BuildContext context,
    String? colorHex,
    String? label,
    ThemeData theme, {
    IconData? icon,
  }) {
    final isSelected = selectedColor == colorHex;
    final isNoneOption = colorHex == null;

    return GestureDetector(
      onTap: () => onColorChanged(colorHex),
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: isNoneOption
              ? theme.colorScheme.surfaceContainerHighest
              : parseHex(colorHex),
          border: Border.all(
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.outline,
            width: isSelected ? 3 : 1,
          ),
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        ),
        child: isNoneOption
            ? Icon(
                icon ?? Icons.palette_outlined,
                color: theme.colorScheme.onSurfaceVariant,
                size: 20,
              )
            : isSelected
            ? Icon(Icons.check, color: contrastOfHex(colorHex), size: 20)
            : null,
      ),
    );
  }
}
