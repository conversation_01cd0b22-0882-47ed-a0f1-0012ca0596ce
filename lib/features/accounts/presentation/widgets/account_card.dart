import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/account.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/utils/color_utils.dart';
import 'package:budapp/utils/icon_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Card widget displaying account information in a list
class AccountCard extends ConsumerWidget {
  const AccountCard({
    super.key,
    required this.account,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onSetPrimary,
    this.onDeactivate,
    this.showActions = false,
  });
  final Account account;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onSetPrimary;
  final VoidCallback? onDeactivate;
  final bool showActions;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Card(
      elevation: 1,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppBorderRadius.lg),
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row
              Row(
                children: [
                  // Account icon (large, clean icon without background container)
                  Icon(
                    _getAccountIcon(),
                    color: _getAccountColor() ?? theme.colorScheme.primary,
                    size: 48, // Large icon size to match category cards
                  ),
                  const SizedBox(width: AppSpacing.md),

                  // Account name and balance
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Account name with status indicators
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                account.name,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: AppTypography.fontWeightSemiBold,
                                  color: theme.colorScheme.onSurface,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (account.isPrimary) ...[
                              const SizedBox(width: AppSpacing.xs),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: AppSpacing.sm,
                                  vertical: AppSpacing.xs,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.success.withValues(
                                    alpha: 0.1,
                                  ),
                                  borderRadius: BorderRadius.circular(
                                    AppBorderRadius.sm,
                                  ),
                                ),
                                child: Text(
                                  l10n.primary,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: AppColors.success,
                                    fontWeight: AppTypography.fontWeightMedium,
                                  ),
                                ),
                              ),
                            ],
                            if (!account.isActive) ...[
                              const SizedBox(width: AppSpacing.xs),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: AppSpacing.sm,
                                  vertical: AppSpacing.xs,
                                ),
                                decoration: BoxDecoration(
                                  color:
                                      theme.colorScheme.surfaceContainerHighest,
                                  borderRadius: BorderRadius.circular(
                                    AppBorderRadius.sm,
                                  ),
                                ),
                                child: Text(
                                  l10n.inactive,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSurfaceVariant,
                                    fontWeight: AppTypography.fontWeightMedium,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                        const SizedBox(height: AppSpacing.xs),
                        // Current balance (without currency symbol)
                        Text(
                          _formatBalanceWithoutCurrency(
                            account.currentBalanceCents,
                          ),
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: AppTypography.fontWeightSemiBold,
                            color: _getBalanceColor(theme),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Actions menu
                  PopupMenuButton<String>(
                    icon: Icon(
                      Icons.more_vert,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          onEdit?.call();
                        case 'setPrimary':
                          onSetPrimary?.call();
                        case 'deactivate':
                          onDeactivate?.call();
                        case 'delete':
                          onDelete?.call();
                      }
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.edit),
                            const SizedBox(width: AppSpacing.sm),
                            Flexible(child: Text(l10n.editAccount)),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'setPrimary',
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.star),
                            const SizedBox(width: AppSpacing.sm),
                            Flexible(child: Text(l10n.setPrimary)),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'deactivate',
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.visibility_off),
                            const SizedBox(width: AppSpacing.sm),
                            Flexible(child: Text(l10n.deactivateAccount)),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.delete, color: theme.colorScheme.error),
                            const SizedBox(width: AppSpacing.sm),
                            Flexible(
                              child: Text(
                                l10n.deleteAccount,
                                style: TextStyle(
                                  color: theme.colorScheme.error,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              // Description (if available)
              if (account.description?.isNotEmpty ?? false) ...[
                const SizedBox(height: AppSpacing.sm),
                Text(
                  account.description!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  IconData _getAccountIcon() {
    // Use account-specific icon if available
    if (account.iconName?.isNotEmpty ?? false) {
      return iconFromName(account.iconName!);
    }

    // Fallback to type-based icons
    switch (account.type) {
      case AccountType.checking:
        return Icons.account_balance;
      case AccountType.savings:
        return Icons.savings;
      case AccountType.creditCard:
        return Icons.credit_card;
      case AccountType.cash:
        return Icons.account_balance_wallet;
      case AccountType.investment:
        return Icons.trending_up;
      case AccountType.loan:
        return Icons.account_balance_outlined;
    }
  }

  Color? _getAccountColor() {
    if (account.colorHex?.isNotEmpty ?? false) {
      try {
        return parseHex(account.colorHex!);
      } on Exception {
        // Invalid color hex, use default
      }
    }

    // Default colors based on account type
    switch (account.type) {
      case AccountType.checking:
        return AppColors.primary;
      case AccountType.savings:
        return AppColors.success;
      case AccountType.creditCard:
        return AppColors.warning;
      case AccountType.cash:
        return AppColors.info;
      case AccountType.investment:
        return AppColors.tertiary;
      case AccountType.loan:
        return AppColors.error;
    }
  }

  Color _getBalanceColor(ThemeData theme) {
    if (account.classification == AccountClassification.liability) {
      // For liabilities, positive balance is debt (red), negative is overpayment (green)
      return account.currentBalanceCents > 0
          ? AppColors.error
          : AppColors.success;
    } else {
      // For assets, positive balance is good (green), negative is concerning (red)
      return account.currentBalanceCents >= 0
          ? theme.colorScheme.onSurface
          : AppColors.error;
    }
  }

  String _formatBalanceWithoutCurrency(int cents) {
    final amount = cents / 100;
    return amount.toStringAsFixed(2);
  }
}
