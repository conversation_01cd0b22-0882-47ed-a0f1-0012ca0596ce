import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';

/// Widget for selecting account icon from predefined options
class AccountIconSelector extends StatelessWidget {
  const AccountIconSelector({
    super.key,
    required this.selectedIcon,
    required this.onIconChanged,
    this.errorText,
  });

  final String? selectedIcon;
  final void Function(String?) onIconChanged;
  final String? errorText;

  static const Map<String, IconData> _availableIcons = {
    'account_balance': Icons.account_balance,
    'account_balance_wallet': Icons.account_balance_wallet,
    'savings': Icons.savings,
    'payments': Icons.payments,
    'credit_card': Icons.credit_card,
    'money_off': Icons.money_off,
    'trending_up': Icons.trending_up,
    'home': Icons.home,
    'directions_car': Icons.directions_car,
    'shopping_cart': Icons.shopping_cart,
    'restaurant': Icons.restaurant,
    'local_gas_station': Icons.local_gas_station,
    'school': Icons.school,
    'work': Icons.work,
    'favorite': Icons.favorite,
    'star': Icons.star,
  };

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              l10n.accountIcon,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: AppTypography.fontWeightMedium,
              ),
            ),
            const SizedBox(width: AppSpacing.sm),
            Text(
              l10n.optional,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.sm),

        // Icon grid
        Wrap(
          spacing: AppSpacing.sm,
          runSpacing: AppSpacing.sm,
          children: [
            // None option
            _buildIconOption(
              context,
              null,
              Icons.circle_outlined,
              l10n.defaultIcon,
              theme,
            ),
            // Icon options
            ..._availableIcons.entries.map(
              (entry) => _buildIconOption(
                context,
                entry.key,
                entry.value,
                null,
                theme,
              ),
            ),
          ],
        ),

        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildIconOption(
    BuildContext context,
    String? iconName,
    IconData iconData,
    String? tooltip,
    ThemeData theme,
  ) {
    final isSelected = selectedIcon == iconName;

    return GestureDetector(
      onTap: () => onIconChanged(iconName),
      child: Tooltip(
        message: tooltip ?? iconName ?? '',
        child: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: isSelected
                ? theme.colorScheme.primaryContainer
                : theme.colorScheme.surfaceContainerHighest,
            border: Border.all(
              color: isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.outline,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(AppBorderRadius.sm),
          ),
          child: Icon(
            iconData,
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurfaceVariant,
            size: 24,
          ),
        ),
      ),
    );
  }
}
