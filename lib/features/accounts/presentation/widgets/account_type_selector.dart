import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/account.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';

/// Widget for selecting account type and automatically setting classification
class AccountTypeSelector extends StatelessWidget {
  const AccountTypeSelector({
    super.key,
    required this.selectedType,
    required this.selectedClassification,
    required this.onTypeChanged,
    this.errorText,
    this.showLabel = true,
  });

  final AccountType? selectedType;
  final AccountClassification? selectedClassification;
  final void Function(AccountType?, AccountClassification?) onTypeChanged;
  final String? errorText;
  final bool showLabel;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showLabel) ...[
          Text(
            l10n.accountType,
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: AppTypography.fontWeightMedium,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
        ],

        // Asset accounts section
        _buildSectionHeader(l10n.assets, theme),
        const SizedBox(height: AppSpacing.xs),
        _buildTypeOption(
          context,
          AccountType.checking,
          AccountClassification.asset,
          l10n.checkingAccount,
          Icons.account_balance,
          theme,
        ),
        _buildTypeOption(
          context,
          AccountType.savings,
          AccountClassification.asset,
          l10n.savingsAccount,
          Icons.savings,
          theme,
        ),
        _buildTypeOption(
          context,
          AccountType.cash,
          AccountClassification.asset,
          l10n.cashAccount,
          Icons.payments,
          theme,
        ),
        _buildTypeOption(
          context,
          AccountType.investment,
          AccountClassification.asset,
          l10n.investmentAccount,
          Icons.trending_up,
          theme,
        ),

        const SizedBox(height: AppSpacing.md),

        // Liability accounts section
        _buildSectionHeader(l10n.liabilities, theme),
        const SizedBox(height: AppSpacing.xs),
        _buildTypeOption(
          context,
          AccountType.creditCard,
          AccountClassification.liability,
          l10n.creditCardAccount,
          Icons.credit_card,
          theme,
        ),
        _buildTypeOption(
          context,
          AccountType.loan,
          AccountClassification.liability,
          l10n.loanAccount,
          Icons.money_off,
          theme,
        ),

        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSectionHeader(String title, ThemeData theme) {
    return Text(
      title,
      style: theme.textTheme.bodyMedium?.copyWith(
        fontWeight: AppTypography.fontWeightMedium,
        color: theme.colorScheme.onSurfaceVariant,
      ),
    );
  }

  Widget _buildTypeOption(
    BuildContext context,
    AccountType type,
    AccountClassification classification,
    String label,
    IconData icon,
    ThemeData theme,
  ) {
    final isSelected = selectedType == type;

    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.xs),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onTypeChanged(type, classification),
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
          child: Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.outline,
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(AppBorderRadius.md),
              color: isSelected
                  ? theme.colorScheme.primaryContainer.withValues(alpha: 0.3)
                  : Colors.transparent,
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: isSelected
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurfaceVariant,
                  size: 24,
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Text(
                    label,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurface,
                      fontWeight: isSelected
                          ? AppTypography.fontWeightMedium
                          : AppTypography.fontWeightRegular,
                    ),
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
