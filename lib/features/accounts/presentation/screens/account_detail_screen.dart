import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/account.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/routing/app_router.dart';
import 'package:budapp/utils/color_utils.dart';
import 'package:budapp/utils/icon_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

/// Screen displaying detailed information about a specific account
class AccountDetailScreen extends ConsumerWidget {
  const AccountDetailScreen({super.key, required this.accountId});
  final String accountId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final accountAsync = ref.watch(accountProvider(accountId));

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.accountDetails),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) => _handleMenuAction(context, ref, value),
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'edit',
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.edit),
                    const SizedBox(width: AppSpacing.sm),
                    Flexible(child: Text(l10n.editAccount)),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'setPrimary',
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.star),
                    const SizedBox(width: AppSpacing.sm),
                    Flexible(child: Text(l10n.setPrimary)),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'deactivate',
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.visibility_off),
                    const SizedBox(width: AppSpacing.sm),
                    Flexible(child: Text(l10n.deactivateAccount)),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'delete',
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.delete, color: theme.colorScheme.error),
                    const SizedBox(width: AppSpacing.sm),
                    Flexible(
                      child: Text(
                        l10n.deleteAccount,
                        style: TextStyle(color: theme.colorScheme.error),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: accountAsync.when(
        data: (account) => account != null
            ? _buildAccountDetail(context, ref, account, theme, l10n)
            : _buildNotFoundState(context, theme, l10n),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) =>
            _buildErrorState(context, ref, error, theme, l10n),
      ),
    );
  }

  Widget _buildAccountDetail(
    BuildContext context,
    WidgetRef ref,
    Account account,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Account header card
          _buildAccountHeader(account, theme, l10n),
          const SizedBox(height: AppSpacing.lg),

          // Account details section
          _buildDetailsSection(account, theme, l10n),
          const SizedBox(height: AppSpacing.lg),

          // Account settings section
          _buildSettingsSection(account, theme, l10n),
          const SizedBox(height: AppSpacing.lg),

          // Recent transactions placeholder
          _buildRecentTransactionsSection(context, account.id, theme, l10n),
        ],
      ),
    );
  }

  Widget _buildAccountHeader(
    Account account,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    final accountColor = account.colorHex != null
        ? parseHex(account.colorHex!)
        : theme.colorScheme.primary;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: accountColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppBorderRadius.lg),
                  ),
                  child: Icon(
                    _getAccountIcon(account.iconName),
                    color: accountColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              account.name,
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: AppTypography.fontWeightSemiBold,
                              ),
                            ),
                          ),
                          if (account.isPrimary) ...[
                            const SizedBox(width: AppSpacing.sm),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppSpacing.sm,
                                vertical: AppSpacing.xs,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.warning,
                                borderRadius: BorderRadius.circular(
                                  AppBorderRadius.sm,
                                ),
                              ),
                              child: Text(
                                l10n.primary,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: AppTypography.fontWeightMedium,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: AppSpacing.xs),
                      Row(
                        children: [
                          Text(
                            _getAccountTypeText(account.type, l10n),
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                          const SizedBox(width: AppSpacing.sm),
                          Container(
                            width: 4,
                            height: 4,
                            decoration: BoxDecoration(
                              color: theme.colorScheme.onSurfaceVariant,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: AppSpacing.sm),
                          Consumer(
                            builder: (context, ref, _) {
                              final currencyFormatter = ref.watch(
                                currencyFormatterProvider,
                              );
                              return Text(
                                currencyFormatter.displayName,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.lg),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  l10n.currentBalance,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                Consumer(
                  builder: (context, ref, _) {
                    final currencyFormatter = ref.watch(
                      currencyFormatterProvider,
                    );
                    return Text(
                      currencyFormatter.formatAmount(
                        account.currentBalanceCents,
                      ),
                      style: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: AppTypography.fontWeightSemiBold,
                        color:
                            account.classification ==
                                AccountClassification.asset
                            ? AppColors.success
                            : AppColors.error,
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsSection(
    Account account,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.accountDetails,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: AppTypography.fontWeightSemiBold,
              ),
            ),
            const SizedBox(height: AppSpacing.md),
            _buildDetailRow(
              l10n.accountType,
              _getAccountTypeText(account.type, l10n),
              theme,
            ),
            _buildDetailRow(
              l10n.classification,
              _getClassificationText(account.classification, l10n),
              theme,
            ),
            Consumer(
              builder: (context, ref, _) {
                final currencyFormatter = ref.watch(currencyFormatterProvider);
                return _buildDetailRow(
                  l10n.currency,
                  currencyFormatter.displayName,
                  theme,
                );
              },
            ),
            _buildDetailRow(
              l10n.dateCreated,
              _formatDate(account.createdAt),
              theme,
            ),
            _buildDetailRow(
              l10n.lastUpdated,
              _formatDate(account.updatedAt),
              theme,
            ),
            if (account.description?.isNotEmpty ?? false) ...[
              const SizedBox(height: AppSpacing.sm),
              Text(
                l10n.description,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: AppTypography.fontWeightMedium,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: AppSpacing.xs),
              Text(account.description!, style: theme.textTheme.bodyMedium),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: AppTypography.fontWeightMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(
    Account account,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.accountSettings,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: AppTypography.fontWeightSemiBold,
              ),
            ),
            const SizedBox(height: AppSpacing.md),
            Row(
              children: [
                Icon(
                  account.isActive ? Icons.visibility : Icons.visibility_off,
                  color: account.isActive ? AppColors.success : AppColors.error,
                  size: 20,
                ),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  account.isActive ? l10n.active : l10n.inactive,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: account.isActive
                        ? AppColors.success
                        : AppColors.error,
                    fontWeight: AppTypography.fontWeightMedium,
                  ),
                ),
              ],
            ),
            if (account.isPrimary) ...[
              const SizedBox(height: AppSpacing.sm),
              Row(
                children: [
                  const Icon(Icons.star, color: AppColors.warning, size: 20),
                  const SizedBox(width: AppSpacing.sm),
                  Text(
                    l10n.primaryAccount,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: AppColors.warning,
                      fontWeight: AppTypography.fontWeightMedium,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRecentTransactionsSection(
    BuildContext context,
    String accountId,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  l10n.recentTransactions,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: AppTypography.fontWeightSemiBold,
                  ),
                ),
                TextButton(
                  onPressed: () => context.push(
                    '${AppRoutes.transactions}/account/$accountId',
                  ),
                  child: Text(l10n.viewAll),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.md),
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.receipt_long_outlined,
                    size: 48,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(height: AppSpacing.sm),
                  Text(
                    l10n.noTransactionsYet,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: AppSpacing.xs),
                  Text(
                    l10n.transactionsWillAppearHere,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotFoundState(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_wallet_outlined,
              size: 64,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              l10n.accountNotFound,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              l10n.accountMayHaveBeenDeleted,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.lg),
            FilledButton(
              onPressed: () => context.pop(),
              child: Text(l10n.goBack),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(
    BuildContext context,
    WidgetRef ref,
    Object error,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
            const SizedBox(height: AppSpacing.md),
            Text(
              l10n.errorLoadingAccount,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              error.toString(),
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.lg),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                OutlinedButton(
                  onPressed: () => context.pop(),
                  child: Text(l10n.goBack),
                ),
                const SizedBox(width: AppSpacing.md),
                FilledButton(
                  onPressed: () => ref.invalidate(accountProvider(accountId)),
                  child: Text(l10n.retry),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(BuildContext context, WidgetRef ref, String action) {
    final l10n = AppLocalizations.of(context)!;
    final updateNotifier = ref.read(accountUpdateProvider.notifier);

    switch (action) {
      case 'edit':
        context.push('/accounts/$accountId/edit');
      case 'setPrimary':
        _showConfirmDialog(
          context,
          l10n.setPrimaryAccount,
          l10n.setPrimaryAccountConfirmation,
          () => updateNotifier.setPrimaryAccount(accountId),
        );
      case 'deactivate':
        _showConfirmDialog(
          context,
          l10n.deactivateAccount,
          l10n.deactivateAccountConfirmation,
          () => updateNotifier.deactivateAccount(accountId),
        );
      case 'delete':
        _showConfirmDialog(
          context,
          l10n.deleteAccount,
          l10n.deleteAccountConfirmation,
          () => updateNotifier.deleteAccount(accountId),
          isDestructive: true,
        );
    }
  }

  void _showConfirmDialog(
    BuildContext context,
    String title,
    String message,
    VoidCallback onConfirm, {
    bool isDestructive = false,
  }) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm();
              // Navigate back after successful action
              context.pop();
            },
            style: isDestructive
                ? FilledButton.styleFrom(
                    backgroundColor: theme.colorScheme.error,
                    foregroundColor: theme.colorScheme.onError,
                  )
                : null,
            child: Text(l10n.confirm),
          ),
        ],
      ),
    );
  }

  IconData _getAccountIcon(String? iconName) {
    return iconFromName(iconName ?? 'account_balance_wallet');
  }

  String _getAccountTypeText(AccountType type, AppLocalizations l10n) {
    switch (type) {
      case AccountType.checking:
        return l10n.checking;
      case AccountType.savings:
        return l10n.savings;
      case AccountType.creditCard:
        return l10n.creditCard;
      case AccountType.loan:
        return l10n.loan;
      case AccountType.investment:
        return l10n.investment;
      case AccountType.cash:
        return l10n.cash;
    }
  }

  String _getClassificationText(
    AccountClassification classification,
    AppLocalizations l10n,
  ) {
    switch (classification) {
      case AccountClassification.asset:
        return l10n.asset;
      case AccountClassification.liability:
        return l10n.liability;
    }
  }

  // Removed _formatCurrency method - now using global currency formatter

  String _formatDate(DateTime? dateTime) {
    if (dateTime == null) return 'N/A';
    return DateFormat.yMMMd().format(dateTime);
  }
}
