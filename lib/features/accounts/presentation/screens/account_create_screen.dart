import 'package:budapp/data/models/account.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/widgets/forms/configs/account_form_config.dart';
import 'package:budapp/widgets/forms/screens/generic_form_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Account creation screen using the generic form system
class AccountCreateScreen extends ConsumerWidget {
  const AccountCreateScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final repository = ref.watch(accountRepositoryProvider);

    final config = AccountFormConfig.create(
      repository: repository,
      onFieldChanged: (fieldKey, value) {
        // Optional: Handle field changes for real-time validation or updates
        debugPrint('Account field $fieldKey changed to: $value');
      },
    );

    return GenericFormScreen<Account>(config: config);
  }
}
