import 'package:budapp/data/models/account.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/widgets/forms/configs/account_form_config.dart';
import 'package:budapp/widgets/forms/screens/generic_form_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Account editing screen using the generic form system
class AccountEditScreen extends ConsumerWidget {
  const AccountEditScreen({super.key, required this.accountId});
  final String accountId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final repository = ref.watch(accountRepositoryProvider);
    final accountAsync = ref.watch(accountProvider(accountId));

    return accountAsync.when(
      data: (account) {
        if (account == null) {
          return Scaffold(
            appBar: AppBar(title: const Text('Account Not Found')),
            body: const Center(
              child: Text('The requested account could not be found.'),
            ),
          );
        }

        final config = AccountFormConfig.edit(
          account: account,
          repository: repository,
          onFieldChanged: (fieldKey, value) {
            // Optional: Handle field changes for real-time validation or updates
            debugPrint('Account field $fieldKey changed to: $value');
          },
        );

        return GenericFormScreen<Account>(config: config);
      },
      loading: () => Scaffold(
        appBar: AppBar(title: const Text('Edit Account')),
        body: const Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(child: Text('Error loading account: $error')),
      ),
    );
  }
}
