import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/repositories/implementations/account_repository_impl.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Provider for the list of all user accounts (real-time)
final accountListProvider = StreamProvider<List<Account>>((ref) {
  final authService = ref.watch(authServiceProvider);
  final user = authService.currentUser;
  if (user == null) throw Exception('User not authenticated');

  final accountRepository = ref.watch(accountRepositoryProvider);
  return accountRepository.watchUserAccounts(user.uid);
});

/// Provider for a specific account by ID (using stream)
final StreamProviderFamily<Account?, String> accountProvider =
    StreamProvider.family<Account?, String>((
      ref,
      accountId,
    ) {
      final authService = ref.watch(authServiceProvider);
      final user = authService.currentUser;
      if (user == null) throw Exception('User not authenticated');

      final accountRepository = ref.watch(accountRepositoryProvider);
      return accountRepository.watchAccountForUser(user.uid, accountId);
    });

/// Provider for active accounts only - PERFORMANCE OPTIMIZED
/// Uses single Firestore query instead of client-side filtering
final activeAccountsProvider = StreamProvider<List<Account>>((ref) {
  final authService = ref.watch(authServiceProvider);
  final user = authService.currentUser;
  if (user == null) throw Exception('User not authenticated');

  final accountRepository = ref.watch(accountRepositoryProvider);
  return accountRepository
      .watchUserAccounts(user.uid)
      .map(
        (accounts) => accounts.where((account) => account.isActive).toList(),
      );
});

/// Provider for asset accounts - PERFORMANCE OPTIMIZED
/// Direct stream from repository instead of cascade filtering
final assetAccountsProvider = StreamProvider<List<Account>>((ref) {
  final authService = ref.watch(authServiceProvider);
  final user = authService.currentUser;
  if (user == null) throw Exception('User not authenticated');

  final accountRepository = ref.watch(accountRepositoryProvider);
  return accountRepository
      .watchUserAccounts(user.uid)
      .map(
        (accounts) => accounts
            .where(
              (account) =>
                  account.isActive &&
                  account.classification == AccountClassification.asset,
            )
            .toList(),
      );
});

/// Provider for liability accounts - PERFORMANCE OPTIMIZED
/// Direct stream from repository instead of cascade filtering
final liabilityAccountsProvider = StreamProvider<List<Account>>((ref) {
  final authService = ref.watch(authServiceProvider);
  final user = authService.currentUser;
  if (user == null) throw Exception('User not authenticated');

  final accountRepository = ref.watch(accountRepositoryProvider);
  return accountRepository
      .watchUserAccounts(user.uid)
      .map(
        (accounts) => accounts
            .where(
              (account) =>
                  account.isActive &&
                  account.classification == AccountClassification.liability,
            )
            .toList(),
      );
});

/// Provider for the primary account
final primaryAccountProvider = FutureProvider<Account?>((ref) async {
  final authService = ref.watch(authServiceProvider);
  final user = authService.currentUser;
  if (user == null) return null;

  final accountRepository = ref.watch(accountRepositoryProvider);
  return accountRepository.getPrimaryAccount(user.uid);
});

/// Account creation notifier
class AccountCreationNotifier extends AsyncNotifier<void> {
  @override
  Future<void> build() async {
    // Initial state
  }

  Future<void> createAccount(Account account) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final accountRepository = ref.read(accountRepositoryProvider);
      await accountRepository.createAccount(account);

      // PERFORMANCE OPTIMIZATION: Invalidate specific cache entries
      if (accountRepository is AccountRepositoryImpl) {
        await accountRepository.invalidateCache([
          'user_account_balances_${account.userId}',
          'user_account_summary_${account.userId}',
        ]);
      }

      // Invalidate relevant providers to refresh the UI
      ref.invalidate(accountListProvider);
      ref.invalidate(activeAccountsProvider);
      ref.invalidate(assetAccountsProvider);
      ref.invalidate(liabilityAccountsProvider);
      ref.invalidate(accountStatsProvider);
    });
  }
}

/// Provider for account creation
final accountCreationProvider =
    AsyncNotifierProvider<AccountCreationNotifier, void>(
      AccountCreationNotifier.new,
    );

/// Account update notifier
class AccountUpdateNotifier extends AsyncNotifier<void> {
  @override
  Future<void> build() async {
    // Initial state
  }

  Future<void> updateAccount(Account account) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final accountRepository = ref.read(accountRepositoryProvider);
      await accountRepository.updateAccount(account.id, account);

      // PERFORMANCE OPTIMIZATION: Invalidate specific cache entries
      if (accountRepository is AccountRepositoryImpl) {
        await accountRepository.invalidateCache([
          'user_account_balances_${account.userId}',
          'user_account_summary_${account.userId}',
        ]);
      }

      // Invalidate providers to refresh the UI
      ref
        ..invalidate(accountListProvider)
        ..invalidate(accountProvider(account.id))
        ..invalidate(activeAccountsProvider)
        ..invalidate(assetAccountsProvider)
        ..invalidate(liabilityAccountsProvider)
        ..invalidate(accountStatsProvider);
    });
  }

  Future<void> deleteAccount(String accountId) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final accountRepository = ref.read(accountRepositoryProvider);
      await accountRepository.deleteAccount(accountId);
      // Invalidate providers to refresh the UI
      ref.invalidate(accountListProvider);
    });
  }

  Future<void> deactivateAccount(String accountId) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final accountRepository = ref.read(accountRepositoryProvider);
      await accountRepository.deactivateAccount(accountId);
      // Invalidate providers to refresh the UI
      ref
        ..invalidate(accountListProvider)
        ..invalidate(accountProvider(accountId));
    });
  }

  Future<void> setPrimaryAccount(String accountId) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final authService = ref.read(authServiceProvider);
      final user = authService.currentUser;
      if (user == null) throw Exception('User not authenticated');

      final accountRepository = ref.read(accountRepositoryProvider);
      await accountRepository.setPrimaryAccount(user.uid, accountId);

      // Invalidate providers to refresh the UI
      ref
        ..invalidate(accountListProvider)
        ..invalidate(primaryAccountProvider);
    });
  }
}

/// Provider for account updates
final accountUpdateProvider =
    AsyncNotifierProvider<AccountUpdateNotifier, void>(
      AccountUpdateNotifier.new,
    );

/// Computed provider for account statistics - PERFORMANCE OPTIMIZED
/// Uses optimized repository method with intelligent caching
final accountStatsProvider = StreamProvider<AccountStats>((ref) {
  final authService = ref.watch(authServiceProvider);
  final user = authService.currentUser;
  if (user == null) throw Exception('User not authenticated');

  final accountRepository = ref.watch(accountRepositoryProvider);

  // Use the optimized getUserAccountSummary method with caching
  return Stream.fromFuture(
    accountRepository.getUserAccountSummary(user.uid),
  ).map((summary) {
    return AccountStats(
      totalAccounts: summary['activeAccounts'] as int? ?? 0,
      totalAssets: summary['totalAssets'] as int? ?? 0,
      totalLiabilities: summary['totalLiabilities'] as int? ?? 0,
      netWorth: summary['netWorth'] as int? ?? 0,
      assetAccountCount: summary['assetAccounts'] as int? ?? 0,
      liabilityAccountCount: summary['liabilityAccounts'] as int? ?? 0,
    );
  });
});

/// Data class for account statistics
class AccountStats {
  const AccountStats({
    required this.totalAccounts,
    required this.totalAssets,
    required this.totalLiabilities,
    required this.netWorth,
    required this.assetAccountCount,
    required this.liabilityAccountCount,
  });
  final int totalAccounts;
  final int totalAssets;
  final int totalLiabilities;
  final int netWorth;
  final int assetAccountCount;
  final int liabilityAccountCount;
}
