import 'package:budapp/data/models/account.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/utils/validation_helpers.dart';
import 'package:budapp/utils/validation_result.dart';
import 'package:flutter/material.dart';

/// Validation utilities for account management forms
// ignore: avoid_classes_with_only_static_members
class AccountValidators {
  /// Validates account name field
  static String? accountName(String? value, BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    // Combine multiple validation checks
    final results = [
      validateRequired(context, value, customMessage: l10n.accountNameRequired),
      _validateAccountNameLength(context, value, l10n),
      validatePattern(
        context,
        value,
        RegExp(
          r'^[a-zA-Z0-9\s\-_.()&'
          "'"
          r',]+$',
        ),
        customMessage: l10n.accountNameInvalidCharacters,
      ),
    ];

    final combinedResult = combineResults(results);
    return toFormFieldValidator(combinedResult);
  }

  /// Validates account description field (optional)
  static String? accountDescription(String? value, BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    // Description is optional, so only validate length if provided
    final lengthResult = _validateAccountDescriptionLength(
      context,
      value,
      l10n,
    );

    return toFormFieldValidator(lengthResult);
  }

  /// Validates initial balance field
  static String? initialBalance(String? value, BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    // Use custom validation for initial balance to match expected error messages
    if (value == null || value.trim().isEmpty) {
      return l10n.initialBalanceRequired;
    }

    // Try to parse as double
    final balance = double.tryParse(value.trim());
    if (balance == null) {
      return l10n.initialBalanceInvalid;
    }

    // Check for reasonable range (allow negative for liabilities)
    if (balance < -*********.99 || balance > *********.99) {
      return l10n.initialBalanceOutOfRange;
    }

    // Check decimal places (max 2 for currency)
    final parts = value.trim().split('.');
    if (parts.length > 2) {
      return l10n.initialBalanceInvalid;
    }
    if (parts.length == 2 && parts[1].length > 2) {
      return l10n.initialBalanceMaxDecimals;
    }

    return null;
  }

  // Currency validation removed - using global currency preference

  /// Validates account type selection
  static String? accountType(AccountType? value, BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    final result = validateNotNull(
      context,
      value,
      customMessage: l10n.accountTypeRequired,
    );

    return toFormFieldValidator(result);
  }

  /// Validates account classification selection
  static String? accountClassification(
    AccountClassification? value,
    BuildContext context,
  ) {
    final l10n = AppLocalizations.of(context)!;

    final result = validateNotNull(
      context,
      value,
      customMessage: l10n.accountClassificationRequired,
    );

    return toFormFieldValidator(result);
  }

  /// Validates account type and classification pairing
  static String? validateTypeClassificationPair(
    AccountType? type,
    AccountClassification? classification,
    BuildContext context,
  ) {
    if (type == null || classification == null) {
      return null; // Let individual validators handle null values
    }

    // Define valid type-classification pairs
    final validPairs = <AccountType, Set<AccountClassification>>{
      AccountType.checking: {AccountClassification.asset},
      AccountType.savings: {AccountClassification.asset},
      AccountType.cash: {AccountClassification.asset},
      AccountType.investment: {AccountClassification.asset},
      AccountType.creditCard: {AccountClassification.liability},
      AccountType.loan: {AccountClassification.liability},
    };

    final allowedClassifications = validPairs[type];
    if (allowedClassifications == null ||
        !allowedClassifications.contains(classification)) {
      return AppLocalizations.of(context)!.accountTypeClassificationMismatch;
    }

    return null;
  }

  /// Validates hex color code
  static String? colorHex(String? value, BuildContext context) {
    // Color is optional, so use the generic hex color validator
    final result = validateHexColor(context, value);
    return toFormFieldValidator(result);
  }

  /// Validates icon name
  static String? iconName(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return null; // Icon is optional
    }

    // Icon name should be a valid identifier
    final iconNameRegex = RegExp(r'^[a-zA-Z][a-zA-Z0-9_]*$');
    if (!iconNameRegex.hasMatch(value.trim())) {
      return AppLocalizations.of(context)!.iconNameInvalid;
    }

    return null;
  }

  /// Comprehensive account validation for form submission
  static Map<String, String> validateAccount({
    required String? name,
    String? description,
    required String? initialBalance,
    required AccountType? type,
    required AccountClassification? classification,
    String? colorHex,
    String? iconName,
    required BuildContext context,
  }) {
    final errors = <String, String>{};

    final nameError = accountName(name, context);
    if (nameError != null) errors['name'] = nameError;

    final descriptionError = accountDescription(description, context);
    if (descriptionError != null) errors['description'] = descriptionError;

    final balanceError = AccountValidators.initialBalance(
      initialBalance,
      context,
    );
    if (balanceError != null) errors['initialBalance'] = balanceError;

    // Currency validation removed - using global currency preference

    final typeError = accountType(type, context);
    if (typeError != null) errors['type'] = typeError;

    final classificationError = accountClassification(classification, context);
    if (classificationError != null) {
      errors['classification'] = classificationError;
    }

    final typeClassificationError = validateTypeClassificationPair(
      type,
      classification,
      context,
    );
    if (typeClassificationError != null) {
      errors['typeClassification'] = typeClassificationError;
    }

    final colorError = AccountValidators.colorHex(colorHex, context);
    if (colorError != null) errors['colorHex'] = colorError;

    final iconError = AccountValidators.iconName(iconName, context);
    if (iconError != null) errors['iconName'] = iconError;

    return errors;
  }

  /// Utility to check if account form is valid
  static bool isAccountFormValid({
    required String? name,
    String? description,
    required String? initialBalance,
    required AccountType? type,
    required AccountClassification? classification,
    String? colorHex,
    String? iconName,
    required BuildContext context,
  }) {
    final errors = validateAccount(
      name: name,
      description: description,
      initialBalance: initialBalance,
      type: type,
      classification: classification,
      colorHex: colorHex,
      iconName: iconName,
      context: context,
    );
    return errors.isEmpty;
  }

  /// Convert initial balance string to cents for storage
  static int? parseBalanceToCents(String? balanceString) {
    if (balanceString == null || balanceString.trim().isEmpty) {
      return null;
    }

    final balance = double.tryParse(balanceString.trim());
    if (balance == null) {
      return null;
    }

    return (balance * 100).round();
  }

  /// Convert cents to balance string for display
  static String formatCentsToBalance(int cents) {
    return (cents / 100.0).toStringAsFixed(2);
  }

  // Private helper methods for specific validation with custom messages

  /// Helper method for account name length validation with specific messages
  static ValidationResult _validateAccountNameLength(
    BuildContext context,
    String? value,
    AppLocalizations l10n,
  ) {
    if (value == null || value.trim().isEmpty) {
      return ValidationResult.success(); // Let required validation handle this
    }

    final trimmedValue = value.trim();

    if (trimmedValue.length < 2) {
      return ValidationResult.singleError(l10n.accountNameMinLength);
    }

    if (trimmedValue.length > 50) {
      return ValidationResult.singleError(l10n.accountNameMaxLength);
    }

    return ValidationResult.success();
  }

  /// Helper method for account description length validation with specific messages
  static ValidationResult _validateAccountDescriptionLength(
    BuildContext context,
    String? value,
    AppLocalizations l10n,
  ) {
    if (value == null || value.trim().isEmpty) {
      return ValidationResult.success(); // Description is optional
    }

    if (value.trim().length > 200) {
      return ValidationResult.singleError(l10n.accountDescriptionMaxLength);
    }

    return ValidationResult.success();
  }
}
