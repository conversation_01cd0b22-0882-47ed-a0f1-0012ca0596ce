import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/config/environment_config.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/common/widgets/app_bar_helpers.dart';
import 'package:budapp/features/dashboard/providers/dashboard_providers.dart';
import 'package:budapp/widgets/performance_tracked_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Main home screen for authenticated users
class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  // Predefined map of common icon codes to IconData for tree shaking compatibility
  static const Map<int, IconData> _iconCodeMap = {
    // Common Material Icons codes
    57415: Icons.restaurant, // food/restaurant
    57796: Icons.local_gas_station, // gas station
    59476: Icons.shopping_cart, // shopping
    57388: Icons.movie, // entertainment
    57778: Icons.medical_services, // health
    57402: Icons.directions_car, // transport
    58136: Icons.home, // home
    59641: Icons.work, // work
    57392: Icons.attach_money, // salary/money
    59235: Icons.trending_up, // investment
    57669: Icons.card_giftcard, // gift
    57421: Icons.school, // education
    57423: Icons.category, // default category
  };

  String _formatCurrency(int cents) {
    final dollars = cents / 100;
    return '\$${dollars.toStringAsFixed(2)}';
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'N/A';
    return '${date.month}/${date.day}/${date.year}';
  }

  IconData _getIconFromString(String iconString) {
    // Try to parse as integer first (for backward compatibility)
    final intValue = int.tryParse(iconString);
    if (intValue != null) {
      // Use predefined map for tree shaking compatibility
      return _iconCodeMap[intValue] ?? Icons.category;
    }

    // Map common icon names to IconData
    switch (iconString.toLowerCase()) {
      case 'school':
        return Icons.school;
      case 'food':
      case 'restaurant':
        return Icons.restaurant;
      case 'gas':
      case 'local_gas_station':
        return Icons.local_gas_station;
      case 'shopping':
      case 'shopping_cart':
        return Icons.shopping_cart;
      case 'entertainment':
      case 'movie':
        return Icons.movie;
      case 'health':
      case 'medical_services':
        return Icons.medical_services;
      case 'transport':
      case 'directions_car':
        return Icons.directions_car;
      case 'home':
        return Icons.home;
      case 'work':
        return Icons.work;
      case 'salary':
      case 'attach_money':
        return Icons.attach_money;
      case 'investment':
      case 'trending_up':
        return Icons.trending_up;
      case 'gift':
        return Icons.card_giftcard;
      default:
        return Icons.category;
    }
  }

  Widget _buildFinancialSummaryCard({
    required String title,
    required int amount,
    required Color color,
    required ThemeData theme,
  }) {
    return Card(
      elevation: AppElevation.sm,
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: AppTypography.fontWeightMedium,
              ),
            ),
            const SizedBox(height: AppSpacing.xs),
            Text(
              _formatCurrency(amount),
              style: theme.textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: AppTypography.fontWeightSemiBold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentPeriodBalanceCard(ThemeData theme) {
    return Consumer(
      builder: (context, ref, child) {
        final balanceAsync = ref.watch(currentPeriodBalanceProvider);

        return balanceAsync.when(
          data: (balance) => Card(
            elevation: AppElevation.sm,
            child: InkWell(
              onTap: () => context.push('/transactions'),
              borderRadius: BorderRadius.circular(AppBorderRadius.md),
              child: Padding(
                padding: const EdgeInsets.all(AppSpacing.lg),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'This Month',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: AppTypography.fontWeightSemiBold,
                          ),
                        ),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSpacing.md),
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Income',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                              const SizedBox(height: AppSpacing.xs),
                              Text(
                                _formatCurrency(balance.totalIncome),
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: AppColors.success,
                                  fontWeight: AppTypography.fontWeightSemiBold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Expenses',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                              const SizedBox(height: AppSpacing.xs),
                              Text(
                                _formatCurrency(balance.totalExpenses),
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: AppColors.error,
                                  fontWeight: AppTypography.fontWeightSemiBold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Net',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                              const SizedBox(height: AppSpacing.xs),
                              Text(
                                _formatCurrency(balance.netIncome),
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: balance.netIncome >= 0
                                      ? AppColors.success
                                      : AppColors.error,
                                  fontWeight: AppTypography.fontWeightSemiBold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          loading: () => Card(
            elevation: AppElevation.sm,
            child: Container(
              height: 120,
              padding: const EdgeInsets.all(AppSpacing.lg),
              child: const Center(child: CircularProgressIndicator()),
            ),
          ),
          error: (error, stack) => Card(
            elevation: AppElevation.sm,
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.lg),
              child: Text('Error loading balance: $error'),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentTransactionsCard(ThemeData theme) {
    return Consumer(
      builder: (context, ref, child) {
        final transactionsAsync = ref.watch(
          recentTransactionsForDashboardProvider,
        );

        return transactionsAsync.when(
          data: (transactions) => Card(
            elevation: AppElevation.sm,
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.lg),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Recent Transactions',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: AppTypography.fontWeightSemiBold,
                        ),
                      ),
                      TextButton(
                        onPressed: () => context.push('/transactions'),
                        child: const Text('View All'),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppSpacing.md),
                  if (transactions.isEmpty)
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: AppSpacing.lg,
                      ),
                      child: Center(
                        child: Text(
                          'No recent transactions',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    )
                  else
                    ...transactions.map(
                      (transaction) => Padding(
                        padding: const EdgeInsets.only(bottom: AppSpacing.sm),
                        child: Row(
                          children: [
                            Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color:
                                    transaction.type == TransactionType.income
                                    ? AppColors.success.withValues(alpha: 0.1)
                                    : AppColors.error.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(
                                  AppBorderRadius.sm,
                                ),
                              ),
                              child: Icon(
                                transaction.type == TransactionType.income
                                    ? Icons.arrow_downward
                                    : Icons.arrow_upward,
                                color:
                                    transaction.type == TransactionType.income
                                    ? AppColors.success
                                    : AppColors.error,
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: AppSpacing.md),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    transaction.description ?? 'No description',
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      fontWeight:
                                          AppTypography.fontWeightMedium,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Text(
                                    _formatDate(transaction.transactionDate),
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: theme.colorScheme.onSurfaceVariant,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Text(
                              '${transaction.type == TransactionType.income ? '+' : '-'}${_formatCurrency(transaction.amountCents)}',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color:
                                    transaction.type == TransactionType.income
                                    ? AppColors.success
                                    : AppColors.error,
                                fontWeight: AppTypography.fontWeightSemiBold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          loading: () => Card(
            elevation: AppElevation.sm,
            child: Container(
              height: 200,
              padding: const EdgeInsets.all(AppSpacing.lg),
              child: const Center(child: CircularProgressIndicator()),
            ),
          ),
          error: (error, stack) => Card(
            elevation: AppElevation.sm,
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.lg),
              child: Text('Error loading transactions: $error'),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopCategoriesSection({
    required String title,
    required AsyncValue<List<TopCategorySpending>> categoriesAsync,
    required ThemeData theme,
  }) {
    return categoriesAsync.when(
      data: (categories) => Card(
        elevation: AppElevation.sm,
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: AppTypography.fontWeightSemiBold,
                ),
              ),
              const SizedBox(height: AppSpacing.md),
              if (categories.isEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: AppSpacing.lg),
                  child: Center(
                    child: Text(
                      'No data available',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                )
              else
                ...categories.map(
                  (category) => Padding(
                    padding: const EdgeInsets.only(bottom: AppSpacing.sm),
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: category.categoryColor != null
                                ? Color(category.categoryColor!)
                                : theme.colorScheme.primary,
                            borderRadius: BorderRadius.circular(
                              AppBorderRadius.sm,
                            ),
                          ),
                          child: Icon(
                            category.categoryIcon != null
                                ? _getIconFromString(category.categoryIcon!)
                                : Icons.category,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: AppSpacing.md),
                        Expanded(
                          child: Text(
                            category.categoryName,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: AppTypography.fontWeightMedium,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          _formatCurrency(category.totalAmount),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: AppTypography.fontWeightSemiBold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
      loading: () => Card(
        elevation: AppElevation.sm,
        child: Container(
          height: 150,
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: const Center(child: CircularProgressIndicator()),
        ),
      ),
      error: (error, stack) => Card(
        elevation: AppElevation.sm,
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Text('Error loading categories: $error'),
        ),
      ),
    );
  }

  Future<void> _onRefresh() async {
    // Invalidate all dashboard providers to trigger refresh
    ref
      ..invalidate(currentPeriodBalanceProvider)
      ..invalidate(recentTransactionsForDashboardProvider)
      ..invalidate(topExpenseCategoriesSelectedPeriodProvider)
      ..invalidate(topIncomeCategoriesSelectedPeriodProvider);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBarHelpers.createTimePeriodScrollableAppBar(
        title: EnvironmentConfig.homePageTitle,
      ),
      body: RefreshIndicator(
        onRefresh: _onRefresh,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current Period Balance Card
              _buildCurrentPeriodBalanceCard(theme),
              const SizedBox(height: AppSpacing.lg),

              // Recent Transactions Card
              _buildRecentTransactionsCard(theme),
              const SizedBox(height: AppSpacing.lg),

              // Top Categories Row
              Row(
                children: [
                  // Top 3 Expenses
                  Expanded(
                    child: Consumer(
                      builder: (context, ref, child) {
                        final expensesAsync = ref.watch(
                          topExpenseCategoriesSelectedPeriodProvider,
                        );
                        return _buildTopCategoriesSection(
                          title: 'Top Expenses',
                          categoriesAsync: expensesAsync,
                          theme: theme,
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),

                  // Top 3 Income
                  Expanded(
                    child: Consumer(
                      builder: (context, ref, child) {
                        final incomeAsync = ref.watch(
                          topIncomeCategoriesSelectedPeriodProvider,
                        );
                        return _buildTopCategoriesSection(
                          title: 'Top Income',
                          categoriesAsync: incomeAsync,
                          theme: theme,
                        );
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppSpacing.lg),

              // Financial Overview Section (moved to bottom)
              Consumer(
                builder: (context, ref, child) {
                  final totalAssetsAsync = ref.watch(totalAssetsProvider);
                  final netWorthAsync = ref.watch(netWorthProvider);

                  return totalAssetsAsync.when(
                    data: (totalAssets) => netWorthAsync.when(
                      data: (netWorth) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Financial Overview',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: AppTypography.fontWeightSemiBold,
                            ),
                          ),
                          const SizedBox(height: AppSpacing.md),
                          Row(
                            children: [
                              Expanded(
                                child: _buildFinancialSummaryCard(
                                  title: 'Total Assets',
                                  amount: totalAssets,
                                  color: AppColors.success,
                                  theme: theme,
                                ),
                              ),
                              const SizedBox(width: AppSpacing.sm),
                              Expanded(
                                child: _buildFinancialSummaryCard(
                                  title: 'Net Worth',
                                  amount: netWorth,
                                  color: netWorth >= 0
                                      ? AppColors.success
                                      : AppColors.error,
                                  theme: theme,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      loading: () =>
                          const Center(child: CircularProgressIndicator()),
                      error: (error, stack) => Card(
                        child: Padding(
                          padding: const EdgeInsets.all(AppSpacing.md),
                          child: Text('Error loading financial data: $error'),
                        ),
                      ),
                    ),
                    loading: () =>
                        const Center(child: CircularProgressIndicator()),
                    error: (error, stack) => Card(
                      child: Padding(
                        padding: const EdgeInsets.all(AppSpacing.md),
                        child: Text('Error loading financial data: $error'),
                      ),
                    ),
                  );
                },
              ),

              // Development Demo Button (only in development)
            ],
          ),
        ),
      ),
    ).trackPerformance('home_screen');
  }
}
