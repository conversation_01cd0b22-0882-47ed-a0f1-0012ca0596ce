import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/categories/providers/category_providers.dart';
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/utils/color_utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'dashboard_providers.g.dart';

/// Provider for calculating total assets from all asset accounts
@riverpod
Future<int> totalAssets(Ref ref) async {
  final accountsAsync = await ref.watch(accountListProvider.future);

  final assetAccounts = accountsAsync.where(
    (account) => account.classification == AccountClassification.asset,
  );

  return assetAccounts.fold<int>(
    0,
    (sum, account) => sum + account.currentBalanceCents,
  );
}

/// Provider for calculating total liabilities from all liability accounts
@riverpod
Future<int> totalLiabilities(Ref ref) async {
  final accountsAsync = await ref.watch(accountListProvider.future);

  final liabilityAccounts = accountsAsync.where(
    (account) => account.classification == AccountClassification.liability,
  );

  return liabilityAccounts.fold<int>(
    0,
    (sum, account) => sum + account.currentBalanceCents,
  );
}

/// Provider for calculating net worth (total assets - total liabilities)
@riverpod
Future<int> netWorth(Ref ref) async {
  final totalAssetsValue = await ref.watch(totalAssetsProvider.future);
  final totalLiabilitiesValue = await ref.watch(
    totalLiabilitiesProvider.future,
  );

  return totalAssetsValue - totalLiabilitiesValue;
}

/// Data model for current period balance information
class CurrentPeriodBalance {
  const CurrentPeriodBalance({
    required this.totalIncome,
    required this.totalExpenses,
    required this.netIncome,
    required this.periodStart,
    required this.periodEnd,
  });
  final int totalIncome;
  final int totalExpenses;
  final int netIncome;
  final DateTime periodStart;
  final DateTime periodEnd;

  CurrentPeriodBalance copyWith({
    int? totalIncome,
    int? totalExpenses,
    int? netIncome,
    DateTime? periodStart,
    DateTime? periodEnd,
  }) {
    return CurrentPeriodBalance(
      totalIncome: totalIncome ?? this.totalIncome,
      totalExpenses: totalExpenses ?? this.totalExpenses,
      netIncome: netIncome ?? this.netIncome,
      periodStart: periodStart ?? this.periodStart,
      periodEnd: periodEnd ?? this.periodEnd,
    );
  }
}

/// Data model for top category spending information
class TopCategorySpending {
  const TopCategorySpending({
    required this.categoryId,
    required this.categoryName,
    required this.totalAmount,
    this.categoryIcon,
    this.categoryColor,
  });
  final String categoryId;
  final String categoryName;
  final int totalAmount;
  final String? categoryIcon;
  final int? categoryColor;
}

/// Provider for selected period's income/expense summary
@riverpod
Future<CurrentPeriodBalance> currentPeriodBalance(Ref ref) async {
  final authService = ref.watch(authServiceProvider);
  final user = authService.currentUser;
  if (user == null) throw Exception('User not authenticated');

  final transactionRepository = ref.watch(transactionRepositoryProvider);

  // Get the selected time period from global state
  final selectedPeriod = ref.watch(timePeriodNotifierProvider);

  final summary = await transactionRepository.getIncomeExpenseSummary(
    user.uid,
    selectedPeriod.startDate,
    selectedPeriod.endDate,
  );

  return CurrentPeriodBalance(
    totalIncome: summary['income'] ?? 0,
    totalExpenses: summary['expenses'] ?? 0,
    netIncome: summary['netIncome'] ?? 0,
    periodStart: selectedPeriod.startDate,
    periodEnd: selectedPeriod.endDate,
  );
}

/// Provider for recent transactions (last 3 for dashboard)
@riverpod
Future<List<Transaction>> recentTransactionsForDashboard(Ref ref) async {
  final authService = ref.watch(authServiceProvider);
  final user = authService.currentUser;
  if (user == null) throw Exception('User not authenticated');

  final transactionRepository = ref.watch(transactionRepositoryProvider);
  return transactionRepository.getRecentTransactions(user.uid, limit: 3);
}

/// Provider for top 3 expense categories for selected period
@riverpod
Future<List<TopCategorySpending>> topExpenseCategoriesSelectedPeriod(
  Ref ref,
) async {
  final authService = ref.watch(authServiceProvider);
  final user = authService.currentUser;
  if (user == null) throw Exception('User not authenticated');

  final transactionRepository = ref.watch(transactionRepositoryProvider);

  // Wait for categories to load
  final categories = await ref.watch(categoryListProvider.future);
  final categoryMap = {for (final cat in categories) cat.id: cat};

  // Get the selected time period from global state
  final selectedPeriod = ref.watch(timePeriodNotifierProvider);

  final spendingByCategory = await transactionRepository.getSpendingByCategory(
    user.uid,
    selectedPeriod.startDate,
    selectedPeriod.endDate,
  );

  // Convert to TopCategorySpending objects and sort by amount
  final topSpending =
      spendingByCategory.entries
          .where((entry) => categoryMap.containsKey(entry.key))
          .map((entry) {
            final category = categoryMap[entry.key]!;
            return TopCategorySpending(
              categoryId: category.id,
              categoryName: category.name,
              totalAmount: entry.value,
              categoryIcon: category.icon,
              categoryColor: category.color != null
                  ? parseHex(category.color!).toARGB32()
                  : null,
            );
          })
          .toList()
        ..sort((a, b) => b.totalAmount.compareTo(a.totalAmount));

  // Return top 3
  return topSpending.take(3).toList();
}

/// Provider for top 3 income categories for selected period
@riverpod
Future<List<TopCategorySpending>> topIncomeCategoriesSelectedPeriod(
  Ref ref,
) async {
  final authService = ref.watch(authServiceProvider);
  final user = authService.currentUser;
  if (user == null) throw Exception('User not authenticated');

  final transactionRepository = ref.watch(transactionRepositoryProvider);

  // Wait for categories to load
  final categories = await ref.watch(categoryListProvider.future);
  final categoryMap = {for (final cat in categories) cat.id: cat};

  // Get the selected time period from global state
  final selectedPeriod = ref.watch(timePeriodNotifierProvider);

  // Get transactions for the selected period and filter for income
  final transactions = await transactionRepository.getTransactionsByDateRange(
    user.uid,
    selectedPeriod.startDate,
    selectedPeriod.endDate,
  );

  // Calculate income by category
  final incomeByCategory = <String, int>{};
  for (final transaction in transactions) {
    if (transaction.type == TransactionType.income &&
        transaction.status == TransactionStatus.completed &&
        transaction.categoryId != null) {
      incomeByCategory[transaction.categoryId!] =
          (incomeByCategory[transaction.categoryId!] ?? 0) +
          transaction.amountCents;
    }
  }

  // Convert to TopCategorySpending objects and sort by amount
  final topIncome =
      incomeByCategory.entries
          .where((entry) => categoryMap.containsKey(entry.key))
          .map((entry) {
            final category = categoryMap[entry.key]!;
            return TopCategorySpending(
              categoryId: category.id,
              categoryName: category.name,
              totalAmount: entry.value,
              categoryIcon: category.icon,
              categoryColor: category.color != null
                  ? parseHex(category.color!).toARGB32()
                  : null,
            );
          })
          .toList()
        ..sort((a, b) => b.totalAmount.compareTo(a.totalAmount));

  // Return top 3
  return topIncome.take(3).toList();
}
