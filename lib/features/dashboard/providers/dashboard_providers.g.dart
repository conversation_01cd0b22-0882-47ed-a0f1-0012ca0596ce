// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$totalAssetsHash() => r'94b584a78038a06935c9b195406799860bd9a57a';

/// Provider for calculating total assets from all asset accounts
///
/// Copied from [totalAssets].
@ProviderFor(totalAssets)
final totalAssetsProvider = AutoDisposeFutureProvider<int>.internal(
  totalAssets,
  name: r'totalAssetsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$totalAssetsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TotalAssetsRef = AutoDisposeFutureProviderRef<int>;
String _$totalLiabilitiesHash() => r'cd3d79087e57af62e94c03097d045ec0daed6440';

/// Provider for calculating total liabilities from all liability accounts
///
/// Copied from [totalLiabilities].
@ProviderFor(totalLiabilities)
final totalLiabilitiesProvider = AutoDisposeFutureProvider<int>.internal(
  totalLiabilities,
  name: r'totalLiabilitiesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$totalLiabilitiesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TotalLiabilitiesRef = AutoDisposeFutureProviderRef<int>;
String _$netWorthHash() => r'bf91ccfce74c2f963f58c01032e94ad54d3c9091';

/// Provider for calculating net worth (total assets - total liabilities)
///
/// Copied from [netWorth].
@ProviderFor(netWorth)
final netWorthProvider = AutoDisposeFutureProvider<int>.internal(
  netWorth,
  name: r'netWorthProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$netWorthHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NetWorthRef = AutoDisposeFutureProviderRef<int>;
String _$currentPeriodBalanceHash() =>
    r'7f59b6f57ff9486cf82fc8b37bbf61bc2e947a47';

/// Provider for selected period's income/expense summary
///
/// Copied from [currentPeriodBalance].
@ProviderFor(currentPeriodBalance)
final currentPeriodBalanceProvider =
    AutoDisposeFutureProvider<CurrentPeriodBalance>.internal(
      currentPeriodBalance,
      name: r'currentPeriodBalanceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$currentPeriodBalanceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentPeriodBalanceRef =
    AutoDisposeFutureProviderRef<CurrentPeriodBalance>;
String _$recentTransactionsForDashboardHash() =>
    r'7083ee19b110c7bdf9be95e2c9ce626784ac26ed';

/// Provider for recent transactions (last 3 for dashboard)
///
/// Copied from [recentTransactionsForDashboard].
@ProviderFor(recentTransactionsForDashboard)
final recentTransactionsForDashboardProvider =
    AutoDisposeFutureProvider<List<Transaction>>.internal(
      recentTransactionsForDashboard,
      name: r'recentTransactionsForDashboardProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$recentTransactionsForDashboardHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RecentTransactionsForDashboardRef =
    AutoDisposeFutureProviderRef<List<Transaction>>;
String _$topExpenseCategoriesSelectedPeriodHash() =>
    r'913c8410b80c1852aa1cbd57111cbd14db3c7c5a';

/// Provider for top 3 expense categories for selected period
///
/// Copied from [topExpenseCategoriesSelectedPeriod].
@ProviderFor(topExpenseCategoriesSelectedPeriod)
final topExpenseCategoriesSelectedPeriodProvider =
    AutoDisposeFutureProvider<List<TopCategorySpending>>.internal(
      topExpenseCategoriesSelectedPeriod,
      name: r'topExpenseCategoriesSelectedPeriodProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$topExpenseCategoriesSelectedPeriodHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TopExpenseCategoriesSelectedPeriodRef =
    AutoDisposeFutureProviderRef<List<TopCategorySpending>>;
String _$topIncomeCategoriesSelectedPeriodHash() =>
    r'90366f32cb237eb9ca1b000499c677ec8b1667c8';

/// Provider for top 3 income categories for selected period
///
/// Copied from [topIncomeCategoriesSelectedPeriod].
@ProviderFor(topIncomeCategoriesSelectedPeriod)
final topIncomeCategoriesSelectedPeriodProvider =
    AutoDisposeFutureProvider<List<TopCategorySpending>>.internal(
      topIncomeCategoriesSelectedPeriod,
      name: r'topIncomeCategoriesSelectedPeriodProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$topIncomeCategoriesSelectedPeriodHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TopIncomeCategoriesSelectedPeriodRef =
    AutoDisposeFutureProviderRef<List<TopCategorySpending>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
