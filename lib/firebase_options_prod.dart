// Firebase configuration for Production environment
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Firebase options for Production environment
class FirebaseOptionsProd {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'FirebaseOptionsProd have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'FirebaseOptionsProd have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'FirebaseOptionsProd have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'FirebaseOptionsProd have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'FirebaseOptionsProd are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA7lck7lbkg2Lmc0H4CJdvkdDIg0FQQuXI',
    appId: '1:133822146578:android:1fcaca12de7e27ece090cb',
    messagingSenderId: '133822146578',
    projectId: 'budapp-prod',
    storageBucket: 'budapp-prod.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyA7lck7lbkg2Lmc0H4CJdvkdDIg0FQQuXI',
    appId: '1:133822146578:ios:1fcaca12de7e27ece090cb',
    messagingSenderId: '133822146578',
    projectId: 'budapp-prod',
    storageBucket: 'budapp-prod.firebasestorage.app',
    iosBundleId: 'com.digitau.budapp',
  );
}
