import 'package:budapp/features/accounts/presentation/screens/account_create_screen.dart';
import 'package:budapp/features/accounts/presentation/screens/account_detail_screen.dart';
import 'package:budapp/features/accounts/presentation/screens/account_edit_screen.dart';
import 'package:budapp/features/accounts/presentation/screens/accounts_list_screen.dart';
import 'package:budapp/features/auth/presentation/screens/auth_wrapper.dart';
import 'package:budapp/features/auth/presentation/screens/biometric_gate_screen.dart';
import 'package:budapp/features/auth/presentation/screens/email_verification_screen.dart';
import 'package:budapp/features/auth/presentation/screens/forgot_password_screen.dart';
import 'package:budapp/features/auth/presentation/screens/login_screen.dart';
import 'package:budapp/features/auth/presentation/screens/signup_screen.dart';
import 'package:budapp/features/auth/providers/biometric_providers.dart';
import 'package:budapp/features/budgets/presentation/screens/budget_edit_screen.dart';
import 'package:budapp/features/budgets/presentation/screens/budgets_list_screen.dart';
import 'package:budapp/features/categories/presentation/screens/categories_list_screen.dart';
import 'package:budapp/features/categories/presentation/screens/category_create_screen.dart';
import 'package:budapp/features/categories/presentation/screens/category_edit_screen.dart';
import 'package:budapp/features/dashboard/presentation/screens/hub_home_screen.dart';
import 'package:budapp/features/goals/presentation/screens/goal_contribution_create_screen.dart';
import 'package:budapp/features/goals/presentation/screens/goal_contribution_edit_screen.dart';
import 'package:budapp/features/goals/presentation/screens/goal_contributions_list_screen.dart';
import 'package:budapp/features/goals/presentation/screens/goal_create_screen.dart';
import 'package:budapp/features/goals/presentation/screens/goal_edit_screen.dart';
import 'package:budapp/features/goals/presentation/screens/goals_list_screen.dart';
import 'package:budapp/features/profile/presentation/screens/forgot_password_screen.dart'
    as profile_forgot;
import 'package:budapp/features/profile/presentation/screens/profile_screen.dart';
import 'package:budapp/features/profile/presentation/screens/unified_profile_management_screen.dart';
import 'package:budapp/features/settings/presentation/screens/currency_settings_screen.dart';
import 'package:budapp/features/tags/presentation/screens/tag_create_screen.dart';
import 'package:budapp/features/tags/presentation/screens/tag_edit_screen.dart';
import 'package:budapp/features/tags/presentation/screens/tags_list_screen.dart';
import 'package:budapp/features/transactions/presentation/screens/transaction_create_screen.dart';
import 'package:budapp/features/transactions/presentation/screens/transaction_edit_screen.dart';
import 'package:budapp/features/transactions/presentation/screens/transaction_filters_screen.dart';
import 'package:budapp/features/transactions/presentation/screens/transaction_search_screen.dart';
import 'package:budapp/features/transactions/presentation/screens/transactions_list_screen.dart';
import 'package:budapp/providers/providers.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Route paths for the application
class AppRoutes {
  static const String splash = '/splash';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String forgotPassword = '/forgot-password';
  static const String emailVerification = '/email-verification';
  static const String biometricGate = '/biometric-gate';
  static const String home = '/home';
  static const String accounts = '/accounts';
  static const String accountCreate = '/accounts/create';
  static const String accountDetail = '/accounts/:id';
  static const String accountEdit = '/accounts/:id/edit';
  static const String categories = '/categories';
  static const String categoryCreate = '/categories/create';
  static const String categoryDetail = '/categories/:id';
  static const String categoryEdit = '/categories/:id/edit';
  // Removed subcategory routes - now handled by unified category routes
  static const String transactions = '/transactions';
  static const String transactionCreate = '/transactions/create';
  static const String transactionEdit = '/transactions/:id/edit';
  static const String profile = '/profile';
  static const String profileManage = '/profile/manage';
  static const String profileForgotPassword = '/profile/forgot-password';
  static const String tags = '/tags';
  static const String tagCreate = '/tags/create';
  static const String tagEdit = '/tags/:id/edit';
  static const String budgets = '/budgets';
  static const String budgetEdit = '/budgets/:id/edit';
  static const String goals = '/goals';
  static const String goalCreate = '/goals/create';
  static const String goalEdit = '/goals/:id/edit';
  static const String goalContributions = '/goals/:id/contributions';
  static const String goalContributionCreate =
      '/goals/:id/contributions/create';
  static const String goalContributionEdit =
      '/goals/:id/contributions/:contributionId/edit';
  static const String currencySettings = '/settings/currency';
  static const String transactionFilters = '/transactions/filters';
  static const String transactionSearch = '/transactions/search';
}

/// AuthStateNotifier that extends ChangeNotifier to make routing reactive
/// to authentication state changes and biometric gate requirements
class AuthStateNotifier extends ChangeNotifier {
  AuthStateNotifier(this._ref) {
    // Listen to auth state changes and notify router
    _authSubscription = _ref.listen(
      authStateProvider,
      (_, next) {
        debugPrint(
          '[AuthStateNotifier] Auth state provider changed: ${next.runtimeType}',
        );
        // Notify GoRouter that authentication state changed
        notifyListeners();
      },
    );

    // Listen to biometric gate state changes and notify router
    _biometricGateSubscription = _ref.listen<BiometricGateStateNotifier>(
      biometricGateStateNotifierProvider,
      (_, _) {
        // Notify GoRouter that biometric gate state changed
        notifyListeners();
      },
    );
  }

  final Ref _ref;
  late final ProviderSubscription _authSubscription;
  late final ProviderSubscription _biometricGateSubscription;

  /// Get current user synchronously for router redirect logic
  User? get currentUser {
    final authState = _ref.read(authStateProvider);
    return authState.when(
      data: (user) => user,
      loading: () => null,
      error: (_, _) => null,
    );
  }

  /// Check if auth state is currently loading
  bool get isAuthLoading {
    final authState = _ref.read(authStateProvider);
    return authState.when(
      data: (_) => false,
      loading: () => true,
      error: (_, _) => false,
    );
  }

  /// Get biometric gate state synchronously for router redirect logic
  bool get biometricGateRequired {
    final biometricGateNotifier = _ref.read(biometricGateStateNotifierProvider);
    return biometricGateNotifier.biometricGateRequired;
  }

  @override
  void dispose() {
    _authSubscription.close();
    _biometricGateSubscription.close();
    super.dispose();
  }

  // Override notifyListeners to include debug output in tests
  @override
  void notifyListeners() {
    debugPrint(
      '[AuthStateNotifier] Auth state changed, notifying router - user: ${currentUser?.uid ?? 'null'}, loading: $isAuthLoading',
    );
    super.notifyListeners();
  }
}

/// Provider for AuthStateNotifier
final authStateNotifierProvider = Provider<AuthStateNotifier>((ref) {
  return AuthStateNotifier(ref);
});

/// Provider for the GoRouter instance
final goRouterProvider = Provider<GoRouter>((ref) {
  final authStateNotifier = ref.watch(authStateNotifierProvider);

  return GoRouter(
    initialLocation:
        AppRoutes.login, // Start directly with login instead of splash
    debugLogDiagnostics: true,
    refreshListenable:
        authStateNotifier, // This makes router reactive to auth changes
    redirect: (context, state) {
      // Get auth state directly from provider for immediate access
      final authState = ref.read(authStateProvider);
      final currentPath = state.uri.path;

      debugPrint(
        '🧭 Router redirect: path=$currentPath, authState=${authState.runtimeType}',
      );

      // Handle auth state
      return authState.when(
        data: (user) {
          debugPrint('🧭 Auth data: user=${user?.uid ?? 'null'}');

          // If user is not authenticated
          if (user == null) {
            // Allow access to auth routes
            if (currentPath == AppRoutes.login ||
                currentPath == AppRoutes.signup ||
                currentPath == AppRoutes.forgotPassword) {
              debugPrint('🧭 Allowing access to auth route: $currentPath');
              return null;
            }
            // Redirect to login
            debugPrint('🧭 Redirecting to login - no user');
            return AppRoutes.login;
          }

          // If user is authenticated but email not verified
          if (!user.emailVerified) {
            if (currentPath == AppRoutes.emailVerification) {
              debugPrint('🧭 Allowing access to email verification');
              return null;
            }
            debugPrint('🧭 Redirecting to email verification');
            return AppRoutes.emailVerification;
          }

          // Check biometric gate
          final biometricGateRequired = ref
              .read(biometricGateStateNotifierProvider)
              .biometricGateRequired;
          if (biometricGateRequired) {
            if (currentPath == AppRoutes.biometricGate) {
              debugPrint('🧭 Allowing access to biometric gate');
              return null;
            }
            debugPrint('🧭 Redirecting to biometric gate');
            return AppRoutes.biometricGate;
          }

          // If trying to access auth routes when authenticated, redirect to home
          if (currentPath == AppRoutes.login ||
              currentPath == AppRoutes.signup ||
              currentPath == AppRoutes.forgotPassword ||
              currentPath == AppRoutes.emailVerification ||
              currentPath == AppRoutes.biometricGate ||
              currentPath == AppRoutes.splash) {
            debugPrint('🧭 Redirecting authenticated user to home');
            return AppRoutes.home;
          }

          // Allow access to authenticated routes
          debugPrint('🧭 Allowing access to authenticated route: $currentPath');
          return null;
        },
        loading: () {
          debugPrint(
            '🧭 Auth loading - staying on current route: $currentPath',
          );
          return null; // Stay on current route while loading
        },
        error: (error, stack) {
          debugPrint('🧭 Auth error - redirecting to login: $error');
          return AppRoutes.login;
        },
      );
    },
    routes: [
      // Splash screen route (initial loading state)
      GoRoute(
        path: AppRoutes.splash,
        name: 'splash',
        builder: (context, state) => const AuthWrapper(),
      ),

      // Public routes (accessible when not authenticated)
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: AppRoutes.signup,
        name: 'signup',
        builder: (context, state) => const SignupScreen(),
      ),
      GoRoute(
        path: AppRoutes.forgotPassword,
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),

      // Email verification route (requires authentication but not verification)
      GoRoute(
        path: AppRoutes.emailVerification,
        name: 'email-verification',
        builder: (context, state) => const EmailVerificationScreen(),
      ),

      // Biometric gate route (requires authentication and verification but not biometric completion)
      GoRoute(
        path: AppRoutes.biometricGate,
        name: 'biometric-gate',
        builder: (context, state) => const BiometricGateScreen(),
      ),

      // Dashboard/Home (requires authentication and verification)
      GoRoute(
        path: AppRoutes.home,
        name: 'home',
        builder: (context, state) => const HubHomeScreen(),
      ),

      // Accounts feature routes
      GoRoute(
        path: AppRoutes.accounts,
        name: 'accounts',
        builder: (context, state) => const AccountsListScreen(),
        routes: [
          GoRoute(
            path: 'create',
            name: 'account-create',
            builder: (context, state) => const AccountCreateScreen(),
          ),
          GoRoute(
            path: ':id',
            name: 'account-detail',
            builder: (context, state) {
              final accountId = state.pathParameters['id']!;
              return AccountDetailScreen(accountId: accountId);
            },
            routes: [
              GoRoute(
                path: 'edit',
                name: 'account-edit',
                builder: (context, state) {
                  final accountId = state.pathParameters['id']!;
                  return AccountEditScreen(accountId: accountId);
                },
              ),
            ],
          ),
        ],
      ),

      // Categories feature routes
      GoRoute(
        path: AppRoutes.categories,
        name: 'categories',
        builder: (context, state) => const CategoriesListScreen(),
        routes: [
          // Unified category creation route
          // Supports both root categories and subcategories via query parameter
          GoRoute(
            path: 'create',
            name: 'category-create',
            builder: (context, state) {
              final parentId = state.uri.queryParameters['parentId'];
              return CategoryCreateScreen(parentId: parentId);
            },
          ),

          // Unified category edit route
          // Handles both root categories and subcategories automatically
          GoRoute(
            path: ':id/edit',
            name: 'category-edit',
            builder: (context, state) {
              final categoryId = state.pathParameters['id']!;
              return CategoryEditScreen(categoryId: categoryId);
            },
          ),
        ],
      ),

      // Transactions feature routes
      GoRoute(
        path: AppRoutes.transactions,
        name: 'transactions',
        builder: (context, state) => const TransactionsListScreen(),
        routes: [
          GoRoute(
            path: 'create',
            name: 'transaction-create',
            builder: (context, state) => const TransactionCreateScreen(),
          ),
          GoRoute(
            path: 'category/:categoryId',
            name: 'transactions-by-category',
            builder: (context, state) {
              final categoryId = state.pathParameters['categoryId']!;
              return TransactionsListScreen(categoryId: categoryId);
            },
          ),
          GoRoute(
            path: 'account/:accountId',
            name: 'transactions-by-account',
            builder: (context, state) {
              final accountId = state.pathParameters['accountId']!;
              return TransactionsListScreen(accountId: accountId);
            },
          ),
          GoRoute(
            path: ':id/edit',
            name: 'transaction-edit',
            builder: (context, state) {
              final transactionId = state.pathParameters['id']!;
              return TransactionEditScreen(transactionId: transactionId);
            },
          ),
          GoRoute(
            path: 'filters',
            name: 'transaction-filters',
            builder: (context, state) => const TransactionFiltersScreen(),
          ),
          GoRoute(
            path: 'search',
            name: 'transaction-search',
            builder: (context, state) => const TransactionSearchScreen(),
          ),
        ],
      ),

      // Tags feature routes
      GoRoute(
        path: AppRoutes.tags,
        name: 'tags',
        builder: (context, state) => const TagsListScreen(),
        routes: [
          GoRoute(
            path: 'create',
            name: 'tag-create',
            builder: (context, state) => const TagCreateScreen(),
          ),
          GoRoute(
            path: ':id/edit',
            name: 'tag-edit',
            builder: (context, state) {
              final tagId = state.pathParameters['id']!;
              return TagEditScreen(tagId: tagId);
            },
          ),
        ],
      ),

      // Budgets feature routes
      GoRoute(
        path: AppRoutes.budgets,
        name: 'budgets',
        builder: (context, state) => const BudgetsListScreen(),
        routes: [
          GoRoute(
            path: ':id/edit',
            name: 'budget-edit',
            builder: (context, state) {
              final budgetId = state.pathParameters['id']!;
              return BudgetEditScreen(budgetId: budgetId);
            },
          ),
        ],
      ),

      // Goals feature routes
      GoRoute(
        path: AppRoutes.goals,
        name: 'goals',
        builder: (context, state) => const GoalsListScreen(),
        routes: [
          GoRoute(
            path: 'create',
            name: 'goal-create',
            builder: (context, state) => const GoalCreateScreen(),
          ),
          GoRoute(
            path: ':id/edit',
            name: 'goal-edit',
            builder: (context, state) {
              final goalId = state.pathParameters['id']!;
              return GoalEditScreen(goalId: goalId);
            },
          ),
          GoRoute(
            path: ':id/contributions',
            name: 'goal-contributions',
            builder: (context, state) {
              final goalId = state.pathParameters['id']!;
              return GoalContributionsListScreen(goalId: goalId);
            },
            routes: [
              GoRoute(
                path: 'create',
                name: 'goal-contribution-create',
                builder: (context, state) {
                  final goalId = state.pathParameters['id']!;
                  return GoalContributionCreateScreen(goalId: goalId);
                },
              ),
              GoRoute(
                path: ':contributionId/edit',
                name: 'goal-contribution-edit',
                builder: (context, state) {
                  final goalId = state.pathParameters['id']!;
                  final contributionId =
                      state.pathParameters['contributionId']!;
                  return GoalContributionEditScreen(
                    goalId: goalId,
                    contributionId: contributionId,
                  );
                },
              ),
            ],
          ),
        ],
      ),

      // Profile feature routes
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const ProfileScreen(),
        routes: [
          GoRoute(
            path: 'manage',
            name: 'profile-manage',
            builder: (context, state) => const UnifiedProfileManagementScreen(),
          ),
          GoRoute(
            path: 'forgot-password',
            name: 'profile-forgot-password',
            builder: (context, state) =>
                const profile_forgot.ForgotPasswordScreen(),
          ),
        ],
      ),

      // Settings routes
      GoRoute(
        path: '/settings/currency',
        name: 'currency-settings',
        builder: (context, state) => const CurrencySettingsScreen(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.home),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
});
