import 'package:budapp/providers/providers.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Firebase service providers
///
/// These providers handle Firebase service initialization and connectivity.

/// Injectable Firebase service that handles initialization and connectivity
class FirebaseInitializationService {
  FirebaseInitializationService(this._auth, this._firestore);
  final FirebaseAuth _auth;
  final FirebaseFirestore _firestore;
  bool _initialized = false;

  /// Initialize Firebase services after Firebase.initializeApp() has been called
  Future<void> initializeServices() async {
    if (_initialized) {
      debugPrint('Firebase services already initialized');
      return;
    }

    try {
      // Configure Firestore settings for optimal performance (2025 best practices)
      try {
        // Enable offline persistence with unlimited cache size for better performance
        _firestore.settings = const Settings(
          persistenceEnabled: true,
          cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
        );
        debugPrint(
          'Firestore offline persistence enabled with unlimited cache',
        );

        // Enable network after settings configuration
        await _firestore.enableNetwork();
        debugPrint('Firestore network enabled successfully');
      } on Exception catch (error) {
        debugPrint('Firestore configuration warning: $error');
        // Continue initialization even if settings fail
      }

      debugPrint('Firebase Auth initialized successfully');
      debugPrint(
        'Firestore initialized successfully with performance optimizations',
      );

      _initialized = true;
      debugPrint('All Firebase services initialized successfully');
    } on Exception catch (e) {
      debugPrint('Error initializing Firebase services: $e');
      rethrow;
    }
  }

  /// Check if services are initialized
  bool get isInitialized => _initialized;

  /// Test Firebase connectivity by performing a simple operation
  Future<Map<String, dynamic>> testConnectivity() async {
    final results = <String, dynamic>{};

    try {
      // Test Firebase Auth
      final authUser = _auth.currentUser;
      results['auth'] = {
        'status': 'connected',
        'currentUser': authUser?.uid ?? 'anonymous',
        'isSignedIn': authUser != null,
      };
    } on Exception catch (e) {
      results['auth'] = {'status': 'error', 'error': e.toString()};
    }

    try {
      // Test Firestore connectivity
      final testDoc = _firestore.collection('_test').doc('connectivity');
      await testDoc.set({
        'timestamp': FieldValue.serverTimestamp(),
        'test': true,
      });

      final snapshot = await testDoc.get();
      await testDoc.delete(); // Clean up test document

      results['firestore'] = {
        'status': 'connected',
        'canWrite': true,
        'canRead': snapshot.exists,
        'serverTimestamp': snapshot.data()?['timestamp'],
      };
    } on Exception catch (e) {
      results['firestore'] = {'status': 'error', 'error': e.toString()};
    }

    return results;
  }

  /// Get current Firebase project information
  Map<String, String> getProjectInfo() {
    final app = Firebase.app();
    return {
      'projectId': app.options.projectId,
      'appId': app.options.appId,
      'apiKey':
          '${app.options.apiKey.substring(0, 10)}...', // Masked for security
      'messagingSenderId': app.options.messagingSenderId,
      'storageBucket': app.options.storageBucket ?? 'not configured',
    };
  }
}

/// Provider for FirebaseInitializationService instance
///
/// This provider creates a FirebaseInitializationService instance with proper dependency injection.
final firebaseServiceProvider = Provider<FirebaseInitializationService>((ref) {
  // Import the providers from the main providers file to avoid duplication
  final auth = ref.watch(firebaseAuthProvider);
  final firestore = ref.watch(firestoreProvider);
  return FirebaseInitializationService(auth, firestore);
});

/// Provider for Firebase initialization status
///
/// This provider tracks whether Firebase services have been initialized.
final firebaseInitializedProvider = Provider<bool>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.isInitialized;
});

/// Provider for Firebase project information
///
/// This provider returns information about the current Firebase project.
final firebaseProjectInfoProvider = Provider<Map<String, String>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getProjectInfo();
});
