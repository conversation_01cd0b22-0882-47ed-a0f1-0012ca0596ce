import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Generic AsyncNotifier for CRUD operations on entities
///
/// This base class provides common patterns for creating, updating, and deleting
/// entities with consistent error handling and state management.
abstract class AsyncEntityNotifier<T> extends AsyncNotifier<void> {
  @override
  Future<void> build() async {
    // Initial state - subclasses can override if needed
  }

  /// Create a new entity
  Future<void> create(T entity) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      await performCreate(entity);
      await onCreateSuccess(entity);
    });
  }

  /// Update an existing entity
  Future<void> updateEntity(String id, T entity) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      await performUpdate(id, entity);
      await onUpdateSuccess(id, entity);
    });
  }

  /// Delete an entity
  Future<void> delete(String id) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      await performDelete(id);
      await onDeleteSuccess(id);
    });
  }

  /// Perform the actual create operation - must be implemented by subclasses
  Future<void> performCreate(T entity);

  /// Perform the actual update operation - must be implemented by subclasses
  Future<void> performUpdate(String id, T entity);

  /// Perform the actual delete operation - must be implemented by subclasses
  Future<void> performDelete(String id);

  /// Called after successful create - can be overridden for custom behavior
  Future<void> onCreateSuccess(T entity) async {
    // Default: invalidate related providers
    invalidateRelatedProviders();
  }

  /// Called after successful update - can be overridden for custom behavior
  Future<void> onUpdateSuccess(String id, T entity) async {
    // Default: invalidate related providers
    invalidateRelatedProviders();
  }

  /// Called after successful delete - can be overridden for custom behavior
  Future<void> onDeleteSuccess(String id) async {
    // Default: invalidate related providers
    invalidateRelatedProviders();
  }

  /// Invalidate related providers - must be implemented by subclasses
  void invalidateRelatedProviders();
}

/// Generic AsyncNotifier for managing lists of entities
///
/// This base class provides common patterns for managing entity lists
/// with real-time updates and filtering capabilities.
abstract class AsyncEntityListNotifier<T> extends AsyncNotifier<List<T>> {
  @override
  Future<List<T>> build() async {
    return loadEntities();
  }

  /// Load entities from the data source - must be implemented by subclasses
  Future<List<T>> loadEntities();

  /// Refresh the entity list
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      return loadEntities();
    });
  }

  /// Add an entity to the list (optimistic update)
  void addEntity(T entity) {
    state.whenData((entities) {
      state = AsyncValue.data([...entities, entity]);
    });
  }

  /// Update an entity in the list (optimistic update)
  void updateEntity(String id, T updatedEntity) {
    state.whenData((entities) {
      final updatedList = entities.map((entity) {
        return getEntityId(entity) == id ? updatedEntity : entity;
      }).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  /// Remove an entity from the list (optimistic update)
  void removeEntity(String id) {
    state.whenData((entities) {
      final updatedList = entities
          .where((entity) => getEntityId(entity) != id)
          .toList();
      state = AsyncValue.data(updatedList);
    });
  }

  /// Get the ID of an entity - must be implemented by subclasses
  String getEntityId(T entity);

  /// Filter entities based on a predicate
  List<T> filterEntities(bool Function(T) predicate) {
    return state.maybeWhen(
      data: (entities) => entities.where(predicate).toList(),
      orElse: () => [],
    );
  }
}

/// Generic AsyncNotifier for managing a single entity
///
/// This base class provides common patterns for loading and managing
/// individual entities with caching and error handling.
abstract class AsyncSingleEntityNotifier<T> extends AsyncNotifier<T?> {
  AsyncSingleEntityNotifier(this.entityId);
  final String entityId;

  @override
  Future<T?> build() async {
    return loadEntity(entityId);
  }

  /// Load a single entity by ID - must be implemented by subclasses
  Future<T?> loadEntity(String id);

  /// Refresh the entity
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      return loadEntity(entityId);
    });
  }

  /// Update the entity (optimistic update)
  void updateEntity(T updatedEntity) {
    state = AsyncValue.data(updatedEntity);
  }

  /// Clear the entity
  void clearEntity() {
    state = const AsyncValue.data(null);
  }
}

/// Mixin for common entity operations
mixin EntityOperationsMixin<T> {
  /// Validate entity before operations
  bool validateEntity(T entity) {
    // Default implementation - can be overridden
    return true;
  }

  /// Handle operation errors
  String getErrorMessage(Object error) {
    // Default error message - can be overridden
    return 'An error occurred: $error';
  }

  /// Log operation for debugging
  void logOperation(String operation, String entityType, [String? entityId]) {
    // Default implementation - can be overridden for logging
    // print('$operation $entityType${entityId != null ? ' ($entityId)' : ''}');
  }
}

/// Helper class for batch operations on entities
class BatchEntityOperations<T> {
  BatchEntityOperations({required this.entities, required this.operation});
  final List<T> entities;
  final Future<void> Function(T) operation;

  /// Execute operations on all entities
  Future<List<T>> execute() async {
    final results = <T>[];
    final errors = <String>[];

    for (final entity in entities) {
      try {
        await operation(entity);
        results.add(entity);
      } on Exception catch (e) {
        errors.add(e.toString());
      }
    }

    if (errors.isNotEmpty) {
      throw Exception('Batch operation failed: ${errors.join(', ')}');
    }

    return results;
  }

  /// Execute operations with progress callback
  Future<List<T>> executeWithProgress(
    void Function(int completed, int total) onProgress,
  ) async {
    final results = <T>[];
    final errors = <String>[];

    for (var i = 0; i < entities.length; i++) {
      try {
        await operation(entities[i]);
        results.add(entities[i]);
      } on Exception catch (e) {
        errors.add(e.toString());
      }

      onProgress(i + 1, entities.length);
    }

    if (errors.isNotEmpty) {
      throw Exception('Batch operation failed: ${errors.join(', ')}');
    }

    return results;
  }
}
