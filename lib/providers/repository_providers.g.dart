// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'repository_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userProfileNotifierHash() =>
    r'12ede9e436aa02ffc6de248ec35766bb8865e80d';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$UserProfileNotifier
    extends BuildlessAutoDisposeAsyncNotifier<UserProfile?> {
  late final String userId;

  FutureOr<UserProfile?> build(String userId);
}

/// Future providers for additional repository implementations
///
/// As we implement more repositories, they will be added here following
/// the same pattern:
///
/// ```dart
/// final accountRepositoryProvider = Provider<IAccountRepository>((ref) {
///   final firestore = ref.watch(firestoreProvider);
///   return AccountRepositoryImpl(firestore);
/// });
///
/// final transactionRepositoryProvider = Provider<ITransactionRepository>((ref) {
///   final firestore = ref.watch(firestoreProvider);
///   return TransactionRepositoryImpl(firestore);
/// });
///
/// final categoryRepositoryProvider = Provider<ICategoryRepository>((ref) {
///   final firestore = ref.watch(firestoreProvider);
///   return CategoryRepositoryImpl(firestore);
/// });
///
/// final budgetRepositoryProvider = Provider<IBudgetRepository>((ref) {
///   final firestore = ref.watch(firestoreProvider);
///   return BudgetRepositoryImpl(firestore);
/// });
/// ```
/// Repository provider documentation
///
/// ## Usage in Services/Controllers
///
/// ```dart
/// class UserService {
///   final IUserRepository _userRepository;
///
///   UserService(this._userRepository);
///
///   Future<UserProfile?> getCurrentUser(String uid) {
///     return _userRepository.getUserById(uid);
///   }
/// }
///
/// final userServiceProvider = Provider<UserService>((ref) {
///   final userRepo = ref.watch(userRepositoryProvider);
///   return UserService(userRepo);
/// });
/// ```
///
/// ## Usage in Widgets
///
/// ```dart
/// class UserProfileWidget extends ConsumerWidget {
///   final String userId;
///
///   const UserProfileWidget({required this.userId});
///
///   @override
///   Widget build(BuildContext context, WidgetRef ref) {
///     final userRepo = ref.watch(userRepositoryProvider);
///
///     return FutureBuilder<UserProfile?>(
///       future: userRepo.getUserById(userId),
///       builder: (context, snapshot) {
///         // Build UI based on user data
///       },
///     );
///   }
/// }
/// ```
///
/// ## Testing with Mocks
///
/// ```dart
/// class MockUserRepository extends Mock implements IUserRepository {}
///
/// testWidgets('UserProfileWidget displays user data', (tester) async {
///   final mockRepo = MockUserRepository();
///   when(() => mockRepo.getUserById(any())).thenAnswer(
///     (_) async => UserProfile(/* test data */),
///   );
///
///   await tester.pumpWidget(
///     ProviderScope(
///       overrides: [
///         userRepositoryProvider.overrideWithValue(mockRepo),
///       ],
///       child: UserProfileWidget(userId: 'test-id'),
///     ),
///   );
///
///   // Verify widget behavior
/// });
/// ```
/// AsyncNotifier for user profile operations
///
/// Copied from [UserProfileNotifier].
@ProviderFor(UserProfileNotifier)
const userProfileNotifierProvider = UserProfileNotifierFamily();

/// Future providers for additional repository implementations
///
/// As we implement more repositories, they will be added here following
/// the same pattern:
///
/// ```dart
/// final accountRepositoryProvider = Provider<IAccountRepository>((ref) {
///   final firestore = ref.watch(firestoreProvider);
///   return AccountRepositoryImpl(firestore);
/// });
///
/// final transactionRepositoryProvider = Provider<ITransactionRepository>((ref) {
///   final firestore = ref.watch(firestoreProvider);
///   return TransactionRepositoryImpl(firestore);
/// });
///
/// final categoryRepositoryProvider = Provider<ICategoryRepository>((ref) {
///   final firestore = ref.watch(firestoreProvider);
///   return CategoryRepositoryImpl(firestore);
/// });
///
/// final budgetRepositoryProvider = Provider<IBudgetRepository>((ref) {
///   final firestore = ref.watch(firestoreProvider);
///   return BudgetRepositoryImpl(firestore);
/// });
/// ```
/// Repository provider documentation
///
/// ## Usage in Services/Controllers
///
/// ```dart
/// class UserService {
///   final IUserRepository _userRepository;
///
///   UserService(this._userRepository);
///
///   Future<UserProfile?> getCurrentUser(String uid) {
///     return _userRepository.getUserById(uid);
///   }
/// }
///
/// final userServiceProvider = Provider<UserService>((ref) {
///   final userRepo = ref.watch(userRepositoryProvider);
///   return UserService(userRepo);
/// });
/// ```
///
/// ## Usage in Widgets
///
/// ```dart
/// class UserProfileWidget extends ConsumerWidget {
///   final String userId;
///
///   const UserProfileWidget({required this.userId});
///
///   @override
///   Widget build(BuildContext context, WidgetRef ref) {
///     final userRepo = ref.watch(userRepositoryProvider);
///
///     return FutureBuilder<UserProfile?>(
///       future: userRepo.getUserById(userId),
///       builder: (context, snapshot) {
///         // Build UI based on user data
///       },
///     );
///   }
/// }
/// ```
///
/// ## Testing with Mocks
///
/// ```dart
/// class MockUserRepository extends Mock implements IUserRepository {}
///
/// testWidgets('UserProfileWidget displays user data', (tester) async {
///   final mockRepo = MockUserRepository();
///   when(() => mockRepo.getUserById(any())).thenAnswer(
///     (_) async => UserProfile(/* test data */),
///   );
///
///   await tester.pumpWidget(
///     ProviderScope(
///       overrides: [
///         userRepositoryProvider.overrideWithValue(mockRepo),
///       ],
///       child: UserProfileWidget(userId: 'test-id'),
///     ),
///   );
///
///   // Verify widget behavior
/// });
/// ```
/// AsyncNotifier for user profile operations
///
/// Copied from [UserProfileNotifier].
class UserProfileNotifierFamily extends Family<AsyncValue<UserProfile?>> {
  /// Future providers for additional repository implementations
  ///
  /// As we implement more repositories, they will be added here following
  /// the same pattern:
  ///
  /// ```dart
  /// final accountRepositoryProvider = Provider<IAccountRepository>((ref) {
  ///   final firestore = ref.watch(firestoreProvider);
  ///   return AccountRepositoryImpl(firestore);
  /// });
  ///
  /// final transactionRepositoryProvider = Provider<ITransactionRepository>((ref) {
  ///   final firestore = ref.watch(firestoreProvider);
  ///   return TransactionRepositoryImpl(firestore);
  /// });
  ///
  /// final categoryRepositoryProvider = Provider<ICategoryRepository>((ref) {
  ///   final firestore = ref.watch(firestoreProvider);
  ///   return CategoryRepositoryImpl(firestore);
  /// });
  ///
  /// final budgetRepositoryProvider = Provider<IBudgetRepository>((ref) {
  ///   final firestore = ref.watch(firestoreProvider);
  ///   return BudgetRepositoryImpl(firestore);
  /// });
  /// ```
  /// Repository provider documentation
  ///
  /// ## Usage in Services/Controllers
  ///
  /// ```dart
  /// class UserService {
  ///   final IUserRepository _userRepository;
  ///
  ///   UserService(this._userRepository);
  ///
  ///   Future<UserProfile?> getCurrentUser(String uid) {
  ///     return _userRepository.getUserById(uid);
  ///   }
  /// }
  ///
  /// final userServiceProvider = Provider<UserService>((ref) {
  ///   final userRepo = ref.watch(userRepositoryProvider);
  ///   return UserService(userRepo);
  /// });
  /// ```
  ///
  /// ## Usage in Widgets
  ///
  /// ```dart
  /// class UserProfileWidget extends ConsumerWidget {
  ///   final String userId;
  ///
  ///   const UserProfileWidget({required this.userId});
  ///
  ///   @override
  ///   Widget build(BuildContext context, WidgetRef ref) {
  ///     final userRepo = ref.watch(userRepositoryProvider);
  ///
  ///     return FutureBuilder<UserProfile?>(
  ///       future: userRepo.getUserById(userId),
  ///       builder: (context, snapshot) {
  ///         // Build UI based on user data
  ///       },
  ///     );
  ///   }
  /// }
  /// ```
  ///
  /// ## Testing with Mocks
  ///
  /// ```dart
  /// class MockUserRepository extends Mock implements IUserRepository {}
  ///
  /// testWidgets('UserProfileWidget displays user data', (tester) async {
  ///   final mockRepo = MockUserRepository();
  ///   when(() => mockRepo.getUserById(any())).thenAnswer(
  ///     (_) async => UserProfile(/* test data */),
  ///   );
  ///
  ///   await tester.pumpWidget(
  ///     ProviderScope(
  ///       overrides: [
  ///         userRepositoryProvider.overrideWithValue(mockRepo),
  ///       ],
  ///       child: UserProfileWidget(userId: 'test-id'),
  ///     ),
  ///   );
  ///
  ///   // Verify widget behavior
  /// });
  /// ```
  /// AsyncNotifier for user profile operations
  ///
  /// Copied from [UserProfileNotifier].
  const UserProfileNotifierFamily();

  /// Future providers for additional repository implementations
  ///
  /// As we implement more repositories, they will be added here following
  /// the same pattern:
  ///
  /// ```dart
  /// final accountRepositoryProvider = Provider<IAccountRepository>((ref) {
  ///   final firestore = ref.watch(firestoreProvider);
  ///   return AccountRepositoryImpl(firestore);
  /// });
  ///
  /// final transactionRepositoryProvider = Provider<ITransactionRepository>((ref) {
  ///   final firestore = ref.watch(firestoreProvider);
  ///   return TransactionRepositoryImpl(firestore);
  /// });
  ///
  /// final categoryRepositoryProvider = Provider<ICategoryRepository>((ref) {
  ///   final firestore = ref.watch(firestoreProvider);
  ///   return CategoryRepositoryImpl(firestore);
  /// });
  ///
  /// final budgetRepositoryProvider = Provider<IBudgetRepository>((ref) {
  ///   final firestore = ref.watch(firestoreProvider);
  ///   return BudgetRepositoryImpl(firestore);
  /// });
  /// ```
  /// Repository provider documentation
  ///
  /// ## Usage in Services/Controllers
  ///
  /// ```dart
  /// class UserService {
  ///   final IUserRepository _userRepository;
  ///
  ///   UserService(this._userRepository);
  ///
  ///   Future<UserProfile?> getCurrentUser(String uid) {
  ///     return _userRepository.getUserById(uid);
  ///   }
  /// }
  ///
  /// final userServiceProvider = Provider<UserService>((ref) {
  ///   final userRepo = ref.watch(userRepositoryProvider);
  ///   return UserService(userRepo);
  /// });
  /// ```
  ///
  /// ## Usage in Widgets
  ///
  /// ```dart
  /// class UserProfileWidget extends ConsumerWidget {
  ///   final String userId;
  ///
  ///   const UserProfileWidget({required this.userId});
  ///
  ///   @override
  ///   Widget build(BuildContext context, WidgetRef ref) {
  ///     final userRepo = ref.watch(userRepositoryProvider);
  ///
  ///     return FutureBuilder<UserProfile?>(
  ///       future: userRepo.getUserById(userId),
  ///       builder: (context, snapshot) {
  ///         // Build UI based on user data
  ///       },
  ///     );
  ///   }
  /// }
  /// ```
  ///
  /// ## Testing with Mocks
  ///
  /// ```dart
  /// class MockUserRepository extends Mock implements IUserRepository {}
  ///
  /// testWidgets('UserProfileWidget displays user data', (tester) async {
  ///   final mockRepo = MockUserRepository();
  ///   when(() => mockRepo.getUserById(any())).thenAnswer(
  ///     (_) async => UserProfile(/* test data */),
  ///   );
  ///
  ///   await tester.pumpWidget(
  ///     ProviderScope(
  ///       overrides: [
  ///         userRepositoryProvider.overrideWithValue(mockRepo),
  ///       ],
  ///       child: UserProfileWidget(userId: 'test-id'),
  ///     ),
  ///   );
  ///
  ///   // Verify widget behavior
  /// });
  /// ```
  /// AsyncNotifier for user profile operations
  ///
  /// Copied from [UserProfileNotifier].
  UserProfileNotifierProvider call(String userId) {
    return UserProfileNotifierProvider(userId);
  }

  @override
  UserProfileNotifierProvider getProviderOverride(
    covariant UserProfileNotifierProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'userProfileNotifierProvider';
}

/// Future providers for additional repository implementations
///
/// As we implement more repositories, they will be added here following
/// the same pattern:
///
/// ```dart
/// final accountRepositoryProvider = Provider<IAccountRepository>((ref) {
///   final firestore = ref.watch(firestoreProvider);
///   return AccountRepositoryImpl(firestore);
/// });
///
/// final transactionRepositoryProvider = Provider<ITransactionRepository>((ref) {
///   final firestore = ref.watch(firestoreProvider);
///   return TransactionRepositoryImpl(firestore);
/// });
///
/// final categoryRepositoryProvider = Provider<ICategoryRepository>((ref) {
///   final firestore = ref.watch(firestoreProvider);
///   return CategoryRepositoryImpl(firestore);
/// });
///
/// final budgetRepositoryProvider = Provider<IBudgetRepository>((ref) {
///   final firestore = ref.watch(firestoreProvider);
///   return BudgetRepositoryImpl(firestore);
/// });
/// ```
/// Repository provider documentation
///
/// ## Usage in Services/Controllers
///
/// ```dart
/// class UserService {
///   final IUserRepository _userRepository;
///
///   UserService(this._userRepository);
///
///   Future<UserProfile?> getCurrentUser(String uid) {
///     return _userRepository.getUserById(uid);
///   }
/// }
///
/// final userServiceProvider = Provider<UserService>((ref) {
///   final userRepo = ref.watch(userRepositoryProvider);
///   return UserService(userRepo);
/// });
/// ```
///
/// ## Usage in Widgets
///
/// ```dart
/// class UserProfileWidget extends ConsumerWidget {
///   final String userId;
///
///   const UserProfileWidget({required this.userId});
///
///   @override
///   Widget build(BuildContext context, WidgetRef ref) {
///     final userRepo = ref.watch(userRepositoryProvider);
///
///     return FutureBuilder<UserProfile?>(
///       future: userRepo.getUserById(userId),
///       builder: (context, snapshot) {
///         // Build UI based on user data
///       },
///     );
///   }
/// }
/// ```
///
/// ## Testing with Mocks
///
/// ```dart
/// class MockUserRepository extends Mock implements IUserRepository {}
///
/// testWidgets('UserProfileWidget displays user data', (tester) async {
///   final mockRepo = MockUserRepository();
///   when(() => mockRepo.getUserById(any())).thenAnswer(
///     (_) async => UserProfile(/* test data */),
///   );
///
///   await tester.pumpWidget(
///     ProviderScope(
///       overrides: [
///         userRepositoryProvider.overrideWithValue(mockRepo),
///       ],
///       child: UserProfileWidget(userId: 'test-id'),
///     ),
///   );
///
///   // Verify widget behavior
/// });
/// ```
/// AsyncNotifier for user profile operations
///
/// Copied from [UserProfileNotifier].
class UserProfileNotifierProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          UserProfileNotifier,
          UserProfile?
        > {
  /// Future providers for additional repository implementations
  ///
  /// As we implement more repositories, they will be added here following
  /// the same pattern:
  ///
  /// ```dart
  /// final accountRepositoryProvider = Provider<IAccountRepository>((ref) {
  ///   final firestore = ref.watch(firestoreProvider);
  ///   return AccountRepositoryImpl(firestore);
  /// });
  ///
  /// final transactionRepositoryProvider = Provider<ITransactionRepository>((ref) {
  ///   final firestore = ref.watch(firestoreProvider);
  ///   return TransactionRepositoryImpl(firestore);
  /// });
  ///
  /// final categoryRepositoryProvider = Provider<ICategoryRepository>((ref) {
  ///   final firestore = ref.watch(firestoreProvider);
  ///   return CategoryRepositoryImpl(firestore);
  /// });
  ///
  /// final budgetRepositoryProvider = Provider<IBudgetRepository>((ref) {
  ///   final firestore = ref.watch(firestoreProvider);
  ///   return BudgetRepositoryImpl(firestore);
  /// });
  /// ```
  /// Repository provider documentation
  ///
  /// ## Usage in Services/Controllers
  ///
  /// ```dart
  /// class UserService {
  ///   final IUserRepository _userRepository;
  ///
  ///   UserService(this._userRepository);
  ///
  ///   Future<UserProfile?> getCurrentUser(String uid) {
  ///     return _userRepository.getUserById(uid);
  ///   }
  /// }
  ///
  /// final userServiceProvider = Provider<UserService>((ref) {
  ///   final userRepo = ref.watch(userRepositoryProvider);
  ///   return UserService(userRepo);
  /// });
  /// ```
  ///
  /// ## Usage in Widgets
  ///
  /// ```dart
  /// class UserProfileWidget extends ConsumerWidget {
  ///   final String userId;
  ///
  ///   const UserProfileWidget({required this.userId});
  ///
  ///   @override
  ///   Widget build(BuildContext context, WidgetRef ref) {
  ///     final userRepo = ref.watch(userRepositoryProvider);
  ///
  ///     return FutureBuilder<UserProfile?>(
  ///       future: userRepo.getUserById(userId),
  ///       builder: (context, snapshot) {
  ///         // Build UI based on user data
  ///       },
  ///     );
  ///   }
  /// }
  /// ```
  ///
  /// ## Testing with Mocks
  ///
  /// ```dart
  /// class MockUserRepository extends Mock implements IUserRepository {}
  ///
  /// testWidgets('UserProfileWidget displays user data', (tester) async {
  ///   final mockRepo = MockUserRepository();
  ///   when(() => mockRepo.getUserById(any())).thenAnswer(
  ///     (_) async => UserProfile(/* test data */),
  ///   );
  ///
  ///   await tester.pumpWidget(
  ///     ProviderScope(
  ///       overrides: [
  ///         userRepositoryProvider.overrideWithValue(mockRepo),
  ///       ],
  ///       child: UserProfileWidget(userId: 'test-id'),
  ///     ),
  ///   );
  ///
  ///   // Verify widget behavior
  /// });
  /// ```
  /// AsyncNotifier for user profile operations
  ///
  /// Copied from [UserProfileNotifier].
  UserProfileNotifierProvider(String userId)
    : this._internal(
        () => UserProfileNotifier()..userId = userId,
        from: userProfileNotifierProvider,
        name: r'userProfileNotifierProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$userProfileNotifierHash,
        dependencies: UserProfileNotifierFamily._dependencies,
        allTransitiveDependencies:
            UserProfileNotifierFamily._allTransitiveDependencies,
        userId: userId,
      );

  UserProfileNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  FutureOr<UserProfile?> runNotifierBuild(
    covariant UserProfileNotifier notifier,
  ) {
    return notifier.build(userId);
  }

  @override
  Override overrideWith(UserProfileNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: UserProfileNotifierProvider._internal(
        () => create()..userId = userId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<UserProfileNotifier, UserProfile?>
  createElement() {
    return _UserProfileNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UserProfileNotifierProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UserProfileNotifierRef
    on AutoDisposeAsyncNotifierProviderRef<UserProfile?> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _UserProfileNotifierProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          UserProfileNotifier,
          UserProfile?
        >
    with UserProfileNotifierRef {
  _UserProfileNotifierProviderElement(super.provider);

  @override
  String get userId => (origin as UserProfileNotifierProvider).userId;
}

String _$userAccountsNotifierHash() =>
    r'5170c1c63e159d03425f9d9b1f630fc32c93e6fb';

abstract class _$UserAccountsNotifier
    extends BuildlessAutoDisposeAsyncNotifier<List<Account>> {
  late final String userId;

  FutureOr<List<Account>> build(String userId);
}

/// AsyncNotifier for user accounts list
///
/// Copied from [UserAccountsNotifier].
@ProviderFor(UserAccountsNotifier)
const userAccountsNotifierProvider = UserAccountsNotifierFamily();

/// AsyncNotifier for user accounts list
///
/// Copied from [UserAccountsNotifier].
class UserAccountsNotifierFamily extends Family<AsyncValue<List<Account>>> {
  /// AsyncNotifier for user accounts list
  ///
  /// Copied from [UserAccountsNotifier].
  const UserAccountsNotifierFamily();

  /// AsyncNotifier for user accounts list
  ///
  /// Copied from [UserAccountsNotifier].
  UserAccountsNotifierProvider call(String userId) {
    return UserAccountsNotifierProvider(userId);
  }

  @override
  UserAccountsNotifierProvider getProviderOverride(
    covariant UserAccountsNotifierProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'userAccountsNotifierProvider';
}

/// AsyncNotifier for user accounts list
///
/// Copied from [UserAccountsNotifier].
class UserAccountsNotifierProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          UserAccountsNotifier,
          List<Account>
        > {
  /// AsyncNotifier for user accounts list
  ///
  /// Copied from [UserAccountsNotifier].
  UserAccountsNotifierProvider(String userId)
    : this._internal(
        () => UserAccountsNotifier()..userId = userId,
        from: userAccountsNotifierProvider,
        name: r'userAccountsNotifierProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$userAccountsNotifierHash,
        dependencies: UserAccountsNotifierFamily._dependencies,
        allTransitiveDependencies:
            UserAccountsNotifierFamily._allTransitiveDependencies,
        userId: userId,
      );

  UserAccountsNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  FutureOr<List<Account>> runNotifierBuild(
    covariant UserAccountsNotifier notifier,
  ) {
    return notifier.build(userId);
  }

  @override
  Override overrideWith(UserAccountsNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: UserAccountsNotifierProvider._internal(
        () => create()..userId = userId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<UserAccountsNotifier, List<Account>>
  createElement() {
    return _UserAccountsNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UserAccountsNotifierProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UserAccountsNotifierRef
    on AutoDisposeAsyncNotifierProviderRef<List<Account>> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _UserAccountsNotifierProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          UserAccountsNotifier,
          List<Account>
        >
    with UserAccountsNotifierRef {
  _UserAccountsNotifierProviderElement(super.provider);

  @override
  String get userId => (origin as UserAccountsNotifierProvider).userId;
}

String _$remoteConfigNotifierHash() =>
    r'a4097fc529ac7ace3d7eccc02f9a49e9a365cd90';

/// AsyncNotifier for Remote Config data
///
/// Copied from [RemoteConfigNotifier].
@ProviderFor(RemoteConfigNotifier)
final remoteConfigNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      RemoteConfigNotifier,
      RemoteConfigData
    >.internal(
      RemoteConfigNotifier.new,
      name: r'remoteConfigNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$remoteConfigNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$RemoteConfigNotifier = AutoDisposeAsyncNotifier<RemoteConfigData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
