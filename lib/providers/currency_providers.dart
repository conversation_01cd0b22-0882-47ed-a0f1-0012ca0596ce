import 'package:budapp/services/currency_preferences_service.dart';
import 'package:budapp/services/currency_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Provider for SharedPreferences instance
final sharedPreferencesProvider = FutureProvider<SharedPreferences>((
  ref,
) async {
  return SharedPreferences.getInstance();
});

/// Provider for CurrencyPreferencesService
final currencyPreferencesServiceProvider = Provider<CurrencyPreferencesService>(
  (ref) {
    final sharedPrefsAsync = ref.watch(sharedPreferencesProvider);
    return sharedPrefsAsync.when(
      data: CurrencyPreferencesService.new,
      loading: () => throw Exception('SharedPreferences not yet loaded'),
      error: (error, stack) =>
          throw Exception('Failed to load SharedPreferences: $error'),
    );
  },
);

/// Provider for current currency preference
final currencyPreferenceProvider =
    StateNotifierProvider<CurrencyPreferenceNotifier, String>((ref) {
      return CurrencyPreferenceNotifier(ref);
    });

/// Provider for supported currencies
final supportedCurrenciesProvider = Provider<List<String>>((ref) {
  // Return default supported currencies
  return CurrencyService.defaultSupportedCurrencies;
});

/// Provider for currency formatting functions
final currencyFormatterProvider = Provider<CurrencyFormatter>((ref) {
  final currentCurrency = ref.watch(currencyPreferenceProvider);
  return CurrencyFormatter(currentCurrency);
});

/// StateNotifier for managing currency preference
class CurrencyPreferenceNotifier extends StateNotifier<String> {
  CurrencyPreferenceNotifier(this._ref)
    : super(CurrencyService.defaultCurrencyCode) {
    _initializePreference();
  }
  final Ref _ref;
  CurrencyPreferencesService? _preferencesService;

  Future<void> _initializePreference() async {
    try {
      final sharedPrefs = await _ref.read(sharedPreferencesProvider.future);
      _preferencesService = CurrencyPreferencesService(sharedPrefs);
      state = _preferencesService!.getCurrentCurrency();
    } on Exception {
      // Keep default currency if initialization fails
      state = CurrencyService.defaultCurrencyCode;
    }
  }

  /// Update currency preference
  Future<bool> setCurrency(String currencyCode) async {
    if (_preferencesService == null) {
      await _initializePreference();
    }

    try {
      final success = await _preferencesService!.setCurrency(currencyCode);
      if (success) {
        state = currencyCode.toUpperCase();
      }
      return success;
    } on Exception {
      return false;
    }
  }

  /// Reset currency to default
  Future<bool> resetToDefault() async {
    if (_preferencesService == null) {
      await _initializePreference();
    }

    try {
      final success = await _preferencesService!.resetCurrencyToDefault();
      if (success) {
        state = CurrencyService.defaultCurrencyCode;
      }
      return success;
    } on Exception {
      return false;
    }
  }

  /// Get currency symbol for current preference
  String getCurrentCurrencySymbol() {
    return CurrencyService.getCurrencySymbol(state);
  }

  /// Get currency display name for current preference
  String getCurrentCurrencyDisplayName() {
    return CurrencyService.getCurrencyDisplayName(state);
  }
}

/// Helper class for currency formatting
class CurrencyFormatter {
  CurrencyFormatter(this.currencyCode);
  final String currencyCode;

  /// Format amount in cents
  String formatAmount(int amountCents) {
    return CurrencyService.formatAmount(amountCents, currencyCode);
  }

  /// Format amount with sign
  String formatAmountWithSign(
    int amountCents, {
    bool showPositiveSign = false,
  }) {
    return CurrencyService.formatAmountWithSign(
      amountCents,
      currencyCode,
      showPositiveSign: showPositiveSign,
    );
  }

  /// Get currency symbol
  String get symbol => CurrencyService.getCurrencySymbol(currencyCode);

  /// Get currency display name
  String get displayName =>
      CurrencyService.getCurrencyDisplayName(currencyCode);

  /// Check if currency uses decimals
  bool get usesDecimals => CurrencyService.currencyUsesDecimals(currencyCode);

  /// Get decimal places
  int get decimalPlaces =>
      CurrencyService.getCurrencyDecimalPlaces(currencyCode);
}
