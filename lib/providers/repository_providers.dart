import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/remote_config_data.dart';
import 'package:budapp/data/models/user_profile.dart';
import 'package:budapp/data/repositories/implementations/account_repository_impl.dart';
import 'package:budapp/data/repositories/implementations/budget_repository_impl.dart';
import 'package:budapp/data/repositories/implementations/category_repository_impl.dart';
import 'package:budapp/data/repositories/implementations/goal_contribution_repository_impl.dart';
import 'package:budapp/data/repositories/implementations/goal_repository_impl.dart';
import 'package:budapp/data/repositories/implementations/remote_config_repository_impl.dart';
import 'package:budapp/data/repositories/implementations/tag_repository_impl.dart';
import 'package:budapp/data/repositories/implementations/transaction_repository_impl.dart';
import 'package:budapp/data/repositories/implementations/user_repository_impl.dart';
import 'package:budapp/data/repositories/interfaces/repositories.dart';
import 'package:budapp/features/budgets/providers/budget_providers.dart';
import 'package:budapp/providers/providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'repository_providers.g.dart';

/// Repository providers for dependency injection
///
/// These providers create and manage repository instances using the
/// repository pattern with dependency injection via Riverpod.

/// Provider for IUserRepository implementation
///
/// This provider creates a UserRepositoryImpl instance that uses FirestoreService
/// for data persistence. The repository is injected with the FirestoreService
/// instance from the core providers.
final userRepositoryProvider = Provider<IUserRepository>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  final firebaseAuth = ref.watch(firebaseAuthProvider);
  return UserRepositoryImpl(firestoreService, firebaseAuth);
});

/// Provider for ITransactionRepository implementation
///
/// This provider creates a TransactionRepositoryImpl instance that uses FirestoreService
/// for data persistence. The repository is injected with the FirestoreService
/// instance from the core providers.
final transactionRepositoryProvider = Provider<ITransactionRepository>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  final budgetTransactionService = ref.watch(budgetTransactionServiceProvider);
  return TransactionRepositoryImpl(firestoreService, budgetTransactionService);
});

/// Provider for IAccountRepository implementation
///
/// This provider creates an AccountRepositoryImpl instance that uses FirestoreService
/// for data persistence with performance optimizations including intelligent caching.
/// The repository is injected with FirestoreService, FirebaseAuth, and CacheService.
final accountRepositoryProvider = Provider<IAccountRepository>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  final firebaseAuth = ref.watch(firebaseAuthProvider);
  final cacheService = ref.watch(cacheServiceProvider);
  return AccountRepositoryImpl(firestoreService, firebaseAuth, cacheService);
});

/// Provider for ICategoryRepository implementation
///
/// This provider creates a CategoryRepositoryImpl instance that uses FirestoreService
/// and FirebaseAuth for data persistence and user context. The repository is injected
/// with the required services from the core providers.
final categoryRepositoryProvider = Provider<ICategoryRepository>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  final firebaseAuth = ref.watch(firebaseAuthProvider);
  return CategoryRepositoryImpl(firestoreService, firebaseAuth: firebaseAuth);
});

/// Provider for BudgetRepository implementation
///
/// This provider creates a BudgetRepositoryImpl instance that uses FirestoreService
/// and FirebaseAuth for data persistence and user context. The repository is injected
/// with the required services from the core providers.
final budgetRepositoryProvider = Provider<BudgetRepository>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  final firebaseAuth = ref.watch(firebaseAuthProvider);
  return BudgetRepositoryImpl(
    firestoreService: firestoreService,
    firebaseAuth: firebaseAuth,
  );
});

/// Provider for IRemoteConfigRepository implementation
///
/// This provider creates a RemoteConfigRepositoryImpl instance that uses RemoteConfigService
/// for configuration management. The repository is injected with the RemoteConfigService
/// instance from the core providers.
final remoteConfigRepositoryProvider = Provider<IRemoteConfigRepository>((ref) {
  final remoteConfigService = ref.watch(remoteConfigServiceProvider);
  return RemoteConfigRepositoryImpl(remoteConfigService);
});

/// Provider for TagRepository implementation
///
/// This provider creates a TagRepositoryImpl instance that uses FirestoreService
/// and FirebaseAuth for data persistence and user context. The repository is injected
/// with the required services from the core providers.
final tagRepositoryProvider = Provider<TagRepository>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  final firebaseAuth = ref.watch(firebaseAuthProvider);
  return TagRepositoryImpl(
    firestoreService: firestoreService,
    firebaseAuth: firebaseAuth,
  );
});

/// Provider for IGoalRepository implementation
///
/// This provider creates a GoalRepositoryImpl instance that uses FirestoreService
/// and FirebaseAuth for data persistence and user context. The repository is injected
/// with the required services from the core providers.
final goalRepositoryProvider = Provider<IGoalRepository>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  final firebaseAuth = ref.watch(firebaseAuthProvider);
  return GoalRepositoryImpl(firestoreService, firebaseAuth);
});

/// Provider for IGoalContributionRepository implementation
///
/// This provider creates a GoalContributionRepositoryImpl instance that uses FirestoreService
/// and FirebaseAuth for data persistence and user context. The repository manages goal
/// contributions in subcollections under users/{userId}/goals/{goalId}/contributions.
final goalContributionRepositoryProvider =
    Provider<IGoalContributionRepository>((ref) {
      final firestoreService = ref.watch(firestoreServiceProvider);
      final firebaseAuth = ref.watch(firebaseAuthProvider);
      return GoalContributionRepositoryImpl(firestoreService, firebaseAuth);
    });

/// Future providers for additional repository implementations
///
/// As we implement more repositories, they will be added here following
/// the same pattern:
///
/// ```dart
/// final accountRepositoryProvider = Provider<IAccountRepository>((ref) {
///   final firestore = ref.watch(firestoreProvider);
///   return AccountRepositoryImpl(firestore);
/// });
///
/// final transactionRepositoryProvider = Provider<ITransactionRepository>((ref) {
///   final firestore = ref.watch(firestoreProvider);
///   return TransactionRepositoryImpl(firestore);
/// });
///
/// final categoryRepositoryProvider = Provider<ICategoryRepository>((ref) {
///   final firestore = ref.watch(firestoreProvider);
///   return CategoryRepositoryImpl(firestore);
/// });
///
/// final budgetRepositoryProvider = Provider<IBudgetRepository>((ref) {
///   final firestore = ref.watch(firestoreProvider);
///   return BudgetRepositoryImpl(firestore);
/// });
/// ```

/// Repository provider documentation
///
/// ## Usage in Services/Controllers
///
/// ```dart
/// class UserService {
///   final IUserRepository _userRepository;
///
///   UserService(this._userRepository);
///
///   Future<UserProfile?> getCurrentUser(String uid) {
///     return _userRepository.getUserById(uid);
///   }
/// }
///
/// final userServiceProvider = Provider<UserService>((ref) {
///   final userRepo = ref.watch(userRepositoryProvider);
///   return UserService(userRepo);
/// });
/// ```
///
/// ## Usage in Widgets
///
/// ```dart
/// class UserProfileWidget extends ConsumerWidget {
///   final String userId;
///
///   const UserProfileWidget({required this.userId});
///
///   @override
///   Widget build(BuildContext context, WidgetRef ref) {
///     final userRepo = ref.watch(userRepositoryProvider);
///
///     return FutureBuilder<UserProfile?>(
///       future: userRepo.getUserById(userId),
///       builder: (context, snapshot) {
///         // Build UI based on user data
///       },
///     );
///   }
/// }
/// ```
///
/// ## Testing with Mocks
///
/// ```dart
/// class MockUserRepository extends Mock implements IUserRepository {}
///
/// testWidgets('UserProfileWidget displays user data', (tester) async {
///   final mockRepo = MockUserRepository();
///   when(() => mockRepo.getUserById(any())).thenAnswer(
///     (_) async => UserProfile(/* test data */),
///   );
///
///   await tester.pumpWidget(
///     ProviderScope(
///       overrides: [
///         userRepositoryProvider.overrideWithValue(mockRepo),
///       ],
///       child: UserProfileWidget(userId: 'test-id'),
///     ),
///   );
///
///   // Verify widget behavior
/// });
/// ```

// AsyncNotifier providers for repository operations with enhanced error handling

/// AsyncNotifier for user profile operations
@riverpod
class UserProfileNotifier extends _$UserProfileNotifier {
  @override
  FutureOr<UserProfile?> build(String userId) async {
    final userRepository = ref.watch(userRepositoryProvider);
    return userRepository.getUserById(userId);
  }

  /// Create or update user profile with error handling
  Future<void> createOrUpdateProfile(UserProfile profile) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final userRepository = ref.read(userRepositoryProvider);
      await userRepository.update(profile.uid, profile);
      return profile;
    });
  }

  /// Update user preferences with error handling
  Future<void> updatePreferences(
    String userId,
    Map<String, dynamic> preferences,
  ) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final userRepository = ref.read(userRepositoryProvider);
      await userRepository.updatePreferences(userId, preferences);
      // Refresh the user profile
      final updatedProfile = await userRepository.getUserById(userId);
      return updatedProfile;
    });
  }
}

/// AsyncNotifier for user accounts list
@riverpod
class UserAccountsNotifier extends _$UserAccountsNotifier {
  @override
  FutureOr<List<Account>> build(String userId) async {
    final accountRepository = ref.watch(accountRepositoryProvider);
    return accountRepository.getAccountsByUserId(userId);
  }

  /// Create new account with error handling
  Future<void> createAccount(Account account) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final accountRepository = ref.read(accountRepositoryProvider);
      await accountRepository.create(account);
      // Refresh the accounts list
      return accountRepository.getAccountsByUserId(account.userId);
    });
  }

  /// Update account with error handling
  Future<void> updateAccount(String accountId, Account account) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final accountRepository = ref.read(accountRepositoryProvider);
      await accountRepository.update(accountId, account);
      // Refresh the accounts list
      return accountRepository.getAccountsByUserId(account.userId);
    });
  }

  /// Delete account with error handling
  Future<void> deleteAccount(String userId, String accountId) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final accountRepository = ref.read(accountRepositoryProvider);
      // Use the implementation-specific method that requires userId context
      await (accountRepository as dynamic).deleteAccountForUser(
        userId,
        accountId,
      );
      // Refresh the accounts list
      return accountRepository.getAccountsByUserId(userId);
    });
  }
}

/// AsyncNotifier for Remote Config data
@riverpod
class RemoteConfigNotifier extends _$RemoteConfigNotifier {
  @override
  FutureOr<RemoteConfigData> build() async {
    final remoteConfigRepository = ref.watch(remoteConfigRepositoryProvider);

    // Initialize if needed
    await remoteConfigRepository.initialize();

    // Try to fetch and activate fresh data
    try {
      await remoteConfigRepository.fetchAndActivate();
    } on Exception {
      // If fetch fails, continue with cached/default values
    }

    return remoteConfigRepository.getCurrentConfig();
  }

  /// Force refresh Remote Config data
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final remoteConfigRepository = ref.read(remoteConfigRepositoryProvider);
      await remoteConfigRepository.forceRefresh();
      return remoteConfigRepository.getCurrentConfig();
    });
  }

  /// Get predefined categories
  PredefinedCategories getPredefinedCategories() {
    final config = state.valueOrNull;
    return config?.categories ?? PredefinedCategories.defaults();
  }

  /// Get premium limits
  PremiumLimits getPremiumLimits() {
    final config = state.valueOrNull;
    return config?.premiumLimits ?? PremiumLimits.defaults();
  }

  /// Check if a feature is enabled
  bool isFeatureEnabled(String featureKey) {
    final remoteConfigRepository = ref.read(remoteConfigRepositoryProvider);
    return remoteConfigRepository.isFeatureEnabled(featureKey);
  }
}
