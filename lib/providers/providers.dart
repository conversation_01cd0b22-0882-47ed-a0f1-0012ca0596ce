import 'dart:async';

import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/providers/error_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/services/cache_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:budapp/services/implementations/firebase_connectivity_service_impl.dart';
import 'package:budapp/services/interfaces/firebase_connectivity_service.dart';
import 'package:budapp/services/performance_service.dart';
import 'package:budapp/services/remote_config_service.dart';
import 'package:budapp/services/secure_storage_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';

export 'error_providers.dart';
export 'firebase_providers.dart';
export 'logging_providers.dart';
export 'repository_providers.dart';
// Export only global provider files for easy importing
// Note: Feature-specific providers should be imported directly from their feature directories
export 'ui_providers.dart';

/// Core Firebase providers
///
/// These providers give access to Firebase services throughout the app.
/// They are the foundation for all other providers that need Firebase access.

/// Provider for Firebase Auth instance
///
/// This provider gives access to the Firebase Auth singleton.
/// Use this provider when you need to access Firebase Auth directly.
final firebaseAuthProvider = Provider<FirebaseAuth>((ref) {
  return FirebaseAuth.instance;
});

/// Provider for Firestore instance
///
/// This provider gives access to the Firestore singleton.
/// Use this provider when you need to access Firestore directly.
final firestoreProvider = Provider<FirebaseFirestore>((ref) {
  return FirebaseFirestore.instance;
});

/// Provider for GoogleSignIn instance
///
/// This provider gives access to the GoogleSignIn singleton.
/// Use this provider when you need to access Google Sign-In directly.
/// Note: In v7+, GoogleSignIn requires async initialization before use.
final googleSignInProvider = Provider<GoogleSignIn>((ref) {
  return GoogleSignIn.instance;
});

/// Provider for Firebase Remote Config instance
///
/// This provider gives access to the Firebase Remote Config singleton.
/// Use this provider when you need to access Remote Config directly.
final firebaseRemoteConfigProvider = Provider<FirebaseRemoteConfig>((ref) {
  return FirebaseRemoteConfig.instance;
});

/// Provider for FirestoreService instance
///
/// This provider creates a FirestoreService instance with dependency injection.
/// The service wraps Firestore operations and provides centralized access.
final firestoreServiceProvider = Provider<FirestoreService>((ref) {
  final firestore = ref.watch(firestoreProvider);
  return FirestoreService(firestore);
});

/// Provider for SecureStorageService instance
///
/// This provider creates a SecureStorageService instance for secure storage
/// of sensitive data such as biometric keys, API keys, and user PINs.
/// The service wraps flutter_secure_storage with proper error handling.
final secureStorageServiceProvider = Provider<SecureStorageService>((ref) {
  return SecureStorageService();
});

/// Provider for CacheService instance
///
/// This provider creates a CacheService instance for intelligent caching
/// with multi-level caching capabilities (memory + persistent storage).
/// Used by repositories for performance optimization.
final cacheServiceProvider = Provider<CacheService>((ref) {
  return CacheService();
});

/// Provider for RemoteConfigService instance
///
/// This provider creates a RemoteConfigService instance with dependency injection.
/// The service wraps Firebase Remote Config operations and provides centralized access.
final remoteConfigServiceProvider = Provider<RemoteConfigService>((ref) {
  final remoteConfig = ref.watch(firebaseRemoteConfigProvider);
  return RemoteConfigService(remoteConfig);
});

/// Provider for FirebaseConnectivityService instance
///
/// This provider creates a FirebaseConnectivityService instance with dependency injection.
/// The service provides Firebase connectivity testing functionality and abstracts
/// Firebase access from the presentation layer, following Repository pattern principles.
final firebaseConnectivityServiceProvider =
    Provider<IFirebaseConnectivityService>((ref) {
      final firebaseAuth = ref.watch(firebaseAuthProvider);
      final firestore = ref.watch(firestoreProvider);
      return FirebaseConnectivityServiceImpl(
        firebaseAuth: firebaseAuth,
        firestore: firestore,
      );
    });

/// Provider for current user stream
///
/// This provider streams the current authenticated user.
/// It automatically updates when the user signs in or out.
/// Includes immediate emission and timeout handling to prevent infinite loading states.
final authStateProvider = StreamProvider<User?>((ref) {
  final auth = ref.watch(firebaseAuthProvider);

  // Prevent automatic disposal between widget rebuilds to avoid Riverpod's
  // internal zero-duration timer that breaks widget tests. We manually manage
  // disposal below.
  final keepAliveLink = ref.keepAlive();

  // Create a stream controller to manage the auth state
  late StreamController<User?> controller;
  StreamSubscription<User?>? authSubscription;
  // Store reference to timeout timer so it can be cancelled when provider is disposed
  Timer? timeoutTimer;

  controller = StreamController<User?>(
    onListen: () {
      // Immediately emit current user if available
      final currentUser = auth.currentUser;
      if (currentUser != null) {
        // debugPrint('Auth state: Found existing user ${currentUser.uid}');
        controller.add(currentUser);
      } else {
        // debugPrint('Auth state: No existing user');
      }

      // Listen to auth state changes
      authSubscription = auth.authStateChanges().listen(
        (user) {
          // debugPrint('Auth state changed: ${user?.uid ?? 'null'}');
          controller.add(user);
        },
        onError: (dynamic error) {
          // debugPrint('Auth state error: $error');
          controller.addError(error as Object);
        },
      );

      // Add timeout fallback and keep a reference for proper disposal
      timeoutTimer = Timer(const Duration(seconds: 3), () {
        if (!controller.isClosed) {
          final fallbackUser = auth.currentUser;
          debugPrint(
            'Auth state timeout fallback: ${fallbackUser?.uid ?? 'null'}',
          );
          controller.add(fallbackUser);
        }
      });

      // Ensure the timeout is cancelled when the provider is disposed
      // (synchronously before Flutter test framework invariant checks).
      ref.onDispose(() {
        authSubscription?.cancel();
        timeoutTimer?.cancel();
        if (!controller.isClosed) {
          controller.close();
        }
        // Allow Riverpod to dispose the provider when truly no longer needed
        keepAliveLink.close();
      });
    },
    onCancel: () {
      // Cancel resources if the last listener is removed while the provider
      // remains alive.
      authSubscription?.cancel();
      timeoutTimer?.cancel();
      if (!controller.isClosed) {
        controller.close();
      }
    },
  );

  return controller.stream;
});

/// Provider for current user (synchronous)
///
/// This provider gives access to the current user synchronously.
/// Returns null if no user is signed in.
final currentUserProvider = Provider<User?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (user) => user,
    loading: () => null,
    error: (_, _) => null,
  );
});

/// Provider for authentication status
///
/// This provider returns true if a user is currently signed in.
final isAuthenticatedProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user != null;
});

/// Provider for email verification status
///
/// This provider returns true if the current user's email is verified.
final isEmailVerifiedProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.emailVerified ?? false;
});

/// Provider for background app initialization
///
/// This provider initializes non-critical services in the background.
/// Critical services (Firebase Auth) are initialized during Firebase.initializeApp() in main().
/// The app can function without waiting for this provider to complete.
final backgroundInitializationProvider = FutureProvider<bool>((ref) async {
  debugPrint('🚀 Starting background initialization...');

  // Use microtask to ensure this doesn't block the main UI thread
  return Future.microtask(() async {
    try {
      // Initialize Performance Monitoring
      await PerformanceService.initialize().timeout(const Duration(seconds: 3));

      // Initialize global error handling
      await ref
          .read(errorHandlerInitializationProvider.future)
          .timeout(const Duration(seconds: 3));

      // Initialize SessionService
      final sessionService = ref.read(sessionServiceProvider);
      await sessionService.initialize().timeout(const Duration(seconds: 3));

      // Pre-warm TimePeriodNotifier
      try {
        final timePeriodNotifier = ref.read(
          timePeriodNotifierProvider.notifier,
        );
        await timePeriodNotifier.initialize().timeout(
          const Duration(seconds: 2),
        );
      } on Exception catch (e) {
        debugPrint('TimePeriodNotifier initialization failed: $e');
      }

      // Initialize Remote Config in background
      // ignore: unawaited_futures
      Future.microtask(() async {
        try {
          final remoteConfigRepository = ref.read(
            remoteConfigRepositoryProvider,
          );
          await remoteConfigRepository.initialize();
          await remoteConfigRepository.fetchAndActivate();
          debugPrint('Background: Remote Config initialized successfully');
        } on Exception catch (e) {
          debugPrint('Background: Remote Config failed: $e');
        }
      });

      debugPrint('🎉 Background initialization completed successfully');
      return true;
    } on Exception catch (e) {
      debugPrint('❌ Background initialization failed: $e');
      // Don't rethrow - app should still work even if background init fails
      return false;
    }
  });
});
