import 'package:budapp/providers/logging_providers.dart';
import 'package:budapp/services/global_error_handler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Provider for the global error handler singleton
///
/// This provider gives access to the centralized error handling service
/// throughout the application using Riverpod dependency injection.
/// The GlobalErrorHandler manages crash reporting and global error handling.
final globalErrorHandlerProvider = Provider<GlobalErrorHandler>((ref) {
  return GlobalErrorHandler();
});

/// Provider for initializing the global error handler
///
/// This provider handles the initialization of the global error handler
/// with proper dependency injection. It ensures the error handler is
/// properly configured with the logging service before being used.
final errorHandlerInitializationProvider = FutureProvider<void>((ref) async {
  final errorHandler = ref.read(globalErrorHandlerProvider);
  final loggingService = ref.read(loggingServiceProvider);

  await errorHandler.initialize(
    logger: loggingService,
    enableInDebug: false, // Only enable crash reporting in release builds
  );
});
