import 'package:budapp/services/logging_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Provider for the logging service singleton
///
/// This provider gives access to the centralized logging service
/// throughout the application using Riverpod dependency injection.
final loggingServiceProvider = Provider<LoggingService>((ref) {
  return LoggingService();
});

/// Provider for convenient logging functions
///
/// This provider exposes commonly used logging functions for easy access
/// without needing to get the full LoggingService instance.
final loggerProvider = Provider<Logger>((ref) {
  final loggingService = ref.read(loggingServiceProvider);
  return Logger(loggingService);
});

/// Convenience wrapper class for cleaner logging API
class Logger {
  Logger(this._loggingService);
  final LoggingService _loggingService;

  /// Log trace level message (development only)
  void trace(String message, {Object? error, StackTrace? stackTrace}) {
    _loggingService.trace(message, error: error, stackTrace: stackTrace);
  }

  /// Log debug level message (development only)
  void debug(String message, {Object? error, StackTrace? stackTrace}) {
    _loggingService.debug(message, error: error, stackTrace: stackTrace);
  }

  /// Log info level message
  void info(String message, {Object? error, StackTrace? stackTrace}) {
    _loggingService.info(message, error: error, stackTrace: stackTrace);
  }

  /// Log warning level message
  void warning(String message, {Object? error, StackTrace? stackTrace}) {
    _loggingService.warning(message, error: error, stackTrace: stackTrace);
  }

  /// Log error level message
  void error(String message, {Object? error, StackTrace? stackTrace}) {
    _loggingService.error(message, error: error, stackTrace: stackTrace);
  }

  /// Log fatal level message (critical errors)
  void fatal(String message, {Object? error, StackTrace? stackTrace}) {
    _loggingService.fatal(message, error: error, stackTrace: stackTrace);
  }
}
