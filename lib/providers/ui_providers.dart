import 'package:flutter_riverpod/flutter_riverpod.dart';

/// UI State Providers
///
/// These providers manage simple UI state that doesn't require complex logic.
/// They are used for things like loading indicators, form states, and UI flags.

/// Provider for global loading state
///
/// This provider can be used to show a global loading indicator.
/// Set to true when performing app-wide operations like initialization.
final globalLoadingProvider = StateProvider<bool>((ref) => false);

/// Provider for secure mode toggle
///
/// This provider manages the secure mode setting for the app.
/// When true, sensitive financial data should be masked.
final secureModeProvider = StateProvider<bool>((ref) => false);

/// Provider for theme mode preference
///
/// This provider manages the user's theme preference.
/// Can be used to override system theme settings.
final themeModeProvider = StateProvider<String>((ref) => 'system');
