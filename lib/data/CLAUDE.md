# Data Directory - CLAUDE.md

This directory contains the shared data layer with models and repository implementations.

## Directory Structure

```
data/
├── models/              # Freezed data models with JSON serialization
└── repositories/        # Repository pattern implementation
    ├── interfaces/      # Abstract repository contracts
    └── implementations/ # Concrete Firebase-based implementations
```

## Architecture Principles

### Repository Pattern (CRITICAL)
**Strict adherence enforced across all layers.**

✅ **CORRECT Usage:**
```dart
// Always use interfaces in business logic
class MyService {
  final IUserRepository _userRepository;
  MyService(this._userRepository);
}
```

❌ **FORBIDDEN:**
```dart
// NEVER: Direct Firebase access outside repositories
final user = FirebaseAuth.instance.currentUser; // VIOLATION!
```

### Data Models

All models use **Freezed** pattern for immutability:

```dart
@freezed
@JsonSerializable()
class MyModel with _$MyModel {
  const factory MyModel({
    required String id,
    @Default(1) int schemaVersion,
  }) = _MyModel;

  // CRITICAL: Private constructor required for toJson()
  const MyModel._();

  factory MyModel.fromJson(Map<String, dynamic> json) {
    final convertedJson = Map<String, dynamic>.from(json);
    if (convertedJson['schemaVersion'] == null) {
      convertedJson['schemaVersion'] = 1;
    }
    return _$MyModelFromJson(convertedJson);
  }
}
```

## Current Models

### Core Entities
- **Account** - Financial accounts with type, balance, currency
- **Transaction** - Financial transactions with atomic balance updates
- **Budget** - Budget allocations with period management
- **Category** - Hierarchical categories for transaction classification
- **Tag** - Many-to-many tagging system
- **Goal** - Savings goals with contribution tracking
- **GoalContribution** - Individual contributions to goals
- **UserProfile** - User preferences and settings

### Supporting Models
- **TimePeriod** - Shared time period management
- **BudgetProgress** - Calculated budget status
- **RemoteConfigData** - Server-side configuration
- **SessionState** - User session management

## Repository Interfaces

Each model has corresponding repository interface in `interfaces/`:
- Clean contract definition
- Firestore-agnostic (can be swapped for other backends)
- Comprehensive CRUD operations
- Proper error handling with Result types

## Implementation Notes

### Schema Versioning
- All models include `schemaVersion` field (default: 1)
- Migration logic handled in `fromJson()` methods
- Backward compatibility maintained

### JSON Serialization
- All models support serialization via generated code
- Firestore timestamps handled appropriately
- Null safety enforced throughout

### Currency System
- **Global currency preference** - no per-record currency fields
- Amounts stored as integer cents for precision
- Currency formatting handled by dedicated service

## Agent Guidelines

When working with data models:
1. **NEVER modify existing model structure without migration plan**
2. Always run `dart run build_runner build --delete-conflicting-outputs` after changes
3. Update corresponding repository interface if schema changes
4. Test serialization/deserialization thoroughly
5. Maintain backward compatibility with existing data

When working with repositories:
1. Always use interfaces in business logic
2. Never access Firebase directly outside repository implementations
3. Use dependency injection via Riverpod providers
4. Implement proper error handling and logging
5. Follow established patterns in existing repositories

## Code Generation Commands

```bash
# After model changes
dart run build_runner build --delete-conflicting-outputs

# Clean build if needed
dart run build_runner clean
dart run build_runner build --delete-conflicting-outputs
```