// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'category.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Category {

 String get id;// Unique category identifier
 String get userId;// Owner user ID
 String get name;// Category display name (2-50 chars)
 CategoryType get type;// Income or expense
 String? get parentId;// Parent category ID (null for root categories)
 String? get description;// Optional description (max 200 chars)
 String? get color;// Hex color code (#RRGGBB format)
 String? get icon;// Icon identifier/name
 bool get isActive;// Soft delete flag
 int get sortOrder;// Sort order within same parent level
 int get schemaVersion;// Schema version for migrations
 DateTime? get createdAt;// Creation timestamp
 DateTime? get updatedAt;// Last update timestamp
@MetadataConverter() Map<String, dynamic> get metadata;
/// Create a copy of Category
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CategoryCopyWith<Category> get copyWith => _$CategoryCopyWithImpl<Category>(this as Category, _$identity);

  /// Serializes this Category to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Category&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.name, name) || other.name == name)&&(identical(other.type, type) || other.type == type)&&(identical(other.parentId, parentId) || other.parentId == parentId)&&(identical(other.description, description) || other.description == description)&&(identical(other.color, color) || other.color == color)&&(identical(other.icon, icon) || other.icon == icon)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.schemaVersion, schemaVersion) || other.schemaVersion == schemaVersion)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,name,type,parentId,description,color,icon,isActive,sortOrder,schemaVersion,createdAt,updatedAt,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'Category(id: $id, userId: $userId, name: $name, type: $type, parentId: $parentId, description: $description, color: $color, icon: $icon, isActive: $isActive, sortOrder: $sortOrder, schemaVersion: $schemaVersion, createdAt: $createdAt, updatedAt: $updatedAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $CategoryCopyWith<$Res>  {
  factory $CategoryCopyWith(Category value, $Res Function(Category) _then) = _$CategoryCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String name, CategoryType type, String? parentId, String? description, String? color, String? icon, bool isActive, int sortOrder, int schemaVersion, DateTime? createdAt, DateTime? updatedAt,@MetadataConverter() Map<String, dynamic> metadata
});




}
/// @nodoc
class _$CategoryCopyWithImpl<$Res>
    implements $CategoryCopyWith<$Res> {
  _$CategoryCopyWithImpl(this._self, this._then);

  final Category _self;
  final $Res Function(Category) _then;

/// Create a copy of Category
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? name = null,Object? type = null,Object? parentId = freezed,Object? description = freezed,Object? color = freezed,Object? icon = freezed,Object? isActive = null,Object? sortOrder = null,Object? schemaVersion = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? metadata = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as CategoryType,parentId: freezed == parentId ? _self.parentId : parentId // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,color: freezed == color ? _self.color : color // ignore: cast_nullable_to_non_nullable
as String?,icon: freezed == icon ? _self.icon : icon // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,schemaVersion: null == schemaVersion ? _self.schemaVersion : schemaVersion // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

}


/// Adds pattern-matching-related methods to [Category].
extension CategoryPatterns on Category {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Category value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Category() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Category value)  $default,){
final _that = this;
switch (_that) {
case _Category():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Category value)?  $default,){
final _that = this;
switch (_that) {
case _Category() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String name,  CategoryType type,  String? parentId,  String? description,  String? color,  String? icon,  bool isActive,  int sortOrder,  int schemaVersion,  DateTime? createdAt,  DateTime? updatedAt, @MetadataConverter()  Map<String, dynamic> metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Category() when $default != null:
return $default(_that.id,_that.userId,_that.name,_that.type,_that.parentId,_that.description,_that.color,_that.icon,_that.isActive,_that.sortOrder,_that.schemaVersion,_that.createdAt,_that.updatedAt,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String name,  CategoryType type,  String? parentId,  String? description,  String? color,  String? icon,  bool isActive,  int sortOrder,  int schemaVersion,  DateTime? createdAt,  DateTime? updatedAt, @MetadataConverter()  Map<String, dynamic> metadata)  $default,) {final _that = this;
switch (_that) {
case _Category():
return $default(_that.id,_that.userId,_that.name,_that.type,_that.parentId,_that.description,_that.color,_that.icon,_that.isActive,_that.sortOrder,_that.schemaVersion,_that.createdAt,_that.updatedAt,_that.metadata);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String name,  CategoryType type,  String? parentId,  String? description,  String? color,  String? icon,  bool isActive,  int sortOrder,  int schemaVersion,  DateTime? createdAt,  DateTime? updatedAt, @MetadataConverter()  Map<String, dynamic> metadata)?  $default,) {final _that = this;
switch (_that) {
case _Category() when $default != null:
return $default(_that.id,_that.userId,_that.name,_that.type,_that.parentId,_that.description,_that.color,_that.icon,_that.isActive,_that.sortOrder,_that.schemaVersion,_that.createdAt,_that.updatedAt,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Category extends Category {
  const _Category({this.id = '', this.userId = '', this.name = '', this.type = CategoryType.expense, this.parentId, this.description, this.color, this.icon, this.isActive = true, this.sortOrder = 0, this.schemaVersion = 1, this.createdAt, this.updatedAt, @MetadataConverter() final  Map<String, dynamic> metadata = const {}}): _metadata = metadata,super._();
  factory _Category.fromJson(Map<String, dynamic> json) => _$CategoryFromJson(json);

@override@JsonKey() final  String id;
// Unique category identifier
@override@JsonKey() final  String userId;
// Owner user ID
@override@JsonKey() final  String name;
// Category display name (2-50 chars)
@override@JsonKey() final  CategoryType type;
// Income or expense
@override final  String? parentId;
// Parent category ID (null for root categories)
@override final  String? description;
// Optional description (max 200 chars)
@override final  String? color;
// Hex color code (#RRGGBB format)
@override final  String? icon;
// Icon identifier/name
@override@JsonKey() final  bool isActive;
// Soft delete flag
@override@JsonKey() final  int sortOrder;
// Sort order within same parent level
@override@JsonKey() final  int schemaVersion;
// Schema version for migrations
@override final  DateTime? createdAt;
// Creation timestamp
@override final  DateTime? updatedAt;
// Last update timestamp
 final  Map<String, dynamic> _metadata;
// Last update timestamp
@override@JsonKey()@MetadataConverter() Map<String, dynamic> get metadata {
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_metadata);
}


/// Create a copy of Category
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CategoryCopyWith<_Category> get copyWith => __$CategoryCopyWithImpl<_Category>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CategoryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Category&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.name, name) || other.name == name)&&(identical(other.type, type) || other.type == type)&&(identical(other.parentId, parentId) || other.parentId == parentId)&&(identical(other.description, description) || other.description == description)&&(identical(other.color, color) || other.color == color)&&(identical(other.icon, icon) || other.icon == icon)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.schemaVersion, schemaVersion) || other.schemaVersion == schemaVersion)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,name,type,parentId,description,color,icon,isActive,sortOrder,schemaVersion,createdAt,updatedAt,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'Category(id: $id, userId: $userId, name: $name, type: $type, parentId: $parentId, description: $description, color: $color, icon: $icon, isActive: $isActive, sortOrder: $sortOrder, schemaVersion: $schemaVersion, createdAt: $createdAt, updatedAt: $updatedAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$CategoryCopyWith<$Res> implements $CategoryCopyWith<$Res> {
  factory _$CategoryCopyWith(_Category value, $Res Function(_Category) _then) = __$CategoryCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String name, CategoryType type, String? parentId, String? description, String? color, String? icon, bool isActive, int sortOrder, int schemaVersion, DateTime? createdAt, DateTime? updatedAt,@MetadataConverter() Map<String, dynamic> metadata
});




}
/// @nodoc
class __$CategoryCopyWithImpl<$Res>
    implements _$CategoryCopyWith<$Res> {
  __$CategoryCopyWithImpl(this._self, this._then);

  final _Category _self;
  final $Res Function(_Category) _then;

/// Create a copy of Category
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? name = null,Object? type = null,Object? parentId = freezed,Object? description = freezed,Object? color = freezed,Object? icon = freezed,Object? isActive = null,Object? sortOrder = null,Object? schemaVersion = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? metadata = null,}) {
  return _then(_Category(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as CategoryType,parentId: freezed == parentId ? _self.parentId : parentId // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,color: freezed == color ? _self.color : color // ignore: cast_nullable_to_non_nullable
as String?,icon: freezed == icon ? _self.icon : icon // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,schemaVersion: null == schemaVersion ? _self.schemaVersion : schemaVersion // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,metadata: null == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}

/// @nodoc
mixin _$CategoryTree {

 Category get category; List<CategoryTree> get children; int get depth;
/// Create a copy of CategoryTree
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CategoryTreeCopyWith<CategoryTree> get copyWith => _$CategoryTreeCopyWithImpl<CategoryTree>(this as CategoryTree, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CategoryTree&&(identical(other.category, category) || other.category == category)&&const DeepCollectionEquality().equals(other.children, children)&&(identical(other.depth, depth) || other.depth == depth));
}


@override
int get hashCode => Object.hash(runtimeType,category,const DeepCollectionEquality().hash(children),depth);

@override
String toString() {
  return 'CategoryTree(category: $category, children: $children, depth: $depth)';
}


}

/// @nodoc
abstract mixin class $CategoryTreeCopyWith<$Res>  {
  factory $CategoryTreeCopyWith(CategoryTree value, $Res Function(CategoryTree) _then) = _$CategoryTreeCopyWithImpl;
@useResult
$Res call({
 Category category, List<CategoryTree> children, int depth
});


$CategoryCopyWith<$Res> get category;

}
/// @nodoc
class _$CategoryTreeCopyWithImpl<$Res>
    implements $CategoryTreeCopyWith<$Res> {
  _$CategoryTreeCopyWithImpl(this._self, this._then);

  final CategoryTree _self;
  final $Res Function(CategoryTree) _then;

/// Create a copy of CategoryTree
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? category = null,Object? children = null,Object? depth = null,}) {
  return _then(_self.copyWith(
category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as Category,children: null == children ? _self.children : children // ignore: cast_nullable_to_non_nullable
as List<CategoryTree>,depth: null == depth ? _self.depth : depth // ignore: cast_nullable_to_non_nullable
as int,
  ));
}
/// Create a copy of CategoryTree
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CategoryCopyWith<$Res> get category {
  
  return $CategoryCopyWith<$Res>(_self.category, (value) {
    return _then(_self.copyWith(category: value));
  });
}
}


/// Adds pattern-matching-related methods to [CategoryTree].
extension CategoryTreePatterns on CategoryTree {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CategoryTree value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CategoryTree() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CategoryTree value)  $default,){
final _that = this;
switch (_that) {
case _CategoryTree():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CategoryTree value)?  $default,){
final _that = this;
switch (_that) {
case _CategoryTree() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( Category category,  List<CategoryTree> children,  int depth)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CategoryTree() when $default != null:
return $default(_that.category,_that.children,_that.depth);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( Category category,  List<CategoryTree> children,  int depth)  $default,) {final _that = this;
switch (_that) {
case _CategoryTree():
return $default(_that.category,_that.children,_that.depth);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( Category category,  List<CategoryTree> children,  int depth)?  $default,) {final _that = this;
switch (_that) {
case _CategoryTree() when $default != null:
return $default(_that.category,_that.children,_that.depth);case _:
  return null;

}
}

}

/// @nodoc


class _CategoryTree extends CategoryTree {
  const _CategoryTree({required this.category, final  List<CategoryTree> children = const [], this.depth = 0}): _children = children,super._();
  

@override final  Category category;
 final  List<CategoryTree> _children;
@override@JsonKey() List<CategoryTree> get children {
  if (_children is EqualUnmodifiableListView) return _children;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_children);
}

@override@JsonKey() final  int depth;

/// Create a copy of CategoryTree
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CategoryTreeCopyWith<_CategoryTree> get copyWith => __$CategoryTreeCopyWithImpl<_CategoryTree>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CategoryTree&&(identical(other.category, category) || other.category == category)&&const DeepCollectionEquality().equals(other._children, _children)&&(identical(other.depth, depth) || other.depth == depth));
}


@override
int get hashCode => Object.hash(runtimeType,category,const DeepCollectionEquality().hash(_children),depth);

@override
String toString() {
  return 'CategoryTree(category: $category, children: $children, depth: $depth)';
}


}

/// @nodoc
abstract mixin class _$CategoryTreeCopyWith<$Res> implements $CategoryTreeCopyWith<$Res> {
  factory _$CategoryTreeCopyWith(_CategoryTree value, $Res Function(_CategoryTree) _then) = __$CategoryTreeCopyWithImpl;
@override @useResult
$Res call({
 Category category, List<CategoryTree> children, int depth
});


@override $CategoryCopyWith<$Res> get category;

}
/// @nodoc
class __$CategoryTreeCopyWithImpl<$Res>
    implements _$CategoryTreeCopyWith<$Res> {
  __$CategoryTreeCopyWithImpl(this._self, this._then);

  final _CategoryTree _self;
  final $Res Function(_CategoryTree) _then;

/// Create a copy of CategoryTree
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? category = null,Object? children = null,Object? depth = null,}) {
  return _then(_CategoryTree(
category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as Category,children: null == children ? _self._children : children // ignore: cast_nullable_to_non_nullable
as List<CategoryTree>,depth: null == depth ? _self.depth : depth // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

/// Create a copy of CategoryTree
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CategoryCopyWith<$Res> get category {
  
  return $CategoryCopyWith<$Res>(_self.category, (value) {
    return _then(_self.copyWith(category: value));
  });
}
}

// dart format on
