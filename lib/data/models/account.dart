import 'package:freezed_annotation/freezed_annotation.dart';

part 'account.freezed.dart';
part 'account.g.dart';

/// Account types for financial accounts
enum AccountType { checking, savings, creditCard, cash, investment, loan }

/// Account classification for balance calculations
enum AccountClassification {
  asset, // Checking, Savings, Cash, Investment
  liability, // Credit Card, Loan
}

/// Financial account data model
@freezed
sealed class Account with _$Account {
  const factory Account({
    @Default('') String id,
    @Default('') String userId,
    @Default('') String name,
    @Default(AccountType.checking) AccountType type,
    @Default(AccountClassification.asset) AccountClassification classification,
    String? description,
    String? iconName,
    String? colorHex,
    @Default(0)
    int initialBalanceCents, // Stored as cents to avoid floating point issues
    @Default(0)
    int currentBalanceCents, // Current balance updated with each transaction
    @Default(false) bool isPrimary,
    @Default(true) bool isActive,
    @Default(1) int schemaVersion, // Schema version for migrations
    DateTime? createdAt,
    DateTime? updatedAt,
    @Default({}) Map<String, dynamic> metadata,
  }) = _Account;

  const Account._();

  factory Account.fromJson(Map<String, dynamic> json) =>
      _$AccountFromJson(json);
}
