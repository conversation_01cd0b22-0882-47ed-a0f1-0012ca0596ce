import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'budget_progress.freezed.dart';

/// Budget progress status
enum BudgetProgressStatus {
  onTrack, // Under 75% of budget
  warning, // 75-100% of budget
  overBudget, // Over 100% of budget
}

/// Budget progress tracking data model
@freezed
sealed class BudgetProgress with _$BudgetProgress {
  const factory BudgetProgress({
    required String budgetId,
    required double spent,
    required double remaining,
    required double percentage,
    required Color statusColor,
    required bool isOverBudget,
    required BudgetProgressStatus status,
  }) = _BudgetProgress;

  /// Creates budget progress from budget and spent amount
  factory BudgetProgress.fromBudgetAndSpent({
    required String budgetId,
    required double budgetAmount,
    required double spentAmount,
  }) {
    final spent = spentAmount.abs();
    final remaining = budgetAmount - spent;
    final percentage = budgetAmount > 0 ? (spent / budgetAmount) * 100 : 0;
    final isOverBudget = spent > budgetAmount;

    // Determine status and color based on percentage
    final BudgetProgressStatus status;
    final Color statusColor;

    if (percentage <= 75) {
      status = BudgetProgressStatus.onTrack;
      statusColor = Colors.green;
    } else if (percentage <= 100) {
      status = BudgetProgressStatus.warning;
      statusColor = Colors.orange;
    } else {
      status = BudgetProgressStatus.overBudget;
      statusColor = Colors.red;
    }

    return BudgetProgress(
      budgetId: budgetId,
      spent: spent,
      remaining: remaining,
      percentage: percentage.toDouble(),
      statusColor: statusColor,
      isOverBudget: isOverBudget,
      status: status,
    );
  }

  /// Creates empty progress (no spending)
  factory BudgetProgress.empty(String budgetId, double budgetAmount) {
    return BudgetProgress.fromBudgetAndSpent(
      budgetId: budgetId,
      budgetAmount: budgetAmount,
      spentAmount: 0,
    );
  }
}

extension BudgetProgressExtension on BudgetProgress {
  /// Gets the progress percentage as a value between 0 and 1 for progress bars
  double get progressValue {
    final value = percentage / 100;
    return value.clamp(0, 1);
  }

  /// Gets the overflow percentage for over-budget scenarios
  double get overflowPercentage {
    return percentage > 100 ? percentage - 100 : 0;
  }

  /// Gets a user-friendly status message
  String get statusMessage {
    switch (status) {
      case BudgetProgressStatus.onTrack:
        return 'On track';
      case BudgetProgressStatus.warning:
        return 'Approaching limit';
      case BudgetProgressStatus.overBudget:
        return 'Over budget';
    }
  }

  /// Gets the percentage as a formatted string
  String get percentageString {
    return '${percentage.toStringAsFixed(1)}%';
  }

  /// Gets the remaining amount as a formatted string with proper sign
  String get remainingString {
    if (remaining >= 0) {
      return '+${remaining.toStringAsFixed(2)}';
    } else {
      return remaining.toStringAsFixed(2);
    }
  }

  /// Gets the spent amount as a formatted string
  String get spentString {
    return spent.toStringAsFixed(2);
  }

  /// Checks if the budget is in a critical state (over budget or very close)
  bool get isCritical {
    return status == BudgetProgressStatus.overBudget || percentage >= 95;
  }

  /// Gets the appropriate icon for the current status
  IconData get statusIcon {
    switch (status) {
      case BudgetProgressStatus.onTrack:
        return Icons.check_circle;
      case BudgetProgressStatus.warning:
        return Icons.warning;
      case BudgetProgressStatus.overBudget:
        return Icons.error;
    }
  }
}
