// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Transaction _$TransactionFromJson(Map<String, dynamic> json) => _Transaction(
  id: json['id'] as String? ?? '',
  userId: json['userId'] as String? ?? '',
  type:
      $enumDecodeNullable(_$TransactionTypeEnumMap, json['type']) ??
      TransactionType.expense,
  status:
      $enumDecodeNullable(_$TransactionStatusEnumMap, json['status']) ??
      TransactionStatus.completed,
  amountCents: (json['amountCents'] as num?)?.toInt() ?? 0,
  fromAccountId: json['fromAccountId'] as String?,
  toAccountId: json['toAccountId'] as String?,
  categoryId: json['categoryId'] as String?,
  description: json['description'] as String?,
  notes: json['notes'] as String?,
  tagIds:
      (json['tagIds'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  schemaVersion: (json['schemaVersion'] as num?)?.toInt() ?? 1,
  transactionDate: json['transactionDate'] == null
      ? null
      : DateTime.parse(json['transactionDate'] as String),
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$TransactionToJson(_Transaction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'type': _$TransactionTypeEnumMap[instance.type]!,
      'status': _$TransactionStatusEnumMap[instance.status]!,
      'amountCents': instance.amountCents,
      'fromAccountId': instance.fromAccountId,
      'toAccountId': instance.toAccountId,
      'categoryId': instance.categoryId,
      'description': instance.description,
      'notes': instance.notes,
      'tagIds': instance.tagIds,
      'schemaVersion': instance.schemaVersion,
      'transactionDate': instance.transactionDate?.toIso8601String(),
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$TransactionTypeEnumMap = {
  TransactionType.income: 'income',
  TransactionType.expense: 'expense',
  TransactionType.transfer: 'transfer',
};

const _$TransactionStatusEnumMap = {
  TransactionStatus.pending: 'pending',
  TransactionStatus.completed: 'completed',
  TransactionStatus.cancelled: 'cancelled',
  TransactionStatus.failed: 'failed',
};
