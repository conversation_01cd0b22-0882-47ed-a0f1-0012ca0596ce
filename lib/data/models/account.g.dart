// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Account _$AccountFromJson(Map<String, dynamic> json) => _Account(
  id: json['id'] as String? ?? '',
  userId: json['userId'] as String? ?? '',
  name: json['name'] as String? ?? '',
  type:
      $enumDecodeNullable(_$AccountTypeEnumMap, json['type']) ??
      AccountType.checking,
  classification:
      $enumDecodeNullable(
        _$AccountClassificationEnumMap,
        json['classification'],
      ) ??
      AccountClassification.asset,
  description: json['description'] as String?,
  iconName: json['iconName'] as String?,
  colorHex: json['colorHex'] as String?,
  initialBalanceCents: (json['initialBalanceCents'] as num?)?.toInt() ?? 0,
  currentBalanceCents: (json['currentBalanceCents'] as num?)?.toInt() ?? 0,
  isPrimary: json['isPrimary'] as bool? ?? false,
  isActive: json['isActive'] as bool? ?? true,
  schemaVersion: (json['schemaVersion'] as num?)?.toInt() ?? 1,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$AccountToJson(_Account instance) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'name': instance.name,
  'type': _$AccountTypeEnumMap[instance.type]!,
  'classification': _$AccountClassificationEnumMap[instance.classification]!,
  'description': instance.description,
  'iconName': instance.iconName,
  'colorHex': instance.colorHex,
  'initialBalanceCents': instance.initialBalanceCents,
  'currentBalanceCents': instance.currentBalanceCents,
  'isPrimary': instance.isPrimary,
  'isActive': instance.isActive,
  'schemaVersion': instance.schemaVersion,
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
  'metadata': instance.metadata,
};

const _$AccountTypeEnumMap = {
  AccountType.checking: 'checking',
  AccountType.savings: 'savings',
  AccountType.creditCard: 'creditCard',
  AccountType.cash: 'cash',
  AccountType.investment: 'investment',
  AccountType.loan: 'loan',
};

const _$AccountClassificationEnumMap = {
  AccountClassification.asset: 'asset',
  AccountClassification.liability: 'liability',
};
