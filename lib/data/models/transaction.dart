import 'package:freezed_annotation/freezed_annotation.dart';

part 'transaction.freezed.dart';
part 'transaction.g.dart';

/// Transaction types
enum TransactionType {
  income, // Money coming in
  expense, // Money going out
  transfer, // Money moving between accounts
}

/// Transaction status
enum TransactionStatus {
  pending, // Transaction is pending
  completed, // Transaction is completed
  cancelled, // Transaction was cancelled
  failed, // Transaction failed
}

/// Financial transaction data model
@freezed
sealed class Transaction with _$Transaction {
  const factory Transaction({
    @Default('') String id,
    @Default('') String userId,
    @Default(TransactionType.expense) TransactionType type,
    @Default(TransactionStatus.completed) TransactionStatus status,
    @Default(0)
    int amountCents, // Stored as cents to avoid floating point issues
    String? fromAccountId, // Source account (for expenses and transfers)
    String? toAccountId, // Destination account (for income and transfers)
    String? categoryId, // Transaction category
    String? description, // User description
    String? notes, // Additional notes
    @Default([]) List<String> tagIds, // Transaction tag IDs
    @Default(1) int schemaVersion, // Schema version for migrations
    DateTime? transactionDate, // When the transaction occurred
    DateTime? createdAt, // When the record was created
    DateTime? updatedAt, // When the record was last updated
    @Default({}) Map<String, dynamic> metadata, // Additional metadata
  }) = _Transaction;

  const Transaction._();

  factory Transaction.fromJson(Map<String, dynamic> json) =>
      _$TransactionFromJson(json);
}
