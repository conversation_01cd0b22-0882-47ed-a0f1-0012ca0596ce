import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

/// Represents the current state of the user session
enum SessionState {
  /// Session is being initialized or restored
  loading,

  /// User is authenticated and session is active
  authenticated,

  /// User is not authenticated
  unauthenticated,

  /// User is authenticated but email is not verified
  emailNotVerified,

  /// An error occurred during session management
  error,
}

/// Represents session-related errors
@immutable
class SessionError {
  const SessionError({
    required this.code,
    required this.message,
    this.originalError,
  });
  final String code;
  final String message;
  final dynamic originalError;

  @override
  String toString() => 'SessionError(code: $code, message: $message)';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SessionError &&
          runtimeType == other.runtimeType &&
          code == other.code &&
          message == other.message;

  @override
  int get hashCode => code.hashCode ^ message.hashCode;
}

/// Represents the current session information
@immutable
class SessionInfo {
  const SessionInfo({
    required this.state,
    this.user,
    this.error,
    this.lastLoginTime,
    this.authProvider,
    this.preferences = const {},
  });

  /// Creates a loading session state
  factory SessionInfo.loading() {
    return const SessionInfo(state: SessionState.loading);
  }

  /// Creates an authenticated session state
  factory SessionInfo.authenticated({
    required User user,
    DateTime? lastLoginTime,
    String? authProvider,
    Map<String, dynamic> preferences = const {},
  }) {
    return SessionInfo(
      state: SessionState.authenticated,
      user: user,
      lastLoginTime: lastLoginTime,
      authProvider: authProvider,
      preferences: preferences,
    );
  }

  /// Creates an unauthenticated session state
  factory SessionInfo.unauthenticated() {
    return const SessionInfo(state: SessionState.unauthenticated);
  }

  /// Creates an email not verified session state
  factory SessionInfo.emailNotVerified({
    required User user,
    Map<String, dynamic> preferences = const {},
  }) {
    return SessionInfo(
      state: SessionState.emailNotVerified,
      user: user,
      preferences: preferences,
    );
  }

  /// Creates an error session state
  factory SessionInfo.error({required SessionError error, User? user}) {
    return SessionInfo(state: SessionState.error, user: user, error: error);
  }
  final SessionState state;
  final User? user;
  final SessionError? error;
  final DateTime? lastLoginTime;
  final String? authProvider;
  final Map<String, dynamic> preferences;

  /// Creates a copy of this session info with updated values
  SessionInfo copyWith({
    SessionState? state,
    User? user,
    SessionError? error,
    DateTime? lastLoginTime,
    String? authProvider,
    Map<String, dynamic>? preferences,
  }) {
    return SessionInfo(
      state: state ?? this.state,
      user: user ?? this.user,
      error: error ?? this.error,
      lastLoginTime: lastLoginTime ?? this.lastLoginTime,
      authProvider: authProvider ?? this.authProvider,
      preferences: preferences ?? this.preferences,
    );
  }

  /// Whether the session is in a loading state
  bool get isLoading => state == SessionState.loading;

  /// Whether the user is authenticated
  bool get isAuthenticated => state == SessionState.authenticated;

  /// Whether the user is unauthenticated
  bool get isUnauthenticated => state == SessionState.unauthenticated;

  /// Whether the user's email is not verified
  bool get isEmailNotVerified => state == SessionState.emailNotVerified;

  /// Whether there's an error in the session
  bool get hasError => state == SessionState.error;

  /// Whether the user has a valid session (authenticated or email not verified)
  bool get hasValidUser =>
      user != null && (isAuthenticated || isEmailNotVerified);

  @override
  String toString() {
    return 'SessionInfo(state: $state, user: ${user?.uid}, error: $error)';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SessionInfo &&
          runtimeType == other.runtimeType &&
          state == other.state &&
          user?.uid == other.user?.uid &&
          error == other.error;

  @override
  int get hashCode =>
      state.hashCode ^ (user?.uid.hashCode ?? 0) ^ (error?.hashCode ?? 0);
}
