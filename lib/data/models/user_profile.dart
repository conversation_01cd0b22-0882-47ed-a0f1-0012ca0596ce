import 'package:firebase_auth/firebase_auth.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_profile.freezed.dart';
part 'user_profile.g.dart';

/// User profile data model for repository operations
@freezed
sealed class UserProfile with _$UserProfile {
  const factory UserProfile({
    @Default('') String uid,
    String? email,
    String? displayName,
    String? photoURL,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    @Default({}) Map<String, dynamic> preferences,
    @Default(false) bool isEmailVerified,
    @Default([]) List<String> authProviders,
    @Default(1) int schemaVersion, // Schema version for migrations
  }) = _UserProfile;

  /// Create UserProfile from Firebase User
  factory UserProfile.fromFirebaseUser(User user) {
    return UserProfile(
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL,
      createdAt: user.metadata.creationTime ?? DateTime.now(),
      lastLoginAt: user.metadata.lastSignInTime ?? DateTime.now(),
      isEmailVerified: user.emailVerified,
      authProviders: user.providerData.map((info) => info.providerId).toList(),
    );
  }

  const UserProfile._();

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);
}
