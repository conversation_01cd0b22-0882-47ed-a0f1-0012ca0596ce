// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'budget.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Budget _$BudgetFromJson(Map<String, dynamic> json) => _Budget(
  id: json['id'] as String,
  userId: json['userId'] as String,
  type:
      $enumDecodeNullable(_$BudgetTypeEnumMap, json['type']) ??
      BudgetType.expense,
  plannedAmountCents: (json['plannedAmountCents'] as num).toInt(),
  currentAmountCents: (json['currentAmountCents'] as num?)?.toInt() ?? 0,
  period:
      $enumDecodeNullable(_$BudgetPeriodEnumMap, json['period']) ??
      BudgetPeriod.monthly,
  periodStart: DateTime.parse(json['periodStart'] as String),
  categoryId: json['categoryId'] as String?,
  parentBudgetId: json['parentBudgetId'] as String?,
  isActive: json['isActive'] as bool? ?? true,
  schemaVersion: (json['schemaVersion'] as num?)?.toInt() ?? 1,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$BudgetToJson(_Budget instance) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'type': _$BudgetTypeEnumMap[instance.type]!,
  'plannedAmountCents': instance.plannedAmountCents,
  'currentAmountCents': instance.currentAmountCents,
  'period': _$BudgetPeriodEnumMap[instance.period]!,
  'periodStart': instance.periodStart.toIso8601String(),
  'categoryId': instance.categoryId,
  'parentBudgetId': instance.parentBudgetId,
  'isActive': instance.isActive,
  'schemaVersion': instance.schemaVersion,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'metadata': instance.metadata,
};

const _$BudgetTypeEnumMap = {
  BudgetType.expense: 'expense',
  BudgetType.income: 'income',
};

const _$BudgetPeriodEnumMap = {
  BudgetPeriod.monthly: 'monthly',
  BudgetPeriod.yearly: 'yearly',
};
