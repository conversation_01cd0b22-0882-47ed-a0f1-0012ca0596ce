// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'budget.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Budget {

 String get id; String get userId; BudgetType get type; int get plannedAmountCents;// Target/budgeted amount in cents
 int get currentAmountCents;// Actual spent/received amount in cents
 BudgetPeriod get period; DateTime get periodStart;// Explicit period start date
// (e.g., 2024-01-01 for January 2024)
 String? get categoryId;// Nullable for total budgets
 String? get parentBudgetId;// Nullable for hierarchy
 bool get isActive; int get schemaVersion;// Schema version for migrations
 DateTime get createdAt; DateTime get updatedAt; Map<String, dynamic> get metadata;
/// Create a copy of Budget
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BudgetCopyWith<Budget> get copyWith => _$BudgetCopyWithImpl<Budget>(this as Budget, _$identity);

  /// Serializes this Budget to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Budget&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.type, type) || other.type == type)&&(identical(other.plannedAmountCents, plannedAmountCents) || other.plannedAmountCents == plannedAmountCents)&&(identical(other.currentAmountCents, currentAmountCents) || other.currentAmountCents == currentAmountCents)&&(identical(other.period, period) || other.period == period)&&(identical(other.periodStart, periodStart) || other.periodStart == periodStart)&&(identical(other.categoryId, categoryId) || other.categoryId == categoryId)&&(identical(other.parentBudgetId, parentBudgetId) || other.parentBudgetId == parentBudgetId)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.schemaVersion, schemaVersion) || other.schemaVersion == schemaVersion)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,type,plannedAmountCents,currentAmountCents,period,periodStart,categoryId,parentBudgetId,isActive,schemaVersion,createdAt,updatedAt,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'Budget(id: $id, userId: $userId, type: $type, plannedAmountCents: $plannedAmountCents, currentAmountCents: $currentAmountCents, period: $period, periodStart: $periodStart, categoryId: $categoryId, parentBudgetId: $parentBudgetId, isActive: $isActive, schemaVersion: $schemaVersion, createdAt: $createdAt, updatedAt: $updatedAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $BudgetCopyWith<$Res>  {
  factory $BudgetCopyWith(Budget value, $Res Function(Budget) _then) = _$BudgetCopyWithImpl;
@useResult
$Res call({
 String id, String userId, BudgetType type, int plannedAmountCents, int currentAmountCents, BudgetPeriod period, DateTime periodStart, String? categoryId, String? parentBudgetId, bool isActive, int schemaVersion, DateTime createdAt, DateTime updatedAt, Map<String, dynamic> metadata
});




}
/// @nodoc
class _$BudgetCopyWithImpl<$Res>
    implements $BudgetCopyWith<$Res> {
  _$BudgetCopyWithImpl(this._self, this._then);

  final Budget _self;
  final $Res Function(Budget) _then;

/// Create a copy of Budget
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? type = null,Object? plannedAmountCents = null,Object? currentAmountCents = null,Object? period = null,Object? periodStart = null,Object? categoryId = freezed,Object? parentBudgetId = freezed,Object? isActive = null,Object? schemaVersion = null,Object? createdAt = null,Object? updatedAt = null,Object? metadata = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as BudgetType,plannedAmountCents: null == plannedAmountCents ? _self.plannedAmountCents : plannedAmountCents // ignore: cast_nullable_to_non_nullable
as int,currentAmountCents: null == currentAmountCents ? _self.currentAmountCents : currentAmountCents // ignore: cast_nullable_to_non_nullable
as int,period: null == period ? _self.period : period // ignore: cast_nullable_to_non_nullable
as BudgetPeriod,periodStart: null == periodStart ? _self.periodStart : periodStart // ignore: cast_nullable_to_non_nullable
as DateTime,categoryId: freezed == categoryId ? _self.categoryId : categoryId // ignore: cast_nullable_to_non_nullable
as String?,parentBudgetId: freezed == parentBudgetId ? _self.parentBudgetId : parentBudgetId // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,schemaVersion: null == schemaVersion ? _self.schemaVersion : schemaVersion // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

}


/// Adds pattern-matching-related methods to [Budget].
extension BudgetPatterns on Budget {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Budget value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Budget() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Budget value)  $default,){
final _that = this;
switch (_that) {
case _Budget():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Budget value)?  $default,){
final _that = this;
switch (_that) {
case _Budget() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  BudgetType type,  int plannedAmountCents,  int currentAmountCents,  BudgetPeriod period,  DateTime periodStart,  String? categoryId,  String? parentBudgetId,  bool isActive,  int schemaVersion,  DateTime createdAt,  DateTime updatedAt,  Map<String, dynamic> metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Budget() when $default != null:
return $default(_that.id,_that.userId,_that.type,_that.plannedAmountCents,_that.currentAmountCents,_that.period,_that.periodStart,_that.categoryId,_that.parentBudgetId,_that.isActive,_that.schemaVersion,_that.createdAt,_that.updatedAt,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  BudgetType type,  int plannedAmountCents,  int currentAmountCents,  BudgetPeriod period,  DateTime periodStart,  String? categoryId,  String? parentBudgetId,  bool isActive,  int schemaVersion,  DateTime createdAt,  DateTime updatedAt,  Map<String, dynamic> metadata)  $default,) {final _that = this;
switch (_that) {
case _Budget():
return $default(_that.id,_that.userId,_that.type,_that.plannedAmountCents,_that.currentAmountCents,_that.period,_that.periodStart,_that.categoryId,_that.parentBudgetId,_that.isActive,_that.schemaVersion,_that.createdAt,_that.updatedAt,_that.metadata);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  BudgetType type,  int plannedAmountCents,  int currentAmountCents,  BudgetPeriod period,  DateTime periodStart,  String? categoryId,  String? parentBudgetId,  bool isActive,  int schemaVersion,  DateTime createdAt,  DateTime updatedAt,  Map<String, dynamic> metadata)?  $default,) {final _that = this;
switch (_that) {
case _Budget() when $default != null:
return $default(_that.id,_that.userId,_that.type,_that.plannedAmountCents,_that.currentAmountCents,_that.period,_that.periodStart,_that.categoryId,_that.parentBudgetId,_that.isActive,_that.schemaVersion,_that.createdAt,_that.updatedAt,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Budget implements Budget {
  const _Budget({required this.id, required this.userId, this.type = BudgetType.expense, required this.plannedAmountCents, this.currentAmountCents = 0, this.period = BudgetPeriod.monthly, required this.periodStart, this.categoryId, this.parentBudgetId, this.isActive = true, this.schemaVersion = 1, required this.createdAt, required this.updatedAt, final  Map<String, dynamic> metadata = const {}}): _metadata = metadata;
  factory _Budget.fromJson(Map<String, dynamic> json) => _$BudgetFromJson(json);

@override final  String id;
@override final  String userId;
@override@JsonKey() final  BudgetType type;
@override final  int plannedAmountCents;
// Target/budgeted amount in cents
@override@JsonKey() final  int currentAmountCents;
// Actual spent/received amount in cents
@override@JsonKey() final  BudgetPeriod period;
@override final  DateTime periodStart;
// Explicit period start date
// (e.g., 2024-01-01 for January 2024)
@override final  String? categoryId;
// Nullable for total budgets
@override final  String? parentBudgetId;
// Nullable for hierarchy
@override@JsonKey() final  bool isActive;
@override@JsonKey() final  int schemaVersion;
// Schema version for migrations
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
 final  Map<String, dynamic> _metadata;
@override@JsonKey() Map<String, dynamic> get metadata {
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_metadata);
}


/// Create a copy of Budget
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BudgetCopyWith<_Budget> get copyWith => __$BudgetCopyWithImpl<_Budget>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BudgetToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Budget&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.type, type) || other.type == type)&&(identical(other.plannedAmountCents, plannedAmountCents) || other.plannedAmountCents == plannedAmountCents)&&(identical(other.currentAmountCents, currentAmountCents) || other.currentAmountCents == currentAmountCents)&&(identical(other.period, period) || other.period == period)&&(identical(other.periodStart, periodStart) || other.periodStart == periodStart)&&(identical(other.categoryId, categoryId) || other.categoryId == categoryId)&&(identical(other.parentBudgetId, parentBudgetId) || other.parentBudgetId == parentBudgetId)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.schemaVersion, schemaVersion) || other.schemaVersion == schemaVersion)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,type,plannedAmountCents,currentAmountCents,period,periodStart,categoryId,parentBudgetId,isActive,schemaVersion,createdAt,updatedAt,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'Budget(id: $id, userId: $userId, type: $type, plannedAmountCents: $plannedAmountCents, currentAmountCents: $currentAmountCents, period: $period, periodStart: $periodStart, categoryId: $categoryId, parentBudgetId: $parentBudgetId, isActive: $isActive, schemaVersion: $schemaVersion, createdAt: $createdAt, updatedAt: $updatedAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$BudgetCopyWith<$Res> implements $BudgetCopyWith<$Res> {
  factory _$BudgetCopyWith(_Budget value, $Res Function(_Budget) _then) = __$BudgetCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, BudgetType type, int plannedAmountCents, int currentAmountCents, BudgetPeriod period, DateTime periodStart, String? categoryId, String? parentBudgetId, bool isActive, int schemaVersion, DateTime createdAt, DateTime updatedAt, Map<String, dynamic> metadata
});




}
/// @nodoc
class __$BudgetCopyWithImpl<$Res>
    implements _$BudgetCopyWith<$Res> {
  __$BudgetCopyWithImpl(this._self, this._then);

  final _Budget _self;
  final $Res Function(_Budget) _then;

/// Create a copy of Budget
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? type = null,Object? plannedAmountCents = null,Object? currentAmountCents = null,Object? period = null,Object? periodStart = null,Object? categoryId = freezed,Object? parentBudgetId = freezed,Object? isActive = null,Object? schemaVersion = null,Object? createdAt = null,Object? updatedAt = null,Object? metadata = null,}) {
  return _then(_Budget(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as BudgetType,plannedAmountCents: null == plannedAmountCents ? _self.plannedAmountCents : plannedAmountCents // ignore: cast_nullable_to_non_nullable
as int,currentAmountCents: null == currentAmountCents ? _self.currentAmountCents : currentAmountCents // ignore: cast_nullable_to_non_nullable
as int,period: null == period ? _self.period : period // ignore: cast_nullable_to_non_nullable
as BudgetPeriod,periodStart: null == periodStart ? _self.periodStart : periodStart // ignore: cast_nullable_to_non_nullable
as DateTime,categoryId: freezed == categoryId ? _self.categoryId : categoryId // ignore: cast_nullable_to_non_nullable
as String?,parentBudgetId: freezed == parentBudgetId ? _self.parentBudgetId : parentBudgetId // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,schemaVersion: null == schemaVersion ? _self.schemaVersion : schemaVersion // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,metadata: null == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}

// dart format on
