import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'goal_contribution.freezed.dart';
part 'goal_contribution.g.dart';

/// Financial goal contribution data model
@freezed
sealed class GoalContribution with _$GoalContribution {
  const factory GoalContribution({
    @Default('') String id, // Unique contribution identifier
    @Default('') String userId, // Owner user ID
    @Default('') String goalId, // Parent goal ID
    @Default(0) int amountCents, // Contribution amount in cents
    DateTime? contributionDate, // When the contribution was made
    String? description, // Optional description
    @Default(true) bool isActive, // Soft delete flag
    @Default(1) int schemaVersion, // Schema version for migrations
    DateTime? createdAt, // Creation timestamp
    DateTime? updatedAt, // Last update timestamp
    @Default({}) Map<String, dynamic> metadata, // Additional metadata
  }) = _GoalContribution;

  const GoalContribution._();

  factory GoalContribution.fromJson(Map<String, dynamic> json) =>
      _$GoalContributionFromJson(json);

  /// Create from Firestore DocumentSnapshot
  factory GoalContribution.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> doc,
  ) {
    final data = doc.data();
    if (data == null) {
      throw Exception('GoalContribution document data is null');
    }

    // Handle Firestore Timestamp conversion
    final convertedData = Map<String, dynamic>.from(data);

    // Convert Firestore Timestamps to DateTime strings
    if (convertedData['contributionDate'] is Timestamp) {
      convertedData['contributionDate'] =
          (convertedData['contributionDate'] as Timestamp)
              .toDate()
              .toIso8601String();
    }
    if (convertedData['createdAt'] is Timestamp) {
      convertedData['createdAt'] = (convertedData['createdAt'] as Timestamp)
          .toDate()
          .toIso8601String();
    }
    if (convertedData['updatedAt'] is Timestamp) {
      convertedData['updatedAt'] = (convertedData['updatedAt'] as Timestamp)
          .toDate()
          .toIso8601String();
    }

    // Ensure document ID matches the id field
    convertedData['id'] = doc.id;

    return GoalContribution.fromJson(convertedData);
  }

  /// Create a new goal contribution
  factory GoalContribution.create({
    required String userId,
    required String goalId,
    required int amountCents,
    required DateTime contributionDate,
    String? description,
  }) {
    final now = DateTime.now();
    return GoalContribution(
      id: '', // Will be set by repository
      userId: userId,
      goalId: goalId,
      amountCents: amountCents,
      contributionDate: contributionDate,
      description: description,
      isActive: true,
      schemaVersion: 1,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Convert to Firestore document data
  Map<String, dynamic> toFirestore() {
    final data = toJson();

    // Convert DateTime to Firestore Timestamp for server storage
    if (data['contributionDate'] is String) {
      data['contributionDate'] = Timestamp.fromDate(
        DateTime.parse(data['contributionDate'] as String),
      );
    }
    if (data['createdAt'] is String) {
      data['createdAt'] = Timestamp.fromDate(
        DateTime.parse(data['createdAt'] as String),
      );
    }
    if (data['updatedAt'] is String) {
      data['updatedAt'] = Timestamp.fromDate(
        DateTime.parse(data['updatedAt'] as String),
      );
    }

    return data;
  }

  /// Generate a new contribution with updated timestamp
  GoalContribution updated() => copyWith(updatedAt: DateTime.now());

  /// Check if contribution has a description
  bool get hasDescription => description != null && description!.isNotEmpty;

  /// Check if contribution was made today
  bool get isMadeToday {
    final date = contributionDate;
    if (date == null) return false;

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final contribDate = DateTime(
      date.year,
      date.month,
      date.day,
    );
    return contribDate.isAtSameMomentAs(today);
  }

  /// Check if contribution was made this week
  bool get isMadeThisWeek {
    final date = contributionDate;
    if (date == null) return false;

    final now = DateTime.now();
    // Get the start of this week (Monday)
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final weekStartDate = DateTime(
      weekStart.year,
      weekStart.month,
      weekStart.day,
    );
    final contribDate = DateTime(
      date.year,
      date.month,
      date.day,
    );
    // Check if contribution date is on or after the start of this week
    return contribDate.isAtSameMomentAs(weekStartDate) ||
        contribDate.isAfter(weekStartDate);
  }

  /// Check if contribution was made this month
  bool get isMadeThisMonth {
    final date = contributionDate;
    if (date == null) return false;

    final now = DateTime.now();
    return date.year == now.year && date.month == now.month;
  }
}

/// Goal contribution validation extension
extension GoalContributionValidation on GoalContribution {
  /// Validates the goal contribution data
  List<String> validate() {
    final errors = <String>[];

    // User ID validation
    if (userId.trim().isEmpty) {
      errors.add('User ID is required');
    }

    // Goal ID validation
    if (goalId.trim().isEmpty) {
      errors.add('Goal ID is required');
    }

    // Amount validation
    if (amountCents <= 0) {
      errors.add('Contribution amount must be greater than 0');
    } else if (amountCents > 9999999999) {
      // ~$100M limit
      errors.add('Contribution amount is too large');
    }

    // Description validation
    if (description != null && description!.length > 500) {
      errors.add('Contribution description must be 500 characters or less');
    }

    // Contribution date validation
    final date = contributionDate;
    if (date != null) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final contribDate = DateTime(
        date.year,
        date.month,
        date.day,
      );

      if (contribDate.isAfter(today)) {
        errors.add('Contribution date cannot be in the future');
      }
    }

    // Don't allow contributions older than 10 years
    if (date != null) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final contribDate = DateTime(date.year, date.month, date.day);
      final tenYearsAgo = today.subtract(const Duration(days: 3650));
      if (contribDate.isBefore(tenYearsAgo)) {
        errors.add('Contribution date cannot be more than 10 years ago');
      }
    }

    return errors;
  }

  /// Checks if the goal contribution is valid
  bool get isValid => validate().isEmpty;

  /// Gets a user-friendly date description
  String get dateDescription {
    if (isMadeToday) {
      return 'Today';
    } else if (isMadeThisWeek) {
      return 'This Week';
    } else if (isMadeThisMonth) {
      return 'This Month';
    } else {
      // Format as "MMM dd, yyyy"
      final months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ];
      final date = contributionDate ?? DateTime.now();
      return '${months[date.month - 1]} ${date.day}, ${date.year}';
    }
  }

  /// Gets a summary message for the contribution
  String get summaryMessage {
    final amount = (amountCents / 100).toStringAsFixed(2);
    final date = dateDescription;
    return 'Contributed \$$amount on $date';
  }
}
