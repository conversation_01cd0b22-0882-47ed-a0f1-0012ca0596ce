// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'remote_config_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PredefinedCategories _$PredefinedCategoriesFromJson(
  Map<String, dynamic> json,
) => _PredefinedCategories(
  incomeCategories:
      (json['incomeCategories'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  expenseCategories:
      (json['expenseCategories'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
);

Map<String, dynamic> _$PredefinedCategoriesToJson(
  _PredefinedCategories instance,
) => <String, dynamic>{
  'incomeCategories': instance.incomeCategories,
  'expenseCategories': instance.expenseCategories,
};

_PremiumLimits _$PremiumLimitsFromJson(Map<String, dynamic> json) =>
    _PremiumLimits(
      maxAccountsFree: (json['maxAccountsFree'] as num?)?.toInt() ?? 3,
      maxAccountsPremium: (json['maxAccountsPremium'] as num?)?.toInt() ?? 50,
      maxCustomCategoriesFree:
          (json['maxCustomCategoriesFree'] as num?)?.toInt() ?? 5,
      maxCustomCategoriesPremium:
          (json['maxCustomCategoriesPremium'] as num?)?.toInt() ?? 100,
      maxBudgetsFree: (json['maxBudgetsFree'] as num?)?.toInt() ?? 10,
      maxBudgetsPremium: (json['maxBudgetsPremium'] as num?)?.toInt() ?? 100,
      maxGoalsFree: (json['maxGoalsFree'] as num?)?.toInt() ?? 5,
      maxGoalsPremium: (json['maxGoalsPremium'] as num?)?.toInt() ?? 50,
    );

Map<String, dynamic> _$PremiumLimitsToJson(_PremiumLimits instance) =>
    <String, dynamic>{
      'maxAccountsFree': instance.maxAccountsFree,
      'maxAccountsPremium': instance.maxAccountsPremium,
      'maxCustomCategoriesFree': instance.maxCustomCategoriesFree,
      'maxCustomCategoriesPremium': instance.maxCustomCategoriesPremium,
      'maxBudgetsFree': instance.maxBudgetsFree,
      'maxBudgetsPremium': instance.maxBudgetsPremium,
      'maxGoalsFree': instance.maxGoalsFree,
      'maxGoalsPremium': instance.maxGoalsPremium,
    };
