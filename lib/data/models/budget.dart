import 'package:freezed_annotation/freezed_annotation.dart';

part 'budget.freezed.dart';
part 'budget.g.dart';

/// Budget types for financial budgets
enum BudgetType {
  expense, // Budget for limiting expenses
  income, // Budget for tracking income goals
}

/// Budget period types
enum BudgetPeriod {
  monthly, // Monthly budget period
  yearly, // Yearly budget period
}

/// Financial budget data model
@freezed
sealed class Budget with _$Budget {
  const factory Budget({
    required String id,
    required String userId,
    @Default(BudgetType.expense) BudgetType type,
    required int plannedAmountCents, // Target/budgeted amount in cents
    @Default(0) int currentAmountCents, // Actual spent/received amount in cents
    @Default(BudgetPeriod.monthly) BudgetPeriod period,
    required DateTime periodStart, // Explicit period start date
    // (e.g., 2024-01-01 for January 2024)
    String? categoryId, // Nullable for total budgets
    String? parentBudgetId, // Nullable for hierarchy
    @Default(true) bool isActive,
    @Default(1) int schemaVersion, // Schema version for migrations
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default({}) Map<String, dynamic> metadata, // Additional metadata
  }) = _Budget;

  factory Budget.fromJson(Map<String, dynamic> json) => _$BudgetFromJson(json);

  /// Creates a new budget with generated ID and timestamps
  factory Budget.create({
    required String userId,
    BudgetType type = BudgetType.expense,
    required int plannedAmountCents,
    int currentAmountCents = 0,
    BudgetPeriod period = BudgetPeriod.monthly,
    required DateTime periodStart,
    String? categoryId,
    String? parentBudgetId,
  }) {
    final now = DateTime.now();
    return Budget(
      id: '', // Will be set by repository
      userId: userId,
      type: type,
      plannedAmountCents: plannedAmountCents,
      currentAmountCents: currentAmountCents,
      period: period,
      periodStart: periodStart,
      categoryId: categoryId,
      parentBudgetId: parentBudgetId,
      isActive: true,
      schemaVersion: 1,
      createdAt: now,
      updatedAt: now,
    );
  }
}

extension BudgetValidation on Budget {
  /// Validates the budget data
  List<String> validate() {
    final errors = <String>[];

    if (plannedAmountCents <= 0) {
      errors.add('Budget planned amount must be greater than 0');
    }

    // Validate hierarchy: can't be parent of itself
    if (parentBudgetId == id) {
      errors.add('Budget cannot be its own parent');
    }

    return errors;
  }

  /// Checks if the budget is valid
  bool get isValid => validate().isEmpty;

  /// Gets the period as a string for display
  String get periodString {
    switch (period) {
      case BudgetPeriod.monthly:
        return 'Monthly';
      case BudgetPeriod.yearly:
        return 'Yearly';
    }
  }

  /// Gets the type as a string for display
  String get typeString {
    switch (type) {
      case BudgetType.expense:
        return 'Expense';
      case BudgetType.income:
        return 'Income';
    }
  }

  /// Checks if this is an overall budget (no specific category)
  bool get isOverallBudget => categoryId == null;

  /// Checks if this is a child budget (has a parent)
  bool get isChildBudget => parentBudgetId != null;

  /// Checks if this is a parent budget (could have children)
  bool get canHaveChildren => categoryId != null;

  /// Gets the period end date based on period type and start date
  DateTime get periodEnd {
    switch (period) {
      case BudgetPeriod.monthly:
        // Last day of the month
        return DateTime(periodStart.year, periodStart.month + 1, 0);
      case BudgetPeriod.yearly:
        // Last day of the year
        return DateTime(periodStart.year, 12, 31);
    }
  }

  /// Checks if this budget is for the specified period
  bool isForPeriod(DateTime targetPeriod) {
    switch (period) {
      case BudgetPeriod.monthly:
        return periodStart.year == targetPeriod.year &&
            periodStart.month == targetPeriod.month;
      case BudgetPeriod.yearly:
        return periodStart.year == targetPeriod.year;
    }
  }

  /// Gets a unique key for this budget's period (for uniqueness constraints)
  String get periodKey {
    switch (period) {
      case BudgetPeriod.monthly:
        return '${periodStart.year}-'
            '${periodStart.month.toString().padLeft(2, '0')}';
      case BudgetPeriod.yearly:
        return '${periodStart.year}';
    }
  }
}

/// Extension for generating display names for budgets
extension BudgetDisplayName on Budget {
  /// Gets the display name for the budget
  /// For category budgets: uses category name
  /// For total budgets: uses "Total Expenses" or "Total Income"
  String getDisplayName({String? categoryName}) {
    if (categoryId == null) {
      // Total budget
      return type == BudgetType.expense ? 'Total Expenses' : 'Total Income';
    } else {
      // Category budget
      return categoryName ?? 'Unknown Category';
    }
  }

  /// Gets the full display name with period information
  /// For category budgets: "{Category Name} - {Period}"
  /// For total budgets: "Total {Type} - {Period}"
  String getFullDisplayName({String? categoryName, String? periodDisplay}) {
    final baseName = getDisplayName(categoryName: categoryName);
    return periodDisplay != null ? '$baseName - $periodDisplay' : baseName;
  }

  /// Checks if this is a total budget (no specific category)
  bool get isTotalBudget => categoryId == null;

  /// Checks if this is a category budget (linked to specific category)
  bool get isCategoryBudget => categoryId != null;
}
