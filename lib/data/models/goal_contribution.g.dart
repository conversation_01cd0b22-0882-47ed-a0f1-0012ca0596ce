// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'goal_contribution.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_GoalContribution _$GoalContributionFromJson(Map<String, dynamic> json) =>
    _GoalContribution(
      id: json['id'] as String? ?? '',
      userId: json['userId'] as String? ?? '',
      goalId: json['goalId'] as String? ?? '',
      amountCents: (json['amountCents'] as num?)?.toInt() ?? 0,
      contributionDate: json['contributionDate'] == null
          ? null
          : DateTime.parse(json['contributionDate'] as String),
      description: json['description'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      schemaVersion: (json['schemaVersion'] as num?)?.toInt() ?? 1,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$GoalContributionToJson(_GoalContribution instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'goalId': instance.goalId,
      'amountCents': instance.amountCents,
      'contributionDate': instance.contributionDate?.toIso8601String(),
      'description': instance.description,
      'isActive': instance.isActive,
      'schemaVersion': instance.schemaVersion,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'metadata': instance.metadata,
    };
