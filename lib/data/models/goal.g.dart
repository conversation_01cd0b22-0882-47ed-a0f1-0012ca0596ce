// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'goal.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Goal _$GoalFromJson(Map<String, dynamic> json) => _Goal(
  id: json['id'] as String? ?? '',
  userId: json['userId'] as String? ?? '',
  name: json['name'] as String? ?? '',
  description: json['description'] as String?,
  targetAmountCents: (json['targetAmountCents'] as num?)?.toInt() ?? 0,
  currentAmountCents: (json['currentAmountCents'] as num?)?.toInt() ?? 0,
  targetDate: json['targetDate'] == null
      ? null
      : DateTime.parse(json['targetDate'] as String),
  isCompleted: json['isCompleted'] as bool? ?? false,
  colorHex: json['colorHex'] as String?,
  iconName: json['iconName'] as String?,
  status:
      $enumDecodeNullable(_$GoalStatusEnumMap, json['status']) ??
      GoalStatus.active,
  isActive: json['isActive'] as bool? ?? true,
  schemaVersion: (json['schemaVersion'] as num?)?.toInt() ?? 1,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  metadata: json['metadata'] == null
      ? const {}
      : const MetadataConverter().fromJson(json['metadata']),
);

Map<String, dynamic> _$GoalToJson(_Goal instance) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'name': instance.name,
  'description': instance.description,
  'targetAmountCents': instance.targetAmountCents,
  'currentAmountCents': instance.currentAmountCents,
  'targetDate': instance.targetDate?.toIso8601String(),
  'isCompleted': instance.isCompleted,
  'colorHex': instance.colorHex,
  'iconName': instance.iconName,
  'status': _$GoalStatusEnumMap[instance.status]!,
  'isActive': instance.isActive,
  'schemaVersion': instance.schemaVersion,
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
  'metadata': const MetadataConverter().toJson(instance.metadata),
};

const _$GoalStatusEnumMap = {
  GoalStatus.active: 'active',
  GoalStatus.paused: 'paused',
  GoalStatus.completed: 'completed',
  GoalStatus.cancelled: 'cancelled',
};
