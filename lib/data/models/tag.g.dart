// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tag.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Tag _$TagFromJson(Map<String, dynamic> json) => _Tag(
  id: json['id'] as String,
  userId: json['userId'] as String,
  name: json['name'] as String,
  color: json['color'] as String,
  usageCount: (json['usageCount'] as num?)?.toInt() ?? 0,
  schemaVersion: (json['schemaVersion'] as num?)?.toInt() ?? 1,
  createdAt: const TimestampConverter().fromJson(json['createdAt'] as Object),
  updatedAt: const TimestampConverter().fromJson(json['updatedAt'] as Object),
);

Map<String, dynamic> _$TagToJson(_Tag instance) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'name': instance.name,
  'color': instance.color,
  'usageCount': instance.usageCount,
  'schemaVersion': instance.schemaVersion,
  'createdAt': const TimestampConverter().toJson(instance.createdAt),
  'updatedAt': const TimestampConverter().toJson(instance.updatedAt),
};
