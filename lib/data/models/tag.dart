import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'tag.freezed.dart';
part 'tag.g.dart';

@freezed
sealed class Tag with _$Tag {
  const factory Tag({
    required String id,
    required String userId,
    required String name,
    required String color,
    @Default(0) int usageCount,
    @Default(1) int schemaVersion,
    @TimestampConverter() required Timestamp createdAt,
    @TimestampConverter() required Timestamp updatedAt,
  }) = _Tag;

  /// Creates Tag from Firestore document
  factory Tag.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>? ?? {};
    return Tag.fromJson({'id': doc.id, ...data});
  }

  factory Tag.fromJson(Map<String, dynamic> json) => _$TagFromJson(json);

  const Tag._();

  /// Creates a new tag with current timestamp
  factory Tag.create({
    required String userId,
    required String name,
    required String color,
  }) {
    final now = Timestamp.now();
    return Tag(
      id: '', // Will be set by repository
      userId: userId,
      name: name,
      color: color,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Creates a copy with updated timestamp
  Tag withUpdatedTimestamp() {
    return copyWith(updatedAt: Timestamp.now());
  }

  /// Validates tag data
  List<String> validate() {
    final errors = <String>[];

    if (userId.trim().isEmpty) {
      errors.add('Tag userId is required');
    }

    if (name.trim().isEmpty) {
      errors.add('Tag name is required');
    }

    if (name.trim().length > 50) {
      errors.add('Tag name must be 50 characters or less');
    }

    if (color.isEmpty) {
      errors.add('Tag color is required');
    }

    // Validate hex color format
    final hexColorRegex = RegExp(r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$');
    if (!hexColorRegex.hasMatch(color)) {
      errors.add('Tag color must be a valid hex color');
    }

    if (usageCount < 0) {
      errors.add('Usage count cannot be negative');
    }

    return errors;
  }

  /// Checks if tag is valid
  bool get isValid => validate().isEmpty;

  /// Converts to Firestore document data
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'name': name,
      'color': color,
      'usageCount': usageCount,
      'schemaVersion': schemaVersion,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }
}

/// Timestamp converter for Firestore
class TimestampConverter implements JsonConverter<Timestamp, Object> {
  const TimestampConverter();

  @override
  Timestamp fromJson(Object json) {
    if (json is Timestamp) {
      return json;
    }
    if (json is Map<String, dynamic>) {
      return Timestamp(json['_seconds'] as int, json['_nanoseconds'] as int);
    }
    throw ArgumentError('Cannot convert $json to Timestamp');
  }

  @override
  Object toJson(Timestamp timestamp) => timestamp;
}
