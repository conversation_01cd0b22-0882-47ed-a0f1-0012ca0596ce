// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'goal.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Goal {

 String get id;// Unique goal identifier
 String get userId;// Owner user ID
 String get name;// Goal name/title (2-100 chars)
 String? get description;// Optional description (max 500 chars)
 int get targetAmountCents;// Target amount in cents
 int get currentAmountCents;// Current progress amount in cents (denormalized)
 DateTime? get targetDate;// Optional target completion date
 bool get isCompleted;// Completion status (for backward compatibility)
 String? get colorHex;// Hex color code (#RRGGBB format)
 String? get iconName;// Icon identifier/name
 GoalStatus get status;// Current goal status
 bool get isActive;// Soft delete flag
 int get schemaVersion;// Schema version for migrations
 DateTime? get createdAt;// Creation timestamp
 DateTime? get updatedAt;// Last update timestamp
@MetadataConverter() Map<String, dynamic> get metadata;
/// Create a copy of Goal
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GoalCopyWith<Goal> get copyWith => _$GoalCopyWithImpl<Goal>(this as Goal, _$identity);

  /// Serializes this Goal to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Goal&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.targetAmountCents, targetAmountCents) || other.targetAmountCents == targetAmountCents)&&(identical(other.currentAmountCents, currentAmountCents) || other.currentAmountCents == currentAmountCents)&&(identical(other.targetDate, targetDate) || other.targetDate == targetDate)&&(identical(other.isCompleted, isCompleted) || other.isCompleted == isCompleted)&&(identical(other.colorHex, colorHex) || other.colorHex == colorHex)&&(identical(other.iconName, iconName) || other.iconName == iconName)&&(identical(other.status, status) || other.status == status)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.schemaVersion, schemaVersion) || other.schemaVersion == schemaVersion)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,name,description,targetAmountCents,currentAmountCents,targetDate,isCompleted,colorHex,iconName,status,isActive,schemaVersion,createdAt,updatedAt,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'Goal(id: $id, userId: $userId, name: $name, description: $description, targetAmountCents: $targetAmountCents, currentAmountCents: $currentAmountCents, targetDate: $targetDate, isCompleted: $isCompleted, colorHex: $colorHex, iconName: $iconName, status: $status, isActive: $isActive, schemaVersion: $schemaVersion, createdAt: $createdAt, updatedAt: $updatedAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $GoalCopyWith<$Res>  {
  factory $GoalCopyWith(Goal value, $Res Function(Goal) _then) = _$GoalCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String name, String? description, int targetAmountCents, int currentAmountCents, DateTime? targetDate, bool isCompleted, String? colorHex, String? iconName, GoalStatus status, bool isActive, int schemaVersion, DateTime? createdAt, DateTime? updatedAt,@MetadataConverter() Map<String, dynamic> metadata
});




}
/// @nodoc
class _$GoalCopyWithImpl<$Res>
    implements $GoalCopyWith<$Res> {
  _$GoalCopyWithImpl(this._self, this._then);

  final Goal _self;
  final $Res Function(Goal) _then;

/// Create a copy of Goal
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? name = null,Object? description = freezed,Object? targetAmountCents = null,Object? currentAmountCents = null,Object? targetDate = freezed,Object? isCompleted = null,Object? colorHex = freezed,Object? iconName = freezed,Object? status = null,Object? isActive = null,Object? schemaVersion = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? metadata = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,targetAmountCents: null == targetAmountCents ? _self.targetAmountCents : targetAmountCents // ignore: cast_nullable_to_non_nullable
as int,currentAmountCents: null == currentAmountCents ? _self.currentAmountCents : currentAmountCents // ignore: cast_nullable_to_non_nullable
as int,targetDate: freezed == targetDate ? _self.targetDate : targetDate // ignore: cast_nullable_to_non_nullable
as DateTime?,isCompleted: null == isCompleted ? _self.isCompleted : isCompleted // ignore: cast_nullable_to_non_nullable
as bool,colorHex: freezed == colorHex ? _self.colorHex : colorHex // ignore: cast_nullable_to_non_nullable
as String?,iconName: freezed == iconName ? _self.iconName : iconName // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as GoalStatus,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,schemaVersion: null == schemaVersion ? _self.schemaVersion : schemaVersion // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

}


/// Adds pattern-matching-related methods to [Goal].
extension GoalPatterns on Goal {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Goal value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Goal() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Goal value)  $default,){
final _that = this;
switch (_that) {
case _Goal():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Goal value)?  $default,){
final _that = this;
switch (_that) {
case _Goal() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String name,  String? description,  int targetAmountCents,  int currentAmountCents,  DateTime? targetDate,  bool isCompleted,  String? colorHex,  String? iconName,  GoalStatus status,  bool isActive,  int schemaVersion,  DateTime? createdAt,  DateTime? updatedAt, @MetadataConverter()  Map<String, dynamic> metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Goal() when $default != null:
return $default(_that.id,_that.userId,_that.name,_that.description,_that.targetAmountCents,_that.currentAmountCents,_that.targetDate,_that.isCompleted,_that.colorHex,_that.iconName,_that.status,_that.isActive,_that.schemaVersion,_that.createdAt,_that.updatedAt,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String name,  String? description,  int targetAmountCents,  int currentAmountCents,  DateTime? targetDate,  bool isCompleted,  String? colorHex,  String? iconName,  GoalStatus status,  bool isActive,  int schemaVersion,  DateTime? createdAt,  DateTime? updatedAt, @MetadataConverter()  Map<String, dynamic> metadata)  $default,) {final _that = this;
switch (_that) {
case _Goal():
return $default(_that.id,_that.userId,_that.name,_that.description,_that.targetAmountCents,_that.currentAmountCents,_that.targetDate,_that.isCompleted,_that.colorHex,_that.iconName,_that.status,_that.isActive,_that.schemaVersion,_that.createdAt,_that.updatedAt,_that.metadata);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String name,  String? description,  int targetAmountCents,  int currentAmountCents,  DateTime? targetDate,  bool isCompleted,  String? colorHex,  String? iconName,  GoalStatus status,  bool isActive,  int schemaVersion,  DateTime? createdAt,  DateTime? updatedAt, @MetadataConverter()  Map<String, dynamic> metadata)?  $default,) {final _that = this;
switch (_that) {
case _Goal() when $default != null:
return $default(_that.id,_that.userId,_that.name,_that.description,_that.targetAmountCents,_that.currentAmountCents,_that.targetDate,_that.isCompleted,_that.colorHex,_that.iconName,_that.status,_that.isActive,_that.schemaVersion,_that.createdAt,_that.updatedAt,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Goal extends Goal {
  const _Goal({this.id = '', this.userId = '', this.name = '', this.description, this.targetAmountCents = 0, this.currentAmountCents = 0, this.targetDate, this.isCompleted = false, this.colorHex, this.iconName, this.status = GoalStatus.active, this.isActive = true, this.schemaVersion = 1, this.createdAt, this.updatedAt, @MetadataConverter() final  Map<String, dynamic> metadata = const {}}): _metadata = metadata,super._();
  factory _Goal.fromJson(Map<String, dynamic> json) => _$GoalFromJson(json);

@override@JsonKey() final  String id;
// Unique goal identifier
@override@JsonKey() final  String userId;
// Owner user ID
@override@JsonKey() final  String name;
// Goal name/title (2-100 chars)
@override final  String? description;
// Optional description (max 500 chars)
@override@JsonKey() final  int targetAmountCents;
// Target amount in cents
@override@JsonKey() final  int currentAmountCents;
// Current progress amount in cents (denormalized)
@override final  DateTime? targetDate;
// Optional target completion date
@override@JsonKey() final  bool isCompleted;
// Completion status (for backward compatibility)
@override final  String? colorHex;
// Hex color code (#RRGGBB format)
@override final  String? iconName;
// Icon identifier/name
@override@JsonKey() final  GoalStatus status;
// Current goal status
@override@JsonKey() final  bool isActive;
// Soft delete flag
@override@JsonKey() final  int schemaVersion;
// Schema version for migrations
@override final  DateTime? createdAt;
// Creation timestamp
@override final  DateTime? updatedAt;
// Last update timestamp
 final  Map<String, dynamic> _metadata;
// Last update timestamp
@override@JsonKey()@MetadataConverter() Map<String, dynamic> get metadata {
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_metadata);
}


/// Create a copy of Goal
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GoalCopyWith<_Goal> get copyWith => __$GoalCopyWithImpl<_Goal>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GoalToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Goal&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.targetAmountCents, targetAmountCents) || other.targetAmountCents == targetAmountCents)&&(identical(other.currentAmountCents, currentAmountCents) || other.currentAmountCents == currentAmountCents)&&(identical(other.targetDate, targetDate) || other.targetDate == targetDate)&&(identical(other.isCompleted, isCompleted) || other.isCompleted == isCompleted)&&(identical(other.colorHex, colorHex) || other.colorHex == colorHex)&&(identical(other.iconName, iconName) || other.iconName == iconName)&&(identical(other.status, status) || other.status == status)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.schemaVersion, schemaVersion) || other.schemaVersion == schemaVersion)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,name,description,targetAmountCents,currentAmountCents,targetDate,isCompleted,colorHex,iconName,status,isActive,schemaVersion,createdAt,updatedAt,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'Goal(id: $id, userId: $userId, name: $name, description: $description, targetAmountCents: $targetAmountCents, currentAmountCents: $currentAmountCents, targetDate: $targetDate, isCompleted: $isCompleted, colorHex: $colorHex, iconName: $iconName, status: $status, isActive: $isActive, schemaVersion: $schemaVersion, createdAt: $createdAt, updatedAt: $updatedAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$GoalCopyWith<$Res> implements $GoalCopyWith<$Res> {
  factory _$GoalCopyWith(_Goal value, $Res Function(_Goal) _then) = __$GoalCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String name, String? description, int targetAmountCents, int currentAmountCents, DateTime? targetDate, bool isCompleted, String? colorHex, String? iconName, GoalStatus status, bool isActive, int schemaVersion, DateTime? createdAt, DateTime? updatedAt,@MetadataConverter() Map<String, dynamic> metadata
});




}
/// @nodoc
class __$GoalCopyWithImpl<$Res>
    implements _$GoalCopyWith<$Res> {
  __$GoalCopyWithImpl(this._self, this._then);

  final _Goal _self;
  final $Res Function(_Goal) _then;

/// Create a copy of Goal
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? name = null,Object? description = freezed,Object? targetAmountCents = null,Object? currentAmountCents = null,Object? targetDate = freezed,Object? isCompleted = null,Object? colorHex = freezed,Object? iconName = freezed,Object? status = null,Object? isActive = null,Object? schemaVersion = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? metadata = null,}) {
  return _then(_Goal(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,targetAmountCents: null == targetAmountCents ? _self.targetAmountCents : targetAmountCents // ignore: cast_nullable_to_non_nullable
as int,currentAmountCents: null == currentAmountCents ? _self.currentAmountCents : currentAmountCents // ignore: cast_nullable_to_non_nullable
as int,targetDate: freezed == targetDate ? _self.targetDate : targetDate // ignore: cast_nullable_to_non_nullable
as DateTime?,isCompleted: null == isCompleted ? _self.isCompleted : isCompleted // ignore: cast_nullable_to_non_nullable
as bool,colorHex: freezed == colorHex ? _self.colorHex : colorHex // ignore: cast_nullable_to_non_nullable
as String?,iconName: freezed == iconName ? _self.iconName : iconName // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as GoalStatus,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,schemaVersion: null == schemaVersion ? _self.schemaVersion : schemaVersion // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,metadata: null == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}

// dart format on
