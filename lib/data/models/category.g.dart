// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Category _$CategoryFromJson(Map<String, dynamic> json) => _Category(
  id: json['id'] as String? ?? '',
  userId: json['userId'] as String? ?? '',
  name: json['name'] as String? ?? '',
  type:
      $enumDecodeNullable(_$CategoryTypeEnumMap, json['type']) ??
      CategoryType.expense,
  parentId: json['parentId'] as String?,
  description: json['description'] as String?,
  color: json['color'] as String?,
  icon: json['icon'] as String?,
  isActive: json['isActive'] as bool? ?? true,
  sortOrder: (json['sortOrder'] as num?)?.toInt() ?? 0,
  schemaVersion: (json['schemaVersion'] as num?)?.toInt() ?? 1,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  metadata: json['metadata'] == null
      ? const {}
      : const MetadataConverter().fromJson(json['metadata']),
);

Map<String, dynamic> _$CategoryToJson(_Category instance) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'name': instance.name,
  'type': _$CategoryTypeEnumMap[instance.type]!,
  'parentId': instance.parentId,
  'description': instance.description,
  'color': instance.color,
  'icon': instance.icon,
  'isActive': instance.isActive,
  'sortOrder': instance.sortOrder,
  'schemaVersion': instance.schemaVersion,
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
  'metadata': const MetadataConverter().toJson(instance.metadata),
};

const _$CategoryTypeEnumMap = {
  CategoryType.income: 'income',
  CategoryType.expense: 'expense',
};
