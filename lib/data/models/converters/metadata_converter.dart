import 'package:freezed_annotation/freezed_annotation.dart';

/// Metadata converter for safe JSON deserialization
/// Handles invalid metadata types by converting them to empty maps
class MetadataConverter extends JsonConverter<Map<String, dynamic>, dynamic> {
  const MetadataConverter();

  @override
  Map<String, dynamic> from<PERSON>son(dynamic json) {
    if (json is Map<String, dynamic>) {
      return json;
    }
    // Return empty map for any invalid type (null, string, etc.)
    return <String, dynamic>{};
  }

  @override
  dynamic toJson(Map<String, dynamic> object) => object;
}
