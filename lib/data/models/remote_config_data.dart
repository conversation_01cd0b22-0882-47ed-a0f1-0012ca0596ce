import 'package:freezed_annotation/freezed_annotation.dart';

part 'remote_config_data.freezed.dart';
part 'remote_config_data.g.dart';

/// Data model for predefined categories from Remote Config
@freezed
sealed class PredefinedCategories with _$PredefinedCategories {
  const factory PredefinedCategories({
    @Default([]) List<String> incomeCategories,
    @Default([]) List<String> expenseCategories,
  }) = _PredefinedCategories;

  factory PredefinedCategories.defaults() {
    return const PredefinedCategories(
      incomeCategories: _defaultIncomeCategories,
      expenseCategories: _defaultExpenseCategories,
    );
  }

  /// Create from JSON
  factory PredefinedCategories.fromJson(Map<String, dynamic> json) =>
      _$PredefinedCategoriesFromJson(json);

  /// Convert to JSON
  @override
  Map<String, dynamic> toJson() =>
      _$PredefinedCategoriesToJson(this as _PredefinedCategories);

  /// Default categories for fallback
  static const _defaultIncomeCategories = [
    'Salary',
    'Freelance',
    'Investment',
    'Business',
    'Other Income',
  ];

  static const _defaultExpenseCategories = [
    'Food & Dining',
    'Transportation',
    'Shopping',
    'Entertainment',
    'Bills & Utilities',
    'Healthcare',
    'Education',
    'Travel',
    'Other Expenses',
  ];
}

/// Data model for premium limits from Remote Config
@freezed
sealed class PremiumLimits with _$PremiumLimits {
  const factory PremiumLimits({
    @Default(3) int maxAccountsFree,
    @Default(50) int maxAccountsPremium,
    @Default(5) int maxCustomCategoriesFree,
    @Default(100) int maxCustomCategoriesPremium,
    @Default(10) int maxBudgetsFree,
    @Default(100) int maxBudgetsPremium,
    @Default(5) int maxGoalsFree,
    @Default(50) int maxGoalsPremium,
  }) = _PremiumLimits;

  /// Default limits for fallback
  factory PremiumLimits.defaults() {
    return const PremiumLimits();
  }

  /// Create from JSON
  factory PremiumLimits.fromJson(Map<String, dynamic> json) =>
      _$PremiumLimitsFromJson(json);

  /// Convert to JSON
  @override
  Map<String, dynamic> toJson() =>
      _$PremiumLimitsToJson(this as _PremiumLimits);
}

/// Combined Remote Config data model
@freezed
sealed class RemoteConfigData with _$RemoteConfigData {
  const factory RemoteConfigData({
    @Default(PredefinedCategories()) PredefinedCategories categories,
    @Default(PremiumLimits()) PremiumLimits premiumLimits,
    @Default(false) bool enableFeatureX,
    @Default(false) bool enableBetaFeatures,
    @Default(1) int schemaVersion, // Schema version for migrations
  }) = _RemoteConfigData;

  /// Default configuration for fallback
  factory RemoteConfigData.defaults() {
    return RemoteConfigData(
      categories: PredefinedCategories.defaults(),
      premiumLimits: PremiumLimits.defaults(),
    );
  }

  const RemoteConfigData._();

  /// Create from JSON
  factory RemoteConfigData.fromJson(Map<String, dynamic> json) {
    return RemoteConfigData(
      categories: json['categories'] != null
          ? PredefinedCategories.fromJson(
              json['categories'] as Map<String, dynamic>,
            )
          : const PredefinedCategories(),
      premiumLimits: json['premiumLimits'] != null
          ? PremiumLimits.fromJson(
              json['premiumLimits'] as Map<String, dynamic>,
            )
          : const PremiumLimits(),
      enableFeatureX: json['enableFeatureX'] as bool? ?? false,
      enableBetaFeatures: json['enableBetaFeatures'] as bool? ?? false,
      schemaVersion: json['schemaVersion'] as int? ?? 1,
    );
  }

  /// Convert to JSON for Firestore
  Map<String, dynamic> toJson() => {
    'categories': categories.toJson(),
    'premiumLimits': premiumLimits.toJson(),
    'enableFeatureX': enableFeatureX,
    'enableBetaFeatures': enableBetaFeatures,
    'schemaVersion': schemaVersion,
  };
}
