import 'package:budapp/data/models/converters/metadata_converter.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'category.freezed.dart';
part 'category.g.dart';

/// Category types for income vs expense categorization
enum CategoryType {
  @JsonValue('income')
  income, // Income categories
  @JsonValue('expense')
  expense, // Expense categories
}

/// Category data model with hierarchical support
@freezed
sealed class Category with _$Category {
  const factory Category({
    @Default('') String id, // Unique category identifier
    @Default('') String userId, // Owner user ID
    @Default('') String name, // Category display name (2-50 chars)
    @Default(CategoryType.expense) CategoryType type, // Income or expense
    String? parentId, // Parent category ID (null for root categories)
    String? description, // Optional description (max 200 chars)
    String? color, // Hex color code (#RRGGBB format)
    String? icon, // Icon identifier/name
    @Default(true) bool isActive, // Soft delete flag
    @Default(0) int sortOrder, // Sort order within same parent level
    @Default(1) int schemaVersion, // Schema version for migrations
    DateTime? createdAt, // Creation timestamp
    DateTime? updatedAt, // Last update timestamp
    @MetadataConverter()
    @Default({})
    Map<String, dynamic> metadata, // Additional metadata
  }) = _Category;

  /// Create from Firestore document snapshot
  factory Category.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data();
    if (data == null) {
      throw Exception('Document data is null for category ${doc.id}');
    }

    // Handle Firestore Timestamp conversion
    final convertedData = Map<String, dynamic>.from(data);

    // Convert Firestore Timestamps to DateTime
    if (convertedData['createdAt'] is Timestamp) {
      convertedData['createdAt'] = (convertedData['createdAt'] as Timestamp)
          .toDate()
          .toIso8601String();
    }
    if (convertedData['updatedAt'] is Timestamp) {
      convertedData['updatedAt'] = (convertedData['updatedAt'] as Timestamp)
          .toDate()
          .toIso8601String();
    }

    // Ensure document ID matches the id field
    convertedData['id'] = doc.id;

    return Category.fromJson(convertedData);
  }

  /// Create a new category
  factory Category.create({
    required String userId,
    required String name,
    required CategoryType type,
    String? parentId,
    String? description,
    String? color,
    String? icon,
    int sortOrder = 0,
  }) {
    final now = DateTime.now();
    return Category(
      id: '', // Will be set by repository
      userId: userId,
      name: name,
      type: type,
      parentId: parentId,
      description: description,
      color: color,
      icon: icon,
      sortOrder: sortOrder,
      schemaVersion: 1,
      createdAt: now,
      updatedAt: now,
    );
  }

  const Category._();

  factory Category.fromJson(Map<String, dynamic> json) =>
      _$CategoryFromJson(json);

  /// Convert to Firestore document data
  Map<String, dynamic> toFirestore() {
    final data = toJson();

    // Convert DateTime to Firestore Timestamp for server storage
    if (data['createdAt'] is String) {
      data['createdAt'] = Timestamp.fromDate(
        DateTime.parse(data['createdAt'] as String),
      );
    }
    if (data['updatedAt'] is String) {
      data['updatedAt'] = Timestamp.fromDate(
        DateTime.parse(data['updatedAt'] as String),
      );
    }

    return data;
  }

  /// Check if this is a root category (no parent)
  bool get isRoot => parentId == null;

  /// Check if this is a subcategory (has parent)
  bool get isSubcategory => parentId != null;

  /// Generate a new category with updated timestamp
  Category updated() => copyWith(updatedAt: DateTime.now());
}

/// Hierarchical category tree structure for UI rendering
/// Note: No JSON serialization due to circular references
@freezed
sealed class CategoryTree with _$CategoryTree {
  const factory CategoryTree({
    required Category category,
    @Default([]) List<CategoryTree> children,
    @Default(0) int depth,
  }) = _CategoryTree;

  const CategoryTree._();

  /// Check if this node has children
  bool get hasChildren => children.isNotEmpty;

  /// Get all descendant categories (recursive)
  List<Category> getAllDescendants() {
    final descendants = <Category>[];
    for (final child in children) {
      descendants
        ..add(child.category)
        ..addAll(child.getAllDescendants());
    }
    return descendants;
  }

  /// Get total count of all categories in tree
  int get totalCount =>
      1 + children.fold(0, (total, child) => total + child.totalCount);
}
