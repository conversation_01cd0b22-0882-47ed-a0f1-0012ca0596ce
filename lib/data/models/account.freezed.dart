// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Account {

 String get id; String get userId; String get name; AccountType get type; AccountClassification get classification; String? get description; String? get iconName; String? get colorHex; int get initialBalanceCents;// Stored as cents to avoid floating point issues
 int get currentBalanceCents;// Current balance updated with each transaction
 bool get isPrimary; bool get isActive; int get schemaVersion;// Schema version for migrations
 DateTime? get createdAt; DateTime? get updatedAt; Map<String, dynamic> get metadata;
/// Create a copy of Account
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountCopyWith<Account> get copyWith => _$AccountCopyWithImpl<Account>(this as Account, _$identity);

  /// Serializes this Account to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Account&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.name, name) || other.name == name)&&(identical(other.type, type) || other.type == type)&&(identical(other.classification, classification) || other.classification == classification)&&(identical(other.description, description) || other.description == description)&&(identical(other.iconName, iconName) || other.iconName == iconName)&&(identical(other.colorHex, colorHex) || other.colorHex == colorHex)&&(identical(other.initialBalanceCents, initialBalanceCents) || other.initialBalanceCents == initialBalanceCents)&&(identical(other.currentBalanceCents, currentBalanceCents) || other.currentBalanceCents == currentBalanceCents)&&(identical(other.isPrimary, isPrimary) || other.isPrimary == isPrimary)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.schemaVersion, schemaVersion) || other.schemaVersion == schemaVersion)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,name,type,classification,description,iconName,colorHex,initialBalanceCents,currentBalanceCents,isPrimary,isActive,schemaVersion,createdAt,updatedAt,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'Account(id: $id, userId: $userId, name: $name, type: $type, classification: $classification, description: $description, iconName: $iconName, colorHex: $colorHex, initialBalanceCents: $initialBalanceCents, currentBalanceCents: $currentBalanceCents, isPrimary: $isPrimary, isActive: $isActive, schemaVersion: $schemaVersion, createdAt: $createdAt, updatedAt: $updatedAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $AccountCopyWith<$Res>  {
  factory $AccountCopyWith(Account value, $Res Function(Account) _then) = _$AccountCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String name, AccountType type, AccountClassification classification, String? description, String? iconName, String? colorHex, int initialBalanceCents, int currentBalanceCents, bool isPrimary, bool isActive, int schemaVersion, DateTime? createdAt, DateTime? updatedAt, Map<String, dynamic> metadata
});




}
/// @nodoc
class _$AccountCopyWithImpl<$Res>
    implements $AccountCopyWith<$Res> {
  _$AccountCopyWithImpl(this._self, this._then);

  final Account _self;
  final $Res Function(Account) _then;

/// Create a copy of Account
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? name = null,Object? type = null,Object? classification = null,Object? description = freezed,Object? iconName = freezed,Object? colorHex = freezed,Object? initialBalanceCents = null,Object? currentBalanceCents = null,Object? isPrimary = null,Object? isActive = null,Object? schemaVersion = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? metadata = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as AccountType,classification: null == classification ? _self.classification : classification // ignore: cast_nullable_to_non_nullable
as AccountClassification,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,iconName: freezed == iconName ? _self.iconName : iconName // ignore: cast_nullable_to_non_nullable
as String?,colorHex: freezed == colorHex ? _self.colorHex : colorHex // ignore: cast_nullable_to_non_nullable
as String?,initialBalanceCents: null == initialBalanceCents ? _self.initialBalanceCents : initialBalanceCents // ignore: cast_nullable_to_non_nullable
as int,currentBalanceCents: null == currentBalanceCents ? _self.currentBalanceCents : currentBalanceCents // ignore: cast_nullable_to_non_nullable
as int,isPrimary: null == isPrimary ? _self.isPrimary : isPrimary // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,schemaVersion: null == schemaVersion ? _self.schemaVersion : schemaVersion // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

}


/// Adds pattern-matching-related methods to [Account].
extension AccountPatterns on Account {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Account value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Account() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Account value)  $default,){
final _that = this;
switch (_that) {
case _Account():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Account value)?  $default,){
final _that = this;
switch (_that) {
case _Account() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String name,  AccountType type,  AccountClassification classification,  String? description,  String? iconName,  String? colorHex,  int initialBalanceCents,  int currentBalanceCents,  bool isPrimary,  bool isActive,  int schemaVersion,  DateTime? createdAt,  DateTime? updatedAt,  Map<String, dynamic> metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Account() when $default != null:
return $default(_that.id,_that.userId,_that.name,_that.type,_that.classification,_that.description,_that.iconName,_that.colorHex,_that.initialBalanceCents,_that.currentBalanceCents,_that.isPrimary,_that.isActive,_that.schemaVersion,_that.createdAt,_that.updatedAt,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String name,  AccountType type,  AccountClassification classification,  String? description,  String? iconName,  String? colorHex,  int initialBalanceCents,  int currentBalanceCents,  bool isPrimary,  bool isActive,  int schemaVersion,  DateTime? createdAt,  DateTime? updatedAt,  Map<String, dynamic> metadata)  $default,) {final _that = this;
switch (_that) {
case _Account():
return $default(_that.id,_that.userId,_that.name,_that.type,_that.classification,_that.description,_that.iconName,_that.colorHex,_that.initialBalanceCents,_that.currentBalanceCents,_that.isPrimary,_that.isActive,_that.schemaVersion,_that.createdAt,_that.updatedAt,_that.metadata);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String name,  AccountType type,  AccountClassification classification,  String? description,  String? iconName,  String? colorHex,  int initialBalanceCents,  int currentBalanceCents,  bool isPrimary,  bool isActive,  int schemaVersion,  DateTime? createdAt,  DateTime? updatedAt,  Map<String, dynamic> metadata)?  $default,) {final _that = this;
switch (_that) {
case _Account() when $default != null:
return $default(_that.id,_that.userId,_that.name,_that.type,_that.classification,_that.description,_that.iconName,_that.colorHex,_that.initialBalanceCents,_that.currentBalanceCents,_that.isPrimary,_that.isActive,_that.schemaVersion,_that.createdAt,_that.updatedAt,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Account extends Account {
  const _Account({this.id = '', this.userId = '', this.name = '', this.type = AccountType.checking, this.classification = AccountClassification.asset, this.description, this.iconName, this.colorHex, this.initialBalanceCents = 0, this.currentBalanceCents = 0, this.isPrimary = false, this.isActive = true, this.schemaVersion = 1, this.createdAt, this.updatedAt, final  Map<String, dynamic> metadata = const {}}): _metadata = metadata,super._();
  factory _Account.fromJson(Map<String, dynamic> json) => _$AccountFromJson(json);

@override@JsonKey() final  String id;
@override@JsonKey() final  String userId;
@override@JsonKey() final  String name;
@override@JsonKey() final  AccountType type;
@override@JsonKey() final  AccountClassification classification;
@override final  String? description;
@override final  String? iconName;
@override final  String? colorHex;
@override@JsonKey() final  int initialBalanceCents;
// Stored as cents to avoid floating point issues
@override@JsonKey() final  int currentBalanceCents;
// Current balance updated with each transaction
@override@JsonKey() final  bool isPrimary;
@override@JsonKey() final  bool isActive;
@override@JsonKey() final  int schemaVersion;
// Schema version for migrations
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
 final  Map<String, dynamic> _metadata;
@override@JsonKey() Map<String, dynamic> get metadata {
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_metadata);
}


/// Create a copy of Account
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountCopyWith<_Account> get copyWith => __$AccountCopyWithImpl<_Account>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AccountToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Account&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.name, name) || other.name == name)&&(identical(other.type, type) || other.type == type)&&(identical(other.classification, classification) || other.classification == classification)&&(identical(other.description, description) || other.description == description)&&(identical(other.iconName, iconName) || other.iconName == iconName)&&(identical(other.colorHex, colorHex) || other.colorHex == colorHex)&&(identical(other.initialBalanceCents, initialBalanceCents) || other.initialBalanceCents == initialBalanceCents)&&(identical(other.currentBalanceCents, currentBalanceCents) || other.currentBalanceCents == currentBalanceCents)&&(identical(other.isPrimary, isPrimary) || other.isPrimary == isPrimary)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.schemaVersion, schemaVersion) || other.schemaVersion == schemaVersion)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,name,type,classification,description,iconName,colorHex,initialBalanceCents,currentBalanceCents,isPrimary,isActive,schemaVersion,createdAt,updatedAt,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'Account(id: $id, userId: $userId, name: $name, type: $type, classification: $classification, description: $description, iconName: $iconName, colorHex: $colorHex, initialBalanceCents: $initialBalanceCents, currentBalanceCents: $currentBalanceCents, isPrimary: $isPrimary, isActive: $isActive, schemaVersion: $schemaVersion, createdAt: $createdAt, updatedAt: $updatedAt, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$AccountCopyWith<$Res> implements $AccountCopyWith<$Res> {
  factory _$AccountCopyWith(_Account value, $Res Function(_Account) _then) = __$AccountCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String name, AccountType type, AccountClassification classification, String? description, String? iconName, String? colorHex, int initialBalanceCents, int currentBalanceCents, bool isPrimary, bool isActive, int schemaVersion, DateTime? createdAt, DateTime? updatedAt, Map<String, dynamic> metadata
});




}
/// @nodoc
class __$AccountCopyWithImpl<$Res>
    implements _$AccountCopyWith<$Res> {
  __$AccountCopyWithImpl(this._self, this._then);

  final _Account _self;
  final $Res Function(_Account) _then;

/// Create a copy of Account
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? name = null,Object? type = null,Object? classification = null,Object? description = freezed,Object? iconName = freezed,Object? colorHex = freezed,Object? initialBalanceCents = null,Object? currentBalanceCents = null,Object? isPrimary = null,Object? isActive = null,Object? schemaVersion = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? metadata = null,}) {
  return _then(_Account(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as AccountType,classification: null == classification ? _self.classification : classification // ignore: cast_nullable_to_non_nullable
as AccountClassification,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,iconName: freezed == iconName ? _self.iconName : iconName // ignore: cast_nullable_to_non_nullable
as String?,colorHex: freezed == colorHex ? _self.colorHex : colorHex // ignore: cast_nullable_to_non_nullable
as String?,initialBalanceCents: null == initialBalanceCents ? _self.initialBalanceCents : initialBalanceCents // ignore: cast_nullable_to_non_nullable
as int,currentBalanceCents: null == currentBalanceCents ? _self.currentBalanceCents : currentBalanceCents // ignore: cast_nullable_to_non_nullable
as int,isPrimary: null == isPrimary ? _self.isPrimary : isPrimary // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,schemaVersion: null == schemaVersion ? _self.schemaVersion : schemaVersion // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,metadata: null == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}

// dart format on
