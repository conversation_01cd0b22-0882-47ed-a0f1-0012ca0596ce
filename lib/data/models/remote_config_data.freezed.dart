// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'remote_config_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PredefinedCategories {

 List<String> get incomeCategories; List<String> get expenseCategories;
/// Create a copy of PredefinedCategories
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PredefinedCategoriesCopyWith<PredefinedCategories> get copyWith => _$PredefinedCategoriesCopyWithImpl<PredefinedCategories>(this as PredefinedCategories, _$identity);

  /// Serializes this PredefinedCategories to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PredefinedCategories&&const DeepCollectionEquality().equals(other.incomeCategories, incomeCategories)&&const DeepCollectionEquality().equals(other.expenseCategories, expenseCategories));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(incomeCategories),const DeepCollectionEquality().hash(expenseCategories));

@override
String toString() {
  return 'PredefinedCategories(incomeCategories: $incomeCategories, expenseCategories: $expenseCategories)';
}


}

/// @nodoc
abstract mixin class $PredefinedCategoriesCopyWith<$Res>  {
  factory $PredefinedCategoriesCopyWith(PredefinedCategories value, $Res Function(PredefinedCategories) _then) = _$PredefinedCategoriesCopyWithImpl;
@useResult
$Res call({
 List<String> incomeCategories, List<String> expenseCategories
});




}
/// @nodoc
class _$PredefinedCategoriesCopyWithImpl<$Res>
    implements $PredefinedCategoriesCopyWith<$Res> {
  _$PredefinedCategoriesCopyWithImpl(this._self, this._then);

  final PredefinedCategories _self;
  final $Res Function(PredefinedCategories) _then;

/// Create a copy of PredefinedCategories
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? incomeCategories = null,Object? expenseCategories = null,}) {
  return _then(_self.copyWith(
incomeCategories: null == incomeCategories ? _self.incomeCategories : incomeCategories // ignore: cast_nullable_to_non_nullable
as List<String>,expenseCategories: null == expenseCategories ? _self.expenseCategories : expenseCategories // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

}


/// Adds pattern-matching-related methods to [PredefinedCategories].
extension PredefinedCategoriesPatterns on PredefinedCategories {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PredefinedCategories value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PredefinedCategories() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PredefinedCategories value)  $default,){
final _that = this;
switch (_that) {
case _PredefinedCategories():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PredefinedCategories value)?  $default,){
final _that = this;
switch (_that) {
case _PredefinedCategories() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<String> incomeCategories,  List<String> expenseCategories)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PredefinedCategories() when $default != null:
return $default(_that.incomeCategories,_that.expenseCategories);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<String> incomeCategories,  List<String> expenseCategories)  $default,) {final _that = this;
switch (_that) {
case _PredefinedCategories():
return $default(_that.incomeCategories,_that.expenseCategories);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<String> incomeCategories,  List<String> expenseCategories)?  $default,) {final _that = this;
switch (_that) {
case _PredefinedCategories() when $default != null:
return $default(_that.incomeCategories,_that.expenseCategories);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PredefinedCategories implements PredefinedCategories {
  const _PredefinedCategories({final  List<String> incomeCategories = const [], final  List<String> expenseCategories = const []}): _incomeCategories = incomeCategories,_expenseCategories = expenseCategories;
  factory _PredefinedCategories.fromJson(Map<String, dynamic> json) => _$PredefinedCategoriesFromJson(json);

 final  List<String> _incomeCategories;
@override@JsonKey() List<String> get incomeCategories {
  if (_incomeCategories is EqualUnmodifiableListView) return _incomeCategories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_incomeCategories);
}

 final  List<String> _expenseCategories;
@override@JsonKey() List<String> get expenseCategories {
  if (_expenseCategories is EqualUnmodifiableListView) return _expenseCategories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_expenseCategories);
}


/// Create a copy of PredefinedCategories
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PredefinedCategoriesCopyWith<_PredefinedCategories> get copyWith => __$PredefinedCategoriesCopyWithImpl<_PredefinedCategories>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PredefinedCategoriesToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PredefinedCategories&&const DeepCollectionEquality().equals(other._incomeCategories, _incomeCategories)&&const DeepCollectionEquality().equals(other._expenseCategories, _expenseCategories));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_incomeCategories),const DeepCollectionEquality().hash(_expenseCategories));

@override
String toString() {
  return 'PredefinedCategories(incomeCategories: $incomeCategories, expenseCategories: $expenseCategories)';
}


}

/// @nodoc
abstract mixin class _$PredefinedCategoriesCopyWith<$Res> implements $PredefinedCategoriesCopyWith<$Res> {
  factory _$PredefinedCategoriesCopyWith(_PredefinedCategories value, $Res Function(_PredefinedCategories) _then) = __$PredefinedCategoriesCopyWithImpl;
@override @useResult
$Res call({
 List<String> incomeCategories, List<String> expenseCategories
});




}
/// @nodoc
class __$PredefinedCategoriesCopyWithImpl<$Res>
    implements _$PredefinedCategoriesCopyWith<$Res> {
  __$PredefinedCategoriesCopyWithImpl(this._self, this._then);

  final _PredefinedCategories _self;
  final $Res Function(_PredefinedCategories) _then;

/// Create a copy of PredefinedCategories
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? incomeCategories = null,Object? expenseCategories = null,}) {
  return _then(_PredefinedCategories(
incomeCategories: null == incomeCategories ? _self._incomeCategories : incomeCategories // ignore: cast_nullable_to_non_nullable
as List<String>,expenseCategories: null == expenseCategories ? _self._expenseCategories : expenseCategories // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}


/// @nodoc
mixin _$PremiumLimits {

 int get maxAccountsFree; int get maxAccountsPremium; int get maxCustomCategoriesFree; int get maxCustomCategoriesPremium; int get maxBudgetsFree; int get maxBudgetsPremium; int get maxGoalsFree; int get maxGoalsPremium;
/// Create a copy of PremiumLimits
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PremiumLimitsCopyWith<PremiumLimits> get copyWith => _$PremiumLimitsCopyWithImpl<PremiumLimits>(this as PremiumLimits, _$identity);

  /// Serializes this PremiumLimits to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PremiumLimits&&(identical(other.maxAccountsFree, maxAccountsFree) || other.maxAccountsFree == maxAccountsFree)&&(identical(other.maxAccountsPremium, maxAccountsPremium) || other.maxAccountsPremium == maxAccountsPremium)&&(identical(other.maxCustomCategoriesFree, maxCustomCategoriesFree) || other.maxCustomCategoriesFree == maxCustomCategoriesFree)&&(identical(other.maxCustomCategoriesPremium, maxCustomCategoriesPremium) || other.maxCustomCategoriesPremium == maxCustomCategoriesPremium)&&(identical(other.maxBudgetsFree, maxBudgetsFree) || other.maxBudgetsFree == maxBudgetsFree)&&(identical(other.maxBudgetsPremium, maxBudgetsPremium) || other.maxBudgetsPremium == maxBudgetsPremium)&&(identical(other.maxGoalsFree, maxGoalsFree) || other.maxGoalsFree == maxGoalsFree)&&(identical(other.maxGoalsPremium, maxGoalsPremium) || other.maxGoalsPremium == maxGoalsPremium));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,maxAccountsFree,maxAccountsPremium,maxCustomCategoriesFree,maxCustomCategoriesPremium,maxBudgetsFree,maxBudgetsPremium,maxGoalsFree,maxGoalsPremium);

@override
String toString() {
  return 'PremiumLimits(maxAccountsFree: $maxAccountsFree, maxAccountsPremium: $maxAccountsPremium, maxCustomCategoriesFree: $maxCustomCategoriesFree, maxCustomCategoriesPremium: $maxCustomCategoriesPremium, maxBudgetsFree: $maxBudgetsFree, maxBudgetsPremium: $maxBudgetsPremium, maxGoalsFree: $maxGoalsFree, maxGoalsPremium: $maxGoalsPremium)';
}


}

/// @nodoc
abstract mixin class $PremiumLimitsCopyWith<$Res>  {
  factory $PremiumLimitsCopyWith(PremiumLimits value, $Res Function(PremiumLimits) _then) = _$PremiumLimitsCopyWithImpl;
@useResult
$Res call({
 int maxAccountsFree, int maxAccountsPremium, int maxCustomCategoriesFree, int maxCustomCategoriesPremium, int maxBudgetsFree, int maxBudgetsPremium, int maxGoalsFree, int maxGoalsPremium
});




}
/// @nodoc
class _$PremiumLimitsCopyWithImpl<$Res>
    implements $PremiumLimitsCopyWith<$Res> {
  _$PremiumLimitsCopyWithImpl(this._self, this._then);

  final PremiumLimits _self;
  final $Res Function(PremiumLimits) _then;

/// Create a copy of PremiumLimits
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? maxAccountsFree = null,Object? maxAccountsPremium = null,Object? maxCustomCategoriesFree = null,Object? maxCustomCategoriesPremium = null,Object? maxBudgetsFree = null,Object? maxBudgetsPremium = null,Object? maxGoalsFree = null,Object? maxGoalsPremium = null,}) {
  return _then(_self.copyWith(
maxAccountsFree: null == maxAccountsFree ? _self.maxAccountsFree : maxAccountsFree // ignore: cast_nullable_to_non_nullable
as int,maxAccountsPremium: null == maxAccountsPremium ? _self.maxAccountsPremium : maxAccountsPremium // ignore: cast_nullable_to_non_nullable
as int,maxCustomCategoriesFree: null == maxCustomCategoriesFree ? _self.maxCustomCategoriesFree : maxCustomCategoriesFree // ignore: cast_nullable_to_non_nullable
as int,maxCustomCategoriesPremium: null == maxCustomCategoriesPremium ? _self.maxCustomCategoriesPremium : maxCustomCategoriesPremium // ignore: cast_nullable_to_non_nullable
as int,maxBudgetsFree: null == maxBudgetsFree ? _self.maxBudgetsFree : maxBudgetsFree // ignore: cast_nullable_to_non_nullable
as int,maxBudgetsPremium: null == maxBudgetsPremium ? _self.maxBudgetsPremium : maxBudgetsPremium // ignore: cast_nullable_to_non_nullable
as int,maxGoalsFree: null == maxGoalsFree ? _self.maxGoalsFree : maxGoalsFree // ignore: cast_nullable_to_non_nullable
as int,maxGoalsPremium: null == maxGoalsPremium ? _self.maxGoalsPremium : maxGoalsPremium // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [PremiumLimits].
extension PremiumLimitsPatterns on PremiumLimits {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PremiumLimits value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PremiumLimits() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PremiumLimits value)  $default,){
final _that = this;
switch (_that) {
case _PremiumLimits():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PremiumLimits value)?  $default,){
final _that = this;
switch (_that) {
case _PremiumLimits() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int maxAccountsFree,  int maxAccountsPremium,  int maxCustomCategoriesFree,  int maxCustomCategoriesPremium,  int maxBudgetsFree,  int maxBudgetsPremium,  int maxGoalsFree,  int maxGoalsPremium)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PremiumLimits() when $default != null:
return $default(_that.maxAccountsFree,_that.maxAccountsPremium,_that.maxCustomCategoriesFree,_that.maxCustomCategoriesPremium,_that.maxBudgetsFree,_that.maxBudgetsPremium,_that.maxGoalsFree,_that.maxGoalsPremium);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int maxAccountsFree,  int maxAccountsPremium,  int maxCustomCategoriesFree,  int maxCustomCategoriesPremium,  int maxBudgetsFree,  int maxBudgetsPremium,  int maxGoalsFree,  int maxGoalsPremium)  $default,) {final _that = this;
switch (_that) {
case _PremiumLimits():
return $default(_that.maxAccountsFree,_that.maxAccountsPremium,_that.maxCustomCategoriesFree,_that.maxCustomCategoriesPremium,_that.maxBudgetsFree,_that.maxBudgetsPremium,_that.maxGoalsFree,_that.maxGoalsPremium);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int maxAccountsFree,  int maxAccountsPremium,  int maxCustomCategoriesFree,  int maxCustomCategoriesPremium,  int maxBudgetsFree,  int maxBudgetsPremium,  int maxGoalsFree,  int maxGoalsPremium)?  $default,) {final _that = this;
switch (_that) {
case _PremiumLimits() when $default != null:
return $default(_that.maxAccountsFree,_that.maxAccountsPremium,_that.maxCustomCategoriesFree,_that.maxCustomCategoriesPremium,_that.maxBudgetsFree,_that.maxBudgetsPremium,_that.maxGoalsFree,_that.maxGoalsPremium);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PremiumLimits implements PremiumLimits {
  const _PremiumLimits({this.maxAccountsFree = 3, this.maxAccountsPremium = 50, this.maxCustomCategoriesFree = 5, this.maxCustomCategoriesPremium = 100, this.maxBudgetsFree = 10, this.maxBudgetsPremium = 100, this.maxGoalsFree = 5, this.maxGoalsPremium = 50});
  factory _PremiumLimits.fromJson(Map<String, dynamic> json) => _$PremiumLimitsFromJson(json);

@override@JsonKey() final  int maxAccountsFree;
@override@JsonKey() final  int maxAccountsPremium;
@override@JsonKey() final  int maxCustomCategoriesFree;
@override@JsonKey() final  int maxCustomCategoriesPremium;
@override@JsonKey() final  int maxBudgetsFree;
@override@JsonKey() final  int maxBudgetsPremium;
@override@JsonKey() final  int maxGoalsFree;
@override@JsonKey() final  int maxGoalsPremium;

/// Create a copy of PremiumLimits
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PremiumLimitsCopyWith<_PremiumLimits> get copyWith => __$PremiumLimitsCopyWithImpl<_PremiumLimits>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PremiumLimitsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PremiumLimits&&(identical(other.maxAccountsFree, maxAccountsFree) || other.maxAccountsFree == maxAccountsFree)&&(identical(other.maxAccountsPremium, maxAccountsPremium) || other.maxAccountsPremium == maxAccountsPremium)&&(identical(other.maxCustomCategoriesFree, maxCustomCategoriesFree) || other.maxCustomCategoriesFree == maxCustomCategoriesFree)&&(identical(other.maxCustomCategoriesPremium, maxCustomCategoriesPremium) || other.maxCustomCategoriesPremium == maxCustomCategoriesPremium)&&(identical(other.maxBudgetsFree, maxBudgetsFree) || other.maxBudgetsFree == maxBudgetsFree)&&(identical(other.maxBudgetsPremium, maxBudgetsPremium) || other.maxBudgetsPremium == maxBudgetsPremium)&&(identical(other.maxGoalsFree, maxGoalsFree) || other.maxGoalsFree == maxGoalsFree)&&(identical(other.maxGoalsPremium, maxGoalsPremium) || other.maxGoalsPremium == maxGoalsPremium));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,maxAccountsFree,maxAccountsPremium,maxCustomCategoriesFree,maxCustomCategoriesPremium,maxBudgetsFree,maxBudgetsPremium,maxGoalsFree,maxGoalsPremium);

@override
String toString() {
  return 'PremiumLimits(maxAccountsFree: $maxAccountsFree, maxAccountsPremium: $maxAccountsPremium, maxCustomCategoriesFree: $maxCustomCategoriesFree, maxCustomCategoriesPremium: $maxCustomCategoriesPremium, maxBudgetsFree: $maxBudgetsFree, maxBudgetsPremium: $maxBudgetsPremium, maxGoalsFree: $maxGoalsFree, maxGoalsPremium: $maxGoalsPremium)';
}


}

/// @nodoc
abstract mixin class _$PremiumLimitsCopyWith<$Res> implements $PremiumLimitsCopyWith<$Res> {
  factory _$PremiumLimitsCopyWith(_PremiumLimits value, $Res Function(_PremiumLimits) _then) = __$PremiumLimitsCopyWithImpl;
@override @useResult
$Res call({
 int maxAccountsFree, int maxAccountsPremium, int maxCustomCategoriesFree, int maxCustomCategoriesPremium, int maxBudgetsFree, int maxBudgetsPremium, int maxGoalsFree, int maxGoalsPremium
});




}
/// @nodoc
class __$PremiumLimitsCopyWithImpl<$Res>
    implements _$PremiumLimitsCopyWith<$Res> {
  __$PremiumLimitsCopyWithImpl(this._self, this._then);

  final _PremiumLimits _self;
  final $Res Function(_PremiumLimits) _then;

/// Create a copy of PremiumLimits
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? maxAccountsFree = null,Object? maxAccountsPremium = null,Object? maxCustomCategoriesFree = null,Object? maxCustomCategoriesPremium = null,Object? maxBudgetsFree = null,Object? maxBudgetsPremium = null,Object? maxGoalsFree = null,Object? maxGoalsPremium = null,}) {
  return _then(_PremiumLimits(
maxAccountsFree: null == maxAccountsFree ? _self.maxAccountsFree : maxAccountsFree // ignore: cast_nullable_to_non_nullable
as int,maxAccountsPremium: null == maxAccountsPremium ? _self.maxAccountsPremium : maxAccountsPremium // ignore: cast_nullable_to_non_nullable
as int,maxCustomCategoriesFree: null == maxCustomCategoriesFree ? _self.maxCustomCategoriesFree : maxCustomCategoriesFree // ignore: cast_nullable_to_non_nullable
as int,maxCustomCategoriesPremium: null == maxCustomCategoriesPremium ? _self.maxCustomCategoriesPremium : maxCustomCategoriesPremium // ignore: cast_nullable_to_non_nullable
as int,maxBudgetsFree: null == maxBudgetsFree ? _self.maxBudgetsFree : maxBudgetsFree // ignore: cast_nullable_to_non_nullable
as int,maxBudgetsPremium: null == maxBudgetsPremium ? _self.maxBudgetsPremium : maxBudgetsPremium // ignore: cast_nullable_to_non_nullable
as int,maxGoalsFree: null == maxGoalsFree ? _self.maxGoalsFree : maxGoalsFree // ignore: cast_nullable_to_non_nullable
as int,maxGoalsPremium: null == maxGoalsPremium ? _self.maxGoalsPremium : maxGoalsPremium // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

/// @nodoc
mixin _$RemoteConfigData {

 PredefinedCategories get categories; PremiumLimits get premiumLimits; bool get enableFeatureX; bool get enableBetaFeatures; int get schemaVersion;
/// Create a copy of RemoteConfigData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RemoteConfigDataCopyWith<RemoteConfigData> get copyWith => _$RemoteConfigDataCopyWithImpl<RemoteConfigData>(this as RemoteConfigData, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RemoteConfigData&&(identical(other.categories, categories) || other.categories == categories)&&(identical(other.premiumLimits, premiumLimits) || other.premiumLimits == premiumLimits)&&(identical(other.enableFeatureX, enableFeatureX) || other.enableFeatureX == enableFeatureX)&&(identical(other.enableBetaFeatures, enableBetaFeatures) || other.enableBetaFeatures == enableBetaFeatures)&&(identical(other.schemaVersion, schemaVersion) || other.schemaVersion == schemaVersion));
}


@override
int get hashCode => Object.hash(runtimeType,categories,premiumLimits,enableFeatureX,enableBetaFeatures,schemaVersion);

@override
String toString() {
  return 'RemoteConfigData(categories: $categories, premiumLimits: $premiumLimits, enableFeatureX: $enableFeatureX, enableBetaFeatures: $enableBetaFeatures, schemaVersion: $schemaVersion)';
}


}

/// @nodoc
abstract mixin class $RemoteConfigDataCopyWith<$Res>  {
  factory $RemoteConfigDataCopyWith(RemoteConfigData value, $Res Function(RemoteConfigData) _then) = _$RemoteConfigDataCopyWithImpl;
@useResult
$Res call({
 PredefinedCategories categories, PremiumLimits premiumLimits, bool enableFeatureX, bool enableBetaFeatures, int schemaVersion
});


$PredefinedCategoriesCopyWith<$Res> get categories;$PremiumLimitsCopyWith<$Res> get premiumLimits;

}
/// @nodoc
class _$RemoteConfigDataCopyWithImpl<$Res>
    implements $RemoteConfigDataCopyWith<$Res> {
  _$RemoteConfigDataCopyWithImpl(this._self, this._then);

  final RemoteConfigData _self;
  final $Res Function(RemoteConfigData) _then;

/// Create a copy of RemoteConfigData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? categories = null,Object? premiumLimits = null,Object? enableFeatureX = null,Object? enableBetaFeatures = null,Object? schemaVersion = null,}) {
  return _then(_self.copyWith(
categories: null == categories ? _self.categories : categories // ignore: cast_nullable_to_non_nullable
as PredefinedCategories,premiumLimits: null == premiumLimits ? _self.premiumLimits : premiumLimits // ignore: cast_nullable_to_non_nullable
as PremiumLimits,enableFeatureX: null == enableFeatureX ? _self.enableFeatureX : enableFeatureX // ignore: cast_nullable_to_non_nullable
as bool,enableBetaFeatures: null == enableBetaFeatures ? _self.enableBetaFeatures : enableBetaFeatures // ignore: cast_nullable_to_non_nullable
as bool,schemaVersion: null == schemaVersion ? _self.schemaVersion : schemaVersion // ignore: cast_nullable_to_non_nullable
as int,
  ));
}
/// Create a copy of RemoteConfigData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PredefinedCategoriesCopyWith<$Res> get categories {
  
  return $PredefinedCategoriesCopyWith<$Res>(_self.categories, (value) {
    return _then(_self.copyWith(categories: value));
  });
}/// Create a copy of RemoteConfigData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PremiumLimitsCopyWith<$Res> get premiumLimits {
  
  return $PremiumLimitsCopyWith<$Res>(_self.premiumLimits, (value) {
    return _then(_self.copyWith(premiumLimits: value));
  });
}
}


/// Adds pattern-matching-related methods to [RemoteConfigData].
extension RemoteConfigDataPatterns on RemoteConfigData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _RemoteConfigData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _RemoteConfigData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _RemoteConfigData value)  $default,){
final _that = this;
switch (_that) {
case _RemoteConfigData():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _RemoteConfigData value)?  $default,){
final _that = this;
switch (_that) {
case _RemoteConfigData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( PredefinedCategories categories,  PremiumLimits premiumLimits,  bool enableFeatureX,  bool enableBetaFeatures,  int schemaVersion)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _RemoteConfigData() when $default != null:
return $default(_that.categories,_that.premiumLimits,_that.enableFeatureX,_that.enableBetaFeatures,_that.schemaVersion);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( PredefinedCategories categories,  PremiumLimits premiumLimits,  bool enableFeatureX,  bool enableBetaFeatures,  int schemaVersion)  $default,) {final _that = this;
switch (_that) {
case _RemoteConfigData():
return $default(_that.categories,_that.premiumLimits,_that.enableFeatureX,_that.enableBetaFeatures,_that.schemaVersion);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( PredefinedCategories categories,  PremiumLimits premiumLimits,  bool enableFeatureX,  bool enableBetaFeatures,  int schemaVersion)?  $default,) {final _that = this;
switch (_that) {
case _RemoteConfigData() when $default != null:
return $default(_that.categories,_that.premiumLimits,_that.enableFeatureX,_that.enableBetaFeatures,_that.schemaVersion);case _:
  return null;

}
}

}

/// @nodoc


class _RemoteConfigData extends RemoteConfigData {
  const _RemoteConfigData({this.categories = const PredefinedCategories(), this.premiumLimits = const PremiumLimits(), this.enableFeatureX = false, this.enableBetaFeatures = false, this.schemaVersion = 1}): super._();
  

@override@JsonKey() final  PredefinedCategories categories;
@override@JsonKey() final  PremiumLimits premiumLimits;
@override@JsonKey() final  bool enableFeatureX;
@override@JsonKey() final  bool enableBetaFeatures;
@override@JsonKey() final  int schemaVersion;

/// Create a copy of RemoteConfigData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RemoteConfigDataCopyWith<_RemoteConfigData> get copyWith => __$RemoteConfigDataCopyWithImpl<_RemoteConfigData>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RemoteConfigData&&(identical(other.categories, categories) || other.categories == categories)&&(identical(other.premiumLimits, premiumLimits) || other.premiumLimits == premiumLimits)&&(identical(other.enableFeatureX, enableFeatureX) || other.enableFeatureX == enableFeatureX)&&(identical(other.enableBetaFeatures, enableBetaFeatures) || other.enableBetaFeatures == enableBetaFeatures)&&(identical(other.schemaVersion, schemaVersion) || other.schemaVersion == schemaVersion));
}


@override
int get hashCode => Object.hash(runtimeType,categories,premiumLimits,enableFeatureX,enableBetaFeatures,schemaVersion);

@override
String toString() {
  return 'RemoteConfigData(categories: $categories, premiumLimits: $premiumLimits, enableFeatureX: $enableFeatureX, enableBetaFeatures: $enableBetaFeatures, schemaVersion: $schemaVersion)';
}


}

/// @nodoc
abstract mixin class _$RemoteConfigDataCopyWith<$Res> implements $RemoteConfigDataCopyWith<$Res> {
  factory _$RemoteConfigDataCopyWith(_RemoteConfigData value, $Res Function(_RemoteConfigData) _then) = __$RemoteConfigDataCopyWithImpl;
@override @useResult
$Res call({
 PredefinedCategories categories, PremiumLimits premiumLimits, bool enableFeatureX, bool enableBetaFeatures, int schemaVersion
});


@override $PredefinedCategoriesCopyWith<$Res> get categories;@override $PremiumLimitsCopyWith<$Res> get premiumLimits;

}
/// @nodoc
class __$RemoteConfigDataCopyWithImpl<$Res>
    implements _$RemoteConfigDataCopyWith<$Res> {
  __$RemoteConfigDataCopyWithImpl(this._self, this._then);

  final _RemoteConfigData _self;
  final $Res Function(_RemoteConfigData) _then;

/// Create a copy of RemoteConfigData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? categories = null,Object? premiumLimits = null,Object? enableFeatureX = null,Object? enableBetaFeatures = null,Object? schemaVersion = null,}) {
  return _then(_RemoteConfigData(
categories: null == categories ? _self.categories : categories // ignore: cast_nullable_to_non_nullable
as PredefinedCategories,premiumLimits: null == premiumLimits ? _self.premiumLimits : premiumLimits // ignore: cast_nullable_to_non_nullable
as PremiumLimits,enableFeatureX: null == enableFeatureX ? _self.enableFeatureX : enableFeatureX // ignore: cast_nullable_to_non_nullable
as bool,enableBetaFeatures: null == enableBetaFeatures ? _self.enableBetaFeatures : enableBetaFeatures // ignore: cast_nullable_to_non_nullable
as bool,schemaVersion: null == schemaVersion ? _self.schemaVersion : schemaVersion // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

/// Create a copy of RemoteConfigData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PredefinedCategoriesCopyWith<$Res> get categories {
  
  return $PredefinedCategoriesCopyWith<$Res>(_self.categories, (value) {
    return _then(_self.copyWith(categories: value));
  });
}/// Create a copy of RemoteConfigData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PremiumLimitsCopyWith<$Res> get premiumLimits {
  
  return $PremiumLimitsCopyWith<$Res>(_self.premiumLimits, (value) {
    return _then(_self.copyWith(premiumLimits: value));
  });
}
}

// dart format on
