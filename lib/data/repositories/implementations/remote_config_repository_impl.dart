import 'dart:async';

import 'package:budapp/data/models/remote_config_data.dart';
import 'package:budapp/data/repositories/interfaces/remote_config_repository.dart';
import 'package:budapp/services/remote_config_service.dart';
import 'package:flutter/foundation.dart';

/// Implementation of Remote Config repository using Firebase Remote Config
class RemoteConfigRepositoryImpl implements IRemoteConfigRepository {
  RemoteConfigRepositoryImpl(this._remoteConfigService);
  final RemoteConfigService _remoteConfigService;
  StreamController<RemoteConfigData>? _configUpdateController;

  @override
  Future<void> initialize() async {
    await _remoteConfigService.initialize();

    // Set up real-time updates if supported
    if (!kIsWeb) {
      _setupRealTimeUpdates();
    }
  }

  @override
  Future<bool> fetchAndActivate() async {
    return _remoteConfigService.fetchAndActivate();
  }

  @override
  RemoteConfigData getCurrentConfig() {
    return _remoteConfigService.getCurrentConfig();
  }

  @override
  PredefinedCategories getPredefinedCategories() {
    return getCurrentConfig().categories;
  }

  @override
  PremiumLimits getPremiumLimits() {
    return getCurrentConfig().premiumLimits;
  }

  @override
  bool isFeatureEnabled(String featureKey) {
    try {
      return _remoteConfigService.getValue<bool>(
        featureKey,
        defaultValue: false,
      );
    } on Exception catch (e) {
      debugPrint('Failed to get feature flag $featureKey: $e');
      return false;
    }
  }

  @override
  T getParameterValue<T>(String key, T defaultValue) {
    return _remoteConfigService.getValue<T>(key, defaultValue: defaultValue);
  }

  @override
  bool get isStale => _remoteConfigService.isStale;

  @override
  DateTime get lastFetchTime => _remoteConfigService.lastFetchTime;

  @override
  Stream<RemoteConfigData>? watchConfigUpdates() {
    if (kIsWeb) {
      return null; // Real-time updates not supported on Web
    }

    return _configUpdateController?.stream;
  }

  @override
  Future<void> forceRefresh() async {
    try {
      await _remoteConfigService.fetch();
      await _remoteConfigService.activate();

      // Notify listeners of config update
      _notifyConfigUpdate();
    } on Exception catch (e) {
      debugPrint('Failed to force refresh Remote Config: $e');
      rethrow;
    }
  }

  @override
  Map<String, dynamic> getStatus() {
    return {
      'lastFetchStatus': _remoteConfigService.lastFetchStatus.name,
      'lastFetchTime': lastFetchTime.toIso8601String(),
      'isStale': isStale,
      'supportsRealTimeUpdates': !kIsWeb,
    };
  }

  /// Set up real-time Remote Config updates
  void _setupRealTimeUpdates() {
    final updateStream = _remoteConfigService.onConfigUpdated;
    if (updateStream == null) return;

    _configUpdateController = StreamController<RemoteConfigData>.broadcast();

    updateStream.listen(
      (update) async {
        try {
          // Activate the new config
          await _remoteConfigService.activate();
          _notifyConfigUpdate();
        } on Exception catch (e) {
          debugPrint('Failed to activate Remote Config update: $e');
        }
      },
      onError: (dynamic error) {
        debugPrint('Remote Config update stream error: $error');
      },
    );
  }

  /// Notify listeners of config updates
  void _notifyConfigUpdate() {
    if (_configUpdateController?.isClosed ?? true) return;

    try {
      final currentConfig = getCurrentConfig();
      _configUpdateController?.add(currentConfig);
    } on Exception catch (e) {
      debugPrint('Failed to notify config update: $e');
    }
  }

  /// Clean up resources
  void dispose() {
    _configUpdateController?.close();
    _configUpdateController = null;
  }
}
