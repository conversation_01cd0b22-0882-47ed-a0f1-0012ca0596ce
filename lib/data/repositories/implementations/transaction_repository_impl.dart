import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/data/repositories/interfaces/transaction_repository.dart';
import 'package:budapp/features/budgets/services/budget_transaction_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart' hide Transaction;

/// Helper class to track balance adjustments during transaction updates
class BalanceAdjustment {
  // positive = add, negative = subtract

  const BalanceAdjustment({
    required this.accountId,
    required this.adjustmentCents,
  });
  final String accountId;
  final int adjustmentCents;

  @override
  String toString() =>
      'BalanceAdjustment(accountId: $accountId, '
      'adjustmentCents: $adjustmentCents)';
}

/// Concrete implementation of ITransactionRepository using Firestore
///
/// This implementation provides transaction management functionality
/// using FirestoreService as the backend storage.
class TransactionRepositoryImpl implements ITransactionRepository {
  TransactionRepositoryImpl(
    this._firestoreService,
    this._budgetTransactionService,
  );
  final FirestoreService _firestoreService;
  final BudgetTransactionService _budgetTransactionService;
  static const String _collectionName = 'transactions';

  /// Get reference to user's transactions collection
  CollectionReference _userTransactionsCollection(String userId) =>
      _firestoreService.collection('users/$userId/$_collectionName');

  /// Get reference to a specific transaction document
  DocumentReference _transactionDoc(String userId, String transactionId) =>
      _userTransactionsCollection(userId).doc(transactionId);

  Future<String> create(Transaction entity) async {
    await _transactionDoc(entity.userId, entity.id).set(entity.toJson());
    return entity.id;
  }

  Future<Transaction?> getById(String id) async {
    // Note: This requires userId context, not available in base interface
    // Use with caution or override with userId parameter
    throw UnimplementedError(
      'Use getTransactionById(userId, transactionId) instead',
    );
  }

  /// Get transaction by ID with user context
  Future<Transaction?> getTransactionById(
    String userId,
    String transactionId,
  ) async {
    final doc = await _transactionDoc(userId, transactionId).get();
    if (!doc.exists) return null;

    final data = doc.data() as Map<String, dynamic>? ?? {};
    return Transaction.fromJson(data);
  }

  Future<void> update(String id, Transaction entity) async {
    final updatedEntity = entity.copyWith(
      id: id, // Ensure ID matches the document ID
      updatedAt: DateTime.now(),
    );
    await _transactionDoc(entity.userId, id).update(updatedEntity.toJson());
  }

  Future<void> delete(String id) async {
    // Note: This requires userId context
    throw UnimplementedError(
      'Use deleteTransaction(userId, transactionId) instead',
    );
  }

  Future<bool> exists(String id) async {
    // Note: This requires userId context
    throw UnimplementedError(
      'Use transactionExists(userId, transactionId) instead',
    );
  }

  /// Check if transaction exists with user context
  Future<bool> transactionExists(String userId, String transactionId) async {
    final doc = await _transactionDoc(userId, transactionId).get();
    return doc.exists;
  }

  Future<List<Transaction>> getAll({int? limit}) async {
    throw UnimplementedError('Use getTransactionsByUserId(userId) instead');
  }

  Future<List<Transaction>> getPaginated({
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    throw UnimplementedError(
      'Use getUserTransactionsPaginated(userId, limit, startAfter) instead',
    );
  }

  /// Get paginated transactions for a user
  Future<List<Transaction>> getUserTransactionsPaginated(
    String userId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    var query = _userTransactionsCollection(
      userId,
    ).orderBy('transactionDate', descending: true).limit(limit);

    if (startAfter != null) {
      query = query.startAfterDocument(startAfter);
    }

    final snapshot = await query.get();
    return snapshot.docs
        .map(
          (doc) =>
              Transaction.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  Stream<Transaction?> watchById(String id) {
    throw UnimplementedError(
      'Use watchTransaction(userId, transactionId) instead',
    );
  }

  Stream<List<Transaction>> watchAll({int? limit}) {
    throw UnimplementedError('Use watchUserTransactions(userId) instead');
  }

  Future<void> batchCreate(List<Transaction> entities) async {
    if (entities.isEmpty) return;

    final batch = _firestoreService.batch();
    final userId = entities.first.userId;

    for (final entity in entities) {
      if (entity.userId != userId) {
        throw ArgumentError('All transactions must belong to the same user');
      }
      batch.set(_transactionDoc(userId, entity.id), entity.toJson());
    }

    await batch.commit();
  }

  Future<void> batchUpdate(Map<String, Transaction> entities) async {
    if (entities.isEmpty) return;

    final batch = _firestoreService.batch();
    final userId = entities.values.first.userId;

    for (final entry in entities.entries) {
      final updatedEntity = entry.value.copyWith(
        id: entry.key,
        updatedAt: DateTime.now(),
      );
      if (updatedEntity.userId != userId) {
        throw ArgumentError('All transactions must belong to the same user');
      }
      batch.update(_transactionDoc(userId, entry.key), updatedEntity.toJson());
    }

    await batch.commit();
  }

  Future<void> batchDelete(List<String> ids) async {
    throw UnimplementedError(
      'Use batchDeleteUserTransactions(userId, transactionIds) instead',
    );
  }

  /// Batch delete transactions for a user
  Future<void> batchDeleteUserTransactions(
    String userId,
    List<String> transactionIds,
  ) async {
    if (transactionIds.isEmpty) return;

    final batch = _firestoreService.batch();

    for (final id in transactionIds) {
      batch.delete(_transactionDoc(userId, id));
    }

    await batch.commit();
  }

  Future<List<Transaction>> query({
    Map<String, dynamic>? where,
    String? orderBy,
    bool descending = false,
    int? limit,
  }) async {
    throw UnimplementedError(
      'Use specific query methods with userId context instead',
    );
  }

  Future<int> count({Map<String, dynamic>? where}) async {
    throw UnimplementedError('Use getUserTransactionCount(userId) instead');
  }

  /// Get transaction count for a user
  Future<int> getUserTransactionCount(String userId) async {
    final snapshot = await _userTransactionsCollection(userId).count().get();
    return snapshot.count ?? 0;
  }

  // ITransactionRepository specific methods

  @override
  Future<List<Transaction>> getTransactionsByUserId(String userId) async {
    final snapshot = await _userTransactionsCollection(
      userId,
    ).orderBy('transactionDate', descending: true).get();

    return snapshot.docs
        .map(
          (doc) =>
              Transaction.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<List<Transaction>> getTransactionsByAccountId(String accountId) async {
    // We need to search across all users or have userId context
    throw UnimplementedError(
      'Use getTransactionsByAccountIdForUser(userId, accountId) instead',
    );
  }

  /// Get transactions for a specific account within a user's context
  Future<List<Transaction>> getTransactionsByAccountIdForUser(
    String userId,
    String accountId,
  ) async {
    final snapshot = await _userTransactionsCollection(userId)
        .where(
          Filter.or(
            Filter('fromAccountId', isEqualTo: accountId),
            Filter('toAccountId', isEqualTo: accountId),
          ),
        )
        .orderBy('transactionDate', descending: true)
        .get();

    return snapshot.docs
        .map(
          (doc) =>
              Transaction.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<List<Transaction>> getTransactionsByType(
    String userId,
    TransactionType type,
  ) async {
    final snapshot = await _userTransactionsCollection(userId)
        .where('type', isEqualTo: type.name)
        .orderBy('transactionDate', descending: true)
        .get();

    return snapshot.docs
        .map(
          (doc) =>
              Transaction.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<List<Transaction>> getTransactionsByStatus(
    String userId,
    TransactionStatus status,
  ) async {
    final snapshot = await _userTransactionsCollection(userId)
        .where('status', isEqualTo: status.name)
        .orderBy('transactionDate', descending: true)
        .get();

    return snapshot.docs
        .map(
          (doc) =>
              Transaction.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<List<Transaction>> getTransactionsByCategory(
    String userId,
    String categoryId,
  ) async {
    final snapshot = await _userTransactionsCollection(userId)
        .where('categoryId', isEqualTo: categoryId)
        .orderBy('transactionDate', descending: true)
        .get();

    return snapshot.docs
        .map(
          (doc) =>
              Transaction.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<List<Transaction>> getTransactionsByDateRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final snapshot = await _userTransactionsCollection(userId)
        .where(
          'transactionDate',
          isGreaterThanOrEqualTo: startDate.toIso8601String(),
        )
        .where(
          'transactionDate',
          isLessThanOrEqualTo: endDate.toIso8601String(),
        )
        .orderBy('transactionDate', descending: true)
        .get();

    return snapshot.docs
        .map(
          (doc) =>
              Transaction.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<List<Transaction>> getTransactionsByTags(
    String userId,
    List<String> tags,
  ) async {
    final snapshot = await _userTransactionsCollection(userId)
        .where('tagIds', arrayContainsAny: tags)
        .orderBy('transactionDate', descending: true)
        .get();

    return snapshot.docs
        .map(
          (doc) =>
              Transaction.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<String> createTransaction(Transaction transaction) async {
    // Validate transaction before creation
    if (!await validateTransaction(transaction)) {
      throw ArgumentError('Invalid transaction data');
    }
    return create(transaction);
  }

  /// Create an income transaction with enhanced validation and atomic balance update
  @override
  Future<String> createIncomeTransaction({
    required String userId,
    required String toAccountId,
    required int amountCents,
    required DateTime transactionDate,
    String? categoryId,
    String? description,
    String? notes,
    List<String> tags = const [],
    Map<String, dynamic> metadata = const {},
  }) async {
    // Validate destination account exists
    if (!await _validateAccountExists(userId, toAccountId)) {
      throw ArgumentError(
        'Destination account does not exist or does not belong to user',
      );
    }

    final now = DateTime.now();
    final transactionId = _firestoreService
        .collection('users')
        .doc(userId)
        .collection('transactions')
        .doc()
        .id;

    final transaction = Transaction(
      id: transactionId,
      userId: userId,
      type: TransactionType.income,
      status: TransactionStatus.completed,
      amountCents: amountCents,
      toAccountId: toAccountId,
      categoryId: categoryId,
      description: description,
      notes: notes,
      tagIds: tags,
      transactionDate: transactionDate,
      createdAt: now,
      updatedAt: now,
      metadata: metadata,
    );

    // Use Firestore transaction for atomic operation
    return _firestoreService.runTransaction<String>((
      firestoreTransaction,
    ) async {
      // Read current account state
      final accountRef = _firestoreService
          .collection('users')
          .doc(userId)
          .collection('accounts')
          .doc(toAccountId);

      final accountSnapshot = await firestoreTransaction.get(accountRef);

      if (!accountSnapshot.exists) {
        throw ArgumentError('Account does not exist');
      }

      final accountData = accountSnapshot.data() ?? {};
      final currentAccount = Account.fromJson(accountData);

      // Calculate new balance (income adds to account)
      final newBalanceCents = currentAccount.currentBalanceCents + amountCents;

      // Update account with new balance
      final updatedAccount = currentAccount.copyWith(
        currentBalanceCents: newBalanceCents,
        updatedAt: now,
      );

      // Create transaction document
      final transactionRef = _userTransactionsCollection(
        userId,
      ).doc(transactionId);

      // Update budgets for this transaction
      await _budgetTransactionService.updateBudgetsForTransaction(
        transaction,
        firestoreTransaction,
      );

      // Perform atomic operations
      firestoreTransaction
        ..set(transactionRef, transaction.toJson())
        ..update(accountRef, updatedAccount.toJson());

      return transactionId;
    });
  }

  /// Create an expense transaction with enhanced validation and atomic balance update
  @override
  Future<String> createExpenseTransaction({
    required String userId,
    required String fromAccountId,
    required int amountCents,
    required DateTime transactionDate,
    String? categoryId,
    String? description,
    String? notes,
    List<String> tags = const [],
    Map<String, dynamic> metadata = const {},
  }) async {
    // Validate source account exists
    if (!await _validateAccountExists(userId, fromAccountId)) {
      throw ArgumentError(
        'Source account does not exist or does not belong to user',
      );
    }

    final now = DateTime.now();
    final transactionId = _firestoreService
        .collection('users')
        .doc(userId)
        .collection('transactions')
        .doc()
        .id;

    final transaction = Transaction(
      id: transactionId,
      userId: userId,
      type: TransactionType.expense,
      status: TransactionStatus.completed,
      amountCents: amountCents,
      fromAccountId: fromAccountId,
      categoryId: categoryId,
      description: description,
      notes: notes,
      tagIds: tags,
      transactionDate: transactionDate,
      createdAt: now,
      updatedAt: now,
      metadata: metadata,
    );

    // Use Firestore transaction for atomic operation
    return _firestoreService.runTransaction<String>((
      firestoreTransaction,
    ) async {
      // Read current account state
      final accountRef = _firestoreService
          .collection('users')
          .doc(userId)
          .collection('accounts')
          .doc(fromAccountId);

      final accountSnapshot = await firestoreTransaction.get(accountRef);

      if (!accountSnapshot.exists) {
        throw ArgumentError('Account does not exist');
      }

      final accountData = accountSnapshot.data() ?? {};
      final currentAccount = Account.fromJson(accountData);

      // Calculate new balance (expense subtracts from account)
      final newBalanceCents = currentAccount.currentBalanceCents - amountCents;

      // Update account with new balance
      final updatedAccount = currentAccount.copyWith(
        currentBalanceCents: newBalanceCents,
        updatedAt: now,
      );

      // Create transaction document
      final transactionRef = _userTransactionsCollection(
        userId,
      ).doc(transactionId);

      // Update budgets for this transaction
      await _budgetTransactionService.updateBudgetsForTransaction(
        transaction,
        firestoreTransaction,
      );

      // Perform atomic operations
      firestoreTransaction
        ..set(transactionRef, transaction.toJson())
        ..update(accountRef, updatedAccount.toJson());

      return transactionId;
    });
  }

  /// Create a transfer transaction with enhanced validation and atomic balance updates
  @override
  Future<String> createTransferTransaction({
    required String userId,
    required String fromAccountId,
    required String toAccountId,
    required int amountCents,
    required DateTime transactionDate,
    String? description,
    String? notes,
    List<String> tags = const [],
    Map<String, dynamic> metadata = const {},
  }) async {
    // Validate accounts are different
    if (fromAccountId == toAccountId) {
      throw ArgumentError('Source and destination accounts must be different');
    }

    // Validate both accounts exist
    if (!await _validateAccountExists(userId, fromAccountId)) {
      throw ArgumentError(
        'Source account does not exist or does not belong to user',
      );
    }
    if (!await _validateAccountExists(userId, toAccountId)) {
      throw ArgumentError(
        'Destination account does not exist or does not belong to user',
      );
    }

    final now = DateTime.now();
    final transactionId = _firestoreService
        .collection('users')
        .doc(userId)
        .collection('transactions')
        .doc()
        .id;

    final transaction = Transaction(
      id: transactionId,
      userId: userId,
      type: TransactionType.transfer,
      status: TransactionStatus.completed,
      amountCents: amountCents,
      fromAccountId: fromAccountId,
      toAccountId: toAccountId,
      description: description,
      notes: notes,
      tagIds: tags,
      transactionDate: transactionDate,
      createdAt: now,
      updatedAt: now,
      metadata: metadata,
    );

    // Use Firestore transaction for atomic operation
    return _firestoreService.runTransaction<String>((
      firestoreTransaction,
    ) async {
      // Read current state of both accounts
      final fromAccountRef = _firestoreService
          .collection('users')
          .doc(userId)
          .collection('accounts')
          .doc(fromAccountId);

      final toAccountRef = _firestoreService
          .collection('users')
          .doc(userId)
          .collection('accounts')
          .doc(toAccountId);

      final fromAccountSnapshot = await firestoreTransaction.get(
        fromAccountRef,
      );
      final toAccountSnapshot = await firestoreTransaction.get(toAccountRef);

      if (!fromAccountSnapshot.exists) {
        throw ArgumentError('Source account does not exist');
      }
      if (!toAccountSnapshot.exists) {
        throw ArgumentError('Destination account does not exist');
      }

      final fromAccountData = fromAccountSnapshot.data() ?? {};
      final toAccountData = toAccountSnapshot.data() ?? {};

      final currentFromAccount = Account.fromJson(fromAccountData);
      final currentToAccount = Account.fromJson(toAccountData);

      // Calculate new balances (transfer subtracts from source, adds to destination)
      final newFromBalanceCents =
          currentFromAccount.currentBalanceCents - amountCents;
      final newToBalanceCents =
          currentToAccount.currentBalanceCents + amountCents;

      // Update both accounts with new balances
      final updatedFromAccount = currentFromAccount.copyWith(
        currentBalanceCents: newFromBalanceCents,
        updatedAt: now,
      );

      final updatedToAccount = currentToAccount.copyWith(
        currentBalanceCents: newToBalanceCents,
        updatedAt: now,
      );

      // Create transaction document
      final transactionRef = _userTransactionsCollection(
        userId,
      ).doc(transactionId);

      // Perform atomic operations
      firestoreTransaction
        ..set(transactionRef, transaction.toJson())
        ..update(fromAccountRef, updatedFromAccount.toJson())
        ..update(toAccountRef, updatedToAccount.toJson());

      return transactionId;
    });
  }

  @override
  Future<void> updateTransaction(
    String transactionId,
    Transaction transaction,
  ) async {
    // Use the atomic update method that handles account balance adjustments
    await updateTransactionWithBalanceAdjustment(
      transaction.userId,
      transactionId,
      transaction,
    );
  }

  /// Update a transaction with atomic account balance adjustments
  /// This method ensures that both the transaction and related account balances
  /// are updated atomically using Firestore batch operations
  Future<void> updateTransactionWithBalanceAdjustment(
    String userId,
    String transactionId,
    Transaction updatedTransaction,
  ) async {
    // Fetch the original transaction to compare changes
    final originalTransaction = await getTransactionById(userId, transactionId);
    if (originalTransaction == null) {
      throw ArgumentError('Transaction not found: $transactionId');
    }

    // Validate that the updated transaction belongs to the same user
    if (updatedTransaction.userId != userId) {
      throw ArgumentError('Transaction must belong to the specified user');
    }

    // Calculate balance adjustments needed
    final balanceAdjustments = await _calculateBalanceAdjustments(
      originalTransaction,
      updatedTransaction,
    );

    // Validate all referenced accounts exist and belong to user
    await _validateAccountsForUpdate(userId, balanceAdjustments);

    // Perform atomic update using Firestore transaction
    await _firestoreService.runTransaction((firestoreTransaction) async {
      final now = DateTime.now();

      // PHASE 1: ALL READS FIRST (required by Firestore transaction constraints)

      // Read all account documents that will be updated
      final accountsToUpdate = <String, Account>{};
      for (final adjustment in balanceAdjustments) {
        final accountRef = _userAccountsCollection(
          userId,
        ).doc(adjustment.accountId);

        final accountSnapshot = await firestoreTransaction.get(accountRef);
        if (!accountSnapshot.exists) {
          throw ArgumentError('Account not found: ${adjustment.accountId}');
        }

        final accountData =
            accountSnapshot.data() as Map<String, dynamic>? ?? {};
        final currentAccount = Account.fromJson(accountData);
        accountsToUpdate[adjustment.accountId] = currentAccount;
      }

      // PHASE 2: ALL WRITES SECOND

      // Update budgets for transaction changes
      await _budgetTransactionService.updateBudgetsForTransactionUpdate(
        originalTransaction,
        updatedTransaction,
        firestoreTransaction,
      );

      // Update the transaction document
      final transactionRef = _transactionDoc(userId, transactionId);
      final updatedTransactionData = updatedTransaction.copyWith(
        id: transactionId,
        updatedAt: now,
      );
      firestoreTransaction.update(
        transactionRef,
        updatedTransactionData.toJson(),
      );

      // Apply balance adjustments to affected accounts
      for (final adjustment in balanceAdjustments) {
        final currentAccount = accountsToUpdate[adjustment.accountId]!;

        // Calculate new balance
        final newBalanceCents =
            currentAccount.currentBalanceCents + adjustment.adjustmentCents;

        // Update account with new balance
        final updatedAccount = currentAccount.copyWith(
          currentBalanceCents: newBalanceCents,
          updatedAt: now,
        );

        final accountRef = _userAccountsCollection(
          userId,
        ).doc(adjustment.accountId);
        firestoreTransaction.update(accountRef, updatedAccount.toJson());
      }
    });
  }

  @override
  Future<void> updateTransactionStatus(
    String transactionId,
    TransactionStatus status,
  ) async {
    // Note: This requires userId context
    throw UnimplementedError(
      'Use updateTransactionStatusForUser(userId, transactionId, status) instead',
    );
  }

  /// Update transaction status with user context
  Future<void> updateTransactionStatusForUser(
    String userId,
    String transactionId,
    TransactionStatus status,
  ) async {
    await _transactionDoc(userId, transactionId).update({
      'status': status.name,
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  @override
  Future<void> deleteTransaction(String userId, String transactionId) async {
    // Fetch the original transaction to understand its impact
    final originalTransaction = await getTransactionById(userId, transactionId);
    if (originalTransaction == null) {
      throw ArgumentError('Transaction not found: $transactionId');
    }

    // Validate that the transaction belongs to the specified user
    if (originalTransaction.userId != userId) {
      throw ArgumentError('Transaction does not belong to the specified user');
    }

    // Calculate reverse balance adjustments (opposite of what the transaction did)
    final balanceAdjustments = _calculateReverseBalanceAdjustments(
      originalTransaction,
    );

    // Validate all referenced accounts exist and belong to user
    await _validateAccountsForUpdate(userId, balanceAdjustments);

    // Perform atomic deletion using Firestore transaction
    await _firestoreService.runTransaction((firestoreTransaction) async {
      final now = DateTime.now();

      // PHASE 1: ALL READS FIRST (required by Firestore transaction constraints)
      final accountsToUpdate = <String, Account>{};

      for (final adjustment in balanceAdjustments) {
        final accountRef = _userAccountsCollection(
          userId,
        ).doc(adjustment.accountId);
        final accountSnapshot = await firestoreTransaction.get(accountRef);

        if (!accountSnapshot.exists) {
          throw ArgumentError('Account not found: ${adjustment.accountId}');
        }

        final accountData =
            accountSnapshot.data() as Map<String, dynamic>? ?? {};
        final currentAccount = Account.fromJson(accountData);
        accountsToUpdate[adjustment.accountId] = currentAccount;
      }

      // PHASE 2: ALL WRITES SECOND

      // Revert budget updates for this transaction
      await _budgetTransactionService.revertBudgetsForTransaction(
        originalTransaction,
        firestoreTransaction,
      );

      // Delete the transaction document
      final transactionRef = _userTransactionsCollection(
        userId,
      ).doc(transactionId);
      firestoreTransaction.delete(transactionRef);

      // Apply reverse balance adjustments to affected accounts
      for (final adjustment in balanceAdjustments) {
        final currentAccount = accountsToUpdate[adjustment.accountId]!;

        // Calculate new balance (reverse the original transaction's effect)
        final newBalanceCents =
            currentAccount.currentBalanceCents + adjustment.adjustmentCents;

        // Update account with new balance
        final updatedAccount = currentAccount.copyWith(
          currentBalanceCents: newBalanceCents,
          updatedAt: now,
        );

        final accountRef = _userAccountsCollection(
          userId,
        ).doc(adjustment.accountId);
        firestoreTransaction.update(accountRef, updatedAccount.toJson());
      }
    });
  }

  /// Delete transaction with user context
  Future<void> deleteTransactionForUser(
    String userId,
    String transactionId,
  ) async {
    await _transactionDoc(userId, transactionId).delete();
  }

  @override
  Future<Map<String, dynamic>> getTransactionStats(String userId) async {
    final transactions = await getTransactionsByUserId(userId);

    final stats = <String, dynamic>{
      'totalTransactions': transactions.length,
      'totalIncome': 0,
      'totalExpenses': 0,
      'totalTransfers': 0,
      'averageTransactionAmount': 0,
      'largestTransaction': 0,
      'smallestTransaction': transactions.isNotEmpty
          ? transactions.first.amountCents
          : 0,
      'transactionsByType': <String, int>{},
      'transactionsByStatus': <String, int>{},
    };

    if (transactions.isEmpty) return stats;

    var totalAmount = 0;
    var largestAmount = 0;
    var smallestAmount = transactions.first.amountCents;

    final typeCount = <String, int>{};
    final statusCount = <String, int>{};

    for (final transaction in transactions) {
      final amount = transaction.amountCents;
      totalAmount += amount;

      if (amount > largestAmount) largestAmount = amount;
      if (amount < smallestAmount) smallestAmount = amount;

      // Count by type
      switch (transaction.type) {
        case TransactionType.income:
          stats['totalIncome'] = (stats['totalIncome'] as int) + amount;
        case TransactionType.expense:
          stats['totalExpenses'] = (stats['totalExpenses'] as int) + amount;
        case TransactionType.transfer:
          stats['totalTransfers'] = (stats['totalTransfers'] as int) + amount;
      }

      typeCount[transaction.type.name] =
          (typeCount[transaction.type.name] ?? 0) + 1;
      statusCount[transaction.status.name] =
          (statusCount[transaction.status.name] ?? 0) + 1;
    }

    stats['averageTransactionAmount'] = totalAmount ~/ transactions.length;
    stats['largestTransaction'] = largestAmount;
    stats['smallestTransaction'] = smallestAmount;
    stats['transactionsByType'] = typeCount;
    stats['transactionsByStatus'] = statusCount;

    return stats;
  }

  @override
  Future<int> calculateAccountBalance(String accountId) async {
    throw UnimplementedError(
      'Use calculateAccountBalanceForUser(userId, accountId) instead',
    );
  }

  /// Calculate account balance with user context

  Future<int> calculateAccountBalanceForUser(
    String userId,
    String accountId,
  ) async {
    final transactions = await getTransactionsByAccountIdForUser(
      userId,
      accountId,
    );

    var balance = 0;
    for (final transaction in transactions) {
      if (transaction.status != TransactionStatus.completed) continue;

      switch (transaction.type) {
        case TransactionType.income:
          if (transaction.toAccountId == accountId) {
            balance += transaction.amountCents;
          }
        case TransactionType.expense:
          if (transaction.fromAccountId == accountId) {
            balance -= transaction.amountCents;
          }
        case TransactionType.transfer:
          if (transaction.fromAccountId == accountId) {
            balance -= transaction.amountCents;
          } else if (transaction.toAccountId == accountId) {
            balance += transaction.amountCents;
          }
      }
    }

    return balance;
  }

  @override
  Future<Map<String, int>> getSpendingByCategory(
    String userId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final transactions = await getTransactionsByDateRange(
      userId,
      startDate,
      endDate,
    );
    final spending = <String, int>{};

    for (final transaction in transactions) {
      if (transaction.type == TransactionType.expense &&
          transaction.status == TransactionStatus.completed &&
          transaction.categoryId != null) {
        spending[transaction.categoryId!] =
            (spending[transaction.categoryId!] ?? 0) + transaction.amountCents;
      }
    }

    return spending;
  }

  @override
  Future<Map<String, int>> getIncomeExpenseSummary(
    String userId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final transactions = await getTransactionsByDateRange(
      userId,
      startDate,
      endDate,
    );

    var totalIncome = 0;
    var totalExpenses = 0;

    for (final transaction in transactions) {
      if (transaction.status != TransactionStatus.completed) continue;

      switch (transaction.type) {
        case TransactionType.income:
          totalIncome += transaction.amountCents;
        case TransactionType.expense:
          totalExpenses += transaction.amountCents;
        case TransactionType.transfer:
        // Transfers don't affect income/expense totals
      }
    }

    return {
      'income': totalIncome,
      'expenses': totalExpenses,
      'netIncome': totalIncome - totalExpenses,
    };
  }

  @override
  Future<List<Transaction>> searchTransactions(
    String userId,
    String query,
  ) async {
    final allTransactions = await getTransactionsByUserId(userId);
    final lowercaseQuery = query.toLowerCase();

    return allTransactions.where((Transaction transaction) {
      return (transaction.description?.toLowerCase().contains(lowercaseQuery) ??
              false) ||
          (transaction.notes?.toLowerCase().contains(lowercaseQuery) ??
              false) ||
          transaction.tagIds.any(
            (tag) => tag.toLowerCase().contains(lowercaseQuery),
          );
    }).toList();
  }

  @override
  Future<List<Transaction>> getRecentTransactions(
    String userId, {
    int limit = 10,
  }) async {
    final snapshot = await _userTransactionsCollection(
      userId,
    ).orderBy('transactionDate', descending: true).limit(limit).get();

    return snapshot.docs
        .map(
          (doc) =>
              Transaction.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<List<Transaction>> getLargestTransactions(
    String userId, {
    int limit = 10,
  }) async {
    final snapshot = await _userTransactionsCollection(
      userId,
    ).orderBy('amountCents', descending: true).limit(limit).get();

    return snapshot.docs
        .map(
          (doc) =>
              Transaction.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Stream<List<Transaction>> watchUserTransactions(String userId) {
    return _userTransactionsCollection(
      userId,
    ).orderBy('transactionDate', descending: true).snapshots().map((snapshot) {
      return snapshot.docs
          .map(
            (doc) =>
                Transaction.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
          )
          .toList();
    });
  }

  @override
  Stream<List<Transaction>> watchAccountTransactions(String accountId) {
    throw UnimplementedError(
      'Use watchAccountTransactionsForUser(userId, accountId) instead',
    );
  }

  /// Watch account transactions with user context

  Stream<List<Transaction>> watchAccountTransactionsForUser(
    String userId,
    String accountId,
  ) {
    return _userTransactionsCollection(userId)
        .where(
          Filter.or(
            Filter('fromAccountId', isEqualTo: accountId),
            Filter('toAccountId', isEqualTo: accountId),
          ),
        )
        .orderBy('transactionDate', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map(
                (doc) => Transaction.fromJson(
                  doc.data() as Map<String, dynamic>? ?? {},
                ),
              )
              .toList();
        });
  }

  @override
  Stream<Transaction?> watchTransaction(String transactionId) {
    throw UnimplementedError(
      'Use watchTransactionForUser(userId, transactionId) instead',
    );
  }

  /// Watch transaction with user context

  Stream<Transaction?> watchTransactionForUser(
    String userId,
    String transactionId,
  ) {
    return _transactionDoc(userId, transactionId).snapshots().map((doc) {
      if (!doc.exists) return null;
      final data = doc.data() as Map<String, dynamic>? ?? {};
      return Transaction.fromJson(data);
    });
  }

  @override
  Future<bool> validateTransaction(Transaction transaction) async {
    // Basic validation rules
    if (transaction.amountCents <= 0) return false;
    if (transaction.userId.isEmpty) return false;

    // Type-specific validation
    switch (transaction.type) {
      case TransactionType.income:
        if (transaction.toAccountId == null) return false;
        // Validate destination account exists and belongs to user
        if (!await _validateAccountExists(
          transaction.userId,
          transaction.toAccountId!,
        )) {
          return false;
        }
      case TransactionType.expense:
        if (transaction.fromAccountId == null) return false;
        // Validate source account exists and belongs to user
        if (!await _validateAccountExists(
          transaction.userId,
          transaction.fromAccountId!,
        )) {
          return false;
        }
      case TransactionType.transfer:
        if (transaction.fromAccountId == null ||
            transaction.toAccountId == null) {
          return false;
        }
        if (transaction.fromAccountId == transaction.toAccountId) return false;
        // Validate both accounts exist and belong to user
        if (!await _validateAccountExists(
              transaction.userId,
              transaction.fromAccountId!,
            ) ||
            !await _validateAccountExists(
              transaction.userId,
              transaction.toAccountId!,
            )) {
          return false;
        }
    }

    return true;
  }

  /// Get reference to user's accounts collection
  CollectionReference _userAccountsCollection(String userId) =>
      _firestoreService.collection('users/$userId/accounts');

  /// Calculate reverse balance adjustments needed when deleting a transaction
  /// This reverses the effect of the original transaction on account balances
  List<BalanceAdjustment> _calculateReverseBalanceAdjustments(
    Transaction transaction,
  ) {
    final adjustments = <BalanceAdjustment>[];

    switch (transaction.type) {
      case TransactionType.income:
        // Income originally added money to toAccountId
        // Deletion should subtract it back
        if (transaction.toAccountId != null) {
          adjustments.add(
            BalanceAdjustment(
              accountId: transaction.toAccountId!,
              adjustmentCents: -transaction.amountCents, // Subtract the amount
            ),
          );
        }

      case TransactionType.expense:
        // Expense originally subtracted money from fromAccountId
        // Deletion should add it back
        if (transaction.fromAccountId != null) {
          adjustments.add(
            BalanceAdjustment(
              accountId: transaction.fromAccountId!,
              adjustmentCents: transaction.amountCents, // Add the amount back
            ),
          );
        }

      case TransactionType.transfer:
        // Transfer originally subtracted from fromAccountId and added to toAccountId
        // Deletion should add back to fromAccountId and subtract from toAccountId
        if (transaction.fromAccountId != null) {
          adjustments.add(
            BalanceAdjustment(
              accountId: transaction.fromAccountId!,
              adjustmentCents: transaction.amountCents, // Add back to source
            ),
          );
        }
        if (transaction.toAccountId != null) {
          adjustments.add(
            BalanceAdjustment(
              accountId: transaction.toAccountId!,
              adjustmentCents:
                  -transaction.amountCents, // Subtract from destination
            ),
          );
        }
    }

    return adjustments;
  }

  /// Calculate balance adjustments needed when updating a transaction
  Future<List<BalanceAdjustment>> _calculateBalanceAdjustments(
    Transaction original,
    Transaction updated,
  ) async {
    final adjustments = <String, int>{}; // accountId -> net adjustment

    // Reverse the impact of the original transaction
    switch (original.type) {
      case TransactionType.income:
        if (original.toAccountId != null) {
          adjustments[original.toAccountId!] =
              (adjustments[original.toAccountId!] ?? 0) - original.amountCents;
        }
      case TransactionType.expense:
        if (original.fromAccountId != null) {
          adjustments[original.fromAccountId!] =
              (adjustments[original.fromAccountId!] ?? 0) +
              original.amountCents;
        }
      case TransactionType.transfer:
        if (original.fromAccountId != null) {
          adjustments[original.fromAccountId!] =
              (adjustments[original.fromAccountId!] ?? 0) +
              original.amountCents;
        }
        if (original.toAccountId != null) {
          adjustments[original.toAccountId!] =
              (adjustments[original.toAccountId!] ?? 0) - original.amountCents;
        }
    }

    // Apply the impact of the updated transaction
    switch (updated.type) {
      case TransactionType.income:
        if (updated.toAccountId != null) {
          adjustments[updated.toAccountId!] =
              (adjustments[updated.toAccountId!] ?? 0) + updated.amountCents;
        }
      case TransactionType.expense:
        if (updated.fromAccountId != null) {
          adjustments[updated.fromAccountId!] =
              (adjustments[updated.fromAccountId!] ?? 0) - updated.amountCents;
        }
      case TransactionType.transfer:
        if (updated.fromAccountId != null) {
          adjustments[updated.fromAccountId!] =
              (adjustments[updated.fromAccountId!] ?? 0) - updated.amountCents;
        }
        if (updated.toAccountId != null) {
          adjustments[updated.toAccountId!] =
              (adjustments[updated.toAccountId!] ?? 0) + updated.amountCents;
        }
    }

    // Convert to BalanceAdjustment objects, filtering out zero adjustments
    return adjustments.entries
        .where((entry) => entry.value != 0)
        .map(
          (entry) => BalanceAdjustment(
            accountId: entry.key,
            adjustmentCents: entry.value,
          ),
        )
        .toList();
  }

  /// Validate that all accounts referenced in balance adjustments exist and belong to user
  Future<void> _validateAccountsForUpdate(
    String userId,
    List<BalanceAdjustment> adjustments,
  ) async {
    for (final adjustment in adjustments) {
      if (!await _validateAccountExists(userId, adjustment.accountId)) {
        throw ArgumentError(
          'Account does not exist or does not belong to user: ${adjustment.accountId}',
        );
      }
    }
  }

  /// Helper method to validate account exists and belongs to user
  Future<bool> _validateAccountExists(String userId, String accountId) async {
    try {
      final accountDoc = await _firestoreService
          .collection('users')
          .doc(userId)
          .collection('accounts')
          .doc(accountId)
          .get();
      return accountDoc.exists;
    } on Exception {
      return false;
    }
  }

  @override
  Future<List<String>> bulkCreateTransactions(
    List<Transaction> transactions,
  ) async {
    if (transactions.isEmpty) return [];

    // Validate all transactions first
    for (final transaction in transactions) {
      if (!await validateTransaction(transaction)) {
        throw ArgumentError('Invalid transaction: ${transaction.id}');
      }
    }

    await batchCreate(transactions);
    return transactions.map((Transaction t) => t.id).toList();
  }

  @override
  Future<List<Transaction>> getTransactionsReferencingAccount(
    String accountId,
  ) async {
    throw UnimplementedError(
      'Use getTransactionsReferencingAccountForUser(userId, accountId) instead',
    );
  }

  /// Get transactions referencing account with user context

  Future<List<Transaction>> getTransactionsReferencingAccountForUser(
    String userId,
    String accountId,
  ) async {
    return getTransactionsByAccountIdForUser(userId, accountId);
  }

  @override
  Future<List<Transaction>> getTransactionsReferencingCategory(
    String categoryId,
  ) async {
    throw UnimplementedError(
      'Use getTransactionsReferencingCategoryForUser(userId, categoryId) instead',
    );
  }

  /// Get transactions referencing category with user context

  Future<List<Transaction>> getTransactionsReferencingCategoryForUser(
    String userId,
    String categoryId,
  ) async {
    return getTransactionsByCategory(userId, categoryId);
  }

  @override
  Future<void> reassignTransactionsToAccount(
    String oldAccountId,
    String newAccountId,
  ) async {
    throw UnimplementedError(
      'Use reassignTransactionsToAccountForUser(userId, oldAccountId, newAccountId) instead',
    );
  }

  /// Reassign transactions to account with user context

  Future<void> reassignTransactionsToAccountForUser(
    String userId,
    String oldAccountId,
    String newAccountId,
  ) async {
    final transactions = await getTransactionsByAccountIdForUser(
      userId,
      oldAccountId,
    );

    if (transactions.isEmpty) return;

    final batch = _firestoreService.batch();

    for (final transaction in transactions) {
      final updatedTransaction = transaction.copyWith(
        fromAccountId: transaction.fromAccountId == oldAccountId
            ? newAccountId
            : transaction.fromAccountId,
        toAccountId: transaction.toAccountId == oldAccountId
            ? newAccountId
            : transaction.toAccountId,
        updatedAt: DateTime.now(),
      );

      batch.update(
        _transactionDoc(userId, transaction.id),
        updatedTransaction.toJson(),
      );
    }

    await batch.commit();
  }

  @override
  Future<void> reassignTransactionsToCategory(
    String oldCategoryId,
    String newCategoryId,
  ) async {
    throw UnimplementedError(
      'Use reassignTransactionsToCategoryForUser(userId, oldCategoryId, newCategoryId) instead',
    );
  }

  /// Reassign transactions to category with user context
  @override
  Future<void> reassignTransactionsToCategoryForUser(
    String userId,
    String oldCategoryId,
    String newCategoryId,
  ) async {
    final transactions = await getTransactionsByCategory(userId, oldCategoryId);

    if (transactions.isEmpty) return;

    final batch = _firestoreService.batch();

    for (final transaction in transactions) {
      final updatedTransaction = transaction.copyWith(
        categoryId: newCategoryId,
        updatedAt: DateTime.now(),
      );

      batch.update(
        _transactionDoc(userId, transaction.id),
        updatedTransaction.toJson(),
      );
    }

    await batch.commit();
  }

  @override
  Stream<List<Transaction>> watchTransactionsByMonthAndCategories({
    required String userId,
    required DateTime month,
    List<String>? categoryIds,
  }) {
    // Calculate month boundaries
    final monthStart = DateTime(month.year, month.month, 1);
    final monthEnd = DateTime(
      month.year,
      month.month + 1,
      1,
    ).subtract(const Duration(days: 1));

    var query = _userTransactionsCollection(userId)
        .where('transactionDate', isGreaterThanOrEqualTo: monthStart)
        .where('transactionDate', isLessThanOrEqualTo: monthEnd)
        .orderBy('transactionDate', descending: true);

    // If specific categories are requested, filter by them
    if (categoryIds != null && categoryIds.isNotEmpty) {
      query = query.where('categoryId', whereIn: categoryIds);
    }

    return query.snapshots().map((snapshot) {
      return snapshot.docs
          .map(
            (doc) => Transaction.fromJson({
              ...doc.data() as Map<String, dynamic>? ?? {},
              'id': doc.id,
            }),
          )
          .toList();
    });
  }
}
