import 'package:budapp/data/models/goal.dart';
import 'package:budapp/data/repositories/interfaces/goal_repository.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Implementation of IGoalRepository using Firestore
///
/// Provides concrete implementation for goal-related data operations
/// using Firestore as the backend. Follows the established repository
/// pattern used throughout BudApp.
class GoalRepositoryImpl implements IGoalRepository {
  GoalRepositoryImpl(this._firestoreService, this._firebaseAuth);
  static const String _collectionName = 'goals';

  final FirestoreService _firestoreService;
  final FirebaseAuth _firebaseAuth;

  /// Current user ID from Firebase Auth
  String get _currentUserId {
    final user = _firebaseAuth.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }
    return user.uid;
  }

  /// Get reference to user's goals collection
  CollectionReference _userGoalsCollection(String userId) =>
      _firestoreService.collection('users/$userId/$_collectionName');

  /// Get reference to a specific goal document
  DocumentReference _goalDoc(String userId, String goalId) =>
      _userGoalsCollection(userId).doc(goalId);

  @override
  Future<String> create(Goal entity) async {
    await _goalDoc(entity.userId, entity.id).set(entity.toFirestore());
    return entity.id;
  }

  @override
  Future<void> update(String id, Goal entity) async {
    final updatedEntity = entity.copyWith(id: id, updatedAt: DateTime.now());
    await _goalDoc(entity.userId, id).update(updatedEntity.toFirestore());
  }

  @override
  Future<Goal?> getGoalById(String id) async {
    return getGoalByIdForUser(_currentUserId, id);
  }

  @override
  Future<Goal?> getGoalByIdForUser(String userId, String goalId) async {
    final doc = await _goalDoc(userId, goalId).get();
    if (!doc.exists) return null;

    return Goal.fromFirestore(doc as DocumentSnapshot<Map<String, dynamic>>);
  }

  @override
  Future<List<Goal>> getGoalsByUserId(String userId) async {
    final snapshot = await _userGoalsCollection(
      userId,
    ).orderBy('createdAt', descending: true).get();

    return snapshot.docs
        .map(
          (doc) =>
              Goal.fromFirestore(doc as DocumentSnapshot<Map<String, dynamic>>),
        )
        .toList();
  }

  @override
  Future<List<Goal>> getActiveGoalsByUserId(String userId) async {
    final snapshot = await _userGoalsCollection(userId)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .get();

    return snapshot.docs
        .map(
          (doc) =>
              Goal.fromFirestore(doc as DocumentSnapshot<Map<String, dynamic>>),
        )
        .toList();
  }

  @override
  Future<List<Goal>> getActiveGoals() async {
    return getActiveGoalsByUserId(_currentUserId);
  }

  @override
  Future<List<Goal>> getGoalsByStatus(String userId, GoalStatus status) async {
    final snapshot = await _userGoalsCollection(userId)
        .where('status', isEqualTo: status.name)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .get();

    return snapshot.docs
        .map(
          (doc) =>
              Goal.fromFirestore(doc as DocumentSnapshot<Map<String, dynamic>>),
        )
        .toList();
  }

  @override
  Future<String> createGoal(Goal goal) async {
    // Ensure the goal has the current user ID
    final goalWithUserId = goal.copyWith(userId: _currentUserId);

    // Generate a new document ID
    final docRef = _userGoalsCollection(_currentUserId).doc();

    final goalWithId = goalWithUserId.copyWith(
      id: docRef.id,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await docRef.set(goalWithId.toFirestore());
    return goalWithId.id;
  }

  @override
  Future<void> updateGoal(String goalId, Goal goal) async {
    final updatedGoal = goal.copyWith(
      id: goalId,
      userId: _currentUserId,
      updatedAt: DateTime.now(),
    );

    await _goalDoc(_currentUserId, goalId).update(updatedGoal.toFirestore());
  }

  @override
  Future<void> deactivateGoal(String goalId) async {
    await _goalDoc(_currentUserId, goalId).update({
      'isActive': false,
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  @override
  Future<void> deleteGoal(String goalId) async {
    await _goalDoc(_currentUserId, goalId).delete();
  }

  @override
  Future<void> reactivateGoal(String goalId) async {
    await _goalDoc(
      _currentUserId,
      goalId,
    ).update({'isActive': true, 'updatedAt': DateTime.now().toIso8601String()});
  }

  @override
  Future<void> updateGoalProgress(
    String goalId,
    int newCurrentAmountCents,
  ) async {
    final updates = <String, dynamic>{
      'currentAmountCents': newCurrentAmountCents,
      'updatedAt': DateTime.now().toIso8601String(),
    };

    // Check if goal should be marked as completed
    final goal = await getGoalById(goalId);
    if (goal != null && newCurrentAmountCents >= goal.targetAmountCents) {
      updates['isCompleted'] = true;
      updates['status'] = GoalStatus.completed.name;
    } else if (goal != null &&
        goal.isCompleted &&
        newCurrentAmountCents < goal.targetAmountCents) {
      // If goal was completed but progress decreased, mark as active again
      updates['isCompleted'] = false;
      updates['status'] = GoalStatus.active.name;
    }

    await _goalDoc(_currentUserId, goalId).update(updates);
  }

  @override
  Future<void> markGoalAsCompleted(String goalId) async {
    await _goalDoc(_currentUserId, goalId).update({
      'isCompleted': true,
      'status': GoalStatus.completed.name,
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  @override
  Future<void> markGoalAsActive(String goalId) async {
    await _goalDoc(_currentUserId, goalId).update({
      'isCompleted': false,
      'status': GoalStatus.active.name,
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  @override
  Future<List<Goal>> searchGoalsByName(String userId, String query) async {
    // Note: Firestore doesn't support full-text search natively
    // This is a simple implementation that gets all goals and filters client-side
    // For production, consider using Algolia or similar service for better search
    final allGoals = await getActiveGoalsByUserId(userId);
    final lowercaseQuery = query.toLowerCase();

    return allGoals.where((goal) {
      return goal.name.toLowerCase().contains(lowercaseQuery) ||
          (goal.description?.toLowerCase().contains(lowercaseQuery) ?? false);
    }).toList();
  }

  @override
  Future<Map<String, dynamic>> getGoalStats(String goalId) async {
    final goal = await getGoalById(goalId);
    if (goal == null) {
      throw Exception('Goal not found: $goalId');
    }

    return {
      'progressPercentage': goal.progressPercentage,
      'remainingAmountCents': goal.remainingAmountCents,
      'isAchieved': goal.isAchieved,
      'daysRemaining': goal.daysRemaining,
      'requiredDailySavingsCents': goal.requiredDailySavingsCents,
    };
  }

  @override
  Future<Map<String, dynamic>> getUserGoalSummary(String userId) async {
    final goals = await getGoalsByUserId(userId);
    final activeGoals = goals.where((g) => g.isActive).toList();
    final completedGoals = goals.where((g) => g.isCompleted).toList();

    final totalTargetAmount = activeGoals.fold<int>(
      0,
      (total, goal) => total + goal.targetAmountCents,
    );

    final totalCurrentAmount = activeGoals.fold<int>(
      0,
      (total, goal) => total + goal.currentAmountCents,
    );

    return {
      'totalGoals': goals.length,
      'activeGoals': activeGoals.length,
      'completedGoals': completedGoals.length,
      'totalTargetAmountCents': totalTargetAmount,
      'totalCurrentAmountCents': totalCurrentAmount,
      'overallProgressPercentage': totalTargetAmount > 0
          ? (totalCurrentAmount / totalTargetAmount * 100).round()
          : 0,
    };
  }

  @override
  Stream<List<Goal>> watchUserGoals(String userId) {
    return _userGoalsCollection(userId)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map(
                (doc) => Goal.fromFirestore(
                  doc as DocumentSnapshot<Map<String, dynamic>>,
                ),
              )
              .toList(),
        );
  }

  @override
  Stream<List<Goal>> watchAllUserGoals(String userId) {
    return _userGoalsCollection(userId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map(
                (doc) => Goal.fromFirestore(
                  doc as DocumentSnapshot<Map<String, dynamic>>,
                ),
              )
              .toList(),
        );
  }

  @override
  Stream<Goal?> watchGoal(String goalId) {
    return watchGoalForUser(_currentUserId, goalId);
  }

  @override
  Stream<Goal?> watchGoalForUser(String userId, String goalId) {
    return _goalDoc(userId, goalId).snapshots().map((doc) {
      if (!doc.exists) return null;
      return Goal.fromFirestore(doc as DocumentSnapshot<Map<String, dynamic>>);
    });
  }

  @override
  Future<bool> validateGoal(Goal goal) async {
    // Use the Goal model's built-in validation
    final validationErrors = goal.validate();
    if (validationErrors.isNotEmpty) {
      throw Exception('Goal validation failed: ${validationErrors.join(', ')}');
    }
    return true;
  }

  @override
  Future<bool> isGoalNameUnique(
    String userId,
    String name, {
    String? excludeGoalId,
  }) async {
    final query = _userGoalsCollection(
      userId,
    ).where('name', isEqualTo: name).where('isActive', isEqualTo: true);

    final snapshot = await query.get();

    // If no goals found, name is unique
    if (snapshot.docs.isEmpty) return true;

    // If excluding a specific goal ID, check if any other goals have this name
    if (excludeGoalId != null) {
      return !snapshot.docs.any((doc) => doc.id != excludeGoalId);
    }

    // Name is not unique
    return false;
  }

  @override
  Future<List<Goal>> getDeletableGoals(String userId) async {
    // For now, return all goals as deletable
    // In the future, this could check for contributions or other dependencies
    return getActiveGoalsByUserId(userId);
  }

  @override
  Future<List<Goal>> getCompletedGoals(String userId) async {
    return getGoalsByStatus(userId, GoalStatus.completed);
  }

  @override
  Future<List<Goal>> getGoalsNearingDeadline(
    String userId,
    int daysAhead,
  ) async {
    final now = DateTime.now();
    final deadline = now.add(Duration(days: daysAhead));

    final allGoals = await getActiveGoalsByUserId(userId);

    return allGoals.where((goal) {
      if (goal.targetDate == null) return false;
      return goal.targetDate!.isAfter(now) &&
          goal.targetDate!.isBefore(deadline);
    }).toList();
  }

  @override
  Future<List<Goal>> getGoalsByDateRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final snapshot = await _userGoalsCollection(userId)
        .where(
          'createdAt',
          isGreaterThanOrEqualTo: Timestamp.fromDate(startDate),
        )
        .where('createdAt', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
        .orderBy('createdAt', descending: true)
        .get();

    return snapshot.docs
        .map(
          (doc) =>
              Goal.fromFirestore(doc as DocumentSnapshot<Map<String, dynamic>>),
        )
        .toList();
  }
}
