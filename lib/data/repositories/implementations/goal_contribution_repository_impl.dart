import 'package:budapp/data/models/goal_contribution.dart';
import 'package:budapp/data/repositories/interfaces/i_goal_contribution_repository.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Implementation of IGoalContributionRepository using Firestore
///
/// Provides concrete implementation for goal contribution-related data operations
/// using Firestore as the backend. Follows the established repository pattern
/// used throughout BudApp with subcollection management for contributions.
class GoalContributionRepositoryImpl implements IGoalContributionRepository {
  GoalContributionRepositoryImpl(this._firestoreService, this._firebaseAuth);

  static const String _goalsCollectionName = 'goals';
  static const String _contributionsCollectionName = 'contributions';

  final FirestoreService _firestoreService;
  final FirebaseAuth _firebaseAuth;

  /// Current user ID from Firebase Auth
  String get _currentUserId {
    final user = _firebaseAuth.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }
    return user.uid;
  }

  /// Get reference to user's goals collection
  CollectionReference _userGoalsCollection(String userId) =>
      _firestoreService.collection('users/$userId/$_goalsCollectionName');

  /// Get reference to a specific goal document
  DocumentReference _goalDoc(String userId, String goalId) =>
      _userGoalsCollection(userId).doc(goalId);

  /// Get reference to goal's contributions subcollection
  CollectionReference _goalContributionsCollection(
    String userId,
    String goalId,
  ) => _goalDoc(userId, goalId).collection(_contributionsCollectionName);

  /// Get reference to a specific contribution document
  DocumentReference _contributionDoc(
    String userId,
    String goalId,
    String contributionId,
  ) => _goalContributionsCollection(userId, goalId).doc(contributionId);

  @override
  Future<String> createContribution(
    String goalId,
    GoalContribution contribution,
  ) async {
    final userId = _currentUserId;

    // Validate goal exists and belongs to user
    if (!await isValidGoalForContribution(goalId)) {
      throw Exception('Goal does not exist or does not belong to current user');
    }

    // Validate contribution
    if (!await validateContribution(goalId, contribution)) {
      throw Exception('Invalid contribution data');
    }

    // Generate ID and prepare contribution with user context
    final contributionId = _goalContributionsCollection(
      userId,
      goalId,
    ).doc().id;
    final contributionWithId = contribution.copyWith(
      id: contributionId,
      userId: userId,
      goalId: goalId,
      updatedAt: DateTime.now(),
    );

    // Use batch operation to atomically create contribution and update goal
    final batch = _firestoreService.batch()
      ..set(
        _contributionDoc(userId, goalId, contributionId),
        contributionWithId.toFirestore(),
      )
      ..update(_goalDoc(userId, goalId), {
        'currentAmountCents': FieldValue.increment(contribution.amountCents),
        'updatedAt': DateTime.now().toIso8601String(),
      });

    await batch.commit();
    return contributionId;
  }

  @override
  Future<void> updateContribution(
    String goalId,
    String contributionId,
    GoalContribution contribution,
  ) async {
    final userId = _currentUserId;

    // Validate goal exists and belongs to user
    if (!await isValidGoalForContribution(goalId)) {
      throw Exception('Goal does not exist or does not belong to current user');
    }

    // Validate contribution
    if (!await validateContribution(goalId, contribution)) {
      throw Exception('Invalid contribution data');
    }

    // Get the current contribution to calculate the amount difference
    final currentContribution = await getContributionById(
      goalId,
      contributionId,
    );
    if (currentContribution == null) {
      throw Exception('Contribution not found');
    }

    final updatedContribution = contribution.copyWith(
      id: contributionId,
      userId: userId,
      goalId: goalId,
      updatedAt: DateTime.now(),
    );

    // Calculate the difference in amount to update the goal
    final amountDifference =
        contribution.amountCents - currentContribution.amountCents;

    // Use batch operation to atomically update contribution and goal
    final batch = _firestoreService.batch()
      ..update(
        _contributionDoc(userId, goalId, contributionId),
        updatedContribution.toFirestore(),
      );

    // Update goal's currentAmountCents with the difference
    if (amountDifference != 0) {
      batch.update(_goalDoc(userId, goalId), {
        'currentAmountCents': FieldValue.increment(amountDifference),
        'updatedAt': DateTime.now().toIso8601String(),
      });
    }

    await batch.commit();
  }

  @override
  Future<void> deleteContribution(String goalId, String contributionId) async {
    final userId = _currentUserId;

    // Validate goal exists and belongs to user
    if (!await isValidGoalForContribution(goalId)) {
      throw Exception('Goal does not exist or does not belong to current user');
    }

    // Get the current contribution to get its amount for goal update
    final currentContribution = await getContributionById(
      goalId,
      contributionId,
    );
    if (currentContribution == null) {
      throw Exception('Contribution not found');
    }

    // Use batch operation to atomically soft-delete contribution and update goal
    final batch = _firestoreService.batch()
      ..update(_contributionDoc(userId, goalId, contributionId), {
        'isActive': false,
        'updatedAt': DateTime.now().toIso8601String(),
      });

    // Subtract the contribution amount from goal's currentAmountCents
    // Only if the contribution is currently active
    if (currentContribution.isActive) {
      batch.update(_goalDoc(userId, goalId), {
        'currentAmountCents': FieldValue.increment(
          -currentContribution.amountCents,
        ),
        'updatedAt': DateTime.now().toIso8601String(),
      });
    }

    await batch.commit();
  }

  @override
  Future<GoalContribution?> getContributionById(
    String goalId,
    String contributionId,
  ) async {
    final userId = _currentUserId;

    final doc = await _contributionDoc(userId, goalId, contributionId).get();
    if (!doc.exists) return null;

    return GoalContribution.fromFirestore(
      doc as DocumentSnapshot<Map<String, dynamic>>,
    );
  }

  @override
  Future<List<GoalContribution>> getContributionsForGoal(String goalId) async {
    final userId = _currentUserId;

    final query = _goalContributionsCollection(
      userId,
      goalId,
    ).orderBy('contributionDate', descending: true);

    final snapshot = await query.get();
    return snapshot.docs
        .map(
          (doc) => GoalContribution.fromFirestore(
            doc as DocumentSnapshot<Map<String, dynamic>>,
          ),
        )
        .toList();
  }

  @override
  Future<List<GoalContribution>> getActiveContributionsForGoal(
    String goalId,
  ) async {
    final userId = _currentUserId;

    final query = _goalContributionsCollection(userId, goalId)
        .where('isActive', isEqualTo: true)
        .orderBy('contributionDate', descending: true);

    final snapshot = await query.get();
    return snapshot.docs
        .map(
          (doc) => GoalContribution.fromFirestore(
            doc as DocumentSnapshot<Map<String, dynamic>>,
          ),
        )
        .toList();
  }

  @override
  Future<List<GoalContribution>> getAllUserContributions() async {
    final userId = _currentUserId;

    // Query all goals and their contributions
    final goalsSnapshot = await _userGoalsCollection(userId).get();
    final allContributions = <GoalContribution>[];

    for (final goalDoc in goalsSnapshot.docs) {
      final contributionsSnapshot = await _goalContributionsCollection(
        userId,
        goalDoc.id,
      ).orderBy('contributionDate', descending: true).get();

      final contributions = contributionsSnapshot.docs
          .map(
            (doc) => GoalContribution.fromFirestore(
              doc as DocumentSnapshot<Map<String, dynamic>>,
            ),
          )
          .toList();

      allContributions.addAll(contributions);
    }

    // Sort by contribution date (newest first)
    allContributions.sort((a, b) {
      final aDate =
          a.contributionDate ?? DateTime.fromMillisecondsSinceEpoch(0);
      final bDate =
          b.contributionDate ?? DateTime.fromMillisecondsSinceEpoch(0);
      return bDate.compareTo(aDate);
    });
    return allContributions;
  }

  @override
  Future<List<GoalContribution>> getActiveUserContributions() async {
    final userId = _currentUserId;

    // Query all goals and their active contributions
    final goalsSnapshot = await _userGoalsCollection(userId).get();
    final allContributions = <GoalContribution>[];

    for (final goalDoc in goalsSnapshot.docs) {
      final contributionsSnapshot =
          await _goalContributionsCollection(userId, goalDoc.id)
              .where('isActive', isEqualTo: true)
              .orderBy('contributionDate', descending: true)
              .get();

      final contributions = contributionsSnapshot.docs
          .map(
            (doc) => GoalContribution.fromFirestore(
              doc as DocumentSnapshot<Map<String, dynamic>>,
            ),
          )
          .toList();

      allContributions.addAll(contributions);
    }

    // Sort by contribution date (newest first)
    allContributions.sort((a, b) {
      final aDate =
          a.contributionDate ?? DateTime.fromMillisecondsSinceEpoch(0);
      final bDate =
          b.contributionDate ?? DateTime.fromMillisecondsSinceEpoch(0);
      return bDate.compareTo(aDate);
    });
    return allContributions;
  }

  @override
  Future<List<GoalContribution>> getContributionsByDateRange(
    String goalId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final userId = _currentUserId;

    final query = _goalContributionsCollection(userId, goalId)
        .where('contributionDate', isGreaterThanOrEqualTo: startDate)
        .where('contributionDate', isLessThanOrEqualTo: endDate)
        .orderBy('contributionDate', descending: true);

    final snapshot = await query.get();
    return snapshot.docs
        .map(
          (doc) => GoalContribution.fromFirestore(
            doc as DocumentSnapshot<Map<String, dynamic>>,
          ),
        )
        .toList();
  }

  @override
  Future<List<GoalContribution>> getRecentContributions(
    String goalId, {
    int limit = 10,
  }) async {
    final userId = _currentUserId;

    final query = _goalContributionsCollection(userId, goalId)
        .where('isActive', isEqualTo: true)
        .orderBy('contributionDate', descending: true)
        .limit(limit);

    final snapshot = await query.get();
    return snapshot.docs
        .map(
          (doc) => GoalContribution.fromFirestore(
            doc as DocumentSnapshot<Map<String, dynamic>>,
          ),
        )
        .toList();
  }

  @override
  Future<int> getTotalContributionAmountForGoal(String goalId) async {
    final userId = _currentUserId;

    final snapshot = await _goalContributionsCollection(
      userId,
      goalId,
    ).where('isActive', isEqualTo: true).get();

    var totalAmount = 0;
    for (final doc in snapshot.docs) {
      final contribution = GoalContribution.fromFirestore(
        doc as DocumentSnapshot<Map<String, dynamic>>,
      );
      totalAmount += contribution.amountCents;
    }

    return totalAmount;
  }

  @override
  Future<int> getContributionCountForGoal(String goalId) async {
    final userId = _currentUserId;

    final snapshot = await _goalContributionsCollection(
      userId,
      goalId,
    ).where('isActive', isEqualTo: true).get();

    return snapshot.docs.length;
  }

  @override
  Future<List<GoalContribution>> getTodaysContributions(String goalId) async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));

    return getContributionsByDateRange(goalId, today, tomorrow);
  }

  @override
  Future<List<GoalContribution>> getThisWeeksContributions(
    String goalId,
  ) async {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final weekStartDate = DateTime(
      weekStart.year,
      weekStart.month,
      weekStart.day,
    );
    final weekEnd = weekStartDate.add(const Duration(days: 7));

    return getContributionsByDateRange(goalId, weekStartDate, weekEnd);
  }

  @override
  Future<List<GoalContribution>> getThisMonthsContributions(
    String goalId,
  ) async {
    final now = DateTime.now();
    final monthStart = DateTime(now.year, now.month, 1);
    final monthEnd = DateTime(now.year, now.month + 1, 1);

    return getContributionsByDateRange(goalId, monthStart, monthEnd);
  }

  // Real-time streams

  @override
  Stream<List<GoalContribution>> watchContributionsForGoal(String goalId) {
    final userId = _currentUserId;

    return _goalContributionsCollection(userId, goalId)
        .orderBy('contributionDate', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map(
                (doc) => GoalContribution.fromFirestore(
                  doc as DocumentSnapshot<Map<String, dynamic>>,
                ),
              )
              .toList(),
        );
  }

  @override
  Stream<List<GoalContribution>> watchActiveContributionsForGoal(
    String goalId,
  ) {
    final userId = _currentUserId;

    return _goalContributionsCollection(userId, goalId)
        .where('isActive', isEqualTo: true)
        .orderBy('contributionDate', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map(
                (doc) => GoalContribution.fromFirestore(
                  doc as DocumentSnapshot<Map<String, dynamic>>,
                ),
              )
              .toList(),
        );
  }

  @override
  Stream<List<GoalContribution>> watchAllUserContributions() async* {
    // This is a complex stream that requires watching multiple subcollections
    // For now, we'll implement a polling approach that could be optimized later
    yield* Stream<List<GoalContribution>>.periodic(
      const Duration(seconds: 5),
      (_) => <GoalContribution>[],
    ).asyncMap((_) => getAllUserContributions());
  }

  @override
  Stream<GoalContribution?> watchContribution(
    String goalId,
    String contributionId,
  ) {
    final userId = _currentUserId;

    return _contributionDoc(userId, goalId, contributionId).snapshots().map((
      doc,
    ) {
      if (!doc.exists) return null;
      return GoalContribution.fromFirestore(
        doc as DocumentSnapshot<Map<String, dynamic>>,
      );
    });
  }

  // Validation and business logic

  @override
  Future<bool> validateContribution(
    String goalId,
    GoalContribution contribution,
  ) async {
    try {
      // Validate goal exists and belongs to user
      if (!await isValidGoalForContribution(goalId)) {
        return false;
      }

      // Use GoalContribution model validation
      final validationErrors = contribution.validate();
      return validationErrors.isEmpty;
    } on Exception {
      return false;
    }
  }

  @override
  Future<bool> isValidGoalForContribution(String goalId) async {
    try {
      final userId = _currentUserId;
      final goalDoc = await _goalDoc(userId, goalId).get();
      return goalDoc.exists;
    } on Exception {
      return false;
    }
  }

  @override
  Future<Map<String, dynamic>> getContributionStatistics(String goalId) async {
    final contributions = await getActiveContributionsForGoal(goalId);

    if (contributions.isEmpty) {
      return {
        'total_amount': 0,
        'contribution_count': 0,
        'average_amount': 0,
        'first_contribution_date': null,
        'last_contribution_date': null,
      };
    }

    final totalAmount = contributions.fold<int>(
      0,
      (accumulator, contribution) => accumulator + contribution.amountCents,
    );

    final sortedByDate = List<GoalContribution>.from(contributions)
      ..sort((a, b) {
        final aDate =
            a.contributionDate ?? DateTime.fromMillisecondsSinceEpoch(0);
        final bDate =
            b.contributionDate ?? DateTime.fromMillisecondsSinceEpoch(0);
        return aDate.compareTo(bDate);
      });

    return {
      'total_amount': totalAmount,
      'contribution_count': contributions.length,
      'average_amount': (totalAmount / contributions.length).round(),
      'first_contribution_date': sortedByDate.first.contributionDate,
      'last_contribution_date': sortedByDate.last.contributionDate,
    };
  }
}
