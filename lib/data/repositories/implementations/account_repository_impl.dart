import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/repositories/interfaces/account_repository.dart';
import 'package:budapp/services/cache_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:budapp/services/performance_rollout_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Concrete implementation of IAccountRepository using Firestore
///
/// This implementation provides account management functionality
/// using FirestoreService as the backend storage with performance optimizations.
class AccountRepositoryImpl implements IAccountRepository {
  AccountRepositoryImpl(
    this._firestoreService,
    this._firebaseAuth,
    this._cacheService, [
    this._performanceRolloutService,
  ]);
  final FirestoreService _firestoreService;
  final FirebaseAuth _firebaseAuth;
  final CacheService _cacheService;
  final PerformanceRolloutService? _performanceRolloutService;
  static const String _collectionName = 'accounts';

  // Performance tracking
  final Map<String, int> _operationCounts = {
    'reads': 0,
    'writes': 0,
    'cache_hits': 0,
    'cache_misses': 0,
  };

  /// Get current user ID from Firebase Auth
  String get _currentUserId {
    final user = _firebaseAuth.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }
    return user.uid;
  }

  /// Get reference to user's accounts collection
  CollectionReference _userAccountsCollection(String userId) =>
      _firestoreService.collection('users/$userId/$_collectionName');

  /// Get reference to a specific account document
  DocumentReference _accountDoc(String userId, String accountId) =>
      _userAccountsCollection(userId).doc(accountId);

  @override
  Future<String> create(Account entity) async {
    await _accountDoc(entity.userId, entity.id).set(entity.toJson());
    return entity.id;
  }

  /// Get account by ID with user context
  Future<Account?> getAccountById(String userId, String accountId) async {
    final doc = await _accountDoc(userId, accountId).get();
    if (!doc.exists) return null;

    final data = doc.data() as Map<String, dynamic>? ?? {};
    return Account.fromJson(data);
  }

  @override
  Future<void> update(String id, Account entity) async {
    final updatedEntity = entity.copyWith(
      id: id, // Ensure ID matches the document ID
      updatedAt: DateTime.now(),
    );
    await _accountDoc(entity.userId, id).update(updatedEntity.toJson());
  }

  /// Check if account exists with user context
  Future<bool> accountExists(String userId, String accountId) async {
    final doc = await _accountDoc(userId, accountId).get();
    return doc.exists;
  }

  /// Get paginated accounts for a user
  Future<List<Account>> getUserAccountsPaginated(
    String userId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    var query = _userAccountsCollection(
      userId,
    ).orderBy('createdAt', descending: true).limit(limit);

    if (startAfter != null) {
      query = query.startAfterDocument(startAfter);
    }

    final snapshot = await query.get();
    return snapshot.docs
        .map(
          (doc) => Account.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  Future<void> batchCreate(List<Account> entities) async {
    if (entities.isEmpty) return;

    final batch = _firestoreService.batch();
    final userId = entities.first.userId;

    for (final entity in entities) {
      if (entity.userId != userId) {
        throw ArgumentError('All accounts must belong to the same user');
      }
      batch.set(_accountDoc(userId, entity.id), entity.toJson());
    }

    await batch.commit();
  }

  Future<void> batchUpdate(Map<String, Account> entities) async {
    if (entities.isEmpty) return;

    final batch = _firestoreService.batch();
    final userId = entities.values.first.userId;

    for (final entry in entities.entries) {
      final updatedEntity = entry.value.copyWith(
        id: entry.key,
        updatedAt: DateTime.now(),
      );
      if (updatedEntity.userId != userId) {
        throw ArgumentError('All accounts must belong to the same user');
      }
      batch.update(_accountDoc(userId, entry.key), updatedEntity.toJson());
    }

    await batch.commit();
  }

  Future<void> batchDelete(List<String> ids) async {
    throw UnimplementedError(
      'Use batchDeleteUserAccounts(userId, accountIds) instead',
    );
  }

  /// Batch delete accounts for a user
  Future<void> batchDeleteUserAccounts(
    String userId,
    List<String> accountIds,
  ) async {
    if (accountIds.isEmpty) return;

    final batch = _firestoreService.batch();

    for (final id in accountIds) {
      batch.delete(_accountDoc(userId, id));
    }

    await batch.commit();
  }

  Future<List<Account>> query({
    Map<String, dynamic>? where,
    String? orderBy,
    bool descending = false,
    int? limit,
  }) async {
    throw UnimplementedError(
      'Use specific query methods with userId context instead',
    );
  }

  Future<int> count({Map<String, dynamic>? where}) async {
    throw UnimplementedError('Use getUserAccountCount(userId) instead');
  }

  /// Get account count for a user
  Future<int> getUserAccountCount(String userId) async {
    final snapshot = await _userAccountsCollection(userId).count().get();
    return snapshot.count ?? 0;
  }

  // IAccountRepository specific methods

  @override
  Future<List<Account>> getAccountsByUserId(String userId) async {
    final snapshot = await _userAccountsCollection(
      userId,
    ).orderBy('createdAt', descending: false).get();

    return snapshot.docs
        .map(
          (doc) => Account.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<List<Account>> getActiveAccountsByUserId(String userId) async {
    final snapshot = await _userAccountsCollection(userId)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: false)
        .get();

    return snapshot.docs
        .map(
          (doc) => Account.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<List<Account>> getAccountsByType(
    String userId,
    AccountType type,
  ) async {
    final snapshot = await _userAccountsCollection(userId)
        .where('type', isEqualTo: type.name)
        .orderBy('createdAt', descending: false)
        .get();

    return snapshot.docs
        .map(
          (doc) => Account.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<List<Account>> getAccountsByClassification(
    String userId,
    AccountClassification classification,
  ) async {
    final snapshot = await _userAccountsCollection(userId)
        .where('classification', isEqualTo: classification.name)
        .orderBy('createdAt', descending: false)
        .get();

    return snapshot.docs
        .map(
          (doc) => Account.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<Account?> getPrimaryAccount(String userId) async {
    final snapshot = await _userAccountsCollection(userId)
        .where('isPrimary', isEqualTo: true)
        .where('isActive', isEqualTo: true)
        .limit(1)
        .get();

    if (snapshot.docs.isEmpty) return null;

    final data = snapshot.docs.first.data() as Map<String, dynamic>? ?? {};
    return Account.fromJson(data);
  }

  @override
  Future<void> setPrimaryAccount(String userId, String accountId) async {
    final batch = _firestoreService.batch();

    // First, unset any existing primary accounts
    final existingPrimary = await _userAccountsCollection(
      userId,
    ).where('isPrimary', isEqualTo: true).get();

    for (final doc in existingPrimary.docs) {
      batch.update(doc.reference, {
        'isPrimary': false,
        'updatedAt': DateTime.now().toIso8601String(),
      });
    }

    // Set the new primary account
    batch.update(_accountDoc(userId, accountId), {
      'isPrimary': true,
      'updatedAt': DateTime.now().toIso8601String(),
    });

    await batch.commit();
  }

  @override
  Future<String> createAccount(Account account) async {
    // Ensure the account has the current user ID
    final accountWithUserId = account.copyWith(userId: _currentUserId);
    return create(accountWithUserId);
  }

  @override
  Future<void> updateAccount(String accountId, Account account) async {
    await update(accountId, account);
  }

  @override
  Future<void> deactivateAccount(String accountId) async {
    throw UnimplementedError(
      'Use deactivateAccountForUser(userId, accountId) instead',
    );
  }

  /// Deactivate account with user context
  Future<void> deactivateAccountForUser(String userId, String accountId) async {
    await _accountDoc(userId, accountId).update({
      'isActive': false,
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  @override
  Future<void> deleteAccount(String accountId) async {
    throw UnimplementedError(
      'Use deleteAccountForUser(userId, accountId) instead',
    );
  }

  /// Delete account with user context
  Future<void> deleteAccountForUser(String userId, String accountId) async {
    await _accountDoc(userId, accountId).delete();
  }

  @override
  Future<void> reactivateAccount(String accountId) async {
    throw UnimplementedError(
      'Use reactivateAccountForUser(userId, accountId) instead',
    );
  }

  /// Reactivate account with user context
  Future<void> reactivateAccountForUser(String userId, String accountId) async {
    await _accountDoc(
      userId,
      accountId,
    ).update({'isActive': true, 'updatedAt': DateTime.now().toIso8601String()});
  }

  @override
  Future<int> getAccountBalance(String accountId) async {
    throw UnimplementedError(
      'Use getAccountBalanceForUser(userId, accountId) instead',
    );
  }

  /// Get account balance with user context
  /// Returns the current balance which is updated with each transaction
  Future<int> getAccountBalanceForUser(String userId, String accountId) async {
    final account = await getAccountById(userId, accountId);
    return account?.currentBalanceCents ?? 0;
  }

  @override
  Future<Map<String, int>> getUserAccountBalances(String userId) async {
    _operationCounts['reads'] = (_operationCounts['reads'] ?? 0) + 1;

    // Check rollout configuration
    final config = _performanceRolloutService?.getConfigForUser(userId);
    final shouldUseOptimizations =
        config?.shouldApplyOptimization(
          PerformanceOptimization.advancedQueryOptimization,
        ) ??
        true; // Default to enabled if no rollout service

    if (!shouldUseOptimizations) {
      // Fallback to individual balance queries (legacy behavior)
      final accounts = await getActiveAccountsByUserId(userId);
      final balances = <String, int>{};
      for (final account in accounts) {
        balances[account.id] = account.currentBalanceCents;
      }
      return balances;
    }

    // PERFORMANCE OPTIMIZATION: Single query to get all account balances
    final cacheKey = 'user_account_balances_$userId';

    // Use intelligent caching if enabled
    if (config?.shouldApplyOptimization(
          PerformanceOptimization.intelligentCaching,
        ) ??
        true) {
      final cachedBalances = await _cacheService.get<Map<String, dynamic>>(
        cacheKey,
      );
      if (cachedBalances != null) {
        _operationCounts['cache_hits'] =
            (_operationCounts['cache_hits'] ?? 0) + 1;
        return Map<String, int>.from(cachedBalances);
      }
    }

    _operationCounts['cache_misses'] =
        (_operationCounts['cache_misses'] ?? 0) + 1;

    // Single query for all active accounts with their current balances
    final snapshot = await _userAccountsCollection(userId)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: false)
        .get();

    final balances = <String, int>{};
    for (final doc in snapshot.docs) {
      final data = doc.data() as Map<String, dynamic>? ?? {};
      final account = Account.fromJson(data);
      balances[account.id] = account.currentBalanceCents;
    }

    // Cache with configured TTL
    if (config?.shouldApplyOptimization(
          PerformanceOptimization.intelligentCaching,
        ) ??
        true) {
      await _cacheService.set(
        cacheKey,
        balances,
        ttl: config?.cacheBalancesTtl ?? const Duration(minutes: 5),
      );
    }

    // Log rollout metrics
    if (config != null) {
      _performanceRolloutService?.logRolloutMetrics(
        'getUserAccountBalances',
        config,
      );
    }

    return balances;
  }

  @override
  Future<List<Account>> searchAccountsByName(
    String userId,
    String query,
  ) async {
    _operationCounts['reads'] = (_operationCounts['reads'] ?? 0) + 1;

    // PERFORMANCE OPTIMIZATION: Use Firestore text search instead of client-side filtering
    final cacheKey = 'search_accounts_${userId}_${query.toLowerCase()}';

    final cachedResults = await _cacheService.get<List<Map<String, dynamic>>>(
      cacheKey,
    );
    if (cachedResults != null) {
      _operationCounts['cache_hits'] =
          (_operationCounts['cache_hits'] ?? 0) + 1;
      return cachedResults.map(Account.fromJson).toList();
    }

    _operationCounts['cache_misses'] =
        (_operationCounts['cache_misses'] ?? 0) + 1;

    if (query.trim().isEmpty) {
      return getAccountsByUserId(userId);
    }

    // For short queries, use prefix matching which is more efficient
    if (query.length <= 3) {
      final snapshot = await _userAccountsCollection(userId)
          .where('name', isGreaterThanOrEqualTo: query)
          .where('name', isLessThan: '${query}z')
          .orderBy('name')
          .orderBy('createdAt', descending: false)
          .get();

      final results = snapshot.docs
          .map(
            (doc) =>
                Account.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
          )
          .toList();

      // Cache search results for 10 minutes
      await _cacheService.set(
        cacheKey,
        results.map((account) => account.toJson()).toList(),
        ttl: const Duration(minutes: 10),
      );

      return results;
    }

    // For longer queries, use the existing approach but with caching
    final allAccounts = await getAccountsByUserId(userId);
    final lowercaseQuery = query.toLowerCase();

    final results = allAccounts.where((account) {
      return account.name.toLowerCase().contains(lowercaseQuery) ||
          (account.description?.toLowerCase().contains(lowercaseQuery) ??
              false);
    }).toList();

    // Cache search results for 10 minutes
    await _cacheService.set(
      cacheKey,
      results.map((account) => account.toJson()).toList(),
      ttl: const Duration(minutes: 10),
    );

    return results;
  }

  @override
  Future<Map<String, dynamic>> getAccountStats(String accountId) async {
    throw UnimplementedError(
      'Use getAccountStatsForUser(userId, accountId) instead',
    );
  }

  /// Get account statistics with user context
  Future<Map<String, dynamic>> getAccountStatsForUser(
    String userId,
    String accountId,
  ) async {
    final account = await getAccountById(userId, accountId);
    if (account == null) return {};

    final balance = await getAccountBalanceForUser(userId, accountId);

    return {
      'accountId': accountId,
      'accountName': account.name,
      'accountType': account.type.name,
      'classification': account.classification.name,
      'currentBalance': balance,
      'initialBalance': account.initialBalanceCents,
      'balanceChange': balance - account.initialBalanceCents,
      'isActive': account.isActive,
      'isPrimary': account.isPrimary,
      'createdAt': account.createdAt?.toIso8601String(),
      'daysSinceCreation': account.createdAt != null
          ? DateTime.now().difference(account.createdAt!).inDays
          : 0,
    };
  }

  @override
  Future<Map<String, dynamic>> getUserAccountSummary(String userId) async {
    _operationCounts['reads'] = (_operationCounts['reads'] ?? 0) + 1;

    // PERFORMANCE OPTIMIZATION: Single composite query with caching
    final cacheKey = 'user_account_summary_$userId';

    final cachedSummary = await _cacheService.get<Map<String, dynamic>>(
      cacheKey,
    );
    if (cachedSummary != null) {
      _operationCounts['cache_hits'] =
          (_operationCounts['cache_hits'] ?? 0) + 1;
      return cachedSummary;
    }

    _operationCounts['cache_misses'] =
        (_operationCounts['cache_misses'] ?? 0) + 1;

    // Single query to get all accounts with their balances
    final accounts = await getAccountsByUserId(userId);
    final activeAccounts = accounts.where((a) => a.isActive).toList();

    final assetAccounts = activeAccounts
        .where((a) => a.classification == AccountClassification.asset)
        .toList();

    final liabilityAccounts = activeAccounts
        .where((a) => a.classification == AccountClassification.liability)
        .toList();

    // Calculate totals directly from account data (no additional queries)
    var totalAssets = 0;
    var totalLiabilities = 0;

    for (final account in assetAccounts) {
      totalAssets += account.currentBalanceCents;
    }

    for (final account in liabilityAccounts) {
      totalLiabilities += account.currentBalanceCents;
    }

    final typeCount = <String, int>{};
    for (final account in activeAccounts) {
      typeCount[account.type.name] = (typeCount[account.type.name] ?? 0) + 1;
    }

    // Get primary account efficiently using existing data
    Account? primaryAccount;
    try {
      primaryAccount = activeAccounts.firstWhere(
        (account) => account.isPrimary,
      );
    } on Exception {
      // No primary account found, which is valid
      primaryAccount = null;
    }

    final summary = {
      'totalAccounts': accounts.length,
      'activeAccounts': activeAccounts.length,
      'inactiveAccounts': accounts.length - activeAccounts.length,
      'assetAccounts': assetAccounts.length,
      'liabilityAccounts': liabilityAccounts.length,
      'totalAssets': totalAssets,
      'totalLiabilities': totalLiabilities,
      'netWorth': totalAssets - totalLiabilities,
      'accountsByType': typeCount,
      'primaryAccount': primaryAccount,
    };

    // Cache summary for 10 minutes
    await _cacheService.set(
      cacheKey,
      summary,
      ttl: const Duration(minutes: 10),
    );

    return summary;
  }

  @override
  Stream<List<Account>> watchUserAccounts(String userId) {
    return _userAccountsCollection(
      userId,
    ).orderBy('createdAt', descending: false).snapshots().map((snapshot) {
      return snapshot.docs
          .map(
            (doc) =>
                Account.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
          )
          .toList();
    });
  }

  @override
  Stream<Account?> watchAccount(String accountId) {
    throw UnimplementedError(
      'Use watchAccountForUser(userId, accountId) instead',
    );
  }

  /// Watch account with user context
  @override
  Stream<Account?> watchAccountForUser(String userId, String accountId) {
    return _accountDoc(userId, accountId).snapshots().map((doc) {
      if (!doc.exists) return null;
      final data = doc.data() as Map<String, dynamic>? ?? {};
      return Account.fromJson(data);
    });
  }

  @override
  Future<bool> validateAccount(Account account) async {
    // Basic validation rules
    if (account.name.trim().isEmpty) return false;
    if (account.userId.isEmpty) return false;

    // Check if account name is unique for the user
    return isAccountNameUnique(
      account.userId,
      account.name,
      excludeAccountId: account.id,
    );
  }

  @override
  Future<bool> isAccountNameUnique(
    String userId,
    String name, {
    String? excludeAccountId,
  }) async {
    final query = _userAccountsCollection(
      userId,
    ).where('name', isEqualTo: name.trim());

    final snapshot = await query.get();

    // If no accounts found, name is unique
    if (snapshot.docs.isEmpty) return true;

    // If excluding an account ID, check if any other accounts have this name
    if (excludeAccountId != null) {
      return !snapshot.docs.any((doc) => doc.id != excludeAccountId);
    }

    // Name is not unique
    return false;
  }

  @override
  Future<List<Account>> getDeletableAccounts(String userId) async {
    // This would typically require checking for dependent transactions
    // For now, return all inactive accounts as potentially deletable
    final accounts = await getAccountsByUserId(userId);
    return accounts.where((account) => !account.isActive).toList();
  }

  @override
  Future<void> transferAccountOwnership(
    String accountId,
    String newUserId,
  ) async {
    throw UnimplementedError(
      'Account ownership transfer requires complex data migration and is not '
      'implemented',
    );
  }

  // Performance optimization methods from PerformanceOptimizedRepository mixin

  Future<Map<String, Account?>> getBatch(List<String> ids) async {
    if (ids.isEmpty) return {};

    _operationCounts['reads'] = (_operationCounts['reads'] ?? 0) + 1;

    final userId = _currentUserId;
    final results = <String, Account?>{};

    // Use batch get for better performance
    final docs = await Future.wait(
      ids.map((id) => _accountDoc(userId, id).get()),
    );

    for (var i = 0; i < ids.length; i++) {
      final doc = docs[i];
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>? ?? {};
        results[ids[i]] = Account.fromJson(data);
      } else {
        results[ids[i]] = null;
      }
    }

    return results;
  }

  Future<List<Account>> getCachedEntities({
    required String cacheKey,
    required Future<List<Account>> Function() fetchFunction,
    Duration? cacheTtl,
  }) async {
    final cached = await _cacheService.get<List<Map<String, dynamic>>>(
      cacheKey,
    );
    if (cached != null) {
      _operationCounts['cache_hits'] =
          (_operationCounts['cache_hits'] ?? 0) + 1;
      return cached.map(Account.fromJson).toList();
    }

    _operationCounts['cache_misses'] =
        (_operationCounts['cache_misses'] ?? 0) + 1;

    final entities = await fetchFunction();

    // Cache as serializable JSON
    final jsonData = entities.map((entity) => entity.toJson()).toList();

    await _cacheService.set(
      cacheKey,
      jsonData,
      ttl: cacheTtl ?? const Duration(minutes: 15),
    );

    return entities;
  }

  Future<Map<String, dynamic>> getCompositeData(
    Map<String, Future<dynamic> Function()> operations,
  ) async {
    final results = <String, dynamic>{};

    // Execute all operations concurrently for better performance
    final futures = operations.map(
      (key, operation) => MapEntry(key, operation()),
    );
    final settledResults = await Future.wait(futures.values, eagerError: false);

    var index = 0;
    for (final key in futures.keys) {
      try {
        results[key] = settledResults[index];
      } on Exception {
        results[key] = null; // Handle individual operation failures gracefully
      }
      index++;
    }

    return results;
  }

  Future<void> invalidateCache(List<String> keys) async {
    for (final key in keys) {
      await _cacheService.remove(key);
    }
  }

  Map<String, dynamic> getPerformanceStats() {
    return Map<String, dynamic>.from(_operationCounts)
      ..addAll(_cacheService.getStats());
  }
}
