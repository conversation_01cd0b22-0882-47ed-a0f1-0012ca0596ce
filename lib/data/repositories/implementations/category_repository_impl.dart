import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/repositories/implementations/budget_repository_impl.dart';
import 'package:budapp/data/repositories/implementations/transaction_repository_impl.dart';
import 'package:budapp/data/repositories/interfaces/category_repository.dart';
import 'package:budapp/features/budgets/services/budget_transaction_service.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Concrete implementation of ICategoryRepository using Firestore
///
/// This implementation provides category management functionality
/// using FirestoreService as the backend storage.
class CategoryRepositoryImpl implements ICategoryRepository {
  CategoryRepositoryImpl(
    this._firestoreService, {
    required FirebaseAuth firebaseAuth,
  }) : _firebaseAuth = firebaseAuth;
  final FirestoreService _firestoreService;
  final FirebaseAuth _firebaseAuth;
  static const String _collectionName = 'categories';

  /// Get current user ID from Firebase Auth
  String get _currentUserId {
    final user = _firebaseAuth.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }
    return user.uid;
  }

  /// Get reference to user's categories collection
  CollectionReference _userCategoriesCollection(String userId) =>
      _firestoreService.collection('users/$userId/$_collectionName');

  /// Get reference to a specific category document
  DocumentReference _categoryDoc(String userId, String categoryId) =>
      _userCategoriesCollection(userId).doc(categoryId);

  @override
  Future<String> create(Category entity) async {
    // If entity has no ID, generate one
    final categoryWithId = entity.id.isEmpty
        ? entity.copyWith(
            id: _userCategoriesCollection(entity.userId).doc().id,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          )
        : entity;

    await _categoryDoc(
      categoryWithId.userId,
      categoryWithId.id,
    ).set(categoryWithId.toJson());
    return categoryWithId.id;
  }

  /// Get category by ID with user context
  @override
  Future<Category?> getCategoryById(String userId, String categoryId) async {
    final doc = await _categoryDoc(userId, categoryId).get();
    if (!doc.exists) return null;

    final data = doc.data() as Map<String, dynamic>? ?? {};
    return Category.fromJson(data);
  }

  @override
  Future<void> update(String id, Category entity) async {
    final updatedEntity = entity.copyWith(
      id: id, // Ensure ID matches the document ID
      updatedAt: DateTime.now(),
    );
    await _categoryDoc(entity.userId, id).update(updatedEntity.toJson());
  }

  @override
  Future<List<Category>> getCategoriesByUserId(String userId) async {
    final snapshot = await _userCategoriesCollection(
      userId,
    ).orderBy('sortOrder').orderBy('createdAt', descending: false).get();

    return snapshot.docs
        .map(
          (doc) => Category.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<List<Category>> getActiveCategoriesByUserId(String userId) async {
    final snapshot = await _userCategoriesCollection(userId)
        .where('isActive', isEqualTo: true)
        .orderBy('sortOrder')
        .orderBy('createdAt', descending: false)
        .get();

    return snapshot.docs
        .map(
          (doc) => Category.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<List<Category>> getActiveCategories() async {
    final userId = _currentUserId;
    return getActiveCategoriesByUserId(userId);
  }

  @override
  Future<List<Category>> getCategoriesByType(
    String userId,
    CategoryType type,
  ) async {
    final snapshot = await _userCategoriesCollection(userId)
        .where('type', isEqualTo: type.name)
        .where('isActive', isEqualTo: true)
        .orderBy('sortOrder')
        .orderBy('createdAt', descending: false)
        .get();

    return snapshot.docs
        .map(
          (doc) => Category.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<List<Category>> getRootCategories(String userId) async {
    final snapshot = await _userCategoriesCollection(userId)
        .where('parentId', isNull: true)
        .where('isActive', isEqualTo: true)
        .orderBy('sortOrder')
        .orderBy('createdAt', descending: false)
        .get();

    return snapshot.docs
        .map(
          (doc) => Category.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<List<Category>> getSubcategories(
    String userId,
    String parentId,
  ) async {
    final snapshot = await _userCategoriesCollection(userId)
        .where('parentId', isEqualTo: parentId)
        .where('isActive', isEqualTo: true)
        .orderBy('sortOrder')
        .orderBy('createdAt', descending: false)
        .get();

    return snapshot.docs
        .map(
          (doc) => Category.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<String> createCategory(Category category) async {
    // Ensure the category has the current user ID
    final categoryWithUserId = category.copyWith(userId: _currentUserId);

    final categoryWithId = categoryWithUserId.copyWith(
      id: _userCategoriesCollection(categoryWithUserId.userId).doc().id,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _categoryDoc(
      categoryWithUserId.userId,
      categoryWithId.id,
    ).set(categoryWithId.toJson());
    return categoryWithId.id;
  }

  @override
  Future<void> updateCategory(String categoryId, Category category) async {
    final updatedCategory = category.copyWith(
      id: categoryId,
      updatedAt: DateTime.now(),
    );

    await _categoryDoc(
      category.userId,
      categoryId,
    ).update(updatedCategory.toJson());
  }

  @override
  Future<void> deactivateCategory(String categoryId) async {
    final userId = _currentUserId;
    await deactivateCategoryWithUserId(userId, categoryId);
  }

  @override
  Future<void> deleteCategory(String categoryId) async {
    // Note: This requires user context, will be improved in next iteration
    throw UnimplementedError(
      'Use deleteCategoryWithConstraints(userId, categoryId) instead',
    );
  }

  /// Delete category with proper constraint checking
  @override
  Future<void> deleteCategoryWithConstraints(
    String userId,
    String categoryId,
  ) async {
    // Get the category first
    final category = await getCategoryById(userId, categoryId);
    if (category == null) {
      throw Exception('Category not found');
    }

    // Check for dependent transactions
    if (await hasDependentTransactionsForUser(userId, categoryId)) {
      throw Exception(
        'Cannot delete category: it has associated transactions. '
        'Please reassign or delete the transactions first.',
      );
    }

    // Check for child subcategories
    if (await hasChildSubcategories(userId, categoryId)) {
      throw Exception(
        'Cannot delete category: it has subcategories. '
        'Please delete or move the subcategories first.',
      );
    }

    // If no constraints, perform hard delete
    await _categoryDoc(userId, categoryId).delete();
  }

  /// Soft delete category with user context
  @override
  Future<void> deactivateCategoryWithUserId(
    String userId,
    String categoryId,
  ) async {
    final category = await getCategoryById(userId, categoryId);
    if (category == null) {
      throw Exception('Category not found');
    }

    final deactivatedCategory = category.copyWith(
      isActive: false,
      updatedAt: DateTime.now(),
    );

    await _categoryDoc(userId, categoryId).update(deactivatedCategory.toJson());
  }

  @override
  Future<void> reactivateCategory(String categoryId) async {
    final userId = _currentUserId;
    final category = await getCategoryById(userId, categoryId);
    if (category == null) {
      throw Exception('Category not found');
    }

    final reactivatedCategory = category.copyWith(
      isActive: true,
      updatedAt: DateTime.now(),
    );

    await _categoryDoc(userId, categoryId).update(reactivatedCategory.toJson());
  }

  @override
  Future<List<Category>> searchCategoriesByName(
    String userId,
    String query,
  ) async {
    // Firestore doesn't support case-insensitive search, so we'll get all
    // categories and filter client-side for now
    final allCategories = await getActiveCategoriesByUserId(userId);
    final lowercaseQuery = query.toLowerCase();

    return allCategories
        .where(
          (category) => category.name.toLowerCase().contains(lowercaseQuery),
        )
        .toList();
  }

  @override
  Future<Map<String, dynamic>> getCategoryStats(String categoryId) async {
    // Placeholder implementation - would require transaction integration
    return {
      'categoryId': categoryId,
      'transactionCount': 0,
      'totalAmount': 0,
      'lastUsed': null,
    };
  }

  @override
  Future<Map<String, dynamic>> getUserCategorySummary(String userId) async {
    final categories = await getCategoriesByUserId(userId);
    final activeCategories = categories.where((c) => c.isActive).toList();

    return {
      'totalCategories': categories.length,
      'activeCategories': activeCategories.length,
      'inactiveCategories': categories.length - activeCategories.length,
      'incomeCategories': activeCategories
          .where((c) => c.type == CategoryType.income)
          .length,
      'expenseCategories': activeCategories
          .where((c) => c.type == CategoryType.expense)
          .length,
    };
  }

  @override
  Stream<List<Category>> watchUserCategories(String userId) {
    return _userCategoriesCollection(userId)
        .orderBy('sortOrder')
        .orderBy('createdAt', descending: false)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map(
                (doc) => Category.fromJson(
                  doc.data() as Map<String, dynamic>? ?? {},
                ),
              )
              .toList();
        });
  }

  @override
  Stream<Category?> watchCategory(String categoryId) {
    throw UnimplementedError(
      'Use watchCategoryForUser(userId, categoryId) instead',
    );
  }

  @override
  Stream<Category?> watchCategoryForUser(String userId, String categoryId) {
    return _categoryDoc(userId, categoryId).snapshots().map((snapshot) {
      if (!snapshot.exists) return null;
      return Category.fromJson(snapshot.data() as Map<String, dynamic>? ?? {});
    });
  }

  @override
  Future<bool> validateCategory(Category category) async {
    // Basic validation
    if (category.name.trim().isEmpty) return false;
    if (category.name.length < 2 || category.name.length > 50) return false;
    if (category.userId.trim().isEmpty) return false;

    // Check for circular reference if parentId is set
    if (category.parentId != null) {
      return validateCategoryHierarchy(category.id, category.parentId);
    }

    return true;
  }

  @override
  Future<bool> isCategoryNameUnique(
    String userId,
    String name, {
    String? excludeCategoryId,
  }) async {
    final query = _userCategoriesCollection(
      userId,
    ).where('name', isEqualTo: name).where('isActive', isEqualTo: true);

    final snapshot = await query.get();

    if (excludeCategoryId != null) {
      return snapshot.docs.where((doc) => doc.id != excludeCategoryId).isEmpty;
    }

    return snapshot.docs.isEmpty;
  }

  // Additional methods will be implemented in the next iteration
  @override
  Future<List<Category>> getDeletableCategories(String userId) async {
    // Placeholder - would require transaction integration
    return getActiveCategoriesByUserId(userId);
  }

  @override
  Future<List<CategoryTree>> getCategoryTree(String userId) async {
    // Placeholder - will implement tree building logic
    final categories = await getActiveCategoriesByUserId(userId);
    return categories
        .where((c) => c.isRoot)
        .map((c) => CategoryTree(category: c))
        .toList();
  }

  @override
  Future<List<CategoryTree>> getCategoryTreeByType(
    String userId,
    CategoryType type,
  ) async {
    // Placeholder - will implement tree building logic
    final categories = await getCategoriesByType(userId, type);
    return categories
        .where((c) => c.isRoot)
        .map((c) => CategoryTree(category: c))
        .toList();
  }

  @override
  Future<void> moveCategoryToParent(
    String categoryId,
    String? newParentId,
  ) async {
    throw UnimplementedError('Will implement in next iteration');
  }

  @override
  Future<void> updateCategorySortOrder(String categoryId, int sortOrder) async {
    throw UnimplementedError('Will implement in next iteration');
  }

  @override
  Future<int> getUserCategoryCount(String userId) async {
    final snapshot = await _userCategoriesCollection(userId).count().get();
    return snapshot.count ?? 0;
  }

  @override
  Future<bool> hasDependentTransactions(String categoryId) async {
    // This method needs userId context - should be called with proper context
    throw UnimplementedError(
      'Use hasDependentTransactionsForUser(userId, categoryId) instead',
    );
  }

  /// Check if category has dependent transactions with user context
  @override
  Future<bool> hasDependentTransactionsForUser(
    String userId,
    String categoryId,
  ) async {
    // Check if any transactions reference this category
    // Create a mock budget repository for this internal usage
    final budgetRepo = BudgetRepositoryImpl(
      firestoreService: _firestoreService,
      firebaseAuth: _firebaseAuth,
    );
    final budgetTransactionService = BudgetTransactionService(
      budgetRepo,
      _firestoreService,
    );
    final transactionRepo = TransactionRepositoryImpl(
      _firestoreService,
      budgetTransactionService,
    );
    final transactions = await transactionRepo
        .getTransactionsReferencingCategoryForUser(userId, categoryId);

    return transactions.isNotEmpty;
  }

  /// Check if category has active child subcategories
  @override
  Future<bool> hasChildSubcategories(String userId, String categoryId) async {
    final subcategories = await getSubcategories(userId, categoryId);
    return subcategories.isNotEmpty;
  }

  @override
  Future<bool> validateCategoryHierarchy(
    String categoryId,
    String? parentId,
  ) async {
    // Placeholder - will implement circular reference detection
    return true;
  }

  @override
  Future<List<Category>> getCategoryAncestors(String categoryId) async {
    throw UnimplementedError('Will implement in next iteration');
  }

  @override
  Future<List<Category>> getCategoryDescendants(String categoryId) async {
    throw UnimplementedError('Will implement in next iteration');
  }

  // Subcategory-specific operations implementation

  @override
  Future<String> createSubcategory(
    Category subcategory,
    String parentId,
  ) async {
    // Ensure the subcategory has the current user ID
    final subcategoryWithUserId = subcategory.copyWith(userId: _currentUserId);

    // Validate parent exists and is active
    final parent = await getCategoryById(
      subcategoryWithUserId.userId,
      parentId,
    );
    if (parent == null || !parent.isActive) {
      throw Exception('Parent category not found or inactive');
    }

    // Validate hierarchy depth
    final parentDepth = await getCategoryDepth(
      subcategoryWithUserId.userId,
      parentId,
    );
    if (parentDepth >= 2) {
      // Max depth of 3 levels (0, 1, 2)
      throw Exception('Maximum category hierarchy depth exceeded');
    }

    // Validate type consistency
    if (subcategoryWithUserId.type != parent.type) {
      throw Exception('Subcategory type must match parent category type');
    }

    // Validate name uniqueness within parent
    final isUnique = await isSubcategoryNameUnique(
      subcategoryWithUserId.userId,
      parentId,
      subcategoryWithUserId.name,
    );
    if (!isUnique) {
      throw Exception('Subcategory name must be unique within parent category');
    }

    // Create subcategory with parent reference
    final subcategoryWithParent = subcategoryWithUserId.copyWith(
      parentId: parentId,
      id: _userCategoriesCollection(subcategoryWithUserId.userId).doc().id,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _categoryDoc(
      subcategoryWithUserId.userId,
      subcategoryWithParent.id,
    ).set(subcategoryWithParent.toJson());

    return subcategoryWithParent.id;
  }

  @override
  Future<void> updateSubcategory(
    String subcategoryId,
    Category subcategory,
  ) async {
    // Validate subcategory exists
    final existing = await getCategoryById(subcategory.userId, subcategoryId);
    if (existing == null || !existing.isSubcategory) {
      throw Exception('Subcategory not found');
    }

    // If parent is changing, validate new parent
    if (subcategory.parentId != existing.parentId &&
        subcategory.parentId != null) {
      final newParent = await getCategoryById(
        subcategory.userId,
        subcategory.parentId!,
      );
      if (newParent == null || !newParent.isActive) {
        throw Exception('New parent category not found or inactive');
      }

      // Validate type consistency with new parent
      if (subcategory.type != newParent.type) {
        throw Exception('Subcategory type must match parent category type');
      }

      // Validate hierarchy depth with new parent
      final newParentDepth = await getCategoryDepth(
        subcategory.userId,
        subcategory.parentId!,
      );
      if (newParentDepth >= 2) {
        throw Exception('Maximum category hierarchy depth exceeded');
      }
    }

    // Validate name uniqueness within parent (if name or parent changed)
    if (subcategory.name != existing.name ||
        subcategory.parentId != existing.parentId) {
      final parentId = subcategory.parentId ?? existing.parentId!;
      final isUnique = await isSubcategoryNameUnique(
        subcategory.userId,
        parentId,
        subcategory.name,
        excludeSubcategoryId: subcategoryId,
      );
      if (!isUnique) {
        throw Exception(
          'Subcategory name must be unique within parent category',
        );
      }
    }

    // Update subcategory
    final updatedSubcategory = subcategory.copyWith(
      id: subcategoryId,
      updatedAt: DateTime.now(),
    );

    await _categoryDoc(
      subcategory.userId,
      subcategoryId,
    ).update(updatedSubcategory.toJson());
  }

  @override
  Future<void> deleteSubcategory(String subcategoryId, String parentId) async {
    // This method needs userId context - should be called with proper context
    throw UnimplementedError(
      'Use deleteSubcategoryWithConstraints(userId, subcategoryId, parentId) instead',
    );
  }

  /// Delete subcategory with proper constraint checking
  @override
  Future<void> deleteSubcategoryWithConstraints(
    String userId,
    String subcategoryId,
    String parentId,
  ) async {
    // Validate subcategory exists and belongs to parent
    final subcategory = await getCategoryById(userId, subcategoryId);
    if (subcategory == null || subcategory.parentId != parentId) {
      throw Exception(
        'Subcategory not found or does not belong to specified parent',
      );
    }

    // Check for dependent transactions
    if (await hasDependentTransactionsForUser(userId, subcategoryId)) {
      throw Exception(
        'Cannot delete subcategory: it has associated transactions. '
        'Please reassign or delete the transactions first.',
      );
    }

    // Check for child subcategories (can have their own children)
    if (await hasChildSubcategories(userId, subcategoryId)) {
      throw Exception(
        'Cannot delete subcategory: it has child subcategories. Please delete or move the child subcategories first.',
      );
    }

    // If no constraints, perform soft delete
    final deactivatedSubcategory = subcategory.copyWith(
      isActive: false,
      updatedAt: DateTime.now(),
    );

    await _categoryDoc(
      userId,
      subcategoryId,
    ).update(deactivatedSubcategory.toJson());
  }

  @override
  Stream<List<Category>> watchSubcategories(String userId, String parentId) {
    return _userCategoriesCollection(userId)
        .where('parentId', isEqualTo: parentId)
        .where('isActive', isEqualTo: true)
        .orderBy('sortOrder')
        .orderBy('createdAt', descending: false)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map(
                (doc) => Category.fromJson(
                  doc.data() as Map<String, dynamic>? ?? {},
                ),
              )
              .toList(),
        );
  }

  @override
  Future<int> getSubcategoryCount(String userId, String parentId) async {
    final snapshot = await _userCategoriesCollection(userId)
        .where('parentId', isEqualTo: parentId)
        .where('isActive', isEqualTo: true)
        .count()
        .get();
    return snapshot.count ?? 0;
  }

  @override
  Future<bool> validateSubcategoryHierarchy(
    String parentId,
    String subcategoryId,
  ) async {
    // Check for circular reference by traversing up the parent chain
    String? currentParentId = parentId;
    final visitedIds = <String>{};

    while (currentParentId != null) {
      if (visitedIds.contains(currentParentId)) {
        return false; // Circular reference detected
      }

      if (currentParentId == subcategoryId) {
        return false; // Would create circular reference
      }

      visitedIds.add(currentParentId);

      // Get parent's parent using current user ID
      final parent = await getCategoryById(_currentUserId, currentParentId);
      currentParentId = parent?.parentId;
    }

    return true;
  }

  @override
  Future<int> getCategoryDepth(String userId, String categoryId) async {
    final category = await getCategoryById(userId, categoryId);
    if (category == null) return -1;

    var depth = 0;
    var currentParentId = category.parentId;

    while (currentParentId != null) {
      depth++;
      final parent = await getCategoryById(userId, currentParentId);
      if (parent == null) break;
      currentParentId = parent.parentId;
    }

    return depth;
  }

  @override
  Future<void> moveSubcategoryToParent(
    String subcategoryId,
    String oldParentId,
    String newParentId,
  ) async {
    // Get subcategory using current user ID
    final subcategory = await getCategoryById(_currentUserId, subcategoryId);
    if (subcategory == null || subcategory.parentId != oldParentId) {
      throw Exception('Subcategory not found or does not belong to old parent');
    }

    // Validate new parent
    final newParent = await getCategoryById(subcategory.userId, newParentId);
    if (newParent == null || !newParent.isActive) {
      throw Exception('New parent category not found or inactive');
    }

    // Validate hierarchy
    final isValidHierarchy = await validateSubcategoryHierarchy(
      newParentId,
      subcategoryId,
    );
    if (!isValidHierarchy) {
      throw Exception('Moving subcategory would create circular reference');
    }

    // Validate depth
    final newParentDepth = await getCategoryDepth(
      subcategory.userId,
      newParentId,
    );
    if (newParentDepth >= 2) {
      throw Exception('Maximum category hierarchy depth exceeded');
    }

    // Validate type consistency
    if (subcategory.type != newParent.type) {
      throw Exception('Subcategory type must match new parent category type');
    }

    // Update subcategory with new parent
    final updatedSubcategory = subcategory.copyWith(
      parentId: newParentId,
      updatedAt: DateTime.now(),
    );

    await _categoryDoc(
      subcategory.userId,
      subcategoryId,
    ).update(updatedSubcategory.toJson());
  }

  @override
  Future<bool> canHaveSubcategories(String userId, String categoryId) async {
    final category = await getCategoryById(userId, categoryId);
    if (category == null || !category.isActive) return false;

    // Check current depth
    final depth = await getCategoryDepth(userId, categoryId);
    return depth <
        2; // Can have subcategories if depth is 0 or 1 (max depth is 2)
  }

  @override
  Future<bool> isSubcategoryNameUnique(
    String userId,
    String parentId,
    String name, {
    String? excludeSubcategoryId,
  }) async {
    final query = _userCategoriesCollection(userId)
        .where('parentId', isEqualTo: parentId)
        .where('name', isEqualTo: name)
        .where('isActive', isEqualTo: true);

    final snapshot = await query.get();

    if (excludeSubcategoryId != null) {
      return snapshot.docs
          .where((doc) => doc.id != excludeSubcategoryId)
          .isEmpty;
    }

    return snapshot.docs.isEmpty;
  }
}
