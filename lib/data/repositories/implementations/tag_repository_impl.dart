import 'package:budapp/data/models/tag.dart';
import 'package:budapp/data/repositories/interfaces/tag_repository.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class TagRepositoryImpl implements TagRepository {
  TagRepositoryImpl({
    required FirestoreService firestoreService,
    required FirebaseAuth firebaseAuth,
  }) : _firestoreService = firestoreService,
       _firebaseAuth = firebaseAuth;
  final FirestoreService _firestoreService;
  final FirebaseAuth _firebaseAuth;

  String get _userId {
    final user = _firebaseAuth.currentUser;
    if (user == null) throw Exception('User not authenticated');
    return user.uid;
  }

  CollectionReference get _tagsCollection =>
      _firestoreService.collection('users').doc(_userId).collection('tags');

  CollectionReference get _transactionsCollection => _firestoreService
      .collection('users')
      .doc(_userId)
      .collection('transactions');

  @override
  Stream<List<Tag>> watchUserTags() {
    return _tagsCollection
        .orderBy('name')
        .snapshots()
        .map((snapshot) => snapshot.docs.map(Tag.fromFirestore).toList());
  }

  @override
  Future<Tag?> getTagById(String tagId) async {
    try {
      final doc = await _tagsCollection.doc(tagId).get();
      if (!doc.exists) return null;

      return Tag.fromFirestore(doc);
    } on Exception catch (e) {
      throw Exception('Failed to get tag: $e');
    }
  }

  @override
  Future<Tag> createTag(Tag tag) async {
    try {
      // Check if tag name already exists
      final existingQuery = await _tagsCollection
          .where('name', isEqualTo: tag.name)
          .limit(1)
          .get();

      if (existingQuery.docs.isNotEmpty) {
        throw TagAlreadyExistsException(tag.name);
      }

      final now = Timestamp.now();
      final tagData = tag.copyWith(createdAt: now, updatedAt: now);

      final docRef = await _tagsCollection.add(tagData.toFirestore());

      return tagData.copyWith(id: docRef.id);
    } on Exception catch (e) {
      if (e is TagAlreadyExistsException) rethrow;
      throw Exception('Failed to create tag: $e');
    }
  }

  @override
  Future<Tag> updateTag(Tag tag) async {
    try {
      // Check if tag exists
      final existingDoc = await _tagsCollection.doc(tag.id).get();
      if (!existingDoc.exists) {
        throw TagNotFoundException(tag.id);
      }

      // Check if new name conflicts with another tag
      if (tag.name.isNotEmpty) {
        final conflictQuery = await _tagsCollection
            .where('name', isEqualTo: tag.name)
            .limit(2)
            .get();

        final conflicts = conflictQuery.docs
            .where((doc) => doc.id != tag.id)
            .toList();

        if (conflicts.isNotEmpty) {
          throw TagAlreadyExistsException(tag.name);
        }
      }

      final updatedTag = tag.copyWith(updatedAt: Timestamp.now());

      await _tagsCollection.doc(tag.id).update(updatedTag.toFirestore());

      return updatedTag;
    } on Exception catch (e) {
      if (e is TagNotFoundException || e is TagAlreadyExistsException) rethrow;
      throw Exception('Failed to update tag: $e');
    }
  }

  @override
  Future<void> deleteTag(String tagId) async {
    try {
      // Check if tag exists
      final tagDoc = await _tagsCollection.doc(tagId).get();
      if (!tagDoc.exists) {
        throw TagNotFoundException(tagId);
      }

      // Use a batch to ensure atomicity
      final batch = _firestoreService.batch()
        ..delete(_tagsCollection.doc(tagId));

      // Remove tag from all transactions
      await _batchRemoveTagFromTransactionsInBatch(tagId, batch);

      // Commit the batch
      await batch.commit();
    } on Exception catch (e) {
      if (e is TagNotFoundException) rethrow;
      throw Exception('Failed to delete tag: $e');
    }
  }

  @override
  Future<void> batchRemoveTagFromTransactions(String tagId) async {
    final batch = _firestoreService.batch();
    await _batchRemoveTagFromTransactionsInBatch(tagId, batch);
    await batch.commit();
  }

  Future<void> _batchRemoveTagFromTransactionsInBatch(
    String tagId,
    WriteBatch batch,
  ) async {
    // Query transactions that contain this tag
    final transactionsQuery = await _transactionsCollection
        .where('tagIds', arrayContains: tagId)
        .get();

    for (final doc in transactionsQuery.docs) {
      final data = doc.data() as Map<String, dynamic>? ?? {};
      final tagIds =
          List<String>.from((data['tagIds'] as Iterable<dynamic>?) ?? [])
            // Remove the tag ID and update the transaction
            ..remove(tagId);
      batch.update(doc.reference, {
        'tagIds': tagIds,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    }
  }

  @override
  Future<int> getTagUsageCount(String tagId) async {
    try {
      final query = await _transactionsCollection
          .where('tagIds', arrayContains: tagId)
          .count()
          .get();

      return query.count ?? 0;
    } on Exception catch (e) {
      throw Exception('Failed to get tag usage count: $e');
    }
  }

  @override
  Future<List<Tag>> searchTags(String searchTerm) async {
    try {
      if (searchTerm.isEmpty) {
        final snapshot = await _tagsCollection.orderBy('name').get();
        return snapshot.docs.map(Tag.fromFirestore).toList();
      }

      // Firestore doesn't support case-insensitive search, so we get all tags
      // and filter in memory. For large datasets, consider Algolia or similar.
      final snapshot = await _tagsCollection.orderBy('name').get();
      final allTags = snapshot.docs.map(Tag.fromFirestore).toList();

      final searchLower = searchTerm.toLowerCase();
      return allTags
          .where((tag) => tag.name.toLowerCase().contains(searchLower))
          .toList();
    } on Exception catch (e) {
      throw Exception('Failed to search tags: $e');
    }
  }

  @override
  Future<List<Tag>> getTagsForTransaction(String transactionId) async {
    try {
      final transactionDoc = await _transactionsCollection
          .doc(transactionId)
          .get();
      if (!transactionDoc.exists) return [];

      final data = transactionDoc.data()! as Map<String, dynamic>;
      final tagIds = List<String>.from(
        (data['tagIds'] as Iterable<dynamic>?) ?? [],
      );

      if (tagIds.isEmpty) return [];

      // Get all tags in a single query
      final tagsQuery = await _tagsCollection
          .where(FieldPath.documentId, whereIn: tagIds)
          .get();

      return tagsQuery.docs.map(Tag.fromFirestore).toList();
    } on Exception catch (e) {
      throw Exception('Failed to get tags for transaction: $e');
    }
  }
}
