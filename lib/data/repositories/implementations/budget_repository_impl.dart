import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/repositories/interfaces/budget_repository.dart';
import 'package:budapp/features/budgets/services/budget_error_service.dart';
import 'package:budapp/features/budgets/services/budget_validators.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Concrete implementation of BudgetRepository using Firestore
///
/// This implementation provides budget management functionality
/// using FirestoreService as the backend storage with proper user isolation,
/// data validation, and overlap prevention.
class BudgetRepositoryImpl implements BudgetRepository {
  BudgetRepositoryImpl({
    required FirestoreService firestoreService,
    required FirebaseAuth firebaseAuth,
  }) : _firestoreService = firestoreService,
       _firebaseAuth = firebaseAuth;
  final FirestoreService _firestoreService;
  final FirebaseAuth _firebaseAuth;
  static const String _collectionName = 'budgets';

  /// Get reference to user's budgets collection
  CollectionReference _userBudgetsCollection(String userId) =>
      _firestoreService.collection('users/$userId/$_collectionName');

  /// Get reference to a specific budget document
  DocumentReference _budgetDoc(String userId, String budgetId) =>
      _userBudgetsCollection(userId).doc(budgetId);

  /// Get current user ID from Firebase Auth
  String get _currentUserId {
    final user = _firebaseAuth.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }
    return user.uid;
  }

  /// Get all budgets for a user (including inactive ones for validation)
  Future<List<Budget>> _getAllUserBudgets(String userId) async {
    final snapshot = await _userBudgetsCollection(userId).get();
    final budgets = <Budget>[];

    for (final doc in snapshot.docs) {
      try {
        final budget = Budget.fromJson(
          doc.data() as Map<String, dynamic>? ?? {},
        );
        budgets.add(budget);
      } catch (error, stackTrace) {
        // Handle JSON parsing errors by wrapping them in BudgetException
        throw BudgetErrorService.handleError(error, stackTrace);
      }
    }

    return budgets;
  }

  @override
  Future<List<Budget>> getAllBudgets() async {
    try {
      final userId = _currentUserId;
      return _getAllUserBudgets(userId);
    } on Exception catch (error, stackTrace) {
      final budgetError = BudgetErrorService.handleError(error, stackTrace);
      BudgetErrorService.logError(budgetError);
      throw budgetError;
    }
  }

  @override
  Future<List<Budget>> getBudgetsByPeriod(
    DateTime period,
    BudgetPeriod periodType,
  ) async {
    try {
      final userId = _currentUserId;

      // Calculate the exact period start date based on period type
      DateTime targetPeriodStart;
      switch (periodType) {
        case BudgetPeriod.monthly:
          targetPeriodStart = DateTime(period.year, period.month, 1);
        case BudgetPeriod.yearly:
          targetPeriodStart = DateTime(period.year, 1, 1);
      }

      // Query budgets that match the exact period start
      final snapshot = await _userBudgetsCollection(userId)
          .where('isActive', isEqualTo: true)
          .where('period', isEqualTo: periodType.name)
          .where(
            'periodStart',
            isEqualTo: Timestamp.fromDate(targetPeriodStart),
          )
          .get();

      final budgets = <Budget>[];
      for (final doc in snapshot.docs) {
        try {
          final budget = Budget.fromJson(
            doc.data() as Map<String, dynamic>? ?? {},
          );
          budgets.add(budget);
        } catch (error, stackTrace) {
          // Handle JSON parsing errors by wrapping them in BudgetException
          throw BudgetErrorService.handleError(error, stackTrace);
        }
      }

      return budgets;
    } on Exception catch (error, stackTrace) {
      final budgetError = BudgetErrorService.handleError(error, stackTrace);
      BudgetErrorService.logError(budgetError);
      throw budgetError;
    }
  }

  @override
  Future<void> batchCreateBudgets(List<Budget> budgets) async {
    try {
      final userId = _currentUserId;

      if (budgets.isEmpty) {
        return;
      }

      // Validate all budgets before creating any
      final allExistingBudgets = await _getAllUserBudgets(userId);

      for (final budget in budgets) {
        final validationResult = BudgetValidators.validateBudgetForCreation(
          budget,
          allExistingBudgets,
        );

        if (!validationResult.isValid) {
          throw BudgetErrorService.validationFailed(validationResult.errors);
        }
      }

      // Use batch operation for atomic creation
      await _firestoreService.runTransaction((transaction) async {
        for (final budget in budgets) {
          final docRef = _userBudgetsCollection(userId).doc();
          final now = DateTime.now();
          final budgetWithId = budget.copyWith(
            id: docRef.id,
            userId: userId,
            createdAt: now,
            updatedAt: now,
          );

          transaction.set(docRef, budgetWithId.toJson());
        }
      });
    } on Exception catch (error, stackTrace) {
      final budgetError = BudgetErrorService.handleError(error, stackTrace);
      BudgetErrorService.logError(budgetError);
      throw budgetError;
    }
  }

  @override
  Future<void> batchUpdateBudgets(List<Budget> budgets) async {
    try {
      final userId = _currentUserId;

      if (budgets.isEmpty) {
        return;
      }

      // Validate all budgets belong to current user and validate budget data
      final allExistingBudgets = await _getAllUserBudgets(userId);

      for (final budget in budgets) {
        if (budget.userId != userId) {
          throw BudgetErrorService.unauthorized(budget.id);
        }

        // Find the original budget for validation
        final originalBudget = allExistingBudgets.firstWhere(
          (b) => b.id == budget.id,
          orElse: () => throw BudgetErrorService.notFound(budget.id),
        );

        // Validate the updated budget
        final validationResult = BudgetValidators.validateBudgetForUpdate(
          budget,
          originalBudget,
          allExistingBudgets,
        );

        if (!validationResult.isValid) {
          throw BudgetErrorService.validationFailed(validationResult.errors);
        }
      }

      // Use batch operation for atomic updates
      await _firestoreService.runTransaction((transaction) async {
        for (final budget in budgets) {
          final docRef = _budgetDoc(userId, budget.id);

          // Verify budget exists
          final currentDoc = await transaction.get(docRef);
          if (!currentDoc.exists) {
            throw BudgetErrorService.notFound(budget.id);
          }

          // Update with current timestamp
          final updatedBudget = budget.copyWith(
            userId: userId,
            updatedAt: DateTime.now(),
          );

          transaction.update(docRef, updatedBudget.toJson());
        }
      });
    } on Exception catch (error, stackTrace) {
      final budgetError = BudgetErrorService.handleError(error, stackTrace);
      BudgetErrorService.logError(budgetError);
      throw budgetError;
    }
  }

  @override
  Future<void> batchDeleteBudgets(List<String> budgetIds) async {
    try {
      final userId = _currentUserId;

      if (budgetIds.isEmpty) {
        return;
      }

      // Use batch operation for atomic deletion
      await _firestoreService.runTransaction((transaction) async {
        for (final budgetId in budgetIds) {
          final docRef = _budgetDoc(userId, budgetId);

          // Verify budget exists and belongs to user
          final currentDoc = await transaction.get(docRef);
          if (!currentDoc.exists) {
            throw BudgetErrorService.notFound(budgetId);
          }

          final currentBudget = Budget.fromJson(
            currentDoc.data() as Map<String, dynamic>? ?? {},
          );

          if (currentBudget.userId != userId) {
            throw BudgetErrorService.unauthorized(budgetId);
          }

          // Soft delete by setting isActive to false
          transaction.update(docRef, {
            'isActive': false,
            'updatedAt': DateTime.now().toIso8601String(),
          });
        }
      });
    } on Exception catch (error, stackTrace) {
      final budgetError = BudgetErrorService.handleError(error, stackTrace);
      BudgetErrorService.logError(budgetError);
      throw budgetError;
    }
  }

  @override
  Stream<List<Budget>> watchBudgets() {
    final userId = _currentUserId;
    return _userBudgetsCollection(userId)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
          final budgets = <Budget>[];
          for (final doc in snapshot.docs) {
            try {
              final budget = Budget.fromJson(
                doc.data() as Map<String, dynamic>? ?? {},
              );
              budgets.add(budget);
            } on Exception {
              // Skip malformed documents in streams to avoid breaking the entire stream
              continue;
            }
          }
          return budgets;
        });
  }

  @override
  Future<Budget?> getBudgetById(String id) async {
    try {
      final userId = _currentUserId;
      final doc = await _budgetDoc(userId, id).get();

      if (!doc.exists) return null;

      final data = doc.data() as Map<String, dynamic>? ?? {};
      final budget = Budget.fromJson(data);

      // Verify budget belongs to current user
      if (budget.userId != userId) {
        throw BudgetErrorService.unauthorized(id);
      }

      return budget;
    } on Exception catch (error, stackTrace) {
      if (error is BudgetException) {
        rethrow;
      }
      final budgetError = BudgetErrorService.handleError(error, stackTrace);
      BudgetErrorService.logError(budgetError);
      throw budgetError;
    }
  }

  @override
  Future<Budget> createBudget(Budget budget) async {
    try {
      final userId = _currentUserId;

      // Enhanced validation using BudgetValidators
      final allBudgets = await _getAllUserBudgets(userId);
      final validationResult = BudgetValidators.validateBudgetForCreation(
        budget,
        allBudgets,
      );

      if (!validationResult.isValid) {
        throw BudgetErrorService.validationFailed(validationResult.errors);
      }

      // Check for overlaps
      final hasOverlap = await validateBudgetOverlap(budget);
      if (!hasOverlap) {
        throw BudgetErrorService.overlapDetected(
          budget.categoryId ?? 'overall',
          budget.period.name,
        );
      }

      // Generate ID and set user context with proper timestamps
      final docRef = _userBudgetsCollection(userId).doc();
      final now = DateTime.now();
      final budgetWithId = budget.copyWith(
        id: docRef.id,
        userId: userId,
        createdAt: now,
        updatedAt: now,
      );

      // Use atomic operation for creation
      await _firestoreService.runTransaction((transaction) async {
        // Double-check for overlaps within transaction using precise period
        // matching
        final existingQuery = await _userBudgetsCollection(userId)
            .where('isActive', isEqualTo: true)
            .where('categoryId', isEqualTo: budget.categoryId)
            .where('period', isEqualTo: budget.period.name)
            .where(
              'periodStart',
              isEqualTo: Timestamp.fromDate(budget.periodStart),
            )
            .get();

        if (existingQuery.docs.isNotEmpty) {
          throw BudgetErrorService.overlapDetected(
            budget.categoryId ?? 'overall',
            budget.period.name,
          );
        }

        // Create the budget
        transaction.set(docRef, budgetWithId.toJson());
      });

      return budgetWithId;
    } on Exception catch (error, stackTrace) {
      final budgetError = BudgetErrorService.handleError(error, stackTrace);
      BudgetErrorService.logError(budgetError);
      throw budgetError;
    }
  }

  @override
  Future<Budget> updateBudget(Budget budget) async {
    try {
      final userId = _currentUserId;

      // Verify budget exists and belongs to user
      final existingBudget = await getBudgetById(budget.id);
      if (existingBudget == null) {
        throw BudgetErrorService.notFound(budget.id);
      }

      // Enhanced validation using BudgetValidators
      final allBudgets = await _getAllUserBudgets(userId);
      final validationResult = BudgetValidators.validateBudgetForUpdate(
        budget,
        existingBudget,
        allBudgets,
      );

      if (!validationResult.isValid) {
        throw BudgetErrorService.validationFailed(validationResult.errors);
      }

      // Check for overlaps (excluding current budget)
      final hasOverlap = await validateBudgetOverlap(budget);
      if (!hasOverlap) {
        throw BudgetErrorService.overlapDetected(
          budget.categoryId ?? 'overall',
          budget.period.name,
        );
      }

      // Update with current timestamp and preserve creation data
      final updatedBudget = budget.copyWith(
        userId: userId,
        createdAt: existingBudget.createdAt, // Preserve original creation time
        updatedAt: DateTime.now(),
      );

      // Use atomic update operation
      await _firestoreService.runTransaction((transaction) async {
        final docRef = _budgetDoc(userId, budget.id);

        // Verify budget still exists and hasn't been modified
        final currentDoc = await transaction.get(docRef);
        if (!currentDoc.exists) {
          throw BudgetErrorService.notFound(budget.id);
        }

        final currentBudget = Budget.fromJson(
          currentDoc.data() as Map<String, dynamic>? ?? {},
        );

        // Check for concurrent modifications
        if (currentBudget.updatedAt.isAfter(existingBudget.updatedAt)) {
          throw BudgetErrorService.concurrencyError();
        }

        // Perform the update
        transaction.update(docRef, updatedBudget.toJson());
      });

      return updatedBudget;
    } on Exception catch (error, stackTrace) {
      final budgetError = BudgetErrorService.handleError(error, stackTrace);
      BudgetErrorService.logError(budgetError);
      throw budgetError;
    }
  }

  @override
  Future<void> deleteBudget(String id) async {
    try {
      final userId = _currentUserId;

      // Verify budget exists and belongs to user
      final existingBudget = await getBudgetById(id);
      if (existingBudget == null) {
        throw BudgetErrorService.notFound(id);
      }

      // Check if budget is already deleted
      if (!existingBudget.isActive) {
        throw BudgetErrorService.invalidOperation('Budget is already deleted');
      }

      // Use atomic operation for soft deletion
      await _firestoreService.runTransaction((transaction) async {
        final docRef = _budgetDoc(userId, id);

        // Verify budget still exists and is active
        final currentDoc = await transaction.get(docRef);
        if (!currentDoc.exists) {
          throw BudgetErrorService.notFound(id);
        }

        final currentBudget = Budget.fromJson(
          currentDoc.data() as Map<String, dynamic>? ?? {},
        );

        if (!currentBudget.isActive) {
          throw BudgetErrorService.invalidOperation(
            'Budget is already deleted',
          );
        }

        // Perform soft delete with proper timestamp
        transaction.update(docRef, {
          'isActive': false,
          'updatedAt': DateTime.now().toIso8601String(),
        });
      });
    } on Exception catch (error, stackTrace) {
      final budgetError = BudgetErrorService.handleError(error, stackTrace);
      BudgetErrorService.logError(budgetError);
      throw budgetError;
    }
  }

  @override
  Stream<List<Budget>> watchBudgetsByMonth(DateTime month) {
    final userId = _currentUserId;

    // Create period start for monthly budgets
    final monthlyPeriodStart = DateTime(month.year, month.month, 1);

    // Create period start for yearly budgets
    final yearlyPeriodStart = DateTime(month.year, 1, 1);

    return _userBudgetsCollection(userId)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
          final budgets = <Budget>[];
          for (final doc in snapshot.docs) {
            try {
              final budget = Budget.fromJson(
                doc.data() as Map<String, dynamic>? ?? {},
              );
              // Filter budgets that belong to the specific period
              var shouldInclude = false;
              switch (budget.period) {
                case BudgetPeriod.monthly:
                  // Monthly budgets must have exact period match
                  shouldInclude =
                      budget.periodStart.year == monthlyPeriodStart.year &&
                      budget.periodStart.month == monthlyPeriodStart.month;
                case BudgetPeriod.yearly:
                  // Yearly budgets must have exact year match
                  shouldInclude =
                      budget.periodStart.year == yearlyPeriodStart.year;
              }
              if (shouldInclude) {
                budgets.add(budget);
              }
            } on Exception {
              // Skip malformed documents in streams to avoid breaking the entire stream
              continue;
            }
          }
          return budgets;
        });
  }

  @override
  Future<Budget?> findLatestBudgetFromPreviousPeriod(
    String? categoryId,
    BudgetType budgetType,
    DateTime targetPeriod,
    BudgetPeriod periodType,
  ) async {
    try {
      final userId = _currentUserId;

      // Calculate the target period start
      DateTime targetPeriodStart;
      switch (periodType) {
        case BudgetPeriod.monthly:
          targetPeriodStart = DateTime(
            targetPeriod.year,
            targetPeriod.month,
            1,
          );
        case BudgetPeriod.yearly:
          targetPeriodStart = DateTime(targetPeriod.year, 1, 1);
      }

      // Query for budgets of the same category and type from previous periods
      final query = _userBudgetsCollection(userId)
          .where('isActive', isEqualTo: true)
          .where('period', isEqualTo: periodType.name)
          .where('type', isEqualTo: budgetType.name)
          .where(
            'periodStart',
            isLessThan: Timestamp.fromDate(targetPeriodStart),
          )
          .orderBy('periodStart', descending: true);

      // Add category filter if specified
      final finalQuery = categoryId != null
          ? query.where('categoryId', isEqualTo: categoryId)
          : query.where('categoryId', isNull: true);

      final snapshot = await finalQuery.limit(1).get();

      if (snapshot.docs.isEmpty) {
        return null;
      }

      try {
        return Budget.fromJson(
          snapshot.docs.first.data() as Map<String, dynamic>? ?? {},
        );
      } catch (error, stackTrace) {
        // Handle JSON parsing errors by wrapping them in BudgetException
        throw BudgetErrorService.handleError(error, stackTrace);
      }
    } on Exception catch (error, stackTrace) {
      final budgetError = BudgetErrorService.handleError(error, stackTrace);
      BudgetErrorService.logError(budgetError);
      throw budgetError;
    }
  }

  /// Update budget status (enhanced soft deletion)
  @override
  Future<void> updateBudgetStatus(String id, {required bool isActive}) async {
    try {
      final userId = _currentUserId;

      // Verify budget exists and belongs to user
      final existingBudget = await getBudgetById(id);
      if (existingBudget == null) {
        throw BudgetErrorService.notFound(id);
      }

      // Use atomic operation for status update
      await _firestoreService.runTransaction((transaction) async {
        final docRef = _budgetDoc(userId, id);

        // Verify budget still exists
        final currentDoc = await transaction.get(docRef);
        if (!currentDoc.exists) {
          throw BudgetErrorService.notFound(id);
        }

        // Update status with timestamp
        transaction.update(docRef, {
          'isActive': isActive,
          'updatedAt': DateTime.now().toIso8601String(),
        });
      });
    } on Exception catch (error, stackTrace) {
      final budgetError = BudgetErrorService.handleError(error, stackTrace);
      BudgetErrorService.logError(budgetError);
      throw budgetError;
    }
  }

  @override
  Future<bool> validateBudgetOverlap(Budget budget) async {
    final userId = _currentUserId;

    // If no category specified (overall budget), no overlap check needed
    if (budget.categoryId == null) {
      return true;
    }

    // Query existing budgets for the same category, period, and period start
    final query = await _userBudgetsCollection(userId)
        .where('isActive', isEqualTo: true)
        .where('categoryId', isEqualTo: budget.categoryId)
        .where('period', isEqualTo: budget.period.name)
        .where('periodStart', isEqualTo: Timestamp.fromDate(budget.periodStart))
        .get();

    // Check for overlaps (excluding current budget if updating)
    for (final doc in query.docs) {
      final existingBudget = Budget.fromJson(
        doc.data() as Map<String, dynamic>? ?? {},
      );

      // Skip if this is the same budget (for updates)
      if (existingBudget.id == budget.id) {
        continue;
      }

      // Found an overlap
      return false;
    }

    return true;
  }
}
