import 'package:budapp/data/models/user_profile.dart';
import 'package:budapp/data/repositories/interfaces/user_repository.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Concrete implementation of IUserRepository using Firestore
///
/// This implementation provides user profile management functionality
/// using FirestoreService as the backend storage.
class UserRepositoryImpl implements IUserRepository {
  UserRepositoryImpl(this._firestoreService, this._firebaseAuth);
  final FirestoreService _firestoreService;
  final FirebaseAuth _firebaseAuth;
  static const String _collectionName = 'users';

  /// Get reference to users collection
  CollectionReference get _usersCollection =>
      _firestoreService.collection(_collectionName);

  /// Get reference to a specific user document
  DocumentReference _userDoc(String uid) => _usersCollection.doc(uid);

  @override
  Future<String> create(UserProfile entity) async {
    await _userDoc(entity.uid).set(entity.toJson());
    return entity.uid;
  }

  @override
  Future<UserProfile?> getById(String id) async {
    final doc = await _userDoc(id).get();
    if (!doc.exists) return null;

    final data = doc.data() as Map<String, dynamic>? ?? {};
    return UserProfile.fromJson(data);
  }

  @override
  Future<void> update(String id, UserProfile entity) async {
    final updatedEntity = entity.copyWith(
      uid: id, // Ensure UID matches the document ID
    );
    await _userDoc(id).update(updatedEntity.toJson());
  }

  @override
  Future<void> delete(String id) async {
    await _userDoc(id).delete();
  }

  @override
  Future<bool> exists(String id) async {
    final doc = await _userDoc(id).get();
    return doc.exists;
  }

  Future<List<UserProfile>> getAll({int? limit}) async {
    Query query = _usersCollection;
    if (limit != null) {
      query = query.limit(limit);
    }

    final snapshot = await query.get();
    return snapshot.docs
        .map(
          (doc) =>
              UserProfile.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  Future<List<UserProfile>> getPaginated({
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    var query = _usersCollection.limit(limit);

    if (startAfter != null) {
      query = query.startAfterDocument(startAfter);
    }

    final snapshot = await query.get();
    return snapshot.docs
        .map(
          (doc) =>
              UserProfile.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  Stream<UserProfile?> watchById(String id) {
    return _userDoc(id).snapshots().map((doc) {
      if (!doc.exists) return null;
      final data = doc.data() as Map<String, dynamic>? ?? {};
      return UserProfile.fromJson(data);
    });
  }

  Stream<List<UserProfile>> watchAll({int? limit}) {
    Query query = _usersCollection;
    if (limit != null) {
      query = query.limit(limit);
    }

    return query.snapshots().map((snapshot) {
      return snapshot.docs
          .map(
            (doc) =>
                UserProfile.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
          )
          .toList();
    });
  }

  Future<void> batchCreate(List<UserProfile> entities) async {
    final batch = _firestoreService.batch();

    for (final entity in entities) {
      batch.set(_userDoc(entity.uid), entity.toJson());
    }

    await batch.commit();
  }

  Future<void> batchUpdate(Map<String, UserProfile> entities) async {
    final batch = _firestoreService.batch();

    for (final entry in entities.entries) {
      final updatedEntity = entry.value.copyWith(uid: entry.key);
      batch.update(_userDoc(entry.key), updatedEntity.toJson());
    }

    await batch.commit();
  }

  Future<void> batchDelete(List<String> ids) async {
    final batch = _firestoreService.batch();

    for (final id in ids) {
      batch.delete(_userDoc(id));
    }

    await batch.commit();
  }

  Future<List<UserProfile>> query({
    Map<String, dynamic>? where,
    String? orderBy,
    bool descending = false,
    int? limit,
  }) async {
    Query query = _usersCollection;

    // Apply where conditions
    if (where != null) {
      for (final entry in where.entries) {
        query = query.where(entry.key, isEqualTo: entry.value);
      }
    }

    // Apply ordering
    if (orderBy != null) {
      query = query.orderBy(orderBy, descending: descending);
    }

    // Apply limit
    if (limit != null) {
      query = query.limit(limit);
    }

    final snapshot = await query.get();
    return snapshot.docs
        .map(
          (doc) =>
              UserProfile.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  Future<int> count({Map<String, dynamic>? where}) async {
    Query query = _usersCollection;

    // Apply where conditions
    if (where != null) {
      for (final entry in where.entries) {
        query = query.where(entry.key, isEqualTo: entry.value);
      }
    }

    final snapshot = await query.count().get();
    return snapshot.count ?? 0;
  }

  // IUserRepository specific methods

  @override
  Future<UserProfile?> getUserById(String uid) => getById(uid);

  @override
  Future<UserProfile?> getUserByEmail(String email) async {
    final snapshot = await _usersCollection
        .where('email', isEqualTo: email)
        .limit(1)
        .get();

    if (snapshot.docs.isEmpty) return null;

    final data = snapshot.docs.first.data()! as Map<String, dynamic>;
    return UserProfile.fromJson(data);
  }

  @override
  Future<void> createOrUpdateFromFirebaseUser(User firebaseUser) async {
    final userProfile = UserProfile.fromFirebaseUser(firebaseUser);

    // Check if user already exists
    final existingUser = await getUserById(firebaseUser.uid);

    if (existingUser != null) {
      // Update existing user with latest Firebase data
      final updatedProfile = existingUser.copyWith(
        email: firebaseUser.email,
        displayName: firebaseUser.displayName,
        photoURL: firebaseUser.photoURL,
        lastLoginAt: DateTime.now(),
        isEmailVerified: firebaseUser.emailVerified,
        authProviders: firebaseUser.providerData
            .map((info) => info.providerId)
            .toList(),
      );
      await update(firebaseUser.uid, updatedProfile);
    } else {
      // Create new user profile
      await create(userProfile);
    }
  }

  @override
  Future<void> updatePreferences(
    String uid,
    Map<String, dynamic> preferences,
  ) async {
    await _userDoc(uid).update({
      'preferences': preferences,
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  @override
  Future<Map<String, dynamic>> getPreferences(String uid) async {
    final user = await getUserById(uid);
    return user?.preferences ?? {};
  }

  @override
  Future<void> updateLastLogin(String uid, DateTime loginTime) async {
    await _userDoc(uid).update({
      'lastLoginAt': loginTime.toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  @override
  Future<void> updateEmailVerificationStatus(
    String uid, {
    required bool isVerified,
  }) async {
    await _userDoc(uid).update({
      'isEmailVerified': isVerified,
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  @override
  Future<void> addAuthProvider(String uid, String providerId) async {
    final user = await getUserById(uid);
    if (user == null) return;

    final updatedProviders = List<String>.from(user.authProviders);
    if (!updatedProviders.contains(providerId)) {
      updatedProviders.add(providerId);

      await _userDoc(uid).update({
        'authProviders': updatedProviders,
        'updatedAt': DateTime.now().toIso8601String(),
      });
    }
  }

  @override
  Future<void> removeAuthProvider(String uid, String providerId) async {
    final user = await getUserById(uid);
    if (user == null) return;

    final updatedProviders = List<String>.from(user.authProviders)
      ..remove(providerId);

    await _userDoc(uid).update({
      'authProviders': updatedProviders,
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  @override
  Future<List<UserProfile>> getUsersByAuthProvider(String providerId) async {
    final snapshot = await _usersCollection
        .where('authProviders', arrayContains: providerId)
        .get();

    return snapshot.docs
        .map(
          (doc) =>
              UserProfile.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<List<UserProfile>> searchUsersByDisplayName(String query) async {
    // Note: Firestore doesn't support case-insensitive text search natively
    // This is a basic implementation that can be enhanced with Algolia or similar
    final snapshot = await _usersCollection
        .where('displayName', isGreaterThanOrEqualTo: query)
        .where('displayName', isLessThan: '$query\uf8ff')
        .get();

    return snapshot.docs
        .map(
          (doc) =>
              UserProfile.fromJson(doc.data() as Map<String, dynamic>? ?? {}),
        )
        .toList();
  }

  @override
  Future<Map<String, dynamic>> getUserStats(String uid) async {
    final user = await getUserById(uid);
    if (user == null) return {};

    return {
      'uid': uid,
      'createdAt': user.createdAt?.toIso8601String(),
      'lastLoginAt': user.lastLoginAt?.toIso8601String(),
      'isEmailVerified': user.isEmailVerified,
      'authProviders': user.authProviders,
      'preferencesCount': user.preferences.length,
      'accountAge': user.createdAt != null
          ? DateTime.now().difference(user.createdAt!).inDays
          : 0,
    };
  }

  @override
  Future<void> deleteUserProfile(String uid) async {
    await delete(uid);
  }

  @override
  Future<Map<String, dynamic>> exportUserData(String uid) async {
    final user = await getUserById(uid);
    if (user == null) return {};

    return {
      'profile': user.toJson(),
      'exportedAt': DateTime.now().toIso8601String(),
      'version': '1',
    };
  }

  @override
  Stream<UserProfile?> watchUserProfile(String uid) => watchById(uid);

  @override
  Future<bool> userExists(String uid) => exists(uid);

  // Profile management methods
  @override
  Future<void> updateDisplayName(String displayName) async {
    final currentUser = _firebaseAuth.currentUser;
    if (currentUser == null) {
      throw Exception('No authenticated user found');
    }

    // Update Firebase Auth display name
    await currentUser.updateDisplayName(displayName);

    // Update Firestore user profile
    await _userDoc(currentUser.uid).update({
      'displayName': displayName,
      'lastLoginAt': DateTime.now().toIso8601String(),
    });
  }

  @override
  Future<void> updateEmail(String email) async {
    final currentUser = _firebaseAuth.currentUser;
    if (currentUser == null) {
      throw Exception('No authenticated user found');
    }

    // Use verifyBeforeUpdateEmail instead of deprecated updateEmail
    await currentUser.verifyBeforeUpdateEmail(email);

    // Update Firestore user profile
    await _userDoc(currentUser.uid).update({
      'email': email,
      'isEmailVerified':
          false, // Email verification is reset when email changes
      'lastLoginAt': DateTime.now().toIso8601String(),
    });
  }

  @override
  Future<UserProfile?> getCurrentUserProfile() async {
    final currentUser = _firebaseAuth.currentUser;
    if (currentUser == null) return null;

    return getUserById(currentUser.uid);
  }

  @override
  Future<void> updateUserProfile(UserProfile userProfile) async {
    final currentUser = _firebaseAuth.currentUser;
    if (currentUser == null) {
      throw Exception('No authenticated user found');
    }

    // Update the profile with current timestamp
    final updatedProfile = userProfile.copyWith(lastLoginAt: DateTime.now());

    await update(currentUser.uid, updatedProfile);
  }
}
