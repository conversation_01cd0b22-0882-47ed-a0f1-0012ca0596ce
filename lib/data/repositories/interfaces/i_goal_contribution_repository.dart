import 'package:budapp/data/models/goal_contribution.dart';

/// Repository interface for goal contribution-related data operations
///
/// Provides methods for CRUD operations on goal contributions with proper error handling,
/// data validation, and Firestore integration. Supports subcollection management under
/// `users/{userId}/goals/{goalId}/contributions` path with real-time updates through streams.
abstract class IGoalContributionRepository {
  /// Create a new contribution for a specific goal
  ///
  /// Creates a new contribution for the specified goal. The contribution will be assigned
  /// a unique ID and stored in the goal's contributions subcollection.
  ///
  /// Returns the ID of the created contribution.
  /// Throws exception if creation fails or validation errors occur.
  Future<String> createContribution(
    String goalId,
    GoalContribution contribution,
  );

  /// Update an existing contribution
  ///
  /// Updates an existing contribution with new data. The contribution must belong to
  /// the current user's goal. The updatedAt timestamp will be automatically set.
  ///
  /// Throws exception if the contribution doesn't exist or belongs to another user.
  Future<void> updateContribution(
    String goalId,
    String contributionId,
    GoalContribution contribution,
  );

  /// Delete a contribution
  ///
  /// Removes a contribution from the specified goal. Uses soft deletion by setting
  /// isActive to false to maintain data integrity.
  ///
  /// Throws exception if the contribution doesn't exist or belongs to another user.
  Future<void> deleteContribution(String goalId, String contributionId);

  /// Get a specific contribution by ID
  ///
  /// Retrieves a single contribution by its ID from the specified goal.
  /// Returns null if the contribution doesn't exist or belongs to another user.
  Future<GoalContribution?> getContributionById(
    String goalId,
    String contributionId,
  );

  /// Get all contributions for a specific goal
  ///
  /// Returns all contributions (active and inactive) for the specified goal.
  /// Contributions are ordered by contribution date (newest first).
  /// Only returns contributions for goals owned by the current user.
  Future<List<GoalContribution>> getContributionsForGoal(String goalId);

  /// Get active contributions for a specific goal
  ///
  /// Returns only active contributions (isActive = true) for the specified goal.
  /// Contributions are ordered by contribution date (newest first).
  Future<List<GoalContribution>> getActiveContributionsForGoal(String goalId);

  /// Get all contributions for the current user
  ///
  /// Returns all contributions across all goals owned by the current user.
  /// Contributions are ordered by contribution date (newest first).
  Future<List<GoalContribution>> getAllUserContributions();

  /// Get active contributions for the current user
  ///
  /// Returns all active contributions across all goals owned by the current user.
  /// Contributions are ordered by contribution date (newest first).
  Future<List<GoalContribution>> getActiveUserContributions();

  /// Get contributions by date range for a specific goal
  ///
  /// Returns contributions made within the specified date range for the goal.
  /// Date range is inclusive of both start and end dates.
  Future<List<GoalContribution>> getContributionsByDateRange(
    String goalId,
    DateTime startDate,
    DateTime endDate,
  );

  /// Get recent contributions for a specific goal
  ///
  /// Returns the most recent contributions for the specified goal.
  /// Limited to the specified number of contributions (default: 10).
  Future<List<GoalContribution>> getRecentContributions(
    String goalId, {
    int limit = 10,
  });

  /// Get total contribution amount for a specific goal
  ///
  /// Calculates and returns the total amount (in cents) of all active contributions
  /// for the specified goal. Used for progress tracking and validation.
  Future<int> getTotalContributionAmountForGoal(String goalId);

  /// Get contribution count for a specific goal
  ///
  /// Returns the number of active contributions for the specified goal.
  Future<int> getContributionCountForGoal(String goalId);

  /// Get contributions made today for a specific goal
  ///
  /// Returns all contributions made today for the specified goal.
  Future<List<GoalContribution>> getTodaysContributions(String goalId);

  /// Get contributions made this week for a specific goal
  ///
  /// Returns all contributions made this week for the specified goal.
  Future<List<GoalContribution>> getThisWeeksContributions(String goalId);

  /// Get contributions made this month for a specific goal
  ///
  /// Returns all contributions made this month for the specified goal.
  Future<List<GoalContribution>> getThisMonthsContributions(String goalId);

  // Real-time streams

  /// Watch all contributions for a specific goal
  ///
  /// Returns a stream that emits the list of contributions whenever there are changes
  /// to the goal's contributions subcollection. Provides real-time updates for UI.
  Stream<List<GoalContribution>> watchContributionsForGoal(String goalId);

  /// Watch active contributions for a specific goal
  ///
  /// Returns a stream that emits only active contributions for the specified goal.
  /// Provides real-time updates for UI components showing active contributions.
  Stream<List<GoalContribution>> watchActiveContributionsForGoal(String goalId);

  /// Watch all contributions for the current user
  ///
  /// Returns a stream that emits all contributions across all user's goals.
  /// Provides real-time updates for dashboard and analytics views.
  Stream<List<GoalContribution>> watchAllUserContributions();

  /// Watch a specific contribution
  ///
  /// Returns a stream that emits updates for a specific contribution.
  /// Useful for detailed contribution views and real-time editing.
  Stream<GoalContribution?> watchContribution(
    String goalId,
    String contributionId,
  );

  // Validation and business logic

  /// Validate a contribution before saving
  ///
  /// Performs comprehensive validation including:
  /// - Goal existence and ownership
  /// - Contribution data validation
  /// - Business rule enforcement
  /// - Amount and date constraints
  ///
  /// Returns true if the contribution is valid, false otherwise.
  Future<bool> validateContribution(
    String goalId,
    GoalContribution contribution,
  );

  /// Check if a goal exists and is owned by current user
  ///
  /// Validates that the specified goal exists and belongs to the current user.
  /// Used internally for validation before contribution operations.
  Future<bool> isValidGoalForContribution(String goalId);

  /// Get contribution statistics for a goal
  ///
  /// Returns a map containing contribution statistics:
  /// - total_amount: Total contribution amount in cents
  /// - contribution_count: Number of contributions
  /// - average_amount: Average contribution amount
  /// - first_contribution_date: Date of first contribution
  /// - last_contribution_date: Date of most recent contribution
  Future<Map<String, dynamic>> getContributionStatistics(String goalId);
}
