/// Interface for performance monitoring in repositories
abstract class IPerformanceMonitoringService {
  void startOperation(String operationName);
  void endOperation(String operationName, {Map<String, dynamic>? metadata});
  void recordQuery(String queryType);
  void recordCacheHit(String cacheKey);
  void recordCacheMiss(String cacheKey);
  Map<String, dynamic> getPerformanceStats();
  String generatePerformanceReport();
  void reset();
}

/// Interface for Firebase performance monitoring
/// Note: Since Dart doesn't support abstract static methods, this serves as documentation
abstract class IPerformanceService {
  // Static methods are documented here but implemented in concrete classes
  // static Future<void> initialize();
  // static Future<void> startTrace(String traceName);
  // static Future<void> stopTrace(String traceName);
  // static Future<void> setTraceMetric(String traceName, String metricName, int value);
  // static Future<void> incrementTraceMetric(String traceName, String metricName, [int incrementBy = 1]);
  // static Future<void> setTraceAttribute(String traceName, String attributeName, String value);
  // static Future<void> trackAuthFlow(String flowType);
  // static Future<void> completeAuthFlow(String flowType);
  // static Future<void> trackScreenLoad(String screenName);
  // static Future<void> completeScreenLoad(String screenName);
}

/// Mock implementation for testing
class MockPerformanceMonitoringService
    implements IPerformanceMonitoringService {
  final Map<String, List<int>> _responseTimes = {};
  final Map<String, int> _queryCounts = {};
  final Map<String, int> _cacheHits = {};
  final Map<String, int> _cacheMisses = {};
  final Map<String, DateTime> _operationStartTimes = {};

  @override
  void startOperation(String operationName) {
    _operationStartTimes[operationName] = DateTime.now();
  }

  @override
  void endOperation(String operationName, {Map<String, dynamic>? metadata}) {
    final startTime = _operationStartTimes.remove(operationName);
    if (startTime == null) return;

    final duration = DateTime.now().difference(startTime).inMilliseconds;
    _responseTimes.putIfAbsent(operationName, () => []).add(duration);
  }

  @override
  void recordQuery(String queryType) {
    _queryCounts[queryType] = (_queryCounts[queryType] ?? 0) + 1;
  }

  @override
  void recordCacheHit(String cacheKey) {
    _cacheHits[cacheKey] = (_cacheHits[cacheKey] ?? 0) + 1;
  }

  @override
  void recordCacheMiss(String cacheKey) {
    _cacheMisses[cacheKey] = (_cacheMisses[cacheKey] ?? 0) + 1;
  }

  @override
  Map<String, dynamic> getPerformanceStats() {
    return <String, dynamic>{
      'operations': <String, dynamic>{},
      'queries': Map<String, int>.from(_queryCounts),
      'cache': <String, dynamic>{
        'hits': Map<String, int>.from(_cacheHits),
        'misses': Map<String, int>.from(_cacheMisses),
        'hit_rates': <String, double>{},
      },
    };
  }

  @override
  String generatePerformanceReport() {
    return 'Mock Performance Report';
  }

  @override
  void reset() {
    _responseTimes.clear();
    _queryCounts.clear();
    _cacheHits.clear();
    _cacheMisses.clear();
    _operationStartTimes.clear();
  }
}

/// Mock implementation for Firebase performance service
class MockPerformanceService {
  static final Map<String, bool> _activeTraces = {};
  static final Map<String, Map<String, dynamic>> _traceData = {};

  static Future<void> initialize() async {
    // Mock initialization - no-op
  }

  static Future<void> startTrace(String traceName) async {
    _activeTraces[traceName] = true;
    _traceData[traceName] = {};
  }

  static Future<void> stopTrace(String traceName) async {
    _activeTraces.remove(traceName);
  }

  static Future<void> setTraceMetric(
    String traceName,
    String metricName,
    int value,
  ) async {
    if (_activeTraces.containsKey(traceName)) {
      _traceData[traceName]?[metricName] = value;
    }
  }

  static Future<void> incrementTraceMetric(
    String traceName,
    String metricName, [
    int incrementBy = 1,
  ]) async {
    if (_activeTraces.containsKey(traceName)) {
      final current = _traceData[traceName]?[metricName] as int? ?? 0;
      _traceData[traceName]?[metricName] = current + incrementBy;
    }
  }

  static Future<void> setTraceAttribute(
    String traceName,
    String attributeName,
    String value,
  ) async {
    if (_activeTraces.containsKey(traceName)) {
      _traceData[traceName]?[attributeName] = value;
    }
  }

  static Future<void> trackAuthFlow(String flowType) async {
    await startTrace('user_login_$flowType');
    await setTraceAttribute('user_login_$flowType', 'auth_flow', flowType);
  }

  static Future<void> completeAuthFlow(String flowType) async {
    await stopTrace('user_login_$flowType');
  }

  static Future<void> trackScreenLoad(String screenName) async {
    await startTrace('screen_load_$screenName');
    await setTraceAttribute(
      'screen_load_$screenName',
      'screen_name',
      screenName,
    );
  }

  static Future<void> completeScreenLoad(String screenName) async {
    await stopTrace('screen_load_$screenName');
  }

  static void reset() {
    _activeTraces.clear();
    _traceData.clear();
  }
}
