import 'package:budapp/data/models/account.dart';

/// Repository interface for account-related data operations
abstract class IAccountRepository {
  /// Create a new account
  Future<String> create(Account entity);

  /// Update an existing account
  Future<void> update(String id, Account entity);

  /// Get all accounts for a specific user
  Future<List<Account>> getAccountsByUserId(String userId);

  /// Get active accounts for a specific user
  Future<List<Account>> getActiveAccountsByUserId(String userId);

  /// Get accounts by type for a specific user
  Future<List<Account>> getAccountsByType(String userId, AccountType type);

  /// Get accounts by classification for a specific user
  Future<List<Account>> getAccountsByClassification(
    String userId,
    AccountClassification classification,
  );

  /// Get the primary account for a user
  Future<Account?> getPrimaryAccount(String userId);

  /// Set an account as primary (unsets other primary accounts)
  Future<void> setPrimaryAccount(String userId, String accountId);

  /// Create a new account
  Future<String> createAccount(Account account);

  /// Update account details
  Future<void> updateAccount(String accountId, Account account);

  /// Soft delete an account (set isActive to false)
  Future<void> deactivateAccount(String accountId);

  /// Hard delete an account (permanent removal)
  Future<void> deleteAccount(String accountId);

  /// Reactivate a deactivated account
  Future<void> reactivateAccount(String accountId);

  /// Get account balance (calculated from transactions)
  Future<int> getAccountBalance(String accountId);

  /// Get account balances for all user accounts
  Future<Map<String, int>> getUserAccountBalances(String userId);

  /// Search accounts by name
  Future<List<Account>> searchAccountsByName(String userId, String query);

  /// Get account statistics
  Future<Map<String, dynamic>> getAccountStats(String accountId);

  /// Get user account summary
  Future<Map<String, dynamic>> getUserAccountSummary(String userId);

  /// Listen to account changes for a user
  Stream<List<Account>> watchUserAccounts(String userId);

  /// Listen to a specific account
  Stream<Account?> watchAccount(String accountId);

  /// Listen to a specific account with user context
  Stream<Account?> watchAccountForUser(String userId, String accountId);

  /// Validate account before creation/update
  Future<bool> validateAccount(Account account);

  /// Check if account name is unique for user
  Future<bool> isAccountNameUnique(
    String userId,
    String name, {
    String? excludeAccountId,
  });

  /// Get accounts that can be deleted (no dependent transactions)
  Future<List<Account>> getDeletableAccounts(String userId);

  /// Transfer account ownership (for account merging scenarios)
  Future<void> transferAccountOwnership(String accountId, String newUserId);
}
