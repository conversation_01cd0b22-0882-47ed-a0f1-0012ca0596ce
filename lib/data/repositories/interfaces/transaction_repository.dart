import 'package:budapp/data/models/transaction.dart';

/// Repository interface for transaction-related data operations
abstract class ITransactionRepository {
  /// Get all transactions for a specific user
  Future<List<Transaction>> getTransactionsByUserId(String userId);

  /// Get transactions for a specific account
  Future<List<Transaction>> getTransactionsByAccountId(String accountId);

  /// Get transactions by type for a user
  Future<List<Transaction>> getTransactionsByType(
    String userId,
    TransactionType type,
  );

  /// Get transactions by status for a user
  Future<List<Transaction>> getTransactionsByStatus(
    String userId,
    TransactionStatus status,
  );

  /// Get transactions by category
  Future<List<Transaction>> getTransactionsByCategory(
    String userId,
    String categoryId,
  );

  /// Get transactions by date range
  Future<List<Transaction>> getTransactionsByDateRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  );

  /// Get transactions by tags
  Future<List<Transaction>> getTransactionsByTags(
    String userId,
    List<String> tags,
  );

  /// Create a new transaction
  Future<String> createTransaction(Transaction transaction);

  /// Create an income transaction with enhanced validation
  Future<String> createIncomeTransaction({
    required String userId,
    required String toAccountId,
    required int amountCents,
    required DateTime transactionDate,
    String? categoryId,
    String? description,
    String? notes,
    List<String> tags = const [],
    Map<String, dynamic> metadata = const {},
  });

  /// Create an expense transaction with enhanced validation
  Future<String> createExpenseTransaction({
    required String userId,
    required String fromAccountId,
    required int amountCents,
    required DateTime transactionDate,
    String? categoryId,
    String? description,
    String? notes,
    List<String> tags = const [],
    Map<String, dynamic> metadata = const {},
  });

  /// Create a transfer transaction with enhanced validation
  Future<String> createTransferTransaction({
    required String userId,
    required String fromAccountId,
    required String toAccountId,
    required int amountCents,
    required DateTime transactionDate,
    String? description,
    String? notes,
    List<String> tags = const [],
    Map<String, dynamic> metadata = const {},
  });

  /// Update transaction details
  Future<void> updateTransaction(String transactionId, Transaction transaction);

  /// Update transaction status
  Future<void> updateTransactionStatus(
    String transactionId,
    TransactionStatus status,
  );

  /// Delete a transaction with atomic balance adjustments
  /// This method ensures that both the transaction deletion and related account balance
  /// adjustments are performed atomically using Firestore batch operations
  Future<void> deleteTransaction(String userId, String transactionId);

  /// Get transaction statistics for a user
  Future<Map<String, dynamic>> getTransactionStats(String userId);

  /// Get account balance from transactions
  Future<int> calculateAccountBalance(String accountId);

  /// Get spending by category for a date range
  Future<Map<String, int>> getSpendingByCategory(
    String userId,
    DateTime startDate,
    DateTime endDate,
  );

  /// Get income vs expense summary
  Future<Map<String, int>> getIncomeExpenseSummary(
    String userId,
    DateTime startDate,
    DateTime endDate,
  );

  /// Search transactions by description
  Future<List<Transaction>> searchTransactions(String userId, String query);

  /// Get recent transactions
  Future<List<Transaction>> getRecentTransactions(
    String userId, {
    int limit = 10,
  });

  /// Get largest transactions
  Future<List<Transaction>> getLargestTransactions(
    String userId, {
    int limit = 10,
  });

  /// Listen to transaction changes for a user
  Stream<List<Transaction>> watchUserTransactions(String userId);

  /// Listen to transaction changes for an account
  Stream<List<Transaction>> watchAccountTransactions(String accountId);

  /// Listen to a specific transaction
  Stream<Transaction?> watchTransaction(String transactionId);

  /// Validate transaction before creation/update
  Future<bool> validateTransaction(Transaction transaction);

  /// Bulk import transactions
  Future<List<String>> bulkCreateTransactions(List<Transaction> transactions);

  /// Get transactions that reference a specific account (for deletion validation)
  Future<List<Transaction>> getTransactionsReferencingAccount(String accountId);

  /// Get transactions that reference a specific category (for deletion validation)
  Future<List<Transaction>> getTransactionsReferencingCategory(
    String categoryId,
  );

  /// Update transactions when account is deleted (reassign to new account)
  Future<void> reassignTransactionsToAccount(
    String oldAccountId,
    String newAccountId,
  );

  /// Update transactions when category is deleted (reassign to new category)
  Future<void> reassignTransactionsToCategory(
    String oldCategoryId,
    String newCategoryId,
  );

  /// Update transactions when category is deleted (reassign to new category) with user context
  Future<void> reassignTransactionsToCategoryForUser(
    String userId,
    String oldCategoryId,
    String newCategoryId,
  );

  /// Watch transactions by month and categories for budget progress calculation
  ///
  /// Filters transactions by the specified month (using calendar month boundaries
  /// in user timezone) and optionally by category IDs. If categoryIds is null,
  /// returns all transactions for the month. If categoryIds is provided, filters
  /// to specific categories including descendant categories for hierarchy support.
  ///
  /// Used by budget progress calculation logic to aggregate spending for specific budgets.
  Stream<List<Transaction>> watchTransactionsByMonthAndCategories({
    required String userId,
    required DateTime month,
    List<String>? categoryIds,
  });
}
