import 'package:budapp/data/models/remote_config_data.dart';

/// Repository interface for Remote Config data operations
abstract class IRemoteConfigRepository {
  /// Initialize Remote Config with default values and settings
  Future<void> initialize();

  /// Fetch and activate Remote Config values
  Future<bool> fetchAndActivate();

  /// Get current Remote Config data
  RemoteConfigData getCurrentConfig();

  /// Get predefined categories
  PredefinedCategories getPredefinedCategories();

  /// Get premium limits
  PremiumLimits getPremiumLimits();

  /// Check if a specific feature is enabled
  bool isFeatureEnabled(String featureKey);

  /// Get a typed parameter value with fallback
  T getParameterValue<T>(String key, T defaultValue);

  /// Check if Remote Config values are stale
  bool get isStale;

  /// Get last fetch time
  DateTime get lastFetchTime;

  /// Listen to Remote Config updates (if supported by platform)
  Stream<RemoteConfigData>? watchConfigUpdates();

  /// Force refresh of Remote Config values
  Future<void> forceRefresh();

  /// Get Remote Config status information
  Map<String, dynamic> getStatus();
}
