import 'package:budapp/data/models/user_profile.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Repository interface for user-related data operations
abstract class IUserRepository {
  /// Create a new user profile
  Future<String> create(UserProfile entity);

  /// Get user profile by ID
  Future<UserProfile?> getById(String id);

  /// Update an existing user profile
  Future<void> update(String id, UserProfile entity);

  /// Delete a user profile
  Future<void> delete(String id);

  /// Check if a user profile exists
  Future<bool> exists(String id);

  /// Get user profile by Firebase User ID
  Future<UserProfile?> getUserById(String uid);

  /// Get user profile by email address
  Future<UserProfile?> getUserByEmail(String email);

  /// Create or update user profile from Firebase User
  Future<void> createOrUpdateFromFirebaseUser(User firebaseUser);

  /// Update user preferences
  Future<void> updatePreferences(String uid, Map<String, dynamic> preferences);

  /// Get user preferences
  Future<Map<String, dynamic>> getPreferences(String uid);

  /// Update last login time
  Future<void> updateLastLogin(String uid, DateTime loginTime);

  /// Update email verification status
  Future<void> updateEmailVerificationStatus(
    String uid, {
    required bool isVerified,
  });

  /// Add authentication provider to user
  Future<void> addAuthProvider(String uid, String providerId);

  /// Remove authentication provider from user
  Future<void> removeAuthProvider(String uid, String providerId);

  /// Get users by authentication provider
  Future<List<UserProfile>> getUsersByAuthProvider(String providerId);

  /// Search users by display name
  Future<List<UserProfile>> searchUsersByDisplayName(String query);

  /// Get user statistics
  Future<Map<String, dynamic>> getUserStats(String uid);

  /// Delete user profile (GDPR compliance)
  Future<void> deleteUserProfile(String uid);

  /// Export user data (GDPR compliance)
  Future<Map<String, dynamic>> exportUserData(String uid);

  /// Listen to user profile changes
  Stream<UserProfile?> watchUserProfile(String uid);

  /// Check if user profile exists
  Future<bool> userExists(String uid);

  // Profile management methods
  /// Update user display name in Firebase Auth and Firestore
  Future<void> updateDisplayName(String displayName);

  /// Update user email in Firebase Auth and Firestore
  Future<void> updateEmail(String email);

  /// Get current user profile
  Future<UserProfile?> getCurrentUserProfile();

  /// Update user profile information
  Future<void> updateUserProfile(UserProfile userProfile);
}
