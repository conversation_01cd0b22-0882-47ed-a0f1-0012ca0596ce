// Repository interfaces for BudApp
//
// This file exports all repository interfaces for easy importing.
// These interfaces define the contract for data operations without
// specifying implementation details.

export 'account_repository.dart';
export 'budget_repository.dart';
export 'category_repository.dart';
export 'goal_repository.dart';
export 'i_goal_contribution_repository.dart';
export 'remote_config_repository.dart';
export 'tag_repository.dart';
export 'transaction_repository.dart';
// Domain-specific repository interfaces
export 'user_repository.dart';

/// Repository interface documentation
///
/// The repository pattern provides a clean separation between the business logic
/// and data access layers. Each repository interface defines:
///
/// 1. **CRUD Operations**: Create, Read, Update, Delete operations
/// 2. **Query Methods**: Specialized queries for domain-specific needs
/// 3. **Real-time Streams**: Live data updates via Firestore listeners
/// 4. **Validation**: Data validation before persistence
/// 5. **Business Logic**: Domain-specific operations and calculations
///
/// ## Repository Interfaces
///
/// ```
/// IUserRepository
/// IAccountRepository
/// ITransactionRepository
/// ICategoryRepository
/// IBudgetRepository
/// ```
///
/// ## Data Models
///
/// Each repository works with strongly-typed data models:
/// - **UserProfile**: User account and preferences
/// - **Account**: Financial accounts (checking, savings, etc.)
/// - **Transaction**: Financial transactions (income, expense, transfer)
/// - **Category**: Transaction categories and subcategories
/// - **Budget**: Budget definitions and tracking
///
/// ## Implementation Notes
///
/// - All monetary amounts are stored as integers in cents to avoid floating-point errors
/// - Dates are stored as ISO 8601 strings for Firestore compatibility
/// - All models include metadata fields for extensibility
/// - Soft deletion is preferred over hard deletion for data integrity
/// - Real-time streams use Firestore listeners for live updates
///
/// ## Usage Example
///
/// ```dart
/// // In a Riverpod provider
/// final userRepositoryProvider = Provider<IUserRepository>((ref) {
///   return UserRepositoryImpl(ref.watch(firestoreProvider));
/// });
///
/// // In a service or controller
/// final userRepo = ref.read(userRepositoryProvider);
/// final user = await userRepo.getUserById('user123');
/// ```
///
/// ## Testing
///
/// Repository interfaces enable easy mocking for unit tests:
///
/// ```dart
/// class MockUserRepository extends Mock implements IUserRepository {}
///
/// final container = ProviderContainer(
///   overrides: [
///     userRepositoryProvider.overrideWithValue(MockUserRepository()),
///   ],
/// );
/// ```
