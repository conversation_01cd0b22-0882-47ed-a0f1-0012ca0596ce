import 'package:budapp/data/models/goal.dart';

/// Repository interface for goal-related data operations
///
/// Provides methods for CRUD operations on financial goals with proper error handling,
/// data validation, and Firestore integration. Supports goal progress tracking,
/// real-time updates through streams, and automatic currentAmountCents management.
abstract class IGoalRepository {
  /// Create a new goal
  ///
  /// Creates a new financial goal for the current user. The goal will be assigned
  /// a unique ID and stored in the user's goals collection.
  ///
  /// Returns the ID of the created goal.
  /// Throws exception if creation fails or validation errors occur.
  Future<String> create(Goal entity);

  /// Update an existing goal
  ///
  /// Updates an existing goal with new data. The goal must belong to the current user.
  /// The updatedAt timestamp will be automatically set.
  ///
  /// Throws exception if the goal doesn't exist or belongs to another user.
  Future<void> update(String id, Goal entity);

  /// Get all goals for a specific user
  ///
  /// Returns all goals (active and inactive) for the specified user.
  /// Goals are ordered by creation date (newest first).
  Future<List<Goal>> getGoalsByUserId(String userId);

  /// Get active goals for a specific user
  ///
  /// Returns only active goals (isActive = true) for the specified user.
  /// Goals are ordered by creation date (newest first).
  Future<List<Goal>> getActiveGoalsByUserId(String userId);

  /// Get active goals for the current user
  ///
  /// Returns only active goals for the currently authenticated user.
  /// Goals are ordered by creation date (newest first).
  Future<List<Goal>> getActiveGoals();

  /// Get goals by status for a specific user
  ///
  /// Returns goals filtered by their status (active, paused, completed, cancelled).
  Future<List<Goal>> getGoalsByStatus(String userId, GoalStatus status);

  /// Get a specific goal by ID
  ///
  /// Returns the goal if found and belongs to the current user, null otherwise.
  /// Throws exception if the goal exists but belongs to a different user.
  Future<Goal?> getGoalById(String id);

  /// Get a specific goal by ID for a user
  ///
  /// Returns the goal if found and belongs to the specified user, null otherwise.
  Future<Goal?> getGoalByIdForUser(String userId, String goalId);

  /// Create a new goal for the current user
  ///
  /// Creates a new goal and automatically assigns the current user ID.
  /// Returns the ID of the created goal.
  Future<String> createGoal(Goal goal);

  /// Update goal details
  ///
  /// Updates an existing goal with new information.
  /// The goal must belong to the current user.
  Future<void> updateGoal(String goalId, Goal goal);

  /// Soft delete a goal (set isActive to false)
  ///
  /// Marks a goal as inactive instead of permanently deleting it.
  /// This preserves the goal data and any associated contributions.
  Future<void> deactivateGoal(String goalId);

  /// Hard delete a goal (permanent removal)
  ///
  /// Permanently removes a goal and all its associated contributions.
  /// This action cannot be undone.
  Future<void> deleteGoal(String goalId);

  /// Reactivate a deactivated goal
  ///
  /// Restores a previously deactivated goal to active status.
  Future<void> reactivateGoal(String goalId);

  /// Update goal progress (currentAmountCents)
  ///
  /// Updates the current progress amount for a goal. This is typically
  /// called when contributions are added, updated, or removed.
  Future<void> updateGoalProgress(String goalId, int newCurrentAmountCents);

  /// Mark goal as completed
  ///
  /// Sets the goal status to completed and isCompleted to true.
  /// This is typically called when currentAmountCents reaches targetAmountCents.
  Future<void> markGoalAsCompleted(String goalId);

  /// Mark goal as active
  ///
  /// Sets the goal status to active and isCompleted to false.
  /// This can be used to reactivate a completed goal.
  Future<void> markGoalAsActive(String goalId);

  /// Search goals by name
  ///
  /// Returns goals that match the search query in their name or description.
  Future<List<Goal>> searchGoalsByName(String userId, String query);

  /// Get goal statistics
  ///
  /// Returns statistics for a specific goal including progress percentage,
  /// remaining amount, days remaining, etc.
  Future<Map<String, dynamic>> getGoalStats(String goalId);

  /// Get user goal summary
  ///
  /// Returns summary statistics for all user goals including total goals,
  /// completed goals, total target amount, total current amount, etc.
  Future<Map<String, dynamic>> getUserGoalSummary(String userId);

  /// Listen to goal changes for a user
  ///
  /// Returns a stream that emits the list of goals whenever they change.
  /// Only includes active goals by default.
  Stream<List<Goal>> watchUserGoals(String userId);

  /// Listen to all goals for a user (including inactive)
  ///
  /// Returns a stream that emits all goals (active and inactive) for a user.
  Stream<List<Goal>> watchAllUserGoals(String userId);

  /// Listen to a specific goal
  ///
  /// Returns a stream that emits updates for a specific goal.
  Stream<Goal?> watchGoal(String goalId);

  /// Listen to a specific goal with user context
  ///
  /// Returns a stream that emits updates for a specific goal,
  /// ensuring it belongs to the specified user.
  Stream<Goal?> watchGoalForUser(String userId, String goalId);

  /// Validate goal before creation/update
  ///
  /// Validates goal data according to business rules.
  /// Returns true if valid, throws exception with details if invalid.
  Future<bool> validateGoal(Goal goal);

  /// Check if goal name is unique for user
  ///
  /// Returns true if the goal name is unique for the user.
  /// Optionally excludes a specific goal ID from the check (for updates).
  Future<bool> isGoalNameUnique(
    String userId,
    String name, {
    String? excludeGoalId,
  });

  /// Get goals that can be deleted (no dependent contributions)
  ///
  /// Returns goals that have no contributions and can be safely deleted.
  Future<List<Goal>> getDeletableGoals(String userId);

  /// Get completed goals for a user
  ///
  /// Returns goals that have been marked as completed.
  Future<List<Goal>> getCompletedGoals(String userId);

  /// Get goals nearing their target date
  ///
  /// Returns goals that are approaching their target date within the specified
  /// number of days. Useful for notifications and reminders.
  Future<List<Goal>> getGoalsNearingDeadline(String userId, int daysAhead);

  /// Get goals by date range
  ///
  /// Returns goals created within the specified date range.
  Future<List<Goal>> getGoalsByDateRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  );
}
