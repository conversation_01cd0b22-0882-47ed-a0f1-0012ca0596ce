import 'package:budapp/data/models/tag.dart';

/// Repository interface for tag operations
abstract class TagRepository {
  /// Watch all tags for the current user
  /// Returns a stream of tag lists that updates in real-time
  Stream<List<Tag>> watchUserTags();

  /// Get a specific tag by ID
  /// Returns null if tag doesn't exist or user doesn't have access
  Future<Tag?> getTagById(String tagId);

  /// Create a new tag
  /// Throws [TagValidationException] if tag data is invalid
  /// Throws [TagAlreadyExistsException] if tag name already exists for user
  Future<Tag> createTag(Tag tag);

  /// Update an existing tag
  /// Throws [TagValidationException] if tag data is invalid
  /// Throws [TagNotFoundException] if tag doesn't exist
  /// Throws [TagAlreadyExistsException] if new name conflicts with existing tag
  Future<Tag> updateTag(Tag tag);

  /// Delete a tag and remove it from all associated transactions
  /// This operation is atomic - either both the tag deletion and
  /// transaction updates succeed, or both fail
  /// Throws [TagNotFoundException] if tag doesn't exist
  Future<void> deleteTag(String tagId);

  /// Remove a tag from all transactions that reference it
  /// Used internally by deleteTag but can be called independently
  /// for cleanup operations
  Future<void> batchRemoveTagFromTransactions(String tagId);

  /// Get usage count for a tag (number of transactions using it)
  /// Used for displaying tag popularity and deletion warnings
  Future<int> getTagUsageCount(String tagId);

  /// Search tags by name
  /// Returns tags that contain the search term (case-insensitive)
  Future<List<Tag>> searchTags(String searchTerm);

  /// Get tags used by a specific transaction
  /// Returns empty list if transaction has no tags
  Future<List<Tag>> getTagsForTransaction(String transactionId);
}

/// Exception thrown when tag validation fails
class TagValidationException implements Exception {
  const TagValidationException(this.message, this.errors);
  final String message;
  final List<String> errors;

  @override
  String toString() => 'TagValidationException: $message';
}

/// Exception thrown when trying to create a tag with a name that already exists
class TagAlreadyExistsException implements Exception {
  const TagAlreadyExistsException(this.tagName);
  final String tagName;

  @override
  String toString() =>
      'TagAlreadyExistsException: Tag "$tagName" already exists';
}

/// Exception thrown when trying to access a tag that doesn't exist
class TagNotFoundException implements Exception {
  const TagNotFoundException(this.tagId);
  final String tagId;

  @override
  String toString() => 'TagNotFoundException: Tag with ID "$tagId" not found';
}
