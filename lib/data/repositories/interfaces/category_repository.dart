import 'package:budapp/data/models/category.dart';

/// Repository interface for category-related data operations
///
/// This interface defines the contract for category data operations
/// following the established repository pattern used throughout BudApp.
abstract class ICategoryRepository {
  /// Create a new category
  Future<String> create(Category entity);

  /// Update an existing category
  Future<void> update(String id, Category entity);

  /// Get all categories for a specific user
  Future<List<Category>> getCategoriesByUserId(String userId);

  /// Get active categories for a specific user
  Future<List<Category>> getActiveCategoriesByUserId(String userId);

  /// Get active categories for the current user
  Future<List<Category>> getActiveCategories();

  /// Get categories by type for a specific user
  Future<List<Category>> getCategoriesByType(String userId, CategoryType type);

  /// Get category by ID with user context
  Future<Category?> getCategoryById(String userId, String categoryId);

  /// Get root categories (no parent) for a specific user
  Future<List<Category>> getRootCategories(String userId);

  /// Get subcategories for a parent category
  Future<List<Category>> getSubcategories(String userId, String parentId);

  /// Create a new category
  Future<String> createCategory(Category category);

  /// Update category details
  Future<void> updateCategory(String categoryId, Category category);

  /// Soft delete a category (set isActive to false)
  Future<void> deactivateCategory(String categoryId);

  /// Hard delete a category (permanent removal)
  Future<void> deleteCategory(String categoryId);

  /// Reactivate a deactivated category
  Future<void> reactivateCategory(String categoryId);

  /// Search categories by name
  Future<List<Category>> searchCategoriesByName(String userId, String query);

  /// Get category statistics
  Future<Map<String, dynamic>> getCategoryStats(String categoryId);

  /// Get user category summary
  Future<Map<String, dynamic>> getUserCategorySummary(String userId);

  /// Listen to category changes for a user
  Stream<List<Category>> watchUserCategories(String userId);

  /// Listen to a specific category
  Stream<Category?> watchCategory(String categoryId);

  /// Listen to a specific category with user context
  Stream<Category?> watchCategoryForUser(String userId, String categoryId);

  /// Validate category before creation/update
  Future<bool> validateCategory(Category category);

  /// Check if category name is unique for user
  Future<bool> isCategoryNameUnique(
    String userId,
    String name, {
    String? excludeCategoryId,
  });

  /// Get categories that can be deleted (no dependent transactions)
  Future<List<Category>> getDeletableCategories(String userId);

  /// Get category tree structure for UI rendering
  Future<List<CategoryTree>> getCategoryTree(String userId);

  /// Get category tree for a specific type
  Future<List<CategoryTree>> getCategoryTreeByType(
    String userId,
    CategoryType type,
  );

  /// Move category to different parent (for reorganizing hierarchy)
  Future<void> moveCategoryToParent(String categoryId, String? newParentId);

  /// Update category sort order
  Future<void> updateCategorySortOrder(String categoryId, int sortOrder);

  /// Get category count for a user
  Future<int> getUserCategoryCount(String userId);

  /// Check if category has dependent transactions
  Future<bool> hasDependentTransactions(String categoryId);

  /// Check if category has dependent transactions with user context
  Future<bool> hasDependentTransactionsForUser(
    String userId,
    String categoryId,
  );

  /// Check if category has active child subcategories
  Future<bool> hasChildSubcategories(String userId, String categoryId);

  /// Delete category with proper constraint checking
  Future<void> deleteCategoryWithConstraints(String userId, String categoryId);

  /// Delete subcategory with proper constraint checking
  Future<void> deleteSubcategoryWithConstraints(
    String userId,
    String subcategoryId,
    String parentId,
  );

  /// Soft delete category with user context
  Future<void> deactivateCategoryWithUserId(String userId, String categoryId);

  /// Validate category hierarchy (prevent circular references)
  Future<bool> validateCategoryHierarchy(String categoryId, String? parentId);

  /// Get category ancestors (parent chain)
  Future<List<Category>> getCategoryAncestors(String categoryId);

  /// Get category descendants (all children recursively)
  Future<List<Category>> getCategoryDescendants(String categoryId);

  // Subcategory-specific operations

  /// Create a new subcategory under a parent category
  Future<String> createSubcategory(Category subcategory, String parentId);

  /// Update an existing subcategory
  Future<void> updateSubcategory(String subcategoryId, Category subcategory);

  /// Delete a subcategory (soft delete)
  Future<void> deleteSubcategory(String subcategoryId, String parentId);

  /// Watch subcategories for a specific parent category
  Stream<List<Category>> watchSubcategories(String userId, String parentId);

  /// Get subcategory count for a parent category
  Future<int> getSubcategoryCount(String userId, String parentId);

  /// Validate subcategory hierarchy (prevent circular references and depth limits)
  Future<bool> validateSubcategoryHierarchy(
    String parentId,
    String subcategoryId,
  );

  /// Get the depth of a category in the hierarchy (0 = root, 1 = subcategory, etc.)
  Future<int> getCategoryDepth(String userId, String categoryId);

  /// Move a subcategory to a different parent
  Future<void> moveSubcategoryToParent(
    String subcategoryId,
    String oldParentId,
    String newParentId,
  );

  /// Check if a category can have subcategories (business rules)
  Future<bool> canHaveSubcategories(String userId, String categoryId);

  /// Validate subcategory name uniqueness within parent
  Future<bool> isSubcategoryNameUnique(
    String userId,
    String parentId,
    String name, {
    String? excludeSubcategoryId,
  });
}
