import 'package:budapp/data/models/budget.dart';

/// Repository interface for budget-related data operations
///
/// Provides methods for CRUD operations on budgets with proper error handling,
/// data validation, and Firestore integration. Supports budget hierarchy,
/// overlap prevention, and real-time updates through streams.
abstract class BudgetRepository {
  /// Watch all budgets for the current user
  ///
  /// Returns a stream of budget list that updates in real-time when budgets
  /// are created, updated, or deleted. Only returns active budgets.
  Stream<List<Budget>> watchBudgets();

  /// Get a specific budget by ID
  ///
  /// Returns the budget if found, null otherwise. Throws exception if
  /// the budget exists but belongs to a different user.
  Future<Budget?> getBudgetById(String id);

  /// Create a new budget
  ///
  /// Validates the budget data and checks for overlaps before creation.
  /// Throws exception if validation fails or overlap is detected.
  /// Returns the created budget with generated ID.
  Future<Budget> createBudget(Budget budget);

  /// Update an existing budget
  ///
  /// Validates the updated budget data and checks for overlaps.
  /// Throws exception if validation fails, budget not found, or overlap detected.
  Future<Budget> updateBudget(Budget budget);

  /// Delete a budget (soft delete)
  ///
  /// Sets isActive to false instead of hard deletion to preserve data integrity.
  /// Throws exception if budget not found or doesn't belong to current user.
  Future<void> deleteBudget(String id);

  /// Update budget status (enhanced soft deletion)
  ///
  /// Updates the isActive status of a budget. Can be used to restore deleted budgets
  /// or to implement more granular status management.
  /// Throws exception if budget not found or doesn't belong to current user.
  Future<void> updateBudgetStatus(String id, {required bool isActive});

  /// Watch budgets for a specific month
  ///
  /// Returns a stream of budgets that are active during the specified month.
  /// Useful for monthly budget views and filtering.
  Stream<List<Budget>> watchBudgetsByMonth(DateTime month);

  /// Validate budget for overlap prevention
  ///
  /// Checks if the budget would overlap with existing budgets for the same
  /// category and period. Returns true if valid (no overlap), false otherwise.
  /// Used for client-side validation before creation/update.
  Future<bool> validateBudgetOverlap(Budget budget);

  /// Get budgets for a specific period and period type
  ///
  /// Returns all active budgets that match the specified period and period type.
  /// Used for budget copying and period-specific filtering.
  Future<List<Budget>> getBudgetsByPeriod(
    DateTime period,
    BudgetPeriod periodType,
  );

  /// Get all budgets for the current user (including inactive ones)
  ///
  /// Returns all budgets regardless of status. Used for validation and
  /// administrative operations.
  Future<List<Budget>> getAllBudgets();

  /// Batch create multiple budgets
  ///
  /// Creates multiple budgets in a single atomic operation.
  /// Used for bulk operations like copying budgets from previous periods.
  Future<void> batchCreateBudgets(List<Budget> budgets);

  /// Batch update multiple budgets
  ///
  /// Updates multiple budgets in a single atomic operation.
  /// Used for bulk operations like percentage adjustments or status changes.
  Future<void> batchUpdateBudgets(List<Budget> budgets);

  /// Batch delete multiple budgets
  ///
  /// Deletes multiple budgets in a single atomic operation.
  /// Used for bulk deletion operations.
  Future<void> batchDeleteBudgets(List<String> budgetIds);

  /// Find the latest budget for a category from a previous period
  ///
  /// Used for fallback logic when no budget exists for the current period.
  /// Returns the most recent budget for the same category and type that is
  /// from a period before the target period.
  Future<Budget?> findLatestBudgetFromPreviousPeriod(
    String? categoryId,
    BudgetType budgetType,
    DateTime targetPeriod,
    BudgetPeriod periodType,
  );
}
