// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get welcomeBack => 'Welcome Back';

  @override
  String get signInToAccount => 'Sign in to your account';

  @override
  String get email => 'Email';

  @override
  String get enterEmailAddress => 'Enter your email address';

  @override
  String get enterYourEmail => 'Enter your email';

  @override
  String get password => 'Password';

  @override
  String get enterPassword => 'Enter your password';

  @override
  String get createPassword => 'Create a password';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get signIn => 'Sign In';

  @override
  String get orContinueWith => 'Or continue with';

  @override
  String get continueWithGoogle => 'Continue with Google';

  @override
  String get continueWithApple => 'Continue with Apple';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get signUp => 'Sign Up';

  @override
  String get signUpToGetStarted => 'Sign up to get started';

  @override
  String get createAccount => 'Create Account';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get confirmPasswordHint => 'Re-enter your password';

  @override
  String get confirmYourPassword => 'Confirm your password';

  @override
  String get iAgreeToThe => 'I agree to the ';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get and => ' and ';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get pleaseAcceptTermsAndPrivacy =>
      'Please accept the Terms of Service and Privacy Policy';

  @override
  String welcomeToApp(String name) {
    return 'Welcome, $name!';
  }

  @override
  String get appleSignupSuccessful => 'Apple signup successful!';

  @override
  String appleSignupFailed(String error) {
    return 'Apple signup failed: $error';
  }

  @override
  String get accountCreatedSuccessfully =>
      'Account created successfully! Please check your email to verify your account.';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get environment => 'Environment:';

  @override
  String get firebaseProject => 'Firebase Project:';

  @override
  String get testFirebaseServices => 'Test Firebase Services';

  @override
  String get firebaseServicesStatus => 'Firebase Services Status:';

  @override
  String get firebaseAuth => 'Firebase Auth:';

  @override
  String get firestore => 'Firestore:';

  @override
  String get signOut => 'Sign Out';

  @override
  String signOutFailed(String error) {
    return 'Sign out failed: $error';
  }

  @override
  String get tryAgain => 'Try Again';

  @override
  String get suggestions => 'Suggestions:';

  @override
  String get or => 'OR';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get checkYourEmail => 'Check Your Email';

  @override
  String get enterEmailForReset =>
      'Enter your email to receive a password reset link';

  @override
  String get passwordResetEmailSent =>
      'We\'ve sent a password reset link to your email';

  @override
  String get sendResetLink => 'Send Reset Link';

  @override
  String get passwordResetEmailSentSuccess =>
      'Password reset email sent! Please check your inbox.';

  @override
  String get unexpectedError =>
      'An unexpected error occurred. Please try again.';

  @override
  String get userNotFound => 'No account found with this email address.';

  @override
  String get invalidEmail => 'Please enter a valid email address.';

  @override
  String get tooManyRequests => 'Too many requests. Please try again later.';

  @override
  String get emailSentSuccessfully => 'Email Sent Successfully!';

  @override
  String checkEmailForResetLink(String email) {
    return 'Please check your email ($email) and click the reset link to create a new password.';
  }

  @override
  String get checkSpamFolder =>
      'Don\'t forget to check your spam folder if you don\'t see the email.';

  @override
  String get resendEmail => 'Resend Email';

  @override
  String get backToSignIn => 'Back to Sign In';

  @override
  String get verifyYourEmail => 'Verify Your Email';

  @override
  String get verificationEmailSentTo => 'We\'ve sent a verification email to:';

  @override
  String get checkEmailForVerification =>
      'Please check your email and click the verification link to continue. You may need to check your spam folder.';

  @override
  String get resendVerificationEmail => 'Resend Verification Email';

  @override
  String get verifiedMyEmail => 'I\'ve Verified My Email';

  @override
  String get welcome => 'Welcome!';

  @override
  String get noEmail => 'No email';

  @override
  String get counterDescription =>
      'You have pushed the button this many times:';

  @override
  String get increment => 'Increment';

  @override
  String get fieldRequired => 'This field is required';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get validEmailRequired => 'Please enter a valid email address';

  @override
  String get passwordRequired => 'Password is required';

  @override
  String get passwordMinLength => 'Password must be at least 6 characters';

  @override
  String get confirmPasswordRequired => 'Please confirm your password';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get budApp => 'BudApp';

  @override
  String get welcomeBackSuccess => 'Welcome back!';

  @override
  String get googleSignInSuccess => 'Welcome! Signed in with Google.';

  @override
  String get appleLoginSuccessful => 'Apple login successful!';

  @override
  String get retry => 'Retry';

  @override
  String get information => 'Information';

  @override
  String get warning => 'Warning';

  @override
  String get error => 'Error';

  @override
  String get criticalError => 'Critical Error';

  @override
  String get accounts => 'Accounts';

  @override
  String get addAccount => 'Add Account';

  @override
  String get filterAccounts => 'Filter Accounts';

  @override
  String get sort => 'Sort';

  @override
  String get settings => 'Settings';

  @override
  String get assets => 'Assets';

  @override
  String get liabilities => 'Liabilities';

  @override
  String get totalAssets => 'Total Assets';

  @override
  String get netWorth => 'Net Worth';

  @override
  String get errorLoadingAccounts => 'Error Loading Accounts';

  @override
  String get sortBy => 'Sort by';

  @override
  String get name => 'Name';

  @override
  String get balance => 'Balance';

  @override
  String get dateCreated => 'Date Created';

  @override
  String get primary => 'Primary';

  @override
  String get active => 'Active';

  @override
  String get inactive => 'Inactive';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get checkingAccount => 'Checking Account';

  @override
  String get savingsAccount => 'Savings Account';

  @override
  String get creditCard => 'Credit Card';

  @override
  String get cashAccount => 'Cash Account';

  @override
  String get investmentAccount => 'Investment Account';

  @override
  String get loanAccount => 'Loan Account';

  @override
  String get clearAll => 'Clear All';

  @override
  String get accountType => 'Account Type';

  @override
  String get allAccounts => 'All Accounts';

  @override
  String get checking => 'Checking';

  @override
  String get savings => 'Savings';

  @override
  String get cash => 'Cash';

  @override
  String get investment => 'Investment';

  @override
  String get loan => 'Loan';

  @override
  String get showInactiveAccounts => 'Show Inactive Accounts';

  @override
  String get includeInactiveAccountsInList =>
      'Include accounts that have been deactivated';

  @override
  String get apply => 'Apply';

  @override
  String get noAccountsYet => 'No accounts yet';

  @override
  String get noAccountsDescription =>
      'Add your first account to start tracking your finances';

  @override
  String get createFirstAccount => 'Create First Account';

  @override
  String get learnAboutAccounts => 'Learn about accounts';

  @override
  String get aboutAccounts => 'About Accounts';

  @override
  String get accountsHelpDescription =>
      'Accounts represent your financial institutions and cash holdings. Track balances across different account types.';

  @override
  String get accountTypesTitle => 'Account Types:';

  @override
  String get checkingDescription => 'Daily spending and bill payments';

  @override
  String get savingsDescription => 'Long-term savings and emergency funds';

  @override
  String get creditCardDescription => 'Credit cards and revolving credit';

  @override
  String get cashDescription => 'Physical cash and digital wallets';

  @override
  String get gotIt => 'Got it';

  @override
  String get accountNameRequired => 'Account name is required';

  @override
  String get accountNameMinLength =>
      'Account name must be at least 2 characters';

  @override
  String get accountNameMaxLength => 'Account name cannot exceed 50 characters';

  @override
  String get accountNameInvalidCharacters =>
      'Account name contains invalid characters';

  @override
  String get accountDescriptionMaxLength =>
      'Account description cannot exceed 200 characters';

  @override
  String get initialBalanceRequired => 'Initial balance is required';

  @override
  String get initialBalanceInvalid => 'Please enter a valid balance amount';

  @override
  String get initialBalanceOutOfRange => 'Balance amount is too large';

  @override
  String get initialBalanceMaxDecimals =>
      'Balance can have at most 2 decimal places';

  @override
  String get currencyCodeRequired => 'Currency code is required';

  @override
  String get currencyCodeInvalid =>
      'Please enter a valid 3-letter currency code';

  @override
  String get currencyCodeUnsupported => 'Currency code is not supported';

  @override
  String get accountTypeRequired => 'Account type is required';

  @override
  String get accountClassificationRequired =>
      'Account classification is required';

  @override
  String get accountTypeClassificationMismatch =>
      'Account type and classification don\'t match';

  @override
  String get colorHexInvalid =>
      'Please enter a valid hex color code (e.g., #FF5733)';

  @override
  String get iconNameInvalid => 'Please enter a valid icon name';

  @override
  String get save => 'Save';

  @override
  String get accountName => 'Account Name';

  @override
  String get accountNameHint => 'e.g., Main Checking';

  @override
  String get initialBalance => 'Initial Balance';

  @override
  String get currency => 'Currency';

  @override
  String get accountDescriptionHint => 'Optional description for this account';

  @override
  String get customize => 'Customize';

  @override
  String get optional => '(optional)';

  @override
  String get accountColor => 'Account Color';

  @override
  String get defaultColor => 'Default';

  @override
  String get accountIcon => 'Account Icon';

  @override
  String get defaultIcon => 'Default';

  @override
  String get creating => 'Creating...';

  @override
  String get invalidBalance => 'Invalid balance amount';

  @override
  String get errorCreatingAccount =>
      'Error creating account. Please try again.';

  @override
  String get creditCardAccount => 'Credit Card Account';

  @override
  String get description => 'Description';

  @override
  String get accountDetails => 'Account Details';

  @override
  String get editAccount => 'Edit Account';

  @override
  String get setPrimary => 'Set as Primary';

  @override
  String get deactivateAccount => 'Deactivate Account';

  @override
  String get deleteAccount => 'Delete Account';

  @override
  String get currentBalance => 'Current Balance';

  @override
  String get classification => 'Classification';

  @override
  String get lastUpdated => 'Last Updated';

  @override
  String get accountSettings => 'Account Settings';

  @override
  String get primaryAccount => 'Primary Account';

  @override
  String get recentTransactions => 'Recent Transactions';

  @override
  String get viewAll => 'View All';

  @override
  String get noTransactionsYet => 'No transactions yet';

  @override
  String get transactionsWillAppearHere =>
      'Transactions for this account will appear here';

  @override
  String get accountNotFound => 'Account Not Found';

  @override
  String get accountMayHaveBeenDeleted =>
      'This account may have been deleted or you don\'t have permission to view it';

  @override
  String get goBack => 'Go Back';

  @override
  String get errorLoadingAccount => 'Error Loading Account';

  @override
  String get setPrimaryAccount => 'Set Primary Account';

  @override
  String get setPrimaryAccountConfirmation =>
      'Are you sure you want to set this as your primary account?';

  @override
  String get deactivateAccountConfirmation =>
      'Are you sure you want to deactivate this account? It will be hidden from your account list.';

  @override
  String get deleteAccountConfirmation =>
      'Are you sure you want to delete this account? This action cannot be undone.';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';

  @override
  String get asset => 'Asset';

  @override
  String get liability => 'Liability';

  @override
  String get updating => 'Updating...';

  @override
  String get updateAccount => 'Update Account';

  @override
  String get accountUpdatedSuccessfully => 'Account updated successfully';

  @override
  String get errorUpdatingAccount =>
      'Error updating account. Please try again.';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get manageAccounts => 'Manage Accounts';

  @override
  String get viewAndEditAccounts => 'View and edit your financial accounts';

  @override
  String get transactions => 'Transactions';

  @override
  String get manageTransactions => 'Manage Transactions';

  @override
  String get addAndViewTransactions =>
      'Add and view your financial transactions';

  @override
  String get budgets => 'Budgets';

  @override
  String get manageBudgets => 'Manage Budgets';

  @override
  String get createAndTrackBudgets => 'Create and track your spending budgets';

  @override
  String get createBudget => 'Create Budget';

  @override
  String get editBudget => 'Edit Budget';

  @override
  String get budgetName => 'Budget Name';

  @override
  String get budgetAmount => 'Budget Amount';

  @override
  String get budgetAmountHint => 'Enter the budget amount';

  @override
  String get budgetAmountRequired => 'Budget amount is required';

  @override
  String get budgetPeriod => 'Budget Period';

  @override
  String get monthly => 'Monthly';

  @override
  String get yearly => 'Yearly';

  @override
  String get budgetCategory => 'Category';

  @override
  String get allCategories => 'All Categories';

  @override
  String get budgetDescription => 'Description (Optional)';

  @override
  String get budgetDescriptionHint => 'Add notes about this budget';

  @override
  String get createFirstBudget => 'Create Your First Budget';

  @override
  String get noBudgetsYet => 'No budgets yet';

  @override
  String get noBudgetsDescription =>
      'Create budgets to track your spending and stay on top of your finances.';

  @override
  String get noBudgetsForMonth => 'No budgets for this month';

  @override
  String get viewAllBudgets => 'View All Budgets';

  @override
  String get budgetCreated => 'Budget created successfully';

  @override
  String get budgetUpdated => 'Budget updated successfully';

  @override
  String get budgetDeleted => 'Budget deleted successfully';

  @override
  String get errorCreatingBudget => 'Failed to create budget';

  @override
  String get errorUpdatingBudget => 'Failed to update budget';

  @override
  String get errorDeletingBudget => 'Failed to delete budget';

  @override
  String get errorLoadingBudget => 'Failed to load budget';

  @override
  String get deleteBudget => 'Delete Budget';

  @override
  String get deleteBudgetConfirmation =>
      'Are you sure you want to delete this budget? This action cannot be undone.';

  @override
  String get discardChanges => 'Discard Changes';

  @override
  String get discardChangesConfirmation =>
      'You have unsaved changes. Are you sure you want to discard them?';

  @override
  String get keepEditing => 'Keep Editing';

  @override
  String get discard => 'Discard';

  @override
  String get saveChanges => 'Save Changes';

  @override
  String get spent => 'Spent';

  @override
  String get remaining => 'Remaining';

  @override
  String get overBudget => 'Over Budget';

  @override
  String get onTrack => 'On Track';

  @override
  String get nearLimit => 'Near Limit';

  @override
  String get goals => 'Goals';

  @override
  String get manageGoals => 'Manage Goals';

  @override
  String get setAndTrackGoals => 'Set and track your financial goals';

  @override
  String get comingSoon => 'Coming Soon';

  @override
  String get profile => 'Profile';

  @override
  String get importExport => 'Import/Export';

  @override
  String get reports => 'Reports';

  @override
  String get help => 'Help';

  @override
  String get feedback => 'Feedback';

  @override
  String get categories => 'Categories';

  @override
  String get addCategory => 'Add Category';

  @override
  String get filterCategories => 'Filter Categories';

  @override
  String get categoryType => 'Category Type';

  @override
  String get incomeCategories => 'Income Categories';

  @override
  String get expenseCategories => 'Expense Categories';

  @override
  String get customCategories => 'Custom Categories';

  @override
  String get predefinedCategories => 'Predefined Categories';

  @override
  String get showInactiveCategories => 'Show Inactive Categories';

  @override
  String get includeInactiveCategoriesInList =>
      'Include categories that have been deactivated';

  @override
  String get noCategoriesYet => 'No categories yet';

  @override
  String get noCategoriesDescription =>
      'Add your first category to start organizing your transactions';

  @override
  String get createFirstCategory => 'Create First Category';

  @override
  String get learnAboutCategories => 'Learn about categories';

  @override
  String get aboutCategories => 'About Categories';

  @override
  String get categoriesHelpDescription =>
      'Categories help you organize and track your income and expenses. Create custom categories or use predefined ones.';

  @override
  String get categoryTypesTitle => 'Category Types:';

  @override
  String get incomeDescription => 'Money coming in (salary, investments, etc.)';

  @override
  String get expenseDescription => 'Money going out (bills, purchases, etc.)';

  @override
  String get subcategoriesDescription =>
      'Create subcategories to organize your categories further';

  @override
  String get errorLoadingCategories => 'Error Loading Categories';

  @override
  String get categoryDetails => 'Category Details';

  @override
  String get editCategory => 'Edit Category';

  @override
  String get addSubcategory => 'Add Subcategory';

  @override
  String get deactivateCategory => 'Deactivate Category';

  @override
  String get deleteCategory => 'Delete Category';

  @override
  String get categoryName => 'Category Name';

  @override
  String get categoryNameHint => 'Enter category name';

  @override
  String get categoryDescriptionHint => 'Enter description (optional)';

  @override
  String get categoryColor => 'Category Color';

  @override
  String get categoryIcon => 'Category Icon';

  @override
  String get parentCategory => 'Parent Category';

  @override
  String get rootCategory => 'Root Category';

  @override
  String get subcategories => 'Subcategories';

  @override
  String subcategoryCount(int count) {
    return '$count subcategories';
  }

  @override
  String get noSubcategories => 'No subcategories';

  @override
  String get expandCategory => 'Expand category';

  @override
  String get collapseCategory => 'Collapse category';

  @override
  String get categoryNotFound => 'Category Not Found';

  @override
  String get categoryMayHaveBeenDeleted =>
      'This category may have been deleted or you may not have permission to access it.';

  @override
  String get errorLoadingCategory => 'Error Loading Category';

  @override
  String get deactivateCategoryConfirmation =>
      'Are you sure you want to deactivate this category? It will be hidden from your category list.';

  @override
  String get deleteCategoryConfirmation =>
      'Are you sure you want to delete this category? This action cannot be undone.';

  @override
  String cannotDeleteCategoryWithTransactions(String categoryName) {
    return 'Cannot delete category \"$categoryName\": it has associated transactions. Please reassign or delete the transactions first.';
  }

  @override
  String cannotDeleteCategoryWithSubcategories(String categoryName) {
    return 'Cannot delete category \"$categoryName\": it has subcategories. Please delete or move the subcategories first.';
  }

  @override
  String cannotDeleteSubcategoryWithTransactions(String subcategoryName) {
    return 'Cannot delete subcategory \"$subcategoryName\": it has associated transactions. Please reassign or delete the transactions first.';
  }

  @override
  String cannotDeleteSubcategoryWithChildren(String subcategoryName) {
    return 'Cannot delete subcategory \"$subcategoryName\": it has child subcategories. Please delete or move the child subcategories first.';
  }

  @override
  String get subcategoryNotBelongToParent =>
      'Subcategory does not belong to the specified parent category.';

  @override
  String errorCheckingDeletionConstraints(String error) {
    return 'Error checking deletion constraints: $error';
  }

  @override
  String failedToDeleteCategory(String error) {
    return 'Failed to delete category: $error';
  }

  @override
  String failedToDeleteSubcategory(String error) {
    return 'Failed to delete subcategory: $error';
  }

  @override
  String get categorySource => 'Category Source';

  @override
  String get custom => 'Custom';

  @override
  String get predefined => 'Predefined';

  @override
  String get income => 'Income';

  @override
  String get expense => 'Expense';

  @override
  String get manageCategories => 'Manage Categories';

  @override
  String get createCategory => 'Create Category';

  @override
  String get createSubcategory => 'Create Subcategory';

  @override
  String get editCategoryTitle => 'Edit Category';

  @override
  String get editSubcategoryTitle => 'Edit Subcategory';

  @override
  String get categoryNameTooShort =>
      'Category name must be at least 2 characters';

  @override
  String get categoryNameTooLong =>
      'Category name must be 50 characters or less';

  @override
  String get categoryNameInvalidCharacters =>
      'Category name can only contain letters, numbers, spaces, hyphens, and apostrophes';

  @override
  String get categoryDescriptionTooLong =>
      'Description must be 200 characters or less';

  @override
  String get categoryTypeRequired => 'Please select a category type';

  @override
  String get categoryColorInvalidFormat =>
      'Color must be in hex format (#RRGGBB)';

  @override
  String get categoryIconInvalidFormat =>
      'Icon must be a valid identifier (letters, numbers, underscores)';

  @override
  String get parentCategoryRequired => 'Please select a parent category';

  @override
  String get categoryCreatedSuccessfully => 'Category created successfully';

  @override
  String get categoryUpdatedSuccessfully => 'Category updated successfully';

  @override
  String get errorCreatingCategory => 'Error creating category';

  @override
  String get errorUpdatingCategory => 'Error updating category';

  @override
  String get selectCategoryType => 'Select Category Type';

  @override
  String get selectParentCategory => 'Select Parent Category';

  @override
  String get noParentCategory => 'No Parent (Root Category)';

  @override
  String get selectColor => 'Select Color';

  @override
  String get selectIcon => 'Select Icon';

  @override
  String get organizeTransactions =>
      'Organize your transactions with custom categories';

  @override
  String get close => 'Close';

  @override
  String get activate => 'Activate';

  @override
  String get categoryDeletionConstraints => 'Category Deletion Constraints';

  @override
  String categoryHasTransactions(int count) {
    return 'This category has $count associated transactions';
  }

  @override
  String categoryHasSubcategories(int count) {
    return 'This category has $count subcategories';
  }

  @override
  String get reassignTransactions => 'Reassign Transactions';

  @override
  String get deleteAnyway => 'Delete Anyway';

  @override
  String get selectNewCategory => 'Select New Category';

  @override
  String get selectCategoryForReassignment =>
      'Select a category to reassign transactions to:';

  @override
  String get noAvailableCategories =>
      'No available categories for reassignment';

  @override
  String get reassignmentConfirmation => 'Reassign and Delete';

  @override
  String get reassigningTransactions => 'Reassigning transactions...';

  @override
  String get transactionsReassignedSuccessfully =>
      'Transactions reassigned successfully';

  @override
  String get errorReassigningTransactions => 'Error reassigning transactions';

  @override
  String get categoryDeletedSuccessfully => 'Category deleted successfully';

  @override
  String get viewTransactions => 'View Transactions';

  @override
  String get transactionList => 'Transaction List';

  @override
  String transactionsInCategory(String categoryName) {
    return 'Transactions in \"$categoryName\"';
  }

  @override
  String get loading => 'Loading...';

  @override
  String get addTransaction => 'Add Transaction';

  @override
  String get transactionType => 'Transaction Type';

  @override
  String get transfer => 'Transfer';

  @override
  String get amount => 'Amount';

  @override
  String get toAccount => 'To Account';

  @override
  String get fromAccount => 'From Account';

  @override
  String get category => 'Category';

  @override
  String get date => 'Date';

  @override
  String get selectDate => 'Select Date';

  @override
  String get notes => 'Notes';

  @override
  String get required => 'Required';

  @override
  String get amountRequired => 'Amount is required';

  @override
  String get amountInvalid => 'Please enter a valid amount';

  @override
  String get invalidNumber => 'Please enter a valid number';

  @override
  String get pleaseSelect => 'Please select';

  @override
  String get noItemsAvailable => 'No items available';

  @override
  String get errorLoadingData => 'Error loading data';

  @override
  String get networkError =>
      'Network connection error. Please check your internet connection and try again.';

  @override
  String get permissionError =>
      'You don\'t have permission to perform this action.';

  @override
  String get notFoundError => 'The requested item was not found.';

  @override
  String get genericError => 'An unexpected error occurred. Please try again.';

  @override
  String get userNotFoundError => 'No user found with this email address.';

  @override
  String get wrongPasswordError => 'Incorrect password. Please try again.';

  @override
  String get emailAlreadyInUseError =>
      'An account already exists with this email address.';

  @override
  String get weakPasswordError =>
      'Password is too weak. Please choose a stronger password.';

  @override
  String get invalidEmailError => 'Please enter a valid email address.';

  @override
  String get userDisabledError => 'This account has been disabled.';

  @override
  String get tooManyRequestsError =>
      'Too many attempts. Please try again later.';

  @override
  String get operationNotAllowedError => 'This operation is not allowed.';

  @override
  String get requiresRecentLoginError => 'Please sign in again to continue.';

  @override
  String get authenticationError => 'Authentication failed. Please try again.';

  @override
  String get permissionDeniedError =>
      'You don\'t have permission to perform this action.';

  @override
  String get serviceUnavailableError =>
      'Service is temporarily unavailable. Please try again later.';

  @override
  String get timeoutError => 'Request timed out. Please try again.';

  @override
  String get documentNotFoundError => 'Document not found.';

  @override
  String get documentAlreadyExistsError => 'Document already exists.';

  @override
  String get quotaExceededError =>
      'Service quota exceeded. Please try again later.';

  @override
  String get preconditionFailedError => 'Operation failed due to precondition.';

  @override
  String get operationAbortedError =>
      'Operation was aborted. Please try again.';

  @override
  String get outOfRangeError => 'Value is out of range.';

  @override
  String get featureNotImplementedError => 'Feature not implemented.';

  @override
  String get internalError => 'Internal server error. Please try again.';

  @override
  String get dataLossError => 'Data loss detected. Please contact support.';

  @override
  String get unauthenticatedError => 'Please sign in to continue.';

  @override
  String get databaseError => 'Database error. Please try again.';

  @override
  String get invalidDateFormatError => 'Invalid date format.';

  @override
  String get invalidNumberFormatError => 'Invalid number format.';

  @override
  String get invalidFormatError => 'Invalid format.';

  @override
  String get invalidArgumentError => 'Invalid argument provided.';

  @override
  String get dismiss => 'Dismiss';

  @override
  String get errorDialogTitle => 'Error';

  @override
  String get ok => 'OK';

  @override
  String get amountTooLarge => 'Amount is too large';

  @override
  String get accountRequired => 'Please select an account';

  @override
  String get categoryRequired => 'Please select a category';

  @override
  String get dateRequired => 'Date is required';

  @override
  String get descriptionTooLong => 'Description must be 100 characters or less';

  @override
  String get notesTooLong => 'Notes must be 500 characters or less';

  @override
  String get transactionSaved => 'Transaction saved successfully';

  @override
  String get errorSavingTransaction =>
      'Error saving transaction. Please try again.';

  @override
  String get selectAccount => 'Select Account';

  @override
  String get selectCategory => 'Select Category';

  @override
  String get noCategory => 'No Category';

  @override
  String get enterAmount => 'Enter amount';

  @override
  String get enterDescription => 'Enter description (optional)';

  @override
  String get enterNotes => 'Enter additional notes (optional)';

  @override
  String get saving => 'Saving...';

  @override
  String get createTransaction => 'Create Transaction';

  @override
  String get updateTransaction => 'Update Transaction';

  @override
  String get transactionCreated => 'Transaction created successfully';

  @override
  String get transactionUpdated => 'Transaction updated successfully';

  @override
  String get transactionDeleted => 'Transaction deleted successfully';

  @override
  String get deletingTransaction => 'Deleting transaction...';

  @override
  String get transactionDeleteError => 'Failed to delete transaction';

  @override
  String get transactionOperationError =>
      'Transaction operation failed. Please try again.';

  @override
  String get operationTimeoutError => 'Operation timed out. Please try again.';

  @override
  String get transactionNotFoundError =>
      'Transaction not found or has been deleted.';

  @override
  String get transactionAlreadyExistsError => 'Transaction already exists.';

  @override
  String get operationFailedError =>
      'Operation failed due to invalid conditions.';

  @override
  String get invalidDataError => 'Invalid data provided.';

  @override
  String get featureNotAvailableError => 'This feature is not available.';

  @override
  String get internalServerError =>
      'Internal server error. Please try again later.';

  @override
  String get dataCorruptionError =>
      'Data corruption detected. Please contact support.';

  @override
  String get authenticationRequiredError =>
      'Authentication required. Please sign in again.';

  @override
  String get firestoreError => 'Database error';

  @override
  String get accountNotFoundError => 'Account not found or has been deleted.';

  @override
  String get invalidAmountError => 'Invalid amount provided.';

  @override
  String get validationError => 'Please check your input and try again.';

  @override
  String get transactionCreateError => 'Failed to create transaction';

  @override
  String get sameAccountError =>
      'Source and destination accounts must be different';

  @override
  String get destinationAccountRequired => 'Destination account is required';

  @override
  String get filterTransactions => 'Filter Transactions';

  @override
  String get searchTransactions => 'Search Transactions';

  @override
  String get searchTransactionsHint => 'Search by description or notes...';

  @override
  String get noTransactionsFound => 'No transactions found';

  @override
  String get tryAdjustingFilters => 'Try adjusting your search or filters';

  @override
  String get clearFilters => 'Clear Filters';

  @override
  String get errorLoadingTransactions => 'Error loading transactions';

  @override
  String get noTransactionsDescription =>
      'Start tracking your finances by adding your first transaction';

  @override
  String get addFirstTransaction => 'Add First Transaction';

  @override
  String get quickTips => 'Quick Tips';

  @override
  String get tipCreateAccounts =>
      'Create accounts for your bank accounts, credit cards, and cash';

  @override
  String get tipCreateCategories =>
      'Set up categories to organize your income and expenses';

  @override
  String get tipRecordTransactions =>
      'Record transactions to track your money flow';

  @override
  String get transactionNotFound => 'Transaction Not Found';

  @override
  String get transactionMayHaveBeenDeleted =>
      'This transaction may have been deleted or you don\'t have permission to view it';

  @override
  String get deleteTransaction => 'Delete Transaction';

  @override
  String get deleteTransactionConfirmation =>
      'Are you sure you want to delete this transaction? This action cannot be undone.';

  @override
  String get editTransaction => 'Edit Transaction';

  @override
  String get errorLoadingTransaction => 'Error loading transaction';

  @override
  String get transactionUpdateError => 'Failed to update transaction';

  @override
  String get errorUpdatingTransaction =>
      'Error updating transaction. Please try again.';

  @override
  String get transactionDetails => 'Transaction Details';

  @override
  String get failed => 'Failed';

  @override
  String get accountsSection => 'Accounts';

  @override
  String get categorySection => 'Category';

  @override
  String get notesSection => 'Notes';

  @override
  String get metadataSection => 'Metadata';

  @override
  String get createdAt => 'Created';

  @override
  String get updatedAt => 'Updated';

  @override
  String get noNotes => 'No notes';

  @override
  String get errorLoadingTransactionDetails =>
      'Error loading transaction details';

  @override
  String get pending => 'Pending';

  @override
  String get completed => 'Completed';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get all => 'All';

  @override
  String get search => 'Search';

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get metadata => 'Metadata';

  @override
  String get transactionId => 'Transaction ID';

  @override
  String get status => 'Status';

  @override
  String get tags => 'Tags';

  @override
  String get from => 'From';

  @override
  String get to => 'To';

  @override
  String get biometricAuthentication => 'Biometric Authentication';

  @override
  String get signInWithBiometrics => 'Sign in with biometrics';

  @override
  String get useBiometrics => 'Use biometrics';

  @override
  String get biometricUnknownError =>
      'An unknown biometric error occurred. Please try again.';

  @override
  String get biometricPasscodeNotSet =>
      'Please set up a device passcode or PIN to use biometric authentication.';

  @override
  String get biometricNotEnrolled =>
      'No biometric data is enrolled on this device. Please set up Face ID, Touch ID, or fingerprint authentication in your device settings.';

  @override
  String get biometricNotAvailable =>
      'Biometric authentication is not available on this device.';

  @override
  String get biometricUserCancelled =>
      'Biometric authentication was cancelled.';

  @override
  String get biometricUserFallback =>
      'Biometric authentication failed. Please use your password to sign in.';

  @override
  String get biometricSystemCancelled =>
      'Biometric authentication was cancelled by the system. Please try again.';

  @override
  String get biometricInvalidContext =>
      'Biometric authentication context is invalid. Please try again.';

  @override
  String get biometricLockedOut =>
      'Biometric authentication is temporarily locked due to too many failed attempts. Please try again later.';

  @override
  String get biometricPermanentlyLockedOut =>
      'Biometric authentication is permanently locked. Please use your password to sign in.';

  @override
  String get biometricTooManyAttempts =>
      'Too many failed biometric attempts. Please wait before trying again.';

  @override
  String get biometricPlatformError =>
      'A biometric platform error occurred. Please try again or use your password.';

  @override
  String get enableBiometricAuth => 'Enable Biometric Authentication';

  @override
  String get biometricAuthEnabled => 'Biometric authentication is enabled';

  @override
  String get biometricAuthDisabled => 'Biometric authentication is disabled';

  @override
  String get setupBiometrics => 'Set up biometrics';

  @override
  String get biometricSetupRequired => 'Biometric Setup Required';

  @override
  String get biometricSetupMessage =>
      'To use biometric authentication, please set up Face ID, Touch ID, or fingerprint authentication in your device settings.';

  @override
  String get goToSettings => 'Go to Settings';

  @override
  String get authenticationSettings => 'Authentication Settings';

  @override
  String get securitySettings => 'Security Settings';

  @override
  String get biometricSettingsDescription =>
      'Use your fingerprint, face, or other biometric data to quickly and securely access your account.';

  @override
  String get biometricAuthRequired => 'Biometric Authentication Required';

  @override
  String get biometricAuthGateDescription =>
      'Please authenticate with your biometric data to access your account.';

  @override
  String get authenticating => 'Authenticating...';

  @override
  String get selectTimePeriod => 'Select Time Period';

  @override
  String get currentMonth => 'Current Month';

  @override
  String get thisMonth => 'This Month';

  @override
  String get selectMonth => 'Select Month';

  @override
  String get selectYear => 'Select Year';

  @override
  String get periodSelector => 'Period Selector';

  @override
  String get previousPeriod => 'Previous Period';

  @override
  String get nextPeriod => 'Next Period';

  @override
  String get cannotSelectFuturePeriod => 'Cannot select future periods';

  @override
  String get invalidPeriodSelection => 'Invalid period selection';

  @override
  String get periodSelectionError => 'Error selecting period';

  @override
  String get weekly => 'Weekly';

  @override
  String get quarterly => 'Quarterly';

  @override
  String get select => 'Select';

  @override
  String get bulkBudgetOperations => 'Bulk Budget Operations';

  @override
  String selectedBudgets(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count budgets selected',
      one: '1 budget selected',
    );
    return '$_temp0';
  }

  @override
  String get selectOperation => 'Select Operation';

  @override
  String get adjustByPercentage => 'Adjust by Percentage';

  @override
  String get adjustByPercentageDescription =>
      'Increase or decrease budget amounts by a percentage';

  @override
  String get percentageChange => 'Percentage Change';

  @override
  String get percentageChangeHint => 'e.g., 10 for +10%, -20 for -20%';

  @override
  String get percentageRequired => 'Percentage is required';

  @override
  String get invalidPercentage => 'Please enter a valid percentage';

  @override
  String get percentageTooLow => 'Percentage cannot be -100% or lower';

  @override
  String get percentageTooHigh => 'Percentage cannot exceed +500%';

  @override
  String get deactivateBudgets => 'Deactivate Budgets';

  @override
  String get deactivateBudgetsDescription => 'Hide budgets from active lists';

  @override
  String get activateBudgets => 'Activate Budgets';

  @override
  String get activateBudgetsDescription => 'Show budgets in active lists';

  @override
  String get deleteBudgets => 'Delete Budgets';

  @override
  String get deleteBudgetsDescription =>
      'Permanently remove budgets (cannot be undone)';

  @override
  String get adjustBudgets => 'Adjust Budgets';

  @override
  String get budgetsAdjusted => 'Budgets adjusted successfully';

  @override
  String get budgetsActivated => 'Budgets activated successfully';

  @override
  String get budgetsDeactivated => 'Budgets deactivated successfully';

  @override
  String get budgetsDeleted => 'Budgets deleted successfully';

  @override
  String get bulkOperationFailed => 'Bulk operation failed';

  @override
  String get currencySettings => 'Currency Settings';

  @override
  String get selectCurrency => 'Select Currency';

  @override
  String get currencySettingsDescription =>
      'Choose your preferred currency for displaying amounts throughout the app. This setting applies to all monetary values.';

  @override
  String get availableCurrencies => 'Available Currencies';

  @override
  String get preview => 'Preview';

  @override
  String get currencyUpdatedSuccessfully => 'Currency updated successfully';

  @override
  String get failedToUpdateCurrency => 'Failed to update currency preference';

  @override
  String get errorBoundaryTitle => 'Something went wrong';

  @override
  String get errorBoundaryMessage =>
      'We\'ve encountered an unexpected error. You can try again or contact support if the problem persists.';

  @override
  String get reportError => 'Report Error';

  @override
  String get errorReported =>
      'Error reported successfully. Thank you for helping us improve the app.';
}
