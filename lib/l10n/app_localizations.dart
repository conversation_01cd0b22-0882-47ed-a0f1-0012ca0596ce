import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('en')];

  /// Welcome message on login screen
  ///
  /// In en, this message translates to:
  /// **'Welcome Back'**
  String get welcomeBack;

  /// Subtitle on login screen
  ///
  /// In en, this message translates to:
  /// **'Sign in to your account'**
  String get signInToAccount;

  /// Email field label
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// Email field hint text
  ///
  /// In en, this message translates to:
  /// **'Enter your email address'**
  String get enterEmailAddress;

  /// Email field hint text for signup
  ///
  /// In en, this message translates to:
  /// **'Enter your email'**
  String get enterYourEmail;

  /// Password field label
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// Password field hint text
  ///
  /// In en, this message translates to:
  /// **'Enter your password'**
  String get enterPassword;

  /// Password field hint text for signup
  ///
  /// In en, this message translates to:
  /// **'Create a password'**
  String get createPassword;

  /// Forgot password link text
  ///
  /// In en, this message translates to:
  /// **'Forgot Password?'**
  String get forgotPassword;

  /// Sign in button text
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signIn;

  /// Divider text for social login
  ///
  /// In en, this message translates to:
  /// **'Or continue with'**
  String get orContinueWith;

  /// Google sign in button text
  ///
  /// In en, this message translates to:
  /// **'Continue with Google'**
  String get continueWithGoogle;

  /// Apple sign in button text
  ///
  /// In en, this message translates to:
  /// **'Continue with Apple'**
  String get continueWithApple;

  /// Text before sign up link
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get dontHaveAccount;

  /// Sign up link text
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signUp;

  /// Subtitle on signup screen
  ///
  /// In en, this message translates to:
  /// **'Sign up to get started'**
  String get signUpToGetStarted;

  /// Create account button text
  ///
  /// In en, this message translates to:
  /// **'Create Account'**
  String get createAccount;

  /// Confirm password field label
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// Confirm password field hint text
  ///
  /// In en, this message translates to:
  /// **'Re-enter your password'**
  String get confirmPasswordHint;

  /// Confirm password field hint text for signup
  ///
  /// In en, this message translates to:
  /// **'Confirm your password'**
  String get confirmYourPassword;

  /// Beginning of terms acceptance text
  ///
  /// In en, this message translates to:
  /// **'I agree to the '**
  String get iAgreeToThe;

  /// Terms of Service link text
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get termsOfService;

  /// Conjunction text between terms and privacy policy
  ///
  /// In en, this message translates to:
  /// **' and '**
  String get and;

  /// Privacy Policy link text
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// Error message when user doesn't accept terms
  ///
  /// In en, this message translates to:
  /// **'Please accept the Terms of Service and Privacy Policy'**
  String get pleaseAcceptTermsAndPrivacy;

  /// Welcome message with user name
  ///
  /// In en, this message translates to:
  /// **'Welcome, {name}!'**
  String welcomeToApp(String name);

  /// Success message for Apple signup
  ///
  /// In en, this message translates to:
  /// **'Apple signup successful!'**
  String get appleSignupSuccessful;

  /// Error message for Apple signup failure
  ///
  /// In en, this message translates to:
  /// **'Apple signup failed: {error}'**
  String appleSignupFailed(String error);

  /// Success message after account creation
  ///
  /// In en, this message translates to:
  /// **'Account created successfully! Please check your email to verify your account.'**
  String get accountCreatedSuccessfully;

  /// Text before sign in link on signup screen
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get alreadyHaveAccount;

  /// Environment label on home screen
  ///
  /// In en, this message translates to:
  /// **'Environment:'**
  String get environment;

  /// Firebase project label on home screen
  ///
  /// In en, this message translates to:
  /// **'Firebase Project:'**
  String get firebaseProject;

  /// Button text to test Firebase connectivity
  ///
  /// In en, this message translates to:
  /// **'Test Firebase Services'**
  String get testFirebaseServices;

  /// Header for Firebase services status section
  ///
  /// In en, this message translates to:
  /// **'Firebase Services Status:'**
  String get firebaseServicesStatus;

  /// Firebase Auth status label
  ///
  /// In en, this message translates to:
  /// **'Firebase Auth:'**
  String get firebaseAuth;

  /// Firestore status label
  ///
  /// In en, this message translates to:
  /// **'Firestore:'**
  String get firestore;

  /// Sign out button text and tooltip
  ///
  /// In en, this message translates to:
  /// **'Sign Out'**
  String get signOut;

  /// Error message when sign out fails
  ///
  /// In en, this message translates to:
  /// **'Sign out failed: {error}'**
  String signOutFailed(String error);

  /// Try again button text
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// Header for suggested actions in error messages
  ///
  /// In en, this message translates to:
  /// **'Suggestions:'**
  String get suggestions;

  /// Divider text (OR)
  ///
  /// In en, this message translates to:
  /// **'OR'**
  String get or;

  /// Reset password screen title
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get resetPassword;

  /// Title when password reset email is sent
  ///
  /// In en, this message translates to:
  /// **'Check Your Email'**
  String get checkYourEmail;

  /// Description on forgot password screen
  ///
  /// In en, this message translates to:
  /// **'Enter your email to receive a password reset link'**
  String get enterEmailForReset;

  /// Description when password reset email is sent
  ///
  /// In en, this message translates to:
  /// **'We\'ve sent a password reset link to your email'**
  String get passwordResetEmailSent;

  /// Send reset link button text
  ///
  /// In en, this message translates to:
  /// **'Send Reset Link'**
  String get sendResetLink;

  /// Success message for password reset email
  ///
  /// In en, this message translates to:
  /// **'Password reset email sent! Please check your inbox.'**
  String get passwordResetEmailSentSuccess;

  /// Generic error message
  ///
  /// In en, this message translates to:
  /// **'An unexpected error occurred. Please try again.'**
  String get unexpectedError;

  /// Error when user not found
  ///
  /// In en, this message translates to:
  /// **'No account found with this email address.'**
  String get userNotFound;

  /// Error for invalid email format
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email address.'**
  String get invalidEmail;

  /// Error for too many requests
  ///
  /// In en, this message translates to:
  /// **'Too many requests. Please try again later.'**
  String get tooManyRequests;

  /// Success title for email sent
  ///
  /// In en, this message translates to:
  /// **'Email Sent Successfully!'**
  String get emailSentSuccessfully;

  /// Instructions after password reset email sent
  ///
  /// In en, this message translates to:
  /// **'Please check your email ({email}) and click the reset link to create a new password.'**
  String checkEmailForResetLink(String email);

  /// Reminder to check spam folder
  ///
  /// In en, this message translates to:
  /// **'Don\'t forget to check your spam folder if you don\'t see the email.'**
  String get checkSpamFolder;

  /// Resend email button text
  ///
  /// In en, this message translates to:
  /// **'Resend Email'**
  String get resendEmail;

  /// Back to sign in button text
  ///
  /// In en, this message translates to:
  /// **'Back to Sign In'**
  String get backToSignIn;

  /// Email verification screen title
  ///
  /// In en, this message translates to:
  /// **'Verify Your Email'**
  String get verifyYourEmail;

  /// Text before showing email address
  ///
  /// In en, this message translates to:
  /// **'We\'ve sent a verification email to:'**
  String get verificationEmailSentTo;

  /// Instructions for email verification
  ///
  /// In en, this message translates to:
  /// **'Please check your email and click the verification link to continue. You may need to check your spam folder.'**
  String get checkEmailForVerification;

  /// Resend verification email button text
  ///
  /// In en, this message translates to:
  /// **'Resend Verification Email'**
  String get resendVerificationEmail;

  /// Button text to check verification status
  ///
  /// In en, this message translates to:
  /// **'I\'ve Verified My Email'**
  String get verifiedMyEmail;

  /// Welcome message on home screen
  ///
  /// In en, this message translates to:
  /// **'Welcome!'**
  String get welcome;

  /// Fallback text when no email available
  ///
  /// In en, this message translates to:
  /// **'No email'**
  String get noEmail;

  /// Counter demo description
  ///
  /// In en, this message translates to:
  /// **'You have pushed the button this many times:'**
  String get counterDescription;

  /// Increment button tooltip
  ///
  /// In en, this message translates to:
  /// **'Increment'**
  String get increment;

  /// Generic validation error for required fields
  ///
  /// In en, this message translates to:
  /// **'This field is required'**
  String get fieldRequired;

  /// Validation error for empty email
  ///
  /// In en, this message translates to:
  /// **'Email is required'**
  String get emailRequired;

  /// Validation error for invalid email format
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email address'**
  String get validEmailRequired;

  /// Validation error for empty password
  ///
  /// In en, this message translates to:
  /// **'Password is required'**
  String get passwordRequired;

  /// Validation error for short password
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 6 characters'**
  String get passwordMinLength;

  /// Validation error for empty confirm password
  ///
  /// In en, this message translates to:
  /// **'Please confirm your password'**
  String get confirmPasswordRequired;

  /// Validation error when passwords don't match
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get passwordsDoNotMatch;

  /// Default app name for welcome messages
  ///
  /// In en, this message translates to:
  /// **'BudApp'**
  String get budApp;

  /// Success message after login
  ///
  /// In en, this message translates to:
  /// **'Welcome back!'**
  String get welcomeBackSuccess;

  /// Success message after Google sign-in
  ///
  /// In en, this message translates to:
  /// **'Welcome! Signed in with Google.'**
  String get googleSignInSuccess;

  /// Success message after Apple login
  ///
  /// In en, this message translates to:
  /// **'Apple login successful!'**
  String get appleLoginSuccessful;

  /// Retry button text
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Information severity title
  ///
  /// In en, this message translates to:
  /// **'Information'**
  String get information;

  /// Warning severity title
  ///
  /// In en, this message translates to:
  /// **'Warning'**
  String get warning;

  /// Error severity title
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// Critical error severity title
  ///
  /// In en, this message translates to:
  /// **'Critical Error'**
  String get criticalError;

  /// Accounts screen title
  ///
  /// In en, this message translates to:
  /// **'Accounts'**
  String get accounts;

  /// Add account button text
  ///
  /// In en, this message translates to:
  /// **'Add Account'**
  String get addAccount;

  /// Filter accounts button tooltip
  ///
  /// In en, this message translates to:
  /// **'Filter Accounts'**
  String get filterAccounts;

  /// Sort menu item
  ///
  /// In en, this message translates to:
  /// **'Sort'**
  String get sort;

  /// Settings menu item
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Assets section header
  ///
  /// In en, this message translates to:
  /// **'Assets'**
  String get assets;

  /// Liabilities section header
  ///
  /// In en, this message translates to:
  /// **'Liabilities'**
  String get liabilities;

  /// Total assets summary card title
  ///
  /// In en, this message translates to:
  /// **'Total Assets'**
  String get totalAssets;

  /// Net worth summary card title
  ///
  /// In en, this message translates to:
  /// **'Net Worth'**
  String get netWorth;

  /// Error message when accounts fail to load
  ///
  /// In en, this message translates to:
  /// **'Error Loading Accounts'**
  String get errorLoadingAccounts;

  /// Sort bottom sheet title
  ///
  /// In en, this message translates to:
  /// **'Sort by'**
  String get sortBy;

  /// Name sort option
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// Balance sort option and field label
  ///
  /// In en, this message translates to:
  /// **'Balance'**
  String get balance;

  /// Date created label
  ///
  /// In en, this message translates to:
  /// **'Date Created'**
  String get dateCreated;

  /// Primary account badge
  ///
  /// In en, this message translates to:
  /// **'Primary'**
  String get primary;

  /// Active account status
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get active;

  /// Inactive account badge
  ///
  /// In en, this message translates to:
  /// **'Inactive'**
  String get inactive;

  /// Edit action menu item
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// Delete action menu item
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Checking account type label
  ///
  /// In en, this message translates to:
  /// **'Checking Account'**
  String get checkingAccount;

  /// Savings account type label
  ///
  /// In en, this message translates to:
  /// **'Savings Account'**
  String get savingsAccount;

  /// Credit card account type label
  ///
  /// In en, this message translates to:
  /// **'Credit Card'**
  String get creditCard;

  /// Cash account type label
  ///
  /// In en, this message translates to:
  /// **'Cash Account'**
  String get cashAccount;

  /// Investment account type label
  ///
  /// In en, this message translates to:
  /// **'Investment Account'**
  String get investmentAccount;

  /// Loan account type label
  ///
  /// In en, this message translates to:
  /// **'Loan Account'**
  String get loanAccount;

  /// Clear all filters button
  ///
  /// In en, this message translates to:
  /// **'Clear All'**
  String get clearAll;

  /// Account type filter section title
  ///
  /// In en, this message translates to:
  /// **'Account Type'**
  String get accountType;

  /// All accounts filter option
  ///
  /// In en, this message translates to:
  /// **'All Accounts'**
  String get allAccounts;

  /// Checking account type
  ///
  /// In en, this message translates to:
  /// **'Checking'**
  String get checking;

  /// Savings account type
  ///
  /// In en, this message translates to:
  /// **'Savings'**
  String get savings;

  /// Cash account type
  ///
  /// In en, this message translates to:
  /// **'Cash'**
  String get cash;

  /// Investment account type
  ///
  /// In en, this message translates to:
  /// **'Investment'**
  String get investment;

  /// Loan account type
  ///
  /// In en, this message translates to:
  /// **'Loan'**
  String get loan;

  /// Show inactive accounts toggle title
  ///
  /// In en, this message translates to:
  /// **'Show Inactive Accounts'**
  String get showInactiveAccounts;

  /// Show inactive accounts toggle description
  ///
  /// In en, this message translates to:
  /// **'Include accounts that have been deactivated'**
  String get includeInactiveAccountsInList;

  /// Apply filters button
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// Empty accounts state title
  ///
  /// In en, this message translates to:
  /// **'No accounts yet'**
  String get noAccountsYet;

  /// Empty accounts state description
  ///
  /// In en, this message translates to:
  /// **'Add your first account to start tracking your finances'**
  String get noAccountsDescription;

  /// Create first account button
  ///
  /// In en, this message translates to:
  /// **'Create First Account'**
  String get createFirstAccount;

  /// Learn about accounts link
  ///
  /// In en, this message translates to:
  /// **'Learn about accounts'**
  String get learnAboutAccounts;

  /// About accounts dialog title
  ///
  /// In en, this message translates to:
  /// **'About Accounts'**
  String get aboutAccounts;

  /// Accounts help dialog description
  ///
  /// In en, this message translates to:
  /// **'Accounts represent your financial institutions and cash holdings. Track balances across different account types.'**
  String get accountsHelpDescription;

  /// Account types section title in help
  ///
  /// In en, this message translates to:
  /// **'Account Types:'**
  String get accountTypesTitle;

  /// Checking account description
  ///
  /// In en, this message translates to:
  /// **'Daily spending and bill payments'**
  String get checkingDescription;

  /// Savings account description
  ///
  /// In en, this message translates to:
  /// **'Long-term savings and emergency funds'**
  String get savingsDescription;

  /// Credit card description
  ///
  /// In en, this message translates to:
  /// **'Credit cards and revolving credit'**
  String get creditCardDescription;

  /// Cash description
  ///
  /// In en, this message translates to:
  /// **'Physical cash and digital wallets'**
  String get cashDescription;

  /// Acknowledgment button text
  ///
  /// In en, this message translates to:
  /// **'Got it'**
  String get gotIt;

  /// Account name validation error
  ///
  /// In en, this message translates to:
  /// **'Account name is required'**
  String get accountNameRequired;

  /// Account name minimum length validation error
  ///
  /// In en, this message translates to:
  /// **'Account name must be at least 2 characters'**
  String get accountNameMinLength;

  /// Account name maximum length validation error
  ///
  /// In en, this message translates to:
  /// **'Account name cannot exceed 50 characters'**
  String get accountNameMaxLength;

  /// Account name invalid characters validation error
  ///
  /// In en, this message translates to:
  /// **'Account name contains invalid characters'**
  String get accountNameInvalidCharacters;

  /// Account description maximum length validation error
  ///
  /// In en, this message translates to:
  /// **'Account description cannot exceed 200 characters'**
  String get accountDescriptionMaxLength;

  /// Initial balance validation error
  ///
  /// In en, this message translates to:
  /// **'Initial balance is required'**
  String get initialBalanceRequired;

  /// Initial balance format validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid balance amount'**
  String get initialBalanceInvalid;

  /// Initial balance range validation error
  ///
  /// In en, this message translates to:
  /// **'Balance amount is too large'**
  String get initialBalanceOutOfRange;

  /// Initial balance decimal places validation error
  ///
  /// In en, this message translates to:
  /// **'Balance can have at most 2 decimal places'**
  String get initialBalanceMaxDecimals;

  /// Currency code validation error
  ///
  /// In en, this message translates to:
  /// **'Currency code is required'**
  String get currencyCodeRequired;

  /// Currency code format validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid 3-letter currency code'**
  String get currencyCodeInvalid;

  /// Currency code support validation error
  ///
  /// In en, this message translates to:
  /// **'Currency code is not supported'**
  String get currencyCodeUnsupported;

  /// Account type validation error
  ///
  /// In en, this message translates to:
  /// **'Account type is required'**
  String get accountTypeRequired;

  /// Account classification validation error
  ///
  /// In en, this message translates to:
  /// **'Account classification is required'**
  String get accountClassificationRequired;

  /// Account type-classification pairing validation error
  ///
  /// In en, this message translates to:
  /// **'Account type and classification don\'t match'**
  String get accountTypeClassificationMismatch;

  /// Color hex validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid hex color code (e.g., #FF5733)'**
  String get colorHexInvalid;

  /// Icon name validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid icon name'**
  String get iconNameInvalid;

  /// Save button text
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Account name field label
  ///
  /// In en, this message translates to:
  /// **'Account Name'**
  String get accountName;

  /// Account name field hint text
  ///
  /// In en, this message translates to:
  /// **'e.g., Main Checking'**
  String get accountNameHint;

  /// Initial balance field label
  ///
  /// In en, this message translates to:
  /// **'Initial Balance'**
  String get initialBalance;

  /// Currency label
  ///
  /// In en, this message translates to:
  /// **'Currency'**
  String get currency;

  /// Account description field hint text
  ///
  /// In en, this message translates to:
  /// **'Optional description for this account'**
  String get accountDescriptionHint;

  /// Customization section title
  ///
  /// In en, this message translates to:
  /// **'Customize'**
  String get customize;

  /// Optional field indicator
  ///
  /// In en, this message translates to:
  /// **'(optional)'**
  String get optional;

  /// Account color selection label
  ///
  /// In en, this message translates to:
  /// **'Account Color'**
  String get accountColor;

  /// Default color option
  ///
  /// In en, this message translates to:
  /// **'Default'**
  String get defaultColor;

  /// Account icon selection label
  ///
  /// In en, this message translates to:
  /// **'Account Icon'**
  String get accountIcon;

  /// Default icon option
  ///
  /// In en, this message translates to:
  /// **'Default'**
  String get defaultIcon;

  /// Creating account loading text
  ///
  /// In en, this message translates to:
  /// **'Creating...'**
  String get creating;

  /// Invalid balance error message
  ///
  /// In en, this message translates to:
  /// **'Invalid balance amount'**
  String get invalidBalance;

  /// Error message when account creation fails
  ///
  /// In en, this message translates to:
  /// **'Error creating account. Please try again.'**
  String get errorCreatingAccount;

  /// Credit card account type label
  ///
  /// In en, this message translates to:
  /// **'Credit Card Account'**
  String get creditCardAccount;

  /// Description field label
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// Account details screen title
  ///
  /// In en, this message translates to:
  /// **'Account Details'**
  String get accountDetails;

  /// Edit account menu option
  ///
  /// In en, this message translates to:
  /// **'Edit Account'**
  String get editAccount;

  /// Set primary account menu option
  ///
  /// In en, this message translates to:
  /// **'Set as Primary'**
  String get setPrimary;

  /// Deactivate account menu option
  ///
  /// In en, this message translates to:
  /// **'Deactivate Account'**
  String get deactivateAccount;

  /// Delete account menu option
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get deleteAccount;

  /// Current balance label on account detail
  ///
  /// In en, this message translates to:
  /// **'Current Balance'**
  String get currentBalance;

  /// Account classification label
  ///
  /// In en, this message translates to:
  /// **'Classification'**
  String get classification;

  /// Last updated label
  ///
  /// In en, this message translates to:
  /// **'Last Updated'**
  String get lastUpdated;

  /// Account settings section title
  ///
  /// In en, this message translates to:
  /// **'Account Settings'**
  String get accountSettings;

  /// Primary account label
  ///
  /// In en, this message translates to:
  /// **'Primary Account'**
  String get primaryAccount;

  /// Recent transactions section title
  ///
  /// In en, this message translates to:
  /// **'Recent Transactions'**
  String get recentTransactions;

  /// View all button text
  ///
  /// In en, this message translates to:
  /// **'View All'**
  String get viewAll;

  /// Empty transactions state title
  ///
  /// In en, this message translates to:
  /// **'No transactions yet'**
  String get noTransactionsYet;

  /// Empty transactions state description
  ///
  /// In en, this message translates to:
  /// **'Transactions for this account will appear here'**
  String get transactionsWillAppearHere;

  /// Account not found error title
  ///
  /// In en, this message translates to:
  /// **'Account Not Found'**
  String get accountNotFound;

  /// Account not found error description
  ///
  /// In en, this message translates to:
  /// **'This account may have been deleted or you don\'t have permission to view it'**
  String get accountMayHaveBeenDeleted;

  /// Go back button text
  ///
  /// In en, this message translates to:
  /// **'Go Back'**
  String get goBack;

  /// Error loading account message
  ///
  /// In en, this message translates to:
  /// **'Error Loading Account'**
  String get errorLoadingAccount;

  /// Set primary account dialog title
  ///
  /// In en, this message translates to:
  /// **'Set Primary Account'**
  String get setPrimaryAccount;

  /// Set primary account confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to set this as your primary account?'**
  String get setPrimaryAccountConfirmation;

  /// Deactivate account confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to deactivate this account? It will be hidden from your account list.'**
  String get deactivateAccountConfirmation;

  /// Delete account confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this account? This action cannot be undone.'**
  String get deleteAccountConfirmation;

  /// Cancel button text
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Confirm button text
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// Asset classification label
  ///
  /// In en, this message translates to:
  /// **'Asset'**
  String get asset;

  /// Liability classification label
  ///
  /// In en, this message translates to:
  /// **'Liability'**
  String get liability;

  /// Loading text when updating an account
  ///
  /// In en, this message translates to:
  /// **'Updating...'**
  String get updating;

  /// Button text for updating an account
  ///
  /// In en, this message translates to:
  /// **'Update Account'**
  String get updateAccount;

  /// Success message when account is updated
  ///
  /// In en, this message translates to:
  /// **'Account updated successfully'**
  String get accountUpdatedSuccessfully;

  /// Error message when account update fails
  ///
  /// In en, this message translates to:
  /// **'Error updating account. Please try again.'**
  String get errorUpdatingAccount;

  /// Quick actions section title on home screen
  ///
  /// In en, this message translates to:
  /// **'Quick Actions'**
  String get quickActions;

  /// Manage accounts card title
  ///
  /// In en, this message translates to:
  /// **'Manage Accounts'**
  String get manageAccounts;

  /// Manage accounts card description
  ///
  /// In en, this message translates to:
  /// **'View and edit your financial accounts'**
  String get viewAndEditAccounts;

  /// Transactions screen title
  ///
  /// In en, this message translates to:
  /// **'Transactions'**
  String get transactions;

  /// Manage transactions card title
  ///
  /// In en, this message translates to:
  /// **'Manage Transactions'**
  String get manageTransactions;

  /// Manage transactions card description
  ///
  /// In en, this message translates to:
  /// **'Add and view your financial transactions'**
  String get addAndViewTransactions;

  /// Budgets screen title
  ///
  /// In en, this message translates to:
  /// **'Budgets'**
  String get budgets;

  /// Manage budgets card title
  ///
  /// In en, this message translates to:
  /// **'Manage Budgets'**
  String get manageBudgets;

  /// Manage budgets card description
  ///
  /// In en, this message translates to:
  /// **'Create and track your spending budgets'**
  String get createAndTrackBudgets;

  /// Create budget screen title
  ///
  /// In en, this message translates to:
  /// **'Create Budget'**
  String get createBudget;

  /// Edit budget screen title
  ///
  /// In en, this message translates to:
  /// **'Edit Budget'**
  String get editBudget;

  /// Budget name field label
  ///
  /// In en, this message translates to:
  /// **'Budget Name'**
  String get budgetName;

  /// Budget amount field label
  ///
  /// In en, this message translates to:
  /// **'Budget Amount'**
  String get budgetAmount;

  /// Budget amount field hint
  ///
  /// In en, this message translates to:
  /// **'Enter the budget amount'**
  String get budgetAmountHint;

  /// Budget amount validation error
  ///
  /// In en, this message translates to:
  /// **'Budget amount is required'**
  String get budgetAmountRequired;

  /// Budget period field label
  ///
  /// In en, this message translates to:
  /// **'Budget Period'**
  String get budgetPeriod;

  /// Label for monthly period type
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get monthly;

  /// Label for yearly period type
  ///
  /// In en, this message translates to:
  /// **'Yearly'**
  String get yearly;

  /// Budget category field label
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get budgetCategory;

  /// All categories filter option
  ///
  /// In en, this message translates to:
  /// **'All Categories'**
  String get allCategories;

  /// Budget description field label
  ///
  /// In en, this message translates to:
  /// **'Description (Optional)'**
  String get budgetDescription;

  /// Budget description field hint
  ///
  /// In en, this message translates to:
  /// **'Add notes about this budget'**
  String get budgetDescriptionHint;

  /// Create first budget button text
  ///
  /// In en, this message translates to:
  /// **'Create Your First Budget'**
  String get createFirstBudget;

  /// Empty budgets state title
  ///
  /// In en, this message translates to:
  /// **'No budgets yet'**
  String get noBudgetsYet;

  /// Empty budgets state description
  ///
  /// In en, this message translates to:
  /// **'Create budgets to track your spending and stay on top of your finances.'**
  String get noBudgetsDescription;

  /// Empty filtered budgets state title
  ///
  /// In en, this message translates to:
  /// **'No budgets for this month'**
  String get noBudgetsForMonth;

  /// View all budgets button text
  ///
  /// In en, this message translates to:
  /// **'View All Budgets'**
  String get viewAllBudgets;

  /// Budget creation success message
  ///
  /// In en, this message translates to:
  /// **'Budget created successfully'**
  String get budgetCreated;

  /// Budget update success message
  ///
  /// In en, this message translates to:
  /// **'Budget updated successfully'**
  String get budgetUpdated;

  /// Budget deletion success message
  ///
  /// In en, this message translates to:
  /// **'Budget deleted successfully'**
  String get budgetDeleted;

  /// Budget creation error message
  ///
  /// In en, this message translates to:
  /// **'Failed to create budget'**
  String get errorCreatingBudget;

  /// Budget update error message
  ///
  /// In en, this message translates to:
  /// **'Failed to update budget'**
  String get errorUpdatingBudget;

  /// Budget deletion error message
  ///
  /// In en, this message translates to:
  /// **'Failed to delete budget'**
  String get errorDeletingBudget;

  /// Budget loading error message
  ///
  /// In en, this message translates to:
  /// **'Failed to load budget'**
  String get errorLoadingBudget;

  /// Delete budget dialog title
  ///
  /// In en, this message translates to:
  /// **'Delete Budget'**
  String get deleteBudget;

  /// Delete budget confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this budget? This action cannot be undone.'**
  String get deleteBudgetConfirmation;

  /// Discard changes dialog title
  ///
  /// In en, this message translates to:
  /// **'Discard Changes'**
  String get discardChanges;

  /// Discard changes confirmation message
  ///
  /// In en, this message translates to:
  /// **'You have unsaved changes. Are you sure you want to discard them?'**
  String get discardChangesConfirmation;

  /// Keep editing button text
  ///
  /// In en, this message translates to:
  /// **'Keep Editing'**
  String get keepEditing;

  /// Discard button text
  ///
  /// In en, this message translates to:
  /// **'Discard'**
  String get discard;

  /// Save changes button text
  ///
  /// In en, this message translates to:
  /// **'Save Changes'**
  String get saveChanges;

  /// Amount spent label
  ///
  /// In en, this message translates to:
  /// **'Spent'**
  String get spent;

  /// Amount remaining label
  ///
  /// In en, this message translates to:
  /// **'Remaining'**
  String get remaining;

  /// Over budget status
  ///
  /// In en, this message translates to:
  /// **'Over Budget'**
  String get overBudget;

  /// On track budget status
  ///
  /// In en, this message translates to:
  /// **'On Track'**
  String get onTrack;

  /// Near limit budget status
  ///
  /// In en, this message translates to:
  /// **'Near Limit'**
  String get nearLimit;

  /// Goals screen title
  ///
  /// In en, this message translates to:
  /// **'Goals'**
  String get goals;

  /// Manage goals card title
  ///
  /// In en, this message translates to:
  /// **'Manage Goals'**
  String get manageGoals;

  /// Manage goals card description
  ///
  /// In en, this message translates to:
  /// **'Set and track your financial goals'**
  String get setAndTrackGoals;

  /// Coming soon placeholder text
  ///
  /// In en, this message translates to:
  /// **'Coming Soon'**
  String get comingSoon;

  /// User profile navigation item
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// Import/Export data navigation item
  ///
  /// In en, this message translates to:
  /// **'Import/Export'**
  String get importExport;

  /// Reports and analytics navigation item
  ///
  /// In en, this message translates to:
  /// **'Reports'**
  String get reports;

  /// Help and support navigation item
  ///
  /// In en, this message translates to:
  /// **'Help'**
  String get help;

  /// Feedback navigation item
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get feedback;

  /// Categories screen title
  ///
  /// In en, this message translates to:
  /// **'Categories'**
  String get categories;

  /// Add category button text
  ///
  /// In en, this message translates to:
  /// **'Add Category'**
  String get addCategory;

  /// Filter categories button tooltip
  ///
  /// In en, this message translates to:
  /// **'Filter Categories'**
  String get filterCategories;

  /// Category type filter section title
  ///
  /// In en, this message translates to:
  /// **'Category Type'**
  String get categoryType;

  /// Income categories filter option
  ///
  /// In en, this message translates to:
  /// **'Income Categories'**
  String get incomeCategories;

  /// Expense categories filter option
  ///
  /// In en, this message translates to:
  /// **'Expense Categories'**
  String get expenseCategories;

  /// Custom categories filter option
  ///
  /// In en, this message translates to:
  /// **'Custom Categories'**
  String get customCategories;

  /// Predefined categories filter option
  ///
  /// In en, this message translates to:
  /// **'Predefined Categories'**
  String get predefinedCategories;

  /// Show inactive categories toggle title
  ///
  /// In en, this message translates to:
  /// **'Show Inactive Categories'**
  String get showInactiveCategories;

  /// Show inactive categories toggle description
  ///
  /// In en, this message translates to:
  /// **'Include categories that have been deactivated'**
  String get includeInactiveCategoriesInList;

  /// Empty categories state title
  ///
  /// In en, this message translates to:
  /// **'No categories yet'**
  String get noCategoriesYet;

  /// Empty categories state description
  ///
  /// In en, this message translates to:
  /// **'Add your first category to start organizing your transactions'**
  String get noCategoriesDescription;

  /// Create first category button
  ///
  /// In en, this message translates to:
  /// **'Create First Category'**
  String get createFirstCategory;

  /// Learn about categories link
  ///
  /// In en, this message translates to:
  /// **'Learn about categories'**
  String get learnAboutCategories;

  /// About categories dialog title
  ///
  /// In en, this message translates to:
  /// **'About Categories'**
  String get aboutCategories;

  /// Categories help dialog description
  ///
  /// In en, this message translates to:
  /// **'Categories help you organize and track your income and expenses. Create custom categories or use predefined ones.'**
  String get categoriesHelpDescription;

  /// Category types section title in help
  ///
  /// In en, this message translates to:
  /// **'Category Types:'**
  String get categoryTypesTitle;

  /// Description for income category type
  ///
  /// In en, this message translates to:
  /// **'Money coming in (salary, investments, etc.)'**
  String get incomeDescription;

  /// Description for expense category type
  ///
  /// In en, this message translates to:
  /// **'Money going out (bills, purchases, etc.)'**
  String get expenseDescription;

  /// Subcategories description in help
  ///
  /// In en, this message translates to:
  /// **'Create subcategories to organize your categories further'**
  String get subcategoriesDescription;

  /// Error message when categories fail to load
  ///
  /// In en, this message translates to:
  /// **'Error Loading Categories'**
  String get errorLoadingCategories;

  /// Category details screen title
  ///
  /// In en, this message translates to:
  /// **'Category Details'**
  String get categoryDetails;

  /// Edit category menu option
  ///
  /// In en, this message translates to:
  /// **'Edit Category'**
  String get editCategory;

  /// Add subcategory menu option
  ///
  /// In en, this message translates to:
  /// **'Add Subcategory'**
  String get addSubcategory;

  /// Deactivate category menu option
  ///
  /// In en, this message translates to:
  /// **'Deactivate Category'**
  String get deactivateCategory;

  /// Delete category menu option
  ///
  /// In en, this message translates to:
  /// **'Delete Category'**
  String get deleteCategory;

  /// Category name field label
  ///
  /// In en, this message translates to:
  /// **'Category Name'**
  String get categoryName;

  /// Hint text for category name field
  ///
  /// In en, this message translates to:
  /// **'Enter category name'**
  String get categoryNameHint;

  /// Hint text for category description field
  ///
  /// In en, this message translates to:
  /// **'Enter description (optional)'**
  String get categoryDescriptionHint;

  /// Category color selection label
  ///
  /// In en, this message translates to:
  /// **'Category Color'**
  String get categoryColor;

  /// Category icon selection label
  ///
  /// In en, this message translates to:
  /// **'Category Icon'**
  String get categoryIcon;

  /// Parent category selection label
  ///
  /// In en, this message translates to:
  /// **'Parent Category'**
  String get parentCategory;

  /// Root category option (no parent)
  ///
  /// In en, this message translates to:
  /// **'Root Category'**
  String get rootCategory;

  /// Subcategories section title
  ///
  /// In en, this message translates to:
  /// **'Subcategories'**
  String get subcategories;

  /// Subcategory count display
  ///
  /// In en, this message translates to:
  /// **'{count} subcategories'**
  String subcategoryCount(int count);

  /// No subcategories message
  ///
  /// In en, this message translates to:
  /// **'No subcategories'**
  String get noSubcategories;

  /// Expand category tooltip
  ///
  /// In en, this message translates to:
  /// **'Expand category'**
  String get expandCategory;

  /// Collapse category tooltip
  ///
  /// In en, this message translates to:
  /// **'Collapse category'**
  String get collapseCategory;

  /// Error message when category is not found
  ///
  /// In en, this message translates to:
  /// **'Category Not Found'**
  String get categoryNotFound;

  /// Additional error message for category not found
  ///
  /// In en, this message translates to:
  /// **'This category may have been deleted or you may not have permission to access it.'**
  String get categoryMayHaveBeenDeleted;

  /// Error message when category fails to load
  ///
  /// In en, this message translates to:
  /// **'Error Loading Category'**
  String get errorLoadingCategory;

  /// Deactivate category confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to deactivate this category? It will be hidden from your category list.'**
  String get deactivateCategoryConfirmation;

  /// Delete category confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this category? This action cannot be undone.'**
  String get deleteCategoryConfirmation;

  /// Error message when trying to delete category with transactions
  ///
  /// In en, this message translates to:
  /// **'Cannot delete category \"{categoryName}\": it has associated transactions. Please reassign or delete the transactions first.'**
  String cannotDeleteCategoryWithTransactions(String categoryName);

  /// Error message when trying to delete category with subcategories
  ///
  /// In en, this message translates to:
  /// **'Cannot delete category \"{categoryName}\": it has subcategories. Please delete or move the subcategories first.'**
  String cannotDeleteCategoryWithSubcategories(String categoryName);

  /// Error message when trying to delete subcategory with transactions
  ///
  /// In en, this message translates to:
  /// **'Cannot delete subcategory \"{subcategoryName}\": it has associated transactions. Please reassign or delete the transactions first.'**
  String cannotDeleteSubcategoryWithTransactions(String subcategoryName);

  /// Error message when trying to delete subcategory with children
  ///
  /// In en, this message translates to:
  /// **'Cannot delete subcategory \"{subcategoryName}\": it has child subcategories. Please delete or move the child subcategories first.'**
  String cannotDeleteSubcategoryWithChildren(String subcategoryName);

  /// Error message when subcategory doesn't belong to parent
  ///
  /// In en, this message translates to:
  /// **'Subcategory does not belong to the specified parent category.'**
  String get subcategoryNotBelongToParent;

  /// Error message when checking deletion constraints fails
  ///
  /// In en, this message translates to:
  /// **'Error checking deletion constraints: {error}'**
  String errorCheckingDeletionConstraints(String error);

  /// Error message when category deletion fails
  ///
  /// In en, this message translates to:
  /// **'Failed to delete category: {error}'**
  String failedToDeleteCategory(String error);

  /// Error message when subcategory deletion fails
  ///
  /// In en, this message translates to:
  /// **'Failed to delete subcategory: {error}'**
  String failedToDeleteSubcategory(String error);

  /// Category source label
  ///
  /// In en, this message translates to:
  /// **'Category Source'**
  String get categorySource;

  /// Label for custom period type
  ///
  /// In en, this message translates to:
  /// **'Custom'**
  String get custom;

  /// Predefined category source
  ///
  /// In en, this message translates to:
  /// **'Predefined'**
  String get predefined;

  /// Income transaction type
  ///
  /// In en, this message translates to:
  /// **'Income'**
  String get income;

  /// Expense transaction type
  ///
  /// In en, this message translates to:
  /// **'Expense'**
  String get expense;

  /// Manage categories card title
  ///
  /// In en, this message translates to:
  /// **'Manage Categories'**
  String get manageCategories;

  /// Create category screen title
  ///
  /// In en, this message translates to:
  /// **'Create Category'**
  String get createCategory;

  /// Create subcategory screen title
  ///
  /// In en, this message translates to:
  /// **'Create Subcategory'**
  String get createSubcategory;

  /// Edit category screen title
  ///
  /// In en, this message translates to:
  /// **'Edit Category'**
  String get editCategoryTitle;

  /// Edit subcategory screen title
  ///
  /// In en, this message translates to:
  /// **'Edit Subcategory'**
  String get editSubcategoryTitle;

  /// Category name too short validation error
  ///
  /// In en, this message translates to:
  /// **'Category name must be at least 2 characters'**
  String get categoryNameTooShort;

  /// Category name too long validation error
  ///
  /// In en, this message translates to:
  /// **'Category name must be 50 characters or less'**
  String get categoryNameTooLong;

  /// Category name invalid characters validation error
  ///
  /// In en, this message translates to:
  /// **'Category name can only contain letters, numbers, spaces, hyphens, and apostrophes'**
  String get categoryNameInvalidCharacters;

  /// Category description too long validation error
  ///
  /// In en, this message translates to:
  /// **'Description must be 200 characters or less'**
  String get categoryDescriptionTooLong;

  /// Category type required validation error
  ///
  /// In en, this message translates to:
  /// **'Please select a category type'**
  String get categoryTypeRequired;

  /// Category color invalid format validation error
  ///
  /// In en, this message translates to:
  /// **'Color must be in hex format (#RRGGBB)'**
  String get categoryColorInvalidFormat;

  /// Category icon invalid format validation error
  ///
  /// In en, this message translates to:
  /// **'Icon must be a valid identifier (letters, numbers, underscores)'**
  String get categoryIconInvalidFormat;

  /// Parent category required validation error
  ///
  /// In en, this message translates to:
  /// **'Please select a parent category'**
  String get parentCategoryRequired;

  /// Category created success message
  ///
  /// In en, this message translates to:
  /// **'Category created successfully'**
  String get categoryCreatedSuccessfully;

  /// Category updated success message
  ///
  /// In en, this message translates to:
  /// **'Category updated successfully'**
  String get categoryUpdatedSuccessfully;

  /// Error creating category message
  ///
  /// In en, this message translates to:
  /// **'Error creating category'**
  String get errorCreatingCategory;

  /// Error updating category message
  ///
  /// In en, this message translates to:
  /// **'Error updating category'**
  String get errorUpdatingCategory;

  /// Category type selector title
  ///
  /// In en, this message translates to:
  /// **'Select Category Type'**
  String get selectCategoryType;

  /// Parent category selector title
  ///
  /// In en, this message translates to:
  /// **'Select Parent Category'**
  String get selectParentCategory;

  /// No parent category option
  ///
  /// In en, this message translates to:
  /// **'No Parent (Root Category)'**
  String get noParentCategory;

  /// Color selector title
  ///
  /// In en, this message translates to:
  /// **'Select Color'**
  String get selectColor;

  /// Icon selector title
  ///
  /// In en, this message translates to:
  /// **'Select Icon'**
  String get selectIcon;

  /// Manage categories card description
  ///
  /// In en, this message translates to:
  /// **'Organize your transactions with custom categories'**
  String get organizeTransactions;

  /// Close button text
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// Activate action menu item
  ///
  /// In en, this message translates to:
  /// **'Activate'**
  String get activate;

  /// Title for category deletion constraints dialog
  ///
  /// In en, this message translates to:
  /// **'Category Deletion Constraints'**
  String get categoryDeletionConstraints;

  /// Message showing number of transactions associated with category
  ///
  /// In en, this message translates to:
  /// **'This category has {count} associated transactions'**
  String categoryHasTransactions(int count);

  /// Message showing number of subcategories
  ///
  /// In en, this message translates to:
  /// **'This category has {count} subcategories'**
  String categoryHasSubcategories(int count);

  /// Button text for reassigning transactions
  ///
  /// In en, this message translates to:
  /// **'Reassign Transactions'**
  String get reassignTransactions;

  /// Button text for force deletion
  ///
  /// In en, this message translates to:
  /// **'Delete Anyway'**
  String get deleteAnyway;

  /// Title for category selection dialog
  ///
  /// In en, this message translates to:
  /// **'Select New Category'**
  String get selectNewCategory;

  /// Instructions for category reassignment
  ///
  /// In en, this message translates to:
  /// **'Select a category to reassign transactions to:'**
  String get selectCategoryForReassignment;

  /// Message when no categories are available for reassignment
  ///
  /// In en, this message translates to:
  /// **'No available categories for reassignment'**
  String get noAvailableCategories;

  /// Confirmation button for reassignment and deletion
  ///
  /// In en, this message translates to:
  /// **'Reassign and Delete'**
  String get reassignmentConfirmation;

  /// Loading message during transaction reassignment
  ///
  /// In en, this message translates to:
  /// **'Reassigning transactions...'**
  String get reassigningTransactions;

  /// Success message after transaction reassignment
  ///
  /// In en, this message translates to:
  /// **'Transactions reassigned successfully'**
  String get transactionsReassignedSuccessfully;

  /// Error message when transaction reassignment fails
  ///
  /// In en, this message translates to:
  /// **'Error reassigning transactions'**
  String get errorReassigningTransactions;

  /// Success message after category deletion
  ///
  /// In en, this message translates to:
  /// **'Category deleted successfully'**
  String get categoryDeletedSuccessfully;

  /// Button text to view associated transactions
  ///
  /// In en, this message translates to:
  /// **'View Transactions'**
  String get viewTransactions;

  /// Title for transaction list dialog
  ///
  /// In en, this message translates to:
  /// **'Transaction List'**
  String get transactionList;

  /// Title showing transactions in a specific category
  ///
  /// In en, this message translates to:
  /// **'Transactions in \"{categoryName}\"'**
  String transactionsInCategory(String categoryName);

  /// Loading indicator text
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// Add transaction screen title
  ///
  /// In en, this message translates to:
  /// **'Add Transaction'**
  String get addTransaction;

  /// Transaction type field label
  ///
  /// In en, this message translates to:
  /// **'Transaction Type'**
  String get transactionType;

  /// Transfer transaction type
  ///
  /// In en, this message translates to:
  /// **'Transfer'**
  String get transfer;

  /// Amount field label
  ///
  /// In en, this message translates to:
  /// **'Amount'**
  String get amount;

  /// Destination account field label for income transactions
  ///
  /// In en, this message translates to:
  /// **'To Account'**
  String get toAccount;

  /// Source account field label for expense transactions
  ///
  /// In en, this message translates to:
  /// **'From Account'**
  String get fromAccount;

  /// Category field label
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get category;

  /// Date field label
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// Date picker placeholder
  ///
  /// In en, this message translates to:
  /// **'Select Date'**
  String get selectDate;

  /// Notes field label
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get notes;

  /// Required field indicator
  ///
  /// In en, this message translates to:
  /// **'Required'**
  String get required;

  /// Amount field validation error
  ///
  /// In en, this message translates to:
  /// **'Amount is required'**
  String get amountRequired;

  /// Amount format validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid amount'**
  String get amountInvalid;

  /// Generic number validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid number'**
  String get invalidNumber;

  /// Dropdown placeholder text
  ///
  /// In en, this message translates to:
  /// **'Please select'**
  String get pleaseSelect;

  /// Dropdown empty state message
  ///
  /// In en, this message translates to:
  /// **'No items available'**
  String get noItemsAvailable;

  /// Dropdown error state message
  ///
  /// In en, this message translates to:
  /// **'Error loading data'**
  String get errorLoadingData;

  /// Network connectivity error message
  ///
  /// In en, this message translates to:
  /// **'Network connection error. Please check your internet connection and try again.'**
  String get networkError;

  /// Permission error message
  ///
  /// In en, this message translates to:
  /// **'You don\'t have permission to perform this action.'**
  String get permissionError;

  /// Not found error message
  ///
  /// In en, this message translates to:
  /// **'The requested item was not found.'**
  String get notFoundError;

  /// Generic error message
  ///
  /// In en, this message translates to:
  /// **'An unexpected error occurred. Please try again.'**
  String get genericError;

  /// User not found error
  ///
  /// In en, this message translates to:
  /// **'No user found with this email address.'**
  String get userNotFoundError;

  /// Wrong password error
  ///
  /// In en, this message translates to:
  /// **'Incorrect password. Please try again.'**
  String get wrongPasswordError;

  /// Email already in use error
  ///
  /// In en, this message translates to:
  /// **'An account already exists with this email address.'**
  String get emailAlreadyInUseError;

  /// Weak password error
  ///
  /// In en, this message translates to:
  /// **'Password is too weak. Please choose a stronger password.'**
  String get weakPasswordError;

  /// Invalid email error
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email address.'**
  String get invalidEmailError;

  /// User disabled error
  ///
  /// In en, this message translates to:
  /// **'This account has been disabled.'**
  String get userDisabledError;

  /// Too many requests error
  ///
  /// In en, this message translates to:
  /// **'Too many attempts. Please try again later.'**
  String get tooManyRequestsError;

  /// Operation not allowed error
  ///
  /// In en, this message translates to:
  /// **'This operation is not allowed.'**
  String get operationNotAllowedError;

  /// Requires recent login error
  ///
  /// In en, this message translates to:
  /// **'Please sign in again to continue.'**
  String get requiresRecentLoginError;

  /// Authentication error
  ///
  /// In en, this message translates to:
  /// **'Authentication failed. Please try again.'**
  String get authenticationError;

  /// Permission denied error message
  ///
  /// In en, this message translates to:
  /// **'You don\'t have permission to perform this action.'**
  String get permissionDeniedError;

  /// Service unavailable error message
  ///
  /// In en, this message translates to:
  /// **'Service is temporarily unavailable. Please try again later.'**
  String get serviceUnavailableError;

  /// Timeout error
  ///
  /// In en, this message translates to:
  /// **'Request timed out. Please try again.'**
  String get timeoutError;

  /// Document not found error
  ///
  /// In en, this message translates to:
  /// **'Document not found.'**
  String get documentNotFoundError;

  /// Document already exists error
  ///
  /// In en, this message translates to:
  /// **'Document already exists.'**
  String get documentAlreadyExistsError;

  /// Quota exceeded error message
  ///
  /// In en, this message translates to:
  /// **'Service quota exceeded. Please try again later.'**
  String get quotaExceededError;

  /// Precondition failed error
  ///
  /// In en, this message translates to:
  /// **'Operation failed due to precondition.'**
  String get preconditionFailedError;

  /// Operation aborted error message
  ///
  /// In en, this message translates to:
  /// **'Operation was aborted. Please try again.'**
  String get operationAbortedError;

  /// Out of range error
  ///
  /// In en, this message translates to:
  /// **'Value is out of range.'**
  String get outOfRangeError;

  /// Feature not implemented error
  ///
  /// In en, this message translates to:
  /// **'Feature not implemented.'**
  String get featureNotImplementedError;

  /// Internal error
  ///
  /// In en, this message translates to:
  /// **'Internal server error. Please try again.'**
  String get internalError;

  /// Data loss error
  ///
  /// In en, this message translates to:
  /// **'Data loss detected. Please contact support.'**
  String get dataLossError;

  /// Unauthenticated error
  ///
  /// In en, this message translates to:
  /// **'Please sign in to continue.'**
  String get unauthenticatedError;

  /// Database error
  ///
  /// In en, this message translates to:
  /// **'Database error. Please try again.'**
  String get databaseError;

  /// Invalid date format error
  ///
  /// In en, this message translates to:
  /// **'Invalid date format.'**
  String get invalidDateFormatError;

  /// Invalid number format error
  ///
  /// In en, this message translates to:
  /// **'Invalid number format.'**
  String get invalidNumberFormatError;

  /// Invalid format error
  ///
  /// In en, this message translates to:
  /// **'Invalid format.'**
  String get invalidFormatError;

  /// Invalid argument error
  ///
  /// In en, this message translates to:
  /// **'Invalid argument provided.'**
  String get invalidArgumentError;

  /// Dismiss button text
  ///
  /// In en, this message translates to:
  /// **'Dismiss'**
  String get dismiss;

  /// Error dialog title
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get errorDialogTitle;

  /// OK button text
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// Amount range validation error
  ///
  /// In en, this message translates to:
  /// **'Amount is too large'**
  String get amountTooLarge;

  /// Account selection validation error
  ///
  /// In en, this message translates to:
  /// **'Please select an account'**
  String get accountRequired;

  /// Category selection validation error
  ///
  /// In en, this message translates to:
  /// **'Please select a category'**
  String get categoryRequired;

  /// Date field validation error
  ///
  /// In en, this message translates to:
  /// **'Date is required'**
  String get dateRequired;

  /// Description length validation error
  ///
  /// In en, this message translates to:
  /// **'Description must be 100 characters or less'**
  String get descriptionTooLong;

  /// Notes length validation error
  ///
  /// In en, this message translates to:
  /// **'Notes must be 500 characters or less'**
  String get notesTooLong;

  /// Transaction save success message
  ///
  /// In en, this message translates to:
  /// **'Transaction saved successfully'**
  String get transactionSaved;

  /// Transaction save error message
  ///
  /// In en, this message translates to:
  /// **'Error saving transaction. Please try again.'**
  String get errorSavingTransaction;

  /// Account selector placeholder
  ///
  /// In en, this message translates to:
  /// **'Select Account'**
  String get selectAccount;

  /// Category selector placeholder
  ///
  /// In en, this message translates to:
  /// **'Select Category'**
  String get selectCategory;

  /// No category option in selector
  ///
  /// In en, this message translates to:
  /// **'No Category'**
  String get noCategory;

  /// Amount field hint text
  ///
  /// In en, this message translates to:
  /// **'Enter amount'**
  String get enterAmount;

  /// Description field hint text
  ///
  /// In en, this message translates to:
  /// **'Enter description (optional)'**
  String get enterDescription;

  /// Notes field hint text
  ///
  /// In en, this message translates to:
  /// **'Enter additional notes (optional)'**
  String get enterNotes;

  /// Saving transaction loading text
  ///
  /// In en, this message translates to:
  /// **'Saving...'**
  String get saving;

  /// Button text for creating a new transaction
  ///
  /// In en, this message translates to:
  /// **'Create Transaction'**
  String get createTransaction;

  /// Button text for updating an existing transaction
  ///
  /// In en, this message translates to:
  /// **'Update Transaction'**
  String get updateTransaction;

  /// Success message when transaction is created
  ///
  /// In en, this message translates to:
  /// **'Transaction created successfully'**
  String get transactionCreated;

  /// Success message when transaction is updated
  ///
  /// In en, this message translates to:
  /// **'Transaction updated successfully'**
  String get transactionUpdated;

  /// Success message when transaction is deleted
  ///
  /// In en, this message translates to:
  /// **'Transaction deleted successfully'**
  String get transactionDeleted;

  /// Loading message when deleting transaction
  ///
  /// In en, this message translates to:
  /// **'Deleting transaction...'**
  String get deletingTransaction;

  /// Error message when transaction deletion fails
  ///
  /// In en, this message translates to:
  /// **'Failed to delete transaction'**
  String get transactionDeleteError;

  /// Generic transaction operation error message
  ///
  /// In en, this message translates to:
  /// **'Transaction operation failed. Please try again.'**
  String get transactionOperationError;

  /// Operation timeout error message
  ///
  /// In en, this message translates to:
  /// **'Operation timed out. Please try again.'**
  String get operationTimeoutError;

  /// Transaction not found error message
  ///
  /// In en, this message translates to:
  /// **'Transaction not found or has been deleted.'**
  String get transactionNotFoundError;

  /// Transaction already exists error message
  ///
  /// In en, this message translates to:
  /// **'Transaction already exists.'**
  String get transactionAlreadyExistsError;

  /// Operation failed error message
  ///
  /// In en, this message translates to:
  /// **'Operation failed due to invalid conditions.'**
  String get operationFailedError;

  /// Invalid data error message
  ///
  /// In en, this message translates to:
  /// **'Invalid data provided.'**
  String get invalidDataError;

  /// Feature not available error message
  ///
  /// In en, this message translates to:
  /// **'This feature is not available.'**
  String get featureNotAvailableError;

  /// Internal server error message
  ///
  /// In en, this message translates to:
  /// **'Internal server error. Please try again later.'**
  String get internalServerError;

  /// Data corruption error message
  ///
  /// In en, this message translates to:
  /// **'Data corruption detected. Please contact support.'**
  String get dataCorruptionError;

  /// Authentication required error message
  ///
  /// In en, this message translates to:
  /// **'Authentication required. Please sign in again.'**
  String get authenticationRequiredError;

  /// Generic Firestore error prefix
  ///
  /// In en, this message translates to:
  /// **'Database error'**
  String get firestoreError;

  /// Account not found error message
  ///
  /// In en, this message translates to:
  /// **'Account not found or has been deleted.'**
  String get accountNotFoundError;

  /// Invalid amount error message
  ///
  /// In en, this message translates to:
  /// **'Invalid amount provided.'**
  String get invalidAmountError;

  /// Generic validation error message
  ///
  /// In en, this message translates to:
  /// **'Please check your input and try again.'**
  String get validationError;

  /// Error message when transaction creation fails
  ///
  /// In en, this message translates to:
  /// **'Failed to create transaction'**
  String get transactionCreateError;

  /// Error message when source and destination accounts are the same for transfers
  ///
  /// In en, this message translates to:
  /// **'Source and destination accounts must be different'**
  String get sameAccountError;

  /// Error message when destination account is not selected for transfers
  ///
  /// In en, this message translates to:
  /// **'Destination account is required'**
  String get destinationAccountRequired;

  /// Filter transactions button text
  ///
  /// In en, this message translates to:
  /// **'Filter Transactions'**
  String get filterTransactions;

  /// Search transactions button text
  ///
  /// In en, this message translates to:
  /// **'Search Transactions'**
  String get searchTransactions;

  /// Hint text for transaction search field
  ///
  /// In en, this message translates to:
  /// **'Search by description or notes...'**
  String get searchTransactionsHint;

  /// Message when no transactions match filters
  ///
  /// In en, this message translates to:
  /// **'No transactions found'**
  String get noTransactionsFound;

  /// Suggestion when no transactions found
  ///
  /// In en, this message translates to:
  /// **'Try adjusting your search or filters'**
  String get tryAdjustingFilters;

  /// Button to clear all filters
  ///
  /// In en, this message translates to:
  /// **'Clear Filters'**
  String get clearFilters;

  /// Error message when transactions fail to load
  ///
  /// In en, this message translates to:
  /// **'Error loading transactions'**
  String get errorLoadingTransactions;

  /// Description for empty transactions state
  ///
  /// In en, this message translates to:
  /// **'Start tracking your finances by adding your first transaction'**
  String get noTransactionsDescription;

  /// Button text to add first transaction
  ///
  /// In en, this message translates to:
  /// **'Add First Transaction'**
  String get addFirstTransaction;

  /// Quick tips section title
  ///
  /// In en, this message translates to:
  /// **'Quick Tips'**
  String get quickTips;

  /// Tip about creating accounts
  ///
  /// In en, this message translates to:
  /// **'Create accounts for your bank accounts, credit cards, and cash'**
  String get tipCreateAccounts;

  /// Tip about creating categories
  ///
  /// In en, this message translates to:
  /// **'Set up categories to organize your income and expenses'**
  String get tipCreateCategories;

  /// Tip about recording transactions
  ///
  /// In en, this message translates to:
  /// **'Record transactions to track your money flow'**
  String get tipRecordTransactions;

  /// Transaction not found error title
  ///
  /// In en, this message translates to:
  /// **'Transaction Not Found'**
  String get transactionNotFound;

  /// Error message when transaction is not found
  ///
  /// In en, this message translates to:
  /// **'This transaction may have been deleted or you don\'t have permission to view it'**
  String get transactionMayHaveBeenDeleted;

  /// Delete transaction dialog title
  ///
  /// In en, this message translates to:
  /// **'Delete Transaction'**
  String get deleteTransaction;

  /// Delete transaction confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this transaction? This action cannot be undone.'**
  String get deleteTransactionConfirmation;

  /// Edit transaction screen title
  ///
  /// In en, this message translates to:
  /// **'Edit Transaction'**
  String get editTransaction;

  /// Error message when transaction fails to load
  ///
  /// In en, this message translates to:
  /// **'Error loading transaction'**
  String get errorLoadingTransaction;

  /// Error message when transaction update fails
  ///
  /// In en, this message translates to:
  /// **'Failed to update transaction'**
  String get transactionUpdateError;

  /// Error message when transaction update fails in modal
  ///
  /// In en, this message translates to:
  /// **'Error updating transaction. Please try again.'**
  String get errorUpdatingTransaction;

  /// Transaction details section title
  ///
  /// In en, this message translates to:
  /// **'Transaction Details'**
  String get transactionDetails;

  /// Failed transaction status
  ///
  /// In en, this message translates to:
  /// **'Failed'**
  String get failed;

  /// Accounts section title in transaction details
  ///
  /// In en, this message translates to:
  /// **'Accounts'**
  String get accountsSection;

  /// Category section title in transaction details
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get categorySection;

  /// Notes section title in transaction details
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get notesSection;

  /// Metadata section title in transaction details
  ///
  /// In en, this message translates to:
  /// **'Metadata'**
  String get metadataSection;

  /// Transaction creation date label
  ///
  /// In en, this message translates to:
  /// **'Created'**
  String get createdAt;

  /// Transaction update date label
  ///
  /// In en, this message translates to:
  /// **'Updated'**
  String get updatedAt;

  /// Placeholder when transaction has no notes
  ///
  /// In en, this message translates to:
  /// **'No notes'**
  String get noNotes;

  /// Error message when transaction details fail to load
  ///
  /// In en, this message translates to:
  /// **'Error loading transaction details'**
  String get errorLoadingTransactionDetails;

  /// Pending transaction status
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// Completed transaction status
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get completed;

  /// Cancelled transaction status
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get cancelled;

  /// All filter option
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// Search placeholder text
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// Today date label
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// Yesterday date label
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get yesterday;

  /// Metadata section title
  ///
  /// In en, this message translates to:
  /// **'Metadata'**
  String get metadata;

  /// Transaction ID field label
  ///
  /// In en, this message translates to:
  /// **'Transaction ID'**
  String get transactionId;

  /// Transaction status field label
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// Transaction tags field label
  ///
  /// In en, this message translates to:
  /// **'Tags'**
  String get tags;

  /// Transfer from account label
  ///
  /// In en, this message translates to:
  /// **'From'**
  String get from;

  /// Transfer to account label
  ///
  /// In en, this message translates to:
  /// **'To'**
  String get to;

  /// Biometric authentication title
  ///
  /// In en, this message translates to:
  /// **'Biometric Authentication'**
  String get biometricAuthentication;

  /// Biometric sign in button text
  ///
  /// In en, this message translates to:
  /// **'Sign in with biometrics'**
  String get signInWithBiometrics;

  /// Biometric authentication button text
  ///
  /// In en, this message translates to:
  /// **'Use biometrics'**
  String get useBiometrics;

  /// Generic biometric error message
  ///
  /// In en, this message translates to:
  /// **'An unknown biometric error occurred. Please try again.'**
  String get biometricUnknownError;

  /// Error when device passcode is not set
  ///
  /// In en, this message translates to:
  /// **'Please set up a device passcode or PIN to use biometric authentication.'**
  String get biometricPasscodeNotSet;

  /// Error when no biometric data is enrolled
  ///
  /// In en, this message translates to:
  /// **'No biometric data is enrolled on this device. Please set up Face ID, Touch ID, or fingerprint authentication in your device settings.'**
  String get biometricNotEnrolled;

  /// Error when biometric authentication is not available
  ///
  /// In en, this message translates to:
  /// **'Biometric authentication is not available on this device.'**
  String get biometricNotAvailable;

  /// Message when user cancels biometric authentication
  ///
  /// In en, this message translates to:
  /// **'Biometric authentication was cancelled.'**
  String get biometricUserCancelled;

  /// Message when user chooses fallback authentication
  ///
  /// In en, this message translates to:
  /// **'Biometric authentication failed. Please use your password to sign in.'**
  String get biometricUserFallback;

  /// Message when system cancels biometric authentication
  ///
  /// In en, this message translates to:
  /// **'Biometric authentication was cancelled by the system. Please try again.'**
  String get biometricSystemCancelled;

  /// Message when biometric context is invalid
  ///
  /// In en, this message translates to:
  /// **'Biometric authentication context is invalid. Please try again.'**
  String get biometricInvalidContext;

  /// Message when biometric authentication is temporarily locked
  ///
  /// In en, this message translates to:
  /// **'Biometric authentication is temporarily locked due to too many failed attempts. Please try again later.'**
  String get biometricLockedOut;

  /// Message when biometric authentication is permanently locked
  ///
  /// In en, this message translates to:
  /// **'Biometric authentication is permanently locked. Please use your password to sign in.'**
  String get biometricPermanentlyLockedOut;

  /// Message when too many biometric attempts have been made
  ///
  /// In en, this message translates to:
  /// **'Too many failed biometric attempts. Please wait before trying again.'**
  String get biometricTooManyAttempts;

  /// Generic biometric platform error message
  ///
  /// In en, this message translates to:
  /// **'A biometric platform error occurred. Please try again or use your password.'**
  String get biometricPlatformError;

  /// Enable biometric authentication setting title
  ///
  /// In en, this message translates to:
  /// **'Enable Biometric Authentication'**
  String get enableBiometricAuth;

  /// Message when biometric authentication is enabled
  ///
  /// In en, this message translates to:
  /// **'Biometric authentication is enabled'**
  String get biometricAuthEnabled;

  /// Message when biometric authentication is disabled
  ///
  /// In en, this message translates to:
  /// **'Biometric authentication is disabled'**
  String get biometricAuthDisabled;

  /// Button text to set up biometric authentication
  ///
  /// In en, this message translates to:
  /// **'Set up biometrics'**
  String get setupBiometrics;

  /// Title for biometric setup dialog
  ///
  /// In en, this message translates to:
  /// **'Biometric Setup Required'**
  String get biometricSetupRequired;

  /// Message for biometric setup dialog
  ///
  /// In en, this message translates to:
  /// **'To use biometric authentication, please set up Face ID, Touch ID, or fingerprint authentication in your device settings.'**
  String get biometricSetupMessage;

  /// Button text to go to device settings
  ///
  /// In en, this message translates to:
  /// **'Go to Settings'**
  String get goToSettings;

  /// Authentication settings screen title
  ///
  /// In en, this message translates to:
  /// **'Authentication Settings'**
  String get authenticationSettings;

  /// Security settings section title
  ///
  /// In en, this message translates to:
  /// **'Security Settings'**
  String get securitySettings;

  /// Description for biometric settings
  ///
  /// In en, this message translates to:
  /// **'Use your fingerprint, face, or other biometric data to quickly and securely access your account.'**
  String get biometricSettingsDescription;

  /// Title for biometric gate screen
  ///
  /// In en, this message translates to:
  /// **'Biometric Authentication Required'**
  String get biometricAuthRequired;

  /// Description for biometric gate screen
  ///
  /// In en, this message translates to:
  /// **'Please authenticate with your biometric data to access your account.'**
  String get biometricAuthGateDescription;

  /// Loading message during authentication
  ///
  /// In en, this message translates to:
  /// **'Authenticating...'**
  String get authenticating;

  /// Title for time period selection modal
  ///
  /// In en, this message translates to:
  /// **'Select Time Period'**
  String get selectTimePeriod;

  /// Label for current month period
  ///
  /// In en, this message translates to:
  /// **'Current Month'**
  String get currentMonth;

  /// Display text for current month period
  ///
  /// In en, this message translates to:
  /// **'This Month'**
  String get thisMonth;

  /// Instruction text for month selection
  ///
  /// In en, this message translates to:
  /// **'Select Month'**
  String get selectMonth;

  /// Instruction text for year selection
  ///
  /// In en, this message translates to:
  /// **'Select Year'**
  String get selectYear;

  /// Accessibility label for period selector widget
  ///
  /// In en, this message translates to:
  /// **'Period Selector'**
  String get periodSelector;

  /// Accessibility label for previous period navigation
  ///
  /// In en, this message translates to:
  /// **'Previous Period'**
  String get previousPeriod;

  /// Accessibility label for next period navigation
  ///
  /// In en, this message translates to:
  /// **'Next Period'**
  String get nextPeriod;

  /// Error message when trying to select a future period
  ///
  /// In en, this message translates to:
  /// **'Cannot select future periods'**
  String get cannotSelectFuturePeriod;

  /// Error message for invalid period selection
  ///
  /// In en, this message translates to:
  /// **'Invalid period selection'**
  String get invalidPeriodSelection;

  /// Generic error message for period selection failures
  ///
  /// In en, this message translates to:
  /// **'Error selecting period'**
  String get periodSelectionError;

  /// Label for weekly period type
  ///
  /// In en, this message translates to:
  /// **'Weekly'**
  String get weekly;

  /// Label for quarterly period type
  ///
  /// In en, this message translates to:
  /// **'Quarterly'**
  String get quarterly;

  /// Select button text
  ///
  /// In en, this message translates to:
  /// **'Select'**
  String get select;

  /// Title for bulk budget operations dialog
  ///
  /// In en, this message translates to:
  /// **'Bulk Budget Operations'**
  String get bulkBudgetOperations;

  /// Shows number of selected budgets
  ///
  /// In en, this message translates to:
  /// **'{count, plural, =1{1 budget selected} other{{count} budgets selected}}'**
  String selectedBudgets(int count);

  /// Label for operation selection
  ///
  /// In en, this message translates to:
  /// **'Select Operation'**
  String get selectOperation;

  /// Option to adjust budgets by percentage
  ///
  /// In en, this message translates to:
  /// **'Adjust by Percentage'**
  String get adjustByPercentage;

  /// Description for percentage adjustment
  ///
  /// In en, this message translates to:
  /// **'Increase or decrease budget amounts by a percentage'**
  String get adjustByPercentageDescription;

  /// Label for percentage input field
  ///
  /// In en, this message translates to:
  /// **'Percentage Change'**
  String get percentageChange;

  /// Hint text for percentage input
  ///
  /// In en, this message translates to:
  /// **'e.g., 10 for +10%, -20 for -20%'**
  String get percentageChangeHint;

  /// Validation error for empty percentage
  ///
  /// In en, this message translates to:
  /// **'Percentage is required'**
  String get percentageRequired;

  /// Validation error for invalid percentage
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid percentage'**
  String get invalidPercentage;

  /// Validation error for percentage too low
  ///
  /// In en, this message translates to:
  /// **'Percentage cannot be -100% or lower'**
  String get percentageTooLow;

  /// Validation error for percentage too high
  ///
  /// In en, this message translates to:
  /// **'Percentage cannot exceed +500%'**
  String get percentageTooHigh;

  /// Option to deactivate budgets
  ///
  /// In en, this message translates to:
  /// **'Deactivate Budgets'**
  String get deactivateBudgets;

  /// Description for deactivating budgets
  ///
  /// In en, this message translates to:
  /// **'Hide budgets from active lists'**
  String get deactivateBudgetsDescription;

  /// Option to activate budgets
  ///
  /// In en, this message translates to:
  /// **'Activate Budgets'**
  String get activateBudgets;

  /// Description for activating budgets
  ///
  /// In en, this message translates to:
  /// **'Show budgets in active lists'**
  String get activateBudgetsDescription;

  /// Option to delete budgets
  ///
  /// In en, this message translates to:
  /// **'Delete Budgets'**
  String get deleteBudgets;

  /// Description for deleting budgets
  ///
  /// In en, this message translates to:
  /// **'Permanently remove budgets (cannot be undone)'**
  String get deleteBudgetsDescription;

  /// Button text to adjust budgets
  ///
  /// In en, this message translates to:
  /// **'Adjust Budgets'**
  String get adjustBudgets;

  /// Success message for budget adjustment
  ///
  /// In en, this message translates to:
  /// **'Budgets adjusted successfully'**
  String get budgetsAdjusted;

  /// Success message for budget activation
  ///
  /// In en, this message translates to:
  /// **'Budgets activated successfully'**
  String get budgetsActivated;

  /// Success message for budget deactivation
  ///
  /// In en, this message translates to:
  /// **'Budgets deactivated successfully'**
  String get budgetsDeactivated;

  /// Success message for budget deletion
  ///
  /// In en, this message translates to:
  /// **'Budgets deleted successfully'**
  String get budgetsDeleted;

  /// Error message for failed bulk operation
  ///
  /// In en, this message translates to:
  /// **'Bulk operation failed'**
  String get bulkOperationFailed;

  /// Currency settings screen title
  ///
  /// In en, this message translates to:
  /// **'Currency Settings'**
  String get currencySettings;

  /// Currency selection title
  ///
  /// In en, this message translates to:
  /// **'Select Currency'**
  String get selectCurrency;

  /// Description for currency settings screen
  ///
  /// In en, this message translates to:
  /// **'Choose your preferred currency for displaying amounts throughout the app. This setting applies to all monetary values.'**
  String get currencySettingsDescription;

  /// Available currencies section title
  ///
  /// In en, this message translates to:
  /// **'Available Currencies'**
  String get availableCurrencies;

  /// Preview section title
  ///
  /// In en, this message translates to:
  /// **'Preview'**
  String get preview;

  /// Success message when currency is updated
  ///
  /// In en, this message translates to:
  /// **'Currency updated successfully'**
  String get currencyUpdatedSuccessfully;

  /// Error message when currency update fails
  ///
  /// In en, this message translates to:
  /// **'Failed to update currency preference'**
  String get failedToUpdateCurrency;

  /// Title for error boundary widget
  ///
  /// In en, this message translates to:
  /// **'Something went wrong'**
  String get errorBoundaryTitle;

  /// Message for error boundary widget
  ///
  /// In en, this message translates to:
  /// **'We\'ve encountered an unexpected error. You can try again or contact support if the problem persists.'**
  String get errorBoundaryMessage;

  /// Button text to report an error
  ///
  /// In en, this message translates to:
  /// **'Report Error'**
  String get reportError;

  /// Success message after reporting an error
  ///
  /// In en, this message translates to:
  /// **'Error reported successfully. Thank you for helping us improve the app.'**
  String get errorReported;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
