{"@@locale": "en", "welcomeBack": "Welcome Back", "@welcomeBack": {"description": "Welcome message on login screen"}, "signInToAccount": "Sign in to your account", "@signInToAccount": {"description": "Subtitle on login screen"}, "email": "Email", "@email": {"description": "Email field label"}, "enterEmailAddress": "Enter your email address", "@enterEmailAddress": {"description": "Email field hint text"}, "enterYourEmail": "Enter your email", "@enterYourEmail": {"description": "Email field hint text for signup"}, "password": "Password", "@password": {"description": "Password field label"}, "enterPassword": "Enter your password", "@enterPassword": {"description": "Password field hint text"}, "createPassword": "Create a password", "@createPassword": {"description": "Password field hint text for signup"}, "forgotPassword": "Forgot Password?", "@forgotPassword": {"description": "Forgot password link text"}, "signIn": "Sign In", "@signIn": {"description": "Sign in button text"}, "orContinueWith": "Or continue with", "@orContinueWith": {"description": "Divider text for social login"}, "continueWithGoogle": "Continue with Google", "@continueWithGoogle": {"description": "Google sign in button text"}, "continueWithApple": "Continue with Apple", "@continueWithApple": {"description": "Apple sign in button text"}, "dontHaveAccount": "Don't have an account?", "@dontHaveAccount": {"description": "Text before sign up link"}, "signUp": "Sign Up", "@signUp": {"description": "Sign up link text"}, "signUpToGetStarted": "Sign up to get started", "@signUpToGetStarted": {"description": "Subtitle on signup screen"}, "createAccount": "Create Account", "@createAccount": {"description": "Create account button text"}, "confirmPassword": "Confirm Password", "@confirmPassword": {"description": "Confirm password field label"}, "confirmPasswordHint": "Re-enter your password", "@confirmPasswordHint": {"description": "Confirm password field hint text"}, "confirmYourPassword": "Confirm your password", "@confirmYourPassword": {"description": "Confirm password field hint text for signup"}, "iAgreeToThe": "I agree to the ", "@iAgreeToThe": {"description": "Beginning of terms acceptance text"}, "termsOfService": "Terms of Service", "@termsOfService": {"description": "Terms of Service link text"}, "and": " and ", "@and": {"description": "Conjunction text between terms and privacy policy"}, "privacyPolicy": "Privacy Policy", "@privacyPolicy": {"description": "Privacy Policy link text"}, "pleaseAcceptTermsAndPrivacy": "Please accept the Terms of Service and Privacy Policy", "@pleaseAcceptTermsAndPrivacy": {"description": "Error message when user doesn't accept terms"}, "welcomeToApp": "Welcome, {name}!", "@welcomeToApp": {"description": "Welcome message with user name", "placeholders": {"name": {"type": "String", "description": "User's display name or email"}}}, "appleSignupSuccessful": "Apple signup successful!", "@appleSignupSuccessful": {"description": "Success message for Apple signup"}, "appleSignupFailed": "Apple signup failed: {error}", "@appleSignupFailed": {"description": "Error message for Apple signup failure", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "accountCreatedSuccessfully": "Account created successfully! Please check your email to verify your account.", "@accountCreatedSuccessfully": {"description": "Success message after account creation"}, "alreadyHaveAccount": "Already have an account?", "@alreadyHaveAccount": {"description": "Text before sign in link on signup screen"}, "environment": "Environment:", "@environment": {"description": "Environment label on home screen"}, "firebaseProject": "Firebase Project:", "@firebaseProject": {"description": "Firebase project label on home screen"}, "testFirebaseServices": "Test Firebase Services", "@testFirebaseServices": {"description": "Button text to test Firebase connectivity"}, "firebaseServicesStatus": "Firebase Services Status:", "@firebaseServicesStatus": {"description": "Header for Firebase services status section"}, "firebaseAuth": "Firebase Auth:", "@firebaseAuth": {"description": "Firebase Auth status label"}, "firestore": "Firestore:", "@firestore": {"description": "Firestore status label"}, "signOut": "Sign Out", "@signOut": {"description": "Sign out button text and tooltip"}, "signOutFailed": "Sign out failed: {error}", "@signOutFailed": {"description": "Error message when sign out fails", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "tryAgain": "Try Again", "@tryAgain": {"description": "Try again button text"}, "suggestions": "Suggestions:", "@suggestions": {"description": "Header for suggested actions in error messages"}, "or": "OR", "@or": {"description": "Divider text (OR)"}, "resetPassword": "Reset Password", "@resetPassword": {"description": "Reset password screen title"}, "checkYourEmail": "Check Your Email", "@checkYourEmail": {"description": "Title when password reset email is sent"}, "enterEmailForReset": "Enter your email to receive a password reset link", "@enterEmailForReset": {"description": "Description on forgot password screen"}, "passwordResetEmailSent": "We've sent a password reset link to your email", "@passwordResetEmailSent": {"description": "Description when password reset email is sent"}, "sendResetLink": "Send Reset Link", "@sendResetLink": {"description": "Send reset link button text"}, "passwordResetEmailSentSuccess": "Password reset email sent! Please check your inbox.", "@passwordResetEmailSentSuccess": {"description": "Success message for password reset email"}, "unexpectedError": "An unexpected error occurred. Please try again.", "@unexpectedError": {"description": "Generic error message"}, "userNotFound": "No account found with this email address.", "@userNotFound": {"description": "Error when user not found"}, "invalidEmail": "Please enter a valid email address.", "@invalidEmail": {"description": "Error for invalid email format"}, "tooManyRequests": "Too many requests. Please try again later.", "@tooManyRequests": {"description": "Error for too many requests"}, "emailSentSuccessfully": "Em<PERSON> Successfully!", "@emailSentSuccessfully": {"description": "Success title for email sent"}, "checkEmailForResetLink": "Please check your email ({email}) and click the reset link to create a new password.", "@checkEmailForResetLink": {"description": "Instructions after password reset email sent", "placeholders": {"email": {"type": "String", "description": "User's email address"}}}, "checkSpamFolder": "Don't forget to check your spam folder if you don't see the email.", "@checkSpamFolder": {"description": "Reminder to check spam folder"}, "resendEmail": "<PERSON><PERSON><PERSON>", "@resendEmail": {"description": "Resend email button text"}, "backToSignIn": "Back to Sign In", "@backToSignIn": {"description": "Back to sign in button text"}, "verifyYourEmail": "Verify Your Email", "@verifyYourEmail": {"description": "Email verification screen title"}, "verificationEmailSentTo": "We've sent a verification email to:", "@verificationEmailSentTo": {"description": "Text before showing email address"}, "checkEmailForVerification": "Please check your email and click the verification link to continue. You may need to check your spam folder.", "@checkEmailForVerification": {"description": "Instructions for email verification"}, "resendVerificationEmail": "Resend Verification Email", "@resendVerificationEmail": {"description": "Resend verification email button text"}, "verifiedMyEmail": "I've Verified My Email", "@verifiedMyEmail": {"description": "Button text to check verification status"}, "welcome": "Welcome!", "@welcome": {"description": "Welcome message on home screen"}, "noEmail": "No email", "@noEmail": {"description": "Fallback text when no email available"}, "counterDescription": "You have pushed the button this many times:", "@counterDescription": {"description": "Counter demo description"}, "increment": "Increment", "@increment": {"description": "Increment button tooltip"}, "fieldRequired": "This field is required", "@fieldRequired": {"description": "Generic validation error for required fields"}, "emailRequired": "Email is required", "@emailRequired": {"description": "Validation error for empty email"}, "validEmailRequired": "Please enter a valid email address", "@validEmailRequired": {"description": "Validation error for invalid email format"}, "passwordRequired": "Password is required", "@passwordRequired": {"description": "Validation error for empty password"}, "passwordMinLength": "Password must be at least 6 characters", "@passwordMinLength": {"description": "Validation error for short password"}, "confirmPasswordRequired": "Please confirm your password", "@confirmPasswordRequired": {"description": "Validation error for empty confirm password"}, "passwordsDoNotMatch": "Passwords do not match", "@passwordsDoNotMatch": {"description": "Validation error when passwords don't match"}, "budApp": "BudApp", "@budApp": {"description": "Default app name for welcome messages"}, "welcomeBackSuccess": "Welcome back!", "@welcomeBackSuccess": {"description": "Success message after login"}, "googleSignInSuccess": "Welcome! Signed in with Google.", "@googleSignInSuccess": {"description": "Success message after Google sign-in"}, "appleLoginSuccessful": "Apple login successful!", "@appleLoginSuccessful": {"description": "Success message after Apple login"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "information": "Information", "@information": {"description": "Information severity title"}, "warning": "Warning", "@warning": {"description": "Warning severity title"}, "error": "Error", "@error": {"description": "Error severity title"}, "criticalError": "Critical Error", "@criticalError": {"description": "Critical error severity title"}, "accounts": "Accounts", "@accounts": {"description": "Accounts screen title"}, "addAccount": "Add Account", "@addAccount": {"description": "Add account button text"}, "filterAccounts": "Filter Accounts", "@filterAccounts": {"description": "Filter accounts button tooltip"}, "sort": "Sort", "@sort": {"description": "Sort menu item"}, "settings": "Settings", "@settings": {"description": "Settings menu item"}, "assets": "Assets", "@assets": {"description": "Assets section header"}, "liabilities": "Liabilities", "@liabilities": {"description": "Liabilities section header"}, "totalAssets": "Total Assets", "@totalAssets": {"description": "Total assets summary card title"}, "netWorth": "Net Worth", "@netWorth": {"description": "Net worth summary card title"}, "errorLoadingAccounts": "Error Loading Accounts", "@errorLoadingAccounts": {"description": "Error message when accounts fail to load"}, "sortBy": "Sort by", "@sortBy": {"description": "Sort bottom sheet title"}, "name": "Name", "@name": {"description": "Name sort option"}, "balance": "Balance", "@balance": {"description": "Balance sort option and field label"}, "dateCreated": "Date Created", "@dateCreated": {"description": "Date created label"}, "primary": "Primary", "@primary": {"description": "Primary account badge"}, "active": "Active", "@active": {"description": "Active account status"}, "inactive": "Inactive", "@inactive": {"description": "Inactive account badge"}, "edit": "Edit", "@edit": {"description": "Edit action menu item"}, "delete": "Delete", "@delete": {"description": "Delete action menu item"}, "checkingAccount": "Checking Account", "@checkingAccount": {"description": "Checking account type label"}, "savingsAccount": "Savings Account", "@savingsAccount": {"description": "Savings account type label"}, "creditCard": "Credit Card", "@creditCard": {"description": "Credit card account type label"}, "cashAccount": "Cash Account", "@cashAccount": {"description": "Cash account type label"}, "investmentAccount": "Investment Account", "@investmentAccount": {"description": "Investment account type label"}, "loanAccount": "<PERSON>an Account", "@loanAccount": {"description": "Loan account type label"}, "clearAll": "Clear All", "@clearAll": {"description": "Clear all filters button"}, "accountType": "Account Type", "@accountType": {"description": "Account type filter section title"}, "allAccounts": "All Accounts", "@allAccounts": {"description": "All accounts filter option"}, "checking": "Checking", "@checking": {"description": "Checking account type"}, "savings": "Savings", "@savings": {"description": "Savings account type"}, "cash": "Cash", "@cash": {"description": "Cash account type"}, "investment": "Investment", "@investment": {"description": "Investment account type"}, "loan": "Loan", "@loan": {"description": "Loan account type"}, "showInactiveAccounts": "Show Inactive Accounts", "@showInactiveAccounts": {"description": "Show inactive accounts toggle title"}, "includeInactiveAccountsInList": "Include accounts that have been deactivated", "@includeInactiveAccountsInList": {"description": "Show inactive accounts toggle description"}, "apply": "Apply", "@apply": {"description": "Apply filters button"}, "noAccountsYet": "No accounts yet", "@noAccountsYet": {"description": "Empty accounts state title"}, "noAccountsDescription": "Add your first account to start tracking your finances", "@noAccountsDescription": {"description": "Empty accounts state description"}, "createFirstAccount": "Create First Account", "@createFirstAccount": {"description": "Create first account button"}, "learnAboutAccounts": "Learn about accounts", "@learnAboutAccounts": {"description": "Learn about accounts link"}, "aboutAccounts": "About Accounts", "@aboutAccounts": {"description": "About accounts dialog title"}, "accountsHelpDescription": "Accounts represent your financial institutions and cash holdings. Track balances across different account types.", "@accountsHelpDescription": {"description": "Accounts help dialog description"}, "accountTypesTitle": "Account Types:", "@accountTypesTitle": {"description": "Account types section title in help"}, "checkingDescription": "Daily spending and bill payments", "@checkingDescription": {"description": "Checking account description"}, "savingsDescription": "Long-term savings and emergency funds", "@savingsDescription": {"description": "Savings account description"}, "creditCardDescription": "Credit cards and revolving credit", "@creditCardDescription": {"description": "Credit card description"}, "cashDescription": "Physical cash and digital wallets", "@cashDescription": {"description": "Cash description"}, "gotIt": "Got it", "@gotIt": {"description": "Acknowledgment button text"}, "accountNameRequired": "Account name is required", "@accountNameRequired": {"description": "Account name validation error"}, "accountNameMinLength": "Account name must be at least 2 characters", "@accountNameMinLength": {"description": "Account name minimum length validation error"}, "accountNameMaxLength": "Account name cannot exceed 50 characters", "@accountNameMaxLength": {"description": "Account name maximum length validation error"}, "accountNameInvalidCharacters": "Account name contains invalid characters", "@accountNameInvalidCharacters": {"description": "Account name invalid characters validation error"}, "accountDescriptionMaxLength": "Account description cannot exceed 200 characters", "@accountDescriptionMaxLength": {"description": "Account description maximum length validation error"}, "initialBalanceRequired": "Initial balance is required", "@initialBalanceRequired": {"description": "Initial balance validation error"}, "initialBalanceInvalid": "Please enter a valid balance amount", "@initialBalanceInvalid": {"description": "Initial balance format validation error"}, "initialBalanceOutOfRange": "Balance amount is too large", "@initialBalanceOutOfRange": {"description": "Initial balance range validation error"}, "initialBalanceMaxDecimals": "Balance can have at most 2 decimal places", "@initialBalanceMaxDecimals": {"description": "Initial balance decimal places validation error"}, "currencyCodeRequired": "Currency code is required", "@currencyCodeRequired": {"description": "Currency code validation error"}, "currencyCodeInvalid": "Please enter a valid 3-letter currency code", "@currencyCodeInvalid": {"description": "Currency code format validation error"}, "currencyCodeUnsupported": "Currency code is not supported", "@currencyCodeUnsupported": {"description": "Currency code support validation error"}, "accountTypeRequired": "Account type is required", "@accountTypeRequired": {"description": "Account type validation error"}, "accountClassificationRequired": "Account classification is required", "@accountClassificationRequired": {"description": "Account classification validation error"}, "accountTypeClassificationMismatch": "Account type and classification don't match", "@accountTypeClassificationMismatch": {"description": "Account type-classification pairing validation error"}, "colorHexInvalid": "Please enter a valid hex color code (e.g., #FF5733)", "@colorHexInvalid": {"description": "Color hex validation error"}, "iconNameInvalid": "Please enter a valid icon name", "@iconNameInvalid": {"description": "Icon name validation error"}, "save": "Save", "@save": {"description": "Save button text"}, "accountName": "Account Name", "@accountName": {"description": "Account name field label"}, "accountNameHint": "e.g., Main Checking", "@accountNameHint": {"description": "Account name field hint text"}, "initialBalance": "Initial Balance", "@initialBalance": {"description": "Initial balance field label"}, "currency": "<PERSON><PERSON><PERSON><PERSON>", "@currency": {"description": "Currency label"}, "accountDescriptionHint": "Optional description for this account", "@accountDescriptionHint": {"description": "Account description field hint text"}, "customize": "Customize", "@customize": {"description": "Customization section title"}, "optional": "(optional)", "@optional": {"description": "Optional field indicator"}, "accountColor": "Account Color", "@accountColor": {"description": "Account color selection label"}, "defaultColor": "<PERSON><PERSON><PERSON>", "@defaultColor": {"description": "Default color option"}, "accountIcon": "Account I<PERSON>", "@accountIcon": {"description": "Account icon selection label"}, "defaultIcon": "<PERSON><PERSON><PERSON>", "@defaultIcon": {"description": "Default icon option"}, "creating": "Creating...", "@creating": {"description": "Creating account loading text"}, "invalidBalance": "Invalid balance amount", "@invalidBalance": {"description": "Invalid balance error message"}, "errorCreatingAccount": "Error creating account. Please try again.", "@errorCreatingAccount": {"description": "Error message when account creation fails"}, "creditCardAccount": "Credit Card Account", "@creditCardAccount": {"description": "Credit card account type label"}, "description": "Description", "@description": {"description": "Description field label"}, "accountDetails": "Account Details", "@accountDetails": {"description": "Account details screen title"}, "editAccount": "Edit Account", "@editAccount": {"description": "Edit account menu option"}, "setPrimary": "Set as Primary", "@setPrimary": {"description": "Set primary account menu option"}, "deactivateAccount": "Deactivate Account", "@deactivateAccount": {"description": "Deactivate account menu option"}, "deleteAccount": "Delete Account", "@deleteAccount": {"description": "Delete account menu option"}, "currentBalance": "Current Balance", "@currentBalance": {"description": "Current balance label on account detail"}, "classification": "Classification", "@classification": {"description": "Account classification label"}, "lastUpdated": "Last Updated", "@lastUpdated": {"description": "Last updated label"}, "accountSettings": "Account <PERSON><PERSON>", "@accountSettings": {"description": "Account settings section title"}, "primaryAccount": "Primary Account", "@primaryAccount": {"description": "Primary account label"}, "recentTransactions": "Recent Transactions", "@recentTransactions": {"description": "Recent transactions section title"}, "viewAll": "View All", "@viewAll": {"description": "View all button text"}, "noTransactionsYet": "No transactions yet", "@noTransactionsYet": {"description": "Empty transactions state title"}, "transactionsWillAppearHere": "Transactions for this account will appear here", "@transactionsWillAppearHere": {"description": "Empty transactions state description"}, "accountNotFound": "Account Not Found", "@accountNotFound": {"description": "Account not found error title"}, "accountMayHaveBeenDeleted": "This account may have been deleted or you don't have permission to view it", "@accountMayHaveBeenDeleted": {"description": "Account not found error description"}, "goBack": "Go Back", "@goBack": {"description": "Go back button text"}, "errorLoadingAccount": "Error Loading Account", "@errorLoadingAccount": {"description": "Error loading account message"}, "setPrimaryAccount": "Set Primary Account", "@setPrimaryAccount": {"description": "Set primary account dialog title"}, "setPrimaryAccountConfirmation": "Are you sure you want to set this as your primary account?", "@setPrimaryAccountConfirmation": {"description": "Set primary account confirmation message"}, "deactivateAccountConfirmation": "Are you sure you want to deactivate this account? It will be hidden from your account list.", "@deactivateAccountConfirmation": {"description": "Deactivate account confirmation message"}, "deleteAccountConfirmation": "Are you sure you want to delete this account? This action cannot be undone.", "@deleteAccountConfirmation": {"description": "Delete account confirmation message"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button text"}, "asset": "<PERSON><PERSON>", "@asset": {"description": "Asset classification label"}, "liability": "Liability", "@liability": {"description": "Liability classification label"}, "updating": "Updating...", "@updating": {"description": "Loading text when updating an account"}, "updateAccount": "Update Account", "@updateAccount": {"description": "Button text for updating an account"}, "accountUpdatedSuccessfully": "Account updated successfully", "@accountUpdatedSuccessfully": {"description": "Success message when account is updated"}, "errorUpdatingAccount": "Error updating account. Please try again.", "@errorUpdatingAccount": {"description": "Error message when account update fails"}, "quickActions": "Quick Actions", "@quickActions": {"description": "Quick actions section title on home screen"}, "manageAccounts": "Manage Accounts", "@manageAccounts": {"description": "Manage accounts card title"}, "viewAndEditAccounts": "View and edit your financial accounts", "@viewAndEditAccounts": {"description": "Manage accounts card description"}, "transactions": "Transactions", "@transactions": {"description": "Transactions screen title"}, "manageTransactions": "Manage Transactions", "@manageTransactions": {"description": "Manage transactions card title"}, "addAndViewTransactions": "Add and view your financial transactions", "@addAndViewTransactions": {"description": "Manage transactions card description"}, "budgets": "Budgets", "@budgets": {"description": "Budgets screen title"}, "manageBudgets": "Manage Budgets", "@manageBudgets": {"description": "Manage budgets card title"}, "createAndTrackBudgets": "Create and track your spending budgets", "@createAndTrackBudgets": {"description": "Manage budgets card description"}, "createBudget": "Create Budget", "@createBudget": {"description": "Create budget screen title"}, "editBudget": "Edit Budget", "@editBudget": {"description": "Edit budget screen title"}, "budgetName": "Budget Name", "@budgetName": {"description": "Budget name field label"}, "budgetAmount": "Budget Amount", "@budgetAmount": {"description": "Budget amount field label"}, "budgetAmountHint": "Enter the budget amount", "@budgetAmountHint": {"description": "Budget amount field hint"}, "budgetAmountRequired": "Budget amount is required", "@budgetAmountRequired": {"description": "Budget amount validation error"}, "budgetPeriod": "Budget Period", "@budgetPeriod": {"description": "Budget period field label"}, "monthly": "Monthly", "@monthly": {"description": "Label for monthly period type"}, "yearly": "Yearly", "@yearly": {"description": "Label for yearly period type"}, "budgetCategory": "Category", "@budgetCategory": {"description": "Budget category field label"}, "allCategories": "All Categories", "@allCategories": {"description": "All categories filter option"}, "budgetDescription": "Description (Optional)", "@budgetDescription": {"description": "Budget description field label"}, "budgetDescriptionHint": "Add notes about this budget", "@budgetDescriptionHint": {"description": "Budget description field hint"}, "createFirstBudget": "Create Your First Budget", "@createFirstBudget": {"description": "Create first budget button text"}, "noBudgetsYet": "No budgets yet", "@noBudgetsYet": {"description": "Empty budgets state title"}, "noBudgetsDescription": "Create budgets to track your spending and stay on top of your finances.", "@noBudgetsDescription": {"description": "Empty budgets state description"}, "noBudgetsForMonth": "No budgets for this month", "@noBudgetsForMonth": {"description": "Empty filtered budgets state title"}, "viewAllBudgets": "View All Budgets", "@viewAllBudgets": {"description": "View all budgets button text"}, "budgetCreated": "Budget created successfully", "@budgetCreated": {"description": "Budget creation success message"}, "budgetUpdated": "Budget updated successfully", "@budgetUpdated": {"description": "Budget update success message"}, "budgetDeleted": "Budget deleted successfully", "@budgetDeleted": {"description": "Budget deletion success message"}, "errorCreatingBudget": "Failed to create budget", "@errorCreatingBudget": {"description": "Budget creation error message"}, "errorUpdatingBudget": "Failed to update budget", "@errorUpdatingBudget": {"description": "Budget update error message"}, "errorDeletingBudget": "Failed to delete budget", "@errorDeletingBudget": {"description": "Budget deletion error message"}, "errorLoadingBudget": "Failed to load budget", "@errorLoadingBudget": {"description": "Budget loading error message"}, "deleteBudget": "Delete Budget", "@deleteBudget": {"description": "Delete budget dialog title"}, "deleteBudgetConfirmation": "Are you sure you want to delete this budget? This action cannot be undone.", "@deleteBudgetConfirmation": {"description": "Delete budget confirmation message"}, "discardChanges": "Discard Changes", "@discardChanges": {"description": "Discard changes dialog title"}, "discardChangesConfirmation": "You have unsaved changes. Are you sure you want to discard them?", "@discardChangesConfirmation": {"description": "Discard changes confirmation message"}, "keepEditing": "Keep Editing", "@keepEditing": {"description": "Keep editing button text"}, "discard": "Discard", "@discard": {"description": "Discard button text"}, "saveChanges": "Save Changes", "@saveChanges": {"description": "Save changes button text"}, "spent": "Spent", "@spent": {"description": "Amount spent label"}, "remaining": "Remaining", "@remaining": {"description": "Amount remaining label"}, "overBudget": "Over Budget", "@overBudget": {"description": "Over budget status"}, "onTrack": "On Track", "@onTrack": {"description": "On track budget status"}, "nearLimit": "Near Limit", "@nearLimit": {"description": "Near limit budget status"}, "goals": "Goals", "@goals": {"description": "Goals screen title"}, "manageGoals": "Manage Goals", "@manageGoals": {"description": "Manage goals card title"}, "setAndTrackGoals": "Set and track your financial goals", "@setAndTrackGoals": {"description": "Manage goals card description"}, "comingSoon": "Coming Soon", "@comingSoon": {"description": "Coming soon placeholder text"}, "profile": "Profile", "@profile": {"description": "User profile navigation item"}, "importExport": "Import/Export", "@importExport": {"description": "Import/Export data navigation item"}, "reports": "Reports", "@reports": {"description": "Reports and analytics navigation item"}, "help": "Help", "@help": {"description": "Help and support navigation item"}, "feedback": "<PERSON><PERSON><PERSON>", "@feedback": {"description": "Feedback navigation item"}, "categories": "Categories", "@categories": {"description": "Categories screen title"}, "addCategory": "Add Category", "@addCategory": {"description": "Add category button text"}, "filterCategories": "Filter Categories", "@filterCategories": {"description": "Filter categories button tooltip"}, "categoryType": "Category Type", "@categoryType": {"description": "Category type filter section title"}, "incomeCategories": "Income Categories", "@incomeCategories": {"description": "Income categories filter option"}, "expenseCategories": "Expense Categories", "@expenseCategories": {"description": "Expense categories filter option"}, "customCategories": "Custom Categories", "@customCategories": {"description": "Custom categories filter option"}, "predefinedCategories": "Predefined Categories", "@predefinedCategories": {"description": "Predefined categories filter option"}, "showInactiveCategories": "Show Inactive Categories", "@showInactiveCategories": {"description": "Show inactive categories toggle title"}, "includeInactiveCategoriesInList": "Include categories that have been deactivated", "@includeInactiveCategoriesInList": {"description": "Show inactive categories toggle description"}, "noCategoriesYet": "No categories yet", "@noCategoriesYet": {"description": "Empty categories state title"}, "noCategoriesDescription": "Add your first category to start organizing your transactions", "@noCategoriesDescription": {"description": "Empty categories state description"}, "createFirstCategory": "Create First Category", "@createFirstCategory": {"description": "Create first category button"}, "learnAboutCategories": "Learn about categories", "@learnAboutCategories": {"description": "Learn about categories link"}, "aboutCategories": "About Categories", "@aboutCategories": {"description": "About categories dialog title"}, "categoriesHelpDescription": "Categories help you organize and track your income and expenses. Create custom categories or use predefined ones.", "@categoriesHelpDescription": {"description": "Categories help dialog description"}, "categoryTypesTitle": "Category Types:", "@categoryTypesTitle": {"description": "Category types section title in help"}, "incomeDescription": "Money coming in (salary, investments, etc.)", "@incomeDescription": {"description": "Description for income category type"}, "expenseDescription": "Money going out (bills, purchases, etc.)", "@expenseDescription": {"description": "Description for expense category type"}, "subcategoriesDescription": "Create subcategories to organize your categories further", "@subcategoriesDescription": {"description": "Subcategories description in help"}, "errorLoadingCategories": "Error Loading Categories", "@errorLoadingCategories": {"description": "Error message when categories fail to load"}, "categoryDetails": "Category Details", "@categoryDetails": {"description": "Category details screen title"}, "editCategory": "Edit Category", "@editCategory": {"description": "Edit category menu option"}, "addSubcategory": "Add Subcategory", "@addSubcategory": {"description": "Add subcategory menu option"}, "deactivateCategory": "Deactivate Category", "@deactivateCategory": {"description": "Deactivate category menu option"}, "deleteCategory": "Delete Category", "@deleteCategory": {"description": "Delete category menu option"}, "categoryName": "Category Name", "@categoryName": {"description": "Category name field label"}, "categoryNameHint": "Enter category name", "@categoryNameHint": {"description": "Hint text for category name field"}, "categoryDescriptionHint": "Enter description (optional)", "@categoryDescriptionHint": {"description": "Hint text for category description field"}, "categoryColor": "Category Color", "@categoryColor": {"description": "Category color selection label"}, "categoryIcon": "Category Icon", "@categoryIcon": {"description": "Category icon selection label"}, "parentCategory": "Parent Category", "@parentCategory": {"description": "Parent category selection label"}, "rootCategory": "Root Category", "@rootCategory": {"description": "Root category option (no parent)"}, "subcategories": "Subcategories", "@subcategories": {"description": "Subcategories section title"}, "subcategoryCount": "{count} subcategories", "@subcategoryCount": {"description": "Subcategory count display", "placeholders": {"count": {"type": "int", "description": "Number of subcategories"}}}, "noSubcategories": "No subcategories", "@noSubcategories": {"description": "No subcategories message"}, "expandCategory": "Expand category", "@expandCategory": {"description": "Expand category tooltip"}, "collapseCategory": "Collapse category", "@collapseCategory": {"description": "Collapse category tooltip"}, "categoryNotFound": "Category Not Found", "@categoryNotFound": {"description": "Error message when category is not found"}, "categoryMayHaveBeenDeleted": "This category may have been deleted or you may not have permission to access it.", "@categoryMayHaveBeenDeleted": {"description": "Additional error message for category not found"}, "errorLoadingCategory": "Error Loading Category", "@errorLoadingCategory": {"description": "Error message when category fails to load"}, "deactivateCategoryConfirmation": "Are you sure you want to deactivate this category? It will be hidden from your category list.", "@deactivateCategoryConfirmation": {"description": "Deactivate category confirmation message"}, "deleteCategoryConfirmation": "Are you sure you want to delete this category? This action cannot be undone.", "@deleteCategoryConfirmation": {"description": "Delete category confirmation message"}, "cannotDeleteCategoryWithTransactions": "Cannot delete category \"{categoryName}\": it has associated transactions. Please reassign or delete the transactions first.", "@cannotDeleteCategoryWithTransactions": {"description": "Error message when trying to delete category with transactions", "placeholders": {"categoryName": {"type": "String", "description": "Name of the category"}}}, "cannotDeleteCategoryWithSubcategories": "Cannot delete category \"{categoryName}\": it has subcategories. Please delete or move the subcategories first.", "@cannotDeleteCategoryWithSubcategories": {"description": "Error message when trying to delete category with subcategories", "placeholders": {"categoryName": {"type": "String", "description": "Name of the category"}}}, "cannotDeleteSubcategoryWithTransactions": "Cannot delete subcategory \"{subcategoryName}\": it has associated transactions. Please reassign or delete the transactions first.", "@cannotDeleteSubcategoryWithTransactions": {"description": "Error message when trying to delete subcategory with transactions", "placeholders": {"subcategoryName": {"type": "String", "description": "Name of the subcategory"}}}, "cannotDeleteSubcategoryWithChildren": "Cannot delete subcategory \"{subcategoryName}\": it has child subcategories. Please delete or move the child subcategories first.", "@cannotDeleteSubcategoryWithChildren": {"description": "Error message when trying to delete subcategory with children", "placeholders": {"subcategoryName": {"type": "String", "description": "Name of the subcategory"}}}, "subcategoryNotBelongToParent": "Subcategory does not belong to the specified parent category.", "@subcategoryNotBelongToParent": {"description": "Error message when subcategory doesn't belong to parent"}, "errorCheckingDeletionConstraints": "Error checking deletion constraints: {error}", "@errorCheckingDeletionConstraints": {"description": "Error message when checking deletion constraints fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "failedToDeleteCategory": "Failed to delete category: {error}", "@failedToDeleteCategory": {"description": "Error message when category deletion fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "failedToDeleteSubcategory": "Failed to delete subcategory: {error}", "@failedToDeleteSubcategory": {"description": "Error message when subcategory deletion fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "categorySource": "Category Source", "@categorySource": {"description": "Category source label"}, "custom": "Custom", "@custom": {"description": "Label for custom period type"}, "predefined": "Predefined", "@predefined": {"description": "Predefined category source"}, "income": "Income", "@income": {"description": "Income transaction type"}, "expense": "Expense", "@expense": {"description": "Expense transaction type"}, "manageCategories": "Manage Categories", "@manageCategories": {"description": "Manage categories card title"}, "createCategory": "Create Category", "@createCategory": {"description": "Create category screen title"}, "createSubcategory": "Create Subcategory", "@createSubcategory": {"description": "Create subcategory screen title"}, "editCategoryTitle": "Edit Category", "@editCategoryTitle": {"description": "Edit category screen title"}, "editSubcategoryTitle": "Edit Subcategory", "@editSubcategoryTitle": {"description": "Edit subcategory screen title"}, "categoryNameTooShort": "Category name must be at least 2 characters", "@categoryNameTooShort": {"description": "Category name too short validation error"}, "categoryNameTooLong": "Category name must be 50 characters or less", "@categoryNameTooLong": {"description": "Category name too long validation error"}, "categoryNameInvalidCharacters": "Category name can only contain letters, numbers, spaces, hyphens, and apostrophes", "@categoryNameInvalidCharacters": {"description": "Category name invalid characters validation error"}, "categoryDescriptionTooLong": "Description must be 200 characters or less", "@categoryDescriptionTooLong": {"description": "Category description too long validation error"}, "categoryTypeRequired": "Please select a category type", "@categoryTypeRequired": {"description": "Category type required validation error"}, "categoryColorInvalidFormat": "Color must be in hex format (#RRGGBB)", "@categoryColorInvalidFormat": {"description": "Category color invalid format validation error"}, "categoryIconInvalidFormat": "Icon must be a valid identifier (letters, numbers, underscores)", "@categoryIconInvalidFormat": {"description": "Category icon invalid format validation error"}, "parentCategoryRequired": "Please select a parent category", "@parentCategoryRequired": {"description": "Parent category required validation error"}, "categoryCreatedSuccessfully": "Category created successfully", "@categoryCreatedSuccessfully": {"description": "Category created success message"}, "categoryUpdatedSuccessfully": "Category updated successfully", "@categoryUpdatedSuccessfully": {"description": "Category updated success message"}, "errorCreatingCategory": "Error creating category", "@errorCreatingCategory": {"description": "Error creating category message"}, "errorUpdatingCategory": "Error updating category", "@errorUpdatingCategory": {"description": "Error updating category message"}, "selectCategoryType": "Select Category Type", "@selectCategoryType": {"description": "Category type selector title"}, "selectParentCategory": "Select Parent Category", "@selectParentCategory": {"description": "Parent category selector title"}, "noParentCategory": "No Parent (Root Category)", "@noParentCategory": {"description": "No parent category option"}, "selectColor": "Select Color", "@selectColor": {"description": "Color selector title"}, "selectIcon": "Select Icon", "@selectIcon": {"description": "Icon selector title"}, "organizeTransactions": "Organize your transactions with custom categories", "@organizeTransactions": {"description": "Manage categories card description"}, "close": "Close", "@close": {"description": "Close button text"}, "activate": "Activate", "@activate": {"description": "Activate action menu item"}, "categoryDeletionConstraints": "Category Deletion Constraints", "@categoryDeletionConstraints": {"description": "Title for category deletion constraints dialog"}, "categoryHasTransactions": "This category has {count} associated transactions", "@categoryHasTransactions": {"description": "Message showing number of transactions associated with category", "placeholders": {"count": {"type": "int", "description": "Number of transactions"}}}, "categoryHasSubcategories": "This category has {count} subcategories", "@categoryHasSubcategories": {"description": "Message showing number of subcategories", "placeholders": {"count": {"type": "int", "description": "Number of subcategories"}}}, "reassignTransactions": "Reassign Transactions", "@reassignTransactions": {"description": "Button text for reassigning transactions"}, "deleteAnyway": "Delete Anyway", "@deleteAnyway": {"description": "Button text for force deletion"}, "selectNewCategory": "Select New Category", "@selectNewCategory": {"description": "Title for category selection dialog"}, "selectCategoryForReassignment": "Select a category to reassign transactions to:", "@selectCategoryForReassignment": {"description": "Instructions for category reassignment"}, "noAvailableCategories": "No available categories for reassignment", "@noAvailableCategories": {"description": "Message when no categories are available for reassignment"}, "reassignmentConfirmation": "Reassign and Delete", "@reassignmentConfirmation": {"description": "Confirmation button for reassignment and deletion"}, "reassigningTransactions": "Reassigning transactions...", "@reassigningTransactions": {"description": "Loading message during transaction reassignment"}, "transactionsReassignedSuccessfully": "Transactions reassigned successfully", "@transactionsReassignedSuccessfully": {"description": "Success message after transaction reassignment"}, "errorReassigningTransactions": "Error reassigning transactions", "@errorReassigningTransactions": {"description": "Error message when transaction reassignment fails"}, "categoryDeletedSuccessfully": "Category deleted successfully", "@categoryDeletedSuccessfully": {"description": "Success message after category deletion"}, "viewTransactions": "View Transactions", "@viewTransactions": {"description": "Button text to view associated transactions"}, "transactionList": "Transaction List", "@transactionList": {"description": "Title for transaction list dialog"}, "transactionsInCategory": "Transactions in \"{categoryName}\"", "@transactionsInCategory": {"description": "Title showing transactions in a specific category", "placeholders": {"categoryName": {"type": "String", "description": "Name of the category"}}}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "addTransaction": "Add Transaction", "@addTransaction": {"description": "Add transaction screen title"}, "transactionType": "Transaction Type", "@transactionType": {"description": "Transaction type field label"}, "transfer": "Transfer", "@transfer": {"description": "Transfer transaction type"}, "amount": "Amount", "@amount": {"description": "Amount field label"}, "toAccount": "To Account", "@toAccount": {"description": "Destination account field label for income transactions"}, "fromAccount": "From Account", "@fromAccount": {"description": "Source account field label for expense transactions"}, "category": "Category", "@category": {"description": "Category field label"}, "date": "Date", "@date": {"description": "Date field label"}, "selectDate": "Select Date", "@selectDate": {"description": "Date picker placeholder"}, "notes": "Notes", "@notes": {"description": "Notes field label"}, "required": "Required", "@required": {"description": "Required field indicator"}, "amountRequired": "Amount is required", "@amountRequired": {"description": "Amount field validation error"}, "amountInvalid": "Please enter a valid amount", "@amountInvalid": {"description": "Amount format validation error"}, "invalidNumber": "Please enter a valid number", "@invalidNumber": {"description": "Generic number validation error"}, "pleaseSelect": "Please select", "@pleaseSelect": {"description": "Dropdown placeholder text"}, "noItemsAvailable": "No items available", "@noItemsAvailable": {"description": "Dropdown empty state message"}, "errorLoadingData": "Error loading data", "@errorLoadingData": {"description": "Dropdown error state message"}, "networkError": "Network connection error. Please check your internet connection and try again.", "@networkError": {"description": "Network connectivity error message"}, "permissionError": "You don't have permission to perform this action.", "@permissionError": {"description": "Permission error message"}, "notFoundError": "The requested item was not found.", "@notFoundError": {"description": "Not found error message"}, "genericError": "An unexpected error occurred. Please try again.", "@genericError": {"description": "Generic error message"}, "userNotFoundError": "No user found with this email address.", "@userNotFoundError": {"description": "User not found error"}, "wrongPasswordError": "Incorrect password. Please try again.", "@wrongPasswordError": {"description": "Wrong password error"}, "emailAlreadyInUseError": "An account already exists with this email address.", "@emailAlreadyInUseError": {"description": "Email already in use error"}, "weakPasswordError": "Password is too weak. Please choose a stronger password.", "@weakPasswordError": {"description": "Weak password error"}, "invalidEmailError": "Please enter a valid email address.", "@invalidEmailError": {"description": "Invalid email error"}, "userDisabledError": "This account has been disabled.", "@userDisabledError": {"description": "User disabled error"}, "tooManyRequestsError": "Too many attempts. Please try again later.", "@tooManyRequestsError": {"description": "Too many requests error"}, "operationNotAllowedError": "This operation is not allowed.", "@operationNotAllowedError": {"description": "Operation not allowed error"}, "requiresRecentLoginError": "Please sign in again to continue.", "@requiresRecentLoginError": {"description": "Requires recent login error"}, "authenticationError": "Authentication failed. Please try again.", "@authenticationError": {"description": "Authentication error"}, "permissionDeniedError": "You don't have permission to perform this action.", "@permissionDeniedError": {"description": "Permission denied error message"}, "serviceUnavailableError": "Service is temporarily unavailable. Please try again later.", "@serviceUnavailableError": {"description": "Service unavailable error message"}, "timeoutError": "Request timed out. Please try again.", "@timeoutError": {"description": "Timeout error"}, "documentNotFoundError": "Document not found.", "@documentNotFoundError": {"description": "Document not found error"}, "documentAlreadyExistsError": "Document already exists.", "@documentAlreadyExistsError": {"description": "Document already exists error"}, "quotaExceededError": "Service quota exceeded. Please try again later.", "@quotaExceededError": {"description": "<PERSON><PERSON><PERSON> exceeded error message"}, "preconditionFailedError": "Operation failed due to precondition.", "@preconditionFailedError": {"description": "Precondition failed error"}, "operationAbortedError": "Operation was aborted. Please try again.", "@operationAbortedError": {"description": "Operation aborted error message"}, "outOfRangeError": "Value is out of range.", "@outOfRangeError": {"description": "Out of range error"}, "featureNotImplementedError": "Feature not implemented.", "@featureNotImplementedError": {"description": "Feature not implemented error"}, "internalError": "Internal server error. Please try again.", "@internalError": {"description": "Internal error"}, "dataLossError": "Data loss detected. Please contact support.", "@dataLossError": {"description": "Data loss error"}, "unauthenticatedError": "Please sign in to continue.", "@unauthenticatedError": {"description": "Unauthenticated error"}, "databaseError": "Database error. Please try again.", "@databaseError": {"description": "Database error"}, "invalidDateFormatError": "Invalid date format.", "@invalidDateFormatError": {"description": "Invalid date format error"}, "invalidNumberFormatError": "Invalid number format.", "@invalidNumberFormatError": {"description": "Invalid number format error"}, "invalidFormatError": "Invalid format.", "@invalidFormatError": {"description": "Invalid format error"}, "invalidArgumentError": "Invalid argument provided.", "@invalidArgumentError": {"description": "Invalid argument error"}, "dismiss": "<PERSON><PERSON><PERSON>", "@dismiss": {"description": "Dismiss button text"}, "errorDialogTitle": "Error", "@errorDialogTitle": {"description": "Error dialog title"}, "ok": "OK", "@ok": {"description": "OK button text"}, "amountTooLarge": "Amount is too large", "@amountTooLarge": {"description": "Amount range validation error"}, "accountRequired": "Please select an account", "@accountRequired": {"description": "Account selection validation error"}, "categoryRequired": "Please select a category", "@categoryRequired": {"description": "Category selection validation error"}, "dateRequired": "Date is required", "@dateRequired": {"description": "Date field validation error"}, "descriptionTooLong": "Description must be 100 characters or less", "@descriptionTooLong": {"description": "Description length validation error"}, "notesTooLong": "Notes must be 500 characters or less", "@notesTooLong": {"description": "Notes length validation error"}, "transactionSaved": "Transaction saved successfully", "@transactionSaved": {"description": "Transaction save success message"}, "errorSavingTransaction": "Error saving transaction. Please try again.", "@errorSavingTransaction": {"description": "Transaction save error message"}, "selectAccount": "Select Account", "@selectAccount": {"description": "Account selector placeholder"}, "selectCategory": "Select Category", "@selectCategory": {"description": "Category selector placeholder"}, "noCategory": "No Category", "@noCategory": {"description": "No category option in selector"}, "enterAmount": "Enter amount", "@enterAmount": {"description": "Amount field hint text"}, "enterDescription": "Enter description (optional)", "@enterDescription": {"description": "Description field hint text"}, "enterNotes": "Enter additional notes (optional)", "@enterNotes": {"description": "Notes field hint text"}, "saving": "Saving...", "@saving": {"description": "Saving transaction loading text"}, "createTransaction": "Create Transaction", "@createTransaction": {"description": "Button text for creating a new transaction"}, "updateTransaction": "Update Transaction", "@updateTransaction": {"description": "Button text for updating an existing transaction"}, "transactionCreated": "Transaction created successfully", "@transactionCreated": {"description": "Success message when transaction is created"}, "transactionUpdated": "Transaction updated successfully", "@transactionUpdated": {"description": "Success message when transaction is updated"}, "transactionDeleted": "Transaction deleted successfully", "@transactionDeleted": {"description": "Success message when transaction is deleted"}, "deletingTransaction": "Deleting transaction...", "@deletingTransaction": {"description": "Loading message when deleting transaction"}, "transactionDeleteError": "Failed to delete transaction", "@transactionDeleteError": {"description": "Error message when transaction deletion fails"}, "transactionOperationError": "Transaction operation failed. Please try again.", "@transactionOperationError": {"description": "Generic transaction operation error message"}, "operationTimeoutError": "Operation timed out. Please try again.", "@operationTimeoutError": {"description": "Operation timeout error message"}, "transactionNotFoundError": "Transaction not found or has been deleted.", "@transactionNotFoundError": {"description": "Transaction not found error message"}, "transactionAlreadyExistsError": "Transaction already exists.", "@transactionAlreadyExistsError": {"description": "Transaction already exists error message"}, "operationFailedError": "Operation failed due to invalid conditions.", "@operationFailedError": {"description": "Operation failed error message"}, "invalidDataError": "Invalid data provided.", "@invalidDataError": {"description": "Invalid data error message"}, "featureNotAvailableError": "This feature is not available.", "@featureNotAvailableError": {"description": "Feature not available error message"}, "internalServerError": "Internal server error. Please try again later.", "@internalServerError": {"description": "Internal server error message"}, "dataCorruptionError": "Data corruption detected. Please contact support.", "@dataCorruptionError": {"description": "Data corruption error message"}, "authenticationRequiredError": "Authentication required. Please sign in again.", "@authenticationRequiredError": {"description": "Authentication required error message"}, "firestoreError": "Database error", "@firestoreError": {"description": "Generic Firestore error prefix"}, "accountNotFoundError": "Account not found or has been deleted.", "@accountNotFoundError": {"description": "Account not found error message"}, "invalidAmountError": "Invalid amount provided.", "@invalidAmountError": {"description": "Invalid amount error message"}, "validationError": "Please check your input and try again.", "@validationError": {"description": "Generic validation error message"}, "transactionCreateError": "Failed to create transaction", "@transactionCreateError": {"description": "Error message when transaction creation fails"}, "sameAccountError": "Source and destination accounts must be different", "@sameAccountError": {"description": "Error message when source and destination accounts are the same for transfers"}, "destinationAccountRequired": "Destination account is required", "@destinationAccountRequired": {"description": "Error message when destination account is not selected for transfers"}, "filterTransactions": "Filter Transactions", "@filterTransactions": {"description": "Filter transactions button text"}, "searchTransactions": "Search Transactions", "@searchTransactions": {"description": "Search transactions button text"}, "searchTransactionsHint": "Search by description or notes...", "@searchTransactionsHint": {"description": "Hint text for transaction search field"}, "noTransactionsFound": "No transactions found", "@noTransactionsFound": {"description": "Message when no transactions match filters"}, "tryAdjustingFilters": "Try adjusting your search or filters", "@tryAdjustingFilters": {"description": "Suggestion when no transactions found"}, "clearFilters": "Clear Filters", "@clearFilters": {"description": "Button to clear all filters"}, "errorLoadingTransactions": "Error loading transactions", "@errorLoadingTransactions": {"description": "Error message when transactions fail to load"}, "noTransactionsDescription": "Start tracking your finances by adding your first transaction", "@noTransactionsDescription": {"description": "Description for empty transactions state"}, "addFirstTransaction": "Add First Transaction", "@addFirstTransaction": {"description": "Button text to add first transaction"}, "quickTips": "Quick Tips", "@quickTips": {"description": "Quick tips section title"}, "tipCreateAccounts": "Create accounts for your bank accounts, credit cards, and cash", "@tipCreateAccounts": {"description": "Tip about creating accounts"}, "tipCreateCategories": "Set up categories to organize your income and expenses", "@tipCreateCategories": {"description": "Tip about creating categories"}, "tipRecordTransactions": "Record transactions to track your money flow", "@tipRecordTransactions": {"description": "Tip about recording transactions"}, "transactionNotFound": "Transaction Not Found", "@transactionNotFound": {"description": "Transaction not found error title"}, "transactionMayHaveBeenDeleted": "This transaction may have been deleted or you don't have permission to view it", "@transactionMayHaveBeenDeleted": {"description": "Error message when transaction is not found"}, "deleteTransaction": "Delete Transaction", "@deleteTransaction": {"description": "Delete transaction dialog title"}, "deleteTransactionConfirmation": "Are you sure you want to delete this transaction? This action cannot be undone.", "@deleteTransactionConfirmation": {"description": "Delete transaction confirmation message"}, "editTransaction": "Edit Transaction", "@editTransaction": {"description": "Edit transaction screen title"}, "errorLoadingTransaction": "Error loading transaction", "@errorLoadingTransaction": {"description": "Error message when transaction fails to load"}, "transactionUpdateError": "Failed to update transaction", "@transactionUpdateError": {"description": "Error message when transaction update fails"}, "errorUpdatingTransaction": "Error updating transaction. Please try again.", "@errorUpdatingTransaction": {"description": "Error message when transaction update fails in modal"}, "transactionDetails": "Transaction Details", "@transactionDetails": {"description": "Transaction details section title"}, "failed": "Failed", "@failed": {"description": "Failed transaction status"}, "accountsSection": "Accounts", "@accountsSection": {"description": "Accounts section title in transaction details"}, "categorySection": "Category", "@categorySection": {"description": "Category section title in transaction details"}, "notesSection": "Notes", "@notesSection": {"description": "Notes section title in transaction details"}, "metadataSection": "<PERSON><PERSON><PERSON>", "@metadataSection": {"description": "Metadata section title in transaction details"}, "createdAt": "Created", "@createdAt": {"description": "Transaction creation date label"}, "updatedAt": "Updated", "@updatedAt": {"description": "Transaction update date label"}, "noNotes": "No notes", "@noNotes": {"description": "Placeholder when transaction has no notes"}, "errorLoadingTransactionDetails": "Error loading transaction details", "@errorLoadingTransactionDetails": {"description": "Error message when transaction details fail to load"}, "pending": "Pending", "@pending": {"description": "Pending transaction status"}, "completed": "Completed", "@completed": {"description": "Completed transaction status"}, "cancelled": "Cancelled", "@cancelled": {"description": "Cancelled transaction status"}, "all": "All", "@all": {"description": "All filter option"}, "search": "Search", "@search": {"description": "Search placeholder text"}, "today": "Today", "@today": {"description": "Today date label"}, "yesterday": "Yesterday", "@yesterday": {"description": "Yesterday date label"}, "metadata": "<PERSON><PERSON><PERSON>", "@metadata": {"description": "Metadata section title"}, "transactionId": "Transaction ID", "@transactionId": {"description": "Transaction ID field label"}, "status": "Status", "@status": {"description": "Transaction status field label"}, "tags": "Tags", "@tags": {"description": "Transaction tags field label"}, "from": "From", "@from": {"description": "Transfer from account label"}, "to": "To", "@to": {"description": "Transfer to account label"}, "biometricAuthentication": "Biometric Authentication", "@biometricAuthentication": {"description": "Biometric authentication title"}, "signInWithBiometrics": "Sign in with biometrics", "@signInWithBiometrics": {"description": "Biometric sign in button text"}, "useBiometrics": "Use biometrics", "@useBiometrics": {"description": "Biometric authentication button text"}, "biometricUnknownError": "An unknown biometric error occurred. Please try again.", "@biometricUnknownError": {"description": "Generic biometric error message"}, "biometricPasscodeNotSet": "Please set up a device passcode or PIN to use biometric authentication.", "@biometricPasscodeNotSet": {"description": "Error when device passcode is not set"}, "biometricNotEnrolled": "No biometric data is enrolled on this device. Please set up Face ID, Touch ID, or fingerprint authentication in your device settings.", "@biometricNotEnrolled": {"description": "Error when no biometric data is enrolled"}, "biometricNotAvailable": "Biometric authentication is not available on this device.", "@biometricNotAvailable": {"description": "Error when biometric authentication is not available"}, "biometricUserCancelled": "Biometric authentication was cancelled.", "@biometricUserCancelled": {"description": "Message when user cancels biometric authentication"}, "biometricUserFallback": "Biometric authentication failed. Please use your password to sign in.", "@biometricUserFallback": {"description": "Message when user chooses fallback authentication"}, "biometricSystemCancelled": "Biometric authentication was cancelled by the system. Please try again.", "@biometricSystemCancelled": {"description": "Message when system cancels biometric authentication"}, "biometricInvalidContext": "Biometric authentication context is invalid. Please try again.", "@biometricInvalidContext": {"description": "Message when biometric context is invalid"}, "biometricLockedOut": "Biometric authentication is temporarily locked due to too many failed attempts. Please try again later.", "@biometricLockedOut": {"description": "Message when biometric authentication is temporarily locked"}, "biometricPermanentlyLockedOut": "Biometric authentication is permanently locked. Please use your password to sign in.", "@biometricPermanentlyLockedOut": {"description": "Message when biometric authentication is permanently locked"}, "biometricTooManyAttempts": "Too many failed biometric attempts. Please wait before trying again.", "@biometricTooManyAttempts": {"description": "Message when too many biometric attempts have been made"}, "biometricPlatformError": "A biometric platform error occurred. Please try again or use your password.", "@biometricPlatformError": {"description": "Generic biometric platform error message"}, "enableBiometricAuth": "Enable Biometric Authentication", "@enableBiometricAuth": {"description": "Enable biometric authentication setting title"}, "biometricAuthEnabled": "Biometric authentication is enabled", "@biometricAuthEnabled": {"description": "Message when biometric authentication is enabled"}, "biometricAuthDisabled": "Biometric authentication is disabled", "@biometricAuthDisabled": {"description": "Message when biometric authentication is disabled"}, "setupBiometrics": "Set up biometrics", "@setupBiometrics": {"description": "Button text to set up biometric authentication"}, "biometricSetupRequired": "Biometric Setup Required", "@biometricSetupRequired": {"description": "Title for biometric setup dialog"}, "biometricSetupMessage": "To use biometric authentication, please set up Face ID, Touch ID, or fingerprint authentication in your device settings.", "@biometricSetupMessage": {"description": "Message for biometric setup dialog"}, "goToSettings": "Go to Settings", "@goToSettings": {"description": "Button text to go to device settings"}, "authenticationSettings": "Authentication Settings", "@authenticationSettings": {"description": "Authentication settings screen title"}, "securitySettings": "Security Settings", "@securitySettings": {"description": "Security settings section title"}, "biometricSettingsDescription": "Use your fingerprint, face, or other biometric data to quickly and securely access your account.", "@biometricSettingsDescription": {"description": "Description for biometric settings"}, "biometricAuthRequired": "Biometric Authentication Required", "@biometricAuthRequired": {"description": "Title for biometric gate screen"}, "biometricAuthGateDescription": "Please authenticate with your biometric data to access your account.", "@biometricAuthGateDescription": {"description": "Description for biometric gate screen"}, "authenticating": "Authenticating...", "@authenticating": {"description": "Loading message during authentication"}, "selectTimePeriod": "Select Time Period", "@selectTimePeriod": {"description": "Title for time period selection modal"}, "currentMonth": "Current Month", "@currentMonth": {"description": "Label for current month period"}, "thisMonth": "This Month", "@thisMonth": {"description": "Display text for current month period"}, "selectMonth": "Select Month", "@selectMonth": {"description": "Instruction text for month selection"}, "selectYear": "Select Year", "@selectYear": {"description": "Instruction text for year selection"}, "periodSelector": "Period Selector", "@periodSelector": {"description": "Accessibility label for period selector widget"}, "previousPeriod": "Previous Period", "@previousPeriod": {"description": "Accessibility label for previous period navigation"}, "nextPeriod": "Next Period", "@nextPeriod": {"description": "Accessibility label for next period navigation"}, "cannotSelectFuturePeriod": "Cannot select future periods", "@cannotSelectFuturePeriod": {"description": "Error message when trying to select a future period"}, "invalidPeriodSelection": "Invalid period selection", "@invalidPeriodSelection": {"description": "Error message for invalid period selection"}, "periodSelectionError": "Error selecting period", "@periodSelectionError": {"description": "Generic error message for period selection failures"}, "weekly": "Weekly", "@weekly": {"description": "Label for weekly period type"}, "quarterly": "Quarterly", "@quarterly": {"description": "Label for quarterly period type"}, "select": "Select", "@select": {"description": "Select button text"}, "bulkBudgetOperations": "Bulk Budget Operations", "@bulkBudgetOperations": {"description": "Title for bulk budget operations dialog"}, "selectedBudgets": "{count, plural, =1{1 budget selected} other{{count} budgets selected}}", "@selectedBudgets": {"description": "Shows number of selected budgets", "placeholders": {"count": {"type": "int"}}}, "selectOperation": "Select Operation", "@selectOperation": {"description": "Label for operation selection"}, "adjustByPercentage": "Adjust by Percentage", "@adjustByPercentage": {"description": "Option to adjust budgets by percentage"}, "adjustByPercentageDescription": "Increase or decrease budget amounts by a percentage", "@adjustByPercentageDescription": {"description": "Description for percentage adjustment"}, "percentageChange": "Percentage Change", "@percentageChange": {"description": "Label for percentage input field"}, "percentageChangeHint": "e.g., 10 for +10%, -20 for -20%", "@percentageChangeHint": {"description": "Hint text for percentage input"}, "percentageRequired": "Percentage is required", "@percentageRequired": {"description": "Validation error for empty percentage"}, "invalidPercentage": "Please enter a valid percentage", "@invalidPercentage": {"description": "Validation error for invalid percentage"}, "percentageTooLow": "Percentage cannot be -100% or lower", "@percentageTooLow": {"description": "Validation error for percentage too low"}, "percentageTooHigh": "Percentage cannot exceed +500%", "@percentageTooHigh": {"description": "Validation error for percentage too high"}, "deactivateBudgets": "Deactivate Budgets", "@deactivateBudgets": {"description": "Option to deactivate budgets"}, "deactivateBudgetsDescription": "Hide budgets from active lists", "@deactivateBudgetsDescription": {"description": "Description for deactivating budgets"}, "activateBudgets": "Activate Budgets", "@activateBudgets": {"description": "Option to activate budgets"}, "activateBudgetsDescription": "Show budgets in active lists", "@activateBudgetsDescription": {"description": "Description for activating budgets"}, "deleteBudgets": "Delete Budgets", "@deleteBudgets": {"description": "Option to delete budgets"}, "deleteBudgetsDescription": "Permanently remove budgets (cannot be undone)", "@deleteBudgetsDescription": {"description": "Description for deleting budgets"}, "adjustBudgets": "Adjust Budgets", "@adjustBudgets": {"description": "Button text to adjust budgets"}, "budgetsAdjusted": "Budgets adjusted successfully", "@budgetsAdjusted": {"description": "Success message for budget adjustment"}, "budgetsActivated": "Budgets activated successfully", "@budgetsActivated": {"description": "Success message for budget activation"}, "budgetsDeactivated": "Budgets deactivated successfully", "@budgetsDeactivated": {"description": "Success message for budget deactivation"}, "budgetsDeleted": "Budgets deleted successfully", "@budgetsDeleted": {"description": "Success message for budget deletion"}, "bulkOperationFailed": "Bulk operation failed", "@bulkOperationFailed": {"description": "Error message for failed bulk operation"}, "currencySettings": "<PERSON><PERSON><PERSON><PERSON>", "@currencySettings": {"description": "Currency settings screen title"}, "selectCurrency": "Select Currency", "@selectCurrency": {"description": "Currency selection title"}, "currencySettingsDescription": "Choose your preferred currency for displaying amounts throughout the app. This setting applies to all monetary values.", "@currencySettingsDescription": {"description": "Description for currency settings screen"}, "availableCurrencies": "Available Currencies", "@availableCurrencies": {"description": "Available currencies section title"}, "preview": "Preview", "@preview": {"description": "Preview section title"}, "currencyUpdatedSuccessfully": "Currency updated successfully", "@currencyUpdatedSuccessfully": {"description": "Success message when currency is updated"}, "failedToUpdateCurrency": "Failed to update currency preference", "@failedToUpdateCurrency": {"description": "Error message when currency update fails"}, "errorBoundaryTitle": "Something went wrong", "@errorBoundaryTitle": {"description": "Title for error boundary widget"}, "errorBoundaryMessage": "We've encountered an unexpected error. You can try again or contact support if the problem persists.", "@errorBoundaryMessage": {"description": "Message for error boundary widget"}, "reportError": "Report Error", "@reportError": {"description": "Button text to report an error"}, "errorReported": "Error reported successfully. Thank you for helping us improve the app.", "@errorReported": {"description": "Success message after reporting an error"}}