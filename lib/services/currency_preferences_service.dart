import 'package:budapp/services/currency_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for managing user currency preferences
class CurrencyPreferencesService {
  CurrencyPreferencesService(this._prefs);
  static const String _currencyPreferenceKey = 'user_currency_preference';

  final SharedPreferences _prefs;

  /// Get the user's preferred currency code
  String getCurrentCurrency() {
    final currency = _prefs.getString(_currencyPreferenceKey);

    // Validate the stored currency code
    if (currency != null && CurrencyService.isValidCurrencyCode(currency)) {
      return currency;
    }

    // Return default if no valid preference found
    return CurrencyService.defaultCurrencyCode;
  }

  /// Set the user's preferred currency code
  Future<bool> setCurrency(String currencyCode) async {
    // Validate currency code before saving
    if (!CurrencyService.isValidCurrencyCode(currencyCode)) {
      throw ArgumentError('Invalid currency code: $currencyCode');
    }

    return _prefs.setString(_currencyPreferenceKey, currencyCode.toUpperCase());
  }

  /// Check if user has set a currency preference
  bool hasCurrencyPreference() {
    return _prefs.containsKey(_currencyPreferenceKey);
  }

  /// Reset currency preference to default
  Future<bool> resetCurrencyToDefault() async {
    return _prefs.remove(_currencyPreferenceKey);
  }

  /// Get currency symbol for current preference
  String getCurrentCurrencySymbol() {
    return CurrencyService.getCurrencySymbol(getCurrentCurrency());
  }

  /// Get currency display name for current preference
  String getCurrentCurrencyDisplayName() {
    return CurrencyService.getCurrencyDisplayName(getCurrentCurrency());
  }

  /// Format amount using current currency preference
  String formatAmount(int amountCents) {
    return CurrencyService.formatAmount(amountCents, getCurrentCurrency());
  }

  /// Format amount with sign using current currency preference
  String formatAmountWithSign(
    int amountCents, {
    bool showPositiveSign = false,
  }) {
    return CurrencyService.formatAmountWithSign(
      amountCents,
      getCurrentCurrency(),
      showPositiveSign: showPositiveSign,
    );
  }

  /// Check if current currency uses decimals
  bool currentCurrencyUsesDecimals() {
    return CurrencyService.currencyUsesDecimals(getCurrentCurrency());
  }

  /// Get decimal places for current currency
  int getCurrentCurrencyDecimalPlaces() {
    return CurrencyService.getCurrencyDecimalPlaces(getCurrentCurrency());
  }
}
