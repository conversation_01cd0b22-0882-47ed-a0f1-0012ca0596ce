import 'dart:async';

import 'package:budapp/data/models/session_state.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service class to handle comprehensive session management
///
/// This service provides:
/// - Enhanced session state management beyond Firebase Auth
/// - User preferences persistence
/// - Session analytics and monitoring
/// - Offline session handling
/// - Session restoration on app startup
class SessionService {
  /// Create SessionService with required AuthService dependency
  SessionService(this._authService);
  // AuthService instance (constructor injected)
  final AuthService _authService;

  // Stream controllers for session state management
  final StreamController<SessionInfo> _sessionController =
      StreamController<SessionInfo>.broadcast();

  // Current session state
  SessionInfo _currentSession = SessionInfo.loading();

  // Firebase Auth subscription
  StreamSubscription<User?>? _authSubscription;

  // Shared preferences instance
  SharedPreferences? _prefs;

  // Session analytics
  DateTime? _sessionStartTime;
  int _sessionCount = 0;

  /// Stream of session state changes
  Stream<SessionInfo> get sessionStream => _sessionController.stream;

  /// Current session information
  SessionInfo get currentSession => _currentSession;

  /// Whether the service is initialized
  bool get isInitialized => _prefs != null;

  /// Initialize the session service
  Future<void> initialize() async {
    try {
      debugPrint('SessionService: Initializing...');

      // Initialize shared preferences
      _prefs = await SharedPreferences.getInstance();

      // Load session analytics
      _sessionCount = _prefs!.getInt('session_count') ?? 0;

      // Start listening to auth state changes
      _startAuthStateListener();

      // Load user preferences and restore session
      await _restoreSession();

      debugPrint('SessionService: Initialized successfully');
    } on Exception catch (e) {
      debugPrint('SessionService: Initialization error: $e');
      _updateSession(
        SessionInfo.error(
          error: SessionError(
            code: 'initialization_failed',
            message: 'Failed to initialize session service',
            originalError: e,
          ),
        ),
      );
    }
  }

  /// Dispose the session service
  void dispose() {
    _authSubscription?.cancel();
    _sessionController.close();
    _endSession();
  }

  /// Start a new session
  Future<void> startSession({required User user, String? authProvider}) async {
    try {
      _sessionStartTime = DateTime.now();
      _sessionCount++;

      // Save session analytics
      await _prefs?.setInt('session_count', _sessionCount);
      await _prefs?.setString(
        'last_login_time',
        _sessionStartTime!.toIso8601String(),
      );
      await _prefs?.setString('last_auth_provider', authProvider ?? 'unknown');

      // Load user preferences
      final preferences = await _loadUserPreferences();

      // Determine session state based on email verification
      final sessionInfo = user.emailVerified
          ? SessionInfo.authenticated(
              user: user,
              lastLoginTime: _sessionStartTime,
              authProvider: authProvider,
              preferences: preferences,
            )
          : SessionInfo.emailNotVerified(user: user, preferences: preferences);

      _updateSession(sessionInfo);

      debugPrint('SessionService: Session started for user ${user.uid}');
    } on Exception catch (e) {
      debugPrint('SessionService: Error starting session: $e');
      _updateSession(
        SessionInfo.error(
          error: SessionError(
            code: 'session_start_failed',
            message: 'Failed to start user session',
            originalError: e,
          ),
          user: user,
        ),
      );
    }
  }

  /// End the current session
  Future<void> endSession() async {
    try {
      _endSession();
      _updateSession(SessionInfo.unauthenticated());
      debugPrint('SessionService: Session ended');
    } on Exception catch (e) {
      debugPrint('SessionService: Error ending session: $e');
    }
  }

  /// Update user preferences
  Future<void> updatePreferences(Map<String, dynamic> preferences) async {
    try {
      // Save preferences to local storage
      for (final entry in preferences.entries) {
        await _savePreference(entry.key, entry.value);
      }

      // Update current session with new preferences
      if (_currentSession.hasValidUser) {
        final updatedPreferences = Map<String, dynamic>.from(
          _currentSession.preferences,
        )..addAll(preferences);

        _updateSession(
          _currentSession.copyWith(preferences: updatedPreferences),
        );
      }

      debugPrint('SessionService: Preferences updated');
    } on Exception catch (e) {
      debugPrint('SessionService: Error updating preferences: $e');
    }
  }

  /// Get a specific preference value
  T? getPreference<T>(String key) {
    return _currentSession.preferences[key] as T?;
  }

  /// Get session analytics
  Map<String, dynamic> getSessionAnalytics() {
    final currentSessionDuration = _sessionStartTime != null
        ? DateTime.now().difference(_sessionStartTime!).inMinutes
        : 0;

    return {
      'session_count': _sessionCount,
      'current_session_duration_minutes': currentSessionDuration,
      'last_login_time': _prefs?.getString('last_login_time'),
      'last_auth_provider': _prefs?.getString('last_auth_provider'),
    };
  }

  /// Force refresh the current session
  Future<void> refreshSession() async {
    try {
      final user = _authService.currentUser;
      if (user != null) {
        await user.reload();
        final authProvider = _determineAuthProvider(user);
        await startSession(user: user, authProvider: authProvider);
      } else {
        await endSession();
      }
    } on Exception catch (e) {
      debugPrint('SessionService: Error refreshing session: $e');
      _updateSession(
        SessionInfo.error(
          error: SessionError(
            code: 'session_refresh_failed',
            message: 'Failed to refresh session',
            originalError: e,
          ),
        ),
      );
    }
  }

  // Private methods

  void _startAuthStateListener() {
    _authSubscription = _authService.authStateChanges.listen(
      (User? user) async {
        if (user != null) {
          final authProvider = _determineAuthProvider(user);
          await startSession(user: user, authProvider: authProvider);
        } else {
          await endSession();
        }
      },
      onError: (dynamic error) {
        debugPrint('SessionService: Auth state error: $error');
        _updateSession(
          SessionInfo.error(
            error: SessionError(
              code: 'auth_state_error',
              message: 'Authentication state error',
              originalError: error,
            ),
          ),
        );
      },
    );
  }

  Future<void> _restoreSession() async {
    try {
      // Check if there's a current user
      final user = _authService.currentUser;
      if (user != null) {
        final authProvider = _prefs?.getString('last_auth_provider');
        await startSession(user: user, authProvider: authProvider);
      } else {
        _updateSession(SessionInfo.unauthenticated());
      }
    } on Exception catch (e) {
      debugPrint('SessionService: Error restoring session: $e');
      _updateSession(
        SessionInfo.error(
          error: SessionError(
            code: 'session_restore_failed',
            message: 'Failed to restore session',
            originalError: e,
          ),
        ),
      );
    }
  }

  Future<Map<String, dynamic>> _loadUserPreferences() async {
    try {
      final preferences = <String, dynamic>{};

      // Load common preferences
      preferences['theme_mode'] = _prefs?.getString('theme_mode') ?? 'system';
      preferences['language'] = _prefs?.getString('language') ?? 'en';
      preferences['notifications_enabled'] =
          _prefs?.getBool('notifications_enabled') ?? true;
      preferences['biometric_enabled'] =
          _prefs?.getBool('biometric_enabled') ?? false;
      preferences['secure_mode'] = _prefs?.getBool('secure_mode') ?? false;

      return preferences;
    } on Exception catch (e) {
      debugPrint('SessionService: Error loading preferences: $e');
      return {};
    }
  }

  Future<void> _savePreference(String key, dynamic value) async {
    try {
      if (value is String) {
        await _prefs?.setString(key, value);
      } else if (value is bool) {
        await _prefs?.setBool(key, value);
      } else if (value is int) {
        await _prefs?.setInt(key, value);
      } else if (value is double) {
        await _prefs?.setDouble(key, value);
      } else {
        await _prefs?.setString(key, value.toString());
      }
    } on Exception catch (e) {
      debugPrint('SessionService: Error saving preference $key: $e');
    }
  }

  String _determineAuthProvider(User user) {
    final providers = user.providerData.map((info) => info.providerId).toList();
    if (providers.contains('google.com')) return 'google';
    if (providers.contains('apple.com')) return 'apple';
    if (providers.contains('password')) return 'email';
    return 'unknown';
  }

  void _updateSession(SessionInfo sessionInfo) {
    _currentSession = sessionInfo;
    _sessionController.add(sessionInfo);
  }

  void _endSession() {
    if (_sessionStartTime != null) {
      final duration = DateTime.now().difference(_sessionStartTime!);
      debugPrint(
        'SessionService: Session ended after ${duration.inMinutes} minutes',
      );
      _sessionStartTime = null;
    }
  }
}
