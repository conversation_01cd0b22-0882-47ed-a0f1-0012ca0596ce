import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Service class to handle Firestore operations and provide centralized access
///
/// This service provides:
/// - Centralized Firestore instance access
/// - Common Firestore operations (collections, batches, transactions)
/// - Connectivity testing
/// - Utility methods for common patterns
///
/// This service is designed to be injectable via Riverpod for better testability
/// and dependency management.
class FirestoreService {
  /// Create a FirestoreService with the provided Firestore instance
  FirestoreService(this._firestore);
  final FirebaseFirestore _firestore;

  /// Get the underlying Firestore instance
  ///
  /// This provides direct access to the Firestore instance for operations
  /// that are not covered by the service methods.
  FirebaseFirestore get instance => _firestore;

  /// Get a reference to a collection
  ///
  /// This is a convenience method that delegates to the underlying Firestore instance.
  CollectionReference<Map<String, dynamic>> collection(String path) {
    return _firestore.collection(path);
  }

  /// Get a reference to a document
  ///
  /// This is a convenience method that delegates to the underlying Firestore instance.
  DocumentReference<Map<String, dynamic>> doc(String path) {
    return _firestore.doc(path);
  }

  /// Create a new batch for atomic operations
  ///
  /// Use this for performing multiple operations atomically.
  WriteBatch batch() {
    return _firestore.batch();
  }

  /// Run a transaction
  ///
  /// This provides access to Firestore transactions for complex atomic operations.
  Future<T> runTransaction<T>(
    TransactionHandler<T> updateFunction, {
    Duration timeout = const Duration(seconds: 30),
  }) {
    return _firestore.runTransaction(updateFunction, timeout: timeout);
  }

  /// Test Firestore connectivity by performing a simple operation
  ///
  /// This method tests both read and write operations to ensure Firestore
  /// is accessible and functioning properly.
  Future<Map<String, dynamic>> testConnectivity() async {
    try {
      // Test Firestore connectivity
      final testDoc = _firestore.collection('_test').doc('connectivity');
      await testDoc.set({
        'timestamp': FieldValue.serverTimestamp(),
        'test': true,
      });

      final snapshot = await testDoc.get();
      await testDoc.delete(); // Clean up test document

      return {
        'status': 'connected',
        'canWrite': true,
        'canRead': snapshot.exists,
        'serverTimestamp': snapshot.data()?['timestamp'],
      };
    } on Exception catch (e) {
      debugPrint('Firestore connectivity test failed: $e');
      return {'status': 'error', 'error': e.toString()};
    }
  }

  /// Enable Firestore network access
  ///
  /// This method enables network access for Firestore, useful for
  /// re-enabling after offline mode.
  Future<void> enableNetwork() async {
    try {
      await _firestore.enableNetwork();
      debugPrint('Firestore network enabled successfully');
    } on Exception catch (e) {
      debugPrint('Failed to enable Firestore network: $e');
      rethrow;
    }
  }

  /// Disable Firestore network access
  ///
  /// This method disables network access for Firestore, putting it
  /// in offline mode.
  Future<void> disableNetwork() async {
    try {
      await _firestore.disableNetwork();
      debugPrint('Firestore network disabled successfully');
    } on Exception catch (e) {
      debugPrint('Failed to disable Firestore network: $e');
      rethrow;
    }
  }

  /// Clear Firestore persistence
  ///
  /// This method clears all cached documents from Firestore persistence.
  /// Note: This can only be called when Firestore is not in use.
  Future<void> clearPersistence() async {
    try {
      await _firestore.clearPersistence();
      debugPrint('Firestore persistence cleared successfully');
    } on Exception catch (e) {
      debugPrint('Failed to clear Firestore persistence: $e');
      rethrow;
    }
  }

  /// Terminate the Firestore instance
  ///
  /// This method terminates the Firestore instance and cleans up resources.
  /// After calling this method, the Firestore instance should not be used.
  Future<void> terminate() async {
    try {
      await _firestore.terminate();
      debugPrint('Firestore terminated successfully');
    } on Exception catch (e) {
      debugPrint('Failed to terminate Firestore: $e');
      rethrow;
    }
  }

  /// Wait for pending writes to complete
  ///
  /// This method waits for all pending writes to complete before returning.
  /// Useful for ensuring data consistency before performing certain operations.
  Future<void> waitForPendingWrites() async {
    try {
      await _firestore.waitForPendingWrites();
      debugPrint('All pending Firestore writes completed');
    } on Exception catch (e) {
      debugPrint('Failed to wait for pending writes: $e');
      rethrow;
    }
  }

  /// Get Firestore settings
  ///
  /// This method returns the current Firestore settings.
  Settings get settings => _firestore.settings;

  /// Configure Firestore settings
  ///
  /// This method allows configuration of Firestore settings.
  /// Note: Settings can only be configured before any Firestore operations.
  void configureSettings(Settings settings) {
    try {
      _firestore.settings = settings;
      debugPrint('Firestore settings configured successfully');
    } on Exception catch (e) {
      debugPrint('Failed to configure Firestore settings: $e');
      rethrow;
    }
  }
}
