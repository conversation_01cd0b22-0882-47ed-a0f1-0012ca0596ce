import 'package:budapp/config/environment_config.dart';
import 'package:budapp/utils/log_sanitizer.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

/// Production-safe logging service with environment-aware configuration
///
/// This service provides:
/// - Structured logging with proper log levels
/// - Environment-specific configuration (dev/staging/prod)
/// - PII sanitization for financial application compliance
/// - Production build safety with automatic log stripping
/// - Performance optimization for release builds
class LoggingService {
  /// Factory constructor for singleton instance
  factory LoggingService() {
    _instance ??= LoggingService._();
    return _instance!;
  }
  LoggingService._() {
    _logger = Logger(
      printer: _createPrinter(),
      level: _getLogLevel(),
      filter: _createFilter(),
    );
  }
  static LoggingService? _instance;
  late final Logger _logger;

  /// Get environment-specific log level
  Level _getLogLevel() {
    switch (EnvironmentConfig.flavor) {
      case 'dev':
        return Level.trace; // All log levels in development
      case 'staging':
        return Level.info; // INFO and above in staging
      case 'prod':
        return Level.warning; // WARNING and above in production
      default:
        return Level.info;
    }
  }

  /// Create environment-specific printer
  PrettyPrinter _createPrinter() {
    switch (EnvironmentConfig.flavor) {
      case 'dev':
        return PrettyPrinter(
          methodCount: 3,
          errorMethodCount: 8,
          lineLength: 80,
          colors: true,
          printEmojis: true,
          dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
        );
      case 'staging':
        return PrettyPrinter(
          methodCount: 1,
          errorMethodCount: 3,
          lineLength: 120,
          colors: false,
          printEmojis: false,
          dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
        );
      case 'prod':
        return PrettyPrinter(
          methodCount: 0,
          errorMethodCount: 1,
          lineLength: 120,
          colors: false,
          printEmojis: false,
          dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
        );
      default:
        return PrettyPrinter(
          methodCount: 1,
          errorMethodCount: 3,
          lineLength: 120,
          colors: false,
          printEmojis: false,
          dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
        );
    }
  }

  /// Create production-safe filter
  LogFilter _createFilter() {
    return ProductionFilter();
  }

  /// Log trace level message (development only)
  void trace(String message, {Object? error, StackTrace? stackTrace}) {
    if (kDebugMode) {
      _logger.t(
        _sanitizeMessage(message),
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  /// Log debug level message (development only)
  void debug(String message, {Object? error, StackTrace? stackTrace}) {
    if (kDebugMode) {
      _logger.d(
        _sanitizeMessage(message),
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  /// Log info level message
  void info(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.i(_sanitizeMessage(message), error: error, stackTrace: stackTrace);
  }

  /// Log warning level message
  void warning(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.w(_sanitizeMessage(message), error: error, stackTrace: stackTrace);
  }

  /// Log error level message
  void error(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.e(_sanitizeMessage(message), error: error, stackTrace: stackTrace);
  }

  /// Log fatal level message (critical errors)
  void fatal(String message, {Object? error, StackTrace? stackTrace}) {
    _logger.f(_sanitizeMessage(message), error: error, stackTrace: stackTrace);
  }

  /// Sanitize message to remove PII and sensitive data
  /// Critical for financial application compliance
  String _sanitizeMessage(String message) {
    return sanitize(message);
  }
}

/// Production-safe log filter that strips debug logs in release builds
class ProductionFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    // In release mode, only log warnings and errors
    if (kReleaseMode) {
      return event.level.value >= Level.warning.value;
    }

    // In debug mode, respect the logger's level configuration
    return event.level.value >= level!.value;
  }
}

/// Extension methods for convenient logging with context
extension LoggingExtensions on Object {
  /// Get class name safely for logging
  String _getClassName() {
    if (kDebugMode) {
      try {
        return toString().split(
          '(',
        )[0]; // Get class name without instance details
      } on Exception {
        return 'Object';
      }
    }
    return 'Object';
  }

  /// Log debug message with object context
  void logDebug(String message) {
    if (kDebugMode) {
      final className = _getClassName();
      LoggingService().debug('[$className] $message');
    }
  }

  /// Log info message with object context
  void logInfo(String message) {
    final className = _getClassName();
    LoggingService().info('[$className] $message');
  }

  /// Log warning message with object context
  void logWarning(String message) {
    final className = _getClassName();
    LoggingService().warning('[$className] $message');
  }

  /// Log error message with object context
  void logError(String message, {Object? error, StackTrace? stackTrace}) {
    final className = _getClassName();
    LoggingService().error(
      '[$className] $message',
      error: error,
      stackTrace: stackTrace,
    );
  }
}

/// Global logging instance for convenience
final log = LoggingService();
