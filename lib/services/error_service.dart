import 'dart:async';

import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/services/global_error_handler.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Generic error service for consistent user-facing error messages
///
/// This service provides standardized error handling and user-friendly
/// error messages across the entire application.
// ignore: avoid_classes_with_only_static_members
class ErrorService {
  /// Get user-friendly error message from any error object
  static String getUserFriendlyMessage(
    BuildContext context,
    Object error, {
    String? fallbackMessage,
  }) {
    final l10n = AppLocalizations.of(context)!;

    // Handle specific error types
    if (error is FirebaseAuthException) {
      return _getFirebaseAuthErrorMessage(l10n, error);
    }

    if (error is FirebaseException) {
      return _getFirebaseErrorMessage(l10n, error);
    }

    if (error is FormatException) {
      return _getFormatErrorMessage(l10n, error);
    }

    if (error is ArgumentError) {
      return _getArgumentErrorMessage(l10n, error);
    }

    // Handle common error patterns by message content
    final errorMessage = error.toString().toLowerCase();

    if (errorMessage.contains('network') ||
        errorMessage.contains('connection') ||
        errorMessage.contains('timeout')) {
      return l10n.networkError;
    }

    if (errorMessage.contains('permission') ||
        errorMessage.contains('unauthorized')) {
      return l10n.permissionError;
    }

    if (errorMessage.contains('not found') ||
        errorMessage.contains('does not exist')) {
      return l10n.notFoundError;
    }

    // Return fallback or generic error message
    return fallbackMessage ?? l10n.genericError;
  }

  /// Get error message for Firebase Auth exceptions
  static String _getFirebaseAuthErrorMessage(
    AppLocalizations l10n,
    FirebaseAuthException error,
  ) {
    switch (error.code) {
      case 'user-not-found':
        return l10n.userNotFoundError;
      case 'wrong-password':
        return l10n.wrongPasswordError;
      case 'email-already-in-use':
        return l10n.emailAlreadyInUseError;
      case 'weak-password':
        return l10n.weakPasswordError;
      case 'invalid-email':
        return l10n.invalidEmailError;
      case 'user-disabled':
        return l10n.userDisabledError;
      case 'too-many-requests':
        return l10n.tooManyRequestsError;
      case 'operation-not-allowed':
        return l10n.operationNotAllowedError;
      case 'requires-recent-login':
        return l10n.requiresRecentLoginError;
      default:
        return l10n.authenticationError;
    }
  }

  /// Get error message for Firebase exceptions
  static String _getFirebaseErrorMessage(
    AppLocalizations l10n,
    FirebaseException error,
  ) {
    switch (error.code) {
      case 'permission-denied':
        return l10n.permissionDeniedError;
      case 'unavailable':
        return l10n.serviceUnavailableError;
      case 'deadline-exceeded':
        return l10n.timeoutError;
      case 'not-found':
        return l10n.documentNotFoundError;
      case 'already-exists':
        return l10n.documentAlreadyExistsError;
      case 'resource-exhausted':
        return l10n.quotaExceededError;
      case 'failed-precondition':
        return l10n.preconditionFailedError;
      case 'aborted':
        return l10n.operationAbortedError;
      case 'out-of-range':
        return l10n.outOfRangeError;
      case 'unimplemented':
        return l10n.featureNotImplementedError;
      case 'internal':
        return l10n.internalError;
      case 'data-loss':
        return l10n.dataLossError;
      case 'unauthenticated':
        return l10n.unauthenticatedError;
      default:
        return l10n.databaseError;
    }
  }

  /// Get error message for format exceptions
  static String _getFormatErrorMessage(
    AppLocalizations l10n,
    FormatException error,
  ) {
    if (error.message.toLowerCase().contains('date')) {
      return l10n.invalidDateFormatError;
    }
    if (error.message.toLowerCase().contains('number')) {
      return l10n.invalidNumberFormatError;
    }
    return l10n.invalidFormatError;
  }

  /// Get error message for argument errors
  static String _getArgumentErrorMessage(
    AppLocalizations l10n,
    ArgumentError error,
  ) {
    return l10n.invalidArgumentError;
  }

  /// Show error snackbar with consistent styling
  static void showErrorSnackBar(
    BuildContext context,
    Object error, {
    String? fallbackMessage,
    Duration duration = const Duration(seconds: 4),
  }) {
    final message = getUserFriendlyMessage(
      context,
      error,
      fallbackMessage: fallbackMessage,
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.error_outline,
              color: Theme.of(context).colorScheme.onError,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: TextStyle(color: Theme.of(context).colorScheme.onError),
              ),
            ),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.error,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: AppLocalizations.of(context)!.dismiss,
          textColor: Theme.of(context).colorScheme.onError,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// Show success snackbar with consistent styling
  static void showSuccessSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.check_circle_outline,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        duration: duration,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show info snackbar with consistent styling
  static void showInfoSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
        duration: duration,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show error dialog with retry option
  static Future<bool> showErrorDialog(
    BuildContext context,
    Object error, {
    String? title,
    String? fallbackMessage,
    bool showRetryButton = false,
  }) async {
    final l10n = AppLocalizations.of(context)!;
    final message = getUserFriendlyMessage(
      context,
      error,
      fallbackMessage: fallbackMessage,
    );

    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(title ?? l10n.errorDialogTitle),
            content: Text(message),
            actions: [
              if (showRetryButton)
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: Text(l10n.retry),
                ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(l10n.ok),
              ),
            ],
          ),
        ) ??
        false;
  }

  /// Log error for debugging and crash reporting
  static Future<void> logError(
    Object error,
    StackTrace? stackTrace, {
    String? context,
    Map<String, dynamic>? additionalData,
    bool fatal = false,
  }) async {
    // Debug console output for development environments
    if (kDebugMode) {
      // ignore: avoid_print - Debug console output for development environments
      print('ERROR${context != null ? ' in $context' : ''}: $error');
      if (stackTrace != null) {
        // ignore: avoid_print - Debug console output for development environments
        print('STACK TRACE: $stackTrace');
      }
      if (additionalData != null) {
        // ignore: avoid_print - Debug console output for development environments
        print('ADDITIONAL DATA: $additionalData');
      }
    }

    // Report to global error handler for crash reporting
    try {
      final errorHandler = GlobalErrorHandler();
      if (errorHandler.isInitialized) {
        await errorHandler.reportError(
          error,
          stackTrace,
          reason: context != null ? 'Error in $context: $error' : null,
          customKeys: additionalData,
          fatal: fatal,
        );
      }
    } on Exception catch (reportingError) {
      // Fallback if crash reporting fails - don't let error reporting break the app
      if (kDebugMode) {
        // ignore: avoid_print - Debug console output for development environments
        print('Failed to report error to crash service: $reportingError');
      }
    }
  }

  /// Set user identifier for crash reports
  static Future<void> setUserContext(String? userId) async {
    try {
      final errorHandler = GlobalErrorHandler();
      if (errorHandler.isInitialized) {
        await errorHandler.setUserIdentifier(userId);
      }
    } on Exception catch (error) {
      if (kDebugMode) {
        // ignore: avoid_print - Debug console output for development environments
        print('Failed to set user context: $error');
      }
    }
  }

  /// Log breadcrumb for user action tracking
  static void logUserAction(String action, [Map<String, dynamic>? data]) {
    try {
      final errorHandler = GlobalErrorHandler();
      if (errorHandler.isInitialized) {
        final dataStr = data != null ? ' - $data' : '';
        errorHandler.logBreadcrumb('User action: $action$dataStr');
      }
    } on Exception catch (error) {
      if (kDebugMode) {
        // ignore: avoid_print - Debug console output for development environments
        print('Failed to log user action: $error');
      }
    }
  }

  /// Show error snackbar with crash reporting integration
  static void showErrorSnackBarWithLogging(
    BuildContext context,
    Object error, {
    String? fallbackMessage,
    String? errorContext,
    Map<String, dynamic>? additionalData,
    Duration duration = const Duration(seconds: 4),
  }) {
    // Log the error for crash reporting
    logError(
      error,
      null, // No stack trace available at UI level
      context: errorContext ?? 'UI Error',
      additionalData: additionalData,
      fatal: false,
    );

    // Show the error snackbar
    showErrorSnackBar(
      context,
      error,
      fallbackMessage: fallbackMessage,
      duration: duration,
    );
  }

  /// Show error dialog with crash reporting integration
  static Future<bool> showErrorDialogWithLogging(
    BuildContext context,
    Object error, {
    String? title,
    String? fallbackMessage,
    String? errorContext,
    Map<String, dynamic>? additionalData,
    bool showRetryButton = false,
  }) async {
    // Log the error for crash reporting
    unawaited(
      logError(
        error,
        null, // No stack trace available at UI level
        context: errorContext ?? 'UI Dialog Error',
        additionalData: additionalData,
        fatal: false,
      ),
    );

    // Show the error dialog
    return showErrorDialog(
      context,
      error,
      title: title,
      fallbackMessage: fallbackMessage,
      showRetryButton: showRetryButton,
    );
  }
}
