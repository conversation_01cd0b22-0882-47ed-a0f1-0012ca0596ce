import 'package:intl/intl.dart';

/// Service for handling currency formatting and operations
// ignore: avoid_classes_with_only_static_members
class CurrencyService {
  static const String _defaultCurrency = 'USD';

  // Currency code to symbol mapping
  static const Map<String, String> _currencySymbols = {
    'USD': r'$',
    'EUR': '€',
    'GBP': '£',
    'CAD': r'C$',
    'AUD': r'A$',
    'JPY': '¥',
    'CNY': '¥',
    'INR': '₹',
    'KRW': '₩',
    'BRL': r'R$',
    'RUB': '₽',
    'MXN': r'MX$',
    'CHF': 'Fr',
    'SEK': 'kr',
    'NOK': 'kr',
    'DKK': 'kr',
    'PLN': 'zł',
    'CZK': 'Kč',
    'HUF': 'Ft',
    'RON': 'lei',
    'BGN': 'лв',
    'HRK': 'kn',
    'RSD': 'дин',
    'TRY': '₺',
    'ILS': '₪',
    'AED': 'د.إ',
    'SAR': '﷼',
    'QAR': '﷼',
    'KWD': 'د.ك',
    'BHD': 'د.ب',
    'OMR': '﷼',
    'JOD': 'د.ا',
    'LBP': 'ل.ل',
    'EGP': 'ج.م',
    'MAD': 'د.م.',
    'DZD': 'د.ج',
    'TND': 'د.ت',
    'LYD': 'د.ل',
    'SYP': 'ل.س',
    'IQD': 'د.ع',
    'YER': '﷼',
    'AFN': '؋',
    'PKR': '₨',
    'LKR': '₨',
    'NPR': '₨',
    'BTN': 'Nu.',
    'BDT': '৳',
    'MMK': 'K',
    'THB': '฿',
    'LAK': '₭',
    'KHR': '៛',
    'VND': '₫',
    'IDR': 'Rp',
    'MYR': 'RM',
    'SGD': r'S$',
    'BND': r'B$',
    'PHP': '₱',
    'TWD': r'NT$',
    'HKD': r'HK$',
    'MOP': r'MOP$',
    'ZAR': 'R',
    'NAD': r'N$',
    'BWP': 'P',
    'SZL': 'E',
    'LSL': 'L',
    'MWK': 'MK',
    'ZMW': 'ZK',
    'AOA': 'Kz',
    'MZN': 'MT',
    'MGA': 'Ar',
    'KES': 'KSh',
    'UGX': 'USh',
    'TZS': 'TSh',
    'RWF': 'RWF',
    'BIF': 'FBu',
    'DJF': 'Fdj',
    'ERN': 'Nfk',
    'ETB': 'Br',
    'SOS': 'Sh',
    'SDG': 'ج.س.',
    'SSP': '£',
    'CDF': 'FC',
    'XAF': 'FCFA',
    'XOF': 'CFA',
    'GMD': 'D',
    'GHS': 'GH₵',
    'GNF': 'FG',
    'LRD': r'L$',
    'SLL': 'Le',
    'STD': 'Db',
    'CVE': r'$',
    'MRO': 'UM',
    'NIO': r'C$',
    'CRC': '₡',
    'GTQ': 'Q',
    'HNL': 'L',
    'SVC': '₡',
    'PAB': 'B/.',
    'JMD': r'J$',
    'HTG': 'G',
    'DOP': r'RD$',
    'CUP': r'$',
    'TTD': r'TT$',
    'BBD': r'Bds$',
    'BZD': r'BZ$',
    'GYD': r'G$',
    'SRD': r'Sr$',
    'UYU': r'$U',
    'PYG': '₲',
    'BOB': 'Bs.',
    'PEN': 'S/.',
    'COP': r'$',
    'VEF': 'Bs F',
    'CLP': r'$',
    'ARS': r'$',
    'FKP': '£',
    'SHP': '£',
    'GIP': '£',
    'JEP': '£',
    'GGP': '£',
    'IMP': '£',
    'WST': r'WS$',
    'SBD': r'SI$',
    'TOP': r'T$',
    'VUV': 'VT',
    'FJD': r'FJ$',
    'PGK': 'K',
    'NCL': 'F',
    'XPF': 'F',
    'NZD': r'NZ$',
    'CKI': r'$',
    'NUR': r'$',
    'TVD': r'$',
  };

  // Currency code to locale mapping for proper formatting
  static const Map<String, String> _currencyLocales = {
    'USD': 'en_US',
    'EUR': 'en_EU',
    'GBP': 'en_GB',
    'CAD': 'en_CA',
    'AUD': 'en_AU',
    'JPY': 'ja_JP',
    'CNY': 'zh_CN',
    'INR': 'en_IN',
    'KRW': 'ko_KR',
    'BRL': 'pt_BR',
    'RUB': 'ru_RU',
    'MXN': 'es_MX',
    'CHF': 'de_CH',
    'SEK': 'sv_SE',
    'NOK': 'nb_NO',
    'DKK': 'da_DK',
    'PLN': 'pl_PL',
    'CZK': 'cs_CZ',
    'HUF': 'hu_HU',
    'RON': 'ro_RO',
    'BGN': 'bg_BG',
    'HRK': 'hr_HR',
    'RSD': 'sr_RS',
    'TRY': 'tr_TR',
    'ILS': 'he_IL',
    'AED': 'ar_AE',
    'SAR': 'ar_SA',
    'QAR': 'ar_QA',
    'KWD': 'ar_KW',
    'BHD': 'ar_BH',
    'OMR': 'ar_OM',
    'JOD': 'ar_JO',
    'LBP': 'ar_LB',
    'EGP': 'ar_EG',
    'MAD': 'ar_MA',
    'DZD': 'ar_DZ',
    'TND': 'ar_TN',
    'LYD': 'ar_LY',
    'SYP': 'ar_SY',
    'IQD': 'ar_IQ',
    'YER': 'ar_YE',
    'AFN': 'fa_AF',
    'PKR': 'ur_PK',
    'LKR': 'si_LK',
    'NPR': 'ne_NP',
    'BTN': 'dz_BT',
    'BDT': 'bn_BD',
    'MMK': 'my_MM',
    'THB': 'th_TH',
    'LAK': 'lo_LA',
    'KHR': 'km_KH',
    'VND': 'vi_VN',
    'IDR': 'id_ID',
    'MYR': 'ms_MY',
    'SGD': 'en_SG',
    'BND': 'ms_BN',
    'PHP': 'en_PH',
    'TWD': 'zh_TW',
    'HKD': 'zh_HK',
    'MOP': 'zh_MO',
    'ZAR': 'en_ZA',
    'NAD': 'en_NA',
    'BWP': 'en_BW',
    'SZL': 'en_SZ',
    'LSL': 'en_LS',
    'MWK': 'en_MW',
    'ZMW': 'en_ZM',
    'AOA': 'pt_AO',
    'MZN': 'pt_MZ',
    'MGA': 'mg_MG',
    'KES': 'en_KE',
    'UGX': 'en_UG',
    'TZS': 'en_TZ',
    'RWF': 'rw_RW',
    'BIF': 'en_BI',
    'DJF': 'fr_DJ',
    'ERN': 'en_ER',
    'ETB': 'am_ET',
    'SOS': 'so_SO',
    'SDG': 'ar_SD',
    'SSP': 'en_SS',
    'CDF': 'fr_CD',
    'XAF': 'fr_CF',
    'XOF': 'fr_BF',
    'GMD': 'en_GM',
    'GHS': 'en_GH',
    'GNF': 'fr_GN',
    'LRD': 'en_LR',
    'SLL': 'en_SL',
    'STD': 'pt_ST',
    'CVE': 'pt_CV',
    'MRO': 'ar_MR',
    'NIO': 'es_NI',
    'CRC': 'es_CR',
    'GTQ': 'es_GT',
    'HNL': 'es_HN',
    'SVC': 'es_SV',
    'PAB': 'es_PA',
    'JMD': 'en_JM',
    'HTG': 'fr_HT',
    'DOP': 'es_DO',
    'CUP': 'es_CU',
    'TTD': 'en_TT',
    'BBD': 'en_BB',
    'BZD': 'en_BZ',
    'GYD': 'en_GY',
    'SRD': 'nl_SR',
    'UYU': 'es_UY',
    'PYG': 'es_PY',
    'BOB': 'es_BO',
    'PEN': 'es_PE',
    'COP': 'es_CO',
    'VEF': 'es_VE',
    'CLP': 'es_CL',
    'ARS': 'es_AR',
    'FKP': 'en_FK',
    'SHP': 'en_SH',
    'GIP': 'en_GI',
    'JEP': 'en_JE',
    'GGP': 'en_GG',
    'IMP': 'en_IM',
    'WST': 'en_WS',
    'SBD': 'en_SB',
    'TOP': 'en_TO',
    'VUV': 'en_VU',
    'FJD': 'en_FJ',
    'PGK': 'en_PG',
    'NCL': 'fr_NC',
    'XPF': 'fr_PF',
    'NZD': 'en_NZ',
    'CKI': 'en_CK',
    'NUR': 'en_NU',
    'TVD': 'en_TV',
  };

  /// Default supported currencies list
  static const List<String> defaultSupportedCurrencies = [
    'USD',
    'EUR',
    'GBP',
    'CAD',
    'AUD',
    'JPY',
    'CNY',
    'INR',
    'KRW',
    'BRL',
    'RUB',
    'MXN',
    'CHF',
    'SEK',
    'NOK',
    'DKK',
    'PLN',
    'CZK',
    'HUF',
    'RON',
  ];

  /// Get currency symbol for a given currency code
  static String getCurrencySymbol(String currencyCode) {
    return _currencySymbols[currencyCode.toUpperCase()] ?? currencyCode;
  }

  /// Get locale for a given currency code
  static String getCurrencyLocale(String currencyCode) {
    return _currencyLocales[currencyCode.toUpperCase()] ?? 'en_US';
  }

  /// Format amount in cents to currency string
  static String formatAmount(int amountCents, String currencyCode) {
    final amount = amountCents / 100.0;
    final symbol = getCurrencySymbol(currencyCode);
    final locale = getCurrencyLocale(currencyCode);

    try {
      final formatter = NumberFormat.currency(
        locale: locale,
        symbol: symbol,
        decimalDigits: 2,
      );
      return formatter.format(amount);
    } on Exception {
      // Fallback to simple formatting if locale-specific formatting fails
      return '$symbol${amount.toStringAsFixed(2)}';
    }
  }

  /// Format amount in cents to currency string with sign prefix
  static String formatAmountWithSign(
    int amountCents,
    String currencyCode, {
    bool showPositiveSign = false,
  }) {
    final formattedAmount = formatAmount(amountCents.abs(), currencyCode);

    if (amountCents < 0) {
      return '-$formattedAmount';
    } else if (amountCents > 0 && showPositiveSign) {
      return '+$formattedAmount';
    } else {
      return formattedAmount;
    }
  }

  /// Validate currency code
  static bool isValidCurrencyCode(String currencyCode) {
    return _currencySymbols.containsKey(currencyCode.toUpperCase());
  }

  /// Get display name for currency (e.g., "USD - US Dollar")
  static String getCurrencyDisplayName(String currencyCode) {
    final code = currencyCode.toUpperCase();
    final symbol = getCurrencySymbol(code);
    return '$code ($symbol)';
  }

  /// Get default currency code
  static String get defaultCurrencyCode => _defaultCurrency;

  /// Check if currency uses decimals (most do, except JPY, KRW, etc.)
  static bool currencyUsesDecimals(String currencyCode) {
    const nonDecimalCurrencies = [
      'JPY',
      'KRW',
      'VND',
      'CLP',
      'ISK',
      'PYG',
      'UGX',
      'VUV',
      'XAF',
      'XOF',
      'XPF',
    ];
    return !nonDecimalCurrencies.contains(currencyCode.toUpperCase());
  }

  /// Get decimal places for currency
  static int getCurrencyDecimalPlaces(String currencyCode) {
    return currencyUsesDecimals(currencyCode) ? 2 : 0;
  }
}
