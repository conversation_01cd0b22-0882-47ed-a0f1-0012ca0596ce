import 'dart:convert';

import 'package:budapp/data/models/remote_config_data.dart';
import 'package:budapp/services/logging_service.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';

/// Service class to handle Firebase Remote Config operations
///
/// This service provides:
/// - Centralized Remote Config instance access
/// - Configuration fetching and activation
/// - Default value management
/// - Error handling and fallback mechanisms
/// - Parameter parsing and type conversion
///
/// This service is designed to be injectable via Riverpod for better testability
/// and dependency management.
class RemoteConfigService {
  /// Create a RemoteConfigService with the provided Remote Config instance
  RemoteConfigService(this._remoteConfig);
  final FirebaseRemoteConfig _remoteConfig;
  final _log = LoggingService();

  /// Remote Config parameter keys
  static const String _predefinedIncomeCategoriesKey =
      'predefined_income_categories';
  static const String _predefinedExpenseCategoriesKey =
      'predefined_expense_categories';
  static const String _maxAccountsFreeKey = 'max_accounts_free';
  static const String _maxAccountsPremiumKey = 'max_accounts_premium';
  static const String _maxCustomCategoriesFreeKey =
      'max_custom_categories_free';
  static const String _maxCustomCategoriesPremiumKey =
      'max_custom_categories_premium';
  static const String _maxBudgetsFreeKey = 'max_budgets_free';
  static const String _maxBudgetsPremiumKey = 'max_budgets_premium';
  static const String _maxGoalsFreeKey = 'max_goals_free';
  static const String _maxGoalsPremiumKey = 'max_goals_premium';
  static const String _enableFeatureXKey = 'enable_feature_x';
  static const String _enableBetaFeaturesKey = 'enable_beta_features';

  /// Get the underlying Remote Config instance
  FirebaseRemoteConfig get instance => _remoteConfig;

  /// Initialize Remote Config with settings and default values
  Future<void> initialize() async {
    try {
      // Configure Remote Config settings
      await _remoteConfig.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: kDebugMode
              ? const Duration(minutes: 5) // Dev: 5 minutes for rapid iteration
              : const Duration(hours: 12), // Prod: 12 hours as per guidelines
        ),
      );

      // Set default values for offline/fallback scenarios
      await _setDefaultValues();

      _log.info('Remote Config initialized successfully');
    } on Exception catch (e) {
      _log.error('Failed to initialize Remote Config', error: e);
      rethrow;
    }
  }

  /// Set default values for all Remote Config parameters
  Future<void> _setDefaultValues() async {
    final defaults = PredefinedCategories.defaults();
    final premiumDefaults = PremiumLimits.defaults();

    await _remoteConfig.setDefaults({
      _predefinedIncomeCategoriesKey: jsonEncode(defaults.incomeCategories),
      _predefinedExpenseCategoriesKey: jsonEncode(defaults.expenseCategories),
      _maxAccountsFreeKey: premiumDefaults.maxAccountsFree,
      _maxAccountsPremiumKey: premiumDefaults.maxAccountsPremium,
      _maxCustomCategoriesFreeKey: premiumDefaults.maxCustomCategoriesFree,
      _maxCustomCategoriesPremiumKey:
          premiumDefaults.maxCustomCategoriesPremium,
      _maxBudgetsFreeKey: premiumDefaults.maxBudgetsFree,
      _maxBudgetsPremiumKey: premiumDefaults.maxBudgetsPremium,
      _maxGoalsFreeKey: premiumDefaults.maxGoalsFree,
      _maxGoalsPremiumKey: premiumDefaults.maxGoalsPremium,
      _enableFeatureXKey: false,
      _enableBetaFeaturesKey: false,
    });
  }

  /// Fetch and activate Remote Config values
  Future<bool> fetchAndActivate() async {
    try {
      final success = await _remoteConfig.fetchAndActivate();

      if (success) {
        _log.info('Remote Config fetched and activated successfully');
      } else {
        _log.info(
          'Remote Config fetch completed but no new values were activated',
        );
      }

      return success;
    } on Exception catch (e) {
      _log.error('Failed to fetch and activate Remote Config', error: e);
      // Don't rethrow - we want to continue with default values
      return false;
    }
  }

  /// Fetch Remote Config values (without activation)
  Future<void> fetch() async {
    try {
      await _remoteConfig.fetch();
      _log.info('Remote Config fetched successfully');
    } on Exception catch (e) {
      _log.error('Failed to fetch Remote Config', error: e);
      // Don't rethrow - we want to continue with default values
    }
  }

  /// Activate fetched Remote Config values
  Future<bool> activate() async {
    try {
      final success = await _remoteConfig.activate();

      if (success) {
        _log.info('Remote Config activated successfully');
      } else {
        _log.info(
          'Remote Config activation completed but no values were changed',
        );
      }

      return success;
    } on Exception catch (e) {
      _log.error('Failed to activate Remote Config', error: e);
      return false;
    }
  }

  /// Get the current Remote Config data
  RemoteConfigData getCurrentConfig() {
    try {
      return RemoteConfigData(
        categories: _getPredefinedCategories(),
        premiumLimits: _getPremiumLimits(),
        enableFeatureX: _remoteConfig.getBool(_enableFeatureXKey),
        enableBetaFeatures: _remoteConfig.getBool(_enableBetaFeaturesKey),
      );
    } on Exception catch (e) {
      _log.error('Failed to get current Remote Config data', error: e);
      // Return defaults on error
      return RemoteConfigData.defaults();
    }
  }

  /// Get predefined categories from Remote Config
  PredefinedCategories _getPredefinedCategories() {
    try {
      final incomeStr = _remoteConfig.getString(_predefinedIncomeCategoriesKey);
      final expenseStr = _remoteConfig.getString(
        _predefinedExpenseCategoriesKey,
      );

      final incomeCategories = incomeStr.isNotEmpty
          ? List<String>.from(jsonDecode(incomeStr) as Iterable<dynamic>)
          : PredefinedCategories.defaults().incomeCategories;

      final expenseCategories = expenseStr.isNotEmpty
          ? List<String>.from(jsonDecode(expenseStr) as Iterable<dynamic>)
          : PredefinedCategories.defaults().expenseCategories;

      return PredefinedCategories(
        incomeCategories: incomeCategories,
        expenseCategories: expenseCategories,
      );
    } on Exception catch (e) {
      _log.error('Failed to parse predefined categories', error: e);
      return PredefinedCategories.defaults();
    }
  }

  /// Get premium limits from Remote Config
  PremiumLimits _getPremiumLimits() {
    try {
      return PremiumLimits(
        maxAccountsFree: _remoteConfig.getInt(_maxAccountsFreeKey),
        maxAccountsPremium: _remoteConfig.getInt(_maxAccountsPremiumKey),
        maxCustomCategoriesFree: _remoteConfig.getInt(
          _maxCustomCategoriesFreeKey,
        ),
        maxCustomCategoriesPremium: _remoteConfig.getInt(
          _maxCustomCategoriesPremiumKey,
        ),
        maxBudgetsFree: _remoteConfig.getInt(_maxBudgetsFreeKey),
        maxBudgetsPremium: _remoteConfig.getInt(_maxBudgetsPremiumKey),
        maxGoalsFree: _remoteConfig.getInt(_maxGoalsFreeKey),
        maxGoalsPremium: _remoteConfig.getInt(_maxGoalsPremiumKey),
      );
    } on Exception catch (e) {
      _log.error('Failed to parse premium limits', error: e);
      return PremiumLimits.defaults();
    }
  }

  /// Get the last fetch status
  RemoteConfigFetchStatus get lastFetchStatus => _remoteConfig.lastFetchStatus;

  /// Get the last fetch time
  DateTime get lastFetchTime => _remoteConfig.lastFetchTime;

  /// Check if Remote Config values are stale
  bool get isStale {
    final now = DateTime.now();
    final fetchTime = lastFetchTime;
    const staleThreshold = kDebugMode
        ? Duration(minutes: 5)
        : Duration(hours: 12);

    return now.difference(fetchTime) > staleThreshold;
  }

  /// Enable real-time updates (not available on Web)
  Stream<RemoteConfigUpdate>? get onConfigUpdated {
    if (kIsWeb) {
      _log.warning('Real-time Remote Config updates not available on Web');
      return null;
    }
    return _remoteConfig.onConfigUpdated;
  }

  /// Get specific parameter value with type safety
  T getValue<T>(String key, {required T defaultValue}) {
    try {
      if (T == String) {
        return _remoteConfig.getString(key) as T;
      } else if (T == int) {
        return _remoteConfig.getInt(key) as T;
      } else if (T == double) {
        return _remoteConfig.getDouble(key) as T;
      } else if (T == bool) {
        return _remoteConfig.getBool(key) as T;
      } else {
        _log.warning('Unsupported type for Remote Config value: $T');
        return defaultValue;
      }
    } on Exception catch (e) {
      _log.error('Failed to get Remote Config value for key $key', error: e);
      return defaultValue;
    }
  }
}
