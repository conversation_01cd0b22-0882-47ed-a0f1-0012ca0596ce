import 'dart:async';

import 'package:budapp/services/cache_service.dart';
import 'package:budapp/services/logging_service.dart';
import 'package:budapp/services/performance_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Performance wrapper for repository operations implementing 2025 best practices
///
/// Provides:
/// - Automatic performance monitoring for Firestore operations
/// - Intelligent caching with cache-aside pattern
/// - Query optimization and batching
/// - Offline-first data access patterns
class RepositoryPerformanceWrapper {
  factory RepositoryPerformanceWrapper() => _instance;
  RepositoryPerformanceWrapper._internal();

  static final RepositoryPerformanceWrapper _instance =
      RepositoryPerformanceWrapper._internal();

  final CacheService _cache = CacheService();
  final LoggingService _log = LoggingService();

  /// Execute a Firestore query with performance monitoring and caching
  Future<T> executeQuery<T>({
    required String queryName,
    required Future<T> Function() queryFunction,
    String? cacheKey,
    Duration? cacheTtl,
    bool useCache = true,
  }) async {
    // Try cache first if enabled
    if (useCache && cacheKey != null) {
      final cachedResult = await _cache.get<T>(cacheKey);
      if (cachedResult != null) {
        _log.debug('Cache hit for query: $queryName');
        return cachedResult;
      }
    }

    // Execute query with performance monitoring
    late T result;
    await PerformanceService.trackFirestoreQuery(queryName, () async {
      result = await queryFunction();
    });

    // Cache the result if caching is enabled
    if (useCache && cacheKey != null) {
      await _cache.set(cacheKey, result, ttl: cacheTtl, persistToDisk: true);
    }

    return result;
  }

  /// Execute a batch operation with performance monitoring
  Future<void> executeBatch({
    required String operationName,
    required List<Future<void> Function(WriteBatch)> operations,
    required FirebaseFirestore firestore,
  }) async {
    await PerformanceService.trackFirestoreQuery(
      'batch_$operationName',
      () async {
        final batch = firestore.batch();

        for (final operation in operations) {
          await operation(batch);
        }

        await batch.commit();
      },
    );
  }

  /// Execute a transaction with performance monitoring
  Future<T> executeTransaction<T>({
    required String transactionName,
    required Future<T> Function(Transaction) transactionFunction,
    required FirebaseFirestore firestore,
  }) async {
    late T result;
    await PerformanceService.trackFirestoreQuery(
      'transaction_$transactionName',
      () async {
        result = await firestore.runTransaction(transactionFunction);
      },
    );
    return result;
  }

  /// Get paginated data with cursor-based pagination (2025 best practice)
  Future<PaginatedResult<T>> getPaginatedData<T>({
    required String collectionPath,
    required T Function(Map<String, dynamic>) fromJson,
    required String queryName,
    DocumentSnapshot? startAfter,
    int limit = 20,
    List<QueryFilter>? filters,
    List<OrderBy>? orderBy,
    String? cacheKeyPrefix,
  }) async {
    final cacheKey = cacheKeyPrefix != null
        ? '${cacheKeyPrefix}_page_${startAfter?.id ?? 'first'}_$limit'
        : null;

    return executeQuery<PaginatedResult<T>>(
      queryName: 'paginated_$queryName',
      cacheKey: cacheKey,
      cacheTtl: const Duration(minutes: 5), // Shorter TTL for paginated data
      queryFunction: () async {
        Query query = FirebaseFirestore.instance.collection(collectionPath);

        // Apply filters
        if (filters != null) {
          for (final filter in filters) {
            query = query.where(filter.field, isEqualTo: filter.value);
          }
        }

        // Apply ordering
        if (orderBy != null) {
          for (final order in orderBy) {
            query = query.orderBy(order.field, descending: order.descending);
          }
        }

        // Apply pagination
        if (startAfter != null) {
          query = query.startAfterDocument(startAfter);
        }
        query = query.limit(limit);

        final snapshot = await query.get();
        final items = snapshot.docs
            .map(
              (doc) => fromJson({
                'id': doc.id,
                ...doc.data()! as Map<String, dynamic>,
              }),
            )
            .toList();

        return PaginatedResult<T>(
          items: items,
          lastDocument: snapshot.docs.isNotEmpty ? snapshot.docs.last : null,
          hasMore: snapshot.docs.length == limit,
        );
      },
    );
  }

  /// Preload data for offline access
  Future<void> preloadData({
    required String dataType,
    required Future<void> Function() preloadFunction,
  }) async {
    await PerformanceService.trackFirestoreQuery(
      'preload_$dataType',
      preloadFunction,
    );
  }

  /// Invalidate cache for specific patterns
  Future<void> invalidateCache(String pattern) async {
    // This would need to be implemented based on cache key patterns
    // For now, we'll clear all cache as a simple implementation
    await _cache.clear();
    _log.info('Cache invalidated for pattern: $pattern');
  }

  /// Get performance statistics
  Map<String, dynamic> getPerformanceStats() {
    return {
      'cache_stats': _cache.getStats(),
      'performance_thresholds': {
        'app_startup_ms': PerformanceService.appStartupThreshold.inMilliseconds,
        'screen_transition_ms':
            PerformanceService.screenTransitionThreshold.inMilliseconds,
        'firestore_query_ms':
            PerformanceService.firestoreQueryThreshold.inMilliseconds,
      },
    };
  }
}

/// Result class for paginated queries
class PaginatedResult<T> {
  const PaginatedResult({
    required this.items,
    this.lastDocument,
    required this.hasMore,
  });
  final List<T> items;
  final DocumentSnapshot? lastDocument;
  final bool hasMore;
}

/// Query filter class
class QueryFilter {
  const QueryFilter({
    required this.field,
    required this.value,
    this.operator = '==',
  });
  final String field;
  final dynamic value;
  final String operator;
}

/// Order by class
class OrderBy {
  const OrderBy({required this.field, this.descending = false});
  final String field;
  final bool descending;
}
