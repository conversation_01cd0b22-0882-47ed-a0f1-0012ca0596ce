import 'dart:async';
import 'dart:convert';

import 'package:budapp/services/logging_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Advanced caching service implementing 2025 best practices
///
/// Provides multi-level caching with:
/// - In-memory cache for frequently accessed data
/// - Persistent cache for offline access
/// - Intelligent cache invalidation
/// - Performance monitoring integration
class CacheService {
  factory CacheService() => _instance;
  CacheService._internal();
  static final CacheService _instance = CacheService._internal();

  final LoggingService _log = LoggingService();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  // In-memory cache with expiration
  final Map<String, _CacheEntry> _memoryCache = {};
  final Map<String, Timer> _expirationTimers = {};

  // Cache configuration
  static const Duration _defaultTtl = Duration(minutes: 15);
  static const int _maxMemoryCacheSize = 100;
  static const String _cachePrefix = 'budapp_cache_';

  /// Initialize the cache service
  Future<void> initialize() async {
    try {
      _log.info('Cache service initialized with 2025 optimizations');
    } on Exception catch (e) {
      _log.error('Failed to initialize cache service', error: e);
    }
  }

  /// Store data in cache with optional TTL
  Future<void> set<T>(
    String key,
    T data, {
    Duration? ttl,
    bool persistToDisk = false,
  }) async {
    try {
      final effectiveTtl = ttl ?? _defaultTtl;
      final serializedData = jsonEncode(data);

      // Store in memory cache
      _setMemoryCache(key, serializedData, effectiveTtl);

      // Optionally persist to disk for offline access
      if (persistToDisk) {
        await _setPersistentCache(key, serializedData, effectiveTtl);
      }

      _log.debug('Cached data for key: $key (TTL: ${effectiveTtl.inMinutes}m)');
    } on Exception catch (e) {
      _log.error('Failed to cache data for key: $key', error: e);
    }
  }

  /// Retrieve data from cache
  Future<T?> get<T>(String key) async {
    try {
      // Try memory cache first
      final memoryData = _getMemoryCache(key);
      if (memoryData != null) {
        _log.debug('Cache hit (memory): $key');
        return jsonDecode(memoryData) as T?;
      }

      // Try persistent cache
      final persistentData = await _getPersistentCache(key);
      if (persistentData != null) {
        _log.debug('Cache hit (persistent): $key');
        // Promote to memory cache
        _setMemoryCache(key, persistentData, _defaultTtl);
        return jsonDecode(persistentData) as T?;
      }

      _log.debug('Cache miss: $key');
      return null;
    } on Exception catch (e) {
      _log.error('Failed to retrieve cached data for key: $key', error: e);
      return null;
    }
  }

  /// Check if key exists in cache
  Future<bool> has(String key) async {
    return _memoryCache.containsKey(key) || await _hasPersistentCache(key);
  }

  /// Remove specific key from cache
  Future<void> remove(String key) async {
    try {
      // Remove from memory cache
      _memoryCache.remove(key);
      _expirationTimers[key]?.cancel();
      _expirationTimers.remove(key);

      // Remove from persistent cache
      await _removePersistentCache(key);

      _log.debug('Removed cached data for key: $key');
    } on Exception catch (e) {
      _log.error('Failed to remove cached data for key: $key', error: e);
    }
  }

  /// Clear all cached data
  Future<void> clear() async {
    try {
      // Clear memory cache
      _memoryCache.clear();
      for (final timer in _expirationTimers.values) {
        timer.cancel();
      }
      _expirationTimers.clear();

      // Clear persistent cache
      await _clearPersistentCache();

      _log.info('Cleared all cached data');
    } on Exception catch (e) {
      _log.error('Failed to clear cache', error: e);
    }
  }

  /// Get cache statistics
  Map<String, dynamic> getStats() {
    return {
      'memory_cache_size': _memoryCache.length,
      'max_memory_cache_size': _maxMemoryCacheSize,
      'active_timers': _expirationTimers.length,
      'default_ttl_minutes': _defaultTtl.inMinutes,
    };
  }

  // Private methods for memory cache management
  void _setMemoryCache(String key, String data, Duration ttl) {
    // Implement LRU eviction if cache is full
    if (_memoryCache.length >= _maxMemoryCacheSize) {
      _evictOldestEntry();
    }

    final entry = _CacheEntry(data, DateTime.now().add(ttl));
    _memoryCache[key] = entry;

    // Set expiration timer
    _expirationTimers[key]?.cancel();
    _expirationTimers[key] = Timer(ttl, () {
      _memoryCache.remove(key);
      _expirationTimers.remove(key);
    });
  }

  String? _getMemoryCache(String key) {
    final entry = _memoryCache[key];
    if (entry == null) return null;

    if (entry.isExpired) {
      _memoryCache.remove(key);
      _expirationTimers[key]?.cancel();
      _expirationTimers.remove(key);
      return null;
    }

    return entry.data;
  }

  void _evictOldestEntry() {
    if (_memoryCache.isEmpty) return;

    String? oldestKey;
    DateTime? oldestTime;

    for (final entry in _memoryCache.entries) {
      if (oldestTime == null || entry.value.createdAt.isBefore(oldestTime)) {
        oldestTime = entry.value.createdAt;
        oldestKey = entry.key;
      }
    }

    if (oldestKey != null) {
      _memoryCache.remove(oldestKey);
      _expirationTimers[oldestKey]?.cancel();
      _expirationTimers.remove(oldestKey);
    }
  }

  // Private methods for persistent cache management
  Future<void> _setPersistentCache(
    String key,
    String data,
    Duration ttl,
  ) async {
    final cacheKey = '$_cachePrefix$key';
    final expirationTime = DateTime.now().add(ttl).millisecondsSinceEpoch;
    final cacheData = {'data': data, 'expires_at': expirationTime};

    await _secureStorage.write(key: cacheKey, value: jsonEncode(cacheData));
  }

  Future<String?> _getPersistentCache(String key) async {
    final cacheKey = '$_cachePrefix$key';
    final cachedValue = await _secureStorage.read(key: cacheKey);

    if (cachedValue == null) return null;

    try {
      final cacheData = jsonDecode(cachedValue) as Map<String, dynamic>;
      final expiresAt = cacheData['expires_at'] as int;

      if (DateTime.now().millisecondsSinceEpoch > expiresAt) {
        // Expired, remove it
        await _secureStorage.delete(key: cacheKey);
        return null;
      }

      return cacheData['data'] as String;
    } on Exception {
      // Invalid cache data, remove it
      await _secureStorage.delete(key: cacheKey);
      return null;
    }
  }

  Future<bool> _hasPersistentCache(String key) async {
    final cacheKey = '$_cachePrefix$key';
    return _secureStorage.containsKey(key: cacheKey);
  }

  Future<void> _removePersistentCache(String key) async {
    final cacheKey = '$_cachePrefix$key';
    await _secureStorage.delete(key: cacheKey);
  }

  Future<void> _clearPersistentCache() async {
    final allKeys = await _secureStorage.readAll();
    for (final key in allKeys.keys) {
      if (key.startsWith(_cachePrefix)) {
        await _secureStorage.delete(key: key);
      }
    }
  }
}

/// Internal cache entry class
class _CacheEntry {
  _CacheEntry(this.data, this.expiresAt) : createdAt = DateTime.now();
  final String data;
  final DateTime expiresAt;
  final DateTime createdAt;

  bool get isExpired => DateTime.now().isAfter(expiresAt);
}
