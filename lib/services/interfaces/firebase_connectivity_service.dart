/// Interface for Firebase connectivity testing service
///
/// This service provides methods to test connectivity and status of various Firebase services
/// including Authentication and Firestore. It abstracts Firebase connectivity testing
/// from the presentation layer, following the Repository pattern principles.
abstract class IFirebaseConnectivityService {
  /// Test Firebase connectivity and return status information
  ///
  /// Returns a Map containing status information for:
  /// - 'auth': Firebase Authentication status and user info
  /// - 'firestore': Firestore connectivity and read/write test results
  ///
  /// Each service entry contains:
  /// - 'status': 'connected', 'not_authenticated', or 'error'
  /// - Additional service-specific information
  Future<Map<String, dynamic>> testFirebaseConnectivity();

  /// Test Firebase Authentication connectivity
  ///
  /// Returns a Map with:
  /// - 'status': 'connected', 'not_authenticated', or 'error'
  /// - 'uid': User ID if authenticated
  /// - 'email': User email if available
  /// - 'error': Error message if status is 'error'
  Future<Map<String, dynamic>> testAuthConnectivity();

  /// Test Firestore connectivity
  ///
  /// Performs a write and read operation to test Firestore connectivity.
  /// Returns a Map with:
  /// - 'status': 'connected' or 'error'
  /// - 'hasData': Boolean indicating if test data was successfully written/read
  /// - 'timestamp': ISO string of test timestamp
  /// - 'error': Error message if status is 'error'
  Future<Map<String, dynamic>> testFirestoreConnectivity();

  /// Check if Firebase services are currently available
  ///
  /// Returns true if both Auth and Firestore are available, false otherwise.
  /// This is a quick check that doesn't perform write operations.
  Future<bool> areFirebaseServicesAvailable();
}
