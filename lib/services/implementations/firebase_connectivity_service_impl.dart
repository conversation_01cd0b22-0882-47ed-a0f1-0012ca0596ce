import 'package:budapp/services/interfaces/firebase_connectivity_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Concrete implementation of IFirebaseConnectivityService
///
/// This service provides Firebase connectivity testing functionality by
/// abstracting direct Firebase access from the presentation layer.
/// It follows dependency injection principles and the Repository pattern.
class FirebaseConnectivityServiceImpl implements IFirebaseConnectivityService {
  FirebaseConnectivityServiceImpl({
    required FirebaseAuth firebaseAuth,
    required FirebaseFirestore firestore,
  }) : _firebaseAuth = firebaseAuth,
       _firestore = firestore;
  final FirebaseAuth _firebaseAuth;
  final FirebaseFirestore _firestore;

  @override
  Future<Map<String, dynamic>> testFirebaseConnectivity() async {
    final results = <String, dynamic>{};

    // Test Firebase Auth
    results['auth'] = await testAuthConnectivity();

    // Test Firestore
    results['firestore'] = await testFirestoreConnectivity();

    return results;
  }

  @override
  Future<Map<String, dynamic>> testAuthConnectivity() async {
    try {
      final user = _firebaseAuth.currentUser;
      return {
        'status': user != null ? 'connected' : 'not_authenticated',
        'uid': user?.uid,
        'email': user?.email,
      };
    } on Exception catch (e) {
      return {'status': 'error', 'error': e.toString()};
    }
  }

  @override
  Future<Map<String, dynamic>> testFirestoreConnectivity() async {
    try {
      final testDoc = _firestore.collection('test').doc('connectivity');

      // Test write operation
      await testDoc.set({'timestamp': FieldValue.serverTimestamp()});

      // Test read operation
      final snapshot = await testDoc.get();

      return {
        'status': 'connected',
        'hasData': snapshot.exists,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } on Exception catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'hasData': false,
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  @override
  Future<bool> areFirebaseServicesAvailable() async {
    try {
      // Quick connectivity check without write operations
      final authResult = await testAuthConnectivity();
      final authAvailable = authResult['status'] != 'error';

      // Test Firestore read-only operation
      final testDoc = _firestore.collection('test').doc('connectivity');
      await testDoc.get();

      return authAvailable; // Firestore read succeeded if we reach here
    } on Exception {
      return false;
    }
  }
}
