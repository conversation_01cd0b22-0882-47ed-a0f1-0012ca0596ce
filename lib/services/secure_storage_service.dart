import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Service for secure storage of sensitive data
///
/// This service provides secure storage capabilities for sensitive information
/// such as:
/// - Biometric authentication keys
/// - API keys for third-party services
/// - User PINs or passwords
/// - Refresh tokens (if implemented outside Firebase handling)
/// - Any other sensitive user data that requires encryption
///
/// The service wraps flutter_secure_storage with:
/// - Proper error handling
/// - Consistent logging
/// - Future-ready interface for sensitive data
class SecureStorageService {
  /// Creates a SecureStorageService instance
  ///
  /// [storage] The FlutterSecureStorage instance to use.
  /// If not provided, creates a default instance with secure options.
  SecureStorageService({FlutterSecureStorage? storage})
    : _storage = storage ?? _createDefaultStorage();
  static const String _keyPrefix = 'budapp_';

  final FlutterSecureStorage _storage;

  /// Creates a default secure storage instance with secure options
  static FlutterSecureStorage _createDefaultStorage() {
    const androidOptions = AndroidOptions(
      encryptedSharedPreferences: true,
      resetOnError: true,
      sharedPreferencesName: 'budapp_secure_prefs',
      preferencesKeyPrefix: _keyPrefix,
    );

    const iosOptions = IOSOptions(
      groupId: 'group.com.digitau.budapp',
      accountName: 'BudApp',
      accessibility: KeychainAccessibility.first_unlock_this_device,
    );

    const linuxOptions = LinuxOptions.defaultOptions;

    const windowsOptions = WindowsOptions.defaultOptions;

    const macOsOptions = MacOsOptions(
      groupId: 'group.com.digitau.budapp',
      accountName: 'BudApp',
      accessibility: KeychainAccessibility.first_unlock_this_device,
    );

    const webOptions = WebOptions(
      dbName: 'budapp_secure_storage',
      publicKey: 'budapp_public_key',
    );

    return const FlutterSecureStorage(
      aOptions: androidOptions,
      iOptions: iosOptions,
      lOptions: linuxOptions,
      wOptions: windowsOptions,
      mOptions: macOsOptions,
      webOptions: webOptions,
    );
  }

  /// Stores a value securely
  ///
  /// [key] The key to store the value under
  /// [value] The value to store securely
  ///
  /// Throws [SecureStorageException] if the operation fails
  Future<void> write(String key, String value) async {
    try {
      final prefixedKey = _keyPrefix + key;
      await _storage.write(key: prefixedKey, value: value);
      debugPrint('SecureStorageService: Successfully wrote key: $key');
    } on Exception catch (e) {
      debugPrint('SecureStorageService: Error writing key $key: $e');
      throw SecureStorageException(
        'Failed to write secure data',
        key: key,
        originalError: e,
      );
    }
  }

  /// Reads a value securely
  ///
  /// [key] The key to read the value from
  ///
  /// Returns the stored value or null if not found
  /// Throws [SecureStorageException] if the operation fails
  Future<String?> read(String key) async {
    try {
      final prefixedKey = _keyPrefix + key;
      final value = await _storage.read(key: prefixedKey);
      debugPrint('SecureStorageService: Successfully read key: $key');
      return value;
    } on Exception catch (e) {
      debugPrint('SecureStorageService: Error reading key $key: $e');
      throw SecureStorageException(
        'Failed to read secure data',
        key: key,
        originalError: e,
      );
    }
  }

  /// Deletes a value securely
  ///
  /// [key] The key to delete
  ///
  /// Throws [SecureStorageException] if the operation fails
  Future<void> delete(String key) async {
    try {
      final prefixedKey = _keyPrefix + key;
      await _storage.delete(key: prefixedKey);
      debugPrint('SecureStorageService: Successfully deleted key: $key');
    } on Exception catch (e) {
      debugPrint('SecureStorageService: Error deleting key $key: $e');
      throw SecureStorageException(
        'Failed to delete secure data',
        key: key,
        originalError: e,
      );
    }
  }

  /// Checks if a key exists
  ///
  /// [key] The key to check
  ///
  /// Returns true if the key exists, false otherwise
  /// Throws [SecureStorageException] if the operation fails
  Future<bool> containsKey(String key) async {
    try {
      final prefixedKey = _keyPrefix + key;
      final keys = await _storage.readAll();
      final exists = keys.containsKey(prefixedKey);
      debugPrint('SecureStorageService: Key $key exists: $exists');
      return exists;
    } on Exception catch (e) {
      debugPrint('SecureStorageService: Error checking key $key: $e');
      throw SecureStorageException(
        'Failed to check secure data',
        key: key,
        originalError: e,
      );
    }
  }

  /// Gets all keys (without the prefix)
  ///
  /// Returns a list of all keys stored in secure storage
  /// Throws [SecureStorageException] if the operation fails
  Future<List<String>> getAllKeys() async {
    try {
      final allKeys = await _storage.readAll();
      final keys = allKeys.keys
          .where((key) => key.startsWith(_keyPrefix))
          .map((key) => key.substring(_keyPrefix.length))
          .toList();
      debugPrint('SecureStorageService: Found ${keys.length} keys');
      return keys;
    } on Exception catch (e) {
      debugPrint('SecureStorageService: Error getting all keys: $e');
      throw SecureStorageException(
        'Failed to get all secure keys',
        originalError: e,
      );
    }
  }

  /// Clears all stored data
  ///
  /// ⚠️ WARNING: This will delete ALL secure data for the app
  /// Use with extreme caution, typically only for logout or data reset
  ///
  /// Throws [SecureStorageException] if the operation fails
  Future<void> deleteAll() async {
    try {
      await _storage.deleteAll();
      debugPrint('SecureStorageService: Successfully cleared all secure data');
    } on Exception catch (e) {
      debugPrint('SecureStorageService: Error clearing all data: $e');
      throw SecureStorageException(
        'Failed to clear all secure data',
        originalError: e,
      );
    }
  }

  // Future methods for planned sensitive data types:

  /// Stores biometric authentication key
  ///
  /// This method is ready for future biometric authentication implementation
  Future<void> storeBiometricKey(String key) async {
    await write('biometric_key', key);
  }

  /// Retrieves biometric authentication key
  ///
  /// This method is ready for future biometric authentication implementation
  Future<String?> getBiometricKey() async {
    return read('biometric_key');
  }

  /// Stores API key for third-party services
  ///
  /// [serviceName] The name of the service (e.g., 'plaid', 'stripe')
  /// [apiKey] The API key to store securely
  Future<void> storeApiKey(String serviceName, String apiKey) async {
    await write('api_key_$serviceName', apiKey);
  }

  /// Retrieves API key for third-party services
  ///
  /// [serviceName] The name of the service (e.g., 'plaid', 'stripe')
  Future<String?> getApiKey(String serviceName) async {
    return read('api_key_$serviceName');
  }

  /// Stores user PIN (for future PIN authentication feature)
  ///
  /// ⚠️ Note: PINs should be hashed before storage
  Future<void> storeUserPin(String hashedPin) async {
    await write('user_pin_hash', hashedPin);
  }

  /// Retrieves user PIN hash (for future PIN authentication feature)
  Future<String?> getUserPinHash() async {
    return read('user_pin_hash');
  }

  /// Stores biometric authentication preference
  Future<void> setBiometricEnabled({required bool enabled}) async {
    await write('biometric_enabled', enabled.toString());
  }

  /// Retrieves biometric authentication preference
  Future<bool> getBiometricEnabled() async {
    final value = await read('biometric_enabled');
    return value?.toLowerCase() == 'true';
  }

  /// Clears biometric authentication preferences
  Future<void> clearBiometricPreferences() async {
    await delete('biometric_enabled');
    await delete('biometric_key');
  }
}

/// Exception thrown by SecureStorageService operations
class SecureStorageException implements Exception {
  const SecureStorageException(this.message, {this.key, this.originalError});
  final String message;
  final String? key;
  final Object? originalError;

  @override
  String toString() {
    final keyInfo = key != null ? ' (key: $key)' : '';
    final errorInfo = originalError != null ? ' - $originalError' : '';
    return 'SecureStorageException: $message$keyInfo$errorInfo';
  }
}
