import 'package:budapp/services/logging_service.dart';
import 'package:flutter/foundation.dart';

/// Performance monitoring service for Firebase query optimization validation
///
/// Tracks repository performance metrics and validates optimization improvements:
/// - Query count reduction (N+1 pattern elimination)
/// - Cache hit rates and efficiency
/// - Response time improvements
/// - Memory usage optimization
class PerformanceMonitoringService {
  factory PerformanceMonitoringService() => _instance;
  PerformanceMonitoringService._internal();
  static final PerformanceMonitoringService _instance =
      PerformanceMonitoringService._internal();

  final LoggingService _log = LoggingService();
  final Map<String, List<int>> _responseTimes = {};
  final Map<String, int> _queryCounts = {};
  final Map<String, int> _cacheHits = {};
  final Map<String, int> _cacheMisses = {};
  final Map<String, DateTime> _operationStartTimes = {};

  /// Start tracking an operation
  void startOperation(String operationName) {
    _operationStartTimes[operationName] = DateTime.now();
  }

  /// End tracking an operation and record performance metrics
  void endOperation(String operationName, {Map<String, dynamic>? metadata}) {
    final startTime = _operationStartTimes.remove(operationName);
    if (startTime == null) return;

    final duration = DateTime.now().difference(startTime).inMilliseconds;

    _responseTimes.putIfAbsent(operationName, () => []).add(duration);

    if (kDebugMode) {
      _log.debug('Operation completed: $operationName in ${duration}ms');
    }
  }

  /// Record a database query
  void recordQuery(String queryType) {
    _queryCounts[queryType] = (_queryCounts[queryType] ?? 0) + 1;
  }

  /// Record a cache hit
  void recordCacheHit(String cacheKey) {
    _cacheHits[cacheKey] = (_cacheHits[cacheKey] ?? 0) + 1;
  }

  /// Record a cache miss
  void recordCacheMiss(String cacheKey) {
    _cacheMisses[cacheKey] = (_cacheMisses[cacheKey] ?? 0) + 1;
  }

  /// Get performance statistics
  Map<String, dynamic> getPerformanceStats() {
    final stats = <String, dynamic>{
      'operations': <String, dynamic>{},
      'queries': Map<String, int>.from(_queryCounts),
      'cache': <String, dynamic>{
        'hits': Map<String, int>.from(_cacheHits),
        'misses': Map<String, int>.from(_cacheMisses),
        'hit_rates': <String, double>{},
      },
    };

    // Calculate response time statistics
    for (final entry in _responseTimes.entries) {
      final times = entry.value;
      if (times.isNotEmpty) {
        times.sort();
        (stats['operations'] as Map<String, dynamic>)[entry.key] = {
          'count': times.length,
          'avg_ms': times.reduce((a, b) => a + b) / times.length,
          'min_ms': times.first,
          'max_ms': times.last,
          'p50_ms': _percentile(times, 0.5),
          'p95_ms': _percentile(times, 0.95),
          'p99_ms': _percentile(times, 0.99),
        };
      }
    }

    // Calculate cache hit rates
    final cacheStats = stats['cache'] as Map<String, dynamic>;
    final hitRates = cacheStats['hit_rates'] as Map<String, double>;

    for (final key in {..._cacheHits.keys, ..._cacheMisses.keys}) {
      final hits = _cacheHits[key] ?? 0;
      final misses = _cacheMisses[key] ?? 0;
      final total = hits + misses;

      if (total > 0) {
        hitRates[key] = hits / total * 100;
      }
    }

    return stats;
  }

  /// Calculate percentile from sorted list
  double _percentile(List<int> sortedValues, double percentile) {
    if (sortedValues.isEmpty) return 0;

    final index = (sortedValues.length - 1) * percentile;
    final lower = index.floor();
    final upper = index.ceil();

    if (lower == upper) {
      return sortedValues[lower].toDouble();
    }

    final weight = index - lower;
    return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
  }

  /// Generate performance report
  String generatePerformanceReport() {
    final stats = getPerformanceStats();
    final buffer = StringBuffer();

    buffer.writeln('PERFORMANCE MONITORING REPORT');
    buffer.writeln('=====================================');
    buffer.writeln();

    // Operations performance
    buffer.writeln('OPERATION PERFORMANCE:');
    final operations = stats['operations'] as Map<String, dynamic>;
    for (final entry in operations.entries) {
      final opStats = entry.value as Map<String, dynamic>;
      buffer.writeln('  ${entry.key}:');
      buffer.writeln('    Count: ${opStats['count']}');
      buffer.writeln(
        '    Average: ${(opStats['avg_ms'] as double).toStringAsFixed(2)}ms',
      );
      buffer.writeln(
        '    P95: ${(opStats['p95_ms'] as double).toStringAsFixed(2)}ms',
      );
      buffer.writeln(
        '    P99: ${(opStats['p99_ms'] as double).toStringAsFixed(2)}ms',
      );
    }
    buffer.writeln();

    // Query counts
    buffer.writeln('QUERY COUNTS:');
    final queries = stats['queries'] as Map<String, int>;
    for (final entry in queries.entries) {
      buffer.writeln('  ${entry.key}: ${entry.value}');
    }
    buffer.writeln();

    // Cache performance
    buffer.writeln('CACHE PERFORMANCE:');
    final cache = stats['cache'] as Map<String, dynamic>;
    final hitRates = cache['hit_rates'] as Map<String, double>;
    for (final entry in hitRates.entries) {
      buffer.writeln(
        '  ${entry.key}: ${entry.value.toStringAsFixed(1)}% hit rate',
      );
    }

    return buffer.toString();
  }

  /// Validate performance improvements
  PerformanceValidationResult validateOptimizations() {
    final stats = getPerformanceStats();
    final issues = <String>[];
    final improvements = <String>[];

    // Validate cache hit rates
    final cache = stats['cache'] as Map<String, dynamic>;
    final hitRates = cache['hit_rates'] as Map<String, double>;

    for (final entry in hitRates.entries) {
      if (entry.value < 50) {
        issues.add(
          'Low cache hit rate for ${entry.key}: ${entry.value.toStringAsFixed(1)}%',
        );
      } else if (entry.value > 80) {
        improvements.add(
          'Good cache hit rate for ${entry.key}: ${entry.value.toStringAsFixed(1)}%',
        );
      }
    }

    // Validate query patterns
    final queries = stats['queries'] as Map<String, int>;
    final userAccountBalanceQueries = queries['getUserAccountBalances'] ?? 0;
    final userAccountSummaryQueries = queries['getUserAccountSummary'] ?? 0;

    if (userAccountBalanceQueries > 0) {
      improvements.add('N+1 pattern eliminated in getUserAccountBalances');
    }

    if (userAccountSummaryQueries > 0) {
      improvements.add('N+1 pattern eliminated in getUserAccountSummary');
    }

    // Validate response times
    final operations = stats['operations'] as Map<String, dynamic>;
    for (final entry in operations.entries) {
      final opStats = entry.value as Map<String, dynamic>;
      final avgTime = opStats['avg_ms'] as double;

      if (avgTime > 1000) {
        issues.add(
          'High response time for ${entry.key}: ${avgTime.toStringAsFixed(2)}ms',
        );
      } else if (avgTime < 200) {
        improvements.add(
          'Good response time for ${entry.key}: ${avgTime.toStringAsFixed(2)}ms',
        );
      }
    }

    return PerformanceValidationResult(
      isOptimized: issues.isEmpty,
      issues: issues,
      improvements: improvements,
      metrics: stats,
    );
  }

  /// Reset all performance metrics
  void reset() {
    _responseTimes.clear();
    _queryCounts.clear();
    _cacheHits.clear();
    _cacheMisses.clear();
    _operationStartTimes.clear();

    _log.info('Performance monitoring metrics reset');
  }
}

/// Result of performance validation
class PerformanceValidationResult {
  const PerformanceValidationResult({
    required this.isOptimized,
    required this.issues,
    required this.improvements,
    required this.metrics,
  });

  final bool isOptimized;
  final List<String> issues;
  final List<String> improvements;
  final Map<String, dynamic> metrics;

  @override
  String toString() {
    final buffer = StringBuffer();

    buffer.writeln('PERFORMANCE VALIDATION RESULT');
    buffer.writeln(
      'Status: ${isOptimized ? 'OPTIMIZED' : 'NEEDS IMPROVEMENT'}',
    );
    buffer.writeln();

    if (improvements.isNotEmpty) {
      buffer.writeln('IMPROVEMENTS:');
      for (final improvement in improvements) {
        buffer.writeln('  ✓ $improvement');
      }
      buffer.writeln();
    }

    if (issues.isNotEmpty) {
      buffer.writeln('ISSUES:');
      for (final issue in issues) {
        buffer.writeln('  ✗ $issue');
      }
    }

    return buffer.toString();
  }
}
