import 'package:budapp/services/logging_service.dart';
import 'package:budapp/services/remote_config_service.dart';
import 'package:flutter/foundation.dart';

/// Performance optimization rollout service using Firebase Remote Config
///
/// Manages gradual rollout of performance optimizations with feature flags:
/// - Intelligent caching enabled/disabled
/// - Query optimization levels
/// - Cache TTL configurations
/// - Performance monitoring settings
class PerformanceRolloutService {
  PerformanceRolloutService(this._remoteConfigService);

  final RemoteConfigService _remoteConfigService;
  final LoggingService _log = LoggingService();

  // Feature flag keys
  static const String _intelligentCachingEnabled =
      'performance_intelligent_caching_enabled';
  static const String _queryOptimizationLevel =
      'performance_query_optimization_level';
  static const String _cacheBalancesTtlSeconds =
      'performance_cache_balances_ttl_seconds';
  static const String _cacheSummaryTtlSeconds =
      'performance_cache_summary_ttl_seconds';
  static const String _cacheSearchTtlSeconds =
      'performance_cache_search_ttl_seconds';
  static const String _performanceMonitoringEnabled =
      'performance_monitoring_enabled';
  static const String _rolloutPercentage = 'performance_rollout_percentage';

  /// Check if intelligent caching is enabled for this user
  bool get isIntelligentCachingEnabled {
    try {
      return _remoteConfigService.instance.getBool(_intelligentCachingEnabled);
    } on Exception catch (e) {
      _log.error('Failed to get intelligent caching flag', error: e);
      return true; // Default to enabled for performance
    }
  }

  /// Get query optimization level (0=disabled, 1=basic, 2=advanced, 3=aggressive)
  int get queryOptimizationLevel {
    try {
      return _remoteConfigService.instance.getInt(_queryOptimizationLevel);
    } on Exception catch (e) {
      _log.error('Failed to get query optimization level', error: e);
      return 2; // Default to advanced
    }
  }

  /// Get cache TTL for account balances (in seconds)
  Duration get cacheBalancesTtl {
    try {
      final seconds = _remoteConfigService.instance.getInt(
        _cacheBalancesTtlSeconds,
      );
      return Duration(seconds: seconds);
    } on Exception catch (e) {
      _log.error('Failed to get cache balances TTL', error: e);
      return const Duration(minutes: 5); // Default 5 minutes
    }
  }

  /// Get cache TTL for account summary (in seconds)
  Duration get cacheSummaryTtl {
    try {
      final seconds = _remoteConfigService.instance.getInt(
        _cacheSummaryTtlSeconds,
      );
      return Duration(seconds: seconds);
    } on Exception catch (e) {
      _log.error('Failed to get cache summary TTL', error: e);
      return const Duration(minutes: 10); // Default 10 minutes
    }
  }

  /// Get cache TTL for search results (in seconds)
  Duration get cacheSearchTtl {
    try {
      final seconds = _remoteConfigService.instance.getInt(
        _cacheSearchTtlSeconds,
      );
      return Duration(seconds: seconds);
    } on Exception catch (e) {
      _log.error('Failed to get cache search TTL', error: e);
      return const Duration(minutes: 10); // Default 10 minutes
    }
  }

  /// Check if performance monitoring is enabled
  bool get isPerformanceMonitoringEnabled {
    try {
      return _remoteConfigService.instance.getBool(
        _performanceMonitoringEnabled,
      );
    } on Exception catch (e) {
      _log.error('Failed to get performance monitoring flag', error: e);
      return kDebugMode; // Default to debug mode only
    }
  }

  /// Get rollout percentage (0-100)
  int get rolloutPercentage {
    try {
      return _remoteConfigService.instance.getInt(_rolloutPercentage);
    } on Exception catch (e) {
      _log.error('Failed to get rollout percentage', error: e);
      return 100; // Default to full rollout
    }
  }

  /// Check if user is in the performance optimization rollout
  bool isUserInRollout(String userId) {
    try {
      final percentage = rolloutPercentage;
      if (percentage >= 100) return true;
      if (percentage <= 0) return false;

      // Use consistent hash of user ID to determine rollout group
      final hash = userId.hashCode.abs() % 100;
      final isInRollout = hash < percentage;

      _log.debug(
        'User rollout check: ${userId.substring(0, 8)} hash=$hash percentage=$percentage inRollout=$isInRollout',
      );

      return isInRollout;
    } on Exception catch (e) {
      _log.error('Failed to determine user rollout status', error: e);
      return true; // Default to including user
    }
  }

  /// Get performance configuration for user
  PerformanceConfig getConfigForUser(String userId) {
    final inRollout = isUserInRollout(userId);

    return PerformanceConfig(
      intelligentCachingEnabled: inRollout && isIntelligentCachingEnabled,
      queryOptimizationLevel: inRollout ? queryOptimizationLevel : 0,
      cacheBalancesTtl: cacheBalancesTtl,
      cacheSummaryTtl: cacheSummaryTtl,
      cacheSearchTtl: cacheSearchTtl,
      performanceMonitoringEnabled: isPerformanceMonitoringEnabled,
      isInRollout: inRollout,
    );
  }

  /// Log rollout metrics for monitoring
  void logRolloutMetrics(String operation, PerformanceConfig config) {
    if (!config.performanceMonitoringEnabled) return;

    _log.info(
      'Performance rollout metrics: $operation inRollout=${config.isInRollout} caching=${config.intelligentCachingEnabled} level=${config.queryOptimizationLevel}',
    );
  }
}

/// Performance configuration for a user
class PerformanceConfig {
  const PerformanceConfig({
    required this.intelligentCachingEnabled,
    required this.queryOptimizationLevel,
    required this.cacheBalancesTtl,
    required this.cacheSummaryTtl,
    required this.cacheSearchTtl,
    required this.performanceMonitoringEnabled,
    required this.isInRollout,
  });

  final bool intelligentCachingEnabled;
  final int queryOptimizationLevel;
  final Duration cacheBalancesTtl;
  final Duration cacheSummaryTtl;
  final Duration cacheSearchTtl;
  final bool performanceMonitoringEnabled;
  final bool isInRollout;

  /// Check if specific optimization should be applied
  bool shouldApplyOptimization(PerformanceOptimization optimization) {
    if (!isInRollout) return false;

    switch (optimization) {
      case PerformanceOptimization.intelligentCaching:
        return intelligentCachingEnabled;
      case PerformanceOptimization.basicQueryOptimization:
        return queryOptimizationLevel >= 1;
      case PerformanceOptimization.advancedQueryOptimization:
        return queryOptimizationLevel >= 2;
      case PerformanceOptimization.aggressiveQueryOptimization:
        return queryOptimizationLevel >= 3;
    }
  }

  @override
  String toString() {
    return 'PerformanceConfig('
        'inRollout: $isInRollout, '
        'caching: $intelligentCachingEnabled, '
        'optimizationLevel: $queryOptimizationLevel, '
        'monitoring: $performanceMonitoringEnabled)';
  }
}

/// Types of performance optimizations
enum PerformanceOptimization {
  intelligentCaching,
  basicQueryOptimization,
  advancedQueryOptimization,
  aggressiveQueryOptimization,
}
