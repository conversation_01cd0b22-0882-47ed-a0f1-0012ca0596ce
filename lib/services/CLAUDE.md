# Services Directory - CLAUDE.md

This directory contains global services that provide infrastructure and cross-cutting concerns.

## Directory Structure

```
services/
├── implementations/     # Concrete service implementations
├── interfaces/         # Service contracts (when applicable)
└── [core services]     # Direct service implementations
```

## Core Services

### Infrastructure Services
- **FirestoreService** - Firebase Firestore abstraction layer
- **RemoteConfigService** - Server-side configuration management
- **SessionService** - User session and authentication state
- **SecureStorageService** - Encrypted local storage
- **LoggingService** - Production-safe logging with PII protection

### Business Services  
- **CurrencyService** - Global currency formatting and conversion
- **CurrencyPreferencesService** - User currency preference management
- **PerformanceService** - App performance monitoring and metrics
- **ErrorService** - Centralized error handling and user-friendly error messaging
- **GlobalErrorHandler** - Firebase Crashlytics integration with comprehensive crash reporting

### Connectivity Services
- **FirebaseConnectivityService** - Firebase connection state management

## Architecture Principles

### Service Layer Rules
1. **Abstract Firebase functionality** - Services should provide clean APIs over Firebase
2. **Dependency injection** - All services injected via Riverpod providers
3. **Error handling** - Comprehensive error handling with proper logging
4. **Testing** - All services must be testable with mocking support

### Security Standards
- **No hardcoded secrets** - All sensitive data via environment variables
- **PII protection** - Automatic sanitization in logging service
- **Input validation** - All user inputs validated and sanitized
- **Secure storage** - Sensitive data encrypted at rest

## Key Service Details

### FirestoreService
- **Purpose**: Abstract Firestore operations with error handling
- **Features**: Collection management, document CRUD, transaction support
- **Security**: User data isolation, proper access control
- **Testing**: Mockable for unit tests

### LoggingService
- **Purpose**: Production-safe structured logging
- **Features**: Automatic PII sanitization, environment-aware levels
- **Security**: Sensitive data redaction (emails, phones, amounts, API keys)
- **Usage**:
```dart
import 'package:budapp/services/logging_service.dart';

log.info('User authentication successful');
log.error('Payment processing error', error: exception);

// Debug logs properly guarded
if (kDebugMode) {
  log.debug('Debug message');
}
```

### RemoteConfigService
- **Purpose**: Server-side configuration management
- **Features**: Environment-specific configs, A/B testing support
- **Caching**: Local caching with background updates
- **Fallbacks**: Graceful degradation when service unavailable

### CurrencyService
- **Purpose**: Global currency formatting and conversion
- **Features**: Multi-currency support, locale-aware formatting
- **Architecture**: Singleton pattern via Riverpod
- **Usage**:
```dart
final currencyFormatter = ref.watch(currencyFormatterProvider);
Text(currencyFormatter.formatAmount(amountCents));
```

### ErrorService ✅ ENHANCED
- **Purpose**: Centralized error handling and user-friendly error messaging
- **Features**: Firebase-specific error translation, consistent UI error display, crash reporting integration
- **Architecture**: Static service with comprehensive error type handling (15+ Firebase Auth codes, 15+ Firestore codes)
- **Enhanced Capabilities**: Crash reporting integration, context-aware logging, user action tracking
- **Security**: Automatic PII protection via LoggingService integration
- **Usage**:
```dart
// User-friendly error display with automatic crash reporting
ErrorService.showErrorSnackBarWithLogging(
  context,
  error,
  errorContext: 'Transaction creation',
  additionalData: {'transactionType': 'transfer'},
);

// Advanced error logging with context
await ErrorService.logError(
  error,
  stackTrace,
  context: 'Payment processing',
  additionalData: {'userId': userId, 'amount': amount},
  fatal: false,
);

// Set user context for crash reports
await ErrorService.setUserContext(userId);

// Log user actions for crash context
ErrorService.logUserAction('Transaction submitted', {'type': 'expense'});
```

### GlobalErrorHandler ✅ NEW SERVICE
- **Purpose**: Firebase Crashlytics integration for comprehensive crash reporting and global error capture
- **Features**: Global error handlers (Flutter, Platform, Isolate), automatic crash reporting, user context tracking
- **Security**: Automatic data sanitization, environment-aware crash collection, custom key size limits (1kB per pair)
- **Architecture**: Singleton with initialization lifecycle, integrated with Riverpod providers
- **Global Handlers**: FlutterError.onError, PlatformDispatcher.instance.onError, Isolate error listeners
- **Usage**:
```dart
// Automatic initialization (via providers)
await GlobalErrorHandler.instance.initialize(
  logger: loggingService,
  enableInDebug: false, // Only crashes in release mode
);

// Manual error reporting with full context
await GlobalErrorHandler.instance.reportError(
  error,
  stackTrace,
  reason: 'Payment processing failed',
  customKeys: {'transactionType': 'transfer', 'accountId': accountId},
  breadcrumbs: ['User initiated transfer', 'Validation passed'],
  fatal: false,
);

// User context management (PII-safe)
await GlobalErrorHandler.instance.setUserIdentifier(userId);

// Breadcrumb logging for crash context
GlobalErrorHandler.instance.logBreadcrumb('User navigated to transactions');

// Test crash reporting (debug mode only)
if (kDebugMode) {
  await GlobalErrorHandler.instance.testCrash();
}
```

## Environment Considerations

### Multi-Environment Support
- Services automatically adapt to environment (dev/staging/prod)
- Different logging levels per environment
- Environment-specific Firebase configurations

### Performance Monitoring
- Built-in performance tracking via PerformanceService
- Automatic screen tracking and custom metrics
- Production monitoring without PII exposure

## Agent Guidelines

When working with services:

1. **Follow dependency injection** - Always use Riverpod providers
2. **Maintain abstraction** - Services should hide implementation details
3. **Comprehensive testing** - Mock all external dependencies
4. **Security first** - Never log sensitive data, validate inputs
5. **Error handling** - Use proper exception handling and logging

### Common Patterns

```dart
// Service with dependency injection
@riverpod
class MyService extends _$MyService {
  final FirestoreService _firestore;
  final LoggingService _logger;
  
  MyService(this._firestore, this._logger);
  
  Future<Result<T>> performOperation() async {
    return AsyncValue.guard(() async {
      _logger.info('Starting operation');
      // Implementation
    });
  }
}
```

### Testing Services

```dart
// Service testing pattern
test('service performs operation correctly', () async {
  final mockFirestore = MockFirestoreService();
  final service = MyService(mockFirestore, MockLoggingService());
  
  when(() => mockFirestore.getData()).thenAnswer((_) async => testData);
  
  final result = await service.performOperation();
  
  expect(result.isSuccess, true);
  verify(() => mockFirestore.getData()).called(1);
});
```

## Error Boundary Integration

### ErrorBoundaryWidget (lib/widgets/error_boundary_widget.dart)
While technically a widget, this component provides service-level error handling capabilities:

- **React-Style Error Boundaries**: Graceful UI degradation when widgets encounter errors
- **Service Integration**: Automatically integrates with ErrorService for crash reporting
- **User Recovery**: Built-in retry mechanisms and user-friendly fallback UI
- **Development Tools**: Debug-mode error details and manual error reporting

```dart
// Wrap critical UI components with error boundaries
Widget build(BuildContext context) {
  return MyComplexWidget().withErrorBoundary(
    showDetails: kDebugMode,
    canRetry: true,
    onError: (error, stackTrace) {
      // Custom error handling logic
    },
  );
}

// Simple safety wrapper for non-critical components
Widget build(BuildContext context) {
  return RiskyWidget().safely(
    fallback: Text('Content temporarily unavailable'),
  );
}
```

## Recent Changes & Context

### ✅ MAJOR UPDATE: Centralized Error Handling Complete (Task 31.8)
- **GlobalErrorHandler Service**: New comprehensive crash reporting service with Firebase Crashlytics integration
- **Enhanced ErrorService**: Added crash reporting integration, context-aware logging, user action tracking
- **Error Boundary System**: React-style error boundaries for graceful UI degradation
- **Provider Integration**: Automatic error handler initialization via Riverpod providers
- **Security Enhancement**: PII protection, environment-aware configuration, user context tracking
- **Production Readiness**: Zero test failures, clean static analysis, comprehensive documentation

### Previous Enhancements
- LoggingService enhanced with automatic PII sanitization
- Multi-environment configuration completed
- Performance monitoring integrated across all services
- RemoteConfigService refactored for better caching and error handling
- All services now follow consistent error handling patterns