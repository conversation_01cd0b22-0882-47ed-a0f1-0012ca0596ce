class ValidationResult {
  const ValidationResult({required this.isValid, required this.errors});

  factory ValidationResult.success() {
    return const ValidationResult(isValid: true, errors: []);
  }

  factory ValidationResult.failure(List<String> errors) {
    return ValidationResult(isValid: false, errors: errors);
  }

  factory ValidationResult.singleError(String error) {
    return ValidationResult(isValid: false, errors: [error]);
  }
  final bool isValid;
  final List<String> errors;

  String? get firstError => errors.isNotEmpty ? errors.first : null;

  String get allErrors => errors.join('\n');
}
