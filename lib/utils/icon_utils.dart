import 'package:flutter/material.dart';

/// Mapping of icon name strings to [IconData].
const Map<String, IconData> _iconMap = {
  'shopping_cart': Icons.shopping_cart,
  'restaurant': Icons.restaurant,
  'local_gas_station': Icons.local_gas_station,
  'home': Icons.home,
  'directions_car': Icons.directions_car,
  'medical_services': Icons.medical_services,
  'school': Icons.school,
  'sports_esports': Icons.sports_esports,
  'movie': Icons.movie,
  'fitness_center': Icons.fitness_center,
  'work': Icons.work,
  'account_balance': Icons.account_balance,
  'savings': Icons.savings,
  'trending_up': Icons.trending_up,
  'card_giftcard': Icons.card_giftcard,
  'attach_money': Icons.attach_money,
  'receipt': Icons.receipt,
  'category': Icons.category,
  'label': Icons.label,
  'bookmark': Icons.bookmark,
  'account_balance_wallet': Icons.account_balance_wallet,
  'credit_card': Icons.credit_card,
  'paid': Icons.paid,
  'payment': Icons.payment,
  'money': Icons.attach_money,
  'business': Icons.business,
  'store': Icons.store,
  'local_atm': Icons.local_atm,
  'currency_exchange': Icons.currency_exchange,
  'show_chart': Icons.show_chart,
  'analytics': Icons.analytics,
  'pie_chart': Icons.pie_chart,
  'bar_chart': Icons.bar_chart,
  'wallet': Icons.account_balance_wallet,
  'money_off': Icons.money_off,
  'monetization_on': Icons.monetization_on,
  'real_estate_agent': Icons.real_estate_agent,
  'account_box': Icons.account_box,
  'folder': Icons.folder,
  'star': Icons.star,
  'favorite': Icons.favorite,
  'pets': Icons.pets,
};

/// Convert an [iconName] string to its [IconData].
/// Returns [fallback] if the name is not recognized.
IconData iconFromName(String iconName, {IconData fallback = Icons.category}) {
  return _iconMap[iconName] ?? fallback;
}
