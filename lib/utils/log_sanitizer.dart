// Common financial data patterns
final RegExp _emailPattern = RegExp(
  r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
);

final RegExp _phonePattern = RegExp(
  r'(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b',
);

final RegExp _creditCardPattern = RegExp(
  r'\b(?:4[0-9]{3}[-\s]?[0-9]{4}[-\s]?[0-9]{4}[-\s]?[0-9]{4}|5[1-5][0-9]{2}[-\s]?[0-9]{4}[-\s]?[0-9]{4}[-\s]?[0-9]{4}|3[47][0-9]{2}[-\s]?[0-9]{6}[-\s]?[0-9]{5}|3[0-9]{3}[-\s]?[0-9]{6}[-\s]?[0-9]{4}|6(?:011|5[0-9]{2})[-\s]?[0-9]{4}[-\s]?[0-9]{4}[-\s]?[0-9]{4})\b',
);

final RegExp _accountNumberPattern = RegExp(r'\b\d{6,17}\b');

final RegExp _ssnPattern = RegExp(r'\b\d{3}-?\d{2}-?\d{4}\b');

final RegExp _jwtTokenPattern = RegExp(
  r'\beyJ[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*\b',
);

final RegExp _apiKeyPattern = RegExp(r'\b[A-Za-z0-9_-]{20,}\b');

final RegExp _ipAddressPattern = RegExp(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b');

final RegExp _routingNumberPattern = RegExp(r'\b[0-9]{9}\b');

// Financial amount patterns (to redact transaction amounts in production)
final RegExp _currencyAmountPattern = RegExp(
  r'\$\s?[\d,]+\.?\d{0,2}(?:\s?USD)?|\b[\d,]+\.?\d{0,2}\s?(?:USD|EUR|GBP|CAD|JPY|AUD)\b',
);

/// Comprehensive sanitization for financial application logging
String sanitize(String input) {
  var sanitized = input;

  // JWT tokens (process before API keys to avoid conflicts)
  sanitized = sanitized.replaceAll(_jwtTokenPattern, '[JWT_TOKEN]');

  // API keys and long alphanumeric strings
  sanitized = sanitized.replaceAll(_apiKeyPattern, '[API_KEY]');

  // Email addresses - preserve domain structure
  sanitized = _sanitizeEmailsInText(sanitized);

  // Phone numbers
  sanitized = sanitized.replaceAll(_phonePattern, '***-***-****');

  // Credit card numbers
  sanitized = sanitized.replaceAll(_creditCardPattern, '****-****-****-****');

  // Currency amounts (in production) - process before account numbers
  sanitized = sanitized.replaceAll(_currencyAmountPattern, '[AMOUNT]');

  // Bank account numbers
  sanitized = sanitized.replaceAll(_accountNumberPattern, '****[ACCOUNT]');

  // Social Security Numbers
  sanitized = sanitized.replaceAll(_ssnPattern, '***-**-****');

  // IP addresses (for privacy)
  sanitized = sanitized.replaceAll(_ipAddressPattern, '***.***.***.***');

  // Bank routing numbers
  sanitized = sanitized.replaceAll(_routingNumberPattern, '[ROUTING]');

  return sanitized;
}

/// Helper method to sanitize emails while preserving domain structure
String _sanitizeEmailsInText(String input) {
  return input.replaceAllMapped(_emailPattern, (match) {
    final email = match.group(0)!;
    final parts = email.split('@');
    if (parts.length != 2) return '***@***.***';

    final domain = parts[1];
    final domainParts = domain.split('.');

    // Create sanitized domain preserving structure
    String sanitizedDomain;
    if (domainParts.length == 2) {
      // Simple domain like test.com -> ***.***
      sanitizedDomain = '***.***';
    } else if (domainParts.length >= 3) {
      // Complex domain like company.org -> ***.***.***
      sanitizedDomain = List.filled(domainParts.length, '***').join('.');
    } else {
      sanitizedDomain = '***.***';
    }

    return '***@$sanitizedDomain';
  });
}

/// Sanitize email specifically (partial reveal for debugging)
String sanitizeEmail(String email) {
  if (email.isEmpty) return email;

  final parts = email.split('@');
  if (parts.length != 2) return '***@***.***';

  final username = parts[0];
  final domain = parts[1];

  // Show first and last character of username if long enough
  String sanitizedUsername;
  if (username.length <= 2) {
    sanitizedUsername = '*' * username.length;
  } else {
    sanitizedUsername =
        '${username[0]}${'*' * (username.length - 2)}${username[username.length - 1]}';
  }

  // Sanitize domain (keep TLD for context)
  final domainParts = domain.split('.');
  String sanitizedDomain;
  if (domainParts.length > 1) {
    sanitizedDomain = '***.${domainParts.last}';
  } else {
    sanitizedDomain = '***';
  }

  return '$sanitizedUsername@$sanitizedDomain';
}

/// Sanitize phone number with partial reveal
String sanitizePhone(String phone) {
  if (phone.isEmpty) return phone;

  // Extract digits only
  final digits = phone.replaceAll(RegExp(r'[^\d]'), '');

  if (digits.length == 10) {
    return '(***) ***-${digits.substring(6)}';
  } else if (digits.length == 11 && digits.startsWith('1')) {
    return '+1 (***) ***-${digits.substring(7)}';
  } else {
    return '***-***-****';
  }
}

/// Sanitize credit card with last 4 digits
String sanitizeCreditCard(String cardNumber) {
  if (cardNumber.isEmpty) return cardNumber;

  final digits = cardNumber.replaceAll(RegExp(r'[^\d]'), '');

  if (digits.length >= 13) {
    return '****-****-****-${digits.substring(digits.length - 4)}';
  } else {
    return '****-****-****-****';
  }
}

/// Sanitize account number with last 4 digits
String sanitizeAccountNumber(String accountNumber) {
  if (accountNumber.isEmpty) return accountNumber;

  final digits = accountNumber.replaceAll(RegExp(r'[^\d]'), '');

  if (digits.length >= 6) {
    return '****${digits.substring(digits.length - 4)}';
  } else {
    return '****[ACCOUNT]';
  }
}

/// Sanitize financial amount for production logs
String sanitizeAmount(double amount, {String currency = 'USD'}) {
  return '[AMOUNT_$currency]';
}

/// Check if a string contains potential PII
bool containsPII(String input) {
  return _emailPattern.hasMatch(input) ||
      _phonePattern.hasMatch(input) ||
      _creditCardPattern.hasMatch(input) ||
      _ssnPattern.hasMatch(input) ||
      _accountNumberPattern.hasMatch(input);
}

/// Check if a string contains sensitive authentication data
bool containsAuthData(String input) {
  return _apiKeyPattern.hasMatch(input) ||
      _jwtTokenPattern.hasMatch(input) ||
      input.toLowerCase().contains('password') ||
      input.toLowerCase().contains('token') ||
      input.toLowerCase().contains('secret') ||
      input.toLowerCase().contains('key');
}

/// Sanitize error messages specifically
String sanitizeError(String errorMessage) {
  var sanitized = sanitize(errorMessage);

  // Additional error-specific sanitization
  sanitized = sanitized.replaceAll(
    RegExp(r'password[:\s=]+[^\s,;]+', caseSensitive: false),
    'password: [REDACTED]',
  );

  sanitized = sanitized.replaceAll(
    RegExp(r'token[:\s=]+[^\s,;]+', caseSensitive: false),
    'token: [REDACTED]',
  );

  sanitized = sanitized.replaceAll(
    RegExp(r'secret[:\s=]+[^\s,;]+', caseSensitive: false),
    'secret: [REDACTED]',
  );

  return sanitized;
}
