import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/utils/validation_result.dart';
import 'package:flutter/material.dart';

/// Generic validation functions for common validation patterns
///
/// This utility provides reusable validation methods that can be used across
/// different validators to reduce code duplication and ensure consistency.

/// Validates that a string is not null or empty
ValidationResult validateRequired(
  BuildContext context,
  String? value, {
  String? customMessage,
}) {
  final l10n = AppLocalizations.of(context)!;

  if (value == null || value.trim().isEmpty) {
    return ValidationResult.singleError(customMessage ?? l10n.fieldRequired);
  }

  return ValidationResult.success();
}

/// Validates string length constraints
ValidationResult validateLength(
  BuildContext context,
  String? value, {
  int? minLength,
  int? maxLength,
  String? fieldName,
}) {
  if (value == null) {
    return ValidationResult.success(); // Let required validation handle null
  }

  final trimmedValue = value.trim();
  final errors = <String>[];

  if (minLength != null && trimmedValue.length < minLength) {
    if (fieldName != null) {
      errors.add('$fieldName must be at least $minLength characters');
    } else {
      errors.add('Must be at least $minLength characters');
    }
  }

  if (maxLength != null && trimmedValue.length > maxLength) {
    if (fieldName != null) {
      errors.add('$fieldName must be $maxLength characters or less');
    } else {
      errors.add('Must be $maxLength characters or less');
    }
  }

  return errors.isEmpty
      ? ValidationResult.success()
      : ValidationResult.failure(errors);
}

/// Validates numeric input and parsing
ValidationResult validateNumber(
  BuildContext context,
  String? value, {
  double? min,
  double? max,
  bool allowNegative = true,
  String? customInvalidMessage,
  String? customRangeMessage,
}) {
  final l10n = AppLocalizations.of(context)!;

  if (value == null || value.trim().isEmpty) {
    return ValidationResult.success(); // Let required validation handle empty
  }

  final number = double.tryParse(value.trim());
  if (number == null) {
    return ValidationResult.singleError(
      customInvalidMessage ?? l10n.invalidNumber,
    );
  }

  if (!allowNegative && number < 0) {
    return ValidationResult.singleError('Value must be positive');
  }

  if (min != null && number < min) {
    return ValidationResult.singleError(
      customRangeMessage ?? 'Value must be at least $min',
    );
  }

  if (max != null && number > max) {
    return ValidationResult.singleError(
      customRangeMessage ?? 'Value must be at most $max',
    );
  }

  return ValidationResult.success();
}

/// Validates decimal places for currency amounts
ValidationResult validateDecimalPlaces(
  BuildContext context,
  String? value, {
  int maxDecimalPlaces = 2,
}) {
  if (value == null || value.trim().isEmpty) {
    return ValidationResult.success();
  }

  final parts = value.trim().split('.');
  if (parts.length > 2) {
    return ValidationResult.singleError('Invalid number format');
  }

  if (parts.length == 2 && parts[1].length > maxDecimalPlaces) {
    return ValidationResult.singleError(
      'Maximum $maxDecimalPlaces decimal places allowed',
    );
  }

  return ValidationResult.success();
}

/// Validates currency amount (combines number and decimal validation)
ValidationResult validateCurrencyAmount(
  BuildContext context,
  String? value, {
  double? min,
  double? max,
  bool allowNegative = true,
}) {
  final l10n = AppLocalizations.of(context)!;

  // Check required
  final requiredResult = validateRequired(context, value);
  if (!requiredResult.isValid) {
    return requiredResult;
  }

  // Check decimal places
  final decimalResult = validateDecimalPlaces(context, value);
  if (!decimalResult.isValid) {
    return decimalResult;
  }

  // Check number validity and range
  return validateNumber(
    context,
    value,
    min: min,
    max: max,
    allowNegative: allowNegative,
    customInvalidMessage: l10n.amountInvalid,
  );
}

/// Validates regex pattern
ValidationResult validatePattern(
  BuildContext context,
  String? value,
  RegExp pattern, {
  String? customMessage,
}) {
  if (value == null || value.trim().isEmpty) {
    return ValidationResult.success(); // Let required validation handle empty
  }

  if (!pattern.hasMatch(value.trim())) {
    return ValidationResult.singleError(customMessage ?? 'Invalid format');
  }

  return ValidationResult.success();
}

/// Validates hex color code
ValidationResult validateHexColor(BuildContext context, String? value) {
  if (value == null || value.trim().isEmpty) {
    return ValidationResult.success(); // Color is optional
  }

  final hexColorRegex = RegExp(r'^#[0-9A-Fa-f]{6}$');
  return validatePattern(
    context,
    value,
    hexColorRegex,
    customMessage: 'Invalid hex color format (e.g., #FF0000)',
  );
}

/// Validates that a value is not null (for dropdowns, selections, etc.)
ValidationResult validateNotNull<T>(
  BuildContext context,
  T? value, {
  String? customMessage,
}) {
  final l10n = AppLocalizations.of(context)!;

  if (value == null) {
    return ValidationResult.singleError(customMessage ?? l10n.fieldRequired);
  }

  return ValidationResult.success();
}

/// Validates email format
ValidationResult validateEmail(BuildContext context, String? value) {
  if (value == null || value.trim().isEmpty) {
    return ValidationResult.success(); // Let required validation handle empty
  }

  final emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );

  return validatePattern(
    context,
    value,
    emailRegex,
    customMessage: 'Invalid email format',
  );
}

/// Validates that two values match (for password confirmation, etc.)
ValidationResult validateMatch(
  BuildContext context,
  String? value1,
  String? value2, {
  String? customMessage,
}) {
  if (value1 != value2) {
    return ValidationResult.singleError(customMessage ?? 'Values do not match');
  }

  return ValidationResult.success();
}

/// Combines multiple validation results
ValidationResult combineResults(List<ValidationResult> results) {
  final allErrors = <String>[];

  for (final result in results) {
    if (!result.isValid) {
      allErrors.addAll(result.errors);
    }
  }

  return allErrors.isEmpty
      ? ValidationResult.success()
      : ValidationResult.failure(allErrors);
}

/// Converts ValidationResult to String? for TextFormField validator
String? toFormFieldValidator(ValidationResult result) {
  return result.isValid ? null : result.firstError;
}
