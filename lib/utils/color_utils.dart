import 'package:flutter/material.dart';

/// Parse a hex color string like `#RRGGBB` or `#AARRGGBB` into a [Color].
///
/// Returns [fallback] if parsing fails.
Color parseHex(String hex, {Color fallback = Colors.grey}) {
  try {
    if (hex.startsWith('#')) {
      final value = int.parse(hex.substring(1), radix: 16);
      if (hex.length == 7) {
        return Color(0xFF000000 | value);
      } else if (hex.length == 9) {
        return Color(value);
      }
    }
  } on Exception catch (_) {}
  return fallback;
}

/// Get a contrasting text color (black or white) for the given [backgroundColor].
Color contrastOf(Color backgroundColor) {
  final luminance = backgroundColor.computeLuminance();
  return luminance > 0.5 ? Colors.black : Colors.white;
}

/// Parse [hex] and return a contrasting text color for it.
Color contrastOfHex(String hex, {Color fallback = Colors.grey}) {
  final color = parseHex(hex, fallback: fallback);
  return contrastOf(color);
}
