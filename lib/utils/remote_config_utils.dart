import 'package:budapp/data/models/remote_config_data.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Utility functions for accessing Remote Config data throughout the app

/// Get predefined income categories
List<String> getIncomeCategories(WidgetRef ref) {
  final remoteConfigNotifier = ref.read(remoteConfigNotifierProvider.notifier);
  return remoteConfigNotifier.getPredefinedCategories().incomeCategories;
}

/// Get predefined expense categories
List<String> getExpenseCategories(WidgetRef ref) {
  final remoteConfigNotifier = ref.read(remoteConfigNotifierProvider.notifier);
  return remoteConfigNotifier.getPredefinedCategories().expenseCategories;
}

/// Get all predefined categories
PredefinedCategories getPredefinedCategories(WidgetRef ref) {
  final remoteConfigNotifier = ref.read(remoteConfigNotifierProvider.notifier);
  return remoteConfigNotifier.getPredefinedCategories();
}

/// Get premium limits
PremiumLimits getPremiumLimits(WidgetRef ref) {
  final remoteConfigNotifier = ref.read(remoteConfigNotifierProvider.notifier);
  return remoteConfigNotifier.getPremiumLimits();
}

/// Check if a feature is enabled
bool isFeatureEnabled(WidgetRef ref, String featureKey) {
  final remoteConfigNotifier = ref.read(remoteConfigNotifierProvider.notifier);
  return remoteConfigNotifier.isFeatureEnabled(featureKey);
}

/// Get maximum accounts for free tier
int getMaxAccountsFree(WidgetRef ref) {
  return getPremiumLimits(ref).maxAccountsFree;
}

/// Get maximum accounts for premium tier
int getMaxAccountsPremium(WidgetRef ref) {
  return getPremiumLimits(ref).maxAccountsPremium;
}

/// Get maximum custom categories for free tier
int getMaxCustomCategoriesFree(WidgetRef ref) {
  return getPremiumLimits(ref).maxCustomCategoriesFree;
}

/// Get maximum custom categories for premium tier
int getMaxCustomCategoriesPremium(WidgetRef ref) {
  return getPremiumLimits(ref).maxCustomCategoriesPremium;
}

/// Get maximum budgets for free tier
int getMaxBudgetsFree(WidgetRef ref) {
  return getPremiumLimits(ref).maxBudgetsFree;
}

/// Get maximum budgets for premium tier
int getMaxBudgetsPremium(WidgetRef ref) {
  return getPremiumLimits(ref).maxBudgetsPremium;
}

/// Get maximum goals for free tier
int getMaxGoalsFree(WidgetRef ref) {
  return getPremiumLimits(ref).maxGoalsFree;
}

/// Get maximum goals for premium tier
int getMaxGoalsPremium(WidgetRef ref) {
  return getPremiumLimits(ref).maxGoalsPremium;
}

/// Check if user can create more accounts based on current count
bool canCreateAccount(
  WidgetRef ref,
  int currentCount, {
  required bool isPremium,
}) {
  final maxAccounts = isPremium
      ? getMaxAccountsPremium(ref)
      : getMaxAccountsFree(ref);
  return currentCount < maxAccounts;
}

/// Check if user can create more custom categories based on current count
bool canCreateCustomCategory(
  WidgetRef ref,
  int currentCount, {
  required bool isPremium,
}) {
  final maxCategories = isPremium
      ? getMaxCustomCategoriesPremium(ref)
      : getMaxCustomCategoriesFree(ref);
  return currentCount < maxCategories;
}

/// Check if user can create more budgets based on current count
bool canCreateBudget(
  WidgetRef ref,
  int currentCount, {
  required bool isPremium,
}) {
  final maxBudgets = isPremium
      ? getMaxBudgetsPremium(ref)
      : getMaxBudgetsFree(ref);
  return currentCount < maxBudgets;
}

/// Check if user can create more goals based on current count
bool canCreateGoal(WidgetRef ref, int currentCount, {required bool isPremium}) {
  final maxGoals = isPremium ? getMaxGoalsPremium(ref) : getMaxGoalsFree(ref);
  return currentCount < maxGoals;
}

/// Get remaining slots for accounts
int getRemainingAccountSlots(
  WidgetRef ref,
  int currentCount, {
  required bool isPremium,
}) {
  final maxAccounts = isPremium
      ? getMaxAccountsPremium(ref)
      : getMaxAccountsFree(ref);
  return (maxAccounts - currentCount).clamp(0, maxAccounts);
}

/// Get remaining slots for custom categories
int getRemainingCategorySlots(
  WidgetRef ref,
  int currentCount, {
  required bool isPremium,
}) {
  final maxCategories = isPremium
      ? getMaxCustomCategoriesPremium(ref)
      : getMaxCustomCategoriesFree(ref);
  return (maxCategories - currentCount).clamp(0, maxCategories);
}

/// Get remaining slots for budgets
int getRemainingBudgetSlots(
  WidgetRef ref,
  int currentCount, {
  required bool isPremium,
}) {
  final maxBudgets = isPremium
      ? getMaxBudgetsPremium(ref)
      : getMaxBudgetsFree(ref);
  return (maxBudgets - currentCount).clamp(0, maxBudgets);
}

/// Get remaining slots for goals
int getRemainingGoalSlots(
  WidgetRef ref,
  int currentCount, {
  required bool isPremium,
}) {
  final maxGoals = isPremium ? getMaxGoalsPremium(ref) : getMaxGoalsFree(ref);
  return (maxGoals - currentCount).clamp(0, maxGoals);
}

/// Refresh Remote Config data
Future<void> refreshConfig(WidgetRef ref) async {
  final remoteConfigNotifier = ref.read(remoteConfigNotifierProvider.notifier);
  await remoteConfigNotifier.refresh();
}
