{"$schema": "https://repomix.com/schemas/latest/schema.json", "input": {"maxFileSize": 52428800}, "output": {"filePath": "repomix-output.xml", "style": "xml", "parsableStyle": false, "fileSummary": false, "directoryStructure": true, "files": true, "removeComments": true, "removeEmptyLines": true, "compress": false, "topFilesLength": 20, "showLineNumbers": false, "copyToClipboard": false, "git": {"sortByChanges": false, "sortByChangesMaxCommits": 100, "includeDiffs": false}}, "include": [], "ignore": {"useGitignore": true, "useDefaultPatterns": true, "customPatterns": []}, "security": {"enableSecurityCheck": true}, "tokenCount": {"encoding": "o200k_base"}}