# Project Structure & Organization

## Architecture Overview

BudApp follows a **feature-based architecture** (not technical layers) with clean separation of concerns and the repository pattern. The project is organized by business domains rather than technical layers.

## Root Directory Structure

```
budapp/
├── lib/                          # Main application code
├── test/                         # Test files (mirrors lib structure)
├── docs/                         # Comprehensive documentation
├── firebase/                     # Firebase configuration and rules
├── android/                      # Android-specific configuration
├── ios/                          # iOS-specific configuration
├── assets/                       # Static assets (icons, images)
├── scripts/                      # Build and deployment scripts
└── coverage/                     # Test coverage reports
```

## Core Application Structure (`lib/`)

### Feature-Based Organization
```
lib/
├── main.dart                     # App entry point (dev environment)
├── main_staging.dart             # Staging environment entry
├── main_prod.dart                # Production environment entry
├── firebase_options_*.dart       # Environment-specific Firebase config
├── config/                       # App-wide configuration
├── features/                     # Feature modules (business domains)
├── data/                         # Data layer (repositories, models)
├── providers/                    # Riverpod providers
├── routing/                      # Navigation configuration
├── services/                     # Cross-cutting services
├── widgets/                      # Shared UI components
├── utils/                        # Utility functions
└── l10n/                         # Localization files
```

### Feature Modules (`lib/features/`)
Each feature is self-contained with its own UI, logic, and models:

```
features/
├── auth/                         # Authentication feature
│   ├── data/                     # Auth-specific data models
│   ├── providers/                # Auth state management
│   ├── screens/                  # Auth UI screens
│   └── widgets/                  # Auth-specific widgets
├── accounts/                     # Account management
├── transactions/                 # Transaction management
├── categories/                   # Category management
├── budgets/                      # Budget management
├── tags/                         # Tag management
├── dashboard/                    # Dashboard and analytics
├── profile/                      # User profile management
├── settings/                     # App settings
└── common/                       # Shared feature components
```

### Data Layer (`lib/data/`)
Repository pattern with clear interfaces and implementations:

```
data/
├── models/                       # Freezed data models
│   ├── user_profile.dart
│   ├── account.dart
│   ├── transaction.dart
│   ├── category.dart
│   └── budget.dart
└── repositories/
    ├── interfaces/               # Abstract repository contracts
    │   ├── base_repository.dart
    │   ├── user_repository.dart
    │   ├── account_repository.dart
    │   └── transaction_repository.dart
    └── implementations/          # Concrete implementations
        ├── user_repository_impl.dart
        ├── account_repository_impl.dart
        └── transaction_repository_impl.dart
```

### Providers (`lib/providers/`)
Organized Riverpod provider hierarchy:

```
providers/
├── providers.dart                # Main exports and core providers
├── firebase_providers.dart      # Firebase service providers
├── repository_providers.dart    # Repository instances
├── ui_providers.dart            # UI state providers
└── logging_providers.dart       # Logging configuration
```

### Configuration (`lib/config/`)
Centralized app configuration:

```
config/
├── app_theme.dart               # Material 3 theme configuration
├── design_tokens.dart           # Design system tokens
├── environment_config.dart      # Multi-environment setup
├── form_constants.dart          # Form field configurations
└── text_overflow_config.dart    # Global text overflow system
```

## Test Structure (`test/`)

Tests mirror the lib structure for easy navigation:

```
test/
├── helpers/                     # Test utilities and setup
│   ├── firebase_test_setup.dart
│   ├── mock_providers.dart
│   ├── test_wrapper.dart
│   └── mock_data_factory.dart
├── features/                    # Feature-specific tests
│   ├── auth/
│   ├── accounts/
│   ├── transactions/
│   └── categories/
├── data/                        # Repository and model tests
├── services/                    # Service layer tests
├── integration/                 # Integration tests
└── unit/                        # Unit tests
```

## Documentation Structure (`docs/`)

Comprehensive project documentation:

```
docs/
├── README.md                    # Documentation index
├── PRD.md                       # Product Requirements Document
├── architecture.md             # Technical architecture guide
├── DEVELOPMENT_WORKFLOW.md      # Development processes
├── TESTING.md                   # Testing strategies
├── rules/                       # Coding standards and guidelines
├── refactor/                    # Refactoring documentation
└── tasks/                       # Task-specific documentation
```

## Key Architectural Patterns

### 1. Repository Pattern Enforcement
- **Interfaces First**: All repositories implement abstract interfaces
- **Dependency Injection**: Repositories injected via Riverpod providers
- **No Direct Firebase Access**: Business logic never calls Firebase directly
- **Service Abstractions**: Cross-cutting concerns use service interfaces

### 2. State Management Pattern
```dart
// Provider definition
@riverpod
class TransactionNotifier extends _$TransactionNotifier {
  @override
  FutureOr<List<Transaction>> build() async {
    // Implementation
  }
}

// Usage in widgets
final transactions = ref.watch(transactionNotifierProvider);
```

### 3. Data Model Pattern
```dart
@freezed
class Transaction with _$Transaction {
  const factory Transaction({
    required String id,
    required double amount,
    required String categoryId,
    // ... other fields
  }) = _Transaction;

  factory Transaction.fromJson(Map<String, dynamic> json) =>
      _$TransactionFromJson(json);
}
```

### 4. Navigation Pattern
```dart
// Route definitions
class AppRoutes {
  static const String home = '/home';
  static const String transactions = '/transactions';
  static const String accounts = '/accounts';
}

// Navigation usage
context.go(AppRoutes.transactions);
context.push('/transactions/create');
```

## File Naming Conventions

### Dart Files
- **Screens**: `*_screen.dart` (e.g., `login_screen.dart`)
- **Widgets**: `*_widget.dart` or descriptive names (e.g., `transaction_card.dart`)
- **Models**: `*.dart` (e.g., `transaction.dart`)
- **Repositories**: `*_repository.dart` for interfaces, `*_repository_impl.dart` for implementations
- **Providers**: `*_providers.dart` (e.g., `auth_providers.dart`)
- **Services**: `*_service.dart` (e.g., `auth_service.dart`)

### Generated Files
- **JSON Serialization**: `*.g.dart`
- **Freezed Models**: `*.freezed.dart`
- **Riverpod Providers**: `*.g.dart`

### Test Files
- **Unit Tests**: `*_test.dart`
- **Widget Tests**: `*_test.dart`
- **Integration Tests**: `*_integration_test.dart`

## Import Organization

### Import Order (enforced by linter)
1. Dart core libraries
2. Flutter libraries
3. Third-party packages
4. Local project imports (relative imports prohibited)

```dart
// ✅ Correct import order
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/providers/transaction_providers.dart';
```

## Code Generation Files

### Location and Patterns
- Generated files are co-located with their source files
- Always excluded from version control via `.gitignore`
- Regenerated via `build_runner` commands

### Common Generated Files
```
lib/data/models/
├── transaction.dart              # Source model
├── transaction.freezed.dart      # Generated Freezed code
└── transaction.g.dart            # Generated JSON serialization

lib/providers/
├── repository_providers.dart     # Source providers
└── repository_providers.g.dart   # Generated Riverpod code
```

## Environment-Specific Files

### Firebase Configuration
```
lib/
├── firebase_options_dev.dart     # Development Firebase config
├── firebase_options_staging.dart # Staging Firebase config
└── firebase_options_prod.dart    # Production Firebase config
```

### Platform Configuration
```
android/app/
├── google-services.json          # Production
├── dev.google-services.json      # Development
└── staging.google-services.json  # Staging

ios/Runner/
├── GoogleService-Info.plist      # Production
├── dev.GoogleService-Info.plist  # Development
└── staging.GoogleService-Info.plist # Staging
```

## Best Practices

### 1. Feature Organization
- Keep features self-contained and independent
- Shared components go in `lib/widgets/common/`
- Feature-specific widgets stay within the feature module

### 2. Import Management
- Use absolute imports (package imports) only
- Organize imports by the enforced order
- Use IDE auto-import features

### 3. File Structure
- Mirror test structure with lib structure
- Keep related files together (models with their tests)
- Use descriptive, consistent naming

### 4. Documentation
- Document architectural decisions in `docs/`
- Keep README files updated
- Use inline documentation for complex logic

### 5. Code Generation
- Run `build_runner` after model changes
- Keep generated files excluded from version control
- Use watch mode during active development

This structure supports the app's feature-based architecture while maintaining clear separation of concerns and enabling easy testing, maintenance, and feature development.