## Memory Bank
Use mcp-memory-bank MCP for Memory Bank.
When asking to read memory bank, use `get_memory_bank_structure` tool from mcp-memory-bank
Memory bank files are located in project's `.taskmaster/memory-bank/` directory
Instructions are in .taskmaster/memory-bank/memory_bank_instructions.md
Don't use ReadMcpResourceTool for memory bank files 
Continue using mcp-memory-bank tools for structure and analysis 
Always use LS tool first to verify file existence
Fall back to direct file reading if MCP resource access fails