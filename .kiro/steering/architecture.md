# Architecture & Design Patterns

## Repository Pattern (STRICTLY ENFORCED)

### Core Principle
**NEVER access Firebase services directly from UI or business logic**. All Firebase interactions MUST go through repository interfaces.

### Forbidden Patterns
```dart
// ❌ NEVER DO THIS
FirebaseAuth.instance.currentUser
FirebaseFirestore.instance.collection('users')
FirebaseAuth.instance.signOut()
```

### Required Patterns
```dart
// ✅ ALWAYS DO THIS
final authService = ref.read(authServiceProvider);
final userRepo = ref.read(userRepositoryProvider);
final accountRepo = ref.read(accountRepositoryProvider);
```

### Repository Structure
- **Interface Definition**: `lib/features/*/domain/repositories/i_*_repository.dart`
- **Implementation**: `lib/features/*/data/repositories/*_repository.dart`
- **Provider Registration**: `lib/features/*/data/providers/repository_providers.dart`

### Service Abstractions
For cross-cutting concerns, create service interfaces:
- `IFirebaseConnectivityService` for connection status
- `IAuthService` for authentication operations
- `ISecureStorageService` for sensitive data

## State Management with Riverpod

### Provider Patterns
```dart
// AsyncNotifier for CRUD operations
@riverpod
class AccountNotifier extends _$AccountNotifier {
  @override
  Future<List<Account>> build() async {
    final repository = ref.read(accountRepositoryProvider);
    return repository.getAccounts();
  }
}

// Simple provider for computed values
@riverpod
String displayCurrency(DisplayCurrencyRef ref) {
  final settings = ref.watch(userSettingsProvider);
  return settings.preferredCurrency;
}
```

### State Management Rules
- Use `AsyncNotifier` for data that requires loading states
- Use simple `@riverpod` functions for computed values
- Always handle loading, error, and success states in UI
- Use `ref.read()` in event handlers, `ref.watch()` in build methods

## Generic Form System

### Form Configuration Pattern
```dart
// Define form configuration
class AccountFormConfig extends GenericFormConfig<Account> {
  @override
  List<FormFieldConfig> get fields => [
    TextFormFieldConfig(
      key: 'name',
      label: 'Account Name',
      validator: ValidationHelpers.validateRequired,
    ),
    ColorPickerFieldConfig(
      key: 'color',
      label: 'Account Color',
      availableColors: AccountConstants.availableColors,
    ),
  ];
}
```

### Form Implementation Rules
- Use `GenericFormScreen<T>` for all entity forms
- Define configurations in `*FormConfig` classes
- Leverage `FormFieldFactory` for consistent field creation
- Maintain type safety with proper generic constraints

## Data Models with Freezed

### Model Structure
```dart
@freezed
class Account with _$Account {
  const factory Account({
    required String id,
    required String name,
    required String userId,
    required AccountType type,
    required int currentBalanceCents,
    @Default(1) int schemaVersion,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _Account;

  factory Account.fromJson(Map<String, dynamic> json) => _$AccountFromJson(json);
}
```

### Model Rules
- All models MUST be immutable using `@freezed`
- Include `schemaVersion` field (currently version 1)
- Use `*Cents` suffix for monetary amounts (stored as integers)
- Include `userId` for user data isolation
- Provide `fromJson`/`toJson` for Firestore serialization

## Firebase Integration

### Security Rules Compliance
- All data MUST be stored under `/users/{userId}/` subcollections
- Validate user ownership in all operations
- Use atomic transactions for financial operations
- Implement comprehensive field validation

### Firestore Patterns
```dart
// Correct collection reference
final userDoc = _firestore.collection('users').doc(userId);
final accountsRef = userDoc.collection('accounts');

// Atomic transaction for balance updates
await _firestore.runTransaction((transaction) async {
  // Update account balances atomically
  transaction.update(fromAccountRef, {'currentBalanceCents': newFromBalance});
  transaction.update(toAccountRef, {'currentBalanceCents': newToBalance});
  transaction.set(transactionRef, transactionData);
});
```

## Error Handling

### Comprehensive Error Service
```dart
// Use centralized error handling
final errorService = ref.read(errorServiceProvider);
try {
  await repository.createAccount(account);
} catch (e) {
  errorService.handleError(e, context);
}
```

### Error Handling Rules
- Use `ErrorService` for consistent error messages
- Provide user-friendly error messages
- Log technical details for debugging
- Handle offline scenarios gracefully
- Show loading states during operations

## Testing Architecture

### Testing Patterns by Layer
- **Repository Tests**: Use `FirebaseTestSetup` with firebase_auth_mocks + fake_cloud_firestore
- **Service Tests**: Use mocktail for dependency mocking
- **Widget Tests**: Use `MockProviders` with firebase_auth_mocks integration
- **Integration Tests**: Use `FirebaseTestSetup` for end-to-end flows

### Test Structure
```dart
// Repository test pattern
void main() {
  group('AccountRepository', () {
    late FirebaseTestSetup testSetup;
    late AccountRepository repository;

    setUp(() async {
      testSetup = FirebaseTestSetup();
      await testSetup.setUp(signedIn: true);
      repository = AccountRepository(testSetup.firestore);
    });

    tearDown(() => testSetup.tearDown());
  });
}
```

## Code Quality Standards

### Static Analysis Compliance
- Follow flutter_lints + 50+ additional rules
- Address critical errors immediately
- Use `dart fix --apply` for automated fixes
- Maintain clean `flutter analyze` status

### Code Organization
- Feature-based directory structure
- Separate domain, data, and presentation layers
- Group related functionality in modules
- Use barrel exports for clean imports

## Material 3 Design System

### UI Component Standards
- Use `AppText` for all text with automatic overflow handling
- Implement Material 3 floating labels on all forms
- Follow design token system for consistent styling
- Ensure accessibility compliance (WCAG AA)

### Component Patterns
```dart
// Use enhanced form components
AppTextFormField(
  label: 'Account Name',
  useFloatingLabel: true, // Material 3 default
  validator: ValidationHelpers.validateRequired,
)

// Use smart text handling
AppText(
  'Transaction Description',
  style: context.textTheme.bodyLarge,
  // Automatic overflow handling based on context
)
```