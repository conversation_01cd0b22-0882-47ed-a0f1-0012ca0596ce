# Development Workflow & Standards

## Daily Development Process

### 1. Environment Setup
```bash
# Start development (uses dev environment by default)
flutter run

# For specific environments
flutter run --flavor staging -t lib/main_staging.dart
flutter run --flavor prod -t lib/main_prod.dart
```

### 2. Code Quality Checks (Pre-commit)
```bash
# Required workflow before any commit
flutter analyze && dart format --set-exit-if-changed . && flutter test
```

### 3. Code Generation Workflow
```bash
# After model or provider changes
flutter packages pub run build_runner build --delete-conflicting-outputs

# For active development (watch mode)
flutter packages pub run build_runner watch --delete-conflicting-outputs
```

## Feature Development Process

### 1. Feature Planning
- Review memory bank files for context
- Check existing patterns in similar features
- Plan repository interfaces first
- Design data models with Freezed
- Plan provider structure with Riverpod

### 2. Implementation Order
1. **Data Models** - Define Freezed models with proper schema versioning
2. **Repository Interface** - Abstract contract in `domain/repositories/`
3. **Repository Implementation** - Concrete Firebase implementation
4. **Providers** - Riverpod providers for state management
5. **UI Components** - Screens and widgets
6. **Tests** - Comprehensive test coverage
7. **Documentation** - Update relevant docs

### 3. Testing Strategy
- **Repository Tests**: Use `FirebaseTestSetup` with firebase_auth_mocks + fake_cloud_firestore
- **Service Tests**: Use mocktail for dependency mocking
- **Widget Tests**: Use `MockProviders` with firebase_auth_mocks integration
- **Integration Tests**: Use `FirebaseTestSetup` for end-to-end flows

## Code Standards

### 1. Static Analysis Compliance
- Follow flutter_lints + 50+ additional rules from analysis_options.yaml
- Address critical errors immediately (currently 5 critical errors to fix)
- Use `dart fix --apply` for automated fixes
- Maintain clean `flutter analyze` status

### 2. Code Organization Rules
- **Feature-based structure** - Organize by business domain, not technical layers
- **Repository pattern enforcement** - No direct Firebase access from UI/business logic
- **Consistent naming** - Follow established patterns (*_screen.dart, *_repository.dart, etc.)
- **Import organization** - Dart core → Flutter → Third-party → Local (absolute imports only)

### 3. Documentation Requirements
- Update memory bank files for significant changes
- Document architectural decisions
- Maintain comprehensive README files
- Use inline documentation for complex business logic

## Git Workflow

### 1. Branch Naming
- `feature/description` - New features
- `fix/description` - Bug fixes
- `refactor/description` - Code refactoring
- `docs/description` - Documentation updates

### 2. Commit Standards
- Clear, descriptive commit messages
- Reference task numbers when applicable
- Include breaking changes in commit message
- Ensure all tests pass before committing

### 3. Pull Request Process
- Run full test suite
- Ensure flutter analyze is clean
- Update documentation if needed
- Review memory bank impact

## Environment Management

### 1. Multi-Environment Configuration
- **Dev**: Orange theme, `budapp-dev` Firebase project, debug App Check
- **Staging**: Blue theme, `budapp-staging-1` Firebase project, debug App Check
- **Production**: Green theme, `budapp-prod` Firebase project, secure attestation

### 2. Firebase Configuration
```bash
# Start emulators for local development
firebase emulators:start

# Test with emulators
flutter test --dart-define=USE_FIREBASE_EMULATOR=true
```

### 3. Build Process
```bash
# Development builds
flutter build apk --flavor dev -t lib/main_dev.dart

# Production builds
flutter build apk --flavor prod -t lib/main_prod.dart
```

## Quality Assurance

### 1. Performance Standards
- Cold start time < 2 seconds
- UI responsiveness at 60fps
- Efficient memory usage
- Proper offline functionality

### 2. Security Standards
- All data under `/users/{userId}/` subcollections
- Comprehensive Firestore Security Rules
- No sensitive data in logs
- Proper authentication state management

### 3. Testing Standards
- Maintain 554+ tests passing
- Test coverage for new features
- Integration tests for critical flows
- Security rules testing where applicable

## Troubleshooting Common Issues

### 1. Build Issues
```bash
# Clean and regenerate
flutter clean && flutter pub get
flutter packages pub run build_runner clean
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### 2. Firebase Connection Issues
- Verify correct `google-services.json`/`GoogleService-Info.plist` files
- Check Firebase project configuration in `lib/firebase_options_*.dart`
- Ensure emulators are running for local development

### 3. State Management Issues
- Use `ref.read()` in event handlers
- Use `ref.watch()` in build methods
- Handle loading, error, and success states properly
- Follow AsyncNotifier patterns for CRUD operations

## Release Process

### 1. Pre-release Checklist
- [ ] All tests passing (554+ tests)
- [ ] Flutter analyze clean
- [ ] Performance benchmarks met
- [ ] Security review completed
- [ ] Documentation updated

### 2. Environment Progression
1. **Development** - Feature development and testing
2. **Staging** - Pre-production validation
3. **Production** - Live release

### 3. Post-release
- Monitor Firebase Analytics and Crashlytics
- Review performance metrics
- Address any critical issues immediately
- Update memory bank with lessons learned