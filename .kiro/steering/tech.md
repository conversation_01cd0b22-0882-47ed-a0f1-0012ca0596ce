# Technology Stack & Build System

## Core Technologies

### Frontend Framework
- **Flutter**: Cross-platform mobile framework with Dart 3.8.1+
- **Material 3**: Design system with comprehensive design tokens
- **Riverpod 2.6.1**: State management with AsyncNotifier pattern
- **go_router 12.1.0**: Declarative routing with authentication guards
- **freezed 2.5.7**: Immutable data models with JSON serialization

### Backend & Services
- **Firebase Core**: Multi-environment project configuration
- **Firebase Auth 5.3.3**: Authentication with email/password and OAuth
- **Cloud Firestore 5.6.0**: NoSQL database with offline persistence
- **Firebase Remote Config 5.3.0**: Feature flags and app configuration
- **Firebase App Check 0.3.1+3**: Enhanced security and request verification
- **Firebase Analytics & Performance**: Monitoring and insights

### Development Tools
- **riverpod_generator 2.6.2**: Code generation for providers
- **json_serializable 6.8.0**: JSON serialization for data models
- **build_runner 2.4.13**: Code generation orchestration
- **very_good_analysis 7.0.0**: Comprehensive static analysis rules

### Testing Framework
- **flutter_test**: Core testing framework
- **mocktail 1.0.4**: Mocking and stubbing for tests
- **fake_cloud_firestore 3.0.3**: Firebase testing with real behavior simulation
- **firebase_auth_mocks 0.14.2**: Authentication testing utilities

## Build System & Environment Management

### Multi-Environment Setup
```bash
# Development (default)
flutter run

# Staging
flutter run --flavor staging -t lib/main_staging.dart

# Production  
flutter run --flavor prod -t lib/main_prod.dart
```

### Environment Configuration
- **Dev**: Orange theme, `budapp-dev` Firebase project, debug App Check
- **Staging**: Blue theme, `budapp-staging-1` Firebase project, debug App Check  
- **Production**: Green theme, `budapp-prod` Firebase project, secure attestation

### Build Commands
```bash
# Development builds
flutter build apk --flavor dev -t lib/main_dev.dart
flutter build ios --flavor dev -t lib/main_dev.dart

# Production builds
flutter build apk --flavor prod -t lib/main_prod.dart
flutter build ios --flavor prod -t lib/main_prod.dart
```

## Code Generation

### Required Generation Commands
```bash
# Generate all code (run after model/provider changes)
flutter packages pub run build_runner build --delete-conflicting-outputs

# Watch mode for development
flutter packages pub run build_runner watch --delete-conflicting-outputs

# Clean generated files
flutter packages pub run build_runner clean
```

### Generated File Patterns
- `*.g.dart`: JSON serialization code
- `*.freezed.dart`: Freezed model implementations  
- `*.gr.dart`: Router generation (if used)
- `repository_providers.g.dart`: Riverpod provider generation

## Testing Commands

### Test Execution
```bash
# Run all tests
flutter test

# Run tests with coverage
flutter test --coverage

# Run specific test file
flutter test test/features/auth/auth_test.dart

# Run integration tests
flutter test integration_test/
```

### Firebase Testing
```bash
# Start Firebase emulators for testing
firebase emulators:start

# Run tests with emulators
flutter test --dart-define=USE_FIREBASE_EMULATOR=true
```

## Quality Assurance

### Static Analysis
```bash
# Check code quality
flutter analyze

# Format code
dart format .

# Fix auto-fixable issues
dart fix --apply
```

### Pre-commit Quality Checks
```bash
# Recommended pre-commit workflow
flutter analyze && dart format --set-exit-if-changed . && flutter test
```

## Firebase Configuration

### Emulator Setup
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Start emulators (configured in firebase.json)
firebase emulators:start

# Emulator ports:
# - Auth: 9099
# - Firestore: 8080  
# - UI: 4000
```

### Security Rules Testing
```bash
# Test Firestore security rules
firebase emulators:exec --only firestore "npm test" --project=budapp-dev
```

## Package Management

### Dependency Updates
```bash
# Check for outdated packages
flutter pub outdated

# Update dependencies
flutter pub upgrade

# Get dependencies after pubspec changes
flutter pub get
```

### Key Dependencies to Monitor
- **firebase_***: Keep Firebase packages in sync
- **riverpod**: State management core
- **freezed**: Data model generation
- **go_router**: Navigation framework

## Platform-Specific Setup

### Android
- **Minimum SDK**: 21
- **Target SDK**: Latest stable
- **Signing**: Environment-specific keystores (dev/staging/prod)
- **Flavors**: Configured in `android/app/build.gradle.kts`

### iOS  
- **Minimum Version**: iOS 12.0
- **Schemes**: Dev/Staging/Prod with separate Bundle IDs
- **Signing**: Automatic signing with team provisioning

## Performance & Monitoring

### Build Optimization
- **Code splitting**: Feature-based architecture enables tree shaking
- **Asset optimization**: Compressed images and icons
- **Bundle analysis**: Use `flutter build apk --analyze-size`

### Monitoring Setup
- **Firebase Performance**: Automatic performance tracking
- **Firebase Analytics**: User behavior and app usage
- **Crashlytics**: Error reporting and crash analysis

## Development Workflow

### Daily Development
1. `flutter run` (uses dev environment by default)
2. Hot reload with `r` for quick iterations
3. Hot restart with `R` for state reset
4. `flutter analyze` before committing

### Release Workflow
1. Test in staging environment
2. Run full test suite
3. Build production artifacts
4. Deploy with environment-specific configurations

## Common Issues & Solutions

### Build Issues
```bash
# Clean build artifacts
flutter clean && flutter pub get

# Regenerate code
flutter packages pub run build_runner clean
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### Firebase Connection Issues
- Verify correct `google-services.json`/`GoogleService-Info.plist` files
- Check Firebase project configuration in `lib/firebase_options_*.dart`
- Ensure emulators are running for local development