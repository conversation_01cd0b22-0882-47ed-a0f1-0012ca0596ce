{"enabled": true, "name": "Code Quality Analyzer", "description": "Listen to source code files in the repository. When changes are detected, analyze the modified code for potential improvements, including code smells, design patterns, and best practices. Generate suggestions for improving code quality while maintaining the existing functionality. Focus on readability, maintainability, and performance optimizations.", "version": "1", "when": {"type": "userTriggered", "patterns": ["lib/**/*.dart", "test/**/*.dart", "android/**/*.kt", "android/**/*.java", "ios/**/*.swift", "ios/**/*.m", "ios/**/*.h"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified code files for potential improvements. Focus on:\n\n1. **Code Smells & Anti-patterns**:\n   - Long methods/classes that should be refactored\n   - Duplicate code that could be extracted\n   - Complex conditional logic that could be simplified\n   - Inappropriate coupling between components\n\n2. **Architecture & Design Patterns**:\n   - Repository pattern compliance (no direct Firebase access)\n   - Proper use of Riverpod state management patterns\n   - Freezed model implementation best practices\n   - Generic form system usage opportunities\n\n3. **Flutter/Dart Best Practices**:\n   - Widget composition vs inheritance\n   - Proper use of const constructors\n   - Efficient build methods\n   - Memory leak prevention\n   - Performance optimizations\n\n4. **Project-Specific Standards**:\n   - Feature-based architecture compliance\n   - Proper error handling with ErrorService\n   - Security rules compliance for Firebase operations\n   - Material 3 design system usage\n   - Testing patterns and coverage\n\n5. **Code Quality Improvements**:\n   - Variable/method naming clarity\n   - Documentation and comments\n   - Type safety enhancements\n   - Null safety best practices\n\nFor each suggestion, provide:\n- **Issue**: What the problem is\n- **Impact**: Why it matters (readability, performance, maintainability)\n- **Solution**: Specific code improvement with examples\n- **Priority**: High/Medium/Low based on impact\n\nFocus on actionable improvements that align with the project's architecture and coding standards. Prioritize suggestions that improve maintainability, performance, and code clarity."}}