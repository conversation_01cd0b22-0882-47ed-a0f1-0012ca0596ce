{"enabled": true, "name": "Flutter Test & Analyze", "description": "Automatically runs flutter test to fix failing tests, flutter analyze to fix issues, and dart format when Dart files are modified", "version": "1", "when": {"type": "userTriggered", "patterns": ["lib/**/*.dart", "test/**/*.dart", "pubspec.yaml", "analysis_options.yaml"]}, "then": {"type": "askAgent", "prompt": "Run \"flutter test\" and fix all failing tests by improving the codebase (not by modifying tests). Then run \"flutter analyze\" and fix all static analysis issues. Finally run \"dart format .\" to format the code. The goal is to have no failing tests, no analyze issues, and properly formatted code."}}