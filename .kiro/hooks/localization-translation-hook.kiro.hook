{"enabled": true, "name": "Localization Translation Monitor", "description": "Monitor changes to files containing user-facing text content (such as .json, .yaml, or other localization files) in the primary language. When changes are detected, automatically identify the new or modified text and generate translations for all configured target languages. Ensure translations maintain proper context and meaning while adhering to locale-specific conventions.", "version": "1", "when": {"type": "userTriggered", "patterns": ["lib/l10n/app_en.arb", "l10n.yaml", "lib/l10n/app_localizations.dart", "lib/l10n/app_localizations_en.dart"]}, "then": {"type": "askAgent", "prompt": "A localization file has been modified. Please analyze the changes and:\n\n1. Identify any new or modified text strings in the primary language (English)\n2. Generate appropriate translations for all configured target languages\n3. Ensure translations maintain proper context and meaning\n4. Follow locale-specific conventions (date formats, currency, etc.)\n5. Update the corresponding .arb files for each target language\n6. Regenerate localization code if needed using `flutter gen-l10n`\n7. Verify that all translation keys are properly referenced in the codebase\n\nFocus on maintaining consistency with existing translation patterns and ensuring cultural appropriateness for each target locale."}}