{"enabled": true, "name": "Documentation Sync", "description": "Monitors all Dart source files and related configuration files for changes, then prompts the agent to update documentation in README.md or the docs/ folder to keep documentation in sync with code changes", "version": "1", "when": {"type": "userTriggered", "patterns": ["lib/**/*.dart", "test/**/*.dart", "pubspec.yaml", "analysis_options.yaml", "firebase.json", "firestore.rules", "android/app/build.gradle.kts", "ios/Runner/Info.plist"]}, "then": {"type": "askAgent", "prompt": "Source files have been modified in this Flutter/Dart project. Please review the changes and update the documentation accordingly. Focus on:\n\n1. **README.md updates** - Update setup instructions, feature lists, or usage examples if core functionality changed\n2. **docs/ folder updates** - Update relevant technical documentation files if architecture, APIs, or implementation patterns changed\n3. **Code examples** - Update any code examples in documentation that may now be outdated\n4. **Architecture documentation** - Update if new patterns, services, or major structural changes were introduced\n\nPay special attention to:\n- New features or removed features that should be reflected in user-facing documentation\n- Changes to setup/installation procedures\n- API changes that affect how developers use the codebase\n- New dependencies or configuration changes\n- Security or authentication changes\n\nPlease analyze the changed files and determine what documentation needs updating, then make the appropriate changes to keep docs synchronized with the codebase."}}