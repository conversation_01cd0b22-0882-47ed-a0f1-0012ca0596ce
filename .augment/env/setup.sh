#!/bin/bash

# BudApp Flutter Development Environment Setup
set -e

echo "🚀 Setting up BudApp Flutter development environment..."

# Update system packages
sudo apt-get update -y

# Install required system dependencies
sudo apt-get install -y \
    curl \
    git \
    unzip \
    xz-utils \
    zip \
    libglu1-mesa \
    openjdk-17-jdk \
    wget \
    gnupg \
    lsb-release

# Set JAVA_HOME
export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
echo 'export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64' >> $HOME/.profile

# Install Node.js (required for Firebase testing)
# Download and verify Node.js setup script for security
NODEJS_SETUP_SCRIPT="/tmp/nodejs_setup.sh"
curl -fsSL https://deb.nodesource.com/setup_18.x -o "$NODEJS_SETUP_SCRIPT"

# Verify the script is from NodeSource (basic verification)
if ! grep -q "NodeSource" "$NODEJS_SETUP_SCRIPT"; then
    echo "❌ Error: Downloaded script doesn't appear to be from NodeSource"
    rm -f "$NODEJS_SETUP_SCRIPT"
    exit 1
fi

# Execute the verified script
sudo -E bash "$NODEJS_SETUP_SCRIPT"
rm -f "$NODEJS_SETUP_SCRIPT"
sudo apt-get install -y nodejs

# Install Firebase CLI globally
sudo npm install -g firebase-tools

# Install Flutter SDK
cd $HOME
if [ ! -d "flutter" ]; then
    echo "📱 Installing Flutter SDK..."
    wget -O flutter.tar.xz https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.32.6-stable.tar.xz
    tar xf flutter.tar.xz
    rm flutter.tar.xz
fi

# Add Flutter to PATH
export PATH="$HOME/flutter/bin:$PATH"
echo 'export PATH="$HOME/flutter/bin:$PATH"' >> $HOME/.profile

# Configure Flutter
flutter config --no-analytics
flutter doctor --android-licenses || true

# Navigate to project directory
cd /mnt/persist/workspace

# Get Flutter dependencies
echo "📦 Installing Flutter dependencies..."
flutter pub get

# Run code generation (required for Freezed models and Riverpod providers)
echo "🔧 Running code generation..."
dart run build_runner build --delete-conflicting-outputs

# Install Firebase test dependencies
echo "🔥 Setting up Firebase test environment..."
cd firebase/test
npm install
cd ../..

# Verify Flutter installation
echo "✅ Verifying Flutter installation..."
flutter doctor

echo "🎉 Setup complete! Ready to run tests."