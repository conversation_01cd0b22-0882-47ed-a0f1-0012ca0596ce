# Gradle Performance Optimizations
org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true

# Android Configuration
android.useAndroidX=true
android.enableJetifier=true

# Build Performance
android.enableR8.fullMode=true

# Android Signing Configuration
# Note: Actual values should be set in local.properties or environment variables
# Never commit actual passwords or keystore paths to version control

# Development Environment Signing (for local development)
# NEVER commit actual passwords here - use local.properties instead
# DEV_KEYSTORE_FILE=keystore/dev-release.jks
# DEV_KEY_ALIAS=dev-key
# DEV_KEYSTORE_PASSWORD=<set-in-local.properties>
# DEV_KEY_PASSWORD=<set-in-local.properties>

# Staging Environment Signing
# STAGING_KEYSTORE_FILE=keystore/staging-release.jks
# STAGING_KEY_ALIAS=staging-key
# STAGING_KEYSTORE_PASSWORD=<your-staging-keystore-password>
# STAGING_KEY_PASSWORD=<your-staging-key-password>

# Production Environment Signing
# PROD_KEYSTORE_FILE=keystore/prod-release.jks
# PROD_KEY_ALIAS=prod-key
# PROD_KEYSTORE_PASSWORD=<your-prod-keystore-password>
# PROD_KEY_PASSWORD=<your-prod-key-password>

# For CI/CD: These values are provided via environment variables from GitHub Secrets
