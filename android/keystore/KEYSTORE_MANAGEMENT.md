# Android Keystore Management for BudApp

## Overview
This directory contains Android signing keystores for the BudApp project. **NEVER commit keystore files to version control.**

## Security Requirements
- All keystore files are excluded from Git via `.gitignore`
- Keystores are stored securely in CI/CD secrets using base64 encoding
- Each environment (dev, staging, prod) has its own keystore
- Strong passwords must be used for all keystores

## Keystore Structure
```
android/keystore/
├── .gitignore                 # Excludes all keystores from Git
├── KEYSTORE_MANAGEMENT.md     # This documentation
├── dev-release.jks           # Development environment keystore (NOT committed)
├── staging-release.jks       # Staging environment keystore (NOT committed)
└── prod-release.jks          # Production environment keystore (NOT committed)
```

## Environment Configuration

### Development Keystore
- **File**: `dev-release.jks`
- **Alias**: `dev-key`
- **Used for**: Development builds and internal testing

### Staging Keystore
- **File**: `staging-release.jks`
- **Alias**: `staging-key`  
- **Used for**: Pre-production testing and QA

### Production Keystore
- **File**: `prod-release.jks`
- **Alias**: `prod-key`
- **Used for**: App Store releases and production distribution

## Generating Keystores

Use the provided script to generate new keystores:
```bash
# From project root
./scripts/generate_keystore.sh <environment>
```

Or manually using keytool:
```bash
keytool -genkey -v \
  -keystore android/keystore/<env>-release.jks \
  -keyalg RSA \
  -keysize 2048 \
  -validity 10000 \
  -alias <env>-key
```

## Local Development Setup

1. **Generate/Obtain Keystores**: Ensure you have the required keystore files
2. **Configure gradle.properties**: Add signing configuration (see template below)
3. **Test Build**: Verify builds work with new signing configuration

### gradle.properties Template
```properties
# Android Signing Configuration
# Development Environment
DEV_KEYSTORE_FILE=keystore/dev-release.jks
DEV_KEY_ALIAS=dev-key
DEV_KEYSTORE_PASSWORD=<secure-password>
DEV_KEY_PASSWORD=<secure-password>

# Staging Environment  
STAGING_KEYSTORE_FILE=keystore/staging-release.jks
STAGING_KEY_ALIAS=staging-key
STAGING_KEYSTORE_PASSWORD=<secure-password>
STAGING_KEY_PASSWORD=<secure-password>

# Production Environment
PROD_KEYSTORE_FILE=keystore/prod-release.jks
PROD_KEY_ALIAS=prod-key
PROD_KEYSTORE_PASSWORD=<secure-password>
PROD_KEY_PASSWORD=<secure-password>
```

## CI/CD Integration

### GitHub Secrets Required
For each environment, add these secrets to your GitHub repository:

**Development:**
- `DEV_KEYSTORE_FILE` - Base64 encoded keystore file
- `DEV_KEY_ALIAS` - Key alias (dev-key)
- `DEV_KEYSTORE_PASSWORD` - Keystore password
- `DEV_KEY_PASSWORD` - Key password

**Staging:**
- `STAGING_KEYSTORE_FILE` - Base64 encoded keystore file  
- `STAGING_KEY_ALIAS` - Key alias (staging-key)
- `STAGING_KEYSTORE_PASSWORD` - Keystore password
- `STAGING_KEY_PASSWORD` - Key password

**Production:**
- `PROD_KEYSTORE_FILE` - Base64 encoded keystore file
- `PROD_KEY_ALIAS` - Key alias (prod-key)  
- `PROD_KEYSTORE_PASSWORD` - Keystore password
- `PROD_KEY_PASSWORD` - Key password

### Base64 Encoding for Secrets
```bash
# Encode keystore for GitHub Secrets
base64 -i android/keystore/dev-release.jks | pbcopy
```

## Security Best Practices

1. **Password Management**
   - Use strong, unique passwords for each keystore
   - Store passwords securely (password manager, secure notes)
   - Never commit passwords to version control

2. **Keystore Backup**
   - Maintain secure backups of all production keystores
   - Store backups in encrypted, offline storage
   - Document backup recovery procedures

3. **Access Control**
   - Limit keystore access to authorized team members only
   - Use separate keystores for different environments
   - Regularly rotate keystores for enhanced security

4. **Monitoring**
   - Monitor CI/CD logs for signing failures
   - Verify APK signatures before distribution
   - Alert on any unauthorized signing attempts

## Troubleshooting

### Common Issues
1. **Build fails with signing error**: Check keystore path and passwords
2. **CI/CD signing fails**: Verify GitHub Secrets are correctly encoded
3. **Wrong signature**: Ensure correct keystore is being used for environment

### Validation Commands
```bash
# Verify APK signature
jarsigner -verify -verbose -certs app-release.apk

# Check keystore information  
keytool -list -v -keystore android/keystore/dev-release.jks

# Test build with specific flavor
./gradlew assembleDevRelease
```

## Emergency Procedures

### Keystore Compromise
1. Immediately revoke compromised keystore
2. Generate new keystore with different passwords
3. Update all CI/CD secrets
4. Rebuild and redistribute affected applications
5. Document incident for security review

### Lost Keystore
1. Check secure backups first
2. If no backup available, generate new keystore
3. Note: Apps signed with lost keystore cannot be updated in app stores
4. May require new app bundle and user migration

## Contact Information
For keystore-related issues or security concerns, contact the development team lead or security officer immediately.

---
**Last Updated**: 2025-07-13
**Version**: 1.0.0