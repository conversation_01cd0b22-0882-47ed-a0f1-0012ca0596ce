# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.

# Keep Firebase classes
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }

# Suppress warnings for Google Play Services (emulator-specific issues)
-dontwarn com.google.android.gms.**
-dontwarn com.google.firebase.**

# Keep Flutter and Dart classes
-keep class io.flutter.** { *; }
-keep class androidx.** { *; }

# Suppress warnings for missing Google Play Services modules in emulator
-dontwarn com.google.android.gms.common.**
-dontwarn com.google.android.gms.security.ProviderInstaller

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep Firebase Auth and Firestore specific classes
-keep class com.google.firebase.auth.** { *; }
-keep class com.google.firebase.firestore.** { *; }

# Suppress specific emulator warnings
-dontwarn com.google.android.gms.dynamite.**
-dontwarn com.google.android.gms.phenotype.**

# Google Play Core (for Flutter deferred components)
-dontwarn com.google.android.play.core.**
-keep class com.google.android.play.core.** { *; }

# Flutter deferred components
-keep class io.flutter.embedding.engine.deferredcomponents.** { *; }
-dontwarn io.flutter.embedding.engine.deferredcomponents.**
