<?xml version="1.0" encoding="utf-8"?>
<!-- Log configuration to reduce noise in development builds -->
<configuration>
    <!-- Suppress Google Play Services warnings in emulator -->
    <logger name="DynamiteModule" level="ERROR" />
    <logger name="ProviderInstaller" level="ERROR" />
    <logger name="GoogleApiManager" level="ERROR" />
    <logger name="FlagRegistrar" level="ERROR" />
    
    <!-- Keep Firebase logs (these are useful) -->
    <logger name="firebase" level="DEBUG" />
    <logger name="flutter" level="DEBUG" />
    
    <!-- Reduce system noise -->
    <logger name="ApplicationLoaders" level="WARN" />
    <logger name="nativeloader" level="WARN" />
    <logger name="hiddenapi" level="WARN" />
</configuration>
