import java.util.Properties

plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
    // Firebase Google Services plugin
    id("com.google.gms.google-services")
}

android {
    namespace = "com.digitau.budapp"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.digitau.budapp"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 23  // Required for Firebase Auth
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        create("dev") {
            // Read from local.properties (for local development) or environment variables (for CI/CD)
            val localProperties = Properties()
            val localPropertiesFile = rootProject.file("local.properties")
            if (localPropertiesFile.exists()) {
                localPropertiesFile.inputStream().use { localProperties.load(it) }
            }
            
            keyAlias = localProperties.getProperty("DEV_KEY_ALIAS") ?: System.getenv("DEV_KEY_ALIAS")
            keyPassword = localProperties.getProperty("DEV_KEY_PASSWORD") ?: System.getenv("DEV_KEY_PASSWORD")
            storeFile = localProperties.getProperty("DEV_KEYSTORE_FILE")?.let { file("../${it}") } 
                ?: System.getenv("DEV_KEYSTORE_FILE")?.let { file(it) }
            storePassword = localProperties.getProperty("DEV_KEYSTORE_PASSWORD") ?: System.getenv("DEV_KEYSTORE_PASSWORD")
        }
        create("staging") {
            val localProperties = Properties()
            val localPropertiesFile = rootProject.file("local.properties")
            if (localPropertiesFile.exists()) {
                localPropertiesFile.inputStream().use { localProperties.load(it) }
            }
            
            keyAlias = localProperties.getProperty("STAGING_KEY_ALIAS") ?: System.getenv("STAGING_KEY_ALIAS")
            keyPassword = localProperties.getProperty("STAGING_KEY_PASSWORD") ?: System.getenv("STAGING_KEY_PASSWORD")
            storeFile = localProperties.getProperty("STAGING_KEYSTORE_FILE")?.let { file("../${it}") } 
                ?: System.getenv("STAGING_KEYSTORE_FILE")?.let { file(it) }
            storePassword = localProperties.getProperty("STAGING_KEYSTORE_PASSWORD") ?: System.getenv("STAGING_KEYSTORE_PASSWORD")
        }
        create("prod") {
            val localProperties = Properties()
            val localPropertiesFile = rootProject.file("local.properties")
            if (localPropertiesFile.exists()) {
                localPropertiesFile.inputStream().use { localProperties.load(it) }
            }
            
            keyAlias = localProperties.getProperty("PROD_KEY_ALIAS") ?: System.getenv("PROD_KEY_ALIAS")
            keyPassword = localProperties.getProperty("PROD_KEY_PASSWORD") ?: System.getenv("PROD_KEY_PASSWORD")
            storeFile = localProperties.getProperty("PROD_KEYSTORE_FILE")?.let { file("../${it}") } 
                ?: System.getenv("PROD_KEYSTORE_FILE")?.let { file(it) }
            storePassword = localProperties.getProperty("PROD_KEYSTORE_PASSWORD") ?: System.getenv("PROD_KEYSTORE_PASSWORD")
        }
    }

    flavorDimensions += "default"
    productFlavors {
        create("dev") {
            dimension = "default"
            applicationIdSuffix = ".dev"
            resValue(
                type = "string",
                name = "app_name",
                value = "BudApp Dev"
            )
            signingConfig = signingConfigs.getByName("dev")
        }
        create("staging") {
            dimension = "default"
            applicationIdSuffix = ".staging"
            resValue(
                type = "string",
                name = "app_name",
                value = "BudApp Staging"
            )
            signingConfig = signingConfigs.getByName("staging")
        }
        create("prod") {
            dimension = "default"
            resValue(
                type = "string",
                name = "app_name",
                value = "BudApp"
            )
            signingConfig = signingConfigs.getByName("prod")
        }
    }



    buildTypes {
        debug {
            // Debug builds use default debug signing
            // No applicationIdSuffix - flavors already provide environment-specific package names
            isMinifyEnabled = false
            isShrinkResources = false
        }
        release {
            // Release builds use environment-specific signing from productFlavors
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            // Signing configuration is handled by productFlavors
            // This ensures no debug keys are ever used in release builds
        }
    }

    // Build performance optimizations
    packagingOptions {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
}

flutter {
    source = "../.."
}


