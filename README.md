# BudApp

A comprehensive personal finance management application built with Flutter and Firebase, designed to empower users to take control of their financial well-being.

## 🚀 Project Status

**✅ Navigation System Refactoring Complete**

The BudApp project has successfully completed a comprehensive navigation system refactoring, transforming from traditional bottom navigation to a modern hub-based navigation with global FAB system. All core patterns and infrastructure are in place:

- **✅ Hub-Based Navigation**: Central home screen with 6 feature cards in responsive 2x3 grid layout
- **✅ Global FAB System**: 3-FAB system with Material 3 design - add transaction (green), back navigation (grey), home navigation (grey)
- **✅ Route Architecture**: Simplified go_router configuration with top-level routes replacing complex ShellRoute system
- **✅ Accounts Enhancement**: Net Worth summary card with real-time assets/liabilities calculation and Material 3 styling
- **✅ Screen Integration**: All major screens updated to use global FAB system with consistent action availability
- **✅ Material 3 Compliance**: Modern design patterns with proper styling, accessibility, and deprecated pattern fixes
- **✅ Feature-Based Architecture**: Complete migration from technical layers to feature modules
- **✅ Modern State Management**: Riverpod with AsyncNotifier and repository pattern
- **✅ Immutable Data Models**: Freezed 3.x implementation with sealed class architecture and JSON serialization
- **✅ Comprehensive Testing**: Tests compile and run successfully with modernized test infrastructure
- **✅ Code Quality**: Clean analyzer, automated formatting, and comprehensive quality gates
- **✅ Dependency Management**: All dependencies updated to latest stable versions (Firebase, Go Router, Build Tools)
- **✅ Secure Storage**: Platform-specific secure storage for sensitive data (biometric keys, API keys)
- **✅ Enhanced Localization**: Comprehensive i18n with 100+ localization keys
- **✅ Centralized Error Handling**: User-friendly error messages across authentication flow
- **✅ Design Token Consistency**: Eliminated hardcoded values with semantic design tokens and global text overflow system
- **✅ Multi-Environment Setup**: Dev/staging/prod with Firebase integration
- **✅ Authentication System**: Complete email/password + Google OAuth implementation
- **✅ Profile Management System**: Complete user profile management with secure password changes and account deletion
- **✅ Enhanced Security Foundation**: Comprehensive Firestore Security Rules with 2024 best practices, input sanitization, financial security validation, business logic validation tests, and edge case boundary testing
- **Category Management**: Complete hierarchical category system with unified user experience, subcategory CRUD operations, deletion constraints, transaction re-assignment, and comprehensive frontend UI

**✅ Account Management Complete**: Full CRUD implementation with Material 3 design, validation, and testing.
**✅ Category Management Complete**: Comprehensive category system with unified user experience, hierarchical support, subcategory CRUD operations, deletion constraints with transaction re-assignment, simplified category model (no source field distinction), repository pattern, complete frontend UI with tree view display and creation/editing forms, and full Firestore integration. All categories treated equally with seamless editing and deletion capabilities. Includes safe deletion with transaction dependency checking, bulk transaction re-assignment functionality, proper `fromFirestore()`/`toFirestore()` methods with Timestamp handling, comprehensive Firestore collection structure documentation, and user-friendly error handling.
**✅ Transaction Database Schema Complete**: Comprehensive database schema documentation with field specifications, validation rules, business logic for transaction types (income/expense/transfer), 8 optimized Firestore composite indexes, security rules, performance considerations, and visual diagrams.
**✅ Transaction UI Components Complete**: Complete transaction form implementation with all required components (TransactionForm, AmountInputField, AccountSelector, CategorySelector, etc.), comprehensive validation, state management with Riverpod providers, 40+ localized strings, and Material 3 design integration. All transaction types (income, expense, transfer) fully operational with dual account selection for transfers, critical bug fixes, Flutter analysis resolution, and comprehensive testing. Transfer transactions support proper source/destination account validation and UI components.
**✅ Transaction Action UI Complete (Task 11.1)**: Complete user interface for transaction editing and deletion actions with enhanced TransactionCard widget featuring action buttons, PopupMenuButton with edit/delete menu items, comprehensive action handling in TransactionsListScreen, localized success messaging, and full testing coverage (371 tests passing).
**✅ Transaction Edit Form/Modal Complete (Task 11.2)**: Complete modal-based transaction editing functionality with TransactionEditModal component featuring Material 3 styling, DraggableScrollableSheet for responsive design, pre-populated form data, comprehensive validation and error handling. Integrated with TransactionsListScreen and TransactionDetailScreen for seamless user experience. All 378 tests passing with modal-based implementation and proper async context safety.
**✅ Atomic Transaction Update Implementation Complete (Task 11.3)**: Complete Firestore transaction update operation with account balance adjustments. Implemented comprehensive atomic transaction update functionality ensuring data consistency with BalanceAdjustment helper class, balance calculation logic for all transaction types (income/expense/transfer), account change handling, and Firestore transaction-based atomic operations. All 378 tests passing with production-ready atomic transaction updates and complete account balance consistency.
**✅ Transaction Editing and Deletion System Complete (Task 11)**: Complete transaction management with enhanced user feedback system. Implemented comprehensive atomic transaction deletion functionality with balance reversal logic for all transaction types, atomic Firestore batch operations, centralized TransactionErrorService for user-friendly error messages, enhanced UI with loading states and success messages, Firestore-specific error handling with retry mechanisms, and comprehensive localization. All 390+ tests passing with production-ready transaction management and excellent user experience.
**✅ Biometric Authentication Complete (Task 4)**: Complete biometric authentication system with Face ID and Fingerprint support. Implemented hardware and software availability detection using local_auth package, secure biometric authentication flow with Firebase Auth integration, biometric preference management with secure storage persistence, **biometric authentication gate for app startup security**, comprehensive error handling with platform-specific error codes and user-friendly messages, specialized UI components (BiometricAuthButton, BiometricErrorBanner, BiometricSettingsTile, BiometricGateScreen), authentication settings screen for biometric enable/disable functionality, **session-based gate logic with router integration**, platform permissions configured for Android (USE_BIOMETRIC, USE_FINGERPRINT) and iOS (Face ID usage description). All 390+ tests passing with production-ready implementation and proper Riverpod state management.
**✅ User Profile Management and Account Recovery Complete (Task 3)**: Complete user profile management system with Firebase Authentication integration. Implemented profile editing (name, email) with real-time validation, secure password change flow with mandatory re-authentication, password reset functionality using Firebase Auth email system, account deletion with multi-step confirmation and comprehensive data cleanup, security features including re-authentication dialogs and password strength indicators, complete UI components (ProfileEditScreen, PasswordChangeScreen, ForgotPasswordScreen, AccountDeletionDialog), seamless GoRouter integration, and GDPR-compliant data removal. Zero static analysis issues with production-ready implementation and comprehensive error handling.
**✅ Tag Management Complete (Task 12)**: Complete tag management system with CRUD operations and transaction associations. Implemented Tag model with Freezed and JSON serialization, TagRepository with full CRUD operations, Riverpod-based TagNotifier with AsyncNotifier pattern, UI components (TagsListScreen with search, TagCreateScreen, TagEditScreen), many-to-many relationships using tagIds array in Transaction model, cascading deletion logic for automatic tag removal from transactions, navigation integration with tags route in navigation drawer and FAB system, comprehensive Firestore security rules with user isolation, and navigation drawer visibility fix by converting screens to CustomScrollView pattern. All 390+ tests passing with production-ready implementation.
**✅ Client-Side Validation Complete**: Comprehensive transaction form validation with TransactionValidators service, localized error messages, real-time validation feedback, transfer-specific validation rules, and complete integration with transaction form state management. All Flutter analyze warnings resolved and test infrastructure updated.
**✅ Firestore Repository for Transaction Creation Complete**: Enhanced transaction repository with account existence validation, type-specific creation methods (income/expense/transfer), comprehensive testing infrastructure, and proper error handling. All transaction types now have robust server-side validation with account ownership verification and business rule enforcement.
**✅ Client-Side Logic for Income/Expense Account Updates Complete**: Implemented atomic balance updates for income and expense transactions using Firestore transactions. Account model enhanced with currentBalanceCents field for real-time balance tracking. All transactions now automatically update account balances with atomic consistency guarantees, comprehensive testing (344 tests passing), and backward compatibility for existing data.
**✅ Current Balance Display Fix Complete**: Resolved critical UI issue where application displayed initial balances instead of current balances throughout the interface. Systematically updated 7 files across presentation layer, providers, and repository implementations to ensure users see accurate real-time financial status. Account cards, detail screens, transaction dropdowns, and summary calculations now correctly display currentBalanceCents. Account edit screen UX improved with clear labeling and explanatory notes. All 362 tests passing with clean code analysis.
**✅ Unified Time-Period-Based Budget Management System Complete**: Complete unified budget management system with time-period integration, comprehensive bulk operations, and transaction-budget integration. Implemented seamless integration with global time period selector, BulkBudgetService with percentage adjustments/status changes/batch deletion, BulkBudgetOperationsDialog with Material 3 design, selection mode UI with multi-select capabilities, Budget model with Freezed and JSON serialization, BudgetRepository with full CRUD operations, BudgetProgressService for real-time tracking, **BudgetTransactionService for automatic budget updates based on transaction activity**, Riverpod providers with AsyncNotifier pattern, complete UI screens (list, edit) with bulk operations support and **inline edit mode functionality**, navigation integration through app drawer, Firestore composite indexes for efficient querying, comprehensive security rules with user data isolation, and complete localization support. **Phase 2-4 Implementation**: Successfully transitioned to edit-only system (Phase 2), implemented comprehensive inline edit mode UI (Phase 3), and completed transaction-budget integration (Phase 4). All 476 tests passing with production-ready implementation featuring powerful bulk operations, unified time period management, and automatic budget tracking.
**✅ Budget Period Management System Complete**: Comprehensive budget period management system with precise period-based filtering, uniqueness constraints, and fallback logic. Implemented `periodStart` field in Budget model (schema v4) for exact period identification, enforced one budget per category per time period at both client and database level with atomic validation, `findLatestBudgetFromPreviousPeriod` method for displaying previous period budgets when none exist for current period, updated Firestore security rules to validate new Budget model structure, added composite indexes for period-based queries, created BudgetMigrationService to populate `periodStart` field for existing budgets, enhanced BudgetValidators and repository validation to use precise period matching with `isForPeriod` method, updated `categoryBudgetInfo` provider with fallback logic and `isFallbackBudget` indicator, and comprehensive testing with dedicated budget period management test suite. All 524 tests passing with production-ready implementation ensuring robust period-specific budget management and data consistency.
**✅ Bottom Navigation System Complete (Task 14)**: Complete custom bottom navigation bar implementation with Material 3 design principles and enhanced user experience. Implemented CustomBottomNavigation with reduced height (64px), teal selection colors, outline icons, special center add button with pulse animation, Categories/Budgets toggle functionality with SharedPreferences persistence, comprehensive Riverpod state management with bottomNavProviders, haptic feedback integration, accessibility compliance, and test environment detection to prevent animation timeout issues. All navigation items integrate seamlessly with GoRouter, toggle state persists across app sessions, and comprehensive testing ensures production-ready implementation. All 390+ tests passing with clean static analysis.
**✅ UI/UX Improvements Complete**: Comprehensive UI/UX enhancements for improved user experience and cleaner interface design. Implemented account card navigation to filtered transaction views, added PopupMenuButton menus to account cards with complete CRUD operations, removed visual clutter from home screen (welcome sections, quick actions, account summaries), relocated development information card from home to profile screen with enhanced Firebase testing functionality, added proper spacing between tabs and category cards, and updated routing with account-specific transaction filtering. All improvements maintain production-ready quality with 390+ tests passing and clean static analysis.
**✅ Card UI Design Improvements Complete**: Advanced card UI design improvements with account-specific iconography and streamlined layouts. Implemented account-specific icons using `iconName` field with 25+ financial icons (bank, investment, asset, generic), replaced container-based icons with large (48px) plain icons without background containers, streamlined transaction cards by removing bottom section with account/category information for cleaner design, maintained all existing functionality (navigation, notes, edit modals, menu actions), and cleaned up unused code. All improvements follow Material 3 design principles with visual consistency across the application. All 390+ tests passing with clean static analysis and production-ready implementation.
**✅ Navigation Refactoring Project Complete**: Comprehensive navigation and UI structure refactoring with enhanced dashboard analytics. Successfully completed all 7 tasks including app drawer removal, navigation link relocation, authentication screen consolidation, profile card enhancements, FAB additions to list screens, enhanced dashboard provider creation, and home screen dashboard implementation. The enhanced dashboard features Current Period Balance Card with monthly income/expense totals, Recent Transactions Card with last 3 transactions display, Top 3 Expenses and Income sections with category analytics, robust icon handling for backward compatibility, and comprehensive technical fixes. All 390+ tests passing with clean static analysis and production-ready implementation featuring modern navigation patterns and valuable financial insights.
**✅ Time Period Modal Redesign Complete**: Complete redesign of TimePeriodModal from list-based to modern grid-based interface with enhanced user experience. Implemented 3x4 month grid layout (Jan-Dec) for intuitive month selection, year navigation with up/down arrow buttons, gradient teal header with current period display, circular selection indicators and current month dots, app-specific color scheme integration (#01A2A1), preserved all existing Riverpod state management and SharedPreferences persistence, maintained period validation and future period prevention, and ensured accessibility compliance with proper touch targets. All tests passing with clean static analysis and successful app compilation, providing users with a modern, touch-friendly interface that aligns with contemporary mobile UI patterns.
**✅ Global Text Overflow System Complete**: Comprehensive text overflow management system with automatic context detection and consistent text handling across the entire application. Implemented AppText widget with context-specific constructors (title, body, critical, note, etc.), TextOverflowConfig class with predefined configurations for different text contexts, automatic style-based context detection, adaptive text support with FittedBox integration, seamless design token integration, and backward compatibility with existing Text widgets. Includes comprehensive documentation, migration guide, extension methods for easy adoption, and complete test coverage. All 450+ tests passing with clean static analysis and production-ready implementation providing consistent text truncation and ellipsis handling throughout the application.
**✅ Hub-Based Navigation System Complete**: Comprehensive navigation system refactoring from traditional bottom navigation to modern hub-based navigation with global FAB system. Implemented hub-style home screen with 6 feature cards in responsive 2x3 grid layout (Accounts, Transactions, Budgets, Goals, Statistics, Settings), global 3-FAB system with Material 3 design featuring add transaction (green), back navigation (grey), and home navigation (grey) FABs with route-based visibility management, simplified go_router architecture by removing complex ShellRoute system and converting to clean top-level routes, enhanced accounts screen with comprehensive Net Worth summary card displaying real-time assets/liabilities calculation, updated all major screens (Transactions, Budgets, Goals, Accounts) to use global FAB system with `.withGlobalFabs()` extension method, fixed all Material 3 compliance issues including deprecated `withOpacity` patterns, and ensured clean analyzer with comprehensive formatting. All tests passing with production-ready implementation providing modern hub-centric navigation with consistent action availability and enhanced user experience across all screens.
**✅ AppBar Architecture Reversal Complete**: Comprehensive AppBar architecture reversal across entire BudApp from SliverAppBar to traditional Scaffold pattern with desired overscroll behavior. Successfully converted all 9 screens from CustomScrollView + SliverAppBar to Scaffold + AppBar + SingleChildScrollView pattern, updated AppBarHelpers factory methods from SliverAppBar to regular AppBar creation (createTimePeriodScrollableAppBar, createStandardScrollableAppBar), systematically migrated widget patterns (SliverToBoxAdapter → direct widgets, SliverFillRemaining → Center/Expanded, SliverList → ListView.builder/Column), achieved desired overscroll behavior where both AppBar and content stretch together, updated all test files to use new method names and testing patterns, maintained all existing functionality including TimePeriodSelector integration and Material 3 design compliance, and ensured quality validation with Flutter analyze clean status. All 524 tests passing with clean static analysis and production-ready implementation providing consistent traditional Scaffold architecture across the entire application.
**✅ Goals Feature Implementation Complete (Task 15)**: Comprehensive goals feature with complete data models, repositories, UI screens, and Firebase integration. Implemented Goal and GoalContribution models with Freezed and JSON serialization, comprehensive repository pattern with full CRUD operations, Riverpod state management with AsyncNotifier pattern, complete UI screens for goal and contribution management with form validation and error handling, production-ready Firestore Security Rules with 31 comprehensive tests covering authentication, authorization, and data validation, successful deployment to development environment with zero warnings, and comprehensive testing with all Flutter and Firebase tests passing. Features include goal creation/editing/deletion, contribution tracking with timestamped progress, real-time updates across devices, offline support, user data isolation, financial data security validation, and proper referential integrity between goals and contributions. All 783+ tests passing with clean static analysis and production-ready implementation providing complete goal tracking functionality.
**✅ Goal Visual Progress Indicators Complete**: Comprehensive visual progress indicator system for financial goals with multiple visualization types and interactive UI components. Implemented GoalProgressBar widget with 3 types (linear, circular, compact), GoalProgressChart widget with advanced visualizations (donut, radial progress, progress ring), and GoalProgressSummary widget for multiple goals display (list, grid, compact, dashboard layouts). Features include Material 3 design compliance, smooth animations, custom goal colors, currency formatting integration, accessibility support, and responsive design. Enhanced existing goal cards with new progress indicators and created comprehensive documentation. All 784 tests passing with clean static analysis and production-ready implementation providing engaging and accessible goal progress visualization.
**✅ Goal Data Model Complete (Task 15.1)**: Comprehensive Firestore Goal Data Model implementation for financial goal tracking feature. Successfully designed and implemented complete Goal model with Freezed and JSON serialization, all required fields (id, name, targetAmountCents, currentAmountCents, targetDate, isCompleted, colorHex, iconName, schemaVersion), GoalStatus enum with proper JSON values (active, paused, completed, cancelled), collection path users/{userId}/goals following BudApp patterns, comprehensive validation system with business rules and format validation, progress calculation methods (progressPercentage, remainingAmountCents, isAchieved, daysRemaining, requiredDailySavingsCents), factory methods for creation and Firestore integration, backward compatibility handling for status migration, complete documentation with schema specifications and security considerations, and comprehensive test suite with 24 test cases covering all functionality. All tests passing with clean static analysis and production-ready implementation providing robust foundation for financial goal tracking with proper validation, security, and performance considerations.
**✅ Goal Contribution Data Model Complete (Task 15.2)**: Comprehensive Firestore GoalContribution Data Model implementation for tracking individual contributions towards financial goals. Successfully designed and implemented complete GoalContribution model with Freezed and JSON serialization, all required fields (id, userId, goalId, amountCents, contributionDate, description), standard BudApp fields (isActive, schemaVersion, createdAt, updatedAt, metadata), subcollection path users/{userId}/goals/{goalId}/contributions following security patterns, comprehensive validation system with 11 business rules covering amount validation (positive, ≤$100M), date validation (not future, ≤10 years ago), description validation (≤500 chars), date helper methods (isMadeToday, isMadeThisWeek, isMadeThisMonth, dateDescription), utility methods (hasDescription, summaryMessage), factory methods for creation and Firestore integration, complete documentation with schema specifications, security rules, indexing strategy, and usage examples, and comprehensive test suite with 27 test cases covering all functionality including edge cases and validation rules. All 51 model tests passing (Goal + GoalContribution) with clean static analysis and production-ready implementation providing robust foundation for goal contribution tracking with proper validation, security, and performance considerations.
**✅ Goal Repository Implementation Complete (Task 15.3)**: Comprehensive Firestore Repository for Goal Management with full CRUD operations, real-time streams, and Riverpod integration. Created IGoalRepository interface with 25+ methods covering all goal management operations including create, read, update, delete, search, validation, progress tracking, and statistics. Implemented GoalRepositoryImpl following established BudApp patterns with FirestoreService and FirebaseAuth integration, proper error handling, user context management, and type-safe operations. Added comprehensive goal management capabilities including goal status management (active, paused, completed, cancelled), automatic progress tracking with currentAmountCents updates, goal completion detection when target is reached, search functionality with name/description filtering, goal statistics and user summary calculations, real-time streams for live updates, validation using Goal model's built-in validation system, uniqueness checking for goal names, and date-based filtering for goals nearing deadlines. Successfully integrated with existing repository provider system, added goalRepositoryProvider to repository_providers.dart, exported IGoalRepository in repositories.dart, and ensured clean Flutter analyze (0 issues) with proper code formatting. Established production-ready foundation for goal management feature with comprehensive CRUD operations, real-time updates, and seamless integration with BudApp's established architectural patterns.

**✅ Goal Contribution Repository Implementation Complete (Task 15.4)**: Complete Firestore Repository for Goal Contribution Management with comprehensive subcollection management and analytics capabilities. Created IGoalContributionRepository interface with 24 methods covering all contribution management operations including CRUD operations, real-time streams, analytics, and validation. Implemented GoalContributionRepositoryImpl following established BudApp patterns with FirestoreService and FirebaseAuth integration, proper error handling, and subcollection path management (users/{userId}/goals/{goalId}/contributions). Added comprehensive contribution management capabilities including CRUD operations with validation, real-time streams for live data updates, goal-specific operations (contributions per goal), user-level operations (contributions across all goals), date-based filtering (getContributionsByDateRange, getRecentContributions, getTodaysContributions), analytics and statistics (total amounts, counts, contribution statistics), and business logic validation using GoalContribution model's built-in validation system. Successfully integrated with existing repository provider system, added goalContributionRepositoryProvider to repository_providers.dart, exported IGoalContributionRepository in repositories.dart, and ensured clean Flutter analyze (0 issues) with proper code formatting. Created comprehensive test suite with 22 passing tests covering all CRUD operations, goal-specific operations, user-level operations, real-time streams, validation, and error handling (2 tests skipped due to fake_cloud_firestore date range query limitations). Established production-ready foundation for goal contribution management feature with comprehensive data access layer, real-time updates, and seamless integration with BudApp's established architectural patterns.

**✅ Goals Feature Implementation Complete (Task 15.5)**: Complete financial goals tracking feature with comprehensive UI implementation and full integration. Successfully implemented complete goals management system including Riverpod providers with AsyncNotifier pattern for goal creation, updates, and deletion, GoalFormConfig integration with generic form system for consistent user experience, comprehensive UI screens (GoalsListScreen with Material 3 design, goal cards with progress indicators, status chips, and menu actions; GoalCreateScreen and GoalEditScreen with full-screen navigation), app router integration with proper route definitions (/goals, /goals/create, /goals/:id/edit), progress tracking with visual progress bars and percentage calculations, status management for active/paused/completed/cancelled goals, currency formatting integration with global currency system, comprehensive form validation and error handling, empty state handling with user-friendly messaging, and pull-to-refresh functionality. All 779 tests passing with clean static analysis (only 12 info-level warnings), successful APK build verification, and production-ready implementation following established BudApp patterns and Material 3 design principles.

**✅ Contribution Entry UI Complete (Task 15.6)**: Complete frontend UI implementation for goal contribution tracking with comprehensive form system and Material 3 design. Successfully implemented GoalContributionFormConfig with create/edit configurations and proper validation for amounts, dates, and descriptions, comprehensive Riverpod providers for contribution operations (creation, updates, deletion, real-time streams, statistics), UI screens (GoalContributionCreateScreen, GoalContributionEditScreen, GoalContributionsListScreen) with proper navigation and error handling, ContributionCard widget with Material 3 styling, smart date formatting, currency display, and edit/delete menu actions, app router integration with contribution routes (/goals/:id/contributions, /goals/:id/contributions/create, /goals/:id/contributions/:contributionId/edit), enhanced goal cards with "Add Contribution" and "View Contributions" menu actions, real-time contribution tracking with automatic provider invalidation, and comprehensive form validation with business rules. All 779 tests passing with clean static analysis (fixed strict_raw_type warning), proper code formatting, and production-ready implementation providing seamless goal contribution management experience.

**✅ Denormalized Goal Amount Updates Complete (Task 15.7)**: Complete client-side logic implementation for automatic goal progress tracking with atomic Firestore operations. Successfully enhanced GoalContributionRepositoryImpl with atomic batch operations using FieldValue.increment() for goal currentAmountCents updates, createContribution() method with automatic goal amount increments during contribution creation, updateContribution() method with amount difference calculation and incremental goal updates for consistency, deleteContribution() method with smart amount subtraction only from active contributions to prevent double-decrements, comprehensive error handling and validation ensuring goal existence before operations, cascade-style batch operations following Flutter linting best practices, and comprehensive testing with 5 new test cases specifically validating denormalized amount updates including creation, updates, deletion, multiple contribution accumulation, and inactive contribution edge cases. All 49 tests passing (44 existing + 5 new) with clean static analysis (0 issues) and production-ready implementation ensuring bulletproof data consistency through atomic Firestore operations and real-time goal progress tracking.

**✅ Flutter Testing Suite Refactoring Research Complete (Task 33.1)**: Comprehensive research and planning phase for testing suite refactoring project completed successfully. Researched fake_cloud_firestore security rules testing capabilities and firebase_auth_mocks integration patterns, documented current testing patterns across 60+ test files, identified 40-60% code reduction opportunity in test setup complexity, created detailed migration strategy with 5-phase implementation plan, added firebase_auth_mocks dependency to project, and established foundation for transforming from fragmented mocking patterns to robust standardized approach. Created comprehensive documentation including research findings, current state analysis, implementation plan, and completion summary. Project ready for Phase 2 (Establish New Testing Foundation) with clear roadmap for enhanced test reliability, security rules testing, and reduced maintenance overhead.

**✅ Firebase Testing Foundation Established (Task 33.2)**: Successfully created comprehensive Firebase testing foundation with firebase_auth_mocks + fake_cloud_firestore integration. Developed standardized Firebase test setup utilities (FirebaseTestSetup), authentication testing helpers (TestAuthHelper), security rules testing framework (SecurityRulesHelper), and enhanced mock data factory with Firebase integration. Created comprehensive test data factories, authentication scenarios, and security rules testing capabilities. Established unified testing approach replacing fragmented mocking patterns with robust standardized foundation enabling security rules testing, real Firebase behavior simulation, and 40-60% reduction in test setup complexity. Foundation ready for authentication tests migration with clear patterns and utilities for enhanced test reliability and reduced maintenance overhead.

**✅ Authentication Tests Migration Complete (Task 33.3)**: Successfully migrated authentication tests to new Firebase testing foundation with firebase_auth_mocks integration. Replaced custom MockFirebaseAuth implementations with standardized TestAuthHelper utilities, updated core mock_providers.dart to use firebase_auth_mocks, enhanced provider overrides for authenticated/unauthenticated scenarios, and fixed mocktail reset() compatibility issues. Achieved 100% test compatibility with all 44 authentication tests passing, simplified test setup patterns, enhanced reliability through real Firebase Auth behavior simulation, and maintained backward compatibility. Eliminated 50+ lines of custom mock implementation while reducing analysis issues and establishing clear migration template for repository tests.

**✅ Repository Tests Migration Complete (Task 33.4)**: Successfully migrated all repository tests to firebase_auth_mocks + fake_cloud_firestore integration with enhanced authentication state management. Migrated 110 repository tests across transaction, account, user, and integration test files, fixed critical authentication state issue in FirebaseTestSetup (signedIn parameter), enhanced provider integration patterns, and established security rules testing foundation. Achieved 100% test compatibility with all repository tests passing, eliminated 100+ lines of custom mock setup code, standardized testing patterns across all repository implementations, and prepared foundation for comprehensive security rules validation. Repository tests now simulate real Firebase Auth + Firestore behavior with proper auth context.

**✅ Service and Widget Tests Migration Complete (Task 33.5)**: Successfully completed migration of service and widget tests to new Firebase testing foundation with selective migration approach. Verified 55 service tests using appropriate mocktail patterns for dependency mocking, migrated 2 integration test files (11 tests) from custom Firebase mocking to FirebaseTestSetup, validated 7 widget tests working with updated MockProviders using firebase_auth_mocks integration. Achieved 100% test compatibility across 227+ tests, maintained correct testing architecture with service layer using dependency mocking and integration layer using Firebase simulation, enhanced integration test reliability with real Firebase Auth + Firestore behavior, and established comprehensive testing foundation ready for security rules validation.
**✅ Global Currency System Complete**: Comprehensive refactoring to implement centralized currency management throughout the application. Successfully removed currency fields from all data models (Account, Transaction, Budget), implemented global currency preference system with SharedPreferences persistence, created CurrencyService with 100+ currency symbols and locale support, developed currency settings UI with Material 3 design and currency preview functionality, updated all UI components to use global currency formatter with reactive Riverpod providers, migrated repository layer to remove currency parameters, updated Firebase Security Rules to remove currency validation, and maintained complete data integrity throughout the migration. Core app fully functional with streamlined user experience where users set currency preference once in Settings and all monetary amounts automatically display in their chosen currency.
**✅ Shared Form Components Complete**: Unified shared color and icon picker components implemented across all entity forms for consistency and code reusability. Fixed critical type casting issue in AccountFormConfig by replacing generic FormFieldConfig<String> with specialized ColorPickerFieldConfig and IconPickerFieldConfig classes. All entity forms (Account, Category, Tag) now use consistent picker components with entity-specific color/icon constants from FormConstants. Enhanced form field factory with proper type handling, comprehensive testing coverage with 554 tests passing, and complete documentation for shared component patterns. Ensures consistent user experience and prevents runtime type casting errors across all form screens.
**✅ Repository Firebase Auth Integration Complete**: Resolved critical Firebase Auth integration issues across all repository implementations. Fixed AccountRepositoryImpl and CategoryRepositoryImpl to properly handle current user ID assignment, preventing collection path "//" errors during entity creation. Updated all repository constructors with proper Firebase Auth dependencies, implemented automatic user ID assignment in create methods, and established consistent Firebase Auth mocking patterns across 554 test files. Added missing Firestore composite index for category queries and deployed to Firebase for optimal performance. All repository operations now work reliably with proper user context and data isolation.
**✅ Generic Form System Complete**: Successfully implemented comprehensive configuration-driven form architecture eliminating code duplication across all entity forms. Created GenericFormScreen<T> and GenericFormConfig<T> system handling Account, Category, Transaction, Budget, and Tag forms through unified patterns. Achieved 60-80% reduction in form-related code while maintaining full functionality and type safety. Established FormFieldFactory with specialized field types (ColorPickerFieldConfig, IconPickerFieldConfig, AccountTypeFormFieldConfig) and shared components across all entities. Comprehensive testing with 554 tests passing, complete documentation with implementation guides, and clean codebase with no deprecated files. All create/edit workflows now use consistent full-screen navigation patterns with standardized validation and error handling.
**✅ Material 3 Floating Label Forms Complete**: Comprehensive Material 3 floating label implementation across all form inputs achieving 100% coverage. Enhanced AppTextFormField and AuthFormField with Material 3 floating label behavior using FloatingLabelBehavior.auto. Successfully converted all transaction forms (AmountInputField, DescriptionField, NotesField) and budget forms (BudgetForm, CategoryBudgetCard, BulkBudgetOperationsDialog) to use Material 3 floating labels while preserving specialized functionality. Maintained backward compatibility with useFloatingLabel parameter, ensured consistent styling across authentication, profile, entity, transaction, and budget screens. All 554 tests passing with flutter analyze clean and proper code formatting. Complete modern user experience with smooth label animations, proper accessibility support, and unified Material 3 compliance across the entire application.
**✅ Android Production Signing Security Complete**: **CRITICAL SECURITY ISSUE RESOLVED** - Eliminated debug keys from all production builds and implemented secure, automated Android signing infrastructure. Removed dangerous debug signing configuration that was being used for release builds, created environment-specific keystores (dev, staging, prod) with proper security isolation, implemented automated keystore generation scripts with validation, established secure password management using `local.properties` (git-ignored) for local development and GitHub Secrets for CI/CD, created comprehensive GitHub Actions workflow with automated builds and signature validation, developed build validation scripts to prevent debug key usage in production, updated Gradle configuration to read from secure local storage with environment variable fallback, and created comprehensive documentation for keystore management and security procedures. All builds now use proper release signing with automated validation ensuring no debug keys can ever reach production. Package name configuration fixed to properly support Firebase integration across all environments.

**✅ Production-Safe Logging Security Enhancement Complete**: **CRITICAL PII PROTECTION IMPLEMENTED** - Comprehensive structured logging system with automatic PII sanitization for financial application compliance. Fixed 4 major PII exposure vulnerabilities in authentication services (email addresses, display names), implemented environment-aware logging configuration (dev: trace, staging: info+, prod: warning+), created LogSanitizer utility with GDPR/PCI DSS compliant data protection, added kDebugMode guards throughout codebase to prevent debug logs in production builds, established automatic sanitization of emails, phone numbers, credit cards, financial amounts, API keys, and JWT tokens, created comprehensive testing suite for logging security verification, and updated development guidelines with proper logging patterns. All debug logs now completely stripped from production builds with verified PII sanitization working correctly across all scenarios.

**✅ Enhanced Firestore Security Rules Complete (Task 31.5)**: **2024 SECURITY BEST PRACTICES IMPLEMENTED** - Comprehensive enhancement of Firestore Security Rules with modern security patterns and attack prevention. Conducted thorough audit of existing 621-line security rules, implemented granular operation controls (get/list vs create/update/delete) for all collections, added advanced validation functions with input sanitization, rate limiting, and field-level security, created enhanced XSS and injection prevention patterns, implemented financial security validation with amount limits ($9.9M max) and business logic enforcement, added timestamp security with future date limits (7 days), created comprehensive attack simulation test suite with 66 security tests (11 basic + 19 enhanced + 36 account-specific), maintained full backward compatibility with existing functionality, and documented all security enhancements. Production-ready security implementation providing comprehensive protection against common attack vectors while preserving seamless user experience.

## Overview

BudApp is targeting tech-savvy individuals aged 22-35 who are transitioning from manual tracking methods (like spreadsheets) to structured budgeting tools. The MVP focuses on delivering core personal finance features through an intuitive mobile application.

## Features

### Core Features (MVP - Phase 1)
- **User Authentication**: Email/password with email verification ✅ *Complete* | Google OAuth ✅ *Complete* | Biometric authentication (Face ID/Fingerprint) with startup gate ✅ *Complete* | Apple OAuth *Planned*
- **Profile Management**: User profile editing, password changes, account recovery, and secure account deletion ✅ *Complete*
- **Error Handling & Recovery**: Comprehensive centralized error handling with Firebase Crashlytics integration, global error boundaries, graceful UI degradation, automatic crash reporting, and user-friendly error recovery mechanisms ✅ *Complete*
- **Account Management**: Multiple financial accounts (Checking, Savings, Credit Card, Cash) with global currency system ✅ *Complete CRUD Implementation*
- **Transaction Management**: Income, expenses, and transfers with categorization ✅ *COMPLETE - Full CRUD operations with atomic balance adjustments, comprehensive user feedback system with centralized error handling, loading states, success messages, and Firestore-specific error handling with retry mechanisms. All transaction types operational with edit/delete functionality and production-ready user experience.*
- **Category System**: Unified category management with Firebase Remote Config integration ✅ *Complete - Simplified category model with seamless user experience, no distinction between predefined and user-created categories. Complete data model, repository, deletion constraints, frontend UI, creation/editing forms, and Firestore integration*
- **Tag Management**: Custom transaction tags with many-to-many relationships ✅ *COMPLETE - Full CRUD operations, cascading deletion logic, search functionality, navigation integration, and comprehensive UI components*
- **Budget Management**: Unified time-period-based budget system with edit-only approach, bulk operations, selection mode UI, and global time period integration ✅ *COMPLETE - Edit-only budget management with bulk operations (percentage adjustments, status changes, batch deletion), selection mode with multi-select capabilities, progress calculation service, overlap prevention, category integration, and comprehensive UI components with Material 3 design. Budget creation functionality removed in favor of edit mode interface.*
- **Financial Goal Tracking**: Savings goals with manual contribution tracking ✅ *COMPLETE - Complete goal management system with Goal and GoalContribution data models, comprehensive repositories with full CRUD operations and real-time streams, Riverpod providers with AsyncNotifier pattern, complete UI implementation with goals list, create, and edit screens, Material 3 design integration, progress tracking with visual indicators, status management (active/paused/completed/cancelled), comprehensive form validation, and automatic goal progress updates with atomic Firestore operations. Denormalized currentAmountCents field automatically maintained through contribution operations ensuring real-time goal progress tracking. All tests passing with production-ready implementation.*
- **Basic Reporting**: Dashboard with account balances and spending analysis *Planned*
- **Multi-Device Sync**: Real-time synchronization with offline-first functionality ✅ *Stream-based providers implemented*
- **Premium Subscription**: Free tier with limitations, premium tier via RevenueCat *Planned*

### Technical Features
- **Multi-Environment Support**: Development, Staging, and Production environments ✅
- **Unified Architecture**: Single main.dart with automatic flavor detection ✅
- **Design System**: Material 3 with comprehensive design tokens, global text overflow system, and dark/light theme support ✅
- **Authentication System**: Complete email/password + Google OAuth authentication with email verification flow ✅
- **Authentication UI**: Complete login/signup/verification screens with form validation ✅
- **State Management**: Riverpod architecture with repository pattern and dependency injection ✅
- **AuthService Architecture**: Instance-based service with dependency injection via Riverpod providers ✅
- **FirestoreService Architecture**: Separate injectable service with comprehensive Firestore operations ✅
- **Repository Pattern**: Clean domain-specific interfaces (User, Transaction, Account) with strict layer separation enforcement ✅
- **Repository Pattern Enforcement**: Eliminated all direct Firebase access from business logic (Task 31.4) ✅
- **Data Models**: Immutable models using freezed with JSON serialization for Firestore ✅
- **UI Architecture**: Complete migration to ConsumerWidget/ConsumerStatefulWidget pattern ✅
- **Error Handling**: Comprehensive AsyncValue-based error handling with AsyncValue.guard() ✅
- **AsyncNotifier Providers**: Authentication and repository operations with robust error propagation ✅
- **Testing Infrastructure**: Comprehensive testing with provider overrides and mocked dependencies ✅
- **Account Management UI**: Complete CRUD implementation with creation, viewing, editing, and deletion ✅
- **Stream-based Data**: Live data streaming from Firestore with real-time provider architecture ✅
- **Navigation System**: Deep linking and route-based navigation with go_router ✅
- **Comprehensive Testing**: Account management features with 554+ passing tests ✅
- **Offline-First Architecture**: Complete functionality without internet connection
- **Firebase Integration**: Core SDK, Auth, Firestore, Remote Config, FCM ✅
- **Firebase Services**: Fully integrated authentication and database services ✅
- **Remote Config Integration**: Predefined categories, premium limits, and feature flags ✅
- **Security**: Comprehensive Firestore Security Rules with data validation and user isolation ✅
- **Firebase App Check**: Multi-layered app verification with environment-specific providers for enhanced API security ✅
- **Security Rules Testing**: Complete test suite with Firebase Emulator integration (32 tests) ✅
- **Referential Integrity**: Database-level referential integrity enforcement in Firestore Security Rules ✅
- **Freezed Data Models**: Immutable data structures with compile-time safety and JSON serialization ✅
- **Performance**: Cold start <2s, 60fps UI target, optimized initialization ✅
- **Service Testing**: Built-in Firebase connectivity testing across all environments ✅
- **Internationalization**: Complete localization infrastructure with flutter_gen_l10n ✅
- **Enhanced Localization**: Comprehensive app_en.arb with 100+ localization keys for improved UX ✅
- **Shared Form Components**: Unified color and icon picker components across all entity forms for consistency ✅
- **Global Currency System**: Centralized currency preference with 100+ currency support and automatic formatting ✅
- **Production-Safe Logging**: Comprehensive structured logging with automatic PII sanitization for financial compliance ✅
- **Global Error Handler**: Firebase Crashlytics integration with global error boundaries, automatic crash reporting, and environment-aware configuration ✅
- **Error Boundary System**: React-style error boundaries for graceful UI degradation with retry mechanisms and user-friendly fallback interfaces ✅
- **Centralized UI Strings**: All user-facing text centralized for i18n support ✅
- **Secure Storage Service**: Platform-specific secure storage for sensitive data (biometric keys, API keys, PINs) ✅
- **Centralized Error Handling**: Comprehensive error handling system with Firebase Crashlytics integration, global error boundaries, PII-safe crash reporting, and user-friendly error recovery mechanisms ✅
- **Design System Consistency**: Semantic design tokens replacing hardcoded values throughout UI ✅
- **Firebase Service Refactoring**: Replaced static services with proper dependency injection pattern ✅
- **Code Quality**: Perfect static analysis with "No issues found!" from flutter analyze ✅
- **Feature-Based Architecture**: Complete restructuring to feature-based organization for improved maintainability ✅
- **Test Organization**: Test structure mirrors implementation with feature-based organization ✅
- **Category Management System**: Complete hierarchical category system with subcategory CRUD operations, deletion constraints with transaction re-assignment, data model, repository pattern, and Riverpod integration ✅
- **Transaction Database Schema**: Comprehensive schema documentation with field specifications, validation rules, business logic, optimized Firestore indexes, and security rules ✅

## Getting Started

### Prerequisites
- Flutter SDK (latest stable)
- Dart 3+
- Android Studio / Xcode for platform development
- Firebase CLI for backend management

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd budapp
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Firebase Setup**
   - Ensure Firebase configuration files are in place:
     - Android: `android/app/google-services.json` (prod), `android/app/dev.google-services.json`, `android/app/staging.google-services.json`
     - iOS: `ios/Runner/GoogleService-Info.plist` (prod), `ios/Runner/dev.GoogleService-Info.plist`, `ios/Runner/staging.GoogleService-Info.plist`

### Running the App

The app supports multiple environments with different Firebase projects. **Development is the default environment.**

#### Quick Development Run (Recommended)
```bash
flutter run
```
- **Defaults to development environment**
- Package: `com.digitau.budapp.dev`
- Firebase Project: `budapp-dev`
- Theme: Orange

#### Environment-Specific Runs

**Development Environment**
```bash
flutter run --flavor dev --dart-define=FLAVOR=dev
# Same as: flutter run
```
- Package: `com.digitau.budapp.dev`
- Firebase Project: `budapp-dev`
- Theme: Orange

**Staging Environment**
```bash
flutter run --flavor staging --dart-define=FLAVOR=staging
```
- Package: `com.digitau.budapp.staging`
- Firebase Project: `budapp-staging-1`
- Theme: Blue

**Production Environment**
```bash
flutter run --flavor prod --dart-define=FLAVOR=prod
```
- Package: `com.digitau.budapp`
- Firebase Project: `budapp-prod`
- Theme: Green

> **Note**: The unified `main.dart` automatically detects the environment via `--dart-define=FLAVOR` and configures Firebase, theming, and app metadata accordingly. The `default-flavor: dev` configuration in `pubspec.yaml` makes `flutter run` use the development environment.

## Project Structure

BudApp follows a **feature-based architecture** that organizes code by business domains rather than technical layers. This approach improves maintainability, scalability, and developer productivity.

```
budapp/
├── lib/
│   ├── main.dart                    # Unified entry point (auto-detects flavor)
│   ├── config/                      # 🔧 App configuration
│   │   ├── environment_config.dart  # Environment configuration management
│   │   ├── design_tokens.dart       # Design system tokens and colors
│   │   └── app_theme.dart          # Material 3 theme configuration
│   ├── features/                    # 🎯 Feature-based modules
│   │   ├── auth/                   # Authentication feature
│   │   │   ├── presentation/       # UI components
│   │   │   │   ├── screens/        # Login, signup, verification screens
│   │   │   │   └── widgets/        # Auth-specific widgets
│   │   │   ├── services/           # Auth business logic
│   │   │   └── providers/          # Riverpod providers
│   │   ├── accounts/               # Account management (ready for implementation)
│   │   │   ├── presentation/
│   │   │   ├── services/
│   │   │   └── providers/
│   │   ├── transactions/           # Transaction CRUD (ready for implementation)
│   │   │   ├── presentation/
│   │   │   ├── services/
│   │   │   └── providers/
│   │   ├── budgets/                # Budget tracking (ready for implementation)
│   │   │   ├── presentation/
│   │   │   ├── services/
│   │   │   └── providers/
│   │   ├── goals/                  # Goal management (ready for implementation)
│   │   │   ├── presentation/
│   │   │   ├── services/
│   │   │   └── providers/
│   │   └── dashboard/              # Home screen and dashboard
│   │       ├── presentation/       # Dashboard UI
│   │       │   └── screens/        # home_screen.dart
│   │       └── providers/          # Dashboard providers
│   ├── data/                       # 📊 Shared data layer
│   │   ├── models/                 # Data models (session_state.dart, remote_config_data.dart)
│   │   └── repositories/           # Repository pattern
│   │       ├── interfaces/         # Repository interfaces
│   │       └── implementations/    # Firestore implementations
│   ├── services/                   # 🔧 Global services
│   │   ├── firestore_service.dart  # Firestore operations service
│   │   ├── remote_config_service.dart # Firebase Remote Config service
│   │   └── session_service.dart    # Session management service
│   ├── providers/                  # 🔗 Global providers
│   │   ├── providers.dart          # Core provider definitions and exports
│   │   ├── firebase_providers.dart # Firebase service providers
│   │   ├── repository_providers.dart # Repository providers
│   │   └── ui_providers.dart       # UI state providers
│   ├── routing/                    # 🧭 Navigation
│   │   └── app_router.dart         # go_router configuration with auth guards
│   ├── utils/                      # 🔧 Utility classes
│   │   └── remote_config_utils.dart # Remote Config helper functions
│   ├── widgets/                    # 🧩 Truly reusable widgets
│   │   └── splash_screen.dart      # App-level components
│   ├── l10n/                       # 🌍 Localization
│   │   ├── app_en.arb              # English localization strings
│   │   └── app_localizations.dart  # Generated localization classes
│   ├── firebase_options_dev.dart    # Development Firebase options
│   ├── firebase_options_staging.dart # Staging Firebase options
│   └── firebase_options_prod.dart   # Production Firebase options
├── android/
│   └── app/
│       ├── google-services.json           # Production Firebase config
│       ├── dev.google-services.json       # Development Firebase config
│       └── staging.google-services.json   # Staging Firebase config
├── ios/
│   └── Runner/
│       ├── GoogleService-Info.plist       # Production Firebase config
│       ├── dev.GoogleService-Info.plist   # Development Firebase config
│       └── staging.GoogleService-Info.plist # Staging Firebase config
├── scripts/                   # Build and deployment scripts
│   ├── select_firebase_config.sh    # Firebase config selection
│   ├── start_emulators.sh           # Firebase emulator startup script
│   └── test_with_emulators.sh       # Test execution with emulators
├── test/                      # 🧪 Test files (mirrors lib structure)
│   ├── features/                  # Feature-based test organization
│   │   └── auth/                  # Authentication feature tests
│   │       ├── auth_test.dart     # Authentication validation tests
│   │       ├── presentation/
│   │       │   ├── screens/       # Screen tests
│   │       │   └── widgets/       # Widget tests
│   │       └── services/          # Service tests
│   ├── data/                      # Data layer tests
│   │   └── repositories/          # Repository tests
│   ├── services/                  # Global service tests
│   │   └── session_service_test.dart
│   ├── providers/                 # Provider tests
│   │   └── providers_test.dart
│   ├── widgets/                   # Shared widget tests
│   │   └── widget_test.dart
│   ├── integration/               # Integration tests
│   ├── unit/                      # Unit tests
│   └── helpers/                   # Test utilities
├── firebase/                  # Firebase configuration and testing
│   ├── remoteconfig_template.json # Firebase Remote Config template
│   ├── security-requirements.md # Security requirements documentation
│   └── test/                 # Firestore Security Rules tests
│       ├── package.json      # Test dependencies
│       ├── README.md         # Testing setup guide
│       ├── security-rules-basic.test.js      # Basic authentication tests
│       └── security-rules-validation.test.js # Data validation tests
├── firebase.json              # Firebase emulator configuration
├── firestore.rules           # Comprehensive Firestore security rules
├── firestore.indexes.json    # Firestore composite indexes (MUST be in root for deployment)
├── .taskmaster/              # Task Master project management
│   ├── memory-bank/          # AI assistant memory
│   └── tasks/                # Project tasks and planning
└── docs/                     # Project documentation
    ├── architecture.md       # Comprehensive architecture guide
    ├── TESTING.md            # Testing guide and setup
    ├── AUTHENTICATION.md     # Authentication implementation details
    ├── DEVELOPMENT_WORKFLOW.md # Development workflow and best practices
    ├── ACCOUNT_DATA_SCHEMA.md # Account database schema documentation
    ├── TRANSACTION_DATABASE_SCHEMA.md # Transaction database schema documentation
    ├── transaction_form_ui_design.md # Transaction form UI/UX design specification
    └── rules/                # Development rules and guidelines
```

## Architecture

BudApp follows modern Flutter architecture patterns with clean separation of concerns, offline-first design, and comprehensive testing. For detailed architectural information, see [Architecture Documentation](docs/architecture.md).

### Technology Stack
- **Frontend**: Flutter with Dart 3
- **Design System**: Material 3 with comprehensive design tokens, global text overflow system, automatic dark/light theme, and Material 3 floating label forms ✅
- **Authentication**: Firebase Auth with email/password + Google OAuth authentication and email verification flow ✅
- **State Management**: Riverpod (AsyncNotifier/Notifier pattern) with repository pattern and dependency injection ✅
- **Navigation**: go_router for declarative routing with authentication guards ✅
- **Data Models**: Freezed for immutable data structures with JSON serialization ✅
- **UI Framework**: Material 3 & Cupertino widgets with consistent floating label forms ✅
- **Backend**: Firebase (Core SDK, Firestore, Auth, Remote Config, FCM, App Check)
- **Localization**: flutter_gen_l10n for internationalization support ✅
- **Subscription Management**: RevenueCat - *planned*
- **Build System**: Android flavors with unified main.dart and automatic environment detection
- **Architecture**: Single main.dart with environment configuration management

### Development Approach
- **Phase 1 (MVP)**: Pure client-side with Firebase services only
- **Phase 2**: Add Cloud Functions for recurring transactions
- **Phase 3**: Server-side intelligence and advanced features

### Key Architectural Patterns
- **Offline-First**: Complete functionality without internet connection
- **Repository Pattern**: Clean separation between UI, business logic, and data layers ✅
- **Repository Pattern Enforcement**: Strict layer separation with no direct Firebase access in business logic ✅
- **Service Abstractions**: Dedicated service interfaces for cross-cutting concerns (e.g., connectivity testing) ✅
- **Dependency Injection**: Riverpod providers for service management ✅
- **Immutable Data**: Freezed models for type safety and predictability ✅
- **Configuration-Driven**: Remote Config for behavior control without app updates ✅
- **Security-First**: User data isolation through Firestore sub-collections ✅

📖 **[Read the complete Architecture Guide →](docs/architecture.md)**

## Firebase Projects

The app uses three separate Firebase projects for different environments:

| Environment | Project ID | Package Name | Purpose |
|-------------|------------|--------------|---------|
| Development | `budapp-dev` | `com.digitau.budapp.dev` | Development and testing |
| Staging | `budapp-staging-1` | `com.digitau.budapp.staging` | Pre-production testing |
| Production | `budapp-prod` | `com.digitau.budapp` | Live application |

## Development

### Code Quality Standards

BudApp enforces comprehensive code quality standards through automated tools and processes:

#### Quick Setup
```bash
# Install Git hooks for automatic quality checks
./scripts/setup-git-hooks.sh

# Run quality checks manually
flutter analyze && dart format . && flutter test
```

#### Current Status
- **1,837 linting issues** (reduced from 4,393 with stricter rules)
- **Automated formatting** with `dart format`
- **Pre-commit hooks** for quality enforcement
- **CI/CD quality gates** in GitHub Actions

#### Key Features
- **Static Analysis**: Comprehensive linting with `flutter_lints` foundation
- **Code Formatting**: Automatic formatting enforcement
- **Import Organization**: Alphabetical sorting and proper grouping
- **Performance Rules**: Const constructor enforcement
- **Security Checks**: Basic security pattern validation

#### Documentation
- 📖 **[Complete Code Quality Guide →](docs/code_quality_standards.md)**
- 🚀 **[Quick Setup Guide →](docs/quick_setup_guide.md)**

### Code Style
- Follow Flutter/Dart conventions
- Use English for all code, comments, and documentation
- Implement comprehensive error handling
- Write self-documenting code with clear naming

### Testing

#### Unit and Widget Tests
```bash
# Run all tests
flutter test

# Run specific test files
flutter test test/auth_test.dart                      # Authentication validation tests
flutter test test/email_verification_widget_test.dart # Email verification UI tests
flutter test test/widget_test.dart                    # Login/signup screen tests
flutter test test/user_repository_test.dart           # User repository tests
flutter test test/transaction_repository_test.dart    # Transaction repository tests
flutter test test/account_repository_test.dart        # Account repository tests
flutter test test/providers_test.dart                 # Provider infrastructure tests

# Run with coverage
flutter test --coverage
```

#### Firebase Emulator Testing
```bash
# Start Firebase emulators for integration testing
./scripts/start_emulators.sh

# Or manually start emulators
firebase emulators:start --only auth,firestore

# Run integration tests with emulators (future)
./scripts/test_with_emulators.sh
```

**Emulator URLs:**
- Auth Emulator: http://localhost:9099
- Firestore Emulator: http://localhost:8080
- Emulator UI: http://localhost:4000

#### Firestore Security Rules Testing
```bash
# Navigate to Firebase test directory
cd firebase/test

# Install test dependencies (first time only)
npm install

# Start Firestore emulator (in separate terminal)
firebase emulators:start --only firestore

# Run all security rules tests
npm test

# Run specific test suites
npm test security-rules-basic.test.js      # Authentication tests (11 tests)
npm test security-rules-validation.test.js # Data validation tests (20 tests)

# Run tests with coverage
npm run test:coverage
```

**Security Rules Test Coverage:**
- ✅ **Authentication & Authorization**: User data isolation, owner-only access
- ✅ **Data Validation**: Field types, required fields, business logic
- ✅ **User Profiles**: Creation, updates, immutable field protection
- ✅ **Accounts**: Type validation, currency codes, field constraints
- ✅ **Transactions**: Business logic validation, account references
- ✅ **Categories & Budgets**: Comprehensive validation rules
- ✅ **Goals & Tags**: Additional entity validation

#### Code Quality Validation
```bash
# Analyze code for issues
flutter analyze

# Format code
dart format .
```

#### Multi-Environment Validation
```bash
# Test all environments build correctly
./scripts/select_firebase_config.sh dev
flutter build apk --debug --flavor dev

./scripts/select_firebase_config.sh staging
flutter build apk --debug --flavor staging

./scripts/select_firebase_config.sh prod
flutter build apk --debug --flavor prod

# Reset to dev environment
./scripts/select_firebase_config.sh dev
```

> **Note**: All validation steps (flutter test, flutter analyze, dart format) must pass before committing changes.

### Building

**Important**: Use the Firebase config selection script before building non-dev environments:

#### Development Build
```bash
./scripts/select_firebase_config.sh dev
flutter build apk --flavor dev
```

#### Staging Build
```bash
./scripts/select_firebase_config.sh staging
flutter build apk --flavor staging
```

#### Production Build
```bash
./scripts/select_firebase_config.sh prod
flutter build apk --flavor prod
```

> **Note**: The script automatically copies the correct Firebase configuration file for each environment. Development is the default configuration.

### Firebase Deployment

#### Important File Locations
Firebase CLI requires specific file locations for successful deployment:

- **`firestore.rules`** - MUST be in project root directory
- **`firestore.indexes.json`** - MUST be in project root directory
- **`firebase.json`** - MUST be in project root directory

> **⚠️ Critical**: Moving `firestore.rules` or `firestore.indexes.json` to subdirectories (like `firebase/`) will cause deployment failures.

#### Deployment Commands
```bash
# Deploy security rules only
firebase deploy --only firestore:rules

# Deploy indexes only
firebase deploy --only firestore:indexes

# Deploy both rules and indexes
firebase deploy --only firestore

# Deploy all Firebase services
firebase deploy
```

#### Multi-Environment Deployment
```bash
# Deploy to development environment
firebase use budapp-dev
firebase deploy --only firestore

# Deploy to staging environment
firebase use budapp-staging-1
firebase deploy --only firestore

# Deploy to production environment
firebase use budapp-prod
firebase deploy --only firestore
```

## Error Handling & Recovery

BudApp implements a comprehensive centralized error handling system designed for financial applications with stringent reliability and user experience requirements.

### Error Handling Architecture

#### 🔧 **GlobalErrorHandler Service**
- **Firebase Crashlytics Integration**: Automatic crash reporting with environment-aware configuration
- **Global Error Handlers**: Captures Flutter framework errors, platform dispatcher errors, and isolate errors
- **User Context Tracking**: Associates crashes with user sessions while maintaining PII protection
- **Custom Error Reporting**: Non-fatal error reporting with context and breadcrumb tracking

```dart
// Initialize error handling (done automatically at app startup)
await GlobalErrorHandler.instance.initialize(
  logger: loggingService,
  enableInDebug: false, // Only crashes in release mode
);

// Manual error reporting with context
await GlobalErrorHandler.instance.reportError(
  error,
  stackTrace,
  reason: 'Payment processing failed',
  customKeys: {'userId': userId, 'amount': amount},
  fatal: false,
);
```

#### 🛡️ **Error Boundary System**
- **React-Style Error Boundaries**: Graceful UI degradation when widgets encounter errors
- **Automatic Recovery**: Built-in retry mechanisms for transient failures
- **User-Friendly Fallbacks**: Custom error UI with helpful messaging and actions
- **Development Tools**: Debug-mode error details and manual error reporting

```dart
// Wrap widgets with error boundaries
Widget build(BuildContext context) {
  return MyWidget().withErrorBoundary(
    showDetails: true,
    canRetry: true,
    onError: (error, stackTrace) {
      // Custom error handling
    },
  );
}

// Simple safety wrapper
Widget build(BuildContext context) {
  return RiskyWidget().safely(
    fallback: Text('Something went wrong'),
  );
}
```

#### 📊 **Enhanced ErrorService**
- **Firebase Error Translation**: Converts Firebase errors to user-friendly messages
- **Crash Reporting Integration**: Automatically logs errors to GlobalErrorHandler
- **Consistent UI**: Standardized error snackbars, dialogs, and messaging
- **Context Awareness**: Error logging with specific context for debugging

```dart
// User-friendly error display with crash reporting
ErrorService.showErrorSnackBarWithLogging(
  context,
  error,
  errorContext: 'Transaction creation',
  additionalData: {'transactionType': type},
);

// Error logging for crash reporting
await ErrorService.logError(
  error,
  stackTrace,
  context: 'Account balance update',
  fatal: false,
);
```

### Security & Privacy

#### 🔒 **PII Protection**
- **Automatic Data Sanitization**: LoggingService automatically redacts sensitive information
- **Crashlytics Safety**: Custom keys filtered to prevent financial data exposure
- **Environment Awareness**: Crash collection disabled in debug builds by default

#### 🌍 **Multi-Environment Configuration**
- **Development**: Full error details, comprehensive logging, crash collection disabled
- **Staging**: Reduced logging, crash collection enabled, detailed error context
- **Production**: Minimal logging, full crash reporting, user-friendly error messages only

### Implementation Features

#### ✅ **Comprehensive Coverage**
- **Global Error Handlers**: Flutter, Platform Dispatcher, and Isolate error capture
- **UI Error Boundaries**: Widget-level error handling with graceful degradation
- **Service Integration**: All services enhanced with crash reporting capabilities
- **Provider Integration**: Riverpod providers automatically initialize error handling

#### 🔄 **Recovery Mechanisms**
- **Automatic Retry**: Built-in retry logic for transient network and service errors
- **User-Initiated Recovery**: Manual retry buttons in error UI components
- **Graceful Degradation**: Fallback UI when primary components fail
- **Session Recovery**: Error handlers maintain user session context

#### 📱 **User Experience**
- **Localized Error Messages**: All error text supports internationalization
- **Material 3 Design**: Error UI follows design system consistency
- **Accessibility Support**: Screen reader compatible error messaging
- **Progressive Disclosure**: Debug details available in development builds only

### Production Readiness

- **🚀 Zero Test Failures**: All 784+ tests passing with error handling integration
- **📊 Clean Static Analysis**: No Flutter analyze warnings or errors
- **🔍 Security Validated**: PII protection tested and verified
- **⚡ Performance Optimized**: Minimal overhead in production builds
- **🌍 Multi-Environment Tested**: Validated across dev/staging/prod environments

The error handling system ensures BudApp maintains exceptional reliability and user experience even when encountering unexpected situations, while providing comprehensive debugging information for development and monitoring.

## Contributing

1. Follow the established code style and patterns
2. Write tests for new features
3. Update documentation as needed
4. Use the Task Master system for project management
5. Ensure all environments work correctly before submitting changes

## License

This project is private and proprietary.

## Support

For development questions and issues, refer to the Task Master documentation in `.taskmaster/` or the Memory Bank in `.taskmaster/memory-bank/`.
