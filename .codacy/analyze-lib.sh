#!/bin/bash

# Codacy Analysis Script for Business Logic Only
# Runs analysis in project root but filters results to exclude test files
# Usage: ./.codacy/analyze-lib.sh [options]

set -e

echo "🔍 Running Codacy analysis on business logic only..."
echo "📁 Analyzing: lib/ directory (excluding test files)"
echo "🚫 Filtering out: test files, generated files, platform code"
echo ""

# Run the analysis and filter results
echo "🚀 Running Codacy CLI analysis..."
./.codacy/cli.sh analyze "$@" | grep -v "test/" | grep -v "integration_test/" | grep -v "\.g\.dart" | grep -v "\.freezed\.dart" | grep -v "\.gr\.dart" | grep -v "\.config\.dart" | grep -v "generated_plugin" | grep -v "firebase_options" | grep -v "l10n/"

echo ""
echo "✅ Analysis complete!"
echo "💡 Results filtered to show only business logic issues"
echo "💡 Test files and generated files were excluded from output"
echo ""
echo "📊 To see all issues (including tests): ./.codacy/cli.sh analyze"
echo "📊 To focus on specific directory: ./.codacy/cli.sh analyze lib/features/accounts/"
