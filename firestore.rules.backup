rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // =============================================================================
    // BudApp Firestore Security Rules
    // =============================================================================
    //
    // This ruleset implements comprehensive security for BudApp's financial data:
    // 1. User data isolation - users can only access their own data
    // 2. Authentication required - all operations require authenticated users
    // 3. Data validation - enforce field types, required fields, and business rules
    // 4. Referential integrity - prevent deletion of entities with dependencies
    // 5. Premium feature limits - enforce account limits based on subscription
    //
    // Collection Structure:
    // - users/{userId}                    # User profiles (top-level)
    //   ├── accounts/{accountId}          # Financial accounts (subcollection)
    //   ├── transactions/{transactionId}  # All transactions (subcollection)
    //   ├── categories/{categoryId}       # User custom categories (subcollection)
    //   ├── budgets/{budgetId}           # User budgets (subcollection)
    //   ├── goals/{goalId}               # Financial goals (subcollection)
    //   └── tags/{tagId}                 # User-defined tags (subcollection)
    // =============================================================================

    // =============================================================================
    // HELPER FUNCTIONS
    // =============================================================================

    // Check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Check if the authenticated user owns the resource
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }



    // ENHANCED SECURITY FUNCTIONS (2024 Best Practices)
    // =============================================================================





    // Note: Rate limiting, business hours, and recent authentication checks
    // are implemented at the application level for better performance





    // Validate required fields are present
    function hasRequiredFields(data, fields) {
      return data.keys().hasAll(fields);
    }

    // Validate field is a valid timestamp
    function isValidTimestamp(value) {
      return value is timestamp;
    }

    // Validate field is a valid enum value
    function isValidEnum(value, allowedValues) {
      return value in allowedValues;
    }

    // Validate string length
    function isValidStringLength(value, minLength, maxLength) {
      return value is string && value.size() >= minLength && value.size() <= maxLength;
    }

    // =============================================================================
    // USER PROFILES
    // =============================================================================

    // User profile documents (top-level collection)
    match /users/{userId} {
      // Users can read their own profile
      allow read: if isOwner(userId);

      // Users can create their own profile with validation
      allow create: if isOwner(userId) && isValidUserProfile(request.resource.data, userId);

      // Users can update their own profile with validation
      allow update: if isOwner(userId) && resource.data != null && isValidUserProfileUpdate(request.resource.data, resource.data);

      // Users cannot delete their profile (handled by backend)
      allow delete: if false;
    }

    // =============================================================================
    // USER PROFILE VALIDATION FUNCTIONS
    // =============================================================================

    // Validate user profile creation
    function isValidUserProfile(data, userId) {
      return data != null &&
             userId != null &&
             request.auth != null &&
             request.auth.uid != null &&
             ('uid' in data) &&
             data.uid == userId &&
             data.uid == request.auth.uid &&
             // Validate email field (optional)
             (!('email' in data) || (data.email is string && data.email.size() > 0)) &&
             // Validate displayName field (optional)
             (!('displayName' in data) || (data.displayName is string && data.displayName.size() > 0)) &&
             // Validate photoURL field (optional, should be string)
             (!('photoURL' in data) || (data.photoURL is string && data.photoURL.size() > 0)) &&
             // Validate schemaVersion field (optional, should be integer)
             (!('schemaVersion' in data) || (data.schemaVersion is int && data.schemaVersion > 0)) &&
             // Validate createdAt field (optional, should be timestamp)
             (!('createdAt' in data) || data.createdAt is timestamp) &&
             // Validate lastLoginAt field (optional, should be timestamp)
             (!('lastLoginAt' in data) || data.lastLoginAt is timestamp) &&
             // Validate preferences field (optional, should be map)
             (!('preferences' in data) || data.preferences is map) &&
             // Validate isEmailVerified field (optional, should be boolean)
             (!('isEmailVerified' in data) || data.isEmailVerified is bool) &&
             // Validate authProviders field (optional, should be list)
             (!('authProviders' in data) || data.authProviders is list);
    }

    // Validate user profile updates
    function isValidUserProfileUpdate(newData, oldData) {
      return newData != null &&
             oldData != null &&
             request.auth != null &&
             request.auth.uid != null &&
             ('uid' in newData) &&
             ('uid' in oldData) &&
             newData.uid == oldData.uid &&
             newData.uid == request.auth.uid &&
             // Validate email field (optional, can be updated)
             (!('email' in newData) || (newData.email is string && newData.email.size() > 0)) &&
             // Validate displayName field (optional)
             (!('displayName' in newData) || (newData.displayName is string && newData.displayName.size() > 0)) &&
             // Validate photoURL field (optional, should be string)
             (!('photoURL' in newData) || (newData.photoURL is string && newData.photoURL.size() > 0)) &&
             // Validate schemaVersion field (optional, should be integer)
             (!('schemaVersion' in newData) || (newData.schemaVersion is int && newData.schemaVersion > 0)) &&
             // Validate createdAt field (should not change)
             (!('createdAt' in newData) || !('createdAt' in oldData) || newData.createdAt == oldData.createdAt) &&
             // Validate lastLoginAt field (optional, should be timestamp)
             (!('lastLoginAt' in newData) || newData.lastLoginAt is timestamp) &&
             // Validate preferences field (optional, should be map)
             (!('preferences' in newData) || newData.preferences is map) &&
             // Validate isEmailVerified field (optional, should be boolean)
             (!('isEmailVerified' in newData) || newData.isEmailVerified is bool) &&
             // Validate authProviders field (optional, should be list)
             (!('authProviders' in newData) || newData.authProviders is list);
    }



    // Validate optional string field (allows null values)
    function isValidOptionalString(data, field, minLength, maxLength) {
      return !(field in data) ||
             data[field] == null ||
             (data[field] is string && data[field].size() >= minLength && data[field].size() <= maxLength);
    }

    // Validate optional map field
    function isValidOptionalMap(data, field) {
      return !(field in data) || data[field] is map;
    }

    // Validate optional boolean field
    function isValidOptionalBoolean(data, field) {
      return !(field in data) || data[field] is bool;
    }

    // Check if optional field values are the same between new and old data
    function isSameOptionalValue(newData, oldData, field) {
      return (!(field in newData) && !(field in oldData)) ||
             ((field in newData) && (field in oldData) && newData[field] == oldData[field]);
    }





    // =============================================================================
    // USER SUBCOLLECTIONS - WITH VALIDATION
    // =============================================================================

    // Financial Accounts - Enhanced with Granular Controls
    match /users/{userId}/accounts/{accountId} {
      // Granular read operations
      allow get: if isOwner(userId);
      allow list: if isOwner(userId);

      // Enhanced account creation with validation
      allow create: if isOwner(userId) && isValidAccountCreation(request.resource.data, userId, accountId);

      // Enhanced account updates with validation
      allow update: if isOwner(userId) && resource.data != null && isValidAccountUpdate(request.resource.data, resource.data, userId);

      // Enhanced account deletion
      allow delete: if isOwner(userId) && resource.data != null && canDeleteAccount(resource.data);
    }

    // =============================================================================
    // ACCOUNT VALIDATION FUNCTIONS
    // =============================================================================

    // Enhanced account creation validation with security controls
    function isValidAccountCreation(data, userId, accountId) {
      return data != null &&
             userId != null &&
             accountId != null &&
             request.auth != null &&
             request.auth.uid != null &&
             request.auth.uid == userId &&
             (!('id' in data) || data.id == accountId) &&
             ('userId' in data) &&
             data.userId == userId &&
             (!('name' in data) || (data.name is string && data.name.size() >= 1 && data.name.size() <= 100)) &&
             (!('type' in data) || data.type in ['checking', 'savings', 'creditCard', 'cash', 'investment', 'loan']) &&
             (!('classification' in data) || data.classification in ['asset', 'liability']) &&
             // Currency code removed - using global currency preference system
             (!('type' in data && 'classification' in data) || isValidTypeClassificationPair(data.type, data.classification)) &&
             // Validate initialBalanceCents field (optional, should be integer)
             (!('initialBalanceCents' in data) || (data.initialBalanceCents is int)) &&
             // Validate currentBalanceCents field (optional, should be integer)
             (!('currentBalanceCents' in data) || (data.currentBalanceCents is int)) &&
             // Validate description field (optional, should be string)
             (!('description' in data) || (data.description is string && data.description.size() <= 200)) &&
             // Validate iconName field (optional, should be string)
             (!('iconName' in data) || (data.iconName is string && data.iconName.size() > 0 && data.iconName.size() <= 50)) &&
             // Validate colorHex field (optional, should be hex color)
             (!('colorHex' in data) || isValidAccountColor(data.colorHex)) &&
             // Validate schemaVersion field (optional, should be integer)
             (!('schemaVersion' in data) || (data.schemaVersion is int && data.schemaVersion > 0)) &&
             // Validate isPrimary field (optional, should be boolean)
             (!('isPrimary' in data) || data.isPrimary is bool) &&
             // Validate isActive field (optional, should be boolean)
             (!('isActive' in data) || data.isActive is bool) &&
             // Validate createdAt field (optional, should be timestamp)
             (!('createdAt' in data) || data.createdAt is timestamp) &&
             // Validate updatedAt field (optional, should be timestamp)
             (!('updatedAt' in data) || data.updatedAt is timestamp) &&
             // Validate metadata field (optional, should be map)
             (!('metadata' in data) || data.metadata is map);
    }



    // Enhanced account updates validation with security controls
    function isValidAccountUpdate(newData, oldData, userId) {
      return newData != null &&
             oldData != null &&
             userId != null &&
             request.auth != null &&
             request.auth.uid != null &&
             request.auth.uid == userId &&
             (!('id' in newData) || !('id' in oldData) || newData.id == oldData.id) &&
             ('userId' in newData) &&
             ('userId' in oldData) &&
             newData.userId == oldData.userId &&
             newData.userId == userId &&
             (!('createdAt' in newData) || !('createdAt' in oldData) || newData.createdAt == oldData.createdAt) &&
             (!('name' in newData) || (newData.name is string && newData.name.size() >= 1 && newData.name.size() <= 100)) &&
             (!('type' in newData) || newData.type in ['checking', 'savings', 'creditCard', 'cash', 'investment', 'loan']) &&
             (!('classification' in newData) || newData.classification in ['asset', 'liability']) &&
             // Currency code removed - using global currency preference system
             (!('type' in newData && 'classification' in newData) || isValidTypeClassificationPair(newData.type, newData.classification)) &&
             // Validate initialBalanceCents field (optional, should be integer)
             (!('initialBalanceCents' in newData) || (newData.initialBalanceCents is int)) &&
             // Validate currentBalanceCents field (optional, should be integer)
             (!('currentBalanceCents' in newData) || (newData.currentBalanceCents is int)) &&
             // Validate description field (optional, should be string)
             (!('description' in newData) || (newData.description is string && newData.description.size() <= 200)) &&
             // Validate iconName field (optional, should be string)
             (!('iconName' in newData) || (newData.iconName is string && newData.iconName.size() > 0 && newData.iconName.size() <= 50)) &&
             // Validate colorHex field (optional, should be hex color)
             (!('colorHex' in newData) || isValidAccountColor(newData.colorHex)) &&
             // Validate schemaVersion field (optional, should be integer)
             (!('schemaVersion' in newData) || (newData.schemaVersion is int && newData.schemaVersion > 0)) &&
             // Validate isPrimary field (optional, should be boolean)
             (!('isPrimary' in newData) || newData.isPrimary is bool) &&
             // Validate isActive field (optional, should be boolean)
             (!('isActive' in newData) || newData.isActive is bool) &&
             // Validate updatedAt field (optional, should be timestamp)
             (!('updatedAt' in newData) || newData.updatedAt is timestamp) &&
             // Validate metadata field (optional, should be map)
             (!('metadata' in newData) || newData.metadata is map);
    }



    // Validate type/classification pair consistency
    function isValidTypeClassificationPair(type, classification) {
      return (
        (type in ['checking', 'savings', 'cash', 'investment'] && classification == 'asset') ||
        (type in ['creditCard', 'loan'] && classification == 'liability')
      );
    }

    // Transactions - Enhanced with Granular Controls and Rate Limiting
    match /users/{userId}/transactions/{transactionId} {
      // Granular read operations
      allow get: if isOwner(userId);
      allow list: if isOwner(userId);

      // Enhanced transaction creation with validation
      allow create: if isOwner(userId) && isValidTransactionCreation(request.resource.data, userId, transactionId);

      // Enhanced transaction updates with validation
      allow update: if isOwner(userId) && resource.data != null && isValidTransactionUpdate(request.resource.data, resource.data, userId);

      // Enhanced transaction deletion
      allow delete: if isOwner(userId);
    }

    // =============================================================================
    // TRANSACTION VALIDATION FUNCTIONS
    // =============================================================================

    // Enhanced transaction creation validation with security controls
    function isValidTransactionCreation(data, userId, transactionId) {
      return data != null &&
             userId != null &&
             transactionId != null &&
             request.auth != null &&
             request.auth.uid != null &&
             request.auth.uid == userId &&
             (!('id' in data) || data.id == transactionId) &&
             ('userId' in data) &&
             data.userId == userId &&
             (!('type' in data) || data.type in ['income', 'expense', 'transfer']) &&
             (!('status' in data) || data.status in ['pending', 'completed', 'cancelled', 'failed']) &&
             (!('amountCents' in data) || (data.amountCents is int && data.amountCents > 0)) &&
             (!('type' in data) || isValidTransactionAccountLogic(data)) &&
             // Validate schemaVersion field (optional, should be integer)
             (!('schemaVersion' in data) || (data.schemaVersion is int && data.schemaVersion > 0)) &&
             // Validate fromAccountId field (optional, should be string)
             (!('fromAccountId' in data) || (data.fromAccountId is string && data.fromAccountId.size() > 0)) &&
             // Validate toAccountId field (optional, should be string)
             (!('toAccountId' in data) || (data.toAccountId is string && data.toAccountId.size() > 0)) &&
             // Validate categoryId field (optional, should be string)
             (!('categoryId' in data) || (data.categoryId is string && data.categoryId.size() > 0)) &&
             // Validate description field (optional, should be string)
             (!('description' in data) || (data.description is string && data.description.size() > 0)) &&
             // Validate notes field (optional, should be string)
             (!('notes' in data) || (data.notes is string && data.notes.size() <= 500)) &&
             // Validate transactionDate field (optional, should be timestamp)
             (!('transactionDate' in data) || data.transactionDate is timestamp) &&
             // Validate createdAt field (optional, should be timestamp)
             (!('createdAt' in data) || data.createdAt is timestamp) &&
             // Validate updatedAt field (optional, should be timestamp)
             (!('updatedAt' in data) || data.updatedAt is timestamp) &&
             // Validate tagIds field (optional, should be list)
             (!('tagIds' in data) || data.tagIds is list);
    }

    // Validate transaction updates
    function isValidTransactionUpdate(newData, oldData, userId) {
      return newData != null &&
             oldData != null &&
             userId != null &&
             request.auth != null &&
             request.auth.uid != null &&
             request.auth.uid == userId &&
             (!('id' in newData) || !('id' in oldData) || newData.id == oldData.id) &&
             ('userId' in newData) &&
             ('userId' in oldData) &&
             newData.userId == oldData.userId &&
             newData.userId == userId &&
             (!('createdAt' in newData) || !('createdAt' in oldData) || newData.createdAt == oldData.createdAt) &&
             (!('type' in newData) || newData.type in ['income', 'expense', 'transfer']) &&
             (!('status' in newData) || newData.status in ['pending', 'completed', 'cancelled', 'failed']) &&
             (!('amountCents' in newData) || (newData.amountCents is int && newData.amountCents > 0)) &&
             (!('type' in newData) || isValidTransactionAccountLogic(newData)) &&
             // Validate schemaVersion field (optional, should be integer)
             (!('schemaVersion' in newData) || (newData.schemaVersion is int && newData.schemaVersion > 0)) &&
             // Validate fromAccountId field (optional, should be string)
             (!('fromAccountId' in newData) || (newData.fromAccountId is string && newData.fromAccountId.size() > 0)) &&
             // Validate toAccountId field (optional, should be string)
             (!('toAccountId' in newData) || (newData.toAccountId is string && newData.toAccountId.size() > 0)) &&
             // Validate categoryId field (optional, should be string)
             (!('categoryId' in newData) || (newData.categoryId is string && newData.categoryId.size() > 0)) &&
             // Validate description field (optional, should be string)
             (!('description' in newData) || (newData.description is string && newData.description.size() > 0)) &&
             // Validate notes field (optional, should be string)
             (!('notes' in newData) || (newData.notes is string && newData.notes.size() <= 500)) &&
             // Validate transactionDate field (optional, should be timestamp)
             (!('transactionDate' in newData) || newData.transactionDate is timestamp) &&
             // Validate updatedAt field (optional, should be timestamp)
             (!('updatedAt' in newData) || newData.updatedAt is timestamp) &&
             // Validate tagIds field (optional, should be list)
             (!('tagIds' in newData) || newData.tagIds is list);
    }

    // Validate transaction account logic based on type
    function isValidTransactionAccountLogic(data) {
      return data != null &&
             (!('type' in data) ||
              (data.type == 'transfer' && 'fromAccountId' in data && 'toAccountId' in data) ||
              (data.type == 'income' && 'toAccountId' in data) ||
              (data.type == 'expense' && 'fromAccountId' in data));
    }

    // Categories (user custom categories)
    match /users/{userId}/categories/{categoryId} {
      // Users can read their own categories
      allow read: if isOwner(userId);

      // Users can create categories with validation
      allow create: if isOwner(userId) && isValidCategory(request.resource.data, userId, categoryId);

      // Users can update categories with validation
      allow update: if isOwner(userId) && isValidCategoryUpdate(request.resource.data, resource.data, userId);

      // Users can delete categories only if they have no active references
      allow delete: if isOwner(userId) && canDeleteCategory(resource.data);
    }

    // Budgets
    match /users/{userId}/budgets/{budgetId} {
      // Users can read their own budgets
      allow read: if isOwner(userId);

      // Users can create budgets with validation
      allow create: if isOwner(userId) && isValidBudget(request.resource.data, userId, budgetId);

      // Users can update budgets with validation
      allow update: if isOwner(userId) && isValidBudgetUpdate(request.resource.data, resource.data, userId);

      // Users can delete budgets
      allow delete: if isOwner(userId);
    }

    // =============================================================================
    // CATEGORY VALIDATION FUNCTIONS
    // =============================================================================

    // Validate category creation
    function isValidCategory(data, userId, categoryId) {
      return hasRequiredFields(data, ['id', 'userId', 'name', 'type', 'schemaVersion', 'createdAt', 'updatedAt']) &&
             data.id == categoryId &&
             data.userId == userId &&
             isValidStringLength(data.name, 2, 50) &&
             isValidEnum(data.type, ['income', 'expense']) &&
             isValidOptionalBoolean(data, 'isActive') &&
             (!('sortOrder' in data) || (data.sortOrder is int && data.sortOrder >= 0 && data.sortOrder <= 999)) &&
             data.schemaVersion == 1 &&
             isValidTimestamp(data.createdAt) &&
             isValidTimestamp(data.updatedAt) &&
             isValidOptionalString(data, 'parentId', 1, 100) &&
             isValidOptionalString(data, 'description', 0, 200) &&
             isValidOptionalString(data, 'icon', 1, 50) &&
             isValidOptionalMap(data, 'metadata') &&
             isValidCategoryColor(data) &&
             isValidCategoryHierarchy(data);
    }

    // Validate category updates
    function isValidCategoryUpdate(newData, oldData, userId) {
      return newData.id == oldData.id &&
             newData.userId == oldData.userId &&
             newData.userId == userId &&
             newData.type == oldData.type && // Prevent type changes
             isSameOptionalValue(newData, oldData, 'parentId') && // Prevent parent changes (use separate move operation)
             newData.schemaVersion == oldData.schemaVersion &&
             newData.createdAt == oldData.createdAt &&
             isValidStringLength(newData.name, 2, 50) &&
             isValidOptionalBoolean(newData, 'isActive') &&
             (!('sortOrder' in newData) || (newData.sortOrder is int && newData.sortOrder >= 0 && newData.sortOrder <= 999)) &&
             isValidTimestamp(newData.updatedAt) &&
             isValidOptionalString(newData, 'description', 0, 200) &&
             isValidOptionalString(newData, 'icon', 1, 50) &&
             isValidOptionalMap(newData, 'metadata') &&
             isValidCategoryColor(newData);
    }

    // Validate category color format (#RRGGBB or #RGB)
    function isValidCategoryColor(data) {
      return !('color' in data) || (data.color is string && data.color.matches('^#([0-9A-Fa-f]{6}|[0-9A-Fa-f]{3})$'));
    }

    // Validate category hierarchy constraints
    function isValidCategoryHierarchy(data) {
      // For now, basic validation. Parent existence validation limited by Firestore Security Rules
      // Full hierarchy validation (depth limits, circular references) must be enforced client-side
      return !('parentId' in data) ||
             data.parentId == null ||
             (data.parentId is string && data.parentId.size() <= 100 && data.parentId != data.id);
    }

    // =============================================================================
    // BUDGET VALIDATION FUNCTIONS
    // =============================================================================

    // Validate budget creation
    function isValidBudget(data, userId, budgetId) {
      return hasRequiredFields(data, ['id', 'userId', 'type', 'plannedAmountCents', 'currentAmountCents', 'period', 'periodStart', 'isActive', 'schemaVersion', 'createdAt', 'updatedAt']) &&
             data.id == budgetId &&
             data.userId == userId &&
             isValidEnum(data.type, ['expense', 'income']) &&
             data.plannedAmountCents is int && data.plannedAmountCents > 0 &&
             data.currentAmountCents is int && data.currentAmountCents >= 0 &&
             isValidEnum(data.period, ['monthly', 'yearly']) &&
             isValidTimestamp(data.periodStart) &&
             data.isActive is bool &&
             data.schemaVersion == 1 &&
             isValidTimestamp(data.createdAt) &&
             isValidTimestamp(data.updatedAt) &&
             isValidOptionalString(data, 'categoryId', 1, 50) &&
             isValidOptionalString(data, 'parentBudgetId', 1, 50) &&
             isValidOptionalMap(data, 'metadata') &&
             isValidBudgetPeriodStart(data);
             // Temporarily removed for compatibility: referencedCategoryExists(data, userId)
    }

    // Validate budget updates
    function isValidBudgetUpdate(newData, oldData, userId) {
      return newData.id == oldData.id &&
             newData.userId == oldData.userId &&
             newData.userId == userId &&
             newData.createdAt == oldData.createdAt &&
             newData.schemaVersion == oldData.schemaVersion &&
             newData.period == oldData.period && // Prevent period type changes
             newData.periodStart == oldData.periodStart && // Prevent period start changes
             isValidEnum(newData.type, ['expense', 'income']) &&
             newData.plannedAmountCents is int && newData.plannedAmountCents > 0 &&
             newData.currentAmountCents is int && newData.currentAmountCents >= 0 &&
             isValidEnum(newData.period, ['monthly', 'yearly']) &&
             isValidTimestamp(newData.periodStart) &&
             newData.isActive is bool &&
             isValidTimestamp(newData.updatedAt) &&
             isValidOptionalString(newData, 'categoryId', 1, 50) &&
             isValidOptionalString(newData, 'parentBudgetId', 1, 50) &&
             isValidOptionalMap(newData, 'metadata');
             // Temporarily removed for compatibility: referencedCategoryExists(newData, userId)
    }



    // Validate budget period start date consistency
    function isValidBudgetPeriodStart(data) {
      // For monthly budgets, periodStart should be the first day of a month
      // For yearly budgets, periodStart should be January 1st
      // Note: Firestore Security Rules have limited date manipulation capabilities
      // Full validation should be done client-side
      return isValidTimestamp(data.periodStart) &&
             data.periodStart <= request.time; // Cannot be in the future beyond current time
    }

    // =============================================================================
    // REFERENTIAL INTEGRITY HELPER FUNCTIONS
    // =============================================================================
    // 
    // ⚠️  KNOWN LIMITATION: These functions provide WEAK referential integrity
    // enforcement due to Firestore Security Rules query limitations.
    //
    // SECURITY RISK: These functions only check isActive flags but do NOT verify
    // that no dependent transactions exist. This relies entirely on app logic
    // to perform dependency checks before setting isActive = false.
    //
    // IMPACT: Direct API calls bypassing app logic could delete accounts/categories
    // with transactions, creating orphaned data and potential data corruption.
    //
    // MITIGATION: 
    // - App must perform thorough dependency checks before deletion
    // - Consider soft deletion with recovery capability
    // - Plan Cloud Functions for production (Phase 2) to enforce true referential integrity
    //
    // CATEGORY DELETION CONSTRAINTS (App-Level Enforcement Required):
    // 1. Check for dependent transactions: getTransactionsReferencingCategory()
    // 2. Check for child subcategories: hasChildSubcategories()
    // 3. Check for budget allocations: getBudgetsUsingCategory()
    // 4. Validate user permissions and category ownership
    // 5. Ensure proper soft deletion workflow (deactivate → validate → delete)
    // =============================================================================

    // Check if account can be safely deleted (LIMITED ENFORCEMENT)
    function canDeleteAccount(accountData) {
      // Account must exist and be inactive to be deleted
      return accountData != null &&
             ('isActive' in accountData) &&
             accountData.isActive == false;
    }

    // Check if category can be safely deleted (LIMITED ENFORCEMENT)
    function canDeleteCategory(categoryData) {
      // ⚠️  WARNING: This only checks isActive flag and source type, NOT actual transaction/budget dependencies
      // The app MUST ensure no dependent transactions/budgets exist before setting isActive = false
      // Direct API deletion could bypass this check and create orphaned references
      //
      // ENHANCED CONSTRAINTS:
      // 1. Category must be deactivated (isActive = false) before deletion
      // 2. Predefined categories cannot be deleted (only custom categories)
      // 3. App-level validation MUST check for:
      //    - Dependent transactions referencing this category
      //    - Child subcategories under this category
      //    - Budget allocations using this category
      return categoryData != null &&
             ('isActive' in categoryData) &&
             categoryData.isActive == false; // Allow deletion of any inactive category
    }

    // Check if tag can be safely deleted (LIMITED ENFORCEMENT)
    function canDeleteTag(tagData) {
      // ⚠️  WARNING: This only checks isActive flag, NOT actual transaction dependencies
      // The app MUST ensure no dependent transactions exist before setting isActive = false
      // Direct API deletion could bypass this check and create orphaned tag references
      return tagData != null &&
             ('isActive' in tagData) &&
             tagData.isActive == false;
    }





    // Goals (financial goals)
    match /users/{userId}/goals/{goalId} {
      // Granular read operations
      allow get: if isOwner(userId);
      allow list: if isOwner(userId);

      // Enhanced goal creation with validation
      allow create: if isOwner(userId) && isValidGoalCreation(request.resource.data, userId, goalId);

      // Enhanced goal updates with validation
      allow update: if isOwner(userId) && resource.data != null && isValidGoalUpdate(request.resource.data, resource.data, userId);

      // Enhanced goal deletion
      allow delete: if isOwner(userId);

      // Goal Contributions (subcollection)
      match /contributions/{contributionId} {
        // Granular read operations
        allow get: if isOwner(userId);
        allow list: if isOwner(userId);

        // Enhanced contribution creation with validation
        allow create: if isOwner(userId) && isValidGoalContributionCreation(request.resource.data, userId, goalId, contributionId);

        // Enhanced contribution updates with validation
        allow update: if isOwner(userId) && resource.data != null && isValidGoalContributionUpdate(request.resource.data, resource.data, userId, goalId);

        // Enhanced contribution deletion
        allow delete: if isOwner(userId);
      }
    }

    // Tags (user-defined transaction tags)
    match /users/{userId}/tags/{tagId} {
      // Users can read their own tags
      allow read: if isOwner(userId);

      // Users can create tags with validation
      allow create: if isOwner(userId) && isValidTag(request.resource.data, userId, tagId);

      // Users can update tags with validation
      allow update: if isOwner(userId) && isValidTagUpdate(request.resource.data, resource.data, userId);

      // Users can delete tags only if they have no active references
      allow delete: if isOwner(userId) && canDeleteTag(resource.data);
    }

    // =============================================================================
    // GOAL VALIDATION FUNCTIONS
    // =============================================================================

    // Enhanced goal creation validation with comprehensive field validation
    function isValidGoalCreation(data, userId, goalId) {
      return data != null &&
             userId != null &&
             goalId != null &&
             request.auth != null &&
             request.auth.uid != null &&
             request.auth.uid == userId &&
             hasRequiredFields(data, ['id', 'userId', 'name', 'targetAmountCents', 'createdAt', 'updatedAt']) &&
             (!('id' in data) || data.id == goalId) &&
             data.userId == userId &&
             isValidStringLength(data.name, 2, 100) &&
             data.targetAmountCents is int && data.targetAmountCents > 0 && data.targetAmountCents <= ************ &&
             isValidTimestamp(data.createdAt) &&
             isValidTimestamp(data.updatedAt) &&
             // Optional fields validation
             isValidOptionalString(data, 'description', 0, 500) &&
             (!('currentAmountCents' in data) || (data.currentAmountCents is int && data.currentAmountCents >= 0)) &&
             (!('targetDate' in data) || data.targetDate == null || (data.targetDate is timestamp && data.targetDate > request.time)) &&
             (!('isCompleted' in data) || data.isCompleted is bool) &&
             (!('colorHex' in data) || isValidGoalColorHex(data.colorHex)) &&
             (!('iconName' in data) || isValidGoalIconName(data.iconName)) &&
             (!('status' in data) || isValidEnum(data.status, ['active', 'paused', 'completed', 'cancelled'])) &&
             (!('isActive' in data) || data.isActive is bool) &&
             (!('schemaVersion' in data) || (data.schemaVersion is int && data.schemaVersion > 0)) &&
             isValidOptionalMap(data, 'metadata');
    }

    // Enhanced goal updates validation with comprehensive field validation
    function isValidGoalUpdate(newData, oldData, userId) {
      return newData != null &&
             oldData != null &&
             userId != null &&
             request.auth != null &&
             request.auth.uid != null &&
             request.auth.uid == userId &&
             (!('id' in newData) || !('id' in oldData) || newData.id == oldData.id) &&
             ('userId' in newData) &&
             ('userId' in oldData) &&
             newData.userId == oldData.userId &&
             newData.userId == userId &&
             (!('createdAt' in newData) || !('createdAt' in oldData) || newData.createdAt == oldData.createdAt) &&
             (!('schemaVersion' in newData) || !('schemaVersion' in oldData) || newData.schemaVersion == oldData.schemaVersion) &&
             (!('name' in newData) || isValidStringLength(newData.name, 2, 100)) &&
             (!('targetAmountCents' in newData) || (newData.targetAmountCents is int && newData.targetAmountCents > 0 && newData.targetAmountCents <= ************)) &&
             (!('updatedAt' in newData) || isValidTimestamp(newData.updatedAt)) &&
             // Optional fields validation
             isValidOptionalString(newData, 'description', 0, 500) &&
             (!('currentAmountCents' in newData) || (newData.currentAmountCents is int && newData.currentAmountCents >= 0)) &&
             (!('targetDate' in newData) || (newData.targetDate is timestamp && newData.targetDate > request.time)) &&
             (!('isCompleted' in newData) || newData.isCompleted is bool) &&
             (!('colorHex' in newData) || isValidGoalColorHex(newData.colorHex)) &&
             (!('iconName' in newData) || isValidGoalIconName(newData.iconName)) &&
             (!('status' in newData) || isValidEnum(newData.status, ['active', 'paused', 'completed', 'cancelled'])) &&
             (!('isActive' in newData) || newData.isActive is bool) &&
             isValidOptionalMap(newData, 'metadata');
    }

    // =============================================================================
    // GOAL HELPER VALIDATION FUNCTIONS
    // =============================================================================

    // Validate goal color hex format (#RRGGBB)
    function isValidGoalColorHex(colorHex) {
      return colorHex is string && colorHex.matches('^#[0-9A-Fa-f]{6}$');
    }

    // Validate goal icon name format (alphanumeric with underscores)
    function isValidGoalIconName(iconName) {
      return iconName is string && iconName.size() >= 1 && iconName.size() <= 50 && iconName.matches('^[a-zA-Z][a-zA-Z0-9_]*$');
    }

    // Validate tag color hex format (#RRGGBB or #RGB)
    function isValidTagColor(color) {
      return color is string && color.matches('^#([0-9A-Fa-f]{6}|[0-9A-Fa-f]{3})$');
    }

    // Validate account color hex format (#RRGGBB)
    function isValidAccountColor(color) {
      return color is string && color.matches('^#[0-9A-Fa-f]{6}$');
    }

    // =============================================================================
    // GOAL CONTRIBUTION VALIDATION FUNCTIONS
    // =============================================================================

    // Enhanced goal contribution creation validation
    function isValidGoalContributionCreation(data, userId, goalId, contributionId) {
      return data != null &&
             userId != null &&
             goalId != null &&
             contributionId != null &&
             request.auth != null &&
             request.auth.uid != null &&
             request.auth.uid == userId &&
             hasRequiredFields(data, ['id', 'userId', 'goalId', 'amountCents', 'contributionDate', 'createdAt', 'updatedAt']) &&
             (!('id' in data) || data.id == contributionId) &&
             data.userId == userId &&
             data.goalId == goalId &&
             data.amountCents is int && data.amountCents > 0 && data.amountCents <= ************ &&
             isValidTimestamp(data.contributionDate) &&
             data.contributionDate <= request.time && // Cannot be in future
             data.contributionDate >= timestamp.date(2014, 1, 1) && // Not older than 10 years (approximately)
             isValidTimestamp(data.createdAt) &&
             isValidTimestamp(data.updatedAt) &&
             // Optional fields validation
             isValidOptionalString(data, 'description', 0, 500) &&
             (!('isActive' in data) || data.isActive is bool) &&
             (!('schemaVersion' in data) || (data.schemaVersion is int && data.schemaVersion > 0)) &&
             isValidOptionalMap(data, 'metadata');
    }

    // Enhanced goal contribution updates validation
    function isValidGoalContributionUpdate(newData, oldData, userId, goalId) {
      return newData != null &&
             oldData != null &&
             userId != null &&
             goalId != null &&
             request.auth != null &&
             request.auth.uid != null &&
             request.auth.uid == userId &&
             (!('id' in newData) || !('id' in oldData) || newData.id == oldData.id) &&
             ('userId' in newData) &&
             ('userId' in oldData) &&
             newData.userId == oldData.userId &&
             newData.userId == userId &&
             ('goalId' in newData) &&
             ('goalId' in oldData) &&
             newData.goalId == oldData.goalId &&
             newData.goalId == goalId &&
             (!('createdAt' in newData) || !('createdAt' in oldData) || newData.createdAt == oldData.createdAt) &&
             (!('schemaVersion' in newData) || !('schemaVersion' in oldData) || newData.schemaVersion == oldData.schemaVersion) &&
             (!('amountCents' in newData) || (newData.amountCents is int && newData.amountCents > 0 && newData.amountCents <= ************)) &&
             (!('contributionDate' in newData) || (isValidTimestamp(newData.contributionDate) && newData.contributionDate <= request.time && newData.contributionDate >= timestamp.date(2014, 1, 1))) &&
             (!('updatedAt' in newData) || isValidTimestamp(newData.updatedAt)) &&
             // Optional fields validation
             isValidOptionalString(newData, 'description', 0, 500) &&
             (!('isActive' in newData) || newData.isActive is bool) &&
             isValidOptionalMap(newData, 'metadata');
    }

    // =============================================================================
    // TAG VALIDATION FUNCTIONS
    // =============================================================================

    // Validate tag creation
    function isValidTag(data, userId, tagId) {
      return hasRequiredFields(data, ['id', 'userId', 'name', 'color', 'createdAt', 'updatedAt']) &&
             data.id == tagId &&
             data.userId == userId &&
             isValidStringLength(data.name, 1, 50) &&
             isValidTagColor(data.color) &&
             isValidTimestamp(data.createdAt) &&
             isValidTimestamp(data.updatedAt) &&
             (!('usageCount' in data) || (data.usageCount is int && data.usageCount >= 0)) &&
             (!('schemaVersion' in data) || (data.schemaVersion is int && data.schemaVersion > 0)) &&
             isValidOptionalBoolean(data, 'isActive');
    }

    // Validate tag updates
    function isValidTagUpdate(newData, oldData, userId) {
      return newData.id == oldData.id &&
             newData.userId == oldData.userId &&
             newData.userId == userId &&
             newData.createdAt == oldData.createdAt &&
             (!('schemaVersion' in newData) || !('schemaVersion' in oldData) || newData.schemaVersion == oldData.schemaVersion) &&
             isValidStringLength(newData.name, 1, 50) &&
             isValidTagColor(newData.color) &&
             isValidTimestamp(newData.updatedAt) &&
             (!('usageCount' in newData) || (newData.usageCount is int && newData.usageCount >= 0)) &&
             isValidOptionalBoolean(newData, 'isActive');
    }

    // =============================================================================
    // DENY ALL OTHER ACCESS
    // =============================================================================

    // Explicitly deny access to any other documents
    // This ensures that only the explicitly allowed paths above are accessible
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
