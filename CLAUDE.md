# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Start

### Essential Commands
```bash
# Development
flutter run                                    # Run app (dev environment)
flutter pub get                               # Install dependencies
dart run build_runner build --delete-conflicting-outputs  # Code generation
dart format . && flutter analyze              # Format and analyze

# Testing  
flutter test --coverage                       # Run tests with coverage
./scripts/start_emulators.sh                  # Start Firebase emulators
cd firebase/test && npm test                  # Test security rules

# Building (use enhanced scripts)
./scripts/build_android.sh dev apk           # Development build
./scripts/build_android.sh staging appbundle # Staging build
./scripts/build_android.sh prod appbundle    # Production build
```

### Quick Reference
| Task | Command | Purpose |
|------|---------|---------|
| Run app | `flutter run` | Development environment |
| Test changes | `flutter test` | Run all tests |
| Build for release | `./scripts/build_android.sh prod appbundle` | Production build |
| Update models | `dart run build_runner build --delete-conflicting-outputs` | After Freezed changes |
| Check quality | `dart format . && flutter analyze` | Code formatting + analysis |

## Architecture Overview

**BudApp** is a Flutter personal finance app using **feature-based architecture** with clean separation of concerns.

### Key Technologies
- **Flutter** with Dart 3.8.1+ and Material 3 design
- **Firebase** (Auth, Firestore, Remote Config, App Check)
- **Riverpod** for state management with AsyncNotifier pattern
- **Freezed** for immutable data models with JSON serialization
- **go_router** for navigation with authentication guards

### Project Structure
```
lib/
├── features/           # Feature-based modules (auth, accounts, transactions, budgets, goals)
├── data/              # Shared data layer (models, repositories)
├── services/          # Global services (Firestore, Remote Config, Session)
├── providers/         # Riverpod providers and dependency injection
├── routing/           # go_router configuration with auth guards
└── widgets/           # Reusable UI components
```

### Multi-Environment Setup
- **Development** (default): `budapp-dev` - Orange theme
- **Staging**: `budapp-staging-1` - Blue theme  
- **Production**: `budapp-prod` - Green theme

Environment auto-detected via `--dart-define=FLAVOR` parameter.

## Development Guidelines

### Core Patterns
- Use `ConsumerWidget`/`ConsumerStatefulWidget` for Riverpod integration
- Implement error handling with `AsyncValue.guard()`
- Follow feature-based organization - keep related code together
- Use dependency injection via Riverpod providers
- Prefer immutable data structures with Freezed

### Repository Pattern (CRITICAL)
**Strict adherence enforced across all layers.**

✅ **CORRECT Usage:**
```dart
// Use repository interfaces in business logic
class MyService {
  final IUserRepository _userRepository;
  MyService(this._userRepository);
}

// Repository implementations with dependency injection
class UserRepositoryImpl implements IUserRepository {
  final FirestoreService _firestoreService;
  UserRepositoryImpl(this._firestoreService);
}
```

❌ **FORBIDDEN:**
```dart
// NEVER: Direct Firebase access outside providers
final user = FirebaseAuth.instance.currentUser; // VIOLATION!
final doc = FirebaseFirestore.instance.collection('users'); // VIOLATION!
```

**Architecture Rules:**
1. **Presentation Layer**: Only access data through services/repository interfaces
2. **Service Layer**: Abstract Firebase functionality, inject dependencies
3. **Repository Layer**: Implement interfaces, use dependency injection
4. **Data Layer**: Models only, no Firebase dependencies

### Currency System
**Global currency preference** - no per-record currency fields.

```dart
// ✅ CORRECT: Use global currency formatter
final currencyFormatter = ref.watch(currencyFormatterProvider);
Text(currencyFormatter.formatAmount(amountCents));

// ✅ CORRECT: Store amounts as integer cents
final transaction = Transaction.create(
  amountCents: (29.99 * 100).round(), // Store as 2999 cents
);
```

### Logging Guidelines (Security Critical)
**Production-safe structured logging with PII protection.**

```dart
// ✅ CORRECT: Use logging service with automatic PII sanitization
import 'package:budapp/services/logging_service.dart';

log.info('User authentication successful');
log.error('Payment processing error', error: exception);

// ✅ CORRECT: Guard debug logs
if (kDebugMode) {
  log.debug('Debug message'); // Properly guarded
}

// ❌ NEVER: Direct print statements
print('User email: <EMAIL>'); // Exposes PII in production!
```

**PII Auto-Sanitization:** Emails, phone numbers, financial amounts, API keys automatically redacted.

### Error Handling Guidelines (Production Critical)
**Comprehensive centralized error handling with Firebase Crashlytics integration.**

```dart
// ✅ CORRECT: Use ErrorService for user-facing errors
import 'package:budapp/services/error_service.dart';

// Show user-friendly error with crash reporting
ErrorService.showErrorSnackBarWithLogging(
  context,
  error,
  errorContext: 'Payment processing',
  additionalData: {'amount': amountCents, 'accountId': accountId},
);

// Log errors for crash reporting (automatic PII sanitization)
await ErrorService.logError(
  error,
  stackTrace,
  context: 'Transaction creation',
  additionalData: {'userId': userId},
  fatal: false,
);

// Track user actions for crash context
ErrorService.logUserAction('Transaction created', {'type': 'income'});
```

**Error Boundary Pattern:**
```dart
// ✅ CORRECT: Wrap risky widgets with error boundaries
Widget build(BuildContext context) {
  return MyComplexWidget().withErrorBoundary(
    fallbackBuilder: (context, error, stackTrace, retry) =>
      CustomErrorWidget(onRetry: retry),
    onError: (error, stackTrace) =>
      analytics.logError('Widget error', error),
  );
}// ✅ CORRECT: Simple error boundary
return MyWidget().safely(
  fallback: Text('Something went wrong'),
);
```

**Architecture Features:**
- **Firebase Crashlytics**: Automatic crash reporting with user context
- **Global Error Handlers**: FlutterError.onError, PlatformDispatcher, Isolate errors
- **Error Boundaries**: Graceful UI degradation with retry mechanisms
- **User Action Tracking**: Breadcrumb logging for crash context
- **Environment Aware**: Debug vs release behavior, PII protection

## Testing Essentials

### Testing Strategy
- **Target Coverage**: 90% (currently 50.2%)
- **Priority**: High-impact files (0% coverage + high line count)
- **Test Suite Health**: 1587+ passing tests

### Key Testing Patterns
```bash
# Run all tests with coverage
flutter test --coverage

# Run specific test files  
flutter test test/features/auth/auth_test.dart

# Coverage analysis
lcov --list coverage/lcov.info | grep filename

# Firebase emulator testing
./scripts/start_emulators.sh
./scripts/test_with_emulators.sh
```

### Testing Infrastructure
- **Mock Factory**: Use `MockDataFactory` for consistent test data
- **Firebase Mocking**: Use `mocktail` for Firebase services
- **Provider Testing**: `ProviderContainer` with overrides
- **Widget Testing**: `ProviderScope` with mock dependencies

## Common Tasks

### Adding New Features
1. Create feature directory in `lib/features/`
2. Add presentation layer (screens, widgets)
3. Create services for business logic
4. Add Riverpod providers for state management
5. Update routing if needed
6. Write tests following existing patterns

**Reference**: Account Management (`lib/features/accounts/`) provides complete CRUD example.

### Updating Data Models
1. Modify Freezed models in `lib/data/models/`
2. Run `dart run build_runner build --delete-conflicting-outputs`
3. Update repository interfaces if needed
4. Update Firestore security rules if schema changes
5. Test with `cd firebase/test && npm test`

**Freezed Pattern** (Critical for JSON serialization):
```dart
@freezed
@JsonSerializable()
class MyModel with _$MyModel {
  const factory MyModel({
    required String id,
    @Default(1) int schemaVersion,
  }) = _MyModel;

  // CRITICAL: Private constructor required for toJson()
  const MyModel._();

  factory MyModel.fromJson(Map<String, dynamic> json) {
    final convertedJson = Map<String, dynamic>.from(json);
    if (convertedJson['schemaVersion'] == null) {
      convertedJson['schemaVersion'] = 1;
    }
    return _$MyModelFromJson(convertedJson);
  }
}
```

### Environment Management
```bash
# Switch environments before building
./scripts/select_firebase_config.sh staging
./scripts/build_android.sh staging appbundle
./scripts/select_firebase_config.sh dev  # Reset to dev
```

## Security & Production

### Android Signing
🔐 **Critical Enhancement**: Environment-specific keystores eliminate debug keys.
- ✅ No debug keys in production
- ✅ Automated signature validation
- ✅ Secure CI/CD integration

**Documentation**: [`docs/ANDROID_SIGNING.md`](docs/ANDROID_SIGNING.md)

### CI/CD Pipeline
🚀 **Branch-based deployment** following 2024 best practices:
- **Development Branch** → Dev environment (automatic)
- **Main Branch** → Staging environment (automatic)  
- **Production Branch** → Production environment (manual only)

```bash
# Development deployment
git push origin development

# Production deployment (manual)
gh workflow run prod-build.yml -f build_type=appbundle -f confirmation=PRODUCTION
```

**Documentation**: [`docs/GITHUB_WORKFLOWS_SECURITY.md`](docs/GITHUB_WORKFLOWS_SECURITY.md)

### Firebase Security
- Firestore Security Rules with user data isolation
- Firebase App Check with environment-specific providers
- Comprehensive security rules testing (32 tests)
- PII protection in all logging

## Current Status

### ✅ Completed Features
- Feature-based architecture migration
- Authentication system (email/password, Google OAuth)
- State management with Riverpod
- Multi-environment setup (dev/staging/prod)
- **Account Management**: Full CRUD with Material 3 design
- **Transaction System**: CRUD operations with atomic balance updates
- **Budget Management**: Period management with transaction integration
- **Category Management**: Unified hierarchical system
- **Tag Management**: Complete tagging with many-to-many relationships
- **Goal System**: Data models and repositories complete
- **Firebase Remote Config**: Server-side configuration management
- **Global Currency System**: Centralized currency preferences
- **Production-Safe Logging**: PII protection with environment-aware configuration
- **Centralized Error Handling**: Firebase Crashlytics integration with global error boundaries

### 🚧 Ready for Implementation
**Goal UI Features**: Goal screens, contribution entry, progress tracking can now be implemented using established repository foundation.

### Quality Metrics
- **Test Coverage**: 50.2% (target: 90%)
- **Test Suite**: 1587+ passing tests
- **Code Quality**: "No issues found!" (Flutter analyze)
- **Architecture**: Feature-based with clean separation

## Documentation & Resources

### Quick Access
- **Architecture**: [`docs/architecture.md`](docs/architecture.md) - System overview with diagrams
- **Testing Guide**: [`docs/TESTING.md`](docs/TESTING.md) - Comprehensive testing strategy
- **Firebase Security**: [`firebase/README.md`](firebase/README.md) - Security architecture
- **Environment Setup**: [`docs/ENVIRONMENT_SETUP.md`](docs/ENVIRONMENT_SETUP.md) - Development setup

### Complete Documentation Index
📚 **See [`docs/DOCUMENTATION_INDEX.md`](docs/DOCUMENTATION_INDEX.md)** for comprehensive documentation organized by category and development task.

## Memory Bank
Use mcp-memory bank MCP for Memory Bank.
When asking to read memory bank, use `get_memory_bank_structure` tool from mcp-memory-bank
Memory bank files are located in project's `.taskmaster/memory-bank/` directory
Instructions are in .taskmaster/memory-bank/memory_bank_instructions.md
Use Read tool with absolute paths: /Users/<USER>/development/budapp/.taskmaster/memory-bank [filename] 
Don't use ReadMcpResourceTool for memory bank files 
Continue using mcp-memory-bank tools for structure and analysis 
Always use LS tool first to verify file existence
Use absolute paths consistently 
Fall back to direct file reading if MCP resource access fails

## Task Master
For task management, refer to Task Master MCP

---
# Codacy Rules
Configuration for AI behavior when interacting with Codacy's MCP Server

## After ANY successful file edit
- YOU MUST run the `codacy_cli_analyze` tool for each edited file
- If any issues are found, propose and apply fixes immediately
- This is a critical requirement, not optional

## After ANY dependency installation
- IMMEDIATELY run `codacy_cli_analyze` with tool set to "trivy"
- Check for security vulnerabilities in new packages
- Stop all operations if vulnerabilities found
- Fix security issues before continuing

## Repository setup
- Use `git remote -v` to determine provider/organization/repository
- Provider mapping: GitHub = "gh", Bitbucket = "bb", GitLab = "gl"
- If repository not found in Codacy, offer to run `codacy_setup_repository`
---


---
*For detailed documentation, implementation guides, and comprehensive references, see [`docs/DOCUMENTATION_INDEX.md`](docs/DOCUMENTATION_INDEX.md)*