# BudApp Static Analysis Configuration
# This file configures the Dart analyzer with strict linting rules to ensure
# high code quality, consistency, and maintainability across the project.
#
# The configuration includes:
# - Comprehensive linting rules for code quality
# - Strict error handling and null safety enforcement
# - Performance optimization guidelines
# - Documentation requirements
# - Team collaboration standards
#
# For more information about analysis options:
# https://dart.dev/guides/language/analysis-options

# Include very_good_analysis for stricter, production-quality rules
include: package:very_good_analysis/analysis_options.yaml

# Analyzer configuration
analyzer:
  # Exclude generated files and build artifacts from analysis
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.gr.dart"
    - "**/*.config.dart"
    - "**/generated_plugin_registrant.dart"
    - "build/**"
    - ".dart_tool/**"
    - "coverage/**"
    - "test/.test_coverage.dart"
    - "integration_test/driver.dart"

  # Enable strict mode for better error detection
  strong-mode:
    implicit-casts: false
    implicit-dynamic: false

  # Language configuration
  language:
    strict-casts: true
    strict-inference: true
    strict-raw-types: true

  # Error configuration - treat warnings as errors in CI/CD
  errors:
    # Treat specific warnings as errors for critical issues
    invalid_assignment: error
    invalid_return_type_for_catch_error: error
    missing_required_param: error
    missing_return: error
    dead_code: error
    unused_import: error
    unused_local_variable: error

    # Security-related issues as errors
    avoid_web_libraries_in_flutter: error

    # Performance issues as errors
    avoid_slow_async_io: error

    # Code style consistency - make trailing commas an error to match Codacy
    require_trailing_commas: error

    # Documentation requirements (warnings for now, can be upgraded to errors)
    public_member_api_docs: false

# Comprehensive linting rules for code quality and consistency
linter:
  rules:
    # === STYLE RULES ===
    # Naming conventions
    camel_case_types: true
    camel_case_extensions: true
    file_names: true
    library_names: true
    library_prefixes: true
    non_constant_identifier_names: true
    constant_identifier_names: true

    # Code formatting and style
    prefer_single_quotes: true
    prefer_double_quotes: false
    require_trailing_commas: true
    sort_child_properties_last: true
    sort_constructors_first: true
    sort_unnamed_constructors_first: true

    # === ERROR PREVENTION ===
    # Null safety and type safety
    avoid_null_checks_in_equality_operators: true
    avoid_returning_null_for_future: true
    avoid_returning_null_for_void: true
    prefer_null_aware_operators: true
    unnecessary_null_aware_assignments: true
    unnecessary_null_checks: true
    unnecessary_null_in_if_null_operators: true

    # Exception handling
    avoid_catching_errors: true
    use_rethrow_when_possible: true
    only_throw_errors: true

    # === PERFORMANCE ===
    # Widget and build optimization
    avoid_unnecessary_containers: true
    sized_box_for_whitespace: true
    use_key_in_widget_constructors: true
    prefer_const_constructors: true
    prefer_const_constructors_in_immutables: true
    prefer_const_declarations: true
    prefer_const_literals_to_create_immutables: true

    # Collection and iteration optimization
    prefer_collection_literals: true
    prefer_for_elements_to_map_fromIterable: true
    prefer_if_elements_to_conditional_expressions: true
    prefer_spread_collections: true

    # === CODE ORGANIZATION ===
    # Import organization
    directives_ordering: true
    avoid_relative_lib_imports: true

    # Class and method organization
    sort_pub_dependencies: true
    prefer_mixin: true
    avoid_private_typedef_functions: true

    # === DOCUMENTATION ===
    # Public API documentation
    public_member_api_docs: true
    package_api_docs: true

    # Comment quality
    slash_for_doc_comments: true

    # === SECURITY ===
    # Web security
    avoid_web_libraries_in_flutter: true

    # === MAINTAINABILITY ===
    # Code complexity
    # Note: avoid_classes_with_only_static_members disabled after comprehensive refactoring
    # Simple utilities converted to functions; complex classes use strategic suppressions
    avoid_classes_with_only_static_members: false
    avoid_function_literals_in_foreach_calls: true
    prefer_function_declarations_over_variables: true

    # Variable and field management
    prefer_final_fields: true
    prefer_final_in_for_each: true
    prefer_final_locals: true
    unnecessary_late: true

    # Method and constructor optimization
    prefer_initializing_formals: true
    unnecessary_constructor_name: true
    unnecessary_overrides: true

    # === FLUTTER SPECIFIC ===
    # Widget best practices (avoiding duplicates from above)
    use_build_context_synchronously: true
    use_colored_box: true
    use_decorated_box: true

    # === DISABLED RULES (Break Freezed models or remove necessary defaults) ===
    # CRITICAL: These rules break Freezed constructor patterns
    always_put_required_named_parameters_first: false
    
    # CRITICAL: These rules remove necessary default values from configurations
    avoid_redundant_argument_values: false
    
    # CRITICAL: These rules conflict with always_use_package_imports
    prefer_relative_imports: false
    
    # PRODUCTIVITY: Temporarily disabled to focus on functional code quality
    # Can be re-enabled later with higher limit (100-120 chars) if desired
    lines_longer_than_80_chars: false
    
    # TEST-SPECIFIC: Disabled for test clarity - individual operations are more readable
    cascade_invocations: false

    # === GRADUAL ADOPTION NOTES ===
    # This configuration provides a solid foundation for code quality.
    # To enable stricter rules in the future:
    # 1. Change include to: package:very_good_analysis/analysis_options.yaml
    # 2. Enable public_member_api_docs: true for documentation requirements
    # 3. Add additional custom rules as needed for team standards
