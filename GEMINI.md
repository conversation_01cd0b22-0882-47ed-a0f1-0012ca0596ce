# GEMINI.md

This file provides guidance to <PERSON> when working with code in this repository. It is structured to be a comprehensive guide, drawing from the project's PRD and existing documentation.

## 1. Product Overview

### 1.1. Product Vision
To become the go-to personal finance management application, empowering users worldwide to achieve financial well-being through intuitive tools, actionable insights, and a secure, reliable platform.

### 1.2. Target Audience
Tech-savvy individuals aged 22-35, new to structured budgeting, seeking a simple yet powerful mobile-first tool to replace spreadsheets or basic note-taking apps.

### 1.3. Core Value Proposition
An intuitive, secure, and comprehensive tool for tracking income and expenses, setting budgets, monitoring financial goals, and gaining insights into spending habits.

## 2. Essential Commands

### 2.1. Development
```bash
# Run the app (uses dev environment by default)
flutter run

# Run specific environments
flutter run --flavor dev --dart-define=FLAVOR=dev
flutter run --flavor staging --dart-define=FLAVOR=staging
flutter run --flavor prod --dart-define=FLAVOR=prod

# Install dependencies
flutter pub get

# Code generation (after model changes)
dart run build_runner build --delete-conflicting-outputs

# Code formatting and analysis
dart format .
flutter analyze
```

### 2.2. Testing
```bash
# Run all tests
flutter test

# Run tests with coverage
flutter test --coverage

# Run specific test files
flutter test test/features/auth/auth_test.dart

# Start Firebase emulators for testing
./scripts/start_emulators.sh

# Run tests with emulators
./scripts/test_with_emulators.sh

# Test Firestore security rules
cd firebase/test && npm test
```

### 2.3. Building
```bash
# Development build
./scripts/select_firebase_config.sh dev
flutter build apk --flavor dev

# Staging build
./scripts/select_firebase_config.sh staging
flutter build apk --flavor staging

# Production build
./scripts/select_firebase_config.sh prod
flutter build apk --flavor prod
```

## 3. Architecture Overview

BudApp is a Flutter personal finance app using a **feature-based architecture** with a clean separation of concerns.

### 3.1. Key Technologies
- **Flutter** with Dart 3
- **Firebase** (Auth, Firestore, Remote Config, Core)
- **Riverpod** for state management (AsyncNotifier pattern)
- **Freezed** for immutable data models
- **go_router** for navigation with auth guards
- **Material 3** design system

### 3.2. Project Structure
```
lib/
├── main.dart                 # Unified entry point with environment detection
├── config/                   # App configuration (themes, environment)
├── features/                 # Feature-based modules
│   ├── auth/
│   ├── accounts/
│   ├── transactions/
│   ├── budgets/
│   ├── goals/
│   └── dashboard/
├── data/                     # Shared data layer
│   ├── models/               # Freezed models
│   └── repositories/         # Repository pattern with interfaces
├── services/                 # Global services
├── providers/                # Riverpod providers
├── routing/                  # go_router configuration
└── widgets/                  # Reusable widgets
```

### 3.3. Multi-Environment Setup
The app supports three environments with separate Firebase projects:
- **Development** (default): `budapp-dev`
- **Staging**: `budapp-staging-1`
- **Production**: `budapp-prod`

## 4. Development Guidelines

### 4.1. Code Patterns
- Use `ConsumerWidget`/`ConsumerStatefulWidget` for Riverpod integration.
- Implement error handling with `AsyncValue.guard()`.
- Follow the feature-based organization.
- Use dependency injection via Riverpod providers.
- Prefer immutable data structures with Freezed.

### 4.2. Testing Requirements
- Write tests for new features using existing patterns.
- Use `MockDataFactory` for test data generation.
- Test authentication flows with Firebase emulators.
- Validate Firestore security rules before deployment.
- Maintain test coverage with `flutter test --coverage`.

### 4.3. Code Quality
- All code must pass `flutter analyze`.
- Use `dart format .` for consistent formatting.
- Follow the repository's established patterns.

## 5. Key Implementation Details

### 5.1. Data Models & Firestore Schema
- All models use Freezed for immutability and have JSON serialization for Firestore.
- The Firestore database is structured with user data isolation in mind. All user-specific data is stored in sub-collections under the `users/{userId}` document.
- Refer to `docs/PRD.md` (Appendix A) for detailed Firestore collection schemas.

### 5.2. Authentication System
- A complete authentication flow is implemented, including email/password, Google OAuth, and biometric authentication.
- Session management is handled securely, with persistence across app restarts.
- Authentication guards are implemented in the routing layer to protect sensitive routes.

### 5.3. State Management
- Riverpod is used for state management, with a focus on the `AsyncNotifier` pattern for asynchronous operations.
- Providers are used for dependency injection of services and repositories.

### 5.4. Firebase Integration
- Firestore is used as the primary database, with security rules enforcing data validation and user access control.
- Firebase Remote Config is used for server-side configuration, including feature flags and predefined categories.
- The Firebase Emulator Suite is used for local development and testing.

## 6. Common Tasks

### 6.1. Adding New Features
1.  Create a new feature directory in `lib/features/`.
2.  Implement the presentation layer (screens, widgets).
3.  Create services for business logic.
4.  Add Riverpod providers for state management.
5.  Update the routing configuration in `go_router`.
6.  Write tests following the existing patterns.

### 6.2. Updating Data Models
1.  Modify the Freezed models in `lib/data/models/`.
2.  Run `dart run build_runner build --delete-conflicting-outputs`.
3.  Update repository interfaces and implementations as needed.
4.  If the schema changes, update the Firestore security rules and run tests.

## 7. Project Status

The project has a solid foundation with a complete authentication system, feature-based architecture, and a robust testing infrastructure. The Account Management feature is fully implemented, serving as a reference for future feature development. The application is ready for the implementation of the next core features: Transaction Management, Budgeting, and Goal Tracking.

## Memory Bank
Use mcp-memory bank MCP for Memory Bank.
Instructions are in .taskmaster/memory-bank/memory_bank_instructions.md
The Memory Bank is located in .taskmaster/memory-bank/

## Task Master
For task management, refer to Task Master MCP

IMPORTANT #1:
- Follow Test-Driven Development (TDD): create tests first, verify they fail, then implement functionality
- Use the Context7 tool to for fresh documentation
- Use websearch tool for searching the web for information and documentation.
- Use Sequential Thinking tool to break down complex problems and plan implementation steps
- Apply all rules from the `docs/rules/` directory
- Follow Flutter/Firebase industry best practices for offline-first architecture with Riverpod state management

IMPORTANT #2 - Before completing the task, run:
- `flutter analyze` (resolve any issues)
- `flutter test` (fix any failing tests by improving codebase)
- `dart format .` (ensure code formatting)

IMPORTANT #3 - When task is complete, update:
- Related documentation files
- READMEs as needed
- CHANGELOG.md with changes made
- Memory bank files to reflect current state and progress

IMPORTANT #4 - When task is complete and docs are updated, mark task as done in Task Master

COMMANDS:
- For rebuilding use command - "dart run build_runner build --delete-conflicting-outputs"
- PROCEED WITH TASK - read memory bank, mark task as in-progress, then proceed with task
- COMPLETE TASK - mark task as done, update docs, update READMEs, update memory bank, then commit to git.