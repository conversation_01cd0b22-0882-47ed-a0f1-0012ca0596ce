# Firestore Category Collection Structure

## Overview

This document defines the specific Firestore collection structure for categories and subcategories in BudApp, including document paths, field definitions, data types, and Flutter model integration.

## Collection Path Structure

### Primary Collection Path
```
users/{userId}/categories/{categoryId}
```

### Path Components
- **users**: Root collection for all user data
- **{userId}**: User's unique identifier (Firebase Auth UID)
- **categories**: Subcollection containing all categories for the user
- **{categoryId}**: Unique category identifier (Firestore auto-generated document ID)

### Hierarchical Organization
Categories and subcategories are stored in the same collection with parent-child relationships managed through the `parentId` field:
- **Root Categories**: `parentId` is `null`
- **Subcategories**: `parentId` references the parent category's document ID

## Document Structure

### Firestore Document Schema
```json
{
  "id": "string",                    // Document ID (matches Firestore document key)
  "userId": "string",               // Owner user ID (Firebase Auth UID)
  "name": "string",                 // Category display name (2-50 chars)
  "type": "string",                 // "income" or "expense"
  "parentId": "string|null",        // Parent category ID (null for root)
  "description": "string|null",     // Optional description (max 200 chars)
  "color": "string|null",           // Hex color code (#RRGGBB format)
  "icon": "string|null",            // Icon identifier/name
  "isActive": "boolean",            // Soft delete flag (default: true)
  "sortOrder": "number",            // Sort order within parent (default: 0)
  "schemaVersion": "number",        // Schema version for migrations (current: 1)
  "createdAt": "Timestamp",         // Firestore Timestamp
  "updatedAt": "Timestamp",         // Firestore Timestamp
  "metadata": "object"              // Additional metadata (default: {})
}
```

### Example Document
```json
{
  "id": "cat_custom_groceries_001",
  "userId": "user_abc123def456",
  "name": "Groceries",
  "type": "expense",
  "parentId": "cat_food_dining",
  "description": "Weekly grocery shopping expenses",
  "color": "#4CAF50",
  "icon": "shopping_cart",
  "isActive": true,
  "sortOrder": 1,
  "schemaVersion": 1,
  "createdAt": "2025-01-01T00:00:00.000Z",
  "updatedAt": "2025-01-01T00:00:00.000Z",
  "metadata": {}
}
```

## Field Definitions

| Field | Type | Required | Constraints | Description |
|-------|------|----------|-------------|-------------|
| `id` | String | Yes | Max 100 chars, Unique | Firestore document ID |
| `userId` | String | Yes | Max 100 chars | Firebase Auth UID |
| `name` | String | Yes | 2-50 chars, Unicode | Display name |
| `type` | String | Yes | "income" \| "expense" | Category type |
| `parentId` | String\|null | No | Max 100 chars, Must exist | Parent reference |
| `description` | String\|null | No | Max 200 chars | User description |
| `color` | String\|null | No | Hex format #RRGGBB | UI color theme |
| `icon` | String\|null | No | Max 50 chars | Icon identifier |
| `isActive` | Boolean | Yes | Default: true | Soft delete flag |
| `sortOrder` | Number | Yes | 0-999, Default: 0 | Display ordering |
| `schemaVersion` | Number | Yes | Current: 1 | Migration support |
| `createdAt` | Timestamp | Yes | Firestore Timestamp | Creation time |
| `updatedAt` | Timestamp | Yes | Firestore Timestamp | Last modification |
| `metadata` | Object | Yes | Max 1KB, Default: {} | Extensible data |

## Flutter Model Integration

### Category Model Class
The Flutter `Category` model provides seamless integration with Firestore:

```dart
@freezed
class Category with _$Category {
  const factory Category({
    required String id,
    required String userId,
    required String name,
    required CategoryType type,
    String? parentId,
    String? description,
    String? color,
    String? icon,
    @Default(true) bool isActive,
    @Default(0) int sortOrder,
    required int schemaVersion,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default({}) Map<String, dynamic> metadata,
  }) = _Category;
}
```

### Firestore Integration Methods

#### fromFirestore()
Converts Firestore document snapshot to Category model:
```dart
factory Category.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
  // Handles Firestore Timestamp conversion to DateTime
  // Ensures document ID consistency
  // Validates required fields
}
```

#### toFirestore()
Converts Category model to Firestore document data:
```dart
Map<String, dynamic> toFirestore() {
  // Converts DateTime to Firestore Timestamp
  // Prepares data for Firestore storage
  // Maintains field type consistency
}
```

### Data Type Conversions

#### Timestamp Handling
- **Storage**: Firestore `Timestamp` objects for server-side timestamps
- **Model**: Dart `DateTime` objects for client-side operations
- **Conversion**: Automatic conversion in `fromFirestore()` and `toFirestore()`

#### Enum Handling
- **CategoryType**: `income` | `expense` (stored as strings)

## Query Patterns

### Common Firestore Queries

#### Get All User Categories
```dart
users/{userId}/categories
  .where('isActive', isEqualTo: true)
  .orderBy('type')
  .orderBy('sortOrder')
```

#### Get Root Categories
```dart
users/{userId}/categories
  .where('parentId', isNull: true)
  .where('isActive', isEqualTo: true)
  .orderBy('sortOrder')
```

#### Get Subcategories
```dart
users/{userId}/categories
  .where('parentId', isEqualTo: parentCategoryId)
  .where('isActive', isEqualTo: true)
  .orderBy('sortOrder')
```

#### Get Categories by Type
```dart
users/{userId}/categories
  .where('type', isEqualTo: 'expense')
  .where('isActive', isEqualTo: true)
  .orderBy('sortOrder')
```

### Required Composite Indexes

```javascript
// User categories by type and sort order
{
  "collectionGroup": "categories",
  "queryScope": "COLLECTION",
  "fields": [
    {"fieldPath": "userId", "order": "ASCENDING"},
    {"fieldPath": "type", "order": "ASCENDING"},
    {"fieldPath": "isActive", "order": "ASCENDING"},
    {"fieldPath": "sortOrder", "order": "ASCENDING"}
  ]
}

// Hierarchical queries - parent's children
{
  "collectionGroup": "categories",
  "queryScope": "COLLECTION",
  "fields": [
    {"fieldPath": "userId", "order": "ASCENDING"},
    {"fieldPath": "parentId", "order": "ASCENDING"},
    {"fieldPath": "sortOrder", "order": "ASCENDING"}
  ]
}
```

## Security Rules

### Firestore Security Rules Implementation
```javascript
match /users/{userId}/categories/{categoryId} {
  // Allow read/write only for authenticated user's own categories
  allow read, write: if isAuthenticated() && isOwner(userId);
  
  // Category creation validation
  allow create: if isAuthenticated() 
    && isOwner(userId)
    && validateCategoryCreate(resource.data);
  
  // Category update validation  
  allow update: if isAuthenticated()
    && isOwner(userId) 
    && validateCategoryUpdate(resource.data, request.data);
  
  // Prevent hard deletion (force soft delete)
  allow delete: if false;
}
```

## Implementation Status

### ✅ Completed
- Firestore collection path structure defined
- Document schema with all required fields
- Flutter Category model with Freezed implementation
- `fromFirestore()` and `toFirestore()` methods implemented
- Firestore Timestamp handling
- Repository integration with proper data conversion

### 🔄 In Progress
- Comprehensive testing of Firestore integration methods
- Performance optimization for large category hierarchies

### 📋 Future Enhancements
- Batch operations for category tree modifications
- Advanced query optimization
- Category usage analytics integration
