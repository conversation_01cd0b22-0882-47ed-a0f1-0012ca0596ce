# Android Signing Documentation for BudApp

## Overview
This document provides comprehensive guidance on Android app signing for the BudApp project. The signing system has been completely redesigned to eliminate the critical security vulnerability of using debug keys in production builds.

## ⚠️ Critical Security Fix Applied
**RESOLVED**: The application was previously using debug keys for release builds, which is a critical security vulnerability. This has been completely fixed with environment-specific signing configurations.

## Architecture Overview

### Multi-Environment Signing Strategy
BudApp uses separate signing keys for each environment to ensure proper isolation and security:

- **Development**: `dev-release.jks` with `dev-key` alias
- **Staging**: `staging-release.jks` with `staging-key` alias  
- **Production**: `prod-release.jks` with `prod-key` alias

### Key Features
- ✅ **Debug keys eliminated** from all release builds
- ✅ **Environment-specific keystores** for proper isolation
- ✅ **Automated signing validation** in build process
- ✅ **Secure CI/CD integration** with GitHub Actions
- ✅ **Comprehensive security checks** and monitoring

## File Structure

```
android/
├── keystore/                           # Keystore directory (not committed to git)
│   ├── .gitignore                      # Excludes all keystores from git
│   ├── KEYSTORE_MANAGEMENT.md          # Keystore management guide
│   ├── dev-release.jks                 # Development keystore (local only)
│   ├── staging-release.jks             # Staging keystore (local only)
│   └── prod-release.jks                # Production keystore (local only)
├── app/
│   └── build.gradle.kts                # Updated with secure signing configs
└── gradle.properties                   # Signing configuration templates

scripts/
├── generate_keystore.sh                # Keystore generation script
├── validate_signing.sh                 # Build validation script
└── build_android.sh                    # Enhanced build script with validation

.github/workflows/
└── android-build.yml                   # CI/CD workflow with secure signing
```

## Signing Configuration

### Gradle Configuration
The `android/app/build.gradle.kts` has been updated with secure signing configurations:

```kotlin
signingConfigs {
    create("dev") {
        keyAlias = project.findProperty("DEV_KEY_ALIAS") as String? ?: System.getenv("DEV_KEY_ALIAS")
        keyPassword = project.findProperty("DEV_KEY_PASSWORD") as String? ?: System.getenv("DEV_KEY_PASSWORD")
        storeFile = project.findProperty("DEV_KEYSTORE_FILE")?.let { file("../${it}") } 
            ?: System.getenv("DEV_KEYSTORE_FILE")?.let { file(it) }
        storePassword = project.findProperty("DEV_KEYSTORE_PASSWORD") as String? ?: System.getenv("DEV_KEYSTORE_PASSWORD")
    }
    // Similar configs for staging and prod
}

productFlavors {
    create("dev") {
        dimension = "default"
        applicationIdSuffix = ".dev"
        signingConfig = signingConfigs.getByName("dev")
    }
    // Similar configs for staging and prod
}

buildTypes {
    release {
        // No debug signing configuration - handled by productFlavors
        isMinifyEnabled = true
        isShrinkResources = true
        // Signing configuration is handled by productFlavors
    }
}
```

### Environment Variables
The system supports both local development and CI/CD through flexible configuration:

**Local Development** (local.properties - NOT committed to git):
```properties
DEV_KEYSTORE_FILE=keystore/dev-release.jks
DEV_KEY_ALIAS=dev-key
DEV_KEYSTORE_PASSWORD=<secure-password>
DEV_KEY_PASSWORD=<secure-password>
```

**CI/CD** (Environment Variables):
- `DEV_KEYSTORE_FILE` - Base64 encoded keystore file
- `DEV_KEY_ALIAS` - Key alias
- `DEV_KEYSTORE_PASSWORD` - Keystore password
- `DEV_KEY_PASSWORD` - Key password

## Local Development Setup

### 1. Generate Keystores
Use the provided script to generate keystores for each environment:

```bash
# Generate development keystore
./scripts/generate_keystore.sh dev

# Generate staging keystore (when needed)
./scripts/generate_keystore.sh staging

# Generate production keystore (when needed)
./scripts/generate_keystore.sh prod
```

### 2. Configure Local Properties
Update `android/local.properties` with your keystore credentials:

```properties
# Development Environment Signing
DEV_KEYSTORE_FILE=keystore/dev-release.jks
DEV_KEY_ALIAS=dev-key
DEV_KEYSTORE_PASSWORD=YourSecurePassword123!
DEV_KEY_PASSWORD=YourSecurePassword123!
```

**IMPORTANT**: Never commit `local.properties` to git - it's automatically ignored and contains sensitive passwords.

### 3. Test Local Builds
```bash
# Build and validate development APK
./scripts/build_android.sh dev apk

# Build and validate development AAB
./scripts/build_android.sh dev appbundle
```

## CI/CD Integration

### GitHub Actions Workflow
The project includes a comprehensive GitHub Actions workflow (`.github/workflows/android-build.yml`) that:

- Runs tests and Flutter analysis
- Builds signed APKs/AABs for each environment
- Validates signatures and security
- Uploads build artifacts
- Prevents debug key usage in any release build

### Required GitHub Secrets
For each environment, add these secrets to your GitHub repository:

#### Development Environment
- `DEV_KEYSTORE_FILE` - Base64 encoded dev-release.jks
- `DEV_KEY_ALIAS` - dev-key
- `DEV_KEYSTORE_PASSWORD` - Your dev keystore password
- `DEV_KEY_PASSWORD` - Your dev key password

#### Staging Environment
- `STAGING_KEYSTORE_FILE` - Base64 encoded staging-release.jks
- `STAGING_KEY_ALIAS` - staging-key
- `STAGING_KEYSTORE_PASSWORD` - Your staging keystore password
- `STAGING_KEY_PASSWORD` - Your staging key password

#### Production Environment
- `PROD_KEYSTORE_FILE` - Base64 encoded prod-release.jks
- `PROD_KEY_ALIAS` - prod-key
- `PROD_KEYSTORE_PASSWORD` - Your production keystore password
- `PROD_KEY_PASSWORD` - Your production key password

### Setting Up GitHub Secrets
1. **Encode Keystore for GitHub Secrets**:
   ```bash
   base64 -i android/keystore/dev-release.jks | pbcopy
   ```

2. **Add Secrets in GitHub**:
   - Go to your repository Settings → Secrets and Variables → Actions
   - Add each secret with the exact names listed above
   - Paste the base64 encoded keystore file for the `*_KEYSTORE_FILE` secrets

## Build Commands

### Manual Building
```bash
# Development builds
./scripts/build_android.sh dev apk
./scripts/build_android.sh dev appbundle

# Staging builds  
./scripts/build_android.sh staging apk
./scripts/build_android.sh staging appbundle

# Production builds
./scripts/build_android.sh prod apk
./scripts/build_android.sh prod appbundle
```

### CI/CD Builds
The GitHub Actions workflow automatically:
- Builds development APKs for all pushes to main/development branches
- Builds staging/production when manually triggered via workflow_dispatch
- Validates all builds for security compliance

## Security Validation

### Automated Validation
Every build automatically runs comprehensive security validation:

```bash
# Validate a specific build
./scripts/validate_signing.sh build/app/outputs/flutter-apk/app-dev-release.apk dev

# Check keystore security only
./scripts/validate_signing.sh --check-keystores
```

### Validation Checks
The validation script performs:
- ✅ **Keystore exclusion verification** - Ensures keystores are not committed to git
- ✅ **Signature verification** - Confirms APK/AAB is properly signed
- ✅ **Debug key detection** - Prevents debug keys in any release build
- ✅ **Certificate validation** - Verifies environment-appropriate certificates
- ✅ **Algorithm verification** - Ensures secure SHA-256 signatures
- ✅ **Password exposure check** - Warns about exposed passwords in gradle.properties

## Security Best Practices

### Keystore Management
1. **Never commit keystores to version control**
   - Keystores are excluded via `.gitignore`
   - Use base64 encoding for CI/CD storage
   
2. **Use strong passwords**
   - Minimum 12 characters with mixed case, numbers, and symbols
   - Store passwords securely (password manager)
   
3. **Separate environment keystores**
   - Each environment has its own keystore
   - Prevents cross-environment security issues

4. **Regular keystore backups**
   - Maintain secure backups of all production keystores
   - Store backups encrypted and offline
   - Document recovery procedures

### Password Management
1. **Local Development**: Store in `gradle.properties` (not committed with real values)
2. **CI/CD**: Use GitHub Secrets with environment variables
3. **Production**: Never expose passwords in logs or configuration files

### Access Control
1. **Limit keystore access** to authorized team members only
2. **Use separate keystores** for different environments
3. **Monitor signing operations** in CI/CD logs
4. **Regular security audits** of signing configuration

## Troubleshooting

### Common Issues

#### 1. Build Fails with Signing Error
```
Error: Keystore file not found for signing config
```
**Solution**: Check keystore path and ensure file exists
```bash
ls -la android/keystore/
```

#### 2. Wrong Password Error
```
Error: Get Key failed: Given final block not properly padded
```
**Solution**: Verify keystore and key passwords match those used during generation

#### 3. CI/CD Signing Fails
```
Error: DEV_KEYSTORE_FILE environment variable not set
```
**Solution**: Verify GitHub Secrets are properly configured and spelled correctly

#### 4. Debug Keys Detected
```
ERROR: Debug signature detected!
```
**Solution**: This indicates a serious configuration error. Check signing configuration in build.gradle.kts

### Diagnostic Commands

```bash
# Check keystore info
keytool -list -v -keystore android/keystore/dev-release.jks

# Verify APK signature
jarsigner -verify -verbose -certs build/app/outputs/flutter-apk/app-dev-release.apk

# Check git status of keystores
git status android/keystore/

# Test build script
./scripts/build_android.sh dev apk
```

## Migration Notes

### What Changed
1. **Removed debug signing** from all release builds
2. **Added environment-specific** signing configurations
3. **Implemented comprehensive validation** for all builds
4. **Created secure CI/CD pipeline** with proper secret management
5. **Established keystore management procedures**

### Impact on Development
- **Local development**: Requires keystore generation and configuration
- **CI/CD**: Requires GitHub Secrets setup for automated builds
- **Team workflow**: Enhanced security with automated validation
- **Production**: Eliminates critical security vulnerability

## Emergency Procedures

### Keystore Compromise
1. **Immediate response**:
   - Revoke compromised keystore immediately
   - Generate new keystore with different passwords
   - Update all CI/CD secrets
   - Rebuild and redistribute affected applications

2. **Documentation**:
   - Document incident for security review
   - Update security procedures if needed

### Lost Keystore
1. **Check secure backups** first
2. **If no backup available**:
   - Generate new keystore
   - Note: Apps signed with lost keystore cannot be updated in app stores
   - May require new app bundle and user migration

## Monitoring and Maintenance

### Regular Tasks
- **Weekly**: Review CI/CD build logs for signing issues
- **Monthly**: Validate keystore backup integrity
- **Quarterly**: Security audit of signing configuration
- **Annually**: Consider keystore rotation for enhanced security

### Key Performance Indicators
- **Build success rate**: Target 99%+ for signing operations
- **Security validation**: 100% pass rate required
- **Debug key detection**: Zero tolerance - all instances must be prevented

## Support and Contact

For signing-related issues:
1. **Security concerns**: Contact security officer immediately
2. **Build failures**: Check troubleshooting section first
3. **Keystore issues**: Review keystore management documentation
4. **CI/CD problems**: Verify GitHub Secrets configuration

---

**Document Version**: 1.0.0  
**Last Updated**: 2025-07-13  
**Next Review**: 2025-10-13

**Security Status**: ✅ **RESOLVED** - Critical debug key vulnerability eliminated  
**Compliance**: ✅ Ready for production deployment