# Firebase Performance Monitoring

This document describes the Firebase Performance Monitoring implementation in BudApp.

## Overview

Firebase Performance Monitoring has been integrated to track:
- App startup performance
- Authentication flow performance  
- Screen load times
- Service initialization times
- Custom business logic performance

## Implementation

### Core Service

The `PerformanceService` class (`lib/services/performance_service.dart`) provides:
- Automatic trace management
- Error handling for test environments
- Predefined trace names for common operations
- Convenience methods for tracking specific flows

### Automatic Tracking

#### App Startup
- Tracks from app initialization to completion
- Measures Firebase service initialization
- Monitors auth state resolution
- Tracks Remote Config fetch performance

#### Authentication Flows
- Email/password sign-in performance
- Google sign-in performance
- Automatic error handling and trace completion

#### Screen Performance
- Home screen load times
- Login screen load times
- Automatic tracking via `PerformanceTrackedScreen` widget

### Usage

#### Basic Trace Tracking
```dart
// Start a custom trace
await PerformanceService.startTrace('my_operation');

// Add metrics and attributes
await PerformanceService.setTraceMetric('my_operation', 'items_processed', 100);
await PerformanceService.setTraceAttribute('my_operation', 'user_type', 'premium');

// Complete the trace
await PerformanceService.stopTrace('my_operation');
```

#### Screen Performance Tracking
```dart
// Wrap any widget with performance tracking
return MyScreen().trackPerformance('my_screen');

// Or use the widget directly
return PerformanceTrackedScreen(
  screenName: 'my_screen',
  child: MyScreen(),
);
```

#### Authentication Flow Tracking
```dart
// Automatically tracked in AuthService methods
await authService.signInWithEmailAndPassword(email: email, password: password);
await authService.signInWithGoogle();
```

## Configuration

### Development vs Production
- Performance collection is **disabled** in debug mode to avoid noise
- Collection is **enabled** in release builds
- Test environments gracefully handle Firebase initialization errors

### Firebase Console
Performance data is available in the Firebase Console under:
- Performance → Dashboard
- Performance → Traces (for custom traces)
- Performance → Screen rendering (for UI performance)

## Predefined Traces

The following traces are automatically tracked:

| Trace Name | Description |
|------------|-------------|
| `app_startup` | Complete app initialization |
| `auth_initialization` | Firebase Auth setup |
| `firestore_initialization` | Firestore setup |
| `remote_config_fetch` | Remote Config fetch and activate |
| `user_login_email_signin` | Email/password authentication |
| `user_login_google_signin` | Google authentication |
| `screen_load_home_screen` | Home screen load time |
| `screen_load_login_screen` | Login screen load time |

## Metrics and Attributes

### Common Metrics
- `initialization_time_ms` - Service initialization duration
- `auth_attempts` - Number of authentication attempts
- `screen_render_time_ms` - Screen rendering duration

### Common Attributes
- `auth_flow` - Type of authentication (email, google)
- `screen_name` - Name of the screen being tracked
- `user_type` - User classification for segmentation
- `environment` - App environment (dev, staging, prod)

## Best Practices

1. **Trace Naming**: Use descriptive, consistent names with underscores
2. **Error Handling**: Always complete traces in finally blocks or catch statements
3. **Attribute Limits**: Firebase limits to 5 custom attributes per trace
4. **Metric Limits**: Firebase limits to 32 custom metrics per trace
5. **Trace Duration**: Keep traces focused on specific operations

## Troubleshooting

### Common Issues

1. **"No Firebase App" errors in tests**
   - Expected behavior - performance monitoring requires Firebase initialization
   - Service gracefully handles these errors with debug logging

2. **Missing performance data in Firebase Console**
   - **Data Processing Delay**: Performance data takes 15-30 minutes to appear in real-time, up to 24 hours for full aggregation
   - **Minimum Data Threshold**: Firebase requires sufficient data before displaying metrics
   - **Collection Enabled**: Verify `setPerformanceCollectionEnabled(true)` is called
   - **Correct Firebase Project**: Ensure you're checking the right Firebase project console
   - **Console Navigation**: Go to Firebase Console → Performance → Traces for custom traces

3. **Trace not completing**
   - Ensure `stopTrace()` is called in all code paths
   - Check for exceptions that might prevent trace completion
   - Use try-finally blocks for critical traces

4. **Debug vs Release Builds**
   - Debug builds: Collection enabled for development visibility
   - Release builds: Should be controlled by build configuration
   - Emulator vs Real Device: Some Firebase features work better on real devices

### Debug Logging

In debug mode, the service logs:
- Trace start/stop events
- Error conditions
- Metric and attribute updates

### Verification Steps

To verify performance monitoring is working:

1. **Check Debug Logs**: Look for these messages in flutter logs:
   ```
   I/flutter: Started trace: app_startup
   I/flutter: Stopped trace: app_startup (data sent to Firebase)
   I/flutter: Performance monitoring initialized (collection enabled)
   ```

2. **Test Data Generation**: The app automatically generates test traces in debug mode:
   ```
   I/flutter: 🔥 Generating test performance data...
   I/flutter: ✅ Test performance data generated successfully!
   ```

3. **Firebase Console Check**:
   - Wait 15-30 minutes after running the app
   - Go to Firebase Console → Performance → Traces
   - Look for traces like: `app_startup`, `auth_initialization`, `performance_test`

4. **Manual Testing**: Trigger authentication flows to generate more data:
   - Sign out and sign back in
   - Navigate between screens
   - Each action generates performance traces

## Future Enhancements

Potential improvements:
- Network request performance tracking
- Database operation performance
- Custom business logic traces
- User journey performance analysis
- Performance alerts and monitoring
