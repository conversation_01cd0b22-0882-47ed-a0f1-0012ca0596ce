# BudApp Testing Strategy

## Executive Summary

This document outlines a comprehensive testing strategy for BudApp, a Flutter + Firebase personal finance application. The strategy follows a testing pyramid approach with unit tests as the foundation, integration tests in the middle, and E2E tests at the top. We target 80% test coverage using fake_cloud_firestore for unit tests and Firebase emulator for integration tests.

## Testing Philosophy

### Core Principles
1. **Fast Feedback Loops**: Unit tests run in milliseconds, integration tests in seconds
2. **Test Independence**: Each test is isolated and can run in any order
3. **Repository Pattern Leverage**: Abstract Firebase dependencies for easy mocking
4. **TDD Approach**: Write failing tests first, then implement functionality
5. **Offline-First Testing**: Validate offline functionality as a first-class citizen

### Testing Pyramid

```
                /\
               /E2E\  (5%)
              /______\
             /        \
            /Integration\ (20%)
           /____________\
          /              \
         /   Unit Tests   \ (75%)
        /__________________\
```

## Testing Architecture

### Repository Pattern for Testability

```dart
// Interface (easy to mock)
abstract class IAccountRepository {
  Future<String> createAccount(Account account);
  Future<Account?> getAccountById(String accountId);
  Stream<List<Account>> watchAccountsByUserId(String userId);
}

// Implementation (uses Firebase)
class AccountRepositoryFirestore implements IAccountRepository {
  final FirebaseFirestore firestore;
  // ... implementation
}

// Test implementation (in-memory)
class FakeAccountRepository implements IAccountRepository {
  final Map<String, Account> _accounts = {};
  // ... fake implementation
}
```

### Test Environment Configuration

#### Unit Tests
- **Tool**: fake_cloud_firestore v3.0.3
- **Mocking**: Mocktail v1.0.4
- **Environment**: Pure Dart, no platform dependencies
- **Speed**: <1ms per test

#### Integration Tests
- **Tool**: Firebase Local Emulator Suite
- **Ports**: Auth (9099), Firestore (8080)
- **Environment**: Real Firebase services (emulated)
- **Speed**: 50-200ms per test

#### E2E Tests
- **Tool**: integration_test package
- **Environment**: Real app on device/emulator
- **Speed**: 1-5s per test

## Testing Tools & Libraries

### Current Dependencies
```yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  fake_cloud_firestore: ^3.0.3
  mocktail: ^1.0.4
```

### Recommended Additions
```yaml
dev_dependencies:
  # Existing...
  firebase_auth_mocks: ^0.14.0    # Better auth mocking
  build_runner: ^2.4.13           # For code generation
  golden_toolkit: ^0.15.0         # Golden tests
  test: ^1.25.8                   # Enhanced test features
```

## Test Organization

### Directory Structure
```
test/
├── unit/
│   ├── models/
│   │   ├── account_test.dart
│   │   ├── transaction_test.dart
│   │   └── ...
│   ├── repositories/
│   │   ├── account_repository_test.dart
│   │   └── ...
│   ├── services/
│   │   ├── auth_service_test.dart
│   │   └── ...
│   └── providers/
│       ├── account_provider_test.dart
│       └── ...
├── widget/
│   ├── screens/
│   │   ├── login_screen_test.dart
│   │   └── ...
│   └── widgets/
│       ├── account_card_test.dart
│       └── ...
├── integration/
│   ├── auth_flow_test.dart
│   ├── transaction_flow_test.dart
│   └── ...
├── golden/
│   ├── account_card_golden_test.dart
│   └── ...
├── helpers/
│   ├── test_wrapper.dart
│   ├── mock_providers.dart
│   ├── fake_repositories.dart
│   └── test_data_factory.dart
└── fixtures/
    ├── test_accounts.json
    └── test_transactions.json
```

## Mocking Strategy

### 1. Unit Test Mocking

```dart
// Use fake_cloud_firestore for repository tests
final fakeFirestore = FakeFirebaseFirestore();

// Use Mocktail for service/provider tests
class MockAuthService extends Mock implements AuthService {}
class MockAccountRepository extends Mock implements IAccountRepository {}
```

### 2. Widget Test Mocking

```dart
// Provider overrides for widget testing
final overrides = [
  authServiceProvider.overrideWithValue(mockAuthService),
  accountRepositoryProvider.overrideWithValue(mockAccountRepository),
];

// Test wrapper
Widget createTestWidget(Widget child, {List<Override> overrides = const []}) {
  return ProviderScope(
    overrides: overrides,
    child: MaterialApp(
      home: child,
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales: AppLocalizations.supportedLocales,
    ),
  );
}
```

### 3. Integration Test Setup

```dart
// Firebase emulator initialization
Future<void> setupEmulator() async {
  await Firebase.initializeApp(
    options: const FirebaseOptions(
      apiKey: 'demo-api-key',
      appId: 'demo-app-id',
      messagingSenderId: 'demo-sender-id',
      projectId: 'demo-budapp-test',
    ),
  );
  
  FirebaseFirestore.instance.useFirestoreEmulator('localhost', 8080);
  await FirebaseAuth.instance.useAuthEmulator('localhost', 9099);
}
```

## Test Data Management

### Test Data Factory
```dart
class TestDataFactory {
  static Account createTestAccount({
    String? id,
    String? userId,
    String? name,
    AccountType? type,
    int? balanceCents,
  }) {
    return Account(
      id: id ?? 'test-account-${DateTime.now().millisecondsSinceEpoch}',
      userId: userId ?? 'test-user',
      name: name ?? 'Test Account',
      type: type ?? AccountType.checking,
      initialBalanceCents: balanceCents ?? 100000,
      currentBalanceCents: balanceCents ?? 100000,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
  
  // Similar factories for other models...
}
```

## CI/CD Integration

### GitHub Actions Configuration
```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      firebase-emulator:
        image: mtlynch/firestore-emulator-docker
        ports:
          - 8080:8080
          - 9099:9099
    
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - run: flutter pub get
      - run: flutter analyze
      - run: flutter test --coverage
      - uses: codecov/codecov-action@v3
```

### Firebase Emulator Version Pinning
```json
{
  "emulators": {
    "auth": {
      "port": 9099
    },
    "firestore": {
      "port": 8080
    }
  },
  "firestore": {
    "rules": "firestore.rules"
  }
}
```

## Performance Testing Considerations

### Test Execution Time Targets
- Unit tests: < 1ms each
- Widget tests: < 50ms each
- Integration tests: < 500ms each
- Full test suite: < 5 minutes

### Parallel Execution
```yaml
# Run unit tests in parallel
flutter test --concurrency=4 test/unit/

# Run integration tests sequentially
flutter test test/integration/
```

## Security Testing

### Security Rules Testing
```javascript
// test/firebase/security_rules_test.js
const { assertFails, assertSucceeds } = require('@firebase/rules-unit-testing');

describe('Account Security Rules', () => {
  it('allows user to read own accounts', async () => {
    const db = authedApp({ uid: 'alice' });
    await assertSucceeds(db.collection('users/alice/accounts').get());
  });
  
  it('denies user reading other accounts', async () => {
    const db = authedApp({ uid: 'alice' });
    await assertFails(db.collection('users/bob/accounts').get());
  });
});
```

## Test Coverage Requirements

### Coverage Targets by Module
- **Models**: 100% (simple data classes)
- **Repositories**: 90% (core business logic)
- **Services**: 85% (complex interactions)
- **Providers**: 80% (state management)
- **Widgets**: 70% (UI components)
- **Screens**: 60% (integration covered separately)
- **Overall**: 80%

### Coverage Reporting
```bash
# Generate coverage report
flutter test --coverage

# Generate HTML report
genhtml coverage/lcov.info -o coverage/html

# View report
open coverage/html/index.html
```

## Migration Strategy

### Phase 1: Foundation (Week 1)
1. Add firebase_auth_mocks dependency
2. Create test helper infrastructure
3. Establish test data factories
4. Set up Firebase emulator configuration

### Phase 2: Unit Tests (Weeks 2-3)
1. Model tests (100% coverage)
2. Repository tests with fake_cloud_firestore
3. Service tests with Mocktail
4. Provider tests with mock dependencies

### Phase 3: Widget Tests (Week 4)
1. Component widget tests
2. Screen widget tests
3. Golden tests for critical UI

### Phase 4: Integration Tests (Week 5)
1. Authentication flows
2. Transaction flows
3. Offline/online sync tests
4. Security rule validation

### Phase 5: CI/CD Integration (Week 6)
1. GitHub Actions setup
2. Coverage reporting
3. Performance monitoring
4. Automated emulator deployment

## Next Steps

1. Review and approve this testing strategy
2. Add recommended dependencies to pubspec.yaml
3. Implement test helper infrastructure
4. Begin TDD implementation with failing tests
5. Track coverage metrics weekly

---

*Document Version: 1.0*  
*Last Updated: January 2025*  
*Status: Draft - Pending Review* 