# BudApp Testing Implementation Plan

## Overview

This document provides a detailed, file-by-file implementation plan for BudApp's comprehensive testing infrastructure. Each section includes specific test files to create, testing patterns to follow, and example code snippets.

## Phase 1: Test Infrastructure Setup

### 1.1 Test Helper Files

#### `test/helpers/test_wrapper.dart`
```dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/config/app_theme.dart';
import 'package:go_router/go_router.dart';

class TestWrapper {
  static Widget createTestWidget(
    Widget child, {
    List<Override> overrides = const [],
    GoRouter? router,
  }) {
    return ProviderScope(
      overrides: overrides,
      child: MaterialApp(
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        localizationsDelegates: AppLocalizations.localizationsDelegates,
        supportedLocales: AppLocalizations.supportedLocales,
        home: router != null
            ? Router(
                routerDelegate: router.routerDelegate,
                routeInformationParser: router.routeInformationParser,
              )
            : Scaffold(body: child),
      ),
    );
  }

  static Widget createMinimalTestWidget(
    Widget child, {
    List<Override> overrides = const [],
  }) {
    return ProviderScope(
      overrides: overrides,
      child: MaterialApp(home: child),
    );
  }
}
```

#### `test/helpers/test_data_factory.dart`
```dart
import 'package:budapp/data/models/models.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class TestDataFactory {
  // Account Test Data
  static Account createTestAccount({
    String? id,
    String? userId,
    String? name,
    AccountType? type,
    int? initialBalanceCents,
    int? currentBalanceCents,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    final now = DateTime.now();
    return Account(
      id: id ?? 'test-account-${now.millisecondsSinceEpoch}',
      userId: userId ?? 'test-user-123',
      name: name ?? 'Test Checking Account',
      type: type ?? AccountType.checking,
      currency: 'USD',
      initialBalanceCents: initialBalanceCents ?? 100000,
      currentBalanceCents: currentBalanceCents ?? 150000,
      isActive: true,
      createdAt: createdAt ?? now,
      updatedAt: updatedAt ?? now,
      schemaVersion: 1,
    );
  }

  // Transaction Test Data
  static Transaction createTestTransaction({
    String? id,
    String? userId,
    String? accountId,
    TransactionType? type,
    int? amountCents,
    String? categoryId,
    DateTime? date,
    String? description,
    List<String>? tagIds,
  }) {
    final now = DateTime.now();
    return Transaction(
      id: id ?? 'test-transaction-${now.millisecondsSinceEpoch}',
      userId: userId ?? 'test-user-123',
      accountId: accountId ?? 'test-account-123',
      type: type ?? TransactionType.expense,
      amountCents: amountCents ?? 5000,
      currency: 'USD',
      categoryId: categoryId ?? 'cat-groceries',
      date: date ?? now,
      description: description ?? 'Test Transaction',
      notes: 'Test notes',
      tagIds: tagIds ?? [],
      createdAt: now,
      updatedAt: now,
      schemaVersion: 1,
    );
  }

  // Category Test Data
  static Category createTestCategory({
    String? id,
    String? userId,
    String? name,
    CategoryType? type,
    String? parentId,
    String? icon,
    String? color,
  }) {
    final now = DateTime.now();
    return Category(
      id: id ?? 'test-category-${now.millisecondsSinceEpoch}',
      userId: userId ?? 'test-user-123',
      name: name ?? 'Test Category',
      type: type ?? CategoryType.expense,
      parentId: parentId,
      icon: icon ?? 'shopping_cart',
      color: color ?? '#FF5733',
      source: CategorySource.custom,
      createdAt: now,
      updatedAt: now,
      schemaVersion: 1,
    );
  }

  // Budget Test Data
  static Budget createTestBudget({
    String? id,
    String? userId,
    String? name,
    int? amountCents,
    String? categoryId,
    BudgetPeriod? period,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    final now = DateTime.now();
    final start = startDate ?? DateTime(now.year, now.month, 1);
    final end = endDate ?? DateTime(now.year, now.month + 1, 0);
    
    return Budget(
      id: id ?? 'test-budget-${now.millisecondsSinceEpoch}',
      userId: userId ?? 'test-user-123',
      name: name ?? 'Test Budget',
      amountCents: amountCents ?? 100000,
      currency: 'USD',
      categoryId: categoryId,
      period: period ?? BudgetPeriod.monthly,
      startDate: start,
      endDate: end,
      isActive: true,
      createdAt: now,
      updatedAt: now,
      schemaVersion: 1,
    );
  }

  // User Test Data
  static UserProfile createTestUser({
    String? uid,
    String? email,
    String? displayName,
    DateTime? createdAt,
    Map<String, dynamic>? preferences,
  }) {
    final now = DateTime.now();
    return UserProfile(
      uid: uid ?? 'test-user-123',
      email: email ?? '<EMAIL>',
      displayName: displayName ?? 'Test User',
      createdAt: createdAt ?? now,
      preferences: preferences ?? {},
    );
  }

  // Tag Test Data
  static Tag createTestTag({
    String? id,
    String? userId,
    String? name,
    String? color,
  }) {
    final now = DateTime.now();
    return Tag(
      id: id ?? 'test-tag-${now.millisecondsSinceEpoch}',
      userId: userId ?? 'test-user-123',
      name: name ?? 'Test Tag',
      color: color ?? '#0000FF',
      createdAt: now,
      updatedAt: now,
      schemaVersion: 1,
    );
  }

  // Goal Test Data
  static Goal createTestGoal({
    String? id,
    String? userId,
    String? name,
    int? targetAmountCents,
    int? currentAmountCents,
    DateTime? targetDate,
  }) {
    final now = DateTime.now();
    return Goal(
      id: id ?? 'test-goal-${now.millisecondsSinceEpoch}',
      userId: userId ?? 'test-user-123',
      name: name ?? 'Test Goal',
      targetAmountCents: targetAmountCents ?? 500000,
      currentAmountCents: currentAmountCents ?? 100000,
      currency: 'USD',
      targetDate: targetDate ?? now.add(const Duration(days: 365)),
      isActive: true,
      createdAt: now,
      updatedAt: now,
      schemaVersion: 1,
    );
  }
}
```

#### `test/helpers/fake_repositories.dart`
```dart
import 'dart:async';
import 'package:budapp/data/models/models.dart';
import 'package:budapp/data/repositories/interfaces/repositories.dart';

class FakeAccountRepository implements IAccountRepository {
  final Map<String, Account> _accounts = {};
  final StreamController<List<Account>> _accountsController = 
      StreamController<List<Account>>.broadcast();

  @override
  Future<String> createAccount(Account account) async {
    _accounts[account.id] = account;
    _notifyListeners();
    return account.id;
  }

  @override
  Future<Account?> getAccountById(String accountId) async {
    return _accounts[accountId];
  }

  @override
  Stream<List<Account>> watchAccountsByUserId(String userId) {
    return _accountsController.stream;
  }

  @override
  Future<void> updateAccount(String accountId, Map<String, dynamic> data) async {
    final account = _accounts[accountId];
    if (account != null) {
      _accounts[accountId] = account.copyWith(
        name: data['name'] ?? account.name,
        currentBalanceCents: data['currentBalanceCents'] ?? account.currentBalanceCents,
        updatedAt: DateTime.now(),
      );
      _notifyListeners();
    }
  }

  @override
  Future<void> deleteAccount(String accountId) async {
    _accounts.remove(accountId);
    _notifyListeners();
  }

  void _notifyListeners() {
    _accountsController.add(_accounts.values.toList());
  }

  void dispose() {
    _accountsController.close();
  }
}

// Similar implementations for other repositories...
```

#### `test/helpers/firebase_emulator_helper.dart`
```dart
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class FirebaseEmulatorHelper {
  static bool _initialized = false;

  static Future<void> initializeEmulator() async {
    if (_initialized) return;

    await Firebase.initializeApp(
      options: const FirebaseOptions(
        apiKey: 'demo-api-key',
        appId: 'demo-app-id',
        messagingSenderId: 'demo-sender-id',
        projectId: 'demo-budapp-test',
        authDomain: 'demo-budapp-test.firebaseapp.com',
        storageBucket: 'demo-budapp-test.appspot.com',
      ),
    );

    FirebaseFirestore.instance.useFirestoreEmulator('localhost', 8080);
    await FirebaseAuth.instance.useAuthEmulator('localhost', 9099);

    _initialized = true;
  }

  static Future<void> clearEmulatorData() async {
    // Clear all collections
    final firestore = FirebaseFirestore.instance;
    
    // Get all users
    final users = await firestore.collection('users').get();
    
    // Delete all user data
    for (final userDoc in users.docs) {
      // Delete subcollections
      await _deleteCollection(firestore, 'users/${userDoc.id}/accounts');
      await _deleteCollection(firestore, 'users/${userDoc.id}/transactions');
      await _deleteCollection(firestore, 'users/${userDoc.id}/categories');
      await _deleteCollection(firestore, 'users/${userDoc.id}/budgets');
      await _deleteCollection(firestore, 'users/${userDoc.id}/goals');
      await _deleteCollection(firestore, 'users/${userDoc.id}/tags');
      
      // Delete user document
      await userDoc.reference.delete();
    }

    // Sign out any authenticated users
    await FirebaseAuth.instance.signOut();
  }

  static Future<void> _deleteCollection(
    FirebaseFirestore firestore,
    String collectionPath,
  ) async {
    final collection = await firestore.collection(collectionPath).get();
    for (final doc in collection.docs) {
      await doc.reference.delete();
    }
  }

  static Future<User> createTestUser({
    required String email,
    required String password,
  }) async {
    final userCredential = await FirebaseAuth.instance
        .createUserWithEmailAndPassword(email: email, password: password);
    return userCredential.user!;
  }
}
```

## Phase 2: Unit Tests

### 2.1 Model Tests

#### `test/unit/models/account_test.dart`
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:budapp/data/models/account.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../helpers/test_data_factory.dart';

void main() {
  group('Account Model Tests', () {
    test('should create Account with all required fields', () {
      final account = TestDataFactory.createTestAccount();
      
      expect(account.id, isNotEmpty);
      expect(account.userId, equals('test-user-123'));
      expect(account.name, equals('Test Checking Account'));
      expect(account.type, equals(AccountType.checking));
      expect(account.currency, equals('USD'));
      expect(account.initialBalanceCents, equals(100000));
      expect(account.currentBalanceCents, equals(150000));
      expect(account.isActive, isTrue);
      expect(account.schemaVersion, equals(1));
    });

    test('should convert Account to JSON correctly', () {
      final account = TestDataFactory.createTestAccount();
      final json = account.toJson();
      
      expect(json['id'], equals(account.id));
      expect(json['userId'], equals(account.userId));
      expect(json['name'], equals(account.name));
      expect(json['type'], equals('checking'));
      expect(json['currency'], equals('USD'));
      expect(json['initialBalanceCents'], equals(100000));
      expect(json['currentBalanceCents'], equals(150000));
      expect(json['isActive'], isTrue);
      expect(json['schemaVersion'], equals(1));
    });

    test('should create Account from JSON correctly', () {
      final now = DateTime.now();
      final json = {
        'id': 'test-123',
        'userId': 'user-123',
        'name': 'Savings Account',
        'type': 'savings',
        'currency': 'USD',
        'initialBalanceCents': 200000,
        'currentBalanceCents': 250000,
        'isActive': true,
        'createdAt': Timestamp.fromDate(now),
        'updatedAt': Timestamp.fromDate(now),
        'schemaVersion': 1,
      };
      
      final account = Account.fromJson(json);
      
      expect(account.id, equals('test-123'));
      expect(account.userId, equals('user-123'));
      expect(account.name, equals('Savings Account'));
      expect(account.type, equals(AccountType.savings));
      expect(account.currentBalanceCents, equals(250000));
    });

    test('should handle copyWith correctly', () {
      final account = TestDataFactory.createTestAccount();
      final updated = account.copyWith(
        name: 'Updated Account',
        currentBalanceCents: 200000,
      );
      
      expect(updated.name, equals('Updated Account'));
      expect(updated.currentBalanceCents, equals(200000));
      expect(updated.id, equals(account.id));
      expect(updated.userId, equals(account.userId));
    });

    test('should validate account types', () {
      expect(AccountType.values, contains(AccountType.checking));
      expect(AccountType.values, contains(AccountType.savings));
      expect(AccountType.values, contains(AccountType.creditCard));
      expect(AccountType.values, contains(AccountType.cash));
      expect(AccountType.values, contains(AccountType.investment));
    });

    test('should handle null optional fields', () {
      final json = {
        'id': 'test-123',
        'userId': 'user-123',
        'name': 'Basic Account',
        'type': 'cash',
        'currency': 'USD',
        'initialBalanceCents': 0,
        'currentBalanceCents': 0,
        'isActive': true,
        'createdAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
        'schemaVersion': 1,
      };
      
      final account = Account.fromJson(json);
      expect(account.id, equals('test-123'));
      expect(account.notes, isNull);
    });
  });
}
```

#### Similar test files for other models:
- `test/unit/models/transaction_test.dart`
- `test/unit/models/category_test.dart`
- `test/unit/models/budget_test.dart`
- `test/unit/models/user_profile_test.dart`
- `test/unit/models/tag_test.dart`
- `test/unit/models/goal_test.dart`

### 2.2 Repository Tests

#### `test/unit/repositories/account_repository_test.dart`
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:budapp/data/repositories/implementations/account_repository_firestore.dart';
import 'package:budapp/data/models/account.dart';
import '../../helpers/test_data_factory.dart';

void main() {
  group('AccountRepositoryFirestore Tests', () {
    late FakeFirebaseFirestore fakeFirestore;
    late AccountRepositoryFirestore repository;
    const testUserId = 'test-user-123';

    setUp(() {
      fakeFirestore = FakeFirebaseFirestore();
      repository = AccountRepositoryFirestore(firestore: fakeFirestore);
    });

    test('should create account successfully', () async {
      final account = TestDataFactory.createTestAccount(userId: testUserId);
      
      final accountId = await repository.createAccount(account);
      
      expect(accountId, isNotEmpty);
      
      // Verify account was saved
      final doc = await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(accountId)
          .get();
      
      expect(doc.exists, isTrue);
      expect(doc.data()?['name'], equals(account.name));
    });

    test('should get account by id', () async {
      final account = TestDataFactory.createTestAccount(userId: testUserId);
      
      // Save account first
      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(account.id)
          .set(account.toJson());
      
      final retrieved = await repository.getAccountById(account.id);
      
      expect(retrieved, isNotNull);
      expect(retrieved!.id, equals(account.id));
      expect(retrieved.name, equals(account.name));
    });

    test('should watch accounts by user id', () async {
      final account1 = TestDataFactory.createTestAccount(
        id: 'acc-1',
        userId: testUserId,
        name: 'Account 1',
      );
      final account2 = TestDataFactory.createTestAccount(
        id: 'acc-2',
        userId: testUserId,
        name: 'Account 2',
      );
      
      // Save accounts
      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(account1.id)
          .set(account1.toJson());
      
      // Listen to stream
      final stream = repository.watchAccountsByUserId(testUserId);
      
      // First emission
      var accounts = await stream.first;
      expect(accounts.length, equals(1));
      expect(accounts[0].name, equals('Account 1'));
      
      // Add second account
      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(account2.id)
          .set(account2.toJson());
      
      // Should emit updated list
      accounts = await stream.first;
      expect(accounts.length, equals(2));
    });

    test('should update account', () async {
      final account = TestDataFactory.createTestAccount(userId: testUserId);
      
      // Save account first
      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(account.id)
          .set(account.toJson());
      
      // Update account
      await repository.updateAccount(account.id, {
        'name': 'Updated Name',
        'currentBalanceCents': 300000,
      });
      
      // Verify update
      final doc = await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(account.id)
          .get();
      
      expect(doc.data()?['name'], equals('Updated Name'));
      expect(doc.data()?['currentBalanceCents'], equals(300000));
    });

    test('should delete account', () async {
      final account = TestDataFactory.createTestAccount(userId: testUserId);
      
      // Save account first
      await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(account.id)
          .set(account.toJson());
      
      // Delete account
      await repository.deleteAccount(account.id);
      
      // Verify deletion
      final doc = await fakeFirestore
          .collection('users')
          .doc(testUserId)
          .collection('accounts')
          .doc(account.id)
          .get();
      
      expect(doc.exists, isFalse);
    });

    test('should handle non-existent account', () async {
      final account = await repository.getAccountById('non-existent');
      expect(account, isNull);
    });

    test('should filter by account type', () async {
      final checking = TestDataFactory.createTestAccount(
        id: 'acc-1',
        userId: testUserId,
        type: AccountType.checking,
      );
      final savings = TestDataFactory.createTestAccount(
        id: 'acc-2',
        userId: testUserId,
        type: AccountType.savings,
      );
      
      await repository.createAccount(checking);
      await repository.createAccount(savings);
      
      final checkingAccounts = await repository
          .getAccountsByType(testUserId, AccountType.checking);
      
      expect(checkingAccounts.length, equals(1));
      expect(checkingAccounts[0].type, equals(AccountType.checking));
    });
  });
}
```

#### Similar repository test files:
- `test/unit/repositories/transaction_repository_test.dart`
- `test/unit/repositories/category_repository_test.dart`
- `test/unit/repositories/budget_repository_test.dart`
- `test/unit/repositories/user_repository_test.dart`
- `test/unit/repositories/tag_repository_test.dart`
- `test/unit/repositories/goal_repository_test.dart`

### 2.3 Service Tests

#### `test/unit/services/auth_service_test.dart`
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:budapp/features/auth/services/biometric_service.dart';
import 'package:budapp/services/secure_storage_service.dart';
import 'package:budapp/data/repositories/interfaces/user_repository.dart';

// Mocks
class MockFirebaseAuth extends Mock implements FirebaseAuth {}
class MockGoogleSignIn extends Mock implements GoogleSignIn {}
class MockUserRepository extends Mock implements IUserRepository {}
class MockBiometricService extends Mock implements BiometricService {}
class MockSecureStorageService extends Mock implements SecureStorageService {}
class MockUser extends Mock implements User {}
class MockUserCredential extends Mock implements UserCredential {}
class MockGoogleSignInAccount extends Mock implements GoogleSignInAccount {}
class MockGoogleSignInAuthentication extends Mock implements GoogleSignInAuthentication {}

void main() {
  group('AuthService Tests', () {
    late AuthService authService;
    late MockFirebaseAuth mockAuth;
    late MockGoogleSignIn mockGoogleSignIn;
    late MockUserRepository mockUserRepository;
    late MockBiometricService mockBiometricService;
    late MockSecureStorageService mockSecureStorage;

    setUp(() {
      mockAuth = MockFirebaseAuth();
      mockGoogleSignIn = MockGoogleSignIn();
      mockUserRepository = MockUserRepository();
      mockBiometricService = MockBiometricService();
      mockSecureStorage = MockSecureStorageService();

      authService = AuthService(
        auth: mockAuth,
        googleSignIn: mockGoogleSignIn,
        userRepository: mockUserRepository,
        biometricService: mockBiometricService,
        secureStorage: mockSecureStorage,
      );
    });

    test('should sign in with email and password successfully', () async {
      final mockUser = MockUser();
      final mockCredential = MockUserCredential();
      
      when(() => mockUser.uid).thenReturn('test-uid');
      when(() => mockUser.email).thenReturn('<EMAIL>');
      when(() => mockCredential.user).thenReturn(mockUser);
      
      when(() => mockAuth.signInWithEmailAndPassword(
        email: any(named: 'email'),
        password: any(named: 'password'),
      )).thenAnswer((_) async => mockCredential);
      
      when(() => mockUserRepository.createOrUpdateFromFirebaseUser(any()))
          .thenAnswer((_) async {});
      
      final result = await authService.signInWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'password123',
      );
      
      expect(result, equals(mockCredential));
      verify(() => mockAuth.signInWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'password123',
      )).called(1);
      verify(() => mockUserRepository.createOrUpdateFromFirebaseUser(mockUser))
          .called(1);
    });

    test('should handle sign in failure', () async {
      when(() => mockAuth.signInWithEmailAndPassword(
        email: any(named: 'email'),
        password: any(named: 'password'),
      )).thenThrow(FirebaseAuthException(
        code: 'wrong-password',
        message: 'Wrong password',
      ));
      
      expect(
        () => authService.signInWithEmailAndPassword(
          email: '<EMAIL>',
          password: 'wrongpassword',
        ),
        throwsA(isA<FirebaseAuthException>()),
      );
    });

    test('should sign out successfully', () async {
      when(() => mockAuth.signOut()).thenAnswer((_) async {});
      when(() => mockGoogleSignIn.signOut()).thenAnswer((_) async {});
      
      await authService.signOut();
      
      verify(() => mockAuth.signOut()).called(1);
      verify(() => mockGoogleSignIn.signOut()).called(1);
    });

    test('should handle Google sign in', () async {
      final mockGoogleAccount = MockGoogleSignInAccount();
      final mockGoogleAuth = MockGoogleSignInAuthentication();
      final mockUser = MockUser();
      final mockCredential = MockUserCredential();
      
      when(() => mockGoogleAccount.authentication)
          .thenAnswer((_) async => mockGoogleAuth);
      when(() => mockGoogleAuth.idToken).thenReturn('google-id-token');
      when(() => mockGoogleAuth.accessToken).thenReturn('google-access-token');
      
      when(() => mockGoogleSignIn.signIn())
          .thenAnswer((_) async => mockGoogleAccount);
      
      when(() => mockAuth.signInWithCredential(any()))
          .thenAnswer((_) async => mockCredential);
      
      when(() => mockUser.uid).thenReturn('google-uid');
      when(() => mockCredential.user).thenReturn(mockUser);
      
      when(() => mockUserRepository.createOrUpdateFromFirebaseUser(any()))
          .thenAnswer((_) async {});
      
      final result = await authService.signInWithGoogle();
      
      expect(result, equals(mockCredential));
      verify(() => mockGoogleSignIn.signIn()).called(1);
      verify(() => mockAuth.signInWithCredential(any())).called(1);
    });

    test('should get current user', () {
      final mockUser = MockUser();
      when(() => mockAuth.currentUser).thenReturn(mockUser);
      
      final user = authService.currentUser;
      
      expect(user, equals(mockUser));
    });

    test('should check if user is signed in', () {
      final mockUser = MockUser();
      when(() => mockAuth.currentUser).thenReturn(mockUser);
      
      expect(authService.isSignedIn, isTrue);
      
      when(() => mockAuth.currentUser).thenReturn(null);
      
      expect(authService.isSignedIn, isFalse);
    });

    test('should send password reset email', () async {
      when(() => mockAuth.sendPasswordResetEmail(email: any(named: 'email')))
          .thenAnswer((_) async {});
      
      await authService.sendPasswordResetEmail(email: '<EMAIL>');
      
      verify(() => mockAuth.sendPasswordResetEmail(email: '<EMAIL>'))
          .called(1);
    });
  });
}
```

#### Similar service test files:
- `test/unit/services/transaction_service_test.dart`
- `test/unit/services/category_service_test.dart`
- `test/unit/services/budget_service_test.dart`
- `test/unit/services/biometric_service_test.dart`
- `test/unit/services/secure_storage_service_test.dart`
- `test/unit/services/remote_config_service_test.dart`

### 2.4 Provider Tests

#### `test/unit/providers/account_provider_test.dart`
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mocktail/mocktail.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/data/repositories/interfaces/account_repository.dart';
import 'package:budapp/data/models/account.dart';
import '../../helpers/test_data_factory.dart';

class MockAccountRepository extends Mock implements IAccountRepository {}

void main() {
  group('AccountProvider Tests', () {
    late ProviderContainer container;
    late MockAccountRepository mockRepository;

    setUp(() {
      mockRepository = MockAccountRepository();
      container = ProviderContainer(
        overrides: [
          accountRepositoryProvider.overrideWithValue(mockRepository),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    test('should fetch accounts for user', () async {
      final accounts = [
        TestDataFactory.createTestAccount(id: 'acc-1'),
        TestDataFactory.createTestAccount(id: 'acc-2'),
      ];
      
      when(() => mockRepository.watchAccountsByUserId(any()))
          .thenAnswer((_) => Stream.value(accounts));
      
      final provider = accountsProvider('test-user-123');
      final result = await container.read(provider.future);
      
      expect(result.length, equals(2));
      expect(result[0].id, equals('acc-1'));
      expect(result[1].id, equals('acc-2'));
    });

    test('should handle empty accounts list', () async {
      when(() => mockRepository.watchAccountsByUserId(any()))
          .thenAnswer((_) => Stream.value([]));
      
      final provider = accountsProvider('test-user-123');
      final result = await container.read(provider.future);
      
      expect(result, isEmpty);
    });

    test('should handle repository errors', () async {
      when(() => mockRepository.watchAccountsByUserId(any()))
          .thenAnswer((_) => Stream.error(Exception('Failed to fetch')));
      
      final provider = accountsProvider('test-user-123');
      
      expect(
        () => container.read(provider.future),
        throwsA(isA<Exception>()),
      );
    });

    test('should create account through notifier', () async {
      final account = TestDataFactory.createTestAccount();
      
      when(() => mockRepository.createAccount(any()))
          .thenAnswer((_) async => account.id);
      
      final notifier = container.read(accountNotifierProvider.notifier);
      await notifier.createAccount(account);
      
      verify(() => mockRepository.createAccount(account)).called(1);
    });

    test('should update account through notifier', () async {
      when(() => mockRepository.updateAccount(any(), any()))
          .thenAnswer((_) async {});
      
      final notifier = container.read(accountNotifierProvider.notifier);
      await notifier.updateAccount('acc-1', {'name': 'Updated'});
      
      verify(() => mockRepository.updateAccount('acc-1', {'name': 'Updated'}))
          .called(1);
    });

    test('should delete account through notifier', () async {
      when(() => mockRepository.deleteAccount(any()))
          .thenAnswer((_) async {});
      
      final notifier = container.read(accountNotifierProvider.notifier);
      await notifier.deleteAccount('acc-1');
      
      verify(() => mockRepository.deleteAccount('acc-1')).called(1);
    });
  });
}
```

## Phase 3: Widget Tests

### 3.1 Component Widget Tests

#### `test/widget/widgets/account_card_test.dart`
```dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:budapp/features/accounts/presentation/widgets/account_card.dart';
import '../../helpers/test_wrapper.dart';
import '../../helpers/test_data_factory.dart';

void main() {
  group('AccountCard Widget Tests', () {
    testWidgets('should display account information', (tester) async {
      final account = TestDataFactory.createTestAccount(
        name: 'Test Checking',
        currentBalanceCents: 150000,
      );
      
      await tester.pumpWidget(
        TestWrapper.createMinimalTestWidget(
          AccountCard(
            account: account,
            onTap: () {},
          ),
        ),
      );
      
      expect(find.text('Test Checking'), findsOneWidget);
      expect(find.text('\$1,500.00'), findsOneWidget);
    });

    testWidgets('should handle tap', (tester) async {
      final account = TestDataFactory.createTestAccount();
      bool tapped = false;
      
      await tester.pumpWidget(
        TestWrapper.createMinimalTestWidget(
          AccountCard(
            account: account,
            onTap: () => tapped = true,
          ),
        ),
      );
      
      await tester.tap(find.byType(AccountCard));
      await tester.pumpAndSettle();
      
      expect(tapped, isTrue);
    });

    testWidgets('should show correct account type icon', (tester) async {
      final checkingAccount = TestDataFactory.createTestAccount(
        type: AccountType.checking,
      );
      
      await tester.pumpWidget(
        TestWrapper.createMinimalTestWidget(
          AccountCard(
            account: checkingAccount,
            onTap: () {},
          ),
        ),
      );
      
      expect(find.byIcon(Icons.account_balance), findsOneWidget);
    });

    testWidgets('should show negative balance in red', (tester) async {
      final account = TestDataFactory.createTestAccount(
        currentBalanceCents: -5000,
      );
      
      await tester.pumpWidget(
        TestWrapper.createMinimalTestWidget(
          AccountCard(
            account: account,
            onTap: () {},
          ),
        ),
      );
      
      final balanceText = tester.widget<Text>(
        find.text('-\$50.00'),
      );
      
      expect(balanceText.style?.color, equals(Colors.red));
    });
  });
}
```

### 3.2 Screen Widget Tests

#### `test/widget/screens/login_screen_test.dart`
```dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mocktail/mocktail.dart';
import 'package:budapp/features/auth/presentation/screens/login_screen.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import '../../helpers/test_wrapper.dart';

class MockAuthService extends Mock implements AuthService {}
class MockLoginNotifier extends Mock implements LoginNotifier {}

void main() {
  group('LoginScreen Widget Tests', () {
    late MockAuthService mockAuthService;
    late MockLoginNotifier mockLoginNotifier;

    setUp(() {
      mockAuthService = MockAuthService();
      mockLoginNotifier = MockLoginNotifier();
    });

    testWidgets('should display login form', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const LoginScreen(),
          overrides: [
            authServiceProvider.overrideWithValue(mockAuthService),
            loginNotifierProvider.overrideWith(() => mockLoginNotifier),
          ],
        ),
      );
      
      expect(find.text('Welcome Back'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(2));
      expect(find.text('Sign In'), findsOneWidget);
      expect(find.text('Sign in with Google'), findsOneWidget);
    });

    testWidgets('should validate email field', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const LoginScreen(),
          overrides: [
            authServiceProvider.overrideWithValue(mockAuthService),
            loginNotifierProvider.overrideWith(() => mockLoginNotifier),
          ],
        ),
      );
      
      // Enter invalid email
      await tester.enterText(
        find.byType(TextFormField).first,
        'invalid-email',
      );
      
      // Try to submit
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle();
      
      expect(find.text('Please enter a valid email'), findsOneWidget);
    });

    testWidgets('should call sign in on valid form submission', (tester) async {
      when(() => mockLoginNotifier.signIn(
        email: any(named: 'email'),
        password: any(named: 'password'),
      )).thenAnswer((_) async {});
      
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const LoginScreen(),
          overrides: [
            authServiceProvider.overrideWithValue(mockAuthService),
            loginNotifierProvider.overrideWith(() => mockLoginNotifier),
          ],
        ),
      );
      
      // Enter valid credentials
      await tester.enterText(
        find.byType(TextFormField).first,
        '<EMAIL>',
      );
      await tester.enterText(
        find.byType(TextFormField).last,
        'password123',
      );
      
      // Submit form
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle();
      
      verify(() => mockLoginNotifier.signIn(
        email: '<EMAIL>',
        password: 'password123',
      )).called(1);
    });

    testWidgets('should show loading indicator during sign in', (tester) async {
      when(() => mockLoginNotifier.build())
          .thenReturn(const AsyncValue.loading());
      
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const LoginScreen(),
          overrides: [
            authServiceProvider.overrideWithValue(mockAuthService),
            loginNotifierProvider.overrideWith(() => mockLoginNotifier),
          ],
        ),
      );
      
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should show error message on sign in failure', (tester) async {
      when(() => mockLoginNotifier.build()).thenReturn(
        AsyncValue.error(
          Exception('Invalid credentials'),
          StackTrace.current,
        ),
      );
      
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const LoginScreen(),
          overrides: [
            authServiceProvider.overrideWithValue(mockAuthService),
            loginNotifierProvider.overrideWith(() => mockLoginNotifier),
          ],
        ),
      );
      
      expect(find.text('Invalid credentials'), findsOneWidget);
    });
  });
}
```

## Phase 4: Integration Tests

### 4.1 Authentication Flow Tests

#### `test/integration/auth_flow_test.dart`
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:budapp/main.dart' as app;
import '../helpers/firebase_emulator_helper.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Authentication Flow Integration Tests', () {
    setUpAll(() async {
      await FirebaseEmulatorHelper.initializeEmulator();
    });

    setUp(() async {
      await FirebaseEmulatorHelper.clearEmulatorData();
    });

    testWidgets('should complete full registration flow', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to registration
      await tester.tap(find.text('Create Account'));
      await tester.pumpAndSettle();

      // Fill registration form
      await tester.enterText(
        find.byKey(const Key('email_field')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('password_field')),
        'Test123!',
      );
      await tester.enterText(
        find.byKey(const Key('confirm_password_field')),
        'Test123!',
      );

      // Submit registration
      await tester.tap(find.text('Sign Up'));
      await tester.pumpAndSettle();

      // Should navigate to home screen
      expect(find.text('Accounts'), findsOneWidget);
    });

    testWidgets('should sign in existing user', (tester) async {
      // Create test user
      await FirebaseEmulatorHelper.createTestUser(
        email: '<EMAIL>',
        password: 'Test123!',
      );

      app.main();
      await tester.pumpAndSettle();

      // Fill login form
      await tester.enterText(
        find.byKey(const Key('email_field')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('password_field')),
        'Test123!',
      );

      // Submit login
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle();

      // Should navigate to home screen
      expect(find.text('Accounts'), findsOneWidget);
    });

    testWidgets('should handle incorrect password', (tester) async {
      // Create test user
      await FirebaseEmulatorHelper.createTestUser(
        email: '<EMAIL>',
        password: 'Test123!',
      );

      app.main();
      await tester.pumpAndSettle();

      // Fill login form with wrong password
      await tester.enterText(
        find.byKey(const Key('email_field')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('password_field')),
        'WrongPassword',
      );

      // Submit login
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle();

      // Should show error message
      expect(find.text('Invalid password'), findsOneWidget);
    });

    testWidgets('should sign out successfully', (tester) async {
      // Create and sign in test user
      final user = await FirebaseEmulatorHelper.createTestUser(
        email: '<EMAIL>',
        password: 'Test123!',
      );

      app.main();
      await tester.pumpAndSettle();

      // Sign in
      await tester.enterText(
        find.byKey(const Key('email_field')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('password_field')),
        'Test123!',
      );
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle();

      // Open drawer
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();

      // Sign out
      await tester.tap(find.text('Sign Out'));
      await tester.pumpAndSettle();

      // Should be back at login screen
      expect(find.text('Welcome Back'), findsOneWidget);
    });
  });
}
```

### 4.2 Transaction Flow Tests

#### `test/integration/transaction_flow_test.dart`
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:budapp/main.dart' as app;
import '../helpers/firebase_emulator_helper.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Transaction Flow Integration Tests', () {
    setUpAll(() async {
      await FirebaseEmulatorHelper.initializeEmulator();
    });

    setUp(() async {
      await FirebaseEmulatorHelper.clearEmulatorData();
      
      // Create test user and sign in
      await FirebaseEmulatorHelper.createTestUser(
        email: '<EMAIL>',
        password: 'Test123!',
      );
    });

    testWidgets('should create income transaction', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Sign in
      await _signIn(tester, '<EMAIL>', 'Test123!');

      // Create account first
      await _createAccount(tester, 'Test Checking', 100000);

      // Navigate to transactions
      await tester.tap(find.text('Transactions'));
      await tester.pumpAndSettle();

      // Add transaction
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // Fill transaction form
      await tester.tap(find.text('Income'));
      await tester.pumpAndSettle();

      await tester.enterText(
        find.byKey(const Key('amount_field')),
        '150.00',
      );
      await tester.enterText(
        find.byKey(const Key('description_field')),
        'Salary',
      );

      // Select account
      await tester.tap(find.text('Select Account'));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Test Checking'));
      await tester.pumpAndSettle();

      // Save transaction
      await tester.tap(find.text('Save'));
      await tester.pumpAndSettle();

      // Verify transaction appears in list
      expect(find.text('Salary'), findsOneWidget);
      expect(find.text('+\$150.00'), findsOneWidget);

      // Verify account balance updated
      await tester.tap(find.text('Accounts'));
      await tester.pumpAndSettle();
      expect(find.text('\$1,150.00'), findsOneWidget);
    });

    testWidgets('should create expense transaction', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Sign in
      await _signIn(tester, '<EMAIL>', 'Test123!');

      // Create account
      await _createAccount(tester, 'Test Checking', 100000);

      // Navigate to transactions
      await tester.tap(find.text('Transactions'));
      await tester.pumpAndSettle();

      // Add transaction
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // Fill transaction form
      await tester.tap(find.text('Expense'));
      await tester.pumpAndSettle();

      await tester.enterText(
        find.byKey(const Key('amount_field')),
        '50.00',
      );
      await tester.enterText(
        find.byKey(const Key('description_field')),
        'Groceries',
      );

      // Select account
      await tester.tap(find.text('Select Account'));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Test Checking'));
      await tester.pumpAndSettle();

      // Save transaction
      await tester.tap(find.text('Save'));
      await tester.pumpAndSettle();

      // Verify transaction appears in list
      expect(find.text('Groceries'), findsOneWidget);
      expect(find.text('-\$50.00'), findsOneWidget);

      // Verify account balance updated
      await tester.tap(find.text('Accounts'));
      await tester.pumpAndSettle();
      expect(find.text('\$950.00'), findsOneWidget);
    });

    testWidgets('should handle offline transaction creation', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Sign in
      await _signIn(tester, '<EMAIL>', 'Test123!');

      // Create account
      await _createAccount(tester, 'Test Checking', 100000);

      // TODO: Simulate offline mode
      // This would require platform-specific code or a test mode

      // Navigate to transactions
      await tester.tap(find.text('Transactions'));
      await tester.pumpAndSettle();

      // Add transaction while offline
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // Fill and save transaction
      await tester.enterText(
        find.byKey(const Key('amount_field')),
        '25.00',
      );
      await tester.enterText(
        find.byKey(const Key('description_field')),
        'Offline Purchase',
      );

      await tester.tap(find.text('Save'));
      await tester.pumpAndSettle();

      // Transaction should appear immediately (optimistic update)
      expect(find.text('Offline Purchase'), findsOneWidget);

      // TODO: Simulate coming back online and verify sync
    });
  });
}

// Helper functions
Future<void> _signIn(WidgetTester tester, String email, String password) async {
  await tester.enterText(
    find.byKey(const Key('email_field')),
    email,
  );
  await tester.enterText(
    find.byKey(const Key('password_field')),
    password,
  );
  await tester.tap(find.text('Sign In'));
  await tester.pumpAndSettle();
}

Future<void> _createAccount(
  WidgetTester tester,
  String name,
  int balanceCents,
) async {
  await tester.tap(find.byIcon(Icons.add));
  await tester.pumpAndSettle();

  await tester.enterText(
    find.byKey(const Key('account_name_field')),
    name,
  );
  await tester.enterText(
    find.byKey(const Key('initial_balance_field')),
    (balanceCents / 100).toStringAsFixed(2),
  );

  await tester.tap(find.text('Save'));
  await tester.pumpAndSettle();
}
```

## Phase 5: Security Rules Tests

### 5.1 Firestore Security Rules Tests

#### `test/firebase/security_rules_test.js`
```javascript
const { initializeTestEnvironment, assertFails, assertSucceeds } = require('@firebase/rules-unit-testing');
const { readFileSync } = require('fs');

describe('Firestore Security Rules', () => {
  let testEnv;

  beforeAll(async () => {
    testEnv = await initializeTestEnvironment({
      projectId: 'demo-budapp-test',
      firestore: {
        rules: readFileSync('../../firestore.rules', 'utf8'),
      },
    });
  });

  afterAll(async () => {
    await testEnv.cleanup();
  });

  afterEach(async () => {
    await testEnv.clearFirestore();
  });

  describe('User Profile Rules', () => {
    test('allows user to read own profile', async () => {
      const alice = testEnv.authenticatedContext('alice');
      const doc = alice.firestore().doc('users/alice');
      
      await assertSucceeds(doc.get());
    });

    test('denies user reading other profiles', async () => {
      const alice = testEnv.authenticatedContext('alice');
      const doc = alice.firestore().doc('users/bob');
      
      await assertFails(doc.get());
    });

    test('allows user to update own profile', async () => {
      const alice = testEnv.authenticatedContext('alice');
      const doc = alice.firestore().doc('users/alice');
      
      await assertSucceeds(doc.set({
        uid: 'alice',
        email: '<EMAIL>',
        displayName: 'Alice',
        updatedAt: new Date(),
      }));
    });

    test('denies invalid profile data', async () => {
      const alice = testEnv.authenticatedContext('alice');
      const doc = alice.firestore().doc('users/alice');
      
      await assertFails(doc.set({
        uid: 'bob', // Wrong UID
        email: '<EMAIL>',
      }));
    });
  });

  describe('Account Rules', () => {
    test('allows user to read own accounts', async () => {
      const alice = testEnv.authenticatedContext('alice');
      const collection = alice.firestore().collection('users/alice/accounts');
      
      await assertSucceeds(collection.get());
    });

    test('denies user reading other accounts', async () => {
      const alice = testEnv.authenticatedContext('alice');
      const collection = alice.firestore().collection('users/bob/accounts');
      
      await assertFails(collection.get());
    });

    test('allows creating valid account', async () => {
      const alice = testEnv.authenticatedContext('alice');
      const doc = alice.firestore().doc('users/alice/accounts/acc-1');
      
      await assertSucceeds(doc.set({
        userId: 'alice',
        name: 'Test Account',
        type: 'checking',
        currency: 'USD',
        initialBalanceCents: 100000,
        currentBalanceCents: 100000,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        schemaVersion: 1,
      }));
    });

    test('denies invalid account type', async () => {
      const alice = testEnv.authenticatedContext('alice');
      const doc = alice.firestore().doc('users/alice/accounts/acc-1');
      
      await assertFails(doc.set({
        userId: 'alice',
        name: 'Test Account',
        type: 'invalid-type', // Invalid
        currency: 'USD',
        initialBalanceCents: 100000,
        currentBalanceCents: 100000,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        schemaVersion: 1,
      }));
    });
  });

  describe('Transaction Rules', () => {
    test('allows creating transaction with valid data', async () => {
      const alice = testEnv.authenticatedContext('alice');
      const doc = alice.firestore().doc('users/alice/transactions/tx-1');
      
      await assertSucceeds(doc.set({
        userId: 'alice',
        accountId: 'acc-1',
        type: 'expense',
        amountCents: 5000,
        currency: 'USD',
        date: new Date(),
        description: 'Test',
        createdAt: new Date(),
        updatedAt: new Date(),
        schemaVersion: 1,
      }));
    });

    test('denies negative amount for expense', async () => {
      const alice = testEnv.authenticatedContext('alice');
      const doc = alice.firestore().doc('users/alice/transactions/tx-1');
      
      await assertFails(doc.set({
        userId: 'alice',
        accountId: 'acc-1',
        type: 'expense',
        amountCents: -5000, // Invalid
        currency: 'USD',
        date: new Date(),
        description: 'Test',
        createdAt: new Date(),
        updatedAt: new Date(),
        schemaVersion: 1,
      }));
    });

    test('denies transaction without required fields', async () => {
      const alice = testEnv.authenticatedContext('alice');
      const doc = alice.firestore().doc('users/alice/transactions/tx-1');
      
      await assertFails(doc.set({
        userId: 'alice',
        accountId: 'acc-1',
        type: 'expense',
        // Missing amountCents
        currency: 'USD',
        date: new Date(),
        description: 'Test',
        createdAt: new Date(),
        updatedAt: new Date(),
        schemaVersion: 1,
      }));
    });
  });

  describe('Budget Rules', () => {
    test('allows creating budget with valid period', async () => {
      const alice = testEnv.authenticatedContext('alice');
      const doc = alice.firestore().doc('users/alice/budgets/budget-1');
      
      await assertSucceeds(doc.set({
        userId: 'alice',
        name: 'Monthly Budget',
        amountCents: 100000,
        currency: 'USD',
        period: 'monthly',
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        schemaVersion: 1,
      }));
    });

    test('denies invalid budget period', async () => {
      const alice = testEnv.authenticatedContext('alice');
      const doc = alice.firestore().doc('users/alice/budgets/budget-1');
      
      await assertFails(doc.set({
        userId: 'alice',
        name: 'Invalid Budget',
        amountCents: 100000,
        currency: 'USD',
        period: 'weekly', // Invalid - only monthly/yearly allowed
        startDate: new Date(),
        endDate: new Date(),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        schemaVersion: 1,
      }));
    });
  });

  describe('Cross-User Access', () => {
    test('denies all cross-user access attempts', async () => {
      const alice = testEnv.authenticatedContext('alice');
      
      // Try to access Bob's data
      await assertFails(alice.firestore().doc('users/bob').get());
      await assertFails(alice.firestore().collection('users/bob/accounts').get());
      await assertFails(alice.firestore().collection('users/bob/transactions').get());
      await assertFails(alice.firestore().collection('users/bob/categories').get());
      await assertFails(alice.firestore().collection('users/bob/budgets').get());
      await assertFails(alice.firestore().collection('users/bob/goals').get());
      await assertFails(alice.firestore().collection('users/bob/tags').get());
    });
  });

  describe('Unauthenticated Access', () => {
    test('denies all unauthenticated access', async () => {
      const unauth = testEnv.unauthenticatedContext();
      
      await assertFails(unauth.firestore().doc('users/alice').get());
      await assertFails(unauth.firestore().collection('users/alice/accounts').get());
    });
  });
});
```

## Testing Best Practices Document

### Create `docs/testing/BEST_PRACTICES.md`
```markdown
# BudApp Testing Best Practices

## General Guidelines

### 1. Test Naming Convention
```dart
// Use descriptive test names following "should [expected behavior] when [condition]"
test('should return null when account does not exist', () {
  // Test implementation
});

// Group related tests
group('AccountRepository', () {
  group('createAccount', () {
    test('should create account with valid data', () {});
    test('should throw exception when user id is missing', () {});
  });
});
```

### 2. Test Data Management
- Use TestDataFactory for consistent test data
- Avoid hardcoded values in tests
- Create minimal data needed for each test
- Clean up test data after each test

### 3. Mocking Guidelines
- Mock at the boundary (repositories, services)
- Don't mock what you don't own (Flutter/Firebase internals)
- Use fake implementations for complex scenarios
- Keep mocks simple and focused

### 4. Async Testing
```dart
// Always use async/await for clarity
test('should fetch data asynchronously', () async {
  final result = await repository.getData();
  expect(result, isNotNull);
});

// Use expectLater for streams
test('should emit values', () async {
  final stream = repository.watchData();
  await expectLater(
    stream,
    emitsInOrder([data1, data2, data3]),
  );
});
```

### 5. Widget Testing
- Test behavior, not implementation
- Use finder by Key for critical elements
- Test user interactions thoroughly
- Verify both visual and functional aspects

### 6. Integration Testing
- Test complete user flows
- Include both happy and error paths
- Test offline functionality
- Verify data persistence

### 7. Performance Considerations
- Keep unit tests under 1ms
- Mock heavy operations
- Use test tags for slow tests
- Run integration tests separately

## Common Patterns

### Repository Testing Pattern
```dart
group('Repository Tests', () {
  late FakeFirebaseFirestore firestore;
  late RepositoryImplementation repository;

  setUp(() {
    firestore = FakeFirebaseFirestore();
    repository = RepositoryImplementation(firestore: firestore);
  });

  test('should perform operation', () async {
    // Arrange
    final testData = TestDataFactory.createTestData();
    
    // Act
    final result = await repository.operation(testData);
    
    // Assert
    expect(result, equals(expectedResult));
  });
});
```

### Provider Testing Pattern
```dart
group('Provider Tests', () {
  late ProviderContainer container;
  late MockRepository mockRepository;

  setUp(() {
    mockRepository = MockRepository();
    container = ProviderContainer(
      overrides: [
        repositoryProvider.overrideWithValue(mockRepository),
      ],
    );
  });

  tearDown(() {
    container.dispose();
  });

  test('should handle state correctly', () async {
    // Test implementation
  });
});
```

### Widget Testing Pattern
```dart
testWidgets('should display widget correctly', (tester) async {
  await tester.pumpWidget(
    TestWrapper.createTestWidget(
      const MyWidget(),
      overrides: [/* provider overrides */],
    ),
  );

  expect(find.text('Expected Text'), findsOneWidget);
  
  await tester.tap(find.byType(ElevatedButton));
  await tester.pumpAndSettle();
  
  expect(find.text('Updated Text'), findsOneWidget);
});
```

## Debugging Tests

### 1. Visual Debugging
```dart
// Use debugDumpApp() to see widget tree
testWidgets('debug widget tree', (tester) async {
  await tester.pumpWidget(MyApp());
  debugDumpApp(); // Prints widget tree to console
});
```

### 2. Golden Test Debugging
```dart
// Update golden files when UI changes
flutter test --update-goldens
```

### 3. Integration Test Debugging
```dart
// Add delays to see what's happening
await tester.pump(const Duration(seconds: 2));

// Take screenshots during test
await tester.takeScreenshot('test-step-1');
```

## Coverage Guidelines

### Running Coverage
```bash
# Generate coverage report
flutter test --coverage

# Generate HTML report
genhtml coverage/lcov.info -o coverage/html

# Open report
open coverage/html/index.html
```

### Excluding Files from Coverage
```dart
// coverage:ignore-file
// coverage:ignore-line
// coverage:ignore-start
// coverage:ignore-end
```

### Coverage Targets
- Aim for 80% overall coverage
- 100% coverage for models
- 90% coverage for repositories
- 85% coverage for services
- Focus on testing behavior, not achieving coverage

## Continuous Integration

### Test Organization
```yaml
# Run different test types separately
flutter test test/unit/
flutter test test/widget/
flutter test test/integration/
```

### Performance Monitoring
- Track test execution time
- Fail builds if tests exceed time limits
- Monitor coverage trends
- Alert on coverage drops

## Common Pitfalls to Avoid

1. **Over-mocking**: Don't mock everything, use real implementations where possible
2. **Brittle Tests**: Test behavior, not implementation details
3. **Slow Tests**: Keep unit tests fast, move slow tests to integration
4. **Flaky Tests**: Ensure tests are deterministic and repeatable
5. **Poor Test Names**: Use descriptive names that explain what is being tested
6. **Missing Edge Cases**: Test error conditions and boundary values
7. **Ignoring Warnings**: Fix all static analysis warnings in tests

## Resources

- [Flutter Testing Documentation](https://flutter.dev/docs/testing)
- [Mocktail Documentation](https://pub.dev/packages/mocktail)
- [Fake Cloud Firestore](https://pub.dev/packages/fake_cloud_firestore)
- [Firebase Testing Guide](https://firebase.google.com/docs/testing)
```

## Summary

This comprehensive testing implementation plan provides:

1. **Complete test infrastructure** with helper classes and utilities
2. **Detailed unit test examples** for all layers (models, repositories, services, providers)
3. **Widget testing patterns** for components and screens
4. **Integration test scenarios** for critical user flows
5. **Security rules testing** to validate Firestore rules
6. **Best practices guide** for consistent testing approach
7. **CI/CD integration** for automated testing

The plan follows TDD principles, leverages the repository pattern for easy mocking, and targets 80% test coverage while maintaining fast feedback loops. 