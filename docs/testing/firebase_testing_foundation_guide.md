# Firebase Testing Foundation Guide

## Overview

This guide provides comprehensive documentation for maintaining and extending BudApp's Firebase testing foundation, which uses firebase_auth_mocks + fake_cloud_firestore for reliable Firebase simulation across all test layers.

## Architecture

### Testing Layer Patterns

```
┌─────────────────────────────────────────────────────────────┐
│                    Testing Architecture                     │
├─────────────────────────────────────────────────────────────┤
│ Widget Tests        │ MockProviders + firebase_auth_mocks   │
├─────────────────────────────────────────────────────────────┤
│ Service Tests       │ mocktail for dependency mocking      │
├─────────────────────────────────────────────────────────────┤
│ Integration Tests   │ FirebaseTestSetup (Auth + Firestore) │
├─────────────────────────────────────────────────────────────┤
│ Repository Tests    │ FirebaseTestSetup (Auth + Firestore) │
└─────────────────────────────────────────────────────────────┘
```

### Core Components

1. **FirebaseTestSetup**: Central Firebase test environment creation
2. **MockProviders**: Provider overrides with firebase_auth_mocks integration
3. **TestWrapper**: Widget testing wrapper with Firebase context
4. **firebase_auth_mocks**: Real Firebase Auth behavior simulation
5. **fake_cloud_firestore**: Real Firestore behavior simulation

## Implementation Patterns

### 1. Repository Tests

**Pattern**: Use FirebaseTestSetup for Firebase Auth + Firestore integration

```dart
import '../../helpers/firebase_test_setup.dart';

group('Repository Tests', () {
  late FirebaseTestSetup testSetup;
  late RepositoryImpl repository;

  setUp(() async {
    testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
      uid: 'test-user-id',
      email: '<EMAIL>',
    );
    
    final firestoreService = FirestoreService(testSetup.firestore);
    repository = RepositoryImpl(firestoreService, testSetup.auth);
  });

  tearDown(() async {
    await testSetup.dispose();
  });

  test('should perform CRUD operations with Firebase context', () async {
    // Test implementation with real Firebase behavior
  });
});
```

### 2. Service Tests

**Pattern**: Use mocktail for dependency mocking (correct service layer testing)

```dart
import 'package:mocktail/mocktail.dart';

class MockRepository extends Mock implements Repository {}

group('Service Tests', () {
  late MockRepository mockRepository;
  late ServiceImpl service;

  setUp(() {
    mockRepository = MockRepository();
    service = ServiceImpl(mockRepository);
  });

  test('should handle business logic correctly', () async {
    // Arrange
    when(() => mockRepository.getData()).thenAnswer((_) async => testData);
    
    // Act & Assert
    final result = await service.processData();
    expect(result, expectedResult);
  });
});
```

### 3. Widget Tests

**Pattern**: Use MockProviders with firebase_auth_mocks integration

```dart
import '../../helpers/test_wrapper.dart';
import '../../helpers/mock_providers.dart';

group('Widget Tests', () {
  testWidgets('should display correctly with Firebase context', (tester) async {
    await tester.pumpWidget(
      TestWrapper.createTestWidget(
        const MyWidget(),
        overrides: MockProviders.authenticatedUserOverrides(),
      ),
    );

    // Test widget behavior with Firebase authentication context
    expect(find.text('Welcome'), findsOneWidget);
  });
});
```

### 4. Integration Tests

**Pattern**: Use FirebaseTestSetup for comprehensive Firebase simulation

```dart
import '../../helpers/firebase_test_setup.dart';

group('Integration Tests', () {
  late FirebaseTestSetup testSetup;
  late ServiceA serviceA;
  late ServiceB serviceB;

  setUp(() async {
    testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
      uid: 'test-user-id',
      email: '<EMAIL>',
    );
    
    final firestoreService = FirestoreService(testSetup.firestore);
    serviceA = ServiceA(firestoreService, testSetup.auth);
    serviceB = ServiceB(firestoreService, testSetup.auth);
  });

  tearDown(() async {
    await testSetup.dispose();
  });

  test('should handle cross-service operations correctly', () async {
    // Test complex interactions with real Firebase behavior
  });
});
```

## Best Practices

### 1. Test Setup Guidelines

- **Always use FirebaseTestSetup for Firebase-dependent tests**
- **Always call testSetup.dispose() in tearDown**
- **Use consistent user IDs for predictable test behavior**
- **Prefer authenticated test setups for realistic scenarios**

### 2. Mocking Strategy

- **Repository Tests**: Use FirebaseTestSetup (test Firebase integration)
- **Service Tests**: Use mocktail for dependencies (test business logic)
- **Widget Tests**: Use MockProviders (test UI with Firebase context)
- **Integration Tests**: Use FirebaseTestSetup (test end-to-end flows)

### 3. Authentication Context

```dart
// ✅ Correct: Authenticated user setup
final testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
  uid: 'test-user-id',
  email: '<EMAIL>',
);

// ✅ Correct: Unauthenticated setup (when needed)
final testSetup = await FirebaseTestSetup.createUnauthenticated();

// ❌ Avoid: Custom MockFirebaseAuth setup
final mockAuth = MockFirebaseAuth(); // Use FirebaseTestSetup instead
```

### 4. Resource Management

```dart
// ✅ Correct: Proper cleanup
tearDown(() async {
  await testSetup.dispose(); // Always dispose Firebase test resources
});

// ✅ Correct: Container disposal for provider tests
tearDown(() {
  container.dispose();
});
```

## Common Patterns

### Provider Testing with Firebase Context

```dart
test('provider should work with Firebase context', () async {
  final container = ProviderContainer(
    overrides: [
      firestoreServiceProvider.overrideWithValue(
        FirestoreService(testSetup.firestore),
      ),
      firebaseAuthProvider.overrideWithValue(testSetup.auth),
    ],
  );

  final result = container.read(myProvider);
  expect(result, expectedValue);
  
  container.dispose();
});
```

### Firestore Data Setup

```dart
setUp(() async {
  // Create test data in Firestore
  await testSetup.firestore
      .collection('users')
      .doc('test-user-id')
      .collection('accounts')
      .doc('test-account-id')
      .set(testAccount.toJson());
});
```

### Authentication State Testing

```dart
test('should handle authenticated user correctly', () async {
  // testSetup.auth automatically has authenticated user
  final currentUser = testSetup.auth.currentUser;
  expect(currentUser, isNotNull);
  expect(currentUser!.uid, equals('test-user-id'));
});
```

## Migration Guidelines

### When Adding New Tests

1. **Identify the test layer** (repository, service, widget, integration)
2. **Choose the appropriate pattern** from this guide
3. **Use FirebaseTestSetup for Firebase-dependent tests**
4. **Use mocktail for service layer dependency mocking**
5. **Always include proper tearDown cleanup**

### When Updating Existing Tests

1. **Check if test uses custom Firebase mocking**
2. **Migrate to FirebaseTestSetup if Firebase integration is needed**
3. **Keep mocktail patterns for service layer tests**
4. **Update provider overrides to use new patterns**
5. **Verify test still passes after migration**

## Troubleshooting

### Common Issues

1. **"User not authenticated" errors**
   - Ensure using `createWithAuthenticatedUser()` not `createUnauthenticated()`
   - Check that `signedIn: true` is set in MockFirebaseAuth

2. **Provider override issues**
   - Use `MockProviders.authenticatedUserOverrides()` for widget tests
   - Ensure FirebaseTestSetup providers are properly overridden

3. **Resource cleanup issues**
   - Always call `await testSetup.dispose()` in tearDown
   - Dispose ProviderContainer instances in tearDown

4. **Test isolation issues**
   - Each test should create its own FirebaseTestSetup instance
   - Avoid sharing test setup between tests

### Performance Considerations

- **FirebaseTestSetup creation is fast** (~10ms per test)
- **Proper disposal prevents memory leaks**
- **Use targeted test runs for faster development**

## Security Rules Testing (Future)

The foundation is ready for security rules testing:

```dart
// Future pattern for security rules testing
test('should enforce security rules correctly', () async {
  final testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
    uid: 'test-user-id',
    email: '<EMAIL>',
  );
  
  // Security rules will be enforced with proper auth context
  await expectLater(
    unauthorizedOperation(),
    throwsA(isA<FirebaseException>()),
  );
});
```

## Conclusion

This Firebase testing foundation provides:

- **Reliable Firebase behavior simulation**
- **Consistent authentication context across all tests**
- **Proper separation of concerns between test layers**
- **Foundation for comprehensive security rules testing**
- **Maintainable and scalable testing patterns**

Follow these patterns to ensure robust, reliable tests that accurately simulate real Firebase behavior while maintaining appropriate testing architecture.
