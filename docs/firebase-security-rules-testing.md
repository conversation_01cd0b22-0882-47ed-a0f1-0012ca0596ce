# Firebase Security Rules Testing Documentation

## Overview

This document describes the comprehensive testing strategy for Firebase Security Rules in the BudApp project. The testing suite ensures data security, validation, and business logic compliance across all Firestore collections.

## Test Structure

### Test Files Organization

```
firebase/test/
├── security-rules-basic.test.js          # Basic access control tests
├── security-rules-enhanced.test.js       # Enhanced validation tests
├── account-security-rules.test.js        # Account-specific tests
├── budget-security-rules.test.js         # Budget-specific tests
├── category-security-rules.test.js       # Category-specific tests
├── goal-security-rules.test.js           # Goal-specific tests
├── tag-security-rules.test.js            # Tag-specific tests
├── transaction-security-rules.test.js    # Transaction-specific tests
├── business-logic-validation.test.js     # Business logic validation tests
└── edge-case-boundary.test.js            # Edge cases and boundary tests
```

## Test Categories

### 1. Basic Security Tests
- **Authentication**: Verify users can only access their own data
- **Authorization**: Test read/write permissions per collection
- **Cross-user Access**: Ensure users cannot access other users' data

### 2. Data Validation Tests
- **Field Validation**: Test required fields, data types, and formats
- **String Length Limits**: Validate maximum/minimum string lengths
- **Numeric Ranges**: Test valid ranges for numeric fields
- **Date Validation**: Ensure proper date formats and constraints

### 3. Business Logic Validation Tests
- **Hierarchical Categories**: Test parent-child relationships and circular reference prevention
- **Account-Transaction Consistency**: Validate transaction account references
- **Transfer Logic**: Ensure transfer transactions have proper account fields
- **Budget-Category Relationships**: Test budget category references
- **Tag-Transaction Relationships**: Validate tag references in transactions

### 4. Edge Case and Boundary Tests
- **String Length Boundaries**: Test exact limits (100 chars for accounts, 50 for tags, etc.)
- **Numeric Boundaries**: Test maximum values (999,999,999 cents for transactions)
- **Date Edge Cases**: Test very old dates, future date restrictions
- **Unicode Support**: Test special characters, emojis, and international text
- **Zero and Negative Values**: Test boundary conditions for amounts

### 5. Security Enhancement Tests
- **Malicious Input**: Test injection attempts and malformed data
- **Field Tampering**: Verify immutable fields cannot be changed
- **Schema Validation**: Ensure all required fields are present
- **Color Validation**: Test hex color format validation

## Key Validation Rules

### Account Validation
- Name: 1-100 characters
- Type: Must be valid account type
- Balance: Can be negative (for credit accounts)
- Required fields: id, userId, name, type, classification

### Transaction Validation
- Amount: 1-999,999,999 cents (positive only)
- Account Logic:
  - Expense: requires `fromAccountId`
  - Income: requires `toAccountId`
  - Transfer: requires both `fromAccountId` and `toAccountId`
- Notes: Maximum 500 characters
- Status: Must be valid enum value

### Category Validation
- Name: 1-100 characters
- Type: 'income' or 'expense'
- Parent-Child: Same type as parent, no circular references
- Sort Order: Non-negative integer

### Tag Validation
- Name: 1-50 characters
- Color: Valid hex format (#RRGGBB)
- Usage Count: Non-negative integer

### Budget Validation
- Amount: Positive values only
- Period: Valid period format
- Category Reference: Must exist and belong to same user

### Goal Validation
- Target Amount: Positive values only
- Current Amount: Non-negative
- Target Date: Cannot be in the past for contributions

## Running Tests

### Prerequisites
```bash
# Install dependencies
cd firebase/test
npm install

# Start Firebase emulator
firebase emulators:start --only firestore
```

### Run All Tests
```bash
npm test
```

### Run Specific Test Suites
```bash
# Business logic tests
npm test business-logic-validation.test.js

# Edge case tests
npm test edge-case-boundary.test.js

# Specific entity tests
npm test account-security-rules.test.js
npm test transaction-security-rules.test.js
```

## Test Data Patterns

### Valid Test Data Examples
```javascript
// Valid Account
{
  id: 'account1',
  userId: 'user1',
  name: 'Checking Account',
  type: 'checking',
  classification: 'asset',
  initialBalanceCents: 100000,
  currentBalanceCents: 100000,
  schemaVersion: 1,
  isPrimary: true,
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  metadata: {}
}

// Valid Transaction (Expense)
{
  id: 'transaction1',
  userId: 'user1',
  type: 'expense',
  status: 'completed',
  amountCents: 2500,
  fromAccountId: 'account1',
  description: 'Grocery shopping',
  schemaVersion: 1,
  transactionDate: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
  isActive: true
}
```

## Security Rules Coverage

### Collections Tested
- ✅ Users
- ✅ Accounts
- ✅ Transactions
- ✅ Categories
- ✅ Budgets
- ✅ Tags
- ✅ Goals
- ✅ Goal Contributions

### Validation Coverage
- ✅ Authentication and authorization
- ✅ Field validation and data types
- ✅ String length limits
- ✅ Numeric ranges and boundaries
- ✅ Date validation
- ✅ Business logic constraints
- ✅ Cross-entity relationships
- ✅ Edge cases and boundary conditions
- ✅ Security against malicious input

## Continuous Integration

Tests should be run as part of the CI/CD pipeline to ensure:
1. All security rules are properly validated
2. Business logic constraints are enforced
3. Data integrity is maintained
4. No regressions are introduced

## Troubleshooting

### Common Issues
1. **Emulator Not Running**: Ensure Firebase emulator is started before running tests
2. **Permission Denied**: Check that test data includes proper userId fields
3. **Validation Errors**: Verify test data matches the expected schema
4. **Timeout Issues**: Increase test timeout for complex validation tests

### Debug Mode
Enable debug logging for detailed rule evaluation:
```bash
FIRESTORE_EMULATOR_HOST=localhost:8080 npm test -- --verbose
```

## Maintenance

### Adding New Tests
1. Follow existing test patterns
2. Include both positive and negative test cases
3. Test boundary conditions
4. Validate business logic constraints
5. Update this documentation

### Updating Rules
When modifying security rules:
1. Update corresponding tests
2. Run full test suite
3. Verify no regressions
4. Update validation documentation
