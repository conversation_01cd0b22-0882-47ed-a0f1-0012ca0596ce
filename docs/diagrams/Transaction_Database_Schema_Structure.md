erDiagram
    USER {
        string uid PK
        string email
        timestamp createdAt
    }
    
    ACCOUNT {
        string id PK
        string userId FK
        string name
        string type
        string classification
        int initialBalanceCents
        string currencyCode
        boolean isActive
        timestamp createdAt
        timestamp updatedAt
    }
    
    TRANSACTION {
        string id PK
        string userId FK
        string type
        string status
        int amountCents
        string currencyCode
        string fromAccountId FK
        string toAccountId FK
        string categoryId FK
        string description
        string notes
        string[] tags
        timestamp transactionDate
        timestamp createdAt
        timestamp updatedAt
        map metadata
    }
    
    CATEGORY {
        string id PK
        string userId FK
        string name
        string type
        string source
        string parentId FK
        string description
        string color
        string icon
        boolean isActive
        int sortOrder
        timestamp createdAt
        timestamp updatedAt
    }
    
    USER ||--o{ ACCOUNT : owns
    USER ||--o{ TRANSACTION : owns
    USER ||--o{ CATEGORY : owns
    ACCOUNT ||--o{ TRANSACTION : "fromAccount"
    ACCOUNT ||--o{ TRANSACTION : "toAccount"
    CATEGORY ||--o{ TRANSACTION : categorizes
    CATEGORY ||--o{ CATEGORY : "parent-child"