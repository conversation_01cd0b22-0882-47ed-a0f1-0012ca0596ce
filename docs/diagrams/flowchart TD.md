flowchart TD
    A[App Bar: ← Add Transaction Save] --> B[Transaction Type Selector]
    B --> C{Transaction Type}
    
    C -->|Income| D[Amount Input<br/>$ 1,234.56 USD]
    C -->|Expense| D
    C -->|Transfer| D
    
    D --> E{Account Selection}
    
    E -->|Income| F[To Account *<br/>🏦 Chase Checking]
    E -->|Expense| G[From Account *<br/>🏦 Chase Checking]
    E -->|Transfer| H[From Account *<br/>🏦 Chase Checking<br/><br/>To Account *<br/>💰 Savings Account]
    
    F --> I{Category Section}
    G --> I
    H --> J[Transaction Details]
    
    I -->|Income/Expense| K[Category<br/>🍔 Food & Dining]
    I -->|Transfer| J
    
    K --> J
    J --> L[Date *<br/>📅 January 15, 2025]
    L --> M[Description<br/>Grocery shopping...]
    M --> N[Notes<br/>Weekly grocery run...]
    N --> O[Save Button]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style D fill:#fff3e0
    style O fill:#e8f5e8