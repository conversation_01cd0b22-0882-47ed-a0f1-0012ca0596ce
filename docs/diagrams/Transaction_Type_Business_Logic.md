flowchart TD
    A[Transaction Creation] --> B{Transaction Type?}
    
    B -->|Income| C[Income Transaction]
    B -->|Expense| D[Expense Transaction]
    B -->|Transfer| E[Transfer Transaction]
    
    C --> C1[Required: toAccountId]
    C --> C2[Optional: categoryId]
    C --> C3[Forbidden: fromAccountId]
    C1 --> C4[Money flows INTO account]
    
    D --> D1[Required: fromAccountId]
    D --> D2[Optional: categoryId]
    D --> D3[Forbidden: toAccountId]
    D1 --> D4[Money flows OUT OF account]
    
    E --> E1[Required: fromAccountId]
    E --> E2[Required: toAccountId]
    E --> E3[Forbidden: categoryId]
    E --> E4[Validation: fromAccountId ≠ toAccountId]
    E1 --> E5[Money moves BETWEEN accounts]
    
    C4 --> F[Validate Business Rules]
    D4 --> F
    E5 --> F
    
    F --> G[Create Transaction Record]
    G --> H[Update Account Balances]
    
    style C fill:#e8f5e8
    style D fill:#ffe8e8
    style E fill:#e8f0ff
    style F fill:#fff3e0
    style H fill:#f3e5f5