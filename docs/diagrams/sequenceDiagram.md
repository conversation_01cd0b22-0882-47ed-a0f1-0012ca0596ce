sequenceDiagram
    participant User
    participant TypeSelector
    participant AmountField
    participant AccountSelector
    participant CategorySelector
    participant FormValidator
    participant SaveButton
    
    User->>TypeSelector: Select "Expense"
    TypeSelector->>AccountSelector: Show "From Account"
    TypeSelector->>CategorySelector: Show expense categories
    TypeSelector->>FormValidator: Reset validation
    
    User->>AmountField: Enter "25.50"
    AmountField->>FormValidator: Validate amount
    FormValidator->>SaveButton: Update button state
    
    User->>AccountSelector: Select account
    AccountSelector->>FormValidator: Validate selection
    FormValidator->>SaveButton: Update button state
    
    User->>CategorySelector: Select "Food & Dining"
    CategorySelector->>FormValidator: Validate (optional)
    
    User->>SaveButton: Tap Save
    SaveButton->>FormValidator: Final validation
    FormValidator->>SaveButton: Enable submission
    SaveButton->>User: Show loading state
    
    Note over User,SaveButton: Form submission flow
    SaveButton->>User: Success feedback