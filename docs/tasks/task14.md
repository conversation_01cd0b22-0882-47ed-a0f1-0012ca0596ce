I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end. Make sure you fix all the linting, compilation or validation issues after successful implementation of the plan.

### Observations

Based on my exploration of the BudApp codebase, I found that Task 14 "Budget Editing and Deletion" has 5 well-defined subtasks focused on implementing budget editing and soft deletion functionality. The project already has comprehensive budget data models and repository interfaces defined in `/Users/<USER>/development/budapp/lib/data/repositories/interfaces/budget_repository.dart`, along with supporting infrastructure like Remote Config for budget limits, localization strings, and Firestore security rules planning. However, the actual budget system implementation doesn't exist yet - the budget providers are placeholder files and there are no budget UI components. Task 14 depends on Task 13 (Budget Creation and Progress Tracking), which means the entire budget foundation needs to be built first. The project follows established patterns with Repository pattern, Riverpod state management, feature-based architecture, and comprehensive testing that I can leverage for this implementation.

### Approach

This plan implements Task 14 by building upon the existing budget data models and repository interfaces to create a complete budget editing and deletion system. The approach follows the established project patterns: implementing the Repository layer first with Firestore operations, then building Riverpod providers for state management, followed by Material 3 UI components for editing and deletion. The implementation uses soft deletion to preserve data integrity, atomic Firestore operations for consistency, and comprehensive error handling with user feedback. Each subtask builds incrementally, ensuring proper testing and integration with existing systems like Remote Config limits and security rules.

### Reasoning

I analyzed the task structure from the tasks.json file and found Task 14 has 5 subtasks for budget editing and deletion. I explored the existing budget infrastructure by examining the budget repository interface, data models, Remote Config integration, and localization strings. I searched for existing budget implementations and found only placeholder files, confirming that the budget system foundation needs to be built. I reviewed the established project patterns from other features like accounts, transactions, and categories to understand the architecture approach. I identified the dependency on Task 13 and the need to implement the complete budget system foundation before the editing and deletion features.

## Mermaid Diagram

sequenceDiagram
    participant User
    participant UI as Budget Edit Screen
    participant Provider as Budget Provider
    participant Validator as Budget Validator
    participant Repository as Budget Repository
    participant Firestore as Firestore DB
    participant ErrorService as Error Service

    User->>UI: Opens budget edit screen
    UI->>Provider: Load existing budget data
    Provider->>Repository: getBudget(budgetId)
    Repository->>Firestore: Fetch budget document
    Firestore-->>Repository: Budget data
    Repository-->>Provider: Budget object
    Provider-->>UI: Budget state with data
    UI-->>User: Pre-populated form

    User->>UI: Modifies budget fields
    UI->>Validator: Validate form data
    Validator-->>UI: Validation results

    User->>UI: Clicks save button
    UI->>Provider: updateBudget(budgetData)
    Provider->>Validator: Final validation
    Validator-->>Provider: Validation passed
    Provider->>Repository: updateBudget(budgetId, budget)
    Repository->>Firestore: Atomic update operation
    
    alt Update Success
        Firestore-->>Repository: Success response
        Repository-->>Provider: Update confirmed
        Provider-->>UI: Success state
        UI-->>User: Success message & navigation
    else Update Failure
        Firestore-->>Repository: Error response
        Repository-->>Provider: Error details
        Provider->>ErrorService: Process error
        ErrorService-->>Provider: User-friendly message
        Provider-->>UI: Error state
        UI-->>User: Error message with retry option
    end

    User->>UI: Clicks delete button
    UI-->>User: Confirmation dialog
    User->>UI: Confirms deletion
    UI->>Provider: deleteBudget(budgetId)
    Provider->>Repository: deleteBudget(budgetId) [soft deletion]
    Repository->>Firestore: Update status to 'cancelled'
    Firestore-->>Repository: Success response
    Repository-->>Provider: Deletion confirmed
    Provider-->>UI: Success state
    UI-->>User: Success message & navigation to list

## Proposed File Changes

### lib/data/models/budget.dart(MODIFY)

References: 

- lib/data/models/account.dart
- lib/data/models/transaction.dart
- lib/data/repositories/interfaces/budget_repository.dart

Create a comprehensive Budget data model using Freezed for immutability and JSON serialization. Include all fields from the existing budget repository interface: `id`, `userId`, `name`, `description`, `period`, `status`, `targetAmountCents`, `currencyCode`, `categoryId`, `startDate`, `endDate`, `isRecurring`, `alertsEnabled`, `alertThresholdPercent`, `createdAt`, `updatedAt`, and `metadata`. Add the BudgetPeriod and BudgetStatus enums. Include helper methods like `isOverallBudget`, `isCategoryBudget`, and validation methods. Use `@freezed` annotation and include `toJson()` and `fromJson()` methods for Firestore integration. Follow the same pattern as existing models in `/Users/<USER>/development/budapp/lib/data/models/account.dart` and `/Users/<USER>/development/budapp/lib/data/models/transaction.dart`.

### lib/data/models/budget_progress.dart(MODIFY)

References: 

- lib/data/models/account.dart
- lib/data/repositories/interfaces/budget_repository.dart

Create a BudgetProgress data model using Freezed to track budget spending progress. Include fields: `budgetId`, `targetAmountCents`, `spentAmountCents`, `remainingAmountCents`, `progressPercent`, `isOverBudget`, and `calculatedAt`. Add helper methods for progress calculations and status checks. Include JSON serialization for potential caching or API responses. Follow the same Freezed pattern as other data models in the project.

### lib/data/repositories/implementations/budget_repository_impl.dart(MODIFY)

References: 

- lib/data/repositories/implementations/account_repository_impl.dart
- lib/data/repositories/implementations/transaction_repository_impl.dart
- lib/data/repositories/interfaces/budget_repository.dart

Implement the IBudgetRepository interface with full Firestore integration. Create methods for all CRUD operations including `createBudget()`, `updateBudget()`, `updateBudgetStatus()`, and `deleteBudget()` (soft deletion by setting status to cancelled). Implement budget editing with atomic Firestore operations and proper error handling. Use the collection path `users/{userId}/budgets` and follow the same patterns as `/Users/<USER>/development/budapp/lib/data/repositories/implementations/account_repository_impl.dart` and `/Users/<USER>/development/budapp/lib/data/repositories/implementations/transaction_repository_impl.dart`. Include real-time listeners with `watchUserBudgets()` and `watchBudget()` methods. Implement budget progress calculation by aggregating transaction data. Add validation methods and conflict checking for overlapping budget periods.

### lib/providers/repository_providers.dart(MODIFY)

References: 

- lib/data/repositories/implementations/budget_repository_impl.dart(MODIFY)

Add the budget repository provider to the existing repository providers. Uncomment and implement the `budgetRepositoryProvider` that's currently commented out in the file. Follow the same pattern as other repository providers like `accountRepositoryProvider` and `transactionRepositoryProvider`. Ensure proper dependency injection with Firestore service.

### lib/features/budgets/providers/budget_providers.dart(MODIFY)

References: 

- lib/features/accounts/providers/account_providers.dart
- lib/features/transactions/providers/transaction_providers.dart

Replace the placeholder content with comprehensive Riverpod providers for budget management. Create `BudgetListNotifier` extending `AsyncNotifier<List<Budget>>` for managing the list of user budgets with real-time updates. Create `BudgetNotifier` extending `AsyncNotifier<Budget?>` for managing individual budget state. Add providers for budget editing (`budgetEditProvider`), budget deletion (`budgetDeletionProvider`), and budget progress tracking (`budgetProgressProvider`). Include error handling and loading states. Follow the same patterns as `/Users/<USER>/development/budapp/lib/features/accounts/providers/account_providers.dart` and `/Users/<USER>/development/budapp/lib/features/transactions/providers/transaction_providers.dart`. Use `@riverpod` annotations and include proper dependency injection.

### lib/features/budgets/services/budget_validators.dart(MODIFY)

References: 

- lib/features/accounts/services/account_validators.dart
- lib/features/transactions/services/transaction_validators.dart
- lib/utils/remote_config_utils.dart

Create a comprehensive validation service for budget operations. Include methods for validating budget creation and editing: `validateBudgetName()`, `validateTargetAmount()`, `validateDateRange()`, `validateBudgetPeriod()`, and `validateCategoryReference()`. Add business logic validation like checking for overlapping budget periods, ensuring end date is after start date, and validating target amounts are positive. Include Remote Config integration to check budget limits for free vs premium users using `/Users/<USER>/development/budapp/lib/utils/remote_config_utils.dart`. Follow the same pattern as `/Users/<USER>/development/budapp/lib/features/accounts/services/account_validators.dart` and `/Users/<USER>/development/budapp/lib/features/transactions/services/transaction_validators.dart`.

### lib/features/budgets/services/budget_error_service.dart(NEW)

References: 

- lib/features/transactions/services/transaction_error_service.dart

Create a centralized error handling service for budget operations. Include methods for translating Firestore errors into user-friendly messages: `handleBudgetCreationError()`, `handleBudgetUpdateError()`, `handleBudgetDeletionError()`, and `handleValidationError()`. Add specific error handling for budget-related scenarios like exceeding premium limits, overlapping budget periods, and invalid date ranges. Include localization support using the app's localization system. Follow the same pattern as `/Users/<USER>/development/budapp/lib/features/transactions/services/transaction_error_service.dart` for consistent error handling across the application.

### lib/features/budgets/presentation/screens/budgets_list_screen.dart(MODIFY)

References: 

- lib/features/accounts/presentation/screens/accounts_list_screen.dart
- lib/features/transactions/presentation/screens/transactions_list_screen.dart

Create the main budgets list screen with Material 3 design. Display a list of user budgets with budget cards showing name, target amount, progress, and status. Include filtering options by status (active, paused, completed) and period (monthly, yearly). Add a floating action button for creating new budgets. Implement pull-to-refresh functionality and empty state handling. Include navigation to budget detail and edit screens. Use Riverpod for state management with the budget providers. Follow the same layout and design patterns as `/Users/<USER>/development/budapp/lib/features/accounts/presentation/screens/accounts_list_screen.dart` and `/Users/<USER>/development/budapp/lib/features/transactions/presentation/screens/transactions_list_screen.dart`.

### lib/features/budgets/presentation/screens/budget_edit_screen.dart(MODIFY)

References: 

- lib/features/accounts/presentation/screens/account_edit_screen.dart
- lib/features/transactions/presentation/screens/transaction_edit_screen.dart

Create the budget editing screen with a comprehensive form for modifying budget details. Include form fields for budget name, description, target amount, period, category selection, date range, and alert settings. Pre-populate all fields with current budget data. Implement form validation using the budget validators service. Add save and cancel actions with proper loading states and error handling. Include confirmation dialogs for significant changes. Use Material 3 form components and follow the same patterns as `/Users/<USER>/development/budapp/lib/features/accounts/presentation/screens/account_edit_screen.dart` and `/Users/<USER>/development/budapp/lib/features/transactions/presentation/screens/transaction_edit_screen.dart`. Integrate with Riverpod providers for state management and real-time updates.

### lib/features/budgets/presentation/screens/budget_detail_screen.dart(NEW)

References: 

- lib/features/accounts/presentation/screens/account_detail_screen.dart
- lib/features/transactions/presentation/screens/transaction_detail_screen.dart

Create a detailed budget view screen showing comprehensive budget information and progress. Display budget name, description, target amount, current spending, progress visualization, and remaining amount. Include a progress bar or chart showing spending vs target. Add action buttons for editing and deleting the budget. Show related transactions that contribute to this budget's spending. Include budget status management (pause/resume/complete). Follow Material 3 design principles and use the same layout patterns as `/Users/<USER>/development/budapp/lib/features/accounts/presentation/screens/account_detail_screen.dart` and `/Users/<USER>/development/budapp/lib/features/transactions/presentation/screens/transaction_detail_screen.dart`.

### lib/features/budgets/presentation/widgets/budget_card.dart(MODIFY)

References: 

- lib/features/accounts/presentation/widgets/account_card.dart
- lib/features/transactions/presentation/widgets/transaction_card.dart

Create a reusable budget card widget for displaying budget information in lists. Show budget name, target amount, current spending, progress percentage, and status indicator. Include a progress bar visualization and color coding based on budget status (green for on track, yellow for approaching limit, red for over budget). Add tap handling for navigation to budget detail screen. Include a popup menu with edit and delete options. Use Material 3 Card component and follow the same design patterns as `/Users/<USER>/development/budapp/lib/features/accounts/presentation/widgets/account_card.dart` and `/Users/<USER>/development/budapp/lib/features/transactions/presentation/widgets/transaction_card.dart`.

### lib/features/budgets/presentation/widgets/budget_form.dart(MODIFY)

References: 

- lib/features/transactions/presentation/widgets/transaction_form.dart

Create a comprehensive budget form widget for editing budgets only. Include form fields for all budget properties: name, description, target amount, period selection, category selection, date range pickers, recurring options, and alert settings. Implement real-time validation with error messages. Add amount input formatting and currency display. Include category selector that integrates with the existing category system. Use Material 3 form components and follow the same patterns as `/Users/<USER>/development/budapp/lib/features/transactions/presentation/widgets/transaction_form.dart`. Support editing mode with proper field pre-population. **Note**: Budget creation functionality has been removed in Phase 2.

### lib/features/budgets/presentation/widgets/budget_deletion_dialog.dart(NEW)

References: 

- lib/features/categories/presentation/widgets/category_deletion_dialog.dart
- lib/features/profile/presentation/widgets/account_deletion_dialog.dart

Create a confirmation dialog for budget deletion with clear warnings about the action. Display budget name and current progress information. Explain that deletion will mark the budget as cancelled but preserve historical data. Include options for permanent deletion vs soft deletion. Add confirmation text input for critical budgets. Use Material 3 dialog components and follow the same patterns as `/Users/<USER>/development/budapp/lib/features/categories/presentation/widgets/category_deletion_dialog.dart` and `/Users/<USER>/development/budapp/lib/features/profile/presentation/widgets/account_deletion_dialog.dart`. Include proper error handling and loading states during deletion.

### lib/features/budgets/presentation/widgets/budget_progress_indicator.dart(NEW)

References: 

- lib/config/design_tokens.dart

Create a visual progress indicator widget for budget spending progress. Display a progress bar with percentage completion, spent amount, remaining amount, and target amount. Use color coding to indicate budget status (green for healthy, yellow for warning, red for over budget). Include animated progress updates and responsive design for different screen sizes. Add optional compact mode for use in cards vs detailed mode for full screens. Follow Material 3 design principles and use the project's design tokens from `/Users/<USER>/development/budapp/lib/config/design_tokens.dart`.

### lib/features/budgets/presentation/widgets/empty_budgets_state.dart(MODIFY)

References: 

- lib/features/accounts/presentation/widgets/empty_accounts_state.dart
- lib/features/transactions/presentation/widgets/empty_transactions_state.dart

Create an empty state widget for when users have no budgets. Include an illustration, helpful text explaining budget benefits, and a prominent call-to-action button to create their first budget. Add tips about budget management and links to help documentation. Use Material 3 design and follow the same patterns as `/Users/<USER>/development/budapp/lib/features/accounts/presentation/widgets/empty_accounts_state.dart` and `/Users/<USER>/development/budapp/lib/features/transactions/presentation/widgets/empty_transactions_state.dart`. Include proper localization support.

### lib/l10n/app_en.arb(MODIFY)

Add comprehensive localization strings for budget editing and deletion features. Include strings for: budget editing form labels (`editBudget`, `budgetName`, `targetAmount`, `budgetPeriod`, `alertSettings`), validation messages (`budgetNameRequired`, `invalidTargetAmount`, `invalidDateRange`), action buttons (`saveBudget`, `deleteBudget`, `cancelEdit`), confirmation dialogs (`confirmBudgetDeletion`, `budgetDeletionWarning`), success messages (`budgetUpdatedSuccessfully`, `budgetDeletedSuccessfully`), and error messages (`budgetUpdateFailed`, `budgetDeletionFailed`). Follow the existing localization patterns in the file and ensure all strings have proper descriptions.

### lib/routing/app_router.dart(MODIFY)

Add routing configuration for budget-related screens. Include routes for `/budgets` (list), `/budgets/create`, `/budgets/:id` (detail), and `/budgets/:id/edit`. Ensure proper parameter passing for budget IDs and navigation flow. Follow the existing routing patterns for accounts and transactions in the file. Add route guards if needed for authentication and add proper route transitions.

### firestore.rules(MODIFY)

Add comprehensive Firestore Security Rules for the budgets collection at `users/{userId}/budgets/{budgetId}`. Include rules for read access (authenticated users can read their own budgets), write access (authenticated users can create/update their own budgets), and validation rules for budget data structure. Add validation for required fields, data types, and business logic constraints. Include rules to prevent hard deletion if using soft deletion approach. Add validation for budget limits based on user subscription tier. Follow the existing security rule patterns for accounts and transactions in the file.

### test/features/budgets/budget_editing_test.dart(NEW)

References: 

- test/features/accounts/account_edit_screen_test.dart
- test/features/transactions/transaction_form_test.dart

Create comprehensive tests for budget editing functionality. Include unit tests for budget validation, repository update operations, and provider state management. Add widget tests for the budget edit screen, form validation, and user interactions. Include integration tests for end-to-end budget editing flow with Firestore operations. Test error handling, loading states, and success scenarios. Follow the testing patterns from `/Users/<USER>/development/budapp/test/features/accounts/account_edit_screen_test.dart` and `/Users/<USER>/development/budapp/test/features/transactions/transaction_form_test.dart`. Use Firebase Emulator for integration tests.

### test/features/budgets/budget_deletion_test.dart(NEW)

Create comprehensive tests for budget deletion functionality. Include unit tests for soft deletion logic, repository operations, and provider state updates. Add widget tests for deletion confirmation dialogs and UI feedback. Include integration tests for end-to-end deletion flow with Firestore operations. Test both soft deletion (status update) and hard deletion scenarios. Verify that deleted budgets are properly removed from UI lists and that historical data is preserved. Follow the testing patterns from existing deletion tests in the project and use Firebase Emulator for integration tests.

### test/data/repositories/budget_repository_test.dart(NEW)

References: 

- test/data/repositories/account_repository_test.dart
- test/data/repositories/transaction_repository_test.dart

Create comprehensive tests for the budget repository implementation. Include tests for all CRUD operations: `createBudget()`, `updateBudget()`, `updateBudgetStatus()`, `deleteBudget()`, and query methods. Test Firestore integration, error handling, and data serialization/deserialization. Include tests for budget progress calculation and real-time listeners. Test validation methods and conflict checking. Follow the testing patterns from `/Users/<USER>/development/budapp/test/data/repositories/account_repository_test.dart` and `/Users/<USER>/development/budapp/test/data/repositories/transaction_repository_test.dart`. Use Firebase Emulator for all Firestore operations.

### test/helpers/mock_data_factory.dart(MODIFY)

Add comprehensive mock data factory methods for budget testing. Include `createBudget()`, `createMultipleBudgets()`, `createBudgetProgress()`, and `createBudgetWithProgress()` methods. Add helper methods for creating budgets with different statuses, periods, and categories. Include methods for creating test scenarios like over-budget situations, completed budgets, and overlapping budget periods. Follow the existing patterns in the file for other data types and ensure proper test data consistency.