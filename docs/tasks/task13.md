I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end. Make sure you fix all the linting, compilation or validation issues after successful implementation of the plan.

### Observations

I analyzed Task 13 "Budget Creation and Progress Tracking" which implements monthly budgets with visual progress indicators using client-side Flutter with Firestore integration. The task has 10 subtasks covering database schema, repository implementation, UI components, transaction aggregation, progress calculation, hierarchy management, overlap prevention, and security rules. The project has solid foundations with authentication, accounts, categories, and transactions already complete, following established patterns with Riverpod state management, Repository pattern, and comprehensive testing.

### Approach

This implementation follows the established BudApp architecture patterns using Riverpod for state management, Repository pattern for data access, and Firestore for backend storage. The approach builds incrementally through the 10 subtasks, starting with data models and repository layer, then implementing UI components, business logic for progress calculation and hierarchy management, and finally security rules and comprehensive testing. Each step leverages existing patterns from completed features like transactions and categories.

### Reasoning

I analyzed the task structure from the tasks.json file which contains detailed subtask definitions and dependencies. I reviewed the memory bank files to understand the current project status, technical context, and established patterns. I examined the PRD to understand the budget requirements including hierarchy management and overlap prevention. The project structure shows a feature-based organization with established patterns for data models, repositories, providers, and UI components that can be followed for budget implementation.

## Mermaid Diagram

sequenceDiagram
    participant User
    participant UI as Budget UI
    participant Provider as Budget Provider
    participant Repository as Budget Repository
    participant Progress as Progress Service
    participant Firestore
    participant Transaction as Transaction Repository

    User->>UI: Create Budget
    UI->>Provider: createBudget(budgetData)
    Provider->>Repository: createBudget(budget)
    Repository->>Firestore: Save budget document
    Firestore-->>Repository: Success
    Repository-->>Provider: Budget created
    Provider-->>UI: Update budget list
    UI-->>User: Show success message

    User->>UI: View Budget Progress
    UI->>Provider: watchBudgetProgress(budgetId)
    Provider->>Repository: getBudgetById(budgetId)
    Provider->>Transaction: watchTransactionsByMonthAndCategories()
    Transaction-->>Provider: Transaction stream
    Provider->>Progress: calculateBudgetProgress(budget, transactions)
    Progress-->>Provider: BudgetProgress
    Provider-->>UI: Progress data
    UI-->>User: Display progress bar & status

    User->>UI: Add Transaction
    Note over UI,Firestore: Transaction creation triggers budget update
    UI->>Provider: Real-time transaction stream
    Provider->>Progress: Recalculate progress
    Progress-->>Provider: Updated progress
    Provider-->>UI: Updated progress display
    UI-->>User: Live progress update

## Proposed File Changes

### lib/data/models/budget.dart(NEW)

References: 

- lib/data/models/account.dart
- lib/data/models/transaction.dart

Create the Budget data model using Freezed and json_serializable following the established pattern from `account.dart` and `transaction.dart`. Include fields: id (String), name (String), type (String, default 'expense'), amount (double), currency (String), period (String, default 'monthly'), categoryId (String?, nullable for overall budgets), parentBudgetId (String?, nullable for hierarchy), isActive (bool, default true), schemaVersion (int, default 1), createdAt (DateTime), updatedAt (DateTime). Add validation methods for amount > 0, valid period values, and proper hierarchy relationships. Include copyWith, toJson, fromJson methods via code generation.

### lib/data/models/budget_progress.dart(NEW)

References: 

- config/design_tokens.dart

Create a simple data class for budget progress tracking. Include fields: budgetId (String), spent (double), remaining (double), percentage (double), statusColor (Color from Material 3 design tokens), isOverBudget (bool). Add computed properties for progress status (on track, warning, over budget) and helper methods for color determination based on percentage thresholds (green < 75%, orange 75-100%, red > 100%). This class will be used by UI components for progress visualization.

### lib/data/repositories/interfaces/budget_repository.dart(MODIFY)

References: 

- lib/data/repositories/interfaces/account_repository.dart
- lib/data/repositories/interfaces/transaction_repository.dart(MODIFY)

Create the BudgetRepository interface following the established pattern from `account_repository.dart` and `transaction_repository.dart`. Define methods: watchBudgets() returning Stream<List<Budget>>, getBudgetById(String id) returning Future<Budget?>, createBudget(Budget budget) returning Future<void>, updateBudget(Budget budget) returning Future<void>, deleteBudget(String id) returning Future<void> (soft delete), watchBudgetsByMonth(DateTime month) returning Stream<List<Budget>>, and validateBudgetOverlap(Budget budget) returning Future<bool>. Include comprehensive documentation for each method explaining the expected behavior and error conditions.

### lib/data/repositories/implementations/budget_repository_impl.dart(NEW)

References: 

- lib/data/repositories/implementations/account_repository_impl.dart
- lib/data/repositories/implementations/transaction_repository_impl.dart(MODIFY)

Implement the BudgetRepository interface using Firestore following patterns from `account_repository_impl.dart` and `transaction_repository_impl.dart`. Use collection path `users/{userId}/budgets`. Implement all CRUD operations with proper error handling, data validation, and Firestore transactions where needed. Add client-side validation for budget overlap prevention by querying existing budgets for the same category and period. Implement soft delete by setting isActive to false. Include comprehensive error handling with custom exceptions and logging. Use Firestore batch operations for atomic updates when needed.

### lib/data/repositories/interfaces/repositories.dart(MODIFY)

Add export for the new BudgetRepository interface to maintain the centralized repository exports pattern established in this file. Add `export 'budget_repository.dart';` to make the interface available throughout the application.

### lib/data/repositories/implementations/transaction_repository_impl.dart(MODIFY)

Extend the existing TransactionRepository implementation to add a new method `watchTransactionsByMonthAndCategories(DateTime month, List<String>? categoryIds)` that returns a Stream<List<Transaction>>. This method should filter transactions by the specified month (using month start/end boundaries) and optionally by category IDs (including descendant categories for hierarchy support). Use Firestore compound queries with proper indexing. This method will be used by budget progress calculation logic to aggregate spending for specific budgets.

### lib/data/repositories/interfaces/transaction_repository.dart(MODIFY)

Add the new method signature `Stream<List<Transaction>> watchTransactionsByMonthAndCategories(DateTime month, List<String>? categoryIds)` to the TransactionRepository interface to support budget progress calculation. Include comprehensive documentation explaining the month filtering behavior (calendar month boundaries in user timezone) and category filtering (null means all categories, non-null filters to specific categories including descendants).

### lib/features/budgets/services/budget_validators.dart(NEW)

References: 

- lib/features/accounts/services/account_validators.dart
- lib/features/categories/services/category_validators.dart

Create budget validation service following the pattern from `account_validators.dart` and `category_validators.dart`. Include validation methods: validateBudgetName(String name), validateBudgetAmount(double amount), validateBudgetPeriod(String period), validateBudgetHierarchy(String? parentBudgetId, String? categoryId), and validateBudgetOverlap(Budget budget, List<Budget> existingBudgets). Return validation results with user-friendly error messages. Include business logic for preventing overlapping budgets (same category + period) and invalid hierarchy relationships (parent budget must exist and be for parent category).

### lib/features/budgets/services/budget_progress_service.dart(NEW)

References: 

- config/design_tokens.dart

Create service for calculating budget progress by aggregating transaction data. Include method `calculateBudgetProgress(Budget budget, List<Transaction> transactions, List<Category> categories)` that returns BudgetProgress. Implement logic to: filter transactions by budget's category and descendants (for hierarchy roll-up), sum transaction amounts for the budget period, calculate remaining amount and percentage, determine status color based on thresholds. Handle different budget types (expense vs income). Include helper methods for category hierarchy traversal and month boundary calculations. Use design tokens for status colors.

### lib/features/budgets/providers/budget_providers.dart(MODIFY)

References: 

- lib/features/accounts/providers/account_providers.dart
- lib/features/transactions/providers/transaction_providers.dart

Implement comprehensive Riverpod providers for budget management following patterns from `account_providers.dart` and `transaction_providers.dart`. Create BudgetsController extending AsyncNotifier<List<Budget>> with methods for CRUD operations. Add budgetProgressProvider using AsyncNotifierProvider.family<BudgetProgress, String> for individual budget progress tracking. Include budgetsByMonthProvider for filtering budgets by month. Integrate with BudgetRepository and BudgetProgressService. Add proper error handling and loading states. Use dependency injection for repository access.

### lib/providers/repository_providers.dart(MODIFY)

Add the BudgetRepository provider to the centralized repository providers file following the established pattern. Add `final budgetRepositoryProvider = Provider<BudgetRepository>((ref) => BudgetRepositoryImpl(firestore: ref.watch(firestoreProvider)));` to make the budget repository available for dependency injection throughout the application. This maintains consistency with other repository providers in the file.

### lib/features/budgets/presentation/screens/budgets_list_screen.dart(NEW)

References: 

- lib/features/accounts/presentation/screens/accounts_list_screen.dart
- lib/features/transactions/presentation/screens/transactions_list_screen.dart

Create the main budgets list screen following the pattern from `accounts_list_screen.dart` and `transactions_list_screen.dart`. Display list of active budgets with progress indicators, budget names, amounts, and visual progress bars. Include floating action button for creating new budgets. Add month selector for filtering budgets by period. Implement pull-to-refresh and empty state handling. Use Material 3 design with proper loading states and error handling. Include navigation to budget detail/edit screens and budget creation flow.

### lib/features/budgets/presentation/screens/budget_create_screen.dart(NEW)

References: 

- lib/features/accounts/presentation/screens/account_create_screen.dart
- lib/features/transactions/presentation/screens/transaction_create_screen.dart

**REMOVED IN PHASE 2**: Budget creation functionality has been removed from the application. Budgets are now managed through editing existing budgets only, following the edit-only budget management approach.

### lib/features/budgets/presentation/screens/budget_edit_screen.dart(NEW)

References: 

- lib/features/accounts/presentation/screens/account_edit_screen.dart
- lib/features/transactions/presentation/screens/transaction_edit_screen.dart

Create budget editing screen following the pattern from `account_edit_screen.dart` and `transaction_edit_screen.dart`. Pre-populate form fields with existing budget data. Allow editing of name, amount, and category (with validation for hierarchy constraints). Include delete button with confirmation dialog for soft deletion. Implement real-time progress updates when amount changes. Add proper validation and error handling. Navigate back to budget list on successful update or deletion with appropriate feedback messages.

### lib/features/budgets/presentation/widgets/budget_card.dart(NEW)

References: 

- lib/features/accounts/presentation/widgets/account_card.dart
- lib/features/transactions/presentation/widgets/transaction_card.dart

Create budget card widget following the pattern from `account_card.dart` and `transaction_card.dart`. Display budget name, amount, category, and visual progress indicator. Include progress bar with color coding (green/orange/red) based on spending percentage. Show spent amount, remaining amount, and percentage. Add tap gesture for navigation to budget detail/edit screen. Include overflow menu for edit/delete actions. Use Material 3 design with proper spacing and typography. Handle loading and error states for progress data.

### lib/features/budgets/presentation/widgets/budget_progress_bar.dart(NEW)

References: 

- config/design_tokens.dart

Create reusable budget progress bar widget for visual progress indication. Accept BudgetProgress object as input and render horizontal progress bar with appropriate color coding. Include percentage text overlay and spent/remaining amount labels. Handle edge cases like over-budget scenarios (progress > 100%) with different visual treatment. Use Material 3 design tokens for colors and spacing. Make widget responsive and accessible with proper semantics for screen readers.

### lib/features/budgets/presentation/widgets/budget_form.dart(NEW)

References: 

- lib/features/transactions/presentation/widgets/transaction_form.dart

Create reusable budget form widget following the pattern from `transaction_form.dart`. Include form fields for budget name, amount, category selection, and parent budget selection. Implement client-side validation with real-time feedback. Add category selector that integrates with existing category system and supports hierarchy selection. Include amount input with currency formatting. Handle form state management with proper validation and error display. Make form reusable for both create and edit scenarios.

### lib/features/budgets/presentation/widgets/empty_budgets_state.dart(NEW)

References: 

- lib/features/accounts/presentation/widgets/empty_accounts_state.dart
- lib/features/transactions/presentation/widgets/empty_transactions_state.dart

Create empty state widget for when no budgets exist following the pattern from `empty_accounts_state.dart` and `empty_transactions_state.dart`. Display helpful illustration, explanatory text about budget benefits, and call-to-action button to create first budget. Use Material 3 design with proper spacing and typography. Include different messages for filtered views (e.g., no budgets for selected month) vs completely empty state. Make widget engaging and educational for new users.

### lib/routing/app_router.dart(MODIFY)

Add budget-related routes to the existing router configuration following the established pattern. Add routes for `/budgets` (BudgetsListScreen), `/budgets/create` (BudgetCreateScreen), and `/budgets/:id/edit` (BudgetEditScreen). Ensure proper route parameters and navigation guards. Integrate with the existing shell route structure and maintain consistency with other feature routes. Add proper route transitions and back navigation handling.

### lib/widgets/navigation/app_navigation_drawer.dart(MODIFY)

Add budget navigation item to the existing navigation drawer following the established pattern. Add budget icon and label with navigation to `/budgets` route. Position appropriately in the navigation hierarchy (likely after transactions, before reports). Ensure proper highlighting for active route and maintain Material 3 design consistency with other navigation items.

### firestore.rules(MODIFY)

Add comprehensive Firestore Security Rules for the budgets collection following the established patterns for user data isolation and validation. Add rules for `/databases/(default)/documents/users/{uid}/budgets/{budgetId}` that: allow read/write only for authenticated users who own the data, validate budget document structure (required fields, data types, value constraints), enforce business rules (amount > 0, valid period values), prevent hard deletion (only allow isActive updates for soft delete), and validate hierarchy relationships where possible. Include proper error messages for rule violations.

### firestore.indexes.json(MODIFY)

Add composite indexes for efficient budget queries following the established pattern. Add indexes for: `users/{userId}/budgets` collection with fields [categoryId, period, isActive] for overlap prevention queries, [period, isActive] for monthly filtering, and [parentBudgetId, isActive] for hierarchy queries. Ensure indexes support the query patterns used by the BudgetRepository implementation for optimal performance.

### lib/l10n/app_en.arb(MODIFY)

Add comprehensive localization strings for budget features following the established pattern. Include strings for: budget creation/editing forms (labels, placeholders, validation messages), budget list screen (titles, empty states, actions), progress indicators (status messages, percentages), error messages (overlap prevention, hierarchy validation), success messages (creation, update, deletion), and navigation labels. Ensure consistent terminology and user-friendly language throughout.

### test/data/models/budget_test.dart(NEW)

References: 

- test/helpers/mock_data_factory.dart(MODIFY)

Create comprehensive unit tests for the Budget model following the pattern from `account_test.dart` and `transaction_test.dart`. Test JSON serialization/deserialization, model validation, copyWith functionality, equality comparisons, and edge cases. Include tests for hierarchy validation, overlap detection logic, and business rule enforcement. Verify proper handling of nullable fields and default values. Use the established test helper patterns and mock data factory.

### test/data/repositories/budget_repository_test.dart(NEW)

References: 

- test/data/repositories/account_repository_test.dart
- test/helpers/firebase_test_helper.dart

Create comprehensive unit tests for the BudgetRepository implementation following the pattern from `account_repository_test.dart` and `transaction_repository_test.dart`. Test all CRUD operations, stream subscriptions, error handling, validation logic, and Firestore integration. Include tests for overlap prevention, hierarchy validation, soft deletion, and query filtering. Use Firebase Emulator for integration testing and mock Firestore for unit testing. Verify proper error propagation and data consistency.

### test/features/budgets/services/budget_progress_service_test.dart(NEW)

References: 

- test/helpers/mock_data_factory.dart(MODIFY)

Create unit tests for the BudgetProgressService following established testing patterns. Test progress calculation logic with various scenarios: under budget, on budget, over budget, different transaction types, category hierarchy roll-up, and edge cases (no transactions, negative amounts). Verify color determination logic and percentage calculations. Test month boundary handling and timezone considerations. Use mock data factory for test data generation.

### test/features/budgets/presentation/screens/budgets_list_screen_test.dart(NEW)

References: 

- test/helpers/test_wrapper.dart
- test/helpers/mock_providers.dart

Create widget tests for the BudgetsListScreen following the pattern from `accounts_list_screen_test.dart` and `transactions_list_screen_test.dart`. Test widget rendering with different data states (loading, empty, populated, error), user interactions (tap to edit, create new budget, month selection), navigation behavior, and progress indicator display. Use test wrapper and mock providers for isolated testing. Verify proper Material 3 design implementation and accessibility features.

### test/features/budgets/presentation/screens/budget_create_screen_test.dart(NEW)

References: 

- test/helpers/test_wrapper.dart
- test/helpers/mock_providers.dart

Create widget tests for the BudgetCreateScreen following established patterns. Test form validation, user input handling, category selection, hierarchy selection, save/cancel actions, error display, and navigation behavior. Verify client-side validation triggers and error message display. Test successful creation flow and error handling scenarios. Use mock providers and test wrapper for isolated testing.

### test/features/budgets/presentation/widgets/budget_card_test.dart(NEW)

References: 

- test/helpers/test_wrapper.dart

Create widget tests for the BudgetCard component following the pattern from `account_card_test.dart` and `transaction_card_test.dart`. Test widget rendering with different budget states, progress indicator display, color coding, tap interactions, overflow menu actions, and accessibility features. Verify proper Material 3 design implementation and responsive layout. Test edge cases like over-budget scenarios and loading states.

### test/integration/budget_integration_test.dart(NEW)

References: 

- test/integration/account_repository_integration_test.dart
- test/helpers/firebase_test_helper.dart

Create comprehensive integration tests for budget functionality following the pattern from existing integration tests. Test end-to-end workflows: create budget → add transactions → verify progress updates, budget hierarchy creation and roll-up calculations, overlap prevention validation, budget editing and deletion flows. Use Firebase Emulator for realistic testing environment. Verify real-time updates and data consistency across the application. Test error scenarios and recovery flows.

### test/unit/firestore_security_rules_test.dart(MODIFY)

Extend the existing Firestore Security Rules tests to include comprehensive testing for budget collection rules. Add test cases for: authenticated user access to own budgets, prevention of unauthorized access to other users' budgets, validation of budget document structure and required fields, enforcement of business rules (amount > 0, valid periods), soft deletion validation (isActive field updates), and hierarchy relationship validation where possible. Use Firebase Emulator for rule testing.

### test/helpers/mock_data_factory.dart(MODIFY)

Extend the existing mock data factory to include budget-related test data generation following the established pattern. Add methods: `createMockBudget()`, `createMockBudgetProgress()`, `createMockBudgetList()`, and `createMockBudgetHierarchy()`. Include various budget scenarios for testing: different amounts, categories, hierarchy relationships, and progress states. Ensure generated data is realistic and covers edge cases for comprehensive testing.