I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end. Make sure you fix all the linting, compilation or validation issues after successful implementation of the plan.

### Observations

Based on my exploration, I found that:

1. **Goal infrastructure is partially prepared**: Remote Config already includes `maxGoalsFree` and `maxGoalsPremium` limits, localization strings exist for goals, and Firestore Security Rules have placeholder goal validation functions.

2. **Architecture is solid**: Tasks 29 & 30 are complete, so Riverpod, Repository pattern, freezed models, go_router, and testing infrastructure are fully established.

3. **Similar patterns exist**: Budget management provides an excellent reference for denormalized field updates via `budget_progress_service.dart`, and other features show established patterns for CRUD operations, UI components, and validation.

4. **Missing components**: No goal data models exist yet, no goal routes in go_router, and only placeholder providers. The security rules have goal validation functions defined but not fully implemented.

5. **Test infrastructure ready**: Mock data factory has placeholders for goals, and security rules tests expect goal validation functions.

### Approach

This implementation will follow the established architectural patterns in the codebase, creating a complete goal tracking feature with manual contributions. The approach leverages:

1. **Freezed data models** for `Goal` and `GoalContribution` with immutability and JSON serialization
2. **Repository pattern** with abstract interfaces and concrete Firestore implementations  
3. **Riverpod state management** with AsyncNotifier for form handling and providers for data access
4. **Client-side denormalized updates** using Firestore batch operations (similar to budget progress service)
5. **go_router navigation** with proper route definitions and parameter passing
6. **Comprehensive validation** both client-side and via Firestore Security Rules
7. **Established UI patterns** following existing form components, progress indicators, and list screens

The implementation will be done in logical phases: data models → repositories → UI components → business logic → security rules → testing.

### Reasoning

I analyzed the task requirements and explored the codebase to understand the current state. I found that goal infrastructure is partially prepared with Remote Config limits and localization strings already in place. I examined the architecture and confirmed that Riverpod, Repository pattern, and other foundations are complete. I studied similar features like budgets to understand established patterns for denormalized field updates and CRUD operations. I checked the security rules and found placeholder goal validation functions. I also verified that test infrastructure and mock data factories are ready for extension.

## Mermaid Diagram

sequenceDiagram
    participant User
    participant UI as Goal UI Components
    participant Provider as Goal Providers
    participant Service as Goal Progress Service
    participant Repo as Goal Repository
    participant ContribRepo as Contribution Repository
    participant Firestore as Firestore Database

    Note over User, Firestore: Goal Creation Flow
    User->>UI: Create new goal
    UI->>Provider: Submit goal form
    Provider->>Repo: createGoal(goal)
    Repo->>Firestore: Save goal document
    Firestore-->>Repo: Success
    Repo-->>Provider: Goal created
    Provider-->>UI: Update UI state
    UI-->>User: Show success & navigate

    Note over User, Firestore: Contribution Flow with Denormalized Updates
    User->>UI: Add contribution
    UI->>Provider: Submit contribution
    Provider->>ContribRepo: createContribution(contribution)
    ContribRepo->>Service: updateCurrentAmount(goal, delta)
    Service->>Firestore: Batch write (contribution + goal update)
    Note over Service, Firestore: Atomic operation updates both<br/>contribution subcollection and<br/>goal.currentAmount field
    Firestore-->>Service: Batch success
    Service-->>ContribRepo: Update complete
    ContribRepo-->>Provider: Contribution added
    Provider-->>UI: Refresh goal progress
    UI-->>User: Show updated progress

    Note over User, Firestore: Goal Progress Visualization
    User->>UI: View goals list
    UI->>Provider: Watch goals stream
    Provider->>Repo: watchGoals(userId)
    Repo->>Firestore: Real-time listener
    Firestore-->>Repo: Goals data
    Repo-->>Provider: Goals stream
    Provider->>UI: Calculate progress %
    UI-->>User: Display progress bars

## Proposed File Changes

### lib/data/models/goal.dart(NEW)

References: 

- lib/data/models/budget.dart
- lib/data/models/account.dart

Create a new freezed data model for financial goals following the established pattern from `budget.dart` and `account.dart`. Include fields: `id`, `userId`, `name`, `description`, `targetAmount`, `currentAmount` (denormalized), `currency`, `targetDate`, `isCompleted`, `color`, `icon`, `schemaVersion`, `createdAt`, `updatedAt`. Use `@freezed` annotation with `json_serializable` for automatic JSON conversion. Include validation methods and factory constructors. Add `copyWith` functionality for immutable updates.

### lib/data/models/goal_contribution.dart(NEW)

References: 

- lib/data/models/transaction.dart

Create a new freezed data model for goal contributions. Include fields: `id`, `goalId`, `amount`, `contributionDate`, `description`, `createdAt`, `updatedAt`. Use `@freezed` annotation with `json_serializable` for automatic JSON conversion. Follow the same pattern as other models in the codebase for consistency.

### lib/data/repositories/interfaces/goal_repository.dart(NEW)

References: 

- lib/data/repositories/interfaces/budget_repository.dart

Create abstract interface for goal repository following the pattern from `budget_repository.dart`. Define methods: `watchGoals(String userId)`, `getGoal(String userId, String goalId)`, `createGoal(Goal goal)`, `updateGoal(Goal goal)`, `deleteGoal(String userId, String goalId)`. Include error handling and return types using `AsyncValue` where appropriate.

### lib/data/repositories/interfaces/goal_contribution_repository.dart(NEW)

References: 

- lib/data/repositories/interfaces/transaction_repository.dart

Create abstract interface for goal contribution repository. Define methods: `watchContributions(String userId, String goalId)`, `getContribution(String userId, String goalId, String contributionId)`, `createContribution(GoalContribution contribution)`, `updateContribution(GoalContribution contribution)`, `deleteContribution(String userId, String goalId, String contributionId)`. Follow the same pattern as other repository interfaces.

### lib/data/repositories/implementations/goal_repository_impl.dart(NEW)

References: 

- lib/data/repositories/implementations/budget_repository_impl.dart
- lib/data/repositories/implementations/account_repository_impl.dart

Implement concrete goal repository using Firestore operations. Follow the pattern from `budget_repository_impl.dart`. Implement all interface methods with proper error handling, data serialization/deserialization, and Firestore collection path `users/{userId}/goals`. Include proper logging and exception handling. Use batch operations where appropriate for atomic updates.

### lib/data/repositories/implementations/goal_contribution_repository_impl.dart(NEW)

References: 

- lib/data/repositories/implementations/transaction_repository_impl.dart

Implement concrete goal contribution repository using Firestore operations. Use subcollection path `users/{userId}/goals/{goalId}/contributions`. Implement all interface methods with proper error handling and data serialization. Include methods to trigger denormalized `currentAmount` updates on the parent goal document using batch operations. Follow the pattern from `transaction_repository_impl.dart` for subcollection operations.

### lib/features/goals/services/goal_progress_service.dart(NEW)

References: 

- lib/features/budgets/services/budget_progress_service.dart

Create a service for managing denormalized `currentAmount` updates on goal documents, similar to `budget_progress_service.dart`. Include methods: `updateCurrentAmount(Goal goal, num delta)`, `recalculateCurrentAmount(Goal goal)`, `calculateProgress(Goal goal)`. Use Firestore batch operations to atomically update both contribution and goal documents. Include error handling and validation to ensure data consistency.

### lib/features/goals/providers/goal_providers.dart(MODIFY)

References: 

- lib/features/budgets/providers/budget_providers.dart

Replace the placeholder content with actual Riverpod providers for goal management. Create providers: `goalRepositoryProvider`, `goalContributionRepositoryProvider`, `goalProgressServiceProvider`, `goalsStreamProvider(String userId)`, `goalProvider(String userId, String goalId)`. Follow the pattern from `budget_providers.dart` for consistency and proper dependency injection.

### lib/features/goals/providers/goal_form_providers.dart(NEW)

References: 

- lib/features/transactions/presentation/providers/transaction_form_providers.dart

Create form-specific providers for goal creation and editing using AsyncNotifier pattern. Include providers: `goalFormControllerProvider`, `contributionFormControllerProvider`. Handle form state, validation, and submission logic. Follow the pattern from `transaction_form_providers.dart` for form handling and error management.

### lib/features/goals/services/goal_validators.dart(NEW)

References: 

- lib/features/budgets/services/budget_validators.dart
- lib/features/accounts/services/account_validators.dart

Create validation utilities for goal and contribution forms. Include methods: `validateGoalName(String name)`, `validateTargetAmount(double amount)`, `validateTargetDate(DateTime date)`, `validateContributionAmount(double amount, Goal goal)`. Follow the pattern from `budget_validators.dart` and `account_validators.dart` for consistent validation logic and error messages.

### lib/features/goals/presentation/screens/goals_list_screen.dart(NEW)

References: 

- lib/features/budgets/presentation/screens/budgets_list_screen.dart

Create the main goals list screen following the pattern from `budgets_list_screen.dart`. Include goal cards showing progress, target amount, current amount, and completion status. Add floating action button for creating new goals. Implement pull-to-refresh and empty state handling. Use Riverpod to watch goals stream and handle loading/error states.

### lib/features/goals/presentation/screens/goal_create_screen.dart(NEW)

References: 

- lib/features/budgets/presentation/screens/budget_create_screen.dart
- lib/features/accounts/presentation/screens/account_create_screen.dart

Create goal creation screen with form fields for name, description, target amount, target date, color, and icon selection. Follow the pattern from `budget_create_screen.dart`. Include validation, error handling, and success navigation. Use AsyncNotifier for form state management and proper loading indicators during submission.

### lib/features/goals/presentation/screens/goal_edit_screen.dart(NEW)

References: 

- lib/features/budgets/presentation/screens/budget_edit_screen.dart

Create goal editing screen similar to `budget_edit_screen.dart`. Pre-populate form fields with existing goal data. Include validation and proper error handling. Allow editing of all goal properties except `currentAmount` which is managed through contributions. Include delete functionality with confirmation dialog.

### lib/features/goals/presentation/screens/goal_detail_screen.dart(NEW)

References: 

- lib/features/accounts/presentation/screens/account_detail_screen.dart

Create goal detail screen showing goal information, progress visualization, and contribution history. Include a floating action button for adding contributions. Show progress bar, percentage completion, remaining amount, and target date. Include list of recent contributions with edit/delete options. Follow the pattern from `account_detail_screen.dart` for layout and navigation.

### lib/features/goals/presentation/screens/contribution_create_screen.dart(COMPLETED)

References:

- lib/features/transactions/presentation/screens/transaction_create_screen.dart

✅ **COMPLETED**: Created contribution entry screen with form fields for amount, date, and description. Implemented using generic form system with proper validation for positive amounts and reasonable limits. Includes proper error handling and navigation. Uses GoalContributionFormConfig for form configuration and integrates with Riverpod providers for state management.

### lib/features/goals/presentation/widgets/goal_card.dart(NEW)

References: 

- lib/features/budgets/presentation/widgets/budget_card.dart
- lib/features/accounts/presentation/widgets/account_card.dart

Create goal card widget for displaying goal summary in list view. Include goal name, progress bar, current/target amounts, completion percentage, and target date. Add visual indicators for completed goals and near-deadline goals. Follow the design pattern from `budget_card.dart` and `account_card.dart` for consistency.

### lib/features/goals/presentation/widgets/goal_progress_bar.dart(NEW)

References: 

- lib/features/budgets/presentation/widgets/budget_progress_bar.dart

Create progress bar widget for visualizing goal completion. Include percentage display, color coding for different completion levels, and animation support. Follow the pattern from `budget_progress_bar.dart` but adapt for goal-specific requirements like completion status and target dates.

### lib/features/goals/presentation/widgets/goal_form.dart(NEW)

References: 

- lib/features/budgets/presentation/widgets/budget_form.dart
- lib/features/accounts/presentation/widgets/account_color_selector.dart

Create reusable goal form widget with fields for name, description, target amount, target date, color, and icon. Include validation, error display, and proper form state management. Follow the pattern from `budget_form.dart` and include color/icon selectors similar to account and category forms.

### lib/features/goals/presentation/widgets/contribution_card.dart(COMPLETED)

References:

- lib/features/transactions/presentation/widgets/transaction_card.dart

✅ **COMPLETED**: Created contribution card widget for displaying individual contributions. Shows contribution amount with currency formatting, smart date formatting (Today, Yesterday, This Week), optional description, and edit/delete menu actions. Includes proper Material 3 styling, color theming, and accessibility features. Integrates with contribution providers for real-time updates.

### lib/features/goals/presentation/widgets/empty_goals_state.dart(NEW)

References: 

- lib/features/budgets/presentation/widgets/empty_budgets_state.dart
- lib/features/accounts/presentation/widgets/empty_accounts_state.dart

Create empty state widget for when user has no goals. Include motivational message, illustration, and call-to-action button to create first goal. Follow the pattern from `empty_budgets_state.dart` and other empty state widgets in the app.

### lib/routing/app_router.dart(MODIFY)

Add goal-related routes to the `AppRoutes` class and router configuration. Include routes: `/goals`, `/goals/create`, `/goals/:id`, `/goals/:id/edit`, `/goals/:id/contributions/create`. Add the routes to the ShellRoute configuration following the pattern of other feature routes. Import the new goal screen widgets and configure proper parameter passing.

### firestore.rules(MODIFY)

Complete the implementation of goal validation functions that are already defined but not fully implemented. Update `isValidGoal` function to validate all required fields including `targetAmountCents`, `currencyCode`, `targetDate`, and optional fields. Add validation for goal contributions in a new subcollection rule `match /users/{userId}/goals/{goalId}/contributions/{contributionId}` with proper CRUD permissions and validation functions. Ensure referential integrity between goals and contributions.

### lib/providers/repository_providers.dart(MODIFY)

Add goal and goal contribution repository providers to the central repository providers file. Include `goalRepositoryProvider` and `goalContributionRepositoryProvider` following the same pattern as existing repository providers. Ensure proper dependency injection and provider organization.

### test/helpers/mock_data_factory.dart(MODIFY)

Implement the placeholder goal creation methods that are currently commented out. Add `createGoal()` and `createGoalContribution()` methods following the pattern of other mock data creation methods. Include realistic test data with various goal states (in-progress, completed, near-deadline). Update the `createCompleteUserData()` method to include sample goals.

### test/features/goals/goal_repository_test.dart(NEW)

References: 

- test/data/repositories/account_repository_test.dart

Create comprehensive unit tests for goal repository implementation. Test all CRUD operations, error handling, data serialization/deserialization, and Firestore integration. Follow the pattern from `budget_repository_test.dart` and other repository tests. Include tests for edge cases and validation scenarios.

### test/features/goals/goal_contribution_repository_test.dart(NEW)

References: 

- test/data/repositories/transaction_repository_test.dart

Create unit tests for goal contribution repository implementation. Test CRUD operations, subcollection handling, and denormalized field updates. Include tests for batch operations and error scenarios. Follow the pattern from transaction repository tests for subcollection operations.

### test/features/goals/goal_progress_service_test.dart(NEW)

References: 

- lib/features/budgets/services/budget_progress_service.dart

Create unit tests for goal progress service. Test denormalized field update logic, progress calculations, and batch operation handling. Include tests for edge cases like negative contributions and goal completion scenarios. Follow the pattern from budget progress service tests.

### test/features/goals/presentation/goal_create_screen_test.dart(NEW)

References: 

- test/features/accounts/presentation/account_create_screen_test.dart

Create widget tests for goal creation screen. Test form validation, error handling, submission flow, and navigation. Include tests for all form fields and edge cases. Follow the pattern from account and budget creation screen tests.

### test/features/goals/presentation/goals_list_screen_test.dart(NEW)

References: 

- test/features/budgets/presentation/screens/budgets_list_screen.dart

Create widget tests for goals list screen. Test goal display, empty states, loading states, error handling, and navigation to create/detail screens. Include tests for pull-to-refresh functionality. Follow the pattern from other list screen tests.

### firebase/test/goal-security-rules.test.js(NEW)

References: 

- firebase/test/account-security-rules.test.js

Create Firebase Emulator tests for goal security rules. Test CRUD operations for goals and contributions, validation rules, user isolation, and referential integrity. Include tests for unauthorized access attempts and invalid data scenarios. Follow the pattern from existing security rule tests.

## Task 15.6 Completion Summary

### ✅ **Contribution Entry UI Implementation Complete**

**Date Completed**: 2025-01-16

**Implemented Components**:

1. **Form Configuration System**
   - `lib/widgets/forms/configs/goal_contribution_form_config.dart` - Complete form configuration with validation
   - Integrated with existing generic form system
   - Proper data mapping between form data and GoalContribution entities

2. **Riverpod Providers**
   - `lib/features/goals/providers/goal_contribution_providers.dart` - Comprehensive provider system
   - Real-time data streams and state management
   - CRUD operation providers with proper error handling

3. **UI Screens**
   - `lib/features/goals/presentation/screens/goal_contribution_create_screen.dart` - Contribution creation
   - `lib/features/goals/presentation/screens/goal_contribution_edit_screen.dart` - Contribution editing
   - `lib/features/goals/presentation/screens/goal_contributions_list_screen.dart` - Contribution listing

4. **UI Components**
   - `lib/features/goals/presentation/widgets/contribution_card.dart` - Material 3 styled contribution cards
   - Smart date formatting and currency display
   - Edit/delete menu actions with confirmation dialogs

5. **Navigation & Routing**
   - Added contribution routes to `lib/routing/app_router.dart`
   - Updated goal cards with "Add Contribution" and "View Contributions" actions
   - Proper parameter passing and navigation flow

**Quality Assurance**:
- ✅ All 779 tests passing
- ✅ Flutter analyze clean (fixed strict_raw_type warning)
- ✅ Code properly formatted
- ✅ Follows established BudApp patterns
- ✅ Material 3 design compliance

**Key Features**:
- Real-time contribution tracking with Riverpod streams
- Currency formatting integration
- Smart date validation and formatting
- Offline-first architecture support
- Comprehensive form validation
- Seamless integration with existing goal management

The contribution entry UI is now fully functional and ready for users to track their progress toward financial goals.

## Task 15.7 Completion Summary

### ✅ **Denormalized Goal Amount Updates Implementation Complete**

**Date Completed**: 2025-01-16

**Implemented Components**:

1. **Enhanced GoalContributionRepositoryImpl**
   - `lib/data/repositories/implementations/goal_contribution_repository_impl.dart` - Enhanced with atomic batch operations
   - Automatic goal `currentAmountCents` updates for all contribution operations
   - Proper error handling and validation throughout

2. **Atomic Batch Operations**
   - `createContribution()` - Uses FieldValue.increment() for atomic goal amount increments
   - `updateContribution()` - Calculates amount differences and applies incremental updates
   - `deleteContribution()` - Subtracts amounts only from active contributions

3. **Data Consistency Features**
   - Firestore batch writes ensuring atomic operations between contribution and goal documents
   - Edge case handling for inactive contributions and repeated operations
   - Comprehensive validation ensuring goal existence before operations

4. **Testing Infrastructure**
   - Added 5 new test cases specifically for denormalized amount updates
   - Tests cover creation, updates, deletion, accumulation, and edge cases
   - All 49 tests passing (44 existing + 5 new)

5. **Documentation Updates**
   - Updated `docs/GOAL_CONTRIBUTION_REPOSITORY.md` with implemented batch operation examples
   - Code examples showing actual implementation patterns

**Quality Assurance**:
- ✅ All 49 tests passing
- ✅ Flutter analyze clean (0 issues)
- ✅ Cascade-style batch operations following linting best practices
- ✅ Comprehensive edge case coverage
- ✅ Production-ready atomic operations

**Key Features**:
- Real-time goal progress tracking with automatic updates
- Bulletproof data consistency through atomic Firestore operations
- Smart handling of contribution state changes
- Optimized batch operations with conditional updates
- Comprehensive error handling and validation

**Technical Achievement**:
Successfully implemented client-side logic for denormalized `currentAmountCents` updates ensuring goal progress is always accurate and up-to-date. The implementation provides users with real-time feedback on their financial goal achievements while maintaining bulletproof data consistency through atomic Firestore operations.

The goal contribution system now provides complete end-to-end functionality from UI entry to automatic goal progress tracking.