# Unified Time-Period-Based Budget Management System

## Overview

The Unified Time-Period-Based Budget Management System provides comprehensive budget tracking, editing, and progress monitoring capabilities for BudApp. It features seamless integration with the global time period selector, powerful bulk operations, an intuitive selection mode UI for managing multiple budgets efficiently, and automatic transaction-budget integration for real-time budget updates.

## Features

### Core Functionality
- **Budget Editing**: Edit existing budget amounts, names, and settings through an intuitive edit mode interface with inline editing capabilities
- **Transaction-Budget Integration**: Automatic budget updates based on transaction activity with real-time progress tracking
- **Budget Hierarchy**: Support for parent-child budget relationships for detailed organization
- **Progress Tracking**: Real-time calculation of budget progress based on transaction data with automatic updates
- **Category Integration**: Link budgets to specific expense/income categories with transaction-based updates
- **Period Management**: Precise period-based budget management with `periodStart` field for exact period identification
- **Uniqueness Constraints**: Enforced one budget per category per time period at both client and database level
- **Fallback Logic**: Display previous period budgets when none exist for current period
- **Overlap Prevention**: Client-side validation to prevent conflicting budget periods
- **Soft Deletion**: Budgets are deactivated rather than permanently deleted

### Unified Time-Period Integration
- **Global Time Period Selector**: Seamless integration with the global time period selector component
- **Period-Based Management**: Budget operations filtered and organized by selected time periods with precise `periodStart` matching
- **Period-Specific Filtering**: Only budgets belonging to the selected period are displayed using exact period identification
- **Fallback Display**: When no budget exists for selected period, shows most recent budget from previous period with visual indicator
- **Consistent Navigation**: Unified time period experience across home, transactions, and budgets screens

### Bulk Operations System
- **Percentage Adjustments**: Bulk adjust budget amounts by percentage (increase/decrease)
- **Status Management**: Bulk activate or deactivate multiple budgets simultaneously
- **Batch Deletion**: Delete multiple budgets in a single operation with confirmation
- **Selection Mode**: Multi-select UI with visual feedback and selection count display
- **Atomic Operations**: All bulk operations are performed atomically for data consistency

### Edit Mode UI System
- **Inline Edit Mode**: Comprehensive inline editing functionality with toggle-based activation
- **Simultaneous Editing**: Edit both total budget and individual category budgets in single interface
- **Real-time Validation**: Live validation feedback during editing with error handling
- **State Management**: Persistent edit mode state with proper form handling

### Budget Types
- **Category Budgets**: Specific budgets for individual spending/income categories (categoryId != null)
- **Total Budgets**: Overall budgets for expenses and income (categoryId == null)
- **Expense Budgets**: Set spending limits for categories or overall expenses
- **Income Budgets**: Track expected income targets

### Total Budget System
- **Automatic Creation**: Total budgets are automatically created alongside category budgets during transaction processing
- **Independent Tracking**: Total expense and income budgets exist independently of category budgets
- **Real-time Updates**: Total budgets are automatically updated when transactions are created, modified, or deleted
- **Period-specific**: Each time period has its own set of total budgets
- **Aggregation Logic**: Total budgets track the sum of all spending/income within their type for the period
- **UI Integration**: Total budget cards are displayed prominently at the top of each budget tab

### Real-Time Amount Tracking
- **currentAmountCents Field**: Budgets now use the `currentAmountCents` field for real-time tracking
- **Automatic Updates**: The BudgetTransactionService automatically updates budget amounts when transactions change
- **Progress Calculation**: Budget progress is calculated using real-time amounts instead of querying transactions
- **Performance Optimization**: Eliminates the need to aggregate transactions for budget display

### Budget Period Management System
- **Precise Period Identification**: Each budget has a `periodStart` field that defines the exact period it belongs to
- **Uniqueness Enforcement**: Only one budget per category per time period is allowed, enforced at both client and database level
- **Period-Based Filtering**: Budgets are filtered by exact `periodStart` date matching for precise period management
- **Fallback Logic**: When no budget exists for the current period, displays the most recent budget from a previous period
- **Migration Support**: Existing budgets can be migrated to include `periodStart` field based on creation date and period type
- **Database Validation**: Firestore security rules enforce period-based uniqueness constraints and field validation

### Budget Periods
- **Monthly**: Budget cycles reset every month with `periodStart` set to first day of month (e.g., 2024-03-01)
- **Yearly**: Budget cycles reset annually with `periodStart` set to January 1st (e.g., 2024-01-01)

## Data Model

### Budget Schema (Schema Version 4)
```dart
class Budget {
  final String id;
  final String userId;
  final BudgetType type; // expense, income
  final int plannedAmountCents; // Target/budgeted amount in cents
  final int currentAmountCents; // Actual spent/received amount in cents
  final String currencyCode; // ISO 4217 currency code (e.g., "USD", "EUR")
  final BudgetPeriod period; // monthly, yearly
  final DateTime periodStart; // Explicit period start date (e.g., 2024-01-01 for January 2024)
  final String? categoryId; // Nullable for total budgets
  final String? parentBudgetId; // Nullable for hierarchy
  final bool isActive;
  final int schemaVersion; // Version 4 for periodStart field addition
  final DateTime createdAt;
  final DateTime updatedAt;
}
```

### Budget Progress
```dart
class BudgetProgress {
  final String budgetId;
  final double spent;
  final double remaining;
  final double percentage;
  final bool isOverBudget;
  final DateTime calculatedAt;
}
```

## Architecture

### Data Layer
- **Budget Model**: Freezed data class with JSON serialization
- **Budget Repository**: Interface and Firestore implementation
- **Budget Validation Service**: Client-side business rule validation

### Business Layer
- **Budget Progress Service**: Real-time progress calculation using `currentAmountCents`
- **Budget Transaction Service**: Automatic budget creation and updates during transaction processing
- **Budget Providers**: Riverpod providers for state management including total budget providers

### Service Layer
- **BulkBudgetService**: Handles bulk operations with atomic transactions
  - `adjustBudgetsByPercentage()`: Bulk percentage adjustments with validation
  - `changeBudgetStatus()`: Bulk activation/deactivation operations
  - `deleteBudgets()`: Batch deletion with comprehensive error handling
  - Comprehensive validation and error reporting with `BulkOperationResult`
- **BudgetTransactionService**: Manages automatic budget creation and real-time updates
  - `updateBudgetForTransaction()`: Updates budget amounts when transactions change
  - `createDefaultBudget()`: Creates category and total budgets automatically
  - `ensureTotalBudgetExists()`: Ensures total budgets exist for transaction types

### Provider Layer
- **budgetsByMonthProvider**: Get budgets for a specific month
- **totalBudgetByMonthAndTypeProvider**: Get total budget for specific month and type
- **totalBudgetsByMonthProvider**: Get all total budgets for a month
- **templateBudgetsForFuturePeriodProvider**: Get template budgets for future periods
- **budgetProgressProvider**: Calculate real-time budget progress

### Presentation Layer
- **Budget Screens**: List and edit screens with selection mode support and edit mode toggle
- **Budget Widgets**: Cards, forms, progress bars, empty states, and bulk operations dialog
- **Navigation Integration**: Accessible through app drawer with time period integration
- **Selection Mode**: Multi-select capabilities with bulk action toolbar

## Firestore Configuration

### Security Rules
Budget data is protected with comprehensive validation:
- User data isolation (users can only access their own budgets)
- Required field validation
- Business rule enforcement
- Referential integrity checks

### Composite Indexes
Three composite indexes support efficient querying:
1. `isActive + createdAt` - Active budgets ordered by creation
2. `isActive + period + createdAt` - Budgets filtered by period
3. `isActive + categoryId + period` - Category-specific budget queries

## User Interface

### Budget List Screen
- Displays all active budgets with progress indicators
- Filter by budget type and period
- Edit mode toggle for inline budget editing
- Empty state guidance for users with upcoming budget management features

### Budget Editing
- Inline editing with edit mode toggle
- Form validation with real-time feedback
- Category selection integration
- Parent budget selection for hierarchical organization
- Overlap detection and prevention

### Progress Visualization
- Progress bars with color-coded status
- Percentage completion display
- Over-budget warnings
- Real-time updates based on transaction changes

### Bulk Operations UI
- **Selection Mode**: Multi-select interface with checkboxes and visual feedback
- **Bulk Action Toolbar**: Appears when budgets are selected with operation buttons
- **BulkBudgetOperationsDialog**: Material 3 dialog for bulk operations with:
  - Percentage adjustment controls with validation
  - Activation/deactivation options with confirmation
  - Batch deletion with comprehensive warnings
  - Form validation and error handling
- **Selection Count Display**: Shows number of selected budgets in AppBar
- **Visual Feedback**: Selected budgets highlighted with distinct styling

## Navigation

Budget management is accessible through:
- **App Drawer**: "Budgets" menu item
- **Direct Routes**: `/budgets`, `/budgets/:id/edit`

## Implementation Status

✅ **Completed Features:**
- Complete data model and repository implementation
- Firestore security rules and composite indexes
- All UI screens and widgets
- Navigation integration
- Progress calculation service
- Client-side validation
- Real-time state management with Riverpod

## Budget Migration System

### BudgetMigrationService
The `BudgetMigrationService` handles schema migrations for existing budget data:

**Key Features:**
- **Schema Version Migration**: Migrates budgets from schema v3 to v4 with `periodStart` field population
- **Batch Processing**: Handles large datasets efficiently with Firestore batch limits (500 documents per batch)
- **Field Conversion**: Converts legacy fields (`amount` → `plannedAmountCents`, `currency` → `currencyCode`)
- **Error Handling**: Comprehensive error tracking and reporting for migration issues
- **Period Calculation**: Derives `periodStart` from `createdAt` date and period type
- **Atomic Operations**: Uses Firestore transactions to ensure data consistency

**Usage:**
```dart
final migrationService = ref.read(budgetMigrationServiceProvider);
final result = await migrationService.migrateToSchemaVersion4();
```

🔄 **Future Enhancements:**
- Budget templates and presets
- Advanced analytics and reporting
- Budget sharing and collaboration
- Notification system for budget alerts
- Export functionality

## Technical Notes

### Dependencies
- `riverpod` - State management
- `freezed` - Data class generation
- `json_annotation` - JSON serialization
- `cloud_firestore` - Backend data storage

### Performance Considerations
- Composite indexes ensure efficient Firestore queries for period-based filtering
- Stream-based real-time updates minimize unnecessary rebuilds
- Client-side validation reduces server round trips
- Soft deletion maintains data integrity while improving performance
- Period-based queries use optimized indexes for `isActive + period + periodStart` combinations
- Fallback logic minimizes database queries by caching previous period results

### Security
- All budget operations require user authentication
- Firestore rules enforce user data isolation and schema version 4 compliance
- Period-based uniqueness constraints enforced at database level
- Client-side validation prevents invalid data submission
- Referential integrity maintained through validation functions
- Migration operations require proper authentication and validation
