# Error Handling Architecture

## Overview

BudApp implements a comprehensive centralized error handling system designed for financial applications with stringent reliability and user experience requirements. The system provides four layers of error handling: global error capture, UI error boundaries, service-level error translation, and user-friendly recovery mechanisms.

## Architecture Components

### 🔧 GlobalErrorHandler Service

**Location**: `lib/services/global_error_handler.dart`

A singleton service that integrates with Firebase Crashlytics to capture and report all application errors while maintaining PII protection and environment-aware configuration.

#### Key Features
- **Global Error Handlers**: Captures Flutter framework errors, platform dispatcher errors, and isolate errors
- **Firebase Crashlytics Integration**: Automatic crash reporting with user context and custom metadata
- **Environment Configuration**: Debug/staging/production specific crash collection settings
- **PII Protection**: Automatic data sanitization to protect financial information
- **User Context Tracking**: Associates crashes with user sessions while maintaining privacy

#### Initialization
```dart
// Automatic initialization at app startup via Riverpod providers
await GlobalErrorHandler.instance.initialize(
  logger: loggingService,
  enableInDebug: false, // Only crashes in release mode
);
```

#### Manual Error Reporting
```dart
await GlobalErrorHandler.instance.reportError(
  error,
  stackTrace,
  reason: 'Payment processing failed',
  customKeys: {'transactionType': 'transfer', 'amount': '2500'},
  breadcrumbs: ['User initiated transfer', 'Account validation passed'],
  fatal: false,
);
```

#### Architecture Details

**Global Error Handlers Configured**:
1. **FlutterError.onError**: Captures Flutter framework errors (widget build failures, etc.)
2. **PlatformDispatcher.instance.onError**: Captures asynchronous errors outside Flutter context
3. **Isolate.current.addErrorListener**: Captures isolate errors and background thread failures

**Crashlytics Integration**:
- Custom key size limits (1kB per key-value pair)
- Breadcrumb logging with 64kB total limit
- User identifier setting with PII protection
- Environment-aware crash collection enabling

### 🛡️ Error Boundary System

**Location**: `lib/widgets/error_boundary_widget.dart`

React-style error boundaries that provide graceful UI degradation when widgets encounter errors, preventing complete app crashes and providing recovery mechanisms.

#### Components

**ErrorBoundaryWidget**:
- Catches errors in child widget tree
- Displays user-friendly fallback UI
- Provides retry mechanisms for transient failures
- Integrates with crash reporting for error logging

**SafeWidget**:
- Simplified wrapper for basic error safety
- Minimal fallback UI for non-critical components

#### Usage Patterns

**Full Error Boundary**:
```dart
Widget build(BuildContext context) {
  return MyComplexWidget().withErrorBoundary(
    showDetails: kDebugMode,
    canRetry: true,
    onError: (error, stackTrace) {
      // Custom error handling logic
      analytics.logError('widget_error', {'widget': 'MyComplexWidget'});
    },
    fallbackBuilder: (context, error, stackTrace, retry) => CustomErrorUI(),
  );
}
```

**Simple Safety Wrapper**:
```dart
Widget build(BuildContext context) {
  return RiskyWidget().safely(
    fallback: Text('Content temporarily unavailable'),
    onError: (error, stackTrace) => logError(error),
  );
}
```

#### Error Detection Logic
Error boundaries only catch UI-related errors to avoid interfering with global system errors:
- RenderBox errors
- RenderFlex layout errors
- Widget build failures
- Layout constraint violations

### 📊 Enhanced ErrorService

**Location**: `lib/services/error_service.dart`

A comprehensive service that translates technical errors into user-friendly messages while integrating with crash reporting for comprehensive error tracking.

#### Key Features
- **Firebase Error Translation**: Converts Firebase errors to localized user messages
- **Crash Reporting Integration**: Automatically logs errors to GlobalErrorHandler
- **Consistent UI Components**: Standardized error snackbars, dialogs, and messaging
- **Context-Aware Logging**: Error logging with specific context for debugging

#### Error Translation

**Firebase Auth Errors**:
```dart
// Handles all Firebase Auth error codes
case 'user-not-found': return l10n.userNotFoundError;
case 'wrong-password': return l10n.wrongPasswordError;
case 'email-already-in-use': return l10n.emailAlreadyInUseError;
// ... and 10+ more specific error codes
```

**Firebase Firestore Errors**:
```dart
// Handles all Firestore error codes
case 'permission-denied': return l10n.permissionDeniedError;
case 'unavailable': return l10n.serviceUnavailableError;
case 'deadline-exceeded': return l10n.timeoutError;
// ... and 15+ more specific error codes
```

#### UI Integration

**Error Display with Logging**:
```dart
// Combines user-friendly display with crash reporting
ErrorService.showErrorSnackBarWithLogging(
  context,
  error,
  errorContext: 'Transaction creation',
  additionalData: {'transactionType': type, 'accountId': accountId},
);
```

**Error Dialog with Recovery**:
```dart
final shouldRetry = await ErrorService.showErrorDialogWithLogging(
  context,
  error,
  title: 'Transaction Failed',
  errorContext: 'Account balance update',
  showRetryButton: true,
);

if (shouldRetry) {
  // Retry logic
}
```

### 🔗 Provider Integration

**Location**: `lib/providers/error_providers.dart`

Riverpod providers that automatically initialize error handling at app startup and integrate with the existing provider architecture.

#### Provider Definitions
```dart
final globalErrorHandlerProvider = Provider<GlobalErrorHandler>((ref) {
  return GlobalErrorHandler.instance;
});

final errorHandlerInitializationProvider = FutureProvider<void>((ref) async {
  final errorHandler = ref.read(globalErrorHandlerProvider);
  final loggingService = ref.read(loggingServiceProvider);
  await errorHandler.initialize(logger: loggingService, enableInDebug: false);
});
```

#### App Startup Integration
```dart
// In lib/providers/providers.dart
final appInitializationProvider = FutureProvider<void>((ref) async {
  try {
    await PerformanceService.initialize();
    await ref.read(errorHandlerInitializationProvider.future); // Error handling init
    final firebaseService = ref.read(firebaseServiceProvider);
    await firebaseService.initializeServices();
  } catch (error, stackTrace) {
    // Error handling initialized first, so this will be captured
    rethrow;
  }
});
```

## Security & Privacy

### 🔒 PII Protection

The error handling system implements comprehensive PII protection to comply with financial application requirements:

#### LoggingService Integration
- **Automatic Sanitization**: All error messages automatically sanitized via LoggingService
- **Pattern Detection**: Emails, phone numbers, credit cards, amounts, API keys automatically redacted
- **Custom Key Filtering**: Crashlytics custom keys filtered to prevent financial data exposure

#### Data Sanitization Examples
```dart
// Before sanitization
"User <EMAIL> failed to transfer $2,500.00 to account 4532-1234-5678-9012"

// After sanitization
"User [EMAIL] failed to transfer [AMOUNT] to account [CREDIT_CARD]"
```

### 🌍 Environment Configuration

#### Development Environment
- **Full Error Details**: Complete stack traces and error context
- **Comprehensive Logging**: All log levels (trace, debug, info, warning, error)
- **Crash Collection Disabled**: Prevent development crashes from polluting production data
- **Debug UI**: Error boundaries show detailed error information

#### Staging Environment
- **Reduced Logging**: Info level and above
- **Crash Collection Enabled**: Test crash reporting functionality
- **Detailed Context**: Error context for debugging without PII exposure

#### Production Environment
- **Minimal Logging**: Warning level and above only
- **Full Crash Reporting**: Complete crash collection for reliability monitoring
- **User-Friendly Messages**: Only localized, user-friendly error messages displayed
- **PII Protection**: All sensitive data automatically sanitized

## Implementation Patterns

### 🔄 Recovery Mechanisms

#### Automatic Retry Logic
```dart
// Built into repository pattern
return AsyncValue.guard(() async {
  try {
    return await _performOperation();
  } catch (error) {
    if (_isTransientError(error)) {
      await Future.delayed(Duration(seconds: 1));
      return await _performOperation(); // Single retry
    }
    rethrow;
  }
});
```

#### User-Initiated Recovery
```dart
// Error boundary retry button
ElevatedButton.icon(
  onPressed: _retry,
  icon: const Icon(Icons.refresh),
  label: Text(l10n.retry),
)

void _retry() {
  ErrorService.logUserAction('Error boundary retry', {
    'error': _error.toString(),
  });

  setState(() {
    _error = null;
    _stackTrace = null;
    _hasError = false;
  });
}
```

#### Graceful Degradation
```dart
// Provider-level fallback
@riverpod
class TransactionListNotifier extends _$TransactionListNotifier {
  @override
  Future<List<Transaction>> build() async {
    return AsyncValue.guard(() async {
      try {
        return await ref.read(transactionRepositoryProvider).getUserTransactions();
      } catch (error) {
        // Log error but provide fallback
        ErrorService.logError(error, null, context: 'Transaction list loading');
        return <Transaction>[]; // Empty list fallback
      }
    });
  }
}
```

### 📱 User Experience

#### Material 3 Error UI
All error UI components follow Material 3 design principles:
- **Consistent Color Scheme**: Error colors from theme.colorScheme.error
- **Proper Typography**: Theme-consistent text styles and hierarchy
- **Accessibility Support**: Screen reader compatible with proper semantic labels
- **Responsive Design**: Adapts to different screen sizes and orientations

#### Localization Support
```dart
// lib/l10n/app_en.arb error handling additions
"errorBoundaryTitle": "Something went wrong",
"errorBoundaryMessage": "We've encountered an unexpected error. Our team has been notified and we're working to fix it.",
"reportError": "Report Error",
"errorReported": "Error reported successfully. Thank you for helping us improve the app.",
"retry": "Retry"
```

#### Progressive Disclosure
- **Production**: Only user-friendly messages and basic retry options
- **Debug Mode**: Detailed error information, manual error reporting, stack traces

## Testing Strategy

### Unit Testing
```dart
group('GlobalErrorHandler', () {
  test('initializes with proper configuration', () async {
    final handler = GlobalErrorHandler.instance;
    final mockLogger = MockLoggingService();

    await handler.initialize(logger: mockLogger, enableInDebug: false);

    expect(handler.isInitialized, true);
    verify(() => mockLogger.info('Global error handler initialized successfully')).called(1);
  });

  test('reports error with custom context', () async {
    // Test error reporting with context and custom keys
  });
});
```

### Widget Testing
```dart
testWidgets('ErrorBoundaryWidget shows fallback UI on error', (tester) async {
  await tester.pumpWidget(
    MaterialApp(
      home: ErrorBoundaryWidget(
        child: ThrowsErrorWidget(),
        fallbackBuilder: (context, error, stackTrace, retry) =>
          Text('Error occurred'),
      ),
    ),
  );

  await tester.pumpAndSettle();
  expect(find.text('Error occurred'), findsOneWidget);
});
```

### Integration Testing
```dart
group('Error Handling Integration', () {
  testWidgets('provider errors are captured by global handler', (tester) async {
    // Test that provider errors trigger global error handler
    // Verify crash reporting integration
    // Test error boundary integration
  });
});
```

## Performance Considerations

### 🚀 Optimization Features

#### Minimal Overhead
- **Lazy Initialization**: Error handlers only set up when needed
- **Efficient Error Detection**: Error boundary checks are minimal performance impact
- **Batch Reporting**: Multiple errors batched for efficient crash reporting

#### Memory Management
- **Size Limits**: Custom keys and breadcrumbs have enforced size limits
- **Cleanup**: Error boundaries properly dispose of resources
- **Weak References**: No memory leaks from error handler references

#### Production Optimization
- **Conditional Compilation**: Debug code stripped from production builds
- **Log Level Filtering**: Production builds only process relevant log levels
- **Async Operations**: Error reporting doesn't block UI thread

## Monitoring & Analytics

### 📊 Error Tracking Metrics

#### Crashlytics Dashboard
- **Crash-Free Users**: Percentage of users not experiencing crashes
- **Error Trends**: Track error frequency over time
- **Error Impact**: Users affected by specific error types
- **Custom Keys**: Filter by transaction types, user actions, app state

#### Custom Metrics
```dart
// Track specific error patterns
FirebaseAnalytics.instance.logEvent(
  name: 'error_boundary_triggered',
  parameters: {
    'widget_type': 'TransactionForm',
    'error_type': 'validation_error',
    'user_action': 'submit_transaction',
  },
);
```

## Migration Guide

### From Existing Error Handling

#### 1. Replace Direct Error Logging
```dart
// Before
print('Error: $error');

// After
ErrorService.logError(error, stackTrace, context: 'specific_operation');
```

#### 2. Add Error Boundaries
```dart
// Before
Widget build(BuildContext context) {
  return MyWidget();
}

// After
Widget build(BuildContext context) {
  return MyWidget().withErrorBoundary();
}
```

#### 3. Update Provider Error Handling
```dart
// Before
@riverpod
Future<Data> fetchData(FetchDataRef ref) async {
  return await repository.getData();
}

// After
@riverpod
Future<Data> fetchData(FetchDataRef ref) async {
  return AsyncValue.guard(() async {
    return await repository.getData();
  });
}
```

## Best Practices

### ✅ Do's
- **Always use AsyncValue.guard()** for provider operations
- **Wrap critical UI components** with error boundaries
- **Provide meaningful error context** in all error reporting
- **Use ErrorService methods** for consistent user messaging
- **Test error scenarios** as part of your test suite

### ❌ Don'ts
- **Don't expose technical details** to users in production
- **Don't skip error logging** for better debugging
- **Don't catch and ignore errors** without proper handling
- **Don't hardcode error messages** - use localization
- **Don't report PII** in error logs or crash reports

## Troubleshooting

### Common Issues

#### Error Handler Not Initialized
```dart
// Problem: GlobalErrorHandler accessed before initialization
// Solution: Check provider dependency order in app initialization

// Correct initialization order
final appInitializationProvider = FutureProvider<void>((ref) async {
  await ref.read(errorHandlerInitializationProvider.future); // First
  await ref.read(firebaseServiceProvider).initializeServices(); // Then other services
});
```

#### Error Boundaries Not Catching Errors
```dart
// Problem: Error boundary only catches UI-related errors
// Solution: Use _shouldCatchError logic or global handler for system errors

// Check error type before boundary handling
bool _shouldCatchError(Object error) {
  final errorString = error.toString().toLowerCase();
  return errorString.contains('renderbox') ||
         errorString.contains('widget') ||
         errorString.contains('build');
}
```

#### PII in Error Reports
```dart
// Problem: Sensitive data appearing in crash reports
// Solution: Verify LoggingService integration and sanitization

// Ensure all error reporting goes through ErrorService
ErrorService.logError(error, stackTrace); // Automatically sanitized
```

### Debug Commands

#### Test Error Boundaries
```dart
// In debug builds only
if (kDebugMode) {
  FloatingActionButton(
    onPressed: () => throw Exception('Test error boundary'),
    child: Icon(Icons.bug_report),
  );
}
```

#### Test Crash Reporting
```dart
// In debug builds only
if (kDebugMode) {
  await GlobalErrorHandler.instance.testCrash(); // Triggers test crash
}
```

## Related Documentation

- [Architecture Overview](architecture.md) - Overall system architecture
- [Testing Strategy](TESTING.md) - Testing approaches and patterns
- [Security Guidelines](firebase-security-rules-testing.md) - Security implementation
- [Performance Monitoring](performance-monitoring.md) - Performance optimization
- [Localization](../lib/l10n/app_en.arb) - Error message localization

---

*This documentation covers the complete error handling architecture implemented in Task 31.8. For implementation details, see the source files in `lib/services/` and `lib/widgets/`.*