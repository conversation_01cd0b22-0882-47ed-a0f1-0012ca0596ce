# BudApp Architecture

This document provides a comprehensive overview of BudApp's architecture, design patterns, and implementation details.

## Table of Contents

- [Overview](#overview)
- [Architecture Principles](#architecture-principles)
- [Technology Stack](#technology-stack)
- [System Architecture](#system-architecture)
- [State Management (Riverpod)](#state-management-riverpod)
- [Data Layer (Repository Pattern + Freezed)](#data-layer-repository-pattern--freezed)
- [Navigation (go_router)](#navigation-go_router)
- [UI/Design System](#uidesign-system)
- [Dependency Injection](#dependency-injection)
- [Testing Architecture](#testing-architecture)
- [Firebase Integration](#firebase-integration)
- [Localization](#localization)
- [Best Practices](#best-practices)

## Overview

BudApp is a personal finance management application built with Flutter and Firebase, following modern architectural patterns and best practices. The application uses an offline-first approach with real-time synchronization, implementing a clean architecture with clear separation of concerns.

### Key Architectural Decisions

- **Feature-Based Architecture**: Code organized by business domains rather than technical layers
- **Offline-First**: Complete functionality without internet connection
- **Repository Pattern**: Clean separation between UI, business logic, and data layers
- **Immutable Data**: All data models use `freezed` for immutability and type safety
- **Reactive State Management**: Riverpod for predictable state handling
- **Declarative Navigation**: go_router for type-safe routing
- **Dependency Injection**: Riverpod providers for service management
- **Security-First**: Comprehensive Firestore Security Rules and user data isolation

## Architecture Principles

### 1. Clean Architecture
The application follows clean architecture principles with clear layer separation:
- **Presentation Layer**: UI widgets and screens
- **Application Layer**: State management and business logic
- **Domain Layer**: Repository interfaces and data models
- **Infrastructure Layer**: Firebase services and external APIs

### 2. Offline-First Design
All features work without internet connection:
- Local data persistence with Firestore offline cache
- Optimistic updates for immediate UI feedback
- Background synchronization when connectivity is restored
- Conflict resolution with server-timestamp authority

### 3. Type Safety
Strong typing throughout the application:
- Freezed data models with compile-time safety
- Repository interfaces for clear contracts
- Provider-based dependency injection
- Comprehensive error handling with AsyncValue

## Technology Stack

### Frontend
- **Flutter**: Cross-platform mobile framework with Dart 3
- **Riverpod**: State management with AsyncNotifier/Notifier pattern
- **go_router**: Declarative routing with authentication guards
- **freezed**: Immutable data models with JSON serialization
- **Material 3**: Design system with comprehensive design tokens and global text overflow system

### Backend
- **Firebase Core**: Project configuration and initialization
- **Firebase Auth**: Authentication with email/password and OAuth
- **Cloud Firestore**: NoSQL database with offline persistence
- **Firebase Remote Config**: Feature flags and app configuration
- **Firestore Security Rules**: Server-side validation and access control

### Development Tools
- **riverpod_generator**: Code generation for providers
- **json_serializable**: JSON serialization for data models
- **build_runner**: Code generation orchestration
- **flutter_gen_l10n**: Internationalization and localization
- **mocktail**: Testing with mocks and stubs

## System Architecture

```mermaid
graph TB
    UI[UI Layer<br/>Screens & Widgets] --> SM[State Management<br/>Riverpod Providers]
    SM --> BL[Business Logic<br/>Services & Notifiers]
    BL --> DL[Data Layer<br/>Repository Pattern]
    DL --> FB[Firebase Services<br/>Auth, Firestore, Config]
    
    UI --> NAV[Navigation<br/>go_router]
    NAV --> AUTH[Auth Guards<br/>Route Protection]
    
    DL --> CACHE[Local Cache<br/>Firestore Offline]
    FB --> SYNC[Real-time Sync<br/>Firestore Listeners]
    
    subgraph "Data Models"
        MODELS[Freezed Models<br/>UserProfile, Account, Transaction<br/>Goal, GoalContribution, Budget]
    end
    
    DL --> MODELS
    SM --> MODELS
```

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant UI as UI Widget
    participant Provider as Riverpod Provider
    participant Repository as Repository
    participant Firestore as Firestore Service
    participant Firebase as Firebase Backend
    
    UI->>Provider: Watch/Read Provider
    Provider->>Repository: Call Repository Method
    Repository->>Firestore: Execute Firestore Operation
    Firestore->>Firebase: Network Request (if online)
    Firebase-->>Firestore: Response Data
    Firestore-->>Repository: Processed Data
    Repository-->>Provider: Domain Model
    Provider-->>UI: AsyncValue<Model>
    
    Note over Firestore: Offline persistence handles<br/>local cache automatically
```

## State Management (Riverpod)

BudApp uses Riverpod for state management with a well-organized provider hierarchy.

### Provider Organization

```
lib/providers/
├── providers.dart          # Core Firebase providers and exports
├── auth_providers.dart     # Authentication state and operations
├── ui_providers.dart       # UI state (loading, theme, etc.)
├── firebase_providers.dart # Firebase service providers
└── repository_providers.dart # Repository instances
```

### Provider Types and Usage

#### 1. Core Providers
```dart
// Firebase service providers
final firebaseAuthProvider = Provider<FirebaseAuth>((ref) => FirebaseAuth.instance);
final firestoreProvider = Provider<FirebaseFirestore>((ref) => FirebaseFirestore.instance);

// Authentication state
final authStateProvider = StreamProvider<User?>((ref) {
  final auth = ref.watch(firebaseAuthProvider);
  return auth.authStateChanges();
});
```

#### 2. AsyncNotifier Providers
```dart
@riverpod
class LoginNotifier extends _$LoginNotifier {
  @override
  FutureOr<void> build() {
    // Initial state
  }

  Future<void> signInWithEmail(String email, String password) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final authService = ref.read(authServiceProvider);
      await authService.signInWithEmailAndPassword(email, password);
    });
  }
}
```

#### 3. Repository Providers
```dart
@riverpod
IUserRepository userRepository(UserRepositoryRef ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  return UserRepositoryImpl(firestoreService);
}
```

### Best Practices

1. **Use `ref.watch()` in widgets** for reactive updates
2. **Use `ref.read()` in callbacks** for one-time access
3. **Use `AsyncValue.guard()`** for error handling
4. **Use `@riverpod` annotation** for code generation
5. **Organize providers by domain** (auth, UI, repositories)

## Data Layer (Repository Pattern + Freezed)

The data layer implements the Repository pattern with immutable data models and **strict enforcement of layer separation**.

### Repository Pattern Structure

```
lib/data/
├── repositories/
│   ├── interfaces/           # Abstract repository contracts
│   │   ├── base_repository.dart
│   │   ├── user_repository.dart
│   │   ├── account_repository.dart
│   │   ├── transaction_repository.dart
│   │   ├── budget_repository.dart
│   │   ├── category_repository.dart
│   │   ├── goal_repository.dart
│   │   └── tag_repository.dart
│   └── implementations/      # Concrete implementations
│       ├── user_repository_impl.dart
│       ├── account_repository_impl.dart
│       ├── transaction_repository_impl.dart
│       ├── budget_repository_impl.dart
│       ├── category_repository_impl.dart
│       ├── goal_repository_impl.dart
│       └── tag_repository_impl.dart
```

### Repository Pattern Enforcement

**Critical Architecture Rule**: All business logic must interact with repository interfaces, never directly with Firebase services. This ensures:
- ✅ **Testability**: Easy mocking and unit testing
- ✅ **Maintainability**: Clean separation of concerns
- ✅ **Flexibility**: Ability to swap data sources without changing business logic
- ✅ **Type Safety**: Compile-time interface contract enforcement

### Repository Interface Example

```dart
abstract class IUserRepository extends IBaseRepository<UserProfile> {
  Future<UserProfile?> getUserProfile(String userId);
  Future<void> createUserProfile(UserProfile profile);
  Future<void> updateUserProfile(UserProfile profile);
  Stream<UserProfile?> watchUserProfile(String userId);
}
```

### Service Abstraction Layer

For cross-cutting concerns that don't fit the repository pattern, service abstractions are used:

```dart
// Firebase connectivity testing service
abstract class IFirebaseConnectivityService {
  Future<Map<String, dynamic>> testFirebaseConnectivity();
  Future<Map<String, dynamic>> testAuthConnectivity();
  Future<Map<String, dynamic>> testFirestoreConnectivity();
  Future<bool> areFirebaseServicesAvailable();
}
```

### Implementation with Dependency Injection

```dart
class FirebaseConnectivityServiceImpl implements IFirebaseConnectivityService {
  final FirebaseAuth _firebaseAuth;
  final FirebaseFirestore _firestore;

  FirebaseConnectivityServiceImpl({
    required FirebaseAuth firebaseAuth,
    required FirebaseFirestore firestore,
  }) : _firebaseAuth = firebaseAuth, _firestore = firestore;

  @override
  Future<Map<String, dynamic>> testFirebaseConnectivity() async {
    // Implementation using injected dependencies
  }
}
```

### Freezed Data Models

All data models use `freezed` for immutability and JSON serialization:

```dart
@freezed
class UserProfile with _$UserProfile {
  const factory UserProfile({
    required String uid,
    String? email,
    String? displayName,
    String? photoURL,
    required DateTime createdAt,
    required DateTime lastLoginAt,
    @Default({}) Map<String, dynamic> preferences,
    @Default(false) bool isEmailVerified,
    @Default([]) List<String> authProviders,
  }) = _UserProfile;

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);
}
```

### Benefits of Freezed

1. **Immutability**: Data cannot be accidentally modified
2. **copyWith()**: Easy creation of modified copies
3. **Equality**: Automatic value equality implementation
4. **JSON Serialization**: Seamless Firestore integration
5. **Pattern Matching**: Exhaustive when() methods
6. **Code Generation**: Reduces boilerplate significantly

## Navigation (go_router)

BudApp uses go_router for declarative, type-safe navigation with authentication guards.

### Router Configuration

```dart
final goRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppRoutes.splash,
    redirect: (context, state) {
      final authState = ref.read(authStateProvider);
      return authState.when(
        data: (user) {
          // Authentication-based routing logic
          if (user == null) return AppRoutes.login;
          if (!user.emailVerified) return AppRoutes.emailVerification;
          return null; // Allow access
        },
        loading: () => AppRoutes.splash,
        error: (_, __) => AppRoutes.login,
      );
    },
    routes: [
      GoRoute(path: AppRoutes.login, builder: (context, state) => const LoginScreen()),
      GoRoute(path: AppRoutes.home, builder: (context, state) => const HomeScreen()),
      // ... other routes
    ],
  );
});
```

### Route Definitions

```dart
class AppRoutes {
  static const String splash = '/splash';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String home = '/home';
  static const String forgotPassword = '/forgot-password';
  static const String emailVerification = '/email-verification';
}
```

### Navigation Usage

```dart
// Declarative navigation
context.go(AppRoutes.home);
context.push(AppRoutes.signup);
context.pop();

// Named route navigation
context.goNamed('home');
context.pushNamed('signup');
```

### Authentication Guards

The router automatically handles authentication state:
- Unauthenticated users → Login screen
- Authenticated but unverified → Email verification
- Fully authenticated → Requested route or Home

## UI/Design System

BudApp implements a comprehensive design system with Material 3 theming, a global text overflow management system, and a unified generic form architecture.

### Generic Form Architecture

The application uses a configuration-driven form system that eliminates code duplication and ensures consistency across all entity forms:

#### Core Components
- **GenericFormScreen<T>**: Single form screen handling all entity types through configuration
- **GenericFormConfig<T>**: Type-safe configuration defining form structure and behavior
- **FormFieldFactory**: Creates appropriate widgets based on field configuration types
- **Shared Components**: Unified color/icon pickers and form fields across all entities

#### Benefits
- **Code Reduction**: 60-80% reduction in form-related code
- **Type Safety**: Compile-time type checking for all form configurations
- **Consistency**: Standardized form layouts, validation, and navigation patterns
- **Maintainability**: Single source of truth for form behavior

For detailed information, see [Generic Form Architecture](GENERIC_FORM_ARCHITECTURE.md).

### Design Tokens

The application uses a centralized design token system for consistent styling:

```dart
// lib/config/design_tokens.dart
class AppColors {
  static const primary = Color(0xFF6750A4);
  static const onPrimary = Color(0xFFFFFFFF);
  // ... other color tokens
}

class AppTypography {
  static const headlineLarge = TextStyle(fontSize: 32, fontWeight: FontWeight.bold);
  static const bodyMedium = TextStyle(fontSize: 14, fontWeight: FontWeight.normal);
  // ... other typography tokens
}

class AppTextOverflowTokens {
  static const maxLinesSingle = 1;
  static const maxLinesBody = 3;
  static const minScaleFactor = 0.8;
}
```

### Global Text Overflow System

A smart text overflow system provides consistent text handling across the application:

#### AppText Widget
```dart
// Context-specific constructors
AppText.title('Long title text that will be truncated')
AppText.body('Body text with multi-line support')
AppText.critical('Important text that needs attention')

// Automatic context detection
AppText('Text content', style: Theme.of(context).textTheme.headlineMedium)
```

#### Key Features
- **Context-Aware Configuration**: Automatic detection based on text style properties
- **Predefined Contexts**: Title, body, label, note, critical, button, listItem
- **Adaptive Text Support**: Optional responsive scaling with FittedBox
- **Design Token Integration**: Seamlessly integrated with existing design system
- **Backward Compatibility**: Existing Text widgets continue to work

#### Configuration System
```dart
enum TextContext { title, body, label, critical, note, button, listItem }

class TextOverflowConfig {
  final int? maxLines;
  final TextOverflow overflow;
  final bool softWrap;
  final bool adaptive;
}
```

### Material 3 Integration

The design system fully supports Material 3 with:
- Dynamic color schemes for light/dark themes
- Proper contrast ratios and accessibility
- Consistent spacing and elevation
- Typography scale integration with text overflow system

## Dependency Injection

BudApp uses Riverpod providers for dependency injection throughout the application.

### Service Dependencies

```mermaid
graph TD
    A[AuthService] --> B[FirebaseAuth]
    A --> C[GoogleSignIn]
    A --> D[IUserRepository]
    
    E[UserRepository] --> F[FirestoreService]
    G[TransactionRepository] --> F
    H[AccountRepository] --> F
    
    F --> I[FirebaseFirestore]
    
    J[UI Components] --> A
    J --> E
    J --> G
    J --> H
    J --> K[IFirebaseConnectivityService]
    
    K --> B
    K --> I
    
    L[FirebaseConnectivityServiceImpl] --> B
    L --> I
```

### Provider Hierarchy

1. **Core Firebase Providers**: Direct Firebase service instances
2. **Service Providers**: Business logic services with injected dependencies
3. **Repository Providers**: Data access layer with service dependencies
4. **UI Providers**: State management for UI components

### Repository Pattern Enforcement in Providers

All repository implementations use proper dependency injection instead of direct Firebase access:

```dart
// ✅ CORRECT: Repository with proper dependency injection
@riverpod
IUserRepository userRepository(UserRepositoryRef ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  final firebaseAuth = ref.watch(firebaseAuthProvider);
  return UserRepositoryImpl(firestoreService, firebaseAuth);
}

// ✅ CORRECT: Service abstraction with dependency injection
final firebaseConnectivityServiceProvider = Provider<IFirebaseConnectivityService>((ref) {
  final firebaseAuth = ref.watch(firebaseAuthProvider);
  final firestore = ref.watch(firestoreProvider);
  return FirebaseConnectivityServiceImpl(
    firebaseAuth: firebaseAuth,
    firestore: firestore,
  );
});

// ❌ FORBIDDEN: Direct Firebase access in business logic
// FirebaseAuth.instance.currentUser (violates Repository pattern)
// FirebaseFirestore.instance.collection() (violates layer separation)
```

### Example Dependency Chain

```dart
// 1. Core Firebase provider
final firebaseAuthProvider = Provider<FirebaseAuth>((ref) => FirebaseAuth.instance);

// 2. Repository provider with dependency injection
final userRepositoryProvider = Provider<IUserRepository>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  final firebaseAuth = ref.watch(firebaseAuthProvider);
  return UserRepositoryImpl(firestoreService, firebaseAuth);
});

// 3. Service provider with multiple dependencies
final authServiceProvider = Provider<AuthService>((ref) {
  final auth = ref.watch(firebaseAuthProvider);
  final googleSignIn = ref.watch(googleSignInProvider);
  final userRepository = ref.watch(userRepositoryProvider);
  
  return AuthService(
    auth: auth,
    googleSignIn: googleSignIn,
    userRepository: userRepository,
  );
});

// 4. Service abstraction provider
final firebaseConnectivityServiceProvider = Provider<IFirebaseConnectivityService>((ref) {
  final firebaseAuth = ref.watch(firebaseAuthProvider);
  final firestore = ref.watch(firestoreProvider);
  return FirebaseConnectivityServiceImpl(
    firebaseAuth: firebaseAuth,
    firestore: firestore,
  );
});
```

## Testing Architecture

BudApp implements comprehensive testing with proper mocking and provider overrides.

### Test Structure

```
test/
├── helpers/                 # Test utilities and helpers
│   ├── test_wrapper.dart   # Widget test wrapper with providers
│   ├── mock_providers.dart # Mock provider implementations
│   └── data_factories.dart # Test data generation
├── unit/                   # Unit tests for business logic
├── widget/                 # Widget tests for UI components
└── integration/            # Integration tests for user flows
```

### Test Helpers

```dart
class TestWrapper {
  static Widget createTestWidget(
    Widget child, {
    List<Override> overrides = const [],
  }) {
    return ProviderScope(
      overrides: overrides,
      child: MaterialApp(
        localizationsDelegates: const [AppLocalizations.delegate],
        supportedLocales: const [Locale('en')],
        home: child,
      ),
    );
  }
}
```

### Mock Providers

```dart
class MockAuthService extends Mock implements AuthService {}
class MockUserRepository extends Mock implements IUserRepository {}

final mockAuthServiceProvider = Provider<AuthService>((ref) => MockAuthService());
final mockUserRepositoryProvider = Provider<IUserRepository>((ref) => MockUserRepository());
```

### Testing Patterns

1. **Unit Tests**: Test business logic in isolation
2. **Widget Tests**: Test UI components with mocked dependencies
3. **Integration Tests**: Test complete user flows
4. **Provider Tests**: Test provider behavior and dependencies
5. **Repository Tests**: Test data layer with fake Firestore

## Firebase Integration

BudApp integrates with Firebase services through a clean abstraction layer including Authentication, Firestore, Remote Config, and App Check for enhanced security.

### Service Architecture

```dart
// Injectable Firebase service
class FirestoreService {
  final FirebaseFirestore _firestore;
  
  FirestoreService(this._firestore);
  
  Future<DocumentSnapshot> getDocument(String path) async {
    return await _firestore.doc(path).get();
  }
  
  Stream<QuerySnapshot> watchCollection(String path) {
    return _firestore.collection(path).snapshots();
  }
}
```

### Security Rules Integration

Firestore Security Rules provide server-side validation:

```javascript
// User data isolation
match /users/{userId} {
  allow read, write: if request.auth != null && request.auth.uid == userId;
  
  // Subcollections with validation
  match /accounts/{accountId} {
    allow read, write: if request.auth != null 
      && request.auth.uid == userId
      && isValidAccount(request.resource.data);
  }
}
```

### Remote Config Integration

Firebase Remote Config provides server-side configuration management:

```dart
// Remote Config service
class RemoteConfigService {
  final FirebaseRemoteConfig _remoteConfig;
  
  RemoteConfigService(this._remoteConfig);
  
  Future<void> initialize() async {
    await _remoteConfig.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: kDebugMode 
          ? const Duration(minutes: 5)
          : const Duration(hours: 12),
      ),
    );
  }
  
  RemoteConfigData getCurrentConfig() {
    return RemoteConfigData(
      categories: _getPredefinedCategories(),
      premiumLimits: _getPremiumLimits(),
      enableFeatureX: _remoteConfig.getBool('enable_feature_x'),
    );
  }
}
```

**Key Features:**
- **Predefined Categories**: Server-managed income/expense categories
- **Premium Limits**: Dynamic subscription tier limits
- **Feature Flags**: A/B testing and gradual rollout capabilities
- **Real-time Updates**: Live configuration changes (mobile only)

### Firebase App Check Integration

Firebase App Check provides enhanced security by verifying that requests come from legitimate app instances:

```dart
// App Check configuration in EnvironmentConfig
static Future<void> initializeAppCheck() async {
  try {
    await FirebaseAppCheck.instance.activate(
      // Environment-specific providers
      webProvider: kIsWeb ? ReCaptchaV3Provider(appCheckWebRecaptchaSiteKey) : null,
      androidProvider: androidAppCheckProvider,  // Debug or Play Integrity
      appleProvider: appleAppCheckProvider,      // Debug or App Attest
    );
    
    debugPrint('Firebase App Check activated for $environmentName environment');
  } catch (e) {
    debugPrint('Error activating Firebase App Check: $e');
    // Continue app execution even if App Check fails
  }
}
```

**Key Security Features:**
- **Environment-Aware Configuration**: Debug providers for dev/staging, secure attestation for production
- **Multi-Platform Support**: Android (Play Integrity), iOS (App Attest), Web (reCAPTCHA v3)
- **Automatic Initialization**: Activates during app startup with graceful error handling
- **Request Verification**: Ensures only legitimate app instances can access Firebase services
- **Cost Protection**: Prevents unauthorized Firebase service consumption and API abuse

### Offline-First Implementation

1. **Firestore Offline Persistence**: Automatic local caching
2. **Optimistic Updates**: Immediate UI feedback
3. **Real-time Listeners**: Automatic sync when online
4. **Conflict Resolution**: Server timestamp authority
5. **Remote Config Caching**: Offline access to configuration data

## Localization

BudApp uses `flutter_gen_l10n` for internationalization support.

### Configuration

```yaml
# pubspec.yaml
flutter:
  generate: true

# l10n.yaml
arb-dir: lib/l10n
template-arb-file: app_en.arb
output-localization-file: app_localizations.dart
```

### Usage

```dart
// In widgets
Text(AppLocalizations.of(context)!.signIn)

// In validation
String? validateEmail(String? value, BuildContext context) {
  if (value?.isEmpty ?? true) {
    return AppLocalizations.of(context)!.emailRequired;
  }
  return null;
}
```

### String Organization

All user-facing strings are centralized in `lib/l10n/app_en.arb`:

```json
{
  "signIn": "Sign In",
  "signUp": "Sign Up",
  "emailRequired": "Email is required",
  "passwordRequired": "Password is required"
}
```

## Best Practices

### 1. Provider Organization
- Group providers by domain (auth, UI, repositories)
- Use `@riverpod` annotation for code generation
- Implement proper dependency injection chains
- Use `AsyncValue.guard()` for error handling

### 2. Data Models
- Always use `freezed` for data models
- Implement custom `fromJson()` for complex types
- Use meaningful field names and documentation
- Handle nullable fields appropriately

### 3. Repository Pattern
- **Enforce strict layer separation**: Never use `FirebaseAuth.instance` or `FirebaseFirestore.instance` directly in business logic
- Define clear interface contracts for all data access
- Implement comprehensive error handling with proper exception types
- Use dependency injection for all Firebase services through Riverpod providers
- Create service abstractions for cross-cutting concerns (e.g., connectivity testing)
- Write thorough tests for all operations with proper mocking

### 4. Navigation
- Use named routes for better maintainability
- Implement authentication guards at router level
- Handle deep linking and route parameters
- Test navigation flows thoroughly

### 5. Testing
- Write tests for all business logic
- Use provider overrides for mocking
- Test error scenarios and edge cases
- Maintain high test coverage for critical paths

### 6. Firebase Integration
- Use service abstraction for testability
- Implement comprehensive Security Rules
- Handle offline scenarios gracefully
- Monitor performance and costs

## Code Examples and Patterns

### Creating a New Repository

1. **Define the Interface**:
```dart
// lib/data/repositories/interfaces/budget_repository.dart
abstract class IBudgetRepository extends IBaseRepository<Budget> {
  Future<List<Budget>> getUserBudgets(String userId);
  Future<Budget?> getBudget(String budgetId);
  Future<void> updateBudget(Budget budget);
  Future<void> deleteBudget(String budgetId);
  Stream<List<Budget>> watchUserBudgets(String userId);
}
```

2. **Implement the Repository**:
```dart
// lib/data/repositories/implementations/budget_repository_impl.dart
class BudgetRepositoryImpl implements IBudgetRepository {
  final FirestoreService _firestoreService;

  BudgetRepositoryImpl(this._firestoreService);

  @override
  Future<List<Budget>> getUserBudgets(String userId) async {
    final snapshot = await _firestoreService.getCollection(
      'users/$userId/budgets',
    );
    return snapshot.docs
        .map((doc) => Budget.fromJson(doc.data()))
        .toList();
  }

  // ... other implementations
}
```

3. **Create the Provider**:
```dart
// lib/providers/repository_providers.dart
@riverpod
IBudgetRepository budgetRepository(BudgetRepositoryRef ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  return BudgetRepositoryImpl(firestoreService);
}
```

### Adding a New Screen with Navigation

1. **Create the Screen**:
```dart
// lib/screens/budgets/budget_list_screen.dart
class BudgetListScreen extends ConsumerWidget {
  const BudgetListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final budgets = ref.watch(userBudgetsProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.budgets),
      ),
      body: budgets.when(
        data: (budgetList) => ListView.builder(
          itemCount: budgetList.length,
          itemBuilder: (context, index) => BudgetTile(budget: budgetList[index]),
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => ErrorWidget(error),
      ),
    );
  }
}
```

2. **Add Route Definition**:
```dart
// lib/routing/app_router.dart
class AppRoutes {
  // ... existing routes
  static const String budgets = '/budgets';
  static const String budgetDetail = '/budgets/:budgetId';
}

// Add to routes list
GoRoute(
  path: AppRoutes.budgets,
  name: 'budgets',
  builder: (context, state) => const BudgetListScreen(),
),
```

3. **Navigate to Screen**:
```dart
// From any widget
ElevatedButton(
  onPressed: () => context.go(AppRoutes.budgets),
  child: Text(AppLocalizations.of(context)!.viewBudgets),
)
```

### Creating AsyncNotifier Providers

```dart
@riverpod
class BudgetNotifier extends _$BudgetNotifier {
  @override
  FutureOr<List<Budget>> build() async {
    final user = ref.watch(currentUserProvider);
    if (user == null) return [];

    final repository = ref.watch(budgetRepositoryProvider);
    return repository.getUserBudgets(user.uid);
  }

  Future<void> createBudget(Budget budget) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final repository = ref.read(budgetRepositoryProvider);
      await repository.createBudget(budget);

      // Refresh the list
      ref.invalidateSelf();
    });
  }

  Future<void> deleteBudget(String budgetId) async {
    state = await AsyncValue.guard(() async {
      final repository = ref.read(budgetRepositoryProvider);
      await repository.deleteBudget(budgetId);

      // Update state optimistically
      final currentBudgets = state.value ?? [];
      state = AsyncValue.data(
        currentBudgets.where((b) => b.id != budgetId).toList(),
      );
    });
  }
}
```

### Writing Tests

1. **Repository Test**:
```dart
// test/repositories/budget_repository_test.dart
void main() {
  group('BudgetRepository Tests', () {
    late IBudgetRepository repository;
    late MockFirestoreService mockFirestore;

    setUp(() {
      mockFirestore = MockFirestoreService();
      repository = BudgetRepositoryImpl(mockFirestore);
    });

    test('should create budget successfully', () async {
      // Arrange
      final budget = BudgetFactory.create();
      when(() => mockFirestore.setDocument(any(), any()))
          .thenAnswer((_) async {});

      // Act
      await repository.createBudget(budget);

      // Assert
      verify(() => mockFirestore.setDocument(
        'users/${budget.userId}/budgets/${budget.id}',
        budget.toJson(),
      )).called(1);
    });
  });
}
```

2. **Widget Test**:
```dart
// test/screens/budget_list_screen_test.dart
void main() {
  group('BudgetListScreen Tests', () {
    testWidgets('displays budgets correctly', (tester) async {
      // Arrange
      final budgets = [BudgetFactory.create(), BudgetFactory.create()];

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const BudgetListScreen(),
          overrides: [
            userBudgetsProvider.overrideWith(
              (ref) => AsyncValue.data(budgets),
            ),
          ],
        ),
      );

      // Act
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(BudgetTile), findsNWidgets(2));
    });
  });
}
```

## Performance Considerations

### 1. Provider Optimization
- Use `autoDispose` for providers that don't need to persist
- Implement `family` providers for parameterized data
- Use `select` to watch specific parts of complex state
- Cache expensive computations with appropriate TTL

### 2. Firestore Optimization
- Use composite indexes for complex queries
- Implement pagination for large datasets
- Minimize document reads with efficient queries
- Use real-time listeners judiciously

### 3. UI Performance
- Use `const` constructors wherever possible
- Implement `RepaintBoundary` for expensive widgets
- Use `ListView.builder` for large lists
- Optimize image loading and caching

### 4. Memory Management
- Dispose of streams and controllers properly
- Use `ref.onDispose()` in providers for cleanup
- Monitor memory usage in development
- Implement proper error boundaries

## Security Considerations

### 1. Data Validation
- Validate all user inputs on client and server
- Use Firestore Security Rules for server-side validation
- Implement proper sanitization for user-generated content
- Use type-safe data models to prevent injection

### 2. Authentication Security
- Implement proper session management
- Use secure storage for sensitive data
- Validate authentication state on every request
- Implement proper logout and session cleanup

### 3. Firebase Security
- Use least-privilege principle in Security Rules
- Implement proper user data isolation
- Monitor and log security events
- Regular security rule testing and auditing

## Deployment and CI/CD

### 1. Build Configuration
- Separate Firebase configurations for environments
- Environment-specific build flavors
- Automated testing in CI pipeline
- Code quality checks and linting

### 2. Release Process
- Automated builds for different environments
- Comprehensive testing before deployment
- Staged rollouts for production releases
- Monitoring and rollback procedures

---

For more specific implementation details, refer to the individual documentation files in the `docs/` directory and the comprehensive code examples throughout the codebase.

## Repository Pattern Enforcement Examples

### ✅ CORRECT Implementation Patterns

#### Repository Implementation with Dependency Injection
```dart
class UserRepositoryImpl implements IUserRepository {
  final FirestoreService _firestoreService;
  final FirebaseAuth _firebaseAuth;

  UserRepositoryImpl(this._firestoreService, this._firebaseAuth);

  @override
  Future<UserProfile?> getUserProfile(String userId) async {
    final user = _firebaseAuth.currentUser; // ✅ Using injected dependency
    // Implementation...
  }
}
```

#### Service Abstraction for Cross-Cutting Concerns
```dart
class FirebaseConnectivityServiceImpl implements IFirebaseConnectivityService {
  final FirebaseAuth _firebaseAuth;
  final FirebaseFirestore _firestore;

  FirebaseConnectivityServiceImpl({
    required FirebaseAuth firebaseAuth,
    required FirebaseFirestore firestore,
  }) : _firebaseAuth = firebaseAuth, _firestore = firestore;

  @override
  Future<bool> areFirebaseServicesAvailable() async {
    // ✅ Using injected dependencies, not direct instance access
    return _firebaseAuth.currentUser != null;
  }
}
```

#### Presentation Layer Using Service Abstractions
```dart
class ProfileScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Future<void> _testFirebaseConnectivity() async {
      // ✅ Using service abstraction through provider
      final connectivityService = ref.read(firebaseConnectivityServiceProvider);
      final results = await connectivityService.testFirebaseConnectivity();
    }
  }
}
```

### ❌ FORBIDDEN Anti-Patterns

#### Direct Firebase Access in Business Logic
```dart
class SomeService {
  Future<void> badMethod() async {
    // ❌ NEVER do this - violates Repository pattern
    final user = FirebaseAuth.instance.currentUser;
    final doc = await FirebaseFirestore.instance.collection('users').doc(user!.uid).get();
  }
}
```

#### Presentation Layer with Direct Firebase Access
```dart
class BadWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Future<void> badConnectivityTest() async {
      // ❌ FORBIDDEN - UI should never access Firebase directly
      final isConnected = FirebaseAuth.instance.currentUser != null;
      final firestore = FirebaseFirestore.instance;
    }
  }
}
```

### Code Review Checklist

When reviewing code, ensure:
- [ ] No direct `FirebaseAuth.instance` or `FirebaseFirestore.instance` calls outside of provider definitions
- [ ] All repositories use dependency injection for Firebase services
- [ ] Presentation layer uses service abstractions or repositories through providers
- [ ] Service interfaces are defined for cross-cutting concerns
- [ ] Tests use proper mocking with provider overrides

## Related Documentation

- [Development Workflow](DEVELOPMENT_WORKFLOW.md)
- [Testing Guide](TESTING.md)
- [Authentication Documentation](AUTHENTICATION.md)
- [Remote Config Documentation](REMOTE_CONFIG.md)
- [Firebase Setup](ENVIRONMENT_SETUP.md)
- [Riverpod Rules](rules/riverpod.md)
- [Flutter Architecture Rules](rules/flutter_app_architecture.md)
