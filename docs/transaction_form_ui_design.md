# Transaction Input Form UI/UX Design Specification

**Document Version**: 1.3
**Date**: January 2025
**Task**: 10.1 - Design Transaction Input Form UI/UX
**Status**: ✅ Design Complete & Implementation Complete (Tasks 10.3, 10.4 & 10.5)

## Overview

This document defines the UI/UX design for the Transaction Input Form, supporting three transaction types: Income, Expense, and Transfer. The design follows Material 3 principles and BudApp's established design patterns.

## Design Principles

### Core UX Principles
- **Progressive Disclosure**: Show only relevant fields based on transaction type
- **Smart Defaults**: Pre-select primary account and current date
- **Visual Hierarchy**: Amount prominence → Accounts → Details
- **Real-time Validation**: Immediate feedback with clear error messages
- **Accessibility First**: WCAG AA compliance with screen reader support

### Material 3 Integration
- Uses BudApp's design tokens (`AppColors`, `AppSpacing`, `AppTypography`)
- Consistent with existing form patterns (`AuthFormField`)
- Follows established component patterns (`AccountTypeSelector`, `ParentCategorySelector`)

## Screen Layout Structure

### 1. App Bar
```
┌─────────────────────────────────────────┐
│ ← [Back]    Add Transaction    [Save] │
└─────────────────────────────────────────┘
```
- **Back Button**: Navigate to previous screen
- **Title**: "Add Transaction" 
- **Save Button**: Primary action (disabled until form is valid)

### 2. Transaction Type Selector
```
┌─────────────────────────────────────────┐
│  [Income]  [Expense]  [Transfer]       │
│     ●         ○          ○             │
└─────────────────────────────────────────┘
```
- **Component**: `SegmentedButton` (Material 3)
- **Default**: Income selected
- **Behavior**: Changes form fields dynamically
- **Colors**: 
  - Selected: `AppColors.primary`
  - Unselected: `AppColors.surfaceContainerHighest`

### 3. Amount Input Section
```
┌─────────────────────────────────────────┐
│              Amount *                   │
│  ┌─────────────────────────────────────┐ │
│  │ $ [    1,234.56    ] USD ▼         │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```
- **Large Text**: 24sp for amount input
- **Currency Prefix**: Dynamic based on selected currency
- **Currency Selector**: Dropdown with user's currencies
- **Validation**: Real-time formatting and validation
- **Keyboard**: Numeric with decimal support

### 4. Account Selection Section

#### Income Transaction
```
┌─────────────────────────────────────────┐
│              To Account *               │
│  ┌─────────────────────────────────────┐ │
│  │ 🏦 Chase Checking        ▼         │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

#### Expense Transaction  
```
┌─────────────────────────────────────────┐
│             From Account *              │
│  ┌─────────────────────────────────────┐ │
│  │ 🏦 Chase Checking        ▼         │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

#### Transfer Transaction
```
┌─────────────────────────────────────────┐
│             From Account *              │
│  ┌─────────────────────────────────────┐ │
│  │ 🏦 Chase Checking        ▼         │ │
│  └─────────────────────────────────────┘ │
│                                         │
│              To Account *               │
│  ┌─────────────────────────────────────┐ │
│  │ 💰 Savings Account       ▼         │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

- **Component**: Custom `AccountSelector` dropdown
- **Display**: Icon + Account Name + Balance (optional)
- **Default**: Primary account pre-selected
- **Validation**: Required fields marked with *

### 5. Category Selection Section
```
┌─────────────────────────────────────────┐
│              Category                   │
│  ┌─────────────────────────────────────┐ │
│  │ 🍔 Food & Dining         ▼         │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```
- **Visibility**: Hidden for Transfer transactions
- **Component**: `CategorySelector` with hierarchical support
- **Display**: Icon + Category Name
- **Behavior**: Filters by transaction type (income/expense)
- **Optional**: Not required for transactions

### 6. Transaction Details Section
```
┌─────────────────────────────────────────┐
│               Date *                    │
│  ┌─────────────────────────────────────┐ │
│  │ 📅 January 15, 2025      ▼         │ │
│  └─────────────────────────────────────┘ │
│                                         │
│             Description                 │
│  ┌─────────────────────────────────────┐ │
│  │ Grocery shopping at Whole Foods     │ │
│  └─────────────────────────────────────┘ │
│                                         │
│               Notes                     │
│  ┌─────────────────────────────────────┐ │
│  │ Weekly grocery run                  │ │
│  │                                     │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

- **Date**: Required, defaults to today
- **Description**: Optional, single line (max 100 chars)
- **Notes**: Optional, multi-line (max 500 chars)

## Component Specifications

### TransactionTypeSelector
```dart
Widget TransactionTypeSelector({
  required TransactionType selectedType,
  required ValueChanged<TransactionType> onChanged,
})
```
- **Implementation**: `SegmentedButton<TransactionType>`
- **Styling**: Material 3 with BudApp colors
- **Animation**: Smooth transition between selections

### AmountInputField
```dart
Widget AmountInputField({
  required TextEditingController controller,
  required String currencyCode,
  required ValueChanged<String> onCurrencyChanged,
  String? errorText,
})
```
- **Features**: Currency formatting, decimal support
- **Validation**: Positive numbers only, max 2 decimal places
- **Accessibility**: Semantic labels for screen readers

### AccountSelector
```dart
Widget AccountSelector({
  required String label,
  required List<Account> accounts,
  Account? selectedAccount,
  required ValueChanged<Account?> onChanged,
  String? errorText,
})
```
- **Display**: Account icon, name, and current balance
- **Filtering**: Active accounts only
- **Validation**: Required field validation

### CategorySelector
```dart
Widget CategorySelector({
  required CategoryType type,
  Category? selectedCategory,
  required ValueChanged<Category?> onChanged,
})
```
- **Features**: Hierarchical category display
- **Filtering**: By transaction type (income/expense)
- **Search**: Quick category search functionality

## Interaction Flows

### 1. Transaction Type Change
1. User taps different transaction type
2. Form fields animate to show/hide relevant sections
3. Account labels update ("From Account" vs "To Account")
4. Category section shows/hides based on type
5. Validation resets for changed fields

### 2. Amount Input
1. User taps amount field
2. Numeric keyboard appears
3. Real-time formatting as user types
4. Currency symbol updates based on selection
5. Validation feedback for invalid amounts

### 3. Account Selection
1. User taps account dropdown
2. Modal bottom sheet with account list
3. Accounts show icon, name, and balance
4. Selection updates form and closes modal
5. Transfer validation prevents same account selection

### 4. Form Submission
1. Real-time validation on all fields
2. Save button enables when form is valid
3. Loading state during submission
4. Success feedback and navigation
5. Error handling with specific messages

## Responsive Design

### Mobile Portrait (Primary)
- Single column layout
- Full-width form fields
- Comfortable touch targets (44dp minimum)
- Optimized for one-handed use

### Mobile Landscape
- Maintains single column for consistency
- Adjusted spacing for shorter height
- Keyboard-aware scrolling

### Tablet
- Centered form with max width constraint
- Larger touch targets and spacing
- Enhanced visual hierarchy

## Accessibility Requirements

### Screen Reader Support
- Semantic labels for all form fields
- State announcements for validation
- Clear focus indicators
- Logical tab order

### Visual Accessibility
- High contrast ratios (WCAG AA)
- Scalable text (up to 200%)
- Clear error messaging
- Color-independent information

### Motor Accessibility
- Minimum 44dp touch targets
- Generous spacing between elements
- Easy-to-reach primary actions
- Gesture alternatives

## Error States & Validation

### Field-Level Validation
- **Amount**: "Amount must be greater than 0"
- **Account**: "Please select an account"
- **Date**: "Please select a valid date"
- **Transfer**: "Source and destination accounts must be different"

### Form-Level Validation
- Real-time validation as user types
- Error messages below relevant fields
- Save button disabled until form is valid
- Clear error summary for accessibility

## Animation & Transitions

### Type Selector Animation
- 300ms ease-in-out transition
- Smooth color changes
- Content fade in/out for field changes

### Field Transitions
- 200ms slide animations for showing/hiding fields
- Staggered animations for multiple field changes
- Smooth height transitions

### Loading States
- Skeleton loading for account/category lists
- Progress indicators for form submission
- Smooth state transitions

## Implementation Notes

### File Structure
```
lib/features/transactions/presentation/
├── screens/
│   └── transaction_create_screen.dart
├── widgets/
│   ├── transaction_type_selector.dart
│   ├── amount_input_field.dart
│   ├── account_selector.dart
│   ├── category_selector.dart
│   └── transaction_form.dart
└── docs/
    └── transaction_form_ui_design.md (this file)
```

### Dependencies
- Existing `AuthFormField` pattern
- `AccountTypeSelector` component reference
- `ParentCategorySelector` component reference
- Material 3 `SegmentedButton`
- BudApp design tokens

### Testing Considerations
- Widget tests for each component
- Integration tests for form flows
- Accessibility testing with screen readers
- Visual regression tests for different states

---

## Implementation Status

### ✅ Completed (Tasks 10.3, 10.4 & 10.5 - January 2025)

All UI components have been successfully implemented following this design specification, including complete transfer transaction functionality:

#### Core Components Implemented
- **TransactionForm**: Main form widget with comprehensive validation and submission logic
- **TransactionTypeSelector**: SegmentedButton for income/expense/transfer selection
- **AmountInputField**: Currency-aware amount input with real-time formatting
- **AccountSelector**: Dynamic account selection with proper labeling and dual account support for transfers
- **CategorySelector**: Type-filtered category selection with hierarchical support
- **DatePickerField**: Date selection with Material 3 date picker
- **DescriptionField**: Single-line description input with character limits
- **NotesField**: Multi-line notes input with proper validation

#### Transfer Transaction Features
- **Dual Account Selection**: Conditional rendering of "From Account" and "To Account" selectors for transfer transactions
- **Transfer Validation**: Comprehensive validation ensuring source and destination accounts are different
- **State Management**: Extended `TransactionFormState` with `toAccountId` and `toAccountError` fields
- **Critical Bug Fixes**: Resolved copyWith method issues using sentinel value pattern for proper null handling
- **Localization**: Added transfer-specific validation messages ("sameAccountError", "destinationAccountRequired")

#### State Management
- **TransactionFormState**: Comprehensive form state management with Riverpod
- **Real-time Validation**: Field-level and form-level validation with user-friendly error messages
- **CRUD Providers**: Complete transaction creation, update, and deletion providers

#### Integration Features
- **Localization**: 40+ transaction-specific localized strings added to app_en.arb
- **Navigation**: TransactionCreateScreen with proper routing integration
- **Design System**: Full Material 3 compliance with BudApp design tokens
- **Accessibility**: WCAG AA compliance with screen reader support

#### Quality Metrics
- **Tests**: All 317 tests passing with comprehensive coverage including transfer transaction test suite
- **Code Quality**: Flutter analyze reports "No issues found!"
- **Architecture**: Follows established Riverpod + Repository patterns
- **Documentation**: Complete implementation matching design specifications
- **Transfer Testing**: Comprehensive test coverage for transfer transaction functionality with 6 test cases

**Status**: All transaction form UI components complete for income, expense, and transfer transactions. Ready for transaction list implementation and CRUD operations.
