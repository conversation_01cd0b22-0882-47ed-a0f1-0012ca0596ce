# Goal Data Schema

This document defines the comprehensive Firestore data model for financial goals in BudApp, including field specifications, validation rules, security considerations, and indexing strategy.

## Collection Structure

### Firestore Path
```
/users/{userId}/goals/{goalId}
```

**Rationale**: Goals are stored as a subcollection under each user document to ensure:
- **Data Isolation**: Users can only access their own goals
- **Security**: Firestore Security Rules can easily enforce user ownership
- **Performance**: Queries are automatically scoped to the user
- **Scalability**: Each user's goals are isolated, preventing collection size limits

## Goal Document Schema

### Core Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | String | ✅ | Unique goal identifier (matches document ID) |
| `userId` | String | ✅ | Owner user ID for data isolation |
| `name` | String | ✅ | Goal name/title (2-100 characters) |
| `description` | String? | ❌ | Optional description (max 500 characters) |
| `targetAmountCents` | int | ✅ | Target amount in cents (avoids floating point issues) |
| `currentAmountCents` | int | ✅ | Current progress amount in cents (denormalized for performance) |
| `targetDate` | DateTime? | ❌ | Optional target completion date |
| `isCompleted` | bool | ✅ | Completion status (for backward compatibility) |
| `colorHex` | String? | ❌ | Hex color code (#RRGGBB format) for UI customization |
| `iconName` | String? | ❌ | Icon identifier/name for UI display |
| `status` | GoalStatus | ✅ | Current goal status (active, paused, completed, cancelled) |
| `isActive` | bool | ✅ | Soft delete flag (default: true) |
| `schemaVersion` | int | ✅ | Schema version for migrations (default: 1) |
| `createdAt` | DateTime | ✅ | Creation timestamp |
| `updatedAt` | DateTime | ✅ | Last update timestamp |
| `metadata` | Map<String, dynamic> | ✅ | Additional metadata for extensibility (default: {}) |

### Enums

#### GoalStatus
```dart
enum GoalStatus {
  active,    // Goal is actively being tracked
  paused,    // Goal is temporarily paused
  completed, // Goal has been achieved
  cancelled, // Goal was cancelled
}
```

**JSON Values**: `"active"`, `"paused"`, `"completed"`, `"cancelled"`

## Data Model Implementation

### Freezed Model
```dart
@freezed
@JsonSerializable()
class Goal with _$Goal {
  const factory Goal({
    required String id,
    required String userId,
    required String name,
    String? description,
    required int targetAmountCents,
    @Default(0) int currentAmountCents,
    DateTime? targetDate,
    @Default(false) bool isCompleted,
    String? colorHex,
    String? iconName,
    @Default(GoalStatus.active) GoalStatus status,
    @Default(true) bool isActive,
    @Default(1) int schemaVersion,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default({}) Map<String, dynamic> metadata,
  }) = _Goal;
}
```

### Factory Methods

#### Create New Goal
```dart
Goal.create({
  required String userId,
  required String name,
  String? description,
  required int targetAmountCents,
  int currentAmountCents = 0,
  DateTime? targetDate,
  String? colorHex,
  String? iconName,
  GoalStatus status = GoalStatus.active,
})
```

#### From Firestore Document
```dart
Goal.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc)
```

#### From JSON
```dart
Goal.fromJson(Map<String, dynamic> json)
```

### Instance Methods

#### Progress Calculation
```dart
double get progressPercentage // Returns 0.0 to 1.0
int get remainingAmountCents  // Remaining amount to reach target
bool get isAchieved          // True if current >= target
```

#### Date Calculations
```dart
bool get hasDeadline         // True if targetDate is set
bool get isOverdue          // True if past target date and not completed
int? get daysRemaining      // Days until target date (null if no date)
int? get requiredDailySavingsCents // Required daily savings to meet target
```

#### Utility Methods
```dart
Goal updated()              // Returns copy with updated timestamp
Map<String, dynamic> toJson() // Convert to JSON for Firestore
```

## Validation Rules

### Business Logic Validation
```dart
extension GoalValidation on Goal {
  List<String> validate()    // Returns list of validation errors
  bool get isValid          // True if no validation errors
  String get statusString   // Human-readable status
  String get progressMessage // User-friendly progress message
}
```

### Validation Constraints

#### Name Validation
- **Required**: Cannot be null or empty
- **Length**: 2-100 characters after trimming
- **Characters**: Any valid Unicode characters

#### Description Validation
- **Optional**: Can be null
- **Length**: Maximum 500 characters if provided

#### Amount Validation
- **Target Amount**: Must be greater than 0
- **Current Amount**: Cannot be negative

#### Date Validation
- **Target Date**: Cannot be in the past (if provided)
- **Created/Updated**: Must be valid DateTime objects

#### Color Validation
- **Format**: Must match regex `^#[0-9A-Fa-f]{6}$` if provided
- **Example**: `#FF0000` for red

#### Icon Validation
- **Format**: Must match regex `^[a-zA-Z][a-zA-Z0-9_]*$` if provided
- **Example**: `savings_goal`, `vacation_fund`

## Indexing Strategy

### Recommended Firestore Indexes

#### Single Field Indexes (Automatic)
- `userId` (ascending)
- `status` (ascending)
- `isActive` (ascending)
- `createdAt` (ascending/descending)
- `updatedAt` (ascending/descending)
- `targetDate` (ascending/descending)

#### Composite Indexes (Manual Configuration)
```javascript
// Active goals by creation date
{
  collection: "goals",
  fields: [
    { fieldPath: "userId", order: "ASCENDING" },
    { fieldPath: "isActive", order: "ASCENDING" },
    { fieldPath: "createdAt", order: "DESCENDING" }
  ]
}

// Goals by status and target date
{
  collection: "goals",
  fields: [
    { fieldPath: "userId", order: "ASCENDING" },
    { fieldPath: "status", order: "ASCENDING" },
    { fieldPath: "targetDate", order: "ASCENDING" }
  ]
}

// Goals by completion status
{
  collection: "goals",
  fields: [
    { fieldPath: "userId", order: "ASCENDING" },
    { fieldPath: "isCompleted", order: "ASCENDING" },
    { fieldPath: "updatedAt", order: "DESCENDING" }
  ]
}
```

## Security Considerations

### Firestore Security Rules
```javascript
// Goals collection rules
match /users/{userId}/goals/{goalId} {
  allow read, write: if request.auth != null 
    && request.auth.uid == userId
    && isValidGoal(resource.data, userId, goalId);
}

function isValidGoal(data, userId, goalId) {
  return hasRequiredFields(data, [
    'id', 'userId', 'name', 'targetAmountCents', 
    'currentAmountCents', 'isCompleted', 'status',
    'isActive', 'schemaVersion', 'createdAt', 'updatedAt'
  ]) &&
  data.id == goalId &&
  data.userId == userId &&
  isValidStringLength(data.name, 2, 100) &&
  data.targetAmountCents is int &&
  data.targetAmountCents > 0 &&
  data.currentAmountCents is int &&
  data.currentAmountCents >= 0 &&
  data.isCompleted is bool &&
  isValidEnum(data.status, ['active', 'paused', 'completed', 'cancelled']) &&
  data.isActive is bool &&
  data.schemaVersion is int &&
  isValidTimestamp(data.createdAt) &&
  isValidTimestamp(data.updatedAt) &&
  isValidOptionalString(data, 'description', 0, 500) &&
  isValidOptionalHexColor(data, 'colorHex') &&
  isValidOptionalIconName(data, 'iconName');
}
```

### Data Privacy
- **User Isolation**: Goals are scoped to individual users
- **No Cross-User Access**: Security rules prevent access to other users' goals
- **Soft Deletion**: Use `isActive` flag instead of hard deletion for audit trails

## Migration Strategy

### Schema Versioning
- **Current Version**: 1
- **Backward Compatibility**: `fromJson` handles missing fields with defaults
- **Future Migrations**: Use `schemaVersion` field for data transformations

### Migration Considerations
- **Status Migration**: Handle transition from `isCompleted` boolean to `status` enum
- **Amount Migration**: Ensure all amounts are stored in cents
- **Date Migration**: Ensure all dates are properly formatted ISO strings

## Performance Optimization

### Denormalization
- **Current Amount**: Stored directly in goal document for fast progress calculations
- **Completion Status**: Both `isCompleted` and `status` for flexible querying

### Query Optimization
- **User Scoping**: All queries automatically scoped to user subcollection
- **Index Usage**: Composite indexes for common query patterns
- **Pagination**: Use `createdAt` or `updatedAt` for cursor-based pagination

## Usage Examples

### Creating a Goal
```dart
final goal = Goal.create(
  userId: 'user123',
  name: 'Vacation Fund',
  description: 'Save for summer vacation',
  targetAmountCents: 200000, // $2,000.00
  targetDate: DateTime(2024, 6, 1),
  colorHex: '#4CAF50',
  iconName: 'vacation',
);
```

### Progress Tracking
```dart
final progressPercent = goal.progressPercentage * 100;
final remaining = goal.remainingAmountCents / 100; // Convert to dollars
final daysLeft = goal.daysRemaining;
final dailySavings = goal.requiredDailySavingsCents?.let((cents) => cents / 100);
```

### Validation
```dart
final errors = goal.validate();
if (errors.isNotEmpty) {
  // Handle validation errors
  print('Validation errors: ${errors.join(', ')}');
}
```

This schema provides a robust foundation for financial goal tracking with proper validation, security, and performance considerations.
