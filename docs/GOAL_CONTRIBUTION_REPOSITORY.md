# Goal Contribution Repository

## Overview

The Goal Contribution Repository provides comprehensive data access layer for managing goal contributions in BudApp. This repository follows the established Repository pattern with Riverpod integration, offering full CRUD operations, real-time streams, analytics, and validation for goal contribution management.

## Repository Interface

### IGoalContributionRepository

The repository interface defines 24 methods covering all contribution management operations:

#### CRUD Operations
- `createContribution(GoalContribution contribution)` - Create new contribution
- `getContribution(String contributionId, String goalId)` - Get contribution by ID
- `updateContribution(GoalContribution contribution)` - Update existing contribution
- `deleteContribution(String contributionId, String goalId)` - Soft delete contribution

#### Real-Time Streams
- `watchContributionsForGoal(String goalId)` - Watch all contributions for a goal
- `watchActiveContributionsForGoal(String goalId)` - Watch active contributions only
- `watchAllUserContributions()` - Watch all user contributions across goals
- `watchContribution(String contributionId, String goalId)` - Watch specific contribution

#### Goal-Specific Operations
- `getContributionsForGoal(String goalId)` - Get all contributions for a goal
- `getActiveContributionsForGoal(String goalId)` - Get active contributions only
- `getTotalContributionAmountForGoal(String goalId)` - Calculate total amount
- `getContributionCountForGoal(String goalId)` - Count contributions for goal
- `getRecentContributions(String goalId, int limit)` - Get recent contributions

#### User-Level Operations
- `getAllUserContributions()` - Get all user contributions across goals
- `getActiveUserContributions()` - Get active user contributions only

#### Date-Based Filtering
- `getContributionsByDateRange(String goalId, DateTime start, DateTime end)` - Filter by date range
- `getTodaysContributions(String goalId)` - Get today's contributions
- `getThisWeeksContributions(String goalId)` - Get this week's contributions
- `getThisMonthsContributions(String goalId)` - Get this month's contributions

#### Analytics & Statistics
- `getContributionStatistics(String goalId)` - Calculate comprehensive statistics
- `validateContribution(GoalContribution contribution)` - Validate contribution data
- `checkGoalExists(String goalId)` - Verify goal existence

## Implementation

### GoalContributionRepositoryImpl

The concrete implementation follows established BudApp patterns:

#### Dependencies
- `FirestoreService` - Database operations
- `FirebaseAuth` - User authentication context

#### Key Features
- **User Context Management** - Automatic user ID resolution from Firebase Auth
- **Subcollection Path Management** - Handles `users/{userId}/goals/{goalId}/contributions` paths
- **Type-Safe Operations** - Full type safety with GoalContribution model
- **Error Handling** - Comprehensive error handling with meaningful messages
- **Soft Deletion** - Uses `isActive` flag for data integrity
- **Real-Time Updates** - Firestore snapshots for live data updates

#### Collection Path Pattern
```dart
String _getContributionsPath(String goalId) {
  final userId = _auth.currentUser?.uid;
  if (userId == null) throw Exception('User not authenticated');
  return 'users/$userId/goals/$goalId/contributions';
}
```

## Provider Integration

### Riverpod Provider Setup

```dart
// Repository provider
final goalContributionRepositoryProvider = Provider<IGoalContributionRepository>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  final auth = ref.watch(firebaseAuthProvider);
  return GoalContributionRepositoryImpl(firestoreService, auth);
});
```

### Usage in UI Components

```dart
class ContributionListScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final repository = ref.watch(goalContributionRepositoryProvider);
    
    return StreamBuilder<List<GoalContribution>>(
      stream: repository.watchActiveContributionsForGoal(goalId),
      builder: (context, snapshot) {
        // Handle stream data
      },
    );
  }
}
```

## Analytics and Statistics

### Contribution Statistics

The repository provides comprehensive statistics calculation:

```dart
class ContributionStatistics {
  final int totalContributions;
  final int totalAmountCents;
  final int averageAmountCents;
  final DateTime? firstContributionDate;
  final DateTime? lastContributionDate;
  final int contributionsThisMonth;
  final int contributionsThisWeek;
}
```

### Usage Example
```dart
final stats = await repository.getContributionStatistics(goalId);
print('Total contributed: \$${stats.totalAmountCents / 100}');
print('Average contribution: \$${stats.averageAmountCents / 100}');
```

## Validation System

### Built-in Validation

The repository integrates with GoalContribution model validation:

```dart
Future<void> validateContribution(GoalContribution contribution) async {
  final errors = contribution.validate();
  if (errors.isNotEmpty) {
    throw ValidationException('Validation failed: ${errors.join(', ')}');
  }
  
  // Additional business logic validation
  final goalExists = await checkGoalExists(contribution.goalId);
  if (!goalExists) {
    throw ValidationException('Goal does not exist');
  }
}
```

### Validation Rules
- Amount must be positive and ≤ $100M
- Date cannot be in future or older than 10 years
- Description must be ≤ 500 characters
- Goal must exist and belong to user

## Error Handling

### Exception Types
- `ValidationException` - Data validation failures
- `NotFoundException` - Contribution or goal not found
- `UnauthorizedException` - User not authenticated
- `FirestoreException` - Database operation failures

### Error Handling Pattern
```dart
try {
  await repository.createContribution(contribution);
} on ValidationException catch (e) {
  // Handle validation errors
} on NotFoundException catch (e) {
  // Handle not found errors
} catch (e) {
  // Handle general errors
}
```

## Testing

### Test Coverage

The repository includes comprehensive test suite:

- **22 Passing Tests** - Full CRUD operations, streams, analytics
- **2 Skipped Tests** - Due to fake_cloud_firestore date range limitations
- **100% Core Functionality** - All critical operations tested

### Test Categories
1. **CRUD Operations** - Create, read, update, delete contributions
2. **Goal-Specific Operations** - Goal-related queries and calculations
3. **User-Level Operations** - Cross-goal contribution queries
4. **Real-Time Streams** - Firestore snapshot testing
5. **Validation** - Data validation and business rules
6. **Error Handling** - Exception scenarios and edge cases

### Test Setup
```dart
void main() {
  late IGoalContributionRepository repository;
  late FakeFirebaseFirestore firestore;
  late MockFirebaseAuth auth;

  setUp(() {
    firestore = FakeFirebaseFirestore();
    auth = MockFirebaseAuth();
    final firestoreService = FirestoreService(firestore);
    repository = GoalContributionRepositoryImpl(firestoreService, auth);
  });
}
```

## Performance Considerations

### Efficient Querying
- Uses Firestore compound indexes for optimal performance
- Implements pagination for large contribution lists
- Leverages subcollection structure for goal-specific queries

### Recommended Indexes
```javascript
// Firestore indexes for optimal performance
users/{userId}/goals/{goalId}/contributions
- isActive + contributionDate (descending)
- contributionDate + amountCents (descending)
- isActive + amountCents (descending)
```

### Memory Management
- Streams automatically dispose when widgets unmount
- Efficient data structures for large contribution lists
- Lazy loading for historical data

## Integration with Goal Management

### Goal Progress Updates

The repository is designed to work with goal progress tracking:

```dart
// After creating contribution, update goal progress
await goalContributionRepository.createContribution(contribution);
await goalRepository.updateGoalProgress(goalId, contribution.amountCents);
```

### Denormalized Data Pattern

Future implementation will include automatic goal amount updates:

```dart
// ✅ IMPLEMENTED: Automatic goal currentAmountCents updates
Future<String> createContribution(String goalId, GoalContribution contribution) async {
  // Use batch operation to atomically create contribution and update goal
  final batch = _firestoreService.batch()
    ..set(contributionDoc, contribution.toJson())
    ..update(goalDoc, {
      'currentAmountCents': FieldValue.increment(contribution.amountCents),
      'updatedAt': DateTime.now().toIso8601String(),
    });

  await batch.commit();
  return contributionId;
}

Future<void> updateContribution(String goalId, String contributionId, GoalContribution contribution) async {
  // Calculate amount difference and update goal accordingly
  final currentContribution = await getContributionById(goalId, contributionId);
  final amountDifference = contribution.amountCents - currentContribution.amountCents;

  final batch = _firestoreService.batch()
    ..update(contributionDoc, contribution.toJson());

  if (amountDifference != 0) {
    batch.update(goalDoc, {
      'currentAmountCents': FieldValue.increment(amountDifference),
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  await batch.commit();
}

Future<void> deleteContribution(String goalId, String contributionId) async {
  // Get contribution amount before soft deletion
  final currentContribution = await getContributionById(goalId, contributionId);

  final batch = _firestoreService.batch()
    ..update(contributionDoc, {'isActive': false, 'updatedAt': DateTime.now().toIso8601String()});

  // Only subtract if contribution was active
  if (currentContribution.isActive) {
    batch.update(goalDoc, {
      'currentAmountCents': FieldValue.increment(-currentContribution.amountCents),
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  await batch.commit();
}
```

## Security

### Firestore Security Rules

The repository works with comprehensive security rules:

```javascript
match /users/{userId}/goals/{goalId}/contributions/{contributionId} {
  allow read, write: if request.auth != null 
    && request.auth.uid == userId
    && validateGoalContribution(resource.data);
}
```

### Data Isolation
- All operations scoped to authenticated user
- Contributions isolated within goal subcollections
- No cross-user data access possible

## Future Enhancements

### Planned Features
1. **Batch Operations** - Bulk contribution creation/updates
2. **Contribution Categories** - Categorize contribution sources
3. **Recurring Contributions** - Automated recurring contribution tracking
4. **Advanced Analytics** - Trend analysis and forecasting
5. **Export Functionality** - CSV/PDF export of contribution history

### Migration Support
- Schema versioning for future data model changes
- Backward compatibility for existing contributions
- Migration utilities for data transformation

## Best Practices

### Repository Usage
1. Always use repository interface, never concrete implementation
2. Handle exceptions appropriately in UI layer
3. Use streams for real-time updates
4. Validate data before repository operations
5. Leverage analytics methods for dashboard displays

### Performance Tips
1. Use pagination for large contribution lists
2. Implement proper stream disposal
3. Cache frequently accessed data
4. Use appropriate Firestore indexes
5. Batch operations when possible

This repository provides a robust, production-ready foundation for goal contribution management with comprehensive functionality, proper error handling, and seamless integration with BudApp's architectural patterns.
