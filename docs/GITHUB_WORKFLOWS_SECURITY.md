# GitHub Workflows Security & Branch Protection

## Overview

This document outlines the comprehensive security strategy for BudApp's GitHub workflows, implementing branch-based deployment with enhanced security measures following 2024 industry best practices.

## Branch Strategy

### Branch-Based Deployment Model

```
development → DEV environment (automatic)
main → STAGING environment (automatic)
production → PROD environment (manual only)
```

### Branch Protection Rules

#### Development Branch
- **Purpose**: Development environment builds
- **Protection Level**: Standard
- **Required Reviews**: 1
- **Required Status Checks**: 
  - `test` (comprehensive test suite)
  - `code-quality` (static analysis, formatting, documentation)
- **Dismiss Stale Reviews**: Yes
- **Enforce for Admins**: Yes

#### Main Branch (Staging)
- **Purpose**: Staging environment builds
- **Protection Level**: Enhanced
- **Required Reviews**: 1
- **Required Status Checks**:
  - `test` (comprehensive test suite)
  - `code-quality` (static analysis, formatting, documentation)
- **Dismiss Stale Reviews**: Yes
- **Enforce for Admins**: Yes
- **Additional**: Higher test coverage threshold (85%)

#### Production Branch
- **Purpose**: Production environment builds
- **Protection Level**: Maximum
- **Required Reviews**: 2
- **Required Status Checks**:
  - `test` (comprehensive test suite)
  - `code-quality` (static analysis, formatting, documentation)
  - `security-audit` (comprehensive security validation)
- **Dismiss Stale Reviews**: Yes
- **Enforce for Admins**: Yes
- **Additional**: 
  - Manual deployment only
  - Requires "PRODUCTION" confirmation
  - Maximum test coverage threshold (90%)

## Security Enhancements

### 1. Environment-Specific Keystores

Each environment uses separate keystores to ensure complete isolation:

```yaml
# Dev Environment
DEV_KEYSTORE_FILE: ${{ secrets.DEV_KEYSTORE_FILE }}
DEV_KEY_ALIAS: ${{ secrets.DEV_KEY_ALIAS }}
DEV_KEYSTORE_PASSWORD: ${{ secrets.DEV_KEYSTORE_PASSWORD }}
DEV_KEY_PASSWORD: ${{ secrets.DEV_KEY_PASSWORD }}

# Staging Environment
STAGING_KEYSTORE_FILE: ${{ secrets.STAGING_KEYSTORE_FILE }}
STAGING_KEY_ALIAS: ${{ secrets.STAGING_KEY_ALIAS }}
STAGING_KEYSTORE_PASSWORD: ${{ secrets.STAGING_KEYSTORE_PASSWORD }}
STAGING_KEY_PASSWORD: ${{ secrets.STAGING_KEY_PASSWORD }}

# Production Environment
PROD_KEYSTORE_FILE: ${{ secrets.PROD_KEYSTORE_FILE }}
PROD_KEY_ALIAS: ${{ secrets.PROD_KEY_ALIAS }}
PROD_KEYSTORE_PASSWORD: ${{ secrets.PROD_KEYSTORE_PASSWORD }}
PROD_KEY_PASSWORD: ${{ secrets.PROD_KEY_PASSWORD }}
```

### 2. Branch-Based Trigger Validation

Each workflow validates it's running on the correct branch:

```yaml
- name: Validate branch
  run: |
    if [ "${{ github.ref_name }}" != "production" ]; then
      echo "❌ ERROR: Production builds can only be triggered from the 'production' branch!"
      exit 1
    fi
```

### 3. Enhanced Security Auditing

#### Production Security Audit
- Comprehensive dependency vulnerability scanning
- Hardcoded secret detection with strict patterns
- Code complexity analysis
- Certificate validation

#### Build Signature Validation
- Jarsigner verification for all builds
- Debug key detection and rejection
- Package name validation
- Certificate chain verification

### 4. Environment-Specific Security Measures

#### Development Environment
- **Security Level**: Standard
- **Keystore**: Development-specific
- **Validation**: Basic signature verification
- **Artifact Retention**: 7 days

#### Staging Environment
- **Security Level**: Enhanced
- **Keystore**: Staging-specific
- **Validation**: Enhanced signature verification + package name validation
- **Artifact Retention**: 30 days
- **Additional**: Higher test coverage requirements

#### Production Environment
- **Security Level**: Maximum
- **Keystore**: Production-specific
- **Validation**: Comprehensive security audit + signature verification
- **Artifact Retention**: 365 days
- **Additional**: 
  - Manual confirmation required
  - Multi-stage validation
  - Comprehensive security audit
  - Certificate validity checks

## Workflow Security Features

### 1. Intelligent Caching Strategy

```yaml
- name: Cache Flutter dependencies
  uses: actions/cache@v4
  with:
    path: |
      ~/.pub-cache
      ~/.flutter
    key: ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-${{ hashFiles('**/pubspec.lock') }}
    restore-keys: |
      ${{ runner.os }}-flutter-${{ env.FLUTTER_VERSION }}-
      ${{ runner.os }}-flutter-
```

### 2. Multi-Stage Validation

1. **Pre-deployment Validation**
   - Branch validation
   - Confirmation validation
   - Deployment logging

2. **Comprehensive Testing**
   - Code formatting
   - Static analysis
   - Test suite execution
   - Coverage validation

3. **Security Audit**
   - Dependency vulnerability scanning
   - Hardcoded secret detection
   - Security pattern validation

4. **Build Process**
   - Environment-specific keystore setup
   - Signed build generation
   - Signature validation

5. **Final Security Check**
   - Artifact signature verification
   - Package name validation
   - Security compliance confirmation

### 3. Audit Logging

All production deployments include comprehensive audit logging:

```yaml
- name: Log deployment attempt
  run: |
    echo "🚨 PRODUCTION DEPLOYMENT INITIATED"
    echo "Timestamp: $(date -u)"
    echo "Branch: ${{ github.ref_name }}"
    echo "Commit: ${{ github.sha }}"
    echo "Actor: ${{ github.actor }}"
    echo "Build Type: ${{ github.event.inputs.build_type }}"
```

## Secret Management

### GitHub Secrets Configuration

Each environment requires the following secrets:

#### Development Secrets
- `DEV_KEYSTORE_FILE` (Base64 encoded keystore)
- `DEV_KEY_ALIAS` (Key alias)
- `DEV_KEYSTORE_PASSWORD` (Keystore password)
- `DEV_KEY_PASSWORD` (Key password)

#### Staging Secrets
- `STAGING_KEYSTORE_FILE` (Base64 encoded keystore)
- `STAGING_KEY_ALIAS` (Key alias)
- `STAGING_KEYSTORE_PASSWORD` (Keystore password)
- `STAGING_KEY_PASSWORD` (Key password)

#### Production Secrets
- `PROD_KEYSTORE_FILE` (Base64 encoded keystore)
- `PROD_KEY_ALIAS` (Key alias)
- `PROD_KEYSTORE_PASSWORD` (Keystore password)
- `PROD_KEY_PASSWORD` (Key password)

### Secret Security Best Practices

1. **Environment Isolation**: Each environment uses separate secrets
2. **Base64 Encoding**: Keystore files are Base64 encoded for secure storage
3. **No Hardcoding**: No secrets stored in code or configuration files
4. **Rotation**: Regular secret rotation schedule
5. **Access Control**: Limited access to production secrets

## Compliance & Monitoring

### Security Compliance

- **OWASP Top 10**: Addressed through comprehensive security auditing
- **Supply Chain Security**: Dependency vulnerability scanning
- **Code Security**: Static analysis and security pattern detection
- **Build Security**: Signed builds with certificate validation

### Monitoring & Alerting

- **Build Status**: Comprehensive build status reporting
- **Security Alerts**: Immediate alerts for security validation failures
- **Audit Trail**: Complete audit trail for all deployments
- **Performance Metrics**: Build performance and optimization tracking

## Usage Instructions

### Setting Up Branch Protection

1. **Run Branch Protection Setup**:
   ```bash
   # Validate current protection rules
   gh workflow run branch-protection-setup.yml -f action=validate
   
   # Setup protection rules
   gh workflow run branch-protection-setup.yml -f action=setup
   ```

2. **Verify Protection Rules**:
   - Check GitHub repository settings
   - Verify required status checks
   - Confirm review requirements

### Deploying to Environments

#### Development Deployment
```bash
# Automatic on push to development branch
git push origin development
```

#### Staging Deployment
```bash
# Automatic on push to main branch
git push origin main
```

#### Production Deployment
```bash
# Manual trigger only from production branch
gh workflow run prod-build.yml -f build_type=appbundle -f confirmation=PRODUCTION
```

## Emergency Procedures

### Security Incident Response

1. **Immediate Actions**:
   - Disable affected workflows
   - Rotate compromised secrets
   - Audit recent deployments

2. **Investigation**:
   - Review workflow execution logs
   - Analyze security audit results
   - Check artifact integrity

3. **Recovery**:
   - Update security measures
   - Re-enable workflows
   - Perform security validation

### Rollback Procedures

1. **Identify Issue**: Monitor deployment status and alerts
2. **Stop Deployment**: Cancel running workflows if necessary
3. **Assess Impact**: Review affected environments
4. **Rollback**: Deploy previous known-good version
5. **Investigate**: Analyze failure and implement fixes

## Continuous Improvement

### Regular Security Reviews

- **Monthly**: Review and update security patterns
- **Quarterly**: Audit branch protection rules
- **Annually**: Comprehensive security assessment

### Performance Optimization

- **Caching**: Continuously optimize caching strategies
- **Parallel Execution**: Identify opportunities for parallelization
- **Build Optimization**: Monitor and improve build times

### Documentation Updates

- Keep security documentation current
- Update procedures based on lessons learned
- Share security best practices with team

---

*This document is part of BudApp's comprehensive security strategy. For questions or updates, refer to the project maintainers.*