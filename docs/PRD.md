**Product Requirement Document: <PERSON><PERSON><PERSON> (MVP)**

*   **Document Version:** 1.3
*   **Date:** 25.06.2025
*   **Author:** [<PERSON><PERSON><PERSON>, with assistance from PM Expert AI]
*   **Status:** Complete

**Suggestion:** *Consider version controlling this document (e.g., in Git alongside your codebase) to track changes and maintain history.*

## 1. Introduction

### 1.1. Document Purpose
This document outlines the product requirements for the Minimum Viable Product (MVP) of BudApp, a personal finance management application. It serves as the central source of truth for the project team, detailing the product's objectives, target users, features, and technical considerations.

### 1.2. Product Vision
To become the go-to personal finance management application, empowering users worldwide to achieve financial well-being through intuitive tools, actionable insights, and a secure, reliable platform. BudApp will start as a focused mobile application and evolve into a comprehensive cross-platform financial companion.

### 1.3. Product Objectives (MVP)
*   Provide users with a simple, secure, and reliable mobile tool for fundamental personal finance management.
*   Enable users to track income and expenses accurately.
*   Allow users to create and monitor basic budgets.
*   Offer a foundation for tracking savings goals.
*   Deliver basic insights into spending habits.
*   Establish a secure and scalable platform for future feature development.
*   Validate the core value proposition with the target audience.

### 1.4. Core Value Proposition
An intuitive, secure, and comprehensive tool for tracking income and expenses, setting budgets, monitoring financial goals, and gaining insights into spending habits.

## 2. Target Audience
Primarily targeting tech-savvy individuals aged 22-35 who are in their early-to-mid career, seeking to transition from spreadsheets or simple note-taking to a structured budgeting tool for the first time. These users are typically comfortable with mobile applications, have regular income, and are looking for a modern solution to replace manual tracking methods or overly complex existing tools. They value simplicity and want to build healthy financial habits without the complexity of advanced investment tracking or business accounting features.

**Suggestion:** *As you gather user feedback post-MVP, consider creating more detailed user personas to further refine feature development and marketing efforts.*

## 3. User Stories (MVP)

User stories will be grouped by the major features of the MVP.

**Suggestion:** *For larger projects, consider managing user stories in a dedicated tool (e.g., Jira, Trello, Asana) for better tracking and sprint planning.*

**Implementation Update:** *Each user story should include 3-5 clear acceptance criteria following this format:*
```
Given [precondition]
When [action]
Then [expected result]
```

**⚠️ CRITICAL COMPLETION REQUIREMENT**: This PRD is not considered complete until ALL MVP user stories (US1.1 through USO1.2, excluding Phase 2 stories) have their acceptance criteria fully defined. Development should not begin until this requirement is met to ensure clear expectations and testable outcomes.

*Examples for MVP User Stories:*

**US5.3 Budget Progress Indicators - Example Acceptance Criteria:**
```
Given a user has a monthly budget of $500 for Groceries
And they have spent $400 in the current month
When they view the budget screen
Then they should see a progress bar showing 80% completion
And the progress bar should be colored orange (warning threshold)
And they should see "$400 of $500 spent" text below the progress bar

Given a user has exceeded their budget
When they view the budget screen  
Then the progress bar should be 100%+ and colored red
And they should see "Budget exceeded by $X" warning message
```

**US4.1 Income Transaction Recording - Example Acceptance Criteria:**
```
Given a user is on the transaction entry screen
When they select "Income" as transaction type
And enter amount "$1000", select "Salary" category, and "Checking" account
And tap "Save Transaction"
Then the transaction should be saved successfully
And the Checking account balance should increase by $1000
And they should see a confirmation message "Income transaction added"
And they should be redirected to the transaction list
```

*This format will be used for all user stories to ensure clear expectations and testable outcomes.*

### 3.1. User Authentication & Profile Management
*   **US1.1:** As a new user, I want to register for an account using my email and a secure password so that I can start using BudApp.
*   **US1.2:** As a new user, I want to register using my Google account so that I can sign up quickly and securely.
*   **US1.3:** As a new user, I want to register using my Apple account so that I can sign up quickly and securely.
*   **US1.4:** As a registered user, I want to log in with my email and password so that I can access my financial data.
*   **US1.5:** As a registered user, I want to log in with my Google account so that I can access my financial data.
*   **US1.6:** As a registered user, I want to log in with my Apple account so that I can access my financial data.
*   **US1.7:** As a registered user, I want to manage my profile (e.g., update username, change password) so that my account information is current and secure.
*   **US1.8:** As a user, I want my session to be managed securely (e.g., JWTs) so that my data is protected and I remain logged in across app uses.
*   **US1.9:** As a new user, I want to verify my email address so that I can secure my account and enable features like password recovery.
*   **US1.10:** As a user, I want a "Forgot Password" flow so that I can recover access to my account if I forget my credentials.
*   **US1.11:** As a potential user, I want to see an engaging feature tour or welcome screen before signing up so that I understand the app's value proposition.
*   **US1.12:** As a user, I want a clear path to permanently delete my account and all associated data, in compliance with my "Right to be Forgotten".
*   **US1.13:** As a registered user, I want to enable and use my device's biometric authentication (Face ID/Fingerprint) to log in quickly and securely.

### 3.2. Account Management
*   **US2.1:** As a user, I want to create multiple financial accounts (e.g., Checking, Savings, Credit Card, Cash) so that I can track all my finances in one place.
*   **US2.2:** As a user, I want to assign a type (e.g., Asset, Liability) to each account so that my financial overview is accurate.
*   **US2.3:** As a user, I want to set an initial balance for each account so that my current financial standing is correctly reflected from the start.
*   **US2.4:** As a user, I want to edit the details of an existing account (e.g., name, icon), but the account type (Asset/Liability) is locked after creation to preserve financial data integrity.
*   **US2.5:** As a user, I want to delete an account that's no longer needed, but the system should prevent deletion if the account has any transactions to maintain data integrity.
*   **US2.6:** As a user, I want to mark an account as "primary" or "favorite" so that I can easily access frequently used accounts.

### 3.3. Categories & Subcategories
*   **US3.1:** As a user, I want to see a predefined list of common income and expense categories (provided via Firebase Remote Config) so that I can quickly start categorizing my transactions.
*   **US3.2:** As a user, I want to create custom categories and subcategories so that I can organize my finances according to my specific needs.
*   **US3.3:** As a user, I want to edit existing custom categories and subcategories so that I can refine my organization over time.
*   **US3.4:** As a user, I want to delete custom categories and subcategories so that I can clean up my organization. **(MVP implementation: categories cannot be deleted if they have associated transactions or subcategories. Users must first reassign transactions and subcategories before deletion is allowed)**.
*   **US3.5:** As a user, I want to assign custom icons and colors to categories so that I can visually distinguish them and personalize my experience.

### 3.4. Transaction Management
*   **US4.1:** As a user, I want to record an income transaction with details like amount, date, time, category, and receiving account, so that my income is accurately tracked.
*   **US4.2:** As a user, I want to record an expense transaction with details like amount, date, time, category, and paying account, so that my spending is accurately tracked.
*   **US4.3:** As a user, I want to record a transfer transaction between my accounts (e.g., from Checking to Savings) so that my internal fund movements are accurately reflected.
*   **US4.4:** As a user, I want the transaction entry UI to be simple and intuitive, so that I don't need to understand complex financial concepts.
*   **US4.5:** As a user, I want to add optional notes and simple tags to my transactions so that I can add more context or search for them later.
*   **US4.6:** As a user, I want to edit existing transactions so that I can correct any mistakes.
*   **US4.7:** As a user, I want to delete transactions (understanding this will adjust account balances).

### 3.4.1. Tag Management
*   **US4T.1:** As a user, I want to create custom tags so that I can add flexible labels to my transactions for filtering and organization.
*   **US4T.2:** As a user, I want to edit existing tags so that I can refine my organization system over time.
*   **US4T.3:** As a user, I want to delete tags that I no longer use so that I can keep my tag list clean and relevant.
*   **US4T.4:** As a user, I want to view all my created tags in one place so that I can manage and select from them when tagging transactions.

### 3.5. Budgeting
*   **US5.1:** As a user, I want to set an overall monthly budget for my total expenses so that I can manage my overall spending.
*   **US5.2:** As a user, I want to set a monthly budget **for a single category** (e.g., Groceries) so that I can control my spending in that area.
*   **US5.3:** As a user, I want to see visual indicators (e.g., progress bars) of my budget progress so that I can quickly understand how much I've spent versus my budget.
*   **US5.4:** As a user, I want to edit the amount of an existing budget so that I can adjust my financial plan.
*   **US5.5:** As a user, I want to delete a budget I no longer need so I can keep my budget overview clean.

### 3.6. Financial Goal Tracking
*   **US6.1:** As a user, I want to create savings goals (e.g., "Vacation Fund," "New Laptop") with a target amount so that I can work towards specific financial objectives.
*   **US6.2:** As a user, I want to track my contributions towards my savings goals so that I can see my progress.
*   **US6.3:** As a user, I want to see visual indicators of my progress towards achieving my goals.

### 3.7. Basic Reporting & Insights
*   **US7.1:** As a user, I want to see an overview dashboard with my current account balances, recent transactions, and budget summaries so that I can get a quick snapshot of my financial health.
*   **US7.2:** As a user, I want to view simple reports of my expenses and income by category and time period (e.g., a pie chart of this month's spending) so that I can understand my financial habits.
*   **US7.3:** As a user, I want to select different months to view historical data in my transaction lists and reports so that I can analyze my financial patterns over time.

### 3.8. Notifications & Alerts
*   **US8.1:** As a user, I want to receive in-app alerts when I'm about to exceed or have exceeded my budget for a category (e.g., "You've spent 80% of your Groceries budget") so that I can adjust my spending.
*   **US8.2:** As a user, I want to receive in-app low balance alerts for my accounts (configurable threshold) so that I can avoid overdrafts or insufficient funds.
*   **US8.3:** As a user, I want these alerts to be visible the next time I open the app, ensuring I don't miss important budget or balance warnings. **(MVP Note: Push notifications are planned for Phase 3 - server-side intelligence)**

### 3.9. Multi-Device Sync & Basic Offline Support
*   **US9.1:** As a user with multiple devices (e.g., phone and tablet), I want my financial data to be synchronized across all devices linked to my account so that I always see the latest information.
*   **US9.2:** As a user, I want to be able to view my data and record new transactions even when I'm offline, so that the app is usable without constant internet connectivity.
*   **US9.3:** As a user, I want my offline changes to sync automatically when connectivity is restored.

### 3.10. Subscription Management
*   **US10.1:** As a user, I want to be able to use the core features of the app for free so that I can evaluate its usefulness.
*   **US10.2:** As a user, I want the option to subscribe to a premium tier to unlock advanced features so that I can get more value from the app.
*   **US10.3:** As a user, I want to be able_ (if offered)_ to purchase a lifetime premium option.
*   **US10.4:** As a user, I want the in-app purchase process for subscriptions to be secure and straightforward.

### 3.11. Settings
*   **US11.1:** As a user, I want to select my preferred currency during first-time setup and be able to change it later in settings so that amounts are displayed correctly and the app remains usable if my financial context changes. **(MVP Note: Changing the base currency does NOT convert historical amounts - it only changes the currency symbol displayed. A strong warning will explain that this could lead to misleading reports. True multi-currency conversion is part of the post-MVP "Multi-Currency Support" feature.)**
*   **US11.2:** As a user, I want to set my preferred date and time formats so that information is displayed in a way I understand.
*   **US11.3:** As a user, I want to manage my notification preferences so that I only receive alerts I find useful.
*   **US11.4:** As a user, I want an option to export my data (JSON file of all my transactions, accounts, and categories for a selectable date range) so that I have a personal backup or can use it elsewhere.
*   **US11.5:** As a user, I want to toggle between Dark Mode and Light Mode so that I can choose the appearance that is most comfortable for my eyes.
*   **US11.6:** As a user, I want a "Secure Mode" option to mask my balances on screen so that I can maintain privacy in public places.

### 3.12. Onboarding Flow
*   **USO1.1:** As a new user, after I first log in, I want to be guided through creating my first financial account (or accept a default "Cash" account) and logging my first transaction so that I immediately see value.
*   **USO1.2:** As a new user transitioning from a spreadsheet, I want to import my transaction history from a CSV file so that I can bring my historical data into BudApp without manual entry.

### 3.13. Phase 2 User Stories (Post-MVP)
*   **US4.8:** As a user, I want to set up recurring transactions (daily, weekly, monthly, yearly) for regular income or expenses (e.g., salary, rent) so that I don't have to enter them manually each time.

## 4. Product Features (MVP)

This section details the features planned for the Minimum Viable Product.

<!-- Acceptance-criteria Gherkin syntax will be added to each user story during backlog grooming rather than inline in this PRD to keep the document concise. -->

### 4.1. User Authentication & Profile Management
*   **Description**: Secure user registration (email/password, OAuth 2.0 for Google & Apple), login with biometric authentication support, profile management (username, password change, email verification), and session management using JWTs.
*   **Key Details**: Strong password policies, email verification flow, secure OAuth integration, biometric authentication (Face ID/Touch ID/Fingerprint) for quick secure login.
*   **Corresponds to User Stories**: US1.1 - US1.13.

### 4.2. Account Management
*   **Description**: Ability to create, edit, and delete multiple financial accounts (e.g., Checking, Savings, Credit Card, Cash). Users can assign account types (Asset, Liability), set initial balances, and mark accounts as "primary" or "favorite."
*   **Key Details**: 
    *   Clear distinction between asset and liability accounts
    *   Initial balance setting is crucial for accurate starting points
    *   Accounts cannot be deleted if they have associated transactions - referential integrity enforced
*   **Corresponds to User Stories**: US2.1 - US2.6.

### 4.3. Categories & Subcategories
*   **Description**: A predefined list of common income/expense categories will be provided via Firebase Remote Config, allowing for dynamic updates and localization without app updates. Users can create, edit, and delete custom categories and subcategories, assigning custom icons and colors.
*   **Key Details**: 
    *   **Remote Config Categories**: Predefined categories delivered via Remote Config for instant updates and A/B testing
    *   **User Customization**: Full flexibility for users to tailor categories to their specific needs
    *   **Onboarding Enhancement**: Predefined list reduces friction during initial setup
    *   **Localization Ready**: Categories can be updated per market without app releases
*   **Corresponds to User Stories**: US3.1 - US3.5.

### 4.4. Transaction Management
*   **Description**: Record income, expenses, and transfers with simple editing capabilities. The system maintains data integrity through straightforward transaction documents and client-side balance calculations while providing an intuitive UI experience. Transactions include fields for amount, date, time, category, account, optional notes, and simple tags.
*   **Key Details**:
    *   **Simple Data Model**: Each transaction stored as a single document with all necessary fields
    *   **Transaction Types**: Income, Expense, and Transfer transactions with clear type field
    *   **Intuitive UI**: Clean interface for entering and editing transactions
    *   **Direct Editing**: Users can modify any transaction field directly - it's their personal data
    *   **Balance Calculation**: Account balances computed by aggregating transaction amounts
    *   **State Synchronization**: Real-time listeners ensure immediate UI updates across devices
    *   **Hard Delete**: Transactions can be permanently deleted - no soft delete complexity
    *   **Referential Integrity**: Accounts/categories cannot be deleted if they have related transactions
    *   **Categories vs. Tags**: Categories for budgeting/reporting structure; tags for flexible filtering
    *   **Transfer Handling**: Transfers excluded from income/expense reports to prevent double-counting
    *   **Recurring Logic**: Deferred to Phase 2 - MVP users enter transactions manually
*   **Corresponds to User Stories**: US4.1 - US4.7, US4T.1 - US4T.4.

### 4.5. Budgeting
*   **Description**: Users can set monthly budgets for overall income/expenses and for specific categories. Visual indicators (e.g., progress bars) will show budget progress.
*   **Key Details**: 
    *   Focus on simple, monthly budgeting for MVP.
    *   **Budget Period**: For the MVP, a 'monthly' budget strictly corresponds to the **calendar month** (e.g., July 1st to July 31st). The ability to define custom budget start/end dates is a post-MVP consideration.
    *   **Budget Types**: Budgets must be defined as either an 'Expense' budget (a spending limit) or an 'Income' budget (an earning target). 
    *   **Overall vs. Category Budgets**: Users can create an overall expense/income budget (covering all transactions of that type) or a budget for a specific category.
    *   **Budget Hierarchy Roll-up**: When a budget is set on a parent category, spending in all of its sub-categories counts toward that budget consumption automatically (e.g., "Gas" and "Public Transit" roll into the "Transportation" budget).
    *   **Overlapping Budget Prevention**: To avoid user confusion, the MVP will prohibit creating a budget for a sub-category if its parent category already has a budget. The UI will disable the budget creation option for sub-categories in this case.
    *   **Budget Deletion**: Users can delete existing budgets. This action is non-destructive to past transaction data and simply removes the budget tracking.
    *   **Budget Management Approach**: The application uses an edit-only approach for budget management. Budget creation functionality has been removed in favor of editing existing budgets through an intuitive edit mode interface.
*   **Mid-Month Budget Logic**: A monthly budget applies to all transactions within the calendar month, regardless of when the budget was created or edited. If a user creates a budget on June 20th, all expenses from June 1st onwards will count towards it. Similarly, changing a budget amount mid-month affects the evaluation of all transactions within that month.
*   **Corresponds to User Stories**: US5.1 - US5.5.

### 4.6. Financial Goal Tracking
*   **Description**: Users can create savings goals (e.g., "Vacation Fund") with target amounts, track contributions, and see visual progress.
*   **Key Details**: Simple goal creation and manual contribution tracking for MVP.
*   **Contribution Method**: For the MVP, contributions are added through a dedicated manual action (e.g., a "Contribute" button on the goal screen). Automatically linking contributions to transfer transactions is deferred to a post-MVP release.
*   **Goal Lifecycle**: Once a goal's contributions reach its target amount, it will be marked as 'Completed' with a clear visual indicator. Completed goals will appear in a separate list or section on the Goals screen. For the MVP, completed goals cannot be edited. **Goals can be deleted by the user. Deleting a goal will also delete its associated manual contribution history. Alternatively, users can delete individual contribution transactions, which will adjust the goal's progress accordingly.** This gives users full control over their financial data.
*   **Known UX Limitation**: Users may intuitively expect that transferring money to a savings account could automatically contribute to a goal. The manual contribution model creates a disconnect and requires double entry (log the transfer, then log the contribution). This is a deliberate MVP trade-off for development speed but may generate user feedback requests.
*   **Corresponds to User Stories**: US6.1 - US6.3.

### 4.7. Basic Reporting & Insights
*   **Description**: An overview dashboard showing current balances, recent transactions, and budget summaries. Simple expense/income reports by category/period (e.g., pie chart of spending) with month selection capability.
*   **Key Details**: 
    *   Focus on providing immediately useful, easy-to-understand visual summaries.
    *   **Time Period Selection**: Users can select any month to view historical transaction lists and reports. The current calendar month is selected by default when opening the app.
    *   **Consistent Month Context**: The selected month applies consistently across transaction lists, spending reports, income reports, and budget progress views.
    *   **Navigation**: Month selector should be easily accessible (e.g., dropdown or month picker) on relevant screens without disrupting the primary user flow.
    *   **Data Availability**: Historical data is available for all months where the user has recorded transactions. Empty months display appropriate empty states.
    *   **Future Scope**: Custom date ranges spanning multiple months remain a post-MVP feature.
*   **Firebase Implementation Strategy**:
    *   **Client-Side Aggregation**: Perform simple calculations (account balances, category totals) on the client using cached data
    *   **Materialized Views**: Pre-compute monthly spending summaries in separate Firestore collections for performance
    *   **Cost-Effective Queries**: Use composite indexes and denormalized data to minimize document reads
    *   **Progressive Data Loading**: Load essential dashboard data first, then fetch detailed reports on demand
    *   **Caching Strategy**: Cache computed report data with appropriate TTL to reduce repeated calculations
*   **Corresponds to User Stories**: US7.1 - US7.3.

### 4.8. Notifications & Alerts
*   **Description**: In-app alert system for budget threshold alerts (e.g., "You've spent 80% of your Groceries budget") and configurable low balance alerts. Alerts are displayed when users open the app and when crossing thresholds during transaction entry.
*   **Key Details**: For MVP, alerts are calculated and displayed client-side. Push notifications will be added in Phase 3 with server-side intelligence.
*   **Corresponds to User Stories**: US8.1 - US8.3.

### 4.9. Multi-Device Sync & Basic Offline Support
*   **Description**: Data synchronized across multiple devices linked to the same user account. Ability to view data and record transactions offline, with changes syncing when connectivity is restored.
*   **Key Details**: Last-Writer-Wins (LWW) conflict resolution policy will be used, based on server-authoritative `updated_at` timestamps.
*   **Corresponds to User Stories**: US9.1 - US9.3. (Refer to Section 6.3.2 for detailed sync strategy).

### 4.10. Subscription Management
*   **Description**: Integration with RevenueCat for managing subscriptions with a free tier and premium tier structure. For detailed premium feature definitions, see Section 4.13.
*   **Key Details**: Secure in-app purchase flow. Clear distinction of free vs. premium tiers.
*   **Free Tier Philosophy**: The free tier is designed to be a perpetually useful tool for users with the simplest financial needs (e.g., tracking a single checking account and cash). The limitations are intended to create a natural upgrade path for users who begin to manage more complex finances (e.g., adding credit cards, savings accounts, or detailed custom budgets), at which point they have already experienced the core value of the app.
*   **Corresponds to User Stories**: US10.1 - US10.4.

### 4.11. Settings
*   **Description**: User preferences for currency selection (default to locale, allow override), date/time format, notification controls, data export (JSON), Dark Mode/Light Mode toggle, and "Secure Mode" (mask balances).
*   **Key Details**: 
    *   Provide essential customization and accessibility options.
    *   **Currency Management**: While the MVP UI supports displaying and calculating in one base currency at a time, all transactions are stored with currency codes in the backend to enable a smoother transition to multi-currency support in future releases.
*   **Corresponds to User Stories**: US11.1 - US11.6.

### 4.12. Data Import & Onboarding
*   **Description**: Simple CSV import functionality to help users transitioning from spreadsheets bring their historical transaction data into BudApp.
*   **Key Details**: 
    *   Basic CSV parser with column mapping (Date, Description, Amount, Category)
    *   Error handling for malformed data with clear feedback
    *   Import validation and preview before final import
    *   Transactions imported into user's selected account
*   **Column Mapping Interface**:
    *   **File Upload**: User selects CSV file from device storage
    *   **Column Detection**: App automatically detects common column names (Date, Amount, Description, Category)
    *   **Manual Mapping**: Dropdown selectors for each required field mapping:
        *   Date Column (required) - supports multiple date formats
        *   Amount Column (required) - handles positive/negative values
        *   Description Column (required) - transaction description/merchant
        *   Category Column (optional) - maps to existing categories or creates new ones
    *   **Account Selection**: User selects which account to import transactions into
*   **Validation & Error Handling**:
    *   **File Size Limits**: Maximum 5MB file size, up to 10,000 transactions per import
    *   **Row-Level Validation**: Clear error messages for invalid data:
        *   "Row 15: Invalid date format 'Feb 30, 2024'. Please use MM/DD/YYYY format."
        *   "Row 23: Amount '$1,500.00' contains invalid characters. Use numbers only."
        *   "Row 8: Description field is empty. Please provide a transaction description."
    *   **Error Report**: Summary of all validation errors with row numbers and suggested fixes
    *   **Partial Import Support**: User can choose to import valid rows and fix errors separately
*   **Import Preview & Confirmation**:
    *   **Preview Table**: Shows first 10 rows of parsed data with mapped columns
    *   **Import Summary**: "Found 95 valid transactions out of 100 rows. 5 rows have errors."
    *   **Category Handling**: Preview shows which categories will be created vs. matched to existing
    *   **Confirmation Dialog**: "Import 95 transactions into Checking Account? This action cannot be undone."
    *   **Progress Indicator**: Shows import progress for large files
*   **Post-Import Experience**:
    *   **Success Message**: "Successfully imported 95 transactions"
    *   **Navigation**: Direct link to transaction list filtered to imported date range
    *   **Cleanup Options**: Suggestion to review and categorize any uncategorized transactions
*   **Corresponds to User Stories**: USO1.2.

### 4.13. Premium Features (Framework in MVP)
The following features define the **Premium Tier**. The in-app purchase framework will ship in the MVP, but some of these may launch progressively.

*   **Day-One Premium Features**:
    *   **Increased Account Limit**: Up to **5** accounts (Free tier: 2).
    *   **Unlimited Custom Categories**: Free tier is limited to **3** user-created custom categories in addition to the predefined set.
*   **Planned Post-Launch Premium Features**:
    *   **Category Spend Analysis Report** (month-to-date trends).

**Firebase Remote Config Integration**:
*   Premium features are controlled server-side via Firebase Remote Config feature flags
*   Configuration keys:
    *   `max_accounts_free`: Maximum accounts for free tier (default: 2)
    *   `max_accounts_premium`: Maximum accounts for premium tier (default: 5)
    *   `max_custom_categories_free`: Custom category limit for free tier (default: 3)
    *   `premium_features_enabled`: Array of enabled premium features per environment
*   **Real-time Feature Control**: Premium features can be enabled/disabled instantly without app updates
*   **A/B Testing Ready**: Remote Config supports user segmentation for testing different premium limits
*   **Graceful Degradation**: App handles Remote Config fetch failures by falling back to hardcoded defaults

## 5. Design and UX Considerations

*   **Overall Philosophy**: Intuitive, clean, and user-friendly interface. The application should be easy to navigate and understand, even for users new to budgeting apps.
*   **Onboarding**: Implement a minimal user onboarding process using a progressive disclosure pattern, guiding users through initial setup (e.g., adding first account, first transaction) step-by-step.
*   **Visual Design**: Support both Dark Mode and Light Mode. Use clear visual hierarchy and consistent design language (leveraging Flutter's Material/Cupertino widgets or a chosen Flutter component library).
*   **Accessibility**: Meet specific WCAG AA requirements for mobile accessibility:
    *   **Minimum Tap Targets**: All interactive elements must have a minimum tap target of 44x44 points
    *   **Screen Reader Support**: The app must be fully navigable and usable with VoiceOver (iOS) and TalkBack (Android)
    *   **Color Contrast**: All text must meet a minimum color contrast ratio of 4.5:1 against its background
    *   **Focus Management**: Clear visual focus indicators and logical navigation order
    *   **Text Sizing**: Support for iOS Dynamic Type and Android font scaling up to 200%
    *   **Alternative Text**: All images, icons, and graphics must have descriptive alternative text
    *   **Semantic Labels**: All form fields and interactive elements must have clear semantic labels
*   **Feedback & Responsiveness**: The app should provide immediate feedback for user actions. UI should be responsive and performant.
*   **Secure Mode**: A toggle to mask balance values and other sensitive financial figures on screen for privacy in public.
*   **Error Handling**: User-friendly error messages that guide the user on how to resolve issues.
*   **App State Restoration**: Save draft states for incomplete user actions. If the user is creating a transaction and leaves the app (backgrounded or killed by OS), the entered data will be saved as a draft. Upon returning to the app, the user will be prompted to continue or discard the draft. This applies to transaction creation, budget setup, and goal creation flows.
*   **Visual Transaction Differentiation**: The transaction list UI should use clear visual cues (e.g., distinct icons, color-coded amounts like green for income and red for expenses) to help users instantly distinguish between income, expense, and transfer transactions at a glance.
*   **Simple Edit Flow**: Provide low-friction "Edit" action where users can modify any transaction field directly. When a user taps 'Edit' on a transaction, they are presented with the standard transaction entry form, pre-filled with the current data. Upon saving, the app shows a brief confirmation like 'Transaction updated.' Changes sync across devices in real-time via Firestore listeners.
*   **Onboarding Balance Flexibility**: During first-time account creation the "Initial Balance" field is optional; the user can skip and edit later to reduce friction.
*   **Empty State Design**: All primary screens (Budgets, Goals, Reports, Transactions) must have designed empty states when no data exists. These states should be encouraging and provide clear calls-to-action (e.g., "Create Your First Budget" button with explanatory text). Month-specific empty states should acknowledge the selected time period (e.g., "No transactions in March 2024").
*   **Error Handling Standards**: Consistent, user-friendly error messaging across all scenarios:
    *   **Network/Offline Error**: "You appear to be offline. Your changes have been saved and will sync when you reconnect."
    *   **Validation Error**: "Please enter a valid amount."
    *   **Permission Error**: "You do not have permission to perform this action."
    *   **Server Error**: "Something went wrong on our end. Please try again in a few moments."
    *   **Data Import Error**: "We couldn't import some rows from your CSV file. Please check the format and try again."

#### 5.X Deletion Constraints & User Guidance
*   **Referential Integrity UI**: When users attempt to delete accounts, categories, or goals with related transactions, show clear error messages explaining what's preventing deletion.
*   **Helpful Guidance**: Display the number of related transactions and provide easy navigation to view/manage them (e.g., "This account has 15 transactions. View transactions →").
*   **Guided Re-assignment Flow**: When deletion is blocked, provide streamlined re-assignment options:
    1. **Deletion Attempt**: User taps delete on category/account with transactions
    2. **Block Modal**: "Cannot delete 'Groceries' because it has 42 transactions."
    3. **Action Options**: 
        *   "Re-assign Transactions" (primary action)
        *   "View Transactions First" (secondary)
        *   "Cancel" (dismiss)
    4. **Re-assignment Interface**: If user chooses re-assignment:
        *   **Destination Selection**: "Move all 42 'Groceries' transactions to:" with category/account picker
        *   **Preview**: "This will move transactions from Jan 2024 to March 2024"
        *   **Confirmation**: "Move 42 transactions from 'Groceries' to 'Food & Dining'?"
        *   **Progress**: Show progress bar for bulk operations
        *   **Success**: "Successfully moved 42 transactions. 'Groceries' category has been deleted."
*   **UX Friction Acknowledgment**: The rules preventing deletion of accounts/categories with transactions are good for data integrity but create potential user frustration. A user wanting to restructure their categories may face a tedious process of manually re-assigning hundreds of transactions before deleting one old category.
*   **MVP Mitigation**: The guided re-assignment flow above provides immediate relief for basic cases. Post-MVP roadmap includes a "**Bulk Re-categorization Tool**" for advanced category restructuring workflows.
*   **Confirmation Dialogs**: Always confirm deletion with clear warnings about permanent data loss.

#### 5.Y Device and Orientation Scope
*   **Primary Target**: The MVP UI is optimised for portrait-mode phones.
*   **Tablets & Landscape**: The application will remain functional on tablets and in landscape, but dedicated responsive layouts are deferred to a post-MVP enhancement.

#### 5.Z Lifecycle of Account Deletion
*   **Self-Serve Deletion**: A "Delete Account" option is available in app settings.
*   **Subscription Handling**: If the user has an active subscription, the app displays a warning instructing them to cancel via **the platform where they originally subscribed** (e.g., their device's App Store / Google Play account), with a **required deep link** that takes the user directly to their subscription management page (e.g., `itms-apps://apps.apple.com/account/subscriptions` for iOS).
*   **Confirmation**: The flow requires the user to confirm by typing "DELETE" before erasure proceeds.
*   **MVP Deletion Process**: User-facing deletion is immediate from client perspective. **COMPLIANCE RISK MITIGATION**: A basic automated Cloud Function for GDPR-compliant data erasure (including user document deletion across all collections) should be implemented as part of MVP release criteria to mitigate legal and reputational risk from day one. Manual backup cleanup can remain post-MVP, but automated Firestore deletion must be available at launch.
*   **Grace Period (Post-MVP)**: A 14-day deactivation phase may be added post-MVP.

#### 5.AA Designed Empty States
*   **Requirement**: All primary screens (e.g., Budgets, Goals, Reports) must have a designed "empty state" when no data exists for them yet.
*   **Design Principles**: These states should be encouraging and provide a clear call-to-action (e.g., a "Create Your First Budget" button with a brief explanation of the feature's value).
*   **Purpose**: Critical for user guidance and continued engagement beyond initial onboarding, preventing users from encountering uninviting blank screens.
*   **Month-Specific Empty States**: When users select a month with no transaction data, the empty state should acknowledge the selected time period (e.g., "No transactions in March 2024" rather than generic "No transactions").

#### 5.BB Behavior of Primary/Favorite Account
*   **Default Account Selection**: The account marked as "primary" or "favorite" will be used as the **default pre-selected account** in the "From Account" field when creating a new expense transaction.
*   **Fallback Behavior**: If no primary account is set, the "From Account" field will default to the most recently used account for a transaction. If there are no previous transactions, the field will be left blank, requiring the user to select one.
*   **User Benefit**: This reduces the number of taps required for the most common user action, improving efficiency for users who primarily use one account for daily expenses.

#### 5.CC Month Selector Design & Behavior
*   **Placement**: Month selector should be prominently placed at the top of transaction list and report screens, clearly indicating the current view context.
*   **Default Behavior**: Always defaults to the current calendar month when the user opens the app or navigates to a new session.
*   **First-Use Default Behavior**: For a new user's first session, after they log their first transaction(s), the reports screen should default to the most recent month that contains data, rather than the current calendar month, to immediately showcase the value of their data entry.
*   **State Persistence**: The selected month persists within the current app session as users navigate between different screens (transactions, reports, budgets).
*   **Visual Feedback**: Clearly indicate the selected month and provide intuitive navigation (e.g., prev/next arrows, month picker dropdown). The month picker UI should visually distinguish or highlight months that contain transaction data to help users navigate their history more easily.
*   **Performance**: Month changes should be fast and responsive, with smooth transitions between different month views.
*   **Context Awareness**: The selected month governs the data shown on historical screens like transaction lists and reports.
*   **Dashboard Balance Behavior**: The main dashboard displays a combination of live and month-specific data:
    *   **Always Live (Real-Time)**: Account balances, total net worth, individual account balances on the Accounts screen
    *   **Month-Specific (Based on Selected Month)**: Budget progress indicators, monthly spending summaries, category breakdowns, transaction counts
    *   **Budget Screens**: Display progress based on the selected month for historical budget analysis
    *   **Reports Screens**: All data is month-specific based on the selected month context

## 6. Phased Implementation Strategy

BudApp will be developed in three phases to balance speed-to-market with feature completeness:

### Phase 1: MVP - Pure Flutter (Target: 3-4 months)
*   **Approach**: Client-side only, minimal backend dependencies
*   **Backend**: Firestore + Firebase Auth + FCM + Remote Config (no Cloud Functions)
*   **Core Features**:
    *   User authentication (email/password, OAuth)
    *   Account and transaction management (CRUD operations)
    *   Category management with predefined categories (via Remote Config) and custom categories
    *   Client-side budget tracking and calculations
    *   Basic reporting and dashboard (computed client-side)
    *   Goal tracking with manual contributions
    *   Offline-first functionality with Firestore persistence
    *   Basic in-app notifications for budget alerts
    *   Feature flags and premium limits controlled via Remote Config
*   **Business Logic**: All implemented in Flutter using Dart
*   **Remote Config Usage**:
    *   Premium feature limits (account counts, category limits)
    *   Feature toggles for gradual rollout
    *   Configurable thresholds (budget alerts, low balance warnings)
    *   Emergency kill switches for problematic features
    *   Predefined category lists (income and expense categories with icons/colors)
    *   Dynamic category updates without app releases
*   **Limitations**: 
    *   No recurring transactions (users create manually)
    *   No server-side notifications (only in-app alerts)
    *   No complex data aggregation or analytics
*   **Benefits**: Fast development, lower costs, full offline capability, instant feature control

### Phase 2: Recurring Transactions (Target: +1-2 months)
*   **New Backend Components**: Cloud Functions + Cloud Scheduler
*   **Added Features**:
    *   Automated recurring transaction generation
    *   Scheduled background jobs for maintenance
    *   Enhanced data validation via Cloud Functions
*   **Technical Changes**:
    *   Add TypeScript interfaces for Cloud Functions
    *   Implement scheduled functions for recurring transactions
    *   Add Cloud Function testing to CI/CD pipeline
*   **User Impact**: Users can set up salary, rent, and other recurring transactions

### Phase 3: Server-Side Intelligence (Target: **** months)
*   **Enhanced Backend**: Advanced Cloud Functions + external integrations
*   **Added Features**:
    *   Server-side push notifications for budget alerts
    *   Advanced data aggregation and analytics
    *   Materialized views for complex reports
    *   GDPR-compliant data export and deletion workflows
    *   Email notifications and digests
*   **Technical Changes**:
    *   Complex data processing workflows
    *   Integration with external services (email, analytics)
    *   Advanced caching and performance optimization
    *   Comprehensive audit logging
*   **User Impact**: Smarter notifications, better insights, more automated workflows

### Migration Strategy Between Phases
*   **Data Compatibility**: All phases use the same Firestore data model
*   **Zero Downtime**: New features added without disrupting existing functionality
*   **Progressive Enhancement**: Each phase builds on the previous without breaking changes
*   **Feature Flags**: Use Firebase Remote Config to gradually roll out new features
*   **User Communication**: Clear messaging about new capabilities as they become available

## 7. Technical Requirements

### 7.1. Technology Stack

#### 7.1.1. Backend
*   **Platform**: Firebase (Firestore + Auth + Cloud Messaging + Remote Config)
*   **MVP Phase 1 Approach**: Minimal backend - Firestore for data storage, Firebase Auth for authentication, FCM for basic notifications, Remote Config for feature flags
*   **Business Logic**: Firestore Security Rules for data validation and access control
*   **Validation**: Firestore Security Rules & client-side validation
*   **Database**: Cloud Firestore (NoSQL) with Security Rules enabled on all collections
*   **Authentication**: Firebase Authentication (email/password, OAuth Google & Apple). JWTs automatically issued and consumed by the Flutter client
*   **Realtime**: Firestore real-time listeners for instant sync across devices
*   **Notifications**: Firebase Cloud Messaging (FCM) for push notifications triggered from client
*   **Feature Flags & Configuration**: Firebase Remote Config for server-side feature toggles, app configuration, and premium feature management
*   **Testing**: Firebase Emulator Suite for local development and Security Rules testing
*   **CI/CD**: GitHub Actions with Firebase CLI for Security Rules deployment and app builds
*   **Hosting**: Firebase manages infrastructure; automatic scaling
*   **Observability**: Firebase Performance Monitoring, Crashlytics, and Cloud Logging
*   **Future Phases**: 
    *   **Phase 2**: Add Cloud Functions for recurring transaction generation
    *   **Phase 3**: Add server-side notification logic and complex workflows

#### 7.1.2. Mobile
*   **Framework**: Flutter SDK (latest stable) with Dart 3
*   **UI Kit**: Material 3 & Cupertino (no third-party UI libs for MVP)
*   **State Management**: Riverpod (AsyncNotifier/Notifier)
*   **Navigation**: go_router
*   **Offline Database**: **Firestore Offline Persistence** – built-in caching with custom pre-population strategy for complete offline functionality
*   **Sync/Network**: Firebase Flutter SDKs (cloud_firestore, firebase_auth). Firestore handles sync automatically with conflict resolution
*   **Remote Config**: firebase_remote_config for feature flags and app configuration management
*   **Forms**: Flutter `Form`/`TextFormField` + `flutter_hooks` where helpful
*   **Local Preferences & Secrets**: shared_preferences (non-sensitive), flutter_secure_storage (auth tokens, sensitive settings)
*   **Notifications**: firebase_messaging for FCM integration
*   **Testing**: flutter_test, integration_test; mocks for Firebase services
*   **Build/Deployment**: GitHub Actions – static analysis (dart analyze), tests, security scan (Flutter secure lint), Android & iOS build artifacts
*   **Error Monitoring**: Firebase Crashlytics with correlation IDs
*   **Data Security**: Firestore data encrypted at rest and in transit by default

#### 7.1.3. Monorepo & Build System
*   **Repository Layout**: Flat repo; Flutter project lives at **repo root** (`lib/`, `android/`, `ios/`, etc.) alongside a `firebase/` folder for Security Rules and configuration
*   **MVP Backend Deployment**: `firebase deploy` in CI for Security Rules and indexes only
*   **Design Token Management**: design tokens JSON stored in `lib/design/`, converted to Dart constants via build_runner
*   **Future Phases**: Node tooling with **pnpm** will be added in Phase 2 for Cloud Functions development

### 7.2. Architecture Overview
*   **Data Source of Truth**: Cloud Firestore with Security Rules
*   **Offline-First Principle**: Firestore's built-in offline persistence with custom pre-population ensures all user data is available offline
*   **Configuration Management**: Firebase Remote Config provides server-side control over app behavior, feature flags, and premium features without app updates
*   **MVP Phase 1 Business Logic Distribution**:
    *   Basic validation and access control enforced through Firestore Security Rules
    *   All business logic, calculations, and workflows implemented client-side in Flutter
    *   Financial calculations (balances, budgets, reports) performed client-side with local caching
    *   No server-side processing required for core functionality
    *   Feature toggles and app configuration managed server-side via Remote Config
*   **Client ↔ Backend Communication**:
    *   Direct Firestore SDK calls with automatic caching and sync
    *   Real-time listeners for document and collection changes
    *   Client-triggered FCM notifications for local budget alerts
    *   Periodic Remote Config fetches for feature flags and configuration updates
*   **Sync Engine**: Firestore's built-in sync with optimistic updates and automatic conflict resolution
*   **Authentication**: Firebase Auth with email/password + OAuth (Google, Apple). Auth state automatically integrated with Firestore Security Rules
*   **Schema Evolution**: NoSQL schema flexibility allows gradual migration; version fields track document structure changes
*   **Scalability**: Firebase auto-scales; Firestore supports millions of concurrent users with automatic sharding
*   **Future Architecture Evolution**:
    *   **Phase 2**: Introduce Cloud Functions for recurring transaction generation and scheduled tasks
    *   **Phase 3**: Add server-side notification logic and complex data processing workflows

### 7.3. Data Management

#### 7.3.1. Data Model
*   The authoritative data model is defined through **Firestore document schemas** implemented in TypeScript interfaces and enforced via Security Rules and Cloud Functions. Detailed collection definitions are outlined in **Appendix A: Firestore Collection Schemas**.
*   **General Considerations**:
    *   Use sub-collections for hierarchical relationships where appropriate
    *   Denormalize data judiciously for query performance (document all denormalization decisions)
    *   Implement reference fields with Firestore DocumentReferences
    *   Use Firestore's automatic indexing and create composite indexes for common query patterns
    *   All user-specific collections must have Security Rules enforcing user access
    *   Store currency codes (ISO 4217) in a standardized field
    *   Use Firestore Timestamps for all date/time fields
    *   Use hard deletes - no `deletedAt` fields needed
*   **Query Optimization Strategies**:
    *   **Composite Indexes**: Create for multi-field queries (e.g., accountId + date range)
    *   **Denormalization**: Cache frequently accessed computed values in documents (e.g., account balances, monthly spending totals)
    *   **Materialized Views**: Pre-compute common report data in separate collections for complex financial reports
    *   **Batch Operations**: Use Firestore batch writes for related document updates
*   **Reporting & Analytics**:
    *   **Client-Side Aggregation**: Perform simple aggregations (account balances, category totals) on the client
    *   **Cloud Functions for Complex Reports**: Use scheduled functions to pre-compute complex monthly/yearly reports
    *   **BigQuery Integration**: Consider for advanced analytics (post-MVP)
*   **Categories Hierarchy**: Categories stored in a `categories` collection with optional `parentId` field referencing parent category document ID; `null` for top-level categories

**Suggestion:** *Monitor Firestore usage costs closely, especially document reads/writes. Implement intelligent caching and denormalization to minimize query costs.*

#### 7.3.2. Sync Strategy for Multi-Device and Offline Support
Firestore provides built-in offline support, but the MVP implements an enhanced strategy for complete offline functionality:

*   **Offline-First Architecture**:
    *   Enable Firestore offline persistence on app initialization
    *   Pre-populate user dataset on login using batch reads (prioritize last 6 months)
    *   Implement custom caching strategy to ensure critical user documents are available offline
    *   Use Firestore's metadata to track sync state and handle offline/online transitions
*   **Intelligent Data Management**:
    *   **Data Pruning**: Prioritize recent transactions (last 6 months) for offline cache
    *   **Cache Size Monitoring**: Monitor and manage Firestore cache size, implement cache eviction for older data
    *   **User Configuration**: Allow users to configure offline data range
    *   **Storage Warnings**: Warn users about extended offline periods and cache limits
*   **Sync Mechanism**:
    *   Firestore automatically handles sync when online
    *   Optimistic updates with automatic retry on connection restore
    *   Real-time listeners update UI instantly when changes sync
    *   **Selective Listening**: Implement smart listener management to reduce costs
*   **Conflict Resolution**: 
    *   Firestore uses optimistic concurrency control
    *   Last-write-wins based on server timestamps
    *   Custom merge strategies implemented in Security Rules for specific collections
*   **Data Pre-population Strategy**:
    *   On initial login: fetch essential data (accounts, categories, recent transactions)
    *   Background fetch: load older data progressively
    *   Store fetch timestamp to optimize subsequent syncs
    *   Background refresh when online to ensure cache freshness
*   **Offline Capabilities**:
    *   Full CRUD operations available offline
    *   Queries executed against local cache
    *   Pending writes queued and synced when online

#### 7.3.2a. Sync Error Handling
When sync operations fail or conflicts occur, the app must provide clear user feedback and recovery options:

*   **Sync Failure Notification**:
    *   **Immediate Feedback**: "Your changes are saved locally but haven't synced yet. Check your internet connection."
    *   **Persistent Indicator**: Sync status icon in the app header showing pending/failed sync state
    *   **Retry Mechanism**: Automatic retry with exponential backoff, manual retry button available
*   **Offline Changes Tracking**:
    *   **Pending Changes List**: Users can view which changes are waiting to sync
    *   **Visual Indicators**: Transactions/accounts with pending changes shown with distinct UI styling
    *   **Status Messages**: "3 transactions waiting to sync" with option to view details
*   **Conflict Resolution**:
    *   **Automatic Resolution**: Last-write-wins for most conflicts with server timestamp authority
    *   **User Notification**: "Your changes from [date] were synced successfully"
    *   **Data Loss Prevention**: If local changes would be overwritten, user is notified before sync
*   **Extended Offline Scenarios**:
    *   **Cache Expiration Warning**: "You've been offline for 25 days. Your changes will expire in 5 days."
    *   **Data Backup Prompt**: Suggest manual data export before cache expiration
    *   **Recovery Options**: Guide users through account recovery if local data is lost

#### 7.3.3. Data Integrity & Validation
*   **Simplified Financial Model**: 
    *   Each transaction stored as a single document with clear type (income/expense/transfer)
    *   Account balances calculated by aggregating transaction amounts
    *   Use Firestore transactions for atomic operations when needed
*   **Simple Transaction Management**:
    *   Transactions can be edited and deleted directly - users have full control
    *   Hard deletes - no soft delete complexity needed for personal finance
*   **Referential Integrity**:
    *   Prevent deletion of accounts, categories, or goals that have related transactions
    *   Show user which transactions are blocking deletion and require cleanup first
    *   Categories with subcategories cannot be deleted until subcategories are removed/reassigned
*   **Currency Precision**: Use integers to represent monetary amounts in smallest currency units (e.g., cents) to avoid floating-point precision issues
*   **Date and Timezone Handling**:
    *   Store all timestamps as Firestore Timestamp objects (UTC)
    *   Store user's timezone preference in user profile
    *   Client-side conversion for display using user's timezone
*   **Balance Consistency**:
    *   Calculate account balances by summing relevant transactions
    *   Use Firestore transactions for atomic updates when balance precision is critical
    *   Cache calculated balances in account documents for performance
*   **Validation Strategy**:
    *   Firestore Security Rules for field validation and user access control
    *   Client-side validation for immediate user feedback and referential integrity checks
    *   Minimal Cloud Functions only for complex business rules that can't be handled by Security Rules
*   **Error Handling**: Consistent error patterns from Firestore operations with proper error propagation to client

#### 7.3.4. Database Backup & Recovery
*   Leverage Firebase's automated backup features:
    *   Daily automated Firestore backups to Cloud Storage
    *   Point-in-time recovery available
*   **Recovery Time Objective (RTO)**: 2 hours
*   **Recovery Point Objective (RPO)**: 24 hours (daily backups)
*   **Backup Strategy**:
    *   Automated daily exports via Cloud Scheduler and Cloud Functions
    *   Exports stored in regional Cloud Storage buckets
    *   Retention policy: 30 days of daily backups
    *   **Disaster Recovery Testing**: A disaster recovery drill, involving a full restoration of data from a backup into a test environment, must be performed and documented before launch and on a bi-annual basis thereafter
*   **Application-Level Audit Logs**:
    *   Implement critical operation logging in Cloud Functions for granular recovery
    *   Log account deletions, bulk transaction operations, and system-level changes
    *   Store audit logs separately from main data for compliance and recovery purposes

#### 7.3.5. Data Migration Strategy
*   **Schema Versioning**: 
    *   Each document includes a `schemaVersion` field
    *   Cloud Functions handle backward compatibility
    *   Batch migration functions for major schema updates
*   **Migration Chains for Long-Absent Users**:
    *   **Sequential Migration Support**: Migration functions designed to run sequentially (e.g., v2->v3, then v3->v4)
    *   **Version Gap Handling**: Users returning after missing multiple schema updates can be migrated through the complete chain
    *   **Migration Function Persistence**: All migration functions retained in codebase to support users upgrading from any historical version
    *   **Error Recovery**: If migration chain fails partway through, user data remains in last successful state with clear error reporting
*   **Migration Process**:
    *   Deploy new Cloud Functions with migration logic
    *   Run migrations incrementally to avoid quota issues
    *   Monitor migration progress via Cloud Logging
    *   Test migration chains with data from all historical schema versions

#### 7.3.6. Schema Evolution & Compatibility
*   **NoSQL Flexibility**: 
    *   Firestore's schemaless nature allows gradual field additions
    *   Optional fields with defaults handle backward compatibility
    *   Version fields track document structure evolution
*   **Client Compatibility**:
    *   Mobile app checks minimum supported API version on startup
    *   Force update mechanism for breaking changes
    *   Graceful degradation for newer server features

#### 7.3.7. Offline Sync Edge-Case Handling

Firestore's offline persistence handles most edge cases automatically, but the following require special attention:

1. **Large Initial Dataset**: Implement paginated initial sync with progress indication when pre-populating offline cache
2. **Offline Duration Limits**: Firestore caches offline writes for up to 30 days; warn users about extended offline periods
3. **Cache Size Management**: Monitor and manage Firestore cache size; implement cache eviction strategy if needed
4. **Conflict Resolution**: While Firestore handles basic conflicts, implement custom merge functions in Security Rules for complex scenarios
5. **Network Transition Handling**: Gracefully handle transitions between offline/online states with appropriate UI feedback
6. **Quota Management**: Implement client-side throttling to avoid hitting Firestore quotas during sync bursts

### 7.4. Security
*   **Authentication**: Firebase Authentication with email/password & OAuth (Google/Apple). Passwords hashed with scrypt by Firebase
*   **Authorization**: Firestore Security Rules ensure users only access their own data
    *   **Security Rules Testing**: Comprehensive unit tests for all Security Rules using Firebase Emulator Suite
    *   **Rule Complexity Management**: Balance security with performance; overly complex rules can impact read/write performance
*   **Data Encryption**:
    *   Data in transit: TLS/SSL everywhere
    *   Data at rest: Firestore provides automatic encryption at rest
    *   Client-side: Use `flutter_secure_storage` for sensitive data (auth tokens, API keys)
*   **Input Validation**: 
    *   Security Rules validate document structure and field types
    *   Cloud Functions perform additional business logic validation
    *   Client-side validation for user experience
*   **Rate Limiting**: 
    *   Implement rate limiting in Cloud Functions using Firebase App Check
    *   Firestore Security Rules can limit write frequency per user
*   **GDPR/CCPA Compliance**:
    *   Right to Access & Portability: Cloud Function exports user data to JSON
    *   Right to be Forgotten (Erasure):
        *   Cloud Function anonymizes/deletes PII from Firestore
        *   Batch delete operations for user's documents
        *   Schedule deletion from backups after retention period
    *   Consent Management: Track consent in user profile document
    *   Privacy Policy: Accessible from app settings
    *   **Data Lifecycle & Erasure Implementation**:
        *   User deletion triggers Cloud Function
        *   Function deletes/anonymizes data across all collections
        *   Deletion tracked for audit purposes
*   **Secure Mode Feature**: Client-side UI feature to mask sensitive financial data
*   **API Security**:
    *   Firebase App Check for API abuse prevention
    *   Cloud Function authentication via Firebase Auth tokens
    *   CORS configuration for web endpoints
*   **Regular Security Audits**: Quarterly security review of Rules and Functions

### 7.5. Performance & Scalability
*   **Client-Side**:
    *   **Flutter Performance Metrics**:
        *   Cold start < 2s on mid-range devices
        *   Screen transition < 100ms
        *   Maintain 60 fps for scrolls/animations
    *   Bundle-size monitoring via `flutter build apk --analyze-size`
    *   Efficient queries using Firestore's offline cache
    *   Implement pagination for large collections
    *   **Listener Management**: Disconnect real-time listeners when app is backgrounded to reduce costs
*   **Backend (Firebase)**:
    *   Optimize Firestore queries with composite indexes for common query patterns
    *   Minimize document reads through efficient data structure and denormalization
    *   Cloud Functions with minimal cold start time (<1s)
    *   Use Firestore batch operations for bulk updates
    *   **Query Optimization**: Implement selective listening strategies - use one-time reads where real-time updates aren't critical
*   **Caching Strategy**:
    *   Firestore's built-in offline cache as primary cache
    *   Additional in-memory caching for frequently accessed data
    *   Cache computed values (e.g., account balances, monthly totals) with TTL
    *   **Intelligent Data Pruning**: For offline cache, prioritize last 6 months of data to manage storage
*   **Cost Management**:
    *   **Document Read/Write Optimization**: Batch operations where possible, cache frequently accessed data
    *   **Usage Monitoring**: Track per-user document reads/writes, cache hit rates, and query execution times
    *   **Cost Controls**: Implement application-level throttling and budget alerts in Google Cloud Console
    *   **Expected Costs**: Target <$0.50 per user per month for Firestore operations
*   **Scalability Considerations**:
    *   Firestore automatically scales to millions of users
    *   Monitor and optimize for Firestore quotas and operational efficiency
    *   Implement data aggregation patterns for analytics
    *   **Cost Optimization**: Consider user-configurable data retention preferences for very long-term cost management

### 7.6. Firebase Remote Config Implementation
*   **MVP Integration**: Firebase Remote Config is a core component of the MVP for feature flags, app configuration, and premium feature management
*   **Configuration Categories**:
    *   **Feature Flags**: Enable/disable specific features (e.g., `budget_alerts_enabled`, `goal_tracking_enabled`)
    *   **Premium Limits**: Control free vs premium tier limits without app updates
    *   **App Behavior**: Configurable thresholds, UI text, and behavioral parameters
    *   **Emergency Controls**: Kill switches for problematic features
*   **Key Configuration Parameters**:
    ```json
    {
      "max_accounts_free": 2,
      "max_accounts_premium": 5,
      "max_custom_categories_free": 3,
      "budget_alert_threshold": 0.8,
      "low_balance_threshold": 50,
      "onboarding_flow_version": "v1",
      "premium_features_enabled": ["advanced_reports", "unlimited_categories"],
      "maintenance_mode": false,
      "minimum_app_version": "1.0.0",
      "predefined_income_categories": [
        {"id": "salary", "name": "Salary", "icon": "work", "color": "#4CAF50"},
        {"id": "freelance", "name": "Freelance", "icon": "laptop", "color": "#2196F3"},
        {"id": "investment", "name": "Investment", "icon": "trending_up", "color": "#FF9800"}
      ],
      "predefined_expense_categories": [
        {"id": "groceries", "name": "Groceries", "icon": "shopping_cart", "color": "#F44336"},
        {"id": "transportation", "name": "Transportation", "icon": "directions_car", "color": "#9C27B0"},
        {"id": "entertainment", "name": "Entertainment", "icon": "movie", "color": "#E91E63"},
        {"id": "utilities", "name": "Utilities", "icon": "power", "color": "#607D8B"}
      ]
    }
    ```
*   **Implementation Strategy**:
    *   **Fetch Policy**: Fetch on app startup with 12-hour cache expiration
    *   **Default Values**: All config values have hardcoded defaults in case of fetch failures
    *   **User Segmentation**: Support for A/B testing different limits and features
    *   **Activation Strategy**: Fetch configs during app initialization, activate immediately
*   **Caching & Offline Behavior**:
    *   Cached configs available offline for 7 days
    *   Graceful fallback to defaults if cache expires
    *   Background fetch attempts when connectivity restored
*   **Development & Testing**:
    *   Separate Remote Config environments (dev, staging, production)
    *   Local development uses hardcoded defaults when offline
    *   Integration tests verify Remote Config fetch and fallback behavior
*   **Security Considerations**:
    *   No sensitive data in Remote Config (public by design)
    *   Server-side validation of premium status in Firestore Security Rules
    *   Remote Config used for UI behavior only, not authorization
*   **Monitoring & Analytics**:
    *   Track Remote Config fetch success/failure rates
    *   Monitor which feature flags are most active
    *   Alert on Remote Config service failures

### 7.7. Notifications
*   **MVP Approach - In-App Alerts Only**: 
    *   Client-side alert calculation and display
    *   Alerts shown when users open the app and when crossing thresholds during transaction entry
    *   Alert preferences stored in user profile document
*   **Alert Types**:
    *   Budget threshold alerts calculated client-side
    *   Low balance alerts with configurable thresholds (controlled by Remote Config)
    *   Alerts displayed prominently on dashboard and relevant screens
*   **Configurable Thresholds** (via Remote Config):
    *   Budget alerts at configurable percentage (default: 80%)
    *   Low balance at configurable amount in primary currency (default: 50)
    *   User can override Remote Config defaults in settings
*   **Future Enhancement (Phase 3)**: Server-side push notifications via FCM and Cloud Functions

### 7.8. Internationalization (i18n) & Localization (l10n)
*   **MVP**: Single language (English).
*   **Foundation**: Implement an i18n framework early to facilitate future language additions.
*   Currency, date, and number formats should be locale-aware where possible, with user overrides.
*   **Remote Config Integration**: UI text and localization strings can be updated via Remote Config for quick fixes

### 7.9. Monitoring and Observability
*   **Firebase Native Tools**: 
    *   Firebase Performance Monitoring for app performance metrics
    *   Firebase Crashlytics for crash reporting and analytics
    *   Cloud Logging for server-side logs
    *   Firebase Analytics for user behavior tracking
*   **Key Monitoring Areas**: 
    *   App performance metrics (startup time, screen rendering)
    *   Firestore usage and quotas
    *   Cloud Function execution times and errors
    *   User engagement and retention metrics
*   **Custom Performance Metrics**:
    *   **Firestore Operations**: Document reads/writes per user action, query execution times
    *   **Cache Performance**: Cache hit rates, offline sync performance
    *   **Cost Tracking**: Per-user Firestore costs, monthly usage trends
    *   **Real-time Listener Usage**: Active listener counts and associated costs
*   **Cost Monitoring**:
    *   Daily Firestore usage reports and cost projections
    *   Automated alerts for unusual usage spikes
    *   Per-user cost tracking with threshold alerts
    *   Monthly cost analysis and optimization recommendations
*   **Custom Dashboards**: Firebase console with custom alerts for critical metrics
*   **Correlation**: Use Firebase Analytics user properties to correlate client and server events

### 7.10. Development Process & Quality Assurance
*   **Monorepo Structure**: Flat root layout (Flutter code at repo root) + `firebase/` folder for Security Rules and configuration
*   **Firebase Project Setup**:
    *   Development, staging, and production Firebase projects
    *   Environment-specific configuration files
    *   Automated Security Rules deployment
*   **Seed Data**:
    *   Test user accounts for development
    *   Predefined categories managed via Remote Config (no database seeding required)
*   **MVP Phase 1 Testing Strategy**:
    *   **Unit Tests**: flutter_test for all business logic and services
    *   **Widget Tests**: Flutter widget testing for UI components
    *   **Integration Tests**: Firebase Emulator Suite for Security Rules testing
    *   **E2E Tests**: integration_test for critical user flows
    *   **Remote Config Tests**: Test config fetch, caching, and fallback scenarios
    *   **Coverage**: Aim for >90% coverage on business logic
    *   **Local Development**: Use Firebase Emulators for all local development to avoid production costs
*   **Performance Testing**:
    *   **Test Environment**: Production-like Firebase project with seeded dataset of 10,000 transactions across 5 accounts and 20 categories
    *   **Test Devices**: Google Pixel 6a (Android) and iPhone 12 (iOS) as benchmark devices
    *   **Performance Tools**: 
        *   Firebase Performance Monitoring for real-world performance metrics
        *   Flutter DevTools for detailed app profiling and frame rate analysis
        *   Firestore Profiler for query performance and cost analysis
    *   **Test Scenarios**:
        *   Cold app startup time measurement (target: <2s)
        *   Screen transition timing across all major flows (target: <100ms)
        *   Frame rate analysis during scrolling and animations (target: 60fps)
        *   Large dataset query performance (1000+ transactions)
        *   Offline sync performance with significant data volumes
    *   **Validation Process**: Performance benchmarks run on specified devices before each release candidate approval
    *   **Continuous Monitoring**: Performance regression detection via automated tests in CI/CD pipeline
*   **CI/CD Pipeline (GitHub Actions)**:
    *   Automated testing with Firebase Emulators for Security Rules
    *   Flutter tests and static analysis
    *   Remote Config testing with mock configurations
    *   Automated Security Rules deployment
    *   Environment-specific Remote Config deployment (dev/staging/prod)
    *   Flutter app builds for iOS and Android
    *   No Cloud Functions deployment required for MVP
*   **Code Quality**:
    *   Dart analyzer for Flutter code
    *   Pre-commit hooks for code formatting
    *   Strict linting rules for consistency
*   **MVP Development Best Practices**:
    *   **Dart Models**: Define strong typed models for all Firestore documents
    *   **Service Layer**: Create reusable service classes for Firestore operations
    *   **Repository Pattern**: Abstract Firestore operations behind repository interfaces
    *   **Remote Config Service**: Create dedicated service for config fetching with fallback handling
    *   **Security Rules Coverage**: Test all Security Rules paths and edge cases
    *   **Cost-Aware Development**: Monitor Firestore usage during development
    *   **Feature Flag Testing**: Test all code paths for enabled/disabled feature states
*   **Feature Flags**: Firebase Remote Config for gradual feature rollout
*   **Future Phases**:
    *   **Phase 2**: Add Cloud Functions development, testing, and deployment to pipeline
    *   **Phase 3**: Introduce TypeScript interfaces and advanced server-side testing

## 8. Non-Functional Requirements (NFRs) Summary

*   **Security**: Adherence to best practices outlined in Sec 7.4, including Security Rules, data encryption, and secure authentication.
*   **Performance** (measured on benchmark devices: Google Pixel 6a for Android, iPhone 12 for iOS):
    *   App cold start time (target: < 2 seconds).
    *   Screen transition time (target: < 100ms).
    *   Maintain 60 fps for scrolls/animations.
    *   API P95 response time (target: < 200ms for core operations).
    *   Offline operations should be seamless.
*   **Reliability**:
    *   System uptime (target: 99.9%).
    *   Data integrity ensured by double-entry and robust validation.
    *   Low crash rate for mobile app (target: < 0.1% of sessions).
*   **Scalability**: Architecture should support growth to [e.g., 10,000] active users within the first year without major re-architecture.
*   **Usability**: Intuitive and easy-to-learn interface. Target high user satisfaction scores (e.g., >4.0 app store rating).
*   **Maintainability**: Well-structured, documented code. Adherence to monorepo structure and ESM.
*   **Testability**: High test coverage for critical paths. All tests pass in CI.
*   **Compliance**: GDPR/CCPA readiness as detailed.

**Suggestion:** *Quantify NFRs where possible (as shown with examples). These targets will help guide development and testing.*

## 9. Success Metrics (MVP)

How we will measure the success of the BudApp MVP:

*   **User Acquisition & Adoption**:
    *   Number of app downloads.
    *   Number of registered users.
    *   Activation Rate: % of downloads that complete onboarding and record at least one transaction.
*   **User Engagement**:
    *   Daily Active Users (DAU) / Monthly Active Users (MAU) ratio.
    *   Average session duration.
    *   Feature Adoption:
        *   % of active users who have created at least one budget.
        *   % of active users who have created at least one financial goal.
        *   Average number of transactions logged per active user per week.
*   **Retention**:
    *   Day 1, Day 7, Day 30 user retention rates.
    *   Churn rate.
*   **App Performance & Stability**:
    *   App crash rate (via observability stack - Phase 2).
    *   Average API response time (via Grafana Cloud - Phase 2).
    *   App Store ratings and reviews.
*   **Monetization (Post-MVP focus, but foundation in MVP)**:
    *   Free-to-Premium conversion rate.
    *   **Premium Tier Adoption**: % of active users using one or more premium features (see §4.13).
    *   **Paywall Encounter Rate**: % of Daily Active Users who encounter a premium feature limitation (e.g., try to add a 3rd account) - tracks effectiveness of free tier limits.
*   **User Feedback & Satisfaction (Qualitative)**:
    *   Conduct **5 user interviews** per month post-launch to understand motivations, pain points, and feature requests.
    *   Analyze App Store review comments for recurring themes and sentiment trends.
    *   Track Net Promoter Score (NPS) through in-app surveys.
*   **Future Diagnostic Metrics (Phase 2)**: Post-MVP, success metrics will be enhanced with funnel analysis to diagnose friction points. Key funnels: (1) Transaction Creation Completion Rate, (2) Budget Creation Completion Rate, and (3) Onboarding Completion Rate.

**Suggestion:** *Set specific targets for these metrics before launch (e.g., "Achieve 1,000 MAU within 3 months post-launch"). Regularly track and review these metrics to inform product decisions.*

## 10. Release Criteria (MVP)

The BudApp MVP will be considered ready for release when the following criteria are met:

*   All MVP features defined in Section 4 are implemented and pass QA.
*   User stories (Section 3) associated with MVP features are fulfilled.
*   Critical and Major bugs identified during testing are resolved.
*   Security:
    *   Security Rules implemented and tested.
    *   Authentication flows are secure and tested.
    *   Basic GDPR/CCPA measures (privacy policy, data export) are in place.
*   Performance: Meets NFR targets for app responsiveness and API speed under simulated load.
*   Offline sync functionality is robust and tested across various scenarios (conflicts, intermittent connectivity).
*   In-app alerts for budget thresholds and low balances are functional and reliable.
*   Remote Config integration:
    *   Feature flags working correctly for premium limits and feature toggles.
    *   Graceful fallback to defaults when Remote Config is unavailable.
    *   Config caching and offline behavior verified.
*   **Materialized Views Implementation**: At least one materialized view (e.g., `monthly_spending_summaries` collection) is implemented and tested to validate the Firestore reporting pattern and establish technical foundation for future reports.
*   Subscription management flow (free tier, placeholder for premium) via RevenueCat is functional.
*   Legal: Privacy Policy and Terms of Service are finalized and accessible within the app.
*   Documentation:
    *   Basic user-facing FAQ or help section.
    *   Internal technical documentation for key architectural components.
*   CI/CD pipeline is operational for both backend and mobile deployments.
*   Successful build and submission to TestFlight (iOS) and Google Play Internal Testing (Android).
*   Observability infrastructure (OpenTelemetry + Grafana Cloud) is planned for Phase 2 implementation.

## 11. Future Considerations (Post-MVP Roadmap)

The following features are planned for future releases beyond the MVP:

*   **Global Transaction Search (HIGH PRIORITY)**: Search functionality to quickly find specific transactions by merchant, amount, category, notes, or tags. Essential for managing large transaction histories.
*   **Advanced Reporting & Visualizations**: Customizable dashboards, cash flow projections, net worth tracking, comparison reports, trend analysis.
    *   **Reporting by Tags**: Allow users to filter reports and view spending summaries based on one or more selected tags.
*   **Bulk Re-categorization Tool**: Streamline category restructuring by allowing users to bulk re-assign transactions from one category to another, making account and category deletion workflows much smoother.
*   **Debt Management**: Detailed loan tracking, debt payoff planning tools.
*   **Investment Tracking**: Basic manual tracking of investment accounts.
*   **Advanced Transaction Features**: Split transactions, transaction attachments (receipts), advanced search/filtering, bulk editing.
*   **Multi-Currency Support**: True multi-currency accounts with automatic/manual exchange rate handling.
    *   **Note on Multi-Currency Refactor**: The "Multi-Currency Support" feature represents a significant architectural evolution, not just an incremental addition. The MVP's single-currency model is a deliberate choice to reduce complexity, but stakeholders should be aware that implementing multi-currency support will require a major engineering effort, including database schema migrations and refactoring of all financial logic.
*   **Shared Budgets & Accounts**: Ability to share items with other BudApp users.
*   **Import/Export Enhancements**: Import from CSV/OFX, automated bank account linking (e.g., Plaid).
*   **Full Internationalization (i18n) & Localization (l10n)**.
*   **Web Application**: Companion web interface.
*   **AI-Powered Insights & Recommendations**: Automated categorization, spending pattern analysis, predictive forecasting.
*   **Invoice Management (for Freelancers/Small Businesses)**.
*   **Enhanced Security Features**: Two-Factor Authentication (2FA), biometric authentication.
*   **Gamification & Rewards**.

**Suggestion:** *Prioritize this list based on MVP feedback and strategic goals. Each major feature here will warrant its own PRD or feature specification.*

## 12. Risks and Dependencies

### 12.1. Risks
*   **Technical Complexity**:
    *   Implementing the transaction ledger system and balance calculations accurately and efficiently.
    *   Developing a robust and bug-free multi-device sync mechanism with offline support can be challenging.
    *   Learning curve and ramp-up time for Flutter/Dart and Firebase Cloud Functions.
    *   Firebase **vendor lock-in & pricing** evolutions.
    *   Limitations of Cloud Function cold starts and timeout limits for heavy workloads.
    *   Maturity of some Flutter plugins compared to native SDKs.
*   **Performance**:
    *   Complex Security Rules impacting query performance at scale.
    *   Flutter performance on lower-end devices or with complex widget trees.
    *   Background sync impacting battery life if not optimized.
*   **Firebase-Specific Risks**:
    *   **Query Limitations (CRITICAL RISK - MVP IMPLEMENTATION REQUIRED)**: The choice of Firestore will heavily constrain future reporting capabilities. Firestore is not well-suited for ad-hoc aggregate queries common in financial reporting (e.g., "compare average spending in Category X between this quarter and last quarter"). Any report not based on pre-defined materialized views will be difficult, slow, or expensive to implement. This trade-off (sacrificing reporting flexibility for development speed and scalability) must be accepted by all stakeholders and may require costly data restructuring or BigQuery integration sooner than expected. 
        *   **REQUIRED ACTIONS**: 
            1. Product owner must provide explicit written sign-off acknowledging these specific reporting limitations before development begins.
            2. **MVP MUST include implementation of at least one materialized view** (e.g., `monthly_spending_summaries` collection) to validate the pattern and establish the technical foundation for future reports.
    *   **Cost Overruns**: Unexpected Firestore costs due to inefficient query patterns or excessive real-time listeners
    *   **Offline Data Size**: Pre-populating user data may hit device storage limits for heavy users
    *   **Real-time Listener Costs**: Active listeners for all user data can become expensive at scale
*   **Security**:
    *   Vulnerabilities in custom authentication or third-party integrations.
    *   Ensuring data privacy and compliance with GDPR/CCPA, especially regarding the "Right to be Forgotten" for financial data.
*   **User Adoption**:
    *   Competition from established budgeting apps.
    *   Onboarding complexity discouraging new users.
*   **Third-Party Dependencies**:
    *   Reliance on Firebase, RevenueCat, and other services (uptime, cost, feature changes).
    *   Future reliance on Plaid or similar for bank aggregation.
    *   **App Store Policy Changes**: Apple or Google can change their subscription, in-app purchase, or content policies, potentially breaking the monetization flow and requiring urgent, unplanned development work to maintain compliance. Recent examples include Apple's App Store Review Guidelines changes and Google Play's billing policy updates.
*   **Scope Creep**: Tendency to add more features into MVP, delaying release.
*   **Monetization**: Achieving a sustainable free-to-premium conversion rate.
*   **Team Capacity/Expertise**: Ensuring the team has the necessary skills for the chosen tech stack and complex features.
*   **Schema Management**: Managing Firestore document schemas across client and server requires careful coordination and versioning strategy.
*   **MVP Scope Risk (HIGH RISK)**: The current MVP scope is extensive (offline sync, social auth, biometric auth, CSV import, in-app alerts, reporting, goals, subscriptions). This is closer to a "Version 1.0" than a traditional MVP, which may extend time-to-market and delay user feedback validation. The 3-4 month timeline in Section 6 is highly optimistic for this scope.
    *   **STRONGLY RECOMMENDED Mitigation Strategy**: Formally define a "Core Validation" release milestone that is even smaller than the current MVP. This internal release would focus on validating the absolute core loop in <2 months:
        *   Email & Password Auth only
        *   Manual Account & Transaction Entry
        *   Basic Category Management (predefined + 3 custom)
        *   A single, simple spending report (pie chart)
        *   This would validate the core value proposition and de-risk the larger investment.
*   **Monetization Balance**: Free tier may be too generous, reducing upgrade incentive; account/category limits will be monitored post-launch.

**Suggestion:** *For each high-impact risk, define mitigation strategies and contingency plans.*

### 12.1.1. Risk Mitigation Strategies
*   **Cost Management**:
    *   Implement cost estimation before launch with per-user cost tracking
    *   Set up automated budget alerts in Google Cloud Console
    *   Monitor usage patterns and implement cost optimization strategies
    *   Design cost-effective query patterns and caching from day one
*   **Scaling Concerns**:
    *   Monitor usage patterns and costs for optimization opportunities
    *   Implement intelligent data pruning and cache management for performance
    *   Design efficient query patterns to maintain app responsiveness
*   **Performance Optimization**:
    *   Implement composite indexes and denormalization strategies
    *   Use selective listening strategies and disconnect listeners when appropriate
    *   Design materialized views for complex reports
    *   Cache frequently accessed data with appropriate TTL
*   **App Store Policy Risk Mitigation**:
    *   Monitor Apple App Store Review Guidelines and Google Play policy updates regularly
    *   Maintain buffer time in product roadmap for potential compliance updates
    *   Stay informed about industry policy changes through developer community channels
    *   Design subscription flows to be easily adaptable to policy changes

### 12.2. Dependencies
*   **External Services**:
    *   Firebase for database, authentication, and backend services.
    *   RevenueCat for subscription management.
    *   Resend for email delivery (via Cloud Functions).
    *   OAuth providers (Google, Apple) for social login.
    *   Firebase Cloud Messaging (FCM) for all push notifications.
*   **Internal**:
    *   Availability of development and QA resources.
    *   Completion of UI/UX designs for all MVP screens, including biometric authentication flows and CSV import interface.
*   **Legal/Compliance**:
    *   Legal review of Privacy Policy, Terms of Service, and GDPR/CCPA compliance strategy, especially for data erasure and retention.

## 13. Open Issues / Questions for Discussion
*   **Resolved**: Strategy for design tokens, state management (Riverpod), navigation (go_router), and offline storage (Firestore offline persistence) are locked.

## Appendix A: Firestore Collection Schemas
The proposed Firestore collection structure is as follows:

### Collections:
*   `users` - User profiles and settings
*   `users/{userId}/accounts` - Financial accounts (sub-collection under user)
*   `users/{userId}/transactions` - All transactions (sub-collection under user)
*   `users/{userId}/categories` - User's custom categories (sub-collection)
*   `users/{userId}/budgets` - User's budgets (sub-collection)
*   `users/{userId}/goals` - Financial goals (sub-collection)
*   `users/{userId}/tags` - User-defined tags (sub-collection)

**Note**: Predefined categories are delivered via Firebase Remote Config, not stored in Firestore collections.

### Key Design Decisions:
*   **User Data Isolation**: All user-specific data stored in sub-collections under the user document for optimal security and performance
*   **Remote Config Categories**: Predefined categories delivered via Remote Config for instant updates; user customizations in user sub-collection
*   **Transaction Structure**: Single collection for all transaction types with discriminator field
*   **Hard Deletes**: Simple permanent deletion - no soft delete complexity
*   **Referential Integrity**: Prevent deletion of entities with related transactions
*   **Schema Versioning**: Each document includes `schemaVersion` for migration support

### Benefits of This Structure:
*   **Security**: Easier to write Security Rules for user data isolation
*   **Performance**: Better query performance within user scope
*   **Billing**: Subcollection operations are more cost-effective
*   **Backup/Restore**: Easier to export/import user data
*   **Scalability**: Each user's data is naturally partitioned
*   **Simplicity**: No complex soft delete logic or audit trails

**General Schema Considerations:**
*   **Document References**: Use Firestore references for relationships between documents
*   **Composite Indexes**: Create for common query patterns within user scope
*   **Security Rules**: Enforce user access control at collection level
*   **Currency Storage**: Store amounts as integers (smallest unit) with currency code
*   **Timestamps**: Use Firestore Timestamp type for all date/time fields

### Field-Level Document Schemas

#### Users Collection: `users/{userId}`
```typescript
interface User {
  id: string;                          // Document ID (Firebase Auth UID)
  email: string;                       // User's email address
  displayName?: string;                // User's display name
  preferredCurrency: string;           // ISO 4217 code (e.g., "USD")
  dateFormat: string;                  // User's preferred date format
  timeFormat: '12h' | '24h';          // User's preferred time format
  darkMode: boolean;                   // UI theme preference
  secureMode: boolean;                 // Mask balances for privacy
  primaryAccountId?: string;           // Reference to favorite account ID
  notificationPreferences: {
    budgetAlerts: boolean;
    lowBalanceAlerts: boolean;
    budgetThreshold: number;           // 0.0-1.0 (e.g., 0.8 for 80%)
    lowBalanceAmount: number;          // In smallest currency unit
  };
  schemaVersion: number;               // For data migration
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

#### Accounts Sub-collection: `users/{userId}/accounts/{accountId}`
```typescript
interface Account {
  id: string;                          // Document ID
  name: string;                        // User-defined account name
  type: 'asset' | 'liability';         // Account type (locked after creation)
  subtype: 'checking' | 'savings' | 'credit_card' | 'cash' | 'other';
  initialBalance: number;              // Starting balance in smallest currency unit
  currency: string;                    // ISO 4217 code
  color?: string;                      // Hex color for UI
  icon?: string;                       // Icon identifier
  isPrimary: boolean;                  // Is this the user's primary account
  isActive: boolean;                   // Can be set to false to "archive"
  schemaVersion: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

#### Transactions Sub-collection: `users/{userId}/transactions/{transactionId}`
```typescript
interface Transaction {
  id: string;                          // Document ID
  amount: number;                      // Amount in smallest currency unit
  currency: string;                    // ISO 4217 code
  date: Timestamp;                     // Transaction date/time
  type: 'income' | 'expense' | 'transfer';
  description: string;                 // Transaction description
  categoryId?: string;                 // Reference to category document ID (optional for transfers)
  accountId: string;                   // Source account reference
  toAccountId?: string;                // Destination account (for transfers only)
  tagIds: DocumentReference[];         // Array of references to tag documents
  notes?: string;                      // Optional user notes
  schemaVersion: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

#### Categories Sub-collection: `users/{userId}/categories/{categoryId}`
```typescript
interface Category {
  id: string;                          // Document ID
  name: string;                        // Category name
  type: 'income' | 'expense';          // Category type
  parentId?: string;                   // Reference to parent category (for subcategories)
  color: string;                       // Hex color
  icon: string;                        // Icon identifier
  isActive: boolean;                   // Can be set to false to "archive"
  schemaVersion: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

#### Budgets Sub-collection: `users/{userId}/budgets/{budgetId}`
```typescript
interface Budget {
  id: string;                          // Document ID
  name: string;                        // Budget name
  type: 'income' | 'expense';          // Budget type
  amount: number;                      // Budget amount in smallest currency unit
  currency: string;                    // ISO 4217 code
  period: 'monthly';                   // MVP only supports monthly
  categoryId?: string;                 // Optional category reference (null for overall budget)
  isActive: boolean;                   // Active/inactive status
  schemaVersion: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

#### Goals Sub-collection: `users/{userId}/goals/{goalId}`
```typescript
interface Goal {
  id: string;                          // Document ID
  name: string;                        // Goal name (e.g., "Vacation Fund")
  description?: string;                // Optional description
  targetAmount: number;                // Target amount in smallest currency unit
  currentAmount: number;               // Current progress amount (denormalized field)
  currency: string;                    // ISO 4217 code
  targetDate?: Timestamp;              // Optional target completion date
  isCompleted: boolean;                // Completion status
  color?: string;                      // Hex color for UI
  icon?: string;                       // Icon identifier
  schemaVersion: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

**Data Integrity Note**: `currentAmount` is a denormalized field that is automatically updated via client-side aggregation whenever a `GoalContribution` is added, modified, or deleted. The sum of all contributions in the sub-collection is the source of truth, and `currentAmount` provides quick access for UI display without requiring sub-collection queries.

#### Goal Contributions Sub-collection: `users/{userId}/goals/{goalId}/contributions/{contributionId}`
```typescript
interface GoalContribution {
  id: string;                          // Document ID
  userId: string;                      // Owner user ID (for security)
  goalId: string;                      // Parent goal ID
  amountCents: number;                 // Contribution amount in cents
  contributionDate: Timestamp;         // When the contribution was made
  description?: string;                // Optional description (max 500 chars)
  isActive: boolean;                   // Soft delete flag (default: true)
  schemaVersion: number;               // Schema version for migrations (default: 1)
  createdAt: Timestamp;                // Creation timestamp
  updatedAt: Timestamp;                // Last update timestamp
  metadata: Record<string, any>;       // Additional metadata (default: {})
  date: Timestamp;                     // Contribution date
  notes?: string;                      // Optional notes
  schemaVersion: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

#### Tags Sub-collection: `users/{userId}/tags/{tagId}`
```typescript
interface Tag {
  id: string;                          // Document ID
  name: string;                        // Tag name
  color?: string;                      // Optional color
  usageCount: number;                  // Number of transactions using this tag
  schemaVersion: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

## Appendix B: Monorepo Project Structure
```
.
├── lib/                                # Flutter source code
│   ├── models/                         # Dart models for Firestore documents
│   ├── services/                       # Business logic services
│   ├── repositories/                   # Data access layer
│   └── design/                         # Design tokens → Dart constants
├── android/ ios/ web/ linux/ macos/ windows/   # Flutter platform folders
├── firebase/
│   ├── firestore.rules                 # Firestore Security Rules
│   ├── firestore.indexes.json          # Firestore composite indexes
│   ├── storage.rules                   # Cloud Storage Security Rules (if needed)
│   ├── remoteconfig/                   # Remote Config templates & environments
│   │   ├── template.json               # Remote Config parameter template
│   │   ├── conditions.json             # Remote Config conditions for A/B testing
│   │   └── defaults.json               # Default Remote Config values
│   └── seeds/                          # Seed data scripts
├── scripts/                            # Build, release & utility scripts
├── docs/                               # Product & developer documentation
├── .github/workflows/                  # CI pipelines (tests, build, security scans, Firebase deployments)
├── firebase.json                       # Firebase project configuration
├── .firebaserc                         # Firebase project aliases
└── README.md

Phase 2 additions:
├── firebase/
│   ├── functions/                      # Cloud Functions (TypeScript)
│   │   ├── src/                        # Function source code
│   │   ├── package.json                # Function dependencies
│   │   └── tsconfig.json               # TypeScript configuration
├── package.json                        # Node dependencies for Cloud Functions
└── pnpm-workspace.yaml                 # pnpm workspace config
```

### Firebase Flutter Packages
```yaml
dependencies:
  # Firebase Core
  firebase_core: ^2.24.0
  
  # Authentication
  firebase_auth: ^4.15.0
  
  # Database
  cloud_firestore: ^4.13.0
  
  # Messaging
  firebase_messaging: ^14.7.0
  
  # Remote Config
  firebase_remote_config: ^4.3.0
  
  # Analytics & Monitoring
  firebase_analytics: ^10.7.0
  firebase_crashlytics: ^3.4.0
  firebase_performance: ^0.9.3
```

### UI & Design
- **Material 3** - Design system
- **Cupertino** - iOS-style widgets

### Essential Flutter Packages
```yaml
dependencies:
  # State Management
  flutter_riverpod: ^2.4.0
  riverpod_annotation: ^2.3.0
  
  # Navigation
  go_router: ^12.1.0
  
  # Data Classes & Serialization
  freezed: ^2.4.0
  json_annotation: ^4.8.0
  
  # Forms & Hooks
  flutter_hooks: ^0.20.0
  
  # Local Storage
  shared_preferences: ^2.2.0
  flutter_secure_storage: ^9.0.0
  
  # Network & Connectivity
  dio: ^5.3.0
  connectivity_plus: ^5.0.0
  
  # Device & App Info
  device_info_plus: ^9.1.0
  package_info_plus: ^4.2.0
  permission_handler: ^11.1.0
  
  # Biometric Authentication
  local_auth: ^2.1.6
  
  # CSV Import
  csv: ^5.0.2
  
  # Internationalization Foundation
  flutter_localizations:
    sdk: flutter
  intl: ^0.18.0
  
  # Utilities
  url_launcher: ^6.2.0
  share_plus: ^7.2.0
  
  # Development
  build_runner: ^2.4.0

dev_dependencies:
  # Code Generation
  riverpod_generator: ^2.3.0
  json_serializable: ^6.7.0
  freezed: ^2.4.0
  
  # Testing
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  mocktail: ^1.0.0
  
  # Linting & Analysis
  very_good_analysis: ^5.1.0
  
  # Build Tools
  flutter_launcher_icons: ^0.13.0
  flutter_native_splash: ^2.3.0
```
