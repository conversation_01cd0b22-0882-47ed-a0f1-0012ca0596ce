# Firebase Remote Config Integration

This document describes BudApp's Firebase Remote Config implementation, which provides server-side configuration management for predefined categories, premium limits, and feature flags.

## Overview

Firebase Remote Config allows the app to modify behavior and content without requiring users to download an app update. The integration provides:

- **Predefined Categories**: Server-managed income and expense categories
- **Premium Limits**: Dynamic limits for free and premium subscription tiers
- **Feature Flags**: A/B testing and gradual feature rollout capabilities
- **Configuration Templates**: Structured configuration management

## Architecture

### Service Layer

**RemoteConfigService** (`lib/services/remote_config_service.dart`)
- Centralized Firebase Remote Config instance management
- Configuration fetching and activation with error handling
- Default value management for offline scenarios
- Parameter parsing and type conversion
- Real-time update support (mobile platforms only)

### Repository Layer

**IRemoteConfigRepository** (`lib/data/repositories/interfaces/remote_config_repository.dart`)
- Clean interface for Remote Config operations
- Repository pattern integration with dependency injection

**RemoteConfigRepositoryImpl** (`lib/data/repositories/implementations/remote_config_repository_impl.dart`)
- Concrete implementation using RemoteConfigService
- Real-time configuration streaming (when supported)
- Status reporting and staleness detection

### Data Models

**RemoteConfigData** (`lib/data/models/remote_config_data.dart`)
- Immutable data structures using Freezed
- Type-safe parameter access
- JSON serialization support
- Default fallback values

```dart
@freezed
class RemoteConfigData with _$RemoteConfigData {
  const factory RemoteConfigData({
    required PredefinedCategories categories,
    required PremiumLimits premiumLimits,
    @Default(false) bool enableFeatureX,
    @Default(false) bool enableBetaFeatures,
  }) = _RemoteConfigData;
}
```

### Utility Functions

**RemoteConfigUtils** (`lib/utils/remote_config_utils.dart`)
- Convenient helper functions for accessing configuration data
- Business logic helpers for limit checking
- Integration with Riverpod providers

## Configuration Parameters

### Predefined Categories

```json
{
  "predefined_income_categories": [
    "Salary", "Freelance", "Investment", "Business", "Other Income"
  ],
  "predefined_expense_categories": [
    "Food & Dining", "Transportation", "Shopping", "Entertainment",
    "Bills & Utilities", "Healthcare", "Education", "Travel", "Other Expenses"
  ]
}
```

### Premium Limits

| Parameter | Free Tier | Premium Tier | Description |
|-----------|-----------|--------------|-------------|
| `max_accounts_free` | 3 | - | Maximum accounts for free users |
| `max_accounts_premium` | - | 50-100 | Maximum accounts for premium users |
| `max_custom_categories_free` | 5 | - | Maximum custom categories for free users |
| `max_custom_categories_premium` | - | 100 | Maximum custom categories for premium users |
| `max_budgets_free` | 10 | - | Maximum budgets for free users |
| `max_budgets_premium` | - | 100 | Maximum budgets for premium users |
| `max_goals_free` | 5 | - | Maximum goals for free users |
| `max_goals_premium` | - | 50 | Maximum goals for premium users |

### Feature Flags

- `enable_feature_x`: Boolean flag for experimental features
- `enable_beta_features`: Boolean flag for beta testing features
- `maintenance_mode`: Boolean flag for app maintenance mode
- `force_update_required`: Boolean flag for required app updates

## Usage Examples

### Accessing Predefined Categories

```dart
// Using RemoteConfigUtils
class TransactionCategoryScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final incomeCategories = RemoteConfigUtils.getIncomeCategories(ref);
    final expenseCategories = RemoteConfigUtils.getExpenseCategories(ref);
    
    return CategorySelector(
      incomeOptions: incomeCategories,
      expenseOptions: expenseCategories,
    );
  }
}
```

### Checking Premium Limits

```dart
// Check if user can create more accounts
final canCreateAccount = RemoteConfigUtils.canCreateAccount(
  ref,
  currentAccountCount: 2,
  isPremium: false,
); // Returns true (2 < 3 for free tier)

// Get remaining account slots
final remainingSlots = RemoteConfigUtils.getRemainingAccountSlots(
  ref,
  currentCount: 2,
  isPremium: false,
); // Returns 1 (3 - 2 = 1 remaining)
```

### Feature Flag Checking

```dart
// Check if a feature is enabled
final isFeatureEnabled = RemoteConfigUtils.isFeatureEnabled(
  ref,
  'enable_beta_features',
);

if (isFeatureEnabled) {
  // Show beta feature UI
}
```

### Refreshing Configuration

```dart
// Force refresh Remote Config (useful for debug/admin features)
await RemoteConfigUtils.refreshConfig(ref);
```

## Configuration Template

The Remote Config template is maintained in `firebase/remoteconfig_template.json` and includes:

### Conditions

- `premium_users`: Target users with premium subscription
- `beta_testers`: Target beta testing user group
- `app_version_1_1`: Target users with app version 1.1+

### Example Configuration

```json
{
  "conditions": [
    {
      "name": "premium_users",
      "expression": "user.properties.subscription_status == 'premium'"
    }
  ],
  "parameters": {
    "max_accounts_premium": {
      "defaultValue": {"value": "50"},
      "conditionalValues": {
        "premium_users": {"value": "100"}
      },
      "description": "Maximum number of accounts for premium tier users",
      "valueType": "NUMBER"
    }
  }
}
```

## Development Settings

### Fetch Intervals

- **Development**: 5 minutes for rapid iteration
- **Production**: 12 hours as per Firebase recommendations

### Real-time Updates

- **Mobile**: Supported via `onConfigUpdated` stream
- **Web**: Not supported (fetch manually)

### Debugging

```dart
// Check Remote Config status
final service = ref.read(remoteConfigServiceProvider);
debugPrint('Fetch Status: ${service.lastFetchStatus}');
debugPrint('Last Fetch: ${service.lastFetchTime}');
debugPrint('Is Stale: ${service.isStale}');
```

## Testing

### Unit Tests

Tests are located in `test/services/remote_config_service_test.dart` and `test/data/repositories/remote_config_repository_test.dart`.

```bash
# Run Remote Config specific tests
flutter test test/services/remote_config_service_test.dart
flutter test test/data/repositories/remote_config_repository_test.dart
```

### Integration Testing

Remote Config can be tested with Firebase Emulator:

```bash
# Start emulators (Remote Config testing support is limited)
firebase emulators:start --only auth,firestore

# Run integration tests
flutter test test/integration/
```

### Mock Testing

```dart
// Example mock for testing
class MockRemoteConfigService extends Mock implements RemoteConfigService {}

// In tests
setUp(() {
  container = ProviderContainer(
    overrides: [
      remoteConfigServiceProvider.overrideWithValue(
        MockRemoteConfigService(),
      ),
    ],
  );
});
```

## Best Practices

### Error Handling

- Always provide default values for fallback scenarios
- Use `AsyncValue.guard()` for safe error handling
- Don't throw exceptions for Remote Config failures

### Performance

- Cache Remote Config data appropriately
- Use the staleness check to avoid unnecessary fetches
- Respect Firebase's throttling limits

### Security

- Never store sensitive data in Remote Config parameters
- Validate parameter values on the client side
- Use server-side conditions for security-sensitive features

### Maintenance

- Keep the Remote Config template in version control
- Document parameter purposes and expected values
- Monitor fetch success rates and performance

## Deployment

### Template Upload

```bash
# Upload Remote Config template
firebase deploy --only remoteconfig
```

### Publishing Changes

1. Update parameters in Firebase Console
2. Test with development conditions
3. Gradually roll out to production users
4. Monitor metrics and user feedback

## Troubleshooting

### Common Issues

1. **Stale Data**: Check `isStale` and force refresh if needed
2. **Fetch Failures**: Verify network connectivity and Firebase project settings
3. **Type Errors**: Ensure parameter types match expected values
4. **Missing Parameters**: Check default values and parameter names

### Debug Commands

```dart
// Force fetch and activate
await service.fetchAndActivate();

// Check parameter values
final config = service.getCurrentConfig();
debugPrint('Current config: ${config.toJson()}');

// Monitor real-time updates
service.onConfigUpdated?.listen((update) {
  debugPrint('Config updated: ${update.updatedKeys}');
});
```

## Future Enhancements

- **A/B Testing**: Implement sophisticated A/B testing framework
- **Personalization**: User-specific configuration based on behavior
- **Analytics Integration**: Track Remote Config parameter effectiveness
- **Admin Dashboard**: Internal tools for configuration management
- **Rollback Capabilities**: Safe configuration rollback mechanisms

## Related Documentation

- [Firebase Remote Config Rules](docs/rules/firebase/firebase_remote_config.md)
- [Architecture Documentation](docs/architecture.md)
- [Testing Guide](docs/TESTING.md)
- [Development Workflow](docs/DEVELOPMENT_WORKFLOW.md)