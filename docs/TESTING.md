# Testing Guide

This document outlines the testing strategy and setup for BudApp, including unit tests, widget tests, and Firebase testing foundation with firebase_auth_mocks integration.

## Testing Strategy

### Test Types

1. **Unit Tests**: Test individual functions and business logic
2. **Widget Tests**: Test UI components and user interactions with Firebase context
3. **Integration Tests**: Test Firebase Auth + Firestore integration with FirebaseTestSetup
4. **Repository Tests**: Test data layer with firebase_auth_mocks + fake_cloud_firestore
5. **Service Tests**: Test business logic with mocked dependencies
6. **End-to-End Tests**: Test complete user flows (future)

### Current Test Coverage (620+ Tests)

- ✅ **Authentication Tests**: 44 tests with firebase_auth_mocks integration
- ✅ **Repository Tests**: 175+ tests with FirebaseTestSetup (Auth + Firestore simulation)
  - ✅ **Goal Repository**: 58 comprehensive tests covering all 29 IGoalRepository methods
  - ✅ **Goal Contribution Repository**: 38 comprehensive tests covering all 24 IGoalContributionRepository methods
  - ✅ **Transaction Repository**: 35 tests with atomic operations and balance management
  - ✅ **Account Repository**: 25 tests with CRUD operations and validation
  - ✅ **User Repository**: 20 tests with profile management and authentication
- ✅ **Service Tests**: 55 tests with appropriate dependency mocking
- ✅ **Widget Tests**: UI components with Firebase authentication context
- ✅ **Integration Tests**: Firebase Auth + Firestore integration testing
- ✅ **Material 3 UI**: Design system and component styling
- ✅ **Firebase Testing Foundation**: Comprehensive Firebase simulation capabilities

## Running Tests

### Quick Test Commands

```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/auth_test.dart
flutter test test/email_verification_widget_test.dart

# Run tests with coverage
flutter test --coverage

# Run tests in verbose mode
flutter test --verbose
```

### Test Files Structure

```
test/
├── data/
│   └── repositories/                   # Repository tests with FirebaseTestSetup
│       ├── goal_repository_test.dart          # 58 comprehensive tests for Goal Repository
│       ├── goal_contribution_repository_test.dart  # 38 comprehensive tests for Goal Contribution Repository
│       ├── transaction_repository_test.dart
│       ├── account_repository_test.dart
│       ├── user_repository_test.dart
│       └── ...
├── services/                          # Service tests with dependency mocking
│   ├── session_service_test.dart
│   ├── secure_storage_service_test.dart
│   └── remote_config_service_test.dart
├── features/
│   ├── auth/                          # Authentication tests with firebase_auth_mocks
│   │   ├── services/
│   │   └── presentation/
│   ├── dashboard/                     # Widget tests with Firebase context
│   ├── transactions/                  # Feature-specific integration tests
│   └── budgets/
├── integration/                       # Firebase integration tests
│   ├── account_repository_integration_test.dart
│   └── auth_navigation_test.dart
├── helpers/                           # Testing utilities and setup
│   ├── firebase_test_setup.dart      # Firebase Auth + Firestore test setup
│   ├── mock_providers.dart           # Provider overrides with firebase_auth_mocks
│   └── test_wrapper.dart             # Widget testing wrapper
└── examples/                          # Testing pattern examples
    └── firebase_testing_foundation_example_test.dart
```

## Firebase Testing Foundation

### Overview

BudApp uses a comprehensive Firebase testing foundation that provides real Firebase Auth + Firestore behavior simulation for reliable testing across all layers.

### Key Components

#### 1. FirebaseTestSetup
Central utility for creating Firebase test environments with authenticated users:

```dart
// Create test setup with authenticated user
final testSetup = await FirebaseTestSetup.createWithAuthenticatedUser(
  uid: 'test-user-id',
  email: '<EMAIL>',
);

// Use in repository tests
final firestoreService = FirestoreService(testSetup.firestore);
final repository = RepositoryImpl(firestoreService, testSetup.auth);

// Clean up resources
await testSetup.dispose();
```

#### 2. firebase_auth_mocks Integration
All authentication tests use firebase_auth_mocks for consistent Firebase Auth behavior:

```dart
// MockProviders automatically use firebase_auth_mocks
final container = ProviderContainer(
  overrides: MockProviders.authenticatedUserOverrides(),
);
```

#### 3. Testing Patterns by Layer

**Repository Tests**: Use FirebaseTestSetup for Firebase Auth + Firestore integration
**Service Tests**: Use mocktail for dependency mocking (correct pattern)
**Widget Tests**: Use MockProviders with firebase_auth_mocks integration
**Integration Tests**: Use FirebaseTestSetup for comprehensive Firebase simulation

#### 4. Goal Repository Testing Patterns

The Goal Repository test suite demonstrates comprehensive testing patterns for complex Firebase repository implementations:

**Test Organization (8 groups, 58 tests):**
- **Basic CRUD Operations**: Create, read, update, delete operations with proper validation
- **Goal Management Operations**: Goal lifecycle management (activate, deactivate, complete)
- **Search and Analytics**: Search functionality, statistics, and summary calculations
- **Real-time Streams**: Live data updates with proper async stream testing
- **Validation and Business Logic**: Input validation, business rules, and domain constraints
- **Date-based Operations**: Deadline tracking and date range queries
- **Error Handling**: Authentication errors, invalid data, and edge cases
- **Edge Cases and Integration**: Concurrent operations, large datasets, and performance testing

**Key Testing Techniques:**
```dart
// Helper method pattern for test data generation
Goal createTestGoal({
  String? id,
  String? name,
  // ... other parameters
}) {
  return Goal(
    id: id ?? 'test-goal-${DateTime.now().millisecondsSinceEpoch}',
    // ... with sensible defaults
  );
}

// Real-time stream testing with proper async handling
test('watchUserGoals should emit active goals', () async {
  final stream = goalRepository.watchUserGoals(testUserId);
  final streamTest = expectLater(
    stream.take(2),
    emitsInOrder([
      isEmpty, // Initial empty state
      hasLength(1), // After adding goal
    ]),
  );
  
  await Future<void>.delayed(const Duration(milliseconds: 100));
  await goalRepository.createGoal(createTestGoal());
  await streamTest;
});

// Concurrent operations testing
test('should handle concurrent goal creation', () async {
  final goals = List.generate(5, (i) => createTestGoal(name: 'Goal $i'));
  final futures = goals.map((goal) => goalRepository.createGoal(goal));
  final results = await Future.wait(futures);
  
  expect(results, hasLength(5));
  expect(results.every((id) => id.isNotEmpty), isTrue);
});
```

**Business Logic Testing:**
- Goal progress tracking with automatic completion detection
- Goal status transitions (active → completed → active)
- Name uniqueness validation with exclusion handling
- Date-based filtering and deadline calculations
- Statistics and analytics calculations

#### 5. Goal Contribution Repository Testing Patterns

The Goal Contribution Repository test suite demonstrates advanced testing patterns for subcollection management and complex validation:

**Test Organization (10 groups, 38 tests):**
- **Basic CRUD Operations**: Create, read, update, delete with subcollection handling
- **Goal-Specific Operations**: Contribution management per goal with aggregation
- **Date-Based Filtering**: Time-based queries and contribution tracking
- **User-Level Operations**: Cross-goal contribution management
- **Real-time Streams**: Live updates for contribution data
- **Validation and Business Logic**: Amount limits, date validation, goal validation
- **Error Handling**: Invalid goals, validation failures, authentication errors
- **Advanced Validation and Edge Cases**: Boundary conditions and data integrity
- **Concurrent Operations**: Race conditions and multi-user scenarios
- **Performance and Stress Testing**: Large datasets and efficiency testing

**Advanced Testing Techniques:**
```dart
// Validation testing with boundary conditions
test('createContribution should handle extremely large amounts', () async {
  final largeContribution = GoalContribution.create(
    userId: 'test-user-1',
    goalId: testGoalId,
    amountCents: 1000000000, // $10M - exceeds limit
    contributionDate: DateTime.now(),
  );

  expect(
    () => contributionRepository.createContribution(testGoalId, largeContribution),
    throwsA(isA<Exception>()),
  );
});

// Concurrent operations testing
test('should handle concurrent contribution creation', () async {
  final contributions = List.generate(5, (i) => GoalContribution.create(
    userId: 'test-user-1',
    goalId: testGoalId,
    amountCents: 1000 * (i + 1),
    contributionDate: DateTime.now(),
  ));

  final futures = contributions.map((contribution) => 
    contributionRepository.createContribution(testGoalId, contribution));
  final results = await Future.wait(futures);

  expect(results, hasLength(5));
  expect(results.every((id) => id.isNotEmpty), isTrue);
});

// Performance testing with large datasets
test('should handle large numbers of contributions efficiently', () async {
  final contributions = List.generate(50, (i) => GoalContribution.create(
    userId: 'test-user-1',
    goalId: testGoalId,
    amountCents: 1000 * (i + 1),
    contributionDate: DateTime.now().subtract(Duration(days: i)),
  ));

  for (final contribution in contributions) {
    await contributionRepository.createContribution(testGoalId, contribution);
  }

  final stopwatch = Stopwatch()..start();
  final allContributions = await contributionRepository.getContributionsForGoal(testGoalId);
  stopwatch.stop();

  expect(allContributions, hasLength(50));
  expect(stopwatch.elapsedMilliseconds, lessThan(5000));
});
```

**Domain-Specific Testing:**
- Amount validation with business rules ($0.01 - $99,999,999.99)
- Date validation (no future dates, maximum 10 years ago)
- Description length limits (500 characters maximum)
- Subcollection path validation (users/{userId}/goals/{goalId}/contributions)
- Cross-goal contribution aggregation and statistics
- Soft deletion with isActive flag management

### Security Rules Testing Ready

The foundation is prepared for comprehensive security rules testing with proper authentication context across all Firebase operations.

## Firebase Emulator Testing

### Setup

The project includes Firebase emulator configuration for comprehensive testing:

```json
{
  "emulators": {
    "auth": { "port": 9099 },
    "firestore": { "port": 8080 },
    "ui": { "enabled": true, "port": 4000 },
    "singleProjectMode": true
  }
}
```

### Starting Emulators

```bash
# Start emulators manually
firebase emulators:start --only auth,firestore

# Or use the provided script
./scripts/start_emulators.sh
```

### Emulator URLs

- **Auth Emulator**: http://localhost:9099
- **Firestore Emulator**: http://localhost:8080
- **Emulator UI**: http://localhost:4000

### Running Tests with Emulators

```bash
# Run integration tests with emulators (future)
./scripts/test_with_emulators.sh

# Or manually
firebase emulators:exec --only auth,firestore "flutter test test/integration/"
```

## Test Categories

### 1. Authentication Tests (`auth_test.dart`)

Tests core authentication validation logic:

- Email format validation
- Password strength requirements
- Confirm password matching
- Form validation edge cases

```dart
test('Email validation should work correctly', () {
  expect(AuthValidators.email('<EMAIL>'), isNull);
  expect(AuthValidators.email('invalid'), equals('Please enter a valid email address'));
});
```

### 2. Widget Tests (`email_verification_widget_test.dart`)

Tests UI components without Firebase dependency:

- Email verification screen layout
- Material 3 styling compliance
- Button interactions
- Error state handling

```dart
testWidgets('EmailVerificationScreen displays correctly', (tester) async {
  await tester.pumpWidget(MaterialApp(home: EmailVerificationScreen()));
  expect(find.text('Verify Your Email'), findsAtLeastNWidgets(1));
});
```

### 3. Firebase Service Tests

Tests Firebase service integration:

- Service initialization checks
- Error handling for uninitialized state
- Mock Firebase interactions

```dart
test('Firebase service throws appropriate error when not initialized', () {
  expect(() => FirebaseService.auth, throwsA(isA<Exception>()));
});
```

## Testing Best Practices

### 1. Test Organization

- Group related tests using `group()` blocks
- Use descriptive test names that explain the expected behavior
- Follow the Arrange-Act-Assert pattern

### 2. Widget Testing

- Use `testWidgets()` for UI component testing
- Always call `await tester.pumpAndSettle()` after widget creation
- Test both positive and negative scenarios

### 3. Firebase Testing

- Use emulators for all Firebase-related tests
- Clean up authentication state between tests
- Test error scenarios and edge cases

### 4. Test Data

- Use consistent test data across tests
- Create helper functions for common test scenarios
- Avoid hardcoded values in test assertions

## Continuous Integration

### GitHub Actions (Future)

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - run: flutter pub get
      - run: flutter test
      - run: flutter analyze
```

### Pre-commit Hooks

Ensure all tests pass before committing:

```bash
# Run all quality checks
flutter test
flutter analyze
dart format --set-exit-if-changed .
```

## Troubleshooting

### Common Issues

1. **Firebase not initialized**: Ensure emulators are running for integration tests
2. **Widget not found**: Check that `await tester.pumpAndSettle()` is called
3. **Test timeouts**: Increase timeout for slow operations

### Debug Tips

```bash
# Run tests with verbose output
flutter test --verbose

# Run specific test with debugging
flutter test test/auth_test.dart --verbose

# Check test coverage
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
open coverage/html/index.html
```

## Future Enhancements

### Planned Test Additions

1. **Integration Tests**: Complete Firebase Auth flow testing
2. **E2E Tests**: Full user journey testing with `integration_test`
3. **Performance Tests**: Widget rendering and Firebase operation performance
4. **Accessibility Tests**: Screen reader and accessibility compliance

### Test Infrastructure

1. **Automated Testing**: CI/CD pipeline with automated test execution
2. **Test Reporting**: Coverage reports and test result dashboards
3. **Mock Services**: Comprehensive mocking for external dependencies
4. **Test Data Management**: Automated test data setup and cleanup

## Resources

- [Flutter Testing Documentation](https://docs.flutter.dev/testing)
- [Firebase Emulator Suite](https://firebase.google.com/docs/emulator-suite)
- [Widget Testing Guide](https://docs.flutter.dev/cookbook/testing/widget)
- [Integration Testing](https://docs.flutter.dev/testing/integration-tests)
