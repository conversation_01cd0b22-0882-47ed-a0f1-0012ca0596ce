# Bottom Navigation Implementation

## Overview
BudApp features a custom bottom navigation bar that provides intuitive access to all major app sections while maintaining Material 3 design principles and accessibility standards.

## Design Specifications

### Visual Design
- **Height**: 64px (reduced from standard 80px)
- **Background**: Surface color with top border
- **Selection Color**: Teal (#01A2A1) from design tokens
- **Icons**: Outline style (no fill when selected)
- **Icon Size**: 28px (larger than standard for better visibility)
- **Typography**: Body small for labels

### Navigation Items
1. **Home** (`/home`) - Home outline icon
2. **Accounts** (`/accounts`) - Account balance outline icon  
3. **Add Transaction** (Center) - Special circular teal button with plus icon
4. **Categories/Budgets** (Toggle) - Category/wallet outline icons
5. **Profile** (`/profile`) - Person outline icon

### Categories/Budgets Toggle
The fourth navigation item toggles between Categories and Budgets:
- **Categories Mode**: Shows category icon, navigates to `/categories`
- **Budgets Mode**: Shows wallet icon, navigates to `/budgets`
- State persisted in SharedPreferences
- Toggle behavior: tapping when already on the current route switches modes

## Technical Implementation

### Architecture
```
CustomBottomNavigation
├── BottomNavItemWidget (4 standard items)
└── CenterAddButton (special add transaction button)
```

### Key Components

#### 1. CustomBottomNavigation (`lib/widgets/navigation/custom_bottom_navigation.dart`)
- Main container widget with Row layout
- Integrates with GoRouter for navigation
- Manages toggle state and route detection

#### 2. BottomNavProviders (`lib/widgets/navigation/bottom_nav_providers.dart`)
- `CategoryBudgetToggle` enum and notifier
- `BottomNavItem` data class
- `bottomNavItems` provider with toggle-aware item generation
- `BottomNavIndex` notifier for current route tracking

#### 3. BottomNavItem (`lib/widgets/navigation/bottom_nav_item.dart`)
- `BottomNavItemWidget` for standard navigation items
- `CenterAddButton` for the special add transaction button
- Haptic feedback integration
- Accessibility support

### State Management
- **Riverpod** providers for navigation state
- **SharedPreferences** for toggle persistence
- **GoRouter** integration for route management

### Animation Handling
The center add button features a subtle pulse animation that is automatically disabled during tests to prevent `pumpAndSettle` timeout issues:

```dart
// Test environment detection prevents animation conflicts
final isInTest = WidgetsBinding.instance.runtimeType.toString().contains('Test');
if (!isInTest) {
  _pulseController.repeat(reverse: true);
}
```

## Integration Points

### MainAppShell Updates
- Replaced standard `NavigationBar` with `CustomBottomNavigation`
- Removed separate FAB since add button is integrated
- Added conditional visibility based on route

### Route Configuration
Navigation integrates with existing GoRouter configuration:
- Home: `/home`
- Accounts: `/accounts` 
- Add Transaction: `/transactions/add`
- Categories: `/categories`
- Budgets: `/budgets`
- Profile: `/profile`

## Accessibility Features
- Semantic labels for all navigation items
- Proper touch targets (minimum 48x48dp)
- High contrast support
- Screen reader compatibility
- Haptic feedback for user interaction

## Testing Considerations
- Animation detection prevents test timeouts
- Comprehensive widget tests for all navigation scenarios
- Integration tests verify route navigation
- Toggle state persistence testing

## Future Enhancements
- Badge support for notifications
- Dynamic icon updates based on app state
- Gesture-based navigation shortcuts
- Customizable navigation order
