# Authentication System

## Overview

BudApp implements a comprehensive authentication system using Firebase Auth with a modern Material 3 UI design. The system supports multiple authentication methods and provides a seamless user experience across all platforms.

## Features

### ✅ Completed Features
- **Material 3 UI Design**: Modern, accessible authentication screens
- **Dark/Light Theme Support**: Automatic system theme detection
- **Email/Password Authentication**: Complete Firebase Auth integration with login and signup
- **Email Verification Flow**: Automatic email verification with resend capability and user-friendly UI
- **Password Reset Flow**: Complete forgot password functionality with Firebase Auth integration
- **Google Sign-In Integration**: Complete Google OAuth authentication with Firebase Auth
- **Authentication State Management**: AuthWrapper with email verification status checking
- **Form Validation**: Comprehensive client-side validation with real-time feedback
- **Loading States**: Visual feedback during all authentication processes
- **Error Handling**: Comprehensive Firebase Auth error handling with user-friendly messages
- **Responsive Design**: Works across different screen sizes and orientations
- **Accessibility**: Proper labels, focus management, screen reader support
- **Security**: Proper BuildContext safety and async operation handling

### 🔄 In Progress

### 📋 Planned
- **OAuth Integration**: Google and Apple Sign-In implementation
- **Biometric Authentication**: Face ID/Touch ID/Fingerprint support
- **Session Management**: JWT handling and automatic refresh
- **Account Management**: Profile settings and account deletion

## Architecture

### Authentication Flow
```
App Launch → AuthWrapper → Check Auth State
                        ├─ Not Authenticated → LoginScreen
                        ├─ Authenticated + Email Verified → HomeScreen
                        └─ Authenticated + Email Unverified → EmailVerificationScreen
```

### Component Structure
```
lib/
├── screens/auth/
│   ├── auth_wrapper.dart              # Authentication state management with email verification
│   ├── login_screen.dart              # Login form with Firebase Auth integration
│   ├── signup_screen.dart             # Registration form with automatic email verification
│   ├── email_verification_screen.dart # Email verification flow with resend capability
│   └── forgot_password_screen.dart    # Password reset flow with Firebase Auth
├── services/
│   ├── firebase_service.dart          # Firebase initialization and configuration
│   └── auth_service.dart              # Centralized authentication service with Google Sign-In
├── widgets/auth/
│   ├── auth_form_field.dart   # Custom form field with validation
│   ├── auth_button.dart       # Primary/secondary authentication buttons
│   └── social_login_button.dart # Google/Apple sign-in buttons
└── config/
    ├── app_theme.dart         # Material 3 theme configuration
    └── design_tokens.dart     # Design system tokens
```

## Design System

### Design Tokens
The authentication system uses a comprehensive design token system defined in `lib/config/design_tokens.dart`:

- **Colors**: Material 3 color scheme with light/dark theme support
- **Typography**: Consistent font sizes, weights, and styles
- **Spacing**: Standardized spacing values (xs, sm, md, lg, xl, xxl)
- **Border Radius**: Consistent corner radius values
- **Financial Colors**: Success, warning, info, and error colors

### Theme Configuration
- **Automatic Theme Detection**: Uses `ThemeMode.system` to follow device settings
- **Material 3 Support**: Full Material 3 color scheme and components
- **Consistent Styling**: All authentication components follow design tokens
- **Accessibility**: High contrast ratios and proper color usage

## UI Components

### AuthFormField
Custom form field component with:
- Consistent styling using design tokens
- Built-in validation support
- Password visibility toggle
- Proper accessibility labels
- Loading and error states

### SocialLoginButton
Social authentication buttons with:
- Platform-specific branding (Google, Apple)
- Loading states
- Proper color schemes for light/dark themes
- Accessibility support

### AuthButton
Primary and secondary authentication buttons with:
- Loading states
- Consistent styling
- Proper touch targets
- Accessibility support

## Form Validation

### Validation Rules
- **Email**: Format validation using regex
- **Password**: Minimum 6 characters requirement
- **Confirm Password**: Must match original password
- **Required Fields**: All required fields validated

### User Experience
- **Real-time Validation**: Validation on field blur and form submission
- **Clear Error Messages**: User-friendly error descriptions
- **Visual Feedback**: Error states with proper color coding
- **Accessibility**: Screen reader compatible error announcements

## Authentication Methods

### Email/Password Authentication
- **Login**: Email and password with Firebase Auth integration
- **Signup**: Email, password, confirm password with automatic email verification
- **Email Verification**: Automatic verification email sending with resend capability
- **Verification Flow**: Dedicated screen with periodic verification checking
- **Validation**: Comprehensive client-side validation with Firebase Auth integration
- **Error Handling**: Complete Firebase Auth error mapping to user-friendly messages
- **Rate Limiting**: 60-second rate limiting for resend verification emails
- **User Experience**: Clear instructions and feedback throughout verification process

### Password Reset Flow
- **Forgot Password Screen**: Dedicated UI for password reset requests
- **Firebase Integration**: Uses Firebase Auth `sendPasswordResetEmail` method
- **Email Validation**: Comprehensive email validation before sending reset link
- **Success State**: Clear feedback when reset email is sent successfully
- **Error Handling**: Comprehensive Firebase Auth error handling with user-friendly messages
- **Resend Functionality**: Ability to resend password reset email
- **Navigation**: Seamless navigation from login screen and back
- **User Experience**: Clear instructions and visual feedback throughout the process

### Google Sign-In Integration
- **Complete OAuth Flow**: Full Google Sign-In implementation with Firebase Auth
- **Unified AuthService**: Centralized authentication service handling all auth methods
- **Cross-Platform Support**: Works on both login and signup screens
- **Error Handling**: Comprehensive error handling for Google Sign-In specific errors
- **User Feedback**: Clear success/error messages with user display names
- **Security**: Proper credential handling and Firebase Auth integration
- **Provider Detection**: Ability to detect which authentication providers user has linked
- **Sign-Out Management**: Proper Google Sign-In and Firebase Auth sign-out coordination

### Social Authentication (UI Ready)
- **Google Sign-In**: Google branding with proper OAuth flow (UI complete, integration planned)
- **Apple Sign-In**: Apple branding with Sign in with Apple guidelines (UI complete, integration planned)
- **Loading States**: Visual feedback during OAuth processes
- **Error Handling**: OAuth-specific error handling

## Security Features

### Client-Side Security
- **Input Validation**: Comprehensive validation before submission
- **Password Visibility**: Secure password input with toggle option
- **Form Protection**: CSRF protection and input sanitization
- **Session Management**: Secure token handling (planned)

### Firebase Security
- **Authentication Rules**: Firebase Auth security rules
- **Data Protection**: User data isolation and protection
- **Secure Communication**: HTTPS-only communication
- **Token Management**: JWT token handling and refresh

## Testing

### Current Tests
- **Widget Tests**: Login, signup, and forgot password screen component testing
- **Validation Tests**: Comprehensive form validation logic testing
- **Authentication Tests**: Email/password authentication validation testing
- **Password Reset Tests**: Forgot password screen UI and validation testing
- **AuthService Tests**: Centralized authentication service API testing
- **Google Sign-In Tests**: Google authentication integration testing
- **UI Tests**: Component rendering and interaction testing

### Planned Tests
- **Integration Tests**: End-to-end authentication, email verification, and password reset flow testing
- **Unit Tests**: Firebase Auth service testing with emulator
- **Security Tests**: Authentication security validation
- **Email Verification Tests**: Email verification flow testing with Firebase emulator
- **Password Reset Integration Tests**: End-to-end password reset flow testing with Firebase emulator

## Usage Examples

### Basic Login Flow
```dart
// Navigate to login screen
Navigator.push(context, MaterialPageRoute(
  builder: (context) => const LoginScreen(),
));

// AuthWrapper automatically handles authentication state
MaterialApp(
  home: const AuthWrapper(), // Shows LoginScreen or HomeScreen
)
```

### Custom Form Field
```dart
AuthFormField(
  label: 'Email',
  controller: emailController,
  validator: AuthValidators.email,
  keyboardType: TextInputType.emailAddress,
  prefixIcon: Icon(Icons.email_outlined),
)
```

### Social Login Button
```dart
SocialLoginButton.google(
  onPressed: handleGoogleLogin,
  isLoading: isGoogleLoading,
)
```

### Email Verification Screen
```dart
// EmailVerificationScreen automatically handles:
// - Periodic verification checking (every 3 seconds)
// - Resend verification email with rate limiting
// - User-friendly UI with clear instructions
// - Sign out functionality
// - Automatic navigation when verified

// AuthWrapper automatically routes to EmailVerificationScreen
// when user is authenticated but email is not verified
```

### Password Reset Flow
```dart
// Navigate to forgot password screen from login
Navigator.push(context, MaterialPageRoute(
  builder: (context) => const ForgotPasswordScreen(),
));

// ForgotPasswordScreen automatically handles:
// - Email validation
// - Firebase Auth sendPasswordResetEmail
// - Success/error state management
// - User-friendly feedback
// - Navigation back to login
```

### Google Sign-In Integration
```dart
// Google Sign-In for login
try {
  final userCredential = await AuthService.signInWithGoogle();
  // User successfully signed in
  print('Welcome, ${userCredential.user?.displayName}!');
} on FirebaseAuthException catch (e) {
  // Handle specific Firebase Auth errors
  switch (e.code) {
    case 'sign_in_canceled':
      print('Google sign-in was canceled');
      break;
    case 'network-request-failed':
      print('Network error occurred');
      break;
    default:
      print('Google sign-in failed: ${e.message}');
  }
}

// Google Sign-Up (same as sign-in for Google)
final userCredential = await AuthService.signUpWithGoogle();

// Check authentication providers
final providers = AuthService.getUserProviders();
final isGoogleUser = AuthService.isSignedInWithGoogle;
final isEmailUser = AuthService.isSignedInWithEmailPassword;
```

## Session Management

### Enhanced Session Management
BudApp implements comprehensive session management through the `SessionService` class:

- **Automatic Session Restoration**: Sessions are restored on app startup
- **User Preferences Persistence**: App-specific preferences stored locally
- **Session Analytics**: Tracks session duration, count, and authentication methods
- **Enhanced Error Handling**: Comprehensive error handling with user-friendly messages
- **Real-time State Updates**: Session state changes broadcast via streams

### Session States
```dart
enum SessionState {
  loading,           // Session is being initialized
  authenticated,     // User is authenticated and verified
  unauthenticated,   // User is not authenticated
  emailNotVerified,  // User authenticated but email not verified
  error,            // Session error occurred
}
```

### Usage
```dart
// Listen to session changes
SessionService.instance.sessionStream.listen((sessionInfo) {
  // Handle session state changes
});

// Get current session
final session = SessionService.instance.currentSession;

// Update user preferences
await SessionService.instance.updatePreferences({
  'theme_mode': 'dark',
  'notifications_enabled': false,
});
```

For detailed session management documentation, see [SESSION_MANAGEMENT.md](SESSION_MANAGEMENT.md).

## Error Handling

### Comprehensive Error Management
BudApp implements robust error handling for all authentication flows:

- **Centralized Error Service**: `AuthErrorService` provides consistent error mapping
- **User-Friendly Messages**: Firebase Auth exceptions converted to readable messages
- **Contextual Messaging**: Error messages adapted based on authentication context
- **Severity Levels**: Errors categorized by severity (info, warning, error, critical)
- **Suggested Actions**: Actionable suggestions provided for each error type
- **Retry Functionality**: Automatic retry options for recoverable errors

### Error Categories
```dart
enum ErrorCategory {
  authentication,  // Login/signup related errors
  network,        // Network connectivity issues
  validation,     // Input validation errors
  permission,     // Account permissions/restrictions
  rateLimit,      // Too many requests
  system,         // System/unknown errors
  user,           // User-initiated actions (e.g., canceled)
}
```

### Error Severity Levels
```dart
enum ErrorSeverity {
  info,     // Informational messages
  warning,  // Warnings that don't block functionality
  error,    // Errors that block functionality
  critical, // Critical errors requiring immediate attention
}
```

### Usage Examples
```dart
// Handle authentication errors
try {
  await AuthService.signInWithEmailAndPassword(email, password);
} catch (e) {
  final authError = AuthService.handleAuthError(e);

  // Show error with SnackBar
  AuthErrorSnackBar.show(
    context,
    authError,
    contextName: 'login',
    onRetry: authError.isRetryable ? () => retryLogin() : null,
  );

  // Or use banner widget
  AuthErrorBanner(
    error: authError,
    context: 'login',
    onRetry: () => retryLogin(),
    showSuggestedActions: true,
  );
}
```

### Error Mapping Examples
- `user-not-found` → "No account found with this email address. Please check your email or create a new account."
- `wrong-password` → "Incorrect password. Please try again or reset your password."
- `email-already-in-use` → "An account already exists with this email address. Please sign in instead."
- `weak-password` → "Password is too weak. Please choose a stronger password with at least 6 characters."
- `too-many-requests` → "Too many failed attempts. Please wait a few minutes before trying again."

### Contextual Messages
Error messages are adapted based on the authentication context:
- **Login context**: Suggests checking credentials or creating account
- **Signup context**: Suggests using different email or signing in
- **Password reset context**: Provides specific guidance for password recovery

## Configuration

### Theme Configuration
The authentication system automatically uses the app's theme configuration:
- Light theme: Defined in `AppTheme.lightTheme`
- Dark theme: Defined in `AppTheme.darkTheme`
- System detection: `ThemeMode.system` in MaterialApp

### Firebase Configuration
Firebase Auth is configured through the existing Firebase service:
- Multi-environment support (dev, staging, prod)
- Automatic initialization
- Service connectivity testing

## Next Steps

1. **Implement Apple Sign-In**: Add Apple OAuth authentication
2. **Add Biometric Auth**: Implement biometric authentication
3. **Session Management**: Implement JWT handling and refresh
4. **Account Management**: Add profile settings and account deletion
5. **Enhanced Security**: Add additional security features and monitoring

## Troubleshooting

### Common Issues
1. **Theme not applying**: Ensure `AppTheme` is imported and used in MaterialApp
2. **Validation not working**: Check that validators are properly assigned to form fields
3. **Loading states not showing**: Verify that loading state variables are properly managed
4. **Navigation issues**: Ensure proper navigation context and route management

### Debug Tips
- Use Flutter Inspector to debug widget tree
- Check console for validation errors
- Verify theme configuration in debug mode
- Test on both light and dark themes
