# Codacy Integration Guide

## Overview

This document provides comprehensive guidance for Codacy integration in the BudApp Flutter project, focusing on code quality analysis and coverage reporting optimized for business logic while excluding generated files.

## Table of Contents

- [Quick Start](#quick-start)
- [Configuration Overview](#configuration-overview)
- [Generated File Handling](#generated-file-handling)
- [CI/CD Integration](#cicd-integration)
- [Local Development](#local-development)
- [Coverage Reporting](#coverage-reporting)
- [Quality Standards](#quality-standards)
- [Troubleshooting](#troubleshooting)
- [Best Practices](#best-practices)

## Quick Start

### Prerequisites
- Flutter 3.32.6+ with Dart SDK
- Codacy project token (stored in GitHub secrets)
- Project configured with `.codacy.yml` and `.codacy/cli-config.yaml`

### Running Local Analysis
```bash
# Install Codacy CLI (one-time setup)
curl -L https://github.com/codacy/codacy-analysis-cli/releases/latest/download/codacy-analysis-cli-linux -o codacy-analysis-cli
chmod +x codacy-analysis-cli

# Run analysis focusing on business logic
./codacy-analysis-cli analyze --tool dartanalyzer --directory . --parallel 4

# Generate and upload coverage (requires token)
flutter test --coverage
export CODACY_PROJECT_TOKEN=your_token_here
bash <(curl -Ls https://coverage.codacy.com/get.sh) report --coverage-reports coverage/lcov.info
```

## Configuration Overview

### Architecture Decision
The BudApp Codacy integration uses a **hybrid configuration approach**:

1. **`.codacy.yml`** (Project Root) - Universal exclusions and tool configuration
2. **`.codacy/cli-config.yaml`** - Local CLI analysis customization  
3. **`.codacy/tools-configs/analysis_options.yaml`** - Dart analyzer specific rules

This approach provides:
- ✅ Centralized exclusion management
- ✅ Business logic focus (excludes 38+ generated files)
- ✅ Alignment with `very_good_analysis` standards
- ✅ Multi-environment CI/CD support

### Core Configuration Files

#### .codacy.yml (Project Root)
```yaml
exclude_paths:
  - "**/*.g.dart"           # JSON serialization (Riverpod, Freezed)
  - "**/*.freezed.dart"     # Immutable data models
  - "**/*.gr.dart"          # go_router generation
  - "**/*.config.dart"      # Configuration files
  - "**/generated_plugin_registrant.dart"
  - "build/**"
  - ".dart_tool/**"
  - "coverage/**"
  - "test/.test_coverage.dart"
  - "integration_test/driver.dart"

engines:
  dartanalyzer:
    enabled: true
    configuration_file: ".codacy/tools-configs/analysis_options.yaml"
  checkov:
    enabled: true
  trivy:
    enabled: true
```

#### .codacy/cli-config.yaml (Enhanced Local Analysis)
```yaml
analysis:
  max_issues: 100000
  parallel: 4
  tools:
    - dartanalyzer
    - trivy
  memory_limit: "2048M"
  timeout: "30m"

output:
  format: "json"
  file: "codacy-results.json"
  pretty: true

quality:
  threshold: 80
  fail_below_threshold: false
```

## Generated File Handling

### Problem Statement
BudApp uses extensive code generation:
- **26 `.g.dart` files** (Riverpod providers, JSON serialization)
- **12 `.freezed.dart` files** (Immutable data models)
- **Auto-generated localization** files
- **Firebase configuration** files

### Solution Strategy
**Configuration-level filtering** ensures generated files are excluded from:
- Static analysis scoring
- Coverage calculations
- Issue reporting
- Quality metrics

### Exclusion Patterns
```yaml
# Generated Code Patterns (Comprehensive)
exclude_paths:
  # Riverpod & JSON Serialization
  - "lib/data/models/*.g.dart"
  - "lib/features/**/providers/*.g.dart" 
  - "lib/features/**/services/*.g.dart"
  
  # Freezed Immutable Models
  - "lib/data/models/*.freezed.dart"
  
  # Router Generation
  - "lib/routing/*.gr.dart"
  
  # Localization (Auto-generated)
  - "lib/l10n/app_localizations*.dart"
  
  # Firebase Configuration
  - "lib/firebase_options*.dart"
  
  # Build Artifacts
  - "build/**"
  - ".dart_tool/**"
  - "coverage/**"
```

### Business Logic Focus
Analysis concentrates on:
- ✅ `lib/features/*/presentation/` (UI components)
- ✅ `lib/features/*/services/` (Business logic)
- ✅ `lib/data/repositories/` (Data layer interfaces)
- ✅ `lib/services/` (Global services)
- ✅ `lib/providers/` (State management)

## CI/CD Integration

### GitHub Actions Workflow
The Codacy integration is embedded in `.github/workflows/code-quality.yml`:

#### Coverage Upload (Enhanced)
```yaml
- name: Upload coverage to Codacy
  run: |
    # Filter generated files for accurate metrics
    lcov --remove coverage/lcov.info \
      '**/*.g.dart' \
      '**/*.freezed.dart' \
      '**/*.config.dart' \
      --output-file coverage/lcov_filtered.info
    
    # Upload filtered coverage
    bash <(curl -Ls https://coverage.codacy.com/get.sh) report \
      --coverage-reports coverage/lcov_filtered.info \
      --project-token ${{ secrets.CODACY_PROJECT_TOKEN }}
```

#### Static Analysis Job
```yaml
codacy-analysis:
  runs-on: ubuntu-latest
  steps:
    - name: Run Codacy Analysis
      run: |
        ./codacy-analysis-cli analyze \
          --tool dartanalyzer \
          --directory . \
          --format json \
          --max-allowed-issues 100 \
          --parallel 4
```

### Multi-Environment Support
- **Development**: Automated analysis on push to `development` branch
- **Staging**: Quality gates on PR to `main` branch  
- **Production**: Comprehensive analysis on `main` branch merges

## Local Development

### CLI Installation
```bash
# Download latest Codacy CLI
curl -L https://github.com/codacy/codacy-analysis-cli/releases/latest/download/codacy-analysis-cli-linux -o codacy-analysis-cli
chmod +x codacy-analysis-cli

# Move to PATH (optional)
sudo mv codacy-analysis-cli /usr/local/bin/
```

### Analysis Commands
```bash
# Full project analysis
codacy-analysis-cli analyze --tool dartanalyzer --directory .

# Specific directory analysis
codacy-analysis-cli analyze --tool dartanalyzer --directory lib/features/transactions

# JSON output for processing
codacy-analysis-cli analyze --tool dartanalyzer --directory . --format json --output results.json

# Security analysis
codacy-analysis-cli analyze --tool trivy --directory .
```

### Coverage Analysis
```bash
# Generate coverage with filtered output
flutter test --coverage
lcov --remove coverage/lcov.info \
  '**/*.g.dart' \
  '**/*.freezed.dart' \
  --output-file coverage/lcov_business_logic.info

# View coverage summary
lcov --summary coverage/lcov_business_logic.info
```

## Coverage Reporting

### Dual Coverage Strategy
BudApp maintains **parallel coverage reporting**:

1. **Codecov** (Raw coverage including generated files)
2. **Codacy** (Filtered coverage focusing on business logic)

### Coverage Filtering Process
```bash
# Step 1: Generate comprehensive coverage
flutter test --coverage

# Step 2: Filter generated files
lcov --remove coverage/lcov.info \
  '**/*.g.dart' \
  '**/*.freezed.dart' \
  '**/*.config.dart' \
  '**/*.gr.dart' \
  '**/generated_plugin_registrant.dart' \
  --output-file coverage/lcov_filtered.info

# Step 3: Upload filtered coverage
bash <(curl -Ls https://coverage.codacy.com/get.sh) report \
  --coverage-reports coverage/lcov_filtered.info
```

### Coverage Targets
- **Overall Project**: 50.2% → 90% (current → target)
- **Business Logic**: 70%+ (filtered coverage focusing on maintainable code)
- **Repository Layer**: 95%+ (critical data operations)
- **Service Layer**: 85%+ (business logic)

## Quality Standards

### Dart-Specific Rules
Aligned with `very_good_analysis` package:

```yaml
# Key Rules Disabled for Framework Compatibility
linter:
  rules:
    always_put_required_named_parameters_first: false  # Freezed compatibility
    avoid_redundant_argument_values: false            # Necessary defaults
    prefer_relative_imports: false                     # Package imports
    lines_longer_than_80_chars: false                 # Productivity focus
    cascade_invocations: false                         # Test readability
    public_member_api_docs: false                      # Gradual adoption
```

### Quality Gates
- **Static Analysis**: Max 100 issues per analysis run
- **Security**: Zero high/critical vulnerabilities
- **Performance**: Analysis completion within 30 minutes
- **Coverage**: Business logic coverage ≥ 70%

### Repository Pattern Enforcement
Custom rules focus on:
- ✅ Repository interface implementations
- ✅ Service layer dependency injection
- ✅ Firebase abstraction compliance
- ✅ Currency system consistency

## Troubleshooting

### Common Issues

#### 1. Generated Files in Analysis
**Problem**: `.g.dart` files causing noise in results
**Solution**: Verify `.codacy.yml` exclusion patterns
```bash
# Check excluded files are working
codacy-analysis-cli analyze --tool dartanalyzer --directory . --format json | jq '.results[].filename' | grep -E '\.(g|freezed)\.dart$'
# Should return empty if exclusions work
```

#### 2. Coverage Upload Failures
**Problem**: Codacy coverage upload timeouts or failures
**Solution**: Use filtered coverage file
```bash
# Verify filtered file size is reasonable
du -h coverage/lcov_filtered.info
# Should be significantly smaller than lcov.info

# Test upload with verbose output
bash <(curl -Ls https://coverage.codacy.com/get.sh) report \
  --coverage-reports coverage/lcov_filtered.info \
  --project-token $CODACY_PROJECT_TOKEN \
  --verbose
```

#### 3. Configuration Conflicts
**Problem**: Different results between local and CI analysis
**Solution**: Ensure configuration synchronization
```bash
# Compare configurations
diff analysis_options.yaml .codacy/tools-configs/analysis_options.yaml

# Validate Codacy config loading
codacy-analysis-cli validate-configuration
```

#### 4. Memory Issues in CI
**Problem**: Analysis CLI running out of memory
**Solution**: Adjust memory limits in `.codacy/cli-config.yaml`
```yaml
analysis:
  memory_limit: "4096M"  # Increase from 2048M
  timeout: "45m"         # Increase from 30m
```

### Debug Commands
```bash
# Validate configuration
codacy-analysis-cli validate-configuration

# Test tool availability
codacy-analysis-cli list-tools

# Check excluded paths
codacy-analysis-cli analyze --tool dartanalyzer --directory . --dry-run

# Coverage file inspection
head -20 coverage/lcov.info
grep -c "*.g.dart" coverage/lcov.info  # Should be 0 after filtering
```

## Best Practices

### Development Workflow
1. **Before Commit**: Run local analysis on changed files
2. **Before PR**: Verify CI analysis passes
3. **After Merge**: Monitor coverage trends in Codacy dashboard

### Configuration Management
1. **Sync Rules**: Keep analysis_options.yaml aligned with Codacy configuration
2. **Version Control**: All Codacy configuration files in Git
3. **Regular Updates**: Review exclusion patterns as codebase evolves

### Performance Optimization
1. **Parallel Analysis**: Use `--parallel 4` for faster local analysis
2. **Selective Analysis**: Analyze specific directories during development
3. **Incremental CI**: Consider delta analysis for large repositories

### Security Considerations
1. **Token Management**: Store `CODACY_PROJECT_TOKEN` in GitHub secrets
2. **Branch Protection**: Require Codacy analysis for PR merges
3. **Access Control**: Limit Codacy dashboard access to team members

## Integration with Existing Tools

### very_good_analysis Compatibility
Codacy configuration inherits from `very_good_analysis`:
- Same rule set baseline
- Strategic rule disables for framework compatibility
- Consistent formatting standards

### Firebase Integration
- Security rules analysis with Checkov
- Infrastructure scanning with Trivy
- Configuration file exclusions

### Repository Pattern Support
- Interface implementation validation
- Dependency injection compliance
- Service layer architecture enforcement

## Monitoring and Metrics

### Key Metrics to Track
1. **Business Logic Coverage**: Focus on non-generated code
2. **Issue Velocity**: Rate of issue introduction vs resolution
3. **Technical Debt**: Complexity and maintainability trends
4. **Security Posture**: Vulnerability detection and resolution time

### Dashboard Configuration
- **Quality Overview**: Business logic focus metrics
- **Coverage Trends**: Filtered coverage over time
- **Issue Categories**: Categorized by severity and type
- **Team Performance**: Individual and team quality metrics

---

## Summary

The BudApp Codacy integration provides comprehensive code quality analysis focused on business logic while maintaining development productivity. The configuration excludes 38+ generated files, ensuring quality metrics reflect maintainable code quality.

### Key Benefits
- ✅ **Accurate Metrics**: Business logic focused analysis
- ✅ **Automated CI/CD**: Seamless GitHub Actions integration
- ✅ **Parallel Reporting**: Codecov + Codacy dual coverage
- ✅ **Security Integration**: Vulnerability and secret scanning
- ✅ **Framework Aligned**: Compatible with very_good_analysis

### Next Steps
1. Configure `CODACY_PROJECT_TOKEN` in GitHub repository secrets
2. Monitor first CI/CD run with Codacy integration
3. Review quality dashboard and adjust thresholds as needed
4. Train team on local Codacy CLI usage for development workflow

For additional support, refer to the official [Codacy documentation](https://docs.codacy.com/) or consult the project's technical lead.