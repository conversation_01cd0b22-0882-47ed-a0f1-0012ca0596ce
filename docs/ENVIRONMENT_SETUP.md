# Environment Setup Guide

This document provides detailed instructions for setting up the BudApp development environment with multiple Firebase projects.

## Overview

BudApp uses a multi-environment setup with three distinct Firebase projects:
- **Development** (`budapp-dev`): For active development and testing
- **Staging** (`budapp-staging-1`): For pre-production testing and QA
- **Production** (`budapp-prod`): For the live application

Each environment includes full Firebase services integration:
- **Firebase Core SDK**: Project initialization and configuration
- **Firebase Auth**: User authentication and session management
- **Cloud Firestore**: Database services with offline support
- **Firebase Service Testing**: Built-in connectivity verification

## Firebase Configuration Files

### Android Configuration
The Android app uses different `google-services.json` files for each environment:

```
android/app/
├── google-services.json           # Active configuration (gets overwritten by scripts)
├── dev.google-services.json       # Development (budapp-dev)
├── staging.google-services.json   # Staging (budapp-staging-1)
└── prod.google-services.json      # Production (budapp-prod)
```

### iOS Configuration
The iOS app uses different `GoogleService-Info.plist` files for each environment:

```
ios/Runner/
├── dev.GoogleService-Info.plist     # Development (budapp-dev)
├── staging.GoogleService-Info.plist # Staging (budapp-staging-1)
└── prod.GoogleService-Info.plist    # Production (budapp-prod)
```

## Flutter Flavors

### Android Flavors
Configured in `android/app/build.gradle.kts`:

```kotlin
flavorDimensions += "default"
productFlavors {
    create("dev") {
        dimension = "default"
        applicationIdSuffix = ".dev"
        resValue("string", "app_name", "BudApp Dev")
    }
    create("staging") {
        dimension = "default"
        applicationIdSuffix = ".staging"
        resValue("string", "app_name", "BudApp Staging")
    }
    create("prod") {
        dimension = "default"
        resValue("string", "app_name", "BudApp")
    }
}
```

### Unified Architecture
The app uses a single main entry point with automatic environment detection:

- `lib/main.dart` - Unified entry point that auto-detects flavor and configures environment
- `lib/config/environment_config.dart` - Centralized environment configuration management

### Firebase Options Files
Environment-specific Firebase configuration:

- `lib/firebase_options_dev.dart` - Development Firebase configuration
- `lib/firebase_options_staging.dart` - Staging Firebase configuration
- `lib/firebase_options_prod.dart` - Production Firebase configuration

### Firebase Service
Centralized Firebase service management:

- `lib/services/firebase_service.dart` - Firebase Auth & Firestore initialization and testing

### Environment Detection
The app automatically detects the environment using:
- `--dart-define=FLAVOR=<environment>` parameter
- Defaults to `dev` environment when no flavor is specified
- Configures Firebase, theming, and app metadata based on detected environment

## Running Different Environments

### Quick Development (Default)
```bash
flutter run
```
- **Automatically uses development environment**
- Package: `com.digitau.budapp.dev`
- Firebase: `budapp-dev`
- Theme: Orange
- App Name: "BudApp Dev"

### Development (Explicit)
```bash
flutter run --flavor dev --dart-define=FLAVOR=dev
```
- Package: `com.digitau.budapp.dev`
- Firebase: `budapp-dev`
- Theme: Orange
- App Name: "BudApp Dev"

### Staging
```bash
flutter run --flavor staging --dart-define=FLAVOR=staging
```
- Package: `com.digitau.budapp.staging`
- Firebase: `budapp-staging-1`
- Theme: Blue
- App Name: "BudApp Staging"

### Production
```bash
flutter run --flavor prod --dart-define=FLAVOR=prod
```
- Package: `com.digitau.budapp`
- Firebase: `budapp-prod`
- Theme: Green
- App Name: "BudApp"

> **Note**: The unified `main.dart` automatically detects the environment via `--dart-define=FLAVOR` and configures all settings accordingly.

## Building for Release

### Android Builds (Automated - Recommended)

Use the provided build script for seamless Android builds with automatic Firebase configuration:

```bash
# Development APK
./scripts/build_android.sh dev

# Staging APK
./scripts/build_android.sh staging

# Production APK
./scripts/build_android.sh prod

# App Bundle for Play Store
./scripts/build_android.sh prod appbundle
```

### Android Builds (Manual)

For manual builds, select Firebase configuration first:

```bash
# Development
./scripts/select_firebase_config.sh dev
flutter build apk --flavor dev -t lib/main_dev.dart

# Staging
./scripts/select_firebase_config.sh staging
flutter build apk --flavor staging -t lib/main_staging.dart

# Production
./scripts/select_firebase_config.sh prod
flutter build apk --flavor prod -t lib/main_prod.dart
```

### iOS Builds

iOS builds require manual Xcode setup first (see `docs/IOS_SETUP.md`):

```bash
# Development
flutter build ios --flavor dev -t lib/main_dev.dart

# Staging
flutter build ios --flavor staging -t lib/main_staging.dart

# Production
flutter build ios --flavor prod -t lib/main_prod.dart
```

## Build Scripts

### Available Scripts

- `scripts/build_android.sh` - Automated Android build with Firebase config selection
- `scripts/select_firebase_config.sh` - Manual Firebase configuration selection
- `scripts/setup_ios_schemes.sh` - iOS setup helper (provides instructions)

### Script Usage

```bash
# Android builds (recommended)
./scripts/build_android.sh <flavor> [build_type]

# Manual Firebase config selection
./scripts/select_firebase_config.sh <flavor>

# iOS setup guidance
./scripts/setup_ios_schemes.sh
```

## Firebase Configuration Management

### Android Configuration Files

The Android app requires the correct `google-services.json` file for each environment:

- `android/app/google-services.json` - Active configuration (gets overwritten by scripts)
- `android/app/dev.google-services.json` - Development configuration
- `android/app/staging.google-services.json` - Staging configuration
- `android/app/prod.google-services.json` - Production configuration

**Important**: The build scripts automatically copy the appropriate configuration file to `google-services.json` before building.

## Troubleshooting

### Common Issues

1. **Firebase connection errors**:
   - For Android: Use build scripts or manually select Firebase config
   - For iOS: Ensure correct scheme is selected in Xcode
   - Check that Firebase configuration files are in place

2. **Build failures**:
   ```bash
   flutter clean
   flutter pub get
   ```

3. **iOS build issues**:
   - iOS schemes need manual setup in Xcode
   - See `docs/IOS_SETUP.md` for detailed instructions

4. **Android Firebase config issues**:
   ```bash
   # Select specific config manually
   ./scripts/select_firebase_config.sh prod
   ./scripts/select_firebase_config.sh dev
   ./scripts/select_firebase_config.sh staging

   # Or copy manually if needed
   cp android/app/prod.google-services.json android/app/google-services.json
   ```

### Verification

To verify your environment setup:

1. Build and run each flavor using the build scripts
2. Check that the app name and theme color match the expected environment
3. Verify Firebase connectivity by checking the Firebase console
4. **Test Firebase Services**: Use the "Test Firebase Services" button in the dev environment to verify:
   - Firebase Auth connectivity
   - Firestore read/write operations
   - Service initialization status

## Next Steps

- Complete iOS setup in Xcode (see `docs/IOS_SETUP.md`)
- Configure CI/CD pipelines for automated builds
- Test all environments on physical devices

## Environment Indicators

Each environment has visual indicators to help developers identify which environment they're using:

| Environment | Color Theme | App Icon Badge | Firebase Project |
|-------------|-------------|----------------|------------------|
| Development | Orange | Dev | budapp-dev |
| Staging | Blue | Staging | budapp-staging-1 |
| Production | Green | None | budapp-prod |

## Troubleshooting

### Common Issues

1. **Wrong Firebase Project**: Check that the correct configuration files are in place
2. **Package Name Conflicts**: Ensure different package names for each environment
3. **Build Cache Issues**: Run `flutter clean` if switching between environments
4. **Firebase Initialization Errors**: Verify Firebase configuration matches the project

### Verification Commands

```bash
# Check current Flutter configuration
flutter doctor

# Verify dependencies
flutter pub get

# Analyze code for issues
flutter analyze

# Run tests
flutter test
```

## Default Flavor Configuration

The project is configured with `default-flavor: dev` in `pubspec.yaml`, which means:
- `flutter run` automatically uses the development environment
- No need to specify `--flavor dev` for daily development
- All Firebase services connect to the development project by default
- Visual indicators clearly show you're in the development environment

## Current Implementation Status

✅ **Completed Features:**
- Multi-environment Firebase configuration (dev, staging, prod)
- Unified main.dart with automatic environment detection
- Firebase Core SDK integration with proper initialization
- Firebase Auth and Firestore services integration
- Environment-specific theming and visual indicators
- Comprehensive build scripts and documentation
- Firebase connectivity testing across all environments
- Complete authentication UI with Material 3 design
- Design system with comprehensive design tokens
- Automatic dark/light theme support
- Form validation and user experience components

🔄 **In Progress:**
- Firebase authentication logic implementation
- Email verification and password reset flows
- OAuth integration (Google, Apple Sign-In)

📋 **Planned:**
- Firestore data models and Security Rules
- State management with Riverpod
- Core financial features (accounts, transactions, budgets)
- Premium subscription integration
- Advanced reporting and analytics

## Next Steps

After setting up the environment, you can proceed with:
1. Implementing authentication flows (Firebase Auth is ready, UI complete)
2. Setting up Firestore data models (Firestore is ready)
3. Configuring Remote Config
4. Setting up push notifications
5. Testing Firebase services using the built-in connectivity testing
