# DeepSource Integration for Dart/Flutter

This document explains the DeepSource integration setup for Dart/Flutter projects.

## Current Status: Analysis Only

**Important**: DeepSource currently does **not support Dart/Flutter for test coverage**. The integration only includes static code analysis via `dart-analyze`.

### Supported Languages for Coverage
According to [DeepSource documentation](https://docs.deepsource.com/docs/languages), test coverage is supported for:
- Go, Rust, Java, Scala, C#, JavaScript, PHP, Python, Ruby, C & C++, Swift, Kotlin

**Dart/Flutter is not in this list**, so coverage reporting is not available.

## Current Implementation

### 1. `.deepsource.toml` Configuration

Only includes the dart-analyze analyzer:

```toml
[[analyzers]]
name = "dart-analyze"
type = "community"
```

### 2. GitHub Workflow

Modified `.github/workflows/deepsource.yml` to:

- Run `dart analyze` and generate SARIF reports
- Upload SARIF reports to DeepSource for static analysis
- **Note**: Coverage steps have been removed due to lack of Dart support

### 3. Key Features

- **Automated Analysis**: Static analysis reports are sent to DeepSource on every push to `main`, `development`, and `production` branches, as well as on pull requests
- **SARIF Format**: Uses industry-standard SARIF format for analysis results
- **Community Analyzer**: Uses the community dart-analyze analyzer

## Setup Requirements

### DeepSource DSN Secret

You need to configure the `DEEPSOURCE_DSN` environment variable in your GitHub repository:

1. **Get your DSN from DeepSource**:
   - Go to your repository's **Settings** page in DeepSource
   - Navigate to the **Reporting** section (not Code Coverage, since that's not supported)
   - Click **Copy** to copy your DSN

2. **Add GitHub Secret**:
   - Go to your GitHub repository
   - Navigate to **Settings** > **Secrets and variables** > **Actions**
   - Click **New repository secret**
   - Name: `DEEPSOURCE_DSN`
   - Value: Paste the DSN from DeepSource
   - Click **Add secret**

### Workflow Execution

The workflow will:

1. Checkout code (using PR head commit to avoid merge commits)
2. Setup Dart/Flutter environment
3. Install dependencies with `flutter pub get`
4. Run `dart analyze` and generate SARIF report
5. Install DeepSource CLI
6. Upload analysis SARIF report to DeepSource

## Analysis Report Details

- **Format**: SARIF (Static Analysis Results Interchange Format)
- **Analyzer**: Community dart-analyze analyzer
- **Excluded Files**: Generated files are excluded via `.deepsource.toml` patterns

## Troubleshooting

### Common Issues

1. **Missing DSN**: Ensure `DEEPSOURCE_DSN` secret is properly configured
2. **SARIF Generation Failed**: Verify `dart analyze` runs successfully
3. **Merge Commits**: The workflow uses `ref: ${{ github.event.pull_request.head.sha }}` to avoid merge commit issues

### Verification

After setup, you should see:
- Static analysis results in your DeepSource dashboard
- Code quality metrics and trends
- Analysis reports for each commit and PR

## Coverage Alternative

Since DeepSource doesn't support Dart coverage, your existing coverage integrations remain the primary solution:
- **Codecov**: Continues to provide coverage metrics and reports
- **Codacy**: Continues to provide coverage analysis
- **Local Coverage**: Use `flutter test --coverage` and `genhtml` for local HTML reports

## Future Support

When DeepSource adds Dart/Flutter coverage support, you can:
1. Add the `test-coverage` analyzer back to `.deepsource.toml`
2. Add coverage generation and upload steps to the workflow
3. Use the `--key dart` parameter (when supported)
