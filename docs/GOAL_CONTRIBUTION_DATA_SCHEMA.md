# Goal Contribution Data Schema

## Overview

The GoalContribution data model represents individual contributions made towards specific financial goals in BudApp. This model tracks the amount, date, and optional description for each contribution, enabling detailed progress tracking and contribution history.

## Collection Path

```
users/{userId}/goals/{goalId}/contributions/{contributionId}
```

This subcollection structure ensures:
- **Security**: User data isolation through Firestore Security Rules
- **Performance**: Efficient querying within goal context
- **Organization**: Clear hierarchical relationship between goals and contributions
- **Scalability**: Distributed data structure for large contribution histories

## Field Specifications

### Required Fields

| Field | Type | Description | Validation |
|-------|------|-------------|------------|
| `id` | `String` | Unique contribution identifier | Auto-generated by Firestore |
| `userId` | `String` | Owner user ID | Must match authenticated user |
| `goalId` | `String` | Parent goal ID | Must reference existing goal |
| `amountCents` | `int` | Contribution amount in cents | > 0, ≤ 9,999,999,999 (~$100M) |
| `contributionDate` | `DateTime` | When contribution was made | Not future, ≤ 10 years ago |
| `createdAt` | `DateTime` | Record creation timestamp | Auto-set on creation |
| `updatedAt` | `DateTime` | Last update timestamp | Auto-updated on changes |

### Optional Fields

| Field | Type | Description | Validation |
|-------|------|-------------|------------|
| `description` | `String?` | Optional contribution description | ≤ 500 characters |

### System Fields

| Field | Type | Default | Description |
|-------|------|---------|-------------|
| `isActive` | `bool` | `true` | Soft delete flag |
| `schemaVersion` | `int` | `1` | Schema version for migrations |
| `metadata` | `Map<String, dynamic>` | `{}` | Additional metadata |

## Data Model Implementation

### Freezed Model
```dart
@freezed
@JsonSerializable()
class GoalContribution with _$GoalContribution {
  const factory GoalContribution({
    required String id,
    required String userId,
    required String goalId,
    required int amountCents,
    required DateTime contributionDate,
    String? description,
    @Default(true) bool isActive,
    @Default(1) int schemaVersion,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default({}) Map<String, dynamic> metadata,
  }) = _GoalContribution;
}
```

### Factory Methods

#### Create New Contribution
```dart
GoalContribution.create({
  required String userId,
  required String goalId,
  required int amountCents,
  required DateTime contributionDate,
  String? description,
})
```

#### From JSON/Firestore
```dart
GoalContribution.fromJson(Map<String, dynamic> json)
GoalContribution.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc)
```

### Utility Methods

#### Update Timestamp
```dart
GoalContribution updated() // Returns copy with updated timestamp
```

#### Date Helpers
```dart
bool get isMadeToday        // Contribution made today
bool get isMadeThisWeek     // Contribution made this week
bool get isMadeThisMonth    // Contribution made this month
String get dateDescription  // User-friendly date description
```

#### Summary
```dart
String get summaryMessage   // "Contributed $50.00 on Today"
bool get hasDescription     // Has non-empty description
```

## Validation Rules

### Business Rules
1. **Amount Validation**: Must be positive (> 0) and reasonable (≤ $100M)
2. **Date Validation**: Cannot be in future, not older than 10 years
3. **Description Validation**: Optional, maximum 500 characters
4. **User/Goal Validation**: Must reference valid user and goal IDs

### Implementation
```dart
extension GoalContributionValidation on GoalContribution {
  List<String> validate()  // Returns list of validation errors
  bool get isValid         // True if no validation errors
}
```

## Security Considerations

### Firestore Security Rules
```javascript
// Goal contributions subcollection
match /users/{userId}/goals/{goalId}/contributions/{contributionId} {
  allow read, write: if request.auth != null 
    && request.auth.uid == userId
    && validateGoalContribution(resource.data);
}

function validateGoalContribution(data) {
  return data.keys().hasAll(['id', 'userId', 'goalId', 'amountCents', 'contributionDate']) &&
         data.userId == request.auth.uid &&
         data.amountCents is int && data.amountCents > 0 &&
         data.contributionDate is timestamp &&
         (!('description' in data) || data.description is string);
}
```

### Data Isolation
- User data completely isolated by `userId` field
- Contributions linked to specific goals via `goalId`
- Firestore Security Rules enforce user ownership

## Indexing Strategy

### Recommended Indexes
```javascript
// For contribution history queries
users/{userId}/goals/{goalId}/contributions
- contributionDate (descending)
- isActive + contributionDate (descending)

// For contribution analytics
users/{userId}/goals/{goalId}/contributions  
- isActive + amountCents (descending)
- contributionDate + amountCents (descending)
```

### Query Patterns
```dart
// Recent contributions for a goal
contributions
  .where('isActive', isEqualTo: true)
  .orderBy('contributionDate', descending: true)
  .limit(10)

// Contributions in date range
contributions
  .where('isActive', isEqualTo: true)
  .where('contributionDate', isGreaterThanOrEqualTo: startDate)
  .where('contributionDate', isLessThanOrEqualTo: endDate)
  .orderBy('contributionDate', descending: true)
```

## Usage Examples

### Creating a Contribution
```dart
final contribution = GoalContribution.create(
  userId: 'user123',
  goalId: 'goal456',
  amountCents: 5000, // $50.00
  contributionDate: DateTime.now(),
  description: 'Monthly savings contribution',
);
```

### Validation
```dart
final errors = contribution.validate();
if (errors.isNotEmpty) {
  print('Validation errors: ${errors.join(', ')}');
}
```

### Date Helpers
```dart
print(contribution.dateDescription); // "Today", "This week", "Jan 15, 2025"
print(contribution.summaryMessage);   // "Contributed $50.00 on Today"
```

## Integration with Goal Model

### Relationship
- GoalContribution belongs to Goal (parent-child relationship)
- Goal.currentAmountCents should be updated when contributions change
- Contributions enable detailed progress tracking beyond simple amount

### Consistency
- Goal progress calculations should consider active contributions
- Soft deletion maintains referential integrity
- Schema versioning enables future enhancements

## Migration Considerations

### Schema Version 1
- Initial implementation with core fields
- Supports basic contribution tracking
- Foundation for future enhancements

### Future Enhancements
- Contribution categories/types
- Recurring contribution patterns
- Contribution source tracking
- Enhanced metadata fields

## Testing Strategy

### Unit Tests
- Model creation and validation
- JSON serialization/deserialization
- Date helper methods
- Validation rules

### Integration Tests
- Firestore CRUD operations
- Security rules validation
- Goal-contribution relationship
- Query performance

This schema provides a robust foundation for goal contribution tracking while maintaining consistency with BudApp's architectural patterns and security requirements.
