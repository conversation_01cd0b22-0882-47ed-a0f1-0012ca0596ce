# Codacy CLI Configuration Guide

This document explains how to configure Codacy CLI analysis for the BudApp project, including what is checked and how to customize the analysis.

## Configuration Files Overview

### 1. Primary Configuration: `.codacy.yml`

The main configuration file located at the project root controls:

```yaml
# Engine Selection - Which analysis tools to run
engines:
  dartanalyzer:
    enabled: true
    analysis_options: analysis_options.yaml  # Use project's Dart config
  checkov:
    enabled: true  # Security/infrastructure analysis
  metrics:
    enabled: true  # Code complexity metrics

# File Exclusions - What files/paths to skip
exclude_paths:
  - "**/*.g.dart"        # Generated files
  - "**/*.freezed.dart"  # Freezed data classes
  - "build/**"           # Build artifacts
  # ... (see full list in .codacy.yml)

# Quality Rules
rules:
  dartanalyzer:
    fail_on_warnings: true
    analysis_options_file: analysis_options.yaml

# Coverage Configuration
coverage:
  include_paths:
    - "lib/features/**"      # Business logic
    - "lib/data/repositories/**"
    - "lib/services/**"
  exclude_paths:
    - "**/*.g.dart"          # Exclude generated files
```

### 2. Tool Versions: `.codacy/codacy.yaml`

Specifies which versions of analysis tools to use:

```yaml
runtimes:
  - dart@3.7.2
  - node@22.2.0
  - python@3.11.11

tools:
  - dartanalyzer@3.7.2
  - semgrep@1.78.0      # Security analysis
  - trivy@0.59.1        # Vulnerability scanning
  - eslint@8.57.0       # JavaScript linting
```

### 3. Dart Analysis: `analysis_options.yaml`

Flutter/Dart specific rules (used by dartanalyzer engine):

```yaml
include: package:very_good_analysis/analysis_options.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
  strong-mode:
    implicit-casts: false
  errors:
    invalid_assignment: error
    missing_return: error
```

## What Codacy CLI Checks

### 1. **Code Quality Issues**
- **Dart Analyzer**: 200+ lint rules from `very_good_analysis`
- **Code complexity**: Cyclomatic complexity, file length
- **Maintainability**: Code duplication, cognitive complexity

### 2. **Security Vulnerabilities**
- **Semgrep**: Pattern-based security analysis
- **Trivy**: Known vulnerability database scanning
- **Hardcoded secrets**: API keys, passwords detection

### 3. **Performance Issues**
- **Missing const constructors**
- **Unnecessary widget usage**
- **Memory leak patterns**

### 4. **Documentation**
- **Public API documentation**: Missing dartdoc comments
- **Code comments**: Quality and coverage

## Customizing Analysis

### Adding/Removing Analysis Tools

Edit `.codacy.yml`:

```yaml
engines:
  # Disable an engine
  semgrep:
    enabled: false
  
  # Add new engine
  sonarjs:
    enabled: true
```

### Excluding Files/Directories

Add patterns to `.codacy.yml`:

```yaml
exclude_paths:
  - "lib/legacy/**"        # Exclude legacy code
  - "**/test_data/**"      # Exclude test fixtures
  - "lib/generated/**"     # Exclude any generated code
```

### Customizing Dart Rules

Modify `analysis_options.yaml`:

```yaml
linter:
  rules:
    # Disable specific rule
    avoid_print: false
    
    # Enable stricter rules
    prefer_single_quotes: true
    require_trailing_commas: true
```

### Setting Coverage Thresholds

In `.codacy.yml`:

```yaml
coverage:
  minimum_coverage: 80  # Require 80% coverage
  
  # Focus coverage on business logic
  include_paths:
    - "lib/features/**"
    - "lib/services/**"
  
  exclude_paths:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
```

### Configuring Duplication Detection

```yaml
duplication:
  config:
    languages:
      dart:
        minimum_tokens: 50  # Smaller threshold = more sensitive
        
  exclude_paths:
    - "**/*.g.dart"  # Don't check generated files
```

## CLI Usage Examples

### Basic Analysis
```bash
# Run analysis on all files
./codacy-analysis-cli analyze --directory . --format json

# Run specific tool only
./codacy-analysis-cli analyze --tool dartanalyzer --directory .
```

### Advanced Options
```bash
# Set maximum allowed issues
./codacy-analysis-cli analyze \
  --max-allowed-issues 50 \
  --directory . \
  --output results.json

# Parallel execution for faster analysis
./codacy-analysis-cli analyze \
  --parallel 4 \
  --directory .

# Upload results to Codacy platform
./codacy-analysis-cli analyze \
  --directory . \
  --upload \
  --project-token $CODACY_PROJECT_TOKEN
```

### Filtering Results
```bash
# Filter by severity
./codacy-analysis-cli analyze \
  --directory . \
  --severity Error,Warning

# Focus on specific directories
./codacy-analysis-cli analyze \
  --directory lib/features \
  --directory lib/services
```

## CI/CD Integration

### GitHub Actions (Current Setup)
```yaml
- name: Run Codacy Analysis
  run: |
    ./codacy-analysis-cli analyze \
      --tool dartanalyzer \
      --directory . \
      --format json \
      --output codacy-results.json \
      --max-allowed-issues 100 \
      --parallel 4
```

### Local Development
```bash
# Quick check before commit
make codacy-check

# Full analysis with coverage
make codacy-full
```

## Understanding Results

### Result Format
```json
{
  "results": [
    {
      "filename": "lib/features/budget/budget_service.dart",
      "line": 45,
      "patternId": "dart-prefer-const-constructors",
      "message": "Prefer const with constant constructors",
      "category": "CodeStyle",
      "level": "Info"
    }
  ]
}
```

### Issue Priorities
1. **Error**: Critical issues that must be fixed
2. **Warning**: Important issues that should be addressed
3. **Info**: Style/best practice suggestions

### Pattern Categories
- **Security**: Potential vulnerabilities
- **ErrorProne**: Logic errors, bugs
- **Performance**: Performance bottlenecks
- **CodeStyle**: Formatting, conventions
- **Compatibility**: Version compatibility issues

## Best Practices

### 1. **Focus on Business Logic**
- Exclude generated files (`.g.dart`, `.freezed.dart`)
- Prioritize `lib/features/` and `lib/services/`
- Skip platform-specific code

### 2. **Gradual Improvement**
- Start with error-level issues
- Set realistic issue thresholds
- Gradually tighten rules

### 3. **Team Consistency**
- Use shared `analysis_options.yaml`
- Document custom rules
- Regular team reviews of configuration

### 4. **Performance Optimization**
- Use parallel analysis (`--parallel 4`)
- Exclude large asset directories
- Cache analysis results in CI

## Troubleshooting

### Common Issues

1. **Analysis Too Slow**
   ```yaml
   # Add more exclusions to .codacy.yml
   exclude_paths:
     - "build/**"
     - "**/*.g.dart"
   ```

2. **Too Many Issues**
   ```bash
   # Increase threshold temporarily
   --max-allowed-issues 200
   ```

3. **False Positives**
   ```yaml
   # Disable specific patterns
   rules:
     dartanalyzer:
       patterns:
         - pattern: some-pattern-id
           enabled: false
   ```

### Getting Help

- **Codacy Documentation**: https://docs.codacy.com/
- **CLI Help**: `./codacy-analysis-cli --help`
- **Pattern Reference**: Check tool-specific documentation

## Project-Specific Configuration

For BudApp, the configuration focuses on:

1. **Business Logic Quality**: Features, services, repositories
2. **Generated File Exclusion**: 38+ types of generated files excluded
3. **Security**: Hardcoded secrets detection, vulnerability scanning
4. **Flutter Best Practices**: Very Good Analysis rules
5. **Coverage**: 80% minimum on business logic code

This setup ensures high code quality while avoiding noise from generated and platform-specific code.
