# Global Text Overflow System

The BudApp implements a comprehensive global text overflow handling system that automatically manages text truncation and display across the entire application. This system provides consistent behavior while allowing for customization when needed.

## Overview

The text overflow system consists of several components:

1. **TextOverflowConfig** - Configuration classes for different overflow behaviors
2. **AppText** - Smart text widget that automatically applies appropriate overflow handling
3. **Text Extensions** - Extension methods for easy migration and utility functions
4. **Design Token Integration** - Consistent overflow constants integrated with the design system

## Core Components

### TextContext Enum

Defines different text contexts for automatic overflow handling:

- `title` - Large headings and titles (single line with ellipsis)
- `body` - Body text and descriptions (multi-line with ellipsis)
- `label` - Form labels and small text (single line with ellipsis)
- `critical` - Critical information like amounts (adaptive scaling or wrap)
- `note` - Secondary information and notes (multi-line with fade)
- `button` - Button text (single line with ellipsis or scaling)
- `listItem` - List item text (context-dependent)

### TextOverflowConfig Class

Configuration class that defines overflow behavior:

```dart
const config = TextOverflowConfig(
  maxLines: 2,
  overflow: TextOverflow.ellipsis,
  softWrap: true,
  adaptive: false,
  minScaleFactor: 0.8,
  maxScaleFactor: 1.2,
);
```

### AppText Widget

Smart text widget that automatically handles overflow based on context:

```dart
// Automatic context detection
AppText('Hello World')

// Explicit context
AppText.title('Page Title')
AppText.body('Description text')
AppText.critical('\$1,234.56')

// Manual configuration
AppText('Custom', 
  overflowConfig: TextOverflowConfig(maxLines: 3),
)
```

## Usage Examples

### Basic Usage

```dart
// Replace standard Text widgets
Text('Title') → AppText.title('Title')
Text('Description') → AppText.body('Description')
Text('Label') → AppText.label('Label')
```

### Context-Specific Usage

```dart
// Transaction titles
AppText.title(transaction.description)

// Account descriptions
AppText.body(account.description)

// Amount displays (critical information)
AppText.critical('\$${amount.toStringAsFixed(2)}')

// Notes and secondary info
AppText.note(transaction.notes)

// Button text
AppText.button('Save Transaction')
```

### Custom Configuration

```dart
// Custom overflow behavior
AppText(
  'Long text content',
  overflowConfig: TextOverflowConfig(
    maxLines: 5,
    overflow: TextOverflow.fade,
    softWrap: true,
  ),
)

// Override specific properties
AppText.body(
  'Body text',
  maxLines: 2, // Override default body maxLines
)
```

## Predefined Configurations

### AppTextOverflow Constants

- `title` - Single line with ellipsis, no soft wrap
- `body` - 3 lines max with ellipsis, soft wrap enabled
- `label` - Single line with ellipsis, no soft wrap
- `critical` - Adaptive scaling, visible overflow, soft wrap
- `note` - 2 lines max with ellipsis, soft wrap enabled
- `button` - Single line with adaptive scaling
- `listItem` - Single line with ellipsis
- `compact` - Single line with ellipsis
- `expanded` - 5 lines max with ellipsis
- `unlimited` - No overflow restrictions

## Automatic Context Detection

The system can automatically detect appropriate overflow behavior based on TextStyle:

```dart
// Large text → title context
AppText('Title', style: TextStyle(fontSize: 24.0))

// Bold text → title or label context
AppText('Bold', style: TextStyle(fontWeight: FontWeight.w600))

// Small text → note context
AppText('Small', style: TextStyle(fontSize: 12.0))

// Normal text → body context (default)
AppText('Normal text')
```

## Migration Guide

### From Text to AppText

1. **Simple replacement:**
   ```dart
   Text('Hello') → AppText('Hello')
   ```

2. **With context:**
   ```dart
   Text('Title', style: titleStyle) → AppText.title('Title', style: titleStyle)
   ```

3. **Remove manual overflow handling:**
   ```dart
   Text(
     'Long text',
     maxLines: 1,
     overflow: TextOverflow.ellipsis,
   )
   ↓
   AppText.title('Long text')
   ```

### Extension Methods

For gradual migration, use extension methods:

```dart
Text('Hello').withSmartOverflow
Text('Title').withTitleOverflow
Text('Body').withBodyOverflow
```

## Design Token Integration

The system integrates with existing design tokens:

```dart
class AppTextOverflowTokens {
  static const maxLinesSingle = 1;
  static const maxLinesCompact = 2;
  static const maxLinesBody = 3;
  static const maxLinesExpanded = 5;
  
  static const minScaleFactor = 0.8;
  static const maxScaleFactor = 1.2;
  static const buttonMinScaleFactor = 0.85;
}
```

## Accessibility

The system maintains full accessibility compliance:

- Screen reader compatibility
- Semantic labels preserved
- Text scaling support
- High contrast mode support

## Performance Considerations

- Automatic context detection is lightweight
- Adaptive text uses LayoutBuilder only when needed
- No unnecessary rebuilds
- Efficient text measurement

## Testing

Comprehensive test coverage includes:

- Unit tests for configuration classes
- Widget tests for AppText behavior
- Integration tests for real-world scenarios
- Accessibility tests

## Best Practices

1. **Use context-specific constructors** when the text purpose is clear
2. **Let automatic detection work** for general text
3. **Use manual configuration** only when needed
4. **Test with long text** to verify overflow behavior
5. **Consider accessibility** when customizing behavior

## Future Enhancements

Potential future improvements:

- Fade overflow effects
- Custom truncation indicators
- Dynamic line height adjustment
- Responsive text sizing
- Advanced typography features
