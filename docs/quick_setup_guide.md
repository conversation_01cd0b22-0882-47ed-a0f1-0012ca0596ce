# Quick Setup Guide - Code Quality Standards

## Overview

This guide helps developers quickly set up and use the BudApp code quality standards and tools.

## Prerequisites

- Flutter SDK 3.27.1 or later
- Git repository access
- IDE with Dart/Flutter support (VS Code, IntelliJ, etc.)

## Quick Setup (5 minutes)

### 1. Install Git Hooks

```bash
# Run from project root
./scripts/setup-git-hooks.sh
```

This installs:
- Pre-commit hook (code quality checks)
- Commit message validation (conventional commits)
- Pre-push hook (comprehensive checks for protected branches)

### 2. Configure IDE

#### VS Code
Add to `.vscode/settings.json`:
```json
{
  "dart.lineLength": 80,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": true,
    "source.organizeImports": true
  },
  "dart.showLintNames": true,
  "dart.analysisExcludedFolders": [
    "build",
    ".dart_tool"
  ]
}
```

#### IntelliJ/Android Studio
1. Go to Settings → Editor → Code Style → Dart
2. Set line length to 80
3. Enable "Format code on save"
4. Enable "Organize imports on save"

### 3. Verify Setup

```bash
# Check analysis
flutter analyze

# Check formatting
dart format --set-exit-if-changed .

# Run tests
flutter test
```

## Daily Workflow

### Before Committing

The pre-commit hook automatically runs:
1. Code formatting check
2. Static analysis
3. Import organization
4. Basic security checks
5. Test execution (optional)

### Manual Quality Checks

```bash
# Quick quality check
flutter analyze && dart format . && flutter test

# Comprehensive check (matches CI/CD)
./scripts/pre-commit-hook.sh
```

### Fixing Common Issues

#### Import Sorting
```bash
# Auto-fix import issues
dart fix --apply
```

#### Code Formatting
```bash
# Format all files
dart format .
```

#### Missing Const Constructors
```bash
# Auto-fix where possible
dart fix --apply
```

## CI/CD Integration

### GitHub Actions

The project includes two workflows:

1. **android-build.yml**: Enhanced with quality checks
2. **code-quality.yml**: Dedicated quality analysis

Both run automatically on:
- Push to main/development
- Pull requests
- Daily scheduled runs

### Quality Gates

All CI/CD pipelines enforce:
- Zero static analysis errors
- Consistent code formatting
- Passing test suite
- Security validation

## Troubleshooting

### Common Issues

#### "Analysis issues found"
```bash
# See detailed issues
flutter analyze --verbose

# Fix automatically where possible
dart fix --apply
```

#### "Code formatting issues"
```bash
# Fix formatting
dart format .
```

#### "Tests failing"
```bash
# Run specific test
flutter test test/path/to/test.dart

# Run with verbose output
flutter test --reporter=expanded
```

#### "Pre-commit hook failing"
```bash
# Skip hooks temporarily (not recommended)
git commit --no-verify

# Or fix issues and commit normally
flutter analyze && dart format . && git commit
```

### Getting Help

1. **Check documentation**: `docs/code_quality_standards.md`
2. **Run diagnostics**: `flutter doctor -v`
3. **Team discussion**: Discuss in team channels
4. **IDE issues**: Check IDE-specific documentation

## Configuration Files

### Key Files
- `analysis_options.yaml`: Linting configuration
- `.github/workflows/`: CI/CD workflows
- `scripts/pre-commit-hook.sh`: Local quality checks
- `scripts/setup-git-hooks.sh`: Hook installation

### Customization

To adjust quality standards:
1. Edit `analysis_options.yaml`
2. Update team documentation
3. Communicate changes to team
4. Re-run setup scripts

## Best Practices

### Development
- Run `flutter analyze` frequently during development
- Use IDE real-time linting feedback
- Fix issues as they appear (don't accumulate)
- Write tests for new features

### Code Reviews
- Ensure CI/CD passes before review
- Focus on logic and architecture in reviews
- Quality issues should be caught by automation
- Use conventional commit messages

### Team Collaboration
- Discuss quality standard changes as a team
- Share knowledge about new linting rules
- Help teammates with setup issues
- Continuously improve standards

## Advanced Usage

### Custom Rules

To add project-specific rules:
1. Edit `analysis_options.yaml`
2. Test with `flutter analyze`
3. Update documentation
4. Communicate to team

### Stricter Analysis

To enable very_good_analysis:
1. Change include in `analysis_options.yaml`
2. Address new issues systematically
3. Update CI/CD if needed

### Performance Monitoring

Track quality metrics:
- Linting issue count trends
- Test coverage percentages
- Build success rates
- Code review cycle times

## Next Steps

1. **Complete setup**: Follow this guide
2. **Practice workflow**: Make a test commit
3. **Read full documentation**: `docs/code_quality_standards.md`
4. **Join team discussions**: Participate in quality improvements

## Support

For issues with this setup:
- Check troubleshooting section above
- Review full documentation
- Ask team members
- Create issue in project repository
