# Hierarchical Category Data Model Design

## Overview

This document defines the data model design for custom categories and subcategories in BudApp, supporting hierarchical parent-child relationships while integrating with the existing predefined categories from Firebase Remote Config.

## Current State Analysis

### Existing Category System
- **Predefined Categories**: Delivered via Firebase Remote Config as simple string lists
  - Income categories: `['Salary', 'Freelance', 'Investment', 'Business', 'Other Income']`
  - Expense categories: `['Food & Dining', 'Transportation', 'Shopping', 'Entertainment', 'Bills & Utilities', 'Healthcare', 'Education', 'Travel', 'Other Expenses']`
- **Transaction Integration**: Transaction model has `categoryId` field (String type)
- **User Scope**: Categories will be user-specific, stored in `users/{userId}/categories/{categoryId}`

### Architecture Context
- **Database**: Cloud Firestore with user-scoped subcollections
- **Data Models**: Freezed immutable models with JSON serialization
- **State Management**: Riverpod with Repository pattern
- **Security**: Firestore Security Rules with user data isolation

## Data Model Design

### Core Category Model

```dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'category.freezed.dart';
part 'category.g.dart';

/// Category types for income vs expense categorization
enum CategoryType {
  income,   // Income categories
  expense,  // Expense categories
}

/// Category source to differentiate between predefined and custom categories
enum CategorySource {
  predefined, // Categories from Remote Config
  custom,     // User-created categories
}

/// Custom category data model with hierarchical support
@freezed
class Category with _$Category {
  const factory Category({
    required String id,                    // Unique category identifier
    required String userId,               // Owner user ID
    required String name,                 // Category display name (2-50 chars)
    required CategoryType type,           // Income or expense
    required CategorySource source,       // Predefined or custom
    String? parentId,                     // Parent category ID (null for root categories)
    String? description,                  // Optional description (max 200 chars)
    String? color,                        // Hex color code (#RRGGBB format)
    String? icon,                         // Icon identifier/name
    @Default(true) bool isActive,         // Soft delete flag
    @Default(0) int sortOrder,            // Sort order within same parent level
    required int schemaVersion,           // Schema version for migrations
    required DateTime createdAt,          // Creation timestamp
    required DateTime updatedAt,          // Last update timestamp
    @Default({}) Map<String, dynamic> metadata, // Additional metadata
  }) = _Category;

  const Category._();

  /// Create from JSON (Firestore document)
  factory Category.fromJson(Map<String, dynamic> json) {
    // Handle metadata conversion to ensure proper type
    final metadata = json['metadata'];
    final convertedJson = Map<String, dynamic>.from(json);
    
    if (metadata != null && metadata is Map) {
      convertedJson['metadata'] = Map<String, dynamic>.from(metadata);
    } else {
      convertedJson['metadata'] = <String, dynamic>{};
    }
    
    return _$CategoryFromJson(convertedJson);
  }

  /// Convert to JSON for Firestore
  Map<String, dynamic> toJson() => _$CategoryToJson(this);

  /// Check if this is a root category (no parent)
  bool get isRoot => parentId == null;

  /// Check if this is a subcategory (has parent)
  bool get isSubcategory => parentId != null;

  /// Generate a new category with updated timestamp
  Category updated() => copyWith(updatedAt: DateTime.now());

  /// Create a new custom category
  factory Category.createCustom({
    required String userId,
    required String name,
    required CategoryType type,
    String? parentId,
    String? description,
    String? color,
    String? icon,
    int sortOrder = 0,
  }) {
    final now = DateTime.now();
    return Category(
      id: '', // Will be set by repository
      userId: userId,
      name: name,
      type: type,
      source: CategorySource.custom,
      parentId: parentId,
      description: description,
      color: color,
      icon: icon,
      sortOrder: sortOrder,
      schemaVersion: 1,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Create a predefined category reference
  factory Category.createPredefined({
    required String userId,
    required String name,
    required CategoryType type,
    required String predefinedId,
  }) {
    final now = DateTime.now();
    return Category(
      id: predefinedId,
      userId: userId,
      name: name,
      type: type,
      source: CategorySource.predefined,
      parentId: null, // Predefined categories are always root level
      schemaVersion: 1,
      createdAt: now,
      updatedAt: now,
    );
  }
}
```

### Category Tree Structure Model

```dart
/// Hierarchical category tree structure for UI rendering
@freezed
class CategoryTree with _$CategoryTree {
  const factory CategoryTree({
    required Category category,
    @Default([]) List<CategoryTree> children,
    @Default(0) int depth,
  }) = _CategoryTree;

  const CategoryTree._();

  /// Check if this node has children
  bool get hasChildren => children.isNotEmpty;

  /// Get all descendant categories (recursive)
  List<Category> getAllDescendants() {
    final descendants = <Category>[];
    for (final child in children) {
      descendants.add(child.category);
      descendants.addAll(child.getAllDescendants());
    }
    return descendants;
  }

  /// Get total count of all categories in tree
  int get totalCount => 1 + children.fold(0, (sum, child) => sum + child.totalCount);
}
```

## Entity-Relationship Diagram

```
┌─────────────────────────────────────────────────────┐
│                     User                            │
│ ─────────────────────────────────────────────────── │
│ id: String (Primary Key)                           │
│ email: String                                      │
│ ... (other user fields)                           │
└─────────────────────────────────────────────────────┘
                           │
                           │ 1:N (userId)
                           ▼
┌─────────────────────────────────────────────────────┐
│                   Category                          │
│ ─────────────────────────────────────────────────── │
│ id: String (Primary Key)                           │
│ userId: String (Foreign Key → User.id)            │
│ name: String (Required, 2-50 chars)               │
│ type: CategoryType (income|expense)                │
│ source: CategorySource (predefined|custom)         │
│ parentId: String? (Self-referencing FK)           │
│ description: String? (Optional, max 200 chars)    │
│ color: String? (Hex color #RRGGBB)                │
│ icon: String? (Icon identifier)                   │
│ isActive: Boolean (Default: true)                 │
│ sortOrder: Integer (Default: 0)                   │
│ schemaVersion: Integer (Current: 1)               │
│ createdAt: DateTime (Required)                     │
│ updatedAt: DateTime (Required)                     │
│ metadata: Map<String, dynamic> (Additional data)  │
└─────────────────────────────────────────────────────┘
                           │
                           │ Self-referencing 1:N
                           │ (parentId → id)
                           │
                     ┌─────┴─────┐
                     ▼           ▼
              [Parent Category] [Child Categories]
                           │
                           │ 1:N (categoryId)
                           ▼
┌─────────────────────────────────────────────────────┐
│                 Transaction                         │
│ ─────────────────────────────────────────────────── │
│ id: String (Primary Key)                           │
│ userId: String (Foreign Key → User.id)            │
│ categoryId: String? (Foreign Key → Category.id)   │
│ ... (other transaction fields)                    │
└─────────────────────────────────────────────────────┘
```

## Firestore Collection Structure

### Document Path Pattern
```
users/{userId}/categories/{categoryId}
```

### Document Structure
```json
{
  "id": "cat_custom_001",
  "userId": "user_123",
  "name": "Groceries",
  "type": "expense",
  "source": "custom",
  "parentId": "cat_food_dining",
  "description": "Weekly grocery shopping",
  "color": "#4CAF50",
  "icon": "shopping_cart",
  "isActive": true,
  "sortOrder": 1,
  "schemaVersion": 1,
  "createdAt": "2025-01-01T00:00:00.000Z",
  "updatedAt": "2025-01-01T00:00:00.000Z",
  "metadata": {}
}
```

### Predefined Category Integration
Predefined categories from Remote Config will be virtually represented in the user's category collection:
- Not stored as documents (to avoid duplication)
- Dynamically merged with user categories in Repository layer
- Use predefined category name as ID for consistency
- Allow users to create subcategories under predefined categories

## Field Definitions and Constraints

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | Required, Unique, Max 100 chars | Firestore document ID |
| userId | String | Required, Max 100 chars | Reference to user document |
| name | String | Required, 2-50 chars, Unicode support | Display name |
| type | Enum | Required (income\|expense) | Category classification |
| source | Enum | Required (predefined\|custom) | Category origin |
| parentId | String | Optional, Max 100 chars, Must exist | Parent category reference |
| description | String | Optional, Max 200 chars | User description |
| color | String | Optional, Hex format #RRGGBB | UI color theme |
| icon | String | Optional, Max 50 chars | Icon identifier |
| isActive | Boolean | Required, Default true | Soft delete flag |
| sortOrder | Integer | Required, Default 0, Range 0-999 | Display ordering |
| schemaVersion | Integer | Required, Current value 1 | Data migration support |
| createdAt | Timestamp | Required, Server timestamp | Creation time |
| updatedAt | Timestamp | Required, Server timestamp | Last modification |
| metadata | Map | Optional, Max 1KB | Extensible data |

## Business Rules and Validation

### Hierarchical Constraints
1. **Maximum Depth**: Limit category tree to 3 levels (root → level 1 → level 2)
2. **Circular Reference Prevention**: Category cannot be its own parent (direct or indirect)
3. **Cross-Type Restriction**: Subcategories must match parent category type (income/expense)
4. **Parent Existence**: Parent category must exist and be active
5. **Source Consistency**: Custom categories can only have custom or predefined parents

### Naming Rules
1. **Uniqueness**: Category names must be unique within same parent scope and user
2. **Reserved Names**: Cannot duplicate predefined category names at root level
3. **Character Validation**: Unicode support, no leading/trailing whitespace
4. **Length Limits**: 2-50 characters for name, 0-200 for description

### Deletion Constraints
1. **Soft Delete Only**: Categories with transactions cannot be hard deleted
2. **Cascade Behavior**: Deactivating parent affects all descendant categories
3. **Transaction Reassignment**: Required before final deletion if transactions exist
4. **Subcategory Handling**: Must handle or reassign subcategories before parent deletion

## Indexing Strategy

### Composite Indexes
```javascript
// Primary query patterns for efficient retrieval
[
  // User's active categories by type and sort order
  {
    "collectionGroup": "categories",
    "queryScope": "COLLECTION",
    "fields": [
      {"fieldPath": "userId", "order": "ASCENDING"},
      {"fieldPath": "type", "order": "ASCENDING"},
      {"fieldPath": "isActive", "order": "ASCENDING"},
      {"fieldPath": "sortOrder", "order": "ASCENDING"}
    ]
  },
  
  // Hierarchical queries - parent's children
  {
    "collectionGroup": "categories",
    "queryScope": "COLLECTION", 
    "fields": [
      {"fieldPath": "userId", "order": "ASCENDING"},
      {"fieldPath": "parentId", "order": "ASCENDING"},
      {"fieldPath": "sortOrder", "order": "ASCENDING"}
    ]
  },
  
  // Root categories by type
  {
    "collectionGroup": "categories",
    "queryScope": "COLLECTION",
    "fields": [
      {"fieldPath": "userId", "order": "ASCENDING"},
      {"fieldPath": "parentId", "order": "ASCENDING"}, // null for root
      {"fieldPath": "type", "order": "ASCENDING"},
      {"fieldPath": "sortOrder", "order": "ASCENDING"}
    ]
  },

  // Category source filtering
  {
    "collectionGroup": "categories", 
    "queryScope": "COLLECTION",
    "fields": [
      {"fieldPath": "userId", "order": "ASCENDING"},
      {"fieldPath": "source", "order": "ASCENDING"},
      {"fieldPath": "type", "order": "ASCENDING"},
      {"fieldPath": "sortOrder", "order": "ASCENDING"}
    ]
  }
]
```

### Single-Field Indexes
- `userId` (automatic)
- `parentId` (for parent-child queries)
- `type` (for income/expense filtering)
- `isActive` (for active/inactive filtering)
- `createdAt` (for chronological sorting)
- `updatedAt` (for recently modified queries)

## Security Considerations

### Firestore Security Rules Enhancement
```javascript
// Enhanced security rules for categories collection
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId}/categories/{categoryId} {
      // Allow read/write only for authenticated user's own categories
      allow read, write: if isAuthenticated() && isOwner(userId);
      
      // Category creation validation
      allow create: if isAuthenticated() 
        && isOwner(userId)
        && validateCategoryCreate(resource.data);
      
      // Category update validation  
      allow update: if isAuthenticated()
        && isOwner(userId) 
        && validateCategoryUpdate(resource.data, request.data);
      
      // Category deletion (soft delete only)
      allow delete: if false; // Force soft delete through isActive flag
    }
  }
  
  // Validation functions
  function validateCategoryCreate(data) {
    return data.keys().hasAll(['id', 'userId', 'name', 'type', 'source', 'isActive', 'sortOrder', 'schemaVersion', 'createdAt', 'updatedAt'])
      && data.name is string && data.name.size() >= 2 && data.name.size() <= 50
      && data.type in ['income', 'expense'] 
      && data.source in ['predefined', 'custom']
      && data.isActive is bool
      && data.sortOrder is int && data.sortOrder >= 0 && data.sortOrder <= 999
      && data.schemaVersion == 1
      && (!data.keys().hasAny(['parentId']) || validateParentId(data.parentId))
      && (!data.keys().hasAny(['color']) || validateColor(data.color))
      && (!data.keys().hasAny(['description']) || (data.description is string && data.description.size() <= 200));
  }
  
  function validateCategoryUpdate(before, after) {
    return after.diff(before).affectedKeys().hasOnly(['name', 'description', 'color', 'icon', 'isActive', 'sortOrder', 'updatedAt'])
      && (after.name == before.name || (after.name is string && after.name.size() >= 2 && after.name.size() <= 50))
      && after.type == before.type  // Prevent type changes
      && after.source == before.source  // Prevent source changes
      && after.parentId == before.parentId;  // Prevent parent changes (use separate move operation)
  }
  
  function validateParentId(parentId) {
    return parentId is string && parentId.size() <= 100;
  }
  
  function validateColor(color) {
    return color is string && color.matches('^#[0-9A-Fa-f]{6}$');
  }
}
```

### Data Integrity Rules
1. **User Isolation**: Categories strictly scoped to user via Security Rules
2. **Immutable Fields**: ID, userId, type, source, parentId cannot be changed after creation
3. **Referential Integrity**: Parent category must exist (validated client-side due to Firestore limitations)
4. **Type Consistency**: Subcategories must match parent type
5. **Soft Delete Only**: Hard deletion prevented to maintain transaction references

## Query Patterns and Performance

### Common Query Operations
1. **Get All User Categories**: `userId == currentUser && isActive == true ORDER BY type, sortOrder`
2. **Get Root Categories**: `userId == currentUser && parentId == null ORDER BY type, sortOrder`
3. **Get Category Children**: `userId == currentUser && parentId == categoryId ORDER BY sortOrder`
4. **Get Income Categories**: `userId == currentUser && type == 'income' && isActive == true ORDER BY sortOrder`
5. **Get Expense Categories**: `userId == currentUser && type == 'expense' && isActive == true ORDER BY sortOrder`

### Performance Optimizations
1. **Batch Reads**: Load category trees in single batch operation
2. **Client-Side Caching**: Cache category tree in memory for session
3. **Lazy Loading**: Load subcategories on-demand for deep hierarchies
4. **Index Coverage**: All query patterns covered by composite indexes
5. **Pagination**: Support pagination for users with many categories

## Integration Points

### Repository Interface
```dart
abstract class ICategoryRepository {
  // CRUD operations
  Future<Category> create(Category category);
  Future<Category?> getById(String categoryId);
  Future<Category> update(Category category);
  Future<void> softDelete(String categoryId);
  
  // Hierarchical queries
  Stream<List<Category>> watchUserCategories(String userId);
  Future<List<Category>> getRootCategories(String userId, CategoryType? type);
  Future<List<Category>> getSubcategories(String userId, String parentId);
  Future<CategoryTree> getCategoryTree(String userId, CategoryType? type);
  
  // Validation and constraints
  Future<bool> canDelete(String categoryId);
  Future<List<Transaction>> getTransactionsUsingCategory(String categoryId);
  Future<bool> hasSubcategories(String categoryId);
  
  // Business logic helpers
  Future<int> getNextSortOrder(String userId, String? parentId);
  Future<bool> isNameUnique(String userId, String name, String? parentId, String? excludeId);
}
```

### Remote Config Integration
```dart
class CategoryService {
  // Merge predefined and custom categories
  Future<List<Category>> getAllCategoriesForUser(String userId) async {
    final customCategories = await categoryRepository.getUserCategories(userId);
    final predefinedCategories = await remoteConfigService.getPredefinedCategories();
    
    return _mergeCategoriesWithPredefined(customCategories, predefinedCategories, userId);
  }
  
  // Convert predefined categories to Category objects
  List<Category> _convertPredefinedCategories(PredefinedCategories predefined, String userId) {
    final categories = <Category>[];
    
    for (final name in predefined.incomeCategories) {
      categories.add(Category.create(
        userId: userId,
        name: name,
        type: CategoryType.income,
      ));
    }
    
    for (final name in predefined.expenseCategories) {
      categories.add(Category.create(
        userId: userId,
        name: name,
        type: CategoryType.expense,
      ));
    }
    
    return categories;
  }
}
```

## Migration Strategy

### Schema Version Management
- **Version 1**: Initial implementation with fields defined above
- **Future Versions**: Add new optional fields, maintain backward compatibility
- **Migration Function**: Handle data transformation between versions

### Data Migration Plan
1. **Existing Transactions**: Update transaction.categoryId to reference new category IDs
2. **Predefined Categories**: Create migration mapping from old string categories to new IDs
3. **User Categories**: Import any existing user categories with proper validation
4. **Transaction Reassignment**: Batch update transactions with new category references

## Testing Strategy

### Unit Tests
- Category model validation and business rules
- Hierarchical operations (tree building, descendant retrieval)
- Circular reference detection
- Data serialization/deserialization

### Integration Tests  
- Repository CRUD operations with Firestore
- Security Rules validation with Firebase Emulator
- Category tree queries with realistic data sets
- Performance testing with large category hierarchies

### End-to-End Tests
- Category creation and management workflows
- Transaction assignment to categories
- Deletion workflows with constraint validation
- Predefined category integration

## Implementation Roadmap

### Phase 1: Core Model (Current Task 9.1)
- ✅ Design data model and document structure
- ⏳ Create Category Freezed model
- ⏳ Define Firestore Security Rules
- ⏳ Create composite indexes

### Phase 2: Repository Implementation (Task 9.2)
- Implement ICategoryRepository interface
- Create Firestore-backed repository
- Add hierarchical query operations
- Implement validation and constraints

### Phase 3: Service Layer (Task 9.3-9.4)
- Create CategoryService with business logic
- Integrate with Remote Config predefined categories
- Implement category tree operations
- Add deletion constraint validation

### Phase 4: UI Implementation (Task 9.5-9.6)
- Category listing and tree visualization
- Category creation and editing forms
- Icon and color selection components
- Hierarchical navigation interface

### Phase 5: Deletion Workflows (Task 9.7-9.9)
- Deletion constraint checking
- Transaction reassignment interface
- Guided deletion workflows
- Cascade deletion handling

## Conclusion

This hierarchical category data model provides a robust foundation for custom category management while maintaining integration with predefined categories from Remote Config. The design supports arbitrary depth hierarchies, ensures data integrity through comprehensive validation, and provides efficient query patterns for optimal performance.

The model follows BudApp's established patterns using Freezed for immutability, Firestore for persistence, and Riverpod for state management, ensuring consistency with the existing codebase architecture.