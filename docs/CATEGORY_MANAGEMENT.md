# Category Management System

## Overview

The Category Management System provides a comprehensive foundation for organizing financial transactions in BudApp. It supports hierarchical category structures, type-based organization, both predefined and custom categories, and full Firestore integration with proper data conversion methods.

## Architecture

### Data Model

The `Category` model is implemented using Freezed for immutability and includes:

```dart
@freezed
class Category with _$Category {
  const factory Category({
    required String id,
    required String userId,
    required String name,
    required CategoryType type,
    required CategorySource source,
    String? description,
    String? color,
    String? icon,
    String? parentId,
    @Default(true) bool isActive,
    @Default(0) int sortOrder,
    @Default(1) int schemaVersion,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default({}) Map<String, dynamic> metadata,
  }) = _Category;
}
```

### Enums

#### CategoryType
- `income`: Categories for income transactions
- `expense`: Categories for expense transactions

#### CategorySource
- `custom`: User-created categories
- `predefined`: System-provided categories from Remote Config

### Hierarchical Structure

Categories support parent-child relationships:
- **Root Categories**: Categories without a `parentId`
- **Subcategories**: Categories with a `parentId` referencing another category

Helper methods:
- `category.isRoot`: Returns true if category has no parent
- `category.isSubcategory`: Returns true if category has a parent

## Repository Pattern

### Interface: ICategoryRepository

```dart
abstract class ICategoryRepository {
  Future<String> create(Category category);
  Future<Category?> getById(String categoryId);
  Future<void> update(Category category);
  Future<void> delete(String categoryId);
  Future<List<Category>> getCategoriesByUserId(String userId);
  Future<List<Category>> getCategoriesByType(String userId, CategoryType type);
  Stream<List<Category>> watchCategoriesByUserId(String userId);
  Future<bool> validateCategory(Category category);
  Future<bool> isCategoryNameUnique(String userId, String name, [String? excludeId]);
  Future<int> getUserCategoryCount(String userId);

  // Deletion constraint methods
  Future<bool> hasDependentTransactionsForUser(String categoryId, String userId);
  Future<bool> hasChildSubcategories(String categoryId);
  Future<void> deleteCategoryWithConstraints(String categoryId, String userId);
  Future<void> deleteSubcategoryWithConstraints(String subcategoryId, String userId);
  Future<void> deactivateCategoryWithUserId(String categoryId, String userId);
}
```

### Implementation: CategoryRepositoryImpl

The Firestore implementation provides:
- **CRUD Operations**: Complete create, read, update, delete functionality with deletion constraints
- **Real-time Updates**: Stream-based data synchronization
- **Validation**: Business rule enforcement and data integrity
- **Querying**: Filtered category retrieval by type and status
- **User Isolation**: All operations scoped to authenticated user
- **Deletion Safety**: Comprehensive constraint checking to prevent data corruption

## Riverpod Integration

### Core Providers

```dart
// Repository provider
final categoryRepositoryProvider = Provider<ICategoryRepository>((ref) {
  return CategoryRepositoryImpl(ref.watch(firestoreServiceProvider));
});

// All categories stream
final categoriesProvider = StreamProvider.autoDispose<List<Category>>((ref) {
  final userId = ref.watch(currentUserProvider)?.uid;
  if (userId == null) return Stream.value([]);
  
  return ref.watch(categoryRepositoryProvider).watchCategoriesByUserId(userId);
});

// Filtered categories by type
final categoriesByTypeProvider = StreamProvider.autoDispose
    .family<List<Category>, CategoryType>((ref, type) {
  final categories = ref.watch(categoriesProvider).value ?? [];
  return Stream.value(
    categories.where((category) => category.type == type && category.isActive).toList(),
  );
});

// Root categories only
final rootCategoriesProvider = StreamProvider.autoDispose<List<Category>>((ref) {
  final categories = ref.watch(categoriesProvider).value ?? [];
  return Stream.value(
    categories.where((category) => category.isRoot && category.isActive).toList(),
  );
});
```

### Usage in Widgets

```dart
class CategoryListWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final categoriesAsync = ref.watch(categoriesProvider);
    
    return categoriesAsync.when(
      data: (categories) => ListView.builder(
        itemCount: categories.length,
        itemBuilder: (context, index) => CategoryTile(category: categories[index]),
      ),
      loading: () => const CircularProgressIndicator(),
      error: (error, stack) => Text('Error: $error'),
    );
  }
}
```

## Validation Rules

### Business Logic Validation

1. **Name Requirements**:
   - Minimum 2 characters
   - Maximum 50 characters
   - Must be unique per user
   - Cannot be empty or whitespace only

2. **Type Consistency**:
   - Parent and child categories must have the same type
   - Cannot change type if category has subcategories

3. **Hierarchy Constraints**:
   - Maximum 2 levels deep (root → subcategory)
   - Cannot create circular references
   - Cannot set self as parent

4. **User Limits**:
   - Custom categories limited by premium tier
   - Predefined categories don't count toward limits

## Firestore Integration

### Data Conversion Methods

The Category model includes specialized methods for Firestore integration:

#### fromFirestore()
```dart
factory Category.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
  // Handles Firestore Timestamp conversion to DateTime
  // Ensures document ID consistency
  // Validates required fields
}
```

#### toFirestore()
```dart
Map<String, dynamic> toFirestore() {
  // Converts DateTime to Firestore Timestamp
  // Prepares data for Firestore storage
  // Maintains field type consistency
}
```

### Timestamp Handling
- **Storage**: Firestore `Timestamp` objects for server-side timestamps
- **Model**: Dart `DateTime` objects for client-side operations
- **Conversion**: Automatic conversion in `fromFirestore()` and `toFirestore()`

## Firestore Schema

### Collection Structure
```
users/{userId}/categories/{categoryId}
```

### Document Fields
```json
{
  "id": "string",
  "userId": "string",
  "name": "string",
  "type": "income|expense",
  "source": "custom|predefined",
  "description": "string?",
  "color": "string?",
  "icon": "string?",
  "parentId": "string?",
  "isActive": "boolean",
  "sortOrder": "number",
  "schemaVersion": "number",
  "createdAt": "timestamp",
  "updatedAt": "timestamp",
  "metadata": "map"
}
```

### Deletion Constraints

The Category Management System implements comprehensive deletion constraints to maintain data integrity:

#### Client-Side Validation
- **Transaction Dependencies**: Categories with associated transactions cannot be deleted
- **Child Subcategories**: Categories with active child subcategories cannot be deleted
- **Predefined Protection**: Only custom categories can be deleted (predefined categories are protected)

#### CategoryDeletionService
Centralized service for handling category deletion with proper validation and transaction re-assignment:

```dart
class CategoryDeletionService {
  // Deletion with constraints
  Future<CategoryDeletionResult> deleteCategoryWithConstraints(String categoryId);
  Future<CategoryDeletionResult> deleteSubcategoryWithConstraints(String subcategoryId);

  // Transaction re-assignment
  Future<List<Transaction>> getTransactionsForCategory(String userId, String categoryId);
  Future<CategoryReassignmentResult> validateReassignmentTarget(String userId, String oldCategoryId, String newCategoryId);
  Future<CategoryReassignmentResult> reassignCategoryTransactions(String userId, String oldCategoryId, String newCategoryId);
  Future<CategoryReassignmentResult> deleteWithReassignment(String userId, String categoryId, String newCategoryId);
  Future<CategoryReassignmentResult> deleteSubcategoryWithReassignment(String userId, String subcategoryId, String parentId, String newCategoryId);
}
```

#### Deletion Methods
- `deleteCategoryWithConstraints()`: Safe category deletion with full validation
- `deleteSubcategoryWithConstraints()`: Safe subcategory deletion with hierarchy checks
- `hasDependentTransactionsForUser()`: Checks for transaction dependencies
- `hasChildSubcategories()`: Validates no active child subcategories exist

#### Transaction Re-assignment Methods
- `getTransactionsForCategory()`: Retrieves all transactions associated with a category
- `validateReassignmentTarget()`: Validates that a new category is suitable for re-assignment
- `reassignCategoryTransactions()`: Performs bulk transaction re-assignment to a new category
- `deleteWithReassignment()`: Combined re-assignment and deletion operation for categories
- `deleteSubcategoryWithReassignment()`: Combined re-assignment and deletion operation for subcategories

#### Re-assignment Validation
The system validates re-assignment targets to ensure data integrity:
- **Same Type**: New category must have the same CategoryType (income/expense)
- **Active Status**: Target category must be active
- **User Ownership**: Target category must belong to the same user
- **Different Category**: Cannot reassign to the same category
- **Existence Check**: Target category must exist in the system

#### Error Handling
- **CategoryDeletionResult**: Class for deletion operation results
- **CategoryReassignmentResult**: Class for re-assignment operation results with transaction counts
- **Structured Results**: Consistent error responses with detailed information
- **Localized Messages**: User-friendly error messages explaining operation failures
- **Action Guidance**: Clear instructions on how to resolve conflicts
- **Success Messages**: Informative feedback on successful operations

### Security Rules

Categories are protected by Firestore Security Rules:
- User isolation: Users can only access their own categories
- Data validation: All fields validated for type and constraints
- Referential integrity: Parent category existence validated
- Business rules: Name uniqueness and hierarchy constraints enforced
- Deletion constraints: Only custom categories can be deleted, predefined categories protected

## Testing

### Test Coverage

The Category Management System includes comprehensive tests:

1. **Model Tests**: Serialization, business logic, helper methods
2. **Repository Tests**: CRUD operations, validation, error handling
3. **Provider Tests**: State management, stream updates, filtering
4. **Integration Tests**: End-to-end category workflows

### Running Tests

```bash
# Run all category-related tests
flutter test test/data/repositories/category_repository_test.dart

# Run with coverage
flutter test --coverage
```

## Usage Examples

### Creating a Category

```dart
final category = Category(
  id: 'category_id',
  userId: 'user_id',
  name: 'Groceries',
  type: CategoryType.expense,
  source: CategorySource.custom,
  description: 'Food and household items',
  color: '#4CAF50',
  icon: 'shopping_cart',
  isActive: true,
  sortOrder: 0,
  schemaVersion: 1,
  createdAt: DateTime.now(),
  updatedAt: DateTime.now(),
  metadata: {},
);

await ref.read(categoryRepositoryProvider).create(category);
```

### Querying Categories

```dart
// Get all active categories for user
final categories = await ref.read(categoryRepositoryProvider)
    .getCategoriesByUserId(userId);

// Get only expense categories
final expenseCategories = await ref.read(categoryRepositoryProvider)
    .getCategoriesByType(userId, CategoryType.expense);

// Watch for real-time updates
ref.listen(categoriesProvider, (previous, next) {
  next.when(
    data: (categories) => print('Categories updated: ${categories.length}'),
    loading: () => print('Loading categories...'),
    error: (error, stack) => print('Error: $error'),
  );
});
```

## Integration Points

### Transaction Management
Categories will be used for:
- Transaction categorization during creation
- Filtering and grouping transactions
- Budget allocation and tracking

### Budget Tracking
Categories enable:
- Category-based budget creation
- Spending tracking by category
- Budget vs actual analysis

### Goal Management
Categories support:
- Category-specific savings goals
- Spending reduction targets
- Financial planning by category

## Frontend UI Implementation

### Categories List Screen
The main category management interface provides:

- **Hierarchical Display**: Tree view showing parent-child relationships
- **Interactive Trees**: Expandable/collapsible categories with smooth animations
- **Visual Connections**: Connection lines showing category hierarchy
- **Summary Dashboard**: Statistics showing category counts by type and source
- **Advanced Filtering**: Filter by type (income/expense), source (custom/predefined), and active status

### UI Components

#### CategoriesListScreen
Main screen located at `lib/features/categories/presentation/screens/categories_list_screen.dart`:
- Displays categories in hierarchical tree structure
- Includes summary section with category statistics
- Provides filter button for advanced filtering
- Handles loading, error, and empty states
- Supports pull-to-refresh functionality

#### CategoryTreeWidget
Hierarchical display component at `lib/features/categories/presentation/widgets/category_tree_widget.dart`:
- Renders parent categories with expand/collapse functionality
- Shows visual connection lines for subcategories
- Supports smooth animations for tree expansion
- Handles category and subcategory tap events

#### CategoryCard
Individual category display at `lib/features/categories/presentation/widgets/category_card.dart`:
- Shows category icon, name, and description
- Displays type indicators (income/expense)
- Includes action menu for category operations
- Supports different depth levels for hierarchy

#### CategoryTypeFilter
Advanced filtering interface at `lib/features/categories/presentation/widgets/category_type_filter.dart`:
- Bottom sheet with filter options
- Filter by category type (income/expense/all)
- Filter by category source (custom/predefined/all)
- Toggle for showing inactive categories
- Apply and clear filter actions

#### EmptyCategoriesState
User onboarding component at `lib/features/categories/presentation/widgets/empty_categories_state.dart`:
- Friendly illustration and messaging
- Call-to-action for creating first category
- Help dialog explaining category concepts
- Links to category creation workflow

### Navigation Integration
- Added to bottom navigation with "Categories" tab
- Route: `/categories` for main listing
- Route: `/categories/:id` for category details (planned)
- FAB integration for "Add Category" action

### Localization
Complete i18n support with 50+ category-specific strings:
- Category type labels (income, expense)
- Source labels (custom, predefined)
- Filter options and descriptions
- Empty state messaging
- Help dialog content
- Action button labels

### Testing
Comprehensive widget test suite covering:
- Category list display with data
- Loading and error states
- Empty state with onboarding
- Filter bottom sheet functionality
- Summary section statistics
- Pull-to-refresh interactions

## Category Creation/Editing UI

### ✅ Complete Implementation

The category management system includes comprehensive CRUD interfaces for creating and editing categories and subcategories.

#### Form Components

**CategoryValidators Service**
- Client-side validation with localized error messages
- Field validation for name, type, description, color, and icon
- Business rule validation for hierarchy and uniqueness

**Reusable Form Widgets**
- `CategoryTypeSelector`: Radio button selection for income/expense types with descriptions
- `CategoryColorSelector`: Color palette with predefined options and default selection
- `CategoryIconSelector`: Icon library with visual preview and default option
- `ParentCategorySelector`: Dropdown for selecting parent categories with type filtering

#### CRUD Screens

**CategoryCreateScreen**
- New category creation with type selection
- Optional color and icon customization
- Real-time validation with immediate feedback
- Material 3 design with proper theming

**CategoryEditScreen**
- Existing category modification
- Form pre-population with current values
- Validation and error handling
- Success/error notifications

**SubcategoryCreateScreen**
- Subcategory creation with parent selection
- Type inheritance from parent category
- Hierarchy validation and depth limits
- Parent category filtering by type

**SubcategoryEditScreen**
- Subcategory modification with hierarchy validation
- Parent category selection with self-exclusion
- Type consistency enforcement
- Complete form validation

#### Navigation Integration

- Floating action button on categories list screen
- Modal bottom sheet for creation options
- Complete routing setup for all CRUD operations
- Breadcrumb navigation for subcategory editing

#### User Experience Features

- Real-time field validation with error display
- Loading states during form submission
- Success/error feedback with SnackBar notifications
- Accessibility support with semantic labels
- Dark/light theme compatibility

## Next Steps

1. **✅ Category Creation/Editing Forms**: Complete user interface for CRUD operations
2. **Transaction Integration**: Category selection in transaction forms
3. **Budget Integration**: Category-based budget allocation
4. **Remote Config Integration**: Predefined category synchronization
