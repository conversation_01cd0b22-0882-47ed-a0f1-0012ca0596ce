# Documentation Index

This document provides a comprehensive index of all documentation in the BudApp project, organized by category and development task for easy navigation.

## Quick Navigation

| Category | Documents | Purpose |
|----------|-----------|---------|
| [**Architecture**](#core-architecture--patterns) | System design, patterns, organization | Understanding system design |
| [**Development Rules**](#development-rules--best-practices) | Firebase, testing, quality standards | Daily development guidelines |
| [**Feature Guides**](#feature-implementation-guides) | Auth, accounts, transactions, budgets | Implementing specific features |
| [**Firebase & Security**](#firebase-integration--security) | Security rules, testing, configuration | Firebase development |
| [**Testing**](#testing--quality-assurance) | Testing strategy, patterns, tools | Quality assurance |
| [**Project Management**](#project-management--development) | PRD, setup, workflow, tasks | Project organization |

---

## Core Architecture & Patterns

### Primary Architecture Documents
- [`docs/architecture.md`](architecture.md) - Complete system architecture overview with mermaid diagrams
- [`docs/rules/flutter_app_architecture.md`](rules/flutter_app_architecture.md) - Flutter-specific architecture patterns and best practices
- [`docs/rules/riverpod.md`](rules/riverpod.md) - Comprehensive Riverpod state management patterns and rules

### Design Patterns & Organization
- [`docs/refactor/RESTRUCTURING_PLAN.md`](refactor/RESTRUCTURING_PLAN.md) - Feature-based architecture migration plan
- [`docs/refactor/RESTRUCTURING_SUMMARY.md`](refactor/RESTRUCTURING_SUMMARY.md) - Architecture refactoring results
- [`docs/rules/effective_dart.md`](rules/effective_dart.md) - Dart language best practices
- [`docs/SESSION_MANAGEMENT.md`](SESSION_MANAGEMENT.md) - User session and state persistence patterns

### Code Quality & Reviews
- [`docs/rules/code_review.md`](rules/code_review.md) - Code review standards and checklists
- [`docs/rules/dart_3_updates.md`](rules/dart_3_updates.md) - Dart 3 migration and new features
- [`docs/rules/flutter_errors.md`](rules/flutter_errors.md) - Common Flutter error patterns and solutions

---

## Development Rules & Best Practices

### Firebase Integration Rules
- [`docs/rules/firebase/cloud_firestore.md`](rules/firebase/cloud_firestore.md) - Firestore best practices, performance, and security
- [`docs/rules/firebase/firebase_auth.md`](rules/firebase/firebase_auth.md) - Authentication patterns, error handling, and security
- [`docs/rules/firebase/firebase_remote_config.md`](rules/firebase/firebase_remote_config.md) - Server-side configuration management
- [`docs/rules/firebase/firebase_analytics.md`](rules/firebase/firebase_analytics.md) - Analytics integration and tracking patterns
- [`docs/rules/firebase/firebase_crashlytics.md`](rules/firebase/firebase_crashlytics.md) - Error reporting and crash analysis
- [`docs/rules/firebase/flutterfire_configure.md`](rules/firebase/flutterfire_configure.md) - Firebase project configuration

### Testing Standards
- [`docs/rules/mocktail.md`](rules/mocktail.md) - Mocking patterns and testing utilities
- [`docs/ASYNC_VALUE_ERROR_HANDLING.md`](ASYNC_VALUE_ERROR_HANDLING.md) - AsyncValue error handling patterns

---

## Feature Implementation Guides

### Authentication & Security
- [`docs/AUTHENTICATION.md`](AUTHENTICATION.md) - Complete authentication system implementation
- [`docs/SECURE_STORAGE_USAGE.md`](SECURE_STORAGE_USAGE.md) - Secure storage patterns for sensitive data

### Account Management
- [`docs/ACCOUNT_DATA_SCHEMA.md`](ACCOUNT_DATA_SCHEMA.md) - Account data model and validation rules
- [`docs/ACCOUNT_MANAGEMENT_UI_DESIGN.md`](ACCOUNT_MANAGEMENT_UI_DESIGN.md) - Account UI components and patterns
- [`docs/ACCOUNT_VALIDATION.md`](ACCOUNT_VALIDATION.md) - Account validation rules and business logic

### Transaction System
- [`docs/TRANSACTION_DATABASE_SCHEMA.md`](TRANSACTION_DATABASE_SCHEMA.md) - Transaction data model and relationships
- [`docs/TRANSACTION_CARD_UI_DESIGN.md`](TRANSACTION_CARD_UI_DESIGN.md) - Transaction UI component design
- [`docs/transaction_form_ui_design.md`](transaction_form_ui_design.md) - Transaction form patterns and validation
- [`docs/diagrams/Transaction_Database_Schema_Structure.md`](diagrams/Transaction_Database_Schema_Structure.md) - Visual transaction schema
- [`docs/diagrams/Transaction_Type_Business_Logic.md`](diagrams/Transaction_Type_Business_Logic.md) - Transaction type business rules

### Budget & Category Management
- [`docs/BUDGET_MANAGEMENT.md`](BUDGET_MANAGEMENT.md) - Budget system implementation and UI patterns
- [`docs/CATEGORY_MANAGEMENT.md`](CATEGORY_MANAGEMENT.md) - Category system with hierarchical organization
- [`docs/HIERARCHICAL_CATEGORY_DATA_MODEL.md`](HIERARCHICAL_CATEGORY_DATA_MODEL.md) - Category data structure and relationships
- [`docs/FIRESTORE_CATEGORY_COLLECTION_STRUCTURE.md`](FIRESTORE_CATEGORY_COLLECTION_STRUCTURE.md) - Firestore category schema

### UI/UX & Navigation
- [`docs/BOTTOM_NAVIGATION.md`](BOTTOM_NAVIGATION.md) - Custom bottom navigation implementation
- [`docs/NAVIGATION_REFACTORING.md`](NAVIGATION_REFACTORING.md) - Navigation architecture and routing patterns
- [`docs/TIME_PERIOD_MODAL_REDESIGN.md`](TIME_PERIOD_MODAL_REDESIGN.md) - Time period selection UI design
- [`docs/text-overflow-system.md`](text-overflow-system.md) - Global text overflow handling system

### Remote Configuration
- [`docs/REMOTE_CONFIG.md`](REMOTE_CONFIG.md) - Server-side configuration and feature flags

---

## Firebase Integration & Security

### Core Firebase Documentation
- [`firebase/README.md`](../firebase/README.md) - Firebase security architecture overview
- [`firebase/security-requirements.md`](../firebase/security-requirements.md) - Comprehensive security rules requirements
- [`firebase/REFERENTIAL_INTEGRITY_LIMITATIONS.md`](../firebase/REFERENTIAL_INTEGRITY_LIMITATIONS.md) - ⚠️ **Critical security limitations documentation**

### Security Rules & Testing
- [`firebase/test/README.md`](../firebase/test/README.md) - Security rules testing guide
- Root files: `firestore.rules`, `firestore.indexes.json` - Production security rules and database indexes

### Configuration Files
- `firebase.json` - Firebase project configuration
- Various `google-services.json` and `GoogleService-Info.plist` files for multi-environment setup

---

## Testing & Quality Assurance

### Testing Strategy & Implementation
- [`docs/TESTING.md`](TESTING.md) - Comprehensive testing guide and strategy
- [`docs/testing/TESTING_STRATEGY.md`](testing/TESTING_STRATEGY.md) - Detailed testing approach and patterns
- [`docs/testing/IMPLEMENTATION_PLAN.md`](testing/IMPLEMENTATION_PLAN.md) - Testing implementation roadmap

### Development Tools
- [`docs/EMULATOR_TROUBLESHOOTING.md`](EMULATOR_TROUBLESHOOTING.md) - Firebase emulator setup and debugging
- [`docs/performance-monitoring.md`](performance-monitoring.md) - Performance tracking and optimization

### Code Analysis
- [`docs/analysis/current_screen_patterns_analysis.md`](analysis/current_screen_patterns_analysis.md) - Screen pattern analysis for consolidation

---

## Project Management & Development

### Project Documentation
- [`docs/PRD.md`](PRD.md) - Product Requirements Document (MVP specification)
- [`docs/implementation-history.md`](implementation-history.md) - Complete project implementation timeline
- [`docs/DEVELOPMENT_WORKFLOW.md`](DEVELOPMENT_WORKFLOW.md) - Development process and Git workflow

### Environment & Setup
- [`docs/ENVIRONMENT_SETUP.md`](ENVIRONMENT_SETUP.md) - Complete development environment configuration
- [`docs/IOS_SETUP.md`](IOS_SETUP.md) - iOS-specific setup and configuration
- [`README.md`](../README.md) - Project overview and quick start guide
- [`CHANGELOG.md`](../CHANGELOG.md) - Project change history

### Android Signing & CI/CD
- [`docs/ANDROID_SIGNING.md`](ANDROID_SIGNING.md) - **Critical security enhancement** - Environment-specific keystores and signing validation
- [`docs/GITHUB_WORKFLOWS_SECURITY.md`](GITHUB_WORKFLOWS_SECURITY.md) - **Advanced CI/CD pipeline** - Branch-based deployment strategy

### Refactoring & Improvements
- [`docs/refactor/001-refactor_summary.md`](refactor/001-refactor_summary.md) - Authentication refactoring summary
- [`docs/refactor/002-refactor_summary.md`](refactor/002-refactor_summary.md) - Architecture improvements summary
- [`docs/refactor/003-consolidations.md`](refactor/003-consolidations.md) - Code consolidation achievements
- [`docs/refactor/IMPLEMENTATION_CHECKLIST.md`](refactor/IMPLEMENTATION_CHECKLIST.md) - Implementation tracking

### Task Documentation
- [`docs/tasks/task13.md`](tasks/task13.md) - Budget creation and progress tracking implementation
- [`docs/tasks/task14.md`](tasks/task14.md) - Budget editing and deletion implementation
- [`docs/tasks/task15.md`](tasks/task15.md) - Future task planning

### Architecture Diagrams
- [`docs/diagrams/flowchart TD.md`](diagrams/flowchart%20TD.md) - System flow diagrams
- [`docs/diagrams/sequenceDiagram.md`](diagrams/sequenceDiagram.md) - Sequence diagrams for user flows

---

## Memory Bank & AI Context

### AI Assistant Context
- [`.taskmaster/memory-bank/projectbrief.md`](../.taskmaster/memory-bank/projectbrief.md) - Project overview and goals
- [`.taskmaster/memory-bank/activeContext.md`](../.taskmaster/memory-bank/activeContext.md) - Current development status and focus
- [`.taskmaster/memory-bank/progress.md`](../.taskmaster/memory-bank/progress.md) - Completed features and what's left to build
- [`.taskmaster/memory-bank/systemPatterns.md`](../.taskmaster/memory-bank/systemPatterns.md) - System architecture and technical patterns
- [`.taskmaster/memory-bank/techContext.md`](../.taskmaster/memory-bank/techContext.md) - Technology stack and development setup
- [`.taskmaster/memory-bank/productContext.md`](../.taskmaster/memory-bank/productContext.md) - Product context and user needs
- [`.taskmaster/memory-bank/memory_bank_instructions.md`](../.taskmaster/memory-bank/memory_bank_instructions.md) - Memory bank usage guide

---

## Quick Reference by Development Task

### Setting Up Development Environment
1. [`docs/ENVIRONMENT_SETUP.md`](ENVIRONMENT_SETUP.md) - Complete setup guide
2. [`docs/IOS_SETUP.md`](IOS_SETUP.md) - iOS-specific configuration
3. [`docs/EMULATOR_TROUBLESHOOTING.md`](EMULATOR_TROUBLESHOOTING.md) - Firebase emulator setup

### Implementing New Features
1. [`docs/architecture.md`](architecture.md) - Architecture patterns to follow
2. [`docs/rules/flutter_app_architecture.md`](rules/flutter_app_architecture.md) - Flutter-specific patterns
3. [`docs/rules/riverpod.md`](rules/riverpod.md) - State management implementation
4. Reference existing feature implementations in [`lib/features/accounts/`](../lib/features/accounts/) for patterns

### Working with Firebase
1. [`docs/rules/firebase/cloud_firestore.md`](rules/firebase/cloud_firestore.md) - Firestore best practices
2. [`firebase/security-requirements.md`](../firebase/security-requirements.md) - Security rules requirements
3. [`firebase/README.md`](../firebase/README.md) - Security architecture overview

### Testing & Quality
1. [`docs/TESTING.md`](TESTING.md) - Testing guide and commands
2. [`docs/rules/mocktail.md`](rules/mocktail.md) - Mocking patterns
3. [`docs/rules/code_review.md`](rules/code_review.md) - Code review standards

### Understanding Project Status
1. [`.taskmaster/memory-bank/activeContext.md`](../.taskmaster/memory-bank/activeContext.md) - Current work focus
2. [`.taskmaster/memory-bank/progress.md`](../.taskmaster/memory-bank/progress.md) - What's complete and what's next
3. [`docs/implementation-history.md`](implementation-history.md) - Complete project timeline

### Security & Production
1. [`docs/ANDROID_SIGNING.md`](ANDROID_SIGNING.md) - Android signing security
2. [`docs/GITHUB_WORKFLOWS_SECURITY.md`](GITHUB_WORKFLOWS_SECURITY.md) - CI/CD pipeline security
3. [`firebase/security-requirements.md`](../firebase/security-requirements.md) - Firebase security rules

---

## Document Categories Summary

| Category | Documents | Focus Area |
|----------|-----------|------------|
| **Architecture** | 12 docs | System design, patterns, organization |
| **Firebase Rules** | 8 docs | Firebase integration and best practices |
| **Feature Guides** | 15 docs | Specific feature implementation |
| **Testing** | 6 docs | Quality assurance and testing strategy |
| **Project Management** | 10 docs | Setup, workflow, and project organization |
| **Security** | 8 docs | Firebase security, Android signing, CI/CD |
| **Memory Bank** | 7 docs | AI context and project knowledge |

**Total**: 66+ documentation files covering all aspects of BudApp development.

---

*This index is maintained to provide quick access to all project documentation. For daily development guidance, see the main [`CLAUDE.md`](../CLAUDE.md) file.*