# Secure Storage Usage Guide

This document explains how to use the `SecureStorageService` in BudApp for storing sensitive data securely.

## Overview

The `SecureStorageService` provides a secure way to store sensitive information such as:
- Biometric authentication keys
- API keys for third-party services (Plaid, <PERSON>e, etc.)
- User PINs or temporary passwords
- Refresh tokens (if implemented outside Firebase handling)
- Any other sensitive user data requiring encryption

## Architecture

The service follows BudApp's established patterns:
- **Riverpod Integration**: Available via `secureStorageServiceProvider`
- **Error Handling**: Custom exceptions with proper error context
- **Testing Support**: Comprehensive test coverage with mocks
- **Platform Support**: Works across iOS, Android, Web, Desktop

## Basic Usage

### 1. Accessing the Service

```dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/providers.dart';

class MyWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final secureStorage = ref.read(secureStorageServiceProvider);
    
    // Use the service...
    return Container();
  }
}
```

### 2. Storing Sensitive Data

```dart
// Store a generic secure value
await secureStorage.write('my_secret_key', 'sensitive_value');

// Store API key for a third-party service
await secureStorage.storeApiKey('plaid', 'plaid_api_key_here');

// Store biometric authentication key
await secureStorage.storeBiometricKey('biometric_key_value');

// Store user PIN hash (always hash PINs before storage!)
final hashedPin = hashPin('1234'); // Use proper hashing
await secureStorage.storeUserPin(hashedPin);
```

### 3. Retrieving Sensitive Data

```dart
// Read a generic secure value
final value = await secureStorage.read('my_secret_key');
if (value != null) {
  // Use the value
}

// Read API key for a service
final plaidKey = await secureStorage.getApiKey('plaid');
if (plaidKey != null) {
  // Initialize Plaid with the key
}

// Read biometric key
final biometricKey = await secureStorage.getBiometricKey();
if (biometricKey != null) {
  // Use for biometric authentication
}
```

### 4. Managing Keys

```dart
// Check if a key exists
final hasKey = await secureStorage.containsKey('my_secret_key');

// Get all stored keys
final allKeys = await secureStorage.getAllKeys();

// Delete a specific key
await secureStorage.delete('my_secret_key');

// Clear all secure data (use with caution!)
await secureStorage.deleteAll();
```

## Error Handling

The service throws `SecureStorageException` for all errors:

```dart
try {
  await secureStorage.write('key', 'value');
} on SecureStorageException catch (e) {
  // Handle secure storage specific errors
  print('Secure storage error: ${e.message}');
  if (e.key != null) {
    print('Failed key: ${e.key}');
  }
  if (e.originalError != null) {
    print('Original error: ${e.originalError}');
  }
} catch (e) {
  // Handle other errors
  print('Unexpected error: $e');
}
```

## Common Use Cases

### 1. Biometric Authentication

```dart
class BiometricAuthService {
  final SecureStorageService _secureStorage;
  
  BiometricAuthService(this._secureStorage);
  
  Future<void> enableBiometricAuth(String authKey) async {
    try {
      await _secureStorage.storeBiometricKey(authKey);
    } on SecureStorageException catch (e) {
      throw BiometricSetupException('Failed to store biometric key: ${e.message}');
    }
  }
  
  Future<String?> getBiometricAuthKey() async {
    try {
      return await _secureStorage.getBiometricKey();
    } on SecureStorageException catch (e) {
      print('Failed to retrieve biometric key: ${e.message}');
      return null;
    }
  }
  
  Future<void> disableBiometricAuth() async {
    try {
      await _secureStorage.delete('biometric_key');
    } on SecureStorageException catch (e) {
      print('Failed to delete biometric key: ${e.message}');
    }
  }
}
```

### 2. Third-Party API Integration

```dart
class PlaidService {
  final SecureStorageService _secureStorage;
  
  PlaidService(this._secureStorage);
  
  Future<void> configureApiKey(String apiKey) async {
    await _secureStorage.storeApiKey('plaid', apiKey);
  }
  
  Future<PlaidClient?> createClient() async {
    final apiKey = await _secureStorage.getApiKey('plaid');
    if (apiKey == null) {
      throw PlaidConfigurationException('API key not configured');
    }
    
    return PlaidClient(apiKey: apiKey);
  }
}
```

### 3. User PIN Authentication

```dart
class PinAuthService {
  final SecureStorageService _secureStorage;
  
  PinAuthService(this._secureStorage);
  
  Future<void> setUserPin(String pin) async {
    // Always hash PINs before storage
    final hashedPin = _hashPin(pin);
    await _secureStorage.storeUserPin(hashedPin);
  }
  
  Future<bool> verifyPin(String inputPin) async {
    final storedHash = await _secureStorage.getUserPinHash();
    if (storedHash == null) return false;
    
    final inputHash = _hashPin(inputPin);
    return inputHash == storedHash;
  }
  
  Future<void> removePin() async {
    await _secureStorage.delete('user_pin_hash');
  }
  
  String _hashPin(String pin) {
    // Use proper hashing algorithm (e.g., bcrypt, scrypt, etc.)
    // This is a simplified example
    return pin; // Replace with actual hashing
  }
}
```

## Provider Integration

### Creating a Service with Secure Storage

```dart
@riverpod
class BiometricAuthNotifier extends _$BiometricAuthNotifier {
  @override
  AsyncValue<bool> build() {
    return const AsyncValue.data(false);
  }
  
  Future<void> enableBiometric(String authKey) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final secureStorage = ref.read(secureStorageServiceProvider);
      await secureStorage.storeBiometricKey(authKey);
    });
  }
  
  Future<bool> isBiometricEnabled() async {
    try {
      final secureStorage = ref.read(secureStorageServiceProvider);
      final key = await secureStorage.getBiometricKey();
      return key != null;
    } catch (e) {
      return false;
    }
  }
}
```

## Security Best Practices

### 1. Key Naming Conventions
- Use descriptive, consistent naming
- Avoid exposing sensitive information in key names
- Use the service-specific methods when available

### 2. Data Handling
- Always hash sensitive data like PINs before storage
- Validate data before storing
- Clear sensitive data from memory after use

### 3. Error Handling
- Always handle `SecureStorageException`
- Log errors appropriately (without exposing sensitive data)
- Provide fallback mechanisms when possible

### 4. Testing
- Use mocks for testing (see `test/services/secure_storage_service_test.dart`)
- Test error scenarios
- Test data persistence and retrieval

## Platform-Specific Notes

### iOS
- Uses Keychain Services
- Data persists across app updates
- Requires device unlock for first access

### Android
- Uses EncryptedSharedPreferences
- Data may be lost on app updates (depending on device)
- Supports hardware-backed encryption on compatible devices

### Web
- Uses IndexedDB with encryption
- Limited to browser security model
- Data tied to browser/domain

### Desktop
- Platform-specific secure storage implementations
- May require additional setup for some distributions

## Migration Guide

If you need to migrate from SharedPreferences to secure storage:

```dart
class PreferenceMigrationService {
  final SecureStorageService _secureStorage;
  final SharedPreferences _prefs;
  
  PreferenceMigrationService(this._secureStorage, this._prefs);
  
  Future<void> migrateSensitiveData() async {
    // Migrate API keys
    final oldApiKey = _prefs.getString('api_key');
    if (oldApiKey != null) {
      await _secureStorage.storeApiKey('legacy_service', oldApiKey);
      await _prefs.remove('api_key');
    }
    
    // Migrate other sensitive data...
  }
}
```

## Testing

For testing, use the mock provided in the test file:

```dart
class MockSecureStorageService extends Mock implements SecureStorageService {}

void testMyFeature() {
  final mockSecureStorage = MockSecureStorageService();
  
  when(() => mockSecureStorage.read('test_key'))
      .thenAnswer((_) async => 'test_value');
  
  // Your test code...
}
```

## Troubleshooting

### Common Issues

1. **Platform differences**: Different platforms handle secure storage differently
2. **Data loss**: Some platforms may lose data during app updates
3. **Performance**: Secure operations are slower than regular storage
4. **Backup/Restore**: Secure data typically doesn't sync across devices

### Solutions

1. **Graceful degradation**: Handle cases where secure storage fails
2. **Backup strategies**: For non-sensitive derived data, consider secure cloud backup
3. **Caching**: Cache frequently accessed secure data in memory (temporarily)
4. **User communication**: Inform users about platform-specific behaviors

## Future Enhancements

The service is designed to be extensible for future needs:
- Additional convenience methods for specific data types
- Integration with biometric authentication libraries
- Support for additional encryption algorithms
- Cloud backup integration for non-sensitive metadata

---

*This service provides the foundation for secure data storage in BudApp. Always follow security best practices and test thoroughly on all target platforms.* 