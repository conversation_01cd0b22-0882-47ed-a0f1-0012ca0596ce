# AsyncValue Error Handling Guide

This document describes the comprehensive AsyncValue-based error handling system implemented in BudApp, providing robust error propagation and user-friendly error management across all operations.

## Overview

The AsyncValue error handling system provides:
- Consistent error handling patterns across all operations
- Automatic loading state management
- Type-safe error handling with proper error propagation
- User-friendly error messages with contextual information
- Centralized error management through providers

## Architecture

### AsyncNotifier Providers

All authentication and repository operations use AsyncNotifier providers with AsyncValue.guard() for robust error handling:

```dart
@riverpod
class LoginNotifier extends _$LoginNotifier {
  @override
  FutureOr<void> build() {}

  Future<void> signInWithEmail(String email, String password) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final authService = ref.read(authServiceProvider);
      await authService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    });
  }
}
```

### Error Handler Provider

Centralized error message management:

```dart
@riverpod
class AuthErrorHandler extends _$AuthErrorHandler {
  @override
  String? build() => null;

  String handleError(Object error, {String? context}) {
    final authError = AuthErrorService.handleFirebaseAuthException(
      error as FirebaseAuthException,
    );
    
    state = authError.userMessage;
    return authError.userMessage;
  }
}
```

## Implementation Patterns

### Authentication Operations

#### Login
- `LoginNotifier` - Email/password login with AsyncValue.guard()
- Automatic loading state management
- Error handling with user-friendly messages

#### Signup
- `SignupNotifier` - Account creation with enhanced error handling
- Integration with user profile creation
- Proper error propagation

#### Google Sign-In
- `GoogleSignInNotifier` - Google authentication with AsyncValue pattern
- Handles OAuth flow errors gracefully
- Consistent error messaging

#### Password Reset
- `PasswordResetNotifier` - Password reset email operations
- Email validation and error handling
- User feedback for success/failure states

#### Email Verification
- `EmailVerificationNotifier` - Email verification operations
- Periodic status checking with error handling
- Resend functionality with rate limiting

### Repository Operations

#### User Profile Management
- `UserProfileNotifier` - User profile CRUD operations with error handling
- Preference updates with AsyncValue.guard()
- Profile synchronization error management

#### Account Management
- `UserAccountsNotifier` - Account management with AsyncValue.guard()
- Create, update, delete operations with proper error handling
- Balance calculation error management

## UI Integration

### Loading States

AsyncValue automatically manages loading states:

```dart
Consumer(
  builder: (context, ref, child) {
    final loginState = ref.watch(loginNotifierProvider);
    
    return ElevatedButton(
      onPressed: loginState.isLoading ? null : () => _handleLogin(),
      child: loginState.isLoading 
        ? const CircularProgressIndicator()
        : const Text('Sign In'),
    );
  },
)
```

### Error Display

Dynamic error banners based on AsyncValue states:

```dart
Consumer(
  builder: (context, ref, child) {
    final loginState = ref.watch(loginNotifierProvider);
    
    return Column(
      children: [
        if (loginState.hasError)
          AuthErrorBanner(
            error: AuthErrorService.handleFirebaseAuthException(
              loginState.error as FirebaseAuthException,
            ),
            onRetry: () => _retryLogin(),
          ),
        // ... rest of UI
      ],
    );
  },
)
```

## Benefits

### Robust Error Propagation
- All operations use `AsyncValue.guard()` for consistent error handling
- Automatic error type preservation
- Proper error state management

### Improved User Experience
- Real-time loading indicators
- Clear error messages with contextual information
- Consistent error display patterns across the app
- Retry functionality for recoverable errors

### Developer Experience
- Simplified error handling patterns
- Reduced boilerplate code for loading states
- Type-safe error handling with AsyncValue
- Centralized error message management

### Architecture Consistency
- All authentication operations follow the same AsyncValue pattern
- Repository operations use consistent error handling
- Unified approach to state management and error handling

## Best Practices

### 1. Always Use AsyncValue.guard()
```dart
// ✅ Good
state = await AsyncValue.guard(() async {
  return await someAsyncOperation();
});

// ❌ Bad
try {
  state = AsyncValue.data(await someAsyncOperation());
} catch (e) {
  state = AsyncValue.error(e, StackTrace.current);
}
```

### 2. Handle Loading States in UI
```dart
// ✅ Good
ElevatedButton(
  onPressed: state.isLoading ? null : onPressed,
  child: state.isLoading 
    ? const CircularProgressIndicator()
    : const Text('Action'),
)
```

### 3. Provide User-Friendly Error Messages
```dart
// ✅ Good
if (state.hasError) {
  final authError = AuthErrorService.handleFirebaseAuthException(
    state.error as FirebaseAuthException,
  );
  return Text(authError.userMessage);
}
```

### 4. Use Centralized Error Handling
```dart
// ✅ Good
final errorHandler = ref.read(authErrorHandlerProvider.notifier);
final errorMessage = errorHandler.handleError(error, context: 'login');
```

## Testing

AsyncValue providers can be easily tested with provider overrides:

```dart
testWidgets('login shows error on failure', (tester) async {
  await tester.pumpWidget(
    ProviderScope(
      overrides: [
        loginNotifierProvider.overrideWith(() => MockLoginNotifier()),
      ],
      child: const LoginScreen(),
    ),
  );
  
  // Test error state handling
});
```

## Migration Guide

To migrate existing error handling to AsyncValue:

1. Convert StatefulWidget to ConsumerStatefulWidget
2. Replace manual loading state with AsyncNotifier
3. Use AsyncValue.guard() for async operations
4. Update UI to watch AsyncValue states
5. Implement error display with AsyncValue.hasError

This comprehensive error handling system provides a solid foundation for reliable error management throughout the application, ensuring users receive clear feedback and developers have consistent patterns to follow.
