# Development Workflow Guide

This document outlines the development workflow for BudApp with multi-environment support.

## Quick Start for Developers

### Daily Development
```bash
# Start developing - uses dev environment by default
flutter run

# Hot reload during development
r

# Hot restart if needed
R

# Quit when done
q
```

### Testing Different Environments
```bash
# Development (default)
flutter run

# Staging
flutter run --flavor staging -t lib/main_staging.dart

# Production
flutter run --flavor prod -t lib/main_prod.dart
```

## Environment Overview

| Environment | Command | Package Name | Firebase Project | Theme | Use Case |
|-------------|---------|--------------|------------------|-------|----------|
| **Development** | `flutter run` | `com.digitau.budapp.dev` | `budapp-dev` | Orange | Daily development |
| **Staging** | `flutter run --flavor staging -t lib/main_staging.dart` | `com.digitau.budapp.staging` | `budapp-staging-1` | Blue | Pre-production testing |
| **Production** | `flutter run --flavor prod -t lib/main_prod.dart` | `com.digitau.budapp` | `budapp-prod` | Green | Live application |

## Key Features

### Default Flavor Configuration
- **`default-flavor: dev`** in `pubspec.yaml` makes development seamless
- No need to specify `--flavor dev` for daily work
- `flutter run` automatically connects to development Firebase project
- Visual indicators clearly show which environment you're using

### Environment Isolation
- **Separate Firebase projects** prevent data mixing between environments
- **Distinct package names** allow installing multiple versions simultaneously
- **Color-coded themes** provide immediate visual feedback
- **Dynamic app names** help identify environment in device app list

### Firebase Integration
- Each environment connects to its own Firebase project
- Development: `budapp-dev`
- Staging: `budapp-staging-1`
- Production: `budapp-prod`

## Development Best Practices

### 1. Use Default Development Environment
```bash
# ✅ Recommended for daily development
flutter run

# ❌ Unnecessary for development
flutter run --flavor dev -t lib/main_dev.dart
```

### 2. Test in Staging Before Production
```bash
# Test features in staging environment
flutter run --flavor staging -t lib/main_staging.dart

# Only deploy to production after staging validation
flutter run --flavor prod -t lib/main_prod.dart
```

### 3. Visual Environment Verification
Always check the app's visual indicators:
- **Orange theme** = Development environment
- **Blue theme** = Staging environment  
- **Green theme** = Production environment

### 4. Firebase Project Verification
Check the Firebase project name displayed in the app:
- Development: "Firebase Project: budapp-dev"
- Staging: "Firebase Project: budapp-staging-1"
- Production: "Firebase Project: budapp-prod"

## Building for Release

### Development Build
```bash
flutter build apk --flavor dev -t lib/main_dev.dart
flutter build ios --flavor dev -t lib/main_dev.dart
```

### Staging Build
```bash
flutter build apk --flavor staging -t lib/main_staging.dart
flutter build ios --flavor staging -t lib/main_staging.dart
```

### Production Build
```bash
flutter build apk --flavor prod -t lib/main_prod.dart
flutter build ios --flavor prod -t lib/main_prod.dart
```

## Troubleshooting

### Common Issues

1. **Wrong Environment**: Check the theme color and Firebase project name in the app
2. **Build Errors**: Run `flutter clean` and `flutter pub get`
3. **Firebase Connection Issues**: Verify the correct config files are in place
4. **Package Conflicts**: Ensure different package names for each environment

### Verification Commands
```bash
# Check Flutter setup
flutter doctor

# Verify dependencies
flutter pub get

# Analyze code
flutter analyze

# Run tests
flutter test
```

## Next Steps

After mastering the development workflow:
1. Implement authentication flows
2. Set up Firestore data models
3. Configure Remote Config
4. Set up push notifications
5. Implement CI/CD pipeline

## Support

For development questions:
- Check the Memory Bank in `.taskmaster/memory-bank/`
- Review Task Master documentation in `.taskmaster/`
- Refer to environment setup guide in `docs/ENVIRONMENT_SETUP.md`
