# Account Data Schema

This document defines the comprehensive Firestore data model for user financial accounts in BudApp, including field specifications, validation rules, security considerations, and indexing strategy.

## Collection Structure

### Firestore Path
```
/users/{userId}/accounts/{accountId}
```

**Rationale**: Accounts are stored as a subcollection under each user document to ensure:
- **Data Isolation**: Users can only access their own accounts
- **Security**: Firestore Security Rules can easily enforce user ownership
- **Performance**: Queries are automatically scoped to the user
- **Scalability**: Each user's accounts are isolated, preventing collection size limits

## Account Document Schema

### Core Fields

| Field | Type | Required | Description | Validation |
|-------|------|----------|-------------|------------|
| `id` | string | ✅ | Unique account identifier | Must match document ID |
| `userId` | string | ✅ | Owner's Firebase Auth UID | Must match parent collection userId |
| `name` | string | ✅ | Account display name | 1-100 characters |
| `type` | string | ✅ | Account type enum | See AccountType enum below |
| `classification` | string | ✅ | Asset or liability classification | `asset` or `liability` |
| `currencyCode` | string | ✅ | ISO 4217 currency code | 3-letter uppercase (e.g., "USD") |
| `createdAt` | timestamp | ✅ | Account creation timestamp | Firestore timestamp |
| `updatedAt` | timestamp | ✅ | Last modification timestamp | Firestore timestamp |

### Financial Fields

| Field | Type | Required | Description | Validation |
|-------|------|----------|-------------|------------|
| `initialBalanceCents` | integer | ✅ | Starting balance in cents (immutable) | Any integer (can be negative) |
| `currentBalanceCents` | integer | ✅ | Real-time balance in cents (mutable) | Any integer (can be negative) |

**Dual Balance Architecture**: Accounts maintain both initial and current balances:
- `initialBalanceCents`: Starting balance when account was created (never changes)
- `currentBalanceCents`: Real-time balance updated with each transaction (changes frequently)

**UI Display Pattern**: All user-facing components display `currentBalanceCents` to show accurate real-time financial status. The `initialBalanceCents` is only used for historical reference and account editing.

**Rationale for Cents**: Using integer cents instead of decimal amounts prevents floating-point precision issues common in financial calculations.

### Optional Fields

| Field | Type | Required | Default | Description | Validation |
|-------|------|----------|---------|-------------|------------|
| `description` | string | ❌ | null | Account description/notes | 0-500 characters |
| `iconName` | string | ❌ | null | Icon identifier for UI | 1-50 characters |
| `colorHex` | string | ❌ | null | Color code for UI display | Valid hex color format |
| `isPrimary` | boolean | ❌ | false | Primary account flag | Only one per user recommended |
| `isActive` | boolean | ❌ | true | Account active status | Soft delete mechanism |
| `metadata` | map | ❌ | {} | Extensible key-value storage | Max 10 keys |

## Data Types and Enums

### AccountType Enum
```dart
enum AccountType {
  checking,     // Standard checking account
  savings,      // Savings account
  creditCard,   // Credit card account
  cash,         // Cash/wallet
  investment,   // Investment account
  loan          // Loan account
}
```

### AccountClassification Enum
```dart
enum AccountClassification {
  asset,        // Positive balances increase net worth (checking, savings, cash, investment)
  liability     // Positive balances decrease net worth (credit cards, loans)
}
```

### Classification Mapping
| AccountType | Classification | Rationale |
|-------------|----------------|-----------|
| checking | asset | Positive balance increases net worth |
| savings | asset | Positive balance increases net worth |
| cash | asset | Physical cash is an asset |
| investment | asset | Investment value is an asset |
| creditCard | liability | Credit card debt is a liability |
| loan | liability | Loan balance is a liability |

## Firestore Security Rules

### Current Validation Issues
The current Firestore Security Rules have inconsistencies with the Dart model:

**Issues Identified:**
1. Security rules use different enum values than Dart model
2. Rules validate `type` as `asset`/`liability` but should validate `classification`
3. Rules validate `classification` with account type values

### Corrected Security Rules
```javascript
function isValidAccount(data, userId, accountId) {
  return hasRequiredFields(data, [
    'id', 'userId', 'name', 'type', 'classification', 
    'currencyCode', 'initialBalanceCents', 'createdAt', 'updatedAt'
  ]) &&
  data.id == accountId &&
  data.userId == userId &&
  isValidStringLength(data.name, 1, 100) &&
  isValidEnum(data.type, [
    'checking', 'savings', 'creditCard', 'cash', 'investment', 'loan'
  ]) &&
  isValidEnum(data.classification, ['asset', 'liability']) &&
  isValidCurrencyCode(data.currencyCode) &&
  data.initialBalanceCents is int &&
  isValidTimestamp(data.createdAt) &&
  isValidTimestamp(data.updatedAt) &&
  isValidOptionalString(data, 'description', 0, 500) &&
  isValidOptionalString(data, 'iconName', 1, 50) &&
  isValidOptionalString(data, 'colorHex', 6, 7) &&
  isValidOptionalBoolean(data, 'isPrimary') &&
  isValidOptionalBoolean(data, 'isActive') &&
  isValidOptionalMap(data, 'metadata') &&
  isValidTypeClassificationPair(data.type, data.classification);
}

function isValidTypeClassificationPair(type, classification) {
  return (
    (type in ['checking', 'savings', 'cash', 'investment'] && classification == 'asset') ||
    (type in ['creditCard', 'loan'] && classification == 'liability')
  );
}
```

## Indexing Strategy

### Composite Indexes Needed

1. **User Account Listing** (Automatic via subcollection)
   ```
   Collection: /users/{userId}/accounts
   Fields: userId (ascending), isActive (ascending), name (ascending)
   ```

2. **Account Type Filtering**
   ```
   Collection: /users/{userId}/accounts  
   Fields: userId (ascending), type (ascending), isActive (ascending)
   ```

3. **Primary Account Query**
   ```
   Collection: /users/{userId}/accounts
   Fields: userId (ascending), isPrimary (ascending), isActive (ascending)
   ```

### Single Field Indexes
- `createdAt` - For chronological ordering
- `updatedAt` - For change tracking
- `currencyCode` - For currency-based filtering

## Data Access Patterns

### Common Queries

1. **List All Active Accounts**
   ```dart
   FirebaseFirestore.instance
     .collection('users/$userId/accounts')
     .where('isActive', isEqualTo: true)
     .orderBy('name')
   ```

2. **Get Accounts by Type**
   ```dart
   FirebaseFirestore.instance
     .collection('users/$userId/accounts')
     .where('type', isEqualTo: AccountType.checking.name)
     .where('isActive', isEqualTo: true)
   ```

3. **Find Primary Account**
   ```dart
   FirebaseFirestore.instance
     .collection('users/$userId/accounts')
     .where('isPrimary', isEqualTo: true)
     .where('isActive', isEqualTo: true)
     .limit(1)
   ```

## Business Rules

### Account Creation Rules
1. **Unique Names**: Account names should be unique per user (enforced at app level)
2. **Primary Account**: Only one account can be marked as primary per user
3. **Currency Consistency**: All accounts should use user's default currency unless specifically needed
4. **Initial Balance**: Can be positive, negative, or zero depending on account type

### Account Deletion Rules
1. **Soft Delete**: Set `isActive = false` instead of document deletion
2. **Referential Integrity**: Cannot delete accounts with associated transactions
3. **Primary Account**: Cannot delete the primary account if it's the only active account

### Update Rules
1. **Immutable Fields**: `id`, `userId`, `createdAt` cannot be changed
2. **Balance Updates**: `initialBalanceCents` should only be updated for corrections, not for transactions
3. **Timestamp**: `updatedAt` must be updated on every modification

## Security Considerations

### Data Isolation
- All accounts are stored under user-specific subcollections
- Firestore Security Rules enforce user ownership
- No cross-user account access possible

### Sensitive Data
- Financial amounts stored as integers to prevent precision loss
- No sensitive authentication data stored in account documents
- Metadata field should not contain PII or sensitive information

### Audit Trail
- `createdAt` and `updatedAt` provide change tracking
- Soft deletes via `isActive` preserve historical data
- Consider adding `lastModifiedBy` for shared account scenarios (future)

## Migration Considerations

### Version 1.0 Schema
The current schema is designed for MVP launch with these considerations:

1. **Extensibility**: `metadata` field allows future feature additions
2. **Simplicity**: Minimal required fields for quick implementation
3. **Compliance**: Follows accounting principles for asset/liability classification

### Future Schema Enhancements
Potential future additions:
- `accountNumber` (encrypted) for bank integration
- `institutionId` for Plaid/bank API integration
- `lastSyncAt` for external account synchronization
- `balanceHistory` subcollection for balance tracking
- `sharedWith` for multi-user access (family accounts)

## Data Validation Examples

### Valid Account Document
```json
{
  "id": "acc_01HXX1234567890",
  "userId": "firebase_auth_uid_123",
  "name": "Chase Checking",
  "type": "checking",
  "classification": "asset",
  "description": "Primary checking account",
  "initialBalanceCents": 150000,
  "currencyCode": "USD",
  "iconName": "bank",
  "colorHex": "#1E40AF",
  "isPrimary": true,
  "isActive": true,
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z",
  "metadata": {
    "importSource": "manual",
    "lastReviewDate": "2024-01-15"
  }
}
```

### Invalid Examples

**Missing Required Field:**
```json
{
  "id": "acc_01HXX1234567890",
  "userId": "firebase_auth_uid_123",
  "name": "Chase Checking"
  // Missing: type, classification, currencyCode, etc.
}
```

**Invalid Type/Classification Pair:**
```json
{
  "type": "creditCard",
  "classification": "asset"  // Should be "liability"
}
```

**Invalid Currency Code:**
```json
{
  "currencyCode": "dollars"  // Should be "USD"
}
```

## Implementation Checklist

### Dart Model Updates
- ✅ Current Account model is correctly implemented
- ✅ Freezed integration working properly
- ✅ JSON serialization functional

### Firestore Security Rules Updates
- ❌ Fix enum validation inconsistencies
- ❌ Add type/classification pair validation
- ❌ Update validation function to match Dart model

### Repository Implementation
- ✅ AccountRepository interface exists
- ✅ AccountRepositoryImpl with basic CRUD operations
- ❌ Add business rule enforcement (unique names, primary account)

### Testing
- ✅ Unit tests for Account model
- ❌ Integration tests for Firestore operations
- ❌ Security rules validation tests

### Documentation
- ✅ This schema documentation
- ❌ API documentation updates
- ❌ Developer guide updates

## Conclusion

The Account data schema provides a solid foundation for BudApp's financial account management with proper security isolation, flexible categorization, and extensibility for future features. The schema balances simplicity for MVP development with the flexibility needed for advanced financial management features.

Key strengths:
- **Security**: User data isolation through subcollections
- **Performance**: Optimized for common query patterns
- **Flexibility**: Extensible metadata and optional fields
- **Reliability**: Integer-based financial calculations
- **Compliance**: Proper accounting classification system

The identified inconsistencies between Dart models and Firestore Security Rules need to be addressed to ensure data integrity and security.