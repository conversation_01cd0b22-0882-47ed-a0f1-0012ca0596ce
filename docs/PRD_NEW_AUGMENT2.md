# BudApp Product Requirements Document (v2.0)

## Document Information
- **Version**: 2.0 (Reconstruction Edition)
- **Date**: January 2025
- **Status**: Draft
- **Author**: Development Team
- **Based on**: Lessons learned from BudApp v1.0 implementation

## Executive Summary

BudApp v2.0 is a comprehensive personal finance management application designed for tech-savvy individuals aged 22-35 who are transitioning from manual tracking methods to structured budgeting tools. This version incorporates critical lessons learned from the v1.0 implementation, focusing on improved architecture, proactive dependency management, and enhanced offline-first capabilities.

### Key Improvements from v1.0
- **Proactive Architecture**: Built with industry best practices from day one
- **Offline-First Design**: True offline functionality with automatic synchronization
- **Robust Testing**: TDD approach with comprehensive test coverage
- **Dependency Management**: Proactive update strategy to prevent compatibility crises
- **Performance Optimization**: Improved startup times and responsiveness

## Product Vision

**Vision Statement**: Empower young professionals to take control of their financial future through intelligent, offline-first budgeting tools that adapt to their lifestyle.

**Mission**: Provide a seamless, intuitive financial management experience that works anywhere, anytime, with or without internet connectivity.

## Target Audience

### Primary Users
- **Age**: 22-35 years old
- **Tech Proficiency**: High comfort with mobile apps and digital tools
- **Financial Stage**: Early career to established professionals
- **Current Behavior**: Manual tracking (spreadsheets, notes) or basic apps
- **Pain Points**: Inconsistent tracking, lack of insights, online-only limitations

### User Personas

#### Sarah, 28, Marketing Manager
- **Income**: $65,000/year
- **Goals**: Save for house down payment, reduce dining out expenses
- **Challenges**: Irregular income from freelance work, forgets to track expenses
- **Needs**: Automatic categorization, offline access, goal tracking

#### Mike, 31, Software Engineer  
- **Income**: $95,000/year
- **Goals**: Maximize investments, track multiple income streams
- **Challenges**: Complex financial situation, wants detailed analytics
- **Needs**: Advanced reporting, API integrations, data export

## Core Value Propositions

1. **True Offline-First Experience**: Full functionality without internet connection
2. **Intelligent Automation**: Smart categorization and transaction detection
3. **Goal-Oriented Budgeting**: Focus on achieving financial objectives
4. **Privacy-First Design**: Local data storage with optional cloud sync
5. **Seamless Multi-Device**: Consistent experience across all devices

## Technical Architecture (v2.0 Improvements)

### Core Technology Stack
```yaml
Framework: Flutter (Latest Stable)
Backend: Firebase (Firestore, Auth, Remote Config, FCM)
State Management: Riverpod with AsyncNotifier pattern
Data Models: Freezed with proper JSON serialization
Local Storage: SQLite with automatic cloud sync
Testing: Comprehensive TDD approach
```

### Architecture Principles

#### 1. Offline-First from Foundation
- **Local Database**: SQLite as primary data store
- **Cloud Sync**: Automatic background synchronization
- **Conflict Resolution**: Last-write-wins with user override options
- **Data Persistence**: Automatic caching of all user data

#### 2. Reactive State Management
```dart
// Example: Transaction management with offline persistence
@riverpod
class TransactionNotifier extends _$TransactionNotifier {
  @override
  FutureOr<List<Transaction>> build() async {
    // Enable automatic offline persistence
    await persist(
      ref.watch(storageProvider.future),
      key: 'transactions',
      options: const StorageOptions(cacheTime: StorageCacheTime.days(30)),
    );
    
    // Stream-based data: local first, then remote updates
    return _loadTransactions();
  }
}
```

#### 3. Repository Pattern with Dependency Injection
- **Abstract Interfaces**: All data access through repository interfaces
- **Concrete Implementations**: Firebase and local database implementations
- **Dependency Injection**: Riverpod-based DI for testability
- **Error Handling**: Comprehensive error handling and recovery

#### 4. Proactive Dependency Management
- **Version Pinning**: Specific versions to prevent compatibility issues
- **Regular Updates**: Weekly dependency audits and updates
- **Migration Planning**: Advance planning for major version updates
- **Testing Strategy**: Comprehensive testing before dependency updates

## Feature Requirements

### Phase 1: Core MVP (8 weeks)

#### 1.1 Account Management
**User Stories**:
- As a user, I can create and manage multiple financial accounts (checking, savings, credit cards)
- As a user, I can set account balances and track changes over time
- As a user, I can categorize accounts by type and institution

**Acceptance Criteria**:
- Support for 10+ account types with custom icons
- Real-time balance calculations
- Account history and transaction tracking
- Offline account creation and editing

**Technical Implementation**:
```dart
@freezed
class Account with _$Account {
  const factory Account({
    required String id,
    required String name,
    required AccountType type,
    required int balanceCents,
    required String currency,
    String? institutionName,
    Color? color,
    IconData? icon,
    required DateTime createdAt,
    DateTime? updatedAt,
    @Default(false) bool synchronized,
  }) = _Account;
}
```

#### 1.2 Transaction Management
**User Stories**:
- As a user, I can add, edit, and delete transactions
- As a user, I can categorize transactions automatically and manually
- As a user, I can search and filter transactions by various criteria
- As a user, I can transfer money between accounts

**Acceptance Criteria**:
- Support for income, expense, and transfer transaction types
- Automatic categorization with manual override
- Advanced search and filtering capabilities
- Bulk transaction operations
- Complete offline functionality

**Technical Implementation**:
```dart
@freezed
class Transaction with _$Transaction {
  const factory Transaction({
    required String id,
    required int amountCents,
    required String currency,
    required String description,
    required String categoryId,
    required String accountId,
    required TransactionType type,
    required DateTime transactionDate,
    required DateTime createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
    @Default(false) bool synchronized,
  }) = _Transaction;
}
```

#### 1.3 Budget Management
**User Stories**:
- As a user, I can create monthly budgets for different categories
- As a user, I can track budget progress in real-time
- As a user, I can receive notifications when approaching budget limits
- As a user, I can view budget performance over time

**Acceptance Criteria**:
- Monthly budget periods with rollover options
- Real-time budget tracking and alerts
- Visual progress indicators and charts
- Budget vs. actual reporting
- Offline budget management

#### 1.4 Goal Tracking
**User Stories**:
- As a user, I can set financial goals with target amounts and dates
- As a user, I can track progress toward my goals
- As a user, I can allocate funds to specific goals
- As a user, I can celebrate goal achievements

**Acceptance Criteria**:
- Multiple goal types (savings, debt payoff, purchase)
- Progress tracking with visual indicators
- Goal prioritization and recommendations
- Achievement celebrations and milestones

### Phase 2: Enhanced Features (4 weeks)

#### 2.1 Advanced Analytics
- Spending trends and patterns analysis
- Income vs. expense tracking
- Category-wise spending insights
- Monthly and yearly financial reports

#### 2.2 Data Import/Export
- CSV import for transactions
- Bank statement parsing (where possible)
- Data export in multiple formats
- Backup and restore functionality

#### 2.3 Customization
- Custom categories and subcategories
- Personalized dashboard layouts
- Theme customization
- Currency and locale support

### Phase 3: Premium Features (4 weeks)

#### 3.1 Advanced Budgeting
- Zero-based budgeting methodology
- Envelope budgeting system
- Budget templates and recommendations
- Multi-month budget planning

#### 3.2 Investment Tracking
- Investment account support
- Portfolio performance tracking
- Asset allocation insights
- Investment goal integration

#### 3.3 Bill Management
- Recurring bill tracking
- Payment reminders
- Bill categorization
- Payment history

## User Experience Requirements

### Design Principles
1. **Simplicity First**: Clean, uncluttered interface
2. **Offline Indication**: Clear status of sync and offline capabilities
3. **Progressive Disclosure**: Advanced features available but not overwhelming
4. **Consistent Patterns**: Familiar UI patterns throughout the app
5. **Accessibility**: WCAG AA compliance for all users

### Key User Flows

#### 1. First-Time User Onboarding
1. Welcome screen with value proposition
2. Account creation (email/Google sign-in)
3. Initial account setup (1-3 accounts)
4. Sample transaction creation
5. Budget setup wizard
6. Goal setting (optional)

#### 2. Daily Transaction Entry
1. Quick add transaction (FAB)
2. Smart category suggestion
3. Account selection
4. Amount and description entry
5. Immediate local save
6. Background sync when online

#### 3. Budget Review
1. Dashboard overview
2. Category drill-down
3. Transaction list for category
4. Budget adjustment if needed
5. Goal progress check

## Technical Requirements

### Performance Requirements
- **App Startup**: <2 seconds cold start on mid-range devices
- **Transaction Entry**: <500ms from tap to save
- **Data Sync**: Background sync without UI blocking
- **Memory Usage**: <100MB RAM usage during normal operation
- **Battery Impact**: Minimal battery drain from background operations

### Security Requirements
- **Data Encryption**: AES-256 encryption for local data
- **Network Security**: TLS 1.3 for all network communications
- **Authentication**: Multi-factor authentication support
- **Privacy**: No PII in analytics or crash reports
- **Compliance**: GDPR and CCPA compliance

### Platform Requirements
- **iOS**: iOS 14.0+ (iPhone and iPad)
- **Android**: Android 8.0+ (API level 26+)
- **Offline Storage**: 500MB local storage capacity
- **Network**: Graceful handling of poor connectivity

## Quality Assurance

### Testing Strategy
- **Unit Tests**: >90% coverage for business logic
- **Widget Tests**: All UI components tested
- **Integration Tests**: End-to-end user flows
- **Performance Tests**: Load testing with large datasets
- **Accessibility Tests**: Screen reader and keyboard navigation

### Quality Metrics
- **Crash Rate**: <0.1% of sessions
- **ANR Rate**: <0.05% of sessions (Android)
- **App Store Rating**: >4.5 stars
- **User Retention**: >70% after 30 days

## Monetization Strategy

### Freemium Model
**Free Tier**:
- Up to 3 accounts
- Basic transaction management
- Simple budgeting
- 1 financial goal
- Local data storage

**Premium Tier** ($4.99/month or $49.99/year):
- Unlimited accounts
- Advanced analytics and reporting
- Multiple goals and advanced goal types
- Cloud sync across devices
- Data export capabilities
- Priority customer support
- Investment tracking
- Bill management

### Revenue Projections
- **Year 1**: 10,000 active users, 5% conversion rate → $30,000 ARR
- **Year 2**: 50,000 active users, 8% conversion rate → $240,000 ARR
- **Year 3**: 100,000 active users, 10% conversion rate → $600,000 ARR

## Success Metrics

### User Engagement
- **Daily Active Users**: Target 30% of monthly users
- **Session Duration**: Average 5+ minutes per session
- **Feature Adoption**: 80% of users use budgeting features
- **Goal Completion**: 60% of users complete at least one goal

### Technical Metrics
- **App Performance**: 95th percentile load times <3 seconds
- **Sync Success Rate**: >99.5% successful syncs
- **Offline Usage**: 40% of sessions include offline usage
- **Data Accuracy**: <0.1% data inconsistency reports

### Business Metrics
- **Customer Acquisition Cost**: <$10 per user
- **Lifetime Value**: >$50 per premium user
- **Churn Rate**: <5% monthly for premium users
- **Net Promoter Score**: >50

## Risk Assessment and Mitigation

### Technical Risks
1. **Dependency Conflicts**: Proactive management and testing
2. **Data Synchronization Issues**: Comprehensive conflict resolution
3. **Performance Degradation**: Continuous monitoring and optimization
4. **Security Vulnerabilities**: Regular security audits

### Business Risks
1. **Market Competition**: Focus on unique offline-first value proposition
2. **User Acquisition**: Invest in content marketing and referral programs
3. **Monetization Challenges**: A/B test pricing and feature combinations
4. **Regulatory Changes**: Stay informed on financial app regulations

## Implementation Timeline

### Phase 1: Foundation (Weeks 1-8)
- Project setup and architecture
- Core data models and repositories
- Basic UI framework
- Account and transaction management
- Offline-first implementation
- Basic budgeting features

### Phase 2: Enhancement (Weeks 9-12)
- Advanced analytics
- Goal tracking
- Data import/export
- UI polish and optimization
- Comprehensive testing

### Phase 3: Premium Features (Weeks 13-16)
- Investment tracking
- Bill management
- Advanced budgeting methods
- Premium feature implementation
- App store preparation and launch

### Phase 4: Post-Launch (Weeks 17-20)
- User feedback integration
- Performance optimization
- Feature refinements
- Marketing and user acquisition

## Development Guidelines

### Code Organization
```
lib/
├── features/              # Feature-based organization
│   ├── auth/             # Authentication module
│   │   ├── data/         # Repositories and data sources
│   │   ├── domain/       # Business logic and entities
│   │   └── presentation/ # UI components and providers
│   ├── accounts/         # Account management
│   ├── transactions/     # Transaction management
│   ├── budgets/          # Budget tracking
│   └── goals/           # Goal management
├── shared/               # Shared components
│   ├── data/            # Common repositories and services
│   ├── domain/          # Shared business logic
│   ├── presentation/    # Reusable UI components
│   └── utils/           # Utility functions
├── core/                # Core application setup
│   ├── config/          # App configuration
│   ├── constants/       # App constants
│   ├── errors/          # Error handling
│   └── network/         # Network configuration
└── main.dart           # Application entry point
```

### Data Model Standards
```dart
// All data models must follow this pattern
@freezed
class Transaction with _$Transaction {
  const factory Transaction({
    required String id,
    required int amountCents,        // Always store as cents
    required String currency,        // ISO 4217 currency codes
    required String description,
    required String categoryId,
    required String accountId,
    required TransactionType type,
    required DateTime transactionDate,
    required DateTime createdAt,
    DateTime? updatedAt,
    @Default({}) Map<String, dynamic> metadata,
    @Default(false) bool synchronized,
    @Default(false) bool deleted,    // Soft delete flag
  }) = _Transaction;

  factory Transaction.fromJson(Map<String, dynamic> json) =>
      _$TransactionFromJson(json);

  // Business logic methods
  const Transaction._();

  bool get isIncome => type == TransactionType.income;
  bool get isExpense => type == TransactionType.expense;
  bool get isTransfer => type == TransactionType.transfer;

  double get amount => amountCents / 100.0;
}
```

### Repository Pattern Implementation
```dart
// Abstract repository interface
abstract class ITransactionRepository {
  Stream<List<Transaction>> watchTransactions({
    String? accountId,
    List<String>? categoryIds,
    DateTimeRange? dateRange,
    TransactionType? type,
  });

  Future<Transaction?> getTransaction(String id);
  Future<Transaction> createTransaction(Transaction transaction);
  Future<Transaction> updateTransaction(Transaction transaction);
  Future<void> deleteTransaction(String id);
  Future<List<Transaction>> searchTransactions(String query);
  Future<void> syncPendingTransactions();
}

// Concrete implementation with offline-first logic
class TransactionRepository implements ITransactionRepository {
  final FirestoreService _firestoreService;
  final LocalDatabaseService _localDatabaseService;
  final ConnectivityService _connectivityService;

  TransactionRepository({
    required FirestoreService firestoreService,
    required LocalDatabaseService localDatabaseService,
    required ConnectivityService connectivityService,
  }) : _firestoreService = firestoreService,
       _localDatabaseService = localDatabaseService,
       _connectivityService = connectivityService;

  @override
  Stream<List<Transaction>> watchTransactions({
    String? accountId,
    List<String>? categoryIds,
    DateTimeRange? dateRange,
    TransactionType? type,
  }) async* {
    // Always emit local data first
    final localTransactions = await _localDatabaseService.getTransactions(
      accountId: accountId,
      categoryIds: categoryIds,
      dateRange: dateRange,
      type: type,
    );

    yield localTransactions;

    // Then try to fetch fresh data if online
    if (await _connectivityService.isConnected) {
      try {
        final remoteTransactions = await _firestoreService.getTransactions(
          accountId: accountId,
          categoryIds: categoryIds,
          dateRange: dateRange,
          type: type,
        );

        // Update local cache
        await _localDatabaseService.saveTransactions(remoteTransactions);

        // Emit fresh data
        yield remoteTransactions;
      } catch (e) {
        // Log error but don't throw - local data already emitted
        _logError('Failed to fetch remote transactions', e);
      }
    }
  }
}
```

### State Management with Riverpod
```dart
// Provider for transaction management
@riverpod
class TransactionNotifier extends _$TransactionNotifier {
  @override
  FutureOr<List<Transaction>> build() async {
    final repository = ref.watch(transactionRepositoryProvider);

    // Enable automatic offline persistence
    await persist(
      ref.watch(storageProvider.future),
      key: 'transactions',
      options: const StorageOptions(
        cacheTime: StorageCacheTime.days(30),
        syncAcrossInstances: true,
      ),
    );

    // Return initial data from repository
    return repository.watchTransactions().first;
  }

  Future<void> addTransaction(Transaction transaction) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final repository = ref.read(transactionRepositoryProvider);

      // Create transaction with optimistic update
      final newTransaction = await repository.createTransaction(transaction);

      // Update local state
      final currentTransactions = await future;
      final updatedTransactions = [...currentTransactions, newTransaction];

      // Trigger balance recalculation
      ref.invalidate(accountBalanceProvider(transaction.accountId));

      return updatedTransactions;
    });
  }

  Future<void> updateTransaction(Transaction transaction) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final repository = ref.read(transactionRepositoryProvider);

      // Update transaction
      final updatedTransaction = await repository.updateTransaction(transaction);

      // Update local state
      final currentTransactions = await future;
      final updatedTransactions = currentTransactions
          .map((t) => t.id == transaction.id ? updatedTransaction : t)
          .toList();

      // Trigger balance recalculation
      ref.invalidate(accountBalanceProvider(transaction.accountId));

      return updatedTransactions;
    });
  }

  Future<void> deleteTransaction(String id) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final repository = ref.read(transactionRepositoryProvider);

      // Find transaction to get account ID for balance update
      final currentTransactions = await future;
      final transactionToDelete = currentTransactions.firstWhere((t) => t.id == id);

      // Delete transaction
      await repository.deleteTransaction(id);

      // Update local state
      final updatedTransactions = currentTransactions.where((t) => t.id != id).toList();

      // Trigger balance recalculation
      ref.invalidate(accountBalanceProvider(transactionToDelete.accountId));

      return updatedTransactions;
    });
  }
}

// Provider for filtered transactions
@riverpod
Future<List<Transaction>> filteredTransactions(
  FilteredTransactionsRef ref, {
  String? accountId,
  List<String>? categoryIds,
  DateTimeRange? dateRange,
  TransactionType? type,
}) async {
  final repository = ref.watch(transactionRepositoryProvider);
  return repository.watchTransactions(
    accountId: accountId,
    categoryIds: categoryIds,
    dateRange: dateRange,
    type: type,
  ).first;
}
```

### Error Handling Strategy
```dart
// Centralized error handling
class AppError {
  final String message;
  final String? code;
  final dynamic originalError;
  final StackTrace? stackTrace;

  const AppError({
    required this.message,
    this.code,
    this.originalError,
    this.stackTrace,
  });

  factory AppError.network({String? message}) => AppError(
    message: message ?? 'Network connection failed',
    code: 'NETWORK_ERROR',
  );

  factory AppError.validation({required String message}) => AppError(
    message: message,
    code: 'VALIDATION_ERROR',
  );

  factory AppError.unknown({required dynamic error, StackTrace? stackTrace}) => AppError(
    message: 'An unexpected error occurred',
    code: 'UNKNOWN_ERROR',
    originalError: error,
    stackTrace: stackTrace,
  );
}

// Error handling in repositories
class TransactionRepository implements ITransactionRepository {
  @override
  Future<Transaction> createTransaction(Transaction transaction) async {
    try {
      // Validate transaction
      _validateTransaction(transaction);

      // Attempt to save locally first
      final localTransaction = await _localDatabaseService.saveTransaction(transaction);

      // Then sync to remote if online
      if (await _connectivityService.isConnected) {
        try {
          final remoteTransaction = await _firestoreService.createTransaction(transaction);
          // Update local with server-generated fields
          return await _localDatabaseService.updateTransaction(remoteTransaction);
        } catch (e) {
          // Mark as pending sync
          return await _localDatabaseService.markPendingSync(localTransaction);
        }
      }

      return localTransaction;
    } on ValidationException catch (e) {
      throw AppError.validation(message: e.message);
    } on NetworkException catch (e) {
      throw AppError.network(message: e.message);
    } catch (e, stackTrace) {
      throw AppError.unknown(error: e, stackTrace: stackTrace);
    }
  }

  void _validateTransaction(Transaction transaction) {
    if (transaction.amountCents <= 0) {
      throw ValidationException('Transaction amount must be greater than zero');
    }

    if (transaction.description.trim().isEmpty) {
      throw ValidationException('Transaction description is required');
    }

    // Additional validation rules...
  }
}
```

### Testing Strategy
```dart
// Test utilities
class TestHelpers {
  static ProviderContainer createTestContainer({
    List<Override> overrides = const [],
  }) {
    final container = ProviderContainer(
      overrides: [
        // Mock Firebase services
        firebaseAuthProvider.overrideWithValue(MockFirebaseAuth()),
        firestoreProvider.overrideWithValue(FakeFirebaseFirestore()),

        // Mock local services
        localDatabaseServiceProvider.overrideWithValue(MockLocalDatabaseService()),
        connectivityServiceProvider.overrideWithValue(MockConnectivityService()),

        ...overrides,
      ],
    );

    addTearDown(container.dispose);
    return container;
  }

  static Transaction createTestTransaction({
    String? id,
    int? amountCents,
    String? description,
    TransactionType? type,
  }) {
    return Transaction(
      id: id ?? 'test-transaction-${DateTime.now().millisecondsSinceEpoch}',
      amountCents: amountCents ?? 1000, // $10.00
      currency: 'USD',
      description: description ?? 'Test transaction',
      categoryId: 'test-category',
      accountId: 'test-account',
      type: type ?? TransactionType.expense,
      transactionDate: DateTime.now(),
      createdAt: DateTime.now(),
    );
  }

  static Account createTestAccount({
    String? id,
    String? name,
    AccountType? type,
    int? balanceCents,
  }) {
    return Account(
      id: id ?? 'test-account-${DateTime.now().millisecondsSinceEpoch}',
      name: name ?? 'Test Account',
      type: type ?? AccountType.checking,
      balanceCents: balanceCents ?? 100000, // $1,000.00
      currency: 'USD',
      createdAt: DateTime.now(),
    );
  }
}

// Integration test example
void main() {
  group('Transaction Management Integration Tests', () {
    late ProviderContainer container;
    late MockLocalDatabaseService mockLocalDb;
    late MockFirestoreService mockFirestore;

    setUp(() {
      mockLocalDb = MockLocalDatabaseService();
      mockFirestore = MockFirestoreService();

      container = TestHelpers.createTestContainer(
        overrides: [
          localDatabaseServiceProvider.overrideWithValue(mockLocalDb),
          firestoreServiceProvider.overrideWithValue(mockFirestore),
        ],
      );
    });

    testWidgets('should create transaction and update balance', (tester) async {
      // Arrange
      final account = TestHelpers.createTestAccount(balanceCents: 100000);
      final transaction = TestHelpers.createTestTransaction(
        accountId: account.id,
        amountCents: 5000, // $50.00 expense
        type: TransactionType.expense,
      );

      when(() => mockLocalDb.saveTransaction(any()))
          .thenAnswer((_) async => transaction);
      when(() => mockFirestore.createTransaction(any()))
          .thenAnswer((_) async => transaction);

      // Act
      await container.read(transactionNotifierProvider.notifier)
          .addTransaction(transaction);

      // Assert
      final transactions = await container.read(transactionNotifierProvider.future);
      expect(transactions, contains(transaction));

      // Verify balance was updated
      final updatedBalance = await container.read(
        accountBalanceProvider(account.id).future,
      );
      expect(updatedBalance, equals(95000)); // $1000 - $50 = $950
    });
  });
}
```

## Deployment and DevOps

### CI/CD Pipeline
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'

      - name: Install dependencies
        run: flutter pub get

      - name: Run code generation
        run: dart run build_runner build --delete-conflicting-outputs

      - name: Run tests
        run: flutter test --coverage

      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: coverage/lcov.info

  analyze:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2

      - name: Install dependencies
        run: flutter pub get

      - name: Run analysis
        run: flutter analyze

      - name: Check formatting
        run: dart format --set-exit-if-changed .

  build:
    needs: [test, analyze]
    runs-on: ubuntu-latest
    strategy:
      matrix:
        platform: [android, ios]
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2

      - name: Build ${{ matrix.platform }}
        run: |
          if [ "${{ matrix.platform }}" == "android" ]; then
            flutter build apk --release
          else
            flutter build ios --release --no-codesign
          fi
```

### Environment Configuration
```dart
// lib/core/config/app_config.dart
enum Environment { dev, staging, prod }

class AppConfig {
  static Environment get environment {
    const env = String.fromEnvironment('ENVIRONMENT', defaultValue: 'dev');
    switch (env) {
      case 'staging':
        return Environment.staging;
      case 'prod':
        return Environment.prod;
      default:
        return Environment.dev;
    }
  }

  static String get apiBaseUrl {
    switch (environment) {
      case Environment.dev:
        return 'https://api-dev.budapp.com';
      case Environment.staging:
        return 'https://api-staging.budapp.com';
      case Environment.prod:
        return 'https://api.budapp.com';
    }
  }

  static bool get enableLogging => environment != Environment.prod;
  static bool get enableCrashlytics => environment == Environment.prod;
}
```

## Conclusion

BudApp v2.0 represents a significant evolution from the original implementation, incorporating critical lessons learned and industry best practices. The focus on offline-first architecture, proactive dependency management, and comprehensive testing will ensure a robust, scalable, and user-friendly financial management application.

The key to success lies in maintaining the architectural principles established in this PRD while remaining flexible to user feedback and market demands. By following the technical requirements, implementation guidelines, and code templates outlined here, the development team can deliver a superior product that meets user needs and business objectives.

This PRD serves as the foundation for the reconstruction effort, providing clear guidance on features, technical implementation, testing strategies, and deployment procedures. The detailed code examples and templates ensure consistency and best practices throughout the development process.

Regular reviews and updates to this document will ensure alignment between development efforts and business goals throughout the project lifecycle. The comprehensive approach outlined here will result in a more maintainable, scalable, and user-friendly application that avoids the pitfalls encountered in the original implementation.
