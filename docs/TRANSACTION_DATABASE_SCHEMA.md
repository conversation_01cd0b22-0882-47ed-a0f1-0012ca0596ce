# Transaction Database Schema

This document defines the comprehensive Firestore data model for financial transactions in BudApp, including field specifications, validation rules, indexing strategy, and performance considerations.

## Collection Structure

### Firestore Path
```
/users/{userId}/transactions/{transactionId}
```

**Rationale**: Transactions are stored as a subcollection under each user document to ensure:
- **Data Isolation**: Users can only access their own transactions
- **Security**: Firestore Security Rules can easily enforce user ownership
- **Performance**: Queries are automatically scoped to the user
- **Scalability**: Each user's transactions are isolated, preventing collection size limits
- **Cost Optimization**: Queries don't scan across all users

## Transaction Document Schema

### Core Fields (Required)

| Field | Type | Description | Validation |
|-------|------|-------------|------------|
| `id` | string | Unique transaction identifier | Must match document ID |
| `userId` | string | Owner's Firebase Auth UID | Must match parent collection userId |
| `type` | string | Transaction type enum | `income`, `expense`, `transfer` |
| `status` | string | Transaction status enum | `pending`, `completed`, `cancelled`, `failed` |
| `amountCents` | number | Amount in smallest currency unit | Integer > 0 |
| `currencyCode` | string | ISO 4217 currency code | 3-letter uppercase (e.g., "USD") |
| `transactionDate` | timestamp | When the transaction occurred | Firestore timestamp |
| `createdAt` | timestamp | Record creation timestamp | Firestore timestamp |
| `updatedAt` | timestamp | Last modification timestamp | Firestore timestamp |

### Account Reference Fields (Conditional)

| Field | Type | Required | Description | Validation |
|-------|------|----------|-------------|------------|
| `fromAccountId` | string | Conditional | Source account reference | Required for expense/transfer |
| `toAccountId` | string | Conditional | Destination account reference | Required for income/transfer |

### Optional Fields

| Field | Type | Required | Default | Description | Validation |
|-------|------|----------|---------|-------------|------------|
| `categoryId` | string | ❌ | null | Category reference | Valid category ID or null |
| `description` | string | ❌ | null | User description | 0-500 characters |
| `notes` | string | ❌ | null | Additional notes | 0-1000 characters |
| `tags` | array | ❌ | [] | Transaction tags | Array of strings, max 10 tags |
| `metadata` | map | ❌ | {} | Extensible key-value storage | Max 10 keys |

## Transaction Type Business Logic

### Income Transactions
- **Required**: `toAccountId` (destination account)
- **Optional**: `categoryId` (income category)
- **Forbidden**: `fromAccountId`
- **Purpose**: Money coming into an account from external sources

### Expense Transactions
- **Required**: `fromAccountId` (source account)
- **Optional**: `categoryId` (expense category)
- **Forbidden**: `toAccountId`
- **Purpose**: Money leaving an account for external purposes

### Transfer Transactions
- **Required**: `fromAccountId` (source), `toAccountId` (destination)
- **Forbidden**: `categoryId` (transfers don't have categories)
- **Validation**: `fromAccountId` ≠ `toAccountId`
- **Purpose**: Money moving between user's own accounts

## Data Types and Enums

### TransactionType Enum
```typescript
enum TransactionType {
  income = "income",     // Money coming in
  expense = "expense",   // Money going out
  transfer = "transfer"  // Money moving between accounts
}
```

### TransactionStatus Enum
```typescript
enum TransactionStatus {
  pending = "pending",       // Transaction is pending
  completed = "completed",   // Transaction is completed
  cancelled = "cancelled",   // Transaction was cancelled
  failed = "failed"         // Transaction failed
}
```

### Supported Currencies
USD, EUR, GBP, JPY, AUD, CAD, CHF, CNY, SEK, NZD, MXN, SGD, HKD, NOK, TRY, ZAR, BRL, INR, KRW, RUB

## Indexing Strategy

### Required Composite Indexes

Based on common query patterns in the application:

#### 1. User Transactions by Date (Primary)
```json
{
  "collectionGroup": "transactions",
  "fields": [
    {"fieldPath": "userId", "order": "ASCENDING"},
    {"fieldPath": "transactionDate", "order": "DESCENDING"}
  ]
}
```

#### 2. User Transactions by Type and Date
```json
{
  "collectionGroup": "transactions",
  "fields": [
    {"fieldPath": "userId", "order": "ASCENDING"},
    {"fieldPath": "type", "order": "ASCENDING"},
    {"fieldPath": "transactionDate", "order": "DESCENDING"}
  ]
}
```

#### 3. User Transactions by Status and Date
```json
{
  "collectionGroup": "transactions",
  "fields": [
    {"fieldPath": "userId", "order": "ASCENDING"},
    {"fieldPath": "status", "order": "ASCENDING"},
    {"fieldPath": "transactionDate", "order": "DESCENDING"}
  ]
}
```

#### 4. User Transactions by Category and Date
```json
{
  "collectionGroup": "transactions",
  "fields": [
    {"fieldPath": "userId", "order": "ASCENDING"},
    {"fieldPath": "categoryId", "order": "ASCENDING"},
    {"fieldPath": "transactionDate", "order": "DESCENDING"}
  ]
}
```

#### 5. User Transactions by From Account and Date
```json
{
  "collectionGroup": "transactions",
  "fields": [
    {"fieldPath": "userId", "order": "ASCENDING"},
    {"fieldPath": "fromAccountId", "order": "ASCENDING"},
    {"fieldPath": "transactionDate", "order": "DESCENDING"}
  ]
}
```

#### 6. User Transactions by To Account and Date
```json
{
  "collectionGroup": "transactions",
  "fields": [
    {"fieldPath": "userId", "order": "ASCENDING"},
    {"fieldPath": "toAccountId", "order": "ASCENDING"},
    {"fieldPath": "transactionDate", "order": "DESCENDING"}
  ]
}
```

#### 7. User Transactions by Tags and Date
```json
{
  "collectionGroup": "transactions",
  "fields": [
    {"fieldPath": "userId", "order": "ASCENDING"},
    {"fieldPath": "tags", "order": "ASCENDING"},
    {"fieldPath": "transactionDate", "order": "DESCENDING"}
  ]
}
```

#### 8. User Transactions by Amount
```json
{
  "collectionGroup": "transactions",
  "fields": [
    {"fieldPath": "userId", "order": "ASCENDING"},
    {"fieldPath": "amountCents", "order": "DESCENDING"}
  ]
}
```

### Single Field Indexes (Automatic)
- `userId` (automatic via subcollection)
- `transactionDate` (automatic)
- `type` (automatic)
- `status` (automatic)

## Security Rules

### Current Implementation
```javascript
// Validate transaction creation
function isValidTransaction(data, userId, transactionId) {
  return hasRequiredFields(data, [
    'id', 'userId', 'type', 'status', 'amountCents', 
    'currencyCode', 'transactionDate', 'createdAt', 'updatedAt'
  ]) &&
  data.id == transactionId &&
  data.userId == userId &&
  isValidEnum(data.type, ['income', 'expense', 'transfer']) &&
  isValidEnum(data.status, ['pending', 'completed', 'cancelled']) &&
  isPositiveInteger(data.amountCents) &&
  isValidCurrencyCode(data.currencyCode) &&
  isValidTimestamp(data.transactionDate) &&
  isValidTimestamp(data.createdAt) &&
  isValidTimestamp(data.updatedAt) &&
  isValidOptionalString(data, 'fromAccountId', 1, 50) &&
  isValidOptionalString(data, 'toAccountId', 1, 50) &&
  isValidOptionalString(data, 'categoryId', 1, 50) &&
  isValidOptionalString(data, 'description', 0, 500) &&
  isValidOptionalString(data, 'notes', 0, 1000) &&
  isValidOptionalArray(data, 'tags', 0, 10) &&
  isValidOptionalMap(data, 'metadata') &&
  isValidTransactionTypeLogic(data);
}
```

### Business Logic Validation
```javascript
function isValidTransactionTypeLogic(data) {
  return (
    (data.type == 'income' && 
     data.toAccountId != null && 
     data.fromAccountId == null) ||
    (data.type == 'expense' && 
     data.fromAccountId != null && 
     data.toAccountId == null) ||
    (data.type == 'transfer' && 
     data.fromAccountId != null && 
     data.toAccountId != null && 
     data.fromAccountId != data.toAccountId &&
     data.categoryId == null)
  );
}
```

## Performance Considerations

### Query Optimization
- **Pagination**: Use `limit()` and `startAfter()` for large result sets
- **Date Ranges**: Use compound queries with `isGreaterThanOrEqualTo` and `isLessThanOrEqualTo`
- **Account Queries**: Use `Filter.or()` for fromAccountId/toAccountId searches
- **Caching**: Implement client-side caching for frequently accessed data

### Cost Optimization
- **Index Selection**: Minimize number of composite indexes
- **Query Limits**: Always use reasonable limits (default: 20-50 items)
- **Offline Support**: Leverage Firestore offline persistence

### Scalability Patterns
- **Subcollection Isolation**: Each user's transactions are isolated
- **Batch Operations**: Use batch writes for bulk operations
- **Real-time Updates**: Use snapshots for live data synchronization

## Common Query Patterns

### 1. Recent Transactions
```dart
_userTransactionsCollection(userId)
  .orderBy('transactionDate', descending: true)
  .limit(20)
```

### 2. Transactions by Account
```dart
_userTransactionsCollection(userId)
  .where(Filter.or(
    Filter('fromAccountId', isEqualTo: accountId),
    Filter('toAccountId', isEqualTo: accountId),
  ))
  .orderBy('transactionDate', descending: true)
```

### 3. Transactions by Date Range
```dart
_userTransactionsCollection(userId)
  .where('transactionDate', isGreaterThanOrEqualTo: startDate)
  .where('transactionDate', isLessThanOrEqualTo: endDate)
  .orderBy('transactionDate', descending: true)
```

### 4. Transactions by Category
```dart
_userTransactionsCollection(userId)
  .where('categoryId', isEqualTo: categoryId)
  .orderBy('transactionDate', descending: true)
```

## Data Examples

### Valid Income Transaction
```json
{
  "id": "txn_01HXX1234567890",
  "userId": "firebase_auth_uid_123",
  "type": "income",
  "status": "completed",
  "amountCents": 250000,
  "currencyCode": "USD",
  "toAccountId": "acc_01HXX1234567890",
  "categoryId": "cat_salary",
  "description": "Monthly salary",
  "notes": "January 2025 salary payment",
  "tags": ["salary", "monthly"],
  "transactionDate": "2025-01-15T10:30:00Z",
  "createdAt": "2025-01-15T10:30:00Z",
  "updatedAt": "2025-01-15T10:30:00Z",
  "metadata": {
    "source": "manual",
    "payrollId": "PR_2025_01"
  }
}
```

### Valid Expense Transaction
```json
{
  "id": "txn_01HXX1234567891",
  "userId": "firebase_auth_uid_123",
  "type": "expense",
  "status": "completed",
  "amountCents": 4599,
  "currencyCode": "USD",
  "fromAccountId": "acc_01HXX1234567890",
  "categoryId": "cat_groceries",
  "description": "Weekly grocery shopping",
  "tags": ["groceries", "food"],
  "transactionDate": "2025-01-15T14:30:00Z",
  "createdAt": "2025-01-15T14:30:00Z",
  "updatedAt": "2025-01-15T14:30:00Z"
}
```

### Valid Transfer Transaction
```json
{
  "id": "txn_01HXX1234567892",
  "userId": "firebase_auth_uid_123",
  "type": "transfer",
  "status": "completed",
  "amountCents": 100000,
  "currencyCode": "USD",
  "fromAccountId": "acc_01HXX1234567890",
  "toAccountId": "acc_01HXX1234567891",
  "description": "Transfer to savings",
  "transactionDate": "2025-01-15T16:00:00Z",
  "createdAt": "2025-01-15T16:00:00Z",
  "updatedAt": "2025-01-15T16:00:00Z"
}
```

## Implementation Status

### ✅ Completed
- Transaction data model (Freezed)
- Repository interface and implementation
- Firestore security rules
- Basic CRUD operations
- Query methods for common patterns
- Stream-based real-time updates

### ⏳ Pending (Next Tasks)
- Composite index definitions in firestore.indexes.json
- Transaction form UI implementation
- Validation service implementation
- Bulk import/export functionality
- Advanced analytics queries
- Performance monitoring

### 🔄 Future Enhancements
- Recurring transaction support
- Transaction categorization AI
- Receipt attachment support
- Multi-currency conversion
- Advanced reporting and analytics

---

**Related Documentation:**
- [Account Data Schema](ACCOUNT_DATA_SCHEMA.md)
- [Transaction Form UI Design](transaction_form_ui_design.md)
- [Firestore Security Rules](../firestore.rules)
- [API Documentation](../lib/data/repositories/interfaces/transaction_repository.dart)
