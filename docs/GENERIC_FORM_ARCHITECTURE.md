# Generic Form Architecture

## Overview

The BudApp implements a comprehensive generic form system that provides configuration-driven forms for any entity type. This system eliminates code duplication, ensures consistency, and provides type-safe form handling across all create/edit operations.

## Core Components

### 1. GenericFormScreen<T>

The main form screen that can handle any entity type through configuration.

```dart
class GenericFormScreen<T> extends StatelessWidget {
  const GenericFormScreen({super.key, required this.config});
  final GenericFormConfig<T> config;
}
```

**Usage:**
```dart
// Account creation
final config = AccountFormConfig.create(repository: repository);
return GenericFormScreen<Account>(config: config);

// Category editing
final config = CategoryFormConfig.edit(category: category, repository: repository);
return GenericFormScreen<Category>(config: config);
```

### 2. GenericFormConfig<T>

Configuration class that defines form structure and behavior.

```dart
class GenericFormConfig<T> {
  const GenericFormConfig({
    required this.title,
    required this.fields,
    required this.onSubmit,
    this.initialData,
    this.onDelete,
    this.saveButtonText,
    this.showDeleteButton = false,
    // ... additional configuration options
  });
}
```

### 3. BaseEditableFormScreen

The underlying form implementation that handles UI rendering and state management.

```dart
class BaseEditableFormScreen<T> extends ConsumerStatefulWidget {
  const BaseEditableFormScreen({
    required this.title,
    required this.fieldConfigs,
    required this.onSubmit,
    // ... additional parameters
  });
}
```

## Form Field System

### FormFieldConfig

Base configuration for form fields with type-safe value handling.

```dart
abstract class FormFieldConfig<T> {
  const FormFieldConfig({
    required this.key,
    required this.label,
    this.initialValue,
    this.validator,
    this.isRequired = false,
    // ... additional properties
  });
}
```

### Specialized Field Types

#### TextFormFieldConfig
```dart
TextFormFieldConfig(
  key: 'name',
  label: 'Account Name',
  validator: AccountValidators.accountName,
  isRequired: true,
)
```

#### ColorPickerFieldConfig
```dart
ColorPickerFieldConfig(
  key: 'color',
  label: 'Color',
  availableColors: FormConstants.accountColors,
  initialValue: Colors.blue,
)
```

#### IconPickerFieldConfig
```dart
IconPickerFieldConfig(
  key: 'icon',
  label: 'Icon',
  availableIcons: FormConstants.accountIcons,
  initialValue: Icons.account_balance,
)
```

#### DropdownFormFieldConfig
```dart
DropdownFormFieldConfig<AccountType>(
  key: 'type',
  label: 'Account Type',
  items: AccountType.values,
  itemBuilder: (type) => DropdownMenuItem(
    value: type,
    child: Text(type.displayName),
  ),
)
```

## Entity-Specific Configurations

### AccountFormConfig

Handles account creation and editing with specialized fields.

```dart
class AccountFormConfig {
  static GenericFormConfig<Account> create({
    required AccountRepository repository,
    Function(String, dynamic)? onFieldChanged,
  }) {
    return GenericFormConfig<Account>(
      title: 'Create Account',
      fields: [
        TextFormFieldConfig(/* name field */),
        AccountTypeFormFieldConfig(/* type selector */),
        ColorPickerFieldConfig(/* color picker */),
        IconPickerFieldConfig(/* icon picker */),
        TextFormFieldConfig(/* initial balance */),
      ],
      onSubmit: (formData) async {
        final account = _mapFormDataToAccount(formData);
        await repository.createAccount(account);
      },
    );
  }
}
```

### CategoryFormConfig

Handles category creation and editing with parent category support.

```dart
class CategoryFormConfig {
  static GenericFormConfig<Category> create({
    required CategoryRepository repository,
    String? parentId,
  }) {
    return GenericFormConfig<Category>(
      title: parentId != null ? 'Create Subcategory' : 'Create Category',
      fields: [
        TextFormFieldConfig(/* name field */),
        if (parentId == null) CategoryTypeFormFieldConfig(/* type selector */),
        if (parentId != null) ParentCategoryFormFieldConfig(/* parent selector */),
        ColorPickerFieldConfig(/* color picker */),
        IconPickerFieldConfig(/* icon picker */),
      ],
      onSubmit: (formData) async {
        final category = _mapFormDataToCategory(formData, parentId);
        await repository.createCategory(category);
      },
    );
  }
}
```

## Form Field Factory

The `FormFieldFactory` creates appropriate widgets based on field configuration types.

```dart
class FormFieldFactory {
  static Widget createField(FormFieldConfig config, /* ... */) {
    return switch (config.runtimeType) {
      TextFormFieldConfig => _createTextFormField(config as TextFormFieldConfig),
      ColorPickerFieldConfig => ColorPickerFormFieldImpl(config: config as ColorPickerFieldConfig),
      IconPickerFieldConfig => IconPickerFormFieldImpl(config: config as IconPickerFieldConfig),
      DropdownFormFieldConfig => _createDropdownField(config as DropdownFormFieldConfig),
      // ... additional field types
      _ => throw UnsupportedError('Unsupported field type: ${config.runtimeType}'),
    };
  }
}
```

## Data Mapping

### Entity to Form Data
```dart
Map<String, dynamic> getInitialFormData() {
  if (initialData == null) return {};
  
  return dataMapper?.entityToFormData(initialData!) ?? {
    'name': initialData!.name,
    'type': initialData!.type,
    'color': initialData!.color,
    'icon': initialData!.icon,
    // ... additional fields
  };
}
```

### Form Data to Entity
```dart
Future<void> _handleSubmit(Map<String, dynamic> formData) async {
  final entity = dataMapper?.formDataToEntity(formData) ?? 
    _defaultEntityMapping(formData);
  
  await onSubmit(entity);
}
```

## Validation System

### Field-Level Validation
```dart
TextFormFieldConfig(
  key: 'name',
  label: 'Account Name',
  validator: (value) {
    if (value == null || value.isEmpty) {
      return 'Account name is required';
    }
    if (value.length > 50) {
      return 'Account name must be 50 characters or less';
    }
    return null;
  },
)
```

### Form-Level Validation
```dart
GenericFormConfig<Account>(
  // ... other configuration
  validator: (formData) {
    // Cross-field validation
    if (formData['type'] == AccountType.creditCard && 
        (formData['initialBalance'] as double) > 0) {
      return 'Credit card accounts should have negative or zero initial balance';
    }
    return null;
  },
)
```

## Navigation Integration

All forms use consistent full-screen navigation:

```dart
// Router configuration
GoRoute(
  path: '/accounts/create',
  name: 'account-create',
  builder: (context, state) => const AccountCreateScreen(),
),
GoRoute(
  path: '/accounts/:id/edit',
  name: 'account-edit',
  builder: (context, state) {
    final accountId = state.pathParameters['id']!;
    return AccountEditScreen(accountId: accountId);
  },
),
```

## Benefits

### Code Reduction
- **Before**: 8+ separate form implementations
- **After**: Single generic system
- **Impact**: 60-80% reduction in form code

### Type Safety
- Compile-time type checking for all form configurations
- Automatic type conversion between form data and entities
- Type-safe field value handling

### Consistency
- Standardized form layouts and behavior
- Uniform validation and error handling
- Consistent navigation patterns

### Maintainability
- Single source of truth for form behavior
- Easy to add new entity types
- Simplified testing with shared patterns

## Testing

The generic form system includes comprehensive testing:

```dart
testWidgets('AccountFormConfig creates valid form', (tester) async {
  final config = AccountFormConfig.create(repository: mockRepository);
  
  await tester.pumpWidget(
    MaterialApp(home: GenericFormScreen<Account>(config: config)),
  );
  
  expect(find.text('Create Account'), findsOneWidget);
  expect(find.byType(TextFormField), findsNWidgets(2)); // name, balance
  expect(find.byType(ColorPickerFormFieldImpl), findsOneWidget);
  expect(find.byType(IconPickerFormFieldImpl), findsOneWidget);
});
```

## Future Enhancements

1. **Dynamic Field Dependencies**: Fields that show/hide based on other field values
2. **Advanced Validation**: Async validation with server-side checks
3. **Custom Field Types**: Additional specialized field implementations
4. **Form Templates**: Predefined form configurations for common patterns
5. **Accessibility**: Enhanced screen reader and keyboard navigation support
