# iOS Environment Setup Guide

This guide explains how to set up iOS build configurations and schemes for the BudApp project's multi-environment setup (dev, staging, prod).

## Prerequisites

- Xcode installed on your Mac
- Flutter project already configured with Android flavors
- Firebase configuration files already in place:
  - `ios/Runner/prod.GoogleService-Info.plist` (production)
  - `ios/Runner/dev.GoogleService-Info.plist` (development)
  - `ios/Runner/staging.GoogleService-Info.plist` (staging)

## Step-by-Step Setup

### 1. Open the iOS Project in Xcode

```bash
open ios/Runner.xcworkspace
```

### 2. Create Build Configurations

1. In Xcode, select the **Runner** project in the Project Navigator
2. Select the **Runner** project (not target) in the main editor
3. Go to the **Info** tab
4. In the **Configurations** section, you'll see Debug, Release, and Profile

#### Create Dev Configuration:
1. Click the **+** button below the configurations list
2. Select **Duplicate "Debug" Configuration**
3. Rename it to **Dev**
4. Set the configuration file to `ios/Flutter/Dev.xcconfig`

#### Create Staging Configuration:
1. Click the **+** button below the configurations list
2. Select **Duplicate "Release" Configuration**
3. Rename it to **Staging**
4. Set the configuration file to `ios/Flutter/Staging.xcconfig`

#### Create Prod Configuration:
1. Click the **+** button below the configurations list
2. Select **Duplicate "Release" Configuration**
3. Rename it to **Prod**
4. Set the configuration file to `ios/Flutter/Prod.xcconfig`

### 3. Create Schemes

#### Create Dev Scheme:
1. Go to **Product** > **Scheme** > **Manage Schemes...**
2. Click the **+** button to add a new scheme
3. Name it **Runner-Dev**
4. Make sure **Runner** is selected as the target
5. Click **OK**
6. Select the new **Runner-Dev** scheme and click **Edit**
7. In the **Build** section, make sure Runner is selected
8. In the **Run** section:
   - Set **Build Configuration** to **Dev**
   - Set **Launch** to **Wait for executable to be launched**
9. In the **Archive** section:
   - Set **Build Configuration** to **Dev**
10. Click **Close**

#### Create Staging Scheme:
1. Repeat the above steps but:
   - Name the scheme **Runner-Staging**
   - Use **Staging** build configuration for Run and Archive

#### Create Prod Scheme:
1. Repeat the above steps but:
   - Name the scheme **Runner-Prod**
   - Use **Prod** build configuration for Run and Archive

### 4. Verify Configuration

After setting up the configurations and schemes, verify that:

1. Each scheme uses the correct build configuration
2. Each build configuration uses the correct .xcconfig file
3. The bundle identifiers are different for each environment:
   - Dev: `com.digitau.budapp.dev`
   - Staging: `com.digitau.budapp.staging`
   - Prod: `com.digitau.budapp`

## Testing the Setup

### Using Flutter Commands

Once the iOS schemes are set up, you can build for specific environments:

```bash
# Development
flutter build ios --flavor dev

# Staging  
flutter build ios --flavor staging

# Production
flutter build ios --flavor prod
```

### Using Xcode

1. Select the appropriate scheme (Runner-Dev, Runner-Staging, or Runner-Prod)
2. Build and run the project
3. Verify that the correct app name and bundle identifier are used

## Troubleshooting

### Common Issues

1. **Build fails with "No such file or directory"**
   - Make sure the .xcconfig files exist in `ios/Flutter/`
   - Check that the configuration files are properly referenced

2. **Firebase not connecting to the right project**
   - Verify that the correct GoogleService-Info.plist file is being used:
     - Dev: `dev.GoogleService-Info.plist`
     - Staging: `staging.GoogleService-Info.plist`
     - Prod: `prod.GoogleService-Info.plist`
   - Check the Firebase project configuration in the Firebase console

3. **Bundle identifier conflicts**
   - Make sure each environment has a unique bundle identifier
   - Check that the .xcconfig files have the correct PRODUCT_BUNDLE_IDENTIFIER

### Verification Commands

```bash
# Check current schemes
xcodebuild -project ios/Runner.xcodeproj -list

# Build specific configuration
xcodebuild -workspace ios/Runner.xcworkspace -scheme Runner-Dev -configuration Dev
```

## Next Steps

After completing the iOS setup:

1. Test building and running each environment
2. Verify Firebase connectivity for each environment
3. Set up CI/CD pipelines for automated builds
4. Configure app store deployment for staging and production

## Additional Resources

- [Flutter iOS Deployment Guide](https://docs.flutter.dev/deployment/ios)
- [Xcode Build Configuration Guide](https://developer.apple.com/documentation/xcode/build-settings-reference)
- [Firebase iOS Setup](https://firebase.google.com/docs/ios/setup)
