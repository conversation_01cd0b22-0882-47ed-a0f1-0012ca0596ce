# Build Optimization Guide

This document explains the build optimizations implemented to resolve GitHub Actions timeout issues.

## Problem

The GitHub Actions workflow was failing with timeout errors (exit code 143) after ~19 minutes during the Android build process.

## Root Causes

1. **Missing Gradle Wrapper Files**: `gradlew`, `gradlew.bat`, and `gradle-wrapper.jar` were ignored by `.gitignore`
2. **Gradle Performance**: Insufficient JVM heap size and missing parallel processing
3. **NDK/CMake Downloads**: <PERSON><PERSON> was downloading NDK and CMake during execution
4. **Missing Caching**: Insufficient caching of build artifacts
5. **No Build Timeouts**: No explicit timeout handling

## Solutions Implemented

### 1. Fixed Missing Gradle Wrapper Files

**Problem**: Gradle wrapper files were being ignored by `.gitignore`, causing "No such file or directory" and "ClassNotFoundException" errors in CI.

**Solution**:
- **Updated `.gitignore`** and **`android/.gitignore`** to stop ignoring Gradle wrapper files
- **Added all wrapper files** to repository: `gradlew`, `gradlew.bat`, `gradle-wrapper.jar`, `gradle-wrapper.properties`
- **Created verification script** (`verify_gradle_wrapper.sh`) to ensure all files are present and functional
- **Integrated verification** into GitHub workflow

### 2. Gradle Performance Optimizations (`android/gradle.properties`)

```properties
# Gradle Performance Optimizations
org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true

# Build Performance
android.enableR8.fullMode=true
```

### 3. Enhanced Caching (`.github/workflows/dev-build.yml`)

- **Gradle Cache**: Includes daemon and build cache
- **Android NDK/CMake Cache**: Prevents re-downloading during builds
- **Flutter Dependencies Cache**: Speeds up dependency resolution

### 4. Build Process Optimizations

- **Pre-warm Gradle Daemon**: Initializes Gradle before main build
- **Clean Build Artifacts**: Ensures clean state (with error handling)
- **Parallel Processing**: Uses all available CPU cores
- **Verbose Logging**: Better debugging information
- **Gradle Wrapper Verification**: Ensures all wrapper files are present

### 5. Timeout Management

- **Job Timeout**: 30 minutes maximum
- **Build Step Timeout**: 25 minutes maximum
- **Early Failure Detection**: Fails fast on issues

## Performance Monitoring

### Local Testing

Use the provided scripts to test and verify setup locally:

```bash
# Verify Gradle wrapper is properly set up
./scripts/verify_gradle_wrapper.sh

# Test build performance
./scripts/test_build_performance.sh
```

### CI Monitoring

The workflow now includes:
- Build start/end timestamps
- System resource information
- Detailed timing with `time` command
- Memory and CPU core reporting

## Expected Results

- **Local Builds**: Should complete in 5-10 minutes
- **CI Builds**: Should complete in 10-15 minutes
- **Timeout Prevention**: Builds fail gracefully if taking too long

## Troubleshooting

### If Builds Still Timeout

1. Check system resources in CI logs
2. Verify caching is working properly
3. Consider reducing build complexity
4. Check for dependency conflicts

### Performance Regression

1. Run local performance test
2. Compare with previous successful builds
3. Check for new dependencies or configuration changes
4. Review Gradle build scans if available

## Maintenance

- Monitor build times regularly
- Update Gradle version when available
- Review and optimize dependencies periodically
- Keep NDK/CMake versions current but stable
