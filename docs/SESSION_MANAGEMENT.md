# Session Management Documentation

## Overview

BudApp implements comprehensive session management through the `SessionService` class, which extends Firebase Auth's built-in session handling with additional features for user preferences, analytics, and enhanced state management.

## Architecture

### Core Components

1. **SessionService** - Singleton service managing session state and user preferences
2. **SessionInfo** - Data model representing current session state
3. **SessionState** - Enum defining possible session states
4. **SessionError** - Error model for session-related issues
5. **AuthWrapper** - UI component that responds to session state changes
6. **SplashScreen** - Loading screen during session initialization

### Session States

```dart
enum SessionState {
  loading,           // Session is being initialized or restored
  authenticated,     // User is authenticated and email is verified
  unauthenticated,   // User is not authenticated
  emailNotVerified,  // User is authenticated but email is not verified
  error,            // An error occurred during session management
}
```

## Features

### 1. Enhanced Session Management

- **Automatic Session Restoration**: Sessions are automatically restored on app startup
- **Real-time State Updates**: Session state changes are broadcast via streams
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Session Analytics**: Tracks session duration, count, and authentication methods

### 2. User Preferences Persistence

The SessionService persists user preferences using SharedPreferences:

```dart
// Default preferences stored
{
  'theme_mode': 'system',           // Theme preference
  'language': 'en',                 // Language preference
  'notifications_enabled': true,    // Notification settings
  'biometric_enabled': false,       // Biometric authentication
  'secure_mode': false,            // Privacy mode
}
```

### 3. Session Analytics

Tracks important session metrics:

```dart
{
  'session_count': int,                    // Total number of sessions
  'current_session_duration_minutes': int, // Current session duration
  'last_login_time': String,              // ISO 8601 timestamp
  'last_auth_provider': String,           // 'email', 'google', 'apple'
}
```

## Usage

### Basic Usage

```dart
// Initialize session service (done automatically by AuthWrapper)
await SessionService.instance.initialize();

// Listen to session changes
SessionService.instance.sessionStream.listen((sessionInfo) {
  switch (sessionInfo.state) {
    case SessionState.authenticated:
      // Navigate to main app
      break;
    case SessionState.unauthenticated:
      // Show login screen
      break;
    case SessionState.emailNotVerified:
      // Show email verification screen
      break;
    // ... handle other states
  }
});

// Get current session
final currentSession = SessionService.instance.currentSession;
```

### Managing User Preferences

```dart
// Update preferences
await SessionService.instance.updatePreferences({
  'theme_mode': 'dark',
  'notifications_enabled': false,
});

// Get a specific preference
final themeMode = SessionService.instance.getPreference<String>('theme_mode');
```

### Session Analytics

```dart
// Get session analytics
final analytics = SessionService.instance.getSessionAnalytics();
print('Total sessions: ${analytics['session_count']}');
print('Current session duration: ${analytics['current_session_duration_minutes']} minutes');
```

### Manual Session Management

```dart
// Refresh session (useful after profile updates)
await SessionService.instance.refreshSession();

// End session (called automatically on sign out)
await SessionService.instance.endSession();
```

## Integration with Firebase Auth

The SessionService builds on Firebase Auth's foundation:

1. **AuthStateChanges**: Listens to Firebase Auth state changes
2. **Automatic Persistence**: Firebase Auth handles token persistence
3. **Enhanced State**: Adds application-specific session state management
4. **User Preferences**: Persists app-specific user preferences

## Error Handling

Session errors are handled gracefully with specific error codes:

```dart
// Common error codes
'initialization_failed'    // Failed to initialize session service
'session_start_failed'     // Failed to start user session
'session_refresh_failed'   // Failed to refresh session
'auth_state_error'         // Authentication state error
'session_restore_failed'   // Failed to restore session
```

## UI Integration

### AuthWrapper

The `AuthWrapper` component automatically handles session state changes:

```dart
class AuthWrapper extends StatefulWidget {
  // Automatically initializes SessionService
  // Shows appropriate screens based on session state
  // Handles loading states and errors
}
```

### SplashScreen

Enhanced loading screen shown during session initialization:

- Animated app logo and branding
- Environment indicator (dev/staging only)
- Loading indicator with status text
- Smooth transitions to main app

## Testing

Comprehensive test suite covers:

- SessionService singleton behavior
- Session state management
- User preferences handling
- Error handling scenarios
- SessionInfo model behavior
- SessionError model behavior

Run tests with:
```bash
flutter test test/session_service_test.dart
```

## Best Practices

1. **Singleton Usage**: Always use `SessionService.instance` - don't create new instances
2. **Stream Listening**: Use `sessionStream` for reactive UI updates
3. **Error Handling**: Always handle session errors gracefully
4. **Preference Updates**: Use `updatePreferences()` for batch updates
5. **Analytics**: Leverage session analytics for user behavior insights

## Security Considerations

1. **Secure Storage**: Sensitive preferences should use `flutter_secure_storage`
2. **Token Handling**: Firebase Auth handles token security automatically
3. **Session Timeout**: Consider implementing session timeout for sensitive apps
4. **Biometric Integration**: Prepared for future biometric authentication

## Future Enhancements

1. **Biometric Authentication**: Integration with `local_auth` package
2. **Session Timeout**: Automatic session expiration
3. **Multi-Device Management**: Track and manage sessions across devices
4. **Advanced Analytics**: More detailed user behavior tracking
5. **Offline Session**: Enhanced offline session management

## Migration from Previous Implementation

The new session management is backward compatible with the existing AuthService:

1. **AuthWrapper**: Updated to use SessionService instead of direct Firebase Auth
2. **AuthService**: Remains unchanged - SessionService uses it internally
3. **Existing Screens**: No changes required - routing handled by AuthWrapper
4. **Sign Out**: Updated to use AuthService.signOut() for consistency

## Performance Considerations

1. **Lazy Initialization**: SessionService initializes only when needed
2. **Stream Efficiency**: Uses broadcast streams for multiple listeners
3. **Preference Caching**: User preferences are cached in memory
4. **Minimal Firebase Calls**: Reduces unnecessary Firebase Auth calls

## Troubleshooting

### Common Issues

1. **Session Not Restoring**: Ensure `initialize()` is called before using the service
2. **Preferences Not Persisting**: Check SharedPreferences permissions
3. **Stream Not Updating**: Ensure proper stream subscription management
4. **Build Errors**: Run `flutter pub get` after adding session management

### Debug Information

Enable debug logging to troubleshoot session issues:

```dart
// SessionService automatically logs debug information
// Check console for session-related debug messages
```
