# CodeCov Integration for BudApp

## Overview

This document describes the CodeCov integration setup for the BudApp Flutter project, which provides automated test coverage reporting and analysis.

## Configuration

### GitHub Actions Workflow

The CodeCov integration is configured in `.github/workflows/code-quality.yml` as part of the `test-coverage` job:

```yaml
- name: Upload coverage to Codecov
  uses: codecov/codecov-action@v4
  with:
    files: ./coverage/lcov.info
    fail_ci_if_error: true
    verbose: true
    name: codecov-umbrella
    token: ${{ secrets.CODECOV_TOKEN }}
    commit_parent: ${{ github.event.before }}
    override_branch: ${{ github.ref_name }}
    override_commit: ${{ github.sha }}
    slug: hropov/budapp
    working-directory: ./
  env:
    CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
```

### CodeCov Configuration

The project uses `.codecov.yml` for configuration:

- **Coverage targets**: 100% target with 1% threshold
- **Ignored files**: Generated files, test files, platform-specific code
- **Flags**: Separate tracking for core, features, and widgets
- **Comments**: Enabled for pull requests with diff and tree layout

### Key Features

1. **Automatic Upload**: Coverage reports are uploaded on every push to `main` and `development` branches
2. **Pull Request Comments**: CodeCov adds comments to PRs showing coverage changes
3. **Status Checks**: GitHub status checks show coverage status
4. **Detailed Reports**: HTML coverage reports are also generated and stored as artifacts

## Troubleshooting

### Common Issues

1. **Missing CODECOV_TOKEN**: Ensure the secret is configured in GitHub repository settings
2. **Coverage file not found**: Verify that `flutter test --coverage` generates `coverage/lcov.info`
3. **Upload failures**: Check workflow logs for authentication or network issues
4. **"Missing Head Report" in CodeCov**: This occurs when CodeCov doesn't associate the uploaded coverage with the correct commit
   - Ensure `override_commit` and `override_branch` parameters are set correctly
   - Verify the `slug` parameter matches your repository
   - Check that `require_ci_to_pass` is configured in `.codecov.yml`
5. **Coverage report uploaded but not visible**:
   - Wait for CodeCov processing to complete (can take a few minutes)
   - Check that flags in workflow match those defined in `.codecov.yml`
   - Verify the coverage file format is correct (LCOV format for Flutter)

### Recent Fixes Applied

- Set `fail_ci_if_error: true` to catch upload failures early
- Added `token` parameter in addition to environment variable for better authentication
- Fixed duplicate `coverage:` sections in `.codecov.yml`
- Unified coverage configuration into single section
- Added explicit `override_branch`, `override_commit`, and `commit_parent` parameters
- Added `slug` parameter for proper repository identification
- Added verification step to check coverage file before upload
- Added `codecov` section with `require_ci_to_pass` and `notify` settings
- Removed conflicting flags parameter to match `.codecov.yml` configuration

## Verification

To verify CodeCov integration is working:

1. Check that workflow runs complete successfully
2. Visit CodeCov dashboard to see coverage reports
3. Verify that PR comments include coverage information
4. Check that status checks appear on commits
5. Ensure coverage reports are uploaded for the current head commit (not just previous commits)

## Coverage Exclusions

The following files/directories are excluded from coverage reporting:

- Generated files (`**/*.g.dart`, `**/*.freezed.dart`)
- Firebase options files (`lib/firebase_options*.dart`)
- Localization files (`lib/l10n/app_localizations*.dart`)
- Test files (`test/**/*`, `integration_test/**/*`)
- Platform-specific code (`ios/**/*`, `android/**/*`, etc.)

## Maintenance

- Review coverage targets periodically
- Update exclusion patterns as needed
- Monitor CodeCov action version for updates
- Ensure CODECOV_TOKEN remains valid
