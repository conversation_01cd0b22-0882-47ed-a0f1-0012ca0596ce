# Account Validation Documentation

This document describes the comprehensive client-side validation system for account management in BudApp.

## Overview

The `AccountValidators` class provides robust client-side validation for all account-related input fields before data submission to Firestore. It includes immediate feedback to users for invalid entries and supports the app's localization system.

## Features

- **Comprehensive Field Validation**: Covers all account model fields including name, description, balance, currency, type, and visual properties
- **Type-Classification Pairing**: Enforces business rules for valid account type and classification combinations
- **Localized Error Messages**: All validation errors are displayed in the user's preferred language
- **Utility Functions**: Helper methods for data conversion and form validation
- **Extensive Test Coverage**: 100% test coverage with comprehensive test cases

## Validation Rules

### Account Name (`accountName`)

**Requirements:**
- Required field (cannot be null or empty)
- Minimum 2 characters after trimming
- Maximum 50 characters
- Allowed characters: letters, numbers, spaces, and common punctuation (-, _, ., (), &, ', ,)

**Examples:**
```dart
// Valid names
"My Checking Account"
"Savings-2024"
"Cash & Change"
"Primary (Main)"
"Investment_Portfolio"

// Invalid names
""                    // Empty
"A"                   // Too short
"Account@Home"        // Invalid character (@)
```

### Account Description (`accountDescription`)

**Requirements:**
- Optional field (can be null or empty)
- Maximum 200 characters when provided

### Initial Balance (`initialBalance`)

**Requirements:**
- Required field (cannot be null or empty)
- Must be a valid decimal number
- Range: -999,999,999.99 to 999,999,999.99
- Maximum 2 decimal places for currency precision
- Negative values allowed (for liability accounts)

**Examples:**
```dart
// Valid balances
"0"
"100.50"
"1000.99"
"-500.00"       // Negative for liabilities

// Invalid balances
"abc"           // Not a number
"100.123"       // Too many decimal places
"**********.00" // Out of range
```

### Currency Code (`currencyCode`)

**Requirements:**
- Required field (cannot be null or empty)
- Must be exactly 3 uppercase letters (ISO 4217 format)
- Must be from supported currency list

**Supported Currencies:**
USD, EUR, GBP, JPY, AUD, CAD, CHF, CNY, SEK, NZD, MXN, SGD, HKD, NOK, TRY, ZAR, BRL, INR, KRW, RUB

### Account Type & Classification

**Requirements:**
- Both type and classification are required
- Must be valid enum values
- Type-classification pairs must follow business rules

**Valid Type-Classification Pairs:**
- **Assets**: `checking`, `savings`, `cash`, `investment` → `asset`
- **Liabilities**: `creditCard`, `loan` → `liability`

### Visual Properties

#### Color Hex (`colorHex`)
- Optional field
- Must be valid 6-digit hex color format: `#RRGGBB`
- Example: `#FF5733`

#### Icon Name (`iconName`)
- Optional field
- Must be valid identifier format (letters, numbers, underscores)
- Must start with a letter
- Example: `account_balance`

## Usage Examples

### Basic Field Validation

```dart
import 'package:budapp/features/accounts/services/account_validators.dart';

// In your form widget
TextFormField(
  controller: nameController,
  validator: (value) => AccountValidators.accountName(value, context),
  decoration: InputDecoration(labelText: 'Account Name'),
),

TextFormField(
  controller: balanceController,
  validator: (value) => AccountValidators.initialBalance(value, context),
  decoration: InputDecoration(labelText: 'Initial Balance'),
  keyboardType: TextInputType.numberWithOptions(decimal: true),
),
```

### Comprehensive Form Validation

```dart
// Validate entire account form
final errors = AccountValidators.validateAccount(
  name: nameController.text,
  description: descriptionController.text,
  initialBalance: balanceController.text,
  currencyCode: selectedCurrency,
  type: selectedType,
  classification: selectedClassification,
  colorHex: selectedColor,
  iconName: selectedIcon,
  context: context,
);

if (errors.isEmpty) {
  // Form is valid, proceed with submission
  submitAccount();
} else {
  // Display errors to user
  showValidationErrors(errors);
}
```

### Quick Form Validity Check

```dart
// Check if form is valid without getting specific errors
final isValid = AccountValidators.isAccountFormValid(
  name: nameController.text,
  initialBalance: balanceController.text,
  currencyCode: selectedCurrency,
  type: selectedType,
  classification: selectedClassification,
  context: context,
);

// Enable/disable submit button based on validity
submitButton.enabled = isValid;
```

### Balance Conversion Utilities

```dart
// Convert user input to cents for storage
final balanceString = "100.50";
final cents = AccountValidators.parseBalanceToCents(balanceString);
// Result: 10050

// Convert cents back to display format
final displayBalance = AccountValidators.formatCentsToBalance(10050);
// Result: "100.50"
```

## Error Messages

All validation errors are localized and user-friendly. The system provides specific error messages for each validation failure:

- `accountNameRequired`: "Account name is required"
- `accountNameMinLength`: "Account name must be at least 2 characters"
- `accountNameMaxLength`: "Account name cannot exceed 50 characters"
- `accountNameInvalidCharacters`: "Account name contains invalid characters"
- `initialBalanceRequired`: "Initial balance is required"
- `initialBalanceInvalid`: "Please enter a valid balance amount"
- `currencyCodeUnsupported`: "Currency code is not supported"
- `accountTypeClassificationMismatch`: "Account type and classification don't match"

And many more... See `lib/l10n/app_en.arb` for the complete list.

## Type-Classification Business Rules

The validation enforces strict business rules for account type and classification pairing:

### Asset Accounts (Positive balances represent money you have)
- **Checking**: Daily spending and bill payments
- **Savings**: Long-term savings and emergency funds  
- **Cash**: Physical cash and digital wallets
- **Investment**: Investment accounts and portfolios

### Liability Accounts (Positive balances represent money you owe)
- **Credit Card**: Credit cards and revolving credit
- **Loan**: Personal loans, mortgages, and other debt

## Testing

The validation system includes comprehensive test coverage:

```bash
# Run validation tests
flutter test test/features/accounts/services/account_validators_test.dart

# Run all tests
flutter test
```

**Test Coverage:**
- ✅ All individual field validation rules
- ✅ Type-classification pairing validation
- ✅ Comprehensive form validation
- ✅ Balance conversion utilities
- ✅ Edge cases and error conditions
- ✅ Localization integration

## Integration with Forms

The validator is designed to integrate seamlessly with Flutter's form validation system:

```dart
class AccountFormScreen extends ConsumerStatefulWidget {
  @override
  ConsumerState<AccountFormScreen> createState() => _AccountFormScreenState();
}

class _AccountFormScreenState extends ConsumerState<AccountFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _balanceController = TextEditingController();
  
  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          TextFormField(
            controller: _nameController,
            validator: (value) => AccountValidators.accountName(value, context),
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.accountName,
            ),
          ),
          TextFormField(
            controller: _balanceController,
            validator: (value) => AccountValidators.initialBalance(value, context),
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.initialBalance,
            ),
          ),
          ElevatedButton(
            onPressed: () {
              if (_formKey.currentState!.validate()) {
                // Form is valid, proceed with submission
                _submitAccount();
              }
            },
            child: Text(AppLocalizations.of(context)!.createAccount),
          ),
        ],
      ),
    );
  }
}
```

## Security Considerations

**Client-Side Only**: This validation is for user experience and immediate feedback. **Server-side validation in Firestore Security Rules is still required** for data integrity and security.

**Data Sanitization**: The validator trims whitespace and normalizes input where appropriate, but does not perform HTML sanitization as Firestore stores structured data.

**Business Rule Enforcement**: Type-classification pairing rules are enforced to maintain data consistency and support proper balance calculations.

## Future Enhancements

1. **Extended Currency Support**: Add more international currencies based on user demand
2. **Custom Validation Rules**: Support for user-defined validation rules via Remote Config
3. **Async Validation**: Integration with server-side uniqueness checks
4. **Rich Error Messages**: Enhanced error messages with suggestions for fixing validation issues
5. **Accessibility**: Enhanced screen reader support for validation feedback

## Related Documentation

- [Account Data Schema](./ACCOUNT_DATA_SCHEMA.md) - Data model specifications
- [Account Management UI Design](./ACCOUNT_MANAGEMENT_UI_DESIGN.md) - User interface design
- [Firestore Security Rules](../firebase/firestore.rules) - Server-side validation