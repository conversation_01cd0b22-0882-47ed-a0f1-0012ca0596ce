# Account Management UI/UX Design

This document describes the comprehensive UI/UX design for BudApp's Account Management feature, including wireframes, component specifications, and interaction patterns.

## Overview

The Account Management system provides users with intuitive tools to manage their financial accounts, including creating, viewing, editing, and organizing accounts across different types and classifications. The system uses unified shared color and icon picker components for consistency across the application.

## Design Principles

### 1. **Financial Clarity**
- Clear distinction between assets and liabilities
- Prominent display of balances and account status
- Color-coded visual indicators for account types

### 2. **Hierarchical Organization**
- Grouped by classification (Assets/Liabilities)
- Filtered by account type
- Sortable by multiple criteria

### 3. **Progressive Disclosure**
- Summary cards showing key metrics
- Direct navigation to filtered transaction views
- Context-sensitive actions and information

### 4. **Accessibility & Usability**
- Material 3 design system compliance
- Consistent interaction patterns
- Support for light/dark themes

## Screen Architecture

### 1. Account List Screen (`AccountsListScreen`)

**Purpose**: Primary interface for viewing and managing all user accounts

#### **Layout Structure**
```
AppBar
├── Title: "Accounts"
├── Filter Action (IconButton)
└── Overflow Menu (Sort, Settings)

RefreshIndicator
└── ScrollView
    ├── Summary Cards Row
    │   ├── Total Assets Card
    │   └── Net Worth Card
    ├── Assets Section
    │   ├── Section Header (with count badge)
    │   └── Account Cards List
    ├── Liabilities Section
    │   ├── Section Header (with count badge)
    │   └── Account Cards List
    └── Bottom Padding (for FAB)

FloatingActionButton.extended
├── Icon: add
└── Label: "Add Account"
```

#### **Key Features**
- **Pull-to-Refresh**: Updates account data
- **Real-time Summary**: Total assets and net worth calculation
- **Contextual Organization**: Assets and liabilities grouped separately
- **Quick Actions**: Tap to view details, FAB to create new account

#### **Empty State**
- Illustrated placeholder with call-to-action
- Educational content about account types
- Single-action account creation

### 2. Account Card Component (`AccountCard`)

**Purpose**: Compact representation of account information in lists

#### **Visual Structure**
```
Card Container
├── Header Row
│   ├── Account Icon (large, plain, account-specific)
│   ├── Account Info Column
│   │   ├── Name + Primary Badge + Status Badge
│   │   └── Account Type Label
│   └── Actions Menu (optional)
├── Balance Section
│   ├── Balance Label + Amount (no currency symbol)
│   └── Currency Code Badge
└── Description (if available)
```

#### **Design Specifications**
- **Card Elevation**: 1dp for subtle depth
- **Border Radius**: 12dp for modern appearance
- **Padding**: 16dp internal spacing
- **Icon System**:
  - Size: 48px (large, prominent display)
  - Style: Plain icons without background containers
  - Source: Account-specific icons from `iconName` field with type-based fallbacks
  - Color: Account-specific color or theme primary
- **Typography**:
  - Title: titleMedium, semiBold
  - Subtitle: bodyMedium, onSurfaceVariant
  - Balance: titleLarge, semiBold, color-coded

#### **Interactive Elements**
- **Tap Target**: Full card navigates to filtered transaction view for the account
- **Status Indicators**: Primary badge (green), Inactive badge (gray)
- **Actions Menu**: PopupMenuButton with edit, set primary, deactivate, and delete options
- **Balance Color Coding**:
  - Assets: Normal text for positive, red for negative
  - Liabilities: Red for debt, green for overpayment

### 3. Account Type Filter (`AccountTypeFilter`)

**Purpose**: Bottom sheet for filtering accounts by type and status

#### **Content Structure**
```
Bottom Sheet Container
├── Header Row
│   ├── Title: "Filter Accounts"
│   └── Clear All Button
├── Account Type Section
│   ├── Section Title: "Account Type"
│   ├── All Accounts Chip (default selected)
│   └── Individual Type Chips (grid layout)
├── Divider
├── Show Inactive Toggle
│   ├── Toggle Title + Description
│   └── Switch Control
└── Apply Button (full width)
```

#### **Filter Chips Design**
- **Selection State**: Primary container color with border
- **Unselected State**: Surface variant color
- **Content**: Icon + label in horizontal layout
- **Interaction**: Tap to select, immediate visual feedback

#### **Account Type Mapping**
| Type | Icon | Default Color | Classification |
|------|------|--------------|----------------|
| Checking | account_balance | Primary | Asset |
| Savings | savings | Success | Asset |
| Credit Card | credit_card | Warning | Liability |
| Cash | account_balance_wallet | Info | Asset |
| Investment | trending_up | Tertiary | Asset |
| Loan | account_balance_outlined | Error | Liability |

### 4. Empty Accounts State (`EmptyAccountsState`)

**Purpose**: Onboarding experience for new users

#### **Content Flow**
```
Centered Container
├── Illustration (circular icon container)
├── Primary Message: "No accounts yet"
├── Description: Educational text
├── Primary CTA: "Create First Account"
└── Secondary CTA: "Learn about accounts"
```

#### **Educational Modal**
- **Account Types Overview**: Icon + name + description
- **Common Use Cases**: Brief explanations
- **Getting Started Tips**: Next steps guidance

## Interaction Patterns

### 1. **Navigation Flow**
```
Accounts List → Account Detail → Edit Account
             ↓
           Create Account → Form Completion → Success → Back to List
```

### 2. **Filter & Sort**
- **Filter**: Bottom sheet with immediate preview
- **Sort**: Bottom sheet with options (Name, Balance, Date Created)
- **State Persistence**: Filters remain active during session

### 3. **Account Actions**
- **Primary Action**: Tap card to view details
- **Secondary Actions**: Context menu (Edit, Delete, Set Primary)
- **Bulk Actions**: Future enhancement for multiple selection

### 4. **Status Management**
- **Active/Inactive Toggle**: Soft delete pattern
- **Primary Account**: Single selection with automatic deselection
- **Visual Feedback**: Immediate badge updates

## Responsive Design

### **Mobile Portrait (Default)**
- Single column layout
- Full-width cards
- Stack summary cards vertically on small screens

### **Mobile Landscape**
- Maintain single column for consistency
- Adjust padding for better landscape viewing

### **Tablet (Future)**
- Two-column layout for account cards
- Sidebar navigation integration
- Master-detail pattern for account viewing

## Accessibility Features

### **Screen Reader Support**
- Semantic labels for all interactive elements
- Account balance announcements with currency
- Status indicators clearly announced

### **Visual Accessibility**
- High contrast color ratios (4.5:1 minimum)
- Non-color-dependent status indicators
- Scalable text support

### **Motor Accessibility**
- Minimum 44dp touch targets
- Generous spacing between interactive elements
- Voice control compatibility

## State Management Integration

### **Provider Architecture**
```dart
// Core data providers
accountListProvider          // All user accounts
activeAccountsProvider       // Filtered active accounts
assetAccountsProvider       // Asset accounts only
liabilityAccountsProvider   // Liability accounts only
primaryAccountProvider      // User's primary account

// Action providers
accountCreationProvider    // Account creation state
accountUpdateProvider     // Account modification state

// Computed providers
accountStatsProvider      // Summary statistics
```

### **Data Flow**
1. **Load**: AuthService → AccountRepository → UI
2. **Create**: Form → Provider → Repository → Refresh
3. **Update**: Action → Provider → Repository → Invalidate
4. **Filter**: Local state → UI filtering

## Error Handling

### **Loading States**
- Skeleton loading for cards
- Shimmer effect during data fetch
- Progressive loading for large account lists

### **Error States**
- Network error with retry option
- Authentication error with re-login
- Validation errors with inline feedback

### **Offline Behavior**
- Show cached data with indicator
- Queue actions for when online
- Graceful degradation of features

## Performance Considerations

### **Optimization Strategies**
- Lazy loading for large account lists
- Image caching for custom account icons
- Efficient list rebuilding with keys
- Provider invalidation scoping

### **Memory Management**
- Dispose unused providers
- Stream subscription cleanup
- Image memory optimization

## Implementation Status

### ✅ **Completed Components**
- [x] `AccountsListScreen` - Main accounts listing
- [x] `AccountCard` - Individual account display
- [x] `AccountTypeFilter` - Filtering interface
- [x] `EmptyAccountsState` - Onboarding experience
- [x] Account providers - State management
- [x] Localization strings - Full i18n support

### 🚧 **Next Phase (Task 6.3+)**
- [ ] Account creation form
- [ ] Account detail screen
- [ ] Account editing interface
- [ ] Delete confirmation dialogs
- [ ] Account search functionality

### 🔮 **Future Enhancements**
- [ ] Bulk account operations
- [ ] Account merging capability
- [ ] Import from CSV/OFX
- [ ] Account balance charts
- [ ] Custom account icons

## Technical Specifications

### **Dependencies**
```yaml
# Core UI
flutter: ^3.x
flutter_riverpod: ^2.x
go_router: ^13.x

# State & Data
freezed_annotation: ^2.x
json_annotation: ^4.x

# Localization
flutter_localizations:
  sdk: flutter
```

### **File Structure**
```
lib/features/accounts/
├── presentation/
│   ├── screens/
│   │   ├── accounts_list_screen.dart
│   │   ├── account_detail_screen.dart      # Next phase
│   │   └── account_form_screen.dart        # Next phase
│   └── widgets/
│       ├── account_card.dart
│       ├── account_type_filter.dart
│       ├── empty_accounts_state.dart
│       └── account_form_field.dart         # Next phase
├── providers/
│   └── account_providers.dart
└── services/                               # Future
    └── account_validation_service.dart
```

## Conclusion

The Account Management UI/UX design provides a comprehensive, user-friendly interface for managing financial accounts. The design emphasizes clarity, organization, and ease of use while maintaining consistency with BudApp's overall design system.

Key strengths:
- **Intuitive Organization**: Clear asset/liability separation
- **Visual Hierarchy**: Effective use of typography and spacing
- **Progressive Disclosure**: Summary → detail → action flow
- **Accessibility**: Full screen reader and motor support
- **Performance**: Optimized rendering and state management

The implemented components provide a solid foundation for the complete account management experience, with clear pathways for extending functionality in future development phases.