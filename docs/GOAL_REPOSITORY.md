# Goal Repository Implementation

## Overview

The Goal Repository provides comprehensive data access operations for financial goal management in BudApp. It implements the repository pattern with Firestore integration, following established BudApp architectural patterns.

## Architecture

### Interface: IGoalRepository

Located at `lib/data/repositories/interfaces/goal_repository.dart`

The interface defines 25+ methods covering all goal management operations:

- **CRUD Operations**: Create, read, update, delete goals
- **Real-time Streams**: Live data updates using Firestore snapshots
- **Search & Filtering**: Name/description search, status filtering, date range queries
- **Statistics & Analytics**: Progress calculations, user summaries
- **Validation**: Business rule enforcement, uniqueness checking

### Implementation: GoalRepositoryImpl

Located at `lib/data/repositories/implementations/goal_repository_impl.dart`

Key features:
- FirestoreService and FirebaseAuth integration
- User context management with automatic user ID assignment
- Type-safe operations with proper error handling
- Real-time streams for live UI updates
- Automatic goal completion detection

## Collection Structure

Goals are stored in Firestore using the following path:
```
users/{userId}/goals/{goalId}
```

This provides:
- User data isolation
- Efficient querying
- Security rule enforcement
- Scalable performance

## Key Methods

### CRUD Operations

```dart
// Create a new goal
Future<String> createGoal(Goal goal)

// Get goals for current user
Future<List<Goal>> getActiveGoals()

// Update goal details
Future<void> updateGoal(String goalId, Goal goal)

// Soft delete (deactivate)
Future<void> deactivateGoal(String goalId)
```

### Real-time Streams

```dart
// Watch active goals for user
Stream<List<Goal>> watchUserGoals(String userId)

// Watch specific goal
Stream<Goal?> watchGoal(String goalId)
```

### Progress Management

```dart
// Update goal progress
Future<void> updateGoalProgress(String goalId, int newCurrentAmountCents)

// Mark as completed
Future<void> markGoalAsCompleted(String goalId)
```

### Search & Analytics

```dart
// Search by name/description
Future<List<Goal>> searchGoalsByName(String userId, String query)

// Get user statistics
Future<Map<String, dynamic>> getUserGoalSummary(String userId)

// Goals nearing deadline
Future<List<Goal>> getGoalsNearingDeadline(String userId, int daysAhead)
```

## Provider Integration

The repository is available through Riverpod dependency injection:

```dart
// Provider definition
final goalRepositoryProvider = Provider<IGoalRepository>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  final firebaseAuth = ref.watch(firebaseAuthProvider);
  return GoalRepositoryImpl(firestoreService, firebaseAuth);
});

// Usage in other providers or widgets
final goalRepo = ref.read(goalRepositoryProvider);
final goals = await goalRepo.getActiveGoals();
```

## Validation

The repository integrates with the Goal model's built-in validation:

```dart
Future<bool> validateGoal(Goal goal) async {
  final validationErrors = goal.validate();
  if (validationErrors.isNotEmpty) {
    throw Exception('Goal validation failed: ${validationErrors.join(', ')}');
  }
  return true;
}
```

## Error Handling

- **Authentication**: Throws exception if user not authenticated
- **Validation**: Uses Goal model validation with detailed error messages
- **Firestore**: Proper exception handling for network and permission errors
- **Type Safety**: Compile-time type checking throughout

## Usage Examples

### Creating a Goal

```dart
final goalRepo = ref.read(goalRepositoryProvider);

final newGoal = Goal.create(
  userId: 'user123', // Will be overridden by repository
  name: 'Emergency Fund',
  description: 'Save for 6 months of expenses',
  targetAmountCents: 1000000, // $10,000
  targetDate: DateTime.now().add(Duration(days: 365)),
  colorHex: '#4CAF50',
  iconName: 'savings',
);

final goalId = await goalRepo.createGoal(newGoal);
```

### Watching Goals

```dart
// In a Riverpod provider
@riverpod
Stream<List<Goal>> userGoals(Ref ref) {
  final goalRepo = ref.watch(goalRepositoryProvider);
  final authState = ref.watch(authStateProvider);
  
  return authState.when(
    data: (user) => user != null 
        ? goalRepo.watchUserGoals(user.uid)
        : Stream.value([]),
    loading: () => Stream.value([]),
    error: (_, __) => Stream.value([]),
  );
}
```

### Updating Progress

```dart
// When a contribution is added
await goalRepo.updateGoalProgress(goalId, newCurrentAmountCents);

// The repository automatically detects completion
// and updates status if target is reached
```

## Testing

The repository interface enables easy mocking for unit tests:

```dart
class MockGoalRepository extends Mock implements IGoalRepository {}

final container = ProviderContainer(
  overrides: [
    goalRepositoryProvider.overrideWithValue(MockGoalRepository()),
  ],
);
```

## Security

- User data isolation through collection path structure
- Authentication required for all operations
- Firestore Security Rules enforcement
- Input validation before database operations

## Performance

- Efficient Firestore queries with proper indexing
- Real-time streams for live updates
- Pagination support for large datasets
- Optimized for mobile performance

## Future Enhancements

- Batch operations for multiple goals
- Advanced search with full-text indexing
- Goal templates and categories
- Goal sharing and collaboration
- Offline synchronization improvements
