# Flutter Analysis Issues - Batch Fix Scripts

This directory contains scripts to automatically fix common Flutter analysis issues in the BudApp project.

## Scripts Overview

### 1. `fix_flutter_analysis_safe.sh` (RECOMMENDED)
The main batch fix script that handles most common issues safely.

**What it fixes:**
- ✅ Package imports (`always_use_package_imports`)
- ✅ Catch clauses (`avoid_catches_without_on_clauses`)
- ✅ Unnecessary lambdas (`unnecessary_lambdas`)
- ✅ Local variable types (`omit_local_variable_types`)
- ✅ Unnecessary breaks (`unnecessary_breaks`)
- ✅ Unnecessary await (`unnecessary_await_in_return`)
- ✅ Integer literals (`prefer_int_literals`)

**Usage:**
```bash
./fix_flutter_analysis_safe.sh
```

### 2. `fix_package_imports.py`
Specialized Python script for smart package import conversion.

**What it does:**
- Converts relative imports (`../../../something.dart`) to package imports (`package:budapp/something.dart`)
- Intelligently calculates correct package paths
- Handles nested directory structures

**Usage:**
```bash
python3 fix_package_imports.py
```

### 3. `fix_long_lines.py`
Advanced Python script for intelligent line breaking.

**What it does:**
- Breaks lines longer than 80 characters
- Smart breaking at method signatures, method chains, assignments
- Preserves code structure and readability
- Skips comments and imports

**Usage:**
```bash
python3 fix_long_lines.py
```

### 4. `fix_flutter_analysis.sh`
Comprehensive but more aggressive script (use with caution).

**What it includes:**
- All fixes from the safe script
- More aggressive line breaking
- Additional pattern matching

**Usage:**
```bash
./fix_flutter_analysis.sh
```

## Recommended Workflow

### Step 1: Check Current Status
```bash
flutter analyze --no-pub
```

### Step 2: Run Safe Fixes
```bash
./fix_flutter_analysis_safe.sh
```

### Step 3: Handle Long Lines (if needed)
```bash
python3 fix_long_lines.py
```

### Step 4: Review Changes
```bash
git diff
```

### Step 5: Test Everything
```bash
flutter test
flutter analyze --no-pub
```

### Step 6: Format Code
```bash
dart format lib/
```

## Issue Types Handled

### 1. Package Imports (`always_use_package_imports`)
**Before:**
```dart
import '../../../services/firestore_service.dart';
import '../../models/account.dart';
```

**After:**
```dart
import 'package:budapp/services/firestore_service.dart';
import 'package:budapp/data/models/account.dart';
```

### 2. Catch Clauses (`avoid_catches_without_on_clauses`)
**Before:**
```dart
try {
  // code
} catch (error) {
  // handle
}
```

**After:**
```dart
try {
  // code
} on Exception catch (error) {
  // handle
}
```

### 3. Unnecessary Lambdas (`unnecessary_lambdas`)
**Before:**
```dart
list.map((item) => item.toString())
list.where((item) => item.isNotEmpty)
```

**After:**
```dart
list.map(toString)
list.where(isNotEmpty)
```

### 4. Local Variable Types (`omit_local_variable_types`)
**Before:**
```dart
String name = getName();
final int count = getCount();
```

**After:**
```dart
var name = getName();
final count = getCount();
```

### 5. Long Lines (`lines_longer_than_80_chars`)
**Before:**
```dart
final result = someVeryLongMethodCall(parameter1, parameter2, parameter3, parameter4);
```

**After:**
```dart
final result = someVeryLongMethodCall(
    parameter1, parameter2, parameter3, parameter4);
```

## Safety Considerations

### What the Scripts DON'T Touch
- ❌ Freezed model files (to avoid breaking generated code)
- ❌ Generated files (`.g.dart`, `.freezed.dart`)
- ❌ API method signatures
- ❌ Comments and documentation
- ❌ Import statements in complex breaking scenarios

### What to Review Manually
1. **Complex method signatures** - May need manual adjustment
2. **Generated code** - Should be regenerated, not manually fixed
3. **API contracts** - Ensure no breaking changes
4. **Test files** - May need specific handling

## Error Recovery

If something goes wrong:

1. **Revert changes:**
   ```bash
   git checkout -- lib/
   ```

2. **Fix specific files:**
   ```bash
   git checkout -- lib/path/to/specific/file.dart
   ```

3. **Run build_runner** (if models are affected):
   ```bash
   dart run build_runner build --delete-conflicting-outputs
   ```

## Common Issues & Solutions

### Issue: Import paths are wrong after fix
**Solution:** The script calculates relative paths automatically, but complex nested structures might need manual adjustment.

### Issue: Freezed models are broken
**Solution:** The script skips files with `@freezed` annotations. If affected, run:
```bash
dart run build_runner build --delete-conflicting-outputs
```

### Issue: Tests are failing
**Solution:** Review the changes and ensure test imports are correct. Test files might need different handling.

### Issue: Code is not formatted properly
**Solution:** Run `dart format lib/` after all fixes.

## Performance Tips

- **Run in stages:** Use the safe script first, then address specific issues
- **Review changes:** Always check `git diff` before committing
- **Test frequently:** Run tests after each script execution
- **Use selectively:** You can copy and modify scripts for specific directories

## Monitoring Progress

Check progress with:
```bash
# Total issues
flutter analyze --no-pub | wc -l

# Specific issue types
flutter analyze --no-pub | grep "always_use_package_imports" | wc -l
flutter analyze --no-pub | grep "avoid_catches_without_on_clauses" | wc -l
flutter analyze --no-pub | grep "lines_longer_than_80_chars" | wc -l
```

## Contributing

When modifying the scripts:
1. Test on a small subset of files first
2. Ensure no regression in existing functionality
3. Add appropriate safety checks
4. Update this documentation

---

**Note:** These scripts are designed specifically for the BudApp project structure and patterns. Modify as needed for different projects.