---
type: "always_apply"
---
# **BudApp Development Guidelines**

## **Project Overview**

BudApp is a comprehensive personal finance management application built with Flutter and Firebase. It targets tech-savvy individuals aged 22-35 transitioning from manual tracking to structured budgeting tools.

**Core Stack**: Flutter + Firebase (Firestore, Auth, Remote Config, FCM) + Riverpod + go_router

**Development Phase**: Phase 1 MVP - Pure client-side, no Cloud Functions

---

## **Architecture Principles**

### **Offline-First Design**
- Complete functionality without internet connection
- Firestore offline persistence with automatic sync
- Optimistic updates for immediate UI feedback
- Last-Write-Wins conflict resolution
- Pre-populate essential data for offline access

### **Clean Architecture Layers**
```
UI Layer (ConsumerWidget) → Riverpod Providers → Repository Layer → Firestore SDK
```

### **Repository Pattern**
- Abstract interfaces for testability
- Concrete implementations with Firestore
- All data operations in repositories, not UI
- Proper error handling with AsyncValue
- Cache frequently accessed data

---

## **Code Standards**

### **Naming Conventions**
- Files/Directories: `snake_case.dart`
- Classes/Enums: `PascalCase`
- Variables/Methods: `camelCase`
- Constants: `kCamelCase`
- Private: `_prefixed`
- Providers: `suffixProvider`
- Booleans: `isActive`, `hasError`, `canSubmit`

### **Type Safety**
- Explicit types always - avoid `dynamic`
- Use `freezed` for all data models
- Sealed classes for exhaustive matching
- Never mutate state directly - use `copyWith`

### **Financial Data**
- Amounts: Store as integers (cents)
- Currency: ISO 4217 codes ("USD", "EUR")
- Timestamps: Firestore `Timestamp` type
- Enums: Lowercase (`asset`, `liability`, `income`, `expense`, `transfer`)

---

## **Project Structure**
```
lib/
├── features/           # Feature modules
│   ├── auth/          # Authentication
│   ├── accounts/      # Account management
│   ├── transactions/  # Transaction CRUD
│   ├── budgets/       # Budget tracking
│   └── goals/         # Goal management
├── providers/         # Riverpod providers
├── data/
│   ├── repositories/  # Repository pattern
│   └── models/       # Freezed models
├── services/         # Business logic
├── routing/          # go_router config
├── widgets/          # Reusable UI
├── config/           # Theme & tokens
└── l10n/            # Localization
```

---

## **State Management (Riverpod)**

### **Provider Types**
```dart
// Service provider with DI
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService(
    auth: ref.watch(firebaseAuthProvider),
    googleSignIn: ref.watch(googleSignInProvider),
    userRepository: ref.watch(userRepositoryProvider),
  );
});

// AsyncNotifier for operations
@riverpod
class LoginNotifier extends _$LoginNotifier {
  @override
  FutureOr<void> build() {}
  
  Future<void> signIn(String email, String password) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      await ref.read(authServiceProvider).signInWithEmailAndPassword(email, password);
    });
  }
}
```

### **Best Practices**
- Use `ref.watch()` in widgets
- Use `ref.read()` in callbacks
- Use `AsyncValue.guard()` for errors
- Use `@riverpod` annotation
- Handle loading/error states

---

## **Data Models (Freezed)**

### **Model Requirements**
```dart
@freezed
class UserProfile with _$UserProfile {
  const factory UserProfile({
    required String uid,
    String? email,
    required DateTime createdAt,
    @Default({}) Map<String, dynamic> preferences,
  }) = _UserProfile;

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);
}
```

- Match Firestore field names exactly
- Include `schemaVersion` for migrations
- Use `@JsonKey` for custom mapping
- Handle nullable fields gracefully

---

## **Firebase Integration**

### **Firestore Structure**
```
users/{userId}/
├── accounts/{accountId}
├── transactions/{transactionId}
├── categories/{categoryId}
├── budgets/{budgetId}
├── goals/{goalId}
└── tags/{tagId}
```

### **Security Rules**
- User data isolation under `/users/{userId}/`
- Authentication required for all operations
- Field validation and business logic
- Immutable field protection
- Referential integrity checks

### **Remote Config**
- Feature flags and premium limits
- Predefined categories
- A/B testing support
- 12-hour cache with graceful failures

---

## **UI & Design**

### **Material 3 Design System**
- Centralized `AppTheme` and `DesignTokens`
- No hardcoded values - use tokens
- Light/dark theme support
- Financial colors (green income, red expense)

### **Widget Guidelines**
- Always use `ConsumerWidget`/`ConsumerStatefulWidget`
- Prefer small widget classes over methods
- Use `const` constructors
- Implement loading/error/empty states
- Minimum 44x44 tap targets

### **Accessibility**
- WCAG AA compliance
- Screen reader support
- Dynamic type support
- Semantic labels
- Proper contrast ratios

---

## **Navigation (go_router)**

### **Route Configuration**
```dart
final goRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    redirect: (context, state) {
      final authState = ref.read(authStateProvider);
      // Authentication-based routing
    },
    routes: [
      GoRoute(path: '/login', builder: (_, __) => LoginScreen()),
      GoRoute(path: '/home', builder: (_, __) => HomeScreen()),
    ],
  );
});
```

### **Authentication Flow**
- Unauthenticated → Login
- Unverified email → Verification
- Authenticated → Home/Requested route

---

## **Testing Requirements**

### **Test Coverage**
- Unit: Business logic, repositories
- Widget: UI with provider overrides
- Integration: User flows with emulator
- Golden: Visual regression
- Minimum 80% coverage

### **Test Helpers**
```dart
class TestWrapper {
  static Widget createTestWidget(Widget child, {
    List<Override> overrides = const [],
  }) {
    return ProviderScope(
      overrides: overrides,
      child: MaterialApp(home: child),
    );
  }
}
```

---

## **Performance Requirements**

- 60fps scrolling/animations
- <2s cold start (Pixel 6a, iPhone 12)
- Optimized bundle size
- Paginated large data sets
- Cached computed values
- Disposed listeners

---

## **Security Guidelines**

- Use `flutter_secure_storage` for tokens
- Client + server validation
- Biometric authentication support
- Secure mode for privacy
- GDPR/CCPA compliance
- Complete account deletion

---

## **Development Workflow**

### **Environments**
- Dev: `flutter run` (default)
- Staging: `flutter run --flavor staging`
- Prod: `flutter run --flavor prod`
- Visual indicators: Orange/Blue/Green

### **Code Generation**
```bash
flutter pub run build_runner build --delete-conflicting-outputs
```

### **Quality Checks**
```bash
flutter analyze
dart format .
flutter test --coverage
```

---

## **Feature Implementation**

### **Transactions**
- Single document per transaction
- Type field: `income`, `expense`, `transfer`
- Client-side balance calculation
- Direct editing allowed
- Hard delete with recalculation

### **Budgets**
- Monthly calendar-based only
- Category hierarchy roll-up
- Real-time progress tracking
- Visual thresholds (green/orange/red)

### **Goals**
- Manual contribution model (MVP)
- Visual progress indicators
- Completed goals separate section

### **Categories**
- Predefined via Remote Config
- Custom user categories
- Hierarchical parent-child
- Cannot delete if has transactions

---

## **Phase Roadmap**

### **Phase 1 (Current): MVP**
- Core features client-side only
- Firebase services (no Functions)
- RevenueCat subscriptions
- Offline-first functionality

### **Phase 2: Recurring**
- Cloud Functions + Scheduler
- Automated recurring transactions
- Background maintenance

### **Phase 3: Intelligence**
- Push notifications
- Complex aggregations
- Email digests
- GDPR automation

---

## **Common Patterns**

### **AsyncValue Error Handling**
```dart
state = await AsyncValue.guard(() async {
  // Operation that might fail
});
```

### **Repository Pattern**
```dart
abstract class IUserRepository {
  Future<UserProfile?> getUserProfile(String userId);
  Stream<UserProfile?> watchUserProfile(String userId);
}
```

### **Provider Testing**
```dart
final container = ProviderContainer(
  overrides: [
    authServiceProvider.overrideWithValue(mockAuthService),
  ],
);
```

---

## **Quick Reference**

### **Memory Bank**: `.taskmaster/memory-bank/`
### **Firebase Rules**: `firestore.rules`
### **Design Tokens**: `lib/config/design_tokens.dart`
### **Routes**: `lib/routing/app_router.dart`

### **Key Documentation**
- [Architecture](mdc:docs/architecture.md)
- [Testing Guide](mdc:docs/TESTING.md)
- [PRD](mdc:docs/PRD.md)
- [Riverpod Rules](mdc:docs/rules/riverpod.md)
- [Firebase Rules](mdc:docs/rules/firebase)
