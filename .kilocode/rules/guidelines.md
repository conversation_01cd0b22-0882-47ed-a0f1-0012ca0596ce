---
type: "always_apply"
---

IMPORTANT #1:
- Follow Test-Driven Development (TDD): create tests first, verify they fail, then implement functionality
- Use the Context7 tool to for fresh documentation
- Use websearch tool for searching the web for information and documentation.
- Use Sequential Thinking tool to break down complex problems and plan implementation steps
- Apply all rules from the `docs/rules/` directory
- Follow Flutter/Firebase industry best practices for offline-first architecture with Riverpod state management

IMPORTANT #2 - Before completing the task, run:
- `flutter analyze` (resolve any issues)
- `flutter test` (fix any failing tests by improving codebase)
- `dart format .` (ensure code formatting)

IMPORTANT #3 - When task is complete, update:
- Related documentation files
- READMEs as needed
- CHANGELOG.md with changes made
- Memory bank files to reflect current state and progress

IMPORTANT #4 - When task is complete and docs are updated, mark task as done in Task Master

COMMANDS:
- For rebuilding use command - "dart run build_runner build --delete-conflicting-outputs"
- PROCEED WITH TASK - read memory bank, mark task as in-progress, then proceed with task
- COMPLETE TASK - mark task as done, update docs, update READMEs, update memory bank, then commit to git.