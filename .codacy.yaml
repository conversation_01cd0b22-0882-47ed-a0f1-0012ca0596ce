---
# Minimal Codacy Configuration for BudApp Flutter Project
# Focus: Coverage monitoring and duplication detection with basic Dart analysis
# Synchronized with analysis_options.yaml for consistent linting

# Global file exclusions - apply to all analysis tools
exclude_paths:
  # Generated files - never analyze these (matches analysis_options.yaml)
  - "**/*.g.dart"
  - "**/*.freezed.dart"
  - "**/*.gr.dart"
  - "**/*.config.dart"
  - "**/generated_plugin_registrant.dart"
  - "lib/l10n/app_localizations*.dart"
  - "lib/firebase_options*.dart"

  # Test files - exclude from main analysis (focus on business logic)
  - "test/**"
  - "integration_test/**"
  - "test/.test_coverage.dart"
  - "integration_test/driver.dart"

  # Platform code - handled by native tools
  - "android/**"
  - "ios/**"
  - "linux/**"
  - "macos/**"
  - "windows/**"
  - "web/**"

  # Build artifacts and tooling
  - "build/**"
  - ".dart_tool/**"
  - "coverage/**"
  - "**/.flutter-plugins*"
  - "**/ephemeral/**"

  # Assets and media files
  - "assets/**"
  - "**/*.png"
  - "**/*.jpg"
  - "**/*.jpeg"
  - "**/*.ico"
  - "**/*.svg"

  # Configuration and documentation files
  - "**/*.md"
  - "**/*.yaml"
  - "**/*.yml"
  - "**/*.json"
  - "**/*.xml"
  - "**/*.plist"
  - "**/*.lock"
  - "**/*.gitignore"

  # Scripts, docs, and temporary files
  - "scripts/**"
  - "docs/**"
  - "firebase/**"
  - "emulator-data/**"
  - "*debug.log"
  - "repomix*"
  - "*.md"

# Include paths - focus analysis on business logic only
include_paths:
  - "lib/features/**"         # Feature modules (main business logic)
  - "lib/data/repositories/**" # Data layer
  - "lib/services/**"         # Business services
  - "lib/providers/**"        # State management
  - "lib/utils/**"           # Utility functions
  - "lib/widgets/**"         # UI components (excluding generated)
  - "lib/routing/**"         # Navigation logic (excluding generated)

# Duplication detection configuration - PRIMARY FOCUS
duplication:
  # Configuration for duplication detection
  config:
    # Set reasonable threshold for Flutter projects
    minTokenMatch: 200  # Catch meaningful duplications
    languages:
      - "dart"

# Coverage configuration - PRIMARY FOCUS
coverage:
  include_paths:
    - "lib/features/**"
    - "lib/data/repositories/**"
    - "lib/services/**"
    - "lib/providers/**"
    - "lib/utils/**"
    - "lib/widgets/**"
  exclude_paths:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.config.dart"
    - "**/*.gr.dart"
    - "lib/firebase_options*.dart"
    - "lib/l10n/**"
    - "lib/main.dart"
    - "test/**"

# Language configuration - Dart only for minimal setup
languages:
  dart:
    enabled: true
    extensions:
      - ".dart"

# Tool-specific configurations
engines:
  dartanalyzer:
    enabled: true
    # Use project's analysis_options.yaml for consistency
    exclude_paths:
      # Additional exclusions specific to dartanalyzer
      - "test/**"
      - "integration_test/**"
      - "**/*.g.dart"
      - "**/*.freezed.dart"
      - "**/*.gr.dart"
      - "**/*.config.dart"
      - "**/generated_plugin_registrant.dart"
      - "build/**"
      - ".dart_tool/**"
      - "coverage/**"

# Minimal Configuration Notes:
# 1. Focus on coverage and duplication monitoring with basic Dart analysis
# 2. Only dartanalyzer tool enabled in .codacy/codacy.yaml
# 3. Exclude all generated files, platform code, and test files
# 4. Uses same exclusions as analysis_options.yaml for consistency
# 5. To add more tools, update .codacy/codacy.yaml and add tool-specific config here
# 6. Expected result: Much fewer issues, focused on actual business logic code quality