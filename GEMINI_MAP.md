# BudApp Codebase Map

This document provides a comprehensive map of the BudApp codebase, focusing on the `lib/` folder. It outlines the architecture, key components, and design patterns used in the application.

## 1. Project Structure Overview

The `lib/` folder is organized into the following main directories:

- **`config/`**: Contains application-wide configurations, such as themes, design tokens, and environment settings.
- **`data/`**: Handles data persistence and management, including models, repositories, and data sources.
- **`features/`**: Implements the core features of the application, such as accounts, transactions, and budgets. Each feature is organized by layer (e.g., `presentation`, `services`, `providers`).
- **`l10n/`**: Manages localization and internationalization of the application.
- **`providers/`**: Defines application-level providers for dependency injection and state management.
- **`routing/`**: Configures the application's navigation and routing logic.
- **`services/`**: Provides shared services used across multiple features, such as authentication, logging, and error handling.
- **`widgets/`**: Contains reusable widgets that are not specific to any single feature.

## 2. Architectural Patterns

The application follows a clean architecture pattern, with a clear separation of concerns between the data, domain, and presentation layers. Key architectural patterns include:

- **Repository Pattern**: The `data/repositories/` directory contains repository implementations that abstract the data sources from the rest of the application. This allows for easy swapping of data sources without affecting the business logic.
- **Provider Pattern (with Riverpod)**: The application uses the `flutter_riverpod` package for dependency injection and state management. Providers are defined in the `providers/` directory and are used to access repositories, services, and other dependencies throughout the application.
- **Feature-Driven Development**: The `features/` directory is organized by feature, with each feature containing its own set of widgets, providers, and services. This promotes modularity and makes it easier to add new features to the application.
- **Generic Form System**: The `widgets/forms/` directory contains a generic form system that allows for the creation of dynamic forms with minimal boilerplate code. This system is used for all create/edit screens in the application.

## 3. Key Components and Solutions

### 3.1. Configuration (`config/`)

- **`app_theme.dart`**: Defines the application's theme, including colors, typography, and other design tokens.
    - `AppTheme` class:
        - `lightTheme`: A static getter that returns the `ThemeData` for the light theme.
        - `darkTheme`: A static getter that returns the `ThemeData` for the dark theme.
        - `getTextOverflowConfig`: A static method that returns the `TextOverflowConfig` for a given `TextContext`.
        - `getDefaultTextOverflow`: A static method that returns the default `TextOverflowConfig` for a given `TextStyle`.
- **`design_tokens.dart`**: Contains a set of design tokens that are used throughout the application to ensure a consistent look and feel.
    - `AppColors`: A class with static constants for all the colors used in the app.
    - `AppSpacing`: A class with static constants for spacing values.
    - `AppTypography`: A class with static constants for typography values.
    - `AppTextOverflowTokens`: A class with static constants for text overflow configurations.
    - `AppBorderRadius`: A class with static constants for border radius values.
    - `AppElevation`: A class with static constants for elevation values.
    - `DesignTokens`: A class that combines all the design tokens into a single class for easy access.
- **`environment_config.dart`**: Manages environment-specific configurations, such as API endpoints and feature flags.
    - `EnvironmentConfig` class:
        - `flavor`: A static getter that returns the current environment flavor (dev, staging, prod).
        - `environmentName`: A static getter that returns the display name for the current environment.
        - `appTitle`: A static getter that returns the app title for the current environment.
        - `themeColor`: A static getter that returns the theme color for the current environment.
        - `firebaseProjectName`: A static getter that returns the Firebase project name for the current environment.
        - `firebaseOptions`: A static getter that returns the `FirebaseOptions` for the current environment.
        - `showFirebaseTesting`: A static getter that returns whether to show Firebase testing features.
        - `isDevelopment`: A static getter that returns whether the current environment is development.
        - `isStaging`: A static getter that returns whether the current environment is staging.
        - `isProduction`: A static getter that returns whether the current environment is production.
        - `firebaseInitMessage`: A static getter that returns the debug print message for Firebase initialization.
        - `homePageTitle`: A static getter that returns the app home page title.
        - `useDebugAppCheckProvider`: A static getter that returns whether to use the debug App Check provider.
        - `appCheckWebRecaptchaSiteKey`: A static getter that returns the App Check web reCAPTCHA site key.
        - `androidAppCheckProvider`: A static getter that returns the Android App Check provider based on the environment.
        - `appleAppCheckProvider`: A static getter that returns the Apple App Check provider based on the environment.
        - `initializeAppCheck`: A static method that initializes Firebase App Check with environment-specific configuration.
        - `environmentSummary`: A static getter that returns a map of the environment summary for debugging.
- **`form_constants.dart`**: Provides shared constants for form fields across the application.
    - `FormConstants` class:
        - `standardColors`: A static constant list of standard colors for entity color selection.
        - `standardIcons`: A static constant list of standard icons for entity icon selection.
        - `categoryColors`: A static constant list of category-specific colors.
        - `categoryIcons`: A static constant list of category-specific icons.
        - `accountColors`: A static constant list of account-specific colors.
        - `accountIcons`: A static constant list of account-specific icons.
        - `tagColors`: A static constant list of tag-specific colors.
        - `tagIcons`: A static constant list of tag-specific icons.
        - `goalColors`: A static constant list of goal-specific colors.
        - `goalIcons`: A static constant list of goal-specific icons.
- **`text_overflow_config.dart`**: Defines different text contexts for automatic overflow handling.
    - `TextContext` enum: Defines different text contexts for automatic overflow handling.
    - `OverflowStrategy` enum: Defines different overflow handling strategies.
    - `TextOverflowConfig` class: A configuration class for text overflow behavior.
    - `AppTextOverflow` class: A class with predefined text overflow configurations for different contexts.

### 3.2. Data Layer (`data/`)

- **`models/`**: Contains the data models for the application, such as `Account`, `Transaction`, and `Budget`. These models are implemented using the `freezed` package to provide immutable data classes.
    - **`account.dart`**: Defines the `Account` model, `AccountType` enum, and `AccountClassification` enum.
    - **`budget.dart`**: Defines the `Budget` model, `BudgetType` enum, and `BudgetPeriod` enum.
    - **`budget_progress.dart`**: Defines the `BudgetProgress` model and `BudgetProgressStatus` enum.
    - **`category.dart`**: Defines the `Category` model, `CategoryType` enum, and `CategoryTree` model.
    - **`goal.dart`**: Defines the `Goal` model and `GoalStatus` enum.
    - **`goal_contribution.dart`**: Defines the `GoalContribution` model.
    - **`remote_config_data.dart`**: Defines the `RemoteConfigData`, `PredefinedCategories`, and `PremiumLimits` models.
    - **`session_state.dart`**: Defines the `SessionState` enum, `SessionError` class, and `SessionInfo` class.
    - **`tag.dart`**: Defines the `Tag` model.
    - **`transaction.dart`**: Defines the `Transaction` model, `TransactionType` enum, and `TransactionStatus` enum.
    - **`user_profile.dart`**: Defines the `UserProfile` model.
- **`repositories/`**: Implements the repository pattern, with interfaces defined in `interfaces/` and implementations in `implementations/`. The repositories are responsible for fetching and storing data from various sources, such as Firestore and secure storage.
    - **`interfaces/`**: Contains the interfaces for all repositories.
        - `account_repository.dart`: `IAccountRepository`
        - `budget_repository.dart`: `IBudgetRepository`
        - `category_repository.dart`: `ICategoryRepository`
        - `goal_repository.dart`: `IGoalRepository`
        - `i_goal_contribution_repository.dart`: `IGoalContributionRepository`
        - `performance_optimized_repository.dart`: `IPerformanceMonitoringService`, `IPerformanceService`
        - `remote_config_repository.dart`: `IRemoteConfigRepository`
        - `tag_repository.dart`: `TagRepository`
        - `transaction_repository.dart`: `ITransactionRepository`
        - `user_repository.dart`: `IUserRepository`
    - **`implementations/`**: Contains the concrete implementations of the repository interfaces.
        - `account_repository_impl.dart`: `AccountRepositoryImpl`
        - `budget_repository_impl.dart`: `BudgetRepositoryImpl`
        - `category_repository_impl.dart`: `CategoryRepositoryImpl`
        - `goal_contribution_repository_impl.dart`: `GoalContributionRepositoryImpl`
        - `goal_repository_impl.dart`: `GoalRepositoryImpl`
        - `remote_config_repository_impl.dart`: `RemoteConfigRepositoryImpl`
        - `tag_repository_impl.dart`: `TagRepositoryImpl`
        - `transaction_repository_impl.dart`: `TransactionRepositoryImpl`
        - `user_repository_impl.dart`: `UserRepositoryImpl`
- **`converters/`**: Provides custom converters for serializing and deserializing data, such as the `MetadataConverter` for handling timestamps.

### 3.3. Features (`features/`)

Each feature directory is organized into the following subdirectories:

- **`presentation/`**: Contains the UI components for the feature, including screens, widgets, and providers.
- **`services/`**: Implements the business logic for the feature, such as validation, error handling, and data processing.
- **`providers/`**: Defines the providers for the feature, which are used to access the feature's services and repositories.

#### Key Features:

- **`accounts/`**: Manages user accounts, including creating, editing, and deleting accounts.
    - **`presentation/`**
        - **`screens/`**
            - `account_create_screen.dart`: `AccountCreateScreen` widget.
            - `account_detail_screen.dart`: `AccountDetailScreen` widget.
            - `account_edit_screen.dart`: `AccountEditScreen` widget.
            - `accounts_list_screen.dart`: `AccountsListScreen` widget.
        - **`widgets/`**
            - `account_card.dart`: `AccountCard` widget.
            - `account_color_selector.dart`: `AccountColorSelector` widget.
            - `account_icon_selector.dart`: `AccountIconSelector` widget.
            - `account_type_filter.dart`: `AccountTypeFilter` widget.
            - `account_type_selector.dart`: `AccountTypeSelector` widget.
            - `empty_accounts_state.dart`: `EmptyAccountsState` widget.
    - **`providers/`**
        - `account_providers.dart`: Defines all providers related to accounts, including `accountListProvider`, `accountProvider`, `activeAccountsProvider`, `assetAccountsProvider`, `liabilityAccountsProvider`, `primaryAccountProvider`, `accountCreationProvider`, `accountUpdateProvider`, and `accountStatsProvider`.
    - **`services/`**
        - `account_validators.dart`: `AccountValidators` class with static methods for validating account fields.
- **`auth/`**: Implements user authentication, including email/password, social login, and biometric authentication.
    - **`presentation/`**
        - **`screens/`**
            - `auth_settings_screen.dart`: `AuthSettingsScreen` widget.
            - `auth_wrapper.dart`: `AuthWrapper` widget.
            - `biometric_gate_screen.dart`: `BiometricGateScreen` widget.
            - `email_verification_screen.dart`: `EmailVerificationScreen` widget.
            - `forgot_password_screen.dart`: `ForgotPasswordScreen` widget.
            - `login_screen.dart`: `LoginScreen` widget.
            - `signup_screen.dart`: `SignupScreen` widget.
        - **`widgets/`**
            - `auth_button.dart`: `AuthButton` and `AuthTextButton` widgets.
            - `auth_error_banner.dart`: `AuthErrorBanner` and `AuthErrorSnackBar` widgets.
            - `auth_form_field.dart`: `AuthFormField` widget and `AuthValidators` class.
            - `biometric_auth_button.dart`: `BiometricAuthButton` and `BiometricIconButton` widgets.
            - `biometric_error_banner.dart`: `BiometricErrorBanner` widget.
            - `biometric_settings_tile.dart`: `BiometricSettingsTile` widget.
            - `social_login_button.dart`: `SocialLoginButton` and `AuthDivider` widgets.
    - **`providers/`**
        - `auth_providers.dart`: Defines all providers related to authentication, including `authServiceProvider`, `authErrorServiceProvider`, `sessionServiceProvider`, `isFullyAuthenticatedProvider`, `userProvidersProvider`, `isSignedInWithGoogleProvider`, `isSignedInWithEmailPasswordProvider`, `userDisplayNameProvider`, `userEmailProvider`, `userPhotoUrlProvider`, `loginNotifierProvider`, `signupNotifierProvider`, `googleSignInNotifierProvider`, `passwordResetNotifierProvider`, `emailVerificationNotifierProvider`, and `authErrorHandlerProvider`.
        - `biometric_providers.dart`: Defines all providers related to biometric authentication, including `biometricServiceProvider`, `biometricAvailabilityProvider`, `biometricPreferenceProvider`, `biometricAuthReadyProvider`, `biometricAuthLoadingProvider`, `biometricAuthErrorProvider`, `biometricAuthNotifierProvider`, and `biometricGateStateNotifierProvider`.
    - **`services/`**
        - `auth_error_service.dart`: `AuthErrorService` class with static methods for handling authentication errors.
        - `auth_service.dart`: `AuthService` class for handling authentication operations.
        - `biometric_error_service.dart`: `BiometricErrorService` class with static methods for handling biometric authentication errors.
        - `biometric_service.dart`: `BiometricService` class for handling biometric authentication operations.
- **`budgets/`**: Allows users to create and track budgets for different categories.
    - **`data/`**
        - **`models/`**
            - `category_budget_info.dart`: `CategoryBudgetInfo` and `CategoryTypeBudgetSummary` models.
    - **`presentation/`**
        - **`screens/`**
            - `budget_edit_screen.dart`: `BudgetEditScreen` widget.
            - `budgets_list_screen.dart`: `BudgetsListScreen` widget.
        - **`widgets/`**
            - `budget_card.dart`: `BudgetCard` widget.
            - `budget_copy_dialog.dart`: `BudgetCopyDialog` widget.
            - `budget_deletion_dialog.dart`: `BudgetDeletionDialog` widget.
            - `budget_form.dart`: `BudgetForm` widget.
            - `budget_overview_card.dart`: `BudgetOverviewCard` widget.
            - `budget_progress_bar.dart`: `BudgetProgressBar` and `DetailedBudgetProgressBar` widgets.
            - `bulk_budget_operations_dialog.dart`: `BulkBudgetOperationsDialog` widget.
            - `category_budget_card.dart`: `CategoryBudgetCard` widget.
            - `empty_budgets_state.dart`: `EmptyBudgetsState`, `FilteredEmptyBudgetsState`, and `NoMatchingBudgetsState` widgets.
    - **`providers/`**
        - `budget_providers.dart`: Defines all providers related to budgets, including `budgetsProvider`, `budgetsByMonthProvider`, `templateBudgetsForFuturePeriodProvider`, `totalBudgetByMonthAndTypeProvider`, `totalBudgetsByMonthProvider`, `budgetProvider`, `budgetProgressProvider`, `spendingBreakdownProvider`, `totalSpendingProvider`, `spendingTrendProvider`, `budgetEditProvider`, `budgetsControllerProvider`, `categoryBudgetInfoProvider`, `categoryTypeBudgetSummaryProvider`, `budgetCopyServiceProvider`, `currentTimePeriodProvider`, `currentBudgetPeriodProvider`, `bulkBudgetServiceProvider`, `bulkBudgetControllerProvider`, and `budgetTransactionServiceProvider`.
    - **`services/`**
        - `budget_copy_service.dart`: `BudgetCopyService` class for copying budgets between time periods.
        - `budget_error_service.dart`: `BudgetErrorService` class with static methods for handling budget-related errors.
        - `budget_progress_service.dart`: `BudgetProgressService` class for calculating budget progress and spending analysis.
        - `budget_transaction_service.dart`: `BudgetTransactionService` class for handling automatic budget updates when transactions are created, updated, or deleted.
        - `budget_validators.dart`: `BudgetValidators` class with static methods for validating budget fields.
        - `bulk_budget_service.dart`: `BulkBudgetService` class for performing bulk operations on budgets.
- **`categories/`**: Manages the categories that are used to classify transactions.
    - **`presentation/`**
        - **`screens/`**
            - `categories_list_screen.dart`: `CategoriesListScreen` widget.
            - `category_create_screen.dart`: `CategoryCreateScreen` widget.
            - `category_edit_screen.dart`: `CategoryEditScreen` widget.
        - **`widgets/`**
            - `category_card.dart`: `CategoryCard` widget.
            - `category_color_selector.dart`: `CategoryColorSelector` widget.
            - `category_deletion_dialog.dart`: `CategoryDeletionDialog` and `CategoryReassignmentDialog` widgets.
            - `category_icon_selector.dart`: `CategoryIconSelector` widget.
            - `category_tree_widget.dart`: `CategoryTreeWidget`, `SimpleCategoryTreeWidget`, and `CategoryTreeStats` widgets.
            - `category_type_filter.dart`: `CategoryTypeFilter` widget.
            - `category_type_selector.dart`: `CategoryTypeSelector` widget.
            - `empty_categories_state.dart`: `EmptyCategoriesState` widget.
            - `parent_category_selector.dart`: `ParentCategorySelector` widget.
    - **`providers/`**
        - `category_providers.dart`: Defines all providers related to categories, including `categoryListProvider`, `categoryProvider`, `activeCategoriesProvider`, `incomeCategoriesProvider`, `expenseCategoriesProvider`, `rootCategoriesProvider`, `rootCategoriesByTypeProvider`, `categoryStatsProvider`, `categoryCreateNotifierProvider`, `categoryUpdateNotifierProvider`, `categoryValidationNotifierProvider`, `subcategoriesProvider`, `subcategoryCountProvider`, `canHaveSubcategoriesProvider`, `subcategoryCreateNotifierProvider`, `subcategoryUpdateNotifierProvider`, `subcategoryValidationNotifierProvider`, and `categoryDeletionServiceProvider`.
    - **`services/`**
        - `category_deletion_service.dart`: `CategoryDeletionService` class for handling category deletion with proper constraint checking.
        - `category_validators.dart`: `CategoryValidators` class with static methods for validating category fields.
- **`common/`**: Contains shared widgets, services, and providers that are used across multiple features.
    - **`models/`**
        - `time_period.dart`: `TimePeriod` model and `PeriodType` enum.
    - **`providers/`**
        - `time_period_providers.dart`: Defines all providers related to time periods, including `timePeriodNotifierProvider`, `availablePeriodsProvider`, `isCurrentMonthProvider`, `isFuturePeriodProvider`, `currentPeriodDisplayTextProvider`, `currentPeriodDateRangeTextProvider`, `isDateInSelectedPeriodProvider`, and `selectedPeriodDateRangeProvider`.
    - **`services/`**
        - `time_period_service.dart`: `TimePeriodService` class for managing time periods and date calculations.
    - **`widgets/`**
        - `app_bar_helpers.dart`: `AppBarHelpers` class with static methods for creating consistent `AppBar` widgets.
        - `time_period_modal.dart`: `TimePeriodModal` widget for selecting time periods.
        - `time_period_selector.dart`: `TimePeriodSelector` and `CompactTimePeriodSelector` widgets.
- **`dashboard/`**: Provides an overview of the user's financial situation.
    - **`presentation/`**
        - **`screens/`**
            - `home_screen.dart`: `HomeScreen` widget.
    - **`providers/`**
        - `dashboard_providers.dart`: Defines all providers related to the dashboard, including `totalAssetsProvider`, `totalLiabilitiesProvider`, `netWorthProvider`, `currentPeriodBalanceProvider`, `recentTransactionsForDashboardProvider`, `topExpenseCategoriesSelectedPeriodProvider`, and `topIncomeCategoriesSelectedPeriodProvider`.
- **`goals/`**: Allows users to set and track financial goals.
    - **`presentation/`**
        - **`screens/`**
            - `goal_contribution_create_screen.dart`: `GoalContributionCreateScreen` widget.
            - `goal_contribution_edit_screen.dart`: `GoalContributionEditScreen` widget.
            - `goal_contributions_list_screen.dart`: `GoalContributionsListScreen` widget.
            - `goal_create_screen.dart`: `GoalCreateScreen` widget.
            - `goal_edit_screen.dart`: `GoalEditScreen` widget.
            - `goals_list_screen.dart`: `GoalsListScreen` widget.
        - **`widgets/`**
            - `contribution_card.dart`: `ContributionCard` widget.
            - `goal_progress_bar.dart`: `GoalProgressBar` widget.
            - `goal_progress_chart.dart`: `GoalProgressChart` widget.
            - `goal_progress_summary.dart`: `GoalProgressSummary` widget.
    - **`providers/`**
        - `goal_contribution_providers.dart`: Defines all providers related to goal contributions, including `goalContributionsProvider`, `activeGoalContributionsProvider`, `goalContributionProvider`, `watchGoalContributionsProvider`, `watchActiveGoalContributionsProvider`, `goalContributionStatsProvider`, `totalGoalContributionAmountProvider`, `goalContributionCountProvider`, `recentGoalContributionsProvider`, `goalContributionCreationProvider`, `goalContributionUpdateProvider`, and `goalContributionDeletionProvider`.
        - `goal_providers.dart`: Defines all providers related to goals, including `activeUserGoalsProvider`, `goalProvider`, `watchUserGoalsProvider`, `watchGoalProvider`, `goalStatsProvider`, `userGoalSummaryProvider`, `goalCreationProvider`, `goalUpdateProvider`, and `goalDeletionProvider`.
- **`profile/`**: Manages user profiles, including password changes and account deletion.
    - **`presentation/`**
        - **`screens/`**
            - `forgot_password_screen.dart`: `ForgotPasswordScreen` widget.
            - `password_change_screen.dart`: `PasswordChangeScreen` widget.
            - `profile_edit_screen.dart`: `ProfileEditScreen` widget.
            - `profile_screen.dart`: `ProfileScreen` widget.
            - `unified_profile_management_screen.dart`: `UnifiedProfileManagementScreen` widget.
        - **`widgets/`**
            - `account_deletion_dialog.dart`: `AccountDeletionDialog` widget.
            - `re_authentication_dialog.dart`: `ReAuthenticationDialog` widget.
    - **`providers/`**
        - `profile_providers.dart`: Defines all providers related to user profiles, including `userProfileProvider`, `profileUpdateProvider`, `passwordChangeProvider`, `accountDeletionProvider`, and `passwordResetProvider`.
    - **`services/`**
        - `profile_cleanup_service.dart`: `ProfileCleanupService` class for handling comprehensive data cleanup during account deletion.
        - `profile_validators.dart`: `ProfileValidators` class with static methods for validating profile fields.
- **`settings/`**: Manages application settings.
    - **`presentation/`**
        - **`screens/`**
            - `currency_settings_screen.dart`: `CurrencySettingsScreen` widget.
- **`tags/`**: Provides a way to tag transactions for easier filtering and analysis.
    - **`presentation/`**
        - **`screens/`**
            - `tag_create_screen.dart`: `TagCreateScreen` widget.
            - `tag_edit_screen.dart`: `TagEditScreen` widget.
            - `tags_list_screen.dart`: `TagsListScreen` widget.
        - **`widgets/`**
            - `tag_card.dart`: `TagCard` and `TagChip` widgets.
            - `tag_color_selector.dart`: `TagColorSelector` widget.
            - `tag_deletion_dialog.dart`: `TagDeletionDialog` widget.
            - `tag_selector.dart`: `TagSelector` widget.
            - `tags_empty_state.dart`: `TagsEmptyState` widget.
    - **`providers/`**
        - `tag_providers.dart`: Defines all providers related to tags, including `userTagsProvider`, `tagByIdProvider`, `tagSearchProvider`, `tagCreatorProvider`, `tagUpdaterProvider`, `tagDeleterProvider`, `tagsForTransactionProvider`, and `tagUsageCountProvider`.
    - **`services/`**
        - `tag_validators.dart`: `TagValidators` class with static methods for validating tag fields.
- **`transactions/`**: Handles financial transactions, such as income, expenses, and transfers.
    - **`presentation/`**
        - **`providers/`**
            - `transaction_form_providers.dart`: `TransactionFormState` class and `transactionFormProvider`.
        - **`screens/`**
            - `transaction_create_screen.dart`: `TransactionCreateScreen` widget.
            - `transaction_edit_screen.dart`: `TransactionEditScreen` widget.
            - `transaction_filters_screen.dart`: `TransactionFiltersScreen` widget.
            - `transaction_search_screen.dart`: `TransactionSearchScreen` widget.
            - `transactions_list_screen.dart`: `TransactionsListScreen` widget.
        - **`widgets/`**
            - `account_selector.dart`: `AccountSelector` widget.
            - `amount_input_field.dart`: `AmountInputField` widget.
            - `category_selector.dart`: `CategorySelector` widget.
            - `date_picker_field.dart`: `DatePickerField` widget.
            - `description_field.dart`: `DescriptionField` widget.
            - `empty_transactions_state.dart`: `EmptyTransactionsState` widget.
            - `notes_field.dart`: `NotesField` widget.
            - `transaction_card.dart`: `TransactionCard` widget.
            - `transaction_form.dart`: `TransactionForm` widget.
            - `transaction_metadata_card.dart`: `TransactionMetadataCard` widget.
            - `transaction_type_selector.dart`: `TransactionTypeSelector` widget.
    - **`providers/`**
        - `transaction_providers.dart`: Defines all providers related to transactions, including `transactionListProvider`, `transactionCreatorProvider`, `transactionUpdaterProvider`, and `transactionDeleterProvider`.
    - **`services/`**
        - `transaction_error_service.dart`: `TransactionErrorService` class with static methods for handling transaction-related errors.
        - `transaction_validators.dart`: `TransactionValidators` class with static methods for validating transaction fields.

### 3.4. Localization (`l10n/`)

- **`app_localizations.dart`**: Defines the abstract class for localizations, which is used to access localized strings throughout the application.
- **`app_localizations_en.dart`**: Provides the English translations for the application.

### 3.5. Providers (`providers/`)

- **`repository_providers.dart`**: Defines the providers for the application's repositories.
- **`firebase_providers.dart`**: Provides access to Firebase services, such as Auth and Firestore.
- **`error_providers.dart`**: Manages error handling and reporting.
- **`currency_providers.dart`**: Handles currency formatting and conversion.
    - `sharedPreferencesProvider`: Provider for `SharedPreferences` instance.
    - `currencyPreferencesServiceProvider`: Provider for `CurrencyPreferencesService`.
    - `currencyPreferenceProvider`: `StateNotifierProvider` for the current currency preference.
    - `supportedCurrenciesProvider`: Provider for the list of supported currencies.
    - `currencyFormatterProvider`: Provider for the `CurrencyFormatter`.
    - `CurrencyPreferenceNotifier`: `StateNotifier` for managing currency preference.
    - `CurrencyFormatter`: Helper class for currency formatting.
- **`async_entity_notifier.dart`**: `AsyncEntityNotifier`, `AsyncEntityListNotifier`, and `AsyncSingleEntityNotifier` base classes for managing entities with Riverpod.
- **`ui_providers.dart`**: `globalLoadingProvider`, `secureModeProvider`, and `themeModeProvider` for managing UI state.

### 3.6. Routing (`routing/`)

- **`app_router.dart`**: Configures the application's routes using the `go_router` package. It defines the routes for each screen and handles navigation between them.
    - `AppRoutes`: A class with static constants for all the routes in the app.
    - `AuthStateNotifier`: A `ChangeNotifier` that makes the router reactive to authentication state changes.
    - `goRouterProvider`: The main `GoRouter` provider.

### 3.7. Services (`services/`)

- **`auth_service.dart`**: Implements the authentication logic, including signing in, signing out, and managing user sessions.
- **`firestore_service.dart`**: Provides a wrapper around the Firestore API for easier data access.
- **`secure_storage_service.dart`**: Manages the secure storage of sensitive data, such as API keys and user credentials.
- **`logging_service.dart`**: Implements a logging service for debugging and error reporting.
- **`performance_service.dart`**: Tracks the performance of the application, including screen load times and other metrics.
- **`cache_service.dart`**: `CacheService` class for multi-level caching.
- **`currency_preferences_service.dart`**: `CurrencyPreferencesService` class for managing user currency preferences.
- **`currency_service.dart`**: `CurrencyService` class for handling currency formatting and operations.
- **`error_service.dart`**: `ErrorService` class for consistent user-facing error messages.
- **`global_error_handler.dart`**: `GlobalErrorHandler` class for centralized error handling.
- **`implementations/`**: Contains the concrete implementations of the service interfaces.
    - `firebase_connectivity_service_impl.dart`: `FirebaseConnectivityServiceImpl`
- **`interfaces/`**: Contains the interfaces for all services.
    - `firebase_connectivity_service.dart`: `IFirebaseConnectivityService`
- **`performance_benchmark_service.dart`**: `PerformanceBenchmarkService` class for measuring and validating app performance.
- **`performance_monitoring_service.dart`**: `PerformanceMonitoringService` class for tracking repository performance metrics.
- **`performance_rollout_service.dart`**: `PerformanceRolloutService` class for managing gradual rollout of performance optimizations.
- **`repository_performance_wrapper.dart`**: `RepositoryPerformanceWrapper` class for wrapping repository operations with performance monitoring and caching.
- **`session_service.dart`**: `SessionService` class for handling comprehensive session management.

### 3.8. Widgets (`widgets/`)

- **`forms/`**: Contains a generic form system that is used to create dynamic forms with minimal boilerplate code. This system is used for all create/edit screens in the application.
- **`navigation/`**: Implements the application's navigation components, such as the bottom navigation bar and the floating action button.
- **`common/`**: Contains a set of common widgets that are used throughout the application, such as text fields, buttons, and loading indicators.
- **`coming_soon_screen.dart`**: `ComingSoonScreen` widget.
- **`error_boundary_widget.dart`**: `ErrorBoundaryWidget` widget.
- **`performance_tracked_screen.dart`**: `PerformanceTrackedScreen` widget.
- **`splash_screen.dart`**: `SplashScreen` widget.

## 4. Code Generation

The application uses several code generation packages to reduce boilerplate code and improve developer productivity. These include:

- **`freezed`**: For creating immutable data classes.
- **`riverpod_generator`**: For generating providers for Riverpod.
- **`go_router_builder`**: For generating the application's routes.
- **`build_runner`**: For running the code generation tools.

## 5. Testing

The `test/` directory contains the unit and integration tests for the application. The tests are organized by feature and follow the same structure as the `lib/` folder. The application uses the `flutter_test` and `mockito` packages for testing.

This map provides a high-level overview of the BudApp codebase. For more detailed information, please refer to the source code and the documentation in the `docs/` folder.